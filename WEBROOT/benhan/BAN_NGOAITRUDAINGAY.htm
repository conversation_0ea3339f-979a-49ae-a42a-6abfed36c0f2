<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />

<link rel="stylesheet" href="../common/css/custom_ban.css"></link>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jquery/jquery.confirm.js"></script>
<link href="../common/css/custom.css" />
<link rel="stylesheet" href="../common/css/custom.css">
<!-- <link rel="stylesheet" href="../noitru/custom_nt.css"> -->
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../noitru/jquery.bootstrap-growl.min.js"></script>
<script type="text/javascript" src="../benhan/BAN_Common.js?v=16"></script>
<script type="text/javascript"
	src="../benhan/BAN_NGOAITRUDAINGAY.js?v=240529"></script>
<script type="text/javascript"
	src="../noitru/NTU02D023_Thongtinhanhchinh.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>
<style>
.ui-jqgrid-htable,
.ui-jqgrid-btable,
.ui-jqgrid-pager,
.ui-jqgrid-view,
.ui-jqgrid-bdiv,
.ui-jqgrid-hdiv,
.ui-jqgrid-hbox,
.ui-jqgrid { max-width: 100% !important; width:100% !important; }
</style>


<script type="text/javascript">
	var opt = [];
	var lang = '{lang}';
	var hospital_id = '{hospital_id}';
	var province_id = '{province_id}';
	var ma_tinh_id = '{ma_tinh}';
	var company_id = '{company_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_id);
	var sys_par = [];
	var ma_lk = '{ma_lk}';
	var _mode = '{mode}';
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var _param = session_par;
</script>

<style>
	.form-inline {
		overflow: auto ;
	}
</style>
<div width="100%" id="divMain-BA" style="border: 0px;">
	<ul id="tabs" class="nav nav-tabs" style="margin-top: 10px;"
		data-tabs="tabs">
		<li class="active"><a href="#tabBenhAnHoiKham" data-toggle="tab">A.Hỏi bệnh/khám bệnh</a></li>
		<li><a href="#tabBenhAnTongKet" data-toggle="tab">B.Tổng kết
				hồ sơ bệnh án</a></li>
		<li id="liBenhNhanThongTin"><a href="#tabBenhNhanThongTin"
			data-toggle="tab">C.Thông tin bệnh nhân</a></li>
	</ul>

	<div id="my-tab-content" class="tab-content">
		<input type="hidden" id="hidPHONGKHAMDANGKYID" name="hidPHONGKHAMDANGKYID">
		<input type="hidden" id="hidKHAMBENHID" name="hidKHAMBENHID">
		<div>
			<a href="#" class="scrollToTop before"></a>
		</div>
		<div id="tabBenhAnHoiKham" class="tab-pane active mgb10" style="padding-bottom: 10px;">
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">I. Lý do vào viện:</div>
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group required">
							<label>Lý do vào viện:</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtLYDOVAOVIEN"
								maxlength="500" name="txtLYDOVAOVIEN" title=""
								valrule="Lý do vào viện,trim_required|max_length[500]"
								style="width: 100%;">
						</div>						
					</div>
				</div>
			</div>
			<!-- END FORM INLINE -->
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">II. Hỏi bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Quá trình bệnh lý:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLY"
								valrule="Quá trình bệnh lý,max_length[500]"
								class=" input-sm i-col-3" id="txtQTBENHLY"
								style="height: 80px !important; width: 100%" maxlength="500"
								rows="4"></textarea>
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>2. Tiền sử bệnh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Bản thân:</label>
						</div>
					</div>
					
					<div class=" form-inline" style="margin-bottom: 5px;">
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtMABANTHAN" name="txtMABANTHAN"
								valrule="Mã bản thân,max_length[500]" title="" attrIcd="0">
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtBANTHAN" name="txtBANTHAN"
								valrule="Bản thân,max_length[500]" title="">
						</div>						
					</div>									
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Gia đình:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtGIADINH" valrule="Gia đình,max_length[500]"
								class=" input-sm i-col-3" id="txtGIADINH"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="2">Không có bệnh gì lạ</textarea>
						</div>
					</div>	
				</div>
			</div>
			<!-- END FORM INLINE -->
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">III. Khám bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Toàn thân: </label>
						</div>
					</div>
					<div class=" form-inline">
					 <div class="col-xs-9 low-padding">
					 	<div class="col-xs-12 low-padding">
							<textarea name="txtTOANTHAN" valrule="Toàn thân,max_length[3000]"
								class=" input-sm i-col-3" id="txtTOANTHAN"
								style="height: 75px !important; width: 100%" maxlength="3000"
								rows="3">Bệnh tỉnh, tiếp xúc được, da niêm hồng, kết mạc mắt không vàng</textarea>
						</div>
						<div class="col-xs-12 low-padding mgt5">						 
							<label class=''>2. Các bộ phận:</label>						
					   </div>					
					   <div class="col-xs-12 low-padding">		
							<textarea name="txtCACBOPHAN" valrule="Các bộ phận,max_length[3000]"
								class=" input-sm i-col-3" id="txtCACBOPHAN"
								style="width: 100%" maxlength="3000"
								rows="4">Tim đều:
Phổi không ran bệnh lý:
Bụng mềm:							 
							</textarea>
					   </div>		
					 </div>
						<div class="col-xs-3 low-padding">
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class="mgl10">Mạch</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input class="col-xs-4 form-control input-sm "
										style="width: 100%;" id="txtMACH" name="txtMACH"
										valrule="Mạch,is_natural|max_length[3]" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhiệt độ:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhiệt độ,numeric|max_length[6]" maxlength="6"
										class="form-control input-sm " style="width: 100%;"
										id="txtNHIETDO" name="txtNHIETDO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>C</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Huyết áp:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Huyết áp,is_natural|max_length[3]"
										class="form-control input-sm " style="width: 45%;"
										id="txtHUYETAP1" name="txtHUYETAP1" title=""> / <input
										class="form-control input-sm "
										style="width: 45%; float: right;"
										valrule="Huyết áp,is_natural|max_length[3]" id="txtHUYETAP2"
										name="txtHUYETAP2" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>mmHg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhịp thở:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhịp thở,is_natural|max_length[3]"
										class="form-control input-sm " style="width: 100%;"
										id="txtNHIPTHO" name="txtNHIPTHO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Cân nặng:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Cân nặng,numeric|max_length[6]" maxlength="6"
										class="form-control input-sm " style="width: 100%;"
										id="txtCANNANG" name="txtCANNANG" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>kg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chiều cao:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Chiều cao,numeric|max_length[6]" maxlength="6"
										class="form-control input-sm " style="width: 100%;"
										id="txtCHIEUCAO" name="txtCHIEUCAO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>cm</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chỉ số BMI:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<label id="txtBMI" name="txtBMI" title=""></label>
								</div>
							</div>
						</div>
					</div>					
					<div class=" form-inline">
						<div class="col-xs-12 low-padding mgt5">
							<label class='l-col-3'>3. Tóm tắt kết quả cận lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-10 low-padding">
							<textarea name="txtXETNGHIEMCLS"
								valrule="Các xét nghiệm cận lâm sàng
								cần làm,max_length[3000]"
								class=" input-sm i-col-3" id="txtXETNGHIEMCLS"
								style="height: 60px !important; width: 99%" maxlength="3000"
								rows="3"></textarea>
						</div>
						<div class="col-xs-2 low-padding" style="text-align: left;">
							<button id="btnKQCLS" class="btn btn-sm btn-primary" style="width: 20% !important;">...</button>
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Chẩn đoán ban đầu: </label>
						</div>
					</div>					
					<div class=" form-inline" style="margin-bottom: 5px;">
						<div class="col-xs-2 low-padding ">
							<label class='l-col-3 mgl5'>Bệnh chính</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtMACHANDOANBANDAU" name="txtMACHANDOANBANDAU"
								valrule="Mã chẩn đoán ban đầu,max_length[3000]" title="" attrIcd="0">
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtCHANDOANBANDAU" name="txtCHANDOANBANDAU"
								valrule="Chẩn đoán ban đầu,max_length[3000]" title="">
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								   id="txtGHICHU_BENHCHINH" name="txtGHICHU_BENHCHINH"
								   valrule="Ghi chú bệnh chính,max_length[3000]" title="">
						</div>
					</div>
					<div class=" form-inline" style="margin-bottom: 5px;">
						<div class="col-xs-2 low-padding ">
							<label class='l-col-3 mgl5'>Bệnh phụ</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtMACHANDOANBANDAU_KEMTHEO" name="txtMACHANDOANBANDAU_KEMTHEO"
								valrule="Mã chẩn đoán ban đầu kèm theo,max_length[3000]" title="" attrIcd="0">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtCHANDOANBANDAU_KEMTHEO" name="txtCHANDOANBANDAU_KEMTHEO"
								valrule="Chẩn đoán ban đầu kèm theo,max_length[3000]" title="">
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-1 low-padding ">
							<label class='l-col-3'>Mức độ THA</label>
						</div>
						<div class="col-xs-5 low-padding ">
							<input class="form-control input-sm" style="width: 100%;"
								   id="txtMUCDOTHA" name="txtMUCDOTHA"
								   valrule="Mức độ THA,max_length[3000]" title="">
						</div>
						<div class="col-xs-1 low-padding ">
							<label class='l-col-3 mgl5'>Giai đoạn THA</label>
						</div>
						<div class="col-xs-5 low-padding ">
							<input class="form-control input-sm" style="width: 100%;"
								   id="txtGIAIDOANTHA" name="txtGIAIDOANTHA"
								   valrule="Giai đoạn THA,max_length[3000]" title="">
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Đã xử lý (thuốc, chăm sóc):</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtDAXULYTHUOC"
								valrule="Tóm tắt bệnh án,max_length[500]"
								class=" input-sm i-col-3" id="txtDAXULYTHUOC"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3">điều trị ngoại trú</textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding mgt5">
							<label class='l-col-3'>6. Chẩn đoán khi ra viện: </label>
						</div>
					</div>
					<div class=" form-inline" style="margin-bottom: 5px;">
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtMABENHRAVIEN" name="txtMABENHRAVIEN"
								valrule="Mã bệnh khi ra viện,max_length[500]" title="" attrIcd="0">
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" style="width: 100%;"
								id="txtTENBENHRAVIEN" name="txtTENBENHRAVIEN"
								valrule="Tên bệnh khi ra viện,max_length[500]" title="">
						</div>						
					</div>
								 
				</div>
			</div><!-- END FORM INLINE -->
			<div class=" form-inline">
				<div class="col-xs-2 low-padding mgt5">
					<label class='l-col-3'>7. Điều trị ngoại trú</label>
				</div>
				<div class="col-xs-1 low-padding mgt5">
					<label class=''>Từ ngày: </label>
				</div>
				<div class="col-xs-2 low-padding mgt5">
					<div class="input-group">
						<input class="form-control input-sm" id="txtTUNGAYDTRI"
							name="txtTUNGAYDTRI"
							valrule="TỪ ngày điều trị,date|max_length[10]" title=""
							data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span
							class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
							type="sCal"
							onclick="NewCssCal('txtTUNGAYDTRI','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div>
				</div>
				<div class="col-xs-1 mgt5">
					<label class=''>Đến ngày: </label>
				</div>
				<div class="col-xs-2 low-padding mgt5">
					<div class="input-group">
						<input class="form-control input-sm" id="txtDENNGAYDTRI"
							name="txtDENNGAYDTRI"
							valrule="TỪ ngày điều trị,date|max_length[10]" title=""
							data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span
							class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
							type="sCal"
							onclick="NewCssCal('txtDENNGAYDTRI','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div>
				</div>
			</div>
			<div id="dvTIMKIEM" class=" form-inline" style="display:none;">
				<div class="col-xs-1 low-padding mgt5">
					<label class='l-col-3'>Tìm kiếm theo năm</label>
				</div>
				<div class="col-xs-1 low-padding mgt5">
					<select class="form-control input-sm" id="cboNAM" style="width: 100%;">
							<option value="0" selected>Tất cả</option>
					</select> 
				</div>
				<div class="col-xs-1 low-padding mgt5 mgl5">
					<button class="btn btn-sm btn-primary" id="btnTIMKIEM" style="width: 90%;">
						<span class="glyphicon glyphicon-search"></span> Tìm kiếm
					</button>
				</div>
			</div>
			
			<div class="col-xs-12 low-padding" style="margin-bottom: 60px;">
				<table id="grdDIENBIENBENH"></table>
				<div id="pager_grdDIENBIENBENH"></div>
			</div>
		</div>
		<div id="tabBenhAnTongKet" class="tab-pane mgb10" style="padding-bottom: 10px;">

			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">TỔNG KẾT BỆNH ÁN:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>1. Quá trình bệnh lý và diễn biến
								lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtBENHLYDBLS"
								valrule="Quá trình bệnh lý và diễn biến lâm sàng,max_length[2000]"
								class=" input-sm i-col-3"
								id="txtBENHLYDBLS"
								style="height: 100px !important; width: 100%" maxlength="2000"
								rows="5">Bệnh diễn tiến tốt, tiên lượng tạm ổn</textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding mgt5">
							<label class=''>2. Tóm tắt kết quả xét nghiệm cận
								lâm sàng có giá trị chẩn đoán:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-10 low-padding">
							<textarea name="txtKQXNCLS"
								valrule="Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán,max_length[500]"
								class=" input-sm i-col-3" id="txtKQXNCLS"
								style="height: 80px !important; width: 99%" maxlength="500"
								rows="4"></textarea>
						</div>
						<div class="col-xs-2 low-padding" style="text-align: left;">
							<button id="btnKQCLS1" class="btn btn-sm btn-primary" style="width: 20% !important;">...</button>
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class=''>3. Chẩn đoán ra viện:</label>
						</div>
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Bệnh chính:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHCHINH" name="txtMABENHCHINH" valrule="Mã bệnh chính,max_length[500]" title="" attrIcd="0" targetCtr="txtBENHCHINH">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHCHINH" name="txtBENHCHINH" valrule="Tên bệnh chính,max_length[500]" title="">
						</div>
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Bệnh kèm theo(nếu có):</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHKEMTHEO" name="txtMABENHKEMTHEO" valrule="Mã bệnh kèm theo,max_length[500]" title="" attrIcd="1" targetCtr="txtBENHKEMTHEO">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHKEMTHEO" name="txtBENHKEMTHEO" valrule="Tên bệnh kèm theo,max_length[500]" title="">
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Phương pháp điều trị: </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtPPDIEUTRI" 
								valrule="Phương pháp điều trị,max_length[500]"
								class=" input-sm i-col-3" id="txtPPDIEUTRI"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3">Điều trị nội khoa</textarea>
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Tình trạng người bệnh ra viện: </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTTNBRAVIEN"
								valrule="Tình trạng người bệnh ra viện,max_length[500]"
								class=" input-sm i-col-3" id="txtTTNBRAVIEN"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3">Bệnh ổn</textarea>
						</div>
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>6. Hướng điều trị và các chế độ tiếp theo:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHUONGDIEUTRI"
								valrule="Hướng điều trị ,max_length[500]"
								class=" input-sm i-col-3" id="txtHUONGDIEUTRI"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3">Uống thuốc theo toa</textarea>
						</div>
					</div>														
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding mgb35" style="width: 100%">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th colspan="2"
											style="font-size: x-small; text-align: center;">Hồ sơ,
											phim, ảnh</th>
									</tr>
									<tr>
										<th style="font-size: x-small; text-align: center;">Loại</th>
										<th style="font-size: x-small; text-align: center;">Số tờ</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td><label class='l-col-1 control-label'>- X -
												quang</label></td>
										<td><input class="form-control input-sm"
											valrule="Số X - Quang,is_natural|max_length[5]" style="width: 100%;"
											id="txtSOXQUANG" name="txtSOXQUANG" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- CT
												Scanner</label></td>
										<td><input class="form-control input-sm"
											valrule="Số CT Scanner,is_natural|max_length[5]" style="width: 100%;"
											id="txtSOSCANNER" name="txtSOSCANNER" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Siêu
												âm</label></td>
										<td><input class="form-control input-sm "
											valrule="Số Siêu âm,is_natural|max_length[5]" style="width: 100%;"
											id="txtSOSIEUAM" name="txtSOSIEUAM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Xét
												nghiệm</label></td>
										<td><input class="form-control input-sm "
											valrule="Số Xét nghiệm,is_natural|max_length[5]" style="width: 100%;"
											id="txtSOXETNGHIEM" name="txtSOXETNGHIEM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>-
												Khác……………</label></td>
										<td><input class="form-control input-sm " style="width: 100%;"
											valrule="Số khác,is_natural|max_length[5]" id="txtSOKHAC"
											name="txtSOKHAC" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Toàn
												bộ hồ sơ</label></td>
										<td><input class="form-control input-sm" style="width: 100%;"
											id="txtSOTOANBOHOSO" name="txtSOTOANBOHOSO" title=""
											disabled="disabled"></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<!-- END FORM INLINE -->
		</div>

		<div id="tabBenhNhanThongTin" class="tab-pane"></div>

		<div class="form-inline btn-fixed">
			<div class="col-xs-12 low-padding mgt10" style="text-align: right;" id="gridButton">
				<div class="col-xs-7 low-padding">
					<button class="btn btn-sm btn-primary" id="btnLuu">
						<span class="glyphicon glyphicon-pencil"></span> Lưu
					</button>
					<button class="btn btn-sm btn-primary" id="btnThemMoi">
						<span class="glyphicon glyphicon-floppy-remove"></span> Lưu & Đóng
					</button>
					<button class="btn btn-sm btn-primary" id="btnInBieuMau">
						<span class="fixDisplay glyphicon glyphicon-print"></span> In bệnh án
					</button>				
					<button class="btn btn-sm btn-primary" id="btn_Close">
						<span class="glyphicon glyphicon-remove-circle"></span> Đóng
					</button>
					<button class="btn btn-sm btn-primary" id="btnLuuBAMau">
						<span class="glyphicon glyphicon-pencil"></span> Lưu BA mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btnXoaBAMau">
						<span class="glyphicon glyphicon-pencil"></span> Xóa BA mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btnCopy">
						<span class="glyphicon glyphicon-pencil"></span> Copy BA
					</button>
				</div>
				<div class="col-xs-5 low-padding mgt3">
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Tên BA mẫu:</label>
					</div>
					<div class="col-xs-4 low-padding">
						<input class="form-control input-sm" id="txtTENBENHANMAU"
							maxlength="500" name="txtTENBENHANMAU" title="" valrule="Tên bệnh án mẫu,max_length[500]"
							style="width: 100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Bệnh án mẫu:</label>
					</div>
					<div class="col-xs-4 low-padding">
						<div class="input-group" style="width:100%">				     			
							<select class="form-control input-sm" id="cboMAUBAID" style="width: 100%;" reffld="TENBAMAU" >
							</select> 
			     		</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt5 mgb5" style="text-align: center;">
				<div class="col-xs-7 low-padding" style="text-align: center;">
					<button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
						<span class="glyphicon glyphicon-ok"></span> Ký & In
					</button>
					<button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
						<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
					</button>
					<button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
						<span class="glyphicon glyphicon-print"></span> In ký số
					</button>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
	var opt = [];
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var dept_id = '{dept_id}';
	var phongid = '{subdept_id}';	
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var db_schema = '{db_schema}';
	var lang = "vi";
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	session_par[4] = db_schema;
	var table_name = '{table}';

	var _opts = new Object();
	var mode = '{showMode}';
	if (mode == 'dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data = EventUtil.getVar("dlgVar");
		_opts.khambenhid = data.khambenhid;
		_opts.hosobenhanid = data.hosobenhanid;
		_opts.benhnhanid = data.benhnhanid;
		_opts.loaibenhanid = data.loaibenhanid;
		_opts.maloaibenhan = data.maloaibenhan;
		_opts.trang_thai = data.trang_thai;
		_opts.khoaid = dept_id;		
		_opts.phongid=phongid;		
		_opts.phongKhamDKId=data.phongKhamDKId;
		_opts.khamBenhId=data.khamBenhId;
		_opts.hosobenhanid_hientai = typeof data.hosobenhanid_hientai == "undefined" ? "" : data.hosobenhanid_hientai;
		// L2PT-76863 start
		_opts.loaitiepnhanid=data.loaitiepnhanid;
		_opts.trangthaikhambenh=data.trangthaikhambenh;
		_opts.NTU_INBA_HSDBA=data.NTU_INBA_HSDBA;
		// L2PT-76863 end
	}
	initRest(_opts._uuid);

	_opts._param = session_par;
	_opts._uuid = uuid;
	initRest(_opts._uuid);
	var BANNGOAITRUDAINGAY = new BAN_NGOAITRUDAINGAY(_opts);
	BANNGOAITRUDAINGAY.load();
</script>