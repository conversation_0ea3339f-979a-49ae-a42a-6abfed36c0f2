/*
 Mã màn hình  : DUC11N001
File mã nguồn : DUC11N001_YeuCauNhapThuoc.js
<PERSON><PERSON><PERSON> đích  : <PERSON><PERSON><PERSON> yêu cầu thuốc từ kho khác
<PERSON> lập tr<PERSON><PERSON> c<PERSON> n<PERSON> chú
TuanNM	- 08092016 - Comment
 */
function mgrYeuCauNhapThuoc(i_mode) {

	this.objectId = _object_id;
	var _object_id = '';
	var _mode = i_mode;
	if (_mode == '')
		_mode = "edit";
	var _soLuongKhaDung = 0;
	var _gridId1 = "gridList_THUOC";
	var _param = session_par;
	var _dsKhoCap = "";
	var _thuocVattu = "";
	var _loaiphieunhap = 'NKK';
	var Ancot ='f';
	var ThuocChuaCoTrongKho =0;

	var _NhapXuatCTSQL = "DUC11N001.09";
	// var _NhapXuatSQL = "DUC30X001.02";
	// var _formSQL = "DUC11N001.01";
	// var _thuocSQL = "DUC11N001.02";
	var _selectUserName = "DUC11N001.03";
	var selectKhoId = "DUC11N001.04";
	var _selectKhoCungUng = "DUC11N001.05";
  
 	var PHARMA_LOADGRID_DUC11N001 = '0';
	var CHBoSung ='';
	var flag_thuoctd = 0;
	var that = this;
	this.load = doLoad;
	var loaiThuoc ='';
	var PHARMA_HIENTHI_IMPORT_PHIEUDT ='';
	var showImport =0;
	var PHARMA_NT_MACSYT ='';
	var macsyt =''; 
	var PHARMA_YEUCAUDUTRU_VUOTKHADUNG ='0';
	var PHARMA_BOSUNG_THEOLO ='0';
	var PHARMA_CHECK_HSD_CHUYENKHO = '0';
	var PHARMA_XUATTRAMAU_NCC_BARCODE ='';
	var PHARMA_CHECK_DEL_TVT_BUTT = '0';
	var show_cbLoaiTVT = '';
	var maKho_BarcodeMau = '';

	function doLoad() {
		
		show_cbLoaiTVT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_SHOW_CBLOAITVT_DUTRU'].join('$'));
		
		if(((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "VATTU" && _param[0] !='42346'){
			ComboUtil.getComboTag("cboLoaiThuoc", "DUC67T001.62", [ {
				"name" : "[0]",
				"value" : "1"
			} ], "", "");
		}else if(((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "THUOC" && _param[0] =='42346'){
			ComboUtil.getComboTag("cboLoaiThuoc", "DUC67T001.63", [ {
				"name" : "[0]",
				"value" : "1"
			} ], "", {
				value : '-1',
				text : 'Chọn'
			});
		}else{
			ComboUtil.getComboTag("cboLoaiThuoc", "DUC67T001.26", [ {
				"name" : "[0]",
				"value" : "1"
			} ], "", {
				value : '-1',
				text : 'Chọn'
			});
		}
		
		var  cauhinhbs = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_DTBS_TVT_NGOAI_DM'].join('$'));
		PHARMA_YEUCAUDUTRU_VUOTKHADUNG = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_YEUCAUDUTRU_VUOTKHADUNG'].join('$'));
		
		PHARMA_NT_MACSYT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH',
		'PHARMA_NT_MACSYT');
		if (PHARMA_NT_MACSYT =='1'){ 
			var _par = $("#hid_LoaiKhoId").val() + '$';
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC53M001.05", _par); 
			macsyt = result[0].MACSYT;
		}
		else macsyt = _opts.hospital_code;
		
		PHARMA_BOSUNG_THEOLO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_BOSUNG_THEOLO'].join('$'));
		PHARMA_CHECK_HSD_CHUYENKHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_CHECK_HSD_CHUYENKHO'].join('$'));
		PHARMA_XUATTRAMAU_NCC_BARCODE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_XUATTRAMAU_NCC_BARCODE'].join('$'));
		PHARMA_CHECK_DEL_TVT_BUTT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_CHECK_DEL_TVT_BUTT'].join('$'));
		
		if (($("#hid_HINHTHUCID").val() == "9"  || $("#hid_HINHTHUCID").val() == "2") && cauhinhbs =='1' &&$("#hidHoanTra").val()!='1')
			CHBoSung ='1';
		if($("#hidDieuChinh").val()=='1'){
			CHBoSung ='1';
			$('#cmdLuuPhieu').hide();
			$('#cmdCanDoi').show();
		}else{
			$('#divMATHUOC').attr("class","col-md-12 low-padding");
			$('#divTHUOCTD').hide();
		}
			
		if($("#hidDuyetLamSan").val()=='1'){
			$('#cmdLuuPhieu').hide();
			$("#cmdGuiYeuCau").html("Duyệt lâm sàn");
			$('#cmdTuChoi').show();			
		}
		 
		if($("#hidDieuChinh").val()!='1' && $("#hid_HINHTHUCID").val() == "2" && _param[0] =='1111')
			{
				$('#divTHUOCTD').hide();
				$('#divLoaiThuoc').show();
				$('#divMATHUOC').attr("class","col-md-7 low-padding");
				$('#divLoaiThuoc').attr("class","col-md-5 low-padding");
				loaiThuoc = '1';
			}
	    if((((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "VATTU" && _param[0] !='42346')
	    	|| (((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "THUOC" && _param[0] =='42346')){
	    	$('#divTHUOCTD').hide();
			$('#divLoaiThuoc').show();
			$('#divMATHUOC').attr("class","col-md-7 low-padding");
			$('#divLoaiThuoc').attr("class","col-md-5 low-padding");
			loaiThuoc = '1';
	    }
			
		var _parMaPhieu = ['PHARMA_CAP_NHAT_MA_PHIEU_NX'];
		var resultMP = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
				_parMaPhieu.join('$'));
		PHARMA_LOADGRID_DUC11N001 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_LOADGRID_DUC11N001');
		if(resultMP==0) {
			$('#txtMA_PHIEU').attr('disabled', 'disabled');
		}
		
		if($("#hidHoanTra").val() == '1'){
			$('#div_HSD_TVT').show();
		}
		
 
		if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
			$("#div_kho").css("display", "none");
			$("#div_buttt").css("display", "none");
			$("#_sochungtu").css("display", "");
			
			var _par = ['PHARMA_KHONG_CAN_KETOANDUOC_DUYET'];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
					_par.join('$'));
			
			if(result == '1'){
				$("#cmdXuatNCC").css("display", "none");
			}
			else {
				$("#cmdGuiYeuCau").css("display", "none");	
			}
			//$("#cmdXuatNCC").css("display", "none");
			
			loadComboGridNCC();
			if(PHARMA_XUATTRAMAU_NCC_BARCODE != '' || jQuery.type(PHARMA_XUATTRAMAU_NCC_BARCODE)!== "undefined"){
				var _par = [$("#hid_LoaiKhoId").val()];
				maKho_BarcodeMau = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC11N001.LOAIKHO",
						_par.join('$'));
			}
			
		} else 
			if (($("#hid_HINHTHUCID").val() == "9")) {
				//$("#div_kho").css("display", "none");
				$("#cmdXuatNCC").css("display", "none");
				$("#div_ncc").css("display", "none");
				$("#div_diachi").css("display", "none");
				
			}
		else
		{
			$("#div_buttt").css("display", "none");
			$("#cmdXuatNCC").css("display", "none");
			$("#div_ncc").css("display", "none");
			$("#div_diachi").css("display", "none");
			
		}

		$("#txtMA_PHIEU").focus();
		if ($("#hid_LOAIGIAODIEN").val() == "VATTU") {
			_thuocVattu = "vật tư";
			$("#lbMA_THUOC").text("Mã vật tư");
		} else if ($("#hid_LOAIGIAODIEN").val() == "THUOC") {
			_thuocVattu = "thuốc";
			$("#lbMA_THUOC").text("Mã thuốc");
		} else {
			_thuocVattu = "thuốc/VT";
			$("#lbMA_THUOC").text("Mã thuốc/VT");
		}
	  Ancot = 'f'; // f: ko an cot, t là ẩn cột
 		if( checkAnGia($("#hid_HINHTHUCID").val())==1) {
 			Ancot = 't';
 			$('[name=divGia]').hide();	};
 			
 	 if( (_param[0] =='1111' || _param[0] =='10284') &&  $("#hid_HINHTHUCID").val() != "1" ){	//10284
 		var barCode = '';
 		if(((PHARMA_XUATTRAMAU_NCC_BARCODE != '' || jQuery.type(PHARMA_XUATTRAMAU_NCC_BARCODE)!== "undefined" ) 
				&& PHARMA_XUATTRAMAU_NCC_BARCODE.includes(maKho_BarcodeMau)) && $("#hidLoaiNhapBu").val() == "XUATTRANCC_TK"){
 			barCode =  "BarCode,BARCODE,40,0,f,l,0;" ;
 		}
		GridUtil.init(_gridId1, "100%", "180", "Danh sách " + _thuocVattu,
				false, "THUOCVATTUID,THUOCVATTUID,0,0,t,l,0;Mã " + _thuocVattu
						+ ",MA_THUOC,30,0,f,l,0;Tên " + _thuocVattu
						+ ",TEN_THUOC,80,0,f,l,0;"
						+ "Hoạt chất,HOATCHAT,60,0,f,l,0;"
						+ "Đơn vị,DON_VI_TINH,20,0,f,l,0;"
						+ "Số lượng,SL_YC,20,decimal!3,f,r;"
						+ barCode
						+"TONKHOCTID,TONKHOCTID,0,0,t,l,0;"
						+ "Đơn giá,DON_GIA,30,decimal!3,"+Ancot+",r;"
						+ "VAT%,VAT,0,0,t,l,0;"
						+ "Thành tiền,TIEN_TRUOC_VAT,40,decimal!3,"+Ancot+",r;"
						+ "Thuế GTGT,THUEGTGT,40,decimal!3,t,r;"
						+ "Hàm lượng,LIEULUONG,40,0,f,r;"
						+ "Số lô,SOLO,40,0,f,r;"
						+ "Hạn sử dụng,HANSUDUNG,40,0,f,r;"
						+ "sl khả dụng,SL_KHADUNG,30,0,f,l,0;"
						+ "DONGIAVAT,DON_GIA_VAT,0,0,t,l,0;"
						+ "THANH_TIEN,THANH_TIEN,0,number,t,l,0;"
						+ "SO_DU,SO_DU,0,0,t,l,0;"
						+ "GIA_BHYT,GIABHYT,0,0,t,l,0;"
						+ "GIA_NHANDAN,GIANHANDAN,0,0,t,l,0;"
						+ "GIA_DICHVU,GIADICHVU,0,0,t,l,0;"
						+ "GIA_TRANBHYT,GIATRANBHYT,0,0,t,l,0;"
						+ "THUOCKHOYC,THUOCKHOYC,0,0,f,l,0;"
						+ "THUOCKHOCAP,THUOCKHOCAP,0,0,f,l,0;"
						+ "MARK,MARK,0,0,f,l,0;"
						+ "ACT,ACT,20,d,f,l,0",true,{
							rowNum : 1000,
							rowList : [ 1000, 3000 ]
						});
		
 	 }else 
 		GridUtil.init(_gridId1, "100%", "180", "Danh sách " + _thuocVattu,
				false, "THUOCVATTUID,THUOCVATTUID,0,0,t,l,0;Mã " + _thuocVattu
						+ ",MA_THUOC,30,0,f,l,0;Tên " + _thuocVattu
						+ ",TEN_THUOC,80,0,f,l,0;"
						+ "Hoạt chất,HOATCHAT,60,0,f,l,0;"
						+ "Đơn vị,DON_VI_TINH,20,0,f,l,0;"
						+ "Số lượng,SL_YC,20,number,f,r;"
						+ "Đơn giá,DON_GIA,30,number,"+Ancot+",r;"
						+ "VAT%,VAT,0,0,t,l,0;"
						+ "Thành tiền,TIEN_TRUOC_VAT,50,number,"+Ancot+",r;"
						+ "Thuế GTGT,THUEGTGT,40,number,f,r;"
						+ "Hàm lượng,LIEULUONG,40,0,f,r;"
						+ "Số lô,SO_LO,30,0,f,r;"
						+ "Hạn sử dụng,HSD,40,0,f,r;"
						+ "sl khả dụng,SL_KHADUNG,30,0,f,l,0;"
						+ "DONGIAVAT,DON_GIA_VAT,0,0,t,l,0;"
						+ "THANH_TIEN,THANH_TIEN,0,number,t,l,0;"
						+ "SO_DU,SO_DU,0,0,t,l,0;"
						+ "GIA_BHYT,GIABHYT,0,0,t,l,0;"
						+ "GIA_NHANDAN,GIANHANDAN,0,0,t,l,0;"
						+ "GIA_DICHVU,GIADICHVU,0,0,t,l,0;"
						+ "GIA_TRANBHYT,GIATRANBHYT,0,0,t,l,0;"
						+ "TONKHOCTID,TONKHOCTID,0,0,f,l,0;"
						+ "THUOCKHOYC,THUOCKHOYC,0,0,t,l,0;"
						+ "THUOCKHOCAP,THUOCKHOCAP,0,0,f,l,0;"
						+ "MARK,MARK,0,0,t,l,0;"
						+ "ACT,ACT,20,d,f,l,0",true,{
							rowNum : 1000,
							rowList : [ 1000, 3000 ]
						}); 
		bindEvent();
		getUserName();
		getTenKho();
		if($("#hidTRANGTHAIID").val() == "4"){
			_loadEditHT();
			
		}
		else{
			_loadcontrolgrid();
		}
		Number.prototype.format = function(n, x) {
			var re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\.' : '$')
					+ ')';

			var valueStr = this.toFixed(Math.max(0, ~~n)).replace(
					new RegExp(re, 'g'), '$&,');
			var last2 = valueStr.slice(-3);
			if (last2 == '.00')
				valueStr = valueStr.substring(0, valueStr.length - 3);
			return valueStr;
		};

		var sql_par1 = [];
		sql_par1.push({
			"name" : "[0]",
			"value" : _dsKhoCap
		});
		checkRole('cmdLuuPhieu');
		checkRole('cmdGuiYeuCau');
		checkRole('cmdXuatNCC');
		checkRole('cmdXoaPhieu');
		checkRole('cmdTuChoi');
		if(_param[0] =='10284')  document.getElementById("cmdXuatNCC").innerHTML = "Duyệt";
		if ($("#hid_NHAPXUATID").val() != "0") {
			loadNhapXuat($("#hid_NHAPXUATID").val());
			loadComboGrid();
			if($("#hidCopy").val()=='1')
			{$("#hid_NHAPXUATID").val('');
			$("#txtMA_PHIEU").val(layMaPhieu());
			$("#txtSO_CHUNG_TU").val(laySoChungTu());
			}
			if($("#hidTRANGTHAIID").val() == 1){
				  $("#cmdXoaPhieu").css("display", "");
			}else{
				$("#cmdXoaPhieu").css("display", "none");			
			}
			if((((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "VATTU" && _param[0] !='42346')
		    	|| (((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "THUOC" && _param[0] =='42346')){
				var _par = [$("#hid_NHAPXUATID").val()];
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC11N001.LOAITVT",
						_par.join('$'));
				if (result != -1){
					/*ComboUtil.getComboTag("cboLoaiThuoc", "DUC67T001.62", [ {
						"name" : "[0]",
						"value" : "1"
					} ], result, "");*/
					setTimeout(function(){ 
						$('#cboLoaiThuoc').val(result);
						loadComboGrid();
					}, 500);
					$('#cboLoaiThuoc').attr('disabled', 'disabled');
				}
					
			}
			
			// reloadCash();
		}  
		if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
			loadComboGrid();
		}

		if ($("#hid_KHOCAPID").val() == "0") {
			ComboUtil.getComboTag("cboKho", _selectKhoCungUng, sql_par1, "",
					"", "sql");
		} else {
			ComboUtil.getComboTag("cboKho", _selectKhoCungUng, sql_par1, $(
					"#hid_KHOCAPID").val(), "", "sql");

		}
		var _parimport = ['PHARMA_HIENTHI_IMPORT_PHIEUDT'];
		PHARMA_HIENTHI_IMPORT_PHIEUDT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",_parimport.join('$'));
		
		if(PHARMA_HIENTHI_IMPORT_PHIEUDT.includes($("#hid_HINHTHUCID").val()))
		{
			$("#divImport").show();
			showImport =1;
		} else $("#divImport").hide()
		
	
		
	}
	function bindEvent() {
		$('#btnFileTemp').on('click', function () {
		    window.location.href = '../upload/Import_YCThuoc.xls';
		});
		$('#btnImport').on('click', function () {
//			var attrLink = '/upload/fileSelectXLS_thuocvattu.jsp?action=uploadExcel&useFilter=false';
//		    window.open(window.location.pathname.substring(0, window.location.pathname.indexOf("/",2)) + attrLink + ".htm");
			var paramInput={							
			};
			var url2 = '/upload/fileSelectXLS_YCThuoc.jsp?action=uploadExcel&useFilter=false';
			url2 = window.location.pathname.substring(0, window.location.pathname.indexOf("/",2)) + url2
			console.log("open url2: "+ url2);
			var popup = DlgUtil.buildPopupUrl("dlgthuocvattu","divthuocvattu",url2,paramInput,"Tải file tài liệu",700,150);
			popup.open("dlgthuocvattu");
		});
	    $("#btnFresh").click(function(){
	    	if($("#cboKho").val() =='0') {
	    		DlgUtil.showMsg('Chưa chọn kho.');
	    		$("#cboKho").focus();
	    		return;
	    	}
	    	/*if($("#hidHoanTra").val()=='1') { 
	    		DlgUtil.showMsg('Chỉ có chức năng cho phiếu dự trù.'); 
	    		return;
	    	}*/
			var param = RSUtil.buildParam("", [$("#hid_LoaiKhoId").val(),  $("#cboKho").val() ]);
			GridUtil.loadGridBySqlPage('gridList_THUOC',"DUC.IMPORT.08",param);
		});
		GridUtil.setGridParam(_gridId1, {
			gridComplete : function() {
				var rowCount = $("#gridList_THUOC").getGridParam("reccount");
				if (rowCount > 0&& CHBoSung=='1' ) {
					var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
					for (var i = 0; i < rowIds.length; i++) {
						rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);
 
						if(rowData['THUOCKHOYC'] ==''){
							var _color='#FF9900';
							var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
							$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').each(function(index, element) {			        
								$(element).css('background-color', _color);
								$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').attr('title','Thuốc chưa có trong kho lập');
						    });
						}
						if(rowData['THUOCKHOCAP'] ==''){
							var _color='#FF9900';
							var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
							$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').each(function(index, element) {			        
								$(element).css('background-color', _color);
								$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').attr('title','Thuốc chưa có trong kho cung ứng');
						    });
						}
						if(Number(rowData['SL_KHADUNG'])< Number(rowData['SL_YC'])){
							 
							var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
							$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').attr('title','Thuốc hết hạn hoặc sắp hết hạn.');
						    $("#gridList_THUOC").jqGrid('setRowData', rowIds[i], "", {
								color : 'red'
							});
						}
					}
				}
			}
		});
		$.jMaskGlobals = {
			maskElements : 'input,td,span,div',
			dataMaskAttr : '*[data-mask]',
			dataMask : true,
			watchInterval : 300,
			watchInputs : true,
			watchDataMask : true,
			byPassKeys : [ 9, 16, 17, 18, 36, 37, 38, 39, 40, 91 ],
			translation : {
				'0' : {
					pattern : /\d/
				},
				'9' : {
					pattern : /\d/,
					optional : true
				},
				'#' : {
					pattern : /\d/,
					recursive : true
				},
				'A' : {
					pattern : /[a-zA-Z0-9]/
				},
				'S' : {
					pattern : /[a-zA-Z]/
				}
			}
		};

		var ngay_lap = "";
		var ngay_su_dung = "";
		ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
		if (ngay_lap == null || ngay_lap == "" ) {
			var d = new Date();
			var _month = (d.getMonth() + 1);
			var _date = d.getDate();
			if (_month < 10) {
				_month = '0' + _month;
			}
			if (_date < 10) {
				_date = '0' + _date;
			}
			ngay_lap = _date + "/" + _month + "/" + d.getFullYear();
		}
		$("#txtNGAY_LAP").val(ngay_lap);

		ngay_su_dung = ($("#txtNGAY_SU_DUNG").val()).substring(0, 10);
		if (ngay_su_dung == null || ngay_su_dung == "") {
			var d = new Date();
			var _month = (d.getMonth() + 1);
			var _date = d.getDate();
			if (_month < 10) {
				_month = '0' + _month;
			}
			if (_date < 10) {
				_date = '0' + _date;
			}
			ngay_su_dung = _date + "/" + _month + "/" + d.getFullYear();
		}
		$("#txtNGAY_SU_DUNG").val(ngay_su_dung);

		/*
		 * $(':input').keydown(function (e) { if (e.which === 13) { var index =
		 * $(':input').index(this) + 1; $(':input').eq(index).focus(); } });
		 */
		$('#txtMA_THUOC').change(function() {
			if($("#hidTHUOCVATTU").val() == ''){
				loadComboGrid();
			}
//			loadComboGrid();
			if ($("#hidTHUOCVATTU").val() == '') {
				// $("#txtMA_THUOC").val(row.MA_THUOC);
				$("#txtTEN_THUOC").val('');
				$("#hidTHUOCKHOYC").val('');
				$("#txtDON_VI_TINH").val('');
				//$("#txtVAT").val('');
				$("#txtSO_LUONG").val('0');
				$('#txtSO_LUONG').attr('disabled', 'disabled');
				// bachnv thay doi 
				//_Reset();
				
				$("#txtTEN_THUOC").val("");
				$("#txtDON_VI_TINH").val("");
				$("#txtSO_LUONG").val("0");
				$("#txtDON_GIA").val("0");
			//	$("#txtVAT").val("0");
				$("#txtDON_GIA_VAT").val("0");
				$("#txtTHANH_TIEN").val("0");
				$("#txtGIABAN").val("0");
				//
			}
		});
		$("#txtMA_PHIEU").val(layMaPhieu());
		if($("#hid_HINHTHUCID").val() =='1' && _param[0] =='993' && $("#hidLoaiNhapBu").val() == "XUATTRANCC_TK")
			$("#txtSO_CHUNG_TU").val(laySoChungTu());
		
		$(':input').keydown(function(e) {
			if (e.which === 13) {
				var index = $(':input').index(this) + 1;
				$(':input').eq(index).focus();
			}
		});
		$("#txtMA_THUOC").keydown(function(e) {
			if (e.which == 13) {
				$("#txtSO_LUONG").focus();
				$("#txtSO_LUONG").select();
			}
		});
		$('#txtSO_LUONG').on('click', function() {
			$("#txtSO_LUONG").select();

		});
		$('select').on('change', '', function(e) {
			
			if (($("#hid_HINHTHUCID").val() == "9" )) {
				$('#cmdButt').removeAttr("disabled");
				
			}
			loadComboGrid();

		});

		// Number($('#txtSO_LUONG').val()).format(2,3);
		$('#txtSO_LUONG').change(
				function() {
					$('#txtSO_LUONG').val(
							Number(replaceStrtoNum($('#txtSO_LUONG').val()))
									.format(3, 3));
					_isValidateThuoc();
					tinhTien();

				});
		// $('#tiendon').mask('#.###');
		$("#txtSO_LUONG")
				.keydown(
						function(e) {
							// Allow: backspace, delete, tab, escape, enter and
							// .
							if (e.which == 13) {
								$("#cmdOK").focus();
								$("#cmdOK").select();
							}
							if ($.inArray(e.keyCode, [ 46, 8, 9, 27, 13, 110,
									190 ]) !== -1
									||
									// Allow: Ctrl+A
									(e.keyCode == 65 && e.ctrlKey === true) ||
									// Allow: Ctrl+C
									(e.keyCode == 67 && e.ctrlKey === true) ||
									// Allow: Ctrl+X
									(e.keyCode == 88 && e.ctrlKey === true) ||
									// Allow: home, end, left, right
									(e.keyCode >= 35 && e.keyCode <= 39)) {
								// let it happen, don't do anything
								return;
							}
							// Ensure that it is a number and stop the keypress
							if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57))
									&& (e.keyCode < 96 || e.keyCode > 105)) {
								e.preventDefault();
							}

						});

		$('#cmdOK').on('click', function() {
			$("#txtMA_THUOC").focus();
			$("#txtMA_THUOC").select();

			var _isvalid = _isValidateThuoc();
			
			// kiem tra so luong dinh muc tu truc -- daidv
			var  PHARMA_CHECK_SOLUONG_DMTT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_CHECK_SOLUONG_DMTT'].join('$'));
			if((PHARMA_CHECK_SOLUONG_DMTT == '1' || PHARMA_CHECK_SOLUONG_DMTT == '2')  &&  $("#hid_HINHTHUCID").val() == "9" &&  $("#hidHoanTra").val()!='1'){
				if($("#hidTHUOCVATTU").val() != ""){			
					var _par_kho = {
							"ThuocVatTuid" : $("#hidTHUOCVATTU").val(),
							"Khoid" : $("#hid_LoaiKhoId").val()
						};
					var param_str_kho = JSON.stringify(_par_kho);
					var _par = param_str_kho + '$';
					
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC02N001.KTSLDM", _par);
					//var data = $.parseJSON(result);
					if (result != null && result.length > 0) {
						var row = result[0];
						if(PHARMA_CHECK_SOLUONG_DMTT == '1'){
							if(row.LOAIBUTT == '1' || row.LOAIBUTT == '2'){
								if(parseFloat(replaceStrtoNum($("#txtSO_LUONG").val())) > parseFloat(row.SLDINHMUC)){
									DlgUtil.showMsg('Số lượng nhập lớn hơn số lương định mức!');
									return ;
								}
							}	
						}else{
							if(parseFloat(replaceStrtoNum($("#txtSO_LUONG").val())) > parseFloat(row.SLDINHMUC)){
								DlgUtil.showMsg('Số lượng nhập lớn hơn số lương định mức!');
								return ;
							}
						}
						
					}
				}
			}

			if (_isvalid == '1') {
				if(parseFloat(replaceStrtoNum($("#txtSO_LUONG").val())) > parseFloat(_soLuongKhaDung) && $("#hid_HINHTHUCID").val() == "2" && PHARMA_YEUCAUDUTRU_VUOTKHADUNG == '1'){
					DlgUtil.showConfirm("Số lượng yêu cầu không được vượt quá số lượng khả dụng. Ban có muốn tiếp tục?",
							function(flag) {
								if (flag) {
									tinhTien();
									AddOrUpdateRowData();
									_Reset();
									$('#txtSO_LUONG').attr('disabled', 'disabled');
								}else{
									$("#txtSO_LUONG").val('0');
									_Reset();
								}
							});
				}else{
					tinhTien();
					AddOrUpdateRowData();
					_Reset();
					$('#txtSO_LUONG').attr('disabled', 'disabled');
				}
			}

		});
		$('#cmdButt').on('click', function() {
			
			loadDsThuocBuTT($("#hid_LoaiKhoId").val(),$("#cboKho").val());
			//loadDsThuocBuTT("422");
			$(document).ready(function() {
				var callback = function() {
					if ($.active !== 0) {
						setTimeout(callback, '500');
						return;
					}
					reloadCash();
	
				};
				callback();

			});
			
		});
		
		$('#ckbHSDTVT').change(function() {		
			if ($("#ckbHSDTVT").prop('checked') == true){
				$('#txtMA_THUOC').attr('disabled', 'disabled');
				loadDsThuocHetHSD($("#hid_LoaiKhoId").val(),$("#cboKho").val());
				//loadDsThuocBuTT("422");
				$(document).ready(function() {
					var callback = function() {
						if ($.active !== 0) {
							setTimeout(callback, '500');
							return;
						}
						reloadCash();
		
					};
					callback();

				});
			}else{
				$('#txtMA_THUOC').removeAttr("disabled");
				$('#gridList_THUOC').jqGrid('clearGridData');
				reloadCash();
			}
		});
		
		
		$('#cmdLuuPhieu').on('click', function() {
			var _isValid = _isValidateNhapKho();
			if (_isValid == '1') {
				
				if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
					if($("#hid_HINHTHUCID").val() =='1' && _param[0] =='993'){
						var _par = [ $("#txtSO_CHUNG_TU").val(),$("#hid_NHAPXUATID").val() ];
						var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC_CHECKSCT",
								_par.join('$'));
						if(result == '0'){	
						    	var maphieu = laySoChungTu();
						    	DlgUtil.showMsg('Số chứng từ '+ $("#txtSO_CHUNG_TU").val() + ' đã tồn tại ;thay số chứng từ mới: ' + maphieu);
								$("#txtSO_CHUNG_TU").val(maphieu);
								return;
						}
					}
					if ($("#hidTRANGTHAIID").val() == "4"){
						EditPhieuHT();
					}else{
						LuuphieuXuatNcc();	
					}
				} else {
					var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
							_par.join('$'));
					if(result == '0'){	
					    	var maphieu = layMaPhieu();
					    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
							$("#txtMA_PHIEU").val(layMaPhieu());
							
					}
				
					guiYeuCau("1");
				}

			}
		});

		$('#cmdGuiYeuCau').on('click', function() {
			var _isValid = _isValidateNhapKho();
			if (_isValid == '1') {
               if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK"){
            	   GuiYCXuatNcc();
            	   }
               else {
            	   guiYeuCau("5"); 
               }
				
			}

		});
		
		$('#cmdCanDoi').on('click', function() {
			if ($("#hid_NHAPXUATID").val() != "0"){
				
				var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC11N001.KT.BUTT",$("#hid_NHAPXUATID").val()));
				
				if( ret.SUCCESS > 0){
					DlgUtil.showMsg("Đã thực hiện cân đối số liệu trong phiếu.");
					var _par = RSUtil.buildParam("", [ $("#hid_NHAPXUATID").val() ]);
					GridUtil.loadGridBySqlPage(_gridId1, "DUC11N001.CANDOIBUTT", _par);
					GridUtil.setGridParam(_gridId1, {
						gridComplete : function() {
							var rowCount = $("#gridList_THUOC").getGridParam("reccount");
							if (rowCount > 0) {

								var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
								for (var i = 0; i < rowIds.length; i++) {
									rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);
									if(rowData['MARK'] == '1'){
										var _color='#ffff99';
										var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
										$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').each(function(index, element) {			        
											$(element).css('background-color', _color);
									    });
									}else if(rowData['MARK'] == '2'){
										var _color='#ff9980';
										var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
										$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').each(function(index, element) {			        
											$(element).css('background-color', _color);
									    });
									}
								}		
							}
						}
					});
				}else{
					DlgUtil.showMsg(ret.MESSAGE);
				}
			}

		});
		
		$('#cmdDong').on('click', function(e) {
			EventUtil.raiseEvent("YCNhap_success", {
				ncc : 'YC_NHAP',
				id : "0"
			});

		});

		$('#cmdXuatNCC').on('click', function(e) {
			var _isValid = _isValidateNhapKho();
			if (_isValid == '1') {
				TraKho();
			}
		});
     $('#cmdXoaPhieu').on('click', function(e) {
    	 if($("#hid_NHAPXUATID").val()=='0' || $("#hid_NHAPXUATID").val()=='0'){
             DlgUtil.showMsg('Chưa có phiếu'); return;
    	 }
			DlgUtil
			.showConfirm("Bạn có chắc chắn muốn Hủy phiếu không ?",
					function(flag) {
						if (flag) {
							
							var _par = [ parseFloat($("#hid_NHAPXUATID").val()) ];
							var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.XOAPHIEU", _par
									.join('$'));
							if (result == '0') {
								DlgUtil.showMsg('Hủy phiếu lỗi');
							} else {
								// DlgUtil.showMsg('Nhập kho thành công');
								// EventUtil.raiseEvent("nhapkho_success",{ncc:'NHAP_KHO',id:$("#hid_NHAPXUATID").val()});

								DlgUtil.showMsg('Hủy phiếu thành công !', function() {
									EventUtil.raiseEvent("YCNhap_success", {
										ncc : 'NHAP_KHO',
										id : $("#hid_NHAPXUATID").val()
									});
								});
							}
							
						}

					});
			

		});
     
     $('#cmdTuChoi').on('click', function(e) {			
			DlgUtil
			.showConfirm("Bạn có chắc chắn muốn từ chối không ?",
					function(flag) {
						if (flag) {
							
							var _par = [ parseFloat($("#hid_NHAPXUATID").val()) ];
							var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC11N001.20", _par
									.join('$'));
							if (result == '0') {
								DlgUtil.showMsg('Từ chối phiếu lỗi');
							} else {
								// DlgUtil.showMsg('Nhập kho thành công');
								// EventUtil.raiseEvent("nhapkho_success",{ncc:'NHAP_KHO',id:$("#hid_NHAPXUATID").val()});

								DlgUtil.showMsg('Từ chối thành công !', function() {
									EventUtil.raiseEvent("YCNhap_success", {
										ncc : 'NHAP_KHO',
										id : $("#hid_NHAPXUATID").val()
									});
								});
							}
							
						}

					});
			

		});
     
     // Hien thi danh sach thuoc tuong duong trong kho
     $("#cmdBUHCTD").on("click",function(e){
			/*EventUtil.setEvent("YCNhap_success",function(e){
				DlgUtil.close("dlgYCNhap");
				_loadDSPhieu();
			});*/
			var myVar={khoycid:$("#hid_LoaiKhoId").val(), khoid: $("#cboKho").val(),thuocvattuid: $("#hidTHUOCVATTU").val()};
			
			dlgPopup=DlgUtil.buildPopupUrl("dlgYCThuocTD","divDlg","manager.jsp?func=../duoc/DUC67T006_DSThuocTuongDuong",myVar,"Thuốc tương đương",800,510);
			DlgUtil.open("dlgYCThuocTD");
			
		});

		GridUtil.setGridParam(_gridId1, {
			onSelectRow : function(id) {
				if (id) {
					loadRowSelectThuoc(id);
					if($("#hidTRANGTHAIID").val() != "4"){
						$('#txtSO_LUONG').removeAttr("disabled");
						$('#cmdOK').removeAttr("disabled");
						
					}
				}
			}
		});
		
	}
	
	$("#cboKho").on("change",function(e){
		//_loadDSPhieu();
		var rowCount = $("#gridList_THUOC").getGridParam("reccount");
		if(rowCount > 0){
			if(confirm('Nếu chọn Kho cấp khác, Thì danh sách thuốc vật tư sẽ bị Xóa hết. Bạn có muốn tiếp tục không ?')){
				$("#gridList_THUOC").jqGrid("clearGridData", true).trigger("reloadGrid");
				}else{
					$("#cboKho").val($("#hid_khoid_old").val());
				}
		}
		$("#hid_khoid_old").val($("#cboKho").val());
		if($("#ckbHSDTVT").prop('checked') == true){
			loadDsThuocHetHSD($("#hid_LoaiKhoId").val(),$("#cboKho").val());	
			$(document).ready(function() {
				var callback = function() {
					if ($.active !== 0) {
						setTimeout(callback, '500');
						return;
					}
					reloadCash();
	
				};
				callback();

			});
		}
	});
	
	$("#cboLoaiThuoc").on("change",function(e){
		if((((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "VATTU" && _param[0] !='42346')
		    	|| (((show_cbLoaiTVT != '' || jQuery.type(show_cbLoaiTVT)!== "undefined" ) && show_cbLoaiTVT.includes($("#hid_HINHTHUCID").val())) && $("#hidHoanTra").val()!='1' && $("#hid_LOAIGIAODIEN").val() == "THUOC" && _param[0] =='42346')){
			$('#gridList_THUOC').jqGrid('clearGridData');
			reloadCash();
		}
	});
	
	function _loadcontrolgrid() {
		$("#" + _gridId1).bind(
				"CustomAction",
				function(e, act, rid) {
					if(PHARMA_CHECK_DEL_TVT_BUTT == '1' && $("#hid_HINHTHUCID").val() == "9"){
						var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC11N001.CHECKBUTT",$("#hid_NHAPXUATID").val());
						if(ret > 0){
							DlgUtil.showMsg('Phiếu lĩnh bù. Không thể xóa thuốc');
							return;
						}
					}
					DlgUtil
							.showConfirm("Bạn có muốn xóa bản ghi này ko ?",
									function(flag) {
										if (flag) {
											$('#' + _gridId1).jqGrid(
													'delRowData', rid);
											$("#tiendon").text("");
											$("#tongcong").text("");
											$('#txtSO_LUONG').attr('disabled',
													'disabled');
											reloadCash();
											_Reset();

											return true;
										}

									});
					// console.log("CustomAction act="+act+" rid="+rid);
					// return true;

				});

		$('#cmdAddNhaCungCap').on(
				'click',
				function(e) {

					dlgPopup = DlgUtil.buildPopupUrl("dlgNhaCungCap", "divDlg",
							"manager.jsp?func=../duoc/DUC_NHACUNGCAP", "",
							"Thêm mới nhà cung cấp", 600, 200);

					dlgPopup.open("dlgNhaCungCap");

				});
		EventUtil.setEvent("nhacungcap_cancel", function(e) {
			DlgUtil.close("dlgNhaCungCap");
			loadComboGridNCC();

		});

		EventUtil.setEvent("nhacungcap_success", function(e) {
			DlgUtil.close("dlgNhaCungCap");
			loadComboGridNCC();

			$("#txtNhaCungCap").val(e.ten);
			$("#hid_NHACUNGCAPID").val(e.nccid);
			$("#txtDIA_CHI").val(e.diachi);

		});
		
		EventUtil.setEvent("thuoctd_success",function(e){
			DlgUtil.close("dlgYCThuocTD");
			flag_thuoctd = $("#hidTHUOCVATTU").val();
			
			loadEditorData(e.thuocvattuid, "1");
			
			
		});
	}
	function _loadEditHT() {
		
		
		$("#cmdGuiYeuCau").css("display", "none");
		$('#cmdNhapKho').css("display", "none");	
		$("#cmdXuatNCC").css("display", "none");
		
		
		$("#txtMA_THUOC").attr('readonly', true);
	
	


	}
	// load thuoc sau khi tim kiem o muc ma thuoc
	function loadEditorData(_keyField, isAddNew) {
		// var sql = _formSQL;

		var sql_par = [];

		if (($("#hid_HINHTHUCID").val() == "9" && $("#hidLoaiPhieu").val() == "2")
				|| $("#hidHoanTra").val() == '1') {
			sql_par = [ _keyField, $("#hid_LoaiKhoId").val(),"" ];
		} else if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
			sql_par = [ _keyField, $("#hid_LoaiKhoId").val(),"NCC" ];
		} else {
			sql_par = [ _keyField, $("#cboKho").val(),"" ];
		}
		// var sql_par=_keyField+'$';
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC11N001.01", sql_par
				.join('$'));
		if (data_ar != null && data_ar.length > 0) {

			var row = data_ar[0];

			// sql_par = setSysParam(sql_par);
			// var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO(sql, sql_par);

			// var rows = JSON.parse(data_ar);

			$("#txtMA_THUOC").val(row.MA_THUOC);
			$("#txtTEN_THUOC").val(row.TEN_THUOC);
			$("#txtDON_VI_TINH").val(row.DON_VI_TINH);
			$("#hidTHUOCVATTU").val(_keyField);
			$("#h_donvitinhid").val(row.DONVITINHID);
			$("#hidLieuLuong").val(row.LIEULUONG);
			if($("#hid_HINHTHUCID").val() != "1" || _param[0] !='10284'){
				$("#txtDON_GIA").val(Number(row.DON_GIA).format(6, 3));
				$("#txtDON_GIA_VAT").val(Number(row.GIA_BAN).format(2, 3));
				$("#txtVAT").val(row.VAT);
			}
			
			
			// $("#txtDON_GIA_VAT").val(Number(row.DON_GIA_VAT).format(2,3));
			 $("#hid_giaBHYT").val(row.GIABHYT);
			 $("#hid_gianhandan").val(row.GIANHANDAN);
			 $("#hid_giadichvu").val(row.GIADICHVU);
			 $("#hid_giatranBHYT").val(row.GIATRANBHYT);
			$('#txtSO_LUONG').removeAttr("disabled");
			$('#cmdOK').removeAttr("disabled");
			tinhTien();
			_soLuongKhaDung = row.KHA_DUNG;

		} else {
			if (isAddNew != null && isAddNew != '0') {
				DlgUtil.showMsg("Không có dữ liệu");
			}
			return;
		}

		// GridUtil.loadGridBySqlPage(_gridId1, _thuocSQL, sql_par);

	}
	// hien thi danh sach thuoc khi nhap trong ma thuoc
	function loadComboGrid() {
		var _col = "";		
		if(PHARMA_LOADGRID_DUC11N001 == '0' || PHARMA_LOADGRID_DUC11N001 == null){
			if(PHARMA_BOSUNG_THEOLO == '1' ||($("#hid_HINHTHUCID").val() == "1" && _param[0] =='10284')  ){
				 _col = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
						//	+ "Đơn giá,DON_GIA,0,0,t,l;" + "VAT,VAT,0,0,t,l;"
							+ "Mã thuốc,MA_THUOC,10,0,f,l;"
							+ "Tên thuốc,TEN_THUOC,15,0,f,l;"
							+ "Hoạt chất,HOATCHAT,12,0,f,l;" 
							+ "Hàm lượng,LIEULUONG,7,0,f,l;"						
							+ "Đơn giá,DON_GIA,7,0,"+Ancot+",l;"
							+ "ĐVT,DON_VI_TINH,5,0,f,l;"
							+ "Số lô,SOLO,7,0,f,l;"
							+ "Hạn dùng,HSD,8,0,f,l;"
							+ "Tồn kho,TON_KHO,7,0,f,l;" 
							+ "Khả dụng,KHA_DUNG,7,0,f,l;"
							+ "Gói thầu,GOITHAU,7,0,f,l;"
							+ "QĐ thầu,MATHAU,7	,0,f,l;"
				           	//+ "Nước sx,NUOCSANXUAT,7,0,f,l;"
				           	+ "THUOCKHOYC,THUOCKHOYC,8,0,t,l;"
				           	+ "TONKHOCTID,TONKHOCTID,0,0,t,l";
							;
			}
			else{
				 _col = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
						//	+ "Đơn giá,DON_GIA,0,0,t,l;" + "VAT,VAT,0,0,t,l;"
							+ "Mã thuốc,MA_THUOC,11,0,f,l;"
							+ "Tên thuốc,TEN_THUOC,18,0,f,l;"
							+ "Hoạt chất,HOATCHAT,8,0,f,l;" 
							+ "Hàm lượng,LIEULUONG,8,0,f,l;"
							+ "Gói thầu,GOITHAU,8,0,f,l;"
							+ "QĐ thầu,MATHAU,7,0,f,l;"
							+ "Đơn giá,DON_GIA,7,0,"+Ancot+",l;"
							+ "ĐVT,DON_VI_TINH,5,0,f,l;"
							+ "Đóng gói,DONGGOI,0,0,t,l;"
							+ "Hàm lượng,LIEULUONG,8,0,t,l;"
							+ "CS tủ trực,SLTT,7,0,f,l;"
							+ "Tồn kho,TON_KHO,5,0,f,l;" 
							+ "Khả dụng,KHA_DUNG,6,0,f,l;"
				           	+ "Nước sx,NUOCSANXUAT,8,0,f,l;"
				           	+ "THUOCKHOYC,THUOCKHOYC,8,0,t,l"
							;
			}
			if(((PHARMA_XUATTRAMAU_NCC_BARCODE != '' || jQuery.type(PHARMA_XUATTRAMAU_NCC_BARCODE)!== "undefined" ) 
					&& PHARMA_XUATTRAMAU_NCC_BARCODE.includes(maKho_BarcodeMau)) && $("#hidLoaiNhapBu").val() == "XUATTRANCC_TK"){
				_col = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
					//	+ "Đơn giá,DON_GIA,0,0,t,l;" + "VAT,VAT,0,0,t,l;"
						+ "Mã thuốc,MA_THUOC,11,0,f,l;"
						+ "Tên thuốc,TEN_THUOC,18,0,f,l;"
						+ "Hoạt chất,HOATCHAT,8,0,f,l;" 
						+ "Hàm lượng,LIEULUONG,8,0,f,l;"
						//+ "Gói thầu,GOITHAU,8,0,f,l;"
						//+ "QĐ thầu,MATHAU,7,0,f,l;"
						+ "BarCode,BARCODE,8,0,f,l;"
						+ "Đơn giá,DONGIAVAT,7,0,"+Ancot+",l;"
						+ "ĐVT,DON_VI_TINH,5,0,f,l;"
						+ "Đóng gói,DONGGOI,0,0,t,l;"
						+ "Hàm lượng,LIEULUONG,8,0,t,l;"
						//+ "CS tủ trực,SLTT,7,0,f,l;"
						+ "Tồn kho,TON_KHO,5,0,f,l;" 
						+ "Khả dụng,KHA_DUNG,6,0,f,l;"
			           	//+ "Nước sx,NUOCSANXUAT,8,0,f,l;"
			           	+ "THUOCKHOYC,THUOCKHOYC,8,0,t,l;"
			           	+ "TONKHOCTID,TONKHOCTID,0,0,t,l"
						;
			}
		}
		else{
			_col = PHARMA_LOADGRID_DUC11N001;			
		}
		
		
		var _khoCapId = 0;
		if ($("#hid_KHOCAPID").val() == "0") {
			_khoCapId = $("#cboKho").val();
		} else {
			_khoCapId = $("#hid_KHOCAPID").val();
		}
		var _khoYeuCauId = $("#hid_LoaiKhoId").val();
		var sql_par = [];
		if (($("#hid_HINHTHUCID").val() == "9" && $("#hidLoaiPhieu").val() == "2")
				|| $("#hidHoanTra").val() == '1') {
			sql_par.push({
				"name" : "[0]",
				"value" : _khoCapId
			});
			sql_par.push({
				"name" : "[1]",
				"value" : _khoYeuCauId
			});
		} else if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
			sql_par.push({
				"name" : "[0]",
				"value" : _khoYeuCauId
			});
			sql_par.push({
				"name" : "[1]",
				"value" : _khoYeuCauId
			});
		} else {
			sql_par.push({
				"name" : "[0]",
				"value" : _khoYeuCauId
			});
			sql_par.push({
				"name" : "[1]",
				"value" : _khoCapId
			});
		}
		// put loai thuoc hay vat tu

		/*
		 * if ($("#hid_LOAIGIAODIEN").val() == "VATTU") { sql_par.push({ "name" :
		 * "[0]", "value" : _khoYeuCauId }); sql_par.push({ "name" : "[1]",
		 * "value" : _khoCapId }); } // put loai thuoc hay vat tu
		 *  /* if ($("#hid_LOAIGIAODIEN").val() == "VATTU") { sql_par.push({
		 * "name" : "[2]", "value" : 1 }); sql_par.push({ "name" : "[3]",
		 * "value" : 2 }); } else if ($("#hid_LOAIGIAODIEN").val() == "THUOC") {
		 * sql_par.push({ "name" : "[2]", "value" : 0 }); sql_par.push({ "name" :
		 * "[3]", "value" : 3 }); }
		 */

		$("#hidTHUOCVATTU").val("");

		/*var _selfnc = function(event, ui) {
			$("#txtMA_THUOC").val(ui.item.MA_THUOC);
			$("#hidTHUOCVATTU").val(ui.item.THUOCVATTUID);
			$("#txtTEN_THUOC").val(ui.item.TEN_THUOC);
			$("#txtDON_GIA").val(ui.item.DON_GIA);
			//$("#txtVAT").val(ui.item.VAT);
			$("#hidHoatChat").val(ui.item.HOATCHAT);

			_objDrugTemp = [];
			loadEditorData(ui.item.THUOCVATTUID, "1");

			return false;
		};*/
		/*ComboUtil.initComboGrid(FormUtil.escape("txtMA_THUOC"), "DUC11N001.08",
				sql_par, "780px", _col, _selfnc);
		*/
		// chuannt sua lai loadcombogrid moi
		var sql = 'DUC11N001.08';
		if(loaiThuoc =='1') {
			sql_par.push({
				"name" : "[2]",
				"value" : $("#cboLoaiThuoc").val()
			});
			sql_par.push({
				"name" : "[3]",
				"value" : $("#cboLoaiThuoc").val()
			});
			sql = 'DUC11N001.08.02';
		}
//		if (($("#hid_HINHTHUCID").val() == "9"  || $("#hid_HINHTHUCID").val() == "2") && CHBoSung =='1')
		if(CHBoSung=='1')
			sql = 'DUC11N001.08.01';
		
		if(PHARMA_BOSUNG_THEOLO == '1' ||($("#hid_HINHTHUCID").val() == "1" && _param[0] =='10284') ){sql = 'DUC11N001.08.03';}
		if(((PHARMA_XUATTRAMAU_NCC_BARCODE != '' || jQuery.type(PHARMA_XUATTRAMAU_NCC_BARCODE)!== "undefined" ) 
				&& PHARMA_XUATTRAMAU_NCC_BARCODE.includes(maKho_BarcodeMau)) && $("#hidLoaiNhapBu").val() == "XUATTRANCC_TK"){
			sql = 'DUC02N001.03.01';
		}
		if(PHARMA_LOADGRID_DUC11N001 != '0' && PHARMA_LOADGRID_DUC11N001 != null){sql = 'DUC11N001.08.06';}
		if(PHARMA_LOADGRID_DUC11N001.includes("DUONG_DUNG")){
			sql = 'DUC11N001.08.DD01';
		}
		ComboUtil.initComboGrid(FormUtil.escape("txtMA_THUOC"), sql, sql_par, "1100px", _col, function(event, ui){
			
			$("#txtMA_THUOC").val(ui.item.MA_THUOC);
			$("#hidTHUOCVATTU").val(ui.item.THUOCVATTUID);
			$("#txtTEN_THUOC").val(ui.item.TEN_THUOC);
			$("#txtDON_GIA").val(ui.item.DON_GIA);
			$("#txtDON_GIA_VAT").val(ui.item.DON_GIA);
			 $("#txtVAT").val(ui.item.VAT);
			$("#hidHoatChat").val(ui.item.HOATCHAT);
			if(PHARMA_BOSUNG_THEOLO == '1' || ($("#hid_HINHTHUCID").val() == "1" && _param[0] =='10284') ){
				$("#hidHSD").val(ui.item.HSD);
				$("#hidSOLO").val(ui.item.SOLO);
				$("#hidTonKhoCTID").val(ui.item.TONKHOCTID);
			}
			if(((PHARMA_XUATTRAMAU_NCC_BARCODE != '' || jQuery.type(PHARMA_XUATTRAMAU_NCC_BARCODE)!== "undefined" ) 
					&& PHARMA_XUATTRAMAU_NCC_BARCODE.includes(maKho_BarcodeMau)) && $("#hidLoaiNhapBu").val() == "XUATTRANCC_TK"){
				$("#hidBARCODE").val(ui.item.BARCODE);
				$("#hidTonKhoCTID").val(ui.item.TONKHOCTID);
			}
			if(CHBoSung=='1')
				$("#hidTHUOCKHOYC").val(ui.item.THUOCKHOYC);
			
			//_objDrugTemp = [];
			loadEditorData(ui.item.THUOCVATTUID, "1");

			return false;
		});
	}
	// nhập nhà cung cấp
	function loadComboGridNCC() {
		var _col = "NHACUNGCAPID,NHACUNGCAPID,0,0,t,l;"
				+ "Nhà cung cấp,TEN,40,0,f,l;"

				+ "Địa chỉ,DIACHI,50,0,f,l";

		var _sql = "DUC04D001.05";

		// +" And Tvt.Thuocvattuid = Tk.Thuocvattuid(+)"
		// +" And Tk.Khoid = [0] AND tvt.csytid = 3";
		// var _sql_par=[];
		// _sql_par.push({"name":"[0]","value":_kho_id},{"name":"[1]","value":_company_id});
		$("#hid_NHACUNGCAPID").val("0");
		var _selfnc = function(event, ui) {
			$("#txtNhaCungCap").val(ui.item.TEN);
			$("#hid_NHACUNGCAPID").val(ui.item.NHACUNGCAPID);
			$("#txtDIA_CHI").val(ui.item.DIACHI);

			return false;
		};

		ComboUtil.initComboGrid(FormUtil.escape("txtNhaCungCap"), _sql, [ "" ],
				"850px", _col, _selfnc);
	}

	function setSysParam(_par_ar) {
		var v_par = _par_ar;
		for (var i1 = 0; i1 < _param.length; i1++) {
			console.log('_param[' + i1 + ']=' + _param[i1]);
			v_par.push({
				"name" : "[S" + i1 + "]",
				"value" : _param[i1]
			});
		}
		return v_par;
	}
	// them/ sua thuoc trong danh sach thuoc da chon
	function AddOrUpdateRowData() {
		/*if (flag_thuoctd == 0) {
			$('input[id*="gs_"]').val("");
			$('select[id*="gs_"]').val("ALL");
			$("#gridList_THUOC").jqGrid('setGridParam', {
				search : false,
				postData : {
					"filters" : ""
				}
			}).trigger("reloadGrid");
		}*/
		var hi_thuocid = '0';
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		for (var k = 0; k < rowIds.length; k++) {
			var rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[k]);
			hi_thuocid = rowData.THUOCVATTUID;
			/*if(flag_thuoctd == hi_thuocid){
				//$("#hidTHUOCVATTU").val(rowData.THUOCVATTUID);
				$("#hidAddOrUpdate").val(rowIds[k]);
				hi_thuocid = '-1';
				flag_thuoctd = 0;
				break;
			}*/
			if(flag_thuoctd > 0){
                if(flag_thuoctd == hi_thuocid){
				//$("#hidTHUOCVATTU").val(rowData.THUOCVATTUID);
					$("#hidAddOrUpdate").val(rowIds[k]);
					hi_thuocid = '-1';
					flag_thuoctd = 0;
					break;
			    }
			}else{	
				if(PHARMA_BOSUNG_THEOLO == '1' || ($("#hid_HINHTHUCID").val() == "1" && _param[0] =='10284') || (((PHARMA_XUATTRAMAU_NCC_BARCODE != '' || jQuery.type(PHARMA_XUATTRAMAU_NCC_BARCODE)!== "undefined" ) 
						&& PHARMA_XUATTRAMAU_NCC_BARCODE.includes(maKho_BarcodeMau)) && $("#hidLoaiNhapBu").val() == "XUATTRANCC_TK")){				
					var tonkhoctid = rowData.TONKHOCTID;
					if (tonkhoctid == $("#hidTonKhoCTID").val()) {
						$("#hidAddOrUpdate").val(rowIds[k]);
						break;
					} else {
						hi_thuocid = '0';
					}
				}
				else{
					if (hi_thuocid == $("#hidTHUOCVATTU").val()) {

						$("#hidAddOrUpdate").val(rowIds[k]);
						break;
					} else {
						hi_thuocid = '0';
					}
				}
			}
			

		}
		var _hoatchat ='';
		if($("#hidHoatChat").val() !='&nbsp'){
			_hoatchat = $("#hidHoatChat").val();
		}
		if (hi_thuocid == '0') {

			var rowCount = $("#gridList_THUOC").getGridParam("reccount");

			var datarow = {
				THUOCVATTUID : $("#hidTHUOCVATTU").val(),
				MA_THUOC : $("#txtMA_THUOC").val(),
				TEN_THUOC : $("#txtTEN_THUOC").val(),
				DON_VI_TINH : $("#txtDON_VI_TINH").val(),
				LIEULUONG : $("#hidLieuLuong").val(),
				SL_YC : replaceStrtoNum($("#txtSO_LUONG").val()),
				HOATCHAT : _hoatchat,
				DON_GIA : replaceStrtoNum($("#txtDON_GIA").val()),
				THUEGTGT :replaceStrtoNum(Number(replaceStrtoNum($("#txtDON_GIA").val()) * 
			    		   parseFloat($("#txtVAT").val())* replaceStrtoNum($("#txtSO_LUONG").val())/100).format(3,3)),
				DON_GIA_VAT : replaceStrtoNum($("#txtDON_GIA_VAT").val()),
				VAT : $("#txtVAT").val(),
				THANH_TIEN : replaceStrtoNum($("#txtTHANH_TIEN").val()),
				TIEN_TRUOC_VAT : replaceStrtoNum($("#hid_thanhtientruocvat").val()),
				SL_KHADUNG : _soLuongKhaDung,
				GIABHYT : $("#hid_giaBHYT").val(),
				GIANHANDAN : $("#hid_gianhandan").val(),
				GIADICHVU : $("#hid_giadichvu").val(),
				GIATRANBHYT : $("#hid_giatranBHYT").val(),
				DONVITINHID : $("#h_donvitinhid").val(),
				THUOCKHOYC :$("#hidTHUOCKHOYC").val(),
				HSD :$("#hidHSD").val(),
				SO_LO :$("#hidSOLO").val(),
				BARCODE :$("#hidBARCODE").val(),
				TONKHOCTID :$("#hidTonKhoCTID").val()
			};
			$('#gridList_THUOC').jqGrid('addRowData', rowCount + 1, datarow);
			if(CHBoSung=='1' && $("#hidTHUOCKHOYC").val()==''){
				var _color='#FF9900';
				var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
//				$("#gridList_THUOC").jqGrid('setRowData', rowIds[rowCount], "", {
//					color : 'red'
//				});
				$('#gridList_THUOC').find("tr[id='" + rowIds[rowCount] + "']").find('td').each(function(index, element) {			        
					$(element).css('background-color', _color);
					$('#gridList_THUOC').find("tr[id='" + rowIds[rowCount] + "']").find('td').attr('title','Thuốc chưa có trong kho lập');
			    });
			}
			reloadCash();
			$("#hidAddOrUpdate").val(rowCount + 1);
		}else if(hi_thuocid == '-1'){
			var item_id = $("#hidAddOrUpdate").val();
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THUOCVATTUID',
					$("#hidTHUOCVATTU").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'MA_THUOC',
					$("#txtMA_THUOC").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'TEN_THUOC',
					$("#txtTEN_THUOC").val());
			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_YC',
					replaceStrtoNum($("#txtSO_LUONG").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'HOATCHAT',
					_hoatchat);
			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'LIEULUONG',
					$("#hidLieuLuong").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA',
					replaceStrtoNum($("#txtDON_GIA").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'VAT',
					$("#txtVAT").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THANH_TIEN',
					replaceStrtoNum($("#txtTHANH_TIEN").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'TIEN_TRUOC_VAT',
					replaceStrtoNum($("#hid_thanhtientruocvat").val()));			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_KHADUNG',
					_soLuongKhaDung);
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA_VAT',
					replaceStrtoNum($("#txtDON_GIA_VAT").val()));
			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THUEGTGT',
					replaceStrtoNum(Number(parseFloat($("#txtDON_GIA").val()) * 
						    		   parseFloat($("#txtVAT").val())* parseFloat($("#txtSO_LUONG").val())/100).format(3,3))
						    		   );
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIABHYT',
					$("#hid_giaBHYT").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIANHANDAN',
					 $("#hid_gianhandan").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIADICHVU',
					$("#hid_giadichvu").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIATRANBHYT',
					$("#hid_giatranBHYT").val());
			
			reloadCash();
		} 
		else {
			var item_id = $("#hidAddOrUpdate").val();
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_YC',
					replaceStrtoNum($("#txtSO_LUONG").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'HOATCHAT',
					_hoatchat);
			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'LIEULUONG',
					$("#hidLieuLuong").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA',
					replaceStrtoNum($("#txtDON_GIA").val()));
//			$('#gridList_THUOC').jqGrid('setCell', item_id, 'VAT',
//					$("#txtVAT").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THANH_TIEN',
					replaceStrtoNum($("#txtTHANH_TIEN").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_KHADUNG',
					_soLuongKhaDung);
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA_VAT',
					replaceStrtoNum($("#txtDON_GIA_VAT").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THUEGTGT',
					replaceStrtoNum(Number(replaceStrtoNum($("#txtDON_GIA").val()) * 
				    		   parseFloat($("#txtVAT").val())* replaceStrtoNum($("#txtSO_LUONG").val())/100).format(3,3))
				    		   );
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA_VAT',
							replaceStrtoNum($("#txtDON_GIA").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIABHYT',
					$("#hid_giaBHYT").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIANHANDAN',
					 $("#hid_gianhandan").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIADICHVU',
					$("#hid_giadichvu").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIATRANBHYT',
					$("#hid_giatranBHYT").val());
			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'HSD',
					$("#hidHSD").val());			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SO_LO',
					$("#hidSOLO").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'BARCODE',
					$("#hidBARCODE").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'TONKHOCTID',
					$("#hidTonKhoCTID").val());
			
			reloadCash();
		}
	}
	// tinh toan lai so tien tung loai thuoc
	function reloadCash() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		var sumAll = 0;
		var thuegtgt = 0;
		for (var k = 0; k < rowIds.length; k++) {
			var rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[k]);

			var sumPrice = 0;
			if(rowData.THANH_TIEN =='') rowData.THANH_TIEN ='0';

			sumPrice = parseFloat(rowData.THANH_TIEN);

			sumAll = (parseFloat(sumAll) + parseFloat(sumPrice)).toFixed(2);
			 thuegtgt = thuegtgt + (parseFloat(rowData.DON_GIA) * parseFloat(rowData.VAT)* parseFloat(rowData.SL_YC)/100);

		}
		var thuegtgt2 = parseFloat(thuegtgt.toFixed(2));
		// $("#h_thanhtoantong").val(sumAll);
		$("#tiendon").text(Number(sumAll - thuegtgt2).format(2, 3) + 'đ');
		$("#tiendon").val(sumAll - thuegtgt2);
		$("#tongcong").text(Number(sumAll).format(2, 3) + 'đ');
		$("#thuegtgt").text(thuegtgt2.format(2, 3, ',') + 'đ');
		$("#thuegtgt").val(thuegtgt2);
		//$("#tiendon").val(sumAll);
		$("#tongcong").val(sumAll);

	}
	// validate so luong thuoc da nhap
	function _isValidateThuoc() {
		var kq = '0';
		
		if(PHARMA_BOSUNG_THEOLO == '1' && PHARMA_CHECK_HSD_CHUYENKHO == '0' && $("#hid_HINHTHUCID").val() != '1'){
			var d = new Date();
			var _month = (d.getMonth() + 1);
			var _date = d.getDate();
			if (_month < 10) {
				_month = '0' + _month;
			}
			if (_date < 10) {
				_date = '0' + _date;
			}
			var strDate = _date + "/" + _month + "/" + d.getFullYear();
			var ngayht = moment(strDate, 'DD/MM/YYYY');
			var hsd = moment($("#hidHSD").val(), 'DD/MM/YYYY');
			if (hsd <= ngayht) {
				DlgUtil.showMsg("Hạn dùng của Lô này đã hết hạn !");			
				kq = '0';
				return kq;
			 }
	    }

		if (parseFloat(replaceStrtoNum($("#txtSO_LUONG").val())) <= 0) {
			DlgUtil.showMsg('Số lượng phải lớn hơn không !');
			$("#txtSO_LUONG").val('0');
			$("#txtSO_LUONG").val('0');
			kq = 0;
		} else if (parseFloat(replaceStrtoNum($("#txtSO_LUONG").val())) > parseFloat(_soLuongKhaDung)) {
			if(PHARMA_YEUCAUDUTRU_VUOTKHADUNG == '0'){
				DlgUtil.showMsg('Số lượng yêu cầu không được vượt quá số lượng khả dụng !');
				$("#txtSO_LUONG").val('0');
				kq = 0;
			}else{
				if($("#hid_HINHTHUCID").val() == "2"){
					kq = 1;
				}else{
					DlgUtil.showMsg('Số lượng yêu cầu không được vượt quá số lượng khả dụng !');
					$("#txtSO_LUONG").val('0');
					kq = 0;
				}	
			}
		} else
			kq = 1;
		return kq;
	}
	// lay ten cua nguoi lap
	function getUserName() {
		var userId = $("#hid_UserId").val();
		var sql = _selectUserName;
		var sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : userId
		});
		sql_par = setSysParam(sql_par);
		var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO(sql, sql_par);

		var rows = JSON.parse(data_ar);
		if (rows != null && rows.length > 0) {
			var row = rows[0];
			$("#txtNGUOI_LAP").val(row.FULL_NAME);

		}

	}
	// get ten kho lap
	function getTenKho() {
		var khoId = $("#hid_LoaiKhoId").val();
		var sql = selectKhoId;
		var sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : khoId
		});
		sql_par = setSysParam(sql_par);
		var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO(sql, sql_par);

		var rows = JSON.parse(data_ar);
		if (rows != null && rows.length > 0) {
			var row = rows[0];
			$("#txtKHO_LAP").val(row.TEN);
			_dsKhoCap = row.DSKHOBUTT;
		}

	}
	function guiYeuCau(_trangThaiId) {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var today = new Date();
			var dd = today.getDate();
			var mm = today.getMonth() + 1; // January is 0!
			var yyyy = today.getFullYear();
			var hour = today.getHours();
			var minute = today.getMinutes();
			var second = today.getSeconds();

			if (dd < 10) {
				dd = '0' + dd;
			}

			if (mm < 10) {
				mm = '0' + mm;
			}
			if (hour.toString().length == 1) {
				hour = '0' + hour;
			}
			if (minute.toString().length == 1) {
				minute = '0' + minute;
			}
			if (second.toString().length == 1) {
				second = '0' + second;
			}

			today = dd + '/' + mm + '/' + yyyy + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaylap = $("#txtNGAY_LAP").val() + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaysudung = '';
			if($("#txtNGAY_SU_DUNG").val().trim() != ''){
				_ngaysudung = $("#txtNGAY_SU_DUNG").val() + ' ' + hour + ':'
				+ minute + ':' + second;
			}
				
			var kieu = '';
			if (($("#hid_HINHTHUCID").val() == "9" && $("#hidLoaiPhieu").val() == "2")
					|| $("#hidHoanTra").val() == "1") {
				kieu = '2';
			} else
				kieu = '3';
			// FormUtil.setFormToObject("","",objData);
			var param_ar_kho = {
				"DOIUNGID" : $("#cboKho").val(),// kho cap
				"KHOID" : $("#hid_LoaiKhoId").val(), // kho lap
				"MA_PHIEU" : FormUtil.escape($("#txtMA_PHIEU").val()),
				"NGUOINX" : $("#txtNGUOI_LAP").val(),
				"NGAY_LAP" : _ngaylap,
				"NGAYCAPNHAT" : today,
				"NGAYTHUCHIEN" : today,
				"NGUOITHUCHIEN" : $("#txtNGUOI_LAP").val(),
				"SO_CHUNG_TU" : null,
				"NGAY_LAP_SCT" : null,
				"NGAYSUDUNGTHUOC" : _ngaysudung,
				"CHIET_KHAU" : "0",
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_DON_GIA" : $("#tiendon").val(),
				"KIEU" : kieu,
				// "TIEN_CONLAI" : $("#conlai").val(),
				// "CSYTID" : "1",
				"HINHTHUCID" : $("#hid_HINHTHUCID").val(),
				"TRANGTHAIID" : _trangThaiId,
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),				
				"NHAPXUATID_CHA" : "0",
				"NHACUNGCAPID" : null,
				"DIA_CHI" : null,
				"NGUOI_GIAO" : $("#txtNGUOI_GIAO").val(),
				"HSD" : null,
				"DANGXULY" : $("#hidDuyetLamSan").val()

			};

			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			// xml nhap kho thuoc chi tiet
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			// var param_str = createXML(param_arr);

			var param_str = JSON.stringify(param_arr);

			var _par = [ str_kho_json, param_str ];
			
			if($("#hidDuyetLamSan").val()=='1'){
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC11N001.DLS.06", _par
						.join('$'));
				var data = $.parseJSON(result);

				var _succes = data.SUCCESS;

				if (_succes == '0') {
					DlgUtil.showMsg(data.MESSAGE);

				} else {
					$("#hid_NHAPXUATID").val(_succes);
					if (_trangThaiId == "1")
						// DlgUtil.showMsg('Lưu phiếu thành công');
						DlgUtil.showMsg('Lưu phiếu thành công !', function() {
							EventUtil.raiseEvent("YCNhap_success", {
								ncc : 'NHAP_KHO',
								id : $("#hid_NHAPXUATID").val()
							});
						});

					else if (_trangThaiId == "5")
						// DlgUtil.showMsg('Gửi yêu cầu thành công');
						DlgUtil.showMsg('Duyệt dược lâm sàn thành công !', function() {
							EventUtil.raiseEvent("YCNhap_success", {
								ncc : 'NHAP_KHO',
								id : $("#hid_NHAPXUATID").val()
							});
						});

				}
			}else{
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC11N001.06", _par
						.join('$'));
				var data = $.parseJSON(result);

				var _succes = data.SUCCESS;

				if (_succes == '0') {
					DlgUtil.showMsg(data.MESSAGE);

				} else {
					$("#hid_NHAPXUATID").val(_succes);
					if (_trangThaiId == "1")
						// DlgUtil.showMsg('Lưu phiếu thành công');
						DlgUtil.showMsg('Lưu phiếu thành công !', function() {
							EventUtil.raiseEvent("YCNhap_success", {
								ncc : 'NHAP_KHO',
								id : $("#hid_NHAPXUATID").val()
							});
						});

					else if (_trangThaiId == "5")
						
						// DlgUtil.showMsg('Gửi yêu cầu thành công');
						DlgUtil.showMsg('Gửi yêu cầu thành công !', function() {
							
							var _par = ['PHARMA_TUDONG_IN_PHIEU_CHUYENKHO'];
							var tudongin = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA",
									_par.join('$'));
							if (tudongin == 1 && $("#hid_HINHTHUCID").val() == "2" && $("#hid_LOAIGIAODIEN").val() == "THUOC"){
								var par = [ 
											{
											name : 'nhapxuatid',//nhapxuatid
											type : 'String',
											value : $("#hid_NHAPXUATID").val()
										}];

									openReport('window', 'DUC_PHIEUXUATCHUYENTHUOC',
											'pdf', par);
									openReport('window', 'NTU004_PHIEULINHTHUOC_01DBV01_TT23_A4',
											'pdf', par);
									setTimeout(function() { 
										EventUtil.raiseEvent("YCNhap_success", {
											ncc : 'NHAP_KHO',
											id : $("#hid_NHAPXUATID").val()
										});
								    }, 3000);
								
							}else{
								EventUtil.raiseEvent("YCNhap_success", {
									ncc : 'NHAP_KHO',
									id : $("#hid_NHAPXUATID").val()
								});
							}
						});

				}
			}
			
		} else {
			DlgUtil.showMsg('Danh sách thuốc không được rỗng !');
		}

	}
// chuannt them luu phieu xuat thuoc vat tu cho NCC
	function LuuphieuXuatNcc() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var today = new Date();
			var dd = today.getDate();
			var mm = today.getMonth() + 1; // January is 0!
			var yyyy = today.getFullYear();
			var hour = today.getHours();
			var minute = today.getMinutes();
			var second = today.getSeconds();

			if (dd < 10) {
				dd = '0' + dd;
			}

			if (mm < 10) {
				mm = '0' + mm;
			}
			if (hour.toString().length == 1) {
				hour = '0' + hour;
			}
			if (minute.toString().length == 1) {
				minute = '0' + minute;
			}
			if (second.toString().length == 1) {
				second = '0' + second;
			}

			today = dd + '/' + mm + '/' + yyyy + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaylap = $("#txtNGAY_LAP").val() + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaysudung = $("#txtNGAY_SU_DUNG").val() + ' ' + hour + ':'
					+ minute + ':' + second;

			// FormUtil.setFormToObject("","",objData);
			var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
					_par.join('$'));
			if(result == '0'){	
			    	var maphieu = layMaPhieu();
			    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
					$("#txtMA_PHIEU").val(layMaPhieu());
					
			}
			var param_ar_kho = {
				// "DOIUNGID" : $("#cboKho").val(),// kho cap
				"KHOID" : $("#hid_LoaiKhoId").val(), // kho lap
				"MA_PHIEU" : FormUtil.escape($("#txtMA_PHIEU").val()),
				"NGUOINX" : $("#txtNGUOI_LAP").val(),
				"NGAY_LAP" : _ngaylap,
				"NGAYCAPNHAT" : today,
				"NGAYTHUCHIEN" : today,
				"NGUOITHUCHIEN" : $("#txtNGUOI_LAP").val(),
				"SO_CHUNG_TU" : $("#txtSO_CHUNG_TU").val(),
				"NGAY_LAP_SCT" : null,
				"NGAYSUDUNGTHUOC" : _ngaysudung,
				"CHIET_KHAU" : "0",
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_DON_GIA" : $("#tiendon").val(),
				"KIEU" : "1",
				"TIENTHUEVAT" : $("#thuegtgt").val(),				
				// "TIEN_CONLAI" : $("#conlai").val(),
				// "CSYTID" : "1",
				"HINHTHUCID" : "1",
				"TRANGTHAIID" : "1",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : "0",
				"NHACUNGCAPID" : $("#hid_NHACUNGCAPID").val(),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"NGUOI_GIAO" : $("#txtNGUOI_GIAO").val(),
				"HSD" : null

			};

			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			// xml nhap kho thuoc chi tiet
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			// var param_str = createXML(param_arr);

			var param_str = JSON.stringify(param_arr);

			var _par = [ str_kho_json, param_str ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC03X001.11", _par
					.join('$'));
			var data = $.parseJSON(result);

			var _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);

			} else {
				$("#hidTRANGTHAIID").val('4');
				$("#hid_NHAPXUATID").val(_succes);
				$('#cmdLuuPhieu').attr('disabled', 'disabled');
				DlgUtil.showMsg('Lưu phiếu thành công !', function() {
					EventUtil.raiseEvent("nhapkho_success", {
						ncc : 'NHAP_KHO',
						id : $("#hid_NHAPXUATID").val()
					});
				});

			}
		} else {
			DlgUtil.showMsg('Danh sách thuốc không được rỗng !');
		}

	}
	function TraKho() {
		/*
		 * if($("#hid_XUATTRAID").val() == '0'){ LuuPhieu();
		 * if($("#hid_XUATTRAID").val() != '0'){ _ActTraKho(); }
		 *  } else{ _ActTraKho(); }
		 */
		//LuuphieuXuatNcc();
		_ActTraKho();
	}

	function _ActTraKho() {

		/*var _par = [ $("#hid_NHAPXUATID").val() ];

		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC03X001.12", _par
				.join('$'));

		if (result == '0') {
			DlgUtil.showMsg('Xuất trả ' + _thuocVattu + ' có lỗi');
		} else {
			DlgUtil.showMsg('Đã xuất trả ' + _thuocVattu, function() {
				EventUtil.raiseEvent("YCNhap_success", {
					ncc : 'XUATTR_NCC',
					id : $("#hid_NHAPXUATID").val()
				});
			});
		}*/
		
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var today = new Date();
			var dd = today.getDate();
			var mm = today.getMonth() + 1; // January is 0!
			var yyyy = today.getFullYear();
			var hour = today.getHours();
			var minute = today.getMinutes();
			var second = today.getSeconds();

			if (dd < 10) {
				dd = '0' + dd;
			}

			if (mm < 10) {
				mm = '0' + mm;
			}
			if (hour.toString().length == 1) {
				hour = '0' + hour;
			}
			if (minute.toString().length == 1) {
				minute = '0' + minute;
			}
			if (second.toString().length == 1) {
				second = '0' + second;
			}

			today = dd + '/' + mm + '/' + yyyy + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaylap = $("#txtNGAY_LAP").val() + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaysudung = $("#txtNGAY_SU_DUNG").val() + ' ' + hour + ':'
					+ minute + ':' + second;

			// FormUtil.setFormToObject("","",objData);
			var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
					_par.join('$'));
			if(result == '0'){	
			    	var maphieu = layMaPhieu();
			    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
					$("#txtMA_PHIEU").val(layMaPhieu());
					
			}
			var param_ar_kho = {
				// "DOIUNGID" : $("#cboKho").val(),// kho cap
				"KHOID" : $("#hid_LoaiKhoId").val(), // kho lap
				"MA_PHIEU" : FormUtil.escape($("#txtMA_PHIEU").val()),
				"NGUOINX" : $("#txtNGUOI_LAP").val(),
				"NGAY_LAP" : _ngaylap,
				"NGAYCAPNHAT" : today,
				"NGAYTHUCHIEN" : today,
				"NGUOITHUCHIEN" : $("#txtNGUOI_LAP").val(),
				"SO_CHUNG_TU" : $("#txtSO_CHUNG_TU").val(),
				"NGAY_LAP_SCT" : null,
				"NGAYSUDUNGTHUOC" : _ngaysudung,
				"CHIET_KHAU" : "0",
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_DON_GIA" : $("#tiendon").val(),
				"KIEU" : "1",
				"TIENTHUEVAT" : $("#thuegtgt").val(),
				// "TIEN_CONLAI" : $("#conlai").val(),
				// "CSYTID" : "1",
				"HINHTHUCID" : "1",
				"TRANGTHAIID" : "5",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : "0",
				"NHACUNGCAPID" : $("#hid_NHACUNGCAPID").val(),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"NGUOI_GIAO" : $("#txtNGUOI_GIAO").val(),
				"HSD" : null

			};

			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			// xml nhap kho thuoc chi tiet
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			// var param_str = createXML(param_arr);

			var param_str = JSON.stringify(param_arr);

			var _par = [ str_kho_json, param_str ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC03X001.13", _par
					.join('$'));
			if (result == '0') {
				DlgUtil.showMsg('Xuất trả ' + _thuocVattu + ' có lỗi');
			} else {
				DlgUtil.showMsg('Đã xuất trả ' + _thuocVattu, function() {
					var PHARMA_NT_DAYCONGDUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA',
					'PHARMA_NT_DAYCONGDUOC');
					if(PHARMA_NT_DAYCONGDUOC=='1'){
						var par =[] ;
						par.push({"name":"[0]","value": result} );
				    	var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC47T002.05', par);
						var phieunk = JSON.parse(data_ar); 
						var xuatid = result;
						if(phieunk[0].XUATID!='') 
							xuatid = phieunk[0].XUATID;   
						_daycongXuatThuoc(xuatid);
					}
					
					EventUtil.raiseEvent("YCNhap_success", {
						ncc : 'XUATTR_NCC',
						id : $("#hid_NHAPXUATID").val()
					});
				});

			}
		} else {
			DlgUtil.showMsg('Danh sách thuốc không được rỗng !');
		}

	}
	// chuannt them gui yeu cau xuat kho cho NCC
	function GuiYCXuatNcc() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var today = new Date();
			var dd = today.getDate();
			var mm = today.getMonth() + 1; // January is 0!
			var yyyy = today.getFullYear();
			var hour = today.getHours();
			var minute = today.getMinutes();
			var second = today.getSeconds();

			if (dd < 10) {
				dd = '0' + dd;
			}

			if (mm < 10) {
				mm = '0' + mm;
			}
			if (hour.toString().length == 1) {
				hour = '0' + hour;
			}
			if (minute.toString().length == 1) {
				minute = '0' + minute;
			}
			if (second.toString().length == 1) {
				second = '0' + second;
			}

			today = dd + '/' + mm + '/' + yyyy + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaylap = $("#txtNGAY_LAP").val() + ' ' + hour + ':' + minute
					+ ':' + second;
			var _ngaysudung = $("#txtNGAY_SU_DUNG").val() + ' ' + hour + ':'
					+ minute + ':' + second;

			// FormUtil.setFormToObject("","",objData);
			var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
					_par.join('$'));
			if(result == '0'){	
			    	var maphieu = layMaPhieu();
			    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
					$("#txtMA_PHIEU").val(layMaPhieu());
					
			}
			var param_ar_kho = {
				// "DOIUNGID" : $("#cboKho").val(),// kho cap
				"KHOID" : $("#hid_LoaiKhoId").val(), // kho lap
				"MA_PHIEU" : FormUtil.escape($("#txtMA_PHIEU").val()),
				"NGUOINX" : $("#txtNGUOI_LAP").val(),
				"NGAY_LAP" : _ngaylap,
				"NGAYCAPNHAT" : today,
				"NGAYTHUCHIEN" : today,
				"NGUOITHUCHIEN" : $("#txtNGUOI_LAP").val(),
				"SO_CHUNG_TU" : $("#txtSO_CHUNG_TU").val(),
				"NGAY_LAP_SCT" : null,
				"NGAYSUDUNGTHUOC" : _ngaysudung,
				"CHIET_KHAU" : "0",
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_DON_GIA" : $("#tiendon").val(),
				"KIEU" : "3",
				// "TIEN_CONLAI" : $("#conlai").val(),
				// "CSYTID" : "1",
				"HINHTHUCID" : "1",
				 "TRANGTHAIID" : "5",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : "0",
				"NHACUNGCAPID" : $("#hid_NHACUNGCAPID").val(),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"NGUOI_GIAO" : $("#txtNGUOI_GIAO").val(),
				"HSD" : null

			};

			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			// xml nhap kho thuoc chi tiet
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			// var param_str = createXML(param_arr);

			var param_str = JSON.stringify(param_arr);

			var _par = [ str_kho_json, param_str ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC03X001.11", _par
					.join('$'));
			var data = $.parseJSON(result);

			var _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);

			} else {
				$("#hid_NHAPXUATID").val(_succes);
				$('#cmdLuuPhieu').attr('disabled', 'disabled');
				DlgUtil.showMsg('Gửi phiếu yêu cầu xuất thành công !', function() {
					EventUtil.raiseEvent("YCNhap_success", {
						ncc : 'XUATTR_NCC',
						id : $("#hid_NHAPXUATID").val()
					});
				});

			}
		} else {
			DlgUtil.showMsg('Danh sách thuốc không được rỗng !');
		}

	}
	// sửa phiếu đã hoàn trả chỉ lưu phiếu ko sửa thuốc
	function EditPhieuHT() {
		var today = new Date();
		var dd = today.getDate();
		var mm = today.getMonth() + 1; // January is 0!
		var yyyy = today.getFullYear();
		var hour = today.getHours();
		var minute = today.getMinutes();
		var second = today.getSeconds();

		if (dd < 10) {
			dd = '0' + dd;
		}

		if (mm < 10) {
			mm = '0' + mm;
		}
		if (hour.toString().length == 1) {
			hour = '0' + hour;
		}
		if (minute.toString().length == 1) {
			minute = '0' + minute;
		}
		if (second.toString().length == 1) {
			second = '0' + second;
		}

		today = dd + '/' + mm + '/' + yyyy + ' ' + hour + ':' + minute
				+ ':' + second;
		var _ngaylap = $("#txtNGAY_LAP").val() + ' ' + hour + ':' + minute
				+ ':' + second;
		var _ngaysudung = $("#txtNGAY_SU_DUNG").val() + ' ' + hour + ':'
				+ minute + ':' + second;
		
		var param_ar_kho = {
				// "DOIUNGID" : $("#cboKho").val(),// kho cap
				"KHOID" : $("#hid_LoaiKhoId").val(), // kho lap
				"MA_PHIEU" : FormUtil.escape($("#txtMA_PHIEU").val()),
				"NGUOINX" : $("#txtNGUOI_LAP").val(),
				"NGAY_LAP" : _ngaylap,
				"NGAYCAPNHAT" : today,
				"NGAYTHUCHIEN" : today,
				"NGUOITHUCHIEN" : $("#txtNGUOI_LAP").val(),
				"SO_CHUNG_TU" : $("#txtSO_CHUNG_TU").val(),
				"NGAY_LAP_SCT" : null,
				"NGAYSUDUNGTHUOC" : _ngaysudung,
				"CHIET_KHAU" : "0",
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_DON_GIA" : $("#tiendon").val(),
				"KIEU" : "1",
				"TIENTHUEVAT" : $("#thuegtgt").val(),
				// "TIEN_CONLAI" : $("#conlai").val(),
				// "CSYTID" : "1",
				"HINHTHUCID" : "1",
				// TRANGTHAIID" : _trangThaiId,
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : "0",
				"NHACUNGCAPID" : $("#hid_NHACUNGCAPID").val(),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"NGUOI_GIAO" : $("#txtNGUOI_GIAO").val(),
				"HSD" : null

			};
		// var param_ar_kho = {"":161};
		var param_str_kho = JSON.stringify(param_ar_kho);

		var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
				"}");	

		var _par = [ str_kho_json ];		
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC02N001.07", _par
				.join('$'));

		var data = $.parseJSON(result);

		 _succes = data.SUCCESS;
	
		
		
		if (_succes == '0') {
			DlgUtil.showMsg(data.MESSAGE);

		} else {
			$("#hid_NHAPXUATID").val(_succes);
			$('#cmdLuuPhieu').attr('disabled', 'disabled');
			DlgUtil.showMsg('Lưu phiếu thành công !', function() {
				EventUtil.raiseEvent("nhapkho_success", {
					ncc : 'NHAP_KHO',
					id : $("#hid_NHAPXUATID").val()
				});
			});

		}
	}
	function _isValidateNhapKho() {
		var kq = '0';
		
		
		if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
			if ($("#hid_NHACUNGCAPID").val() == '0') {
				DlgUtil.showMsg('Bạn phải chọn đúng nhà cung cấp !',
						function() {
							$("#txtNhaCungCap").focus();
						});
				kq = '0';
				return kq;
			}
			
			/*if ($("#txtSO_CHUNG_TU").val() == '') {
				DlgUtil.showMsg('Bạn phải nhập số chứng từ  !',
						function() {
							$("#txtSO_CHUNG_TU").focus();
						});
				kq = '0';
				return kq;
			}*/

		}
		else {
			if ($("#cboKho").val() == '0') {
				DlgUtil.showMsg('Bạn hãy chọn kho !');
				$("#cboKho").focus();
				return kq;
			} else {
				kq = '1';
			}
		}
		$("#txtMA_PHIEU").val($.trim($("#txtMA_PHIEU").val()));
		if ($("#txtMA_PHIEU").val() == "" || $("#txtMA_PHIEU").val() == null) {
			DlgUtil.showMsg('Bạn phải nhập Mã Phiếu !');
			$("#txtMA_PHIEU").focus();
			kq = '0';
			return kq;
		} else if ($("#txtMA_PHIEU").val().length > 30) {
			DlgUtil.showMsg('Mã phiếu phải nhỏ hơn 30 ký tự');
			$("#txtMA_PHIEU").focus();
		} else {
			kq = '1';
		}
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds == null || rowIds.length == 0) {
			DlgUtil.showMsg('Danh sách thuốc không được rỗng !');
			kq = '0';
			return kq;
		}
		if(showImport =='1'){
			var kt =0;
			var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
			for (var i = 0; i < rowIds.length; i++) {
				rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);
				
				if (rowData['THUOCVATTUID'] == '') {
					 DlgUtil.showMsg('Kiểm tra thuốc '+rowData['TEN_THUOC']+' không có trong hệ thống!');
					 return 0;
				}
				if (rowData['SL_KHADUNG'] == '') {
					 DlgUtil.showMsg('Thuốc'+rowData['TEN_THUOC']+' chưa có trong kho cấp!');
					 return 0;
				}
				if ( (Number(rowData['SL_KHADUNG']) <   Number(rowData['SL_YC'])) && $("#hid_HINHTHUCID").val() == "2" && PHARMA_YEUCAUDUTRU_VUOTKHADUNG == '0') {
					if($("#hid_HINHTHUCID").val() == "2" && PHARMA_YEUCAUDUTRU_VUOTKHADUNG == '1'){
						kq ='1';
					}else{
						DlgUtil.showMsg('Thuốc'+rowData['TEN_THUOC']+' không đủ để cấp!');
						return 0;
					}
				}
				kq ='1';
			}
		}

		return kq;
	}

	function loadNhapXuat(_keyField) {

		var sql_par = [ _keyField ];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC30X001.02", sql_par
				.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];

			$("#hid_KHOCAPID").val(row.KHO_CAP_ID);// kho cap
			$('#cboKho').attr('disabled', 'disabled');
			// $("#txtKHO").val(row.KHO_CAP_NAME);
			$("#hid_LoaiKhoId").val(row.KHOID);
			$("#txtMA_PHIEU").val(FormUtil.unescape(row.MA_PHIEU));
			$("#txtNGAY_LAP").val(row.NGAY_LAP);
			$("#txtNGUOI_LAP").val(row.NGUOI_LAP);
			$("#txtNGAY_SU_DUNG").val(row.NGAY_SU_DUNG);
			$("#txtNGUOI_GIAO").val(row.NGUOIGIAO);
			$("#txtSO_CHUNG_TU").val(row.SO_CHUNG_TU);
			$("#txtGHI_CHU").val(FormUtil.unescape(row.GHI_CHU));
			_hinhThucId = row.HINH_THUC_ID;
			$("#tiendon").text(Number(row.TIENDON).format(2, 3) + 'đ');
			$("#tongcong").text(Number(row.TONGTIEN).format(2, 3) + 'đ');
			$("#tiendon").val(Number(row.TIENDON).toFixed(2));
			$("#tongcong").val(Number(row.TONGTIEN).toFixed(2));			
			$("#thuegtgt").val(Number(row.TIENTHUEVAT).toFixed(2));
			$("#thuegtgt").text(Number(row.TIENTHUEVAT).format(2, 3) + 'đ');
			// chuan them nha cung cap
			$("#hid_NHACUNGCAPID").val(row.NHACUNGCAPID);

			$("#txtNhaCungCap").val(row.TEN_NCC);
			$("#txtDIA_CHI").val(row.DIACHI_NCC);

			$("#hid_NHAPXUATID_CHA").val(row.NHAPXUATID);
			// $("#cboNguoi_Lap").val(row.ID_NGUOILAP);
			var _par = RSUtil.buildParam("", [ _keyField ]);
			GridUtil.loadGridBySqlPage(_gridId1, _NhapXuatCTSQL, _par);

			/*
			 * _hinhThucId = row.HINH_THUC_ID;
			 * $("#tiendon").text(Number(row.TIENDON).format(2,3) + 'đ');
			 * $("#tongcong").text(Number(row.TONGTIEN).format(2,3) + 'đ');
			 * $("#tiendon").val(Number(row.TIENDON).toFixed(2));
			 * $("#tongcong").val(Number(row.TONGTIEN).toFixed(2));
			 * $("#hid_NHAPXUATID_CHA").val(row.NHAPXUATID);
			 */
			/*var _par = RSUtil.buildParam("", [ _keyField ]);
			GridUtil.loadGridBySqlPage(_gridId1, _NhapXuatCTSQL, _par);
*/
			//$("#gridList_THUOC").setSelection(1, true);

			$("#gridList_THUOC").setSelection(1, true);

		} else {

			DlgUtil.showMsg("Không có dữ liệu");

			return;
		}
	}
	function loadRowSelectThuoc(item_id) {
		
		//var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		var rowData = $('#gridList_THUOC').jqGrid('getRowData', item_id);
		 //hi_thuocid = rowData['THUOCVATTUID'];
		$("#hidTHUOCVATTU").val(rowData['THUOCVATTUID']);
		$('#txtMA_THUOC').val(rowData['MA_THUOC']);
		$('#txtTEN_THUOC').val(rowData['TEN_THUOC']);
		$('#txtDON_VI_TINH').val(rowData['DON_VI_TINH']);
		$('#txtSO_LUONG').val(Number(rowData['SL_YC']).format(2, 3));		
		$('#txtDON_GIA').val(Number(rowData['DON_GIA']).format(6, 3));
		$('#txtVAT').val(rowData['VAT']);
		$('#txtTHANH_TIEN').val(Number(rowData['THANH_TIEN']).format(3,3));	
		$('#hid_thanhtientruocvat').val( (Number(rowData['DON_GIA']) * Number(rowData['SL_YC']) ).format(3,3));
		$('#txtDON_GIA_VAT').val(Number(rowData['DON_GIA_VAT']).format(3, 3));		
		$('#txtTHUE_GTGT').val(Number(rowData['THUEGTGT']).format(3,3));
		$("#hidTonKhoCTID").val(rowData['TONKHOCTID']);
		$("#hidHSD").val(rowData['HSD']);
		$("#hidSOLO").val(rowData['SO_LO']);
		_soLuongKhaDung = rowData['SL_KHADUNG'];

	}

	function tinhTien() {
		var donGiaVAT = replaceStrtoNum($('#txtDON_GIA_VAT').val());
		var thanhtien = (replaceStrtoNum($('#txtSO_LUONG').val()) * donGiaVAT);
		var thanhtientruocvat =replaceStrtoNum($('#txtSO_LUONG').val())* replaceStrtoNum($('#txtDON_GIA').val());		
		$("#hid_thanhtientruocvat").val(Number(thanhtientruocvat).format(3,3));
		$("#txtTHANH_TIEN").val(Number(thanhtien).format(3, 3));
	}
	function _Reset() {
		$("#txtMA_THUOC").val("");
		$("#txtTEN_THUOC").val("");
		$("#txtDON_VI_TINH").val("");
		$("#txtSO_LUONG").val("0");
		$("#txtDON_GIA").val("0");
	//	$("#txtVAT").val("0");
		$("#txtDON_GIA_VAT").val("0");
		$("#txtTHANH_TIEN").val("0");
		$("#txtGIABAN").val("0");
		$("#hidTHUOCKHOYC").val("");
	//	$("#txtVAT").val("0");
		// $('#txtSO_LUONG').attr('disabled','disabled');
	}
	function layMaPhieu() {
		/*if ($('#hidHoanTra').val() == 1) {
			_loaiphieunhap = 'HTKK';
		}
		if ($("#hidLoaiNhapBu").val() == "XUATTRANCC_TK") {
			_loaiphieunhap = 'XKNCC';
		}

		var _par = [ _loaiphieunhap ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC02N001.04", _par
				.join('$'));
		if (result == '0') {
			DlgUtil.showMsg('Lấy stt lỗi');
			return;
		} else {
			return result;
		}*/
		var kieu = '';
		if (($("#hid_HINHTHUCID").val() == "9" && $("#hidLoaiPhieu").val() == "2")
				|| $("#hidHoanTra").val() == "1") {
			kieu = '2';
		} else if($("#hid_HINHTHUCID").val() == "1"){
			kieu = '1';
		} else
			kieu = '3';
		var _par = [ $("#hid_HINHTHUCID").val(),kieu,$("#hid_LoaiKhoId").val() ];
		if( $("#hid_HINHTHUCID").val() =='2' && _param[0] =='10284' && $("#hid_LOAIGIAODIEN").val() == "THUOC"){
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_GET_SOPHIEU4",
					_par.join('$'));
		}
		else if ( $("#hid_HINHTHUCID").val() =='9' && _param[0] =='10284'){
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_GET_SOPHIEU5",
					_par.join('$'));
		}
		else 
		{
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_GET_SOPHIEU",
				_par.join('$'));
		}
		var data = $.parseJSON(result);

		 _succes = data.SUCCESS;
		if (_succes == '0') {
			DlgUtil.showMsg('Lấy stt lỗi');
			return ;
		} else {
			//var _val = data.MESSAGE.replace("MA1", _loaiphieunhap);
			//_val = data.MESSAGE.replace("MA", _loaiphieunhap);
			return data.MESSAGE;
		}
	}
	function laySoChungTu(){
		var _par = [ $("#hid_HINHTHUCID").val(),'1'  ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_SOCHUNGTU",
				_par.join('$'));
		
		var data = $.parseJSON(result);

		 _succes = data.SUCCESS;
		if (_succes == '0') {
			DlgUtil.showMsg('Không lấy được số chứng từ');
			return ;
		} else {
			//var _val = data.MESSAGE.replace("MA1", _loaiphieunhap);
			//_val = data.MESSAGE.replace("MA", _loaiphieunhap);
			return data.MESSAGE;
		}
	}
	function replaceStrtoNum(strSoluong) {
		return Number(strSoluong.replace(/[^0-9\.]+/g, ""));
	}
	function loadDsThuocBuTT(khoidyc,khoidcap){
		var _par = RSUtil.buildParam("", [ khoidyc,khoidcap ]);
		GridUtil.loadGridBySqlPage(_gridId1, "DUC11N001.11", _par);
		GridUtil.setGridParam(_gridId1, {
			gridComplete : function() {
				var rowCount = $("#gridList_THUOC").getGridParam("reccount");
				if (rowCount > 0) {
					checkSoLuong();
				}
			}
		});
	}
	function loadDsThuocHetHSD(khoidyc,khoidcap){
		var _par = RSUtil.buildParam("", [ khoidyc,khoidcap ]);
		GridUtil.loadGridBySqlPage(_gridId1, "DUC11N001.HETHSD01", _par);
		GridUtil.setGridParam(_gridId1, {
			gridComplete : function() {
				var rowCount = $("#gridList_THUOC").getGridParam("reccount");
				if (rowCount > 0) {
					//checkSoLuong();
				}
			}
		});
	}
	
	function checkSoLuong() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		for (var i = 0; i < rowIds.length; i++) {
			rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);

			if (parseFloat(rowData['SO_DU']) < 0) {

				$("#gridList_THUOC").jqGrid('setRowData', rowIds[i], "", {
					color : 'red'
				});
			}
			if(rowData['THUOCKHOYC'] =='') 
				{
					$("#gridList_THUOC").jqGrid('setRowData', rowIds[i], "", {
						color : 'red'
					});
				}
			
		}
	};
	function checkAnGia(_hinhthuc){
		var _par = ['PHARMA_GD_AN_GIA_TVT'];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
				_par.join('$'));
		if (typeof result === "undefined") return 0;
	   var hinhthuc =result.split(",");
	   if(hinhthuc.indexOf(_hinhthuc) != -1) return 1;
	   return 0;
	};
	function checkRole(control){	
//		that.opt.ht
		var _parPQ = 'DUC01S002_PhieuYeuCau' +'%ht='+$("#hid_HINHTHUCID").val()+'&'+ '$';
		  var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ	);
//		  var kt =0;
		  for(var i =0; i< result.length ;i++){
		    	if(result[i].ELEMENT_ID == control){
		    		//kt= kt+ 1;
		    		if (result[i].ROLES  =='1') 
		    			$('#'+result[i].ELEMENT_ID).show();
		    		if (result[i].ROLES  =='0'||result[i].ROLES  =='') 
		    			$('#'+result[i].ELEMENT_ID).hide();
		    	}
		    
		    }
//		  if(kt==0) $('#'+control).hide();
	};
	// day cong xuat tra nha cung cap
	function _daycongXuatThuoc(nhapxuatid) {
 		var PHARMA_NT_DAYCONGDUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA',
 				'PHARMA_NT_DAYCONGDUOC');
		if (PHARMA_NT_DAYCONGDUOC  == 1) {
		 
			// var sql_par = [];
			// sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
			// jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
			var objSend = new Object();
			var objData = new Object();
			var objHeader = new Object();
			var objBody = new Object();
			var objSeccurity = new Object();
			var objDonThuoc = new Object();
			var objThongTinBN = new Object();
			var objDSChiTietThuoc = new Object();
			var objnhapthuoc = new Object();

			var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
					'BTKD_WS_URL');
			var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
					'BTKD_WS_USER');
			var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
					'BTKD_WS_PASS');
		 
			
			// tao header
			objHeader.SENDER_CODE = macsyt;
			objHeader.SENDER_NAME = '';
			objHeader.TRANSACTION_TYPE = "M0002";
			objHeader.TRANSACTION_NAME = "FTP";
			objHeader.TRANSACTION_ID = "";
			objHeader.PROVIDER = "12";
 
			objHeader.ACTION_TYPE = "0";
			// tao don thuoc
//			objDonThuoc.DON_THUOC_ID = data_btkd[0].MAUBENHPHAMID;
			
			var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC34X001_15",
					[ nhapxuatid ].join('$'));
			
			

			objnhapthuoc.LUOT_XUAT_ID = '';
			objnhapthuoc.MA_LUOT_XUAT = nhapxuatid;
			var sohoadon = $("#txtSO_CHUNG_TU").val();
			if($("#txtSO_CHUNG_TU").val() =='') sohoadon =$("#txtMA_PHIEU").val();
			objnhapthuoc.SO_HOA_DON = sohoadon;
			objnhapthuoc.SO_PHIEU = $("#txtMA_PHIEU").val();
			objnhapthuoc.NGAY_XUAT =  $("#txtNGAY_SU_DUNG").val() ;
			objnhapthuoc.MA_CS_NHAP = macsyt;
			objnhapthuoc.TEN_CS_NHAP = $("#txtNhaCungCap").val(); 
			objnhapthuoc.MA_CS_XUAT = macsyt;
			objnhapthuoc.MA_CS_XUAT_BYT = macsyt;
			objnhapthuoc.GHI_CHU =$("#txtGHI_CHU").val() ;
			objnhapthuoc.UUID = '';
			objnhapthuoc.LOAI_PHIEU_XUAT = '2';
			
			
			

			objBody.XUAT_THUOC = objnhapthuoc; 
			
			var j =0;
			var objChiTietThuoc = [];
			for (var i = 0; i < data_btkd.length; i++) {
				var objThuoc = new Object();
				if(data_btkd[i].SODANGKY != '')
					{
						
						objThuoc.LUOT_XUAT_ID = nhapxuatid;// data_btkd[i].MAUBENHPHAMID;
						// objThuoc.STT = data_btkd[i].THUTU;
						// if(_opts.option=='02D010' || _opts.option=='02D017')
						objThuoc.THUOC_XUAT_ID = data_btkd[i].NHAPXUATCTID; 
						objThuoc.LUOT_NHAP_ID ='1';
						objThuoc.THUOC_NHAP_ID ='1';
						objThuoc.MA_THUOC = data_btkd[i].MA;
						objThuoc.TEN_THUOC = data_btkd[i].TEN; 
						objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
//						objThuoc.TEN_HOAT_CHAT = data_btkd[i].HOATCHAT; 
//						objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
//						objThuoc.NHA_SAN_XUAT = data_btkd[i].NHA_SAN_XUAT;
 						objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA; 
 						objThuoc.NGAY_SAN_XUAT = '';
						objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
						objThuoc.BAO_CHE = ''; 
						objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
						objThuoc.SO_LO = data_btkd[i].SO_LO;
						objThuoc.HAN_DUNG = data_btkd[i].HAN_DUNG;
						
						objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
						 
						objThuoc.GHI_CHU = '';
						objChiTietThuoc.push(objThuoc);
						j = j+1;
					}	
			}
			if(j==0) return; 
			
			
			
			
			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
			objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc; 

			objData.HEADER = objHeader;
			objData.BODY = objBody;
			objData.SECURITY = objSeccurity;
			objData.HEADER = objHeader;
			objSend.DATA = objData;

			var x2js = new X2JS();
			objSend = JSON.stringify(objSend);
			var obj = x2js.json2xml_str($.parseJSON(objSend));
			obj = obj.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(
					/&amp;/g, '&');
			obj = btoa(unescape(encodeURIComponent(obj)));// atob(obj);

			// gui lay ma dinh danh va MK
			var resultCongBTKD = ajaxSvc.CongBTKDWS.xuatThuocNewAPI(
					BTKD_WS_URL, BTKD_WS_USER, BTKD_WS_PASS, obj); 
			var resultText = $(resultCongBTKD).find("Error > Error_Message").text();
			var resultNumber = $(resultCongBTKD).find("Error > Error_Number").text();
			if (resultText && resultText.length > 0) {
				 
				DlgUtil.showMsg("Thông báo Gửi Dữ liệu nhập thuốc sang BYT :" + resultText, function() {
					EventUtil.raiseEvent("nhapkho_success", {
						ncc : 'NHAP_KHO',
						id : $("#hid_NHAPXUATID").val()
					});
				});
				
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra");
			}
			
			
			 
		}
	};
	
	
	
}
