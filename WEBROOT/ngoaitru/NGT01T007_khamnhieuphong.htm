
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">       
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           


<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>  
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>   
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>

<link rel="stylesheet" href="../common/script/alertify/themes/alertify.core.css" />
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.default.css" id="toggleCSS" />
<script type="text/javascript" src="../common/script/alertify/alertify.js" ></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT01T007_khamnhieuphong.js?v=20170525"></script>
<div style="width:100%;">
<div id="dialog" class="width_dialog dialog-khamnhieuphong" style="margin:-13px 0 0 15px">
		<div class="row form-inline" >		
			<table id="grdDanhSachPK"></table> 
		</div>
		<div class="form-inline">
			<div class="low-padding" style="width: 100%;float: left;">
				<label style="width: 20%;">Phòng khám</label>
				<input class="input-sm i-col-m_fl" id="txtPhongKhamDuocChon" name="txtPhongKhamDuocChon" readonly  style="width:79%;" title="">
			</div>
		</div>
		<div class="form-inline">
			<div class="low-padding" style="width: 100%;float: left;">
				<label style="width: 20%;">Khám chính</label>
				<label><input type="checkbox" id="chkPHONGCHINH" value=""></label>
			</div>
		</div>
		<div class="row form-inline">
			<div class="low-padding" style="margin-left:23%;">
				<button class="btn btn-sm btn-primary" id="btnXoaPhongKham">
					<span class="glyphicon glyphicon-remove"></span> Xóa
				</button>	
				<button class="btn btn-sm btn-primary" id="btnChonPhongKham">
					<span class="glyphicon glyphicon-pencil"></span> Thêm
				</button>
				<button class="btn btn-sm btn-primary" id="btnThoatKNP">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng
				</button>
			</div>
			
		</div>
		<div class="row form-inline">
			<table id="grdDSPhongDuocChon"></table> 
		</div>
		
	 </div>
</div>
<script type="text/javascript">
	var uuid = '{uuid}';
	initRest(uuid,"/vnpthis");

	var mode = '{showMode}';	
	var data;
	
	var _opts=new Object();
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		_opts.khambenhid = data.khambenhid
	}
	
	var bn = new KHAMNHIEUPHONG(_opts);
	bn.load();
</script>
