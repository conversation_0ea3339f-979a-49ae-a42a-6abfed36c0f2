<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>

<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">       

<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>  
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>   
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>

<script type="text/javascript" src="../ngoaitru/NGT02K070_ThuocHaiSao_New.js?v=20190161"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>




<div width="100%" id="divMain-put" style="border: 0px;">
		

	<div class="tab-content" id="thuoc2sao"> 
	    <div><a href="#" class="scrollToTop before"></a></div>  
		<div id="tabPhanUngThuoc" class="tab-pane active" style="margin-left: 15px;">
			<!-- <input type="hidden" name="hidPHANUNGTHUOCID" id="hidPHANUNGTHUOCID" value="" /> -->
			<input type="hidden" name="hidKHAMBENHID" id="hidKHAMBENHID" value="" />
			<input type="hidden" name="hidLOAIHOICHAN" id="hidLOAIHOICHAN" value="2" />
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
					<div class="panel-heading">Chẩn đoán bệnh nhiễm khuẩn:</div>
					<div class="panel-body">
						<div class=" form-inline">
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-3 control-label'>VP cộng đồng</label> <input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkVPCONGDONG" name="chkVPCONGDONG" title="">
							</div>
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-4 control-label'>Shock nhiễm khuẩn</label> <input
												class="form-control input-sm i-col-3" type="checkbox"
												id="chkSHOCK_NK" name="chkSHOCK_NK" title="">
							</div>
							
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-4 control-label'>Nhiễm khuẩn huyết</label> <input
												class="form-control input-sm i-col-3" type="checkbox"
												id="chkNK_HUYET" name="chkNK_HUYET" title="">
							</div>
						</div>
						<div class=" form-inline">
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-3 control-label'>VP bệnh viện</label> <input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkVPBENHVIEN" name="chkVPBENHVIEN" title="">
							</div>
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-4 control-label'>Viêm màng não/các NK trung ương</label> <input
												class="form-control input-sm i-col-3" type="checkbox"
												id="chkNK_TRUNGUONG" name="chkNK_TRUNGUONG" title="">
							</div>
							
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-4 control-label'>NK da, mô mềm</label> <input
												class="form-control input-sm i-col-3" type="checkbox"
												id="chkNK_DA" name="chkNK_DA" title="">
							</div>
						</div>
						<div class=" form-inline">
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-3 control-label'>VP thở máy</label> <input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkVPTHOMAY" name="chkVPTHOMAY" title="">
							</div>
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-4 control-label'>NK tiết niệu</label> <input
												class="form-control input-sm i-col-3" type="checkbox"
												id="chkNK_TIETNIEU" name="chkNK_TIETNIEU" title="">
							</div>
							
							<div class="col-xs-4 low-padding">
								<div class=" form-inline">
									<label
													class='l-col-4 control-label'>Nhiễm khuẩn khác:</label>
													
								</div>
							</div>
						</div>
						<div class=" form-inline">
							<div class="col-xs-4 low-padding">
								
							</div>
							<div class="col-xs-4 low-padding">
								<label
												class='l-col-4 control-label'>NK ổ bụng</label> <input
												class="form-control input-sm i-col-3" type="checkbox"
												id="chkNK_OBUNG" name="chkNK_OBUNG" title="">
							</div>
							
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm" style="width: 100%;" 
													id="txtNK_KHAC" name="txtNK_KHAC"  maxlength="1500"
													valrule="Nhiễm khuẩn khác,max_length[1500]" 
													title=""  >
							</div>
						</div>
						<div class=" form-inline mgt5">
							<div class="col-xs-2 low-padding">
								<label class='control-label'>Bệnh mắc kèm khác:</label>
							</div>
							<div class="col-xs-10 low-padding">
								<input class="form-control input-sm" style="width: 100%;" 
								id="txtBENH_KEMKHAC" name="txtBENH_KEMKHAC" 
								valrule="Bệnh mắc kèm khác,max_length[2000]" 
								title=""  >
							</div>
						</div>
						<div class="form-inline" >
							<div class="col-xs-9 low-padding">
								<div class="form-inline">
									<div class="col-xs-12 low-padding">
										<label class='l-col-3 control-label '>Tình trạng lâm sàng sau hội chẩn:</label>
									</div>
								</div>
								<div class="form-inline">
									<div class="col-xs-12 low-padding">
										<textarea name="txtTINHTRANG_LS" valrule="Tình trạng lâm sàng sau hội chẩn,max_length[1900]"
											class="form-control input-sm i-col-3 " id="txtTINHTRANG_LS"
											style="width: 100%" maxlength="1900"
											rows="2"></textarea>
									</div>
								</div>
							</div>	
							<div class="col-xs-1 low-padding">
							</div>
							<div class="col-xs-2 low-padding" >
								<div class=" form-inline">
									<div class="col-xs-12 low-padding">
										<label class='l-col-3 control-label '>Nhiệt độ:</label>
									</div>
								</div>
								<div class=" form-inline">
									<div class="col-xs-12 low-padding">
										<input class="form-control input-sm" style="width: 100%;" 
										id="txtNHIETDO" name="txtNHIETDO" maxlength="3"
										valrule="Nhiệt độ,max_length[3]" 
										title=""  >
									</div>
								</div>
							</div>
							
						</div>
						
						<div class="mgt5 form-inline" >
							<div class="col-xs-4 low-padding">
								<label class='control-label'>KQ cận lâm sàng gần nhất:&nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp   Bạch cầu</label>
							</div>
							<div class="col-xs-2 low-padding">
								<input class="form-control input-sm" style="width: 100%;" 
								id="txtBACHCAU" name="txtBACHCAU" 
								valrule="Bạch cầu,max_length[20]" 
								maxlength="10"
								title=""  >
							</div>
							<div class="col-xs-1 low-padding">
								<label class='mgl5 control-label'>CRP:</label>
							</div>
							<div class="col-xs-2 low-padding">
								<input class="form-control input-sm" style="width: 100%;" 
								id="txtCRP" name="txtCRP" 
								maxlength="10"
								valrule="CRP,max_length[20]" 
								title=""  >
							</div>
							<div class="col-xs-1 low-padding">
								<label class='mgl5 control-label'>PCT:</label>
							</div>
							<div class="col-xs-2 low-padding">
								<input class="form-control input-sm" style="width: 100%;" 
								id="txtPCT" name="txtPCT" 
								maxlength="10"
								valrule="PCT,max_length[10]" 
								title=""  >
							</div>
						</div>
						
						<div class="mgt5 form-inline" >
							<div class="col-xs-12 low-padding">
								<div class="form-inline">
									<div class="col-xs-12 low-padding">
										<label class='l-col-3 control-label '>Khác(dịch não tủy, nước tiểu, chẩn đoán hình ảnh...)</label>
									</div>
								</div>
								<div class="form-inline">
									<div class="col-xs-12 low-padding">
										<textarea name="txtKHAC_DICHNT" valrule="Khác(dịch não tủy, nước tiểu, chẩn đoán hình ảnh,max_length[1900]"
											class="form-control input-sm i-col-3 " id="txtKHAC_DICHNT"
											style="width: 100%" maxlength="1900"
											rows="2"></textarea>
									</div>
								</div>
							</div>	
						</div>
						<div class="mgt5 form-inline" >
							<div class="col-xs-12 low-padding">
								<label class='l-col-3 control-label '>Thanh thải creatinin(ml/ph):</label>
							</div>
						</div>
						<div class="mgt5 form-inline" >
							<div class="col-xs-6 low-padding">
								<div class="form-inline">
									<div class="col-xs-12 low-padding">
										<textarea name="txtTHANHTHAI" valrule="Thanh thải creatinin(ml/ph),max_length[500]"
											class="form-control input-sm i-col-3 " id="txtTHANHTHAI"
											style="width: 100%" maxlength="500"
											rows="2"></textarea>
									</div>
								</div>
							</div>	
							<div class="col-xs-6 low-padding">
							<div class="form-inline">
								<div class="form-inline">
									<div class=" form-inline">
											<label class='mgl5 l-col-3 control-label'>Lọc máu HD(24/48/72) giờ:</label>
											<div class="radio">
												<label><input type="radio" name="radLOCMAUHD" value="0" id="radLOCMAUHD"></label><label style="font-weight: 700 !important; font-size: 0.9em;">Có</label>
											</div> 
											<div class="radio mgl10">
												<label><input type="radio" name="radLOCMAUHD" value="1" id="radLOCMAUHD"></label><label style="font-weight: 700 !important; font-size: 0.9em;">Không</label>
											</div>  
									</div>
								</div>
								<div class="form-inline">
									<div class=" form-inline">
											<label class='mgl5 l-col-3 control-label'>Lọc máu liên tục:</label>
											<div class="radio">
												<label><input type="radio" name="radLOCMAULT" value="0" id="radLOCMAULT"></label><label style="font-weight: 700 !important; font-size: 0.9em;">Có</label>
											</div> 
											<div class="radio mgl10">
												<label><input type="radio" name="radLOCMAULT" value="1" id="radLOCMAULT"></label><label style="font-weight: 700 !important; font-size: 0.9em;">Không</label>
											</div>  
									</div>
								</div>
							</div>	
						</div>
					</div>
			</div> <!-- END FORM INLINE -->
				
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">Xét nghiệm vi sinh:(Không làm ghi rõ lý do?)
				</div>
				<div class="panel-body">
					<div class=" form-inline">
					<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Bệnh phẩm,max_length[2000]"
											id="txtKHONGLAM_VS" maxlength="2000" name="txtKHONGLAM_VS"
											title="">
					</div>
					<div class=" form-inline mgt5">
						<div class="col-xs-12 low-padding">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th style="font-size: x-small; text-align: center;">TT</th>
										<th style="font-size: x-small; text-align: center;">Bệnh phẩm</th>
										<th style="font-size: x-small; text-align: center;">Ngày cấy</th>
										<th style="font-size: x-small; text-align: center;">Ngày trả</th>
										<th style="font-size: x-small; text-align: center;">Kết quả vi khuẩn/nấm kháng sinh đồ</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>01</td>
										<td style="width: 25%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Bệnh phẩm,max_length[1000]"
											id="txtBENHPHAM1" maxlength="1000" name="txtBENHPHAM1"
											title=""></td>
										<td style="width: 15%">
											<div class="input-group">
											<input class="form-control input-sm" valrule="Ngày cấy,date"
												style="float: right; z-index: 0!important;" id="txtNGAYCAP1"
												name="txtNGAYCAP1" title="" data-mask="00/00/0000"
												placeholder="dd/MM/yyyy"> <span
												class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
												type="sCal" 
												onclick="NewCssCal('txtNGAYCAP1','ddMMyyyy','dropdown',false,'24',true)"></span>
											</div>
										</td>
										<td style="width: 15%">
											<div class="input-group">
											<input class="form-control input-sm" valrule="Ngày trả,date"
												style="float: right; z-index: 0!important;" id="txtNGAYTRA1"
												name="txtNGAYTRA1" title="" data-mask="00/00/0000"
												placeholder="dd/MM/yyyy"> <span
												class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
												type="sCal" 
												onclick="NewCssCal('txtNGAYTRA1','ddMMyyyy','dropdown',false,'24',true)"></span>
											</div>
										</td>
										<td style="width: 45%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Kết quả vi khuẩn,max_length[1000]"
											id="txtKQ_VIKHUAN1" maxlength="1000" name="txtKQ_VIKHUAN1"
											title=""></td>
									</tr>
									<tr>
										<td>02</td>
										<td style="width: 25%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Bệnh phẩm,max_length[1000]"
											id="txtBENHPHAM2" maxlength="1000" name="txtBENHPHAM2"
											title=""></td>
										<td style="width: 15%">
											<div class="input-group">
											<input class="form-control input-sm" valrule="Ngày cấy,date"
												style="float: right; z-index: 0!important;" id="txtNGAYCAP2"
												name="txtNGAYCAP2" title="" data-mask="00/00/0000"
												placeholder="dd/MM/yyyy"> <span
												class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
												type="sCal" 
												onclick="NewCssCal('txtNGAYCAP2','ddMMyyyy','dropdown',false,'24',true)"></span>
											</div>
										</td>
										<td style="width: 15%">
											<div class="input-group">
											<input class="form-control input-sm" valrule="Ngày trả,date"
												style="float: right; z-index: 0!important;" id="txtNGAYTRA2"
												name="txtNGAYTRA2" title="" data-mask="00/00/0000"
												placeholder="dd/MM/yyyy"> <span
												class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
												type="sCal" 
												onclick="NewCssCal('txtNGAYTRA2','ddMMyyyy','dropdown',false,'24',true)"></span>
											</div>
										</td>
										<td style="width: 45%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Kết quả vi khuẩn,max_length[1000]"
											id="txtKQ_VIKHUAN2" maxlength="1000" name="txtKQ_VIKHUAN2"
											title=""></td>
									</tr>
									<tr>
										<td>03</td>
										<td style="width: 25%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Bệnh phẩm,max_length[1000]"
											id="txtBENHPHAM3" maxlength="1000" name="txtBENHPHAM3"
											title=""></td>
										<td style="width: 15%">
											<div class="input-group">
											<input class="form-control input-sm" valrule="Ngày cấy,date"
												style="float: right; z-index: 0!important;" id="txtNGAYCAP3"
												name="txtNGAYCAP3" title="" data-mask="00/00/0000"
												placeholder="dd/MM/yyyy"> <span
												class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
												type="sCal" 
												onclick="NewCssCal('txtNGAYCAP3','ddMMyyyy','dropdown',false,'24',true)"></span>
											</div>
										</td>
										<td style="width: 15%">
											<div class="input-group">
											<input class="form-control input-sm" valrule="Ngày trả,date"
												style="float: right; z-index: 0!important;" id="txtNGAYTRA3"
												name="txtNGAYTRA3" title="" data-mask="00/00/0000"
												placeholder="dd/MM/yyyy"> <span
												class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
												type="sCal" 
												onclick="NewCssCal('txtNGAYTRA3','ddMMyyyy','dropdown',false,'24',true)"></span>
											</div>
										</td>
										<td style="width: 45%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Kết quả vi khuẩn,max_length[1000]"
											id="txtKQ_VIKHUAN3" maxlength="1000" name="txtKQ_VIKHUAN3"
											title=""></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th style="font-size: x-small; text-align: center;">Phác đồ KS đang điều trị(Ngày điều trị)</th>
										<th style="font-size: x-small; text-align: center;">Lý do dùng phác đồ KSƯTQL</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td style="width: 50%"><input class="form-control input-sm i-col-3" 
											style="width: 100%"
											valrule="Phác đồ KS đang điều trị,max_length[1000]"
											id="txtPHACDO_KS1" maxlength="1000" name="txtPHACDO_KS1"
											title=""></td>						
										<td style="width: 50%">
											<input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkKHONGDAPUNG" name="chkKHONGDAPUNG" title="">
												<label
												class='l-col-3 control-label'>Không/kém đáp ứng với phác đồ ban đầu</label></td>						
									</tr>
									<tr>
										<td style="width: 50%"><input class="form-control input-sm i-col-3" 
											style="width: 100%"
											valrule="Phác đồ KS đang điều trị,max_length[1000]"
											id="txtPHACDO_KS2" maxlength="1000" name="txtPHACDO_KS2"
											title=""></td>						
										<td style="width: 50%">
											<input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkKHANGTHUOC" name="chkKHANGTHUOC" title="">
											<label
												class='l-col-3 control-label'>KQ vi sinh, vi khuẩn đa/kháng thuốc</label></td>						
									</tr>
									<tr>
										<td style="width: 50%"><input class="form-control input-sm i-col-3" 
											style="width: 100%"
											valrule="Phác đồ KS đang điều trị,max_length[1000]"
											id="txtPHACDO_KS3" maxlength="1000" name="txtPHACDO_KS3"
											title=""></td>						
										<td style="width: 50%">
											<input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkDAGAP_ADR" name="chkDAGAP_ADR" title="">
											<label
												class='l-col-3 control-label'>Đang/đã từng gặp ADR với kháng sinh....</label></td>						
									</tr>
									<tr>
										<td style="width: 50%"><input class="form-control input-sm i-col-3" 
											style="width: 100%"
											valrule="Phác đồ KS đang điều trị,max_length[1000]"
											id="txtPHACDO_KS4" maxlength="1000" name="txtPHACDO_KS4"
											title=""></td>						
										<td style="width: 50%">
											<input
												class="form-control input-sm i-col-4" type="checkbox"
												id="chkLYDO_KHAC" name="chkLYDO_KHAC" title="">
											<label
												class='l-col-3 control-label'>Khác</label></td>						
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">Phác đồ kháng sinh yêu cầu</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th style="font-size: x-small; text-align: center;">Kháng sinh(tên hoạt chất, hàm lượng)</th>
										<th style="font-size: x-small; text-align: center;">Liều dùng/lần(liều nạp nếu có)</th>
										<th style="font-size: x-small; text-align: center;">Khoảng cách dùng</th>
										<th style="font-size: x-small; text-align: center;">Cách dùng</th>
										<th style="font-size: x-small; text-align: center;">Thời gian điều trị(Ngày)</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td style="width: 30%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Kháng sinh(tên hoạt chất, hàm lượng),max_length[1500]"
											id="txtKHANGSINH1" maxlength="1500" name="txtKHANGSINH1"
											title=""></td>
										<td style="width: 20%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Liều dùng một lần,max_length[1000]"
											id="txtLIEUDUNG1" maxlength="1000" name="txtLIEUDUNG1"
											title=""></td>
										<td style="width: 20%">
											<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Khoảng cách dùng,max_length[1000]"
											id="txtKHOANGCACH1" maxlength="1000" name="txtKHOANGCACH1"
											title="">
										</td>
										<td style="width: 15%">
											<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Cách dùng,max_length[1000]"
											id="txtCACHDUNG1" maxlength="1000" name="txtCACHDUNG1"
											title="">
										</td>
										<td ><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Thời gian điều trị,max_length[4]"
											id="txtTHOIGIAN_DT1" maxlength="4" name="txtTHOIGIAN_DT1"
											title=""></td>
									</tr>
									<tr>
										<td style="width: 30%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Kháng sinh(tên hoạt chất, hàm lượng),max_length[1500]"
											id="txtKHANGSINH2" maxlength="1500" name="txtKHANGSINH2"
											title=""></td>
										<td style="width: 20%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Liều dùng một lần,max_length[1000]"
											id="txtLIEUDUNG2" maxlength="1000" name="txtLIEUDUNG2"
											title=""></td>
										<td style="width: 20%">
											<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Khoảng cách dùng,max_length[1000]"
											id="txtKHOANGCACH2" maxlength="1000" name="txtKHOANGCACH2"
											title="">
										</td>
										<td style="width: 15%">
											<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Cách dùng,max_length[1000]"
											id="txtCACHDUNG2" maxlength="1000" name="txtCACHDUNG2"
											title="">
										</td>
										<td ><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Thời gian điều trị,max_length[4]"
											id="txtTHOIGIAN_DT2" maxlength="4" name="txtTHOIGIAN_DT2"
											title=""></td>
									</tr>
									<tr>
										<td style="width: 30%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Kháng sinh(tên hoạt chất, hàm lượng),max_length[1500]"
											id="txtKHANGSINH3" maxlength="1500" name="txtKHANGSINH3"
											title=""></td>
										<td style="width: 20%"><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Liều dùng một lần,max_length[1000]"
											id="txtLIEUDUNG3" maxlength="1000" name="txtLIEUDUNG3"
											title=""></td>
										<td style="width: 20%">
											<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Khoảng cách dùng,max_length[1000]"
											id="txtKHOANGCACH3" maxlength="1000" name="txtKHOANGCACH3"
											title="">
										</td>
										<td style="width: 15%">
											<input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Cách dùng,max_length[1000]"
											id="txtCACHDUNG3" maxlength="1000" name="txtCACHDUNG3"
											title="">
										</td>
										<td ><input style="width: 100%" class="form-control input-sm i-col-3" 
											valrule="Thời gian điều trị,max_length[4]"
											id="txtTHOIGIAN_DT3" maxlength="4" name="txtTHOIGIAN_DT3"
											title=""></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			
			<div class="form-inline panel panel-info" style="margin-bottom: 65px;">
				<div class="panel-heading">Ý kiến của ban QLSDKS:</div>
				<div class="panel-body">
					<div class=" form-inline">
							<label class='l-col-3 control-label'>Thống nhất sử dụng kháng sinh như trên:</label>
							<div class="radio">
								<label><input type="radio" name="radTHONGNHATSUDUNG" value="0" id="radTHONGNHATSUDUNG"></label><label style="font-weight: 700 !important; font-size: 0.9em;">Có</label>
							</div> 
							<div class="radio mgl10">
								<label><input type="radio" name="radTHONGNHATSUDUNG" value="1" id="radTHONGNHATSUDUNG"></label><label style="font-weight: 700 !important; font-size: 0.9em;">Không</label>
							</div>  
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>Ý kiến khác:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtYKIENKHAC" valrule="Ý kiến khác,max_length[1900]"
											class="form-control input-sm i-col-3 " id="txtYKIENKHAC"
											style="width: 100%" maxlength="1900"
											rows="2"></textarea>
						</div>
					</div>
				</div>			
			</div> <!-- END FORM INLINE -->
			
		</div>		
		
		
		<div class="form-inline btn-fixed">
			<div class="col-xs-12 low-padding mgt20 mgb10" style="text-align: center;" id="gridButton">

				<button class="btn btn-sm btn-primary" id="btnLuu">
					<span class="glyphicon glyphicon-pencil"></span> Lưu
				</button>
				<button class="btn btn-sm btn-primary" id="btnCapNhat">
					<span class="glyphicon glyphicon-floppy-remove"></span> Cập nhật
				</button>
				<!-- <button class="btn btn-sm btn-primary" id="btnXoa">
					<span class="glyphicon glyphicon-floppy-remove"></span> Xóa
				</button>
				<button class="btn btn-sm btn-primary" id="btnInBieuMau">
					<span class="fixDisplay glyphicon glyphicon-print"></span> In biểu
					mẫu
				</button> -->
				<button class="btn btn-sm btn-primary" id="btnHuy">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
			</div>
		</div>
	</div>
</div>
		<script>
			var opt = [];
			var hospital_id = '{hospital_id}';
			var user_id = '{user_id}';
			var dept_id = '{dept_id}';
			var user_type = '{user_type}';
			var province_id = '{province_id}';
			var uuid = '{uuid}';
			var db_schema='{db_schema}';
			var lang = "vi";
			console.log('hospital_id=' + hospital_id + ' user_type='
					+ user_type);
			var session_par = [];
			session_par[0] = hospital_id;
			session_par[1] = user_id;
			session_par[2] = user_type;
			session_par[3] = province_id;
			session_par[4] = db_schema;
			var table_name = '{table}';

			var _opts = new Object();
			var mode = '{showMode}';				
			if(mode=='dlg') {
				parent.DlgUtil.tunnel(DlgUtil.moveEvent);
				data=EventUtil.getVar("dlgVar");
				_opts.mabenhnhan = data.mabenhnhan;
				_opts.DICHVUKHAMBENHID = data.DICHVUKHAMBENHID;
				_opts.TIEPNHANID = data.TIEPNHANID;
				_opts.KHAMBENHID = data.KHAMBENHID;

			}

		    _opts._param = session_par;
			_opts._uuid = uuid;
			initRest(uuid,"/vnpthis");
			var thuoc2sao = new NGT02K070_ThuocHaiSao(_opts);
			thuoc2sao.load();
		</script>