function dSCHList(_opts) {

	var _gridDSCHId = "grdDSBNKLN";
	var _gridDSCHHeader = "MÃ BA,MAHOSOBENHAN,80,0,f,l,1,2;" +
		"<PERSON><PERSON><PERSON> b<PERSON><PERSON> nhân,TENBENHNHAN,130,0,f,l,1,2;" +
		"<PERSON><PERSON><PERSON>,NGAYKHAM,80,0,f,l,1,2;" +
		"<PERSON><PERSON><PERSON> b<PERSON>,LOAIBENH_TEXT,90,0,f,l,1,2;" +
		"Tr<PERSON>ng thá<PERSON>,TRANGTHAI_TEXT,70,0,f,l,1,2;" +
		"HOSOBENHANID,HOSOBENHANID,0,0,t,l,1,2;" +
		"GIOITINHID,GIOITINHID,0,0,t,l,1,2;" +
		"NGAYSINH,NGAYSINH,0,0,t,l,1,2;" +
		"NGHENGHIEP,NGHENGHIEP,0,0,t,l,1,2;" +
		"DIACH<PERSON>,DIACHI,0,0,t,l,1,2;" +
		"SDTBENHNHAN,SDTBENHNHAN,0,0,t,l,1,2;" +
		"NGAYPHATHIEN,NGAYPHATHIEN,0,0,t,l,1,2;" +
		"NOIPHATHIEN,NOIPHATHIEN,0,0,t,l,1,2;" +
		"PHANLOAIBN,PHANLOAIBN,0,0,t,l,1,2;" +
		"HATAMTHU,HATAMTHU,0,0,t,l,1,2;" +
		"HATAMTRUONG,HATAMTRUONG,0,0,t,l,1,2;" +
		"CANNANG,CANNANG,0,0,t,l,1,2;" +
		"CHIEUCAO,CHIEUCAO,0,0,t,l,1,2;" +
		"VONGEO,VONGEO,0,0,t,l,1,2;" +
		"HUTTHUOCLA,HUTTHUOCLA,0,0,t,l,1,2;" +
		"MUCDORUOUBIA,MUCDORUOUBIA,0,0,t,l,1,2;" +
		"GIAMANMUOI,GIAMANMUOI,0,0,t,l,1,2;" +
		"RAUTRAICAY_400G,RAUTRAICAY_400G,0,0,t,l,1,2;" +
		"HOATDONGTHELUC,HOATDONGTHELUC,0,0,t,l,1,2;" +
		"CHANDOAN,CHANDOAN,0,0,t,l,1,2;" +
		"BIENCHUNG,BIENCHUNG,0,0,t,l,1,2;" +
		"KQDIEUTRI,KQDIEUTRI,0,0,t,l,1,2;" +
		"THUOCDIEUTRI,THUOCDIEUTRI,0,0,t,l,1,2;" +
		"DUONGHUYET,DUONGHUYET,0,0,t,l,1,2;" +
		"HbA1C,HbA1C,0,0,t,l,1,2;" +
		"ROILOANLIPIDMAU,ROILOANLIPIDMAU,0,0,t,l,1,2;" +
		"TDBIENCHUNGBANCHAN,TDBIENCHUNGBANCHAN,0,0,t,l,1,2;" +
		"BIETXULYHADUONGHUYET,BIETXULYHADUONGHUYET,0,0,t,l,1,2;" +
		"LOAIBENH,LOAIBENH,0,0,t,l,1,2";

	var _gridLSKBKLNId = "grdLSKBKLN";
	var _gridLSKBKLNHeader =
		"Tên bệnh nhân,TENBENHNHAN,150,0,t,l,1,2;" +
		"Loại bệnh,LOAIBENH_TEXT,100,0,f,l,1,2;" +
		"MÃ BA,MAHOSOBENHAN,100,0,f,l,1,2;" +
		"Ngày khám,NGAYKHAM,80,0,f,l,1,2;" +
		"Trạng thái,TRANGTHAI_TEXT,100,0,f,l,1,2;" +
		"HOSOBENHANID,HOSOBENHANID,0,0,t,l,1,2;" +
		"GIOITINHID,GIOITINHID,0,0,t,l,1,2;" +
		"NGAYSINH,NGAYSINH,0,0,t,l,1,2;" +
		"NGHENGHIEP,NGHENGHIEP,0,0,t,l,1,2;" +
		"DIACHI,DIACHI,0,0,t,l,1,2;" +
		"SDTBENHNHAN,SDTBENHNHAN,0,0,t,l,1,2;" +
		"NGAYPHATHIEN,NGAYPHATHIEN,0,0,t,l,1,2;" +
		"NOIPHATHIEN,NOIPHATHIEN,0,0,t,l,1,2;" +
		"PHANLOAIBN,PHANLOAIBN,0,0,t,l,1,2;" +
		"HATAMTHU,HATAMTHU,0,0,t,l,1,2;" +
		"HATAMTRUONG,HATAMTRUONG,0,0,t,l,1,2;" +
		"CANNANG,CANNANG,0,0,t,l,1,2;" +
		"CHIEUCAO,CHIEUCAO,0,0,t,l,1,2;" +
		"VONGEO,VONGEO,0,0,t,l,1,2;" +
		"HUTTHUOCLA,HUTTHUOCLA,0,0,t,l,1,2;" +
		"MUCDORUOUBIA,MUCDORUOUBIA,0,0,t,l,1,2;" +
		"GIAMANMUOI,GIAMANMUOI,0,0,t,l,1,2;" +
		"RAUTRAICAY_400G,RAUTRAICAY_400G,0,0,t,l,1,2;" +
		"HOATDONGTHELUC,HOATDONGTHELUC,0,0,t,l,1,2;" +
		"CHANDOAN,CHANDOAN,0,0,t,l,1,2;" +
		"BIENCHUNG,BIENCHUNG,0,0,t,l,1,2;" +
		"KQDIEUTRI,KQDIEUTRI,0,0,t,l,1,2;" +
		"THUOCDIEUTRI,THUOCDIEUTRI,0,0,t,l,1,2;" +
		"DUONGHUYET,DUONGHUYET,0,0,t,l,1,2;" +
		"HbA1C,HbA1C,0,0,t,l,1,2;" +
		"ROILOANLIPIDMAU,ROILOANLIPIDMAU,0,0,t,l,1,2;" +
		"TDBIENCHUNGBANCHAN,TDBIENCHUNGBANCHAN,0,0,t,l,1,2;" +
		"BIETXULYHADUONGHUYET,BIETXULYHADUONGHUYET,0,0,t,l,1,2;" +
		"LOAIBENH,LOAIBENH,0,0,t,l,1,2;" +
		"Phân loại BN,PHANLOAIBN_TEXT,150,0,f,l,1,2;" +
		"Chẩn đoán,CHANDOAN_TEXT,100,0,f,l,1,2;" +
		"Biến chứng,BIENCHUNG_TEXT,100,0,f,l,1,2;" +
		"Kết quả điều trị,KQDIEUTRI_TEXT,100,0,f,l,1,2";

	var _user_id = -1;
	var _hospital_id;
	flagLoading = false;
	_param = [];
	_cauhinhId = -1;
	var checkRequired;
	var valid_ar = [];
	var that = this;
	this.load = doLoad;
	var checkRequired;
	var isEdit = false;
	var isCopy = false;

	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		var _options = $.extend({}, _opts);
		var uuid = _options._uuid;
		var _param = _options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_initControl();
		_bindEvent();
	}
	function _initControl() {

		GridUtil.init(_gridDSCHId, "100%", "420", "Danh sách bệnh nhân", false, _gridDSCHHeader, false, { rowNum: 10000, rowList: [10000] });
		GridUtil.init(_gridLSKBKLNId, "100%", "220", "Lịch sử khám bệnh", false, _gridLSKBKLNHeader, false, { rowNum: 10000, rowList: [10000] });
		GridUtil.addExcelButton(_gridDSCHId, 'Xuất excel', true);
		sql_par = [_opts.user_id, _opts.db_schema, "*******", _opts.hospital_id, "0"];

		$('#toolbarIdtxtFromDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		$('#toolbarIdtxtToDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		setEnabled([], ['btnSua', 'btnXoa', 'btnLuu', 'btnHuy', "txtNGAYPHATHIEN", "cboNOIPHATHIEN", "txtNGAYKHAM",
			"cboPHANLOAIBN", "txtHATAMTHU", "txtHATAMTRUONG", "txtCANNANG", "txtCHIEUCAO", "txtVONGEO",
			"cboHUTTHUOCLA", "cboMUCDORUOUBIA", "cboGIAMANMUOI", "cboRAUTRAICAY_400G",
			"cboHOATDONGTHELUC", "cboCHANDOAN", "cboBIENCHUNG", "cboKQDIEUTRI", "txtTHUOCDIEUTRI",
			"txtDUONGHUYET", "txtHbA1C", "cboROILOANLIPIDMAU", "cboTDBIENCHUNGBANCHAN",
			"cboBIETXULYHADUONGHUYET"]);
		loadGridData();
	};



	function _bindEvent() {

		GridUtil.setGridParam(_gridDSCHId, {
			onSelectRow: function (id) {
				if (isEdit || isCopy) {
					return;
				}
				GridUtil.unmarkAll(_gridDSCHId);
				GridUtil.markRow(_gridDSCHId, id);

				if (id) {
					if (flagLoading) return;
					var _row = $("#" + _gridDSCHId).jqGrid('getRowData', id);
					setupForm(_row.LOAIBENH)
					setEnabled(["btnSua", "btnXoa"], ["btnLuu", "btnHuy", "btnSaoChep"]);
					loadGridDataLSKB(_row.HOSOBENHANID, _row.LOAIBENH);
					FormUtil.setObjectToForm("inputForm", "", _row);
					$('#txtHbA1C').val(_row.HbA1C);//L2PT-109194
				}
			},
		});

		GridUtil.setGridParam(_gridLSKBKLNId, {
			onSelectRow: function (id) {
				if (isEdit || isCopy) {
					return;
				}
				GridUtil.unmarkAll(_gridLSKBKLNId);
				GridUtil.markRow(_gridLSKBKLNId, id);

				if (id) {
					if (flagLoading) return;
					var _row = $("#" + _gridLSKBKLNId).jqGrid('getRowData', id);
					FormUtil.setObjectToForm("inputForm", "", _row);
					setEnabled(["btnSaoChep"], ["btnLuu", "btnHuy", "btnSua", "btnXoa"]);
				}
			},
			gridComplete: function (id) {
				let _rowids = $("#" + _gridLSKBKLNId).getDataIDs();
				if (_rowids && _rowids.length > 0) {
					_rowids.forEach(function (rowid) {
						let _row = $("#" + _gridLSKBKLNId).jqGrid('getRowData', rowid);
						let phanLoaiBN_text = "", chanDoan_text = "", bienChung_text = "", kQDieuTri_text = "";
						if (_row.PHANLOAIBN)
							phanLoaiBN_text = $('#cboPHANLOAIBN option[value=' + _row.PHANLOAIBN + ']').text();
						if (_row.CHANDOAN)
							chanDoan_text = $('#cboCHANDOAN option[value=' + _row.CHANDOAN + ']').text();
						if (_row.BIENCHUNG)
							bienChung_text = $('#cboBIENCHUNG option[value=' + _row.BIENCHUNG + ']').text();
						if (_row.KQDIEUTRI)
							kQDieuTri_text = $('#cboKQDIEUTRI option[value=' + _row.KQDIEUTRI + ']').text();

						$("#" + _gridLSKBKLNId).jqGrid('setCell', rowid, 'PHANLOAIBN_TEXT', phanLoaiBN_text);
						$("#" + _gridLSKBKLNId).jqGrid('setCell', rowid, 'CHANDOAN_TEXT', chanDoan_text);
						$("#" + _gridLSKBKLNId).jqGrid('setCell', rowid, 'BIENCHUNG_TEXT', bienChung_text);
						$("#" + _gridLSKBKLNId).jqGrid('setCell', rowid, 'KQDIEUTRI_TEXT', kQDieuTri_text);
					})
				}
			},
		});

		$("#btnSua").click(function () {
			isEdit = true;
			flagLoading = true;
			setEnabled(['btnLuu', 'btnHuy', "txtNGAYPHATHIEN", "cboNOIPHATHIEN",
				"cboPHANLOAIBN", "txtHATAMTHU", "txtHATAMTRUONG", "txtCANNANG", "txtCHIEUCAO", "txtVONGEO",
				"cboHUTTHUOCLA", "cboMUCDORUOUBIA", "cboGIAMANMUOI", "cboRAUTRAICAY_400G",
				"cboHOATDONGTHELUC", "cboCHANDOAN", "cboBIENCHUNG", "cboKQDIEUTRI", "txtTHUOCDIEUTRI",
				"txtDUONGHUYET", "txtHbA1C", "cboROILOANLIPIDMAU", "cboTDBIENCHUNGBANCHAN",
				"cboBIETXULYHADUONGHUYET"],
				['btnSua', 'btnXoa', "btnSaoChep"]);
		});

		$("#btnLuu").click(function () {
			save();
		});

		$("#btnXoa").click(function () {
			DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko ?", function (flag) {
				if (flag) {
					let hosobenhanid = $('#hidHOSOBENHANID').val();
					let loaibenh = $('#txtLOAIBENH').val();
					var objData = {
						"HOSOBENHANID": hosobenhanid,
						"LOAIBENH": loaibenh
					};
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT01T010.XOAKQ", JSON.stringify(objData));
					if (fl == 1) {
						DlgUtil.showMsg("Xóa thành công !");
					}
					else {
						DlgUtil.showMsg("Xảy ra lỗi !");
					}
					FormUtil.clearForm('inputForm', "");
					setEnabled([], ['btnSua', 'btnXoa', 'btnLuu', 'btnHuy', "btnSaoChep", "txtNGAYPHATHIEN", "cboNOIPHATHIEN", "txtNGAYKHAM",
						"cboPHANLOAIBN", "txtHATAMTHU", "txtHATAMTRUONG", "txtCANNANG", "txtCHIEUCAO", "txtVONGEO",
						"cboHUTTHUOCLA", "cboMUCDORUOUBIA", "cboGIAMANMUOI", "cboRAUTRAICAY_400G",
						"cboHOATDONGTHELUC", "cboCHANDOAN", "cboBIENCHUNG", "cboKQDIEUTRI", "txtTHUOCDIEUTRI",
						"txtDUONGHUYET", "txtHbA1C", "cboROILOANLIPIDMAU", "cboTDBIENCHUNGBANCHAN",
						"cboBIETXULYHADUONGHUYET"]);
				}
				loadGridData();
			});
		});

		$("#btnSaoChep").click(function () {
			isCopy = true;
			// flagLoading = true;
			setEnabled(['btnLuu', 'btnHuy', "txtNGAYPHATHIEN", "cboNOIPHATHIEN",
				"cboPHANLOAIBN", "txtHATAMTHU", "txtHATAMTRUONG", "txtCANNANG", "txtCHIEUCAO", "txtVONGEO",
				"cboHUTTHUOCLA", "cboMUCDORUOUBIA", "cboGIAMANMUOI", "cboRAUTRAICAY_400G",
				"cboHOATDONGTHELUC", "cboCHANDOAN", "cboBIENCHUNG", "cboKQDIEUTRI", "txtTHUOCDIEUTRI",
				"txtDUONGHUYET", "txtHbA1C", "cboROILOANLIPIDMAU", "cboTDBIENCHUNGBANCHAN",
				"cboBIETXULYHADUONGHUYET"],
				['btnSua', 'btnXoa', "btnSaoChep"]);
			// DlgUtil.showConfirm("Bạn có muốn sao chép ghi này ko ?", function (flag) {
			// 	if (flag) {
			// 		save();
			// 	}
			// });
		});


		$("#btnHuy").click(function () {
			FormUtil.clearForm("inputForm", "");
			setEnabled([], ['btnXoa', 'btnLuu', 'btnHuy', "btnSaoChep", "txtNGAYPHATHIEN", "cboNOIPHATHIEN", "txtNGAYKHAM",
				"cboPHANLOAIBN", "txtHATAMTHU", "txtHATAMTRUONG", "txtCANNANG", "txtCHIEUCAO", "txtVONGEO",
				"cboHUTTHUOCLA", "cboMUCDORUOUBIA", "cboGIAMANMUOI", "cboRAUTRAICAY_400G",
				"cboHOATDONGTHELUC", "cboCHANDOAN", "cboBIENCHUNG", "cboKQDIEUTRI", "txtTHUOCDIEUTRI",
				"txtDUONGHUYET", "txtHbA1C", "cboROILOANLIPIDMAU", "cboTDBIENCHUNGBANCHAN",
				"cboBIETXULYHADUONGHUYET"]);
			isEdit = false;
			isCopy = false;
			flagLoading = false;
			resetRequired();
		});

		$('#btnTimkiem').on('click', function () {
			loadGridData();
		});



	}

	function loadGridData() {
		if (flagLoading)
			return;
		var param_ar = {
			"TUNGAY": $("#toolbarIdtxtFromDate").val(),
			"DENNGAY": $("#toolbarIdtxtToDate").val(),
			"LOAIBENHID": $("#cboLOAIBENH_ID").val(),
			"TRANGTHAIID": $("#cboTRANGTHAI_ID").val(),
		};
		var param_str = JSON.stringify(param_ar);
		var param = RSUtil.buildParam("", [param_str]);
		GridUtil.loadGridBySqlPage(_gridDSCHId, "NGT01T010.DS.02", param);
	}

	function loadGridDataLSKB(hosobenhanid, loaibenh) {
		if (flagLoading)
			return;
		if (!hosobenhanid)
			return;
		var param_ar = {
			"HOSOBENHANID": hosobenhanid,
			"LOAIBENH": loaibenh
		};
		var param_str = JSON.stringify(param_ar);
		var param = RSUtil.buildParam("", [param_str]);
		GridUtil.loadGridBySqlPage(_gridLSKBKLNId, "NGT01T010.LSKB.02", param);
	}

	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
		}
	}

	function save() {
		var _validator=new DataValidator("divMain");
		var valid= _validator.validateForm();
		if (valid) {
			var _validator = new DataValidator([{ region: 'divMain', fields: valid_ar }]);
			var valid = _validator.validateForm();
			if (!valid) {
				return false;
			}
			var objData = new Object();
			FormUtil.setFormToObject("inputForm", "", objData);

			var selectedRow = $("#" + _gridDSCHId).jqGrid('getGridParam', 'selrow');
			if (selectedRow) {
				var rowData = $("#" + _gridDSCHId).jqGrid('getRowData', selectedRow);
				objData['HOSOBENHANID'] = rowData.HOSOBENHANID;
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT01T010.LUUKQ", JSON.stringify(objData));
				flagLoading = false;
				if (fl == 1) {
					DlgUtil.showMsg("Thành công !");
					loadGridData();
					setEnabled([], ['btnSua', 'btnXoa', 'btnLuu', 'btnHuy', "btnSaoChep", "txtNGAYPHATHIEN", "cboNOIPHATHIEN", "txtNGAYKHAM",
						"cboPHANLOAIBN", "txtHATAMTHU", "txtHATAMTRUONG", "txtCANNANG", "txtCHIEUCAO", "txtVONGEO",
						"cboHUTTHUOCLA", "cboMUCDORUOUBIA", "cboGIAMANMUOI", "cboRAUTRAICAY_400G",
						"cboHOATDONGTHELUC", "cboCHANDOAN", "cboBIENCHUNG", "cboKQDIEUTRI", "txtTHUOCDIEUTRI",
						"txtDUONGHUYET", "txtHbA1C", "cboROILOANLIPIDMAU", "cboTDBIENCHUNGBANCHAN",
						"cboBIETXULYHADUONGHUYET"]);
				}
				else {
					DlgUtil.showMsg("Không thành công !");
				}
				isEdit = false;
				isCopy = false;
			}
		}
	}

	function resetRequired() {
		$('#cboPHANLOAIBN').css('background-color', '');
		$('#txtHATAMTHU').css('background-color', '');
		$('#txtHATAMTRUONG').css('background-color', '');
	}

	function setupForm(loaibenh) {
		var optionPLBN = "";
		var optionCD = "";
		if (loaibenh == '1') { // THA
			$(".daithaoduong_form").css("display", "none");
			$("#RAUTRAICAY_400G_lbl").text("Ăn đủ 400g rau và trái cây");
			$("#txtDUONGHUYET").removeAttr("valrule");
			$("#cboROILOANLIPIDMAU").removeAttr("valrule");
			$("#cboTDBIENCHUNGBANCHAN").removeAttr("valrule");
			$("#cboBIETXULYHADUONGHUYET").removeAttr("valrule");
			optionPLBN = '<option value="1">Bệnh nhân mới</option>'
				+ '<option value="2">Bệnh nhân cũ</option>'
				+ '<option value="3">Bỏ, chuyển</option>'
				+ '<option value="4">Chết</option>'
				+ '<option value="5">Bệnh nhân quản lý (không cấp thuốc)</option>';

			optionCD = '<option value="1">THA độ I</option>'
				+ '<option value="2">THA độ II</option>'
				+ '<option value="3">THA độ III</option>'
				+ '<option value="4">THA được kiểm soát</option>'
				+ '<option value="5">Khác/chi tiết</option>';
		} else {			// ĐTĐ
			$(".daithaoduong_form").css("display", "block");
			$("#RAUTRAICAY_400G_lbl").text("Thực hành ăn uống hợp lý");
			$("#txtDUONGHUYET").attr("valrule", "Đường huyết,required");
			$("#cboROILOANLIPIDMAU").attr("valrule", "Rối loạn lipid máu,required");
			$("#cboTDBIENCHUNGBANCHAN").attr("valrule", "Theo dõi biến chứng bàn chân,required");
			$("#cboBIETXULYHADUONGHUYET").attr("valrule", "Biết xử lý hạ đường huyết,required");

			optionPLBN = '<option value="0">Lần đầu tiên đến khám và lấy thuốc</option>'
				+ '<option value="1">Mới chuyển về (trước đây được QLĐT tại CSYT khác)</option>'
				+ '<option value="2">Bệnh nhân cũ</option>'
				+ '<option value="3">Bỏ, chuyển</option>'
				+ '<option value="4">Chết</option>'
				+ '<option value="5">Bệnh nhân quản lý (không cấp thuốc)</option>';

			optionCD = '<option value="1">ĐTĐ típ 1</option>'
				+ '<option value="2">ĐTĐ típ 2</option>'
				+ '<option value="3">ĐTĐ thai kỳ</option>'
				+ '<option value="4">Khác/chi tiết</option>';
		}
		$("#cboPHANLOAIBN").html(optionPLBN);
		$("#cboCHANDOAN").html(optionCD);
	}

	function dateRegEx(date) {
		var pattern = new RegExp("^(3[01]|[12][0-9]|0[1-9])/(1[0-2]|0[1-9])/[0-9]{4}$");
		if (date.search(pattern) === 0) {
			return true;
		} else {
			return false;
		}
	}

	function getValue(data) {
		var pos;
		pos = data.indexOf('-');
		if (pos > 0) {
			return data.substring(0, pos);
		}
		else {
			return "-1";
		}
	}
}
