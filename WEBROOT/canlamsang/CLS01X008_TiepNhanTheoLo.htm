<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.src.js"></script>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20160105" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../canlamsang/ParaclinicalUtility.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS01X008_TiepNhanTheoLo.js?v=20190925"></script>

<style>
	.ui-jqgrid tr.jqgrow td {
		white-space: nowrap !important;
		height:auto;
		vertical-align:text-top;
		padding-top:2px;
	}
	.barcodeStyle{
		border: 1px solid #dcdcdc;
	    width: 100%;
	    margin-left: 1px;
	    margin-top: 0px;
	    background-color: lightgoldenrodyellow;
	    height: 29px;
	    color: red !important;
	    font-family: Helvetica, Sans-Serif !important;
	    font-size: 1.2em !important;
	    font-weight: bold !important;
	    border-radius: 3px;
	    padding: 0px 10px !important;
	}
	input .barcodeStyle {
		color: red !important;
		font-family: Helvetica, Sans-Serif !important;
		font-size: 1.2em !important;
		font-weight: bold !important;
	}
</style>

<div id="divMain">
	<div class="mgt5" id="divThongTinBenhNhan">
		<div class="col-md-12 low-padding" style="display:none">
			<div class="col-md-7 low-padding">
				<div class="col-md-3 low-padding">
					<label class="">Phòng thực hiện</label> 
				</div>
				<div class="col-md-9 low-padding">
					<select class="form-control input-sm" id="cboPhongThucHien" style="width:90%">
						<option value=""></option>
					</select>
				</div>	
			</div>			
			<div class="col-md-5 low-padding">
				<div class="col-md-1 low-padding">
					<label>Từ</label>
				</div>
				<div class="col-md-4 low-padding"> 
					<div class='input-group'>							
						<input class="form-control input-sm" id="txtTuNgay" valrule="Từ ngày,datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div>
				</div>
				<div class="col-md-1 low-padding"></div>
				<div class="col-md-1 low-padding">
					<label>Đến</label> 
				</div>
				<div class="col-md-4 low-padding"> 
					<div class='input-group'>							
						<input class="form-control input-sm" id="txtDenNgay" valrule="Đến ngày,datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div> 
				</div>
				<div class="col-md-1 low-padding"></div>
			</div>
		</div>
		<div class="col-md-12 low-padding">
			<b>Hướng dẫn: Quét barcode trên tất cả các phiếu chỉ định để tiếp nhận đồng thời nhiều bệnh nhân</b>
		</div>
		<div class="col-md-12 low-padding">
			<div class="col-md-11 low-padding">
				<input class="barcodeStyle" id="txtBarcode" onfocus="this.select();" />
			</div>
			<div class="col-md-1 low-padding">
				<button class="mgl5 btn btn-sm btn-primary" id="btnThem">
					<span class="glyphicon glyphicon-search"></span> Thêm
				</button> 
			</div>
		</div>
	</div>
	<div id="divThongBao">
		<div class="col-md-12 low-padding">
			
		</div>
	</div>
	<div id="divDanhSachTiepNhan">
		<div class="col-md-12 low-padding">	
			<table id="grdDanhSachTiepNhan"></table>
			<div id="pager_grdDanhSachTiepNhan"></div>
		</div>
	</div>
	
	<div id="divCommand">
		<div class="col-md-12 low-padding">
			<div class="col-md-9 low-padding">	
				<label id="lblThongBao" style="font-size: 14px; color: red; padding: 0px !important"></label>
			</div>
			<div class="col-md-3 low-padding">
				<button class="btn btn-sm btn-primary" id="btnTiepNhan">
					<span class="glyphicon glyphicon-save"></span> Tiếp nhận (F4)
				</button>
				<button class="btn btn-sm btn-primary" id="btnThoat">
					<span class="glyphicon glyphicon-log-out"></span> Đóng (ESC)
				</button>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">	
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var ten_khoa = '{dept_name}';
	var lang = "vn";
	var type = "{type}";
	console.log('user_id='+user_id+', schema='+schema+', province_id='+province_id+', hospital_id='+hospital_id+', type='+type);
	
	var session_par=[];
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	session_par[0]=hospital_id;

	var mode = '{showMode}';
	var _var1;
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		_var1=EventUtil.getVar("dlgVar");
	}
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	
	initRest(_opts._uuid);
	
	var tt = new DanhSachTiepNhan(_opts);
	tt.load();
</script>