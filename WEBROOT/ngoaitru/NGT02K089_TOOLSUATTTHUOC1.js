function dSCHList(_opts) {
	var _gridPKDK ="grdPKDK"; 
	
	var _gridPKDKHeader= "phong<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,PHONGKHAMDANGKYID,80,0,t,l,1,2;"
							+ "k<PERSON><PERSON><PERSON><PERSON>,KHAMBENHID,80,0,f,l,1,2;"
							+ "<PERSON>ã HSBA,MAHOSOBENHAN,80,0,f,l,1,2;"
							+ "Mã BN,MABENHNHAN,80,0,f,l,1,2;"
							+ "Tên bệnh nhân,TENBENHNHAN,150,0,f,l,1,2;"
							+ "<PERSON><PERSON><PERSON> sinh,NGAYSINH,80,0,f,l,1,2;"
							+ "Ng<PERSON><PERSON> BĐ,NGAY_BD,80,0,f,l,1,2;"
							+ "Ngày KT,NGAY_KT,80,0,f,l,1,2;"
							+ "<PERSON><PERSON> b<PERSON><PERSON> sỹ,BSKHAMBENHID,50,0,t,l,1,2;"
							+ "<PERSON><PERSON> b<PERSON><PERSON> sỹ,MABACSY,50,0,f,l,1,2;"
							+ "<PERSON><PERSON><PERSON>,TENPHONG,150,0,f,l,1,2;"
							+ "<PERSON><PERSON><PERSON> khá<PERSON>,TENDICHVU,150,0,f,l,1,2;"
							+ "Thời gian vào viện,THOIGIANVAOVIEN,80,0,f,l,1,2;"
							+ "Mã nơi GT,MANOIGIOITHIEU,40,0,f,l,1,2;"
							+ "Mã chẩn đoán TD,MACHANDOANTUYENDUOI,40,0,f,l,1,2;"
							+ "Chẩn đoán TD,CHANDOANTUYENDUOI,100,0,f,l,1,2"
							;
	
	var _user_id=-1;
	var _hospital_id;
	flagLoading = false;
	_param=[];
	_cauhinhId = -1;
	var checkRequired;
	var validator = null;
	var valid_ar = [];
	
	var that=this;
	this.load=doLoad;
	
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		validator = new DataValidator("inputForm");
		var _options=$.extend({},_opts);
		var uuid = _options._uuid;
	    _param=_options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_initControl();
		_bindEvent();	
	}
	
	function _initControl(){
		GridUtil.init(_gridPKDK,"100%","200","Cập nhật nơi giới thiệu, CĐ tuyến dưới",false,_gridPKDKHeader, 
				false, { rowNum: 20,rowList: [20, 100, 300]});
	};
	
	function _bindEvent() {
		$("#btnSEARCH").on("click", function(){
			_hsbaid = $("#txtMAHOSOBENHAN").val().trim(); 
			if(_hsbaid == ""){
				DlgUtil.showMsg("Hồ sơ bệnh án không được để trống");
				return; 
			}
			loadGridDataPKDK();
		});
		
		GridUtil.setGridParam(_gridPKDK,{
			onSelectRow: function(id) {
				GridUtil.unmarkAll(_gridPKDK);
	    		GridUtil.markRow(_gridPKDK,id);	
			}
			, gridComplete: function(id){
	        	$(".jqgrow", "#"+_gridPKDK).contextMenu('contextMenuPKDK', {
	    			bindings: {
	    				'updateMABSI': function (t) {
	        				var rowId = $(t).attr("id");	    					
	        				var rowData = $("#"+_gridPKDK).jqGrid('getRowData', rowId);     					
	    					
	        				var paramInput={							
        						loai : "3", 
			   					phongkhamdangkyid : rowData.PHONGKHAMDANGKYID,
			   					khambenhid : rowData.KHAMBENHID, 
			   					mabacsy : rowData.BSKHAMBENHID, 
			   					manoigioithieu : rowData.MANOIGIOITHIEU, 
			   					machandoantuyenduoi : rowData.MACHANDOANTUYENDUOI, 
			   					chandoantuyenduoi : rowData.CHANDOANTUYENDUOI, 
			   					mahosobenhan : $("#txtMAHOSOBENHAN").val().trim()
		   					};			
	    					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K089_SUACHITIET",paramInput,"Cập nhật mã bác sỹ",500,250);
		   					DlgUtil.open("divDlgDichVu");
	    				}
	    			}
	    		}); 
			}
		});
		
		EventUtil.setEvent("ev_dongcuaso", function(e) {
			DlgUtil.close("divDlgDichVu");
			loadGridDataPKDK(); 
		});
	}
	
	function loadGridDataPKDK() {
		var _sql_par =[];
		_sql_par.push({"name":"[0]","value":_hsbaid});	
		GridUtil.loadGridBySqlPage(_gridPKDK,"NGT02K089.LAYDL1",_sql_par);
	}
	
}
