<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	   value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
		src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
		src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
		src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
		src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	  href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	  rel="stylesheet" />
<script type="text/javascript"
		src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
		src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>


<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
		src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/script/jquery/jQuery.print.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask.js"></script>
<script type="text/javascript" src="../common/script/jquery/jcanvas.min.js"></script>
<script type="text/javascript" src="../common/script/chart.js"></script>
<script type="text/javascript" src="../common/utility/Utility.js"></script>
<script type="text/javascript" src="../common/script/third-party-caller.js"></script>
<script type="text/javascript" src="../common/script/sign/sign-hub.js"></script>
<script type="text/javascript" src="../common/script/sign/sign-pdf.js"></script>
<script type="text/javascript" src="../common/multidatepicker/jquery-ui.multidatespicker.js"></script>
<link href="../common/multidatepicker/jquery-ui.multidatespicker.css" rel="stylesheet" />

<script type="text/javascript"
		src="../danhmuc/ImageEditor_Demo.js?v=20210920"></script>

<div id="menu_loading" class="hide">
	<span class="spinner"></span>
</div>
<div width="100%" id="divMain" class="container" tabIndex="0">
	<div class="row">
		<img id="10420" width="200px" src="../upload/getdata.jsp?id=10420">
		<button data-url="../upload/getdata.jsp?id=10420" class="btn btn-sm btn-primary" id="btnImageEditor">
			<span class="glyphicon glyphicon-edit"></span> Editor
		</button>
		<button class="btn btn-sm btn-primary" id="btnImageSave">
			<span class="glyphicon glyphicon-edit"></span> Save
		</button>

		<button class="btn btn-sm btn-primary" id="btnImageDemo">
			<span class="glyphicon glyphicon-edit"></span> demo
		</button>
	</div>
	<div class="row" style="padding-top: 20px">
		<canvas id="cv_test"></canvas>
	</div>

	<div class="col-md-1 low-padding">
		<div class="input-group" style="width: 100%;">
			<input class="form-control input-sm" id="txtNGAYTHEODOI" name="txtNGAYTHEODOI"
				   title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" valrule="Ngày bắt đầu theo dõi,required"
				   maxlength="10">
			<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"
				  onclick="CalMultiDatesPicker.show('txtNGAYTHEODOI')"></span>
		</div>

		<div class="input-group" style="width: 100%;">
			<input class="form-control input-sm" id="txtNGAYTHEODOI2" name="txtNGAYTHEODOI2"
				   title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" valrule="Ngày bắt đầu theo dõi,required"
				   maxlength="10">
			<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"
				  onclick="NewCssCal('txtNGAYTHEODOI2','ddMMyyyy','dropdown',false,'24',true)"></span>
		</div>
	</div>
</div>
<script>
	var DS = new dSCHList();
	DS.load();
</script>