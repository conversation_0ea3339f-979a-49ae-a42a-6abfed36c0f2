/*
 * Nhập kết quả xét nghiệm
 * 
 * VietDA	04/08/2016		tạo mới
 *  
 */

function KetQuaXetNghiem(opt){
	this.load = doLoad;
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;

		initControl();
		bindEvent();
		
		/* nhận dữ liệu từ form cha Danh sách bệnh phẩm */
		$("#hdfIDMauBenhPham").val(opt._idmaubenhpham);
		$("#hdfIDDichVuKB").val(opt._iddichvukb);
		console.log("id mau benh pham="+opt._idmaubenhpham+", id dich vu kb="+opt._iddichvukb+",rolePTH: "+opt._rolePTH);
		
		loadThongTinBenhNhan();
		loadDanhSachKetQua();
		
		$("#txtBarcode").focus();
	}

	function initControl(){
		var $grid = $("#grdKetQuaXetNghiem");

		$grid.jqGrid({
              datatype: 'local',
              colNames: ['','','Mã xét nghiệm','Tên xét nghiệm','Kết quả', 
                         'Trị số bình thường','Trạng thái',
                         'KETQUACLSID','DICHVUTHUCHIENID','DICHVUKHAMBENHID'],
              colModel: [
                  {name: 'ICON', width: 20, search: false },
                  {name: 'TENCHIDINH', width: 30, hidden:true },				  
                  {name: 'MADICHVU', width: 100, formatter:GridUtil.fm.formatStrEncode },
                  {name: 'TENDICHVU', width: 250, formatter:GridUtil.fm.formatStrEncode },
                  {name: 'GIATRI_KETQUA', formatter:GridUtil.fm.formatStrEncode },
                  {name: 'TRISOBINHTHUONG', width: 150, formatter:GridUtil.fm.formatStrEncode },
                  {name: 'TENTRANGTHAI', width: 70, formatter:GridUtil.fm.formatStrEncode },
                  {name: 'KETQUACLSID', hidden:true},
                  {name: 'DICHVUTHUCHIENID', hidden:true},
                  {name: 'DICHVUKHAMBENHID', hidden:true}
              ],
              rowNum: 50,
			  rowList: [20, 50, 100],
              pager: '#pager_grdKetQuaXetNghiem',
              gridview: true,
              ignoreCase: true,
              rownumbers: true,
              viewrecords: true,
              //sortorder: 'desc',
              width:"100%",
              autowidth:true,
              editurl: 'clientArray',
              grouping: true,
	  		  groupingView: {
	  				groupField : ['TENCHIDINH'],
	  				groupColumnShow : [false],
	  				groupText : ['<b>{0}</b>']
	  		  },
              caption: 'Danh sách kết quả',
              onSelectRow: function (rowid) {
            	  GridUtil.unmarkAll("grdKetQuaXetNghiem");
				  GridUtil.markRow("grdKetQuaXetNghiem", rowid);
              }
		});          
		$grid.jqGrid('navGrid', '#pager_grdKetQuaXetNghiem', {edit: false, add: false, del: false});	    
		$grid.jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		$('#gview_grdKetQuaXetNghiem').find('.ui-jqgrid-bdiv').attr("style","height: 256px !important");
		GridUtil.setWidthPercent("grdKetQuaXetNghiem","100%");		
		
		//nghiant 28082017 
		if(opt._rolePTH != "true"){
			$("#btnInBarcode").attr('disabled','true');
			$("#btnLuuBarcode").attr('disabled','true');
		}
		//End nghiant 
	}
	
	function bindEvent(){		
		$('#btnInBarcode').on('click', function () {			
			inMaVach();			
		});

		$('#txtBarcode').keydown(function (e) {
			if (e.keyCode == 13) {
				capBarcodeVaTiepNhan();
			}
		});
		
		$('#btnLuuBarcode').on('click', function () {			
			capBarcodeVaTiepNhan();			
		});

		$('#btnThoat').on('click', function () {			
			EventUtil.raiseEvent("CLS01X007_Thoat",{msg:''});		
		});
	}
	
	// load thong tin benh nhan
	function loadThongTinBenhNhan(){
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
		var row = data_ar[0];

		$("#lblHoTen").text(row["TENBENHNHAN"]==null?"":row["TENBENHNHAN"]);
		$("#chbKhan").prop('checked', row["LOAIMAUBENHPHAM"]=="2"?true:false);
		$("#lblDiaChi").text(row["DIACHI"]==null?"":row["DIACHI"]);
		$("#lblSoPhieu").text(row["SOPHIEU"]==null?"":row["SOPHIEU"]);
		$("#lblNoiGui").text(row["TENPHONG"]==null?"":row["TENPHONG"]);
		$("#lblDoiTuong").text(row["DOITUONG"]==null?"":row["DOITUONG"]);
		$("#lblTGChiDinh").text(row["NGAYDICHVU"]==null?"":row["NGAYDICHVU"]);
		$("#lblTGKetQua").text(row["NGAYMAUBENHPHAM_HOANTHANH"]==null?"":row["NGAYMAUBENHPHAM_HOANTHANH"]);
		$("#txtBarcode").val(row["BARCODE"]==null?"":row["BARCODE"]);
		$("#lblBacSi").text(row["TENBACSI"]==null?"":row["TENBACSI"]);
		$("#lblChanDoan").text(row["CHANDOAN"]==null?"":row["CHANDOAN"]);			
	}
	
	// load danh sach chi dinh
	function loadDanhSachKetQua(){
		_sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,$("#hdfIDMauBenhPham").val()]);
		GridUtil.loadGridBySqlPage("grdKetQuaXetNghiem","CLS01X002.DSKQ2",_sql_par);
	}
	
	function capBarcodeVaTiepNhan(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+
					$("#txtBarcode").val();

		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X007.BAR", param);
		if(rs==0){
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TNBP", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Tiếp nhận thành công",function(){EventUtil.raiseEvent("CLS01X007_TiepNhanBP",{msg:''});},1000);
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể tiếp nhận",function(){$("#txtBarcode").focus();},3000);
			}
		}
		else if(rs==1){
			DlgUtil.showMsg("Không thể cấp barcode cho bệnh phẩm đã xử lý",function(){$("#txtBarcode").focus();},2000);
		}
		else if(rs==2){
			DlgUtil.showMsg("Độ dài barcode giới hạn từ 4 đến 6 kí tự",function(){$("#txtBarcode").focus();},2000);
			$("#txtBarcode").focus();
		}
		else if(rs==3){
			DlgUtil.showMsg("Barcode này đã được cấp phát",function(){$("#txtBarcode").focus();},2000);
		}
		else if(rs==4){
			DlgUtil.showMsg("Không thể cấp barcode cho bệnh phẩm đã trả kết quả",function(){$("#txtBarcode").focus();},2000);
		}
		else {
			DlgUtil.showMsg("Có lỗi xảy ra",function(){$("#txtBarcode").focus();},3000);
		}
	}
	
	function tiepNhanBenhPham(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				$("#hdfIDMauBenhPham").val()+'$'+
				$("#hdfIDDichVuKB").val();
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TNBP", param);
		
		if(rs == '1'){
			DlgUtil.showMsg("Tiếp nhận bệnh phẩm thành công",function(){$("#txtBarcode").focus();},1000);
		} else {
			DlgUtil.showMsg("Có lỗi xảy ra, không thể tiếp nhận",function(){$("#txtBarcode").focus();},1000);
		}
	}
	
	// in mã vạch dán vào mẫu bệnh phẩm
	function inMaVach(){
		if($("#txtBarcode").val()==""){
			DlgUtil.showMsg("Chưa nhập mã vạch cần In",undefined,3000);
		}
		else {
			var par = [ 
				{name:'barcode',type:'String',value:$("#txtBarcode").val()}
			];

			openReport('window', 'CHG_MAUBARCODE_A6', 'pdf', par);
		}
	}
}
