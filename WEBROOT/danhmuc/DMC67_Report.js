function dSCHList(_opts) {
    let _gridDSCHId = "grddvkt_chinh";
    let _gridDSCHHeader = "Id,report_id,30,0,t,l,1,2;" + "Report Name,report_name,150,0,f,l,1,2;" + "Report Path,report_path,300,0,f,l,1,2;" + "Report Code,report_code,200,0,f,l,1,2;" + "Table List,table_list,150,0,f,l,1,2;" + "Group ID,group_id,80,0,f,l,1,2;" + "Company ID,company_id,80,0,f,l,1,2";

    let _user_id = -1;
    let _hospital_id;
    let rpt_schema;
    let flagLoading = false;
    let _param = [];
    let _cauhinhId = -1;
    let user_level_current = -1;

    let isCopy = false;
    let rowToCopy;
    let isEdit = false;

    this.load = doLoad;

    function doLoad(_hosp_id) {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        let _options = $.extend({}, _opts);
        let _param = _options._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        rpt_schema = _param[4];
        _initControl();
        _bindEvent();
        $("input.timepicker").timepicker({});
    }

    function _initControl() {

        GridUtil.init(_gridDSCHId, "100%", "390", "Danh sách Report", false, _gridDSCHHeader, false, {
            rowNum: 20, rowList: [20, 40, 60]
        });
        loadGridData();
        ComboUtil.getComboTag("cboGROUP_ID", "DMC67.REPORT", [], "", "", "sql");

        if (_hospital_id == '10284') {
            $("#lbInform").show();
        }

        //var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC60.02",_user_id);

        /*if (data_ar != null && data_ar.length > 0) {
              var row=data_ar[0];
              user_level_current = row['USER_LEVEL'];
        }*/

        //addNumberOnly($("#cboGROUP_ID"));
        addNumberOnly($("#txtIS_PUBLIC"));
        addNumberOnly($("#txtCOMPANY_ID"));
        addNumberOnly($("#txtNOT_PDF"));

    };

    function _bindEvent() {

        //---------Grid khoa

        GridUtil.setGridParam(_gridDSCHId, {
            onSelectRow: function (id) {
                GridUtil.unmarkAll(_gridDSCHId);
                GridUtil.markRow(_gridDSCHId, id);

                if (id) {
                    if (flagLoading) return;
                    isCopy = true;
                    let _row = $("#" + _gridDSCHId).jqGrid('getRowData', id);
                    _cauhinhId = _row.report_id;

                    let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC67.02", rpt_schema + '$' + _cauhinhId);

                    if (data_ar != null && data_ar.length > 0) {
                        let row = data_ar[0];
                        FormUtil.clearForm("inputForm", "");
                        FormUtil.setObjectToForm("inputForm", "", row);
                        rowToCopy = row;
                        if (row.GROUP_ID == 20) {
                            $('#divPATH').show();
                        } else {
                            $('#divPATH').hide();
                        }
                        if (row.GROUP_ID == 99) {
                            $('#divURL_HMIS').show();
                            $('.hmis_element').show();
                        } else {
                            $('#divURL_HMIS').hide();
                            $('.hmis_element').hide();
                        }

                        if (parseInt(row.GROUP_ID) >= 111) {
                            $('#btnConnector').attr('disabled', false);
                        } else {
                            $('#btnConnector').attr('disabled', true);
                        }
                    }
                    setEnabled(['btnThem', 'btnSua', 'btnXoa', 'btnCopy', 'btnImage', 'btnReloadCachePQ'], ['btnLuu', 'btnHuy', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2']);
                }
            }, ondblClickRow: function (id) {
                if (_hospital_id == '10284') {
                    let _row = $("#" + _gridDSCHId).jqGrid('getRowData', id);
                    let myVar = {REPORT_ID: _row.report_id};
                    let dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg", "manager.jsp?func=../danhmuc/DMC67_VIEWUSER_RP", myVar, "Danh sách cấp quyền người dùng", 800, 600);
                    dlgPopup.open("dlgInPhieuXuat");
                }
            }
        });

        $('#cboGROUP_ID').on('change', function () {
            if ($('#cboGROUP_ID').val() == 20) {
                $('#divPATH').show();
            } else {
                $('#divPATH').hide();
            }
            if ($('#cboGROUP_ID').val() == 99) {
                $('#divURL_HMIS').show();
                $('.hmis_element').show();
            } else {
                $('#divURL_HMIS').hide();
                $('.hmis_element').hide();
            }
        });

        //====================================== ====== ===============================
        $("#btnThem").click(function () {
            isEdit = false;
            flagLoading = true;
            FormUtil.clearForm('inputForm', "");
            $("#txtCOMPANY_ID").val(_hospital_id);
            setEnabled(['btnLuu', 'btnHuy', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2'], ['btnThem', 'btnSua', 'btnXoa', 'btnCopy', 'btnImage']);
        });


        $("#btnSua").click(function () {
            flagLoading = true;
            isEdit = true;
            setEnabled(['btnLuu', 'btnHuy', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2'], ['btnThem', 'btnSua', 'txtREPORT_CODE', 'btnXoa', 'btnCopy', 'btnImage']);
        });

        $("#btnHuy").click(function () {
            if (isEdit) setEnabled(['btnSua', 'btnThem', 'btnXoa', 'btnCopy', 'btnImage'], ['btnLuu', 'btnHuy', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtCOMPANY_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2']);
            else setEnabled(['btnThem'], ['btnSua', 'btnLuu', 'btnHuy', 'btnXoa', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtCOMPANY_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'btnCopy', 'btnImage', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2']);
            flagLoading = false;
            isCopy = false;
        });

        $(".timepicker").bind('keypress', function (evt) {
            let key = String.fromCharCode(evt.which || evt.charCode);
            if (/(^\d$)|(^\d\d$)|(^\d\d\:$)|(^\d\d\:\d$)|(^\d\d\:\d\d$)/.test($(this).val() + key) === false) evt.preventDefault();
        });

        $("#btnLuu").click(function () {
            let _validator = new DataValidator("inputForm");
            let valid = _validator.validateForm();
            if (!valid) {
                return false;
            }

            let locktime_from = $("#txtLOCK_TIME_FROM").val();
            let locktime_to = $("#txtLOCK_TIME_TO").val();

            let locktime_from_2 = $("#txtLOCK_TIME_FROM2").val();
            let locktime_to_2 = $("#txtLOCK_TIME_TO2").val();

            if ($("#chkIS_LOCK_BY_TIME")[0].checked) {
                if (locktime_from.length != 5 || !Date.parse('10/10/2010 ' + locktime_from)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (FROM)!");
                    return false;
                }

                if (locktime_to.length != 5 || !Date.parse('10/10/2010 ' + locktime_to)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (TO)!");
                    return false;
                }

                if (Date.parse('10/10/2010 ' + locktime_from) > Date.parse('10/10/2010 ' + locktime_to)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (FROM lớn hơn TO)!");
                    return false;
                }
            }

            if ($("#chkIS_LOCK_BY_TIME2")[0].checked) {
                if (locktime_from_2.length != 5 || !Date.parse('10/10/2010 ' + locktime_from_2)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (FROM 2)!");
                    return false;
                }

                if (locktime_to_2.length != 5 || !Date.parse('10/10/2010 ' + locktime_to_2)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (TO 2)!");
                    return false;
                }

                if (Date.parse('10/10/2010 ' + locktime_from_2) > Date.parse('10/10/2010 ' + locktime_to_2)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (FROM 2 lớn hơn TO 2)!");
                    return false;
                }
            }

            if ($("#chkIS_LOCK_BY_TIME2")[0].checked && $("#chkIS_LOCK_BY_TIME")[0].checked) {
                if (Date.parse('10/10/2010 ' + locktime_to) > Date.parse('10/10/2010 ' + locktime_from_2)) {
                    DlgUtil.showMsg("Thời gian chặn không hợp lệ (TO lớn hơn FROM 2)!");
                    return false;
                }
            }

            if ($('#cboGROUP_ID').val() == 20 && $('#txtPATH_DIRECT').val() == '') {
                DlgUtil.showMsg("Hãy nhập để biết phiếu in tích hợp ở đâu !");
                return false;
            }
            if ($('#cboGROUP_ID').val() == 99 && $('#txtURL_HMIS').val() == '') {
                DlgUtil.showMsg("Hãy nhập đường dẫn API lấy báo cáo !");
                return false;
            }
            let objData = new Object();
            FormUtil.setFormToObject("inputForm", "", objData);

            if (isEdit) {
                objData["REPORT_ID"] = _cauhinhId;
            }

            let param = [rpt_schema, JSON.stringify(objData)];

            let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC67.03", param.join('$'));

            flagLoading = false;
            isCopy = false;
            if (fl == 1) {
                DlgUtil.showMsg("Thêm mới thành công !");
                loadGridData()
            } else if (fl == 2) {
                DlgUtil.showMsg("Cập nhật thành công !");
                loadGridData()
            } else if (fl == 0) {
                DlgUtil.showMsg("Report đã có !");
            } else if (fl == 3) {
                DlgUtil.showMsg("Không tìm thấy Report!");
            } else if (fl == 4) {
                DlgUtil.showMsg("Không có quyền thao tác với bản ghi này!");
            } else {
                DlgUtil.showMsg("Không thành công !");
            }

            setEnabled(["btnThem"], ["btnXoa", "btnSua", "btnLuu", "btnHuy", 'btnXoa', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'btnCopy', 'btnImage', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2']);
        });

        $("#btnXoa").click(function () {
            DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko?", function (flag) {
                if (flag) {
                    let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC67.04", rpt_schema + '$' + _cauhinhId);

                    if (fl == 1) {
                        DlgUtil.showMsg("Xóa thành công!");
                        loadGridData();
                        flagLoading = false;
                    } else if (fl == 0) {
                        DlgUtil.showMsg("Cần xóa hết các report con SUBREPORT và REPORT_FIELD!");
                    }
                    else if (fl == 2) {
                        DlgUtil.showMsg("Không có quyền thao tác với bản ghi này!");
                    }
                    else {
                        DlgUtil.showMsg("Xảy ra lỗi!");
                    }
                    setEnabled(['btnThem'], ['btnSua', 'btnXoa', 'btnLuu', 'btnHuy', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'btnCopy', 'btnImage', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2']);
                    flagLoading = false;
                }
            });
        });


        $("#btnCopy").click(function () {
            flagLoading = true;
            isEdit = false;
            if (isCopy) {
                FormUtil.clearForm("inputForm", "");
                FormUtil.setObjectToForm("inputForm", "", rowToCopy);
                $("#txtCOMPANY_ID").val(_hospital_id);
                setEnabled(['btnLuu', 'btnHuy', 'txtREPORT_NAME', 'txtREPORT_PATH', 'txtREPORT_CODE', 'txtTABLE_LIST', 'cboGROUP_ID', 'txtIS_PUBLIC', 'txtNOT_PDF', 'txtPATH_DIRECT', 'txtURL_HMIS', 'txtORDER_BY_KEY', 'txtSQL_FUNC', 'txtSQL_PAR_KEY', 'txtDESCRIPTION', 'chkIS_LOCK_BY_TIME', 'chkIS_LOCK_BY_TIME2', 'txtLOCK_TIME_FROM', 'txtLOCK_TIME_FROM2', 'txtLOCK_TIME_TO', 'txtLOCK_TIME_TO2'], ['btnThem', 'btnSua', 'btnXoa', 'btnCopy', 'btnImage']);
            }
        });

        $("#btnImage").click(function () {
            console.log(_cauhinhId);
            if (_cauhinhId === -1) {
                DlgUtil.showMsg("Vui lòng chọn report.");
                return;
            }
            let myVar = {
                report_id: _cauhinhId
            };
            DlgUtil.buildPopupUrl("dlgReportImages", "divDlg", "manager.jsp?func=../danhmuc/DMC67_Report_Images", myVar, "Report Images Template", window.innerWidth - 25, window.innerHeight - 85);
            DlgUtil.open("dlgReportImages");
        });

        $("#btnConnector").click(function () {
            console.log(_cauhinhId);
            if (_cauhinhId === -1) {
                DlgUtil.showMsg("Vui lòng chọn report.");
                return;
            }
            let myVar = {
                report_id: _cauhinhId
            };
            DlgUtil.buildPopupUrl("dlgReportConnector", "divDlg", "manager.jsp?func=../danhmuc/DMC67_Report_Connector", myVar, "Report Connector", 1000, 600);
            DlgUtil.open("dlgReportConnector");
        });

        $("#btnReloadCachePQ").click(function () {
            let hospitalId = $("#company_id").val();
            if (!hospitalId) {
                alert("Không thể lấy thông tin CSYTID");
                return;
            }
            let reportCode = $("#txtREPORT_CODE").val();
            if (!reportCode) {
                alert("Không thể lấy thông tin Report Code");
                return;
            }
            let reportGroup = $("#cboGROUP_ID").val();
            if (!reportGroup) {
                alert("Không thể lấy thông tin Report Group");
                return;
            }
            let idReport = Array
                .from($(`[aria-describedby="grddvkt_chinh_report_code"]`))
                .find(el => el.textContent == reportCode)
                .parentElement
                .querySelector(`[aria-describedby="grddvkt_chinh_report_id"]`)
                .textContent;
            if (!idReport) {
                alert("Không thể lấy thông tin Report Id");
                return;
            }
            let key = `1$${hospitalId}$1$${idReport}$prod`;
            if (reportGroup == 20) {
                key = `1$${hospitalId}$2$${reportCode}$prod`;
            }
            HisCacheUtil.DRC.clearWithKey("GET_REPORT", key);
        })
    }

    //======================================= Load Grid data Section ==================
    function loadGridData() {
        if (flagLoading) return;
        let _sql_par = [];
        _sql_par = RSUtil.setSysParam(_sql_par, _param);
        _sql_par.push({
            "name": "[0]", "value": rpt_schema
        });
        GridUtil.loadGridBySqlPage(_gridDSCHId, "DMC67.01", _sql_par);

    }

    function setEnabled(_ena, _dis) {
        for (const element of _ena) {
            $("#" + element).attr('disabled', false);
        }
        for (const element of _dis) {
            $("#" + element).attr('disabled', true);
        }
    }

    function addNumberOnly(element) {
        $(element).keypress(function (e) {
            if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
                return false;
            }
        });
    }

}

//# sourceURL=/vnpthis/danhmuc/DMC67_Report.js