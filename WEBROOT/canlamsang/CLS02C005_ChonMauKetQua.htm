<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../canlamsang/CLS02C005_ChonMauKetQua.js?v=20180508"></script>

<div id="divMain" class="container">	
	<div id="divChonMauKetQua">
		<div class="form-inline">
			<div class="col-md-12 low-padding">
				<table id="grdChonMauKetQua"></table>
				<div id="pager_grdChonMauKetQua"></div>
			</div>
		</div>
		<div class=" form-inline">
			<div class="col-md-4 low-padding">
			</div>
			<div class="col-md-8 low-padding mgt10 mgb10">
				<button class="btn btn-sm btn-primary" id="btnChon">
					<span class="glyphicon glyphicon-ok"></span> Chọn
				</button>
				<button class="btn btn-sm btn-primary" id="btnThoat">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng
				</button>	
			</div>
		</div>
	</div>
</div>

<input type="hidden" id="hdfIDMauKQ" value="">

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang= "vn";
	var idketquacls = '{idketquacls}';
	
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	
	var mode = '{showMode}';
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	}
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	
	initRest(_opts._uuid);
	
	var ttbn = new ChonMauKetQua(_opts);		
	ttbn.load();
</script>