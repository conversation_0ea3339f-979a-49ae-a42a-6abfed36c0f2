<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />

<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript"
	src="../ngoaitru/NGT04K001_LienKetTaiKhoan.js?v=20210717"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>

<div width="100%" id="divMain" class="container">
	<div class="form-inpfo mgt3" style="text-align: right;">Liên kết
		tài khoản</div>
	<div class="col-md-12 low-padding mgt5">
		<div class="col-md-6 low-padding mgt-10">
			<div class="col-md-12 low-padding">
				<div class="col-md-1 low-padding">
					<label class="mgl10">Mã BN</label>
				</div>
				<div class="col-md-3 low-padding">
					<input class="form-control input-sm" id="txtMABENHNHANSEARCH"
						title="">
				</div>
				<div class="col-md-1 low-padding" style="display:none">
					<label class="mgl10">SĐT</label>
				</div>
				<div class="col-md-3 low-padding" style="display:none">
					<input class="form-control input-sm" id="txtSDTBNSEARCH"
						title="">
				</div>
				<div class="col-md-4 low-padding">
					<button type="button" class="btn btn-sm btn-primary mgl5"
						id="btnTIMKIEM">Tìm kiếm</button>
				</div>
			</div>
			<div class="col-md-12 low-padding">
				<table id="grdBenhNhan"></table>
				<div id="pager_grdBenhNhan"></div>
			</div>
		</div>
		<div class="col-md-6 low-padding"
			style="padding-left: 15px !important;">
			<div id="inputForm" class="panel panel-default">
				<input type="text" id ="hidMANGHENGHIEP" style="display: none;"/>
				<input type="text" id ="hidMADANTOC" style="display: none;"/>
				<input type="text" id ="hidMAQT" style="display: none;"/>
				<input type="text" id ="hidMATINH" style="display: none;"/>
				<input type="text" id ="hidMAHUYEN" style="display: none;"/>
				<input type="text" id ="hidMAXA" style="display: none;"/>
				<input type="text" id ="hidDIACHI" style="display: none;"/>
				<input type="text" id ="hidSDTBENHNHAN" style="display: none;"/>
				<input type="text" id ="hidCMND" style="display: none;"/>
				<input type="text" id ="hidMA_BHYT" style="display: none;"/>
				<div class="panel-heading">Thông tin liên kết</div>
				<div class="panel-body">
					<div class="form-inline mgt5">
						<div class="col-md-12 " style="display: none;">
							<div class="col-md-2 low-padding required">
								<label class="mgl5">BNID</label>
							</div>
							<div class="col-md-4 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtBENHNHANID" name="txtBENHNHANID"
									title="" style="width:100%;" disabled>
							</div>
						</div>
						<div class="col-md-12 ">
							<div class="col-md-2 low-padding required">
								<label class="mgl5">Mã BN</label>
							</div>
							<div class="col-md-4 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtMABENHNHAN" name="txtMABENHNHAN"
									title="" style="width:100%;" disabled>
							</div>
							<div class="col-md-2 low-padding required">
								<label class="mgl5">Tên BN</label>
							</div>
							<div class="col-md-4 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtTENBENHNHAN" name="txtTENBENHNHAN"
									title="" style="width:100%;" disabled>
							</div>
						</div>
						<div class="col-md-12">
							<div class="col-md-12 low-padding" style="">
								<div class="col-md-2 low-padding required"
									style="margin-left: 5px; width: 82px;">
									<label class="">Ngày sinh</label>
								</div>
								<div class="col-md-4 low-padding" style="width: 100px;">
									<div class="input-group " style="width: 175px">
										<input class="form-control input-sm" id="txtNGAYSINH"
											name="txtNGAYSINH" maxlength="10" title=""
											data-mask="00/00/0000" placeholder="dd/MM/yyyy"
											maxlength="10" modeDis="" disabled> <span
											id="calNGAYSINH"
											class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
											type="sCal"
											onclick="NewCssCal('txtNGAYSINH','ddMMyyyy','dropdown',false,'24',true)"
											disabled></span>
									</div>
								</div>
								<div class="col-md-2 low-padding" style="margin-left: 78px;">
									<label class="">Giới tính</label>
								</div>
								<div class="col-md-4 low-padding"
									style="margin-left: -4px; width: 174px;">
									<select class="form-control input-sm kb-i-col-m "
										id="cboGIOITINHID" name="cboGIOITINHID" style="width: 100%;"
										disabled>
										<option value="-1">---Chọn---</option>
										<option value="1">Nam</option>
										<option value="2">Nữ</option>
									</select> </select>
								</div>

							</div>
						</div>
						<div class="col-md-12" style="display:none">
							<div class="col-md-12 low-padding" style="">
								<div class="col-md-2 low-padding required"
									style="margin-left: 5px; width: 82px;">
									<label class="">Số ĐT</label>
								</div>
								<div class="col-md-4 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtSDTBENHNHAN" name="txtSDTBENHNHAN"
									title="" style="width:100%;" disabled>
									</div>
								</div>
								<div class="col-md-2 low-padding" style="margin-left: 78px;">
								</div>
								<div class="col-md-4 low-padding"
									style="margin-left: -4px; width: 174px;">
								</div>

							</div>
						</div>
						<div class="col-md-12 ">
							<div class="col-md-2 low-padding required">
								<label class="mgl5">TK Liên kết</label>
							</div>
							<div class="col-md-10 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtTK_LIENKET" name="txtTK_LIENKET"
									title="" style="width:100%;" disabled>
							</div>
						</div>
					</div>
					<div class="form-inline mgt5">
						<div class="col-md-12" style="text-align: center;">
							<button type="button" class="btn btn-sm btn-primary mgl5" style="display: none;"
								id="btnLienket"><span class="glyphicon glyphicon-plus"></span>Liên kết</button>
<!-- 							<button type="button" class="btn btn-sm btn-primary mgl5" style="display: none;" -->
<!-- 								id="btnSua"><span class="glyphicon glyphicon-pencil"></span>Sửa liên kết</button> -->
							<button type="button" class="btn btn-sm btn-primary mgl5" style="display: none;"
								id="btnHuyLK"><span class="glyphicon glyphicon-trash"></span>Hủy liên kết</button>
							<button type="button" class="btn btn-sm btn-primary mgl5" style="display: none;"
								id="btnInphieu"><span class="glyphicon glyphicon-file"></span>In cam kết</button>
							<button type="button" class="btn btn-sm btn-primary mgl5" style="display: none;"
								id="btnGui"><span class="glyphicon glyphicon-ok"></span>Gửi</button>
							<button type="button" class="btn btn-sm btn-primary mgl5" style="display: none;"
								id="btnHuy"><span class="glyphicon glyphicon-remove"></span>Hủy</button>
						</div>
					</div>
				</div>
			</div>
		</div>

	</div>
</div>
</div>
<script>
	var userInfo = CommonUtil.decode('{userData}');
	var opt = [];
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var dept_id = '{dept_id}';
	var subdept_id = '{subdept_id}';
	var dept_name = '{dept_name}';
	var subdept_name = '{subdept_name}';
	var hospital_code = userInfo.HOSPITAL_CODE;
	var lang = "vn";
	var mode = '{showMode}';
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("PortalWS");
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';

	var _opts = new Object();

	_opts._param = session_par;
	_opts._uuid = uuid;
	_opts._deptId = dept_id;
	_opts.hospital_code = hospital_code;
	_opts.hospital_id = hospital_id;
	initRest(_opts._uuid);

	var dsbn = new NGT04K001_LienKetTaiKhoan(_opts);
	dsbn.load();
</script>
