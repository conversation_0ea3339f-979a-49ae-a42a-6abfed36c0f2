<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">       
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           


<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>  
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>   
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT03K008_ChonBacSyKham.js?v=20180101"></script>

<div class="container">
	<div class="row" style="padding-top:15px;">
	   	<div class="col-xs-12 low-padding">
	   		<div class="col-xs-1 low-padding">
				<label></label>
			</div>
			<div class="col-xs-1 low-padding">
				<label>Bác sĩ</label>
			</div>
			<div class="col-xs-2 low-padding">
				<input class="form-control input-sm i-col-m_fl" id="txtBSIKHAM" name="txtBSIKHAM" title="">
			</div>
			<div class="col-xs-7 low-padding">
				<select class="form-control input-sm" id="cboBACSIID" filterlike="txtBSIKHAM"></select>		
			</div>
		</div>
		 	
	   	<div class="col-xs-12 low-padding mgt20" style="text-align:center;">   		
	    	<button type="button" class="btn btn-sm btn-primary" id="btnLuu">
			      <span class="glyphicon glyphicon-floppy-disk"></span> Lưu
			</button>
			<button type="button" class="btn btn-sm btn-primary" id="btnHuy">
			      <span class="glyphicon glyphicon-remove-circle"></span> Đóng
			</button>
	    </div> 
    </div>
</div>

<script>
	var uuid = '{uuid}';
	initRest(uuid,"/vnpthis");
	var _opts=new Object();
	
	var khoaid = '{dept_id}';
	var phongid = '{subdept_id}';
	
	var mode = '{showMode}';	
	var data;
	
	var _opts=new Object();
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		_opts.khoaid = khoaid;		
	}
	
	var list = new POPUP_CHON_BSIKHAM(_opts);
	list.load();
</script>