<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />


<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript"
	src="../common/script/alertify/alertify.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>

<script type="text/javascript"
	src="../duoc/DUC100DS001_DieuChuyenThuHoi.js?v=20220628036"></script>
<link href="../common/script/tree/skin-win8/ui.fancytree.css"
	rel="stylesheet" />
<script src="../common/script/tree/jquery.fancytrees.js"></script>
<script src="../common/script/tree/jquery.fancytree.filter.js"></script>

<div width="100%" id="divMain" class="container">
	<div id="toolbarId"></div>
	<div id="divSearch" class="col-xs-12 low-padding border-group-1 mgt5">
		<div class="col-xs-12 mgt5 low-padding ">
			<div class="col-xs-1  ">
				<label class="">Kho lập</label>
			</div>
			<div class="col-xs-3">
				<select class="form-control input-sm" id="cboKho">
				</select>
			</div>
			<div class="col-xs-1  ">
				<label class="">Từ ngày</label>
			</div>
			<div class="col-xs-3  ">
				<div class="input-group" style="width: 100%;">
					<input class="form-control input-sm" id="txtTuNgay"
						valrule="Từ ngày,required|datetime|max_length[10]" title=""
						data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
					<span
						class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
						type="sCal" id='spTuNgay'
						onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
				</div>
			</div>
			<div class="col-xs-1  ">
				<label class="">Đến ngày</label>
			</div>
			<div class="col-xs-3">
				<div class="input-group" style="width: 100%;">
					<input class="form-control input-sm" id="txtDenNgay"
						valrule="Ngày duyêt,required|datetime|max_length[10]" title=""
						data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
					<span
						class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
						type="sCal" id='spDenNgay'
						onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
				</div>
			</div>
		</div>


		<div class="col-xs-12 mgt5 low-padding ">
			<div class="col-xs-1  ">
				<label class="">Trạng thái</label>
			</div>
			<div class="col-xs-3 ">
				<select class="form-control input-sm" id="cboTrangThai">
				</select>
			</div>
			<div class="col-xs-1">
				<button type="button" class="btn btn-sm btn-primary" id="btnTimKiem"
					style="margin-top: -3px;">
					<span class="glyphicon glyphicon-search"></span>&nbsp;Tìm kiếm
				</button>
			</div>

		</div>

	</div>


	<div class="col-xs-12 low-padding">
		<div class="col-xs-6 low-padding">
			<div class="col-xs-12 low-padding">
				<table id="grvDsPhieu"></table>
				<div id="pager_grvDsPhieu"></div>
			</div>
		</div>
		<div class="col-xs-6 low-padding"
			style="padding-left: 15px !important;">
			<div class="panel panel-default mgt10">
				<div class="panel-heading">Thông tin phiếu</div>
				<div class="panel-body">
					<div class="col-xs-12 low-padding mgt5 mgb10">
						<div class="col-xs-12 ">
							<div class="col-xs-2 low-padding ">
								<label class="">Kho lập</label>
							</div>
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm " style="width: 100%;"
									id="txtKhoaLap" valrule="Tên,|max_length[150]"
									name="txtKhoLap" title="" style="width:100%;"
									disabled="disabled">
							</div>
							<div class="col-xs-2 low-padding ">
								<label class="mgl5">Người lập</label>
							</div>
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm " style="width: 100%;"
									id="txtNguoiLap" valrule="Tên,|max_length[150]"
									name="txtNguoiLap" title="" style="width:100%;"
									disabled="disabled">
							</div>
						</div>
						<div class="col-xs-12 ">
							<div class="col-xs-2 low-padding ">
								<label class="">Ngày lập</label>
							</div>
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm " style="width: 100%;"
									id="txtNgayLap" valrule="Tên,|max_length[150]"
									name="txtNgayLap" title="" style="width:100%;"
									disabled="disabled">
							</div>
							<div class="col-xs-2 low-padding ">
								<label class="">Kho đối ứng</label>
							</div>
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm " style="width: 100%;"
									id="txtKhoaXuLy" valrule="Tên,|max_length[150]"
									name="txtKhoDoiUng" title="" style="width:100%;"
									disabled="disabled">
							</div>
						</div>
						<div class="col-xs-12 ">
							<div class="col-xs-2 low-padding ">
								<label class="">Tiền đơn</label>
							</div>
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm " style="width: 100%;"
									id="txtTienDon" valrule="Tên,|max_length[150]"
									name="txtTienDon" title="" style="width:100%;"
									disabled="disabled">
							</div>
							<div class="col-xs-2 low-padding ">
								<label class="mgl5">Thành tiền</label>
							</div>
							<div class="col-xs-4 low-padding">
								<input class="form-control input-sm " style="width: 100%;"
									id="txtThanhTien" valrule="Tên,|max_length[150]"
									name="txtThanhTien" title="" style="width:100%;"
									disabled="disabled">
							</div>
						</div>

					</div>
 					<div class="col-xs-12 low-padding mgt5" style="padding-left: 15px !important; text-align: center;  " id='divGoDuyet'>
 						<button type="button" class="btn btn-sm btn-primary" id="btnGoDuyet" disabled="disabled" 
							style="margin-top: 15px;">
							<span class="glyphicon glyphicon-remove"></span>&nbsp;Gỡ duyệt
						</button>
						<button type="button" class="btn btn-sm btn-primary" id="btnHuyGuiDuyet" disabled="disabled" 
							style="margin-top: 15px;">
							<span class="glyphicon glyphicon-remove"></span>&nbsp;Hủy gửi duyệt
						</button>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-12 low-padding">
							<table id="grvDsThuoc"></table>
							<div id="pager_grvDsThuoc"></div>
						</div>
					</div>
					
				</div>
			</div>

		</div>

	</div>
	<input type="hidden" id="hid_NHAPXUATID" value="0"> 
	<input type="hidden" id="hidTrangThai" value="">
	<input type="hidden" id="hidTrangThaiNhap" value="">
	<input type="hidden" id="hidTrangThaiXuat" value="">
	<input type="hidden" id="hidKieu" value="">

	<div class="col-md-12 low-padding" style="width: 647px;">
		<iframe id="ifmXLS" name="ifmXLS"
			style="width: 100%; height: 100%; min-height: 500px; margin-left: -2px !important; display: none">
		</iframe>
	</div>

</div>


<script>
	var opt = [];
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var dept_id = '{dept_id}';
	var subdept_id = '{subdept_id}';
	var lang = "vn";
	console.log('uuid=' + uuid + ', schema=' + schema + ', province_id='
			+ province_id + ', hospital_id=' + hospital_id);
	var userInfo = CommonUtil.decode('{userData}');
	var paramInfo = CommonUtil.decode('{paramData}');

	var session_par = [];
	session_par[1] = user_id;
	session_par[2] = schema;
	session_par[3] = province_id;
	session_par[0] = hospital_id;
	var table_name = '{table}';

	var _opts = new Object();
	_opts.lk = paramInfo.lk;
	_opts.lp = paramInfo.lp;
	_opts.ht = paramInfo.ht;
	_opts.tt = paramInfo.tt;
	_opts.cs = paramInfo.cs;
	_opts.type = paramInfo.type;
	_opts.gd = paramInfo.gd;
	_opts.cs = paramInfo.cs;
	_opts.td = paramInfo.td;
	_opts.lt = paramInfo.lt;
	_opts._param = session_par;
	_opts._uuid = uuid;
	_opts.type = paramInfo.type;
	_opts.backColor = [ "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF",
			"#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF",
			"#FFFFFF", "#FFFFFF", "#FFFFFF" ];
	_opts.imgPath = [ "Edit.png", "", "", "", "Circle_Yellow.png",
			"Circle_Green.png", "Circle_Red.png", "", "", "", "",
			"Shopping_cart.png", "Shopping_cart.png", "" ];
	_opts.foreColor = [ "#000000", "#000000", "#000000", "#808080", "#000000",
			"#000000", "#000000", "#000000", "#000000", "#000000", "#000000",
			"#FF0000", "#800000", "#000000" ];

	initRest(_opts._uuid);
	_opts.hospitalId = userInfo.HOSPITAL_ID;
	var ttbn = new DMThuoc(_opts);
	ttbn.load();
</script>