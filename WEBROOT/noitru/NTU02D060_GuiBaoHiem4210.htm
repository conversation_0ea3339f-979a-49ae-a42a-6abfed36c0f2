<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js"></script>
<script type="text/javascript" src="../common/script/jszip/FileSaver.js"></script>
<script type="text/javascript" src="../common/script/xlsx/xlsx.full.min.js"></script>
<script type="text/javascript" src="../common/script/jszip/jszip.js"></script>
<script type="text/javascript"src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../noitru/NTU02D060_GuiBaoHiem4210.js?version=250313"></script>
<script type="text/javascript" src="..//noitru/cominf.js?v=250313"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<!-- L2PT-43011 start -->
<script type="text/javascript" src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css" type="text/css" />
<!-- L2PT-43011 end -->
<style>
#drop{
	border:2px dashed #bbb;
	-moz-border-radius:2px;
	-webkit-border-radius:2px;
	border-radius:2px;
	padding:2px;
	text-align:center;
	height:50px;
}
</style>

<div id="divMain" class="container">
	<div class="col-xs-12 low-padding">
	<div id="divSearch" class="panel-body border-group-1 mgt5">
		<div class="col-xs-1 low-padding mgt3">
<!-- 			<input type="file" name="xlfile" id="xlf" /> -->
			<div id="drop">Import XLSX</div>
		</div>
		<div class="col-xs-7 low-padding">
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Tuyến</label>
					</div>
					<div class="col-xs-7 low-padding">
						<select class="form-control input-sm" id="cboTuyen">
							<option value="-1" selected="selected">----Tất cả----</option>
							<option value="0">KCBBD</option>
							<option value="1">Nội tỉnh đến</option>
							<option value="2">Ngoại tỉnh đến</option>
						</select>
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-3 low-padding">
						<label class="">Từ ngày</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group" style="width: 100%;">
							<input class="form-control input-sm" id="txtTU_NGAY"
								name="txtTU_NGAY" valrule="Từ ngày,trim_required" title=""
								data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss"> <span
								class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
								type="sCal"
								onclick="NewCssCal('txtTU_NGAY','ddMMyyyy','dropdown',true,'24',true)"></span>
						</div>
					</div>
				</div>	
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="control-label">Lọc theo</label>
					</div>
					<div class="col-xs-6 low-padding">
						<select class="form-control input-sm" style="width: 100%" id="cboLOCTHEO">
							<!-- L2PT-19490 thêm đk tìm kiếm -->
							<option value="4">Ngày vào viện</option>
							<option value="2">Ngày ra viện</option>
							<option value="3">Ngày T.Toán</option> 
							<option value="1">Ngày Q.Toán</option>
							<option value="0">Ngày duyệt BH</option>
							<option value="5">Ngày gửi cổng</option>
						</select>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Loại hồ sơ</label>
					</div>
					<!-- L2PT-54953 start -->
					<div class="col-xs-7 low-padding" id="divLoaiHs">
						<select class="form-control input-sm" id="cboLoaiHS">
							<option value="-1" selected="selected">----Tất cả----</option>
							<option value="0" class="loai-hs-v1">Nội trú</option>
							<option value="1" class="loai-hs-v1">Ngoại trú</option>
							<option value="3" class="loai-hs-v1">Điều trị ngoại trú</option>
							<option value="1" class="loai-hs-v2" style="display:none;">Ngoại trú</option>
							<option value="2" class="loai-hs-v2" style="display:none;">Nội trú</option>
						</select>
					</div>
					<!-- L2PT-54953 end -->
					<!-- L2PT-43011 start -->
					<div class="col-md-7 low-padding" id="divLoaiHsMS">
						<select class="selectpicker SumoUnder" id="cboLoaiHSms"
							multiple data-actions-box="true"
							data-selected-text-format="count > 2" data-width="auto">
								<!-- L2PT-85755 start -->
								<option value="1" class="loai-hs-4210">Khám bệnh</option>
								<option value="2" class="loai-hs-4210">Điều trị ngoại trú</option>
								<option value="3" class="loai-hs-4210">Nội trú</option>
								<option value="7" class="loai-hs-4210">Nhận thuốc</option>
								<option value="9" class="loai-hs-4210">Nội trú dưới 4h</option>
								<option value="01" class="loai-hs-130">01-Khám bệnh</option>
								<option value="02" class="loai-hs-130">02-DTNGT</option>
								<option value="03" class="loai-hs-130">03-Nội trú</option>
								<option value="04" class="loai-hs-130">04-DTNT ban ngày</option>
								<option value="05" class="loai-hs-130">05-DTNGT DN, KB, lĩnh thuốc</option>
								<option value="06" class="loai-hs-130">06-Điều trị lưu</option>
								<option value="07" class="loai-hs-130">07-Nhận thuốc</option>
								<option value="08" class="loai-hs-130">08-DTNGT DN, KB, DVKT|SD thuốc</option>
								<option value="09" class="loai-hs-130">09-Nội trú dưới 4h</option>
								<option value="10" class="loai-hs-130">10-Khác</option>
								<!-- L2PT-85755 end -->
						</select>
					</div>
					<!-- L2PT-43011 end -->
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-3 low-padding">
						<label class="">Đến</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group" style="width: 100%;">
							<input class="form-control input-sm" id="txtDEN_NGAY"
								name="txtDEN_NGAY" valrule="Đến ngày,trim_required" title=""
								data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss"> <span
								class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
								type="sCal"
								onclick="NewCssCal('txtDEN_NGAY','ddMMyyyy','dropdown',true,'24',true)"></span>
						</div>
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Mã thẻ</label>
					</div>
					<div class="col-xs-6 low-padding">
						<select class="selectpicker SumoUnder" id="cboMaThe"
							multiple data-actions-box="true"
							data-selected-text-format="count > 2" data-width="auto">
						</select>
					</div>
				</div>	
			</div>
			<!-- L2PT-69647 start -->
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Đối tượng</label>
					</div>
					<div class="col-xs-7 low-padding" id="divDTBNID">
						<select class="form-control input-sm" id="cboDTBNID">
						</select>
					</div>
				</div>
				<!-- L2PT-98060 start -->
				<div class="col-xs-4 low-padding" id="divPHAMVI" style="display:none;">
					<div class="col-xs-4 low-padding">
						<label class="">Phạm vi</label>
					</div>
					<div class="col-xs-7 low-padding">
						<select class="form-control input-sm" id="cboPHAMVI">
							<option value="-1" selected="selected">----Tất cả----</option>
							<option value="0">Ngoài phạm vi</option>
							<option value="1">Trong phạm vi</option>
						</select>
					</div>
				</div>
				<!-- L2PT-98060 start -->
			</div>
			<!-- L2PT-69647 end -->
		</div>
		<div class="col-xs-2 low-padding">
			<div class="col-xs-12 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Loại thẻ</label>
					</div>
					<div class="col-xs-7 low-padding">
						<select class="form-control input-sm" id="cboLoaiThe">
						</select>
					</div>
			</div>
			<div class="col-xs-12 low-padding">
					<!-- start: cv 130 -->
					<div class="col-xs-4 low-padding">
						<label class="">XML</label>
					</div>
					<div class="col-xs-7 low-padding">
						<select class="form-control input-sm" id="cboLoaiXML">
							<option value="4210">Chuẩn 4210</option>
							<option value="130">Chuẩn 130</option>
						</select>
					</div>
					<!-- end: cv 130 -->
			</div>
		</div>
		<div class="col-xs-2 low-padding">
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<label class="">CSKCB</label>
				</div>
				<div class="col-xs-7 low-padding">
					<select class="form-control input-sm" id="cboCSKCB">
					</select>
				</div>
			</div>
			<!-- L2PT-39437 start -->
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding mgt3">
					<label class="">Khoa</label>
				</div>
				<div class="col-xs-7 low-padding">
					<select class="form-control input-sm" id="cboKhoa">
					</select>
				</div>
				<!-- L2PT-39437 start -->
				<div class="col-xs-12 low-padding">
					<div class="col-xs-12 low-padding">
						<button id="btnSearch" class="btn btn-primary" style="width: 91%">
							<span class="glyphicon glyphicon-search"></span> Tổng hợp số liệu
						</button>
					</div>
				</div>
				<!-- L2PT-39437 end -->
			</div>
			<!-- L2PT-39437 end -->	
		</div>
	</div>
		<div class="col-xs-7 low-padding"
			style="padding-right: 15px !important;">
			<div class="col-xs-12 low-padding">
				<table id="grdDS_HOSO"
					class="table table-striped jambo_table bulk_action"></table>
				<div id="pager_grdDS_HOSO"></div>
			</div>
		</div>
		
		<div class="col-xs-5 low-padding mgt5">
			<!-- SO_HS,T_TONGCHI,T_BHTT,T_BNTT,T_NGUONKHAC,T_THUOC,T_VTYT -->
			<div class="col-xs-12 low-padding mgt5">
				<div class="panel panel-default">
					<div class="panel-heading">Số liệu tổng hợp</div>
					<div class="panel-body">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label class="">Hồ sơ</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm i-col-m_fl" id="txtSO_HS"
									style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
							
							<div class="col-xs-3 low-padding mgt3" style="padding-left: 10px">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Tiền chi</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_TONGCHI" 
									style="width: 100%; text-align: right; float:right; margin-right: 5px !important; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Tiền BHTT</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_BHTT"
									style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Tiền BNTT</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_BNTT"
									style="width: 100%; text-align: right; float:right; margin-right: 5px !important; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Tiền Nguồn khác</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm i-col-m_fl"
									id="txtT_NGUONKHAC" style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt5">
				<div class="panel panel-default">
					<div class="panel-heading">Tiền tổng hợp theo nhóm Mã BHYT</div>
					<div class="panel-body">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label class="">Thuốc</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm i-col-m_fl" id="txtT_THUOC"
									style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
							
							<div class="col-xs-3 low-padding mgt3" style="padding-left: 10px">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>VTYT</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_VTYT" 
									style="width: 100%; text-align: right; float:right; margin-right: 5px !important; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Xét nghiệm</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_XN"
									style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>CĐHA</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_CDHA"
									style="width: 100%; text-align: right; float:right; margin-right: 5px !important; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Máu</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm i-col-m_fl"
									id="txtT_MAU" style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>PTTT</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm i-col-m_fl" id="txtT_PTTT"
									style="width: 100%; text-align: right; float:right; margin-right: 5px !important; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Khám</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm i-col-m_fl" id="txtT_KHAM"
									style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
							
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Giường</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_GIUONG"
									style="width: 100%; text-align: right; float:right; margin-right: 5px !important; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding mgt3">
								<label class="mgl5" style="font-weight: 600 !important;">&Sigma;</label>
								<label>Vận chuyển</label>
							</div>
							<div class="col-xs-3 low-padding mgt3">
								<input class="form-control input-sm" id="txtT_VCHUYEN"
									style="width: 100%; text-align: right; font-weight: bold; color: rgb(204, 0, 0);">
							</div>
						</div>
	
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt5">
				<div class="panel panel-default">
					<div class="panel-heading">Xuất dữ liệu đối soát</div>
					<div class="panel-body">
							<div class="col-xs-12 low-padding mgt3">
								
								<div class="col-xs-4 low-padding mgt3">
									<div class="col-xs-12">
										<button id="chkEXP_REPORT_XML_BHYT" style="width: 100%;"
											class="btn btn-primary " 
											href="">Xuất XML</button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkEXP_REPORT_XML_BHYT_LINK" href=""></a>
									</div>
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_XML_BHYT_CHECK" class="btn btn-primary " style="width: 100%"
											href="">Xuất XML KMH</button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkEXP_REPORT_XML_BHYT_CHECK_LINK" href=""></a>
									</div>
									<div class="col-xs-12 mgt5">
										<button id="chkXML_DS_BN" class="btn btn-primary" style="width: 100%"
											href="">XML DSBN</button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkXML_DS_BN_LINK" href=""></a>
									</div>
									<div class="col-xs-12 mgt5">
										<button id="chkXML_DS_BN_KMH" class="btn btn-primary " style="width: 100%"
											href="">XML DSBN KMH</button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkXML_DS_BN_KMH_LINK" href=""></a>
									</div>
									<!-- <div class="col-xs-6 mgt5">
										<button id="chkXML_4210" style="width: 100%;"
											class="btn btn-primary " 
											href="">XML 4210</button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkXML_4210_LINK" href=""></a>
									</div>
									<div class="col-xs-6 mgt5">
										<button id="chkXML_4210_CHECK" class="btn btn-primary " style="width: 100%"
											href="">4210 KMH </button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkXML_4210_CHECK_LINK" href=""></a>
									</div> -->
								</div>
								<div class="col-xs-4 low-padding mgt3">
									<div class="col-xs-12">
										<button id="chkEXP_REPORT_19_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Báo cáo 19</button>
									</div>
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_20_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Báo cáo 20</button>
									</div>

									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_21_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Báo cáo 21</button>
									</div>
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_79_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Báo cáo 79_80</button>
									</div>
									<!-- L2PT-25669 start -->
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_79_V2_CHECK" style="width: 100%;display:none;" class="btn btn-primary" href="">
										Báo cáo 79_80_v2</button>
									</div>
									<!-- L2PT-25669 end -->
								</div>
								<div class="col-xs-4 low-padding mgt3">
									<div class="col-xs-12">
										<button id="chkEXP_REPORT_80_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Đa tuyến</button>
									</div> 
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_DET_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Chi tiết 79_80</button>
									</div>
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_REPORT_DV_CHECK" style="width: 100%;" class="btn btn-primary" href="">
										Chi tiết DV_TVT</button>
									</div>
									<!-- L2PT-32303 start  -->
									<div class="col-xs-12 mgt5">
										<button id="chkEXP_XML_2076" style="width: 100%;"
											class="btn btn-primary " 
											href="">XML 2076</button>
									</div>
									<div style="visibility: hidden;">
										<a id="chkEXP_XML_2076_LINK" href=""></a>
									</div>
									<!-- end -->
								</div>
								<!--  L2PT-2271 start -->
								<div class="col-xs-12 low-padding mgt5 mgb5" id="divXUAT_XML">
									<div class="col-xs-4">
										<button id="btnXML1" style="width: 100%;" class="btn btn-primary" href="">
										XML1 XLSX</button>
									</div> 
									<div class="col-xs-4">
										<button id="btnXML2" style="width: 100%;" class="btn btn-primary" href="">
										XML2 XLSX</button>
									</div>
									<div class="col-xs-4">
										<button id="btnXML3" style="width: 100%;" class="btn btn-primary" href="">
										XML3 XLSX</button>
									</div>
									<!-- L2PT-37237 start -->
									<div class="col-xs-4 mgt5">
										<button id="btnXML4" style="width: 100%;" class="btn btn-primary" href="">
										XML4 XLSX</button>
									</div>
									<div class="col-xs-4 mgt5">
										<button id="btnXML5" style="width: 100%;" class="btn btn-primary" href="">
										XML5 XLSX</button>
									</div>
									<!-- L2PT-37237 end -->
									<!-- L2PT-86610 start -->
									<div id="divXUAT_XML130" style="display:none;">
										<div class="col-xs-4 mgt5">
											<button id="btnXML6" style="width: 100%;" class="btn btn-primary" href="">
											XML6 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML7" style="width: 100%;" class="btn btn-primary" href="">
											XML7 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML8" style="width: 100%;" class="btn btn-primary" href="">
											XML8 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML9" style="width: 100%;" class="btn btn-primary" href="">
											XML9 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML10" style="width: 100%;" class="btn btn-primary" href="">
											XML10 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML11" style="width: 100%;" class="btn btn-primary" href="">
											XML11 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML12" style="width: 100%;" class="btn btn-primary" href="">
											XML12 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML13" style="width: 100%;" class="btn btn-primary" href="">
											XML13 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML14" style="width: 100%;" class="btn btn-primary" href="">
											XML14 XLSX</button>
										</div>
										<div class="col-xs-4 mgt5">
											<button id="btnXML15" style="width: 100%;" class="btn btn-primary" href="">
											XML15 XLSX</button>
										</div>
									</div>
									<!-- L2PT-86610 end -->
									<!-- // L2PT-53905 start  -->
									<div class="col-xs-4 mgt5">
										<button id="btnXML" style="width: 100%;" class="btn btn-primary" href="">
										XML XLSX</button>
									</div>
									<!-- // L2PT-53905 end -->
								</div>
								<!--  L2PT-2271 end -->
							</div>
					</div>
				</div>
			</div>
			
		</div>
	</div>
	
</div>
<div id="dLoading" class="hidden" style="width: 90% !important; height: 100% !important;min-height:100% !important;  position:absolute;top:0; left:0; text-align: center; vertical-align: middle;">
	<div style=" top: 50%; left:50%; position:absolute;"><i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i></div>
</div>
<script>



	var userInfo = CommonUtil.decode('{userData}');
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var schema_name = userInfo.DB_SCHEMA;
	var hos_code = userInfo.HOSPITAL_CODE;
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var url = '{url}';
	var dept_id = '{dept_id}';
	//var khambenh_id = '{khambenh_id}';
	var session_par = [];
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("PortalWS");
	ajaxSvc.register("InsrWS");
	var _opts = {
		lang : lang,
		_param : session_par,
		_uuid : uuid,
		user_id : user_id,
		hospital_id : hospital_id,
		hos_code : hos_code,
		schema_name : schema_name
	}

	var data;
	var mode = '{showMode}';

	var ptd = new GuiBaoHiem(_opts);
	ptd.load();
</script>