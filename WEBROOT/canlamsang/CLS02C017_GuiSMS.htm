<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/script/SmsSend.js?v=2020200220"></script>
<script src="../common/script/tinymce/tinymce.min.js"></script>

<script src="../common/script/nprogress/nprogress.js"></script>
<script src="../common/script/jquery/jquery.wallform.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS02C017_GuiSMS.js?v=20200417"></script>

<div id="divMain">	
	<table>
		<tr>
			<td class="low-padding" colspan="2">
				<label>Bạn hãy kiểm tra lại kĩ thông tin trước khi gửi tin nhắn</label><p></p>
			</td>
		</tr>
		<tr>
			<td class="low-padding"  style="width:120px !important">
				<label>Số điện thoại</label>
			</td>
			<td class="low-padding">
				<input type="text" id="txtSDT" style="width:200px !important"/>
			</td>
		</tr>
		<tr>
			<td class="low-padding">
				<label id="lblNoiDung">Nội dung tin nhắn</label>
			</td>
			<td class="low-padding">
				<textarea rows="5" cols="70" id="txtNoiDung" style="width:400px !important" onkeyup="ds.countToLove()">				
				</textarea>
				<label id="lblCount" style="float:bottom;margin-left:-3px;"></label>
			</td>
		</tr>
		<tr>
			<td class="low-padding">
				<label>Thời gian gửi</label>
			</td>
			<td class="low-padding">
				<div class="input-group" style="width:200px">
					<input class="form-control input-sm" id="txtTGThucHien" name="txtTGThucHien" valrule="Thời gian thực hiện,required|datetime|max_length[16]" data-mask="00/00/0000 00:00" placeholder="dd/MM/yyyy hh:mm" maxlength="16">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGThucHien" type="sCal" onclick="NewCssCal ('txtTGThucHien','ddMMyyyy','dropdown',true,'24')"></span>
				</div>
			</td>
		</tr>
		<tr>
			<td class="low-padding">
			</td>
			<td><p></p>
			    <button class="btn btn-sm btn-primary" id="btnGui">
					<span class="glyphicon glyphicon-floppy-disk"></span> Gửi 
				</button>	
				<button class="btn btn-sm btn-primary" id="btnClose">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng
				</button>
			</td>
		</tr>
	</table>
</div>
			<!-- nghiant  VNPTHISL2-490 22092017 -->
				<iframe width="0" height="0" border="0" name="dummyframe" id="dummyframe"></iframe>
				<form  method="POST" name="sendSMSFormXN" id="sendSMSFormXN" 
					action="/vnpthis/servlet/otp.controller.SendSMSTEST" target="dummyframe">
					<input type="hidden" name="phoneNumber"  id="phoneNumber" value="">
					<input type="hidden"  name="contentSMS"  id="contentSMS" >
				</form>
			<!-- nghiant  VNPTHISL2-490 22092017 -->

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDKetQuaCLS" value="">

<div id="divTraKetQua2" style="width: 100%; display: none">
	<iframe src="" id="ifmView2" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang = "vn";
	var type = "{type}";
	var idmaubenhpham = '{idmaubenhpham}';
	var tiepnhanid = '{tiepnhanid}';
	var benhnhanid = '{benhnhanid}';
	var tenBN = '{tenBN}';
	var sophieuBN = '{sophieuBN}';
	var sdtBN = '{sdtBN}';
	var mode = '{showMode}';
	var khoaId = {dept_id};
		
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;

	var _opts=new Object();	
	
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);	
		data=EventUtil.getVar("dlgVar");		
		_opts._param=session_par;
		_opts._uuid=uuid;
		_opts._maubenhphamid=data.maubenhphamid;
	}
	
	initRest(_opts._uuid);
	
	var ds = new GuiSMS(_opts);
	ds.load();
</script>