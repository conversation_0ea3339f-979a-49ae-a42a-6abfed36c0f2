/*
 * <PERSON>h mục thuốc
 * 
 * Bachnv		08/09/2016		tạo mới
 * 
 */

function DMThuoc(opt) {
	var _param = session_par;
	var _loai = '';
	var _kho = '';
	var _gd = '';
	this.opt = $.extend({},opt);
	var _tenthuocvat = '';
	var that = this;
	this.load = doLoad;
	var PHARMA_THUOC_MACSYT='';
	var PHARMA_MACSYT_TT_L2='';
	var PHARMA_SHOW_NGUONCT='';
	var hCode = '0';
	function doLoad() {
		// khởi tạo các tab dữ liệu
		$("li[id^='tab'").on("click", function(e) {
			var tabName = $(this).attr("id").substr(3);
			$("li[class='active']").removeClass("active");
			$(this).addClass("active");
			$("div[class='tab active']").removeClass("active");
			// console.log(tabName);
			$("#div" + tabName).addClass("active");
		});
		// $("#hidDuTruID").val("");
		// khởi tạo các control
		ComboUtil.getComboTag("cboKhoLap", "DUC04D001.02", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "", "", "sql");
		ComboUtil.getComboTag("cboNhaCungCap", "DUC04D001.01", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "", "", "sql");
		//create toolbar
		if(that.opt.gd == 'VT'){
			_tenthuocvat = 'vật tư';
		}
		if(that.opt.gd == 'VPP'){
			_tenthuocvat = 'VPP';
		}
		else {
			_tenthuocvat = 'thuốc';
		}
		var ctl_ar = [ {
			type : 'button',
			id : 'btnThem',
			icon : 'pencil',
			text : 'Thêm mới',
//			cssClass : 'wd150'
		}, {
			type : 'button',
			id : 'btnEdit',
			icon : 'sua',
			text : 'Sửa',
//			cssClass : 'wd130'
		},
		{
			type : 'button',
			id : 'btnCheck',
			icon : 'minus-sign',
			text : 'Kiểm tra mã ' + _tenthuocvat + ' trùng',
			cssClass : 'wd200'
		},
		{
			type : 'button',
			id : 'btnSaoChep',
			icon : 'pencil',
			text : 'Sao chép ' + _tenthuocvat + '',
			cssClass : 'wd150'
		},
		/*,
		{
			type : 'button',
			id : 'btnExport',
			icon : 'glyphicon glyphicon-xuattra',
			text : 'Export excel',
			cssClass : 'wd110'
		}*/
		{type:'buttongroup',id:'btnPrint',icon:'print',text:'Xuất file'
			,children:[
		        {id:'btnExport',icon:'print',text:'Xuất TVT',hlink:'#'},
		        {id:'btnExport_2',icon:'print',text:'Xuất kèm thầu',hlink:'#'},
		        {id:'btnExport_3',icon:'print',text:'Xuất TVT khóa',hlink:'#'},
		        {id:'btnExport_4',icon:'print',text:'Xuất TVT liên thông',hlink:'#'} 
		        ]}
		,
		{
			type : 'button',
			id : 'btnInBarcode',
			icon : 'print',
			text : 'In barcode ' + _tenthuocvat + '',
			cssClass : 'wd150'
		},
		{
			type : 'button',
			id : 'btnDayCong',
			icon : 'print',
			text : 'Đẩy cổng dược QG',
			cssClass : 'wd150'
		},{
			type : 'button',
			id : 'btnLienThongThuoc',
			icon : 'print',
			text : 'Liên thông thuốc',
			cssClass : 'wd150'
		},{
			type : 'button',
			id : 'btnLienThongThuocL2',
			icon : 'print',
			text : 'Liên thông L2-L2',
			cssClass : 'wd150'
		},
		{
			type : 'button',
			id : 'btnGuiFast',
			icon : 'print',
			text : 'Gửi tích hợp',
			cssClass : 'wd150'
		},
		{type:'label',id:'lblInfo',icon:'',text:'Danh mục ' + _tenthuocvat + ''} ];
		var toolbar = ToolbarUtil.build('toolbarId', ctl_ar);
		initControl();
		bindEvent();
		loadGrid();
		$("#txtMaThuoc").focus();
		$("#lbTen").text('Tên hoặc mã ' + _tenthuocvat);
		$("#lbTitle").text('Nhóm ' + _tenthuocvat);
		PHARMA_THUOC_MACSYT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_THUOC_MACSYT');
		if(PHARMA_THUOC_MACSYT===undefined || PHARMA_THUOC_MACSYT=='') 
			 $("#toolbarIdbtnDayCong").hide(); 
		PHARMA_LIENTHONGTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_LIENTHONGTHUOC');
		if(PHARMA_LIENTHONGTHUOC!= '1'){
			$("#toolbarIdbtnLienThongThuocL2").hide(); 
			$("#toolbarIdbtnLienThongThuoc").hide();
		}
			  
		PHARMA_MACSYT_TT_L2 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_MACSYT_TT_L2');
		if(PHARMA_MACSYT_TT_L2===undefined || PHARMA_MACSYT_TT_L2==''){
			$("#toolbarIdbtnExport_4").hide(); 
			
		}
		hCode = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_HOSPITALCODE_FAST'].join('$'));
		if(hCode != '0' && hCode != '' ){
			$("#toolbarIdbtnGuiFast").show();
		}else{
			$("#toolbarIdbtnGuiFast").hide();
		}
		
	}
	function loadGrid() {

		var param = RSUtil.buildParam("", [ $("#txtMaThuoc").val().trim(),
		                    				$("#cboLoai").val().trim(), 
		                    				$("#hidMa_parent").val(),
		                    				_loai
		                    				 ]);
		                    		console.log("grid_param=" + JSON.stringify(param));
		                    		GridUtil.loadGridBySqlPage("gridDsThuoc", "DUC44M001.01", param);
		XoaTrang();
	}

	function initControl() {
		var _gridHeader ='';
	
		//var _par = ['PHARMA_SINH_COLUMN_DMTHUOC'];
		var _par = ['PHARMA_SINH_COLUMN_DMTHUOC'];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
				_par.join('$'));
		if(result!='0'&&result!=null)
			_gridHeader = result;
			else {
				if(that.opt.gd == 'VT'){
					var tengocVT = "Tên gốc VT,BIETDUOC,200,0,f,l;";
					if(_param[0] == '23565'){
						tengocVT = "Tên gốc VT,BIETDUOC,200,0,f,l;"
					}
					var mamoithau = '';
					if(_param[0] == '10284'){
						mamoithau = "Mã mời thầu,MAMOITHAU,130,0,f,l;"
					}
					 _gridHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
							+ "Mã vật tư,MA,80,0,f,l;" 
							+ "Tên " + _tenthuocvat + " VT,TEN,200,0,f,l;"
							+ mamoithau 
							+ "Mã HC,MAHOATCHAT,80,0,f,l;"
							+ "Tên vật tư BHYT,TENKHOAHOC,200,0,f,l;"
							+ tengocVT
							+ "Mã BV,MATHUOCBV,80,0,f,l;" 
							+ "SĐK,SODANGKY,80,0,f,l;"
							+ "Hàm lượng,LIEULUONG,80,0,f,l;"
							+ "Đơn giá,GIABAN,80,0,f,r;"
							+ "KHOA,KHOA,0,0,t,l;"
							+ "Khóa,icon,50,0,ns,l;"
							//+ "STT thầu,STTDAUTHAU,80,0,f,r;"	
							+ "QĐ thầu,MATHAU,80,0,f,r;"	
							+ "Ngày QĐ,NAMCUNGUNG,80,0,f,r;"
							+ "Gói thầu,GOITHAU,80,0,f,r;"	
							+ "Hãng sản xuất,HANGSANXUAT,200,0,f,r;"	
							+ "Đơn vị,DONVI,80,0,f,l;" 
						//	+ "Mã ĐD,MA_DD,80,0,f,r;"	
						//	+ "Đường dùng,TEN_DD,100,0,f,r;"	
							+ "Loại thuốc/vật tư,LOAI_TVT,80,0,f,l" ;
				}				
				else {
					 _gridHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
							+ "Mã thuốc,MA,80,0,f,l;" 
							+ "Tên " + _tenthuocvat + " VT,TEN,200,0,f,l;"
							+ "Mã HC,MAHOATCHAT,80,0,f,l;"
							+ "Tên thuốc BHYT,TENKHOAHOC,200,0,f,l;"
							+ "Hoạt chất,HOATCHAT,200,0,f,l;"
							+ "Mã BV,MATHUOCBV,80,0,f,l;" 
							+ "SĐK,SODANGKY,80,0,f,l;"
							+ "Hàm lượng,LIEULUONG,80,0,f,l;"
							+ "Đơn giá,GIABAN,80,0,f,r;"
							+ "KHOA,KHOA,0,0,t,l;"
							+ "Khóa,icon,50,0,ns,l;"
							//+ "STT thầu,STTDAUTHAU,80,0,f,r;"	
							+ "QĐ thầu,MATHAU,80,0,f,r;"	
							+ "Ngày QĐ,NAMCUNGUNG,80,0,f,r;"	
							+ "Gói thầu,GOITHAU,80,0,f,r;"	
							+ "Hãng sản xuất,HANGSANXUAT,200,0,f,r;"	
							+ "Đơn vị,DONVI,80,0,f,l;" 
							+ "Mã ĐD,MA_DD,80,0,f,r;"	
							+ "Đường dùng,TEN_DD,100,0,f,r;"
						    + "Thầu ghép,THAUGHEP,100,0,f,r;"
						    + "Dạng BC,DANGBAOCHE,80,0,f,r;"
							+ "Loại thuốc/vật tư,LOAI_TVT,80,0,f,l" ;
				}
				
			}
		
		
		GridUtil.init("gridDsThuoc", "100%", "388px", "Danh sách " +_tenthuocvat, false,
				_gridHeader,false);
		 
//		$('.ui-search-toolbar').hide();
//		ComboUtil.getComboTag("cboLoaiKho", "DUC54T001.04", [ "" ], "", "",
//				"sql");
//		ComboUtil.getComboTag("cboLoaiThuocVT", "DUC54T001.05", [ "" ], "", "",
//				"sql");
		var sql_par=RSUtil.buildParam("",[that.opt.lk]);
//		ComboUtil.getComboTag("cboKho","DUC01S002.DSKHO",sql_par,"","","sql","",false);
		sql_par = RSUtil.buildParam("",[that.opt.ht]);		
		_loai = that.opt.type ;		
		_leftTree = TreeUtil.init("leftTree", false);
		loadTree_new(_leftTree);
	}
	
	function bindEvent() {

		$('#btnTimKiem').on('click', function () {
//			if($("#hidMa_parent").val()==""){
//				DlgUtil.showMsg("Chưa chọn nhóm!");
//			}
			var param = RSUtil.buildParam("", [$("#txtMaThuoc").val().trim(),
				$("#cboLoai").val().trim(),
				$("#hidMa_parent").val(),
				_loai
			]);
			console.log("grid_param=" + JSON.stringify(param));
			GridUtil.loadGridBySqlPage("gridDsThuoc", "DUC44M001.01", param);
			XoaTrang();
		});
		$('#btnTatCa').on('click', function () {
			location.reload();

			/*	var param = RSUtil.buildParam("", [ $("#txtMaThuoc").val().trim(),
                                                    $("#cboLoai").val().trim(),
                                                    '',
                                                    _loai
                                                     ]);
                                            console.log("grid_param=" + JSON.stringify(param));
                                            GridUtil.loadGridBySqlPage("gridDsThuoc", "DUC44M001.01", param);
                XoaTrang();*/
		});
		// loadGridCT();
		$("#gridDsThuoc").jqGrid('setGridParam', {
			onSelectRow: function (id) {
				var _ret = $("#gridDsThuoc").jqGrid(
					'getRowData', id);

				$("#hidThuocVatTuID").val(_ret.THUOCVATTUID);
				GridUtil.unmarkAll('gridDsThuoc');
				GridUtil.markRow('gridDsThuoc', id);
			}
		});

		$("#leftTree").fancytree(
			{
				activate: function (event, data) {
					var tree = $(this).fancytree("getTree"), node = tree
						.getActiveNode();
					$("#hidMa_parent").val(node.key);
//						alert($("#hidMa_parent").val());
					$("#hidTen_parent").val(node.title);
					$("#txtNhomThuoc").val(node.title);
//						DlgUtil.close("popupChonMa");
					var a = node.data;
					$("#hidThuocVatTuIDParent").val(a.thuocvattuid);
					var param = RSUtil.buildParam("", [$("#txtMaThuoc").val().trim(),
						$("#cboLoai").val().trim(),
						node.key,
						_loai]);
					console.log("grid_param=" + JSON.stringify(param));
					GridUtil.loadGridBySqlPage("gridDsThuoc", "DUC44M001.01", param);
				}
			});
		$("#gridDsThuoc")
			.jqGrid(
				'setGridParam',
				{
					ondblClickRow: function (id) {
						var _ret = $("#gridDsThuoc").jqGrid(
							'getRowData', id);

						$("#hidThuocVatTuID").val(_ret.THUOCVATTUID);
						var _par = {
							thuocvattuid: _ret.THUOCVATTUID,
							type: that.opt.type,
							edit: that.opt.cs,
							gd: that.opt.gd
						};
						if (_loai == '9')
							dlgPopup = DlgUtil
								.buildPopupUrl(
									"dlgNTNCC",
									"divDlg",
									"manager.jsp?func=../duoc/DUC67T001_ThongTinVanPhongPham",
									_par, "Thông tin " + _tenthuocvat, window.innerWidth * 0.95, window.innerHeight * 0.85);

						else
							dlgPopup = DlgUtil
								.buildPopupUrl(
									"dlgNTNCC",
									"divDlg",
									"manager.jsp?func=../duoc/DUC67T001_NhapThongTinThuocVatTu",
									_par, "Thông tin " + _tenthuocvat, window.innerWidth * 0.95, window.innerHeight * 0.80);

						dlgPopup.open("dlgNTNCC");

					},
					gridComplete: function () {
						var ids = $("#gridDsThuoc").getDataIDs();
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var row = $("#gridDsThuoc").jqGrid('getRowData', id);
							if (row.KHOA == "1") {

								var _icon = '';
								if (that.opt.imgPath[row.KHOA] != '')
									_icon = '<center><img src="../common/image/' + that.opt.imgPath[row.KHOA] + '" width="15px"></center>';
								$("#gridDsThuoc").setRowData(id, {icon: _icon});

								$("#gridDsThuoc").find("tr[id='" + id + "']").find('td').each(function (index, element) {
									$(element).css('text-decoration', 'line-through');
								});

							}


						}
					}
				});
		$("#toolbarIdbtnThem")
			.on(
				"click",
				function (e) {
					// alert("bach viem hong");
					// var _par =
					// {nhapxuatid:"0",khoid:$("#cboLoaiKho").val(),tenkho:$("#cboLoaiKho
					// option:selected").text(),kieu:type};
//							if($("#hidMa_parent").val()==''){
////								alert('Chưa chọn nhóm cha!'); 
//								DlgUtil.showMsg("Chưa chọn nhóm cha!");
//								return ;
//							}
					var _par = {
						manhom: $("#hidMa_parent").val(),
						tennhom: $("#hidTen_parent").val(),
						type: that.opt.type,
						ID_parent: $("#hidThuocVatTuIDParent").val(),
						edit: that.opt.cs,
						gd: that.opt.gd
					};
					if (_loai == '9')
						dlgPopup = DlgUtil
							.buildPopupUrl(
								"dlgNTNCC",
								"divDlg",
								"manager.jsp?func=../duoc/DUC67T001_ThongTinVanPhongPham",
								_par, "Thông tin " + _tenthuocvat, window.innerWidth * 0.95, window.innerHeight * 0.95);
					else
						dlgPopup = DlgUtil
							.buildPopupUrl(
								"dlgNTNCC",
								"divDlg",
								"manager.jsp?func=../duoc/DUC67T001_NhapThongTinThuocVatTu",
								_par, "Thêm " + _tenthuocvat, window.innerWidth * 0.95, window.innerHeight * 0.95);

					dlgPopup.open("dlgNTNCC");
				});

//		$("#toolbarIdbtnInBarcode")
//		.on(
//				"click",
//				function(e) {
//					
//					if($("#hidThuocVatTuID").val() == '' || $("#hidThuocVatTuID").val() == '0'){
//						DlgUtil.showMsg("Chưa chọn thuốc vật tư!");
//						return;
//					}else{
//						var par = [ {
//							name : 'thuocvattuid',
//							type : 'String',
//							value : $("#hidThuocVatTuID").val()
//						}];
//					openReport('window', 'DUC_BARCODE_THUOCVATTU', 'pdf', par);
//					}
//				});

		$("#toolbarIdbtnInBarcode")
			.on(
				"click",
				function (e) {
					var _par = {
						mathuoc: $("#txtMaThuoc").val().trim(),
						parent: $("#hidMa_parent").val(),
						kieu: $("#cboLoai").val().trim(),
						loai: _loai
					};
					dlgPopup = DlgUtil
						.buildPopupUrl(
							"dlgNTNCC",
							"divDlg",
							"manager.jsp?func=../duoc/DUC45T001_DSThuocInBarcode",
							_par, "Danh sách thuốc vật tư", 900, 420);

					dlgPopup.open("dlgNTNCC");
				});
		$("#toolbarIdbtnDayCong")
			.on(
				"click",
				function (e) {
					var _par = {
						mathuoc: $("#txtMaThuoc").val().trim(),
						parent: $("#hidMa_parent").val(),
						kieu: $("#cboLoai").val().trim(),
						loai: _loai
					};
					dlgPopup = DlgUtil
						.buildPopupUrl(
							"dlgNTNCC",
							"divDlg",
							"manager.jsp?func=../duoc/DUC45T001_DayCongDuoc",
							_par, "Danh sách thuốc vật tư", 900, 420);

					dlgPopup.open("dlgNTNCC");
				});
		$("#toolbarIdbtnLienThongThuoc")
			.on(
				"click",
				function (e) {
					var _par = {
						mathuoc: $("#txtMaThuoc").val().trim(),
						parent: $("#hidMa_parent").val(),
						kieu: $("#cboLoai").val().trim(),
						loai: _loai
					};
					dlgPopup = DlgUtil
						.buildPopupUrl(
							"dlgNTNCC",
							"divDlg",
							"manager.jsp?func=../duoc/DUC45T001_LienThongDuoc",
							_par, "Danh sách thuốc vật tư", 1200, 520);

					dlgPopup.open("dlgNTNCC");
				});
		
		$("#toolbarIdbtnLienThongThuocL2")
		.on(
			"click",
			function (e) {
				var _par = {
					mathuoc: $("#txtMaThuoc").val().trim(),
					parent: $("#hidMa_parent").val(),
					kieu: $("#cboLoai").val().trim(),
					loai: _loai
				};
				dlgPopup = DlgUtil
					.buildPopupUrl(
						"dlgNTNCC",
						"divDlg",
						"manager.jsp?func=../duoc/DUC45T001_LienThongDuocL2",
						_par, "Danh sách thuốc vật tư", 1200, 520);

				dlgPopup.open("dlgNTNCC");
			});


		$("#toolbarIdbtnSaoChep")
			.on(
				"click",
				function (e) {
					if ($("#hidThuocVatTuID").val() == "") {
						DlgUtil.showMsg("Chưa chọn thuốc vật tư!");
						return;
					}
					// alert("bach viem hong");
					var _par = {
						thuocvattuid: $("#hidThuocVatTuID").val(),
						type: that.opt.type,
						saochep: '1',
						edit: that.opt.cs,
						gd: that.opt.gd
					};
					if (_loai == '9')
						dlgPopup = DlgUtil
							.buildPopupUrl(
								"dlgNTNCC",
								"divDlg",
								"manager.jsp?func=../duoc/DUC67T001_ThongTinVanPhongPham",
								_par, "Thông tin " + _tenthuocvat, window.innerWidth * 0.95, window.innerHeight * 0.95);
					else
						dlgPopup = DlgUtil
							.buildPopupUrl(
								"dlgNTNCC",
								"divDlg",
								"manager.jsp?func=../duoc/DUC67T001_NhapThongTinThuocVatTu",
								_par, "Sửa thuốc vật tư", window.innerWidth * 0.95, window.innerHeight * 0.95);

					dlgPopup.open("dlgNTNCC");
				});

		$("#toolbarIdbtnEdit")
			.on(
				"click",
				function (e) {
					if ($("#hidThuocVatTuID").val() == "") {
						DlgUtil.showMsg("Chưa chọn thuốc vật tư!");
						return;
					}
					// alert("bach viem hong");
					var _par = {
						thuocvattuid: $("#hidThuocVatTuID").val(),
						type: that.opt.type,
						edit: that.opt.cs,
						gd: that.opt.gd
					};
					if (_loai == '9')
						dlgPopup = DlgUtil
							.buildPopupUrl(
								"dlgNTNCC",
								"divDlg",
								"manager.jsp?func=../duoc/DUC67T001_ThongTinVanPhongPham",
								_par, "Thông tin " + _tenthuocvat, window.innerWidth * 0.95, window.innerHeight * 0.95);
					else
						dlgPopup = DlgUtil
							.buildPopupUrl(
								"dlgNTNCC",
								"divDlg",
								"manager.jsp?func=../duoc/DUC67T001_NhapThongTinThuocVatTu",
								_par, "Sửa thuốc vật tư", window.innerWidth * 0.95, window.innerHeight * 0.95);

					dlgPopup.open("dlgNTNCC");
				});
		$("#toolbarIdbtnGuiFast").click(function(){
			var row = $("#gridDsThuoc").jqGrid('getGridParam', 'selrow');
	        var _id = $("#gridDsThuoc").jqGrid('getRowData', row).THUOCVATTUID;

	        if (_id == "" || _id == undefined)
	            return DlgUtil.showMsg("Chưa chọn thuốc vật tư!", undefined, 2000);
	        var danhsach = [];
	        var tvtObj = new Object();
	        tvtObj.YourId = $("#gridDsThuoc").jqGrid('getRowData', row).THUOCVATTUID;
	        tvtObj.ItemCode = $("#gridDsThuoc").jqGrid('getRowData', row).MA;
	        tvtObj.ItemName = $("#gridDsThuoc").jqGrid('getRowData', row).TEN;
	        tvtObj.OtherName = '';
	        tvtObj.Uom = $("#gridDsThuoc").jqGrid('getRowData', row).DONVI;
	        tvtObj.PurchasePrice = Number($("#gridDsThuoc").jqGrid('getRowData', row).GIABAN);
	        tvtObj.Note = '';       
	        tvtObj.Status = '1';
	        danhsach.push(tvtObj);
	        //var jSonStr = JSON.stringify(danhsach);
	        /*var jsonFast = {
	        		"serviceCode" : "S04",
	        		"data" : jSonStr,
	        		"hospitalCode" : "58004",
	        		"checksum" : "<CHECKSUM>"
	        	}*/
	        	try {
	        		var strRetFastApi = ajaxSvc.HMIS_PHAMAR.callFastApi("S01", danhsach,hCode,"<CHECKSUM>", "", 60, "", "", "");
	        		console.log(strRetFastApi);
	        		objRetFastApi = JSON.parse(strRetFastApi);
	        		console.log(objRetFastApi);
	        		var msg = "S01" + ": " + objRetFastApi.message;
	        		if (objRetFastApi.result && objRetFastApi.result.messages) {
	        			msg = msg + ". Chi tiết lỗi: " + objRetFastApi.result.messages
	        		}
	        		DlgUtil.showMsg(msg);
	        		return strRetFastApi;
	        	} catch (err) {
	        		DlgUtil.showMsg("S01" + ": " + err);
	        		return err;
	        	}
		});
		// chuannt them ds trung ma
		$("#toolbarIdbtnCheck")
			.on(
				"click",
				function (e) {
					var _par = {
						loaithuoc: _loai
					};
					dlgPopup = DlgUtil
						.buildPopupUrl(
							"dlgNTNCC",
							"divDlg",
							"manager.jsp?func=../duoc/DUC45T001_DSTrungMa",
							_par, "Danh sách thuốc vật tư trùng mã", 900, 420);

					dlgPopup.open("dlgNTNCC");
				});
		// chuannt them export file excel du lieu thuoc
		/*	$("#toolbarIdbtnExport").on("click",function(e){
                $('#chk_xuatfiles').click();
            });*/

//		$('#chk_xuatfiles').on("click",function(event){
//			_exportexcel();
//		});
		$("#toolbarIdbtnExport").on("click", function (e) {
			// all
			PHARMA_SHOW_NGUONCT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_SHOW_NGUONCT');
			var _DataType = {};
			var mya = '';

			var _sql_par = [$("#cboLoai").val().trim(), $("#hidMa_parent").val(), _loai, '-1'];
			if (PHARMA_SHOW_NGUONCT == '1') {
				mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44TVT_EXCEL1", _sql_par.join('$'));
				_DataType = {
					"MA_THUOC_BV": "s",
					"MA_HOAT_CHAT": "s",
					"HOAT_CHAT": "s",
					"MABYT": "s",
					"TENBYT": "s",
					"MA_ATC": "s",
					"MA_DUONG_DUNG": "s",
					"DUONG_DUNG": "s",
					"HAM_LUONG": "s",
					"TEN_THUOC": "s",
					"SO_DANG_KY": "s",
					"DONG_GOI": "s",
					"DON_VI_TINH": "s",
					"DON_GIA": "s",
					"DON_GIA_TT": "s",
					"GIA_BHYT": "s",
					"GIA_DICH_VU": "s",
					"BHYT_TRA": "s",
					"SO_LUONG": "s",
					"MA_CSKCB": "s",
					"HANG_SX": "s",
					"NUOC_SX": "s",
					"NHA_THAU": "s",
					"STT_DAUTHAU": "s",
					"SO_LAN_SU_DUNG": "s",
					"QUYET_DINH": "s",
					"MA_NHOM_THAU": "s",
					"SO_GOI_THAU": "s",
					"GOI_THAU": "s",
					"CONG_BO": "s",
					"LOAI_THUOC": "s",
					"NHOM_THUOC": "s",
					"LOAI_THAU": "s",
					"NHOM_THAU": "s",
					"SL_CANHBAO": "s",
					"CANHBAO_KETHUOC_DACBIET": "s",
					"KE_TRUNG_HOAT_CHAT": "s",
					"LD_DUYET": "s",
					"KHOA": "s",
					"NHOM_BHXH": "s",
					"GHI_CHU": "s",
					"TEN_NHOM": "s",
					"SO_GIAY_PHEP": "s",
					"TYLEHUHAO": "s",
					"MA_BENH_VIEN": "s",
					"PPCHEBIEN": "s",
					"DANGBAOCHE": "s",
					"MAHIEUSP": "s",
					"LIENTHONGDM": "s",
					"THAUGHEP": "s",
					"NGUON_CHUONG_TRINH":"s"
				};
			} else {
				mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44TVT_EXCEL", _sql_par.join('$'));
				_DataType = {
					"MA_THUOC_BV": "s",
					"MA_HOAT_CHAT": "s",
					"HOAT_CHAT": "s",
					"MABYT": "s",
					"TENBYT": "s",
					"MA_ATC": "s",
					"MA_DUONG_DUNG": "s",
					"DUONG_DUNG": "s",
					"HAM_LUONG": "s",
					"TEN_THUOC": "s",
					"SO_DANG_KY": "s",
					"DONG_GOI": "s",
					"DON_VI_TINH": "s",
					"DON_GIA": "s",
					"DON_GIA_TT": "s",
					"GIA_BHYT": "s",
					"GIA_DICH_VU": "s",
					"BHYT_TRA": "s",
					"SO_LUONG": "s",
					"MA_CSKCB": "s",
					"HANG_SX": "s",
					"NUOC_SX": "s",
					"NHA_THAU": "s",
					"STT_DAUTHAU": "s",
					"SO_LAN_SU_DUNG": "s",
					"QUYET_DINH": "s",
					"MA_NHOM_THAU": "s",
					"SO_GOI_THAU": "s",
					"GOI_THAU": "s",
					"CONG_BO": "s",
					"LOAI_THUOC": "s",
					"NHOM_THUOC": "s",
					"LOAI_THAU": "s",
					"NHOM_THAU": "s",
					"SL_CANHBAO": "s",
					"CANHBAO_KETHUOC_DACBIET": "s",
					"KE_TRUNG_HOAT_CHAT": "s",
					"LD_DUYET": "s",
					"KHOA": "s",
					"NHOM_BHXH": "s",
					"GHI_CHU": "s",
					"TEN_NHOM": "s",
					"SO_GIAY_PHEP": "s",
					"TYLEHUHAO": "s",
					"MA_BENH_VIEN": "s",
					"PPCHEBIEN": "s",
					"DANGBAOCHE": "s",
					"MAHIEUSP": "s",
					"LIENTHONGDM": "s",
					"THAUGHEP": "s"
				};
			}

			var mya_tvt = JSON.stringify(mya);
			var excel_name = 'DSTHUOCVT';

			/*	// don chat
                 var _sql_par_donchat=[$("#cboLoai").val().trim(),$("#hidMa_parent").val(),_loai,'0'];
                  var mya_donchat = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44TVT_EXCEL",_sql_par_donchat.join('$'));
                var mya_tvt_donchat=JSON.stringify(mya_donchat);
                var excel_name_donchat='DSTHUOCVT_DONCHAT';

             // da chat
                 var _sql_par_dachat=[$("#cboLoai").val().trim(),$("#hidMa_parent").val(),_loai,'1'];
                  var mya_dachat = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44TVT_EXCEL",_sql_par_dachat.join('$'));
                var mya_tvt_dachat=JSON.stringify(mya_dachat);
                var excel_name_dachat='DSTHUOCVT_DACHAT';	*/


			// JSONToCSVConvertor(mya_dv, excel_name, true);


			exportXLSX(mya_tvt, _DataType, excel_name, excel_name + ".xlsx");
			// exportXLSX(mya_donchat, _DataType, excel_name_donchat, excel_name_donchat+".xlsx");
			// exportXLSX(mya_dachat, _DataType, excel_name_dachat, excel_name_dachat+".xlsx");
		});


			$("#toolbarIdbtnExport_2").on("click", function (e) {
				/*	//don chat
                     var _sql_par=[$("#cboLoai").val().trim(),$("#hidMa_parent").val(),_loai,'0'];
                      var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44THAU_EXCEL",_sql_par.join('$'));
                    var mya_dv=JSON.stringify(mya);
                    var excel_name='DSTHUOCVT_THAU_DONCHAT';

                    //da chat
                     var _sql_par_dc=[$("#cboLoai").val().trim(),$("#hidMa_parent").val(),_loai,'1'];
                      var mya_dc = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44THAU_EXCEL",_sql_par_dc.join('$'));
                    var mya_dv_dc=JSON.stringify(mya);
                    var excel_name_dc='DSTHUOCVT_THAU_DACHAT';
                    // JSONToCSVConvertor(mya_dv, excel_name, true);
                    */
				//all
				if (that.opt._param[0] == "30360") {
					var _sql_par = [$("#cboLoai").val().trim(), $("#hidMa_parent").val(), _loai];
					var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44_QTIBVDK", _sql_par.join('$'));
					var mya_dv = JSON.stringify(mya);
					var excel_name = 'DSTHUOCVT_THAU';

					var _DataType = {
						"MA_HOAT_CHAT": "s",
						"STT_TT05": "s",
						"HOAT_CHAT": "s",
						"MA_DUONG_DUNG": "s",
						"DUONG_DUNG": "s",
						"HAM_LUONG": "s",
						"TEN_THUOC": "s",
						"TEN_BHYT": "s",
						"SO_DANG_KY": "s",
						"DONG_GOI": "s",
						"DON_VI_TINH": "s",
						"DON_GIA": "s",
						"DON_GIA_TT": "s",
						"GIA_BHYT": "s",
						"GIA_DICH_VU": "s",
						"GIA_BAN": "s",
						"SO_LUONG": "s",
						"HANG_SX": "s",
						"NUOC_SX": "s",
						"NHA_THAU": "s",
						"CONG_BO": "s",
						"QUYET_DINH": "s",
						"MA_CSKCB": "s",
						"MA_THUOC_BV": "s",
						"TEN_THUOC_BV": "s",
						"MA_BENH_VIEN": "s",
						"GHI_CHU": "s",
						"MAHIEUSP" : "s",
						"SOLANSUDUNG" : "s"
					};
					exportXLSX(mya, _DataType, excel_name, excel_name + ".xlsx");
					// exportXLSX(mya_dc, _DataType, excel_name_dc, excel_name_dc+".xlsx");
				} else {
					var _sql_par = [$("#cboLoai").val().trim(), $("#hidMa_parent").val(), _loai];
					var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44THAU_EXCEL", _sql_par.join('$'));
					var mya_dv = JSON.stringify(mya);
					var excel_name = 'DSTHUOCVT_THAU';

					var _DataType = {
						"MA_HOAT_CHAT": "s",
						"STT_TT05": "s",
						"HOAT_CHAT": "s",
						"MA_DUONG_DUNG": "s",
						"DUONG_DUNG": "s",
						"HAM_LUONG": "s",
						"TEN_THUOC": "s",
						"TEN_BHYT": "s",
						"SO_DANG_KY": "s",
						"DONG_GOI": "s",
						"DON_VI_TINH": "s",
						"DON_GIA": "s",
						"DON_GIA_TT": "s",
						"GIA_BHYT": "s",
						"GIA_DICH_VU": "s",
						"GIA_BAN": "s",
						"SO_LUONG": "s",
						"HANG_SX": "s",
						"NUOC_SX": "s",
						"NHA_THAU": "s",
						"CONG_BO": "s",
						"QUYET_DINH": "s",
						"MA_CSKCB": "s",
						"MA_THUOC_BV": "s",
						"TEN_THUOC_BV": "s",
						"MA_BENH_VIEN": "s",
						"GHI_CHU": "s"
					};
					exportXLSX(mya, _DataType, excel_name, excel_name + ".xlsx");
					// exportXLSX(mya_dc, _DataType, excel_name_dc, excel_name_dc+".xlsx");
				}

			});

		$("#toolbarIdbtnExport_3").on("click",function(e){
		
			    //all
			    var _sql_par=[""]; 			 			 
			      var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44_TVTKHOA_EXCEL",_sql_par.join('$'));
			    var mya_dv=JSON.stringify(mya);		    
			    var excel_name='DSTHUOCVT_KHOA';
			    
			    var _DataType = {
						"MA_THUOC":"s",
						"TEN_THUOC":"s",
						"NGAY_CAP_NHAT":"s",
						"NGUOI_CAP_NHAT":"s",
						"LY_DO_KHOA_THUOC":"s"
					};
		     exportXLSX(mya, _DataType, excel_name, excel_name+".xlsx");
		    // exportXLSX(mya_dc, _DataType, excel_name_dc, excel_name_dc+".xlsx");
			});
		
		$("#toolbarIdbtnExport_4").on("click",function(e){
			
		    //all
		    var _sql_par=[""]; 			 			 
		      var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44_LIENTHONG_EX",_sql_par.join('$'));
		    var mya_dv=JSON.stringify(mya);		    
		    var excel_name='import_tvt_lienthong';
		    
		    var _DataType = {
					"MA_HOAT_CHAT":"s",
					"HOAT_CHAT":"s",
					"MA_DUONG_DUNG":"s",
					"DUONG_DUNG":"s",
					"HAM_LUONG":"s",
					"TEN_THUOC_TD":"s",
					"MA_CSKCH_TD":"s",
					"MA_THUOC_TD":"s",
					"MA_THUOC_TT":"s",
					"THUOCVATTUID_TD":"s"
				};
	     exportXLSX(mya, _DataType, excel_name, excel_name+".xlsx");
	    // exportXLSX(mya_dc, _DataType, excel_name_dc, excel_name_dc+".xlsx");
		});
		
	}
	$('#chk_xuatfiles').on("click",function(event){
	
		 var _sql_par=[$("#cboLoai").val().trim(),$("#hidMa_parent").val(),_loai]; 
		 
//		 var _sql_par = [ {
//				name : 'kieu',
//				type : 'String',
//				value : $("#cboLoai").val().trim()},
//				{
//					name : 'manhom',
//					type : 'String',
//					value : ''},
//				{
//						name : 'loai',
//						type : 'String',
//						value : _loai}
//			];
		 
		 
	      var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC44TVT_EXCEL",_sql_par.join('$'));
//		var _sql_par=[loainhomdv]; 
//	    var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC01.EXPORT01",_sql_par.join('$'));
	    var mya_dv=JSON.stringify(mya);
	    
	    var excel_name='DSTHUOCVT';	      
	     JSONToCSVConvertor(mya_dv, excel_name, true);
//		 doViewReport( 'CSV','DUC_EXPORTDANHMUCTHUOC',_sql_par);
	});
	 
	
	 

	 
	function exportXLSX(_jsonObject,_DataType,_wsname,_filename ) {
		var wb = new Workbook();
		var ws = objectToXLSX(_jsonObject,_DataType);
		 
		/* add worksheet to workbook */
		wb.SheetNames.push(_wsname);
		wb.Sheets[_wsname] = ws;
		var wbout = XLSX.write(wb, {bookType:'xlsx', bookSST:true, type: 'binary'});
//		download(s2ab(wbout), _filename, 'application/vnd.ms-excel', 'chkEXP_REPORT_20');
		saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), _filename);
	}
	function s2ab(s) {
		var buf = new ArrayBuffer(s.length);
		var view = new Uint8Array(buf);
		for (var i=0; i!=s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
		return buf;
	}
	
	function Workbook() {
		if(!(this instanceof Workbook)) return new Workbook();
		this.SheetNames = [];
		this.Sheets = {};
	}
	
	objectToXLSX = function (_jsonObject, _DataType) {

		var ws = {};
	    var row;
	    var col;
	    var xml;
	    var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
	    var range = {s: {c:10000000, r:10000000}, e: {c:0, r:0 }};
	    var col_arr = [];
		if(_DataType){
		    col_arr = Object.keys(_DataType);
	
		    for (row = 0; row <= data.length; row++) {
		        for (var col=0; col<col_arr.length; col++) {
					if(range.s.r > row) range.s.r = row;
					if(range.s.c > col) range.s.c = col;
					if(range.e.r < row) range.e.r = row;
					if(range.e.c < col) range.e.c = col;
		        	_col = col_arr[col];
		        	var cell = row>0?{v:data[row-1][_col]}:{v:_col};
					if(cell.v == null) continue;
					var cell_ref = XLSX.utils.encode_cell({c:col,r:row});
					if(row==0) cell.t = 's';
					else cell.t = _DataType[_col];
					
					ws[cell_ref] = cell;
		        }
		    } 
		} else if(data.length>0) {
			col_arr = Object.keys(data[0]);
			
		    for (row = 0; row <= data.length; row++) {
		        for (var col=0; col<col_arr.length; col++) {
					if(range.s.r > row) range.s.r = row;
					if(range.s.c > col) range.s.c = col;
					if(range.e.r < row) range.e.r = row;
					if(range.e.c < col) range.e.c = col;
		        	_col = col_arr[col];
		        	var cell = row>0?{v:data[row-1][_col]}:{v:_col};
					if(cell.v == null) continue;
					var cell_ref = XLSX.utils.encode_cell({c:col,r:row});
					if(row==0) {
						cell.t = 's';
					}
					else if(typeof cell.v === 'number') cell.t = 'n';
					else if(typeof cell.v === 'boolean') cell.t = 'b';
					else if(cell.v instanceof Date) {
						cell.t = 'n'; cell.z = XLSX.SSF._table[14];
						cell.v = datenum(cell.v);
					}
					else cell.t = 's';
					
					ws[cell_ref] = cell;
		        }
		    } 
		}
	    if(range.s.c < 10000000) 
	    	ws['!ref'] = XLSX.utils.encode_range(range);
	    return ws;  
	};
	
 

	function loadTree_new(treeObj) {
		var _sql = "DUC44M001.02";
		var sql_par = _loai +'$'+''+'$'+''+'$';
		var data = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql, sql_par);
		flagLoading = true;
		TreeUtil.load("leftTree", data, false);
	}
	function setSysParam(_par_ar) {
		var v_par = _par_ar;
		for (var i1 = 0; i1 < _param.length; i1++) {
			console.log('_param[' + i1 + ']=' + _param[i1]);
			v_par.push({
				"name" : "[S" + i1 + "]",
				"value" : _param[i1]
			});
		}
		return v_par;
	}
	function loadTree_clear(treeObj) {
		TreeUtil.load("leftTree", "", false);
	}
	$('#cboLoai').change(function() {
		loadGrid();
	});
	$('#txtMaThuoc').change(function() {
		loadGrid();
	});
	
	
	function XoaTrang() {
		$("#hidThuocVatTuID").val("");
	}

	EventUtil.setEvent("Dongpopup", function(e) {
		_leftTree = TreeUtil.init("leftTree", false);
		loadTree_new(_leftTree);
		DlgUtil.close("dlgNTNCC");
//		initControl();
		var param = RSUtil.buildParam("", [ $("#txtMaThuoc").val().trim(),
		                    				$("#cboLoai").val().trim(), 
		                    				$("#hidMa_parent").val(),
		                    				_loai
		                    				 ]);
		                    		console.log("grid_param=" + JSON.stringify(param));
		                    		GridUtil.loadGridBySqlPage("gridDsThuoc", "DUC44M001.01", param);

	});
	EventUtil.setEvent("DSTrungMa_cancel",function(e){
		
		DlgUtil.close("dlgNTNCC");
	});
	//-------------------start xuat excel---------------------------
	function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {
	    //If JSONData is not an object then JSON.parse will parse the JSON string in an Object
	    var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
	    
	    //var CSV = '';    

	    //Set Report title in first row or line
	    
	    var CSV = "\uFEFF";
	    
	    CSV += ReportTitle + '\r\n\n';

	    //This condition will generate the Label/Header
	    if (ShowLabel) {
	        var row = "";
	        
	        //This loop will extract the label from 1st index of on array
	        for (var index in arrData[0]) {
	            
	            //Now convert each value to string and comma-seprated
	            row += index + ',';
	        }

	        row = row.slice(0, -1);
	        
	        //append Label row with line break
	        CSV += row + '\r\n';
	    }
	    
	    //1st loop is to extract each row
	    for (var i = 0; i < arrData.length; i++) {
//	    for (var i = 0; i < 4000; i++) {
	        var row = "";
	        
	        //2nd loop will extract each column and convert it in string comma-seprated
	        for (var index in arrData[i]) {
	            row += '"' + arrData[i][index] + '",';
	        }

	        row.slice(0, row.length - 1);
	        
	        //add a line break after each row
	        CSV += row + '\r\n';
	        
	    }

	    if (CSV == '') {        
	        alert("Invalid data");
	        return;
	    }   
	    
	    //Generate a file name
	    var fileName = "";
	    //this will remove the blank-spaces from the title and replace it with an underscore
	    fileName += ReportTitle.replace(/ /g,"_");   
	    
	    //Initialize file format you want csv or xls
	    //var uri = 'data:text/csv;charset=utf-8,' + escape(CSV);
	    var uri = 'data:text/csv;charset=utf-8,' + encodeURI(CSV);
	    
	    // Now the little tricky part.
	    // you can use either>> window.open(uri);
	    // but this will not work in some browsers
	    // or you will not get the correct file extension    
	    
	    //this trick will generate a temp <a /> tag
	    var link = document.createElement("a");    
	    link.href = uri;
	    
	    //set the visibility hidden so it will not effect on your web-layout
	    link.style = "visibility:hidden";
	    link.download = fileName + ".csv";
	    
	    //this part will append the anchor tag and remove it after automatic click
	    document.body.appendChild(link);
	    link.click();
	    document.body.removeChild(link);
	};
	function doViewReport(fileType, reportCode , _param) {
		//var valid=validateParam();
		//if(!valid) return;
		//var report_id = document.getElementById("hdReportId").value;
		
		var sql_par=[];
		sql_par.push({"name":"[0]","value":reportCode});	
		var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D044.19",sql_par);
		var data_ar_af = JSON.parse(data_ar);
		var arrayLength = data_ar_af.length;
		for (var i = 0; i < arrayLength; i++) {
			var iframe = document.getElementById("ifmXLS");
			var report_url=  window.location.protocol + "//" + window.location.host + "/" + "dreport/";
//			var report_url=  window.location.protocol + "//" + window.location.host + "/" + "dreport/";
			if(report_url=='') report_url="../";
			if(fileType=='grid') {
				document.getElementById("ifmXLS").src = report_url+"report/parameter/ParamBuilder?report_id="+ data_ar_af[0].REPORT_ID+"&filetype="+fileType+"&reportParam="+ getParamToServer(_param)+"&db_name="+DATA_DB_NAME+"&db_schema="+DB_SCHEMA_NAME;
			}
			else {
				document.getElementById("ifmXLS").src = report_url+"report/parameter/ParamBuilder?report_id="+ data_ar_af[0].REPORT_ID+"&filetype="+fileType+"&reportParam="+ getParamToServer(_param);
				//document.getElementById("ifmXLS").src = "https://histest.vnptsoftware.vn/dreport/report/parameter/ParamBuilder?report_id=1418&filetype=xls&reportParam=W3sibmFtZSI6IlZQQVJEX2R0X3R1bmdheSIsInR5cGUiOiJEYXRlOkQiLCJ2YWx1ZSI6IjAxLzAyLzIwMTgifSx7Im5hbWUiOiJWUEFSRF9kdF9kZW5uZ2F5IiwidHlwZSI6IkRhdGU6RCIsInZhbHVlIjoiIn0seyJuYW1lIjoiVlBBUk5fa2hvYWlkIiwidHlwZSI6IlN0cmluZ1tdW10iLCJ2YWx1ZSI6Ii0xQENo4buNbiJ9LHsibmFtZSI6IlZQQVJOX2R0Ym5pZCIsInR5cGUiOiJTdHJpbmdbXVtdIiwidmFsdWUiOiItMUBDaOG7jW4ifSx7Im5hbWUiOiJWUEFSTl9tdWNodW9uZ2lkIiwidHlwZSI6IlN0cmluZ1tdW10iLCJ2YWx1ZSI6Ii0xQENo4buNbiJ9XQ=="
			}
			if (navigator.userAgent.indexOf("MSIE") > -1 && !window.opera){
			    iframe.onreadystatechange = function(){
			        if (iframe.readyState == "complete"){
			        	$("#dLoading").addClass("hidden");
			        }
			    };
			} else {
			    iframe.onload = function(){		    	
			    	$("#dLoading").addClass("hidden");
			    };
			}
		}
	}
	
	function getParamToServer(_param){
		var par_data=JSON.stringify(_param);
		var par_str=window.btoa(unescape(encodeURIComponent(par_data))); //base64.encode(par_data);
		console.log("par_str11="+par_str);
		return par_str;
	}

//	$(function() {
//		$('*').keyup(function(e) {
//			if (e.keyCode == '13') {
//
//				loadGrid();
//				XoaTrang();
//			}
//		})
//	})
}