/*
 * DS phiếu yêu cầu khử khuẩn
 * 
 * Bachnv		17/06/2019		tạo mới
 * 
 */

function DMThuoc(opt) {
	var _param = session_par;
	var _loai = '-1';
	var _kho = '';
	this.opt = $.extend({}, opt);
	var that = this;
	var ngay_lap;
	var PHARMA_IN_PHIEUXUATCHUYENKHO ='0';
	var PHARMA_BC_NHAP_KHO ='0';
	var PHARMA_BC_XUAT_KHO = '0';

	
	var SQL_Phieu = 'DUC85T001_03';
	this.load = doLoad;
	// daidv: Them cau hinh cho in phieu phieu linh va hoantra da duyet -- L2PT-2711
    var PHARMA_IN_PHIEULINH_HOANTRA = 0;
	
	var ctl_ar = [ {
		type : 'button',
		id : 'btnDieuChuyen',
		icon : 'pencil',
		text : 'Điều chuyển',
		style : "width: 130px"
	}, {
		type : 'button',
		id : 'btnThuHoi',
		icon : 'pencil',
		text : 'Thu hồi',
		style : "width: 130px"
	},{
		type : 'button',
		id : 'btnEdit',
		icon : 'sua',
		text : 'Sửa'
	}, {
		type : 'button',
		id : 'btnDuyet',
		icon : 'ok',
		text : 'Duyệt'
	} , {
		type : 'button',
		id : 'btnInPhieuXuat',
		icon : 'print',
		text : 'In Phiếu'
	},
	{type:'buttongroup',id:'btnPrint',icon:'print',text:'In ấn'
			,children:[
		        {id:'tbInPXThuocGayNghienHT',icon:'print',text:'Phiếu xuất thuốc gây nghiện, hướng thần',hlink:'#'},
		        {id:'tbInXuat',icon:'print',text:'In phiếu xuất',hlink:'#'},
		        {id:'tbInXuatChuyenKho',icon:'print',text:'In phiếu xuất giữa các kho',hlink:'#'},
		        {id:'tbInPhieuXuatGNHT',icon:'print',text:'In phiếu xuất GNHT',hlink:'#'},
		        {id:'print_9',icon:'print',text:'Phiếu XK kiêm vận chuyển nội bộ',hlink:'#'},
				{id:'print_10',icon:'print',text:'Phiếu hoàn trả',hlink:'#'},
			    {id:'print_11',icon:'print',text:'Phiếu thu hồi cơ số tủ trực',hlink:'#'},
		        {id:'tbInPhieuYDC_VACCIN_TTB',icon:'print',text:'Biên bản giao nhận vaccin, y dụng cụ, trang thiết bị',hlink:'#'}
		        ]}
	];

	function doLoad() {
		// khởi tạo các tab dữ liệu
		$("li[id^='tab'").on("click", function(e) {
			var tabName = $(this).attr("id").substr(3);
			$("li[class='active']").removeClass("active");
			$(this).addClass("active");
			$("div[class='tab active']").removeClass("active");
			$("#div" + tabName).addClass("active");
		});
		var khoaid = dept_id;
		var toolbar = ToolbarUtil.build('toolbarId', ctl_ar);
//		var that.type = _stringToList(that.opt.type);
		var type = that.opt.type;
		 
		var sql_par=RSUtil.buildParam("",[that.opt.lk]);
		ComboUtil.getComboTag("cboKho","DUC01S002.DSKHO",sql_par,"","","sql","",false);
		if(type=="45") //duyet phieu của khoa khu khuan
			{
				$("#toolbarIdbtnDuyet").show();
				$("#toolbarIdbtnEdit").hide();
				$("#toolbarIdbtnDieuChuyen").hide();
				$("#toolbarIdbtnThuHoi").hide();
				 
				ComboUtil.getComboTag("cboKhoa", "DUC85T001_01", [""], "", "", "");
				$("#cboKhoa").prop("disabled",false);
				SQL_Phieu ='DUC85T001_07';
				var sql_par=RSUtil.buildParam("",["5,6,7"]);
				ComboUtil.getComboTag("cboTrangThai","DUC01S002.TRANGTHAI",sql_par,"",{value:"5,6,7",text:'--Toàn bộ--'},"sql","",false);
				$("#btnGoDuyet").show();
				$("#btnHuyGuiDuyet").hide();
			}
		else {
				$("#toolbarIdbtnDuyet").hide();
				$("#toolbarIdbtnEdit").show();
				$("#toolbarIdbtnDieuChuyen").show();
				$("#toolbarIdbtnThuHoi").show();
				$("#btnGoDuyet").hide();
				$("#btnHuyGuiDuyet").show();
				
				var sql_par = RSUtil.buildParam("",[that.opt.tt]);
				ComboUtil.getComboTag("cboTrangThai","DUC01S002.TRANGTHAI",sql_par,"",{value:that.opt.tt,text:'--Toàn bộ--'},"sql","",false);
		}
		 
		$("#toolbarIdbtnEdit").prop("disabled",true);
		$("#toolbarIdbtnInPhieuXuat").prop("disabled",true);
		$("#toolbarIdtbInPhieuYDC_VACCIN_TTB").hide();
		PHARMA_IN_PHIEUXUATCHUYENKHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_IN_PHIEUXUATCHUYENKHO'].join('$'));
		PHARMA_BC_NHAP_KHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_BC_NHAP_KHO'].join('$'));
		PHARMA_BC_XUAT_KHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_BC_XUAT_KHO'].join('$'));
		if (that.opt.hospitalId == '1022') {
            $("#toolbarIdtbInPhieuYDC_VACCIN_TTB").show();
        }
		initControl();
		bindEvent();
		if (ngay_lap == null || ngay_lap == "") {
			var d = new Date();
			var _month = (d.getMonth() + 1);
			var _date = d.getDate();
			if (_month < 10) {
				_month = '0' + _month;
			}
			if (_date < 10) {
				_date = '0' + _date;
			}
			ngay_lap = _date + "/" + _month + "/" + d.getFullYear();
		}
		
		
		$("#txtTuNgay").val(ngay_lap);
		$("#txtDenNgay").val(ngay_lap);
		
		$(document).ready(function() {
			var callback = function() {
				if ($.active !== 0) {
					setTimeout(callback, '200');
					return;
				}
				_loadDSPhieu();
			};
			callback();
		});
		

	}
	function _loadDSPhieu(){
		var sql_par=RSUtil.buildParam("",[$("#cboKho").val(),'23',$("#cboTrangThai").val(),$('#txtTuNgay').val().trim(),$('#txtDenNgay').val().trim(),that.opt.type ]);
		//$("#grvDsPhieu").setGridParam({page:1});
		//$("#grvDsPhieu").jqGrid("clearGridData", true);
		GridUtil.loadGridBySqlPage("grvDsPhieu",'DUC100DS001_01',sql_par,function(){
			if($("#grvDsPhieu").getGridParam("reccount")>0)
			{				
				$("#grvDsPhieu").setSelection(1,true);
			}
			else{
				FormUtil.clearForm("divCT","");
				$('#grvDsPhieu').clearGridData();
			}
		});
		
		
		//$("#grvDsPhieu").trigger("reloadGrid",[{page:1}]);
	};

	function initControl() {

		// ComboUtil.getComboTag("cboKhoa", "DMC04.03", [], "", "",
		// "sql","",dept_id);

		var _gridHeader = " ,icon,30,0,ns,l;" 
				+ "NHAPXUATID,NHAPXUATID,0,0,t,l;"
				+ "Mã phiếu,MA,100,0,f,l;"
				+ "Ngày tạo,NGAYNX,100,0,f,l;"
				+ "Kho lập,KHOLAP,100,0,f,l;"
				+ "Kho đối ứng,KHODOIUNG,100,0,f,l;"
				+ "Kiểu,KIEU,100,0,t,l;"
				+ "Loại phiếu,LOAIPHIEU,100,0,f,l;"
				+ "Trạng thái,TRANGTHAI,100,0,f,l;"
				+ "STT Nhập,STTNHAPKHO,100,0,f,l;"
				+ "TRANGTHAIID,TRANGTHAIID,20,0,t,l;"
				+ "Người tạo phiếu,NGUOITAO,150,0,f,l;"
				+ "Ngày duyệt,NGAYDUYET,100,0,f,l;"
				+ "Người duyệt,NGUOIDUYET,150,0,f,l;"
				+ "Tổng tiền,TONGTIEN,150,0,t,l;"
				+ "TONGTIENPHAITRA,TONGTIENPHAITRA,150,0,t,l;"
				
				+ "NHAPID,NHAPID,150,0,t,l;"
				+ "TRANGTHAINHAP,TRANGTHAINHAP,150,0,t,l;"
				+ "XUATID,XUATID,150,0,t,l;"
				+ "TRANGTHAIXUAT,TRANGTHAIXUAT,150,0,t,l"
				;

		GridUtil.init("grvDsPhieu", "100%", "420px", "Danh sách phiếu",
				false, _gridHeader, false);
		var _gridHeader2 = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
				+ "Mã vật tư,MA_THUOC,70,0,f,l;"
				+ "Tên vật tư,TEN_THUOC,140,0,f,l;"
				+ "Hàm lượng,LIEULUONG,80,f,l;"
				+ "Đơn vị,DON_VI_TINH,50,0,f,l;"
				+ "SL yêu cầu,SL_YC,70,0,f,l;"
				+ "SL duyệt,SLDUYET,70,0,f,l;"
				+ "Giá bán,DON_GIA,65,0,f,l;"
				+ "TRANGTHAIID,TRANGTHAIID,20,0,t,l"
				 ;

		GridUtil.init("grvDsThuoc", "100%", "213px", "Danh sách thuốc",
				false, _gridHeader2, false);
	}
	function bindEvent() {
		GridUtil.setGridParam("grvDsPhieu", {
			onSelectRow : function(id) {
				GridUtil.unmarkAll("grvDsPhieu");
				GridUtil.markRow("grvDsPhieu", id);
				if (id) {
					var _row = $("#grvDsPhieu").jqGrid('getRowData', id);
					 $("#txtNguoiLap").val(_row.NGUOITAO);
					 $("#txtNgayLap").val(_row.NGAYNX);
					 $("#txtKhoDoiUng").val(_row.KHODOIUNG);
					 $("#txtKhoLap").val(_row.KHOLAP);
					 $("#txtTienDon").val(_row.TONGTIENPHAITRA);
					 $("#txtThanhTien").val(_row.TONGTIEN);
					 $("#hid_NHAPXUATID").val(_row.NHAPXUATID);
					 $("#hidTrangThai").val(_row.TRANGTHAIID);
					 $("#hidTrangThaiNhap").val(_row.TRANGTHAINHAP);
					 $("#hidTrangThaiXuat").val(_row.TRANGTHAIXUAT);
					 $("#toolbarIdbtnInPhieuXuat").prop("disabled",false);
					 $("#hidKieu").val(_row.KIEU);
					 if(_row.TRANGTHAIID!='1') 
						 $("#toolbarIdbtnEdit").prop("disabled",true);
					 else $("#toolbarIdbtnEdit").prop("disabled",false);
					 if(_row.TRANGTHAIID!='5') 
						 $("#toolbarIdbtnDuyet").prop("disabled",true);
					 else $("#toolbarIdbtnDuyet").prop("disabled",false);
					 if(_row.XUATID !='' && _row.TRANGTHAIID!='1')
						 var param = RSUtil.buildParam("", [_row.XUATID]);
					 else if(_row.NHAPID !='' && _row.TRANGTHAIID!='1')
						 var param = RSUtil.buildParam("", [_row.NHAPID]);
					 else 
					 var param = RSUtil.buildParam("", [_row.NHAPXUATID]);
					 
						GridUtil.loadGridBySqlPage("grvDsThuoc", "DUC85T001_04", param);
						
					if(_row.TRANGTHAINHAP == '4' && _row.TRANGTHAIXUAT ==''){
						$("#btnXuatKho").show();
					} else $("#btnXuatKho").hide();
					if(_row.TRANGTHAINHAP=='4')
					{
						 $("#toolbarIdbtnDuyet").prop("disabled",true);
					} else $("#toolbarIdbtnDuyet").prop("disabled",false);
					if(_row.TRANGTHAIID=='6'||_row.TRANGTHAIID=='7')
					{
						 $("#toolbarIdbtnDuyet").prop("disabled",true);
					} else $("#toolbarIdbtnDuyet").prop("disabled",false);
					if(_row.TRANGTHAIID=='6'&&_row.TRANGTHAIXUAT=='5')
					{
						 $("#btnXacNhan").prop("disabled",false);
					} else  $("#btnXacNhan").prop("disabled",true);
					
					
					if(_row.TRANGTHAIID == '6'  ){
						$("#btnGoDuyet").prop("disabled",false);
					} else $("#btnGoDuyet").prop("disabled",true);
					if(_row.TRANGTHAIID == '5'  ){
						$("#btnHuyGuiDuyet").prop("disabled",false);
					} else $("#btnHuyGuiDuyet").prop("disabled",true);
					 
				}
			},
			gridComplete: function(){ 
		        var ids = $("#grvDsPhieu").getDataIDs(); 
		        for(var i=0;i<ids.length;i++){
		        	var id = ids[i];
		        	var row = $("#grvDsPhieu").jqGrid('getRowData',id);
		        	_formatRow(id,row.TRANGTHAIID-1);
//		        	if(row.TRANGTHAIXUAT !='6'){
//		        		$('#grvDsPhieu').find("tr[id='" + row + "']").find('td').each(function(index, element) {			        
//		        			$(element).css({'color':'#FF0000'})
//		        		 });
//		        	}
		        } 
			},
			ondblClickRow : function(id) {
				var myVar = {
						KhoaId : $("#cboKhoa").val(),
						nhapxuatid : $("#hid_NHAPXUATID").val(),
						kieu : $("#hidKieu").val(),
						trangthai: '1'
					};

					dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg",
							"manager.jsp?func=../duoc/DUC100DS001_TaoYeuCau",
							myVar, "Yêu cầu điều chuyển", 1200, 610);
					DlgUtil.open("dlgYCNhap");
			}
		});
		EventUtil.setEvent("appr_success", function(e) {
			DlgUtil.close("dlgYCNhap");
			  _loadDSPhieu();
		});
		$("#toolbarIdbtnDieuChuyen").on(
				"click",
				function(e) {
					EventUtil.setEvent("YCNhap_success", function(e) {
						DlgUtil.close("dlgYCNhap");
						  _loadDSPhieu();
					});
					var myVar = {
							tenkho : $("#cboKho").val(),
							khoid : $("#cboKho").val(),
							nhapxuatid : 0,
							kieu : '2'
					};

					dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg",
							"manager.jsp?func=../duoc/DUC100DS001_TaoYeuCau",
							myVar, "Yêu cầu điều chuyển", 1200, 610);
					DlgUtil.open("dlgYCNhap");
				});
		
		$("#toolbarIdbtnThuHoi").on(
				"click",
				function(e) {
					EventUtil.setEvent("YCNhap_success", function(e) {
						DlgUtil.close("dlgYCNhap");
						  _loadDSPhieu();
					});
					var myVar = {
							tenkho : $("#cboKho").val(),
							khoid : $("#cboKho").val(),
							nhapxuatid : 0,
							kieu : '3'
					};

					dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg",
							"manager.jsp?func=../duoc/DUC100DS001_TaoYeuCau",
							myVar, "Yêu cầu điều chuyển", 1200, 610);
					DlgUtil.open("dlgYCNhap");
				});
		
		$("#toolbarIdbtnEdit").on(
				"click",
				function(e) {
					EventUtil.setEvent("YCNhap_success", function(e) {
						DlgUtil.close("dlgYCNhap");
						  _loadDSPhieu();
					});
					var myVar = {
						KhoaId : $("#cboKhoa").val(),
						nhapxuatid : $("#hid_NHAPXUATID").val(),
						kieu : $("#hidKieu").val()
					};

					dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg",
							"manager.jsp?func=../duoc/DUC100DS001_TaoYeuCau",
							myVar, "Yêu cầu điều chuyển", 1200, 610);
					DlgUtil.open("dlgYCNhap");
				});
		$("#toolbarIdbtnDuyet").on(
				"click",
				function(e) {
					EventUtil.setEvent("YCNhap_success", function(e) {
						DlgUtil.close("dlgYCNhap");
						  _loadDSPhieu();
					});

					var myVar={nhapxuatId:$("#hid_NHAPXUATID").val(),kieu:'2',hinhthuc:'23',ht:'23',khoid: $("#cboKho").val()};
					dlgPopup=DlgUtil.buildPopupUrl("dlgYCNhap","divDlg","manager.jsp?func=../duoc/DUC100DS001_DuyetPhieu",myVar,"Duyệt phiếu",1100,550);
					DlgUtil.open("dlgYCNhap");
//					var myVar = {
//					kho : $("#cboKho").val(),
//					nhapxuatid : $("#hid_NHAPXUATID").val(),
//					yeucau : '0' //nhap kho
//				};
//
//				dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg",
//							"manager.jsp?func=../duoc/DUC100DS001_DuyetYC",
//							myVar, "Duyệt yêu cầu", 1300, 610);
//				DlgUtil.open("dlgYCNhap");
				});
		$("#toolbarIdbtnInPhieuXuat").on(
				"click",
				function(e) {
					var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
					var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;	
					var _kieu = $("#grvDsPhieu").jqGrid('getRowData', row).KIEU;
						var par = [ {
							name : 'nhapxuatid',
							type : 'String',
							value : _id}
						];
					if(PHARMA_IN_PHIEUXUATCHUYENKHO == '1'){
						openReport('window', 'DUC_PHIEUXUATCHUYENKHO', 'pdf', par);	
					}
					if(_param[0] == '32620' && _kieu == '3'){
						openReport('window', 'DUC100_PHIEU_THUHOIKHO', 'pdf', par);
					}else{
						openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
					}
					
						
	  	});
	  		$("#toolbarIdtbInPXThuocGayNghienHT").on("click",function(e){
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
			var par = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _id.toString()
				}];
			if(that.opt.hospitalId == '29040') {
				var ret = '';
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC.DSPHIEU.GNHT", _id);
				if(ret == '' || ret == null) {
					return DlgUtil.showMsg("Phiếu không có thuốc gây nghiện, hướng thần");
				}
				var rptlist = ret.split(";");
				for (var i = 0; i < rptlist.length; i++) {
					openReport('window', rptlist[i].trim(), 'pdf', par);
				}
			} else
				openReport('window', 'DUC015_PHIEUXUATKHOTHUOCGAYNGHIENTAMTHANTIENCHATDUNGLAMTHUOC_MS3_TT192014_A4', 'pdf', par);
		});
		$("#toolbarIdtbInXuatChuyenKho").on("click",function(e){
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
			
			if(PHARMA_BC_NHAP_KHO == 10284){
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATRA.15", _id);
				var _param = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _id
				} ];
				if (ret != null && ret != '') {
					var _freport = ret.split(";");
					for (var i = 0; i < _freport.length; i++) {
						openReport('window', _freport[i].trim(), "pdf",_param);
						//CommonUtil.inPhieu('window', _freport[i].trim(), 'xlsx', _param, _freport[i].trim() + '.xlsx');
					}
				}	
			}else{
				var par = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _id}
				];
				openReport('window', 'DUC_PHIEUXUATCHUYENKHO', 'pdf', par);
			}
			
		});
		$("#toolbarIdtbInPhieuXuatGNHT").on("click",function(e){
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;			
			var par = [ {
				name : 'nhapxuatid',
				type : 'String',
				value : _id.toString()
			}];			
			openReport('window', 'DUC_PHIEUXUATKHO_TGN_THT_BENLUC_LAN_A4', 'pdf', par);	
			if(PHARMA_TUDONG_TAIFILE == 1){	
				rpName= "DUC_PHIEUXUATKHO_TGN_THT_BENLUC_LAN_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
					CommonUtil.inPhieu('window', 'DUC_PHIEUXUATKHO_TGN_THT_BENLUC_LAN_A4', 'xlsx', par, rpName);				
				
			}
			
		});
			$("#toolbarIdprint_9").on("click",function(e){
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
			//var _kieu = $("#grvDsPhieu").jqGrid('getRowData', row).KIEU;
			
			_print(_id,'13');
		});
		//tuyendv L2PT-112090
		$("#toolbarIdprint_10").on("click", function (e) {
            var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (that.opt.hospitalId == '24560' && that.opt.ht == '12') {
                if (that.opt.gd == "VATTU") {
                    var par = [{
                        name: 'nhapxuatid',
                        type: 'String',
                        value: _id
                    }];
                    openReport('window', "DUC_PHIEUHOANTRAVATTU", "pdf", par);
                } else {
                    var par = [{
                        name: 'nhapxuatid',
                        type: 'String',
                        value: _id
                    }];
                    openReport('window', "DUC_PHIEUHOANTRATHUOC", "pdf", par);
                }
                return;

            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grvDsPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            if (that.opt.hospitalId == '7282' && that.opt.ht == '2') {
                openReport('window', "DUC_PHIEUHOANTRA_NHQNM", "pdf", par);
                openReport('window', "DUC_PHIEUHOANTRATVT_TUNGHIA", "pdf", par);
            } else if (that.opt.hospitalId == '39401') {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSTRA.BH01", _id);
                var _param = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                if (ret != null && ret != '') {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        openReport('window', _freport[i].trim(), "pdf", _param);
                    }
                }
            } else {
                openReport('window', "DUC_PHIEUHOANTRA_NHQNM", "pdf", par);
            }


        });
		$("#toolbarIdprint_11").on("click", function (e) {
			var row = $("#grdPhieu").jqGrid("getGridParam", "selrow");
			var _id = $("#grdPhieu").jqGrid("getRowData", row).NHAPXUATID;
			if (_id == "") return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

			var par = [
				{
					name: "nhapxuatid", //nhapxuatid
					type: "String",
					value: _id,
				},
			];
			openReport('window', 'PHIEUTHUHOI_COSO_TUTRUC', 'pdf', par);
		});
			
		$("#toolbarIdtbInPhieuYDC_VACCIN_TTB").on("click", function (e) {
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
			//var _kieu = $("#grvDsPhieu").jqGrid('getRowData', row).KIEU;

			//_print(_id,'1');
			var par = [ {
				name : 'nhapxuatid',
				type : 'String',
				value : _id.toString()
			}];
            var rpName = "BB_GIAONHAN_YDCTTB" + jsonrpc.AjaxJson.getSystemDate("DDMMYY-HH24MISS") + "." + "xls";
            openReport("window", "BB_GIAONHAN_YDCTTB", "pdf", par);
        });
		
 		$("#toolbarIdtbInXuat").on("click",function(e){
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
			//var _kieu = $("#grvDsPhieu").jqGrid('getRowData', row).KIEU;
			
			//_print(_id,'1');
			var par = [ {
				name : 'nhapxuatid',
				type : 'String',
				value : _id.toString()
			}];
			var rpName= '';
			var rpName_ht = '';
			var Rpt= '';
			var Rpt2= '';
			if(PHARMA_BC_XUAT_KHO == 1){
				//doViewReport( 'XLS','DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM',par);
				 rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xls';
			      // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM", 'xls', par, rpName);
				 Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM';
				openReport('window', Rpt, 'pdf', par);
				   
			}
			/*else if (that.opt.gd.includes("VATTU")){
				doViewReport( 'XLS','DUC009_PHIEUXUATKHO_VATTU_BVNT_A4',par);
				openReport('window', 'DUC009_PHIEUXUATKHO_VATTU_BVNT_A4', 'pdf', par);
			}*/
			else if (PHARMA_BC_NHAP_KHO == 919){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
				
				EventUtil.setEvent("InPhieuXuat_success",function(e){
					DlgUtil.close("dlgInPhieuXuat");
					if(that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" ||that.opt.td == "13")){
						_loadDSPhieuBUCSTT();
					}else{
						_loadDSPhieu();
					}
					 
				});
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);

				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (PHARMA_BC_NHAP_KHO == 14866){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
				
				EventUtil.setEvent("InPhieuXuat_success",function(e){
					DlgUtil.close("dlgInPhieuXuat");
					if(that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" ||that.opt.td == "13")){
						_loadDSPhieuBUCSTT();
					}else{
						_loadDSPhieu();
					}
					 
				});
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);

				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (PHARMA_BC_NHAP_KHO == 944){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
				
				EventUtil.setEvent("InPhieuXuat_success",function(e){
					DlgUtil.close("dlgInPhieuXuat");
					if(that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" ||that.opt.td == "13")){
						_loadDSPhieuBUCSTT();
					}else{
						_loadDSPhieu();
					}
				});
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);
				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (that.opt.hospitalId == '5926'){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
			
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);
				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (that.opt.hospitalId == '923'){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
			
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);
				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (that.opt.hospitalId == '30860'){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
			
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);
				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (that.opt.hospitalId == '30680'){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
			
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);
				dlgPopup.open("dlgInPhieuXuat");
			}
			else if (PHARMA_BC_NHAP_KHO == 26780 || PHARMA_BC_NHAP_KHO == 29040){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);				
				var myVar={nhapxuatid:_id};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
						"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);

				dlgPopup.open("dlgInPhieuXuat");
			}
			else if(PHARMA_BC_NHAP_KHO == 10284){
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATRA.15", _id);
				var _param = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _id
				} ];
				if (ret != null && ret != '') {
					var _freport = ret.split(";");
					for (var i = 0; i < _freport.length; i++) {
						openReport('window', _freport[i].trim(), "pdf",_param);
						//CommonUtil.inPhieu('window', _freport[i].trim(), 'xlsx', _param, _freport[i].trim() + '.xlsx');
					}
				}	
			}
			else if (PHARMA_BC_NHAP_KHO == 1108){
				 rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
			       //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA", 'xlsx', par, rpName);
				 Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA';
				openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA', 'pdf', par);
					
				 rpName_ht= "DUC009_PHIEUXUATKHO_QD_BTC_A4_THT" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
			      // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_THT", 'xlsx', par, rpName_ht);
				 Rpt2= 'DUC009_PHIEUXUATKHO_QD_BTC_A4_THT';
				openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_THT', 'pdf', par);
				
			}
			else if (PHARMA_BC_NHAP_KHO == 987){
				var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
				var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
				var _hinhthucid = $("#grvDsPhieu").jqGrid('getRowData', row).HINHTHUCID;
				if (_id == "")
					return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
				
				if(_hinhthucid == 7 || _hinhthucid == 8 || _hinhthucid == 6){
					   rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				      // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO", 'xlsx', par, rpName);
					   Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO';
					openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO', 'pdf', par);
				}else if(that.opt.ht ==2 && that.opt.hospitalId == '987'){
					if(that.opt.gd.includes("VATTU")){
						 rpName= "DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
						//CommonUtil.inPhieu('window', "DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO", 'xlsx', par, rpName);
						 Rpt= 'DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO';
						openReport('window', 'DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO', 'pdf', par);
					}
					else {
						var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
						var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
						if (_id == "")
							return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
						
						EventUtil.setEvent("InPhieuXuat_success",function(e){
							DlgUtil.close("dlgInPhieuXuat");
							_loadDSPhieu();
						});
						var myVar={nhapxuatid:_id};
						dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
								"manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar,"Danh sách phiếu Xuất", 350, 400);

						dlgPopup.open("dlgInPhieuXuat");
						// var rpName= "DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
						// CommonUtil.inPhieu('window', "DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4", 'xlsx', par, rpName);
						// openReport('window', 'DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4', 'pdf', par);
					}					
				}else{
					   rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				      // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO", 'xlsx', par, rpName);
					   Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO';
					openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO', 'pdf', par);
				}	
			}
			else if (PHARMA_BC_NHAP_KHO == 957) {
				if(that.opt.type == 4) {
					 rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				      // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_DUYETYC_QD_BTC_A4_957", 'xlsx', par, rpName);
					 Rpt= 'DUC009_PHIEUXUATKHO_DUYETYC_QD_BTC_A4_957';
					openReport('window', 'DUC009_PHIEUXUATKHO_DUYETYC_QD_BTC_A4_957', 'pdf', par);
				}
				else {
					 rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				       //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
					 Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4';
					openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
				}
			}
			else{
				//doViewReport( 'XLS','DUC009_PHIEUXUATKHO_QD_BTC_A4',par);
				if(that.opt.ht ==2 && that.opt.hospitalId == '987'){
					openReport('window', 'DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4', 'pdf', par);
				}else if(that.opt.ht ==2 && that.opt.hospitalId == '951' && that.opt.gd.includes("VATTU")){
					 rpName= "DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				       //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4", 'xlsx', par, rpName);
					 Rpt= 'DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4';
					openReport('window', 'DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4', 'pdf', par);
				}else if(that.opt.hospitalId == '993' || that.opt.hospitalId == '930'){
					//START L2PT-14367
					 rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				   // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN", 'xlsx', par, rpName);
					 Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN';
					openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN', 'pdf', par);
				}else if(that.opt.ht ==2 && that.opt.hospitalId == '965' && that.opt.gd.includes("THUOC")){
					var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATKHO.965", _id);
					if (ret != null && ret != '') {
						var _freport = ret.split(";");
						for (var i = 0; i < _freport.length; i++) {
							rpName= _freport[i].trim() + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
							CommonUtil.inPhieu('window', _freport[i], 'xlsx', par, rpName);
							openReport('window', _freport[i].trim(), 'pdf', par);
						}
					}
				}else{
					 rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				       //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
					 Rpt= 'DUC009_PHIEUXUATKHO_QD_BTC_A4';
					openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
				}
				   
			}
			if(PHARMA_TUDONG_TAIFILE == 1){				
				if(Rpt != ''){
					CommonUtil.inPhieu('window', Rpt, 'xlsx', par, rpName);
				}
				if(Rpt2 != ''){
					CommonUtil.inPhieu('window', Rpt2, 'xlsx', par, rpName_ht);
				}
			}
			
		});
		
	};
 
		$("#btnHuyGuiDuyet").on("click",function(e){
			var rowIndex = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var row = $("#grvDsPhieu").jqGrid('getRowData', rowIndex);
			var params = [row.NHAPXUATID];
			 
				var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S002.HUYCK01",params.join('$')));
				if(ret.SUCCESS>0){
					DlgUtil.showMsg("Hủy gửi duyệt thành công");
					_loadDSPhieu();
				}
				else{
					DlgUtil.showMsg(ret.MESSAGE);
				}
			 
			
		});
 
	
	
	$("#btnTimKiem").on("click",function(e){
		_loadDSPhieu();
		});
	$("#btnGoDuyet").on("click",function(e){
		if(confirm("Bạn có thực sự muốn gỡ duyệt?")){
			var row = $("#grvDsPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grvDsPhieu").jqGrid('getRowData', row).NHAPXUATID;
			//var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);
			var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC47T001.CANCEL",_id));
			if(ret.SUCCESS>0){
				DlgUtil.showMsg("Gỡ duyệt thành công");
				_loadDSPhieu();
			}
			else{
				DlgUtil.showMsg(ret.MESSAGE);
			}
		 
		}
	});
	$('#cboKho').change(function() {
		_loadDSPhieu();

	});
	$("#btnXacNhan").on("click",function(e){
			var _par = [ $("#hid_NHAPXUATID").val() ];			
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('DUC85T001_10', _par.join('$'));
			var data = $.parseJSON(result);

			var _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);

			} else {
				DlgUtil.showMsg('Xác nhận thành công', function() {
					 _loadDSPhieu();
				});
			}
		});
	
	function _formatRow(_rId, _fIndex)
	{
		var _icon = '';
		if(that.opt.imgPath[_fIndex] != '')
			_icon = '<center><img src="../common/image/' + that.opt.imgPath[_fIndex] + '" width="15px"></center>'; 
        $("#grvDsPhieu").setRowData(_rId,{icon:_icon});
//        $("#grvDsPhieu").find("tr[id='" + _rId + "']").find('td').each(function(index, element) {
//        	$(element).css({'color':that.opt.foreColor[_fIndex]});
//        	//$(element).css({'background-color':that.opt.backColor[_fIndex]});
//	    });
        $("#grvDsPhieu").find("tr[id='" + _rId + "']").find('td').each(function(index, element) {
        	if(_fIndex +1==5)
        		$(element).css({'color':'#FF0000'})
        		
        		//$(element).css({'background-color':that.opt.backColor[_fIndex]});
        	else
        	{
    			
        		$(element).css({'color':that.opt.foreColor[_fIndex]});
    		};
        		//$(element).css({'color':'#FF0000'});
	    });
	}

		
		
		function _print(_id, _loaiphieu){
		var sql_par=_id +"$";
		if(_loaiphieu==0 || _loaiphieu==3){//phieu nhap			
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.03',sql_par);		
			var row=data_ar[0];
	    	var thanhtien = row.THANH_TIEN;
	    	var _no = parseInt(row.NO).format(0, 3, ',');
	    	var _co = parseInt(row.CO).format(0, 3, ',');
	    	var tien_chu = CommonUtil.toWord(row.THANH_TIEN);
			var par = [ {
				name : 'nhapxuatid',
				type : 'String',
				value : _id.toString()
			},
			{
				name : 'so_y_te',
				type : 'String',
				value : row.SO_Y_TE
			},
			{
				name : 'benh_vien',
				type : 'String',
				value : row.BENH_VIEN
			},
			{
				name : 'khoa',
				type : 'String',
				value : row.KHOA
			},
			{
				name : 'ngay_nhap',
				type : 'String',
				value : row.NGAY_NHAP
			},
			{
				name : 'nha_cung_cap',
				type : 'String',
				value : row.NHA_CUNG_CAP
			},
			{
				name : 'kho_nhap',
				type : 'String',
				value : row.KHO_NHAP
			},
			{
				name : 'nguoi_giao',
				type : 'String',
				value : row.NGUOI_GIAO
			},
			{
				name : 'ma_phieu',
				type : 'String',
				value : row.MA_PHIEU
			},
			{
				name : 'no',
				type : 'String',
				value : _no
			},
			{
				name : 'so',
				type : 'String',
				value : row.SO
			},
			{
				name : 'co',
				type : 'String',
				value : _co
			},
			{
				name : 'thanh_tien',
				type : 'String',
				value : thanhtien.toString()
			},
			{
				name : 'tien_bang_chu',
				type : 'String',
				value : row.TIEN_BANG_CHU
			},
			{
				name : 'so_ct',
				type : 'String',
				value : row.SO_CT
			}, 
			{
				name : 'i_sch',
				type : 'String',
				value : that.opt.i_sch.toString()
			},
			{
				name : 'gio',
				type : 'String',
				value : row.GIO
			},
			{
				name : 'ngay',
				type : 'String',
				value : row.NGAY
			},
			{
				name : 'thang',
				type : 'String',
				value : row.THANG
			}, 
			{
				name : 'nam',
				type : 'String',
				value : row.NAM
			},
			{
				name : 'ngay_ct',
				type : 'String',
				value : row.NGAY_CT
			},
			{
				name : 'thang_ct',
				type : 'String',
				value : row.THANG_CT
			}, 
			{
				name : 'nam_ct',
				type : 'String',
				value : row.NAM_CT
			}
			];
			
			if(PHARMA_BC_NHAP_KHO == 1){
				openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4_SN_HNM', 'pdf', par);
			}
			else{
				openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4', 'pdf', par);
			}
			
		}
		else if(_loaiphieu==1 || _loaiphieu==2){//phieu xuat				
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.04',sql_par);			
			var row=data_ar[0];
			var tien_chu = CommonUtil.toWord(row.TONG_TIEN);
			var _no = parseInt(row.NO).format(0, 3, ',');
	    	var _co = parseInt(row.CO).format(0, 3, ',');
			var par = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _id.toString()
				},
				{
					name : 'khoa',
					type : 'String',
					value : row.KHOA.toString()
				},
				{
					name : 'soPhieu',
					type : 'String',
					value : row.SOPHIEU.toString()
				},
				{
					name : 'co',
					type : 'String',
					value : _co
				},
				{
					name : 'no',
					type : 'String',
					value : _no
				},
				{
					name : 'nguoi_giao',
					type : 'String',
					value : row.NGUOI_GIAO.toString()
				},
				{
					name : 'donViNhan',
					type : 'String',
					value : row.DONVINHAN.toString()
				},
				{
					name : 'lyDoXuat',
					type : 'String',
					value : row.LYDOXUAT.toString()
				},
				{
					name : 'khoXuat',
					type : 'String',
					value : row.KHOXUAT
				}
				, 
				{
					name : 'tienBangChu',
					type : 'String',
					value : row.TIEN_BANG_CHU.toString()
				},
				{
					name : 'soChungTu',
					type : 'String',
					value : row.SO_CHUNG_TU.toString()
				}
				];
			

			if(PHARMA_BC_XUAT_KHO == 1){
				openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM', 'pdf', par);
			}
			else{
				openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
			}
			
		}
		else if(_loaiphieu==13){//phieu xuat	huy	
			
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.04',sql_par);			
			var row=data_ar[0];
			var tien_chu = CommonUtil.toWord(row.TONG_TIEN);
			var _no = parseInt(row.NO).format(0, 3, ',');
	    	var _co = parseInt(row.CO).format(0, 3, ',');
			var par = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _id.toString()
				},
				{
					name : 'khoa',
					type : 'String',
					value : row.KHOA.toString()
				},
				{
					name : 'soPhieu',
					type : 'String',
					value : row.SOPHIEU.toString()
				},
				{
					name : 'co',
					type : 'String',
					value : _co
				},
				{
					name : 'no',
					type : 'String',
					value : _no
				},
				{
					name : 'nguoi_giao',
					type : 'String',
					value : row.NGUOI_GIAO.toString()
				},
				{
					name : 'donViNhan',
					type : 'String',
					value : row.DONVINHAN.toString()
				},
				{
					name : 'lyDoXuat',
					type : 'String',
					value : row.LYDOXUAT.toString()
				},
				{
					name : 'khoXuat',
					type : 'String',
					value : row.KHOXUAT
				}
				, 
				{
					name : 'tienBangChu',
					type : 'String',
					value : row.TIEN_BANG_CHU.toString()
				},
				{
					name : 'soChungTu',
					type : 'String',
					value : row.SO_CHUNG_TU.toString()
				}
				];
			openReport('window', 'DUC_PHIEUXUATKHO_KIEMVANCHUYEN_NB', 'pdf', par);
		}
	}
		
}