<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css" />
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css" />
<link rel="stylesheet" href="../common/css/custom.css" />
<link rel="stylesheet" href="../common/css/css_style.css" />
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript"
	src="../common/script/UIUtil.js?v=20170106"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=**********"></script>
<script type="text/javascript"
	src="../canlamsang/ParaclinicalUtility.js"></script>
<script type="text/javascript" src="../canlamsang/GoiBenhNhan.js"></script>
<script type="text/javascript"
	src="../canlamsang/LISConnector.js?v=20170614"></script>
<!-- nghiant 27102017 -->
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<!--end nghiant 27102017 -->
<script type="text/javascript"
	src="../canlamsang/CLS01X010_QMS_CDHA_GOI_BN.js?v=20190419"></script>
<script type="text/javascript"
	src="../common/script/responsive-voice.js?v=2610"></script>

<style>
.ui-jqgrid tr.jqgrow td {
	white-space: nowrap !important;
	height: auto;
	vertical-align: text-top;
	padding-top: 2px;
}

#toolbarId .wd100 {
	float: none !important;
}

#toolbarIdbtnTiepNhanTheoLo {
	width: 150px;
}
</style>

<div width="100%" id="divMain" class="container"
	help="help_V_1_Nhapthongtinxetnghiem">
	<!-- 	<div id='toolbarId'></div> -->
	<!-- 	<div id="goibenhnhan"> -->
	<!-- 		<div id="playlist" style="display: none;"> -->
	<!-- 	        <audio controls autoplay></audio> -->
	<!-- 	    </div> -->
	<!-- 	</div> -->

	<!-- SONDN -->
	<div class="panel-body mgt5" id="dvCall5">
		<div class="col-xs-12 low-padding">
			<div class="col-xs-4">
				<div class="col-xs-4">
					<input type="text" class="form-control input-sm"
						style="font-weight: bold; color: red; font-size: 15px; width: 100%"
						value="" id="txtSTT5" placeholder="STT" disabled>
				</div>
				<div class="col-xs-8">
					<input type="text" class="form-control input-sm"
						style="font-weight: bold; color: green; font-size: 12px; width: 100%"
						value="" id="txtHOTEN5" placeholder="Họ tên" disabled>
				</div>
				<div class="col-xs-8" style="display: none">
					<input type="text" value="" id="txtID5" disabled> <input
						type="text" value="" id="txtLANGOI5" disabled> <input
						type="text" value="" id="txtMABHYT5" disabled> <input
						type="text" value="" id="txtUUTIEN5" disabled> <input
						type="text" value="" id="txtMABENHNHAN5" disabled> <input
						type="text" value="" id="txtSOQUAY5" disabled> <input
						type="text" value="" id="txtMaBA" disabled><input
						type="text" value="" id="txtPhongDG" disabled>
				</div>
				<div style="display: none;">
					<label>Choose voice</label> <select id="voices"></select>
				</div>
			</div>
			<div class="col-xs-8"
				style="font-size: 14px; font-weight: bold;">
				<div class="col-xs-12">
					<div class="col-xs-6">
					<div class="col-xs-5">Trạng thái</div>
					<div class="col-xs-7">	<input type="text" class="form-control input-sm"
							style="font-weight: bold; color: red; font-size: 14px; width: 100%"
							value="" id="txtMSGSTT" placeholder="Gọi bệnh nhân" disabled> </div>
					</div>
					<div class="col-xs-6">
					<div class="col-xs-6">Số lần gọi tối đa</div>
					<div class="col-xs-6">	<input type="number" class="form-control input-sm"
							style="font-weight: bold; color: red; font-size: 14px; width: 100%"
							value="" id="txtSolantoida" placeholder="nhập số lần" min="1"></div>
					</div>
				</div>
			</div>
			<div class="col-xs-12" style="text-align: center; margin-top: 10px;">
				<button type="button" class="btn btn-sm btn-primary heightbtn"
					id="btnGOITIEP5">
					<span class="glyphicon glyphicon-volume-up"></span> Gọi BN
				</button>
<!-- 				<button type="button" class="btn btn-sm btn-primary heightbtn" -->
<!-- 					id="btnGOILAI5"> -->
<!-- 					<span class="glyphicon glyphicon-refresh"></span> Gọi Lại -->
<!-- 				</button> -->
				<button type="button" class="btn btn-sm btn-primary heightbtn"
					id="btnThucHien">
					<span class="glyphicon glyphicon-new-window"></span> Bắt đầu thực hiện
				</button>
				<button type="button" class="btn btn-sm btn-primary heightbtn"
					id="btnDSGOILAI5">
					<span class="glyphicon glyphicon-list"></span> DS gọi lại
				</button>
				<button type="button" class="btn btn-sm btn-primary heightbtn"
					id="btnLCDNHO5">
					<span class="glyphicon glyphicon-picture"></span> Xem LCD
				</button>
			</div>
		</div>
	</div>
	<!-- END SONDN -->
	<div class="panel-body mgt5">
		<div class="col-md-9 low-padding mgt10">
			<div class="col-md-6 low-padding">
				<div class="col-md-3 low-padding">
					<label class="mgl10">Phòng thực hiện</label>
				</div>
				<div class="col-md-9 low-padding">
					<select class="form-control input-sm" id="cboPhongThucHien"
						style="width: 90%">
						<option value=""></option>
					</select>
				</div>
			</div>
			<div class="col-md-6 low-padding">
				<div hidden="true">
					<div class="col-md-1 low-padding">
						<label>Từ</label>
					</div>
					<div class="col-md-3 low-padding">
						<div class='input-group'>
							<input class="form-control input-sm" id="txtTuNgay"
								valrule="Từ ngày,required|datetime|max_length[10]" title=""
								data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
							<span
								class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
								onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
						</div>

					</div>
					<div class="col-md-1 low-padding"></div>
					<div class="col-md-1 low-padding">
						<label>Đến</label>
					</div>
					<div class="col-md-3 low-padding">
						<div class='input-group'>
							<input class="form-control input-sm" id="txtDenNgay"
								valrule="Đến ngày,required|datetime|max_length[10]" title=""
								data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
							<span
								class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
								onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-6 low-padding">
				<div class="col-md-2 low-padding">
					<button class="mgt-3 mgl-15 btn btn-sm btn-primary" id="btnXem">
						<span class="glyphicon glyphicon-search"></span> Xem
					</button>
				</div>
			</div>
		</div>
	</div>

	<div id="divBenhPhamDangLam">
		<div class="col-md-12 low-padding mgt-10 mgb-5">
			<table id="grdBenhPhamDangLam"></table>
			<div id="pager_grdBenhPhamDangLam"></div>
		</div>
	</div>
	<div class="row"></div>
</div>

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang = "vn";
	var user_group_id = '{user_group_id}';

	var session_par = [];
	session_par[1] = user_id;
	session_par[2] = schema;
	session_par[3] = province_id;
	session_par[0] = hospital_id;

	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;
	_opts.subdept_id = '{subdept_id}';

	_opts.dept_id = '{dept_id}';//nghiant 24102017 

	initRest(_opts._uuid);

	var ttbn = new CLS01X010_QMS_CDHA_GOI_BN(_opts);
	ttbn.load();

	var GBN = new JSF_GoiBenhNhan();
</script>