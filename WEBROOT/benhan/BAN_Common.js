// insert dien bien benh
function insDienbienbenh(hosobenhanid,benhnhanid,_gridDIENBIENBENH){ 
		var _return = 1;		
	    var rowIds = $('#'+_gridDIENBIENBENH).jqGrid('getDataIDs');
	    var strBanXml = "<hsba>";
	   // var checkHasDataHK=0;
	    for(var k = 0; k < rowIds.length; k++){
	    	var hs = new Object();
	    	var  rowData =  $('#'+_gridDIENBIENBENH).jqGrid('getRowData', rowIds[k]);	    	
	    	//strBanXml=createXmlDataDienBiencls(strBanXml,rowData.khambenhid,rowData.dienbien_cls2);
	    	strBanXml=createXmlDataDienBiencls(strBanXml,rowData.khambenhid,$("#txtdienbien_cls_"+rowData.khambenhid+"_"+rowData.phongid).val(),rowData.phongid);
	    }		
		strBanXml = strBanXml + "</hsba>";
		// insert du lieu thong tin hoi kham benh		
		var _par_DBLCS = [benhnhanid,hosobenhanid,strBanXml];
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DBBT",_par_DBLCS.join('$'));			
	  return _return;	
	}


//set gia tri vao cac control input khi khoi tao trang
	function setValueOnLoad(_sql, sql_par, loaihsba_code) {
		var formDetail = jsonrpc.AjaxJson.ajaxExecuteQuery(_sql, sql_par);
		if (formDetail != null && formDetail.length > 0) {
			// duyet va fill du lieu vao cac input hoi, kham benh va tong ket
			// benh an
			for (var k = 0; k < formDetail.length; k++) {
				var noidung = formDetail[k][0];
				var maTieuDe = formDetail[k][1];
				var kieuDuLieu = formDetail[k][2];
				// xac dinh id cua input tren form
				if (kieuDuLieu == '1' || kieuDuLieu == '4') {
					maTieuDe = 'txt'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '2') {
					maTieuDe = 'cld'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '5') {
					maTieuDe = 'chk'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '9') {
					maTieuDe = 'rad'
							+ maTieuDe.replace(loaihsba_code + "_", '');
            }else if (kieuDuLieu == '7') {
                maTieuDe = 'cbo'
                    + maTieuDe.replace(loaihsba_code + "_", '');
				}
				// set gia tri cho inputs tren form da duoc nhap du lieu lan
				// truoc
				if (kieuDuLieu != '5' && kieuDuLieu != '9') {
					$("#" + maTieuDe).val(FormUtil.unescape(noidung));
//					$("#" + maTieuDe).val(unescape(noidung));
				} else if (kieuDuLieu == '5') {
					$("#" + maTieuDe).prop('checked', (noidung == 'true'));
				} else if (kieuDuLieu == '9') {
					$('input[name='+ maTieuDe + '][value=' + noidung + ']').prop('checked', true);
				} 
			}
		}
	}

	var CAUHINH_BA_LOAD_MAU='';//L2PT-41824

	//L2PT-42752
	var CAUHINH_LOAD_MAU_THEO_TAB='';
	var current_tab = ''; // đang ở tab nào

	//set gia tri vao cac control input khi khoi tao trang benh an mau
	function setValueOnLoadTMP(_sql, sql_par, loaihsba_code) {
		var formDetail = jsonrpc.AjaxJson.ajaxExecuteQuery(_sql, sql_par);
		if (formDetail != null && formDetail.length > 0) {
			// duyet va fill du lieu vao cac input hoi, kham benh va tong ket
			// benh an
			for (var k = 0; k < formDetail.length; k++) {
				var noidung = formDetail[k][0];
				if (noidung == null || noidung == 'null'){
					noidung='';
				}
				var maTieuDe = formDetail[k][1];
				var kieuDuLieu = formDetail[k][2];
				// xac dinh id cua input tren form
				if (kieuDuLieu == '1' || kieuDuLieu == '4') {
					maTieuDe = 'txt'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '2') {
					maTieuDe = 'cld'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '5') {
					maTieuDe = 'chk'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				}else if (kieuDuLieu == '7') {
                maTieuDe = 'cbo'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				}

				//L2PT-42752
				var parent_tab = ''; // maTieuDe đang ở tab nào
				if (CAUHINH_LOAD_MAU_THEO_TAB == '1' 
					&& current_tab != '' 
					&& $('#'+current_tab).has('#'+maTieuDe).length == 0
				) 
					continue; // nếu có CH, đối tượng ko thuộc tab đang mở thì ko gán đè dl, bỏ qua chạy tiếp for.

				// set gia tri cho inputs tren form da duoc nhap du lieu lan
				// truoc
				if (kieuDuLieu != '5') {
					//L2PT-41824
					try{
						if (CAUHINH_BA_LOAD_MAU != '1' || (CAUHINH_BA_LOAD_MAU == '1' && $("#" + maTieuDe).val().toString().trim() == ''))
						{
							if (CAUHINH_LOAD_MAU_THEO_TAB == '1' && $("#" + maTieuDe).prop('disabled') == true){ //L2PT-Sy đang disabled thì ko cho load đè
							}
							else
								$("#" + maTieuDe).val(FormUtil.unescape(noidung));
						}
					}
					catch(ex){ }
				} else if (kieuDuLieu == '5') {					
					//L2PT-41824
					try{
						var  x=$("#" + maTieuDe).prop('checked');
						if (CAUHINH_BA_LOAD_MAU != '1' || (CAUHINH_BA_LOAD_MAU == '1' && x == false))
						{
							if (CAUHINH_LOAD_MAU_THEO_TAB == '1' && $("#" + maTieuDe).prop('disabled') == true){ //L2PT-Sy đang disabled thì ko cho load đè
							}
							else
								$("#" + maTieuDe).prop('checked', (noidung == 'true'));
						}
					}
					catch(ex){ }
				}
			}
		}
	}
	//set gia tri vao cac control input khi khoi tao trang benh an ngoai tru dai ngay
	function setValueOnLoadBANTDN(_sql, sql_par,_par, loaihsba_code) {
		var formDetail = jsonrpc.AjaxJson.ajaxExecuteQuery(_sql, sql_par);
		if (formDetail != null && formDetail.length > 0) {
			// duyet va fill du lieu vao cac input hoi, kham benh va tong ket
			// benh an
			for (var k = 0; k < formDetail.length; k++) {
				var noidung = formDetail[k][0];				
				var maTieuDe = formDetail[k][1];
				var kieuDuLieu = formDetail[k][2];
				//if ((noidung==null||noidung=='')&&){
				//	noidung='';
				//}
				// xac dinh id cua input tren form
				if (kieuDuLieu == '1' || kieuDuLieu == '4') {
					maTieuDe = 'txt'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '2') {
					maTieuDe = 'cld'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '5') {
					maTieuDe = 'chk'
							+ maTieuDe.replace(loaihsba_code + "_", '');
            }else if (kieuDuLieu == '7') {
                maTieuDe = 'cbo'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				}
				// set gia tri cho inputs tren form da duoc nhap du lieu lan
				// truoc
				if (kieuDuLieu != '5') {
					$("#" + maTieuDe).val(FormUtil.unescape(noidung));
//					$("#" + maTieuDe).val(unescape(noidung));
				} else if (kieuDuLieu == '5') {
					$("#" + maTieuDe).prop('checked', (noidung == 'true'));
				}
			}
		}
		else{					
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.ICDDN_THONGTIN",_par.join('$'));
			if (result != null && result.length > 0) {
				$("#txtQTBENHLY").val("Bệnh nhân đang theo dõi điều trị ngoại trú với chẩn đoán "+ result[0].HKB_QUATRINHBENHLY);
				$("#txtBANTHAN").val(result[0].KBH_TIENSUBENH_BANTHAN);		
				$("#txtXETNGHIEMCLS").val(result[0].TOMTATKQCLS);
				$("#txtMACHANDOANBANDAU").val(result[0].TK_MACHANDOANRAVIEN);
				$("#txtCHANDOANBANDAU").val(result[0].HKB_CHANDOANRAVIEN);
				$("#txtMACHANDOANBANDAU_KEMTHEO").val(result[0].HKB_MACHANDOANRAVIEN_KEMTHEO);
				$("#txtCHANDOANBANDAU_KEMTHEO").val(result[0].HKB_CHANDOANRAVIEN_KEMTHEO);
				$("#txtMABENHRAVIEN").val(result[0].TK_MACHANDOANRAVIEN);
				$("#txtTENBENHRAVIEN").val(result[0].TK_CHANDOANRAVIEN);
				$("#txtMABENHCHINH").val(result[0].TK_MACHANDOANRAVIEN);
				$("#txtBENHCHINH").val(result[0].TK_CHANDOANRAVIEN);
				$("#txtMABENHKEMTHEO").val(result[0].TK_MACHANDOANRAVIEN_KEMTHEO);
				$("#txtBENHKEMTHEO").val(result[0].TK_CHANDOANRAVIEN_KEMTHEO);
				$("#txtTUNGAYDTRI").val(result[0].NGAYXUTRI_DAU);	

				$("#txtMACH").val(result[0].KHAMBENH_MACH);
				$("#txtNHIETDO").val(result[0].KHAMBENH_NHIETDO);
	            /*$("#txtHUYETAP1").val(result[0].KHAMBENH_HUYETAP_LOW);
	            $("#txtHUYETAP2").val(result[0].KHAMBENH_HUYETAP_HIGH);*/
	            $("#txtHUYETAP1").val(result[0].KHAMBENH_HUYETAP_HIGH);
	            $("#txtHUYETAP2").val(result[0].KHAMBENH_HUYETAP_LOW);
				$("#txtNHIPTHO").val(result[0].KHAMBENH_NHIPTHO);
				$("#txtCANNANG").val(result[0].KHAMBENH_CANNANG);
				$("#txtTOANTHAN").val(result[0].KHAMBENH_TOANTHAN);
				$("#txtCACBOPHAN").val(result[0].KHAMBENH_BOPHAN); 
				$("#txtCHIEUCAO").val(result[0].KHAMBENH_CHIEUCAO);

				var ret = (result[0].KHAMBENHID_ICD_DN).split(';');
				var chandoan  = ret[1];
				if(typeof  chandoan != 'undefined'){
					var icd =  chandoan.split('-');
					$("#txtQTBENHLY").val("Bệnh nhân đang theo dõi điều trị ngoại trú với chẩn đoán "+chandoan);
					$("#txtMABANTHAN").val(icd[0]);
					$("#txtBANTHAN").val(icd[1]);
					$("#txtMABENHRAVIEN").val(icd[0]);
					$("#txtTENBENHRAVIEN").val(icd[1]);
					$("#txtMACHANDOANBANDAU").val(icd[0]);
					$("#txtCHANDOANBANDAU").val(icd[1]);
					$("#txtMABENHCHINH").val(icd[0]);
					$("#txtBENHCHINH").val(icd[1]);
				}else{
					for(var k = 0; k < result.length; k++ ){
						var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK.ICDDAINGAY", [{"name" : "[0]","value" : result[k].TK_MACHANDOANRAVIEN}]);
			     		var rows = JSON.parse(data);
			     		if (rows != null && rows.length > 0) {
			     			var check = rows[0]["IDC_DAINGAY"];
							if(check ==1){
								$("#txtQTBENHLY").val("Bệnh nhân đang theo dõi điều trị ngoại trú với chẩn đoán "+result[k].TK_CHANDOANRAVIEN);
								$("#txtMABANTHAN").val(result[k].TK_MACHANDOANRAVIEN);
								$("#txtBANTHAN").val(result[k].TK_CHANDOANRAVIEN);
								$("#txtMABENHRAVIEN").val(result[k].TK_MACHANDOANRAVIEN);
								$("#txtTENBENHRAVIEN").val(result[k].TK_CHANDOANRAVIEN);
								$("#txtMACHANDOANBANDAU").val(result[k].TK_MACHANDOANRAVIEN);
								$("#txtCHANDOANBANDAU").val(result[k].TK_CHANDOANRAVIEN);
								$("#txtMACHANDOANBANDAU_KEMTHEO").val(result[0].TK_MACHANDOANRAVIEN_KEMTHEO);
								$("#txtCHANDOANBANDAU_KEMTHEO").val(result[0].TK_CHANDOANRAVIEN_KEMTHEO);
								$("#txtMABENHCHINH").val(result[k].TK_MACHANDOANRAVIEN);
								$("#txtBENHCHINH").val(result[k].TK_CHANDOANRAVIEN);

								return;
							}
							else{
								var chkt = result[k].TK_CHANDOANRAVIEN_KEMTHEO;
								var res = chkt.split(';');
								for(var i =0; i< res.length; i ++){
									var icds = res[i].split('-');
									var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK.ICDDAINGAY", [{"name" : "[0]","value" : icds[0]}]);
						     		var rows = JSON.parse(data);
						     		if (rows != null && rows.length > 0) {
						     			var check = rows[0]["IDC_DAINGAY"];
										if(check ==1){
											$("#txtQTBENHLY").val("Bệnh nhân đang theo dõi điều trị ngoại trú với chẩn đoán "+icds[1]);
											$("#txtMABANTHAN").val(icds[0]);
											$("#txtBANTHAN").val(icds[1]);
											$("#txtMABENHRAVIEN").val(icds[0]);
											$("#txtTENBENHRAVIEN").val(icds[1]);
											$("#txtMACHANDOANBANDAU").val(icds[0]);
											$("#txtCHANDOANBANDAU").val(icds[1]);
											$("#txtMABENHCHINH").val(result[k].TK_MACHANDOANRAVIEN);
											$("#txtBENHCHINH").val(result[k].TK_CHANDOANRAVIEN);
											return;
										}
						     		}
								}
							}

						}
					}

				}

			}
		}		
	}

	// hàm xử lý fill dữ liệu mà trận khi có dữ liệu ở các cột text thì tự động check vào checkbox tương ứng
	function fillDataMatrix(){
		if( $("#txtDACDIEMLQB31").val() != null && $("#txtDACDIEMLQB31").val() != ""){
			$("#chkDACDIEMLQB21").prop( "checked", true );
		}
		else{
			$("#txtDACDIEMLQB31").prop('disabled', true);
		}
		if( $("#txtDACDIEMLQB61").val() != undefined && $("#txtDACDIEMLQB61").val() != ""){
			$("#chkDACDIEMLQB51").prop( "checked", true );
		}
		else{
			$("#txtDACDIEMLQB61").prop('disabled', true);
		}
		if( $("#txtDACDIEMLQB32").val() != undefined && $("#txtDACDIEMLQB32").val() != ""){
			$("#chkDACDIEMLQB22").prop( "checked", true );
		}
		else{
			$("#txtDACDIEMLQB32").prop('disabled', true);
		}
		if( $("#txtDACDIEMLQB62").val() !=undefined && $("#txtDACDIEMLQB62").val() != ""){
			$("#chkDACDIEMLQB52").prop( "checked", true );
		}
		else{
			$("#txtDACDIEMLQB62").prop('disabled', true);
		}
		if( $("#txtDACDIEMLQB33").val() != undefined && $("#txtDACDIEMLQB33").val() != ""){
			$("#chkDACDIEMLQB23").prop( "checked", true );
		}
		else{
			$("#txtDACDIEMLQB33").prop('disabled', true);
		}
		if( $("#txtDACDIEMLQB63").val() != undefined && $("#txtDACDIEMLQB63").val() != ""){
			$("#chkDACDIEMLQB53").prop( "checked", true );
		}
		else{
			$("#txtDACDIEMLQB63").prop('disabled', true);
		}
	}

	// ham xu ly close trang
	function closePage() {
		$("#btn_Close").on("click", function(e) {
			window.close();
		});
	}
	// goi lai trang sau khi update or save thanh cong
	function callBack(s_check, s_insert) {
		var message = "";
		if (s_insert == 1) {
			message = "Thêm mới";
		} else {
			message = "Cập nhật";
		}
		if (s_check == 0) {
			 DlgUtil.showMsg(message + " hồ sơ bệnh án không thành công");
			return;
		} else if (s_check == 1) {
			alert(message + " hồ sơ bệnh án thành công");
			location.reload();
		}
	}	

	function createXmlData(strXml, nodeName, nodeValue) {
		//xu ly neu la checkbox thi ko escape html
		if(nodeValue != true && nodeValue != false){
			nodeValue = FormUtil.escape(nodeValue);
//			nodeValue = escape(nodeValue);
		}
		strXml = strXml + "\n";
		strXml = strXml + "\t<row>\n";
		strXml = strXml + "\t<col_name>" + nodeName + "</col_name>\n";
		strXml = strXml + "\t<col_value>" + nodeValue + "</col_value>\n";
		strXml = strXml + "\t</row>";
		return strXml;
	}

	function createXmlDataDienBiencls(strXml, khambenhid, dienbien, phongid) {
		//xu ly neu la checkbox thi ko escape html
		if(dienbien != true && dienbien != false){
			dienbien = FormUtil.escape(dienbien);
//			nodeValue = escape(nodeValue);
		}
		strXml = strXml + "\n";
		strXml = strXml + "\t<row>\n";
		strXml = strXml + "\t<khambenhid>" + khambenhid + "</khambenhid>\n";
		strXml = strXml + "\t<dienbien_benhtinh>" + dienbien + "</dienbien_benhtinh>\n";
		strXml = strXml + "\t<phongid>" + phongid + "</phongid>\n";
		strXml = strXml + "\t</row>";
		return strXml;
	}

	//ham load ma icd10 cho cac mau benh an
	function doLoadComboYHCT(_txt, _txtDst) {
		var _sql = 'NT.008.YHCTV4';
		var  _col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
		var _selfnc=function(event, ui) {
			$("#"+_txt).val(ui.item.YHCTCODE);
			$("#"+_txtDst).val(ui.item.YHCTNAME);
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "BAN_LOADICD_YHCT") == 1) {
				$("#"+ _txt.replace("YHCT", 'YHHD')).val(ui.item.ICD10CODE);
				$("#"+_txtDst.replace("YHCT", 'YHHD')).val( ui.item.ICD10NAME);
			}
			return false;
		};
		ComboUtil.initComboGrid(_txt, _sql, [], "600px", _col, _selfnc);

	}
	function doLoadComboKemTheoYHCT(_txt, _txtDst,  _mode = 0) {
		var _sql = 'NT.008.YHCTV4';
		var  _col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
		var _selfnc = function (event, ui) {

			$("#"+ _txt).val(ui.item.YHCTCODE);
			$("#"+_txtDst).val($("#" + _txtDst).val() == '' ? ui.item.YHCTCODE + "-" + ui.item.YHCTNAME : $("#" + _txtDst).val() + ";" + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
			if (_mode == 0) {
				var str1 = $("#" + _txtDst.replace("YHCT", 'YHHD')).val();
				if(str1 != '')
					str1 += ";";
				$("#" + _txtDst.replace("YHCT", 'YHHD')).val(str1 + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			}
			return false;
		};
		ComboUtil.initComboGrid(_txt, _sql, [], "600px", _col, _selfnc);
	}
	function doLoadCombo(_txt,_txtDst, _yhct = 0){
		if (_yhct == 1) {
			var _sql = 'NT.008.YHCTV4';
			var  _col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
		}else {
			var _sql = 'CG.ICD10';
			var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		}
		var _selfnc=function(event, ui) {
			$("#"+_txt).val(ui.item.ICD10CODE);
			$("#"+_txtDst).val(ui.item.ICD10NAME);
			if (_yhct == 1) {
				$("#"+ _txt.replace("YHHD", 'YHCT')).val(ui.item.YHCTCODE);
				$("#"+_txtDst.replace("YHHD", 'YHCT')).val( ui.item.YHCTNAME );
			}
			return false;
		};
		ComboUtil.initComboGrid(_txt, _sql, [], "600px", _col, _selfnc);
	}
	function doLoadComboKemTheo(_txt,_txtDst, _yhct = 0){

		if (_yhct== 1) {
			var _sql = 'NT.008.YHCTV4';
			var _col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
		}else {
			var _sql = 'CG.ICD10';
			var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		}
			var _selfnc=function(event, ui) {

				$("#"+_txt).val(ui.item.ICD10CODE);
				$("#"+_txtDst).val($("#"+_txtDst).val() == '' ? ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME : $("#"+_txtDst).val() + ";" + ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME);
				if (_yhct == 1) {
				   var str1 = $("#" + _txtDst.replace("YHHD", 'YHCT')).val();
						if(str1 != '')
							str1 += ";";
						$("#" + _txtDst.replace("YHHD", 'YHCT')).val(str1 + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				 }
				return false;
			};
		ComboUtil.initComboGrid(_txt, _sql, [], "600px", _col, _selfnc);
	}


	function changePhimAnh(arrSoPhimAnh) {
		for (var j = 0; j < arrSoPhimAnh.length; j++) {
			$("#" + arrSoPhimAnh[j]).on("change", function(e) {
				var sumSoPA = 0;
				for (var l = 0; l < arrSoPhimAnh.length - 1; l++) {
					var checkPA = $("#" + arrSoPhimAnh[l]).val();
					if (checkPA != "" && checkPA != '0') {
						sumSoPA = sumSoPA + parseInt(checkPA)
					}
				}
				if(!isNaN(sumSoPA)){
					$("#txtSOTOANBOHOSO").val(sumSoPA);
				}
			})
		}
		$("#" + arrSoPhimAnh[0]).change();
	}

	function changeDataCheck() {
		$("#chkDACDIEMLQB21").on("change", function(e) {
			if($("#chkDACDIEMLQB21").is(":checked")){
				$("#txtDACDIEMLQB31").prop('disabled', false);
			}
			else{
				$("#txtDACDIEMLQB31").prop('disabled', true);
				$("#txtDACDIEMLQB31").val("");
			}
		});	
		$("#chkDACDIEMLQB22").on("change", function(e) {
			if($("#chkDACDIEMLQB22").is(":checked")){
				$("#txtDACDIEMLQB32").prop('disabled', false);
			}
			else{
				$("#txtDACDIEMLQB32").prop('disabled', true);
				$("#txtDACDIEMLQB32").val("");
			}
		});	
		$("#chkDACDIEMLQB23").on("change", function(e) {
			if($("#chkDACDIEMLQB23").is(":checked")){
				$("#txtDACDIEMLQB33").prop('disabled', false);
			}
			else{
				$("#txtDACDIEMLQB33").prop('disabled', true);
				$("#txtDACDIEMLQB33").val("");
			}
		});	
		$("#chkDACDIEMLQB51").on("change", function(e) {
			if($("#chkDACDIEMLQB51").is(":checked")){
				$("#txtDACDIEMLQB61").prop('disabled', false);
			}
			else{
				$("#txtDACDIEMLQB61").prop('disabled', true);
				$("#txtDACDIEMLQB61").val("");
			}
		});	
		$("#chkDACDIEMLQB52").on("change", function(e) {
			if($("#chkDACDIEMLQB52").is(":checked")){
				$("#txtDACDIEMLQB62").prop('disabled', false);
			}
			else{
				$("#txtDACDIEMLQB62").prop('disabled', true);
				$("#txtDACDIEMLQB62").val("");
			}
		});	
		$("#chkDACDIEMLQB53").on("change", function(e) {
			if($("#chkDACDIEMLQB53").is(":checked")){
				$("#txtDACDIEMLQB63").prop('disabled', false);
			}
			else{
				$("#txtDACDIEMLQB63").prop('disabled', true);
				$("#txtDACDIEMLQB63").val("");
			}
		});	
	}


	// lấy danh sách mã hỏi khám bệnh của khoa
function getMaHoiKham(loaibenhanid) {
		var sql_par = [];
		var arrMaHoiKham = [];
		sql_par.push({
			"name" : "[0]",
			"value" : loaibenhanid
		});
//		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.02", sql_par);
		var _disable = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "BAN_CT_CHAN_INPUT");
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.02.NEW", sql_par);
		// data=jsonrpc.AjaxJson.ajaxExecuteQueryO("select ma_tieude from
		// ban_bieumau where loaibenhanid = '[0]'",sql_par);
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				arrMaHoiKham.push(row["MA_TIEUDE"]);
				if(_disable == 1){
				var maTieuDe = row["MA_TIEUDE"];
				var kieuDuLieu = row["KIEU_DULIEU"];
				var tblName = row["TBL_NAME"];
				var loaihsba_code = row["MALOAIBENHAN"];
				if(tblName != ""){
				if (kieuDuLieu == '1' || kieuDuLieu == '4') {
					maTieuDe = 'txt'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '2') {
					maTieuDe = 'cld'
							+ maTieuDe.replace(loaihsba_code + "_", '');
				} else if (kieuDuLieu == '5') {
					maTieuDe = 'chk'
							+ maTieuDe.replace(loaihsba_code + "_", '');
                    }else if (kieuDuLieu == '7') {
                        maTieuDe = 'cbo'
                            + maTieuDe.replace(loaihsba_code + "_", '');
				}
				$("#" + maTieuDe).attr('disabled', 'disabled');
			}
			}
			}
		}
		return arrMaHoiKham;
	}

	// lấy danh sách mã hỏi khám bệnh của khoa
	function getMaHoiKhamMaTran(loaibenhanid) {
		var sql_par = []; 
		var arrMaHoiKhamTable = [];
		sql_par.push({
			"name" : "[0]",
			"value" : loaibenhanid
		});
		var _disable = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "BAN_CT_CHAN_INPUT");
//		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.03", sql_par);
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.03.NEW", sql_par);
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				arrMaHoiKhamTable.push(row["MA_BIEUMAU_MATRAN"]);
				if(_disable == 1){
					var maTieuDe = row["MA_BIEUMAU_MATRAN"];
					var kieuDuLieu = row["KIEU_DULIEU"];
					var tblName = row["TBL_NAME"];
					var loaihsba_code = row["MALOAIBENHAN"];
					if(tblName != ""){
					if (kieuDuLieu == '1' || kieuDuLieu == '4') {
						maTieuDe = 'txt'
								+ maTieuDe.replace(loaihsba_code + "_", '');
					} else if (kieuDuLieu == '2') {
						maTieuDe = 'cld'
								+ maTieuDe.replace(loaihsba_code + "_", '');
					} else if (kieuDuLieu == '5') {
						maTieuDe = 'chk'
								+ maTieuDe.replace(loaihsba_code + "_", '');
                    }else if (kieuDuLieu == '7') {
                        maTieuDe = 'cbo'
								+ maTieuDe.replace(loaihsba_code + "_", '');
					}

					if (kieuDuLieu != '5') {
						$("#" + maTieuDe).attr('disabled', 'disabled');
					} else if (kieuDuLieu == '5') {
						$("#" + maTieuDe).attr("disabled", true);
					}					
				}
				}
			}
		}
		return arrMaHoiKhamTable;
	}


	function loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid) {
		 //widget thong tin hanh chinh
		  $('#tabBenhNhanThongTin').ntu02d023_tthc({    	
				_khambenhid : khambenhid        	
	      });
		// xu ly su kien load trang khi moi vao
		// Check xem da co du lieu ho so benh an khong
		if(hosobenhanid == null || hosobenhanid==''){
			var sql_par = [];	
			sql_par.push({
				"name" : "[0]",
				"value" : benhnhanid
			}, {
				"name" : "[1]",
				"value" : loaibenhanid
			}, {
				"name" : "[2]",
				"value" : i_hid
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.01", sql_par);
			var rows = JSON.parse(data);
			if (rows != null && rows.length > 0) {
				hosobenhanid = rows[0]["HOSOBENHANID"];
			}	
		}
	    var loadBAtruoc =  jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BAN_CHECK_MOLAIBADTNT');
	    var _sql_par = RSUtil.buildParam("", [hosobenhanid]);
	    var _result = jsonrpc.AjaxJson.getOneValue('NTU01H002.L04', _sql_par);
	    var sql_par = [];
	    sql_par.push({"name": "[0]", value: khambenhid});
	    var _loaitnid = jsonrpc.AjaxJson.getOneValue('GET.LOAITNID', sql_par);

	    if ( loadBAtruoc == 1 && _result == 0 && _loaitnid == 3) {
	        var sql_par = [];
	        sql_par.push({"name": "[0]", value: hosobenhanid});
	        var hosobenhancha_id = jsonrpc.AjaxJson.getOneValue('CHECK_BADN', sql_par);
	        var sql_par1 = [];
	        sql_par1.push({"name": "[0]", value: hosobenhancha_id});
	        sql_par1.push({"name": "[1]", value: hosobenhanid});
	        var hosobenhantruoc_id = jsonrpc.AjaxJson.getOneValue('BAN_GETHSBAID_TRUOC', sql_par1);
	        hosobenhanid = hosobenhantruoc_id;
	    }
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'BAN_DONGBO_BKT_PDT') && _result == 0) {
			var sql_par = [hosobenhanid];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.BENHKT_PDT", sql_par.join('$'));
			if (result != null && result.length > 0) {
				$("#txtBENHKEMTHEO").val(result[0].TK_CHANDOANRAVIEN_KEMTHEO);
			}
		}
		if (hosobenhanid != null && hosobenhanid.length > 0) {			
//			$("#btnThemMoi").text("");	
//			$("#btnThemMoi").append("<span class='glyphicon glyphicon-pencil'></span> Cập nhật");	
			// get cac thong tin cap nhat tren db len giao dien
			// b1: Lay thong tin hoi kham benh va tong ket benh an
			sql_par = [];
			sql_par = [ {
				"name" : "[0]",
				"value" : hosobenhanid
			} ];
//			setValueOnLoad("BAN.04", sql_par);
			setValueOnLoad("BAN.04", sql_par, loaihsba_code);
			// b2: Lay thong tin hoi kham benh dang table(dang ma tran)
			setValueOnLoad("BAN.05", sql_par, loaihsba_code);
			// b3: Lay thong tin ho so phim anh
			setValueOnLoad("BAN.06", sql_par, loaihsba_code);
			fillDataMatrix();
		}		
	}	


	/**
	 * khoi tao du lieu khi chon mau
	 * @param benhnhanid
	 * @param loaibenhanid
	 * @param i_hid
	 * @param loaihsba_code
	 * @param hosobenhanid
	 * @param khambenhid
	 * @returns
	 */
	function loadDataTemp(benhnhanid, loaibenhanid, i_hid, loaihsba_code, mauid, khambenhid, hosobenhanid) {
		 //widget thong tin hanh chinh
		  $('#tabBenhNhanThongTin').ntu02d023_tthc({    	
				_khambenhid : khambenhid        	
	      });

			sql_par = [];
			sql_par = [ {
				"name" : "[0]",
				"value" : mauid
			} ];

			sql_par1=[];
			sql_par1 = [ {
				"name" : "[0]",
				"value" : mauid
			},
			{
				"name" : "[1]",
				"value" : loaibenhanid
			}];

	if (CAUHINH_BA_LOAD_MAU=='') CAUHINH_BA_LOAD_MAU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'CAUHINH_BA_LOAD_MAU'); //L2PT-41824

	//L2PT-42752
	if (CAUHINH_LOAD_MAU_THEO_TAB=='') CAUHINH_LOAD_MAU_THEO_TAB = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'CAUHINH_LOAD_MAU_THEO_TAB');
	// lấy tab hiện tại
	if ($("[href='#tabBenhAnHoiKham']").parent().hasClass('active'))
		current_tab = 'tabBenhAnHoiKham'; 
	else if ($("[href='#tabBenhAnTongKet']").parent().hasClass('active'))
		current_tab = 'tabBenhAnTongKet';  
	else if ($("[href='#tabThongTinNhapLieu']").parent().hasClass('active'))
		current_tab = 'tabThongTinNhapLieu';  
	else if ($("[href='#tabVo']").parent().hasClass('active'))
		current_tab = 'tabVo';  
	else if ($("[href='#tabChong']").parent().hasClass('active'))
		current_tab = 'tabChong';  


//			setValueOnLoad("BAN.04", sql_par);
			setValueOnLoadTMP("BAN.04_MAU", sql_par1, loaihsba_code);
			// b2: Lay thong tin hoi kham benh dang table(dang ma tran)
			setValueOnLoadTMP("BAN.05_MAU", sql_par, loaihsba_code);
			// b3: Lay thong tin ho so phim anh
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "CAUHINH_BA_DEM_CAC_DICHVU") != "1"){ //L2PT-41824 nếu tự động đếm các dv phim ảnh thì ko đc load từ mẫu ra.
				setValueOnLoadTMP("BAN.06_MAU", sql_par, loaihsba_code);
			}

			if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BAN_GHIDE_BENHANMAU') == '1') {
                loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid);
            }

			fillDataMatrix();			
	}
	//load du lieu benh an ngoai tru dai ngay
	function loadDataBANTDN(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid, trang_thai) {
		 //widget thong tin hanh chinh
		  $('#tabBenhNhanThongTin').ntu02d023_tthc({    	
				_khambenhid : khambenhid        	
	      });
		// xu ly su kien load trang khi moi vao
		// Check xem da co du lieu ho so benh an khong
		if(hosobenhanid == null || hosobenhanid==''){
			var sql_par = [];	
			sql_par.push({
				"name" : "[0]",
				"value" : benhnhanid
			}, {
				"name" : "[1]",
				"value" : loaibenhanid
			}, {
				"name" : "[2]",
				"value" : i_hid
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.01", sql_par);
			var rows = JSON.parse(data);
			if (rows != null && rows.length > 0) {
				hosobenhanid = rows[0]["HOSOBENHANID"];
			}	
		}

		if (hosobenhanid != null && hosobenhanid.length > 0) {			
//			$("#btnThemMoi").text("");	
//			$("#btnThemMoi").append("<span class='glyphicon glyphicon-pencil'></span> Cập nhật");	
			// get cac thong tin cap nhat tren db len giao dien
			// b1: Lay thong tin hoi kham benh va tong ket benh an
			var d = new Date();
			var n = d.getFullYear();
			sql_par = [];
			sql_par = [ {
				"name" : "[0]",
				"value" : hosobenhanid
			} ];
			_par=[ {
				"name" : "[0]",
				"value" : benhnhanid
			},
			{
				"name" : "[1]",
				"value" : n
			}];
			var par1=[benhnhanid,n];
//			setValueOnLoad("BAN.04", sql_par);
			setValueOnLoadBANTDN("BAN.04", sql_par,par1, loaihsba_code);		
			// b2: Lay thong tin hoi kham benh dang table(dang ma tran)
			setValueOnLoad("BAN.05", sql_par, loaihsba_code);
			// b3: Lay thong tin ho so phim anh
			setValueOnLoad("BAN.06", sql_par, loaihsba_code);
			fillDataMatrix();

			if(trang_thai == 9){
				$("#btnLuu").prop('disabled', true);
				$("#btnThemMoi").prop('disabled', true);
			}
		}		
	}	

	function loadDataBANTDN1(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid, phongKhamDkId) {
		//widget thong tin hanh chinh
		$('#tabBenhNhanThongTin').ntu02d023_tthc({
			_khambenhid: khambenhid
		});
		// xu ly su kien load trang khi moi vao
		// Check xem da co du lieu ho so benh an khong
		if (hosobenhanid == null || hosobenhanid == '') {
			var sql_par = [];
			sql_par.push({
				"name": "[0]",
				"value": benhnhanid
			}, {
				"name": "[1]",
				"value": loaibenhanid
			}, {
				"name": "[2]",
				"value": i_hid
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.01", sql_par);
			var rows = JSON.parse(data);
			if (rows != null && rows.length > 0) {
				hosobenhanid = rows[0]["HOSOBENHANID"];
			}
		}

		if (hosobenhanid != null && hosobenhanid.length > 0) {
			// get cac thong tin cap nhat tren db len giao dien
			// b1: Lay thong tin hoi kham benh va tong ket benh an
			var d = new Date();
			var n = d.getFullYear();
			var sql_par = [{
				"name": "[0]",
				"value": hosobenhanid
			}, {
				"name": "[1]",
				"value": phongKhamDkId
			}];
			var par1 = [benhnhanid, n];
			setValueOnLoadBANTDN("BAN.04_1", sql_par, par1, loaihsba_code);
			// b2: Lay thong tin hoi kham benh dang table(dang ma tran)
			setValueOnLoad("BAN.05", sql_par, loaihsba_code);
			// b3: Lay thong tin ho so phim anh
			setValueOnLoad("BAN.06", sql_par, loaihsba_code);
			fillDataMatrix();
		}
	}

	function insHSBenhAn(benhnhanid, loaibenhanid, khambenhid, i_hid, checkInsert){
		var _return = 0;
		var _par_ins_ban = [benhnhanid,loaibenhanid,khambenhid];
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.04",
				_par_ins_ban.join('$'));
		if (_return == 0) {
			// truong hop cap nhat or insert khong thanh // cong
			callBack(_return, checkInsert);
		}						

		// lay id cua ho so benh an sau khi insert
		var sql_par_insert = [];
		sql_par_insert.push({
			"name" : "[0]",
			"value" : benhnhanid
		}, {
			"name" : "[1]",
			"value" : loaibenhanid
		}, {
			"name" : "[2]",
			"value" : i_hid
		});
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO(
				"BAN.07", sql_par_insert);
		var rows = JSON.parse(data);
		var hosobenhanid = rows[0]["HOSOBENHANID"];

		return hosobenhanid;
	}

	/**
	 * Lưu mẫu hồ sơ bệnh án
	 * @param khoaid
	 * @param phongid
	 * @param tenmau
	 * @param loaibenhanid
	 * @returns: trả về id của mẫu
	 */
	function insHSBenhAnTemp(khoaid, phongid, tenmau, loaibenhanid){
		var _return = 0;
		var _par_ins_ban = [khoaid,phongid,tenmau,loaibenhanid];
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.04_MAU_1", _par_ins_ban.join('$'));	
		return _return;
	}

	/**
	 * Luu thong tin phim ảnh
	 * @param arrSoPhimAnh
	 * @param hosobenhanid
	 * @param checkInsert
	 * @returns
	 */
	function insHSPhimAnh(arrSoPhimAnh, hosobenhanid, checkInsert){
		var _return = 1;
		var strBanXml = "<hsba>";
		for (var m = 0; m < arrSoPhimAnh.length; m++) {
			var checkPA = $("#" + arrSoPhimAnh[m]).val();
			if (checkPA != "" && checkPA != '0') {
				strBanXml = createXmlData(strBanXml,
						arrSoPhimAnh[m].substr(3), checkPA);
			}
		}
		strBanXml = strBanXml + "</hsba>";
		var _par_phimanh = [ hosobenhanid, strBanXml,checkInsert ];	
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.01",_par_phimanh.join('$'));
		return _return;
	}

	/**
	 * In mau ho so phim anh
	 * @param arrSoPhimAnh
	 * @param hosobenhanid
	 * @param checkInsert
	 * @returns
	 */
	function insHSPhimAnhTemp(arrSoPhimAnh, mauid, checkInsert){
		var _return = 1;
		var strBanXml = "<hsba>";
		for (var m = 0; m < arrSoPhimAnh.length; m++) {
			var checkPA = $("#" + arrSoPhimAnh[m]).val();
			if (checkPA && checkPA != "" && checkPA != '0') {
				strBanXml = createXmlData(strBanXml,
						arrSoPhimAnh[m].substr(3), checkPA);
			}
		}
		strBanXml = strBanXml + "</hsba>";
		var _par_phimanh = [mauid, strBanXml,checkInsert ];	
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.01_MAU",_par_phimanh.join('$'));
		return _return;
	}


  function closePage(){
	  $("#btn_Close").on("click",function(e){			
		  parent.DlgUtil.close("divDlgBenhAnDetail");
		});  
   }	



	function insHoiKham(checkInput, loaihsba_code, arrMaHoiKham, hosobenhanid, checkInsert){
		var _return = 1;
		var strBanXml = "<hsba>";
		var checkHasDataHK = 0;
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'BAN_TONGKETBENHAN2') == 1 && 
			( loaihsba_code == 'MATDAYMAT' ||  loaihsba_code == 'MATLAC' || loaihsba_code == 'MATTREEM' || loaihsba_code ==	'CHANTHUONGMAT' || loaihsba_code ==	'MATBPT' )){
			for (var j = 0; j < checkInput.length; j++) {
				var name = checkInput[j].name;
				var val = checkInput[j].value.trim();

				if (name.slice(-1) == '1') {
					$('#' + name.substring(0, name.length - 1)).val(val.trim())
				}
			}
		}
		for (var i = 0; i < checkInput.length; i++) {
			var name = checkInput[i].name;
			var type = checkInput[i].type;
			var chk = checkInput[i].checked;
			var val = checkInput[i].value.trim();
			if (type == "checkbox") {
				val = checkInput[i].checked;
			}
			if (name != '' && name.length > 3) {
				var keyName = loaihsba_code + "_" + name.substr(3);
				// tao ma hoi kham benh						
				if (arrMaHoiKham.indexOf(keyName) > -1) {
					//check ma hoi kham benh co dung khong
					if (type == "checkbox") {
						if (val) {
							strBanXml = createXmlData(strBanXml,
									keyName, val);
							checkHasDataHK = 1;
						}
					} else if (type == "radio") {
						if (chk) {
							strBanXml = createXmlData(strBanXml,
									keyName, val);
							checkHasDataHK = 1;
						}
					}
					else {
						if (val != null && val.length > 0) {
							strBanXml = createXmlData(strBanXml,
									keyName, val);
							checkHasDataHK = 1;
						}
					}
				}
			}
		}
		strBanXml = strBanXml + "</hsba>";
		// insert du lieu thong tin hoi kham benh
		if (checkHasDataHK == 1) {
			var _par_HK = [hosobenhanid, strBanXml, checkInsert ];
		//	_par_HK = arrParamSys.concat(_par_HK)
			_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.02",_par_HK.join('$'));			
		}
		return _return;
	}

	function insHoiKham1(checkInput, loaihsba_code, arrMaHoiKham, hosobenhanid, checkInsert) {
		var _return = 1;
		var strBanXml = "<hsba>";
		var checkHasDataHK = 0;
		var phongKhamDKId = '';
		var khamBenhId = '';
		for (var i = 0; i < checkInput.length; i++) {
			var name = checkInput[i].name;
			var type = checkInput[i].type;
			var val = checkInput[i].value.trim();
			if (type == "checkbox") {
				val = checkInput[i].checked;
			}
			switch (name) {
				case 'hidKHAMBENHID':
					khamBenhId = val;
					break;
				case 'hidPHONGKHAMDANGKYID':
					phongKhamDKId = val;
					break;
				default:
					break;
			}
			if (name != '' && name.length > 3) {
				var keyName = loaihsba_code + "_" + name.substr(3);
				// tao ma hoi kham benh
				if (arrMaHoiKham.indexOf(keyName) > -1) {
					//check ma hoi kham benh co dung khong
					if (type == "checkbox") {
						if (val) {
							strBanXml = createXmlData(strBanXml,
								keyName, val);
							checkHasDataHK = 1;
						}
					} else {
						if (val != null && val.length > 0) {
							strBanXml = createXmlData(strBanXml,
								keyName, val);
							checkHasDataHK = 1;
						}
					}
				}
			}
		}
		strBanXml = strBanXml + "</hsba>";
		// insert du lieu thong tin hoi kham benh
		if (checkHasDataHK == 1) {
			var _par_HK = [hosobenhanid, strBanXml, phongKhamDKId, khamBenhId];
			//	_par_HK = arrParamSys.concat(_par_HK)
			_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.INS.HSBA", _par_HK.join('$'));
		}
		return _return;
	}

	/**
	 * Luu mau hoi kham benh
	 * @param checkInput
	 * @param loaihsba_code
	 * @param arrMaHoiKham
	 * @param hosobenhanid
	 * @param checkInsert
	 * @returns
	 */
	function insHoiKhamTemp(checkInput, loaihsba_code, arrMaHoiKham, hosobenhanid, checkInsert){
		var _return = 1;
		var strBanXml = "<hsba>";
		var checkHasDataHK = 0;
		for (var i = 0; i < checkInput.length; i++) {
			var name = checkInput[i].name;
			var type = checkInput[i].type;
			var val = checkInput[i].value.trim();
			if (type == "checkbox") {
				val = checkInput[i].checked;
			}
			if (name != '' && name.length > 3) {
				var keyName = loaihsba_code + "_" + name.substr(3);
				// tao ma hoi kham benh						
				if (arrMaHoiKham.indexOf(keyName) > -1) {
					//check ma hoi kham benh co dung khong
					if (type == "checkbox") {
						if (val) {
							strBanXml = createXmlData(strBanXml,
									keyName, val);
							checkHasDataHK = 1;
						}
					} else {
						if (val != null && val.length > 0) {
							strBanXml = createXmlData(strBanXml,
									keyName, val);
							checkHasDataHK = 1;
						}
					}
				}
			}
		}
		strBanXml = strBanXml + "</hsba>";
		// insert du lieu thong tin hoi kham benh
		if (checkHasDataHK == 1) {
			var _par_HK = [hosobenhanid, strBanXml, checkInsert ];
		//	_par_HK = arrParamSys.concat(_par_HK)
			_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.02_MAU",_par_HK.join('$'));			
		}
		return _return;
	}


	function insHoiKhamMaTran(checkInput, loaihsba_code, arrMaHoiKhamTable, hosobenhanid, checkInsert){
		var _return = 1;
		var strBanArrXml = "<hsba>";
		for (var i = 0; i < checkInput.length; i++) {
			var name = checkInput[i].name;
			var type = checkInput[i].type;
			var val = checkInput[i].value.trim();
			if (type == "checkbox") {
				val = checkInput[i].checked;
			}
			if (name != '' && name.length > 3) {
				var keyName = loaihsba_code + "_" + name.substr(3);	
				if (arrMaHoiKhamTable.indexOf(keyName) > -1) {
					//check ma hoi kham benh dang ma tran co dung khong
					if (type == "checkbox") {
						if (val) {
							strBanArrXml = createXmlData(
									strBanArrXml, keyName, val);
						}
					} else {
						if (val != null && val.length > 0) {
							strBanArrXml = createXmlData(
									strBanArrXml, keyName, val);
						}
					}
				}
			}
		}
		strBanArrXml = strBanArrXml + "</hsba>"; 
		// insert thong tin hoi kham benh dang matran (dang table)
		var _par_HKArr = [ hosobenhanid, strBanArrXml,checkInsert ];
		//	_par_HKArr = arrParamSys.concat(_par_HKArr);
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.03",_par_HKArr.join('$'));
		return _return;
	}

	/**
	 * IN HOI KHAM MA TRAN
	 * @param checkInput
	 * @param loaihsba_code
	 * @param arrMaHoiKhamTable
	 * @param hosobenhanid
	 * @param checkInsert
	 * @returns
	 */
	function insHoiKhamMaTranTemp(checkInput, loaihsba_code, arrMaHoiKhamTable, hosobenhanid, checkInsert){
		var _return = 1;
		var strBanArrXml = "<hsba>";
		for (var i = 0; i < checkInput.length; i++) {
			var name = checkInput[i].name;
			var type = checkInput[i].type;
			var val = checkInput[i].value.trim();
			if (type == "checkbox") {
				val = checkInput[i].checked;
			}
			if (name != '' && name.length > 3) {
				var keyName = loaihsba_code + "_" + name.substr(3);	
				if (arrMaHoiKhamTable.indexOf(keyName) > -1) {
					//check ma hoi kham benh dang ma tran co dung khong
					if (type == "checkbox") {
						if (val) {
							strBanArrXml = createXmlData(
									strBanArrXml, keyName, val);
						}
					} else {
						if (val != null && val.length > 0) {
							strBanArrXml = createXmlData(
									strBanArrXml, keyName, val);
						}
					}
				}
			}
		}
		strBanArrXml = strBanArrXml + "</hsba>"; 
		// insert thong tin hoi kham benh dang matran (dang table)
		var _par_HKArr = [ hosobenhanid, strBanArrXml,checkInsert ];
		//	_par_HKArr = arrParamSys.concat(_par_HKArr);
		_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.03_MAU",_par_HKArr.join('$'));
		return _return;
	}

	function deleteBenhAnTemp(mauid){
		var _par = [mauid];
		   var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("DEL_MAU_BA",
		     _par.join('$'));

		if (_return ==0) {
			// truong hop cap nhat or insert khong thanh 
			DlgUtil.showMsg("Bạn phải chọn tên mẫu bệnh án cần xóa");			
		}
		//else if(_return>0){
			//DlgUtil.showMsg("Xóa mẫu hồ sơ bệnh án thành công");
		//}		
		return _return;
	}
	// ham xu ly export ho so benh an
	function exportBan(i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_sch, i_file) {
		$("#btnInBieuMau").on("click", function(e) {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : i_hosobenhanid.toString()
			}, {
				name : 'loaibenhanid',
				type : 'String',
				value : i_loaibenhanid.toString()
			}, {
				name : 'benhnhanid',
				type : 'String',
				value : i_benhnhanid.toString()
			}, {
				name : 'i_sch',
				type : 'String',
				value : i_sch.toString()
			} ];
			if(i_loaibenhanid == 15 ){
				var param = [i_hosobenhanid];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("HSBA.LIETKEANH", i_hosobenhanid);
				var max= (data_ar.length>8?8:data_ar.length);
				for(var i=0;i<data_ar.length;i++){
					var hinh_anh1 = data_ar[i]["URL"];
					if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
					var ghi_chu1 = data_ar[i]["TEN"];
					if(ghi_chu1!="" && ghi_chu1!=null) par.push({name:'ghi_chu'+i,type:'String',value:ghi_chu1});
				}
			}
//			var typeExport = $("#sltInBieuMau").val();
	  		var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BAN_IN_DREPORT');
			if(_cauhinh == '1') {
					var params =[
				{
					name: 'hosobenhanid',
					type: 'String',
					value: i_hosobenhanid
				},
				{
					name: 'loaibenhanid',
					type: 'String',
					value: i_loaibenhanid
				},
				{
					name : 'benhnhanid',
					type : 'String',
					value : i_benhnhanid
				},
				{
					name: 'RPT_CODE',
					type: 'String',
					value: i_file
				}];
				CommonUtil.openReportEmr(params, false);
			}else{
				openReport('window', i_file, "pdf", par);
			}
		})
	}
	//ham xu ly export ban ngoai tru dai ngay
	// ham xu ly export ho so benh an
	function exportBanNTDN(i_hosobenhanid, i_loaibenhanid, i_benhnhanid,i_hid, i_sch, i_file, i_hosobenhanid_hientai) {
		$("#btnInBieuMau").on("click", function(e) {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : i_hosobenhanid.toString()
			}, {
				name : 'loaibenhanid',
				type : 'String',
				value : i_loaibenhanid.toString()
			}, {
				name : 'benhnhanid',
				type : 'String',
				value : i_benhnhanid.toString()
			},{
				name : 'i_hid',
				type : 'String',
				value : i_hid.toString()
			},{
				name : 'i_sch',
				type : 'String',
				value : i_sch.toString()
			}, {
                name : 'hosobenhanid_hientai',
                type : 'String',
                value : i_hosobenhanid_hientai.toString()
            } ];
//			var typeExport = $("#sltInBieuMau").val();
			openReport('window', i_file, "pdf", par);
		})
	}

	function checkIcdCode(icdCode){
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.08", [{"name" : "[0]","value" : icdCode}]);
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			return true;
		}
		return false;
	}

	function checkIcdName(icdName){
		var checkBC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','NGT_CHINHSUABENHCHINH');
		if(checkBC =='1' || checkBC =='2'){
			return true;
		}
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.09", [{"name" : "[0]","value" : icdName}]);
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			return true;
		}
		return false;
	}

	function onfocus(id){
		$(id).focus();
		$(id).css("background-color","yellow");
	}

	// Xử lý sự kiện liên quan ký CA => START
	function exportKyCa(i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_rptcode) {
		// Reset trạng thái nút lưu
		var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU');
		if(_cauhinh != '0' && typeof _cauhinh !== "undefined") {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnExportCa").show();
			var params =[
				{
					name: 'hosobenhanid',
					type: 'String',
					value: i_hosobenhanid
				},
				{
					name: 'loaibenhanid',
					type: 'String',
					value: i_loaibenhanid
				},
				{
					name : 'benhnhanid',
					type : 'String',
					value : i_benhnhanid
				},
				{
					name: 'RPT_CODE',
					type: 'String',
					value: i_rptcode
				}];
			var _check = CommonUtil.checkKyCaByParam(params);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('EMR.GET.CHKC', i_rptcode);
			if(data_ar != null && data_ar.length > 0) {
				if(_check >= data_ar[0].SOLUONGKY) {
					$("#btnKyCa").prop("disabled", true);
					$("#btnLuu").prop("disabled", true);
					$("#btnThemMoi").prop("disabled", true);
				}
			}
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_CHECKSLKY_DISABLECONTROL') == 1) {
				var _obj = new Object();
				_obj.HOSOBENHANID = i_hosobenhanid;
				_obj.RPT_CODE = i_rptcode;
				var check_kysoarr = jsonrpc.AjaxJson.ajaxCALL_SP_O('BAN_CHECKKYSO2', JSON.stringify(_obj));
				if (check_kysoarr.length > 0) {
					_changeControl("tabBenhAnHoiKham", false);
					_changeControl("tabBenhAnTongKet", false);
					for (var i = 0; i < check_kysoarr.length; i++) {
						if (check_kysoarr[i].THUTUVIEW == 1 && check_kysoarr[i].TRANGTHAI == 2) {
							_changeControl("tabBenhAnHoiKham", true);
						} else if (check_kysoarr[i].THUTUVIEW == 2 && check_kysoarr[i].TRANGTHAI == 2) {
							_changeControl("tabBenhAnTongKet", true);
						}
					}
				}
			}
		}
		$('a[href="#tabBenhAnHoiKham"]').on("click", function () {
			var _obj = new Object();
			_obj.HOSOBENHANID = i_hosobenhanid;
			_obj.RPT_CODE = i_rptcode;
			var check_kysoarr = jsonrpc.AjaxJson.ajaxCALL_SP_O('BAN_CHECKKYSO2', JSON.stringify(_obj));
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_CHECKSLKY_DISABLECONTROL') == 1) {
				if (check_kysoarr.length > 0) {
					_changeControl("tabBenhAnHoiKham", false);
					for (var i = 0; i < check_kysoarr.length; i++) {
						if (check_kysoarr[i].THUTUVIEW == 1 && check_kysoarr[i].TRANGTHAI == 2) {
							_changeControl("tabBenhAnHoiKham", true);
						}
					}
				}
			}
		});
		$('a[href="#tabBenhAnTongKet"]').on("click", function () {
			var _obj = new Object();
			_obj.HOSOBENHANID = i_hosobenhanid;
			_obj.RPT_CODE = i_rptcode;
			var check_kysoarr = jsonrpc.AjaxJson.ajaxCALL_SP_O('BAN_CHECKKYSO2', JSON.stringify(_obj));
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_CHECKSLKY_DISABLECONTROL') == 1) {
				if (check_kysoarr.length > 0) {
					_changeControl("tabBenhAnTongKet", false);
					for (var i = 0; i < check_kysoarr.length; i++) {
						if (check_kysoarr[i].THUTUVIEW == 2 && check_kysoarr[i].TRANGTHAI == 2) {
							_changeControl("tabBenhAnTongKet", true);
						}
					}
				}
			}
		});


		$("#btnKyCa").on("click", function () {
			var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'HIS_KYSOBA_CHECK_VALUE');
			if (_check == '1') {
				var obj = new Object();
				obj.HOSOBENHANID = i_hosobenhanid;
				obj.RPT_CODE = i_rptcode;
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("EMR.CHECK.KYBENHAN", JSON.stringify(obj));
				if (ret > '0') {
					DlgUtil.showMsg('Bệnh nhân chưa có xử trí không thể ký số !');
					return;
				}
			}
			$("#btnLuu").trigger("click");
			_caRpt('1', i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_rptcode);
		});

		$("#btnHuyCa").on("click", function () {
			_caRpt('2', i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_rptcode);
		});

		$("#btnExportCa").on("click", function () {
			_exportKyCA(i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_rptcode);
		});
	}
	function _changeControl(_tab,_type) {
		$("#"+_tab+" :input").prop("disabled", _type);
		$("#btnLuu").prop("disabled", _type);
		$("#btnKyCa").prop("disabled", _type);
		$("#btnLuu").prop("disabled", _type);
		$("#btnThemMoi").prop("disabled", _type);
	}
	function _caRpt(_signType, i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_rptcode) {
		var _disable_checkkyso = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'HIS_CHECKKYSO_DISABLECONTROL');
		var params =[
			{
				name: 'hosobenhanid',
				type: 'String',
				value: i_hosobenhanid
			},
			{
				name: 'loaibenhanid',
				type: 'String',
				value: i_loaibenhanid
			},
			{
				name : 'benhnhanid',
				type : 'String',
				value : i_benhnhanid
			},
			{
				name: 'RPT_CODE',
				type: 'String',
				value: i_rptcode
			}];

		CommonUtil.kyCA(params, _signType, true, true);
		EventUtil.setEvent("eventKyCA",function(e){
			var _code = e.res.split("|")[0];
			var _msg = e.res.split("|")[1];
			if(_code == '0') {
				var _check = CommonUtil.checkKyCaByParam(params);
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('EMR.GET.CHKC', i_rptcode);
				if(data_ar != null && data_ar.length > 0) {
					if(_check >= data_ar[0].SOLUONGKY) {
						$("#btnKyCa").prop("disabled", true);
						$("#btnLuu").prop("disabled", true);
						$("#btnThemMoi").prop("disabled", true);
					}
				}
				if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_CHECKSLKY_DISABLECONTROL') == 1) {
					var _obj = new Object();
					_obj.HOSOBENHANID = i_hosobenhanid;
					_obj.RPT_CODE = i_rptcode;
					var check_kysoarr = jsonrpc.AjaxJson.ajaxCALL_SP_O('BAN_CHECKKYSO2', JSON.stringify(_obj));
					if (check_kysoarr.length > 0) {
						_changeControl("tabBenhAnHoiKham", false);
						_changeControl("tabBenhAnTongKet", false);
						for (var i = 0; i < check_kysoarr.length; i++) {
							for (var i = 0; i < check_kysoarr.length; i++) {
								if (check_kysoarr[i].THUTUVIEW == 1 && check_kysoarr[i].TRANGTHAI == 2) {
									_changeControl("tabBenhAnHoiKham", true);
								} else if (check_kysoarr[i].THUTUVIEW == 2 && check_kysoarr[i].TRANGTHAI == 2) {
									_changeControl("tabBenhAnTongKet", true);
								}
							}
						}
					}
				}
			}
			if (_disable_checkkyso  == 1 ){
            $.bootstrapGrowl(_msg,{
                type: 'success',
                delay: 2000,
        		});
		    }else {
		        DlgUtil.showMsg(_msg);
		    }
		});

	}

	function _exportKyCA(i_hosobenhanid, i_loaibenhanid, i_benhnhanid, i_rptcode) {
		var _params = [{
				name: 'hosobenhanid',
				type: 'String',
				value: i_hosobenhanid
			},
			{
				name: 'loaibenhanid',
				type: 'String',
				value: i_loaibenhanid
			},
			{
				name : 'benhnhanid',
				type : 'String',
				value : i_benhnhanid
			},
			{
				name: 'RPT_CODE',
				type: 'String',
				value: i_rptcode
			}];
		CommonUtil.openReportGetCA2(_params, false);
	}
	// Xử lý sự kiện liên quan ký CA => END

	$(document).ready(function() {

		// Check to see if the window is top if not then display button
		$(window).scroll(function() {
			if ($(this).scrollTop() > 500) {
				$('.scrollToTop').fadeIn();
			} else {
				$('.scrollToTop').fadeOut();
			}
		});

		// Click event to scroll to top
		$('.scrollToTop').click(function() {
			$('html, body').animate({
				scrollTop : 0
			}, 800);
			return false;
		});


	//L2PT-42744
	try{ 
		if ($('#btnLuuBAMau').parent().parent().parent().hasClass('btn-fixed'))
			$('#btnLuuBAMau').parent().parent().parent().width($('#divMain-BA').width()-1);
		else if ($('#btnLuuBAMau').parent().parent().hasClass('btn-fixed'))
			$('#btnLuuBAMau').parent().parent().width($('#divMain-BA').width()-1);
	} catch(ex){} 

	//L2PT-40172 
	if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "CAUHINH_BA_DEM_CAC_DICHVU") == "1") {
        var arrSoPhimAnh = ["txtSOXQUANG", "txtSOSCANNER", "txtSOMRI", "txtSOSIEUAM", "txtSOXETNGHIEM", "txtSOKHAC", "txtSOTOANBOHOSO"];
        var arrMA_NHOM = ["XQ", "CT", "MRI", "SA", "XETNGHIEM"];

        var fl_ca_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D021.EV002", _opts.hosobenhanid);
        var tongphieu = 0;
        if (fl_ca_ar != null && fl_ca_ar.length > 0){
        	 tongphieu = fl_ca_ar[0].TONGPHIEU;
        }else{
        	 tongphieu = 0;
        }


        for (var j = 0; j < arrSoPhimAnh.length; j++)
            $("#" + arrSoPhimAnh[j]).attr('disabled', true);

        var objData = new Object({
            KEY: 'BA_DEM_CAC_DICHVU',
            HOSOBENHANID: _opts.hosobenhanid
        });
        var _danhsach = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU_FUNC_ACTION", [JSON.stringify(objData)].join('$'));

        var dem = 0;
        for (var k = 0; k < arrMA_NHOM.length; k++) {
            $("#" + arrSoPhimAnh[k]).val('');
            if (_danhsach && _danhsach.length > 0) {
                for (var i = 0; i < _danhsach.length; i++) {
                    if (_danhsach[i].MA_NHOM && _danhsach[i].MA_NHOM == arrMA_NHOM[k])
                        if (_danhsach[i].CHIDINH && _danhsach[i].CHIDINH != '' && _danhsach[i].CHIDINH > 0) {
                            $("#" + arrSoPhimAnh[k]).val(parseFloat(_danhsach[i].CHIDINH) + parseFloat(_danhsach[i].KETQUA));
                            dem = dem + parseFloat(_danhsach[i].CHIDINH) + parseFloat(_danhsach[i].KETQUA);
                        }

                }
            }
        }
 		$("#txtSOTOANBOHOSO").val(tongphieu);
        $("#txtSOKHAC").val(parseInt(tongphieu) - parseInt(dem));
    }
});	
