
function NGT06K003_UPDATE_TEN_TAYNGUYEN(opt) {		
	this.load=doLoad;
	var _opt = opt;
	function doLoad() {	
		_initControl();
		_bindEvent();
	}
	
	function _initControl(){		
		if(_opt.mabenhan){
			_timkiembn(_opt.mabenhan);
		}
	}
	
	function _bindEvent() {			
		$("#btnLuu").on("click",function(e){
			if(!_kiemtra()) return false;
			objData = new Object();
			FormUtil.setFormToObject("divUpd","",objData);
			
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT06.K003_UPD",JSON.stringify(objData));	
			DlgUtil.showMsg(ret);
		});	
		
		$("#txtMAHOSOBENHAN").keyup(function(e) { 
			if (event.keyCode === 13) {
				_timkiembn($("#txtMAHOSOBENHAN").val());
		    }			
		});
		
		$("#btnHuy").on("click",function(e){
			parent.DlgUtil.close("dlgUpdateTN");
		});	
	}
	
	function _kiemtra(){
		if($('#txtTENBENHNHAN').val().trim().length == 0){
			setErrValidate('txtTENBENHNHAN');
			DlgUtil.showMsg('Hãy nhập tên');
			return false;
		}
		
		if($('#txtDIACHI').val().trim().length == 0){
			setErrValidate('txtDIACHI');
			DlgUtil.showMsg('Hãy nhập địa chỉ');
			return false;
		}		
		return true;
	}
	
	function _timkiembn(mabenhan){
		var sql_par = [];
		sql_par.push({"name":"[0]","value" : mabenhan});
		data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT06.K003_DS", sql_par);
		var rows=$.parseJSON(data);
		if (rows != null && rows.length > 0) {
			var row=rows[0];
			FormUtil.setObjectToForm("divUpd","",row);
		}else{
			FormUtil.clearForm('divUpd');
		}
	}
}
