/*
 * Nhập kết quả chẩn đoán hình ảnh
 * 
 * VietDA	04/08/2016		tạo mới 
 * 
 */

function CapSTTThucHien(opt){

	this.load = doLoad;	
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		initControl();
		bindEvent();

		/* dữ liệu nhận từ form cha Danh sách CDHA */
		$("#hdfIDMauBenhPham").val(opt.maubenhphamid);
		
		console.log("id mau benh pham="+opt.maubenhphamid);
		$("#txtSTT").select();
	}
	
	function initControl(){
		loadTT();
	}	
	
	function bindEvent(){		
		$('#btnLuu').on('click', function () {	
			luuSoLuong();
		});		

		$('#btnClose').on('click', function () {	
			EventUtil.raiseEvent("CLS02C011_Thoat",{msg:'reloadGrid'});				
		});
		
		$("#txtSTT").on("change",function () {	
			luuSoLuong();
		});
	}
	
	function luuSoLuong(){			
		if($("#txtSTT").val()!=""){
			if(isNaN($("#txtSTT").val())) {
				DlgUtil.showMsg('Số thứ tự phải nhập kiểu số');				
				return;
			}
		}
		
		var param = $("#hdfIDMauBenhPham").val()+'$'+
					$("#txtSTT").val();
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C011.LUUSTT", param);
		
		if(rs == '1'){
			document.getElementById("lblThongBao").style.visibility = "hidden";
			
			DlgUtil.showMsg('Cấp số thứ tự thực hiện thành công',undefined,1000);
			EventUtil.raiseEvent("CLS02C011_Luu",{msg:'reloadGrid'});
		} else {
			DlgUtil.showMsg('Có lỗi xảy ra, không thể lưu dữ liệu',undefined,3000);
		}		
	}
	
	function loadTT(){
		var param = opt.maubenhphamid;
		var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C011.CAPSTT", param);
		
		if(data!=undefined && data!=null){
			row = data[0];
			if(//(row["SOTHUTU"]==null || row["SOTHUTU"]=="") && 
					row["SOTIEPTHEO"]!=""){
				$("#txtSTT").val(row["SOTIEPTHEO"]);
				document.getElementById("lblThongBao").style.visibility = "visible";
			}
//			else {
//				$("#txtSTT").val(row["SOTHUTU"]);
//				document.getElementById("lblThongBao").innerHTML= "<-- Số thứ tự đã cấp";
//				document.getElementById("lblThongBao").style.color = "black";
//				document.getElementById("lblThongBao").style.visibility = "visible";
//			}
		}
	}
}
