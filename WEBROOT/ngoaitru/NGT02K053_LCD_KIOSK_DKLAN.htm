<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>

<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>

<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT02K053_LCD_KIOSK_DKLAN.js?v=2112292"></script>

<style type="text/css">
    .table{
        font-weight: bold;
        font-family: Arial;
        color: #FFF;
        text-align: center;


    }
    .table tr td{
        background-color: #005AAB;
        padding-top:25px;
        padding-bottom:20px;
        font-size: 35px;
    }
    img {
        border-radius:50%;
        -moz-border-radius:50%;
        -webkit-border-radius:50%;
        padding: 5px;
    }
    p{
        font-family: Arial;
        font-weight: bold;
    }
    #lblTENBENHVIEN,#lblTENQUAY{
        text-transform: uppercase;
    }
</style>

<body style="background-color:#005AAB !important; ">
<div style="width: 100%">
    <div style="width: 100%;">
        <table border="1"class="table" style="width: 100%;border:#6BC0F4">
            <tr >
                <td style="font-weight: bold;font-size: 56px;color: #fff;text-align: center;font-family: Arial">
                    <span id="lblTENBENHVIEN">BỆNH VIỆN HỮU NGHỊ VIỆT ĐỨC </span>
                    </br>
                    <span id="lblTENQUAY">QUẦY 02</span>
                </td>

            </tr>

        </table>

    </div>
    <div style="width: 100%;">
        <table border="1" style="width: 100%;border:#6BC0F4" class="table" id="list">
        </table>
    </div>
</div>
</body>
<iframe id="main_frame" style="width:100%;height:100%;border:none;display:none"></iframe>

<script type="text/javascript">
    var userInfo=CommonUtil.decode('{userData}');
    var opt=[];
    var hospital_id = '{hospital_id}';

    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var uuid = '{uuid}';
    var phongid = '{subdept_id}';
    //var lang= "vi";
    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    session_par[4] = phongid;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");

    var _opts = new Object();

    _opts._param = session_par;
    _opts._uuid = uuid;

    var tt = new ThongBaoBenhNhan(_opts);
    tt.load();
</script>