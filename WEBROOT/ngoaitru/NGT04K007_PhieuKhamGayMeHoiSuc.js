/*
FeatureId:NGT02K002
Creator: Sonnt
CreateDate: 20/07/2016
Desc: <PERSON><PERSON><PERSON><PERSON> bệnh hỏi bệnh
*/
function NGT02K002_KhamBenhHoiBenh(_opt) {
    this.load = doLoad;
    this.opt = $.extend({}, _opt);
    var that = this;
    var _flagKyca = false;
    var _isEdit = false;
    var _gridDSCS = "grdDSGayme";
    var _khambenhid = '';
    var _khamgmid = '';
    var _fla_ca = '';
    var cf = new Object();
    var loadmau = '0' //L2PT-115503

    //end bvtm-7945
    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof this.opt.lang !== "undefined") ? this.opt.lang : "vn";
        $('#txtNGAYKHAM').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
        _khambenhid = that.opt.khambenhId;
        var _gridHeader = " ,ICON,30,0,ns,l;KHAMBENHID,KHAMBENHID,0,0,t,l;KHAMGAYMEID,KHAMGAYMEID,0,0,t,l;FLAG_CA,FLAG_CA,0,0,t,l;" +
            "Ngày tạo,NGAYGIO,110,0,f,l;Người tạo,HOTEN,180,0,f,l;Hướng xử trí,HUONGXUTRI,450,0,f,l";
        GridUtil.init(_gridDSCS, "100%", "210px", "DANH SÁCH PHIẾU KHÁM GÂY MÊ", false, _gridHeader, true, {
            rowNum: 10,
            rowList: [10, 20, 30]
        });

        $("#txtCHANDOANRAVIEN").prop('disabled', false);
        $("#txtCHANDOANRAVIEN_KEMTHEO").prop('disabled', false);
        $("#lblKHOEMANH").html('Khỏe mạnh');


        if (_opt.hospital_id == '10284') {
            var sql_par = [];
            sql_par.push({"name": "[0]", "value": this.opt.khambenhId});
            sql_par.push({"name": "[1]", "value": this.opt.phongId});
            var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K002.PHONGKHAM", sql_par);
            var rows = $.parseJSON(data);
            for (var i = 0; i < rows.length; i++) {
                $('#tabKBHB').append('<li role="presentation" id = "liKhamBenh"><a href="#pk' + rows[i].PHONGID + '" aria-controls="profile" role="tab" data-toggle="pill">' + rows[i].ORG_NAME + '</a></li>');
                $('#divKB').append('<div id="pk' + rows[i].PHONGID + '" class="tab">');

                $('#pk' + rows[i].PHONGID).ngt02k002_kbhb_phongkham({
                    _data: rows[i]
                });
                $('#divKB').append('</div>');
            }
        }

        var sql_par = [];
        ComboUtil.getComboTag("cboNGHENGHIEPID", "COM.NGHENGHIEP", sql_par, "", "", "sql", "", false);
        ComboUtil.getComboTag("cboDANTOCID", "COM.DANTOC", sql_par, "", "", "sql", "", false);
        ComboUtil.getComboTag("cboQUOCGIAID", "COM.NUOCSX", sql_par, "", "", "sql", "", false);
        
        if (_opt.hospital_id == '10284') ComboUtil.getComboTag("cboMAUID","NGT04K007.MAU", [], '-1' ,{value:'-1',text:'--Lựa chọn--'},"sql"); //L2PT-116009
		else ComboUtil.getComboTag("cboMAUID","LOAD.MAU.GMHS", [], '-1' ,{value:'-1',text:'--Lựa chọn--'},"sql");//L2PT-115503

        var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
        var _sql = "CG.ICD10";

        ComboUtil.initComboGrid("txtMACHANDOANRAVIEN", _sql, sql_par, "600px", _col, function (event, ui) {
            $("#hidICD_RAVIEN").val(ui.item.ICD10CODE);
            $("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
            $("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
            $("#txtGHICHU_BENHCHINH").focus();
            return false;
        });

        ComboUtil.initComboGrid("txtMACHANDOANRAVIEN_KEMTHEO", _sql, sql_par, "600px", _col, function (event, ui) {
            if (ui.item.ICD10CODE == $('#hidICD_RAVIEN').val() && $('#hidICD_RAVIEN').val() != "") {
                DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
                return false;
            }

            var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();

            if (str.indexOf(ui.item.ICD10CODE + '-') > -1) {
                DlgUtil.showMsg("Bệnh kèm theo đã được nhập.");
                return false;
            }

            var _par = [_opt.khambenhId, _opt.phongId, ui.item.ICD10CODE, "1"];
            var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.ICD.TR", _par.join('$'));
            if (resultCheck == '0') {
                DlgUtil.showMsg("Đã tồn tại mã bệnh kèm theo trùng với phòng khám khác");
                return false;
            }

            if (str != '')
                str += ";";
            $("#txtCHANDOANRAVIEN_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
            $("#txtMACHANDOANRAVIEN_KEMTHEO").val("");
            return false;
        });

        this.validator = new DataValidator("divMain");
        //tuyennx_add_start_20170724 check an hien nut sua ghi chu benh chinh
        var NGT_GHICHU_BENHCHINH = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'NGT_GHICHU_BENHCHINH');
        if (NGT_GHICHU_BENHCHINH == '1') {
            $("#divBc").removeClass("col-md-11");
            $("#divBc").addClass("col-md-8");
            $('#divSuaBc').css('display', '');
        }
        //tuyennx_add_end_20170724
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_SUDUNG_KYSO_KYDIENTU;HIS_KHAMGAYME_THEO_CSYT;HIS_KHAMGAYME_AUTO_KYCA;HIS_KHAMGAYME_PRINT_EMR;HIS_KHAMGAYME_SHOW_KT");
        if (data_ar != null && data_ar.length > 0) {
            cf = data_ar[0];
        }
        //KÝ SỐ
        if (cf.HIS_SUDUNG_KYSO_KYDIENTU != "1") {
            $('#' + 'btnKySo').remove();
            $('#' + 'btnHuyKy').remove();
            $('#' + 'btnInKySo').remove();
        }
        if (cf.HIS_KHAMGAYME_THEO_CSYT == '1') {
            $("#divBuaAn").hide();
            $("#divKham").show();
            $("#divRangGia").hide();
            $("#divRangGiaLVC").show();
        }
        if (cf.HIS_KHAMGAYME_AUTO_KYCA == '1') {
            _flagKyca = true;
        }

        if (this.opt.trangthaikhambenh == 9 && cf.HIS_KHAMGAYME_SHOW_KT == '0') {
            $('#btnThem').hide();
            $('#btnLuu').hide();
            $('#btnReset').hide();
            $('#btnXoa').hide();
            $('#btnKySo').hide();
            $('#btnHuyKy').hide();
        }

        loadKhamGayMe();
        loadData();
        bindEvent();
    }

    function loadKhamGayMe() {
        _khamgmid = '';
        var _sql_par = RSUtil.buildParam("", [_opt.khambenhId]);
        GridUtil.loadGridBySqlPage(_gridDSCS, "NTUD036.DSGM84", _sql_par);
    }

    function loadData() {
        var obj = new Object();
        obj.KHAMBENHID = _khambenhid;
        obj.KHAMGAYMEID = _khamgmid;
       
        //L2PT-115503
        if(loadmau == '1'){
        	 obj.KHAMBENHID = '-1';
        	 obj.KHAMGAYMEID = $("#cboMAUID").val();
        }
        
        var data_ar1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT04K007.LAYDL1", JSON.stringify(obj));
        if (data_ar1 != null && data_ar1.length > 0) {
            var row = data_ar1[0];
            //L2PT-115503
            if(loadmau != '1'){
            	FormUtil.setObjectToForm("dvTTHC", "", row);
            }
            
            FormUtil.setObjectToForm("divKB", "", row);
            $("input[name=TUANHOANID][value=" + row.TUANHOANID + "]").prop('checked', true);
            $("input[name=TIEUHOAID][value=" + row.TIEUHOAID + "]").prop('checked', true);
            $("input[name=HOHAPID][value=" + row.HOHAPID + "]").prop('checked', true);
            $("input[name=COTSONGID][value=" + row.COTSONGID + "]").prop('checked', true);
            $("input[name=THANKINHID][value=" + row.TOANTHANID + "]").prop('checked', true);
            $("input[name=TOANTHANID][value=" + row.TOANTHANID + "]").prop('checked', true);
            var $radios = $('input:radio[name=radRANGGIAID]');
            if (row.RANGGIAID == '1' ){
                $radios.filter('[value=1]').prop('checked', true);
            }else {
                $radios.filter('[value=0]').prop('checked', true);
            }
        }
        
        //L2PT-69383
        var sql_par1 = [];
		sql_par1.push({"name":"[0]", value:_opt.phongkhamdangkyid});
		var ghichu_khambenh = jsonrpc.AjaxJson.getOneValue('NGT04.GHICHUPK', sql_par1);
		if(ghichu_khambenh && $("#txtHUONGXUTRI").val().trim() == '' && ghichu_khambenh != 'null')
			$("#txtHUONGXUTRI").val(ghichu_khambenh);
		
        ComboUtil.getComboTag("cboPPVCAMDUKIEN", "PTTT.VOCAM", [], '', {
            value: '-1',
            text: '--Lựa chọn--'
        }, "sql", "", function () {
            if (data_ar1 != null && data_ar1.length > 0) {
                $("#cboPPVCAMDUKIEN").val(data_ar1[0].PPVCAMDUKIEN);
            }
        });

        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _opt.dichvukhambenhId});
        var data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.DICHVUKB.03", sql_par);
        var data2 = JSON.parse(data1);
        if (data2 != null && data2.length > 0) {
            var row1 = data2[0];
            var _madichvu = row1.MADICHVU;
            madichvu_caychi = row1.MADICHVU;
            var _tendichvu = row1.TENDICHVU;
            if ($("#txtHUONGXUTRI").val().trim() == '') {
                $("#txtHUONGXUTRI").val(_tendichvu);
            }

            if (_opt.formID == 'KB') {
                if ($("#txtCHANDOANRAVIEN").val().trim() == '') {
                    $("#txtCHANDOANRAVIEN").val(row1.CHANDOANVAOKHOA);
                }
                if ($("#txtCHANDOANRAVIEN_KEMTHEO").val().trim() == '') {
                    $("#txtCHANDOANRAVIEN_KEMTHEO").val(row1.CHANDOANVAOKHOAKEMTHEO);
                }
            } else {
                if ($("#txtCHANDOANRAVIEN").val().trim() == '') {
                    $("#txtCHANDOANRAVIEN").val(row1.KB_CHANDOANVAOKHOA);
                }
                if ($("#txtCHANDOANRAVIEN_KEMTHEO").val().trim() == '') {
                    $("#txtCHANDOANRAVIEN_KEMTHEO").val(row1.KB_CHANDOANVAOKHOAKEMTHEO);
                }
            }
        }

        if ($('#chkYKBS_KHAC').is(":checked")) {
            $("#divYKIENBSTEXT").show();
        } else {
            $("#divYKIENBSTEXT").hide();
        }
        if ($('#chkXN_KHAC').is(":checked")) {
            $("#divXETNGHIEMTEXT").show();
        } else {
            $("#divXETNGHIEMTEXT").hide();
        }

    }

    function bindEvent() {
        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {pattern: /\d/},
                '9': {pattern: /\d/, optional: true},
                '#': {pattern: /\d/, recursive: true},
                'A': {pattern: /[a-zA-Z0-9]/},
                'S': {pattern: /[a-zA-Z]/}
            }
        };


        $("#txtMACHANDOANRAVIEN").change(function () {
            if ($("#txtMACHANDOANRAVIEN").val() != "")
                $('#txtMACHANDOANRAVIEN_KEMTHEO').focus();
        });
        EventUtil.setEvent("assignSevice_resultTK", function (e) {
            if (e.mode == '0') {
                $('#' + e.ctrId).combogrid("setValue", e.text);
            } else if (e.mode == '1') {
                $('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
            }
            DlgUtil.close(e.popupId);
        });

        EventUtil.setEvent("chinhsua_benhphu", function (e) {
            $('#txtCHANDOANRAVIEN_KEMTHEO').val(e.benhphu);
            DlgUtil.close("dlgBPKT");
        });

        $("#btnHuy").on("click", function () {
            EventUtil.raiseEvent("exam_cancel");
        });

        $("#btnThem").on("click", function () {
            _isEdit = false;
            _capnhat(false, "btnLuu");
        });

        $("#btnLuu").on("click", function () {
            if (_khamgmid == '') {
                DlgUtil.showMsg('Chưa chọn phiếu khám để sửa!');
                return;
            } else if (_fla_ca == '1') {
                DlgUtil.showMsg('Phiếu đã ký số không được sửa!');
                return;
            } else {
                _isEdit = true;
                _capnhat(false, "btnLuu");
            }
        });

        $("#btnIn").on("click", function (e) {
            if(cf.HIS_KHAMGAYME_PRINT_EMR == '1') {
                _caRpt('3');
            } else {
                var par = [
                    {
                        name: 'khamgaymeid',
                        type: 'String',
                        value: _khamgmid
                    }, {
                        name: 'khambenhid',
                        type: 'String',
                        value: that.opt.khambenhId
                    }];
                openReport('window', "PHIEU_KHAMME_TRUOC_PTTT_A4", "pdf", par);
            }
        });

        $("#btnReset").on("click", function (e) {
            FormUtil.clearForm("divKB", "");
        });

        $("#btnBP").on("click", function (e) {
            $('#txtCHANDOANRAVIEN_KEMTHEO').val('');
            $('#txtMACHANDOANRAVIEN_KEMTHEO').val('');
            $('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val('');
        });
        
        //L2PT-115503
        $("#btnLuuMau").click(function(){
        	var objData = new Object();
            FormUtil.setFormToObject("divMain", "", objData);
            objData["KHAMBENHID"] = that.opt.khambenhId;
            objData["PHONGID"] = that.opt.phongId;
            objData["KHOAID"] = that.opt.khoaId;
            objData["TUANHOANID"] = $('input[name=TUANHOANID]:checked', '#radTUANHOANID').val();
            objData["TIEUHOAID"] = $('input[name=TIEUHOAID]:checked', '#radTIEUHOAID').val();
            objData["HOHAPID"] = $('input[name=HOHAPID]:checked', '#radHOHAPID').val();
            objData["COTSONGID"] = $('input[name=COTSONGID]:checked', '#radCOTSONGID').val();
            objData["THANKINHID"] = $('input[name=THANKINHID]:checked', '#radTHANKINHID').val();
            objData["TOANTHANID"] = $('input[name=TOANTHANID]:checked', '#radTOANTHANID').val();
            if ($('input:radio[name="radRANGGIAID"]').is(':checked')) {
                objData["RANGGIAID"] = $.find("[name='radRANGGIAID']:checked")[0].value;
            }
            objData["LUUMAU"] = '1';
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04K007.LUU", JSON.stringify(objData));
            if(parseInt(ret) > 0){
				DlgUtil.showMsg("Thêm mẫu thành công!");	
				if (_opt.hospital_id == '10284') ComboUtil.getComboTag("cboMAUID","NGT04K007.MAU", [], '-1' ,{value:'-1',text:'--Lựa chọn--'},"sql");//L2PT-116009
				else ComboUtil.getComboTag("cboMAUID","LOAD.MAU.GMHS", [], '-1' ,{value:'-1',text:'--Lựa chọn--'},"sql");
				
			}
			else 
				DlgUtil.showMsg("Thêm mẫu thất bại!");	
            
		});
		$("#btnXoaMau").click(function(){
			if($("#cboMAUID").val() == '-1')
				return DlgUtil.showMsg("Chưa chọn mẫu!");	
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04K007.XOA", $("#cboMAUID").val());
			if(fl==1){
				DlgUtil.showMsg("Xóa mẫu thành công !");
				if (_opt.hospital_id == '10284') ComboUtil.getComboTag("cboMAUID","NGT04K007.MAU", [], '-1' ,{value:'-1',text:'--Lựa chọn--'},"sql");//L2PT-116009
				else ComboUtil.getComboTag("cboMAUID","LOAD.MAU.GMHS", [], '-1' ,{value:'-1',text:'--Lựa chọn--'},"sql");
			}
			else 
				DlgUtil.showMsg("Xóa mẫu thất bại!");	
		});
		
		$("#cboMAUID").on("change",function(e){
         	loadmau = '1';
         	loadData();
         	loadmau = '0';
		});
		

        $("#btnXoa").on("click", function () {
            if (_khamgmid == '') {
                DlgUtil.showMsg('Chưa chọn phiếu khám để xóa!');
                return;
            } else if (_fla_ca == '1') {
                DlgUtil.showMsg('Phiếu đã ký số không được xóa!');
                return;
            } else {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04K007.XOA", _khamgmid);
                if (ret == 1) {
                    DlgUtil.showMsg('Xóa thành công!');
                    loadKhamGayMe();
                } else {
                    DlgUtil.showMsg('Xóa thất bại!');
                }
            }
        });

        $("#btnEDITBP").on("click", function (e) {
            var myVar = {
                benhphu: $('#txtCHANDOANRAVIEN_KEMTHEO').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
            DlgUtil.open("dlgBPKT");
        });

        $("#txtMACHANDOANRAVIEN").focusout(function () {
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: $("#txtMACHANDOANRAVIEN").val()
            });
            var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKICD", _sql_par);
            if (ret == '0')
                $("#txtCHANDOANRAVIEN").val("");
        });

        var chkTIENSUNOIKHOAKHAC = document.getElementById("chkNOIKHOAKHAC").checked;
        var chkNGOAIKHOACO = document.getElementById("chkNGOAIKHOACO").checked;
        var chkDIUNGCO = document.getElementById("chkDIUNGCO").checked;
        var chkTHUOCDANGDIEUTRICO = document.getElementById("chkTHUOCDANGDIEUTRICO").checked;
        var chkBENHLAYNHIEMCO = document.getElementById("chkBENHLAYNHIEMCO").checked;
        if (chkTIENSUNOIKHOAKHAC == true) {
            $("#txtTIENSUNOIKHOAKHAC").attr("disabled", false);
        } else {
            $("#txtTIENSUNOIKHOAKHAC").attr("disabled", "disabled");
            $("#txtTIENSUNOIKHOAKHAC").val('');
        }
        if (chkNGOAIKHOACO == true) {
            $("#txtTIENSUNGOAIKHOA").attr("disabled", false);
        } else {
            $("#txtTIENSUNGOAIKHOA").attr("disabled", "disabled");
            $("#txtTIENSUNGOAIKHOA").val('');
        }
        if (chkDIUNGCO == true) {
            $("#txtDIUNG").attr("disabled", false);
        } else {
            $("#txtDIUNG").attr("disabled", "disabled");
            $("#txtDIUNG").val('');
        }
        if (chkTHUOCDANGDIEUTRICO == true) {
            $("#txtTHUOCDANGDIEUTRI").attr("disabled", false);
        } else {
            $("#txtTHUOCDANGDIEUTRI").attr("disabled", "disabled");
            $("#txtTHUOCDANGDIEUTRI").val('');
        }

        if (chkBENHLAYNHIEMCO == true) {
            $("#txtBENHLAYNHIEM").attr("disabled", false);
        } else {
            $("#txtBENHLAYNHIEM").attr("disabled", "disabled");
            $("#txtBENHLAYNHIEM").val('');
        }

        $('#chkNOIKHOAKHAC').on('change', function (e) {
            if ($('#chkNOIKHOAKHAC').is(":checked")) {
                $("#txtTIENSUNOIKHOAKHAC").removeAttr("disabled", "disabled");

            } else {
                $("#txtTIENSUNOIKHOAKHAC").attr("disabled", "disabled");
                $("#txtTIENSUNOIKHOAKHAC").val("");
            }
        });

        $('#chkNGOAIKHOACO').on('change', function (e) {
            if ($('#chkNGOAIKHOACO').is(":checked")) {
                $("#txtTIENSUNGOAIKHOA").removeAttr("disabled", "disabled");

            } else {
                $("#txtTIENSUNGOAIKHOA").attr("disabled", "disabled");
                $("#txtTIENSUNGOAIKHOA").val("");
            }
        });
        $('#chkDIUNGCO').on('change', function (e) {
            if ($('#chkDIUNGCO').is(":checked")) {
                $("#txtDIUNG").removeAttr("disabled", "disabled");

            } else {
                $("#txtDIUNG").attr("disabled", "disabled");
                $("#txtDIUNG").val("");
            }
        });

        $('#chkTHUOCDANGDIEUTRICO').on('change', function (e) {
            if ($('#chkTHUOCDANGDIEUTRICO').is(":checked")) {
                $("#txtTHUOCDANGDIEUTRI").removeAttr("disabled", "disabled");

            } else {
                $("#txtTHUOCDANGDIEUTRI").attr("disabled", "disabled");
                $("#txtTHUOCDANGDIEUTRI").val("");
            }
        });

        $('#chkBENHLAYNHIEMCO').on('change', function (e) {
            if ($('#chkBENHLAYNHIEMCO').is(":checked")) {
                $("#txtBENHLAYNHIEM").removeAttr("disabled", "disabled");

            } else {
                $("#txtBENHLAYNHIEM").attr("disabled", "disabled");
                $("#txtBENHLAYNHIEM").val("");
            }
        });

        $('#radTUANHOANID input').on('change', function () {
            if ($('input[name=TUANHOANID]:checked', '#radTUANHOANID').val() == 1) {
                $("#txtTUANHOAN").removeAttr("disabled", "disabled");
            } else {
                $("#txtTUANHOAN").attr("disabled", "disabled");
                $("#txtTUANHOAN").val("");
            }
        });
        $('#radTIEUHOAID input').on('change', function () {
            if ($('input[name=TIEUHOAID]:checked', '#radTIEUHOAID').val() == 1) {
                $("#txtTIEUHOA").removeAttr("disabled", "disabled");
            } else {
                $("#txtTIEUHOA").attr("disabled", "disabled");
                $("#txtTIEUHOA").val("");
            }
        });
        $('#radHOHAPID input').on('change', function () {
            if ($('input[name=HOHAPID]:checked', '#radHOHAPID').val() == 1) {
                $("#txtHOHAP").removeAttr("disabled", "disabled");
            } else {
                $("#txtHOHAP").attr("disabled", "disabled");
                $("#txtHOHAP").val("");
            }
        });
        $('#radCOTSONGID input').on('change', function () {
            if ($('input[name=COTSONGID]:checked', '#radCOTSONGID').val() == 1) {
                $("#txtCOTSONG").removeAttr("disabled", "disabled");
            } else {
                $("#txtCOTSONG").attr("disabled", "disabled");
                $("#txtCOTSONG").val("");
            }
        });
        $('#radTHANKINHID input').on('change', function () {
            if ($('input[name=THANKINHID]:checked', '#radTHANKINHID').val() == 1) {
                $("#txtTHANKINH").removeAttr("disabled", "disabled");
            } else {
                $("#txtTHANKINH").attr("disabled", "disabled");
                $("#txtTHANKINH").val("");
            }
        });

        $('#radTOANTHANID input').on('change', function () {
            if ($('input[name=TOANTHANID]:checked', '#radTOANTHANID').val() == 1) {
                $("#txtTOAN_THAN").removeAttr("disabled", "disabled");
            } else {
                $("#txtTOAN_THAN").attr("disabled", "disabled");
                $("#txtTOAN_THAN").val("");
            }
        });

        if ($('input[name=TUANHOANID]:checked', '#radTUANHOANID').val() == 1) {
            $("#txtTUANHOAN").removeAttr("disabled", "disabled");
        } else {
            $("#txtTUANHOAN").attr("disabled", "disabled");
            $("#txtTUANHOAN").val("");
        }

        if ($('input[name=TIEUHOAID]:checked', '#radTIEUHOAID').val() == 1) {
            $("#txtTIEUHOA").removeAttr("disabled", "disabled");
        } else {
            $("#txtTIEUHOA").attr("disabled", "disabled");
            $("#txtTIEUHOA").val("");
        }
        if ($('input[name=HOHAPID]:checked', '#radHOHAPID').val() == 1) {
            $("#txtHOHAP").removeAttr("disabled", "disabled");
        } else {
            $("#txtHOHAP").attr("disabled", "disabled");
            $("#txtHOHAP").val("");
        }
        if ($('input[name=COTSONGID]:checked', '#radCOTSONGID').val() == 1) {
            $("#txtCOTSONG").removeAttr("disabled", "disabled");
        } else {
            $("#txtCOTSONG").attr("disabled", "disabled");
            $("#txtCOTSONG").val("");
        }
        if ($('input[name=THANKINHID]:checked', '#radTHANKINHID').val() == 1) {
            $("#txtTHANKINH").removeAttr("disabled", "disabled");
        } else {
            $("#txtTHANKINH").attr("disabled", "disabled");
            $("#txtTHANKINH").val("");
        }

        if ($('input[name=TOANTHANID]:checked', '#radTOANTHANID').val() == 1) {
            $("#txtTOAN_THAN").removeAttr("disabled", "disabled");
        } else {
            $("#txtTOAN_THAN").attr("disabled", "disabled");
            $("#txtTOAN_THAN").val("");
        }

        $("#btnKySo").on("click", function (e) {
            if (_khamgmid == '') {
                DlgUtil.showMsg('Chưa chọn phiếu khám để ký!');
                return;
            } else if (_fla_ca == '1') {
                DlgUtil.showMsg('Phiếu đã ký số!');
                return;
            } else {
                _flagKyca = true;
                _isEdit = true;
                _capnhat();
            }
        });
        $("#btnHuyKy").on("click", function (e) {
            if (_khamgmid == '') {
                DlgUtil.showMsg('Chưa chọn phiếu khám để hủy!');
                return;
            } else {
                _caRpt('2');
            }
        });
        $("#btnInKySo").on("click", function (e) {
            if (_khamgmid == '') {
                DlgUtil.showMsg('Chưa chọn phiếu khám để in!');
                return;
            } else {
                _caRpt('0');
            }
        });

        $('#chkXN_KHAC').on('change', function (e) {
            if ($('#chkXN_KHAC').is(":checked")) {
                $("#txtXETNGHIEM").prop("disabled", false);
                $("#chkXN_BINHTHUONG").prop("checked", false);
            } else {
                $("#txtXETNGHIEM").prop("disabled", true);
                $("#chkXN_BINHTHUONG").prop("checked", true);
            }
        });

        $('#chkXN_BINHTHUONG').on('change', function (e) {
            if ($('#chkXN_BINHTHUONG').is(":checked")) {
                $("#txtXETNGHIEM").val('');
                $("#txtXETNGHIEM").prop("disabled", true);
                $("#chkXN_KHAC").prop("checked", false);
            } else {
                $("#txtXETNGHIEM").prop("disabled", true);
                $("#chkXN_KHAC").prop("checked", true);
            }
        });

        $('#chkYKBS_KHAC').on('change', function (e) {
            if ($('#chkYKBS_KHAC').is(":checked")) {
                $("#txtYKIENCUABACSY").prop("disabled", false);
                $("#chkYKBS_DUDIEUKIEN").prop("checked", false);
            } else {
                $("#txtYKIENCUABACSY").prop("disabled", true);
                $("#chkYKBS_DUDIEUKIEN").prop("checked", true);
            }
        });

        $('#chkYKBS_DUDIEUKIEN').on('change', function (e) {
            if ($('#chkYKBS_DUDIEUKIEN').is(":checked")) {
                $("#txtYKIENCUABACSY").val('');
                $("#txtYKIENCUABACSY").prop("disabled", true);
                $("#chkYKBS_DUDIEUKIEN").prop("checked", false);
            } else {
                $("#txtYKIENCUABACSY").prop("disabled", true);
                $("#chkYKBS_DUDIEUKIEN").prop("checked", true);
            }
        });

        //ductx bvtm-7945
        GridUtil.setGridParam(_gridDSCS, {
            onSelectRow: function (id) {
                GridUtil.unmarkAll(_gridDSCS);
                GridUtil.markRow(_gridDSCS, id);
                if (id) {
                    var _row = $("#" + _gridDSCS).jqGrid('getRowData', id);
                    _khambenhid = _row.KHAMBENHID;
                    _khamgmid = _row.KHAMGAYMEID;
                    _fla_ca = _row.FLAG_CA;
                    loadData();
                }
            },
            gridComplete: function (id) {
                var ids = $("#" + _gridDSCS).getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#" + _gridDSCS).jqGrid('getRowData', id);
                    if (row.FLAG_CA && row.FLAG_CA == '1') {
                        var _icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
                        $("#" + _gridDSCS).jqGrid('setCell', id, 'ICON', _icon);

                    }
                }
            }
        });
        //end bvtm-7945
    }

    function _capnhat(close, eventsource) {

        var objData = new Object();
        FormUtil.setFormToObject("divMain", "", objData);
        if (_isEdit) {
            objData["KHAMGAYMEID"] = _khamgmid;
        } else {
            objData["KHAMGAYMEID"] = '';
        }
        objData["KHAMBENHID"] = that.opt.khambenhId;
        objData["PHONGID"] = that.opt.phongId;
        objData["KHOAID"] = that.opt.khoaId;
        objData["TUANHOANID"] = $('input[name=TUANHOANID]:checked', '#radTUANHOANID').val();
        objData["TIEUHOAID"] = $('input[name=TIEUHOAID]:checked', '#radTIEUHOAID').val();
        objData["HOHAPID"] = $('input[name=HOHAPID]:checked', '#radHOHAPID').val();
        objData["COTSONGID"] = $('input[name=COTSONGID]:checked', '#radCOTSONGID').val();
        objData["THANKINHID"] = $('input[name=THANKINHID]:checked', '#radTHANKINHID').val();
        objData["TOANTHANID"] = $('input[name=TOANTHANID]:checked', '#radTOANTHANID').val();
        if ($('input:radio[name="radRANGGIAID"]').is(':checked')) {
            objData["RANGGIAID"] = $.find("[name='radRANGGIAID']:checked")[0].value;
        }
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04K007.LUU", JSON.stringify(objData));
        if (ret == -1) {
            DlgUtil.showMsg("Thêm mới/cập thất bại!", function () {
                EventUtil.raiseEvent("exam_save", {khambenhid: that.opt.khambenhId});
            });
        } else {
            _isEdit = false;
            if (_flagKyca) {
                _caRpt('1');
            } else {
                loadKhamGayMe();
                DlgUtil.showMsg("Thêm mới/cập nhật thành công!", function () {
                    if (close) {
                        EventUtil.raiseEvent("exam_cancel");
                    }
                    EventUtil.raiseEvent("exam_save", {khambenhid: that.opt.khambenhId});
                });
                $("#txtMACHANDOANRAVIEN").focus();
            }
        }
    }

    $('#txtKHAMBENH_CANNANG').keypress(function (e) {
        if (e.keyCode == '44' || e.charCode == '44') {
            if (document.selection) {
                var range = document.selection.createRange();
                range.text = '.';
            } else if (this.selectionStart || this.selectionStart == '0') {
                var start = this.selectionStart;
                var end = this.selectionEnd;
                $(this).val($(this).val().substring(0, start) + '.' + $(this).val().substring(end, $(this).val().length));
                this.selectionStart = start + 1;
                this.selectionEnd = start + 1;
            } else {
                $(this).val($(this).val() + '.');
            }
            return false;
        }
    });

    function _caRpt(signType) {
        var par_rpt_KySo = [{
            name: 'hosobenhanid',
            type: 'String',
            value: that.opt.hosobenhanId
        }, {
            name: 'khamgaymeid',
            type: 'String',
            value: _khamgmid
        }, {
            name: 'khambenhid',
            type: 'String',
            value: that.opt.khambenhId
        }, {
            name: 'RPT_CODE',
            type: 'String',
            value: 'PHIEU_KHAMME_TRUOC_PTTT_A4'
        }];
        if (signType == '0') {
            CommonUtil.openReportGetCA2(par_rpt_KySo, false);
        } else if (signType == '3') {
            CommonUtil.openReportEmr(par_rpt_KySo, false);
        } else {
            CommonUtil.kyCA(par_rpt_KySo, signType, true);
            EventUtil.setEvent("eventKyCA", function(e) {
                DlgUtil.showMsg(e.res);
                loadKhamGayMe();
            });
            if(signType == '1') {
                var obj = new Object();
                obj.KHAMBENHID = _khambenhid;
                obj.PHONGKHAMDANGKYID = that.opt.phongkhamdangkyid;
                var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('NGT04K007.KETTHUC', JSON.stringify(obj));
                if (fl == 1) {
                    parent.EventUtil.raiseEvent("ketthuckham_success");
                }
            }
        }
    }
}

function checkCanNang(evt, ele) {
    var theEvent = evt || window.event;
    var key = theEvent.keyCode || theEvent.which;
    key = String.fromCharCode(key);
    var value = ele.value + key;
    var regex = /^\d+(.\d{0,2})?$/;
    if (!regex.test(value)) {
        theEvent.returnValue = false;
        if (theEvent.preventDefault) theEvent.preventDefault();
    }
}