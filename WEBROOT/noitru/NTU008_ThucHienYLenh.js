/*
Mã màn hình  : NTU01H001
File mã nguồn : NTU01H001_PhongHanhChinh.js
<PERSON><PERSON><PERSON> đích  : <PERSON><PERSON> lý nghiệp vụ hành chính của nội trú 
Tham số vào :
<PERSON><PERSON><PERSON><PERSON> lập trình	<PERSON> cập nhật  <PERSON> chú
HUNGNT	- 03092016 - Comment
*/


function daybenhan(opt) {
    var _width = $(document).width() - 250;
    var _height = $(window).height() - 100;
    var markData = {};
    var tthc = {};
    var anhCKNV = null;
    var thongTinPDT; //L2PT-56962
    var caType = 0; // 1 or 0
    var lstdvkbids = "";

    var _gridId = "grdDanhSachYLenh";
    this.load = doLoad;
    var _gridHeader =
        "KHAMBENHID,KHAMBENHID,1,0,t,l;" +
        "LOAITH,LOAITH,1,0,t,l;" +
        "DICHVUKHAMBENHID,DICHVUKHAMBENHID,1,0,t,l;" +
        "BUCKET,BUCKET,1,0,t,l;" +
        "FILE_NAME,FILE_NAME,1,0,t,l;" +
        "MEDIA_ID,MEDIA_ID,1,0,t,l;" +
        "URL_IMG,URL_IMG,1,0,t,l;" +
        "BUCKET_DD,BUCKET_DD,1,0,t,l;" +
        "FILE_NAME_DD,FILE_NAME_DD,1,0,t,l;" +
        "MEDIA_ID_DD,MEDIA_ID_DD,1,0,t,l;" +
        "CKDVID_DAKY,CKDVID_DAKY,1,0,t,l;" +
        "URL_IMG_DD,URL_IMG_DD,1,0,t,l;" +
        "CACHDUNG,CACHDUNG,1,0,t,l;" +
        "Tên thuốc/ vật tư/ Dịch vụ kỹ thuật,TENDICHVU,150,0,f,l;" +
        "Cách dùng,CACHDUNG,50,0,f,l;" +
        "Thực hiện,THUCHIEN,50,0,f,l,ES;" +
        "Đơn vị,DONVI,30,0,f,c;" +
        "Trạng thái,TRANGTHAI,40,0,t,l;" +
        "Số lượng,SOLUONG,40,0,f,c;" +
        "Ghi chú,GHICHU,50,0,f,l,ES";

    var signMethod = 1;
    var signStep = 2;

    function doLoad() {
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        // HIS_PATIENT_SIGN_METHOD
        let hisPatientSignMethod = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','HIS_PATIENT_SIGN_METHOD');
        caType = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','HIS_DIEUDUONGDB_CATYPE');
        if (hisPatientSignMethod) {
            let hisPatientSignMethodSp = hisPatientSignMethod.split(",");
            signMethod = hisPatientSignMethodSp[0] ? hisPatientSignMethodSp[0] : "1";
            signStep = hisPatientSignMethodSp[1] ? hisPatientSignMethodSp[1] : "2";
            $("#btnXACNHANDIEUDUONG").toggle(signStep == "2");
        } else {
            $("#btnXACNHANDIEUDUONG").show();
        }
      //L2PT-56962 start
        thongTinPDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','HIS_DIEUDUONGDB_PDT');
        if (thongTinPDT == '1') {
        	$("#divTT_PDT").show();
        }
      //L2PT-56962 end
        $("#cboDUONGDUNG").select2();
        $("#txtNGAYYLENH").val(moment().format('DD/MM/YYYY'));
        GridUtil.init(_gridId, "100%", "450px", "", true, _gridHeader, true, { rowNum: 200, rowList: [200, 400, 600] });
        resize();
        tthc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU008.GET.TTBN", JSON.stringify({
            TIEPNHANID: opt._tiepnhanid,
            KHAMBENHID: opt._khambenhid
        }));
        if (tthc && tthc.length > 0) {
            tthc = tthc[0];
            $("#lblHoTen").html(`Họ và tên: ${tthc.TENBENHNHAN} - ${tthc.GIOITINH}`);
            $("#lblNgaySinh").html(`Ngày sinh: ${tthc.NGAYSINH}`);
            $("#lblChanDoan").html(`<strong>Chẩn đoán: </strong>${tthc.CHANDOANVAOKHOA}; ${tthc.CHANDOANVAOKHOA_KEMTHEO}`)
            $("#lblChanDoan").attr("title", `${tthc.CHANDOANVAOKHOA}; ${tthc.CHANDOANVAOKHOA_KEMTHEO}`);
            //L2PT-56962 start
            if (thongTinPDT == '1') {
            	$("#lblThongTinPDT").html(`<strong>Thông tin phiếu điều trị: Xử lý (Theo dõi): </strong>${tthc.XULYTHEODOI}; <strong>Chế độ dinh dưỡng: </strong>${tthc.CHEDODD}; <strong>Chế độ chăm sóc: </strong>${tthc.CHEDOCS}`);
            }
            //L2PT-56962 end
        } else {
            DlgUtil.showMsg('Không lấy được thông tin bệnh nhân, vui lòng thực hiện lại!');
            return;
        }

        anhCKNV = jsonrpc.AjaxJson.ajaxCALL_SP_C("NTU008.GET.CKNV",user_id+"$");

        if (anhCKNV) {
            $("#imageCKNV")[0].src = anhCKNV;
        }

        ComboUtil.getComboTag("cboDUONGDUNG", "NTU008.DUONGDUNG", [], "", {
            value : '-1',
            text : '-- Chọn --'
        }, "sql", undefined, function () {
            fillValueDisplay('cboDUONGDUNG','txtDUONGDUNG');
            loadGrid();
        });

        $("#cboDUONGDUNG").change(function () {
            loadGrid();
        });

        $("#txtNGAYYLENH").change(function () {
            loadGrid();
        });

        $('input[name="radLOAIYLENH"]').change(function () {
            loadGrid();
        });

        $("#btnBENHNHANKY, #btnBENHNHANKY2").click(function () {
            let _selRows = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
            let _dstdk = "";
            lstdvkbids = _selRows
                .filter(function(el){
                    let ckdvid = $("#" + _gridId).jqGrid('getCell', el, 'CKDVID_DAKY');
                    let isck = (ckdvid && parseInt(ckdvid) > 0);
                    if (isck) {
                        let _tdk = $("#" + _gridId).jqGrid('getCell', el, 'TENDICHVU');
                        _dstdk = `${_dstdk}, ${_tdk}`
                    }
                    return !isck;
                })
                .map(function(el){return $("#" + _gridId).jqGrid('getCell', el, 'DICHVUKHAMBENHID');})
                .join(",");
            _dstdk = _dstdk.replace(/, /, '');
            if (lstdvkbids.length < 1 && caType == 1) {
                let _msg = 'Vui lòng chọn thuốc/dịch vụ trước khi công khai.';
                if (_dstdk) {
                    _msg = 'Danh sách bạn chọn đã được công khai và ký: ' + _dstdk;
                }
                DlgUtil.showMsg(_msg);
                return;
            }
            if (_dstdk) {
                DlgUtil.showConfirm("Những dịch vụ sau đã được ký, sẽ bỏ qua cho lần ký này? " + _dstdk, function(flag){
                    if(flag) {
                        $("#modalNote").modal('hide');
                        if (signStep == "1") {
                            if (anhCKNV) {
                                $("#modalXNDD").modal("show");
                            } else {
                                DlgUtil.showMsg('Chưa có thông tin ảnh chữ ký.</br> Vui lòng cập nhật ảnh chữ ký tại chức năng Quản lý nhân viên.');
                            }
                        } else if (signStep == "2") {
                            if (signMethod == "2") {
                                showSignFinger();
                            } else {
                                showSignPad();
                            }
                        }
                    }
                });
            } else {
                $("#modalNote").modal('hide');
                if (signStep == "1") {
                    if (anhCKNV) {
                        $("#modalXNDD").modal("show");
                    } else {
                        DlgUtil.showMsg('Chưa có thông tin ảnh chữ ký.</br> Vui lòng cập nhật ảnh chữ ký tại chức năng Quản lý nhân viên.');
                    }
                } else if (signStep == "2") {
                    if (signMethod == "2") {
                        showSignFinger();
                    } else {
                        showSignPad();
                    }
                }
            }
        });

        $("#btnXACNHANDIEUDUONG").click(function (){
            if (anhCKNV) {
                $("#modalXNDD").modal("show");
            } else {
                DlgUtil.showMsg('Chưa có thông tin ảnh chữ ký.</br> Vui lòng cập nhật ảnh chữ ký tại chức năng Quản lý nhân viên.');
            }
        });

        $("#btnXNDD_OK").click(function (){
            if (anhCKNV) {
                if (signStep == "1") {
                    $("#modalXNDD").modal("hide");
                    if (signMethod == "2") {
                        showSignFinger();
                    } else {
                        showSignPad();
                    }
                } else if (signStep == "2") {
                    showLoading();
                    let file = FileUtil.dataURLtoFile(anhCKNV,`signpad_dd_${opt._khambenhid}_${Date.now()}.png`);
                    UploadUtil.uploadIDGPromises(file, false)
                        .then(
                            function (result) {
                                let objectData = new Object();
                                objectData.KHAMBENHID = opt._khambenhid;
                                objectData.KHOAID = tthc.KHOAID;
                                objectData.NGAYKY = $("#txtNGAYYLENH").val();
                                objectData.BUCKET_DD = result.bucket;
                                objectData.FILE_NAME_DD = result.file_name;
                                objectData.MEDIA_ID_DD = result.media_id;
                                objectData.URL_IMG_DD = result.url_img;
                                let fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU008.KYXACNHANDD", JSON.stringify(objectData));
                                if (fl == 1) {
                                    hideLoading();
                                    DlgUtil.showMsg('Xác nhận thành công!');
                                    $("#modalXNDD").modal("hide");
                                    loadGrid();
                                } else if (fl == 2) {
                                    hideLoading();
                                    DlgUtil.showMsg('Chưa có dữ liệu bệnh nhân ký!');
                                } else {
                                    DlgUtil.showMsg('Không thành công, vui lòng thực hiện lại!');
                                    hideLoading();
                                }
                            },
                            function (error) {
                                hideLoading();
                                alert(error);
                            })
                        .catch(reason => {hideLoading();alert(JSON.stringify(reason))});
                }
            } else {
                DlgUtil.showMsg('Chưa có thông tin ảnh chữ ký. Vui lòng cập nhật ảnh chữ ký tại chức năng Quản lý nhân viên.');
            }
        });

        $("#btnXNDD_CANCEL").click(function (){
            $("#modalXNDD").modal("hide");
        });

        $("#btnTHUCHIEN, #btnTHUCHIEN2").click(function () {
            if (markData) {
                $("#modalNote").modal('hide');
                $("#modalTHT").modal("show");
            } else {
                DlgUtil.showMsg('Vui lòng chọn dịch vụ trước khi thực hiện.');
            }
        });

        $("#btnHOANTHANH, #btnHOANTHANH2").click(function () {
            var selRows = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
            if (selRows.length === 1) {
                DlgUtil.showMsg('Vui lòng chọn thuốc/dịch vụ trước khi hoàn thành.');
                return;
            }
            $("#modalNote").modal('hide');
            showLoading();
            var objData = {};
            objData.KHAMBENHID = opt._khambenhid;
            objData.LISTDVKBID = selRows.map(function(el){return $("#" + _gridId).jqGrid('getCell', el, 'DICHVUKHAMBENHID');}).join(",");
            objData.TYPE = "2";
            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU008.THYLENH", JSON.stringify(objData));
            if (fl >= 1) {
                setTimeout(function () {
                    hideLoading();
                    loadGrid();
                    $("#btnHOANTHANH2").addClass("disabled");
                    $("#btnTHUCHIEN2").addClass("disabled");
                    $("#btnTHUCHIEN_NHANH").addClass("disabled");
                    $("#btnHOANTHANH").addClass("disabled");
                    $("#btnTHUCHIEN").addClass("disabled");
                    DlgUtil.showMsg('Hoàn thành danh sách y lệnh thành công.');
                }, 500);
            } else {
                setTimeout(function () {
                    hideLoading();
                    DlgUtil.showMsg('Đã có lỗi xảy ra 1.');
                }, 500);
            }
        });

        $("#btnHUYTHUCHIEN").click(function () {
            $("#modalTHT").modal("hide");
        });

        $("#btnTHUCHIEN_THUOC").click(function () {
            LoadingUtil.disabledButton(this, hoanthanhthuoc.bind(this, "1"));
        });

        $("#btnTHUCHIEN_NHANH").click(function () {
            LoadingUtil.disabledButton(this, hoanthanhthuoc.bind(this, "3"));
        });

        $("#btnHOANTHANH_THUOC").click(function () {
            LoadingUtil.disabledButton(this, hoanthanhthuoc.bind(this, "2"));
            // hoanthanhthuoc("2");
        })
    }

    function hoanthanhthuoc(type) {
        if (type != "1" && type != "2" && type != "3") {
            DlgUtil.showMsg('Đã có lỗi xảy ra 2.');
            return;
        }
        var selRows = [];
        selRows = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');

        if (selRows.length < 1) {
            DlgUtil.showMsg('Vui lòng chọn thuốc/dịch vụ trước khi hoàn thành.');
            return;
        }
        $("#modalTHT").modal("hide");
        showLoading();

        var _loaith = "";
        var _ghichu = $("#txtGHICHU").val();
        if(type == "3"){
            for (var i = 0; i < selRows.length; i++) {
                var selected = [];
                $('.selectlist_' + selRows[i]).prop('checked', function () {
                    selected.push($(this).is(":checked") ? "1" : "0");
                });
                _loaith = selected.join("||");
                _ghichu = $('.ghichu_' + selRows[i])[0].value;
            }

        }
        else {
            var sang = $("#chkSANG").is(":checked") ? "1" : "0";
            var trua = $("#chkTRUA").is(":checked") ? "1" : "0";
            var chieu = $("#chkCHIEU").is(":checked") ? "1" : "0";
            var toi = $("#chkTOI").is(":checked") ? "1" : "0";
            _loaith = sang + "||" + trua + "||" + chieu + "||" + toi;
        }

        var objData = {};
        objData.KHAMBENHID = opt._khambenhid;
        objData.LOAITH = _loaith;
        objData.GHICHU = _ghichu;
        objData.DVKBID = "";
        objData.LISTDVKBID = selRows.map(function(el){return $("#" + _gridId).jqGrid('getCell', el, 'DICHVUKHAMBENHID');}).join(",");;
        objData.TYPE = type + "";
        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU008.THYLENH", JSON.stringify(objData));
        if (fl == 1) {
            setTimeout(function () {
                hideLoading();
                loadGrid();
                $("#btnHOANTHANH2").addClass("disabled");
                $("#btnTHUCHIEN2").addClass("disabled");
                $("#btnTHUCHIEN_NHANH").addClass("disabled");
                $("#btnHOANTHANH").addClass("disabled");
                $("#btnTHUCHIEN").addClass("disabled");
                if (type == "1") {
                    DlgUtil.showMsg('Thực hiện y lệnh thành công.');
                } else {
                    DlgUtil.showMsg('Hoàn thành y lệnh thành công.');
                }
            }, 500);
        } else {
            setTimeout(function () {
                hideLoading();
                DlgUtil.showMsg('Đã có lỗi xảy ra 3.');
            }, 500);
        }
    }

    function _bindEvent() {
        GridUtil.setGridParam(_gridId, {
            // ondblClickRow : function(id, iRow, iCol, event) {
            //     GridUtil.unmarkAll(_gridId);
            //     GridUtil.markRow(_gridId,id);
            //     var _row = $('#' + _gridId).jqGrid('getRowData', id);
            //     markData = _row;
            //     if (_row != null) {
            //         if ($(event.target).attr("aria-describedby") == "grdDanhSachYLenh_MAHOSOBENHAN") {
            //
            //         } else {
            //             $("#dienbien").html(_row.DIENBIEN);
            //             $("#ghichu").html(_row.GHICHU);
            //             $("#flipFlop").modal('show');
            //         }
            //     }
            // },
            onSelectRow : function(id, selected, event) {
                GridUtil.unmarkAll(_gridId);
                GridUtil.markRow(_gridId,id);
                var _row = $('#' + _gridId).jqGrid('getRowData', id);
                var loaith = _row.LOAITH;
                if (loaith.split("||").length == 4){
                    $("#chkSANG").prop("checked", parseInt(loaith.split("||")[0]));
                    $("#chkTRUA").prop("checked", parseInt(loaith.split("||")[1]));
                    $("#chkCHIEU").prop("checked", parseInt(loaith.split("||")[2]));
                    $("#chkTOI").prop("checked", parseInt(loaith.split("||")[3]));
                }
                $("#txtGHICHU").val(_row.GHICHU);
                $("#txtCACHDUNG").html("Cách dùng: " + (_row.CACHDUNG ? _row.CACHDUNG : ""));
                markData = _row;
                var selRows = $(this).jqGrid('getGridParam', 'selarrrow');

                if (selRows.length >= 0) {
                    $("#btnTHUCHIEN2").removeClass("disabled");
                    $("#btnTHUCHIEN_NHANH").removeClass("disabled");
                    $("#btnHOANTHANH2").removeClass("disabled");
                    $("#btnTHUCHIEN").removeClass("disabled");
                    $("#btnHOANTHANH").removeClass("disabled");
                } else {
                    $("#btnTHUCHIEN2").addClass("disabled");
                    $("#btnTHUCHIEN_NHANH").addClass("disabled");
                    $("#btnHOANTHANH2").addClass("disabled");
                    $("#btnTHUCHIEN").addClass("disabled");
                    $("#btnHOANTHANH").addClass("disabled");
                }
                // var ids = $("#" + _gridId).getDataIDs();
                // for (var i = 0; i < ids.length; i++) {
                //     $(".selectlist_" + ids[i]).attr('disabled','true');
                //     $(".ghichu_" + ids[i]).attr('disabled','true');
                // }
                // if(selected) {
                //     $(".selectlist_" + id).removeAttr('disabled');
                //     $(".ghichu_" + id).removeAttr('disabled');
                // }
            },

            onSelectAll: function (aRowids, status) {
                if (status && aRowids.length > 0) {
                    $("#btnTHUCHIEN2").removeClass("disabled");
                    $("#btnTHUCHIEN_NHANH").removeClass("disabled");
                    $("#btnHOANTHANH2").removeClass("disabled");
                    $("#btnTHUCHIEN").removeClass("disabled");
                    $("#btnHOANTHANH").removeClass("disabled");
                } else {
                    $("#btnTHUCHIEN2").addClass("disabled");
                    $("#btnTHUCHIEN_NHANH").addClass("disabled");
                    $("#btnHOANTHANH2").addClass("disabled");
                    $("#btnTHUCHIEN").addClass("disabled");
                    $("#btnHOANTHANH").addClass("disabled");
                }
            },

            loadComplete : function(id) {
                $("#grdDanhSachYLenh .jqgrow").hammer({
                    time: 1500
                }).bind("press", function(e) {
                    GridUtil.unmarkAll(_gridId);
                    GridUtil.markRow(_gridId,e.currentTarget.id);
                    markData = $('#' + _gridId).jqGrid('getRowData', e.currentTarget.id);
                    //var loaith = markData.LOAITH;
                    // let _sang = parseInt(loaith.split("||")[0]);
                    // let _trua = parseInt(loaith.split("||")[1]);
                    // let _chieu = parseInt(loaith.split("||")[2]);
                    // let _toi = parseInt(loaith.split("||")[3]);
                    // if (loaith.split("||").length == 4){
                    //     $("#chkSANG, #chkSANG2").prop("checked", parseInt(_sang));
                    //     $("#chkTRUA, #chkTRUA2").prop("checked", parseInt(_trua));
                    //     $("#chkCHIEU, #chkCHIEU2").prop("checked", parseInt(_chieu));
                    //     $("#chkTOI, #chkTOI2").prop("checked", parseInt(_toi));
                    // }
                    $("#txtGHICHU, #txtGHICHU2").val(markData.GHICHU);
                    $("#txtCACHDUNG2").html("Cách dùng: " + (markData.CACHDUNG ? markData.CACHDUNG : ""));
                    if (markData != null) {
                        $("#modalNote").modal('show');
                        document.getSelection().removeAllRanges();
                    }
                });

                $("#" + _gridId).on("click", "input[type='checkbox']", function () {
                    var rowId = $(this).closest("tr").attr("id"); // Lấy ID của dòng chứa checkbox
                    $("#" + _gridId).jqGrid("setSelection", rowId, true); // Chọn dòng tương ứng

                });


                var ids = $("#" + _gridId).getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#" + _gridId).jqGrid('getRowData', id);
                    // if (row["CKDVID_DAKY"] && parseInt(row["CKDVID_DAKY"]) > 0) {
                    //     $(`#${_gridId} #${id}>td`).css("background-color", "rgb(132,153,255)");
                    // } else {
                    //     if (row.TRANGTHAI == 1) {
                    //         $(`#${_gridId} #${id}>td`).css("background-color", "rgb(243 243 180)");
                    //     } else if (row.TRANGTHAI == 2) {
                    //         $(`#${_gridId} #${id}>td`).css("background-color", "rgb(207 211 249)");
                    //     }
                    // }

                    let _loaith = row.LOAITH;
                    let _sang = parseInt(_loaith.split("||")[0]);
                    let _trua = parseInt(_loaith.split("||")[1]);
                    let _chieu = parseInt(_loaith.split("||")[2]);
                    let _toi = parseInt(_loaith.split("||")[3]);
                    // THUCHIEN
                    let _htmlTH = `<div class="">
                        <div class="col-xs-3">
                            <div class="center-align">
                                <input type="checkbox" class="selectlist_${id}" ${_sang == 1 ? "checked='checked'" : ""} style="zoom: 1.5;" >
                                Sáng
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="center-align">
                                <input type="checkbox" class="selectlist_${id}" ${_trua == 1 ? "checked='checked'" : ""} style="zoom: 1.5; ">
                                Trưa
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="center-align">
                                <input type="checkbox" class="selectlist_${id}" ${_chieu == 1 ? "checked='checked'" : ""} style="zoom: 1.5;">
                                Chiều
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="center-align">
                                <input type="checkbox" class="selectlist_${id}" ${_toi == 1 ? "checked='checked'" : ""} style="zoom: 1.5;">
                                Tối
                            </div>
                        </div>
                    </div>`;


                    let _htmlGC = `<div class="">
                        <div class="col-xs-12">
                            <div class="center-align">
                                <input type="text" class="ghichu_${id}" value="${row.GHICHU}">
                            </div>
                        </div>
                    </div>`;

                    $("#" + _gridId).jqGrid('setCell', id, 'THUCHIEN', _htmlTH);
                    $("#" + _gridId).jqGrid('setCell', id, 'GHICHU', _htmlGC);
                }

                let firstRow = $('#' + _gridId).jqGrid('getRowData', 1);
                if (firstRow && firstRow.MEDIA_ID && firstRow.MEDIA_ID.length > 0) {
                    $("#imageSign").attr("src", "../upload/getdata.jsp?id=" + firstRow.MEDIA_ID);
                    $("#imageSignPreview").attr("href", "../upload/getdata.jsp?id=" + firstRow.MEDIA_ID);
                    $("#btnXACNHANDIEUDUONG").removeClass("disabled");
                } else {
                    $("#imageSign").attr("src", "../common/image/sign_blank.png");
                    $("#imageSignPreview").attr("href", "../common/image/sign_blank.png");
                    $("#btnXACNHANDIEUDUONG").addClass("disabled");
                }

                if (firstRow && firstRow.MEDIA_ID_DD && firstRow.MEDIA_ID_DD.length > 0) {
                    $("#imageSignDD").attr("src", "../upload/getdata.jsp?id=" + firstRow.MEDIA_ID_DD);
                    $("#imageSignPreviewDD").attr("href", "../upload/getdata.jsp?id=" + firstRow.MEDIA_ID_DD);
                } else {
                    $("#imageSignDD").attr("src", "../common/image/sign_blank.png");
                    $("#imageSignPreviewDD").attr("href", "../common/image/sign_blank.png");
                }
            },
            onSelectAll: function (aRowids) {
                var selRows = $(this).jqGrid('getGridParam', 'selarrrow');
                if (selRows.length >= 0) {
                    $("#btnHOANTHANH2").removeClass("disabled");
                    $("#btnHOANTHANH").removeClass("disabled");
                } else {
                    $("#btnHOANTHANH2").addClass("disabled");
                    $("#btnHOANTHANH").addClass("disabled");
                }
            },
            // jqGridAfterGridComplete:  function(e, rowid, orgClickEvent) {
            //
            // }
        });

        $("#modalNote").on('click',function(e){
            if($(e.target).closest(".modal-content").length == 0 ){
                $("#modalNote").modal('hide');
            }
        });

        $("#imageSign").click(function () {
            $("#imageSignPreview").trigger("click");
        });

        $("#imageSignDD").click(function () {
            $("#imageSignPreviewDD").trigger("click");
        });

        $(window).resize(function() {
            resize();
            var t = window.outerHeight/2 - 20;
            $("#loading .spinner").css("top", t);
            $("#loadding_title").css("top", t + 10);
        });
    }

    function loadGrid() {
        var objData = new Object();
        objData.KHAMBENHID = opt._khambenhid;
        objData.NGAYDIEUTRI = $('#txtNGAYYLENH').val();
        objData.DUONGDUNG = $('#cboDUONGDUNG').val();
        objData.LOAIYLENH = $('input[name="radLOAIYLENH"]:checked').val()
        // objData.TENBENHNHAN = nvl($('#txtTENBENHNHAN').val().trim(), '-1');
        // objData.PHONGID = $('#cboPHONG').val() == "0" ? "-1" : $('#cboPHONG').val();
        var _sql_par = [ {
            "name" : "[0]",
            "value" : JSON.stringify(objData)
        } ];
        let dsylenh = caType == 0 ? "NTU008.DSYLENH" : "NTU008.DSYLENH2";
        GridUtil.loadGridBySqlPage(_gridId, dsylenh, _sql_par);
    }

    function showLoading() {
        var t = window.outerHeight/2 - 20;
        $("#loading .spinner").css("top", t);
        $("#loadding_title").css("top", t + 10);
        $("#loading").removeClass("hide");
        $("#loading").addClass("show");
    }

    function hideLoading() {
        $("#loading").addClass("hide");
        $("#loading").removeClass("show");
    }

    function resize() {
        $('#' + _gridId).jqGrid('setGridWidth', window.innerWidth - 38);
    }

    function showSignPad() {
        var myModal = DlgUtil.buildSignPadPopup(
        "BỆNH NHÂN KÝ TÊN",
        628, 400,
            {
                signMethod: signMethod,
                TENBENHNHAN: tthc.TENBENHNHAN
            },
        function (e){
            myModal.close();
            var signpadImageBase64 = e.imageBase64;
            if (signpadImageBase64) {
                doSigning(signpadImageBase64, tthc.TENBENHNHAN);
            }
        });
        myModal.open();
    }

    function showSignFinger() {
        let signingPersonsHtml = `<li data-ngkyt="-1" data-ngky="${tthc.TENBENHNHAN}"><a href="#" id="ttBn">Bệnh nhân</a></li>`;
        if (tthc.HO_TEN_CHA && tthc.HO_TEN_CHA.length > 0) {
            signingPersonsHtml = `${signingPersonsHtml}<li data-ngkyt="Bố" data-ngky="${tthc.HO_TEN_CHA}"><a href="#" id="ttBo">Bố</a></li>`
        }
        if (tthc.HO_TEN_ME && tthc.HO_TEN_ME.length > 0) {
            signingPersonsHtml = `${signingPersonsHtml}<li data-ngkyt="Mẹ" data-ngky="${tthc.HO_TEN_ME}"><a href="#" id="ttMe">Mẹ</a></li>`
        }
        if (tthc.TEN_NGUOITHAN && tthc.TEN_NGUOITHAN.length > 0) {
            signingPersonsHtml = `${signingPersonsHtml}<li data-ngkyt="-1" data-ngky="${tthc.TEN_NGUOITHAN}"><a href="#" id="ttNn">Người thân</a></li>`
        }
        signingPersonsHtml = `${signingPersonsHtml}<li data-ngkyt="-1"><a href="#" id="ttKhac">Khác</a></li>`
        let infoHtml =
            `<div class="col-xs-12" style="padding: 7px 4px 7px 0;">
                <strong id="lblHoTen">Tên bệnh nhân: <strong style="color: blue">${tthc.TENBENHNHAN}</strong></strong><br/>
            </div>
            <div class="col-xs-12" style="padding: 7px 4px 7px 0;">
                <label id="lblGioiTinh">Giới tính: <strong>${tthc.GIOITINH}</strong></label><br/>
            </div>
            <div class="col-xs-12" style="padding: 7px 4px 7px 0;">
                <label id="lblNgaySinh">Ngày sinh: <strong>${tthc.NGAYSINH}</strong></label>
            </div>
            <div class="col-xs-12" style="padding: 7px 4px 7px 0;">
                <strong id="lblHoTen">Người ký: </strong>
            </div>
            <div class="col-xs-12" style="padding: 7px 4px 7px 0;">
                <div class="input-group">
                    <div class="input-group-btn">
                        <button type="button" id="ngky_btn" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Bệnh nhân <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" id="ngky_selection">
                            ${signingPersonsHtml}
                        </ul>
                    </div>
                    <input disabled type="text" data-nn="" class="form-control" id="ngky_selected" value="${tthc.TENBENHNHAN}" placeholder="Vui lòng nhập người ký" aria-describedby="basic-addon1">
                </div>
            </div>`;
        let infoJs = `
            $("#ngky_selection li").click(function(){
                let ngky = $(this).data("ngky");
                let ngkyt = $(this).data("ngkyt");
                console.log(ngkyt);
                if (ngky) {
                    $("#ngky_selected")
                        .attr("disabled", "disabled")
                        .data("ngkyt", ngkyt)
                        .removeClass("warning")
                        .val(ngky)
                        .change();
                    $("#ngky_btn").html($(this).text() + "<span class='caret'>");
                } else {
                    $("#ngky_selected")
                        .removeAttr("disabled")
                        .data("ngkyt", "-1")
                        .addClass("warning")
                        .val("");
                    $("#ngky_btn").html("Khác<span class='caret'>");
                }
            });
        `;
        let infoCss = `
            #lblHoTen, #lblGioiTinh, #lblNgaySinh {
                font-size: 16px;
            }
            
            #ngky_btn {
                padding: 6px 15px 6px 6px !important;
                border-top-right-radius: unset !important;
                border-bottom-right-radius: unset !important;
            }
            
            #ngky_btn .caret {
                top: 40%;
                position: absolute;
                margin-left: 4px;
            }
            
            input {
                font-weight: bold;
            }
            
            input.warning {
                border-color: #ffa60b;
                font-weight: normal !important;
                box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            }
           
            .warning::placeholder {
                color: #ffc970 !important;
            }
        `;
        var myModal = DlgUtil.buildFingerScanPopup(
        "Bệnh nhân ký vân tay",
        628, 450, {
                html: infoHtml,
                css: infoCss,
                js: infoJs,
                personSigner: {
                    render: true,
                    el: "ngky_selected"
                }
            },
        function (e){
            myModal.close();
            let fingerImageBase64 = e.imageBase64;
            let finalNgKy = e.finalNgKy;
            if (fingerImageBase64) {
                doSigning(fingerImageBase64, finalNgKy);
            }
        });
        myModal.open();
    }

    function doSigning(imageBase64, finalNgKy) {
        showLoading();
        let patientSignedFile = FileUtil.dataURLtoFile(imageBase64,`signpad_${opt._khambenhid}_${Date.now()}.png`);
        UploadUtil.uploadIDGPromises(patientSignedFile, false)
            .then(
                function (patientSignedResult) {
                    if (signStep == "1") {
                        if (!anhCKNV) {
                            DlgUtil.showMsg('Chưa có dữ liệu chữ ký nhân viên!');
                            hideLoading();
                            return;
                        }
                        let staffSignedFile = FileUtil.dataURLtoFile(anhCKNV,`signpad_dd_${opt._khambenhid}_${Date.now()}.png`);
                        UploadUtil.uploadIDGPromises(staffSignedFile, false)
                            .then(
                                function (staffSignedResult) {
                                    let objectData = new Object();
                                    objectData.KHAMBENHID = opt._khambenhid;
                                    objectData.KHOAID = tthc.KHOAID;
                                    objectData.NGAYKY = $("#txtNGAYYLENH").val();
                                    objectData.BUCKET = patientSignedResult.bucket;
                                    objectData.FILE_NAME = patientSignedResult.file_name;
                                    objectData.MEDIA_ID = patientSignedResult.media_id;
                                    objectData.URL_IMG = patientSignedResult.url_img;
                                    objectData.TEN_NG_KY = finalNgKy;
                                    objectData.BUCKET_DD = staffSignedResult.bucket;
                                    objectData.FILE_NAME_DD = staffSignedResult.file_name;
                                    objectData.MEDIA_ID_DD = staffSignedResult.media_id;
                                    objectData.URL_IMG_DD = staffSignedResult.url_img;
                                    objectData.DVKBIDS = lstdvkbids;
                                    let fl2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU008.KYBENHNHAN2", JSON.stringify(objectData));
                                    if (fl2 == 1) {
                                        hideLoading();
                                        DlgUtil.showMsg('Lưu thông tin ký bệnh nhân thành công!');
                                        loadGrid();
                                    } else {
                                        DlgUtil.showMsg('Ký không thành công, vui lòng thực hiện lại!');
                                        hideLoading();
                                    }
                                },
                                function (error) {
                                    hideLoading();
                                    alert(error);
                                })
                            .catch(reason => {hideLoading();alert(JSON.stringify(reason))});
                    } else if (signStep == "2") {
                        var objectData = new Object();
                        objectData.KHAMBENHID = opt._khambenhid;
                        objectData.KHOAID = tthc.KHOAID;
                        objectData.NGAYKY = $("#txtNGAYYLENH").val();
                        objectData.BUCKET = patientSignedResult.bucket;
                        objectData.FILE_NAME = patientSignedResult.file_name;
                        objectData.MEDIA_ID = patientSignedResult.media_id;
                        objectData.URL_IMG = patientSignedResult.url_img;
                        objectData.TEN_NG_KY = finalNgKy;
						objectData.DVKBIDS = lstdvkbids;
                        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU008.KYBENHNHAN", JSON.stringify(objectData));
                        if (fl == 1) {
                            hideLoading();
                            DlgUtil.showMsg('Lưu thông tin ký bệnh nhân thành công!');
                            loadGrid();
                        } else {
                            DlgUtil.showMsg('Ký không thành công, vui lòng thực hiện lại!');
                            hideLoading();
                        }
                    }
                },
                function (error) {
                    hideLoading();
                    alert(error);
                })
            .catch(reason => {hideLoading();alert(JSON.stringify(reason))});
    }
}