<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEU_PTTT_A4_26320" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="18c5e549-94fd-415a-8af9-02c896054c66">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="dev_baocao_hth"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<parameter name="i_loaidoituong" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call PHIEU_CLS_ALL_CDHA_V1_30360(null,$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},-1,$P{ora_cursor})}]]>
	</queryString>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="SOTHUTU_LAYMAU" class="java.math.BigDecimal"/>
	<field name="SOTHUTU" class="java.math.BigDecimal"/>
	<field name="BARCODE" class="java.lang.String"/>
	<field name="PHONGLAYMAU" class="java.lang.String"/>
	<field name="PHONGTHUCHIEN" class="java.lang.String"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="TENXETNGHIEM" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="DOCTOR_NAME" class="java.lang.String"/>
	<field name="CHUANDOANPHU" class="java.lang.String"/>
	<field name="KHOATHUCHIEN" class="java.lang.String"/>
	<field name="CHANDOANBD" class="java.lang.String"/>
	<field name="NOTE" class="java.lang.String"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="NGAYCHIDINH" class="java.lang.String"/>
	<field name="TENGIUONG" class="java.lang.String"/>
	<field name="TENNHOM" class="java.lang.String"/>
	<field name="CHECK_DT" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="CAPCUU" class="java.lang.String"/>
	<field name="THUONG" class="java.lang.String"/>
	<field name="MADICHVU" class="java.lang.String"/>
	<field name="MANHOM" class="java.lang.String"/>
	<field name="BANGOAITRU" class="java.lang.String"/>
	<field name="MAKHOACHIDINH" class="java.lang.String"/>
	<field name="MAPHONGKHAM" class="java.lang.String"/>
	<field name="DICHVUDOITUONG" class="java.lang.String"/>
	<field name="THANHTIEN" class="java.math.BigDecimal"/>
	<field name="DTBNID" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH" class="java.lang.String"/>
	<field name="YEUCAUKHAM" class="java.lang.String"/>
	<field name="ORG_ADDRESS" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="252">
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="40" y="140" width="323" height="15" uuid="59734d35-008d-4c1e-9686-3370da334fd3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="155" width="43" height="15" uuid="0681a4d4-3dc2-4fef-b3bd-1ec5187d5cbd"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Địa chỉ:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="43" y="155" width="510" height="15" uuid="96a0210f-b609-4959-8a4b-150b87807b45"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIACHI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="170" width="54" height="15" uuid="ac5977c0-7dc6-4dcc-84bb-45a92b6b1622"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Đối tượng:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="55" y="170" width="140" height="15" uuid="ba1e1fd6-fcf5-4a93-9ed3-264a067adca0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}==null?$F{TEN_DTBN}:
$F{SOTHE}.substring( 0,3)=="NTH"?"Nợ thẻ":$F{TEN_DTBN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="140" width="40" height="15" uuid="769310cc-2572-4077-bac9-a4f8a0cb659e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Họ tên:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="199" width="54" height="15" uuid="d53a54d3-5059-4f62-9d10-52ec5e5ca179"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Chẩn đoán:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="54" y="199" width="500" height="15" uuid="2597b26a-b985-4274-9486-ed3398c61659"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{CHANDOAN} == null ? ($F{CHANDOANBD}==null?"":$F{CHANDOANBD}) : $F{CHANDOAN})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="467" y="140" width="46" height="15" uuid="756bb77f-445f-439b-89a7-6a2edcbd522d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giới tính:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="513" y="140" width="39" height="15" uuid="2368e380-1627-4f5f-afb3-708a056f3573"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="368" y="140" width="28" height="15" uuid="532acd1f-c24c-42f2-bc65-47f322830646"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Tuổi:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="395" y="140" width="22" height="15" uuid="e4e4af81-c0c1-4870-aa87-7ab1d71b6cb9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="203" y="170" width="52" height="15" uuid="150dae73-b740-4f5f-96f0-8833ea5272f0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Số thẻ BH:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="214" width="555" height="15" uuid="fdc32a8e-926c-467e-b3dd-28258d438da9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{CHUANDOANPHU}==null?"Bệnh kèm theo: ":"Bệnh kèm theo: "+$F{CHUANDOANPHU})]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement positionType="Float" x="329" y="169" width="29" height="15" uuid="78702282-6ec5-495e-98b4-20c94cd30809"/>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="387" y="169" width="50" height="15" uuid="0cdf87df-aee6-423e-aba5-612ab2aeaf49"/>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="300" y="169" width="29" height="15" uuid="e5723b9c-1703-4a8f-ad9b-472809f1fcca"/>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" x="279" y="169" width="21" height="15" uuid="28d74ceb-ee90-471c-86ea-ad9bf3381057"/>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="258" y="169" width="21" height="15" uuid="bbc9f8d6-13f0-4182-b6f5-e8faafdeab9e"/>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" mode="Transparent" x="358" y="169" width="29" height="15" uuid="031c2829-8ab9-4375-a9a0-61d3cd548ad8"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="388" y="170" width="50" height="15" uuid="4debce12-0f3b-453d-acf3-a8c26f898944"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0,3)!="NTH"?$F{SOTHE}.substring( 10, 15 ):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="359" y="170" width="29" height="15" uuid="1ebb5490-b29f-4008-a4e1-b3176ec6b6e6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0,3)!="NTH"?$F{SOTHE}.substring( 7, 10 ):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="258" y="170" width="21" height="15" uuid="4e4b831c-5e81-4c77-8974-25f1ff374c25"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0,3)!="NTH"?$F{SOTHE}.substring( 0, 2 ):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="329" y="170" width="29" height="15" uuid="f6835039-ebc9-4669-8edc-cac2035e5e91"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0,3)!="NTH"?$F{SOTHE}.substring( 5, 7 ):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="300" y="170" width="29" height="15" uuid="aaec7e2c-8c2a-4c84-a7f3-a91d4d0f4262"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0,3)!="NTH"?$F{SOTHE}.substring( 3, 5 ):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="279" y="170" width="20" height="15" uuid="43f8fa63-1ebd-4fc6-98ed-159d56d10efa"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0,3)!="NTH"?$F{SOTHE}.substring( 2, 3 ):""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="184" width="555" height="15" uuid="430b4496-650c-433f-af35-f0e505df3bf2"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; " + ($F{PHONGKHAM}==null?"":$F{PHONGKHAM}) + ($F{TENGIUONG}==null?"":"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Giường: " + $F{TENGIUONG})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="234" width="30" height="18" uuid="5020dbc4-9ad1-4e11-8987-6fd932150dba"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="30" y="234" width="449" height="18" uuid="8ae2dbc4-bcb5-4c2c-8ecc-a0e116ae2b26"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[YÊU CẦU]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="479" y="234" width="76" height="18" uuid="58347d85-442e-4d49-ad88-1253a79e631f"/>
				<box leftPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[SỐ LƯỢNG]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="418" y="140" width="41" height="15" uuid="c62f2ccb-6d27-47db-845c-f7ddf5f4c1c3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVTUOI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="403" y="0" width="45" height="18" uuid="beae9943-2f18-4421-94b2-969faaf2e936"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<text><![CDATA[Mã BA: ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="448" y="32" width="108" height="18" uuid="d35ccecc-57d3-460c-9a18-94469e197749"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOPHIEU}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="58" width="555" height="19" uuid="bb6fbce5-cb46-41b8-b4b8-80012567e3f8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHIẾU PHẪU THUẬT THỦ THUẬT"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="403" y="17" width="45" height="15" uuid="b18857ca-001e-4898-8743-252f794da42f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<text><![CDATA[Mã BN:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="77" width="555" height="18" uuid="58c63930-a605-40ca-8f90-c995e143f1f8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{PHONGTHUCHIEN}==null?"":$F{PHONGTHUCHIEN}) + " " + ($F{ORG_ADDRESS}==null?"":$F{ORG_ADDRESS})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="19" width="296" height="20" uuid="45290243-257c-4cf7-b12f-3320564fffaf"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="392" y="96" width="150" height="15" uuid="061d9035-c910-4cfe-8b18-a2795342f178"/>
				<box leftPadding="0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BA: " + $F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="296" height="20" uuid="ed6336fe-59ed-4c3a-8c2f-160ce4ace4f1"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="220" y="111" width="46" height="17" uuid="5983c0fd-567e-4c3b-96a9-605054f93ef5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Cấp cứu]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="198" y="112" width="20" height="15" uuid="762eb9b7-e680-49d4-a029-35472fe0d78a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineColor="#333333"/>
					<topPen lineWidth="0.5" lineColor="#333333"/>
					<leftPen lineWidth="0.5" lineColor="#333333"/>
					<bottomPen lineWidth="0.5" lineColor="#333333"/>
					<rightPen lineWidth="0.5" lineColor="#333333"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPCUU}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="294" y="111" width="45" height="17" uuid="e69b76ee-0bde-4863-95c8-7b656eca1708"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Thường]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="448" y="17" width="108" height="15" uuid="ac8d5e9b-7319-4b41-b175-630355f5a247"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="38" width="296" height="20" uuid="ece5cc01-4044-4483-9dc7-d3e145053a3d"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOATHUCHIEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="403" y="32" width="45" height="18" uuid="9effb5a5-c27a-4150-98a1-41ba47a90ccf"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<text><![CDATA[Số phiếu:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="448" y="0" width="108" height="18" uuid="1a19a2a2-20b2-419b-80c5-906ba5b82351"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="272" y="112" width="20" height="15" uuid="f53d7c31-8b19-4d69-9c79-9447a163c6b5">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineColor="#333333"/>
					<topPen lineWidth="0.5" lineColor="#333333"/>
					<leftPen lineWidth="0.5" lineColor="#333333"/>
					<bottomPen lineWidth="0.5" lineColor="#333333"/>
					<rightPen lineWidth="0.5" lineColor="#333333"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THUONG}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="392" y="112" width="151" height="24" uuid="0c30ca9e-23f6-406e-aa3a-87b342cf46ca"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code39" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
		</band>
	</title>
	<detail>
		<band height="18" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="30" y="0" width="449" height="18" uuid="ae36912b-b8f7-4062-bf31-6a129b5c8d6b"/>
				<box leftPadding="2" rightPadding="2">
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENXETNGHIEM} + ($F{GHICHU} == null ? "": (" (" + $F{GHICHU} + ")"))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="479" y="0" width="76" height="18" uuid="3537b062-fed9-48a5-8837-21053267d45d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="18" uuid="cefe1015-73a5-48c4-bd57-44ac0b5cfca1"/>
				<box leftPadding="2">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="109">
			<staticText>
				<reportElement positionType="Float" x="359" y="23" width="185" height="18" uuid="4988b46a-eee3-4cdd-951f-7444cfc8bf07"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁC SỸ CHỈ ĐỊNH]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="359" y="5" width="185" height="18" uuid="0bbfae81-1bd1-4362-a51a-c83062a2fc42"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYCHIDINH}.substring( 0, 2 )+"h"+$F{NGAYCHIDINH}.substring( 3, 5 ) +", ngày "+ $F{NGAYCHIDINH}.substring( 6, 8 )+" tháng "+ $F{NGAYCHIDINH}.substring( 9, 11 )+" năm "+ $F{NGAYCHIDINH}.substring( 12, 16 )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="359" y="90" width="185" height="18" uuid="4460297f-00c1-40df-b327-dab6dda749fa"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DOCTOR_NAME}]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="108" width="555" height="1" uuid="359ce926-0af1-4ec5-a21a-1728fe0729a1"/>
			</break>
		</band>
	</summary>
</jasperReport>
