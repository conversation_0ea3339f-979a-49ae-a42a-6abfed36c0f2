<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js?v=20180101" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>

<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">    
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT02K049_TraCuuLSKCBHISL3.js?v=202011252"></script>
<script type="text/javascript" src="..//noitru/cominf.js?v=20201029" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/xml2json.js?v=20180101" ></script>

<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/xlsx.full.min.js"></script>
<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/ods.js"></script>
<div class="container" style="width: 100%;">
	<div class="" id="dvPhieuChamSoc">
		<div class="col-xs-12 low-padding mgt5">
			<span><b id="txtAnnounce">Tra cứu thông tin LS KCB HIS L3</b></span>
		</div>
		<div class="col-xs-12 low-padding" id="dvLichSuKCB">
			<div class="col-xs-12 low-padding">
				<div class="col-xs-2 low-padding required">
					<label class="">Mã thẻ</label>
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm" id="txtMATHE" style="width:100%" maxlength="15">
				</div>
				<div class="col-xs-2 low-padding required">
					<label class="">Họ tên</label>
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm" id="txtHOTEN" style="width:100%">
				</div>
				<div class="col-xs-2 low-padding required">
					<label class="">Ngày sinh</label>
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm" id="txtNGAYSINH" style="width:100%">
				</div>
				
			</div>
			
			<div class="col-xs-12 low-padding mgt5" style="text-align: center;">
				<button type="submit" id="btnSearch" class="btn btn-default btn-primary">
				<span class="glyphicon glyphicon-search"></span> Tìm kiếm</button>
			</div>
			<div class="col-xs-12 low-padding">
				<table id="grdDS_LichSuKCB" class="table table-striped jambo_table bulk_action"></table>
				<div id="pager_grdDS_LichSuKCB"></div>
			</div>
		</div>
		
		<div class="col-xs-12 low-padding mgt5" style="text-align: center;">
			<button type="submit" id="btnDONG" class="btn btn-default btn-primary">
			<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
		</div>
	</div>
</div>
<script>
	var paramInfo = CommonUtil.decode('{paramData}');

	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var url ='{url}';
	var session_par=[];
	
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("CallHISL3API");

	var _opts={
		lang: lang,
		_param:session_par,
		_uuid:uuid,
		_hospital_id: hospital_id
	}
	
	parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	var data = EventUtil.getVar("dlgVar");

	_opts.MATHE = typeof data.MATHE == 'undefined' ? "" : data.MATHE; 
	_opts.HOTEN = typeof data.HOTEN == 'undefined' ? "" : data.HOTEN; 
	_opts.NGAYSINH = typeof data.NGAYSINH == 'undefined' ? "" : data.NGAYSINH; 
	
	var tba = new lichsukcb(_opts);
	tba.load();	
</script>