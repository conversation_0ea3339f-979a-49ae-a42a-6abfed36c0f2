function ChinhSuaBenhPhu(opt) {
	_gridId = "grdBP";
	this.load = doLoad;
	_gridHeader = "Mã bệnh,ICD10CODE,20,0,f,l;Tên bệnh,ICD10NAME,100,0,e,l; ,ACT,10,d,f,l";
	var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	var _benhphu = "";
	var _benhchinh = opt.benhchinh;
	var load_icd_yhct_sl = 0;
	var sql_icd = 'CG.ICD10';
	var benhphu_yhct = opt.benhphu1;
	var _json = {
		ICD10CODE : "",
		ICD10NAME : ""
	};
	function doLoad() {
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		load_icd_yhct_sl = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_BAC_LOAD_YHCT_SELECT');
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUATENBENHPHU') == '1') {
			_gridHeader = "Mã bệnh,ICD10CODE,20,0,f,l;Tên bệnh,ICD10NAME,100,0,f,l; ,ACT,10,d,f,l";
		}
		if (load_icd_yhct_sl == '1') {
			_gridHeader = "Mã bệnh,ICD10CODE,20,0,f,l;Tên bệnh,ICD10NAME,100,0,e,l;Mã bệnh 1,ICD10CODE1,20,0,t,l;Tên bệnh 1,ICD10NAME1,100,0,t,l; ,ACT,10,d,f,l";
			sql_icd = 'NT.008.YHCTV3';
			_col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
			_json = {
				ICD10CODE : "",
				ICD10NAME : "",
				ICD10CODE1 : "",
				ICD10NAME1 : ""
			};
		}
		//L2PT-96824
		if(opt.yhct == '1'){
			_gridHeader = "Mã bệnh,ICD10CODE,20,0,f,l;Tên bệnh,ICD10NAME,100,0,e,l;Mã bệnh 1,ICD10CODE1,20,0,t,l;Tên bệnh 1,ICD10NAME1,100,0,t,l; ,ACT,10,d,f,l";
			sql_icd = 'NT.008.YHCTV4';
			_col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
			_json = {
				ICD10CODE : "",
				ICD10NAME : "",
				ICD10CODE1 : "",
				ICD10NAME1 : ""
			};
		}
		_gridHeader += "; ,ACT2,50,udb,f,l"; // L2PT-130602
		GridUtil.init(_gridId, "99%", 230, "Danh sách bệnh kèm theo", false, _gridHeader, true);
		ComboUtil.initComboGrid("txtMACHANDOANRAVIEN_KEMTHEO", sql_icd, [], "450px", _col, function(event, ui) {
			if (load_icd_yhct_sl == '1') {
				_json.ICD10CODE = ui.item.YHCTCODE;
				_json.ICD10NAME = ui.item.YHCTNAME + ' [' + ui.item.ICD10NAME + '] ';
				_json.ICD10CODE1 = ui.item.ICD10CODE;
				_json.ICD10NAME1 = ui.item.ICD10NAME;
			} else {
				_json.ICD10CODE = ui.item.ICD10CODE;
				_json.ICD10NAME = ui.item.ICD10NAME;
			}
			//L2PT-96824
			if(opt.yhct == '1'){
				if(ui.item.YHCTCODE){
					if(benhphu_yhct){
						benhphu_yhct =  benhphu_yhct + ';' + ui.item.YHCTCODE + '-' + ui.item.YHCTNAME;
					}
					else{
						benhphu_yhct =   ui.item.YHCTCODE + '-' + ui.item.YHCTNAME;
					}
				}
				_json.ICD10CODE = ui.item.ICD10CODE;
				_json.ICD10NAME = ui.item.ICD10NAME;
				_json.ICD10CODE1 = ui.item.YHCTCODE;
				_json.ICD10NAME1 = ui.item.YHCTNAME;
			}
			if (typeof _benhchinh !== "undefined" && _benhchinh == ui.item.ICD10CODE) {
				DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính!");
				return;
			}
			var _data = jQuery("#" + _gridId).jqGrid('getRowData');
			for (var i = 0; i < _data.length; i++) {
				if (_data[i].ICD10CODE == ui.item.ICD10CODE || _data[i].ICD10CODE == ui.item.YHCTCODE) {
					DlgUtil.showMsg("Bệnh kèm theo đã có trong danh sách");
					return;
				}
			}
			var ids = $("#" + _gridId).getDataIDs();
			jQuery("#grdBP").jqGrid('addRowData', ids.length + 1, _json); // L2PT-130602
			return false;
		});
		// cau hinh: Chan doan kem theo ban dau
		var showCDKTBD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'CSBP_SHOW_CDKTBD');
		if (showCDKTBD == '1') {
			$("#txtCHANDOAN_KEMTHEO_BD").val(opt.chandoan_kt_bd);
		} else {
			$("#showCDKTBD").remove();
		}
		if (opt.type == 2) {
			$("#dvSOLUONGICD").show();
		}
	}
	function _bindEvent() {
		if (opt.benhphu != '') {
			var _benhphus = opt.benhphu.split(';');
			if (opt.benhphu1 != '' && opt.benhphu1 != "undefined" && opt.benhphu1 != undefined) {
				var _benhphus1 = opt.benhphu1.split(';');
			}
			var _vbp = [];
			var sl = '-';
//			var FOMAT_MA_BENHPHU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','FOMAT_MA_BENHPHU');
//			if(FOMAT_MA_BENHPHU == 1){
//				sl = '-';
//			}
//			else{
//				sl = '(';
//			}
			for (var i = 0; i < _benhphus.length; i++) {
				var _json = [];
				var _sbp = _benhphus[i].split(sl);
				var _sbp1 = [];
				if (opt.benhphu1 != '' && opt.benhphu1 != "undefined" && opt.benhphu1 != undefined && i < _benhphus1.length) {
					_sbp1 = _benhphus1[i].split(sl);
//				    return item !== _sbp[0];
				}
				arr = "";
				for (var j = 1; j < _sbp.length; j++) {
					if (j !== 1)
						arr = arr + '-' + _sbp[j];
					else
						arr = arr + _sbp[j];
				}
				if (opt.benhphu1 != '' && opt.benhphu1 != "undefined" && opt.benhphu1 != undefined  && _sbp1.length > 0) {
					arr1 = "";
					for (var k = 1; k < _sbp1.length; k++) {
						if (k !== 1)
							arr1 = arr1 + '-' + _sbp1[k];
						else
							arr1 = arr1 + _sbp1[k];
					}
					_json.ICD10CODE1 = _sbp1[0];
//				if(FOMAT_MA_BENHPHU == 1)
//				{
//					_json.ICD10CODE = _sbp[0];
//					_json.ICD10NAME = arr;
					_json.ICD10NAME1 = arr1;
				}
//				else{
				_json.ICD10CODE = _sbp[0];
				_json.ICD10NAME = arr;
//				}
				_vbp.push(_json);
			}
			GridUtil.fetchGridData(_gridId, _vbp); // L2PT-130602
			
		}
		$("#btnThoat").on("click", function(e) {
			parent.DlgUtil.close('dlgBPKT');
		});
		$("#btnLuu").on("click", function(e) {
			var _json = jQuery("#" + _gridId).jqGrid('getRowData');
			var _benhphu = "";
			var _benhphu1 = "";
			var _kytu_dacbiet = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_KYTU_DACBIET');
			var _kytu_dacbiets = _kytu_dacbiet.split(',');
			for (var i = 0; i < _json.length; i++) {
				_benhphu = _benhphu + _json[i].ICD10CODE + "-" + _json[i].ICD10NAME + ";";
				//L2PT-96824
				if(_json[i].ICD10CODE1)
					_benhphu1 = _benhphu1 + _json[i].ICD10CODE1 + "-" + _json[i].ICD10NAME1 + ";";
//				var FOMAT_MA_BENHPHU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','FOMAT_MA_BENHPHU');
//				if(FOMAT_MA_BENHPHU == 1){
//					_benhphu = _benhphu + _json[i].ICD10CODE + "-" + _json[i].ICD10NAME + ";";
//				}
//				else{
//					_benhphu = _benhphu + _json[i].ICD10NAME + "(" + _json[i].ICD10CODE + ");";
//				}
				for (var j = 0; j < _kytu_dacbiets.length; j++) {
					if (_json[i].ICD10NAME.includes(_kytu_dacbiets[j])) {
						DlgUtil.showMsg("Tên bệnh kèm theo không được chứa ký tự " + _kytu_dacbiets[j]);
						return;
					}
				}
			}
			_benhphu = _benhphu.substring(0, _benhphu.length - 1);
			_benhphu1 = _benhphu1.substring(0, _benhphu1.length - 1);
			console.log(benhphu_yhct);
			if(opt.yhct == '1'){
				_benhphu1 =benhphu_yhct;
			}
			EventUtil.raiseEvent("chinhsua_benhphu", {
				benhphu : _benhphu,
				benhphu1 : _benhphu1,
				chandoanKtBd : $("#txtCHANDOAN_KEMTHEO_BD").val(),
				type : opt.type
			}); //L2PT-19260
		});
		$("#" + _gridId).bind("CustomAction", function(e, act, rid) {
			if (act == 'del') {
				if(opt.yhct == '1'){
					var objIcd = $("#" + _gridId).jqGrid('getRowData', rid);
					var sql_par_k = [];
					sql_par_k.push({"name":"[0]", value: objIcd.ICD10CODE});
					var dsicd = jsonrpc.AjaxJson.getOneValue('GET.ICDYHCT', sql_par_k);
					var dsicds = dsicd.split(';');
					var _rkhoid = 0;
					if(dsicds.length > 0){ 
						for(var i = 0; i < dsicds.length; i++){
							var check_icd = dsicds[i];
							if(check_icd != '-')
								benhphu_yhct = benhphu_yhct.replaceAll(check_icd + ';','').replaceAll(';' + check_icd , '').replaceAll(check_icd,'')
						}
					}
				}
				$("#" + _gridId).jqGrid('delRowData', rid);
				var _json = jQuery("#" + _gridId).jqGrid('getRowData');
				GridUtil.fetchGridData(_json, _json);
			} 
			 // L2PT-130602 start
			else {
				//console.log("CustomAction act="+act+" rid="+rid);
				var _jsonGridData = jQuery("#"+_gridId).jqGrid('getRowData');
				var rowIds = $("#"+_gridId).jqGrid('getDataIDs');
				var _rowUp;
				var _ridDown;
				if(act == "up"){
					_rowUp = rid;
					_rowDown = rid - 1;
					if(rid == 0)
						return false;
				}else{
					if(_jsonGridData.length > 2)
						_rowUp = rid;
					else
						_rowUp = parseInt(rid)+ 1;
					if(_jsonGridData.length > 2)
						_rowDown = parseInt(rid) + 1;
					else
						_rowDown = parseInt(rid);
					if(rid == rowIds.length)
						return false;
				}
				doSwap(_rowUp, _rowDown);
				var jsonGridData = jQuery("#"+_gridId).jqGrid('getRowData');
				//jQuery("#"+_gridId).jqGrid("clearGridData");
				GridUtil.fetchGridData(_gridId, jsonGridData);
			}
			// L2PT-130602 end
			return false;
		});
	}
	// L2PT-130602 start
	function doSwap(_rowUp, _rowDown){
		var $row1 = $("#"+_rowUp), $row2 = $("#"+_rowDown),
		$next1 = $row1.next(".jqgrow"), $prev1 = $row1.prev(".jqgrow"),
        $next2, $prev2, doOneMove = false, movedOnce = false;

	    if ($row2.is($next1) || $prev1.is($row2)) {
	        doOneMove = true;
	    }
	
	    if ($prev1.length > 0 && !$prev1.is($row2)) {
	        $row2.detach().insertAfter($prev1);
	        movedOnce = true;
	    } else if ($next1.length > 0 && !$next1.is($row2)) {
	        $row2.detach().insertBefore($next1);
	        movedOnce = true;
	    }
	    
	    if (doOneMove && movedOnce) {
	        return;
	    }
	
	    $next2 = $row2.next();
	    $prev2 = $row2.prev();
	    if ($prev2.length > 0 && !$prev2.is($row1)) {
	        $row1.detach().insertAfter($prev2);
	    } else if ($next2.length > 0 && !$next2.is($row1)) {
	        $row1.detach().insertBefore($next2);
	    }
	}
	// L2PT-130602 end
}
