/*
Mã màn hình  : NTU02D055
File mã nguồn : NTU02D055_SoKetDieuTri.js
<PERSON>ục đích  : <PERSON><PERSON> sach SO KET DIEU TRI
Tham số vào : 
  khambenhid: id của khám bệnh
  BENHNHANID: id cua BENH NHAN
  BACSYDIEUTRIID: ID CUA BAC SY DIEU TRI  
Người lập trình	<PERSON> cập nhật  <PERSON>hi chú
HUONGPV	- 13092016 - Comment
*/
function NTU02D055_SKDT(opt) {
	var _gridSKDT = "grdPhieuSoKetDieuTri";
	var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
	var that = this;
	this.load = doLoad;
	var tiepnhanid = "";
	var selected_bnhiv_id;
	function doLoad() {
		//L2PT-119796
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'QLLAO_REMOVE_REQ_NGAYKT_DTRI') == "1") {
			$("#lblNGAYKT_DTRI_LAO").removeClass("required");
			$("#txtNGAYKT_DTRI_LAO").removeAttr("valrule");
			
		}
		this.validator = new DataValidator("divMain");
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		$("#txtMABENHNHAN").val(opt.mabenhnhan);
		$("#txtTENBENHNHAN").val(opt.tenbenhnhan);
		$("#txtMA_CSKCB").val(opt.hospital_id);
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.005",opt.khambenhid);
		if (data_ar != null && data_ar.length > 0) {
			var data = data_ar[0];
			$("#txtMA_THE_BHYT").val(data.MABHYT)1
			$("#txtSO_CCCD").val(data.SOCMTND);
		}
		
		initControl();
		bindEvent();
	}
	function initControl() {
		var _gridHeader = "BENHNHANID,BENHNHANID,0,0,t,l;BNHIVID,BNHIVID,0,0,t,l;"
				+ "Bắt đầu ĐT Lao,NGAYBD_DTRI_LAO,300,0,f,l;Kết thúc ĐT Lao,NGAYKT_DTRI_LAO,300,0,f,l;Ngày bắt đầu ĐT CTX,NGAY_BAT_DAU_DT_CTX,250,0,f,l;Ngày khẳng định HIV,NGAYKD_HIV,250,0,f,l";
		GridUtil.init(_gridSKDT, "100%", "210px", "", false, _gridHeader, false, {
			rowNum : 10,
			rowList : [ 10, 20, 30 ]
		});
		$("#" + _gridSKDT)[0].toggleToolbar();
		ComboUtil.getComboTag("cboMA_PHAC_DO_DIEU_TRI", "NTU.CBOPDDT", [ {
			"name" : "[0]",
			"value" : '2'
		} ], "", {}, "sql", "", function() {
			$('#cboMA_PHAC_DO_DIEU_TRI').SumoSelect({
				search : true,
				searchText : 'Tìm kiếm',
				okCancelInMulti : true,
				selectAll : true
			});
			// $('#cboMA_PHAC_DO_DIEU_TRI')[0].sumo.reload();
		});
		//L2PT-114377
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'QD_3176') == "0") {
			$("#cboPHACDO_DTRI_LAO option[value=26]").remove(); 
			$("#cboPHACDO_DTRI_LAO option[value=27]").remove(); 
		}
	}
	function loadGridData() {
		var sql_par=[];
		sql_par.push({"name":"[0]","value":opt.benhnhanid});
		sql_par.push({"name":"[1]","value":"LAO"});
		GridUtil.loadGridBySqlPage(_gridSKDT, "NTUD228.DSBN.2", sql_par);
	}
	function bindEvent() {
		loadGridData();
		GridUtil.setGridParam(_gridSKDT, {
			onSelectRow : function(id, selected) {
				GridUtil.unmarkAll(_gridSKDT);
				GridUtil.markRow(_gridSKDT, id);
				_viewDetailSKDT(id);
			}
		});
		//them moi
		_themmoi();
		//cap nhat
		_capnhat();
		//xoa
		_delete();
		//lam moi trang
		refreshPage();
		//dong trang
		closePage();
		/*$("#cboLOAI_DTRI_LAO").change(function() {
			if ($("#cboLOAI_DTRI_LAO").val() != '0') {
				$('#lblBAC_PD').addClass('required');
				$('#lblNGAYBD_DTRI_LAO').addClass('required');
			} else {
				$('#lblBAC_PD').removeClass('required');
				$('#lblNGAYBD_DTRI_LAO').removeClass('required');
				$('#lblNGAYKT_DTRI_LAO').removeClass('required');
			}
		});*/
		$("#cboMA_TINH_TRANG_DK").change(function() {
			if ($("#cboMA_TINH_TRANG_DK").val() && $("#cboMA_TINH_TRANG_DK").val().toString().includes('1') != '0') {
				$('#lblNGAY_XN_PCR').addClass('required');
				$('#lblNGAY_KQ_XN_PCR').addClass('required');
				$('#lblKQ_XN_PCR').addClass('required');
			} else {
				$('#lblNGAY_XN_PCR').removeClass('required');
				$('#lblNGAY_KQ_XN_PCR').removeClass('required');
				$('#lblKQ_XN_PCR').removeClass('required');
			}
		});
		$("#cboMA_XU_TRI").change(function() {
			if ($("#cboMA_XU_TRI").val() && $("#cboMA_XU_TRI").val().toString().includes('1') != '0') {
				$('#lblNGAY_BAT_DAU_XU_TRI').addClass('required');
				$('#lblNGAY_KET_THUC_XU_TRI').addClass('required');
				$('#lblSO_NGAY_CAP_THUOC_ARV').addClass('required');
			} else {
				$('#lblNGAY_BAT_DAU_XU_TRI').removeClass('required');
				$('#lblNGAY_KET_THUC_XU_TRI').removeClass('required');
				$('#lblSO_NGAY_CAP_THUOC_ARV').removeClass('required');
			}
		});
	}
	function _viewDetailSKDT(rowId) {
		FormUtil.clearForm("divDetail", "");
		var rowData = $('#' + _gridSKDT).jqGrid('getRowData', rowId);
		selected_bnhiv_id = rowData.BNHIVID;
		var sql_par1 = [ selected_bnhiv_id ];
		var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTUD228.TTBN", sql_par1.join('$'));
		rowDetail = dataDetail[0];
		FormUtil.setObjectToForm("", "", rowDetail);
		var str = rowDetail.MA_TINH_TRANG_DK.split(',');
		$('#cboMA_TINH_TRANG_DK')[0].sumo.reload();
		$('#cboMA_TINH_TRANG_DK option').each(function(i) {
			for (var j = 0; j < str.length; j++) {
				if ($(this).val() == str[j]) {
					$('#cboMA_TINH_TRANG_DK')[0].sumo.selectItem(i);
				}
			}
		});
		str = rowDetail.MA_XU_TRI.split(',');
		$('#cboMA_XU_TRI')[0].sumo.reload();
		$('#cboMA_XU_TRI option').each(function(i) {
			for (var j = 0; j < str.length; j++) {
				if ($(this).val() == str[j]) {
					$('#cboMA_XU_TRI')[0].sumo.selectItem(i);
				}
			}
		});
		str = rowDetail.MA_PHAC_DO_DIEU_TRI.split(',');
		// $('#cboMA_PHAC_DO_DIEU_TRI')[0].sumo.reload();
		$('#cboMA_PHAC_DO_DIEU_TRI option').each(function(i) {
			for (var j = 0; j < str.length; j++) {
				if ($(this).val() == str[j]) {
					$('#cboMA_PHAC_DO_DIEU_TRI')[0].sumo.selectItem(i);
				}
			}
		});
	}
	function _validate() {
		var valid = that.validator.validateForm();
		return valid;
	}
	//xu ly them moi
	function _themmoi() {
		$("#btnAdd").bindOnce("click", function() {
			var valid = _validate();
			if (valid) {
				var objData = new Object();
				FormUtil.setFormToObject("", "", objData);
				objData["BNHIVID"] = null;
				objData["BENHNHANID"] = opt.benhnhanid;
				objData["TIEPNHANID"] = opt.tiepnhanid;
				objData["KHAMBENHID"] = opt.khambenhid;
				objData["TYPE"] = "LAO";
				// objData["MA_PHAC_DO_DIEU_TRI"] = $("#cboMA_PHAC_DO_DIEU_TRI").val().toString();
				var _par;
				_par = [ JSON.stringify(objData) ];
				var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTUD228.INS", _par.join('$'));
				if (_result == 1) {
					FormUtil.clearForm("divDetail", "");
					DlgUtil.showMsg("Thêm mới thành công phiếu điều trị LAO");
					loadGridData();
				} else if (_result == 0) {
					DlgUtil.showMsg("Thêm mới thất bại phiếu điều trị LAO");
				}
			}
		}, 500)
	}
	//cap nhat
	function _capnhat() {
		$("#btnEdit").bindOnce("click", function() {
			if (selected_bnhiv_id) {
				var valid = _validate();
				if (valid) {
					var objData = new Object();
					FormUtil.setFormToObject("", "", objData);
					objData["BNHIVID"] = selected_bnhiv_id;
					objData["BENHNHANID"] = opt.benhnhanid;
					objData["TIEPNHANID"] = opt.tiepnhanid;
					objData["KHAMBENHID"] = opt.khambenhid;
					objData["TYPE"] = "LAO";
					var _par;
					_par = [ JSON.stringify(objData) ];
					var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTUD228.INS", _par.join('$'));
					if (_result == 1) {
						FormUtil.clearForm("divDetail", "");
						DlgUtil.showMsg("Cập nhật thành công phiếu điều trị LAO");
						loadGridData();
					} else if (_result == 0) {
						DlgUtil.showMsg("Cập nhật thất bại phiếu điều trị LAO");
					}
				}
			} else {
				DlgUtil.showMsg("Chưa chọn phiếu  để cập nhật");
			}
		}, 500)
	}
	//xoa
	function _delete() {
		$("#btnDelete").bindOnce("click", function() {
			if (selected_bnhiv_id) {
				DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu ?", function(flag) {
					if (flag) {
						var objData = new Object();
						FormUtil.setFormToObject("", "", objData);
						objData["BNHIVID"] = selected_bnhiv_id;
						objData["ACTION"] = 'DEL';
						var _par;
						_par = [ JSON.stringify(objData) ];
						var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTUD228.INS", _par.join('$'));
						if (_result == 1) {
							FormUtil.clearForm("divDetail", "");
							DlgUtil.showMsg("Xóa phiếu thành công");
							loadGridData();
						}
					}
				});
			} else {
				DlgUtil.showMsg("Bạn chưa chọn phiếu để xóa!");
			}
		}, 500)
	}
	function refreshPage() {
		$("#btnRefesh").on("click", function(e) {
			FormUtil.clearForm("divDetail", "");
			loadGridData();
		})
	}
	// ham xu ly close trang
	function closePage() {
		$("#btnClose").on("click", function(e) {
			parent.DlgUtil.close("divDlgHIV");
		})
	}
}