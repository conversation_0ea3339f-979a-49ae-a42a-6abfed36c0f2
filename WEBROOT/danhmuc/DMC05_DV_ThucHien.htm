<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>


<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../danhmuc/DMC05_DV_ThucHien.js?v=2022"></script>

<!--L2PT-33245-->
<script type="text/javascript" src="../common/script/xlsx/xlsx.full.min.js"></script>
<script type="text/javascript" src="../common/script/jszip/FileSaver.js"></script>


<div width="100%" id="divMain" class="container">
	<!--  <legend>QUẢN LÝ DM Cau Hinh</legend>-->
	<!--  <div class="form-inline border-group-1 mgt5 mgb5">
     	<div class="col-md-12 mgt5 mgb5">
	     	<div class="col-md-1 low-padding">
			</div>

			<div class="col-md-3 low-padding">
			<label for="myalue" style="vertical-align: middle"></label>
			</div>
		</div>		
     </div>  -->
	<div class="form-inpfo mgt3" style="text-align: right;">
		<label for="myalue"
			style="font-weight: 600 !important; margin-bottom: 0px;"></label>
	</div>
	<div class="col-md-12 low-padding mgt5">
		<div class="col-md-6 low-padding mgt-10">
			<table id="grdDanhSachCH"></table>
			<div id="pager_grdDanhSachCH"></div>
		</div>
		<div class="col-md-6 low-padding"
			style="padding-left: 15px !important;">
			<div id="inputForm" class="panel panel-default">
				<div class="panel-heading">Kết quả xét nghiệm</div>
				<div class="panel-body">
					<div class="col-md-12 low-padding  mgt5">
						<div class="col-md-12  ">
							<div class="col-md-3 low-padding required">
								<label class="">Mã DV</label>
							</div>
							<div class="col-md-3 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtMADICHVU"
									valrule="Mã DV,required|max_length[30]" name="txtMADICHVU"
									title="" style="width:100%;" disabled>
							</div>
							<div class="col-md-2 low-padding required">
								<label class="mgl5">Nhóm</label>
							</div>
							<div class="col-md-2 low-padding">
								<select class="form-control input-sm kb-i-col-m "
									id="cboGOIDICHVUID" valrule="Nhóm,required"
									style="width: 100%;" disabled>
								</select>
							</div>
							<div class="col-md-2 low-padding">
								<label class="mgl5"><input type="checkbox"
									id="chkLOAIDICHVU" value="" disabled> Là nhóm</span></label>
							</div>
						</div>

						<div class="col-md-12   ">

							<div class="col-md-3 low-padding required">
								<label class="">Tên dịch vu</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtTENDICHVU"
									valrule="Tên dịch vu,required|max_length[1999]"
									name="txtTENDICHVU" title="" style="width:100%;" disabled>
							</div>

						</div>

						<div class="col-md-12   ">

							<div class="col-md-3 low-padding">
								<label class="">Giới hạn trên</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtGIATRILONNHAT"
									valrule="Giới hạn trên,max_length[30]" name="txtGIATRILONNHAT"
									title="" style="width:100%;" disabled>
							</div>

						</div>

						<div class="col-md-12   ">

							<div class="col-md-3 low-padding">
								<label class="">Giới hạn dưới</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtGIATRINHONHAT"
									valrule="Giới hạn dưới,max_length[30]" name="txtGIATRINHONHAT"
									title="" style="width:100%;" disabled>
							</div>

						</div>

						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Đơn vị</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtDONVI"
									valrule="Đơn vị,max_length[30]" name="txtDONVI" title=""
									style="width:100%;" disabled>
							</div>

						</div>

						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Công thức tính tự động</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtCONGTHUC"
									valrule="Công thức tính tự động,max_length[100]"
									name="txtCONGTHUC" title="" style="width:100%;" disabled>
							</div>

						</div>

						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Giá trị bình thường</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtKHOANGGIATRI"
									valrule="Giá trị bình thường,max_length[100]"
									name="txtKHOANGGIATRI" title="" style="width:100%;" disabled>
							</div>

						</div>

						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Thứ tự in trên mẫu BYT</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtTHUTUINMAUBYT"
									valrule="Thứ tự in,is_natural|max_length[10]"
									name="txtTHUTUINMAUBYT" title="" style="width:100%;" disabled>
							</div>

						</div>
						
						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Thứ tự thực hiện</label>
							</div>

							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtTHUTUTHUCHIEN"
									valrule="Thứ tự thực hiện,is_natural|max_length[10]"
									name="txtTHUTUTHUCHIEN" title="" style="width:100%;" disabled>
							</div>

						</div>
						
						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Mã chỉ số</label>
							</div>
							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtMACHISO"
									valrule="Mã chỉ số,max_length[100]"
									name="txtMACHISO" title="" style="width:100%;" disabled>
							</div>
						</div>
						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Mã chỉ số BYT</label>
							</div>
							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtMA_CHI_SO"
									valrule="Mã chỉ số BYT,max_length[100]"
									name="txtMA_CHI_SO" title="" style="width:100%;" disabled>
							</div>
						</div>
						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Tên chỉ số</label>
							</div>
							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtTENCHISO"
									valrule="Tên chỉ số,max_length[500]"
									name="txtTENCHISO" title="" style="width:100%;" disabled>							 
							</div>
						</div>
						<div class="col-md-12  ">

							<div class="col-md-3 low-padding">
								<label class="">Số lượng phim</label>
							</div>
							<div class="col-md-3 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtSOLUONGPHIM"
									valrule="Số lượng|integer" maxlength="2"
									name="txtSOLUONGPHIM" title="" style="width:100%;" disabled>
							</div>
							<div class="col-md-3 low-padding">
								<label class="">Loại phim</label>
							</div>
							<div class="col-md-3 low-padding">
								<select class="form-control input-sm kb-i-col-m "
									id="cboLOAIPHIM" 
									style="width: 100%;" disabled>
									<option value="" selected="selected"></option>
									<option value="3x4">3x4</option>									
									<option value="8x10">8x10</option>
									<option value="10x12">10x12</option>
									<option value="11x14">11x14</option>
									<option value="20x25">20x25</option>
									<option value="25x30">25x30</option>
									<option value="35x43">35x43</option>
								</select>
							</div>
						</div>
						<div class="col-md-12  ">
							<div class="col-md-3 low-padding">
								<label class="">Quy trình XN</label>
							</div>
							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtQUYTRINH_XN"
									name="txtQUYTRINH_XN" maxlength="2000" title="" style="width:100%;" disabled>
							</div>
						</div>
						<div class="col-md-12  ">
							<div class="col-md-3 low-padding">
								<label class="">Số kỹ thuật</label>
							</div>
							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m"
									style="width: 100%;" id="txtSOKYTHUAT"
									name="txtSOKYTHUAT" valrule="Số kỹ thuật,interger|greater_than[0]|max_length[5]" title="" style="width:100%;" disabled>
							</div>
						</div>
						<div class="col-md-12  ">
							<div class="col-md-3 low-padding">
								<label class=""></label>
							</div>
					     	
					     	<div class="col-md-9 low-padding">
								<label class="mgl5"><input type="checkbox"
									id="chkSTATUS" value="" disabled>Sử dụng</span></label>
							</div>

						</div>

						<div class="col-xs-12 low-padding mgt10"
							style="text-align: center;">
							<button class="btn btn-sm btn-primary" id="btnGTBT1" disabled>
								<span class=""></span> Sửa giá bình thường
							</button>

							<button class="btn btn-sm btn-primary" id="btnNMKQ" disabled>
								<span class=""></span> Nhóm mẫu kết quả (chọn)
							</button>

							<button class="btn btn-sm btn-primary" id="btnMKQ" disabled>
								<span class=""></span>Mẫu kết quả (chèn tự động)
							</button>
						</div>


					</div>

					<div class="col-md-12 low-padding mgt5 mgb15">
						<div class="col-md-12 low-padding" style="text-align: center;">
							<button type="button" class="btn btn-sm btn-primary" id="btnThem">
								<span class="glyphicon glyphicon-pencil"></span> Thêm
							</button>
							<button type="button" class="btn btn-sm btn-primary" id="btnSua"
								disabled>
								<span class="glyphicon glyphicon-edit"></span> Sửa
							</button>
							
							<button type="button" class="btn btn-sm btn-primary" id="btnXoa"
								disabled>
								<span class="glyphicon glyphicon-remove"></span> Xóa
							</button>
							 
							<button type="button" class="btn btn-sm btn-primary" id="btnLuu"
								disabled>
								<span class="glyphicon glyphicon-floppy-disk"></span> Lưu
							</button>
							<button type="button" class="btn btn-sm btn-primary" id="btnHuy"
								disabled>
								<span class="glyphicon glyphicon-remove-circle"></span> Hủy
							</button>
							
							<!--L2PT-33245-->
							<button type="button" class="btn btn-sm btn-primary" id="btnXuatExcel">
								<span class="glyphicon glyphicon-remove-circle"></span> Xuất DM
							</button>
						</div>

					</div>
				</div>
			</div>

		</div>
	</div>

	<div id="popupId"></div>
	<div id="divDlg" style="width: 100%; display: none">
		<iframe src="" id="ifmView"
			style="width: 700px; height: 200px; border: dotted 1px blue"
			frameborder="0"></iframe>
	</div>
</div>
<script>
	var paramInfo = CommonUtil.decode('{paramData}');
	var opt = [];

	var schema = '{db_schema}';
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	//var lang= "vi";
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';

	initRest(uuid, "/vnpthis");

	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;

	_opts.loainhomdv = paramInfo.loainhomdv;

	var DS = new dSCHList(_opts);
	DS.load(hospital_id);
</script>