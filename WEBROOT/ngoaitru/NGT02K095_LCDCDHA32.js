 /*
FeatureId:NGT02K028_HienthiDanhSachKham
Creator: ManhNV
CreateDate: 10/09/2016 
Desc: <PERSON><PERSON> sách chờ bệnh nhân hiển thị màn hình chờ
*/
function ThongBaoBenhNhan(opt) {
	this.load=doLoad;
	var _opt = opt;	
	var _SQL=["NGT02K095.LCD"];
	var count = 0;
	
	function doLoad() {
		_initControl();
	}
	
	function _initControl(){			
		document.onwebkitfullscreenchange = fullscreenChanged;
	    document.documentElement.onclick = goFullscreen;
	    document.onkeydown = goFullscreen;
	    
	    $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K095_LCDCDHA32&showMode=dlg");
	    
	    
		$('#phongkham').text(_opt.subdept_name); 
		$('#lblNGAY').text(jsonrpc.AjaxJson.getSystemDate('dd/mm/yyyy')); 
		
		_loadData();
		setInterval(function(){
			_loadData();
		}, 5000);// set 30s
	}
	
	function _loadData(){
		_resetData();
		var th = 2; 
		var sql_par=[_opt.phongid];
		var imgTag = document.getElementById('imgLogo');
		imgTag.src = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_BM2_URL'); 
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(_SQL[0],sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			if(data_ar[0].LOAI == "1" ){
				$('#bntieptheo1').text(_chuanhoastt(data_ar[0].SOTHUTU) + ' - ' + data_ar[0].TENBENHNHAN); 
				$('#mabntieptheo1').text(data_ar[0].MABENHNHAN); 
			}
			if(data_ar[0].LOAI == "1" && data_ar.length>1 ){
				$('#bntieptheo2').text(_chuanhoastt(data_ar[1].SOTHUTU) +' - '+ data_ar[1].TENBENHNHAN); 
				$('#mabntieptheo2').text(data_ar[1].MABENHNHAN); 
			}
			if(data_ar[0].LOAI !== "1" ){
				$('#bntieptheo2').text(_chuanhoastt(data_ar[0].SOTHUTU) + ' - '+data_ar[0].TENBENHNHAN); 
				$('#mabntieptheo2').text(data_ar[0].MABENHNHAN); 
				th = 1; 
			}
			for(var i = 3; i < 8 ; i++){
				if((count+th) == data_ar.length || data_ar.length <2){
					count =0;
					break;
				}	
				$('#sttbntieptheo'+(i)).text(_chuanhoastt(data_ar[count+th].SOTHUTU)); 
				$('#bntieptheo'+(i)).text(data_ar[count+th].TENBENHNHAN); 
				$('#mabntieptheo'+(i)).text(data_ar[count+th].MABENHNHAN); 
				count++;
			}
		}
	}
	
	function _resetData(){
		for(var i = 1; i < 9 ; i++){
			$('#sttbntieptheo'+(i)).text(''); 
			$('#bntieptheo'+(i)).text(''); 
			$('#mabntieptheo'+(i)).text(''); 
		}
	}
	function _chuanhoastt(stt){
		var sttChuan = stt;
		if(stt.length ==1){
			sttChuan = '00'+sttChuan;
		}
		if(stt.length ==2){
			sttChuan = '0'+sttChuan;
		}
		return sttChuan;
	}
	
	function goFullscreen() {
        // Must be called as a result of user interaction to work
		var isFirefox = typeof InstallTrigger !== 'undefined';
		
        mf = document.getElementById("main_frame");
        
        if(!isFirefox){
        	 mf.webkitRequestFullscreen();
//        	 mf.style.display="";
        }else{
        	 mf.mozRequestFullScreen();
        }
       
       
    }
    function fullscreenChanged() {
        if (document.webkitFullscreenElement == null) {
            mf = document.getElementById("main_frame");
            mf.style.display="none";
        }
    }
}

