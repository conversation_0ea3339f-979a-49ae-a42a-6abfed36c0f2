<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT041_PHIEUTHUTIEN_A4_1174" language="groovy" pageWidth="226" pageHeight="453" columnWidth="198" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" isIgnorePagination="true" uuid="3b58cf30-f4f9-4732-9083-3a2767be9a76">
	<property name="ireport.zoom" value="2.143588810000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="phieuthuid" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call ngt041_phieuthutien_a4_1174($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{phieuthuid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="MA_PHIEUTHU" class="java.lang.String"/>
	<field name="NGAY_IN" class="java.lang.String"/>
	<field name="NGUOI_IN" class="java.lang.String"/>
	<field name="TEN_BENHNHAN" class="java.lang.String"/>
	<field name="NAM_SINH" class="java.math.BigDecimal"/>
	<field name="GIOI_TINH" class="java.lang.String"/>
	<field name="DIA_CHI" class="java.lang.String"/>
	<field name="TEN_DICHVU" class="java.lang.String"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="TIEN_MIENGIAM" class="java.lang.String"/>
	<field name="TIEN_THANHTOAN" class="java.math.BigDecimal"/>
	<field name="MA_BENHAN" class="java.lang.String"/>
	<field name="NGAY_THU" class="java.lang.String"/>
	<field name="NGUOI_THU" class="java.lang.String"/>
	<variable name="THANH_TIEN_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="91" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="46" height="18" uuid="7356438d-43c8-4041-8c14-4d73115d1fb0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[PKVA]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="46" y="0" width="152" height="18" uuid="bef7c7df-9622-4959-adc4-a70ab98fc78b"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["HĐ KHÁM BỆNH " + ($F{MA_PHIEUTHU} == null ? "" : $F{MA_PHIEUTHU})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="18" width="198" height="18" uuid="ccdfea0b-69a8-4b04-8acc-b7a663819823"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["" + ($F{NGAY_IN} == null ? "" : $F{NGAY_IN}) + " - " + ($F{NGUOI_IN} == null ? "" : $F{NGUOI_IN})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="38" width="198" height="1" uuid="46917279-d0de-4b74-a102-849ea46ab377"/>
			</line>
			<textField>
				<reportElement positionType="Float" x="0" y="39" width="198" height="18" uuid="87d2f71b-cf80-4d9c-8eaf-458fee6376a7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{TEN_BENHNHAN} == null ? "" : $F{TEN_BENHNHAN}) +
" - " + ($F{NAM_SINH} == null ? "" : $F{NAM_SINH}) +
" - " + ($F{GIOI_TINH} == null ? "" : $F{GIOI_TINH})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="57" width="198" height="18" uuid="8a548d63-f6e8-4265-a455-acac23999efd"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: " + ($F{DIA_CHI} == null ? "" : $F{DIA_CHI})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="87" width="198" height="1" uuid="1e79be74-096c-4ab2-9db3-091ccaf4064c"/>
			</line>
		</band>
	</title>
	<detail>
		<band height="36" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="16" height="18" uuid="071de899-f5e6-495a-956f-01a9369b3998"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT} + "/"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="16" y="0" width="182" height="18" uuid="76c2157f-3af2-4f08-8df4-17b246e90d54"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DICHVU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="88" y="18" width="110" height="18" uuid="c2f3bb31-5c31-48b5-8500-234b768c6d50"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANH_TIEN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="159" splitType="Stretch">
			<line>
				<reportElement positionType="Float" x="0" y="3" width="198" height="1" uuid="1698ad47-a121-40b0-84d9-b8834366653c"/>
			</line>
			<staticText>
				<reportElement positionType="Float" x="0" y="4" width="129" height="16" uuid="91ef335d-bc32-4cd9-8f42-ddbccaeea0ce"/>
				<box rightPadding="5"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Tổng tiền:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="129" y="20" width="69" height="16" uuid="ddff9513-55cb-43e8-b9a7-ba1db0874cd8"/>
				<box rightPadding="15"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEN_THANHTOAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="129" y="4" width="69" height="16" uuid="dad3ffba-9ffb-4e03-b6d6-a2a2900d75b5"/>
				<box rightPadding="15"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{THANH_TIEN_1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="20" width="129" height="16" uuid="93f8f15c-b5fc-46d2-ac01-171eab7e6951"/>
				<box rightPadding="5"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Tổng tiền phải thanh toán:]]></text>
			</staticText>
			<componentElement>
				<reportElement positionType="Float" x="50" y="36" width="98" height="25" uuid="59cfdf95-18b6-4d9d-983d-fb73176a5455"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$F{MA_BENHAN}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="50" y="61" width="98" height="14" uuid="41e97e1a-3595-4ae2-aa95-0bd9164c86a8"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BENHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="75" width="198" height="14" uuid="e20bcbad-39f2-4b55-a1ca-5ca71eb7fa97"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày thu: " + ($F{NGAY_THU} == null ? "" : $F{NGAY_THU})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="89" width="198" height="14" uuid="aec54d29-b49a-4d74-8a9f-d67674a2032d"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Thu ngân"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="103" width="198" height="14" uuid="cc2be2bd-601a-4f6f-a0fd-d34f818505b4"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["(Ký tên)"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="145" width="198" height="14" uuid="1806eac9-b9f3-4017-b1a9-f9e6dd1ff2ab"/>
				<box rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOI_THU} == null ? "" : $F{NGUOI_THU}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
