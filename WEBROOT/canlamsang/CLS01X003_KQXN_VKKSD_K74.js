/*
 * Nhập kết quả xét nghiệm
 * 
 * VietDA	04/08/2016		tạo mới
 *  
 */

function KetQuaXetNghiem2(opt){
	this.load = doLoad;

	var bnGioiTinh = "Nam";
	var bnTuoi = 0;
	var dsThem = [];
	var dsOld = [];
	var dsThemAll = [];
	var dichvuid ='';
	var amtinh ='';
	var duongtinh ='';
	var dichvukhambenhid ='';
	var vikhuanid ='';
	var vikhuanid_check  ='';
	var tenvikhuan ='';
	
	var _grd_DS_KS_HEADER = "VIKHUAN_ID,VIKHUAN_ID,80,0,f,l;" +
	"KHANGSINHID,KHANGSINHID,150,0,f,l;" +
	"KHANGSINHID_CHECK,KHANGSINHID_CHECK,150,0,f,l;" +
	"Mã kháng sinh,MA_KHANGSINH,150,0,f,l;" +
	"Tên kháng sinh,TEN_KHANGSINH,120,0,f,l;" +
	"S,S,120,0,f,l;" +
	"I,I,120,0,f,l;" +
	"R,R,120,0,f,l;" +
	"USER_NAME,USER_NAME,120,0,f,l;" +
	"NGAYTAO,NGAYTAO,120,0,f,l";
	var _grd_DS_KS_NAME = "grdDSKhangSinh";
	
	var _grd_DS_VK_HEADER = "Chọn,'SELECT_RADIO',50,0,f,l;" +
	"VIKHUAN_ID,VIKHUAN_ID,80,0,t,l;" +
	"vkidck,VIKHUANID_CHECK,50,0,t,l;" +
	"Tên vi khuẩn,TEN_VIKHUAN,120,0,f,l;" +
	"Ngày tạo,NGAYTAO,120,0,f,l;" +
	"Người tạo,USER_NAME,120,0,f,l";
	var _grd_DS_VK_NAME = "grdDSViKhuan";
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		
		
		initControl();
		bindEvent();
		
		$("#txtNhanXet").focus();

		/* nhận dữ liệu từ form cha Danh sách bệnh phẩm */
		$("#hdfIDMauBenhPham").val(opt._idmaubenhpham);
//		$("#hdfIDvikhuan").val(opt._vikhuanid);
		$("#hdfIDShowBtnEditTime").val(opt._showNutSuaNgayTraKQ);
		$("#hdfIDDichVuKB").val(opt._iddichvukb);
		$("#hdfIDDichVuID").val(opt._iddichvu);
		console.log("id mau benh pham="+opt._idmaubenhpham+", _showNutSuaNgayTraKQ="+opt._showNutSuaNgayTraKQ+", _show: "+opt._show);
//		if($("#hdfIDShowBtnEditTime").val()=='1'){
//			if(opt._show =='true'){
//				$("#btnEditTimePopUpX").show();	
//			}else{
//				$("#btnEditTimePopUpX").hide();	
//			}
//		}else{
//			$("#btnEditTimePopUpX").hide();	
//		}
		loadThongTinBenhNhan();
		loadDanhSachKetQua();
	}

	function initControl(){
		$.extend($.jgrid.inlineEdit, {
			keys: true
		});
		//nghiant 26262017
		
		var $grid = $("#grdKetQuaXetNghiem");

		$grid.jqGrid({
			datatype: 'local',
			colNames: ['','Mã xét nghiệm','Tên xét nghiệm','Kết quả','Âm tính','Dương tính','Trạng thái','Bất thường','DICHVUID',
			           'KETQUACLSID','DICHVUTHUCHIENID','DICHVUKHAMBENHID','TRANGTHAIKETQUA','VIKHUAN_ID','VIKHUANID_CHECK','KHANGSINHID'],
			colModel: [
			    {name: 'ICON', width: 20, search: false },
				//{name: 'TENCHIDINH', width: 150 },
				{name: 'MADICHVU', width: 100},
				{name: 'TENDICHVU', width: 200},
				//{name: 'TEN_VIKHUAN', width: 150 },
				{name: 'GIATRI_KETQUA', editable: true},
				{name: 'AMTINH', width: 80},
				{name: 'DUONGTINH', width: 80},
				{name: 'TENTRANGTHAI', width: 70},
				{name: 'BATTHUONG', hidden:true},
				{name: 'DICHVUID', hidden:true},
				{name: 'KETQUACLSID', hidden:true},
				{name: 'DICHVUTHUCHIENID', hidden:true},
				{name: 'DICHVUKHAMBENHID', hidden:true},
				{name: 'TRANGTHAIKETQUA', hidden:true},
				{name: 'VIKHUAN_ID', hidden:true},
				{name: 'VIKHUANID_CHECK', hidden:true},
				{name: 'KHANGSINHID', hidden:true}
			],
			rowNum: 50,
			rowList: [20, 50, 100],
			pager: '#pager_grdKetQuaXetNghiem',
			gridview: true,
			ignoreCase: true,
			rownumbers: true,
			viewrecords: true,
			sortorder: 'desc',
			width:"100%",
			autowidth:true,
			editurl: 'clientArray',
			caption: 'Danh sách kết quả',
			onSelectRow: function (rowid,status) {
				GridUtil.unmarkAll("grdKetQuaXetNghiem");
				GridUtil.markRow("grdKetQuaXetNghiem", rowid);
				GridUtil.setEditRow("grdKetQuaXetNghiem",rowid);
				$grid.jqGrid("editRow", rowid, { keys: true });

				$("input[name='GIATRI_KETQUA']").focusout(function() {
					$grid.jqGrid('saveRow',rowid, 'clientArray');
				});
			}
		});          
		$grid.jqGrid('navGrid', '#pager_grdKetQuaXetNghiem', {edit: false, add: false, del: false});	    
//		$grid.jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		
		$('#gview_grdKetQuaXetNghiem').find('.ui-jqgrid-bdiv').attr("style","height: 45px !important");
		GridUtil.setWidthPercent("grdKetQuaXetNghiem","100%");
		
		//vi khuan 
		var $gridVK = $("#grdDSViKhuan");

		$gridVK.jqGrid({
			datatype: 'local',
			colNames: ['VIKHUAN_ID','VIKHUANID_CHECK','Tên vi khuẩn','Mã VK','Mã nhóm','Ngày tạo','Người tạo'],
			colModel: [
			    
//			    { name: 'SELECT', width: 20,
//                    formatter: function radio(cellValue, option) {
//                        return '<input type="radio" name="radio_' + option.gid +       '"  />';
//                    } 
//                },
				{name: 'VIKHUAN_ID', width: 100,hidden:true},
				{name: 'VIKHUANID_CHECK', width: 100,hidden:true},
				{name: 'TEN_VIKHUAN',  width: 186},
				{name: 'MA_VIKHUAN', width: 60},
				{name: 'MA_NHOM', width: 60},
				{name: 'NGAYTAO', width: 100},
				{name: 'USER_NAME', width: 120}
				
				
			],
			rowNum: 50,
			rowList: [20, 50, 100],
			pager: '#pager_grdDSViKhuan',
			gridview: true,
			ignoreCase: true,
			rownumbers: true,
			viewrecords: true,
			sortorder: 'desc',
			width:"100%",
			autowidth:true,
			editurl: 'clientArray',

			caption: 'Danh sách vi khuẩn',
//			onSelectRow: function (rowid,status) {
//				GridUtil.unmarkAll("grdDSViKhuan");
//				GridUtil.markRow("grdDSViKhuan", rowid);
//				GridUtil.setEditRow("grdDSViKhuan",rowid);
//				
//			}
		});          
		$gridVK.jqGrid('navGrid', '#pager_grdDSViKhuan', {edit: false, add: false, del: false});	    
		$gridVK.jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		
		$('#gview_grdDSViKhuan').find('.ui-jqgrid-bdiv').attr("style","height: 180px !important");
		GridUtil.setWidthPercent("grdDSViKhuan","100%");
		
		//end vi khuan 
		
		//khang sinh 
		var $gridKS = $("#grdDSKhangSinh");

		$gridKS.jqGrid({
			datatype: 'local',
			colNames: ['VIKHUAN_ID','KHANGSINHID','KHANGSINHID_CHECK','Mã KS','Mã NKS','Tên kháng sinh','SIR','S','I','R','Ngày tạo','Người tạo'],
			colModel: [
			    
			   
				{name: 'VIKHUAN_ID', width: 100,hidden:true},
				{name: 'KHANGSINHID', width: 100,hidden:true},
				{name: 'KHANGSINHID_CHECK', width: 100,hidden:true},
				{name: 'MA_KHANGSINH', width: 50},
				{name: 'MA_NKS', width: 50},
				{name: 'TEN_KHANGSINH',  width: 200},
				{name: 'SIR',  width: 30},
				
				
				{name: 'S',  width: 50,
					formatter: "checkbox", formatoptions: { disabled: false},
	                edittype: "checkbox", editoptions: {value: "1:0", defaultValue: "0"}
				},
				{name: 'I',  width: 50,
					formatter: "checkbox", formatoptions: { disabled: false},
	                edittype: "checkbox", editoptions: {value: "1:0", defaultValue: "0"}
				},
				{name: 'R',  width: 50,
					formatter: "checkbox", formatoptions: { disabled: false},
	                edittype: "checkbox", editoptions: {value: "1:0", defaultValue: "0"}
				},
				
//				{name: 'S',  width: 50},
//				{name: 'I',  width: 50},
//				{name: 'R',  width: 50},
				{name: 'NGAYTAO', width: 94},
				{name: 'USER_NAME', width: 150},
				
				
			],
			rowNum: 50,
			rowList: [20, 50, 100],
			pager: '#pager_grdDSKhangSinh',
			gridview: true,
			multiselect: true,
			ignoreCase: true,
			rownumbers: true,
			viewrecords: true,
			sortorder: 'desc',
			width:"100%",
			autowidth:true,
			editurl: 'clientArray',

			caption: 'Danh sách kháng sinh',
			onSelectRow: function (rowid,status) {
				GridUtil.unmarkAll("grdDSKhangSinh");
				GridUtil.markRow("grdDSKhangSinh", rowid);
				GridUtil.setEditRow("grdDSKhangSinh",rowid);
//				$("input[name='SIR']").focusout(function() {
//					$grid.jqGrid('saveRow',rowid, 'clientArray');
//				});
				
			}

		});          
		$gridKS.jqGrid('navGrid', '#pager_grdDSKhangSinh', {edit: true, add: false, del: false});	    
		$gridKS.jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		
		$('#gview_grdDSKhangSinh').find('.ui-jqgrid-bdiv').attr("style","height: 180px !important");
		GridUtil.setWidthPercent("grdDSKhangSinh","100%");
		//End khang sinh
		
//		GridUtil.init(_grd_DS_KS_NAME,"100%","100px","Danh sách kháng sinh",true, _grd_DS_KS_HEADER,false,{ rowNum: 500,rowList: [500, 1000, 2000]});
		//GridUtil.init(_grd_DS_VK_NAME,"100%","100px","Danh sách vi khuẩn",false, _grd_DS_VK_HEADER,false,{ rowNum: 500,rowList: [500, 1000, 2000]});
		
		// ẩn hiện các nút xử lý
		if(type=="read"){
			$("#btnLuu").attr('disabled','true');
			$("#btnTraKetQua").attr('disabled','true');
//			$("#btnInKQ").attr('disabled','true');
		} 
		else if(type=="update"){
			$("#btnLuu").removeAttr('disabled');
			$("#btnInKQSoiNhuom").removeAttr('disabled');
			$("#btnInKQ").removeAttr('disabled');
			$("#btnTraKetQua").removeAttr('disabled');
		}
		if(user_group_id=="0" || user_group_id=="1") {
//			$("#btnTraKetQua").removeAttr('disabled');
//			$("#btnHuyKetQua").removeAttr('disabled');
		}
		else if(userRights=="TK") {
//			$("#btnTraKetQua").removeAttr('disabled');
//			$("#btnHuyKetQua").removeAttr('disabled');
		}
		else if(userRights=="KTV"){
//			$("#btnTraKetQua").attr('disabled','true');
//			$("#btnHuyKetQua").attr('disabled','true');
		}
		//nghiant 28082017
		 if(opt._rolePTH != "true" ){
			$("#btnTraKetQua").attr('disabled','true');
//			$("#btnHuyKetQua").attr('disabled','true');
			$("#btnLuu").attr('disabled','true');
//			$("#btnEditTimePopUpX").hide();	
			console.log("abcd=================================");
		}
		//end nghiant 28082017 
	}
	
	function bindEvent(){
		$("#grdKetQuaXetNghiem").jqGrid("setGridParam", {
			gridComplete: function(){
	    		var rowids = $("#grdKetQuaXetNghiem").getDataIDs();
	    		if(rowids!=""){
	    			for(var i=0; i<rowids.length; i++){
		    			var rowid = rowids[i];
		    			var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData', rowid);
						
		    			if(row.VIKHUANID_CHECK != null && row.VIKHUANID_CHECK != "" ){
		    				$("#grdKetQuaXetNghiem").setSelection(rowid, true);	
		    			}
		    			console.log("VKID_CHECK: "+row.VIKHUANID_CHECK+", amtinh: "+row.AMTINH +", duongtinh: "+row.DUONGTINH);
		    			amtinh =row.AMTINH;
		    			duongtinh =row.DUONGTINH;
		    			vikhuanid_check=row.VIKHUANID_CHECK;
		    			 dichvuid = row.DICHVUID;
						 dichvukhambenhid = row.DICHVUKHAMBENHID;
		    			// hiển thị icon trạng thái
		    			var checkAmTinh = '';
		    			var checkDuongTinh = '';
		    			checkAmTinh = '<center><input type="checkbox" id="chkAmTinh" class="VKAmTinh" ></center>';
		    			checkDuongTinh = '<center><input type="checkbox" id="chkDuongTinh" class="VKDuongTinh"></center>';
						$("#grdKetQuaXetNghiem").jqGrid ('setCell', rowid, 5, checkAmTinh);
						$("#grdKetQuaXetNghiem").jqGrid ('setCell', rowid, 6, checkDuongTinh);
						if(row.AMTINH ==null || row.AMTINH =="0" || row.AMTINH ==""){
							 $("#chkAmTinh").prop( "checked", false );
						}else{
							$("#chkAmTinh").prop( "checked", true );
							$("#divDSViKhuan").hide();
							$("#divDSKhangSinh").hide();
						}
						if(row.DUONGTINH ==null || row.DUONGTINH =="0" || row.DUONGTINH =="" ){
							 $("#chkDuongTinh").prop( "checked", false );
						}else{
							$("#chkDuongTinh").prop( "checked", true );
							$("#divDSViKhuan").show();
							 $("#divDSKhangSinh").show();
							
							 duongtinh=1;
							 amtinh=0;
							loadDanhSachViKhuan(); 
							loadDanhSachKhangSinh();
						}
						
		    		}
	    		}
	    	}
		});
		
		$('#btnLuu').on('click', function () {
			luuKetQua();
			loadDanhSachKetQua();
//			EventUtil.raiseEvent("CLS01X002_LUU",{msg:'reloadGrid'});
		});
		
		$("#btnHuy").on("click",function(e){
			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
		});
		
		$('#btnTraKetQua').on('click', function () {	
//			var url = "manager.jsp?func=../canlamsang/CLS01X004_ChonKetQuaXetNghiem&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&loaiform=1"
//			+"&rolePTH="+opt._rolePTH+"&tiepnhanid="+opt._tiepnhanid;//nghiant 01092017 
//
//			EventUtil.setEvent("CLS01X003_THOAT",function(e){
//				DlgUtil.close("dlgTraKetQua");
//				loadThongTinBenhNhan();
//				loadDanhSachKetQua();
//			});	
//			var popup = DlgUtil.buildPopupUrl("dlgTraKetQua","divTraKetQua",url,{},"Trả kết quả xét nghiệm",1016,568);
//			popup.open("dlgTraKetQua");
			
			
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
			$("#hdfIDMauBenhPham").val()+'$'+
			$("#hdfIDDichVuKB").val()
			;

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TKQ", param);
			
			if(rs == '1'){				
				DlgUtil.showMsg("Trả kết quả thành công",undefined,1000);
			} 
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể trả kết quả",undefined,3000);	
			} 
		});
		
		$('#btnHuyKetQua').on('click', function () {	
//			var url2 = "manager.jsp?func=../canlamsang/CLS01X004_ChonKetQuaXetNghiem&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
//			"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&loaiform=2"
//			+"&subdept_id="+opt.subdept_id+"&rolePTH="+opt._rolePTH+"&tiepnhanid="+opt._tiepnhanid;			
//
//			EventUtil.setEvent("CLS01X003_THOAT",function(e){
//				DlgUtil.close("dlgTraKetQua");
//				loadThongTinBenhNhan();
//				loadDanhSachKetQua();
//			});
//			var popup = DlgUtil.buildPopupUrl("dlgTraKetQua","divTraKetQua",url2,{},"Hủy kết quả xét nghiệm",1016,568);
//			popup.open("dlgTraKetQua");
			
			
			if(!rolePTH){//nghiant 05092017 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,1000);	
			}else{
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				$("#hdfIDMauBenhPham").val()+'$'+
				$("#hdfIDDichVuKB").val();//nghiant 01082017 //nghiant 14082017 (bo truyen tiepnhanid)
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HKQ", param);
				
				if(rs == '1'){
					DlgUtil.showMsg("Hủy kết quả thành công",undefined,1000);
					$("#hdfIDMauBenhPham").val("");
				} 
				//nghiant 01082017
				else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,3000);	
				}
				else {
					DlgUtil.showMsg("Có lỗi xảy ra, không thể hủy kết quả",undefined,3000);	
				} 
			}
			
			
		});
		
		$('#btnInKQ').on('click', function () {	
			inKetQua();
		});	
		
		$('#btnInKQSoiNhuom').on('click', function () {	
			inKetQuaSoiNhuom();
		});	
		
		GridUtil.setGridParam(_grd_DS_VK_NAME,{
			onSelectRow: function(id) {
				GridUtil.unmarkAll(_grd_DS_VK_NAME);
	    		GridUtil.markRow(_grd_DS_VK_NAME,id);
	    		
	            var _row = $("#"+_grd_DS_VK_NAME).jqGrid('getRowData', id);
	            vikhuanid=_row.VIKHUAN_ID;
	            tenvikhuan=_row.TEN_VIKHUAN;
//	            if (_row.VIKHUAN_ID != null && status && dsThem.indexOf( _row.VIKHUAN_ID) == -1) {
//					dsThem.push( _row.VIKHUAN_ID);
//					dsOld.push(_row.VIKHUAN_ID);
//					
//				}
//				if (!status) {
//					var j = dsThem.indexOf( _row.VIKHUAN_ID);
//					if (j != -1) {
//						dsThem.splice(j, 1);
//						dsThemSoLuong.splice(j, 1);
//						dsThemGiaKsk.splice(j, 1);
//					}
//				}
				loadDanhSachKhangSinh();
//				 $('#grdKetQuaXetNghiem').jqGrid('setCell', 1, 'GIATRI_KETQUA', tenvikhuan);
//				$("#grdKetQuaXetNghiem").setColProp('GIATRI_KETQUA',{editable:true});
	         
	        },gridComplete: function(id,e){
	        	var rowids = $("#grdDSViKhuan").getDataIDs();
	    		if(rowids!=""){
	    			for(var i=0; i<rowids.length; i++){
		    			var rowid = rowids[i];
		    			var row = $("#grdDSViKhuan").jqGrid('getRowData',rowid);
		    			
		    			if(row.VIKHUANID_CHECK){
		    				vikhuanid_check=row.VIKHUANID_CHECK;
		    				$("#grdDSViKhuan").jqGrid('setSelection', rowids[i], true);
		    	            $('#grdDSViKhuan').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                			$(element).css("color", "blue");
	                	    });
		    	            $('#grdDSViKhuan').find("tr[id='" + rowid + "']").find('input[type="radio"]').each(function(index, element) {
	                	        $(element).attr('checked', 'checked');
	                	    });
		    	            tenvikhuan=row.TEN_VIKHUAN;
		    			}
	    			}
	    		}
	        }
		});
		
		//grid khang sinh 

		
		$("#grdDSKhangSinh").jqGrid("setGridParam", {
			gridComplete: function(){
	    		var rowids = $("#grdDSKhangSinh").getDataIDs();
	    		if(rowids!=""){
	    			for(var i=0; i<rowids.length; i++){
		    			var rowid = rowids[i];
		    			var row = $("#grdDSKhangSinh").jqGrid('getRowData', rowid);
		    			
		    			if(row.KHANGSINHID_CHECK){
		    				$("#grdDSKhangSinh").jqGrid('setSelection', rowids[i], true);
		    	            $('#grdDSKhangSinh').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                			$(element).css("color", "red");
	                	    });

		    			}
		    			
		    			 
						
		    		}
	    		}
	    	}
		});
		
		//nghiant 30012017 check btn am tinh duong tinh 
		$(document).on('change', '.VKAmTinh', function() {
			 var self = $(this);
			    if (self.is(":checked")) {
			    	$("#divDSViKhuan").hide();
			    	$("#divDSKhangSinh").hide();
			    	 $("#chkDuongTinh").prop( "checked", false );
			    	 duongtinh=0;
					 amtinh=1;
			    } else {
			        console.log("Id = " + self.attr("id") + "is Unchecked ");
			        amtinh=0;
			      
			    }
		});
		$(document).on('change', '.VKDuongTinh', function(e) {
			 var self = $(this);
			    if (self.is(":checked")) {
					$("#divDSViKhuan").show();
					$("#divDSKhangSinh").show();
					 var tr = $(e.target).closest('tr');
					 
					 var _rowXN = $("#grdKetQuaXetNghiem").jqGrid('getRowData', tr[0].id);
					 dichvuid = _rowXN.DICHVUID;
					 dichvukhambenhid = _rowXN.DICHVUKHAMBENHID;
					 duongtinh=1;
					 amtinh=0;
					loadDanhSachViKhuan(); 
					loadDanhSachKhangSinh();
					 $("#chkAmTinh").prop( "checked", false );
			    } else {
			        console.log("Id = " + self.attr("id") + "is Unchecked ");
			        duongtinh=0;
			    }
		});
		
		
		

	}
	
	// load thong tin benh nhan
	function loadThongTinBenhNhan(){
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
		var row = data_ar[0];
		
		$("#lblHoTen").text(row["TENBENHNHAN"]==null?"":row["TENBENHNHAN"]);
		bnGioiTinh = row["GIOITINH"];
		bnTuoi = row["TUOI"];
		$("#chbKhan").prop('checked', row["LOAIMAUBENHPHAM"]=="2"?true:false);		
		$("#lblDiaChi").text(row["GIOITINH"]+" - "+row["NGAYSINH"]+" - "+row["DIACHI"]);
		$("#lblSoPhieu").text(row["SOPHIEU"]==null?"":row["SOPHIEU"]);
		$("#lblNoiGui").text(row["TENPHONG"]==null?"":row["TENPHONG"]);
		$("#lblDoiTuong").text(row["DOITUONG"]==null?"":row["DOITUONG"]);
		$("#lblTGChiDinh").text(row["NGAYDICHVU"]==null?"":row["NGAYDICHVU"]);
		$("#lblTGKetQua").text(row["NGAYMAUBENHPHAM_HOANTHANH"]==null?"":row["NGAYMAUBENHPHAM_HOANTHANH"]);
		$("#lblBarcode").text(row["BARCODE"]==null?"":row["BARCODE"]);
		$("#lblBacSi").text(row["TENBACSI"]==null?"":row["TENBACSI"]);
		$("#lblChanDoan").text(row["CHANDOAN"]==null?"":row["CHANDOAN"]);
		$("#txtNhanXet").val(row["GHICHU"]==null?"":row["GHICHU"]);
		$("#lblNguoiThucHien").text(row["NGUOITRAKETQUA"]==null?"":row["NGUOITRAKETQUA"]);
	}
	
	// load danh sach chi dinh
	function loadDanhSachKetQua(){	
		_sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val()]);
		console.log("hdfIDDichVuID: "+$("#hdfIDDichVuID").val());
		GridUtil.loadGridBySqlPage("grdKetQuaXetNghiem","CLS01X002.DSKQ333",_sql_par);
	}
	
	//load DS khang sinh 
	
	function loadDanhSachKhangSinh(){
		_sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,vikhuanid,dichvukhambenhid]);
		GridUtil.loadGridBySqlPage("grdDSKhangSinh","CLS01X002.DSKQ5",_sql_par);
	}
	//load DS vi khuan 
	function loadDanhSachViKhuan(){
		_sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,dichvuid,dichvukhambenhid]);
		GridUtil.loadGridBySqlPage("grdDSViKhuan","CLS01X002.DSKQ4",_sql_par);
	}
	
	// lưu kết quả khi người dùng nhập trên lưới
	function luuKetQua(){
		var reccount = $("#grdKetQuaXetNghiem").getGridParam('reccount');
		//neu ket qua la am tinh 
		if(amtinh =="1"){
			
			//loc lai du lieu 
			for(var i=1; i<=reccount; i++){
				$("#grdKetQuaXetNghiem").jqGrid('saveRow', i, 'clientArray');
				
				var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData',i);
				
	            $('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'AMTINH', 1);
	            $('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'DUONGTINH', 0);
	            
			}
			var param_arr = $("#grdKetQuaXetNghiem").jqGrid('getRowData');
			if(param_arr != null && param_arr.length > 0){
				var param_str = JSON.stringify(param_arr);
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							$("#hdfIDMauBenhPham").val()+'$'+
							param_str;
				
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X004.LKQ1", param);
				
				if(rs == '1'){
					DlgUtil.showMsg("Lưu kết quả thành công",undefined,1000);	
				} else {
					DlgUtil.showMsg("Có lỗi xảy ra, lưu kết quả thất bại",undefined,3000);	
				} 
			} else {
				DlgUtil.showMsg("Không có dữ liệu",undefined,3000);	
			}
		}
		//neu ket qua duong tinh 
		else{
			if(vikhuanid =='' || vikhuanid==undefined || vikhuanid ==null){
				DlgUtil.showMsg("Bạn chưa chọn kết quả!",undefined,1000);	
			}else{
				for(var i=1; i<=reccount; i++){
					$("#grdKetQuaXetNghiem").jqGrid('saveRow', i, 'clientArray');
					
					var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData',i);
					
		            $('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'AMTINH', 0);
		            $('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'DUONGTINH', 1);
		            $('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'GIATRI_KETQUA', tenvikhuan);
		            
				}
				var param_arr = $("#grdKetQuaXetNghiem").jqGrid('getRowData');
				
				var isupdatevk =0;
				var dsks= [];
				if(vikhuanid==vikhuanid_check){
					isupdatevk=1;
				}
				var grid = $('#grdDSKhangSinh');
	            var ids = grid.jqGrid( 'getGridParam', 'selarrrow' );
//	            console.log( JSON.stringify(ids) );
	            for (i = 0, n = ids.length; i < n; i++)
	            {
//	                var rowData = grid.jqGrid("getLocalRow", ids[i]);
	                var rowData = grid.jqGrid('getRowData', ids[i]);
	                if(rowData.S =="1"){
	                	$('#grdDSKhangSinh').jqGrid('setCell', i, 'SIR', "S");
	                }
	                if(rowData.I =="1"){
	                	$('#grdDSKhangSinh').jqGrid('setCell', i, 'SIR', "I");
	                }
	                if(rowData.R =="1"){
	                	$('#grdDSKhangSinh').jqGrid('setCell', i, 'SIR', "R");
	                }
	                dsks.push(rowData);
	                console.log('selected row data:'+ JSON.stringify(dsks));
	            }
	            //thuc hien goi ham luu 
	            if(param_arr != null && param_arr.length > 0){
					var param_str = JSON.stringify(param_arr);
					var param_ks = JSON.stringify(dsks);
					var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+isupdatevk+'$'+vikhuanid+'$'+vikhuanid_check+'$'+
					param_str+'$'+param_ks;
		
					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X004.L3", param);
					
					if(rs == '1'){
						DlgUtil.showMsg("Lưu kết quả thành công",undefined,3000);	
						loadDanhSachKetQua();
						loadDanhSachViKhuan();
						loadDanhSachKhangSinh();
						
					} else {
						DlgUtil.showMsg("Có lỗi xảy ra, lưu kết quả thất bại",undefined,3000);	
						loadDanhSachKetQua();
						loadDanhSachViKhuan();
						loadDanhSachKhangSinh();
					} 
	            }else {
					DlgUtil.showMsg("Không có dữ liệu",undefined,3000);	
				}
	           
				
			}
		}
		
		
	
		
		
	}
	
	function inKetQua(){
		var par = [ 
			{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
			{name:'dichvukhambenhid',type:'String',value:dichvukhambenhid},
			{name:'dichvuid',type:'String',value:dichvuid},
			{name:'vikhuanid',type:'String',value:vikhuanid_check}
		];
		openReport('window', 'CLS_KQ_KHANGSINHDO_944', 'pdf', par);
	}
	
	function inKetQuaSoiNhuom(){
		var par = [ 
			{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
			{name:'dichvukhambenhid',type:'String',value:dichvukhambenhid},
			{name:'dichvuid',type:'String',value:dichvuid}
		];
		openReport('window', 'CLS_KQ_VK_SOINHUOM', 'pdf', par);
	}
	
	//nghiant 26062017
	//nghiant 26062017
	$('#btnEditTimePopUpX').on('click', function () {			
		var url = "manager.jsp?func=../canlamsang/CLS02X009_SuaNgayTraKetQua&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+opt._tiepnhanid;
		EventUtil.setEvent("CLS02X009_Thoat",function(e){
			DlgUtil.close("dlgSuaThoiGianTraKQ2");	
//			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
//			reloadAllGrid();
		});
		var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaThoiGianTraKQ2","divNhapKetQua",url,{},"Sửa ngày trả kết quả",300,400);
		dlgPopup.open("dlgSuaThoiGianTraKQ2");
	});
	
	getColumnIndexByName = function (grid, columnName) {
        var cm = grid.jqGrid('getGridParam', 'colModel'), i, l;
        for (i = 0, l = cm.length; i < l; i += 1) {
            if (cm[i].name === columnName) {
                return i; // return the index
            }
        }
        return -1;
    }
	
	
	
}
