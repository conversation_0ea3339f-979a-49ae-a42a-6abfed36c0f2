<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT013_DONTHUOCGAYNGHIEN_TT052016_A5_902" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="407" leftMargin="0" rightMargin="14" topMargin="0" bottomMargin="0" uuid="681b9fa1-de42-4c31-a798-6cf7bdcdd7fe">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="DONTHUOCHUONGTHAN_TT052016_A5_932_SUB1" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="DONTHUOCHUONGTHAN_TT052016_A5_932_SUB2" class="net.sf.jasperreports.engine.JasperReport"/>
	<queryString>
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="1" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="91" splitType="Stretch">
			<subreport>
				<reportElement positionType="Float" x="1" y="0" width="406" height="45" uuid="b3764138-72be-4632-8e5a-61aa125cf34c"/>
				<subreportParameter name="maubenhphamid">
					<subreportParameterExpression><![CDATA[$P{maubenhphamid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ora_cursor">
					<subreportParameterExpression><![CDATA[$P{ora_cursor}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="parent_name">
					<subreportParameterExpression><![CDATA[$P{parent_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dept_name">
					<subreportParameterExpression><![CDATA[$P{dept_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="org_name">
					<subreportParameterExpression><![CDATA[$P{org_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[SCH]">
					<subreportParameterExpression><![CDATA[$P{[SCH]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[HID]">
					<subreportParameterExpression><![CDATA[$P{[HID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[UID]">
					<subreportParameterExpression><![CDATA[$P{[UID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[IP]">
					<subreportParameterExpression><![CDATA[$P{[IP]}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{DONTHUOCHUONGTHAN_TT052016_A5_932_SUB1}]]></subreportExpression>
			</subreport>
			<break>
				<reportElement positionType="Float" x="0" y="45" width="100" height="1" uuid="71a908e8-c000-42b0-a2ae-36147daac0aa"/>
			</break>
			<subreport>
				<reportElement positionType="Float" x="1" y="46" width="406" height="45" uuid="a4ecbceb-bec8-4516-9db4-f6bd48a5f059"/>
				<subreportParameter name="maubenhphamid">
					<subreportParameterExpression><![CDATA[$P{maubenhphamid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ora_cursor">
					<subreportParameterExpression><![CDATA[$P{ora_cursor}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="parent_name">
					<subreportParameterExpression><![CDATA[$P{parent_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dept_name">
					<subreportParameterExpression><![CDATA[$P{dept_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="org_name">
					<subreportParameterExpression><![CDATA[$P{org_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[SCH]">
					<subreportParameterExpression><![CDATA[$P{[SCH]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[HID]">
					<subreportParameterExpression><![CDATA[$P{[HID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[UID]">
					<subreportParameterExpression><![CDATA[$P{[UID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[IP]">
					<subreportParameterExpression><![CDATA[$P{[IP]}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{DONTHUOCHUONGTHAN_TT052016_A5_932_SUB2}]]></subreportExpression>
			</subreport>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Prevent"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
</jasperReport>
