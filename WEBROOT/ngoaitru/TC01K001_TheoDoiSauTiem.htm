<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js?v=1"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=3" ></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>


<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script src="../common/script/jqgrid/js/context-menu.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/TC01K001_TheoDoiSauTiem.js?v=202410041"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>

<div class="container" id="divMain">
	<div class="col-md-12 low-padding mgt5">
		<div id="inputForm" class="panel panel-default mgt5">
			<div class="panel-body">
				<div class="col-xs-12 mgt5">
				<div class="col-xs-1 low-padding"></div>
					<div class="col-xs-1 low-padding">
						<label class="">Giờ tiêm</label>
					</div>
					<div class="col-xs-4 low-padding">
						<div class="col-xs-8 low-padding">
							 <div class="input-group">
							 <input id="hosobenhanid" name="hosobenhanid" disabled hidden="true"> 	
							  <input class="form-control input-sm" id="txtGiotiem" name="txtGiotiem" title=""  data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19" disabled>
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btngiotiem" type="sCal"  onclick="NewCssCal('txtGiotiem','ddMMyyyy','dropdown',true,'24',true)"></span>							 
							</div> 						
						</div>	
					</div>
					
					<div class="col-xs-1 low-padding">
						<label class="">Giờ về</label>
					</div>
					<div class="col-xs-4 low-padding">
						<div class="col-xs-8 low-padding">
							 <div class="input-group">	
							  <input class="form-control input-sm" id="txtGiove" name="txtGiove" title=""  data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19" disabled>
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btngiove" type="sCal"  onclick="NewCssCal('txtGiove','ddMMyyyy','dropdown',true,'24',true)"></span>							 
							</div> 						
						</div>	
					</div>
					<div class="col-xs-1 low-padding"></div>
				</div>
				<div class="col-xs-12 mgt5">
				<div class="col-xs-1 low-padding"></div>
					<div class="col-xs-1 low-padding">
						<label class="">Nhiệt độ</label>
					</div>
					<div class="col-xs-4 low-padding">
						<input class="form-control input-sm" style="width: 67%;" id="txtNhietdo"
							name="txtNhietdo" maxlength="10" disabled> </input>
					</div>
					<div class="col-xs-1 low-padding">
						<label class="">Người tiêm</label>
					</div>
					<div class="col-xs-4 low-padding input-group">
					<select class="form-control input-sm" id="cboNhanVien" disabled style="width: 67%;"></select>							
					</div>
					<div class="col-xs-1 low-padding"></div>
				</div>

				<div class="col-xs-12">
					<div class="col-xs-1 low-padding" style="text-decoration-style: dashed"></div>
					<div class="col-xs-1 low-padding">
						<label class="">Thứ tự mũi tiêm</label>
					</div>
					<div class="col-xs-4 low-padding">
						<input class="form-control input-sm" id="txtTHUTUMUITIEM" style="width: 100%;" maxlength="2" disabled></input>
					</div>
					<div class="col-xs-1 low-padding"></div>
				</div>
				<div class="col-xs-12">
					<div class="col-xs-1 low-padding" style="text-decoration-style: dashed"></div>
					<div class="col-xs-1 low-padding">
						<label class="">Ghi chú</label>
					</div>
					<div class="col-xs-8 low-padding">
						<textarea class="form-control mgb3" rows="3" id="txtGhichu" style="width: 96%;" maxlength="4000" disabled></textarea> 
					</div>
					<div class="col-xs-1 low-padding"></div>
				</div>

				<div class="col-xs-12 low-padding mgt10 mgb10"
					style="text-align: center;">
					<button type="button" class="btn btn-sm btn-primary" id="btnSua"
						disabled>
						<span class="glyphicon glyphicon-edit"></span> Sửa
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnLuu"
						disabled>
						<span class="glyphicon glyphicon-floppy-disk"></span> Lưu
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnHuy"
						disabled>
						<span class="glyphicon glyphicon-remove"></span> Hủy
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnHenTiem">
						<span class="glyphicon glyphicon-time"></span> Hẹn tiêm chủng
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnDayCong" style="display: none">
						<span class="glyphicon glyphicon-send"></span> Đẩy cổng tiêm chủng
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnLichSuTiem" style="display: none">
						<span class="glyphicon glyphicon-time"></span> Lịch sử tiêm chủng
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt5">
		<div id="searchForm" class="panel panel-default mgt5">
			<div class="panel-body">
				<div class="col-xs-12 mgt5">
					<div class="col-xs-1 low-padding">
						<label class="">Mã BA</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm" id="txtMaBa"
							valrule="Mã BA,required|max_length[15]" name="txtMaBa">
						</input>
					</div>

					<div class="col-xs-4 low-padding">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding required">
								<label style="float: right;margin-right: 15px">Từ ngày</label>
							</div>
							<div class="col-xs-7 low-padding">
								<input class="form-control input-sm" id="txtTuNgay"
									valrule="Từ ngày,datetime|max_length[10]" title=""
									data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
							</div>
							<div class="col-xs-1 low-padding" style="height: 23px">
								<span style="height: 23px"
									class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
									onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
							</div>
						</div>
					</div>

					<div class="col-xs-4 low-padding">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-3 low-padding required" >
								<label style="float: right;margin-right: 15px">Đến ngày</label>
							</div>
							<div class="col-xs-7 low-padding">
								<input class="form-control input-sm" id="txtDenNgay"
							valrule="Đến ngày,datetime|max_length[10]" title=""
							data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
							</div>
							<div class="col-xs-1 low-padding" style="height: 23px">
								<span style="height: 23px" class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
							onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
							</div>
						</div>
					</div>
					<div class="col-xs-1 low-padding mgt-3">
						<button type="button" class="btn btn-sm btn-primary"
							id="btnSearch" style="width: 100%">
							<span class="glyphicon glyphicon-search"></span> Tìm kiếm
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="divDlg" style="display: none">
		<iframe src="" id="ifmView" style="width: 500px; height: 300px;"
				frameborder="0"></iframe>
	</div>
	<div class="col-xs-12 low-padding">
		<table id="grdDanhSachCH"></table>
		<div id="pager_grdDanhSachCH"></div>
	</div>
	<div id="popupId"></div>
</div>
<script>
	var opt = [];
	var schema = '{db_schema}';
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var phongid = '{subdept_id}';
	var uuid = '{uuid}';
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("TIEMCHUNGWS");

	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	session_par[4] = phongid;
	var table_name = '{table}';

	parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;
	var DS = new dSCHList(_opts);
	DS.load(hospital_id);
</script>