/*
 Mã màn hình  : NTU02D006
 File mã nguồn : NTU02D006_PhieuTruyenMau.js
 Mục đích  : <PERSON>ia<PERSON> diện màn hình
 	+ <PERSON>hập thông tin bệnh án
 	+ <PERSON>hập thông tin hỏi bệnh
 Tham số vào : 
 	truyenmau_id 	: ID mẫu bệnh phẩm
 	khambenh_id		: ID khám bệnh
	
 <PERSON>ờ<PERSON> lập trình	 <PERSON> cập nhật  	<PERSON> chú
 linhvv				 1/9/2016			Sửa bổ xung tính năng 
 */
function phieuTruyenMau_K74(_opts) {
	var _user_id;
	var _company_id = _opts.hospital_id;
	var _khambenh_id = _opts.khambenh_id;	
	var _thoigianvaovien = moment(_opts.thoigianvaovien,'DD/MM/YYYY HH:mm:ss')
    
    
	var _truyenmau_id = _opts.truyenmau_id;
	var _maubenhphamid = _opts.maubenhpham_id;
	var _dept_id = _opts.dept_id;
	var modeDisplay = _opts.modeDisplay;
	var sophieu = _opts.sophieu;
	var _action = "";
	var _col="Mã thuốc,MA,30,0,f,l;Tên thuốc,TEN,70,0,f,l";
	//var _sql="select icd10code,icd10name from dmc_icd where upper(icd10code) like upper('%[FILTER]%')";
	var sql_par=[];
	var that=this;
	this.load=doLoad;
	var _isKyCA = false;
	var _rptCode = 'DUC004_PHIEUTRUYENMAU_A4';

	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","NTU_TACH_HHMD_TRUYENMAU");
		if(data_ar != null && data_ar.length > 0){
			if(data_ar[0].NTU_TACH_HHMD_TRUYENMAU == '1'){
				_rptCode = 'DUC004_HHMD_TRUYENMAU_A4';
			}
		}
		//START -- L2HOTRO-11760
		var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		var check_par=[];
	   	check_par.push({"name":"[0]","value":"0.01"});
		var _sys_date_add1 = jsonrpc.AjaxJson.getOneValue("COM.SYSDATE_P", check_par);
		
		$("#txtTGBATDAU").val(_sys_date_add1);
		$("#txtTGKETTHUC").val(_sys_date_add1);
		$("#txtNGAYTAOPHIEU").val(_sys_date_add1);
		$('#txtNGAYTAO').val(sysdate);
		
		
		sql_par.push({"name":"[0]","value":_dept_id});
		ComboUtil.getComboTag("cboBS_CHIDINH","NTU02D006.02",sql_par, "", {value:'',text:'-- Chọn Bác sĩ--'},"sql","", false);
		ComboUtil.getComboTag("cboYT_CHIDINH","NTU02D006.03",sql_par, "", {value:'',text:'-- Chọn y tá--'},"sql","", false);
		
		//ComboUtil.getComboTag("cboMASO_CHEPHAM_MAU","NTU02D006.THUOC",sql_par, "", {value:'',text:'-- Chọn thuốc--'},"sql","", false);
		ComboUtil.initComboGrid("txtTKCHEPHAMMAU","NTU02D006.THUOC",[], "600px",_col, function(event, ui){
			var _ui = ui.item;
			   
			$("#cboCHEPHAMMAU").empty();
			$("#cboCHEPHAMMAU").append('<option value="'+_ui.MA+'">'+_ui.TEN+'</option>');
		});
	
		this.validator = new DataValidator("divMain");
		bindEvent();
		
		if(modeDisplay == '1'){
			$('#XNHH').addClass("disabledbutton");
		}else{
			$('#TPTM').addClass("disabledbutton");
		}
		
		addNumberOnly($("#txtLANTRUYEN"));
		addNumberOnly($("#txtSOLUONGMAU_THUCTE"));
		addNumberOnly($("#txtSOLUONGMAU"));
		addNumberOnly($("#txtDONVIMAU"));
		if(_truyenmau_id != null && _truyenmau_id != ''){
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02.06.TTPTM.VP", _truyenmau_id,[]);
	    	if(data_ar != null){
	    		var _row = data_ar[0];
	    		FormUtil.setObjectToForm("","",_row);
	    		if(_row.BASI_ID != null)
	    		$("#cboBS_CHIDINH").val(FormUtil.unescape(_row.BASI_ID));
	    		if(_row.THOIGIANBATDAU != null)
	    		$("#txtTGBATDAU").val(FormUtil.unescape(_row.THOIGIANBATDAU));
	    		if(_row.NGAYTAOPHIEU != null)
	    		$("#txtNGAYTAOPHIEU").val(FormUtil.unescape(_row.NGAYTAOPHIEU));
	    		
	    		$('#txtSOPHIEU').val(_row.SOPHIEU);
	    		
	    		if(_row.CHEPHAMMAU != '' && _row.TEN_CHEPHAM_MAU != ''){
	    			$("#cboCHEPHAMMAU").empty();
	    			$("#cboCHEPHAMMAU").append('<option value="'+ _row.CHEPHAMMAU +'">'+_row.TEN_CHEPHAM_MAU+'</option>');
	    		}
	    	}
	    	$('#btnCapnhat1').show();
	    	$('#btnCapnhat2').show();
	    	$('#btnSave').hide();
	    	$('#btnLuu').hide();
		}
	
	}
	function bindEvent(){
		$("#btnHuy").on("click",function(e){
			if(modeDisplay == '1'){
				EventUtil.raiseEvent("assignTruyenMau_cancel");	
			}else{
				parent.DlgUtil.close("divDlgPTruyenMau");
			}
		});
		$("#btnClose").on("click",function(e){
			if(modeDisplay == '1'){
				EventUtil.raiseEvent("assignTruyenMau_cancel");	
			}else{
				parent.DlgUtil.close("divDlgPTruyenMau");
			}
					
		});
		$("#btnLuu").on("click",function(e){
			doIns();
		});
		$("#btnSave").on("click",function(e){
			doIns();
		});
		$("#btnMoi").on("click", function(e) {
			FormUtil.clearForm("XNHH","");		
			
			$('#txtNGAYTAO').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#txtNGAYLAYMAU').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#txtHANDUNG').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		});
		$("#btnRefresh").on("click", function(e) {
			FormUtil.clearForm("TPTM","");		
			
			$("#txtNGAYTAOPHIEU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
			$("#txtTGBATDAU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		});
		
		$("#btnCapnhat1").on("click", function(e) {
			doIns();
		});

		$("#btnCapnhat2").on("click", function(e) {
			doIns();
		});
		$("#btnInphieu1").on("click", function(e) {
			doPrint('1');
		});
		$("#btnInphieu2").on("click", function(e) {
			doPrint('2');
		});

		$("#btnKySo").on("click", function (e) {
			_isKyCA = true;
			doIns();
		});
		$("#btnHuyKy").on("click", function (e) {
			_caRpt('2');
		});
		$("#btnInKySo").on("click", function (e) {
			_caRpt('0');
		});
		
	}
	
	function doIns(){
		var objData = new Object();
		FormUtil.setFormToObject("","",objData);
		var valid = false;
		if(modeDisplay == '0'){
			valid = that.validator.validateForm();
		}else{
			valid = true;
		}
		
		if(valid){
			var _TGBATDAU;
			var _NGAYTAO;
			var b_ngayhientai =jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			
			if(modeDisplay == '1'){
				var _TGBATDAU = moment($("#txtTGBATDAU").val().trim(),'DD/MM/YYYY HH:mm:ss');
		    	var _NGAYTAO = moment($("#txtNGAYTAOPHIEU").val().trim(),'DD/MM/YYYY HH:mm:ss');
		    	if(compareDate(b_ngayhientai,$("#txtNGAYTAOPHIEU").val().trim(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Ngày tạo phiếu không được lớn hơn ngày hiện tại!");								
					$('#txtNGtxtNGAYTAOPHIEUAYTAO').focus();
					return false;	
				}
		    	else if(_NGAYTAO > _TGBATDAU){
					$("#txtBATDAUTRUYEN").focus();
					DlgUtil.showMsg("Thời gian bắt đầu phải lớn hơn thời gian tạo phiếu!");
					return false;
				} 
			}else{
		    	var _NGAYTAO = moment($("#txtNGAYTAO").val().trim(),'DD/MM/YYYY HH:mm:ss');
		    	if(compareDate(b_ngayhientai,$("#txtNGAYTAO").val().trim(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Ngày giờ tạo phiếu không được lớn hơn ngày hiện tại!");								
					$('#txtNGAYTAO').focus();
					return false;
				}
			}
			
			if(_thoigianvaovien > _NGAYTAO){
				$("#txtNGAYTAO").focus();
				DlgUtil.showMsg("Ngày tạo phải lớn hơn thời gian vào viện!");
				return false;
			}
			else{
				if(_truyenmau_id == null || _truyenmau_id == '') _truyenmau_id = -1;
				objData.MAUBENHPHAMID = _maubenhphamid;
				objData.MODE_DISPLAY = modeDisplay+'';
				_par = [JSON.stringify(objData),_khambenh_id, _truyenmau_id];
				var ret=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.THEM.PTM.VP",_par.join('$'));

				if (ret == '-1') {
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
					return false;
				} else {
					if (_truyenmau_id == '-1') {
						if (_isKyCA) {
							_truyenmau_id = ret;
							_caRpt('1');
						} else {
							if (modeDisplay == '0') {
								parent.DlgUtil.showMsg("Thêm phiếu truyền máu thành công", undefined, 2000);
								parent.DlgUtil.close("divDlgPTruyenMau");
							} else {
								_msg = "Thêm phiếu truyền máu thành công!";
								var evFunc = EventUtil.getEvent("assignSevice_SaveTruyenMau");
								evFunc({msg: _msg});
							}
							$("#btnLuu").prop("disabled", true);
							$("#btnSave").prop("disabled", true);
							$("#btnCapnhat1").prop("disabled", true);
							$("#btnCapnhat2").prop("disabled", true);
						}

					} else {
						if (_isKyCA) {
							_caRpt('1');
						} else {
							if (modeDisplay == '0') {
								DlgUtil.showMsg("Cập nhật phiếu truyền máu thành công!");
							} else {
								_msg = "Cập nhật phiếu truyền máu thành công!";
								var evFunc = EventUtil.getEvent("assignSevice_SaveTruyenMau");
								evFunc({msg: _msg});
							}
						}
					}
				}
			}
		}
	}
	function doPrint(type){
		if(_truyenmau_id == null || _truyenmau_id == ''){
			return DlgUtil.showMsg("Chưa có phiếu truyền máu!");
		}
		var par = [ {
			name : 'truyenmauid',
			type : 'String',
			value : _truyenmau_id
		}, {
			name : 'phieuso',
			type : 'String',
			value : type
		}];
		openReport('window', _rptCode, "pdf", par);
	}
	
	
	$('.input-sm').keydown(function (e) {
	     if (e.which === 13) {
	         var index = $('.input-sm').index(this) + 1;
	         $('.input-sm').eq(index).focus();
	     }
	 });
	
	function addNumberOnly(element){
		$(element).keypress(function (e) {
		     if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
		        return false;
		    }
		});
	}

	function _caRpt(signType) {
		_isKyCA = false;
		sql_par = [];
		sql_par.push({"name": "[0]", "value": _truyenmau_id});
		var _dataCa = jsonrpc.AjaxJson.ajaxExecuteQueryO('CA.TM.GET.HSBAID', sql_par);
		var rowCA = JSON.parse(_dataCa);
		var _par = [];
		_par = [{
			name: 'hosobenhanid',
			type: 'String',
			value: rowCA[0].HOSOBENHANID
		}, {
			name: 'truyenmauid',
			type: 'String',
			value: _truyenmau_id
		}, {
			name: 'RPT_CODE',
			type: 'String',
			value: _rptCode
		}];
		//ky
		if (signType == '0') {
			CommonUtil.openReportGetCA2(_par, false);
		} else {
			CommonUtil.kyCA(_par, signType, true);
			EventUtil.setEvent("eventKyCA", function (e) {
				DlgUtil.showMsg(e.res);
			});
		}
	}
}