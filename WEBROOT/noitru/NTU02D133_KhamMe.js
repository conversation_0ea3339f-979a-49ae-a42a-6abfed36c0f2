function NTU02D133_KhamMe(_opts) {
	var _gridLSM = "grdLSDuyetMo";
	var _gridLSMHeader = "LICHMOID,LICHMOID,50,0,t,l,1,2;" +
		"<PERSON>h<PERSON><PERSON> gian mổ,THOIGIAN_MO,120,0,f,l,1,2;" +
		"Tr<PERSON>ng thái,TRANGTHAI,100,0,f,l,1,2;" +
		"<PERSON><PERSON>ng thực hiện,PHONG_MO_FULLNAME,200,0,f,l,1,2;" +
		"<PERSON><PERSON><PERSON> <PERSON><PERSON>,BS_CHINH,120,0,f,l,1,2;" +
		"<PERSON><PERSON><PERSON> sĩ yêu cầu,NGUOITAO,140,0,f,l,1,2;" +
		"Ng<PERSON><PERSON><PERSON>,NGUOITAO,140,0,f,l,1,2";

	var _gridDuyetMo = "grdDSDuyetMo";
	var _gridDuyetMoHeader = "LICHMOID,LICHMOID,50,0,t,l,1,2;" +
		"BENHNHANID,BENHNHANID,50,0,t,l,1,2;" +
		"KHAMBENHID,KHAMBENHID,50,0,t,l,1,2;" +
		"HOSOBENHANID,HOSOBENHANID,50,0,t,l,1,2;" +
		"FLAG_CA,FLAG_CA,50,0,t,l,1,2;" +
		" ,ICON,35,0,ns,l;" +
		"Mã BA,MAHOSOBENHAN,120,0,f,l,1,2;" +
		"Tên bệnh nhân,TENBENHNHAN,120,0,f,l,1,2;" +
		// "Trạng thái,TRANGTHAI,100,0,f,l,1,2;" +
		"Thời gian mổ dự kiến,THOIGIAN_MO,120,0,f,l,1,2;" +
		"Ngày yêu cầu,NGAYTAO,80,0,f,l,1,2;" +
		"Khoa yêu cầu,KHOA,200,0,f,l,1,2;" +
		"Phòng thực hiện,PHONG_MO_FULLNAME,200,0,f,l,1,2;" +
		"Bác sĩ chính,BS_CHINH,120,0,f,l,1,2;" +
		"Bác sĩ yêu cầu,NGUOITAO,140,0,f,l,1,2;" +
		"Người duyệt,NGUOITAO,140,0,f,l,1,2;" +
		"trangthaikhamme,trangthaikhamme,0,0,t,l"
	;

	var _lichmoid = '';
	var _hosobenhanid = '';
	var _benhnhanid = '';
	var _khambenhid = '';
	this.load = doLoad;
	var checkRequired;
	var _trangthai = '';
	var _rpt_code_kyso = 'PHIEU_KHAMTIENME';
	var cf = new Object();

	var isModeDAKHOAQUANGTRI = false;
	/*
        cac noi dung isModeDAKHOAQUANGTRI:
        - màn mặc định (cấu hình NTU_LICHMO_LCI = 0; PTTT_PTV_SHOWALL = 0)
		- chỉ cảnh báo Thời gian mổ và Thời gian khám mê trước ngày hiện tại! (không cần chặn)
		- Hiển thị nội dung Thời gian kết thúc dự kiến
		- Thay thế nội dung tìm kiếm theo ngày bằng Từ ngày, đến ngày
    */

	if (_opts.hospital_id == 30360 ) {
		isModeDAKHOAQUANGTRI = true;
	}


	// jira 25337
	// các nội dung chỉnh sửa ở màn Khám mê:
	// - hiển thị các thông tin Gây mê chính, Phương pháp vô cảm ... theo bản ghi bên lịch mổ
	// - thêm nút Từ chối:
	// 	duyệt rồi thì không cho từ chối
	// 	từ chối rồi thì không cho duyệt
	// - thêm nút In phiếu thông qua mổ:
	// 	+ gọi đến báo cáo phiếu thông qua mổ (làm sau)
	// - thêm nút In phiếu Gây mê
	// 	+ gọi đến nút chức năng Gây mê hồi sức Lập đã làm trước đó
	// - tại tab thứ 2 (Chi tiết khám mê) vùng Cận lâm sàng lấy các thông tin chỗ a Việt
	var isDuLieuKhamMeTuDuyetMo = false;

	function doLoad(_hosp_id) {
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		_initControl();
		_bindEvent();
	}

	function _initControl() {
		GridUtil.init(_gridLSM, "100%", "315", "Lịch sử duyệt mổ", false, _gridLSMHeader, false);
		GridUtil.init(_gridDuyetMo, "100%", "200", "Danh sách khám mê", false, _gridDuyetMoHeader, false);

		var _colICD = "ORG_ID,ORG_ID,0,0,t,l;Phòng,PHONG,50,0,f,l;Khoa,KHOA,50,0,f,l";
		var _makhoa = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_GET_MAKHOA_PTGM');
		var sql_par = [];
		sql_par.push({"name": "[0]", "value": _makhoa});
		var sqlPhongMo = "NTU02D132.12";
		// da khoa quang tri chi lay cac phong co loai la phong mo
		if (_opts.hospital_id == 30360) {
			sqlPhongMo = "NTU02D132.L01";
		}
		ComboUtil.initComboGrid("txtPHONG_MO_ID", sqlPhongMo, sql_par, "500px", _colICD, function (event, ui) {
			$("#txtPHONG_MO_ID").val("");
			var option = $('<option value="' + ui.item.ORG_ID + '">' + ui.item.PHONG + '</option>');
			$("#cboPHONG_MO").empty();
			$("#cboPHONG_MO").append(option);
			return false;
		});

		addFunctions(); // lap add so voi a doan

		// jira 25337
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'NTU_LICHMO_LCI;KHAMME_PGMTM;KHAMME_DAKHOA_QUANGTRI;HIS_LICHMO_TGLENLICH');
		if (data_ar != null && data_ar.length > 0) {
			cf = data_ar[0];
			if (data_ar[0].KHAMME_DAKHOA_QUANGTRI == '1') {
				isModeDAKHOAQUANGTRI = true;
			}
		}

		if (cf.NTU_LICHMO_LCI == '2') {
			isDuLieuKhamMeTuDuyetMo = true;

			$('#divNGUYCONHIEMTRUNG').hide();
			$('#divTIENME').hide();

			$('#divTHUPHANUNGTHUOC').show();
			$('#divKHANGSINH').show();
			$('#divKHANGSINHTRUOCMO').show();
			$('#divDUTRUMAU').show();
			$('#divKHOKHANTIENLUONG').show();
			$('#btnTuChoi').show();
			$('#btnInPhieuThongQuaMo').show();
			$('#btnInPhieuGayMe').show();
		} else {
			$('#divNGUYCONHIEMTRUNG').show();
			$('#divTIENME').show();

			$('#divTHUPHANUNGTHUOC').hide();
			$('#divKHANGSINH').hide();
			$('#divKHANGSINHTRUOCMO').hide();
			$('#divDUTRUMAU').hide();
			$('#divKHOKHANTIENLUONG').hide();
			$('#btnTuChoi').hide();
			$('#btnInPhieuThongQuaMo').hide();
			$('#btnInPhieuGayMe').hide();
		}

		if (cf.KHAMME_PGMTM == '1') {
			$('#btnPhieuKhamMeTruocMo').show();
		} else {
			$('#btnPhieuKhamMeTruocMo').hide();
		}

		if (isModeDAKHOAQUANGTRI) {
			$('#divTHOIGIANKETTHUC').show();
			$('#divTIMTHEONGAYCUTHE').hide();
			$('#divTIMTHEOTUNGAYDENNGAY').show();
			$('#divTUTHE').show();
			$('#divTACDUNG').show();
			//$('#btnEXCEL').show();
			$("#txtTUNGAY").val(moment().format('DD/MM/YYYY'));
			$("#txtDENNGAY").val(moment().format('DD/MM/YYYY'));
		}

		var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (kyso_kydientu != "1") {
			$('#' + 'btnKySo').remove();
			$('#' + 'btnHuyKy').remove();
			$('#' + 'btnInKySo').remove();
		}
	};

	function addFunctions(){
		$("#btnLuu").prop('disabled', true);
		$("#btnHuy").prop('disabled', true);
		$("#btnDuyetKhamMe").prop('disabled', true);
		$("#btnHuyKhamMe").prop('disabled', true);
		$("#btnTuChoi").prop('disabled', true);
		$("#btnInPhieuThongQuaMo").prop('disabled', true);
		$("#btnInPhieuGayMe").prop('disabled', true);
		$("#btnPhieuKhamMeTruocMo").prop('disabled', true);

		// $("#txtTHOIGIAN_MO").prop('disabled', true);
		// $("#spTHOIGIAN_MO").prop('disabled', true);
		// $("#txtPHONG_MO_ID").prop('disabled', true);
		// $("#cboPHONG_MO").prop('disabled', true);
		// $("#txtTHOIGIAN_KHAMME").prop('disabled', true);
		// $("#spTHOIGIAN_KHAMME").prop('disabled', true);

		disableInputKhamMe();

		// combo phuong phap vo cam
		ComboUtil.getComboTag("cboPPVCAM","PTTT.VOCAM", [],'',{value:'-1',text:'--Lựa chọn--'},"sql");

		$('#btnCLEARBS_GAYMECHINH').on("click", function () {
			$("#txtGAYMECHINH_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboGAYMECHINH").empty();
			$("#cboGAYMECHINH").append(option);
		});

		$('#btnCLEARBS_PHUME').on("click", function () {
			$("#txtPHUME_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME").empty();
			$("#cboPHUME").append(option);
		});

		$('#btnCLEARBS_DCVONGTRONG').on("click", function () {
			$("#txtDCVONGTRONG_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboDCVONGTRONG").empty();
			$("#cboDCVONGTRONG").append(option);
		});

		$('#btnCLEARBS_DCVONGNGOAI').on("click", function () {
			$("#txtDCVONGNGOAI_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboDCVONGNGOAI").empty();
			$("#cboDCVONGNGOAI").append(option);
		});

		$('#btnCLEARBS_BSHUONGDANGAYME').on("click", function () {
			$("#txtBSHUONGDANGAYME_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBSHDGAYME").empty();
			$("#cboBSHDGAYME").append(option);
		});

		var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
		var sql_par = [];
		var isPtvShowAll = false;
		var _show = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PTTT_PTV_SHOWALL');
		if (_show == '1') {
			isPtvShowAll = true;
		}
		if (isPtvShowAll) {
			sql_par.push({"name": "[0]", "value": 0}, {"name": "[1]", "value": _opts.dept_id});
		} else {
			sql_par.push({"name": "[0]", "value": 1}, {"name": "[1]", "value": _opts.dept_id});
		}


		// buu dien ho chi minh
		if (_opts.hospital_id == 965) {
			sql_par=[];
			sql_par.push({"name":"[0]","value":_opts.hospital_id},{"name":"[1]","value":0});
		}


		ComboUtil.initComboGrid("txtGAYMECHINH_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
			$("#txtGAYMECHINH_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboGAYMECHINH").empty();
			$("#cboGAYMECHINH").append(option);
			return false;
		});
		ComboUtil.initComboGrid("txtPHUME_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
			$("#txtPHUME_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME").empty();
			$("#cboPHUME").append(option);
			return false;
		});
		ComboUtil.initComboGrid("txtDCVONGTRONG_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
			$("#txtDCVONGTRONG_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboDCVONGTRONG").empty();
			$("#cboDCVONGTRONG").append(option);
			return false;
		});
		ComboUtil.initComboGrid("txtDCVONGNGOAI_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
			$("#txtDCVONGNGOAI_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboDCVONGNGOAI").empty();
			$("#cboDCVONGNGOAI").append(option);
			return false;
		});
		ComboUtil.initComboGrid("txtBSHUONGDANGAYME_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
			$("#txtBSHUONGDANGAYME_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboBSHDGAYME").empty();
			$("#cboBSHDGAYME").append(option);
			return false;
		});

		$('#cboTRANGTHAIPHIEU').change(function() {
			$('#btnTIMKIEM').click();
		});

		$('#cboKHOA').change(function() {
			$('#btnTIMKIEM').click();
		});

		$('#btnSua').on('click', function(e) {
			if (_lichmoid == ''){
				DlgUtil.showMsg("Chưa chọn lịch mổ!");
				return;
			}
			enableInputKhamMe();
			$("#btnSua").prop('disabled', true);
			$("#btnLuu").prop('disabled', false);
			$("#btnHuy").prop('disabled', false);
			$("#btnDuyetKhamMe").prop('disabled', true);
			$("#btnTuChoi").prop('disabled', true);
			$("#btnInPhieuThongQuaMo").prop('disabled', true);
			$("#btnInPhieuGayMe").prop('disabled', true);
			$("#btnPhieuKhamMeTruocMo").prop('disabled', true);
		});

		$('#btnLuu').on('click', function(e) {
			saveKhamMe();
		});

		$('#btnHuy').on('click', function(e) {
			disableInputKhamMe();
			clearInputKhamMe();
			var obj = new Object();
			obj._lichmoid = _lichmoid;
			obj._khambenhid = _khambenhid;
			var _par = JSON.stringify(obj);
			var sqlID = "NTU02D133.L01";
			if (isDuLieuKhamMeTuDuyetMo) {
				sqlID = "NTU02D133.L01.2";
			}
			var data_KhamMe = jsonrpc.AjaxJson.ajaxCALL_SP_O(sqlID, _par);
			if (data_KhamMe != null && data_KhamMe.length > 0) {
				var row = data_KhamMe[0];
				fillThongTinKhamMe(row);
			}
			$("#btnSua").prop('disabled', false);
			$("#btnLuu").prop('disabled', true);
			$("#btnHuy").prop('disabled', true);
			$("#btnDuyetKhamMe").prop('disabled', false);
			$("#btnTuChoi").prop('disabled', false);
			$("#btnInPhieuThongQuaMo").prop('disabled', false);
			$("#btnInPhieuGayMe").prop('disabled', false);
			$("#btnPhieuKhamMeTruocMo").prop('disabled', false);

		});

		// duyet roi thi khong cho tu choi
		$('#btnDuyetKhamMe').on('click', function(e) {
			var objData = new Object();
			objData.idLichMo = _lichmoid;
			var par = JSON.stringify(objData);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D133.L03", par);
			var data = $.parseJSON(result);
			if (data == 1) {
				DlgUtil.showMsg('Duyệt thành công!', function () {
					disableInputKhamMe();
					$("#btnSua").prop('disabled', false);
					$("#btnLuu").prop('disabled', true);
					$("#btnHuy").prop('disabled', true);
					$("#btnDuyetKhamMe").prop('disabled', false);
					$("#btnTuChoi").prop('disabled', false);
					$("#btnInPhieuThongQuaMo").prop('disabled', false);
					$("#btnInPhieuGayMe").prop('disabled', false);
					$("#btnPhieuKhamMeTruocMo").prop('disabled', false);
					loadGridData();
				});
			} else if (data == -2) {
				DlgUtil.showMsg('Phải sửa và lưu khám mê trước khi duyệt', function () {
				});
			} else if (data == -3) {
				DlgUtil.showMsg('Khám mê này đã được duyệt trước đó', function () {
				});
			} else if (data == -4) {
				DlgUtil.showMsg('Chưa có khoa cấu hình. Kiểm tra cấu hình HIS_NTU_KHAMME_KHOA', function () {
				});
			} else if (data == -5) {
				DlgUtil.showMsg('Không có khoa tương ứng với mã khoa đã cấu hình. Kiểm tra cấu hình HIS_NTU_KHAMME_KHOA', function () {
				});
			} else if (data == -6) {
				DlgUtil.showMsg('Khám mê đã bị từ chối. không thể duyệt!', function () {
				});
			}
			else {
				DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
				});
			}
		});

		// BVTM-1279
		$('#btnHuyKhamMe').on('click', function (e) {
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D133.L06", _lichmoid);
			if (result == 1) {
				DlgUtil.showMsg('Hủy khám mê thành công !');
				loadGridData();
			} else if (result == 2) {
				DlgUtil.showMsg('Khám mê này chưa được duyệt !');
			} else if (result == 3) {
				DlgUtil.showMsg('Dịch vụ PTTT đã hoàn thành không thể hủy khám mê !');
			} else {
				DlgUtil.showMsg('Đã có lỗi xảy ra!');
			}
		});

		// tu choi roi thi khong cho duyet
		$('#btnTuChoi').on('click', function(e) {
			var obj = {
				lichmoid: _lichmoid,
			}
			var param = JSON.stringify(obj);
			var data = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D133.L05", param);
			if (data == '1') {
				DlgUtil.showMsg('Đã từ chối!', function () {
					disableInputKhamMe();
					$("#btnSua").prop('disabled', false);
					$("#btnLuu").prop('disabled', true);
					$("#btnHuy").prop('disabled', true);
					$("#btnDuyetKhamMe").prop('disabled', false);
					$("#btnTuChoi").prop('disabled', false);
					$("#btnInPhieuThongQuaMo").prop('disabled', false);
					$("#btnInPhieuGayMe").prop('disabled', false);
					$("#btnPhieuKhamMeTruocMo").prop('disabled', false);
					loadGridData();
				});
			} else if (data == -2) {
				DlgUtil.showMsg('Phải sửa và lưu khám mê trước khi từ chối', function () {
				});
			} else if (data == -3) {
				DlgUtil.showMsg('Khám mê này đã được duyệt trước đó. không thể từ chối!', function () {
				});
			} else if (data == -4) {
				DlgUtil.showMsg('Khám mê này đã được từ chối trước đó!', function () {
				});
			} else {
				DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
				});
			}
		});

		$('#btnInPhieuThongQuaMo').on('click', function(e) {

		});

		$('#btnInPhieuGayMe').on('click', function(e) {
			var hosobenhanid = _hosobenhanid;
			var khambenhid = _khambenhid;
			var ten_benhnhan = $('#txtTENBENHNHAN').val();
			if(hosobenhanid == ''){
				DlgUtil.showMsg("Bạn chưa khám mê!");
			} else {
				var idGayMeHoiSuc = checkPhieuGayMeHoiSuc(hosobenhanid,ten_benhnhan,khambenhid); // dua vao hosobenhanid,kiem tra xem co phieu gay me hoi suc tuong ung chua, neu chua co thi insert, tra ve idGayMeHoiSuc
				var name = hosobenhanid + '@' + idGayMeHoiSuc + '@' + khambenhid;
				window.open('manager.jsp?func=../noitru/NTU02D125_PhieuGayMeHoiSuc&showMode=dlg',name,'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			}
		});

		$('#btnPhieuKhamMeTruocMo').on('click', function(e) {
			var khambenhid = _khambenhid;
			if (khambenhid == ''){
				DlgUtil.showMsg("Bạn chưa khám mê!");
			} else {
				var _param = [{
					name: 'khambenhid',
					type: 'String',
					value: khambenhid
				}];
				CommonUtil.openReportGet('window', "PHIEU_GAYMETRUOCMO", "pdf", _param);
			}
		});

		//<!-- Bổ sung nút in phiếu khám mê BVTM-1257-->

		$('#btnPhieuKhamMe').on('click', function(e) {
			var khambenhid = _khambenhid;
			if (khambenhid == ''){
				DlgUtil.showMsg("Bạn chưa khám mê!");
			} else {
				var _param = [{
					name: 'khambenhid',
					type: 'String',
					value: khambenhid
				}];
				CommonUtil.openReportGet('window', "PHIEU_KHAMTIENME", "pdf", _param);
			}
		});
		//<!-- END Bổ sung nút in phiếu khám mê -->

		function validateSaveKhamMe() {
			// var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			// var _ngayhientai_date = stringToDateTime(_ngayhientai);
			// var _ngaymo_date = stringToDateTime($('#txtTHOIGIAN_MO').val());
			// var _ngayme_date = stringToDateTime($('#txtTHOIGIAN_KHAMME').val());

			var _ngayhientai_date = new Date();

			var strTgMo = $('#txtTHOIGIAN_MO').val();
			var _ngaymo_date = new Date(
				strTgMo.substring(6,10)
				+ '-'
				+ strTgMo.substring(3,5)
				+ '-'
				+ strTgMo.substring(0,2)
				+ 'T'
				+ strTgMo.substring(11,13)
				+ ':'
				+ strTgMo.substring(14,16)
				+ ':'
				+ strTgMo.substring(17,19)
				+ 'Z');

			var strTgMe = $('#txtTHOIGIAN_KHAMME').val();
			var _ngayme_date = new Date(
				strTgMe.substring(6,10)
				+ '-'
				+ strTgMe.substring(3,5)
				+ '-'
				+ strTgMe.substring(0,2)
				+ 'T'
				+ strTgMe.substring(11,13)
				+ ':'
				+ strTgMe.substring(14,16)
				+ ':'
				+ strTgMe.substring(17,19)
				+ 'Z');

			if (cf.HIS_LICHMO_TGLENLICH != 1) {
				if (_ngaymo_date < _ngayhientai_date) {
					if (!isModeDAKHOAQUANGTRI) {
						DlgUtil.showMsg("Thời gian mổ không được phép trước thời điểm hiện tại");
						$('#txtTHOIGIAN_MO').focus();
						return -1;
					} else {
						DlgUtil.showMsg("Thời gian mổ trước thời điểm hiện tại");
					}
				}
	
				if (_ngayme_date < _ngayhientai_date) {
					if (!isModeDAKHOAQUANGTRI) {
						DlgUtil.showMsg("Thời gian khám mê không được phép trước thời điểm hiện tại");
						$('#txtTHOIGIAN_KHAMME').focus();
						return -1;
					} else {
						DlgUtil.showMsg("Thời gian khám mê trước thời điểm hiện tại");
					}
	
				}
			}

			if ($('#cboGAYMECHINH').val() == '-1') {
				DlgUtil.showMsg('Chưa chọn gây mê chính!', function () {
					$('#txtGAYMECHINH_ID').focus();
				});
				return -1;
			}

			if ($('#cboPHONG_MO').val() == '-1') {
				DlgUtil.showMsg('Chưa chọn phòng mổ', function () {
					$('#txtPHONG_MO_ID').focus();
				});
				return -1;
			}

			return 1;

		}

		function saveKhamMe(){
			var validator = new DataValidator("inputForm");
			var valid = validator.validateForm();
			if (valid) {
				if (validateSaveKhamMe() == -1){
					return;
				}
				var objData = new Object();
				FormUtil.setFormToObject("divKhamMe","",objData);
				objData.PPVCAM = $("#cboPPVCAM").val();
				objData.KTGAYME = $("#txtKTGAYME").val();
				objData.NCNHIEMTRUNG = $("#txtNCNHIEMTRUNG").val();
				objData.TIENME = $("#txtTIENME").val();
				objData.GAYMECHINH = $("#cboGAYMECHINH").val();
				objData.PHUME = $("#cboPHUME").val();
				objData.DCVONGTRONG = $("#cboDCVONGTRONG").val();
				objData.DCVONGNGOAI = $("#cboDCVONGNGOAI").val();
				objData.BSHDGAYME = $("#cboBSHDGAYME").val();

				objData.PHANUNG_THUOC_GTGM = $("#txtPHANUNG_THUOC_GTGM").val();
				objData.KHANGSINH = $("#txtKHANGSINH").val();
				objData.KHANGSINH_TRUOCMO = $("#txtKHANGSINH_TRUOCMO").val();
				objData.DUTRUMAU = $("#txtDUTRUMAU").val();
				objData.KHOKHAN_TIENLUONG = $("#txtKHOKHAN_TIENLUONG").val();

				objData.THOIGIAN_MO = $("#txtTHOIGIAN_MO").val();
				objData.THOIGIAN_KHAMME = $("#txtTHOIGIAN_KHAMME").val();
				objData.THOIGIAN_KETTHUC = $("#txtTHOIGIAN_KETTHUC").val();
				objData.PHONG_MO = $("#cboPHONG_MO").val();
				objData.TU_THE = $("#txtTU_THE").val();
				objData.TAC_DUNG = $("#txtTAC_DUNG").val();

				objData.idLichMo = _lichmoid;

				var par = JSON.stringify(objData);

				var sqlID = "NTU02D133.L02";
				if (isDuLieuKhamMeTuDuyetMo) {
					sqlID = "NTU02D133.L02.2";
				}

				var result = jsonrpc.AjaxJson.ajaxCALL_SP_I(sqlID, par);
				var data = $.parseJSON(result);
				if (data == 1 || data == 2) {
					DlgUtil.showMsg('Lưu thành công!', function () {
						disableInputKhamMe();
						$("#btnSua").prop('disabled', false);
						$("#btnLuu").prop('disabled', true);
						$("#btnHuy").prop('disabled', true);
						$("#btnDuyetKhamMe").prop('disabled', false);
						$("#btnTuChoi").prop('disabled', false);
						$("#btnInPhieuThongQuaMo").prop('disabled', false);
						$("#btnInPhieuGayMe").prop('disabled', false);
						$("#btnPhieuKhamMeTruocMo").prop('disabled', false);
					});
				} else {
					DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
					});
				}
			}
		}


	}

	function enableInputKhamMe() {
		var idKhamMes = ["cboPPVCAM", "txtGAYMECHINH_ID", "cboGAYMECHINH", "btnCLEARBS_GAYMECHINH",
			"txtKTGAYME", "txtPHUME_ID", "cboPHUME", "btnCLEARBS_PHUME", "txtNCNHIEMTRUNG",
			"txtDCVONGTRONG_ID", "cboDCVONGTRONG", "btnCLEARBS_DCVONGTRONG", "txtTIENME",
			"txtDCVONGNGOAI_ID", "cboDCVONGNGOAI", "btnCLEARBS_DCVONGNGOAI", "txtBSHUONGDANGAYME_ID",
			"cboBSHDGAYME", "btnCLEARBS_BSHUONGDANGAYME",
			"txtPHANUNG_THUOC_GTGM",
			"txtKHANGSINH",
			"txtKHANGSINH_TRUOCMO",
			"txtTU_THE",
			"txtTAC_DUNG",
			"txtDUTRUMAU",
			"txtKHOKHAN_TIENLUONG",
			"txtNOIKHOA", "txtNGOAIKHOA",
			"txtTHUOC", "txtGAYME", "txtDIUNG", "txtTHUCAN", "txtHENPQ", "txtKHAC", "txtTMCHAYMAU",
			"txtDANGDT", "txtDENGHI", "txtHOHAP", "txtTANSOHH", "txtNGHEHH", "txtTIMMACH", "txtTANSOTM",
			"txtKHOTHOTM", "txtHUYETAPTM", "txtNGHETM", "txtTIEUHOA", "txtNOITIET", "txtTHANKINH", "txtBENHKHAC",
			"txtDNKQ", "txtMALL", "txtHAMIENG", "txtNGUACO", "txtKCGIAPCAM", "txtNHOMMAU",
			"txtTTRM", "txtYCMAU", "txtBASO", "txtKCONG", "txtXQUANGPHOI", "txtLYM",
			"txtNACONG", "txtMRI", "txtMONO", "txtCLO", "txtCTSCANNER", "txtNEU", "txtCACONG", "txtEOS",
			"txtGPT", "txtHBG", "txtGOT", "txtHTC", "txtPROTEIN", "txtSIEUAMBUNG", "txtMCH", "txtURE",
			"txtSIEUAMTIM", "txtCGV", "txtGLUCOSE", "txtPLT", "txtHIV", "txtRBC",
			"txtTHOIGIAN_MO",
			"spTHOIGIAN_MO",
			"txtTHOIGIAN_KHAMME",
			"spTHOIGIAN_KHAMME",
			"txtTHOIGIAN_KETTHUC",
			"spTHOIGIAN_KETTHUC",
			"txtPHONG_MO_ID",
			"cboPHONG_MO"
			,"txtBUAANCUOITRUOCMO"
			,"chkRANGGIAKHONG"
			,"chkRANGGIATHAODUOC"
			,"chkRANGGIACODINH"
			,"txtBENHLAYNHIEM"
			,"chkNGHIENTHUOCLA"
			,"chkNGHIENRUOU"
			,"chkNGHIENMATUY"
			,"txtKHAMLAMSANG"
			,"txtTUANHOAN"
			,"txtCOTSONG"
			,"txtXETNGHIEMBATTHUONG"
			,"txtYEUCAUBOSUNG"
			,"txtDUKIENCACHVATHUOCVOCAM"
			,"txtDUKIENGIAMDAUSAUMO"
			,"txtNGAYTHAMLAITRUOCMO"
			,"spNGAYTHAMLAITRUOCMO"
			,"txtASA"
			,"chkCAPCUU"
			,"chkDADAY"
			,"txtTUANHOANM"
			,"txtTUANHOANHA"
			,"txtMOLANTRUOC"
			,"txtTAIBIEN"
			,"txtTIMMACHTS"
			,"txtHOHAPTS"
			,"txtGIADINH"
			,"txtBIEUHIEN"
			,"txtTONGTRANG"
			,"txtCOQUANKHAC"
			,"txtANUONG"
			,"txtKINHCHOT"
			,"txtGANTHAN"
			,"txtYEUCAUDACBIET"
			,"txtKETLUAN"
		];
		for (var i = 0; i< idKhamMes.length; i++) {
			$("#" + idKhamMes[i]).prop('disabled', false);
		}
	}

	function disableInputKhamMe() {
		var idKhamMes = ["cboPPVCAM", "txtGAYMECHINH_ID", "cboGAYMECHINH", "btnCLEARBS_GAYMECHINH",
			"txtKTGAYME", "txtPHUME_ID", "cboPHUME", "btnCLEARBS_PHUME", "txtNCNHIEMTRUNG",
			"txtDCVONGTRONG_ID", "cboDCVONGTRONG", "btnCLEARBS_DCVONGTRONG", "txtTIENME",
			"txtDCVONGNGOAI_ID", "cboDCVONGNGOAI", "btnCLEARBS_DCVONGNGOAI", "txtBSHUONGDANGAYME_ID",
			"cboBSHDGAYME", "btnCLEARBS_BSHUONGDANGAYME",
			"txtPHANUNG_THUOC_GTGM",
			"txtKHANGSINH",
			"txtKHANGSINH_TRUOCMO",
			"txtTU_THE",
			"txtTAC_DUNG",
			"txtDUTRUMAU",
			"txtKHOKHAN_TIENLUONG",
			"txtNOIKHOA", "txtNGOAIKHOA",
			"txtTHUOC", "txtGAYME", "txtDIUNG", "txtTHUCAN", "txtHENPQ", "txtKHAC", "txtTMCHAYMAU",
			"txtDANGDT", "txtDENGHI", "txtHOHAP", "txtTANSOHH", "txtNGHEHH", "txtTIMMACH", "txtTANSOTM",
			"txtKHOTHOTM", "txtHUYETAPTM", "txtNGHETM", "txtTIEUHOA", "txtNOITIET", "txtTHANKINH", "txtBENHKHAC",
			"txtDNKQ", "txtMALL", "txtHAMIENG", "txtNGUACO", "txtKCGIAPCAM", "txtNHOMMAU",
			"txtTTRM", "txtYCMAU", "txtBASO", "txtKCONG", "txtXQUANGPHOI", "txtLYM",
			"txtNACONG", "txtMRI", "txtMONO", "txtCLO", "txtCTSCANNER", "txtNEU", "txtCACONG", "txtEOS",
			"txtGPT", "txtHBG", "txtGOT", "txtHTC", "txtPROTEIN", "txtSIEUAMBUNG", "txtMCH", "txtURE",
			"txtSIEUAMTIM", "txtCGV", "txtGLUCOSE", "txtPLT", "txtHIV", "txtRBC",
			"txtTHOIGIAN_MO",
			"spTHOIGIAN_MO",
			"txtTHOIGIAN_KHAMME",
			"spTHOIGIAN_KHAMME",
			"txtTHOIGIAN_KETTHUC",
			"spTHOIGIAN_KETTHUC",
			"txtPHONG_MO_ID",
			"cboPHONG_MO"
			,"txtBUAANCUOITRUOCMO"
			,"chkRANGGIAKHONG"
			,"chkRANGGIATHAODUOC"
			,"chkRANGGIACODINH"
			,"txtBENHLAYNHIEM"
			,"chkNGHIENTHUOCLA"
			,"chkNGHIENRUOU"
			,"chkNGHIENMATUY"
			,"txtKHAMLAMSANG"
			,"txtTUANHOAN"
			,"txtCOTSONG"
			,"txtXETNGHIEMBATTHUONG"
			,"txtYEUCAUBOSUNG"
			,"txtDUKIENCACHVATHUOCVOCAM"
			,"txtDUKIENGIAMDAUSAUMO"
			,"txtNGAYTHAMLAITRUOCMO"
			,"spNGAYTHAMLAITRUOCMO"
			,"txtASA"
			,"chkCAPCUU"
			,"chkDADAY"
			,"txtTUANHOANM"
			,"txtTUANHOANHA"
			,"txtMOLANTRUOC"
			,"txtTAIBIEN"
			,"txtTIMMACHTS"
			,"txtHOHAPTS"
			,"txtGIADINH"
			,"txtBIEUHIEN"
			,"txtTONGTRANG"
			,"txtCOQUANKHAC"
			,"txtANUONG"
			,"txtKINHCHOT"
			,"txtGANTHAN"
			,"txtYEUCAUDACBIET"
			,"txtKETLUAN"
		];
		for (var i = 0; i< idKhamMes.length; i++) {
			$("#" + idKhamMes[i]).prop('disabled', true);
		}
	}

	function fillThongTinKhamMe(data){
		FormUtil.setObjectToForm("divKhamMeMini","",data);
		FormUtil.setObjectToForm("divKhamMe","",data);

		if (data.GAYMECHINH != '-1') {
			var option = $('<option value="' + data.GAYMECHINH + '">' + data.GAYMECHINH_FULLNAME + '</option>');
			$("#cboGAYMECHINH").empty();
			$("#cboGAYMECHINH").append(option);
		}
		if (data.PHUME != '-1') {
			var option = $('<option value="' + data.PHUME + '">' + data.PHUME_FULLNAME + '</option>');
			$("#cboPHUME").empty();
			$("#cboPHUME").append(option);
		}
		if (data.DCVONGTRONG != '-1') {
			var option = $('<option value="' + data.DCVONGTRONG + '">' + data.DCVONGTRONG_FULLNAME + '</option>');
			$("#cboDCVONGTRONG").empty();
			$("#cboDCVONGTRONG").append(option);
		}
		if (data.DCVONGNGOAI != '-1') {
			var option = $('<option value="' + data.DCVONGNGOAI + '">' + data.DCVONGNGOAI_FULLNAME + '</option>');
			$("#cboDCVONGNGOAI").empty();
			$("#cboDCVONGNGOAI").append(option);
		}
		if (data.BSHDGAYME != '-1') {
			var option = $('<option value="' + data.BSHDGAYME + '">' + data.BSHDGAYME_FULLNAME + '</option>');
			$("#cboBSHDGAYME").empty();
			$("#cboBSHDGAYME").append(option);
		}
	}

	function clearInputKhamMe() {
		var idKhamMes = ["cboPPVCAM", "txtGAYMECHINH_ID", "cboGAYMECHINH", "btnCLEARBS_GAYMECHINH",
			"txtKTGAYME", "txtPHUME_ID", "cboPHUME", "btnCLEARBS_PHUME", "txtNCNHIEMTRUNG",
			"txtDCVONGTRONG_ID", "cboDCVONGTRONG", "btnCLEARBS_DCVONGTRONG", "txtTIENME",
			"txtDCVONGNGOAI_ID", "cboDCVONGNGOAI", "btnCLEARBS_DCVONGNGOAI", "txtBSHUONGDANGAYME_ID",
			"cboBSHDGAYME", "btnCLEARBS_BSHUONGDANGAYME", "txtNOIKHOA", "txtNGOAIKHOA",
			"txtTHUOC", "txtGAYME", "txtDIUNG", "txtTHUCAN", "txtHENPQ", "txtKHAC", "txtTMCHAYMAU",
			"txtDANGDT", "txtDENGHI", "txtHOHAP", "txtTANSOHH", "txtNGHEHH", "txtTIMMACH", "txtTANSOTM",
			"txtKHOTHOTM", "txtHUYETAPTM", "txtNGHETM", "txtTIEUHOA", "txtNOITIET", "txtTHANKINH", "txtBENHKHAC",
			"txtDNKQ", "txtMALL", "txtHAMIENG", "txtNGUACO", "txtKCGIAPCAM", "txtNHOMMAU",
			"txtTTRM", "txtYCMAU", "txtBASO", "txtKCONG", "txtXQUANGPHOI", "txtLYM",
			"txtNACONG", "txtMRI", "txtMONO", "txtCLO", "txtCTSCANNER", "txtNEU", "txtCACONG", "txtEOS",
			"txtGPT", "txtHBG", "txtGOT", "txtHTC", "txtPROTEIN", "txtSIEUAMBUNG", "txtMCH", "txtURE",
			"txtSIEUAMTIM", "txtCGV", "txtGLUCOSE", "txtPLT", "txtHIV", "txtRBC"
			,"txtBUAANCUOITRUOCMO"
			,"chkRANGGIAKHONG"
			,"chkRANGGIATHAODUOC"
			,"chkRANGGIACODINH"
			,"txtBENHLAYNHIEM"
			,"chkNGHIENTHUOCLA"
			,"chkNGHIENRUOU"
			,"chkNGHIENMATUY"
			,"txtKHAMLAMSANG"
			,"txtTUANHOAN"
			,"txtCOTSONG"
			,"txtXETNGHIEMBATTHUONG"
			,"txtYEUCAUBOSUNG"
			,"txtDUKIENCACHVATHUOCVOCAM"
			,"txtDUKIENGIAMDAUSAUMO"
			,"txtNGAYTHAMLAITRUOCMO"
			,"txtMOLANTRUOC"
			,"txtTAIBIEN"
			,"txtTIMMACHTS"
			,"txtHOHAPTS"
			,"txtGIADINH"
			,"txtBIEUHIEN"
			,"txtTONGTRANG"
			,"txtCOQUANKHAC"
			,"txtANUONG"
			,"txtKINHCHOT"
			,"txtGANTHAN"
			,"txtYEUCAUDACBIET"
			,"txtKETLUAN"
		];

		for (var i = 0; i< idKhamMes.length; i++) {
			$("#" + idKhamMes[i]).val('');
		}

		$("#cboGAYMECHINH").empty();
		$('#cboGAYMECHINH').append($('<option>', {
			value: '-1',
			text : '--Lựa chọn--'
		}));

		$("#cboPHUME").empty();
		$('#cboPHUME').append($('<option>', {
			value: '-1',
			text : '--Lựa chọn--'
		}));

		$("#cboDCVONGTRONG").empty();
		$('#cboDCVONGTRONG').append($('<option>', {
			value: '-1',
			text : '--Lựa chọn--'
		}));

		$("#cboDCVONGNGOAI").empty();
		$('#cboDCVONGNGOAI').append($('<option>', {
			value: '-1',
			text : '--Lựa chọn--'
		}));

		$("#cboBSHDGAYME").empty();
		$('#cboBSHDGAYME').append($('<option>', {
			value: '-1',
			text : '--Lựa chọn--'
		}));

	}

	function _bindEvent() {
		ComboUtil.getComboTag("cboKHOA", 'NTU02D132.11', [], "", {
			text: "--- Tất cả ---",
			value: -1
		}, 'sql', '', function () {
			loadGridData();
		});

		GridUtil.setGridParam(_gridDuyetMo, {
			onSelectRow: function (id) {
				GridUtil.unmarkAll(_gridDuyetMo);
				GridUtil.markRow(_gridDuyetMo, id);
				if (id) {
					var _row = $("#" + _gridDuyetMo).jqGrid('getRowData', id);
					_lichmoid = _row.LICHMOID;
					_hosobenhanid = _row.HOSOBENHANID;
					_benhnhanid = _row.BENHNHANID;
					_khambenhid = _row.KHAMBENHID;
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D132.02", _lichmoid);
					if (data_ar != null && data_ar.length > 0) {
						var row = data_ar[0];
						FormUtil.clearForm("inputForm", "");
						FormUtil.setObjectToForm("inputForm", "", row);
						viewTT(row);
						loadGridLichSu();
					}

					disableInputKhamMe();
					clearInputKhamMe();
					var obj = new Object();
					obj._lichmoid = _lichmoid;
					obj._khambenhid = _khambenhid;
					var _par = JSON.stringify(obj);
					var sqlID = "NTU02D133.L01";
					if (isDuLieuKhamMeTuDuyetMo) {
						sqlID = "NTU02D133.L01.2";
					}
					var data_KhamMe = jsonrpc.AjaxJson.ajaxCALL_SP_O(sqlID, _par);
					if (data_KhamMe != null && data_KhamMe.length > 0) {
						var row = data_KhamMe[0];
						fillThongTinKhamMe(row);
					}
					$("#btnSua").prop('disabled', false);
					$("#btnLuu").prop('disabled', true);
					$("#btnHuy").prop('disabled', true);
					$("#btnDuyetKhamMe").prop('disabled', false);
					$("#btnHuyKhamMe").prop('disabled', true);
					$("#btnTuChoi").prop('disabled', false);
					$("#btnInPhieuThongQuaMo").prop('disabled', false);
					$("#btnInPhieuGayMe").prop('disabled', false);
					$("#btnPhieuKhamMeTruocMo").prop('disabled', false);
					if(_row.trangthaikhamme == 1) {
						$("#btnHuyKhamMe").prop('disabled', false);
						$("#btnDuyetKhamMe").prop('disabled', true);
					}
				}
			},
			loadComplete: function(data){
				if (isDuLieuKhamMeTuDuyetMo) {
					changeColorRowKhamMe();
				}
				var ids = $("#" + _gridDuyetMo).getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = $("#" + _gridDuyetMo).jqGrid('getRowData',id);
					var _icon = '';
					if(row.FLAG_CA == '1'){
						_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
					}
					$("#" + _gridDuyetMo).jqGrid ('setCell', id, 'ICON', _icon);
				}
			}
		});

		GridUtil.setGridParam(_gridLSM, {
			onSelectRow: function (id) {
				GridUtil.unmarkAll(_gridLSM);
				GridUtil.markRow(_gridLSM, id);
				if (id) {
					var _row = $("#" + _gridLSM).jqGrid('getRowData', id);
					_lichmoid = _row.LICHMOID;
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D132.02", _lichmoid);
					if (data_ar != null && data_ar.length > 0) {
						var row = data_ar[0];
						FormUtil.clearForm("inputForm", "");
						FormUtil.setObjectToForm("inputForm", "", row);
						viewTT(row);
					}
				}
			}
		});

		$("#btnTIMKIEM").click(function () {
			loadGridData();
		});

		$("#btnEXCEL").click(function () {
			var par = [
				{
					name: 'i_tungay',
					type: 'String',
					value: $('#txtTUNGAY').val() + ''
				},
				{
					name: 'i_denngay',
					type: 'String',
					value: $('#txtDENNGAY').val() + ''
				},
				{
					name: 'i_khoaid',
					type: 'String',
					value: $('#cboKHOA').val() + ""
				},
				{
					name: 'i_trangthai',
					type: 'String',
					value: $('#cboTRANGTHAIPHIEU').val() + ""
				}
				];
			var rpName= "RPT_DANHSACH_BENHNHAN_DUYETMO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
			CommonUtil.inPhieu('window', "RPT_DANHSACH_BENHNHAN_DUYETMO", 'xlsx', par, rpName);
		});

		$("#btnVIEW_HOICHAN").click(function () {
			$('#dsLSDuyetMo').hide();
			$('#search').hide();
			$('#dsDuyetMo').hide();
			$('#closeHoiChuan').show();
			$('#tcHoiChuan').show();
			$('#tcHoiChuan').ntu02d032_phc({
				_grdHoiChan : 'grdPhieuHoiChuan',
				_khambenhid: _khambenhid,
				_benhnhanid: _benhnhanid,
				_lnmbp:	"15",
				_modeView: "1", // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		$("#btnCloseHoiChuan").click(function () {
			$('#dsLSDuyetMo').show();
			$('#search').show();
			$('#dsDuyetMo').show();
			$('#closeHoiChuan').hide();
			$('#tcHoiChuan').hide();
			$('#txtMACHANDOAN').focus();
		});

		$("#btnKySo").on("click", function(e) {
			_kySo();
		});
		$("#btnHuyKy").on("click", function(e) {
			_huyKySo();
		});
		$("#btnInKySo").on("click", function(e) {
			_inPhieuKySo();
		});

	}

	function _kySo() {
		var khambenhid = _khambenhid;
		if (khambenhid == ''){
			DlgUtil.showMsg("Bạn chưa khám mê!");
			return;
		}

		EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
			causer = e.username;
			capassword = e.password;
			DlgUtil.close("divCALOGIN");
			_caRpt('1');
		});
		EventUtil.setEvent("dlgCaLogin_close", function(e) {
			DlgUtil.close("divCALOGIN");
		});
		var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
		var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
		popup.open("divCALOGIN");
	}

	function _huyKySo() {
		var khambenhid = _khambenhid;
		if (khambenhid == ''){
			DlgUtil.showMsg("Bạn chưa khám mê!");
			return;
		}

		EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
			causer = e.username;
			capassword = e.password;
			DlgUtil.close("divCALOGIN");
			_caRpt('2');
		});
		EventUtil.setEvent("dlgCaLogin_close", function(e) {
			DlgUtil.close("divCALOGIN");
		});
		var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
		var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
		popup.open("divCALOGIN");
	}

	function _inPhieuKySo() {
		var khambenhid = _khambenhid;
		if (khambenhid == ''){
			DlgUtil.showMsg("Bạn chưa khám mê!");
			return;
		}

		var par_rpt_KySo = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : _hosobenhanid
			},
			{
				name: 'khambenhid',
				type: 'String',
				value: khambenhid
			}
		];

		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});

		CommonUtil.openReportGetCA2(par_rpt_KySo,false);
	}

	function _caRpt(signType) {
		var khambenhid = _khambenhid;
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'hosobenhanid',
			type : 'String',
			value : _hosobenhanid
		},
			{
				name: 'khambenhid',
				type: 'String',
				value: khambenhid
			}
		];
		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});
		//ky
		var oData = {
			sign_type : signType,
			causer : causer,
			capassword : capassword,
			params : par_rpt_KySo
		};
		var msg = CommonUtil.caRpt(oData, _rpt_code_kyso, true);
		DlgUtil.showMsg(msg);
	}



	function loadGridData() {
		// var _sql_par =
		// 	[
		// 		{"name": "[0]", "value": $("#txtNGAYTAO").val()},
		// 		{"name": "[1]", "value": $("#cboTRANGTHAIPHIEU").val()},
		// 		{"name": "[2]", "value": $("#cboKHOA").val()}
		// 	];
		// // clone từ hàm NTU02D132.01 - ntu02d132_ds_duyetmo và đổi điều kiện truy vấn
		// 	GridUtil.loadGridBySqlPage(_gridDuyetMo, "NTU02D133.L04", _sql_par );

		var obj = {
			ngayTao: $("#txtNGAYTAO").val() + "",
			tuNgay: $("#txtTUNGAY").val() + "",
			denNgay: $("#txtDENNGAY").val() + "",
			trangThaiPhieu: $("#cboTRANGTHAIPHIEU").val() + "",
			khoa: $("#cboKHOA").val() + "",
		}
		var param = [{name: "[0]", value: JSON.stringify(obj)}];
		GridUtil.loadGridBySqlPage("grdDSDuyetMo", "NTU02D133.L04.2", param); // thay đổi page size mặc định chạy vào đây
	}

	function loadGridLichSu() {
		var _sql_par =
			[
				{"name": "[0]", "value": _hosobenhanid}
			];
		GridUtil.loadGridBySqlPage(_gridLSM, "NTU02D132.04", _sql_par);
	}

	function viewTT(data) {
		if (data.PHIEU_HOI_CHAN == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHIEU_HOI_CHAN").empty();
			$("#cboPHIEU_HOI_CHAN").append(option);
		} else {
			var option = $('<option value="' + data.PHIEU_HOI_CHAN + '">' + data.PHIEU_HOI_CHAN + '</option>');
			$("#cboPHIEU_HOI_CHAN").empty();
			$("#cboPHIEU_HOI_CHAN").append(option);
		}
		if (data.PHONG_MO == '-1' || data.PHONG_MO == '') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHONG_MO").empty();
			$("#cboPHONG_MO").append(option);
		} else {
			var option = $('<option value="' + data.PHONG_MO + '">' + data.PHONG_MO_FULLNAME + '</option>');
			$("#cboPHONG_MO").empty();
			$("#cboPHONG_MO").append(option);
		}
		if (data.BS_CHINH != '-1') {
			var option = $('<option value="' + data.BS_CHINH + '">' + data.BS_CHINH_FULLNAME + '</option>');
			$("#cboBS_CHINH").empty();
			$("#cboBS_CHINH").append(option);
		}
		if (data.BS_PHU1 == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBS_PHU1").empty();
			$("#cboBS_PHU1").append(option);
		} else {
			var option = $('<option value="' + data.BS_PHU1 + '">' + data.BS_PHU1_FULLNAME + '</option>');
			$("#cboBS_PHU1").empty();
			$("#cboBS_PHU1").append(option);
		}

		if (data.BS_PHU2 == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBS_PHU2").empty();
			$("#cboBS_PHU2").append(option);
		} else {
			var option = $('<option value="' + data.BS_PHU2 + '">' + data.BS_PHU2_FULLNAME + '</option>');
			$("#cboBS_PHU2").empty();
			$("#cboBS_PHU2").append(option);
		}
		if (data.BS_PHU3 == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBS_PHU3").empty();
			$("#cboBS_PHU3").append(option);
		} else {
			var option = $('<option value="' + data.BS_PHU3 + '">' + data.BS_PHU3_FULLNAME + '</option>');
			$("#cboBS_PHU3").empty();
			$("#cboBS_PHU3").append(option);
		}

		if (data.VC_CHINH == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboVC_CHINH").empty();
			$("#cboVC_CHINH").append(option);
		} else {
			var option = $('<option value="' + data.VC_CHINH + '">' + data.VC_CHINH_FULLNAME + '</option>');
			$("#cboVC_CHINH").empty();
			$("#cboVC_CHINH").append(option);
		}

		if (data.VC_PHU == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboVC_PHU").empty();
			$("#cboVC_PHU").append(option);
		} else {
			var option = $('<option value="' + data.VC_PHU + '">' + data.VC_PHU_FULLNAME + '</option>');
			$("#cboVC_PHU").empty();
			$("#cboVC_PHU").append(option);
		}

		if (data.XEP_LICH == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboXEP_LICH").empty();
			$("#cboXEP_LICH").append(option);
		} else {
			var option = $('<option value="' + data.XEP_LICH + '">' + data.XEP_LICH_FULLNAME + '</option>');
			$("#cboXEP_LICH").empty();
			$("#cboXEP_LICH").append(option);
		}

		if (data.HUU_TRUNG == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboHUU_TRUNG").empty();
			$("#cboHUU_TRUNG").append(option);
		} else {
			var option = $('<option value="' + data.HUU_TRUNG + '">' + data.HUU_TRUNG_FULLNAME + '</option>');
			$("#cboHUU_TRUNG").empty();
			$("#cboHUU_TRUNG").append(option);
		}



		if (data.BS_HOICHAN == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBS_HOICHAN").empty();
			$("#cboBS_HOICHAN").append(option);
		} else {
			var option = $('<option value="' + data.BS_HOICHAN + '">' + data.BS_HOICHAN_FULLNAME + '</option>');
			$("#cboBS_HOICHAN").empty();
			$("#cboBS_HOICHAN").append(option);
		}
		if (data.BS_TANGCUONG == '-1') {
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBS_TANGCUONG").empty();
			$("#cboBS_TANGCUONG").append(option);
		} else {
			var option = $('<option value="' + data.BS_TANGCUONG + '">' + data.BS_TANGCUONG_FULLNAME + '</option>');
			$("#cboBS_TANGCUONG").empty();
			$("#cboBS_TANGCUONG").append(option);
		}
	}

	function stringToDateTime(date) {
		if (date != null && date.length > 0) {
			var parts = date.split("/");
			var ret = new Date(parts[2], parts[1] - 1, parts[0]);
			return ret;
		}
		return "";
	}

	function checkPhieuGayMeHoiSuc(i_hosobenhanid,i_tenbenhnhan,i_khambenhid){
		var ttBenhVien = getTTBenhVien();
		var ttKhac = getTTKhacGmhs(i_khambenhid);
		var param = get_TTHC_GMHS(i_khambenhid);
		if(param != null){
			var obj = {
				hosobenhanid: i_hosobenhanid,
				soyte: ttBenhVien.TENSOYTE,
				tendv: ttBenhVien.TENBENHVIEN,
				khoa: opt._dept_name,
				svv: param.SOVAOVIEN,
				ten: i_tenbenhnhan,
				tuoi: param.TUOI,
				gioitinh: param.GIOITINHID,
				buong: opt._subdept_name,
				giuong: param.GIUONG,
				cannang: ttKhac.CANNANG,
				chieucao: ttKhac.CHIEUCAO,
				nhommau: "",
				ppvocam: $('#cboPPVCAM option:selected').text(),
				bsphauthuat: $('#cboBS_CHINH option:selected').text(),
				ngaygayme: "",
				bsgayme: $('#cboGAYMECHINH option:selected').text(),
				tuthe: "",
				tienme: $('#txtTIENME').val(),
				tacdung: "",
				chandoan: ttKhac.CHANDOAN
			}
			var _par = JSON.stringify(obj);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D125.01", _par);
			var data = $.parseJSON(result);
			if (data != '-1') {
				console.log('-- checkPhieuGayMeHoiSuc success');
			} else {
				console.log('-- checkPhieuGayMeHoiSuc fail');
			}
			return data; // r_id_pgmhs
		}else{
			return DlgUtil.showMsg("Không đủ tham số!");
		}

	}

	function getTTBenhVien() {
		var ttBenhVien = {
			TENBENHVIEN: "Bệnh viện ......................",
			TENSOYTE: "Sở y tế ......................"
		};
		var obj = new Object();
		obj.hospital_id = _opts.hospital_id;
		var _par = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D113.04", _par);
		if (result.length > 0) {
			ttBenhVien = result[0];
		}
		return ttBenhVien;
	}
	function getTTKhacGmhs(i_khambenhid){
		var ttKhac = {
			CANNANG: "",
			CHIEUCAO: "",
			CHANDOAN: ""
		};
		var obj = new Object();
		obj.khambenhid = i_khambenhid;
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.08", JSON.stringify(obj));
		if (result.length > 0) {
			ttKhac =  result[0];
		}
		return ttKhac;
	}

	function get_TTHC_GMHS(khambenhid){
		var obj = {
			khambenhid: khambenhid,
		}
		var _par = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.09", _par);
		if (result.length > 0) {
			var tthc = result[0];
			return tthc;
		}
	}

	function changeColorRowKhamMe(){
		var ids = $("#"+_gridDuyetMo).getDataIDs();
		for(var i = 0; i < ids.length; i++){
			var id = ids[i];
			var row = $("#"+ _gridDuyetMo).jqGrid('getRowData',id);

			// đã duyệt
			if(row.trangthaikhamme == 1){
				$("#"+ _gridDuyetMo).jqGrid('setRowData', id, "", {
					color : 'blue'
				});
			}
			// từ chôi
			else if(row.trangthaikhamme == 2){
				$("#"+ _gridDuyetMo).jqGrid('setRowData', id, "", {
					color : 'red'
				});
			}
		}
	}

	function getCauHinh(tenCauHinh){
		var giaTriCauHinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", tenCauHinh);
		return giaTriCauHinh;
	}
}
