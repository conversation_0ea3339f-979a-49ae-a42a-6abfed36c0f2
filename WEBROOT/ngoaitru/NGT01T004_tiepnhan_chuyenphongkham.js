// manhnv - 25/08/2016

function NGT01T004_TIEPNHAN_CHUYENPHONGKHAM(opt) {		
	this.load=doLoad;
	var _opt = opt;
	var _sql=["NGTPK.CP", "NGTPK.TP"];
	var _khamthemphongdt = "0"; 
	var checkSLKham = 0;
	
	function doLoad() {	
		_initControl();
		_bindEvent();
	}
	
	//hunglv
	var _gridDSCHHeader="phongkhamdangkyid,phongkhamdangkyid,50,0,t,l,1,2;" +
			"<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>m,yeucaukham,20,0,f,l,1,2;" +
			"Phòng,org_name,30,0,f,l,1,2";	
	var _gridDSCHId="grdDanhSachDV"; 	
	function loadGridData() {
		var _sql_par=[{"name":"[0]", "value":_opt.khambenhid}];
		GridUtil.loadGridBySqlPage(_gridDSCHId,"NGT01T004.SELECT",_sql_par);
	}
	//end hunglv
	
	function _initControl(){
//		var par11 = ['NGT_KHAMTHEMPHONG_DT']; 
//		_khamthemphongdt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par11.join('$'));
//		if (_khamthemphongdt != "0" && _khamthemphongdt.indexOf(_opt.doituongbenhnhanid + ",") == "-1"){
//			DlgUtil.showMsg("Đối tượng này không được cấu hình để khám nhiều phòng", function(){
//				parent.DlgUtil.close("dlgPhieuKham");
//				var evFunc=EventUtil.getEvent("assignSevice_closechuyenphong");
//				if(typeof evFunc==='function') {
//					evFunc({msg:''});
//				}else {
//					console.log('evFunc not a function');
//				}
//			});
//			return; 
//		}
		
		//hunglv
		if(_opt.kieu == 2){			
			GridUtil.init(_gridDSCHId,"99%","130","Phòng khám đã đăng ký",false,_gridDSCHHeader,true);
			loadGridData();
		}
		else{
			$("#inputForm").hide();
		}
		//end hunglv
		
		$('#hidKHAMBENHID').val(_opt.khambenhid);
		$('#hidTIEPNHANID').val(_opt.tiepnhanid);
		
		var sSql = "";
		sSql = (_opt.kieu == 1)?_sql[0]:_sql[1];		
		//ComboUtil.getComboTag("cboDICHVUID","NGTDV.002",[{"name":"[0]","value":_opt.doituongbenhnhanid}],_opt.dichvuid,{value:'',text:'Chọn yêu cầu khám'},'sql','', '');

		var par11 = ['LOAD_YEUCAUKHAM_THEO_DT']; 
		var sql_par = []; 
		var LOAD_YEUCAUKHAM_THEO_DT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par11.join('$'));
		
		if(LOAD_YEUCAUKHAM_THEO_DT != "0"){
			sql_par.push({"name":"[0]","value":_opt.doituongbenhnhanid}); 
		}else{
			sql_par.push({"name":"[0]","value":"0"});
		}
		ComboUtil.getComboTag("cboDICHVUID","NGTDV.002",sql_par,_opt.dichvuid,{value:'',text:'Chọn yêu cầu khám'},'sql','', '');
		
		if($('#cboDICHVUID').val() != 0){
			_loadPhongKham($('#cboDICHVUID').val());
		}
		
		if(_opt.kieu == "0"){
			$('#cboDICHVUID').attr('disabled','disabled');
			$('#cboPHONGID').focus();
		}else{
			if(_opt.kieu == "2"){
				$('#cboDICHVUID').attr('disabled',false);
			}
			$('#cboDICHVUID').focus();
		}
	}
	
	function _bindEvent() {			
		$("#btnHuy").on("click",function(e){
			parent.DlgUtil.close("dlgPhieuKham");
			var evFunc=EventUtil.getEvent("assignSevice_closechuyenphong");
			if(typeof evFunc==='function') {
				evFunc({msg:''});
			}
			else {
				console.log('evFunc not a function');
			}
		});
		
		$("#btnInPhieuKham").on("click",function(e){
			_inPhieuKham();
		});
		
		$("#btnLuu").on("click",function(e){
			if($('#cboPHONGID').val() == '0'){
				setErrValidate('cboPHONGID');
				DlgUtil.showMsg('Hãy chọn phòng');
				return false;
			}
			
			if(_opt.kieu != 0){
				if(_checksoluongmaxphongkham($('#cboPHONGID').val()) == '-1'){
					DlgUtil.showMsg('Phòng khám hết số');
					return;
				}
			}
			
			//=========== check dich vu thanh toan dong thoi; 
			var par11 = ['HIS_CANHBAO_KHONG_TTDT']; 
			var checkTt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par11.join('$'));
						
			var msgCheckTt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV018",$('#hidTIEPNHANID').val());
			     if(msgCheckTt && msgCheckTt != ''){
			      DlgUtil.showMsg('Các dịch vụ ' + msgCheckTt + ' miễn giảm thanh toán đồng thời');
			      if(checkTt == '1'){
			       return;
			      }
		     }
			//=========== 
			
			_objData = {
				khambenhid : $("#hidKHAMBENHID").val(),
				phongid : $('#cboPHONGID').val(),
				dichvuid : $('#cboDICHVUID').val(),
				kieu : _opt.kieu,
				phongkhamdangkyid : _opts.phongkhamdangkyid, 
				maubenhphamid : _opts.maubenhphamid
			};
			
			_luudata(_objData);
			
		});
		
		// thay doi thong tin dich vu
		$('#cboDICHVUID').change(function(){
			_loadPhongKham($(this).val());
		});
	}
	
	function _checksoluongmaxphongkham(_phongid){				
		var rets = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.MAXPHONGKHAM",_phongid + '$');
		return rets;
	}
	
	function _inPhieuKham(){
		var objData = new Object();
		FormUtil.setFormToObject("tabTiepNhan","",objData);
		var par = [ {
			name : 'khambenhid',
			type : 'String',
			value : _opt.khambenhid
		}, {
			name : 'phongid',
			type : 'String',
			value : $('#cboPHONGID').val()
		}];

		if (_opt.khambenhid != null && _opt.khambenhid > 0) {
			CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par);
		}
		else{
			DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
		}
	}
	
	function _luudata(_objData){		
		if(_opt.doituongbenhnhanid == 1 && checkSLKham == 0){
			var soluong = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'CHECKTN65BHYT');
			if(parseInt(soluong) > 0){
				var par = [];
				par.push({
					"name" : "[0]",
					"value" : $("#cboPHONGID").val()
				});
				var so_bn_bhyt=0;
				var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT01T001.65BHYT", par);
				var row = JSON.parse(data);
				if (row != null && row.length > 0) { 
					so_bn_bhyt = row[0].DEM; // là số bn bhyt đã khám trong ngày
				}
				if (parseInt(so_bn_bhyt)>=parseInt(soluong)){
						DlgUtil.showConfirm("Phòng khám vượt quá "+soluong+" BN/BHYT trong 1 ngày. Có muốn tiếp tục tiếp nhận hay không?",function(flag) {
							if (flag) {
								checkSLKham = 1;
								_luudata(_objData);
							}
							else{
								return false;
							}
						});				
						return false;
				}
			}
		}
		checkSLKham = 0;	
		
		var rets = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T001.TCPK",JSON.stringify(_objData));	
		ret = rets.split(',');
		
		if(ret[0] == 1){
			var _scc = ret[2] != "" ? "Số cấp cứu " + ret[2] : "";
			$('#lblSTTMOI').text('Bệnh nhân có số thứ tự mới: ' + ret[1] + ". " + _scc);
			DlgUtil.showMsg('Cập nhật thông tin thành công');
			loadGridData();
		}else if(ret == "tontaipk"){
			DlgUtil.showMsg('Bệnh nhân đã đăng ký phòng khám '+$('#cboPHONGID option:selected').text());
		}else if(ret[0] == 'codonthuoc'){
			DlgUtil.showMsg('Bệnh nhân đang có đơn thuốc');
		}else if(ret[0] == 'loicongkhambdhni'){
			DlgUtil.showMsg('Lỗi tính tiền công khám BDHNI');
		}else if(ret[0] == 'dongbenhan'){
			DlgUtil.showMsg('Đã đóng bệnh án hoặc BN đã nhập viện. ');
		}	
			
		//tuyennx_add_start HISL2NT-896
		else if(ret[0] == 'dangkham'){
			DlgUtil.showMsg('Bệnh nhân đang khám không thể đổi phòng');
		}
		//tuyennx_add_end 
		
		else if(ret[0] == 'dangkhamdakham'){
			DlgUtil.showMsg('Bệnh nhân đang khám / đã khám xong. Không được đổi phòng. ');
		}else if(ret[0] == 'doituongktp'){
			DlgUtil.showMsg('Đối tượng này không được phép khám thêm phòng. ');
		}else if(ret[0] == 'trangthaiktp'){
			DlgUtil.showMsg('Trạng thái BN này không được khám thêm phòng');
		}else if(ret[0] == 'loicongkhambdhni'){
			DlgUtil.showMsg('Lỗi cập nhật tỷ lệ công khám. 10284');
		}else if(ret[0] == 'cocls'){
			DlgUtil.showMsg('Tồn tại dịch vụ (VD: CLS,PTTT,CĐHA,Thuốc...) trong phòng khám này, yêu cầu xóa phiếu trước khi đổi. ');
		}else{
			DlgUtil.showMsg('Cập nhật thông tin không thành công');
		}
	}
	
	function _loadPhongKham(dichvuid){
		var sql_par1=[];		
		sql_par1.push({"name":"[0]","value":dichvuid});
		
		ComboUtil.getComboTag("cboPHONGID","NGTPK.DV",sql_par1,_opt.phongid,{},'sql','','');
	}
}