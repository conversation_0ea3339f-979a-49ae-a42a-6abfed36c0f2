<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS01X016_CapNhatNguoiLayMau.js?v=20240202"></script>

<style>
	input[type='text'] {
		width: 100%;		
		margin: 0px auto;
		outline: none;
		border: 0px;
		line-height:20px;
		border: 1px solid lightgrey;
	}
	input[type='text']:focus {
		width: 100%;		
		margin: 0px auto;
		outline: none;
		border: 0px;
		border: 1px solid lightgrey;		
	}

	.FlashCode {
		position: relative;
	}

	.FlashCode > a {
		position: absolute;
		top: 50%;
		right: 5px;
		transform: translate(0, -50%);
	}

	#codeMayIn:disabled {
		background-color: lightgrey;
	}
</style>

<div id="divMain" width="100%" class="container">	
	<div id="divSuaNgayTraKQ" class="col-md-12 low-padding">
		<div class="form-inline" id="guiMau">
			<div class="col-xs-3 low-padding">
				<label>Gửi mẫu</label>
			</div>
			<div class="col-xs-9 low-padding">
				<input type="checkbox" id="chkGUIMAU">
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-3 low-padding">
				<label id="lblLoaiBenhPham">Loại bệnh phẩm</label>
			</div>
			<div class="col-xs-9 low-padding">
				<input type="text" id="txtLoaiBenhPham" />
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-3 low-padding">
				<label>Người lấy mẫu (*)</label>
			</div>
			<div class="col-xs-2 low-padding">
				<input type="text" id="txtBSThucHien" />
			</div>
			<div class="col-xs-7 low-padding">
				<select class="form-control input-sm" id="cboBSThucHien" filterlike="txtBSThucHien" >							
				</select>
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-5 low-padding">
				<label id="lblNgayLayMau">Ngày lấy mẫu (*)</label>
			</div>
			<div class="col-xs-7 low-padding">
				<div class="input-group">
					<input class="form-control input-sm" id="txtTGThucHien" name="txtTGThucHien" valrule="Thời gian thực hiện,required|datetime|max_length[19]" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGThucHien" type="sCal" onclick="NewCssCal('txtTGThucHien','ddMMyyyy','dropdown',true,'24',true)"></span>
				</div>
			</div>
		</div>			
		
		<div class="form-inline">
			<div class="col-xs-3 low-padding">
				<label id="lblNguoiGiaoMau" >Người giao mẫu</label>
			</div>
			<div class="col-xs-2 low-padding">
				<input type="text" id="txtNguoiGiaoMau" />
			</div>
			<div class="col-xs-7 low-padding">
				<select class="form-control input-sm" id="cboNguoiGiaoMau" filterlike="txtNguoiGiaoMau" >							
				</select>
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-5 low-padding">
				<label id="lblTGGiaoMau">Ngày giao mẫu</label>
			</div>
			<div class="col-xs-7 low-padding">
				<div class="input-group">
					<input class="form-control input-sm" id="txtTGGiaoMau" name="txtGiaoMau" valrule="Ngày giao mẫu|datetime|max_length[19]" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGGiaoMau" type="sCal" onclick="NewCssCal('txtTGGiaoMau','ddMMyyyy','dropdown',true,'24',true)"></span>
				</div>
			</div>
		</div>
		
		<div class="form-inline">
			<div class="col-xs-3 low-padding">
				<label id="lblNguoiNhanMau" >Người nhận mẫu</label>
			</div>
			<div class="col-xs-2 low-padding">
				<input type="text" id="txtNguoiNhanMau" />
			</div>
			<div class="col-xs-7 low-padding">
				<select class="form-control input-sm" id="cboNguoiNhanMau" filterlike="txtNguoiNhanMau" >							
				</select>
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-5 low-padding">
				<label id="lblNgayNhanMau" >Ngày nhận mẫu</label>
			</div>
			<div class="col-xs-7 low-padding">
				<div class="input-group">
					<input class="form-control input-sm" id="txtTGNhanMau" name="txtGiaoMau" valrule="Ngày nhận mẫu|datetime|max_length[19]" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGNhanMau" type="sCal" onclick="NewCssCal('txtTGNhanMau','ddMMyyyy','dropdown',true,'24',true)"></span>
				</div>
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-2 low-padding">
				<label>Máy in</label>
			</div>
			<div class="col-xs-5 low-padding" id="DSMayin">
			</div>
			<div class="col-xs-2 low-padding">
				<label>Code</label>
			</div>
			<div class="col-xs-3 low-padding FlashCode">
				<input type="text" id="codeMayIn" disabled />
				<a href=""><i class="fa fa-pencil" aria-hidden="true"></i></a>
			</div>
		</div>
		<div id ="nhapTuDonViKhac"  class="col-md-12 low-padding" style="display: none"> 
			<div class="form-inline">
				<div class="col-xs-3 low-padding">
					<label id="lblNoiGiaoMau">Nơi giao mẫu</label>
				</div>
				<div class="col-xs-9 low-padding">
					<input type="text" id="txtNoiGiaoMau" />
				</div>
			</div>
			<div class="form-inline">
				<div class="col-xs-3 low-padding">
					<label id="lblNguoiGiaoMau2">Người giao mẫu</label>
				</div>
				<div class="col-xs-9 low-padding">
					<input type="text" id="txtNguoiGiaoMau2" />
				</div>
			</div>
			<div class="form-inline">
				<div class="col-xs-5 low-padding">
					<label id="lblTGLayMau2">Ngày lấy mẫu</label>
				</div>
				<div class="col-xs-7 low-padding">
					<div class="input-group">
						<input class="form-control input-sm" id="txtTGLayMau2" name="txtTGLayMau2" valrule="Thời gian lấy mẫu,required|datetime|max_length[19]" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGLayMau2" type="sCal" onclick="NewCssCal('txtTGLayMau2','ddMMyyyy','dropdown',true,'24',true)"></span>
					</div>
				</div>
			</div>
			<div class="form-inline">
				<div class="col-xs-3 low-padding">
					<label id="lblBSChiDinh">Bác sĩ chỉ định</label>
				</div>
				<div class="col-xs-9 low-padding">
					<input type="text" id="txtBSChiDinh" />
				</div>
			</div>
		</div>
		<div class="form-inline">
			<div class="col-xs-12 low-padding">
			    <button class="btn btn-sm btn-primary" id="btnLuu">
					<span class="glyphicon glyphicon-floppy-disk"></span> Lưu
				</button>
				<button class="btn btn-sm btn-primary" id="btnInBarcode">
					<span class="glyphicon glyphicon-print"></span> In barcode
				</button>	
				<button class="btn btn-sm btn-primary" id="btnClose">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng
				</button>
			</div>
		</div>
		
		<div class="form-inline">			
		</div>
	</div>	
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDKetQuaCLS" value="">

<div id="divTraKetQua2" style="width: 100%; display: none">
	<iframe src="" id="ifmView2" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang = "vn";
	var type = "{type}";
	var khoaId = {dept_id};
	var mode = '{showMode}';
		
	var maubenhphamid = getParameterByName('maubenhphamid',window.location.search.substring(1));
	
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	
	var _opts=new Object();	
	
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);	
		data=EventUtil.getVar("dlgVar");		
		_opts._param=session_par;
		_opts._uuid=uuid;
		_opts._idmaubenhpham=maubenhphamid;
	}
	
	initRest(_opts._uuid);
	
	var ds = new CapNhatDuLieu(_opts);
	ds.load();
</script>