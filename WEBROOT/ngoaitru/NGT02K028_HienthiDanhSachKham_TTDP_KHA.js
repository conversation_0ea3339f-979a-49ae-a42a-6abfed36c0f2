function ThongBaoBenhNhan(opt) {
    this.load = doLoad;
    var _opt = opt;

    function doLoad() {
        var _param = opt._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        _initControl();
    }

    function _initControl() {
        document.onwebkitfullscreenchange = fullscreenChanged;
        document.documentElement.onclick = goFullscreen;
        document.onkeydown = goFullscreen;
        $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham_TTDP_KHA&showMode=dlg");
        $('#tenPK_BS').text(_opt.subdept_name + ' - ' + _opt._username);
        _loadData();
        setInterval(function () {
            _loadData();
        }, 3000);
    }

    function _loadData() {
        var sql_par = [_opt.phongid];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par.join('$'));

        if (data_ar != null && data_ar.length > 0) {
            $('#lbSTT_DANGKHAM').text(data_ar[0].STT);
            $('#lbHOTEN_DANGKHAM').text(data_ar[0].HOTEN);
            $('#lbSTT_CHUANBI').text(data_ar[1].STT);
            $('#lbHOTEN_CHUANBI').text(data_ar[1].HOTEN);
        }
    }

    function goFullscreen() {
        // Must be called as a result of user interaction to work
        var isFirefox = typeof InstallTrigger !== 'undefined';
        mf = document.getElementById("main_frame");
        if (!isFirefox) {
            mf.webkitRequestFullscreen();
//        	 mf.style.display="";
        } else {
            mf.mozRequestFullScreen();
        }
    }

    function fullscreenChanged() {
        if (document.webkitFullscreenElement == null) {
            mf = document.getElementById("main_frame");
            mf.style.display = "none";
        }
    }
}

