/*
Mã màn hình  : NTU01H021
File mã nguồn : NTU01H021_PhieuTamUngBenhNhan.js
Mục đích  : Lap phieu tam ung cho benh nhan
Tham số vào : 
  khambenhid: id của khám bệnh
  BENHNHANID: id cua BENH NHAN 
Người lập tr<PERSON>nh	<PERSON> cập nhật  Ghi chú
HUNGNT	- 03122016 - Comment
*/
function VPI01T028_danhsach_phieuthu(opt) {
	var _gridPhieu = "grdPhieuThu";
	var grid = $("#" + _gridPhieu);
	// BVTM-676 start: thêm cột ICON - hiển thị trạng thái đã dùng
	//" ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;" + 
	var _gridHeader = "id,PHIEUTHUID,100,0,t,l,0;" + "id_org,PHIEUTHUID_ORG,100,0,t,l,0;" + "id người dùng,NGUOIDUNGID,100,0,t,l,0;" + "Mặc định,MACDINH,90,0,t,l,0;"
			+ "Đã hủy phiếu,DAHUYPHIEU,90,0,t,l,0;" + "Hủy giữ số,HUYGIUSO,90,0,t,l,0;" + "Số phiếu giữ,SOPHIEU_GIU,90,0,t,l,0;" + "Nhóm phiếu thu id,NHOMPHIEUTHUID,90,0,t,l,0;"
			+ "Trạng thái,TRANGTHAI_HDDT,80,0,f,c,0;" + "Số HĐĐT,INVOICES_PATTERN,80,0,t,l,0;" + "Số HĐĐT,INVOICES_SERIAL,80,0,t,l,0;" + "Số HĐĐT,INVOICES_NUMBER,80,0,f,l,0;"
			+ "Mã sổ,MANHOMPHIEUTHU,120,0,f,l,0;" + "Số phiếu từ,SOPHIEUTO,90,0,t,l,0;" + "Số phiếu đến,SOPHIEUFROM,90,0,t,l,0;" + "Số phiếu khóa từ,KHOASOPHIEUTU,90,0,t,l,0;"
			+ "Tổng tiền,TONGTIEN,80,decimal!3,t,r,0;" + "Tiếp nhận id,TIEPNHANID,100,0,t,l,0;" + "Mã phiếu thu,MAPHIEUTHU,100,0,f,l,0;" + "HTTT,HINHTHUCTHANHTOAN,100,0,t,l,0;"
			+ "Hình thức thanh toán,TEN_HTTT,130,0,f,l,0;" + "Số tiền,DATRA,69,decimal!3,f,r,0;" + "Miễn giảm,MIENGIAM,100,decimal!3,f,r,0;" + "Loại phiếu thu,LOAIPHIEUTHU,90,0,t,l,0;"
			+ "Loại phiếu thu id,LOAIPHIEUTHUID,150,0,t,l,0;" + "Loại phiếu,LOAIPHIEUTHUID_2,100,0,f,l,0;" + "Người thu,NGUOITHU,180,0,f,l,0;" + "Ngày thanh toán,NGAYTHU,120,0,f,c,0;"
			+ "Người chuyển đổi HĐ,NGUOICHUYENDOI,140,0,f,l,0;" + "Ngày chuyển đổi HĐ,THOIGIANCHUYENDOI,140,0,f,l,0;" + "Mã bệnh án,MAHOSOBENHAN,100,0,f,l,0;"
			+ "Mã bệnh nhân,MABENHNHAN,100,0,f,l,0;" + "Họ tên,TENBENHNHAN,100,0,f,l,0;" + "Ngày hoàn phiếu,NGAYHUY,120,0,f,l,0;" + "Người hoàn phiếu,NGUOIHUY,180,0,f,l,0;"
			+ "Ngày hủy HĐĐT,THOIGIANHUYHDDT,120,0,f,l,0;" + "Người hủy HĐĐT,NGUOIHUYHDDT,180,0,f,l,0;" + "Lý do hủy,LYDOHUYPHIEU,255,0,f,l,0;" + "Log Phiếu thu,PHIEUTHULOG,100,0,t,l,0;"
			+ "TENCONGTYBN,TENCONGTYBN,100,0,t,l,0;" + "DIACHI_CTYBN,DIACHI_CTYBN,100,0,t,l,0;" + "MASOTHUE_CTYBN,MASOTHUE_CTYBN,100,0,t,l,0;" + "TREOPHIEU,TREOPHIEU,100,0,t,l,0;"
			+ "SOTHANG_TREO,SOTHANG_TREO,100,0,t,l,0;" + "DAHOAN,DAHOAN,100,0,t,l,0;" + "YC_HOAN,YC_HOAN,100,0,t,l,0;" + "Loại tiếp nhận,LOAITIEPNHANID,100,0,t,l,0;"
			+ "YC_HOANPHIEU,YC_HOANPHIEU,100,0,t,l,0;" + "ID POS,IDPOST,100,0,t,l,0;" + "ID giao dịch,ORDERID,150,0,f,l,0;" + "Mã giao dịch,MA_GD_CK,150,0,f,l,0;"
			+ "Số tài khoản,SOTAIKHOAN,150,0,f,l,0;" + "PHIEUTHUID_SUDUNG,PHIEUTHUID_SUDUNG,100,0,t,l,0";
	var _gridProducts = "grdProducts";
	var _gridProductsHeader = "";
	/*
	var _gridProductsHeader = " ,ACTION,40,d,f,l;" + "Tên dịch vụ,ProdName,120,0,e,l,0;" + "Số lượng,ProdQuantity,120,0,e,l,0;" + "ĐVT,ProdUnit,120,0,f,l,0;" + "Đơn giá,ProdPrice,120,0,e,l,0;"
			+ "Tổng cộng,Total,120,0,e,l,0" + "Thành tiền,Amount,120,0,e,l,0";
	*/
	var that = this;
	this.load = doLoad;
	var _mode = '';//1 them, 2 cap nhat
	var dsCauHinhForm = new Object();
	function doLoad() {
		this.validator = new DataValidator("divPHIEUTHU");
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		initControl();
		bindEvent();
	}
	function initControl() {
		var str_ch = "VPI_DIEUCHINH_LAY_HD";
		var arr_dsCH = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		if (arr_dsCH.length > 0) {
			dsCauHinhForm = arr_dsCH[0];
		}
		GridUtil.init(_gridPhieu, "100%", "155px", "Danh sách hóa đơn", false, _gridHeader, false, {
			rowNum : 10,
			rowList : [ 10, 20, 30 ]
		});
		grid[0].toggleToolbar();
		loadGridData();
		// L2PT-30618 start
		if (opt.data.MODEAR == 1) {
			$("#spaDCTT").text("Điều chỉnh");
			$("#divLOAIDIEUCHINH").show();
		} else if (opt.data.MODEAR == 2) {
			$("#spaDCTT").text("Thay thế");
			$("#divLOAIDIEUCHINH").hide();
		}
		// L2PT-30618 end
	}
	function loadGridData() {
		_sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			"value" : JSON.stringify(opt.data)
		});
		// L2PT-32061 end
		// BVTM-23 start
		GridUtil.loadGridBySqlPage(_gridPhieu, "VPI01T001.03.RG", _sql_par, function() {
			var ids = grid.getDataIDs();
			for (var i = 0; i < ids.length; i++) {}
		});
		// BVTM-23 end
	}
	function bindEvent() {
		GridUtil.setGridParam(_gridPhieu, {
			onSelectRow : function(id, selected) {
				GridUtil.unmarkAll(_gridPhieu);
				GridUtil.markRow(_gridPhieu, id);
				setEnabled([ 'btnChon' ], []);
				var key = grid.jqGrid('getCell', id, 'PHIEUTHUID');
				layTTHoaDon(key);
			}
		});
		//them moi
		$("#btnChon").on("click", function() {
			var xml = "";
			var text_ar = opt.data.MODEAR == 1 ? "điều chỉnh" : "thay thế"; // L2PT-30618
			DlgUtil.showConfirm("Xác nhận " + text_ar + " hóa đơn", function(flag) {
				if (flag) {
					var selRowId = grid.jqGrid("getGridParam", "selrow");
					if (!selRowId) {
						DlgUtil.showMsg("Chưa chọn hóa đơn cần được " + text_ar);
						return;
					}
					var _phieuthuid_cu = grid.jqGrid('getCell', selRowId, 'PHIEUTHUID');
					// L2PT-30618 start
					var _loaidieuchinh = $("#cboLoaiDieuChinh").val();
					if (opt.data.MODEAR == 1) {
						if (typeof _loaidieuchinh == 'undefined' || _loaidieuchinh == "") {
							DlgUtil.showMsg("Chưa chọn loại điều chỉnh");
							return;
						} else {
							var objRoot = new Object();
							var AdjustInv = new Object();
							$("#divHoaDonDT").find('input').each(function() {
								AdjustInv[this.id.substr(3)] = this.value;
							});
							var _type = $("#cboLoaiDieuChinh").val();
							AdjustInv.Type = _type;
							var products = $("#" + _gridProducts).jqGrid('getRowData');
							for (var i = 0; i < products.length; i++) {
								if (_loaidieuchinh == 2) {
									if (parseFloat(products[i].ProdQuantity) < 0 || (products[i].ProdPrice && parseFloat(products[i].ProdPrice) < 0) || parseFloat(products[i].Amount) < 0) {
										DlgUtil.showMsg("Điều chỉnh tăng số lượng, đơn giá, thành tiền phải lớn hơn 0");
										return;
									}
								} else if (_loaidieuchinh == 3) {
									if (parseFloat(products[i].ProdQuantity) >= 0 || (products[i].ProdPrice && parseFloat(products[i].ProdPrice) >= 0) || parseFloat(products[i].Amount) >= 0) {
										DlgUtil.showMsg("Điều chỉnh giảm số lượng, đơn giá, thành tiền phải nhỏ hơn 0");
										return;
									}
								}
								delete products[i].ACTION;
							};
							var objProducts = {
								Product : products
							};
							AdjustInv.Products = objProducts;
							objRoot.AdjustInv = AdjustInv;
							xml = convertJSon2XML(JSON.stringify(objRoot));
							console.log(xml);
						}
					} else if (opt.data.MODEAR == 2) {
						_loaidieuchinh = '-1';
					}
					// L2PT-30618 end
					opt.data.PHIEUTHUID_CU = _phieuthuid_cu;
					opt.data.LOAIDIEUCHINH = _loaidieuchinh;
					opt.data.xmlDieuChinh = xml;
					closePage('assignSevice_chonDV', JSON.stringify(opt.data));
				}
			});
		});
		$("#cboLoaiDieuChinh").change(function() {
			hienThiForm();
		});
		$("#cboChonHD").change(function() {
			var selRowId = grid.jqGrid("getGridParam", "selrow");
			if (!selRowId) {
				return;
			}
			var key = grid.jqGrid('getCell', selRowId, 'PHIEUTHUID');
			layTTHoaDon(key);
		});
		$("#btnDong").on("click", function() {
			closePage('assignSevice_close', 'close');
		});
		$("#" + _gridProducts).bind("CustomAction", function(e, act, rid) {
			if (act == 'del') {
				$("#" + _gridProducts).jqGrid('delRowData', rid);
			}
		});
	}
	// ham xu ly close trang
	function closePage(_evt, _msg) {
		EventUtil.raiseEvent(_evt, {
			msg : _msg
		});
	}
	function layTTHoaDon(key) {
		var hoadonid_moi = opt.data._phieuthuid_hddt;
		var chonHD = $("#cboChonHD").val();
		if (chonHD == 2) {
			key = hoadonid_moi;
		}
		var fl = guiHDDTTheoPhieu(key, opt.data.dsCH, [], '', '1', '', '');
		/*var xmlDoc = $.parseXML(fl);
		var xml = $(xmlDoc);
		$("#divHoaDonDT").append(xmlToForm(xml[0].children[0]));*/
		FormUtil.clearForm('divHoaDonDT', "");
		$("#" + _gridProducts).jqGrid("clearGridData");
		var html_hd = xmlToForm(fl);
		$("#divHoaDonDT").html(html_hd);
		hienThiForm();
	}
	function xmlToForm(xml) {
		var ret = "";
		/*if (xml.children.length == 0) {
			ret += '<div class="col-xs-12 mgt5">' + '<div class="col-xs-3">' + '<label class="">' + xml.tagName + '</label>' + '</div>' + '<div class="col-xs-9">' + '<input id="txt' + xml.tagName +
					'" class="form-control input-sm" value="' + xml.textContent + '" />' + '</div>' + '</div>';
		} else {
			ret += '<div class="col-xs-12 mgt5" hidden>' + '<div class="col-xs-3">' + '<label class="">' + xml.tagName + '</label>' + '</div>' + '</div>';
			for (var i = 0; i < xml.children.length; i++) {
				ret += xmlToForm(xml.children[i]);
			}
		}*/
		var objRoot = JSON.parse(convertXml2JSon(xml));
		var objHoaDon = objRoot.Invoices.Inv.Invoice;
		ret += '<div class="col-xs-12 mgt5">' + '<div class="col-xs-3">' + '<label class="">' + 'key' + '</label>' + '</div>' + '<div class="col-xs-9">' + '<input id="txt' + 'key' +
				'" class="form-control input-sm" value="' + objRoot.Invoices.Inv.key + '" disabled/>' + '</div>' + '</div>';
		var keys = Object.keys(objHoaDon);
		for (var i = 0; i < keys.length; i++) {
			var propName = keys[i];
			var propValue = objHoaDon[propName];
			if (typeof propValue == 'object') {
				if (Array.isArray(propValue.Product)) {
					arrProduct = propValue.Product;
				} else {
					arrProduct = [ propValue.Product ]
				}
				if (arrProduct && arrProduct.length > 0) {
					if (_gridProductsHeader == "") {
						_gridProductsHeader = " ,ACTION,40,d,f,l";
						var keysP = Object.keys(arrProduct[0]);
						for (var j = 0; j < keysP.length; j++) {
							_gridProductsHeader += ";" + keysP[j] + "," + keysP[j] + ",120,0,e,l,0";
						}
						GridUtil.init(_gridProducts, "100%", "345px", "Chi tiết hóa đơn", false, _gridProductsHeader, true);
					}
					GridUtil.fetchGridData(_gridProducts, arrProduct);
					$("#" + _gridProducts).trigger("reloadGrid");
				}
			} else {
				ret += '<div class="col-xs-12 mgt5">' + '<div class="col-xs-3">' + '<label class="">' + propName + '</label>' + '</div>' + '<div class="col-xs-9">' + '<input id="txt' + propName +
						'" class="form-control input-sm" value="' + propValue + '" />' + '</div>' + '</div>';
			}
		}
		return ret;
	}
	function hienThiForm() {
		var _loaidieuchinh = $("#cboLoaiDieuChinh").val();
		if (opt.data.MODEAR == 2) {
			$("#" + _gridProducts).hideCol('ACTION');
			$('#divHoaDonDT').find(':input').prop('disabled', true);
		} else if (opt.data.MODEAR == 1) {
			$("#" + _gridProducts).showCol('ACTION');
			//if (_loaidieuchinh != 2 && _loaidieuchinh != 3) {
			if (!_loaidieuchinh || _loaidieuchinh == "") {
				$("#" + _gridProducts).hideCol('ACTION');
			}
		}
	}
}