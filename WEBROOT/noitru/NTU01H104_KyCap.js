function dsKyCap(_opts) {
    var _gridId = "gridPhieu";
    var _gridIdDatao = "gridPhieuDatao";
    var _gridHeader =
        "PHIEUKYCAPID,PHIEUKYCAPID,50,0,t,l,1,2;" +
        "HOSOBENHANID,HOSOBENHANID,50,0,t,l,1,2;" +
        "BENHNHANID,BENHNHANID,50,0,t,l,1,2;" +
        "TIEPNHANID,TIEPNHANID,50,0,t,l,1,2;" +
        "KHAMBENHID,KHAMBENHID,50,0,t,l,1,2;" +
        "KEYSEARCH,KEYSEARCH,50,0,t,l,1,2;" +
        "PARAM_HASHED,PARAM_HASHED,50,0,t,l,1,2;" +
        "CA_TYPE,CA_TYPE,50,0,t,l,1,2;" +
        "CANCEL_TYPE,CANCEL_TYPE,50,0,t,l,1,2;" +
        "PHIEUID,PHIEUID,50,0,t,l,1,2;" +
        "DOITUONGKYID,DOITUONGKYID,50,0,t,l,1,2;" +
        "RPTCODE,RPTCODE,50,0,t,l,1,2;" +
        "SQL_PARAM,SQL_PARAM,50,0,t,l,1,2;" +
        "THUTUKY,THUTUKY,50,0,t,l,1,2;" +
        "THUTUVIEW,THUTUVIEW,50,0,t,l,1,2;" +
        "LOAIPHIEU,LOAIPHIEU,50,0,t,l,1,2;" +
        "GHICHU,GHICHU,50,0,t,l,1,2;" +
        "TRANGTHAI,TRANGTHAI,50,0,t,l,1,2;" +
        "DUYETKY,DUYETKY,50,0,t,l,1,2;" +
        "KHOA_DUYETMO,KHOA_DUYETMO,50,0,t,l,1,2;" +
        "Mã BA,MAHOSOBENHAN,100,0,f,l,1,2;" +
        "Số phiếu,SOPHIEU,100,0,f,l,1,2;" +
        "Trạng thái,TRANGTHAIKY,60,0,f,l,1,2;" +
        "Tên bệnh nhân,TENBENHNHAN,180,0,f,l,1,2;" +
        "Ngày sinh,NGAYSINH,80,0,f,c,1,2;" +
        "Khoa,KHOA,200,0,f,l,1,2;" +
        "Tên phiếu,TENPHIEU,200,0,f,l,1,2;" +
        "Loại ký,KIEUKY,140,0,f,l,1,2;" +
        "Level ký,LEVELKY,60,0,f,l,1,2;" +
        "Người tạo,NGUOITAOPHIEU,150,f,l,1,2;" +
        "Ngày tạo,NGAYTAO,120,0,f,l,1,2";

    var _gridHeaderDatao =
        "HOSOBENHANID,HOSOBENHANID,50,0,t,l,1,2;" +
        "MAUBENHPHAMID,MAUBENHPHAMID,50,0,t,l,1,2;" +
        "RPT_CODE,RPT_CODE,50,0,t,l,1,2;" +
        "CA_TYPE,CA_TYPE,50,0,t,l,1,2;" +
        "SQL_PARAM,SQL_PARAM,50,0,t,l,1,2;" +
        "LOAIPHIEU,LOAIPHIEU,50,0,t,l,1,2;" +
        "LOAINHOMMAUBENHPHAM,LOAINHOMMAUBENHPHAM,50,0,t,l,1,2;" +
        "LOAIKY,LOAIKY,50,0,t,l,1,2;" +
        "FLAG_CA,FLAG_CA,50,0,t,l,1,2;" +
        "KEY1,KEY1,50,0,t,l,1,2;" +
        "KEY2,KEY2,50,0,t,l,1,2;" +
        "KEY3,KEY3,50,0,t,l,1,2;" +
        "KEY4,KEY4,50,0,t,l,1,2;" +
        "KEY5,KEY5,50,0,t,l,1,2;" +
        "Mã BA,MAHOSOBENHAN,100,0,f,l,1,2;" +
        "Số phiếu,SOPHIEU,100,0,f,l,1,2;" +
        "Trạng thái,TRANGTHAIKY,60,0,f,l,1,2;" +
        "Tên bệnh nhân,TENBENHNHAN,180,0,f,l,1,2;" +
        "Ngày sinh,NGAYSINH,80,0,f,c,1,2;" +
        "Khoa,KHOA,200,0,f,l,1,2;" +
        "Tên phiếu,TENPHIEU,200,0,f,l,1,2;" +
        "Loại ký,KIEUKY,140,0,f,l,1,2;" +
        "Ngày tạo,NGAYTAO,120,0,f,l,1,2";

    this.load = doLoad;

    var loainhommbp = -1;
    var loaiphieu = -1;
    var levelky = -1;
    var kycapid = -1;
    var paramhashed = -1;
    var catype = -1;
    var canceltype = -1;
    var _params = '';
    var _signType = '-1';
    var kieuky = '';

    var dsPhieu = [];
    var dsPhieuId = [];
    var object_ca = {};
    var _isKycap = true;
    var trangthaiKy = '';
    var _rptCode = '';
    var thutuview = -1;
    var duyetky = '';
    var keysearch = '';
    var cf = new Object();

    var _mabenhan;

    //cau hinh
    var hideKyCap = false;
    var notPrint = false;
    var _width = $(document).width() - 150;
    var _height = $(window).height() - 150;


    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        _initControl();
        _bindEvent();

    }

    function _initControl() {
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_KYCAP_HIDE_LEVELKY;HIS_KYCAP_NOTPRINT;HIS_KYCAP_SEARCH;HIS_KYCAP_PHIEUID_SEARCH" +
            ";HIS_KYCAP_VIEW_QLBA;HIS_KYCAP_SHOWBN_DUYETMO");
        if (data_ar != null && data_ar.length > 0) {
            cf = data_ar[0];
            if (cf.HIS_KYCAP_HIDE_LEVELKY == '1') {
                hideKyCap = true;
            }
            if (cf.HIS_KYCAP_NOTPRINT == '1') {
                notPrint = true;
            }
            if (cf.HIS_KYCAP_SEARCH == '1') {
                $("#div_search1").show();
            } else if (cf.HIS_KYCAP_SEARCH == '2') {
                $("#div_search2").show();
                $("#cboKHOA2").select2();
            }
            if (cf.HIS_KYCAP_SHOWBN_DUYETMO == '1') {
                $("#div_duyetmo").show();
            }
        }

        var denngay = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
        $("#txtDENNGAY").val(denngay);
        var tungay = moment($('#txtDENNGAY').val(), "DD/MM/YYYY").add(-1, 'days').format('DD/MM/YYYY');
        $('#txtTUNGAY').val(tungay);

        $("#txtNGAYXACNHAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
        GridUtil.init(_gridId, "100%", "400px", "Danh sách phiếu", false, _gridHeader, false, {
            rowNum: 200,
            rowList: [200, 250, 300]
        });
        GridUtil.init(_gridIdDatao, "100%", "400px", "Danh sách phiếu", true, _gridHeaderDatao, false, {
            rowNum: 200,
            rowList: [200, 250, 300]
        });
        loadCboKhoa();
        loadCboPhieu();
        loadGridData();
        loadGridDataDatao();
    };

    function _bindEvent() {
        GridUtil.setGridParam(_gridId, {
            onSelectRow: function (id) {
                GridUtil.unmarkAll(_gridId);
                GridUtil.markRow(_gridId, id);
                if (id) {
                    var _row = $("#" + _gridId).jqGrid('getRowData', id);
                    var obj = new Object();
                    obj.HOSOBENHANID = _row.HOSOBENHANID;
                    obj.PHIEUID = _row.PHIEUID;
                    obj.KEYSEARCH = _row.KEYSEARCH;
                    paramhashed = _row.PARAM_HASHED;
                    var ghichu = _row.GHICHU;
                    loaiphieu = _row.LOAIPHIEU;
                    levelky = _row.LEVELKY;
                    kycapid = _row.PHIEUKYCAPID;
                    catype = _row.CA_TYPE;
                    kieuky = _row.KIEUKY;
                    canceltype = _row.CANCEL_TYPE;
                    _params = _row.SQL_PARAM;
                    trangthaiKy = _row.TRANGTHAI;
                    _rptCode = _row.RPTCODE;
                    thutuview = _row.THUTUVIEW;
                    duyetky = _row.DUYETKY;
                    keysearch = _row.KEYSEARCH;
                    _mabenhan = _row.MAHOSOBENHAN;
                    $("#lblSOPHIEU").text(_row.SOPHIEU);
                    $("#lblNGAYTAO").text(_row.NGAYTAO);
                    $("#lblTENPHIEU").text(_row.TENPHIEU);
                    $("#txtGHICHU").val(ghichu);

                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H104.02", JSON.stringify(obj));
                    if (data_ar && data_ar.length > 0) {
                        object_ca = data_ar[0];
                        loainhommbp = object_ca.LOAINHOMMAUBENHPHAM;
                        if (object_ca.NGAYMAUBENHPHAM !== '') {
                            $("#lblNGAYTAO").text(object_ca.NGAYMAUBENHPHAM);
                        }
                    }
                    kySo(2);

                    //phiếu đã ký tại cấp hiện tại ko cho từ chối
                    var _pacheck = _params;
                    if (!isObject(_pacheck)) {
                        _pacheck = JSON.parse(_pacheck);
                    }
                    var _check = CommonUtil.checkKyCaByParam(_pacheck, loaiphieu, levelky);
                    if (_check == 0 && _row.TRANGTHAI != '4') {
                        $("#btnTuChoi").show();
                    } else {
                        $("#btnTuChoi").hide();
                    }
                    //check phiếu có duyệt ký hay ko
                    if (duyetky == '1') {
                        $("#divduyetky").show();
                    } else {
                        $("#divduyetky").hide();
                    }
                }
            },
            ondblClickRow: function (id) {
                var rowData = $('#' + _gridId).jqGrid('getRowData', id);
                if (rowData != null) {
                    var paramInput = {
                        paramhashed: paramhashed
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("divDSKy", "divDlg", "manager.jsp?func=../noitru/NTU01H105_DanhSachKyCap", paramInput, "Thông tin trình ký", 800, 450);
                    DlgUtil.open("divDSKy");
                }
            },
            gridComplete : function() {
                var ids = $('#' + _gridId).getDataIDs();
                if (cf.HIS_KYCAP_SHOWBN_DUYETMO == '1') {
                    for (var i = 0; i < ids.length; i++) {
                        var id = ids[i];
                        var row = $('#' + _gridId).jqGrid('getRowData', id);
                        if (row.KHOA_DUYETMO != '') {
                            $("#" + _gridId).jqGrid('setRowData', id, "", {
                                color : '#0000FF'
                            });
                            $("#" + _gridId).jqGrid('setCell', id, 'KHOA', row.KHOA_DUYETMO);
                        }
                    }
                }
            }
        });

        GridUtil.setGridParam(_gridIdDatao, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(_gridIdDatao);
                GridUtil.markRow(_gridIdDatao, id);
                var _row = $("#" + _gridIdDatao).jqGrid('getRowData', id);
                loaiphieu = _row.LOAIPHIEU;
                catype = _row.CA_TYPE;
                kieuky = _row.KIEUKY;
                _rptCode = _row.RPT_CODE;
                object_ca["HOSOBENHANID"] = _row.HOSOBENHANID;
                object_ca["RPT_CODE"] = _row.RPT_CODE;
                object_ca["KEY1"] = _row.KEY1;
                object_ca["KEY2"] = _row.KEY2;
                object_ca["KEY3"] = _row.KEY3;
                object_ca["KEY4"] = _row.KEY4;
                object_ca["KEY5"] = _row.KEY5;
                $("#lblSOPHIEU").text(_row.SOPHIEU);
                $("#lblNGAYTAO").text(_row.NGAYTAO);
                $("#lblTENPHIEU").text(_row.TENPHIEU);
                _params = getParamFromRptCode(_row.SQL_PARAM);
                // luu vao danh sach trang thai hien tai;
                if (status && _gridIdDatao.indexOf(_row.MAUBENHPHAMID) == -1 && _row.MAUBENHPHAMID != null) {
                    _row.SQL_PARAM = JSON.stringify(_params);
                    dsPhieuId.push(_row.MAUBENHPHAMID);
                    dsPhieu.push(_row);
                }
                if (!status) {
                    var j = dsPhieuId.indexOf(_row.MAUBENHPHAMID);
                    if (j != -1) {
                        dsPhieuId.splice(j, 1);
                        dsPhieu.splice(j, 1);
                    }
                }
                kySo(2);
            }
        });

        $("#tabPhieuKyCap").click(function () {
            _isKycap = true;
        });

        $("#tabPhieuDaTao").click(function () {
            reloadData();
            _isKycap = false;
        });

        $("#btnKySo").click(function () {
            _signType = '1';
            kySo();
        });

        $("#btnHuyKy").click(function () {
            _signType = '2';
            kySo();
        });

        $("#btnInKy").click(function () {
            _signType = '0';
            kySo();
        });

        $("#btnTIMKIEM").on("click", function () {
            loadGridData();
            loadGridDataDatao();
        });

        $('#cboTRANGTHAI').change(function () {
            loadGridData();
            loadGridDataDatao();
        });

        $("#btnMORONG").on("click", function () {
            $('#divTT').hide();
            $('#divSearch').removeClass().addClass('col-md-12 low-padding mgt-5');
            $('#btnMORONG').hide();
            $('#btnTHUNHO').show();
        });

        $("#btnTHUNHO").on("click", function () {
            $('#divSearch').removeClass().addClass('col-md-4 low-padding mgt-5');
            $('#divTT').show();
            $('#btnMORONG').show();
            $('#btnTHUNHO').hide();
        });

        $("#btnTuChoi").click(function () {
            var oData = {
                sign_type: '2',
                causer: '-1',
                capassword: '-1',
                params: _params
            };

            var _rs = null;
            var result = $.ajax({
                url: '/vnpthis/apicarpt',
                type: "POST",
                data: JSON.stringify(oData),
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                async: false
            }).done(function (_response) {
                _rs = _response;
            });
            if (_rs.CODE == 0) {
                //get thong tin update ky CA
                var table = '';
                var colum = '';
                var columData = '';
                var _sql_par = [];
                _sql_par.push({"name": "[0]", value: _rptCode});
                var _bdca = jsonrpc.AjaxJson.getOneValue("GET.DB.CA", _sql_par);
                if (_bdca != 'null') {
                    var datadb = _bdca.split(';');
                    table = datadb[0];
                    colum = datadb[1];
                    if (datadb.length == '3') {
                        _params.forEach(function (element) {
                            if (element.name.indexOf(datadb[2]) != '-1' || element.name.indexOf(datadb[2].toLowerCase()) != '-1') {
                                columData = element.value;
                            }
                        });
                    } else {
                        _params.forEach(function (element) {
                            if (element.name.indexOf(colum) != '-1' || element.name.indexOf(colum.toLowerCase()) != '-1') {
                                columData = element.value;
                            }
                        });
                    }
                    var obj = new Object();
                    obj.TABLENAME = table;
                    obj.COLUMNAME = colum;
                    obj.COLUMDATA = columData;
                    obj.SINGTYPE = '2';
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(obj));
                }
                DlgUtil.showMsg("Thành công!");
            } else {
                DlgUtil.showMsg("Thất bại!");
            }
            $("#gridPhieu").trigger("reloadGrid");
        });

        $("#btnXuatPDF").click(function () {
            var emrUtils = new EmrUtils();
            let rs = emrUtils.getTreatmentInfo(_mabenhan);
            if (rs.code == 200) {
                let urlTreatment = rs.results;
                window.open(urlTreatment);
            } else if (rs.code == 401) {
                DlgUtil.showMsg("Dữ liệu bệnh án chưa được kết xuất!");
            } else {
                DlgUtil.showMsg("Đã có lỗi xảy ra!");
            }
        })

        $("#btnBAEMR").on("click", function () {
            if(cf.HIS_KYCAP_VIEW_QLBA == '1') {
                var paramInput = {
                    type: 'DUYET_MO',
                    mabenhan: _mabenhan
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../danhmuc/DMC_IFRAMES", paramInput, "HIS - Quản lý bệnh án", _width, _height);
                DlgUtil.open("divDlgBA");
            } else {
                var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
                if (selRowId != null && selRowId > 0) {
                    var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
                    var paramInput = {
                        khambenhid: rowData.KHAMBENHID,
                        tiepnhanid: rowData.TIEPNHANID,
                        hosobenhanid: rowData.HOSOBENHANID
                    };
                    var _width = $(document).width() - 50;
                    var _height = $(window).height() - 50;
                    dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Quản lý bệnh án", _width, _height);
                    DlgUtil.open("divDlgBA");
                } else {
                    DlgUtil.showMsg('Chưa chọn bệnh nhân!');
                }
            }
        });

        //Lich su benh an
        $("#btnLichSu").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            if (selRowId != null && selRowId > 0) {
                var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
                paramInput = {
                    benhnhanId: rowData.BENHNHANID,
                    khambenhid: rowData.KHAMBENHID,
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn", paramInput, "Lịch sử bệnh án", 1320, 610);
                DlgUtil.open("dlgLichSuBenhAn");

            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân!');
            }
        });

        $('#cboBRANCHID').change(function () {
            var _val = $('#cboBRANCHID').val();
            ComboUtil.getComboTag("cboKHOA2", 'NTU02D132.11.05', [{"name":"[0]","value":_val}], "", {
                text: "--- Khoa ---", value: -1
            }, 'sql', '', function () {
            });
        });
    }

    function loadGridData() {
        var objData = new Object();
        objData.TUNGAY = $("#txtTUNGAY").val() === '' ? '-1' : $("#txtTUNGAY").val();
        objData.DENNGAY = $("#txtDENNGAY").val() === '' ? '-1' : $("#txtDENNGAY").val();
        objData.TRANGTHAI = $("#cboTRANGTHAI").val();
        objData.KHOA = $("#cboKHOA").val() === null ? '-1' : $("#cboKHOA").val();
        objData.PHIEUID = $("#cboPHIEUID").val() === null ? '-1' : $("#cboPHIEUID").val();
        objData.BRANCHID = $("#cboBRANCHID").val() === null ? '-1' : $("#cboBRANCHID").val();
        if (cf.HIS_KYCAP_SEARCH == '2') {
            objData.KHOA = $("#cboKHOA2").val() === null ? '-1' : $("#cboKHOA2").val();
        }
        var _sql_par = [{"name": "[0]", "value": JSON.stringify(objData)}];
        GridUtil.loadGridBySqlPage(_gridId, "NTU01H104.01", _sql_par, function () {
            // var ids = $("#" + _gridId).getDataIDs();
            // for (var id = 1; id <= ids.length; id++) {
            //     var trangthai = $("#" + _gridId).jqGrid('getCell', id, 'TRANGTHAI');
            //     if (trangthai != '2') {
            //         var obj = new Object();
            //         obj.PARAM_HASHED = $("#" + _gridId).jqGrid('getCell', id, 'PARAM_HASHED');
            //         obj.LEVELKY = $("#" + _gridId).jqGrid('getCell', id, 'LEVELKY');
            //         var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H104.07", JSON.stringify(obj));
            //         if (_check > '0') {
            //             $("#" + _gridId).jqGrid('delRowData', id);
            //         }
            //     }
            // }
        });
        $("#viewIframe").on("load", function () {
            $("#viewIframe").contents().find("img").css("width", "100%");
            $("#loading").addClass("hide");
            $("#loading").removeClass("show");
        });
    }

    function loadGridDataDatao() {
        var objData = new Object();
        objData.TUNGAY = $("#txtTUNGAY").val() === '' ? '-1' : $("#txtTUNGAY").val();
        objData.DENNGAY = $("#txtDENNGAY").val() === '' ? '-1' : $("#txtDENNGAY").val();
        var _trangthai = '';
        if ($("#cboTRANGTHAI").val() == '1') {
            _trangthai = '0';
        } else if ($("#cboTRANGTHAI").val() == '2') {
            _trangthai = '1';
        } else if ($("#cboTRANGTHAI").val() == '3') {
            _trangthai = '2';
        } else {
            _trangthai = $("#cboTRANGTHAI").val();
        }
        objData.TRANGTHAI = _trangthai;
        var _sql_par = [{"name": "[0]", "value": JSON.stringify(objData)}];
        GridUtil.loadGridBySqlPage(_gridIdDatao, "NTU01H104.11", _sql_par);
        $("#viewIframe").on("load", function () {
            $("#viewIframe").contents().find("img").css("width", "100%");
            $("#loading").addClass("hide");
            $("#loading").removeClass("show");
        });
    }

    function kySo(_mode) {
        if (!isObject(_params)) {
            _params = JSON.parse(_params);
        }
        //view phiếu đã ký, chưa ký.
        if (_mode == 2) {
            var caCheck = CommonUtil.checkKyCaByParam(_params, loaiphieu);
            if (caCheck > 0) {
                var request = {
                    params: [_params],
                    types: "HTML;PDF"
                };
                $("#loading").addClass("show");
                $("#loading").removeClass("hide");
                $.ajax({
                    url: "/vnpthis/carptrender",
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(request),
                    success: function (respObj) {
                        try {
                            var linkOHRenders = JSON.parse(respObj);
                            if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                $("#viewIframe").attr("src", "");
                                setTimeout(function () {
                                    $("#viewIframe")[0].src = linkOHRenders[0];
                                }, 200);
                            } else {
                                DlgUtil.showError(`Không tồn tại thông tin ký.`, 10000);
                            }
                        } catch (e) {
                            try {
                                if (respObj.code == "0") {
                                    let linkOHRenders = respObj.data;
                                    if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                        $("#viewIframe").attr("src", "");
                                        setTimeout(function () {
                                            $("#viewIframe")[0].src = linkOHRenders[0];
                                        }, 200);
                                    } else {
                                        DlgUtil.showError(`Không tồn tại thông tin ký.`, 10000);
                                    }
                                } else {
                                    DlgUtil.showError(`${respObj.message} (${respObj.code})`, 10000);
                                }
                            } catch (e2) {
                                alert("Đã có lỗi xảy ra");
                            }
                        }
                    },
                    error: function (msg) {
                        alert("View phiếu thất bại");
                    }
                });
            } else {
                var par_data = JSON.stringify(_params);
                var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
                par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
                var uuid = jsonrpc.AjaxJson.getUUID();
                var _reportCode = _params.find(element => element.name.toUpperCase() == 'RPT_CODE')['value'];
                var _url = "../report/DirectReport?code=" + _reportCode + "&filetype=pdf&reportParam=" + par_str + "&uuid=" + uuid;
                $("#viewIframe").attr("src", _url);
            }
        } else {
            if (!_isKycap && dsPhieu.length == '0') {
                DlgUtil.showMsg("Chưa chọn phiếu để ký!");
                return;
            }
            // ký, in ký số.
            if (_signType == '0') {
                CommonUtil.openReportGetCA2(_params, false);
            } else {
                if (_isKycap) {
                    if (catype == '3' || catype == '6') {
                        _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                        let _paramInput = {params: _params, smartca_method: 0};
                        EventUtil.setEvent("dlgCaLogin_confirm", function () {
                            DlgUtil.close("divCALOGIN");
                            let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                            let _usn = _hisl2SmartCa.token.refresh_token;
                            let _pwd = _hisl2SmartCa.token.access_token;
                            let _smartcauser = _hisl2SmartCa.user.uid;
                            _caRpt(_usn, _pwd, _params, kieuky, catype, _smartcauser);
                        });

                        let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                        if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
                            _paramInput.smartca_method = 1;
                            let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                            _popup.open("divCALOGIN");
                            return;
                        } else {
                            EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function (e) {
                                if (e.data && e.data.token && e.data.token.access_token) {
                                    _paramInput.smartca_method = 1;
                                }
                                DlgUtil.close("dlgCA_SMARTCA_LOGIN");
                                let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                                _popup.open("divCALOGIN");
                                return;
                            });
                            DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
                            DlgUtil.open("dlgCA_SMARTCA_LOGIN");
                            return;
                        }
                    } else if (catype == '5') {
                        var lstParams = [];
                        lstParams.push(_params);
                        var _options = new Object();
                        _options.levelky = duyetky == 1 ? '' : levelky;
                        _options.catypelevel = catype;
                        _options.thutuview = thutuview;
                        CommonUtil._caRptTocken(lstParams, _signType, null, null, 'SIGN_TOKEN', null , _options);
                        EventUtil.setEvent("eventKyCA", function (e) {
                            DlgUtil.showMsg(e.res);
                            loadGridData();
                        });
                    } else {
                        EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                            DlgUtil.close("divCALOGIN");
                            _caRpt(e.username, e.password, _params, kieuky, catype, '');
                        });
                        EventUtil.setEvent("dlgCaLogin_close", function () {
                            DlgUtil.close("divCALOGIN");
                        });
                        var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                        var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
                        popup.open("divCALOGIN");
                    }
                } else {
                    if (dsPhieu.length == '1') {
                        CommonUtil.kyCA(_params, _signType);
                        EventUtil.setEvent("eventKyCA", function (e) {
                            DlgUtil.showMsg(e.res);
                            reloadData();
                        });
                    } else {
                        var checkCaType = '';
                        var checkLoaiPhieu = '';
                        for (var i = 0; i < dsPhieu.length; i++) {
                            checkCaType = dsPhieu[0].CA_TYPE;
                            checkLoaiPhieu = dsPhieu[0].LOAINHOMMAUBENHPHAM;
                            if (checkCaType != dsPhieu[i].CA_TYPE) {
                                DlgUtil.showMsg("Tồn tại phương thức ký khác nhau trong danh sách. Không thể thực hiện ký!");
                                reloadData();
                                return;
                            }
                            if (checkLoaiPhieu != dsPhieu[i].LOAINHOMMAUBENHPHAM) {
                                DlgUtil.showMsg("Loại phiếu trong danh sách khác nhau. Không thể thực hiện ký!");
                                reloadData();
                                return;
                            }
                            //phiếu ký cấp ko cho ký nhiều
                            if (dsPhieu.length > 1 && dsPhieu[i].LOAIKY == '1') {
                                DlgUtil.showMsg("Phiếu ký cấp. Không thể thực hiện ký nhiều phiếu!");
                                reloadData();
                                return;
                            }
                        }
                        EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                            DlgUtil.close("divCALOGIN");
                            var _msg = '';
                            dsPhieu.forEach(function (el) {
                                var _msgidx = _caRpt(e.username, e.password, JSON.parse(el.SQL_PARAM), kieuky, catype);
                                _msg = _msg + el.SOPHIEU + ': ' + _msgidx + '</br>';
                            });
                            reloadData();
                            DlgUtil.showMsg(_msg);
                        });
                        EventUtil.setEvent("dlgCaLogin_close", function (e) {
                            DlgUtil.close("divCALOGIN");
                        });
                        var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                        var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
                        popup.open("divCALOGIN");
                    }
                }
            }
        }
    }

    function _caRpt(_causer, _capassword, _params, _title, catype, _smartcauser) {
        //check Ký Hủy
        var _reportCode = _params.find(element => element.name.toUpperCase() == 'RPT_CODE')['value'];
        var _trangthai = _signType == '1' ? "2" : "3";
        var objCheck = new Object();
        objCheck.PARAM_HASHED = paramhashed;
        objCheck.TRANGTHAI = _trangthai;
        objCheck.LEVELKY = levelky;
        objCheck.SIGNTYPE = _signType;
//        if (levelky > '1') {
//            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('EMR.GET.THUTUVIEW', kycapid);
//            trangthaiKy = data_ar[0].TRANGTHAI;
//			}
        
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('EMR.GET.THUTUVIEW', kycapid);
        trangthaiKy = data_ar[0].TRANGTHAI;

        if (trangthaiKy == '2' && _signType == '1') {
            DlgUtil.showMsg("Phiếu đã thực hiện ký số/điện tử!");
            return;
        }
        if (trangthaiKy != '2' && _signType == '2') {
            DlgUtil.showMsg("Phiếu chưa thực hiện ký số/điện tử!");
            return;
        }
        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H104.04", JSON.stringify(objCheck));
        if (fl == 2) {
            DlgUtil.showMsg("Phiếu đã được ký bởi người cấp trên. Không thể thực hiện hủy ký!");
            return;
        } else if (fl == 4) {
            DlgUtil.showMsg("Không thể thực hiện ký do level dưới chưa hoàn thành ký!");
            return;
        }

        //lưu thông tin vs phieu duyệt ký
        if (duyetky == '1') {
            var object = new Object();
            object.KEYSEARCH = keysearch;
            object.NGAYXACNHAN = $("#txtNGAYXACNHAN").val();
            object.XACNHAN = $("input[name='radDUYETKY']:checked").val();
            object.YKIENKHAC = $("#txtYKIENKHAC").val();
            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H104.06", JSON.stringify(object));
        }

        var oData = {
            sign_type: _signType,
            causer: _causer,
            capassword: _capassword,
            levelky: duyetky == 1 ? '' : levelky,
            catypelevel: catype,
            thutuview: thutuview,
            params: _params,
            smartcauser: _smartcauser
        };

        var _options = new Object();
        _options.paramHashed = paramhashed;
        var msg = CommonUtil.caRpt(oData, _reportCode, (loainhommbp != 300) && !notPrint, '', true, _title, catype, _options);
        var _code = msg.split("|")[0];
        var _msg = msg.split("|")[1];
        var _caid = msg.split("|")[2];
        if (_code == '0' || _code == '7' || _code == '8') {
            if (catype == '3') {
                var intervalId = null;
                var smartCaLoaderFunction = function () {
                    console.log("smartCaLoaderFunction is running!")
                    var _sql_par = [];
                    _sql_par.push({"name": "[0]", value: _caid});
                    var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
                    if (fl == 1) {
                        // bat phieu in
                        CommonUtil.openReportGetCA2(_params, false);
                        clearInterval(intervalId);
                    }
                }
                intervalId = setInterval(smartCaLoaderFunction, 4000);
            }
        }
        if (_isKycap) {
            $("#gridPhieu").trigger("reloadGrid");
            DlgUtil.showMsg(_msg, undefined, '2000');
        }
        return _msg;
    }


    function getParamFromRptCode(_param) {
        var oparam = [];
        var paramSelect = JSON.parse(_param);
        paramSelect.forEach(function (el) {
            var name = el.name;
            var patt = /(?<=\[)(((?!\]).)*)/;
            var key = el.value.match(patt)[0];
            var value = el.value.replace("[" + key + "]", object_ca[key]);
            oparam.push({
                name: name,
                type: "String",
                value: value
            })
        });
        return oparam;
    }

    function isObject(obj) {
        return obj.constructor == Object || obj.constructor == Array;
    }

    function reloadData() {
        dsPhieu = [];
        dsPhieuId = [];
        $("#gridPhieuDatao").trigger("reloadGrid");
    }

    function loadCboKhoa() {
        if (cf.HIS_KYCAP_SEARCH == '1') {
            ComboUtil.getComboTag("cboKHOA", 'NTU02D132.11.01', [], "", {
                text: "--- Khoa ---", value: -1
            }, 'sql', '', function () {
            });
        } else if (cf.HIS_KYCAP_SEARCH == '2') {
            var _val = $('#cboBRANCHID').val();
            ComboUtil.getComboTag("cboKHOA2", 'NTU02D132.11.05', [{"name":"[0]","value":_val}], "", {
                text: "--- Khoa ---", value: -1
            }, 'sql', '', function () {
            });
        }

    }

    function loadCboPhieu() {
        ComboUtil.getComboTag("cboPHIEUID", 'HIS.LOAD.PHIEUID', [], "", {
            text: "--- Phiếu ---", value: -1
        }, 'sql', '', function () {
        });
    }

}