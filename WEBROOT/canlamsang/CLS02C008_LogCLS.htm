<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.storageapi.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../canlamsang/CLS02C008_LogCLS.js?v=20180829"></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>

<div id="divMain" class="container">
	<div id='toolbarId'></div>
	
	<div class="mgt5" id="divThongTinThongKe">		
		<div class="col-md-12 low-padding">
			<div class="col-md-1 low-padding">
				<label>Từ ngày</label>	
			</div>
			<div class="col-md-2 low-padding">
				<div class='input-group'>							
					<input class="form-control input-sm" id="txtTuNgay" valrule="Từ ngày,required|datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
				</div> 	
			</div>
			<div class="col-md-1 low-padding">
				<label class="mgl10">Đến ngày</label>		
			</div>
			<div class="col-md-2 low-padding">
				<div class='input-group'>							
					<input class="form-control input-sm" id="txtDenNgay" valrule="Đến ngày,required|datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
				</div> 
			</div>		
			<div class="col-md-2 low-padding" style="text-align: center;">				
				<button class="btn btn-sm btn-primary" id="btnTimKiem" >
					<span class="glyphicon glyphicon-search"></span> Tìm kiếm
				</button>
			</div>
			<div class="col-md-4 low-padding">				
			</div>	
		</div>		
	</div>
	
	<div id="divThongkeXetNghiem">
		<div class="col-md-12 low-padding">
			<div class="col-md-12 low-padding">
				<table id="grdLogRIS"></table>
				<div id="pager_grdLogRIS"></div>
			</div>
		</div>
	</div>
</div>

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang= "vn";
	console.log('user_id='+user_id+', schema='+schema+', province_id='+province_id+', hospital_id='+hospital_id);
	
	var session_par=[];
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	session_par[0]=hospital_id;
	var table_name='{table}';
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	
	initRest(_opts._uuid);
	
	var log = new LogCLS(_opts);		
	log.load();
</script>