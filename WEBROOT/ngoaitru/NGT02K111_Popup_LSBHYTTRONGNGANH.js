function danhsachbenhnhanList(_opts) {
    var _gridId = "grdBenhNhan";
    this.load = doLoad;
    var _gridHeader = "";
    _param = [];

    function doLoad() {
        validator = new DataValidator("inputForm");
        var _options = $.extend({}, _opts);
        _gridHeader = "Tên bệnh nhân,TENBENHNHAN,200,0,f,l;Ngày sinh,NGAYSINH,100,0,f,l;Số thẻ BHYT,MA_BHYT,150,0,f,l;Đơn vị trong ngành,DONVITRONGNGANH,200,0,f,l;" +
            "<PERSON><PERSON><PERSON>,NGAYKHAM,100,0,f,l;Ngày ra viện,NGAYRAVIEN,100,0,f,l;Mã bệnh án,MAHOSOBENHAN,100,0,f,l;Số tiền hỗ trợ,SOTIENHOTRO,100,0,f,l;<PERSON><PERSON><PERSON> đ<PERSON> trị,LOAIDIEUTRI,100,0,f,l";
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        GridUtil.init(_gridId, "100%", "370px", "", false, _gridHeader, true);
        $('#txtTUNGAY').val('01/01/' + jsonrpc.AjaxJson.getSystemDate('YYYY'));
        $('#txtDENNGAY').val('31/12/' + jsonrpc.AjaxJson.getSystemDate('YYYY'));
        if (_opts.hospital_id == '10284') {
        	$("#txtTUNGAY").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
        	$("#txtDENNGAY").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
            var tungay = moment($('#txtTUNGAY').val(), "DD/MM/YYYY").subtract(3, 'months').format('DD/MM/YYYY');
            $("#txtTUNGAY").val(tungay);
        }
        _loadData();
    }

    function _bindEvent() {
       $("#btnTIMKIEM").on("click", function() {
           _loadData();
        });
       $("#btnThoat").on("click", function (e) {
           parent.DlgUtil.close("dlgCV");
       });
    }

    function _loadData() {
        var _sql_par = [];
        _sql_par = RSUtil.setSysParam(_sql_par, _param);
        _sql_par.push({
            "name" : "[0]",
            "value" : _opts.ma_bhyt
        },{
            "name" : "[1]",
            "value" : $("#txtTUNGAY").val()
        },{
            "name" : "[2]",
            "value" : $("#txtDENNGAY").val()
        });
        GridUtil.loadGridBySqlPage(_gridId, 'NGT02K111.LSBHYT1', _sql_par);
    }
}
