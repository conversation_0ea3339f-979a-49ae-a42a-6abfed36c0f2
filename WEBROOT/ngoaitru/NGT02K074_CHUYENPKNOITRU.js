function NGT02K074_CHUYENPKNOITRU(opt) {
	this.load=doLoad;
	var _opt = opt;
	var _dichvuid = 0;
	var _arr = [];
	var _arr1 = [];
	var _phongkhamids = null;
	var _chedokck = 0;
	var _chonbacsy = 0;
	var _ghichu = 0;
	var NTU_CHUYENKHOA_PK_PDT = 0;
	var kyso_saukhiluu = 0;
	var cfObj = new Object();
	var _grdDSPHONGKHAM="grdDSPHONGKHAM";
	var _colHeader= " ,ICON,30,0,ns,l; ,ICONCLS,30,0,ns,l; ,ICONCa,30,0,ns,l;" +
		"KQCLS,KQCLS,60,0,t,l;" +
		"KQCLS,TRANGTHAIKHAMBENH,60,0,t,l;" +
		"PHONGKHAMDANGKYID,PHONGKHAMDANGKYID,50,0,t,l,1,2;" +
		"PHONGID,PHONGID,30,0,t,l,1,2;" +
		"Tên phòng khám,TENPHONG,300,0,f,l,1,2;" +
		"Ngày CĐ,NGAY,150,0,f,l,1,2;" +
		"Ngày bắt đầu,NGAYBATDAU,150,0,f,l,1,2;" +
		"Ngày kết thúc,NGAYKETTHUC,150,0,f,l,1,2;" +
		"Ghi chú,GHICHU_KHAMBENH,100,0,f,l,1,2;" +
		"Kết quả khám chuyên khoa,KETQUA_KCK,200,0,f,l,1,2";
	//BVTM-5578
	if(opt.hospital_id == '10284' && _opt.mode == "0"){
		_colHeader= " ,ICON,30,0,ns,l; ,ICONCLS,30,0,ns,l; ,ICONCa,30,0,ns,l;" +
			"KQCLS,KQCLS,60,0,t,l;" +
			"KQCLS,TRANGTHAIKHAMBENH,60,0,t,l;" +
			"PHONGKHAMDANGKYID,PHONGKHAMDANGKYID,50,0,t,l,1,2;" +
			"PHONGID,PHONGID,30,0,t,l,1,2;" +
			"Tên phòng khám,TENPHONG,150,0,f,l,1,2;" +
			"Ngày CĐ,NGAY,150,0,f,l,1,2;" +
			"Ngày bắt đầu,NGAYBATDAU,80,0,f,l,1,2;" +
			"Ngày kết thúc,NGAYKETTHUC,80,0,f,l,1,2;" +
			"Ghi chú,GHICHU_KHAMBENH,100,0,f,l,1,2;" +
			"Kết quả khám chuyên khoa,KETQUA_KCK,250,0,f,l,1,2";
	}
	var gopgiaodien ;
	//BVTM-5578
	function doLoad() {
		var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_CONGKHAM_CHUYENKHOA;NGT_KCK_SUANGAYCD;NTU_CHEDO_KHAMCHUYENKHOA;NTU_CHEDO_KHAMCHUYENKHOABS;NGT_GHICHU_KCK;" +
			"HIS_BATBUOCGHICHU_KYSOKCK;HIS_SUDUNG_KYSO_KYDIENTU;NTU_CHUYENKHOA_PK_PDT;LOAD_YEUCAUKHAM_THEO_DT;NGT_KCK_GOPGIAODIEN;HIS_KCK_HIENTHIKYSOPK");
		if (config_ar != null && config_ar.length > 0) {
			cfObj = config_ar[0];
		} else {
			DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
			return;
		}
		_initControl();
		_bindEvent();
	}

	function _initControl(){
		$('#hidKHAMBENHIDNTU').val(_opt.khambenhid);
		$('#hidTIEPNHANID').val(_opt.tiepnhanid);
		$('#hidHOSOBENHANID').val(_opt.hosobenhanid);
		$('#hidBENHNHANID').val(_opt.benhnhanid);
		$('#hidDOITUONGBENHNHANID').val(_opt.doituongbenhnhanid);
		$('#hidMABHYT').val(_opt.mabhyt);
		gopgiaodien = cfObj.NGT_KCK_GOPGIAODIEN;
		kyso_saukhiluu = cfObj.HIS_KCK_HIENTHIKYSOPK;
		if(_opt.mode == "0"){
			// che do nhap thong tin kham chuyen khoa;
			$("#divMODE0").show();
			if(opt.hospital_id != '10284') $("#divMODE1").hide();

			_chedokck = cfObj.NTU_CHEDO_KHAMCHUYENKHOA;
			_chedokck == 0 ? $("#divYCK11").show() : $("#divYCK11").hide();
			_chedokck == 0 ? $("#divYCK21").show() : $("#divYCK21").hide();
			_chedokck == 0 ? $("#divYCK31").show() : $("#divYCK31").hide();
			_chedokck == 0 ? $("#divYCK41").show() : $("#divYCK41").hide();
			_chedokck == 0 ? $("#divYCK51").show() : $("#divYCK51").hide();

			_chonbacsy = cfObj.NTU_CHEDO_KHAMCHUYENKHOABS;
			_chonbacsy == 0 ? $("#dvBACSYKHAM1").hide() : $("#dvBACSYKHAM1").show();
			_chonbacsy == 0 ? $("#dvBACSYKHAM2").hide() : $("#dvBACSYKHAM2").show();
			_chonbacsy == 0 ? $("#dvBACSYKHAM3").hide() : $("#dvBACSYKHAM3").show();
			_chonbacsy == 0 ? $("#dvBACSYKHAM4").hide() : $("#dvBACSYKHAM4").show();
			_chonbacsy == 0 ? $("#dvBACSYKHAM5").hide() : $("#dvBACSYKHAM5").show();
			_ghichu = cfObj.NGT_GHICHU_KCK;
			_ghichu == 0 ? $("#dvGHICHU1").hide() : $("#dvGHICHU1").show();
			_ghichu == 0 ? $("#dvGHICHU2").hide() : $("#dvGHICHU2").show();
			_ghichu == 0 ? $("#dvGHICHU3").hide() : $("#dvGHICHU3").show();
			_ghichu == 0 ? $("#dvGHICHU4").hide() : $("#dvGHICHU4").show();
			_ghichu == 0 ? $("#dvGHICHU5").hide() : $("#dvGHICHU5").show();
			//BVTM-5578
			if(opt.hospital_id == '10284'|| gopgiaodien == 1){
				if(_chedokck!=1 && (_chonbacsy+_ghichu ==0)) {
					for(let i = 1;i<6;i++){
						let elmChild = $("#divPHONGKHAM"+i+" >div");
						$(elmChild[0]).removeClass("col-xs-1").addClass("col-xs-2");
						$(elmChild[1]).removeClass("col-xs-3").addClass("col-xs-5");
						$(elmChild[2]).removeClass("col-xs-3").addClass("col-xs-5");
					}
				}
			}
			//BVTM-5578
			_dichvuid = cfObj.NTU_CONGKHAM_CHUYENKHOA;
			if (_chedokck == 1 && _dichvuid == 0){
				DlgUtil.showMsg("Chưa cấu hình dịch vụ cho bệnh nhân khám chuyên khoa từ Nội trú, yêu cầu cấu hình trước khi bắt đầu thao tác. ");
				return;
			}

			if (_chedokck == 0){
				var LOAD_YEUCAUKHAM_THEO_DT = cfObj.LOAD_YEUCAUKHAM_THEO_DT;
				var sql_par=[];
				if(LOAD_YEUCAUKHAM_THEO_DT!=0){
					sql_par.push({"name":"[0]","value":$('#hidDOITUONGBENHNHANID').val()});
				}else{
					sql_par.push({"name":"[0]","value":"0"});
				}
				ComboUtil.getComboTag("cboDICHVUID1","NGTDV.002",sql_par,"",{value:'-1',text:'Chọn yêu cầu khám'},'sql','', '');
				ComboUtil.getComboTag("cboDICHVUID2","NGTDV.002",sql_par,"",{value:'-1',text:'Chọn yêu cầu khám'},'sql','', '');
				ComboUtil.getComboTag("cboDICHVUID3","NGTDV.002",sql_par,"",{value:'-1',text:'Chọn yêu cầu khám'},'sql','', '');
				ComboUtil.getComboTag("cboDICHVUID4","NGTDV.002",sql_par,"",{value:'-1',text:'Chọn yêu cầu khám'},'sql','', '');
				ComboUtil.getComboTag("cboDICHVUID5","NGTDV.002",sql_par,"",{value:'-1',text:'Chọn yêu cầu khám'},'sql','', '');

				if (_chonbacsy == 1){
					ComboUtil.getComboTag("cboBACSYYCID1", "COM.CHONBACSY1",[],"",{value:'-1',text:'--Chọn Bác Sỹ--'},"sql","","");
					ComboUtil.getComboTag("cboBACSYYCID2", "COM.CHONBACSY1",[],"",{value:'-1',text:'--Chọn Bác Sỹ--'},"sql","","");
					ComboUtil.getComboTag("cboBACSYYCID3", "COM.CHONBACSY1",[],"",{value:'-1',text:'--Chọn Bác Sỹ--'},"sql","","");
					ComboUtil.getComboTag("cboBACSYYCID4", "COM.CHONBACSY1",[],"",{value:'-1',text:'--Chọn Bác Sỹ--'},"sql","","");
					ComboUtil.getComboTag("cboBACSYYCID5", "COM.CHONBACSY1",[],"",{value:'-1',text:'--Chọn Bác Sỹ--'},"sql","","");
				}

				_loadPhongKham("1", '-1');
				_loadPhongKham("2", '-1');
				_loadPhongKham("3", '-1');
				_loadPhongKham("4", '-1');
				_loadPhongKham("5", '-1');
			}else{
				_loadPhongKham("1", _dichvuid);
				_loadPhongKham("2", _dichvuid);
				_loadPhongKham("3", _dichvuid);
				_loadPhongKham("4", _dichvuid);
				_loadPhongKham("5", _dichvuid);
			}
			//BVTM-5578
			if(opt.hospital_id == '10284' || gopgiaodien == 1 ){
				$("#divMODE1").show();
				GridUtil.init(_grdDSPHONGKHAM,"68%","130px","Danh sách PK chuyên khoa",false,_colHeader, true, { rowNum: 100,rowList: [100, 300, 500]});
				loadDSPHONGKHAM();
				if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1"){
					$("#btnLuuKySo").show();
					$("#btnHuyKySo").show();
					$("#btnInKySo").show();
				} else {
					$("#btnLuuKySo").hide();
					$("#btnHuyKySo").hide();
					$("#btnInKySo").hide();
					$("#" + _grdDSPHONGKHAM).hideCol('ICONCa');
				}
			}
			//end BVTM-5578
		}else{
			$("#divMODE0").hide();
			$("#divMODE1").show();
			GridUtil.init(_grdDSPHONGKHAM,"100%","130px","Danh sách PK chuyên khoa",false,_colHeader, true, { rowNum: 100,rowList: [100, 300, 500]});
			loadDSPHONGKHAM();
			if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1"){
				$("#btnLuuKySo").show();
				$("#btnHuyKySo").show();
				$("#btnInKySo").show();
			} else {
				$("#btnLuuKySo").hide();
				$("#btnHuyKySo").hide();
				$("#btnInKySo").hide();
				$("#" + _grdDSPHONGKHAM).hideCol('ICONCa');
			}

			// if (cfObj.NGT_KCK_SUANGAYCD == "1"){
			$("#btnSUACD1").show();
			// }
		}
		//BVTM-5578
		if(opt.hospital_id == '10284' || gopgiaodien == 1){
			$(($("#btnLuu").parent().parent().find("div"))[0]).removeClass("col-xs-3").addClass("col-xs-6");
			$("#btnDONG").remove();

			$("#divMODE0").addClass("col-xs-4");
			$("#divMODE1").addClass("col-xs-8");
		}
		//BVTM-5578
		NTU_CHUYENKHOA_PK_PDT = cfObj.NTU_CHUYENKHOA_PK_PDT;// chọn phiếu điều trị khi chuyển khám chuyên khoa phòng khám
		if(NTU_CHUYENKHOA_PK_PDT == 1){
			$('#divPHIEUDIEUTRI').css("display", "");
			var sql_param=[];
			sql_param.push({"name":"[0]","value":$("#hidKHAMBENHIDNTU").val()});
			ComboUtil.getComboTag("cboPHIEUDIEUTRI", "NTU02D010.08" , sql_param, "", {value:'',text:'-- Chọn --'},"sql",'','');
		}
		//Beg_HaNv_150323: Lịch sử khám chuyên khoa - L2PT-37037
		if (_opt.formCall == "NTU_TCBN") {
			$('#btnXOA1').remove();
			$('#btnLuuKySo').remove();
			$('#btnHuyKySo').remove();
			$('#btnInKySo').remove();
			$('#btnIN1').remove();
			$('#btnSUACD1').remove();
		}
		//End_HaNv_150323
	}

	function _bindEvent() {
		$("#btnDONG").on("click", function(e){
			EventUtil.raiseEvent("ngt02k047_chuyenpknoitru_close",{id:''});
		});

		$("#btnINPHIEU1").on("click",function(e){
			_inPhieuKham("0", 1);
		});
		$("#btnINPHIEU2").on("click",function(e){
			_inPhieuKham("0", 2);
		});
		$("#btnINPHIEU3").on("click",function(e){
			_inPhieuKham("0", 3);
		});
		$("#btnINPHIEU4").on("click",function(e){
			_inPhieuKham("0", 4);
		});
		$("#btnINPHIEU5").on("click",function(e){
			_inPhieuKham("0", 5);
		});

		$('#cboDICHVUID1').on('change', function (e) {
			_loadPhongKham("1", $(this).val());
		});
		$('#cboDICHVUID2').on('change', function (e) {
			_loadPhongKham("2", $(this).val());
		});
		$('#cboDICHVUID3').on('change', function (e) {
			_loadPhongKham("3", $(this).val());
		});
		$('#cboDICHVUID4').on('change', function (e) {
			_loadPhongKham("4", $(this).val());
		});
		$('#cboDICHVUID5').on('change', function (e) {
			_loadPhongKham("5", $(this).val());
		});

		$("#btnLuu").on("click",function(e){
			var _phong1 = $("#cboPHONGID1").val();
			var _phong2 = $("#cboPHONGID2").val();
			var _phong3 = $("#cboPHONGID3").val();
			var _phong4 = $("#cboPHONGID4").val();
			var _phong5 = $("#cboPHONGID5").val();

			var _dichvuid1 = _chedokck == 0 ? $("#cboDICHVUID1").val() : _dichvuid;
			var _dichvuid2 = _chedokck == 0 ? $("#cboDICHVUID2").val() : _dichvuid;
			var _dichvuid3 = _chedokck == 0 ? $("#cboDICHVUID3").val() : _dichvuid;
			var _dichvuid4 = _chedokck == 0 ? $("#cboDICHVUID4").val() : _dichvuid;
			var _dichvuid5 = _chedokck == 0 ? $("#cboDICHVUID5").val() : _dichvuid;

			var _bacsy1 = _chonbacsy == 0 ? "-1" : $("#cboBACSYYCID1").val();
			var _bacsy2 = _chonbacsy == 0 ? "-1" : $("#cboBACSYYCID2").val();
			var _bacsy3 = _chonbacsy == 0 ? "-1" : $("#cboBACSYYCID3").val();
			var _bacsy4 = _chonbacsy == 0 ? "-1" : $("#cboBACSYYCID4").val();
			var _bacsy5 = _chonbacsy == 0 ? "-1" : $("#cboBACSYYCID5").val();

			var _ghichu1 =  $("#txtGHICHU1").val();
			var _ghichu2 =	$("#txtGHICHU2").val();
			var _ghichu3 =  $("#txtGHICHU3").val();
			var _ghichu4 =  $("#txtGHICHU4").val();
			var _ghichu5 =  $("#txtGHICHU5").val();

			var _strLength = 0;

			_arr = [];
			_arr1 = [];
			_arrdichvu = [];
			_arrbacsy = [];
			_arrghichu = [];

			if (_phong1 != -1){
				_arr.push(_phong1);
				_arr1.push($('#cboPHONGID1 option:selected').text());
				_arrdichvu.push(_dichvuid1);
				_arrbacsy.push (_bacsy1);
				_arrghichu.push(_ghichu1);
			}

			if (_phong2 != -1){
				_arr.push(_phong2);
				_arr1.push($('#cboPHONGID2 option:selected').text());
				_arrdichvu.push(_dichvuid2);
				_arrbacsy.push (_bacsy2);
				_arrghichu.push(_ghichu2);
			}
			if (_phong3 != -1){
				_arr.push(_phong3);
				_arr1.push($('#cboPHONGID3 option:selected').text());
				_arrdichvu.push(_dichvuid3);
				_arrbacsy.push (_bacsy3);
				_arrghichu.push(_ghichu3);
			}
			if (_phong4 != -1){
				_arr.push(_phong4);
				_arr1.push($('#cboPHONGID4 option:selected').text());
				_arrdichvu.push(_dichvuid4);
				_arrbacsy.push (_bacsy4);
				_arrghichu.push(_ghichu4);
			}
			if (_phong5 != -1){
				_arr.push(_phong5);
				_arr1.push($('#cboPHONGID5 option:selected').text());
				_arrdichvu.push(_dichvuid5);
				_arrbacsy.push (_bacsy5);
				_arrghichu.push(_ghichu5);
			}

			if(_arr.length == 0){
				DlgUtil.showMsg("Chưa chọn phòng chuyển đến. ");
				return;
			}

			for(i=0;i<_arr.length-1;i++){
				for(j=i+1;j<_arr.length;j++){
					if(_arr[i] == _arr[j] && _arr[i] != -1 ){
						DlgUtil.showMsg("Tồn tại phòng khám trùng nhau. Yêu cầu chọn lại. ");
						return;
					}
				}
			}

			// che do full check khong duoc phep trung y/c kham
			if (_chedokck == "0"){
				for(i=0;i<_arrdichvu.length-1;i++){
					for(j=i+1;j<_arrdichvu.length;j++){
						if(_arrdichvu[i] == _arrdichvu[j] && _arrdichvu[i] != -1 ){
							DlgUtil.showMsg("Tồn tại công khám trùng nhau. Yêu cầu chọn lại. ");
							return;
						}
					}
				}
			}

			if(_chedokck == 1 && _dichvuid == 0){
				DlgUtil.showMsg("Chưa cấu hình dịch vụ cho bệnh nhân khám chuyên khoa từ Nội trú, yêu cầu cấu hình trước khi bắt đầu thao tác. ");
			}

			var _objData = {
				tiepnhanid : $("#hidTIEPNHANID").val(),
				khambenhid : $("#hidKHAMBENHIDNTU").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				dtbnid : $('#hidDOITUONGBENHNHANID').val(),
				mabhyt : $("#hidMABHYT").val(),
				chedokck : _chedokck,
				ghichu : $("#txtGHICHU").val()
			};

			var _objData1 = [];
			var _objj = null;
			for(i=0;i<_arr.length;i++){
				_objj = new Object();
				_objj.PHONGID = _arr[i];
				_objj.DICHVUID = _arrdichvu[i];
				_objj.BACSYID = _arrbacsy[i];
				_objj.GHICHU = _arrghichu[i];
				_objData1.push(_objj);

			}

			DlgUtil.showConfirm("Bệnh nhân sẽ khám chuyên khoa ở " + _arr1.length + " phòng. Bạn có muốn tiếp tục? ", function(flag){
				if(flag){
					_luudata(_objData, _objData1);
				}
			});
		});

		GridUtil.setGridParam(_grdDSPHONGKHAM, {
			onSelectRow : function(id) {
				GridUtil.unmarkAll(_grdDSPHONGKHAM);
				GridUtil.markRow(_grdDSPHONGKHAM,id);
				var ret = $("#"+_grdDSPHONGKHAM).jqGrid('getRowData',id);
				_capnhattrangthai(ret);
			}
			,gridComplete: function(id){
				var ids = $("#" + _grdDSPHONGKHAM).getDataIDs();
				for(var i=0;i<ids.length;i++){
					var id = ids[i];
					var row = $("#" + _grdDSPHONGKHAM).jqGrid('getRowData',id);
					var _icon = '';
					var _iconcls = '';
					var _iconCa = '';

					if(row.TRANGTHAIKHAMBENH == 1){
						_icon = '<center><img src="'+ _opt.imgPath[0] +'" width="15px"></center>';
					}else if(row.TRANGTHAIKHAMBENH == 4){
						_icon = '<center><img src="'+ _opt.imgPath[1] +'" width="15px"></center>';
					}else if(row.TRANGTHAIKHAMBENH == 9){
						_icon = '<center><img src="'+ _opt.imgPath[2] +'" width="15px"></center>';
					}

					if(row.KQCLS == "1"){
						_iconcls = '<center><img src="'+ _opt.imgPath[3] +'" width="15px"></center>';
					}else if(row.KQCLS == "2"){
						_iconcls = '<center><img src="'+ _opt.imgPath[4] +'" width="15px"></center>';
					}


					$("#" + _grdDSPHONGKHAM).jqGrid ('setCell', id, 1, _icon);
					$("#" + _grdDSPHONGKHAM).jqGrid ('setCell', id, 2, _iconcls);

					if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1"){
						var par = [{
							name: 'hosobenhanid',
							type: 'String',
							value: $("#hidHOSOBENHANID").val()
						}, {
							name: 'i_khambenhid',
							type: 'String',
							value: $("#hidKHAMBENHIDNTU").val()
						}, {
							name: 'i_phongids',
							type: 'String',
							value: row.PHONGID
						}, {
							name: 'i_pkdkids',
							type: 'String',
							value: row.PHONGKHAMDANGKYID
						}, {
							name: 'RPT_CODE',
							type: 'String',
							value: 'RPT_PHIEUKHAMCHUYENKHOA_A4'
						}];
						var _check = CommonUtil.checkKyCaByParam(par);
						if (_check == '1') {
							_iconCa = '<center><img src="../common/image/ca.png" width="15px"></center>';
						}
						$("#" + _grdDSPHONGKHAM).jqGrid ('setCell', id, 3, _iconCa);
					}

				}
			}
		});

		function _capnhattrangthai(ret){
			$("#hidPHONGKHAMDANGKYID").val(ret != null ? ret.PHONGKHAMDANGKYID : "");
			$("#hidNGAY").val(ret != null ? ret.NGAY : "");
			$("#hidPHONGID").val(ret != null ? ret.PHONGID : "");
			$("#btnIN1").prop("disabled", ret == null ? true:false);
			$("#btnLuuKySo").prop("disabled", ret == null ? true:false);
			$("#btnHuyKySo").prop("disabled", ret == null ? true:false);
			$("#btnInKySo").prop("disabled", ret == null ? true:false);
			$("#btnXOA1").prop("disabled", ret == null ? true:false);
			$("#btnSUACD1").prop("disabled", ret == null ? true:false);
			$("#btnInKQPhieu").prop("disabled", ret == null ? true:false);
		}

		$("#btnIN1").on("click", function(){
			_inPhieuKham("1", "0");
		});

		$("#btnInKQPhieu").on("click", function(){
			var _pkdkid = $("#hidPHONGKHAMDANGKYID").val();
			if(_pkdkid == ""){
				DlgUtil.showMsg("Yêu cầu chọn phòng khám để thao tác");
				return;
			}
			var par = [{
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHIDNTU").val()
			}, {
				name : 'i_phongids',
				type : 'String',
				value : mode == "0" ? $("#cboPHONGID" + stt).val() : $("#hidPHONGID").val()
			}, {
				name : 'i_pkdkids',
				type : 'String',
				value : mode == "0" ? $("#hidPHONGKHAMDANGKYID" + stt).val() : $("#hidPHONGKHAMDANGKYID").val()
			},{
				name : 'phongkhamdangkyid',
				type : 'String',
				value : _pkdkid
			}];
			CommonUtil.inPhieu('window', 'RPT_PHIEUKETQUAHUYENKHOA', 'pdf', par);
		});

		$("#btnXOA1").on("click", function(){
			var _pkdkid = $("#hidPHONGKHAMDANGKYID").val();
			if(_pkdkid == ""){
				DlgUtil.showMsg("Yêu cầu chọn phòng khám để thao tác");
				return;
			}

			DlgUtil.showConfirm("Bạn có muốn xóa phòng khám chuyên khoa này? ", function(flag){
				if(flag){
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K051.XOAPK", _pkdkid);
					if(fl > 0){
						DlgUtil.showMsg("Xóa thành công phòng khám chuyên khoa ");
						loadDSPHONGKHAM();
					}else if (fl == -2){
						DlgUtil.showMsg("Chỉ được phép xóa phòng Chờ khám hoặc Đang khám.");
					}else if (fl == -3){
						DlgUtil.showMsg("Có dịch vụ đã thanh toán/đã hủy cho phòng khám này, không được xóa.");
					}else if (fl == -4){
						DlgUtil.showMsg("Tồn tại mẫu bệnh phẩm không cho phép xóa bằng tool này (vd thuốc, vật tư, CĐHA). Yêu cầu hủy phiếu thủ công trước khi xóa. ");
					}else if (fl == -5){
						DlgUtil.showMsg("Bệnh nhân đang khám 1 phòng, không được xóa. ");
					}else if (fl == -6){
						DlgUtil.showMsg("Lỗi dữ liệu phòng khám cũ, xin kiểm tra lại. ");
					}else if (fl == -7){
						DlgUtil.showMsg("Phòng khám đang xóa không phải là phòng chuyển đến cuối cùng. ");
					}else if (fl == -8){
						DlgUtil.showMsg("Tồn tại dịch vụ đang sửa / đã hoàn thành đối với phòng khám này. Không cho phép xóa.");
					}else if (fl == -10){
						DlgUtil.showMsg("Phòng khám tiếp theo đã thu tiền, không thể xóa phòng hiện tại, yêu cầu hủy thanh toán phòng khám kế tiếp. ");
					}else {
						DlgUtil.showMsg("Lỗi xóa phòng khám. Yêu cầu kiểm tra lại thông tin phòng khám ");
					}
					_capnhattrangthai(null);
				}
			});
		});

		// sondn L2PT-12886
		$("#btnSUACD1").on("click", function(){
			var _pkdkid = $("#hidPHONGKHAMDANGKYID").val();
			if(_pkdkid == ""){
				DlgUtil.showMsg("Yêu cầu chọn phòng khám để thao tác");
				return;
			}
			var selRowId = $('#' + _grdDSPHONGKHAM).jqGrid ('getGridParam', 'selrow');
			var rowData = $('#' + _grdDSPHONGKHAM).jqGrid('getRowData', selRowId);
			paramInput={
				phongkhamdangkyid : _pkdkid,
				ngay : $("#hidNGAY").val(),
				ghichu : rowData.GHICHU_KHAMBENH,
				loai : "8"
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K089_SUACHITIET",paramInput,"Cập nhật phòng khám",600,400);
			DlgUtil.open("divDlgDichVu");
		});

		$("#btnDONG1").on("click", function(){
			EventUtil.raiseEvent("ngt02k047_chuyenpknoitru_close",{id:''});
		});

		// dong popup sua thong tin ngay tiep nhan benh nhan;
		EventUtil.setEvent("ev_dongcuaso", function(e) {
			DlgUtil.close("divDlgDichVu");
			loadDSPHONGKHAM();
		});

		// Ký số
		$("#btnLuuKySo").on("click", function () {
			_caRpt('1');
		});

		$("#btnHuyKySo").on("click", function () {
			_caRpt('2');
		});

		$("#btnInKySo").on("click", function () {
			_caRpt('0');
		});
		// End Ký số
		$("#btnLuuKySo1").on("click", function () {
			_caRpt_TungPhong('1',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID1").val());
		});
		$("#btnHuyKySo1").on("click", function () {
			_caRpt_TungPhong('2',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID1").val());
		});
		$("#btnInKySo1").on("click", function () {
			_caRpt_TungPhong('0',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID1").val());
		});
		$("#btnLuuKySo2").on("click", function () {
			_caRpt_TungPhong('1',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID2").val());
		});
		$("#btnHuyKySo2").on("click", function () {
			_caRpt_TungPhong('2',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID2").val());
		});
		$("#btnInKySo2").on("click", function () {
			_caRpt_TungPhong('0',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID2").val());
		});
		$("#btnLuuKySo3").on("click", function () {
			_caRpt_TungPhong('1',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID3").val());
		});
		$("#btnHuyKySo3").on("click", function () {
			_caRpt_TungPhong('2',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID3").val());
		});
		$("#btnInKySo3").on("click", function () {
			_caRpt_TungPhong('0',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID3").val());
		});
		$("#btnLuuKySo4").on("click", function () {
			_caRpt_TungPhong('1',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID4").val());
		});
		$("#btnHuyKySo4").on("click", function () {
			_caRpt_TungPhong('2',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID4").val());
		});
		$("#btnInKySo4").on("click", function () {
			_caRpt_TungPhong('0',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID4").val());
		});
		$("#btnLuuKySo5").on("click", function () {
			_caRpt_TungPhong('1',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID5").val());
		});
		$("#btnHuyKySo5").on("click", function () {
			_caRpt_TungPhong('2',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID5").val());
		});
		$("#btnInKySo5").on("click", function () {
			_caRpt_TungPhong('0',$("#cboPHONGID1").val(), $("#hidPHONGKHAMDANGKYID5").val());
		});
	}

	function _luudata(_objData, _objData1){
		var _param = [];
		_param.push(JSON.stringify(_objData));
		_param.push(JSON.stringify(_objData1));
		var rets = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K046.CNDATANT",_param.join('$'));
		if(typeof rets == 'undefined' || rets == "loi_save"){
			DlgUtil.showMsg('Cập nhật thông tin không thành công');
			return;
		}else if (rets == "congkhamkhople"){
			DlgUtil.showMsg('Công khám không hợp lệ (phải là công khám 0đ)');
			return;
		}

		var ret = rets.split('@');
		var _strok = "";
		var _strerr = "";

		for(i=0;i<ret.length;i++){
			if(ret[i] == "0"){
				_strerr += _arr1[i] + "; ";
			}else if (ret[i] != ""){
				_strok += _arr1[i] + " - số " + ret[i].split('-')[1] + "; ";
				$("#hidPHONGKHAMDANGKYID" + (i+1)).val(ret[i].split('-')[2]);
			}
		}
		if (_strok != ""){
			loadDSPHONGKHAM();
		}
		_strok = _strok != "" ? "Chuyển thành công tới các phòng: " + _strok : "Chuyển không thành công. ";
		_strerr = _strerr != "" ? "BN đã chờ khám / đang khám ở các phòng : " + _strerr : "";
		$("#lblOK").text(_strok);
		$("#lblERR").text(_strerr);
		if(_strok == "Chuyển không thành công. "){
			return;
		}

		// disable toan bo thong tin, khong cho phep thao tac; 
		$("#btnLuu").prop('disabled', true);
		for(i=1;i<=5;i++){
			$("#txtTKPHONGID" + i).prop('disabled', true);
			$("#cboPHONGID" + i).prop('disabled', true);
			$("#txtTKDICHVUID" + i).prop('disabled', true);
			$("#cboDICHVUID" + i).prop('disabled', true);
			if($("#cboPHONGID" + i).val() != "-1"){
				$("#btnINPHIEU" + i).show();
				if (kyso_saukhiluu == 1){
					$("#btnLuuKySo" + i).show();
					$("#btnHuyKySo" + i).show();
					$("#btnInKySo" + i).show();
				}
			}
		}
	}

	// mode =0: in che do tao moi; mode =1: in che do xem lai; 
	function _inPhieuKham(mode, stt){
		if ($('#hidKHAMBENHIDNTU').val() == ""){
			DlgUtil.showMsg('Chưa có thông tin chuyên khoa truyền vào.');
			return;
		}
		if(mode == "1" && ($("#hidPHONGID").val() == "" || $("#hidPHONGKHAMDANGKYID").val() == "") ){
			DlgUtil.showMsg('Yêu cầu chọn phòng khám để in lại phiếu.');
			return;
		}

		var par = [ {
			name : 'i_khambenhid',
			type : 'String',
			value : $("#hidKHAMBENHIDNTU").val()
		}, {
			name : 'i_phongids',
			type : 'String',
			value : mode == "0" ? $("#cboPHONGID" + stt).val() : $("#hidPHONGID").val()
		}, {
			name : 'i_pkdkids',
			type : 'String',
			value : mode == "0" ? $("#hidPHONGKHAMDANGKYID" + stt).val() : $("#hidPHONGKHAMDANGKYID").val()
		}];

		CommonUtil.inPhieu('window', 'RPT_PHIEUKHAMCHUYENKHOA_A4', 'pdf', par);
	}

	function _loadPhongKham(comboNumber, _dvid){
		var sql_par1=[];
		sql_par1.push({"name":"[0]","value":_dvid});

		ComboUtil.getComboTag("cboPHONGID" + comboNumber, "NGTPK.DV", sql_par1, _opt.phongcuid, {value:-1, text:'-- Chọn phòng khám '+comboNumber+'--'}, "sql", "", function(){
			$("#cboPHONGID" + comboNumber).val(-1);
		});
	}

	function loadDSPHONGKHAM(){
		var _sql_par =[];
		_sql_par.push({"name":"[0]","value":$("#hidTIEPNHANID").val()});
		_sql_par.push({"name":"[1]","value":$("#hidHOSOBENHANID").val()});
		_sql_par.push({"name":"[2]","value":"0"});
		_sql_par.push({"name":"[3]","value":"1"});			// KHAM CHUYEN KHOA; 
		GridUtil.loadGridBySqlPage(_grdDSPHONGKHAM,"NGT02K074.LAYDL",_sql_par);
	}

	// Xử lý sự kiện liên quan ký CA => START
	function _caRpt(signType){
		if (cfObj.HIS_BATBUOCGHICHU_KYSOKCK) {
			var selRowId = $('#' + _grdDSPHONGKHAM).jqGrid ('getGridParam', 'selrow');
			var rowData = $('#' + _grdDSPHONGKHAM).jqGrid('getRowData', selRowId);
			if (rowData.GHICHU_KHAMBENH == '') {
					DlgUtil.showMsg('Chưa nhập ghi chú không thể thao tác!');
					return;
			}
		}
		
			var par = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name: 'i_khambenhid',
				type: 'String',
				value: $("#hidKHAMBENHIDNTU").val()
			}, {
				name: 'i_phongids',
				type: 'String',
				value: $("#hidPHONGID").val()
			}, {
				name: 'i_pkdkids',
				type: 'String',
				value: $("#hidPHONGKHAMDANGKYID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'RPT_PHIEUKHAMCHUYENKHOA_A4'
			}];
		if (signType == '0') {
			CommonUtil.openReportGetCA2(par, false);
		} else {
			CommonUtil.kyCA(par, signType, true);
			EventUtil.setEvent("eventKyCA", function (e) {
				DlgUtil.showMsg(e.res);
			});
		}
	}
	// Xử lý sự kiện liên quan ký CA => END
	function _caRpt_TungPhong(signType, phongid, phongkhamdangkyid){
		var par = [{
			name: 'hosobenhanid',
			type: 'String',
			value: $("#hidHOSOBENHANID").val()
		}, {
			name: 'i_khambenhid',
			type: 'String',
			value: $("#hidKHAMBENHIDNTU").val()
		}, {
			name: 'i_phongids',
			type: 'String',
			value: phongid
		}, {
			name: 'i_pkdkids',
			type: 'String',
			value: phongkhamdangkyid
		}, {
			name: 'RPT_CODE',
			type: 'String',
			value: 'RPT_PHIEUKHAMCHUYENKHOA_A4'
		}];
		if (signType == '0') {
			CommonUtil.openReportGetCA2(par, false);
		} else {
			CommonUtil.kyCA(par, signType, true);
			EventUtil.setEvent("eventKyCA", function (e) {
				DlgUtil.showMsg(e.res);
			});
		}
	}
}
