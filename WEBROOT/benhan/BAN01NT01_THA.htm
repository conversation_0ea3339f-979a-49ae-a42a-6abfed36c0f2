<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jquery/jquery.confirm.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
			<!-- <link rel="stylesheet" href="../noitru/custom_nt.css"> -->
<link rel="stylesheet" href="../common/css/css_style.css">
<link rel="stylesheet" href="../common/css/custom_ban.css"></link>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../benhan/BAN_Common.js?v=08072019"></script>
<script type="text/javascript" src="../noitru/NTU02D023_Thongtinhanhchinh.js"></script>
<script type="text/javascript" src="../benhan/BAN01NT01_THA.js?v=0001"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>


<div width="100%" id="divMain-BA" style="border: 0px;">
	<ul id="tabs" class="nav nav-tabs" style="margin-top: 10px;" data-tabs="tabs">
		<li class="active"><a href="#tabBenhAnHoiKham" data-toggle="tab">A.Bệnh án hỏi khám bệnh</a></li>
		<li><a href="#tabBenhAnTongKet" data-toggle="tab">B.Tổng kết hồ sơ bệnh án</a></li>
		<li><a href="#tabBenhNhanThongTin" data-toggle="tab">C.Thông tin bệnh nhân</a></li>		
	</ul>

	<div id="my-tab-content" class="tab-content">
		<div><a href="#" class="scrollToTop before"></a></div>
		<div id="tabBenhAnHoiKham" class="tab-pane active mgb10" style="padding-bottom: 10px;">
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">				
				<div class="panel-body">
					<input type="hidden" id="hidKHAMBENHID" name="hidKHAMBENHID">
					<input type="hidden" id="hidKHOAID" name="hidKHOAID">
					<input type="hidden" id="hidPHONGID" name="hidPHONGID">
				</div>
			</div> <!-- END FORM INLINE -->
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">I. Lý do vào viện:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-2 low-padding form-group required">
							<label class='l-col-3'>Lý do vào viện:</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtLYDOVAOVIEN"
								maxlength="500" name="txtLYDOVAOVIEN" title="" valrule="Lý do vào viện,trim_required|max_length[500]"
								style="width: 100%;">
						</div>
						
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">II. Hỏi bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Quá trình bệnh lý </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLY" valrule="Quá trình bệnh lý,max_length[500]"
								class="form-control input-sm i-col-3" id="txtQTBENHLY"
								style="height: 80px !important; width: 100%" maxlength="500"
								rows="4"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>2. Tiền sử bệnh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Bản thân: </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<input valrule="Bản thân,max_length[500]" style=" height: 70px !important; width: 100%;"
								class="form-control input-sm" id="txtBANTHAN"
								maxlength="500" rows="3" name="txtBANTHAN" title="">		
						</div>
					</div>
					
							
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Gia đình: </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<input valrule="Gia đình,max_length[500]" style="width: 100%; height: 70px !important;"
								class="form-control input-sm" id="txtGIADINH"
								maxlength="500" rows="3" name="txtGIADINH" title="">
						</div>							
					</div>						
				</div>
			</div><!-- END FORM INLINE -->
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">			
				<div class="panel-heading">III. Khám bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Toàn thân:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-9 low-padding">
							<textarea name="txtTOANTHAN" valrule="Toàn thân,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTOANTHAN"
								style="height: 130px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					
						<div class="col-xs-3 low-padding">
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class="mgl10">Mạch</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input class="col-xs-4 form-control input-sm " style="width: 100%;" id="txtMACH" name="txtMACH" valrule="Mạch,is_natural|max_length[3]" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhiệt độ:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhiệt độ,numeric|max_length[6]" maxlength="6"
												class="form-control input-sm " style="width: 100%;"
												id="txtNHIETDO" name="txtNHIETDO" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>C</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Huyết áp:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Huyết áp,is_natural|max_length[3]"
												class="form-control input-sm " style="width: 45%;"
												id="txtHUYETAP1" name="txtHUYETAP1" title=""> /
									 <input class="form-control input-sm " style="width: 45%; float: right;"
												valrule="Huyết áp,is_natural|max_length[3]"
												id="txtHUYETAP2" name="txtHUYETAP2" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>mmHg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhịp thở:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhịp thở,is_natural|max_length[3]"
									class="form-control input-sm " style="width: 100%;"
									id="txtNHIPTHO" name="txtNHIPTHO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Cân nặng:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Cân nặng,numeric|max_length[6]" maxlength="6"
									class="form-control input-sm " style="width: 100%;"
									id="txtCANNANG" name="txtCANNANG" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>kg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chiều cao:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Chiều cao,numeric|max_length[4]"
										   class="form-control input-sm " style="width: 100%;" maxlength="4"
										   id="txtCHIEUCAO" name="txtCHIEUCAO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>cm</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chỉ số BMI:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<label id="txtBMI" name="txtBMI" title=""></label>
								</div>
							</div>
						</div>
					</div>
				<div class=" form-inline">
					<div class="col-xs-12 low-padding">
						<label class='l-col-3'>2. Các bộ phận:</label>
					</div>
				</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtCACBOPHAN" valrule="Các bộ phận,max_length[500]"
								class="form-control input-sm i-col-3" id="txtCACBOPHAN"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Tóm tắt kết quả cận lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-10 low-padding">
							<textarea name="txtTOMTATKQCLS" valrule="Tóm tắt kết quả cận lâm sàng,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTOMTATKQCLS"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
						<div class="col-xs-2 low-padding" style="text-align: left;">
							<button id="btnKQCLS" class="btn btn-sm btn-primary" style="width: 20% !important;">...</button>
						</div>
					</div>	
				<div class="form-inline">
					<div class="col-xs-2 low-padding">
						<label class='1-col-3'>4. Chẩn đoán ban đầu:</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm" style="width: 100%;" id="txtMACDBANDAU" name="txtMACDBANDAU" valrule="Mã chẩn đoán ban đầu,max_length[500]" title="">
					</div>
					<div class="col-xs-8 low-padding" id="divBc">
						<input class="form-control input-sm" style="width: 100%;" id="txtCDBANDAU" name="txtCDBANDAU" valrule="Chẩn đoán ban đầu,max_length[500]" title="">
					</div>					
				</div>				
				<div class=" form-inline">
					<div class="col-xs-12 low-padding">
						<label class='l-col-3'>5. Đã xử lý (thuốc, chăm sóc):</label>
					</div>
				</div>
				<div class=" form-inline">
					<div class="col-xs-12 low-padding">
						<textarea name="txtDXLTUYENDUOI" valrule="Đã xử lý (thuốc chăm sóc),max_length[500]"
							class="form-control input-sm i-col-3" id="txtDXLTUYENDUOI"
							style="height: 70px !important; width: 100%" maxlength="500"
							rows="3"></textarea>
					</div>
				</div>
			
				<div class="form-inline">
					<div class="col-xs-2 low-padding">
						<label class='l-col-3'>6. Chẩn đoán khi ra viện: </label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm mgt5" style="width: 100%;" id="txtMACHANDOANRAVIEN" name="txtMACHANDOANRAVIEN" valrule="Mã chẩn đoán ra viện,max_length[500]" title="">
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm mgt5" style="width: 100%;" id="txtCHANDOANRAVIEN" name="txtCHANDOANRAVIEN" valrule="Chẩn đoán ra viện,max_length[500]" title="">
					</div>
				</div>
			
				<div class=" form-inline mgb35" style="margin-bottom: 50px !important;">
					<div class="col-xs-2 low-padding  form-inline mgt10">
						<label class='control-label'>7. Điều trị ngoại trú:</label>
					</div>
					<div class="col-xs-2 low-padding  form-inline mgt5">
						<div class="col-xs-4 low-padding">
							<label class=' control-label'>Từ ngày</label>
						</div>
						<div class="col-xs-8 low-padding">
							<div class="input-group">
								<input class="form-control input-sm" valrule="Từ ngày,date"
									style="width: 85%; float: right; z-index: 0!important;" id="cldDTNTTUNGAY"
									name="cldDTNTTUNGAY" title="" data-mask="00/00/0000"
									placeholder="dd/MM/yyyy"> <span
									class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
									type="sCal" 
									onclick="NewCssCal('cldDTNTTUNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
							</div>
						</div>
					</div>
					<div class="col-xs-2 low-padding  form-inline mgt5">
						<div class="col-xs-4 low-padding">
							<label class=' control-label mgl10'>Đến ngày</label>
						</div>
						<div class="col-xs-8 low-padding">
							<div class="input-group ">
								<input class="form-control input-sm"  valrule="Từ ngày,date"
									style="width: 85%; float: right; z-index: 0!important;" id="cldDTNTDENNGAY"
									name="cldDTNTDENNGAY" title="" data-mask="00/00/0000"
									placeholder="dd/MM/yyyy"> <span
									class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
									type="sCal" valrule="Đến ngày,date"
									onclick="NewCssCal('cldDTNTDENNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
							</div>
						</div>
					</div>
					<div class="col-xs-3 low-padding  form-inline"></div>
				</div>
			</div>
		</div>							
		</div>
		<div id="tabBenhAnTongKet" class="tab-pane mgb10" style="padding-bottom: 10px;">
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">VI. KẾT QUẢ ĐIỀU TRỊ:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>1. Quá trình bệnh lý và diễn biến lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLYDIENBIENLS" valrule="Quá trình bệnh lý và diễn biến lâm sàng,max_length[500]"
								class="form-control input-sm i-col-3" id="txtQTBENHLYDIENBIENLS"
								style="height: 80px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>2.Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-10 low-padding">
							<textarea name="txtKETQUAXNCLS"
								valrule="Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán,max_length[500]"
								class="form-control input-sm i-col-3" id="txtKETQUAXNCLS"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
						<div class="col-xs-2 low-padding" style="text-align: left;">
							<button id="btnKQCLS1" class="btn btn-sm btn-primary" style="width: 20% !important;">...</button>
						</div>
					</div>

					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Chẩn đoán ra viện:</label>
						</div>
					</div>
					<div class="panel-body">
						<div class="form-inline">
							<div class="col-xs-2 low-padding form-group">
								<label>+ Bệnh chính:</label>
							</div>
							<div class="col-xs-1 low-padding">
								<input class="form-control input-sm" style="width: 100%;" id=txtMABENHCHINH name="txtMABENHCHINH" valrule="Mã bệnh chính,max_length[500]" title="" attrIcd="0" targetCtr="txtBENHCHINH">
							</div>
							<div class="col-xs-7 low-padding">
								<input class="form-control input-sm" style="width: 100%;" id="txtBENHCHINH" name="txtBENHCHINH" valrule="Tên bệnh chính,max_length[500]" title="">
							</div>
							<div class="col-xs-2 low-padding" style="display:none" id="divSuaBc">
								<input class="form-control input-sm" style="width: 100%;" id="txtGHICHU_BENHCHINH" name="txtGHICHU_BENHCHINH" maxlength="500">
							</div>
						</div>
					</div>
					<div class="panel-body">
						<div class="form-inline">
							<div class="col-xs-2 low-padding form-group">
								<label>+ Bệnh kèm theo(nếu có)</label>
							</div>
							<div class="col-xs-2 low-padding">
								<input class="form-control input-sm" style="width: 100%;" id="txtMABENHKEMTHEO" name="txtMABENHKEMTHEO" valrule="Mã bệnh kèm theo,max_length[500]" title="" attrIcd="1" targetCtr="txtBENHKEMTHEO">
							</div>
							<div class="col-xs-8 low-padding">
								<input class="form-control input-sm" style="width: 100%;" id="txtBENHKEMTHEO" name="txtBENHKEMTHEO" valrule="Tên bệnh kèm theo,max_length[500]" title="">
							</div>
						</div>
					</div>
					
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Phương pháp điều trị:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtPHUONGPHAPDIEUTRI"
								valrule="Phương pháp điều trị,max_length[500]"
								class="form-control input-sm i-col-3" id="txtPHUONGPHAPDIEUTRI"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>	
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Tình trạng người bệnh ra viện:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTTNGUOIBENHRAVIEN"
								valrule="Tình trạng người bệnh ra viện,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTTNGUOIBENHRAVIEN"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>	
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>6. Hướng điều trị và các chế độ tiếp theo:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHUONGDIEUTRICHEDO"
								valrule="Hướng điều trị và các chế độ tiếp theo,max_length[500]"
								class="form-control input-sm i-col-3" id="txtHUONGDIEUTRICHEDO"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>

				<div class=" form-inline">
						<div class="col-xs-1 low-padding" style="width: 2px"></div>
						<div class="col-xs-11 low-padding" style="width: 100%">
							<table class="table table-bordered" style="margin-bottom: 70px;">
								<thead>
									<tr>
										<th colspan="2"
											style="font-size: x-small; text-align: center;">Hồ sơ,
											phim, ảnh</th>
									</tr>
									<tr>
										<th style="font-size: x-small; text-align: center;">Loại</th>
										<th style="font-size: x-small; text-align: center;">Số
											tờ</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td><label class='l-col-1 control-label'>- X -
												quang</label></td>
										<td><input class="form-control input-sm i-col-3" style="width: 100%"
											valrule="Số X - Quang,is_natural|max_length[5]"
											id="txtSOXQUANG" name="txtSOXQUANG" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- CT
												Scanner</label></td> 
										<td><input class="form-control input-sm i-col-3" style="width: 100%"
											valrule="Số CT Scanner,is_natural|max_length[5]"
											id="txtSOSCANNER" name="txtSOSCANNER" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Siêu
												âm</label></td>
										<td><input class="form-control input-sm i-col-3" style="width: 100%"
											valrule="Số Siêu âm,is_natural|max_length[5]"
											id="txtSOSIEUAM" name="txtSOSIEUAM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Xét
												nghiệm</label></td>
										<td><input class="form-control input-sm i-col-3" style="width: 100%"
											valrule="Số Xét nghiệm,is_natural|max_length[5]"
											id="txtSOXETNGHIEM" name="txtSOXETNGHIEM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>-
												Khác……………</label></td>
										<td><input class="form-control input-sm i-col-3" style="width: 100%"
											valrule="Số khác,is_natural|max_length[5]"
											id="txtSOKHAC" name="txtSOKHAC" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Toàn
												bộ hồ sơ</label></td>
										<td><input class="form-control input-sm i-col-3" style="width: 100%"
											id="txtSOTOANBOHOSO" name="txtSOTOANBOHOSO" title=""
											disabled="disabled"></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div> 
		</div>
		<div id="tabBenhNhanThongTin" class="tab-pane"></div>
		
		<div class="form-inline btn-fixed">
			<div class="col-xs-12 low-padding mgt10 " style="text-align: right;" id="gridButton">
				<div class="col-xs-7 low-padding">
					<button class="btn btn-sm btn-primary" id="btnLuu">
						<span class="glyphicon glyphicon-pencil"></span> Lưu
					</button>
					<button class="btn btn-sm btn-primary" id="btnThemMoi">
						<span class="glyphicon glyphicon-floppy-remove"></span> Lưu & Đóng
					</button>
					<button class="btn btn-sm btn-primary" id="btnInBieuMau">
						<span class="fixDisplay glyphicon glyphicon-print"></span> In biểu mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btn_Close">
						<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
					<button class="btn btn-sm btn-primary" id="btnLuuBAMau">
						<span class="glyphicon glyphicon-pencil"></span> Lưu BA mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btnXoaBAMau">
						<span class="glyphicon glyphicon-pencil"></span> Xóa BA mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btnCopy" style=" display: none;">
						<span class="glyphicon glyphicon-pencil"></span> Copy BA
					</button>
				</div>
				<div class="col-xs-5 low-padding mgt3">
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Tên BA mẫu:</label>
					</div>
					<div class="col-xs-4 low-padding">
						<input class="form-control input-sm" id="txtTENBENHANMAU"
							maxlength="500" name="txtTENBENHANMAU" title="" valrule="Tên bệnh án mẫu,max_length[500]"
							style="width: 100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Bệnh án mẫu:</label>
					</div>
					<div class="col-xs-4 low-padding">
						<div class="input-group" style="width:100%">				     			
							<select class="form-control input-sm" id="cboMAUBAID" style="width: 100%;" reffld="TENBAMAU" >
							</select> 
			     		</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt5 mgb5" style="text-align: center; display: none;">
				<div class="col-xs-7 low-padding" style="text-align: center;">
					<button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
						<span class="glyphicon glyphicon-ok"></span> Ký & In
					</button>
					<button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
						<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
					</button>
					<button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
						<span class="glyphicon glyphicon-print"></span> In ký số
					</button>
				</div>
			</div>
		</div>
		<script>
			var opt = [];
			var hospital_id = '{hospital_id}';
			var user_id = '{user_id}';
			var user_type = '{user_type}';
			var province_id = '{province_id}';
			var dept_id = '{dept_id}';
			var uuid = '{uuid}';
			var db_schema='{db_schema}';
			var phongid = '{subdept_id}';			
			var lang = "vi";
			console.log('hospital_id=' + hospital_id + ' user_type='
					+ user_type);
			var session_par = [];
			session_par[0] = hospital_id;
			session_par[1] = user_id;
			session_par[2] = user_type;
			session_par[3] = province_id;
			session_par[4] = db_schema;
			var table_name = '{table}';
	
			var _opts = new Object();
			var mode = '{showMode}';				
			if(mode=='dlg') {		
				parent.DlgUtil.tunnel(DlgUtil.moveEvent);
				data=EventUtil.getVar("dlgVar");		
				_opts.khambenhid = data.khambenhid;
				_opts.hosobenhanid = data.hosobenhanid;		
				_opts.benhnhanid = data.benhnhanid;
				_opts.loaibenhanid = data.loaibenhanid;
				_opts.maloaibenhan = data.maloaibenhan;
				// nvangoc start L2PT-6142
				_opts.sovaovien = data.sovaovien;
				// nvangoc end L2PT-6142
				_opts.khoaid=dept_id;
				_opts.phongid=phongid;
			}
	
			_opts._param = session_par;
			_opts._uuid = uuid;
			initRest(_opts._uuid);
			var BAN01_NT01 = new BAN01NT01_THA(_opts);
			BAN01_NT01.load();
		</script>
	</div>
</div>
