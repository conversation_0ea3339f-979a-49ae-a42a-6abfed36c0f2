/*
FeatureId:NGT02K028_HienthiDanhSachKham
Creator: ManhNV
CreateDate: 10/09/2016
Desc: <PERSON><PERSON> sách chờ bệnh nhân hiển thị màn hình chờ
*/
function ThongBaoBenhNhan(opt) {
    this.load = doLoad;

    function doLoad() {
        var _param = opt._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        _initControl();
    }

    function _initControl() {
        document.onwebkitfullscreenchange = fullscreenChanged;
        document.documentElement.onclick = goFullscreen;
        document.onkeydown = goFullscreen;
        $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K053_LCD_CHOKHAM_KHA&showMode=dlg");

        _loadData();
        setInterval(function () {
            _loadData();
        }, 3000);// set 30s
    }

    function _loadData() {
        var html = '';
        var obj = new Object();
        obj.MAPHONG = 1;
        var sql_par1 = [JSON.stringify(obj)];
        var data_ar1 = jsonrpc.AjaxJson.ajaxCALL_SP_O('NGT_LCD_CHOKHAM_KHA', sql_par1.join('$'));
        if (data_ar1 != null && data_ar1.length > 0) {
            var outputArray = data_ar1.reduce((acc, item) => {
                let found = false;
                acc.forEach(group => {
                    if (group[0].ORG_NAME === item.ORG_NAME) {
                group.push(item);
                found = true;
                    }
                });
                    if (!found) {
                        acc.push([item]);
                    }
                    return acc;
                }, []);

            for (var i = 0; i < outputArray.length ; i++) {

                     html += '  <div class="grid-item ">';
                     html += '   <table border="1" style="width: 100%;border:#6BC0F4" class="table" id="list">';
                     html += '      <tr>';
                     html += '         <td colspan="2" style="background-color: #05307f"><p class="text1">' + outputArray[i][0].ORG_NAME + '</p></td>';
                     html += '    </tr>';
                     html += '    <tr>';
                     html += '         <td style="background-color: #7b8ec1"><p class="text2"> SỐ KHÁM </p></td>';
                     html += '       <td style="background-color: #7b8ec1"><p class="text2"> HỌ VÀ TÊN </p></td>';
                     html += '    </tr>';
                     for (var j = 0; j < outputArray[i].length ; j++) {
                         html += '     <tr>';
                         html += '         <td><p style="font-size: 20px;font-weight: bold">' + outputArray[i][j].STT + '</p></td>';
                         html += '         <td><p style="font-size: 20px;font-weight: bold"> ' +  outputArray[i][j].TENBENHNHAN + '</p></td>';
                         html += '     </tr>';
                     }
                     html += '  </table>';
                     html += ' </div>';

            }

        }
        $('#grid1').html(html);
    }
    function goFullscreen() {
        // Must be called as a result of user interaction to work
        var isFirefox = typeof InstallTrigger !== 'undefined';
        mf = document.getElementById("main_frame");
        if (!isFirefox) {
            mf.webkitRequestFullscreen();
//        	 mf.style.display="";
        } else {
            mf.mozRequestFullScreen();
        }
    }

    function fullscreenChanged() {
        if (document.webkitFullscreenElement == null) {
            mf = document.getElementById("main_frame");
            mf.style.display = "none";
        }
    }
}

