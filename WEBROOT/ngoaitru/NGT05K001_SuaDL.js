function ngt05k001_suadl(_opts) {    
    this.load = doLoad;
    var _key = "HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;";
    var _param = new Object();
    function doLoad() { 
        initControl();
        bindEvent();
    }

    function initControl() {
    	var grdBAHeader= _key;
		var rets = jsonrpc.AjaxJson.ajaxCALL_SP_O("DS_COLUMN",""+'$'+"BENHAN"+'$');
		for(var i = 0; i < rets.length;i++){
			grdBAHeader = grdBAHeader + rets[i].COLUMN_NAME+","+rets[i].COLUMN_NAME+",100,0,f,l,1,2;";
		}
		grdBAHeader = grdBAHeader.substring(0, grdBAHeader.length-1)	    	
        GridUtil.init("grdTTBENHAN", "100%", "50", "Thông tin bệnh án", false, grdBAHeader, false, {rowNum: 50, rowList: [50, 100, 150]});
		$("#grdTTBENHAN")[0].toggleToolbar();
    }

    function bindEvent() {
        $('#btnTimKiem').click(function () {
        	loadBAGridData("BENHAN", "grdTTBENHAN"); 
        });        

        GridUtil.setGridParam("grdTTBENHAN", {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll("grdTTBENHAN");
                GridUtil.markRow("grdTTBENHAN", id);	 			
	 			var row = $("#grdTTBENHAN").jqGrid('getRowData',id);	 			
	 			_param['HOSOBENHANID'] = row.HOSOBENHANID;
	 			_param['TIEPNHANID'] = row.TIEPNHANID;
	 			_param['KHAMBENHID'] = row.KHAMBENHID;
                _loadBang();
            },
            ondblClickRow: function(id) { 
            	var row = $("#grdTTBENHAN").jqGrid('getRowData',id);	 			
	 			_param['HOSOBENHANID'] = row.HOSOBENHANID;
	 			_param['TIEPNHANID'] = row.TIEPNHANID;
	 			_param['KHAMBENHID'] = row.KHAMBENHID;
	 			
	 			_param['MA_BANG'] = "BENHAN";
	        	dlgPopup=DlgUtil.buildPopupUrl("dlgdl","divDlg","manager.jsp?func=../ngoaitru/DMC_TABLE",_param,"Cập nhật thông tin",1500,900);
	    		DlgUtil.open("dlgdl"); 
 			}
        });
    }    
    
    function _loadBang(){ 
		var dem = 0;
		var shtml = '';
		$('#btnDsbang').html(shtml);
		var sql_par=[];		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DS_BANG_DL", sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			shtml += '<tr>';
			for(var i = 0; i < data_ar.length; i++){
				dem += 1;
				shtml += '<td> <button type="button" class="clsbtn" id="btnDM'+data_ar[i].MA_BANG+'">' + data_ar[i].TEN_BANG +' </br> ('+data_ar[i].MA_BANG+')'+'</button></td>';	
				if(dem == 7){
					shtml += '</tr>';
					shtml += '<tr>';
					dem = 0;
				}
			}
		}
		$('#btnDsbang').html(shtml);
		
		$('[id^="btnDM"]').click(function(event) {
        	_param['MA_BANG'] = event.currentTarget.id.replace('btnDM','');
        	dlgPopup=DlgUtil.buildPopupUrl("dlgdl","divDlg","manager.jsp?func=../ngoaitru/DMC_TABLE",_param,"Cập nhật thông tin",1500,900);
    		DlgUtil.open("dlgdl"); 
		})	
	}
    
    function loadBAGridData(type, id){		
		var rets = jsonrpc.AjaxJson.ajaxCALL_SP_O("DS_COLUMN",$('#txtMABENHAN').val()+'$'+type+'$');
		GridUtil.clearGridData(id);
        GridUtil.fetchGridData(id, rets);
	}
} 