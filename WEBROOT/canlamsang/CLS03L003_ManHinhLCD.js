// VietDA 08/10/2016
// <PERSON><PERSON><PERSON> hình LCD hiển thị danh sách chờ lấy mẫu bệnh phẩm
// 

function XuatManHinh(opt) {
	this.load=doLoad;
	
	var opts = opt;	
	
	function doLoad() {
		_initControl();		
		bindEvent();	
	}
	
	function _initControl(){			
		//window.moveTo(0, 0);
        window.resizeTo(screen.width, screen.height);

		$('#tenphong').text("DANH SÁCH CHỜ THỰC HIỆN");
		var _sql_par=[];
		var hospitalData = jsonrpc.AjaxJson.ajaxCALL_SP_O("FRM.MAIN.HOSPITAL", _sql_par.join('$'));
			if(hospitalData.length>0){
				let row = hospitalData[0];				 
				$("#base64image").attr('src', row["ORG_LOGO"]);
			}
		
		loadData();		
		/*
		setInterval(function(){
			loadData();			
		}, 5000);
		*/
	}
	
	function loadData(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					opts.phongid+'$'+
					opts.tungay+'$'+
					opts.denngay+'$'+
					'2$'+ 	// trang thai
					'1000'		// 5 ban ghi tren trang
					; 	
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS03L001.LCD", param);
		
		var html = '';
		if (data_ar != null && data_ar.length > 0) {
			for(var i = 0; i < data_ar.length; i++){
				html += '<tr>';
				html += '<td><h2><center>'+data_ar[i].SOTHUTU+'</center></h2></td>';
				html += '<td><h2>'+data_ar[i].TENBENHNHAN+'</h2></td>';
				html += '<td><h2><center>'+data_ar[i].NGAYSINH+'</center></h2></td>';
				html += '<td class="tomau">'+data_ar[i].TIEPNHANID+'</td>';
				html += '</tr>';
			}
			$('#tenphong').text(data_ar[0].PHONGTHUCHIEN);
		}

		$('#list').html(html);
		
		var constant = parseFloat(getSpeedFromCauHinh()); // cang lon thi cang nhanh chong mat
		if(constant != 0){
			var x = $('#list').prop('scrollHeight') -  $('#list').height();
			var speed = Math.round(x/constant*100);
			if(x>0){
				scroll(speed,x);
				//scroll(20000,2000);
			}
		}
	}
	
	function bindEvent() {
		window.addEventListener('message', function(e) {
			//alert(e.data);
			$('#tbl_list tr').each(function() {
				var _tnid = $(this).find(".tomau").html(); 
			  	if (_tnid == e.data) {
					$( this ).addClass( "changecolor" );
					//$( this ).addClass( "blinking" );
				} else {
					$( this ).removeClass( "changecolor" );					
					//$( this ).removeClass( "blinking" );
				}
			});
		} , false);
	}
	function auToScroll(speed, x){
		scroll(speed,x);
		setInterval(function(){
			scroll(speed, x);
		}, speed * 2);	
	}
	
	function scroll(speed, x) {
	    $('#list').animate( 
	    	{scrollTop: x}
	    	, speed
	    	, function() {
		        $(this).animate({ scrollTop: 0 }, speed, function(){
		        	//window.location.reload(true);	
		        });
		         
		    }
	    );
	}
	
	function getSpeedFromCauHinh(){
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'NTU02D021_LCD_SPEED_SCROLL');
		if (data_ar != null && data_ar.length > 0) {
			return data_ar[0].NTU02D021_LCD_SPEED_SCROLL;
		} else {
			return 1; // default
		}
	}
}

