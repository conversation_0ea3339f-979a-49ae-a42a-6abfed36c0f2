function cls004_doloangxuong(opt) {
	this.load = doLoad;
	var _opt = opt;
	var _rpt_code_kyso = 'PHIEU_DO_LOANG_XUONG';
	var _tocnguoiid="";
	function doLoad() {
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		//$('#txtTHOIGIANCHIDINH').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY hh24:mi:ss'));
		$('#txtNGAYDANGKY').val(_opt.ngaychidinh);
		$('#txtTHOIGIANCHUP').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY hh24:mi:ss'));
		$('#hidKHAMBENHID').val(_opt.khambenhid);
		$('#hidHOSOBENHANID').val(_opt.hosobenhanid);
		$('#hidBENHNHANID').val(_opt.benhnhanid);
		$('#hidDICHVUKHAMBENHID').val(_opt.dichvukhambenhid);
		$('#hidKETQUACLSID').val(_opt.ketquaclsid);

		var data=null;
		var parr = [$('#hidDICHVUKHAMBENHID').val()];
		data = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS004.DLX.INFO", parr.join('$'));
		if (data != null && data != 'null' && data.length > 0) {
			_tocnguoiid=data[0].TOCNGUOIID;
		}

		init_cbo();	
		loadForm(data);
		
	}
	
	function loadForm(data){
		if (data != null && data != 'null' && data.length > 0) {
			//$('#hidDOLOANGXUONGID').val(data[0].DOLOANGXUONGID);
			FormUtil.setObjectToForm("cls_doloangxuong", "", data[0]);
			var option = $('<option value="' + data[0].BACSIID + '">' + data[0].TENBACSI + '</option>');		
			$("#cboBACSIID").empty();
			$("#cboBACSIID").append(option);

			$("[name='radLOANGXUONG']").each(function(){
				$(this).prop('checked', $(this).val() == data[0].LOANGXUONG)});
		}else{
			var option = $('<option value="' + _opt.user_id + '">' + _opt.full_name + '</option>');
			$("#cboBACSIID").empty();
			$("#cboBACSIID").append(option);

		}
	}
	
	 $('#btnCLEARBACSI').on("click", function () {
         $("#txtBACSI").val("");
         var option = $('<option value="-1">--Lựa chọn--</option>');
         $("#cboBACSIID").empty();
         $("#cboBACSIID").append(option);         
     });

	function init_cbo() {
		 var _colBsNgoai = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên bác sỹ,OFFICER_NAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";
	        ComboUtil.initComboGrid("txtBACSI", "CDDV.BSNGOAIPK", [], "600px", _colBsNgoai, function (event, ui) {
	            $("#txtBACSI").val("");
	            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
	            $("#cboBACSIID").empty();
	            $("#cboBACSIID").append(option);
	            return false;
	        });

		var sql_par = [];
		ComboUtil.getComboTag("cboTOCNGUOIID",'DLX.TOCNGUOI', sql_par,_tocnguoiid,{value:'-1',text:'--Lựa chọn--'},"sql");
		var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (kyso_kydientu != "1") {
			$('#' + 'btnKySo').remove();
			$('#' + 'btnHuyKy').remove();
			$('#' + 'btnInKySo').remove();
		}
	}	

	
	function _bindEvent() {

		$("#btnCapNhat").on("click", function(e) {
			var validate = new DataValidator("cls_doloangxuong");
			if (validate.validateForm() && _validate()){
				_capNhat("1");
			}
		});

		$("#btnCapNhatClose").on("click", function(e) {
			var validate = new DataValidator("cls_doloangxuong");
			if (validate.validateForm() && _validate()){
				_capNhat("2");
			}
		});



		$("#btnXoa")
				.on(
						"click",
						function() {
							if ($('#hidDOLOANGXUONGID').val() == "0" || $('#hidDOLOANGXUONGID').val()=="") {
								DlgUtil.showMsg("Dịch vụ đo loãng chưa có thông tin để xóa");
								return;
							}
							DlgUtil.showConfirm("Bạn có chắc chắn xóa thông tin đo loãng xương?", function(flag) {
								if (flag) {
									_delete_doloangxuong();
								}
							});
						});

		$("#btnInPhieu").on("click", function(e) {
			if ($('#hidDOLOANGXUONGID').val() == "0" || $('#hidDOLOANGXUONGID').val()=="") {
				DlgUtil.showMsg("Bệnh nhân chưa có thông tin đo loãng xương để in");
				return;
			}
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			},			
				{
					name : 'dichvukhambenhid',
					type : 'String',
					value : $("#hidDICHVUKHAMBENHID").val()
				}
			];
			openReport('window', "PHIEU_DO_LOANG_XUONG", "pdf", par);
		});

		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close("dlgTTVP");
		});

		$("#btnKySo").on("click", function(e) {
			_caRpt("1");
		});
		$("#btnHuyKy").on("click", function(e) {
			_caRpt("2");
		});
		$("#btnInKySo").on("click", function(e) {
			_inPhieuKySo();
		});

	}

	function _inPhieuKySo() {
		if ($('#hidDOLOANGXUONGID').val() == "0" || $('#hidDOLOANGXUONGID').val()=="") {
			DlgUtil.showMsg("Bệnh nhân chưa có thông tin đo loãng xương in ký số");
			return;
		}

		var par_rpt_KySo = [ {
			name : 'hosobenhanid',
			type : 'String',
			value : $("#hidHOSOBENHANID").val()
		},		
		{
			name : 'dichvukhambenhid',
			type : 'String',
			value : $("#hidDICHVUKHAMBENHID").val()
		}
		];

		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});

		CommonUtil.openReportGetCA2(par_rpt_KySo,false);
	}

	function _caRpt(signType) {
		if ($('#hidDOLOANGXUONGID').val() == "0" || $('#hidDOLOANGXUONGID').val()=="") {
			DlgUtil.showMsg("Bệnh nhân chưa có thông tin đo loãng xương để ký số");
			return;
		}
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : $('#hidHOSOBENHANID').val()
		}, {
			name : 'dichvukhambenhid',
			type : 'String',
			value : $("#hidDICHVUKHAMBENHID").val()
		} ];
		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});
		var msg = CommonUtil.kyCA(par_rpt_KySo, signType, true, false);
		EventUtil.setEvent("eventKyCA",function(e){ 
			DlgUtil.showMsg(e.res);
		});
	}

	function _capNhat(_type) {
		var objData = new Object();
		FormUtil.setFormToObject("cls_doloangxuong", "", objData);
		objData.MODE = ($('#hidDOLOANGXUONGID').val() == "0" || $('#hidDOLOANGXUONGID').val()=="") ? "2" : "1";
		
		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS004.DLX.UPDATE", JSON.stringify(objData));
		  if ($.isNumeric(ret) && ret != -1) {
		  	if(_type=="1"){
				if($('#hidDOLOANGXUONGID').val() == "0" || $('#hidDOLOANGXUONGID').val()=="")
				{
					DlgUtil.showMsg('Thêm mới thông tin đo loãng xương thành công');
				}else{
					DlgUtil.showMsg('cập nhật thông tin đo loãng xương thành công');
				}
				$('#hidDOLOANGXUONGID').val(ret);
			}else if(_type=="2"){
				EventUtil.raiseEvent("CLS02C011_LUU_DONG",{msg:''});
			}

		} else {
			DlgUtil.showMsg('Lưu thông tin thất bại');
		}
	}

	function _delete_doloangxuong() {
		var objData = new Object();
		FormUtil.setFormToObject("cls_doloangxuong", "", objData);
		objData.MODE = "0"; // xoa dl;

		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04.XNKHAC.CAPNHAT", JSON
				.stringify(objData));
		if ($.isNumeric(ret) && ret != -1 ) {
			FormUtil.clearForm("cls_form_input","");
			DlgUtil.showMsg('Xóa thông tin thành công');
			return true;
		} else {
			DlgUtil.showMsg('Xóa thông tin thất bại. ');
			return false;
		}

	}

	function _validate() {

		if ($('#txtNGAYDANGKY').val() == "") {
			setErrValidate('txtTHOIGIANCHIDINH');
			DlgUtil.showMsg('Hãy nhập thời gian chỉ định');
			return false;
		}

		if ($('#cboBACSIID').val() == "") {
			setErrValidate('cboBACSIID');
			DlgUtil.showMsg('Chưa nhập bác sĩ điều trị');
			return false;
		}

		if(parseFloat($('#txtCANNANG').val())<0){
			DlgUtil.showMsg('Cân nặng phải lớn hơn 0');
			onfocus('#txtCANNANG');
			check =  false;
		}


		if($('#txtCHIEUCAO').val()<0){
			DlgUtil.showMsg('Chiều cao phải lớn hơn 0');
			onfocus('#txtCHIEUCAO');
			check =  false;
		}

		return true;
	}

}

