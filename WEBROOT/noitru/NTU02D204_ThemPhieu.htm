<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/html2canvas/html2canvas.js"></script>
<link href="../common/script/lightbox2/css/lightbox.css" rel="stylesheet"/>
<script src="../common/script/lightbox2/js/lightbox.js"></script>
<link href="../common/script/select2/dist/css/select2.min.css" rel="stylesheet"/>
<script src="../common/script/select2/dist/js/select2.min.js"></script>
<script type="text/javascript" src="../noitru/NTU02D204_ThemPhieu.js?v=20250306_01"></script>
<a style="display: none" id="imagePrintData" href="" data-lightbox="image-1">Image #1</a>

<style>
    .select2-container .select2-results__options {
        max-height: 540px !important;
    }

    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
        background-color: #fcfcfc !important;
    }

    .scrollToTopButton {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #1164b4;
        color: white;
        border: none;
        border-radius: 2px;
        padding: 10px 20px;
        cursor: pointer;
        display: none;
    }

    .scrollToBottomButton {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #1164b4;
        color: white;
        border: none;
        border-radius: 2px;
        padding: 10px 20px;
        cursor: pointer;
        display: none;
    }

    .nav-tabs {
        border-bottom: 1px solid #9a9a9a;
    }

    #scrollToTopBtn {
        right: 20px;
    }

    #scrollToBottomBtn {
        right: 20px;
    }

</style>

<div width="100%" id="divMain" class="container">
    <div id='divBenhNhanInfo' class="hidden">
        <ul class="nav nav-tabs">
            <li class="active">
                <a href="#">Thông tin bệnh nhân</a>
            </li>
        </ul>
        <div class="col-md-12 low-padding mgt3">
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Mã BN: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblMABENHNHAN"></label>
                </div>
            </div>
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Mã BA: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblMAHOSOBENHAN"></label>
                </div>
            </div>
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Họ tên: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblTENBENHNHAN"></label>
                </div>
            </div>
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Năm sinh: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblNAMSINH"></label>
                </div>
            </div>
        </div>
        <div class="col-md-12 low-padding mgt3">
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Giới tính: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblGIOITINH"></label>
                </div>
            </div>
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Khoa: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblKHOA"></label>
                </div>
            </div>
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Phòng: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblPHONG"></label>
                </div>
            </div>
            <div class="col-md-3 low-padding">
                <div class="col-md-4 low-padding">
                    <label class="mgl10">Giường: </label>
                </div>
                <div class="col-md-8 low-padding">
                    <label class="notW" id="lblGIUONG"></label>
                </div>
            </div>
        </div>
        <div class="col-md-12 low-padding mgt3">
            <div class="col-md-1 low-padding ">
                <label class="mgl10">Bệnh chính: </label>
            </div>
            <div class="col-md-11 low-padding">
                <input class="form-control input-sm" id="txtGHICHU_BENHCHINH" disabled="disabled"/>
            </div>
        </div>
        <div class="col-md-12 low-padding mgt3 mgb10">
            <div class="col-md-1 low-padding ">
                <label class="mgl10">Bệnh kèm theo: </label>
            </div>
            <div class="col-md-11 low-padding">
                <textarea class="form-control input-sm" row="1" id="txtCHANDOANVAOKHOA_KEMTHEO" disabled="disabled"></textarea>
            </div>
        </div>
    </div>
    <div>
        <ul id="tabsContainer" class="nav nav-tabs hidden" data-tabs="tabs">
            <li role="presentation" class="active" id="tabDanhSach" style="padding: 0px">
                <a href="#tcDSPhieu" id="tabdsp" data-toggle="tab" style="background-color: #1164b4;color: white;">
                    <span class="glyphicon glyphicon-list"></span> Danh sách phiếu
                </a>
            </li>
            <li role="presentation" id="tabHanhChinh" style="padding: 0px">
                <a href="#tcChiTietPhieu" id="tcctp" data-toggle="tab" style="background-color: #1164b4;color: white;">
                    <span class="glyphicon glyphicon-pencil"></span> Chi tiết phiếu</a>
            </li>
        </ul>
        <div class="col-xs-12 low-padding mgb5">
            <ul id="tabsTTPTTT" class="nav nav-tabs hidden" data-tabs="tabs">
                <li role="presentation" class=""></li>
            </ul>
            <div class="tab-content">
                <div id="tcDSPhieu" class="tab-pane active hidden" style="width: 100%">
                    <div id="divDanhSach" class="col-md-12 low-padding mgt3">
                        <div class="col-md-12 low-padding mgt3 mgb3">
                            <div class="col-md-1 low-padding">
                                <label class="mgl5">Danh sách</label>
                            </div>
                            <div class="col-md-11 low-padding">
                                <select class="form-control input-sm" id="cboDANHSACH" style="width: 100%;">
                                    <option value="-1">-- Chọn phiếu --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-1 low-padding phieu-cha" style="display: none">
                            <label class="mgl5">Phiếu cha:</label>
                        </div>
                        <div class="col-md-11 low-padding phieu-cha" style="display: none">
                            <div class="col-md-2 low-padding">
                                <input class="form-control input-sm" id="txtPHIEUCHA" attrIcd="0" modeDisXT=""/>
                            </div>
                            <div class="col-md-9 low-padding">
                                <select class="form-control input-sm" id="cboPhieuCha" style="width: 100%">
                                    <option value="-1">-- Tất cả --</option>
                                </select>
                            </div>
                            <div class="col-md-1 low-padding">
                                <button type="button" class="form-control input-sm" id="btnCLEAR_PHIEUCHA" modeDisXT=""><span
                                        class="glyphicon glyphicon-remove"></span></button>
                            </div>
                        </div>
                        <div class="col-md-12 low-padding mgt-10">
                            <table id="grdDanhSach" style="padding: 10px"></table>
                            <div id="pager_grdDanhSach"></div>
                        </div>
                        <div class="col-md-12 low-padding mgb5">
                            <div class="col-md-12 low-padding mgt10 mgb15">
                                <div class="col-md-12 low-padding" style="text-align: center;">
                                    <button type="button" class="btn btn-sm" id="btnTaoMoi" style="color: white;background-color: #1164b4;border-radius: 2px" disabled>
                                        <span class="glyphicon glyphicon-plus"></span> Tạo mới
                                    </button>
                                    <button type="button" class="btn btn-sm" id="btnCopy" style="color: white;background-color: #1164b4;border-radius: 2px" disabled>
                                        <span class="glyphicon glyphicon-film"></span> Sao chép
                                    </button>
                                    <button type="button" class="btn btn-sm" id="btnInPhieuAll" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                        <span class="glyphicon glyphicon-print"></span> In phiếu
                                    </button>
                                    <button type="button" class="btn btn-sm" id="btnInPhieuGop" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                        <span class="glyphicon glyphicon-print"></span> In phiếu đã chọn
                                    </button>
                                    <button type="button" class="btn btn-sm" data-catype="1" id="btnKySoInAll" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                        <span class="glyphicon glyphicon-pencil"></span> Ký & In
                                    </button>
                                    <button type="button" class="btn btn-sm" data-catype="2" id="btnHuyKySoAll" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                        <span class="glyphicon glyphicon-remove"></span> Hủy ký
                                    </button>
                                    <button type="button" class="btn btn-sm" data-catype="0" id="btnInKySoAll" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                        <span class="glyphicon glyphicon-print"></span> In ký số
                                    </button>
                                    <button type="button" class="btn btn-sm" id="btnXoa" style="color: white;background-color: #1164b4;border-radius: 2px" disabled>
                                        <span class="glyphicon glyphicon-trash"></span> Xóa phiếu
                                    </button>
                                    <button type="button" class="btn btn-sm" data-catype="0" data-catype="0" id="btnInKySoAllCA"
                                            style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                        <span class="glyphicon glyphicon-print"></span> In tất cả phiếu ký
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tcChiTietPhieu" class="tab-pane">
                    <div id="content_CTPhieu"></div>
                    <ul class="nav nav-tabs" data-tabs="tabs">
                        <li role="presentation" class=""></li>
                    </ul>
                    <div class="col-md-12 low-padding mgt5">
                        <div class="col-md-12 low-padding" style="text-align: center;">
                            <button type="button" class="btn btn-sm " id="btnLuu" style="color: white;background-color: #1164b4;border-radius: 2px" disabled>
                                <span class="glyphicon glyphicon-save"></span> Lưu phiếu
                            </button>
                            <button type="button" class="btn btn-sm" id="btnInPhieu" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                <span class="glyphicon glyphicon-print"></span> In phiếu
                            </button>
                            <button type="button" class="btn btn-sm hidden" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnXoaPhieu">
                                <span class="glyphicon glyphicon-trash"></span> Xóa phiếu
                            </button>
                            <button type="button" class="btn btn-sm hidden" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnViewImageTemplate">
                                <span class="glyphicon glyphicon-film"></span> Hiển thị mẫu dạng ảnh
                            </button>
                            <button type="button" class="btn btn-sm hidden" data-catype="1" id="btnLuuKy" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                <span class="glyphicon glyphicon-pencil"></span> Lưu & Ký
                            </button>
                            <button type="button" class="btn btn-sm hidden" data-catype="1" id="btnKySoIn" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                <span class="glyphicon glyphicon-pencil"></span> Ký & In
                            </button>
                            <button type="button" class="btn btn-sm hidden" data-catype="2" id="btnHuyKySo" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                <span class="glyphicon glyphicon-remove"></span> Hủy ký
                            </button>
                            <button type="button" class="btn btn-sm hidden" data-catype="0" id="btnInKySo" style="display: none;color: white;background-color: #1164b4;border-radius: 2px">
                                <span class="glyphicon glyphicon-print"></span> In ký số
                            </button>
                            <button type="button" class="btn btn-sm hidden" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnClose">
                                <span class="glyphicon glyphicon-remove"></span> Đóng
                            </button>
                        </div>
                    </div>
                    <div id="divTempForm" class="col-md-12 low-padding mgt5 mgb5 hidden">
                        <div class="col-md-12 low-padding" style="text-align: center;">
                            <select style="width: 250px;padding: 5px" id="cboTEMPLATE" filterLikeFc="txtTKTEN_MAU">
                                <option value="-1">-- Chọn mẫu --</option>
                            </select>
                            <label style="margin-left: 5px;margin-right: 5px">Nhập tên mẫu</label>
                            <input id="txtTEMP_NAME" name="txtTEMP_NAME" maxlength="200" style="width: 150px">
                            <button type="button" class="btn btn-sm" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnLuuMau">
                                <span class="glyphicon glyphicon-floppy-disk"></span> Lưu mẫu
                            </button>
                            <button type="button" class="btn btn-sm " style="color: white;background-color: #1164b4;border-radius: 2px" id="btnXoaMau">
                                <span class="glyphicon glyphicon-remove"></span>Xóa mẫu
                            </button>
                        </div>
                    </div>
                    <ul class="nav nav-tabs" data-tabs="tabs">
                        <li role="presentation" class=""></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <button id="scrollToTopBtn" class="btn-sm scrollToTopButton">
        <span class="glyphicon glyphicon-arrow-up"></span> Đầu Trang
    </button>
    <button id="scrollToBottomBtn" class="btn-sm scrollToBottomButton">
        <span class="glyphicon glyphicon-arrow-down"></span> Cuối Trang
    </button>
</div>
<script>
    $(document).ready(function () {
        let hospital_id = '{hospital_id}'
        let objectVar = parent.EventUtil.getVar("dlgVar");
        let _showTabChamSocMoi = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "HIS_SHOW_TAB_CHAMSOC_MOI");
        let _configureTtakeCare = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURE_LIST_TAKE_CARE'));
        $('#cboDANHSACH').select2();
        $('#cboDANHSACH').on('select2:open', function () {
            setTimeout(function () {
                $('.select2-search__field').focus();
            }, 500);
        });

        setTimeout(function () {
            if (_showTabChamSocMoi == '1' && objectVar.LOAICHAMSOC != null) {
                if (hospital_id == _configureTtakeCare[0].CSYTID) {
                    if (objectVar.LOAICHAMSOC == 'CHAMSOCCAPMOT' || objectVar.LOAICHAMSOC == 'CHAMSOCCAPHAIBA') {
                        $('#cboDANHSACH').select2('close');
                    }
                }
            } else if (objectVar.KEY_VALUE == '1') {
                $('#cboDANHSACH').select2('close');
            } else {
                $('#cboDANHSACH').select2('open');
            }
        }, 300);
    });

    document.addEventListener('DOMContentLoaded', function () {
        let scrollToTopBtn = document.getElementById('scrollToTopBtn');
        let scrollToBottomBtn = document.getElementById('scrollToBottomBtn');
        window.addEventListener('scroll', function () {
            if (window.scrollY > 100) {
                scrollToTopBtn.style.display = 'block';
                scrollToBottomBtn.style.display = 'block';
            } else {
                scrollToTopBtn.style.display = 'none';
                scrollToBottomBtn.style.display = 'none';
            }
        });
        scrollToTopBtn.addEventListener('click', function () {
            window.scrollTo({top: 0, behavior: 'smooth'});
        });
        scrollToBottomBtn.addEventListener('click', function () {
            window.scrollTo({top: document.body.scrollHeight, behavior: 'smooth'});
        });
    });

    var opt = [];
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var dept_id = '{dept_id}';
    var subdept_id = '{subdept_id}'
    var uuid = '{uuid}';

    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");

    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid
    _opts._dept_id = dept_id;
    _opts._subdept_id = subdept_id
    _opts._user_id = user_id;
    _opts._hospital_id = hospital_id;
    var DS = new dSDTList(_opts);
    DS.load(hospital_id);
</script>