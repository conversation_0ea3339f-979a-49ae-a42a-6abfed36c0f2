<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BCTH_CDHA_THEODICHVU_30180" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="353a96c9-64d7-4e4f-8692-3b7589376950">
	<property name="ireport.zoom" value="1.771561000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="i_tungay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_denngay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_loaitiepnhanid" class="java.lang.String"/>
	<parameter name="i_dtbnid" class="java.lang.String"/>
	<parameter name="i_nhomdvid" class="java.lang.String"/>
	<parameter name="i_nhomdvid_TEXT" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call bcth_cdha_theodichvu_30180($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{i_tungay},$P{i_denngay},$P{i_loaitiepnhanid},$P{i_dtbnid},$P{i_nhomdvid},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="COUNT_STT" class="java.math.BigDecimal"/>
	<field name="TEN_DICHVU" class="java.lang.String"/>
	<field name="THANG_HOANTHANH" class="java.lang.String"/>
	<field name="NGAY_HOANTHANH" class="java.lang.String"/>
	<field name="COUNT_SOLUONG" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="108">
			<textField>
				<reportElement x="0" y="0" width="240" height="18" uuid="9f3ef653-483f-475c-b19f-0662796e963e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="18" width="240" height="18" uuid="b27d7611-e3fa-47b4-98d6-3e6390ff7c7a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="36" width="240" height="18" uuid="913a09e4-**************-5b850613dfc7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dept_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="79" width="555" height="18" uuid="87408acb-d033-413b-b89b-0b1f1ffe9978"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày : " + new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format($P{i_tungay}) + "  đến ngày : " + new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format($P{i_denngay})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="54" width="555" height="25" uuid="fa76c812-a32d-4089-8c30-211145c970c0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BÁO CÁO TỔNG HỢP " + (
    $P{i_nhomdvid}.equals("-1") ? "CĐHA" :  $P{i_nhomdvid_TEXT}.toUpperCase()
)]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<summary>
		<band height="200">
			<crosstab>
				<reportElement x="0" y="0" width="555" height="200" uuid="a6234e0f-a6a6-46a3-9160-a27b66f1386f"/>
				<crosstabHeaderCell>
					<cellContents>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="140" height="20" uuid="ad9cf0d8-c783-4795-ae37-0140a3aef907"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Ngày]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="TEN_DICHVU" width="140">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TEN_DICHVU}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="140" height="20" uuid="447f1bdd-14db-497f-aaa9-9c22815b9b95"/>
								<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TEN_DICHVU}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="THANG_HOANTHANH" height="0" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{THANG_HOANTHANH}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="0" uuid="9dd84625-d601-4dcf-b4ce-0da373d74848"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{THANG_HOANTHANH}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="70" height="20" forecolor="#000000" uuid="b244d910-e1fe-48da-ba1e-969ae74e4be9"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<text><![CDATA[Tổng cộng]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<columnGroup name="NGAY_HOANTHANH" height="20" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{NGAY_HOANTHANH}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="20" uuid="78f8d2b4-f34e-41e7-82fb-6480312a52af"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{NGAY_HOANTHANH}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="COUNT_STTMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{COUNT_STT}]]></measureExpression>
				</measure>
				<crosstabCell width="50" height="20">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="50" height="20" uuid="2518a2e2-b82f-414f-9570-118495241fad"/>
							<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{COUNT_STTMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="TEN_DICHVU">
					<cellContents backcolor="#BFE1FF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="d484c09d-2abb-4f98-b564-c0e74b5d4b6a"/>
							<textFieldExpression><![CDATA[$V{COUNT_STTMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="70" height="20" columnTotalGroup="THANG_HOANTHANH">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="20" forecolor="#000000" uuid="cd5946cf-200b-4d33-b419-d36eacd0b3a1"/>
							<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{COUNT_STTMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="TEN_DICHVU" columnTotalGroup="THANG_HOANTHANH">
					<cellContents backcolor="#005FB3" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" forecolor="#FFFFFF" uuid="75614bb4-f9a3-42be-a462-b28a2e0d0d61"/>
							<textFieldExpression><![CDATA[$V{COUNT_STTMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="0" height="20" columnTotalGroup="NGAY_HOANTHANH">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="TEN_DICHVU" columnTotalGroup="NGAY_HOANTHANH">
					<cellContents backcolor="#BFE1FF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="fbdcfa5b-eaed-44da-a0de-991f882b6bec"/>
							<textFieldExpression><![CDATA[$V{COUNT_STTMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>
