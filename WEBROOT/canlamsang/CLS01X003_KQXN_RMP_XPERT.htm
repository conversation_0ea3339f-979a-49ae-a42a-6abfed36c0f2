<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20170105" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../canlamsang/ParaclinicalUtility.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS01X003_KQXN_RMP_XPERT.js?v=20180629"></script>

<style>
	.ui-jqgrid tr.jqgrow td {
		white-space: nowrap !important;
		height:auto;
		vertical-align:text-top;
		padding-top:2px;
	}
</style>

<div id="divMain">
	<div class="mgt5" id="divThongTinBenhNhan">
		<div class="col-md-12 low-padding">
			<div class="col-md-1 low-padding">
				<label style="font-weight: 700 !important;">Bệnh nhân</label>
			</div>
			<div class="col-md-4 low-padding">
				<label class="text-red" id="lblHoTen" style="text-transform: uppercase; font-size: 1.3em;"></label>
			</div>
			<div class="col-md-1 low-padding">
			 	<input id="chbKhan" class="mgb5" type="checkbox"> <label>  Khẩn </label>
			</div>
			<div class="col-md-1 low-padding">
				<label>Đối tượng</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblDoiTuong"></label>
			</div>
			<div class="col-md-1 low-padding">
				<label>TG chỉ định</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblTGChiDinh"></label> 
			</div>
		</div>
		
		<div class="col-md-12 low-padding">
			<div class="col-md-6 low-padding">
				<label style="font-weight: 700 !important;" id="lblDiaChi"></label>
			</div>
			<div class="col-md-1 low-padding">
				<label>Bác sĩ CĐ</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblBacSi"></label>  
			</div>
			<div class="col-md-1 low-padding">
				<label>TG kết quả</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblTGKetQua"></label>
			</div>
		</div>
		
		<div class="col-md-12 low-padding">
			<div class="col-md-3 low-padding">
				<div class="col-md-3 low-padding">
					<label>Số phiếu</label>
				</div>
				<div class="col-md-8 low-padding"> 
					<label id="lblSoPhieu" style="color: blue; font-size: large;width: 100%"></label> 
				</div>
			</div>
			<div class="col-md-6 low-padding">
				<div class="col-md-3 low-padding">
					<label>Nơi gửi</label>
				</div>
				<div class="col-md-9 low-padding">
					<label id="lblNoiGui"></label>  
				</div>
			</div>
			<div class="col-md-1 low-padding">
				<label>Người trả KQ</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblNguoiThucHien"></label>  
			</div>
		</div>
		
		<div class="col-md-12 low-padding">
			<div class="col-md-3 low-padding">
				<div class="col-md-3 low-padding">
					<label>Barcode</label>
				</div>
				<div class="col-md-8 low-padding"> 
					<label id="lblBarcode" style="color: red; font-size: large;width: 100%"></label> 
				</div>
			</div>
			<div class="col-md-9 low-padding">
				<div class="col-md-2 low-padding">
					<label>Chẩn đoán</label>
				</div>
				<div class="col-md-10 low-padding">
					<label style="font-weight: 700 !important;" id="lblChanDoan"></label>  
				</div>
			</div>
		</div>
	</div>
	<div class="row">
  		<div class="col-md-12 bs-linebreak" style="height:12px;">
  		</div>
  	</div>
  	
	
	
	<div id="divRMPXPERT" class="col-xs-12 low-padding panel panel-info" >
		<h1 style="color: blue; font-size: large;"">Kết quả RMP Xpert:</h1>
		<div class="mgt5" id="divDSVKRMPXPERT" style=" border-style: groove;border-width: 2px;border-color: blue;" >
			<table class="table table-bordered">
				<tbody>
						<tr>
							
							<td>
								<div class="col-xs-6 low-padding">
									<div class="col-xs-2 low-padding">
										<label class='mgt3 mgl10' style="">Số xét nghiệm</label>
									</div>
									<div class="col-xs-4 low-padding">
										<input class="form-control input-sm"
											style="width: 50%;" id="txtSOXN5"
											name="txtSOXN5" valrule="" title="">
									</div>
								</div>
							</td>
							
						</tr>
				</tbody>
			</table>
			<!-- style="border: 3px solid #000000;" -->
			<table class="table table-bordered" >
				<tbody>
					<tr>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class=''>Mã XN</label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class=''>Tên XN</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Ngày nhận mẫu</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Ngày thực hiện</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Trạng thái mẫu</label>
							</div>
						</td>
	
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Âm tính</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Không kháng RMP</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng RMP</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Không xác định kháng RMP</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Có MTB dạng Vết & không xác định kháng RMP</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Lỗi (ghi rõ mã lỗi)</label>
							</div>
						</td>
						<td style="width: 100px;text-align: center;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Xóa text</label>
							</div>
						</td>
						
					</tr>
					
					<tr>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblMACHIDINH"></label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblTENCHIDINH"></label>
							</div>
						</td>
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding">
								<div class="input-group" style="width: 100% important;">
									<input class="form-control input-sm" id="txtNGAYNHANMAU" name="txtNGAYNHANMAU" title="" data-mask="00/00/0000"> 
									<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtNGAYNHANMAU','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</td>
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding">
								<div class="input-group" style="width: 100% important;">
									<input class="form-control input-sm" id="txtNGAYTHUCHIEN" name="txtNGAYTHUCHIEN" title="" data-mask="00/00/0000"> 
									<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtNGAYTHUCHIEN','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtTRANGTHAIMAU" name="txtTRANGTHAIMAU" valrule=""title="">
							</div>
						</td>
	
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtAMTINH" name="txtAMTINH" valrule=""title="">
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKKRMP" name="txtKKRMP" valrule=""title="">
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGRMP" name="txtKHANGRMP" valrule=""title="">
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKXDKHANGRMP" name="txtKXDKHANGRMP" valrule=""title="">
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtMTBDANGVET" name="txtMTBDANGVET" valrule=""title="">
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtMALOI" name="txtMALOI" valrule=""title="">
							</div>
						</td>
						<td style="width: 100px;">
							<div class="col-xs-12 low-padding">
								<button class="btn btn-sm btn-primary" id="btnXoatext1" style="width:100px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-remove-circle"></span>
								</button>
							</div>
						</td>
						
					</tr>
					
					
					
					
				</tbody>
			</table>
			<div class="row">
		  		<div class="col-md-12 bs-linebreak" style="height:5px;">
		  		</div>
		  	</div>
			
			
		</div>
		
		<div class="col-md-12 low-padding" style="margin-top: 20px;" ></div>
		
		<div class="col-md-12 low-padding"  padding-top: 15px !important; >
					<div class="col-md-10 low-padding" >
						<div class="col-md-10 low-padding">
							<div class="col-md-10 low-padding">
								<button class="btn btn-sm btn-primary" id="btnLuu" style="width:110px">
									<span class="glyphicon glyphicon-floppy-disk"></span> Lưu lại 
								</button>
								<button class="btn btn-sm btn-primary" id="btnTraKetQua" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-check"></span> Trả kết quả 
								</button>
								<!--<button class="btn btn-sm btn-primary" id="btnHuyKetQua" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-remove-circle"></span> Hủy kết quả  
								</button>-->
								<button class="btn btn-sm btn-primary" id="btnInKQ" style="width:150px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-print"></span> In kết quả
								</button>
								
								<button class="btn btn-sm btn-primary" id="btnHuy2" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-log-out"></span> Đóng 
								</button>
								
							</div>
			
						</div>
					</div>
					
			</div>	
	
	</div>
	
	<div id="divKetLuan">
			
	</div>
	
	
	
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDDichVuID" value="">
<input type="hidden" id="hdfIDShowBtnEditTime" value="">
<input type="hidden" id="hdfIDKQ1" value="">
<input type="hidden" id="hdfIDKQ2" value="">
<input type="hidden" id="hdfIDKQ1Xoa" value="">
<input type="hidden" id="hdfIDKQ2Xoa" value="">

<div id="divTraKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmView" style="width:1000px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script type="text/javascript">	
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var ten_khoa = '{dept_name}';
	var lang = "vn";
	var type = "{type}";
	var userRights = "{q}";
	var user_group_id = '{user_group_id}';
	console.log("userright="+userRights+",usergroup="+user_group_id);
	var session_par=[];
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	session_par[0]=hospital_id;
	var table_name='{table}';

	var idmaubenhpham = '{idmaubenhpham}';
	var iddichvukb = '{iddichvukb}';
	var iddichvu = '{dichvuid}';
	//nghiant 26062017
	var showNutSuaNgayTraKQ='{showNutSuaNgayTraKQ}';
	//var vikhuanid = '{vikhuanid}';
	//var loaivikhuanid = '{loaivikhuanid}';
	var show='{show}';
	var tiepnhanid='{tiepnhanid}';
	var rolePTH='{rolePTH}';
	var mode = '{showMode}';
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	}
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	_opts._idmaubenhpham=idmaubenhpham;
	_opts._showNutSuaNgayTraKQ=showNutSuaNgayTraKQ;
	_opts._iddichvukb=iddichvukb;
	_opts._iddichvu=iddichvu;
	_opts._show=show;
	_opts._tiepnhanid=tiepnhanid;
	
	//nghiant 01082017 
	//_opts._phong_chuyen_den_id=phong_chuyen_den_id;
	_opts._rolePTH = rolePTH;
	//_opts._vikhuanid = vikhuanid;
	//_opts._loaivikhuanid = loaivikhuanid;
	console.log('rolePTH: '+_opts._rolePTH);
	
	
	initRest(_opts._uuid);
	
	var tt = new KetQuaXetNghiem3(_opts);
	tt.load();
</script>