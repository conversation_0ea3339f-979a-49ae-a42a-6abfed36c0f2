/*
Y<PERSON>u cầu: BVTM-735
Mục đích  : <PERSON><PERSON><PERSON> kiểm tra trước tiêm chủng đối với người lớn
Người lập tr<PERSON>nh	<PERSON> cập nhật  <PERSON>hi chú
DONGNV	- 06042021 - Comment
*/
function NGT02K001_BangKiemTiemChung(opt) {

    this.load = doLoad;
    var that=this;

    function doLoad(){
    	this.validator = new DataValidator("divBKTC");
    	$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
        initControl();
        bindEvent();
    }

    function initControl(){
        var timeStart = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
        $('#txtTHOIGIAN').val(timeStart);
        $('#txtNGAYHEN').val(timeStart);
		var sql_par = [];
		sql_par.push({"name":"[0]","value":opt.hospital_id});		
		ComboUtil.getComboTag("cboNGUOITHUCHIEN","TC01K002.03", sql_par,null,{value:'',text:'Chọn'},"sql");
		ComboUtil.getComboTag("cboCHIDINH_VACXIN","TC01K003.01", sql_par,null,{value:'',text:'--Chọn--'},"sql");
		
		// Xử lý sự kiện liên quan ký CA => START
        var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU');
        if(_cauhinh != '0' && typeof _cauhinh !== "undefined") {
            $("#btnKyCa").show();
            $("#btnHuyCa").show();
        }
		// Xử lý sự kiện liên quan ký CA => END
		
		loadBangKiem();
    }

    function loadBangKiem(){
    	var param = opt.hosobenhanid + "";
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("TC01K003.S03", param);
        if (result && result.length > 0) {
            var obj = result[0];
            setTimeout(function() {
                FormUtil.setObjectToForm("divBKTC", "", obj );
            },500);
            
            $('#divBKTC input[name=radBENH][value=' + obj.BENH + ']').prop('checked', true);
            $('#divBKTC input[name=radDIUNG][value=' + obj.DIUNG + ']').prop('checked', true);
            $('#divBKTC input[name=radPHANUNG][value=' + obj.PHANUNG + ']').prop('checked', true);
            $('#divBKTC input[name=radDONGKINH][value=' + obj.DONGKINH + ']').prop('checked', true);
            $('#divBKTC input[name=radUNGTHU][value=' + obj.UNGTHU + ']').prop('checked', true);
            $('#divBKTC input[name=radTIA_X][value=' + obj.TIA_X + ']').prop('checked', true);
            $('#divBKTC input[name=radTRUYENMAU][value=' + obj.TRUYENMAU + ']').prop('checked', true);
            $('#divBKTC input[name=radMANGTHAI][value=' + obj.MANGTHAI + ']').prop('checked', true);
            $('#divBKTC input[name=radTIEMVACXIN][value=' + obj.TIEMVACXIN + ']').prop('checked', true);
            $('#divBKTC input[name=radTHANNHIET][value=' + obj.THANNHIET + ']').prop('checked', true);
            $('#divBKTC input[name=radTRIGIAC][value=' + obj.TRIGIAC + ']').prop('checked', true);
            $('#divBKTC input[name=radTIENGTIM][value=' + obj.TIENGTIM + ']').prop('checked', true);
            $('#divBKTC input[name=radNHIPTHO][value=' + obj.NHIPTHO + ']').prop('checked', true);
            $('#divBKTC input[name=radCHONGCHIDINH][value=' + obj.CHONGCHIDINH + ']').prop('checked', true);
            $('#divBKTC input[name=radKETLUAN][value=' + obj.KETLUAN + ']').prop('checked', true);
            
            if (obj.HENKHAM == 1) {
            	$('#div_henkham').show();
            }
        }
    }
    
    function saveBangKiem(flag){
        var obj = new Object();
        FormUtil.setFormToObject("divBKTC", "", obj);
        obj["BENH"] = $("input[name='radBENH']:checked").val();
        obj["DIUNG"] = $("input[name='radDIUNG']:checked").val();
        obj["PHANUNG"] = $("input[name='radPHANUNG']:checked").val();
        obj["DONGKINH"] = $("input[name='radDONGKINH']:checked").val();
        obj["UNGTHU"] = $("input[name='radUNGTHU']:checked").val();
        obj["TIA_X"] = $("input[name='radTIA_X']:checked").val();
        obj["TRUYENMAU"] = $("input[name='radTRUYENMAU']:checked").val();
        obj["MANGTHAI"] = $("input[name='radMANGTHAI']:checked").val();
        obj["TIEMVACXIN"] = $("input[name='radTIEMVACXIN']:checked").val();
        obj["THANNHIET"] = $("input[name='radTHANNHIET']:checked").val();
        obj["TRIGIAC"] = $("input[name='radTRIGIAC']:checked").val();
        obj["TIENGTIM"] = $("input[name='radTIENGTIM']:checked").val();
        obj["NHIPTHO"] = $("input[name='radNHIPTHO']:checked").val();
        obj["CHONGCHIDINH"] = $("input[name='radCHONGCHIDINH']:checked").val();
        obj["KETLUAN"] = $("input[name='radKETLUAN']:checked").val();

        var valid = that.validator.validateForm();	
        if (valid) {
        	var vacxin = obj["CHIDINH_VACXIN"];
        	var kq = obj["KETLUAN"];
        	
            if (!kq) {
            	DlgUtil.showMsg('Kết luận chưa được chọn!');
            	return;
            }
            
            if (kq == 1 && !vacxin) {
            	DlgUtil.showMsg('Chỉ định vắc xin chưa được chọn!');
            	return;
            }
            
            var json_par = JSON.stringify(obj);
            var param = [json_par, opt.hosobenhanid];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("TC01K003.I02", param.join('$'));
            if (result == 1) {
                DlgUtil.showMsg('Lưu thành công!', function () {
                });
                if(flag) {
                    var par = [
                        {name: 'hosobenhanid', type: 'String', value: opt.hosobenhanid }
                    ];
                    openReport('window', 'RPT_BANGKIEM_TRUOC_TC', 'pdf', par);
                }
            } else {
                DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
                });
            }
        }
    }

    function bindEvent(){
    	
        $('#chkHENKHAM').on('change', function(e) {
            if($('#chkHENKHAM').is(":checked")) {
            	$('#div_henkham').show();
            } else {
            	$('#div_henkham').hide();
            }
        });

        $('#btnLuu').on('click', function(e) {
        	saveBangKiem(false);
        });

        $('#btnLuuIn').on('click', function(e) {
            saveBangKiem(true);
        });
        
        $('#btnIn').on('click', function(e) {
            var par = [
                {name: 'hosobenhanid', type: 'String', value: opt.hosobenhanid }
            ];
            openReport('window', 'RPT_BANGKIEM_TRUOC_TC', 'pdf', par);
        });

        $("#btnDong").on("click", function(e) {
            parent.DlgUtil.close("BangKiemTruocTiemChung");
        });
        
        $("#btnKyCa").on("click", function () {
            var obj = new Object();
            FormUtil.setFormToObject("divBKTC", "", obj);
            obj["BENH"] = $("input[name='radBENH']:checked").val();
            obj["DIUNG"] = $("input[name='radDIUNG']:checked").val();
            obj["PHANUNG"] = $("input[name='radPHANUNG']:checked").val();
            obj["DONGKINH"] = $("input[name='radDONGKINH']:checked").val();
            obj["UNGTHU"] = $("input[name='radUNGTHU']:checked").val();
            obj["TIA_X"] = $("input[name='radTIA_X']:checked").val();
            obj["TRUYENMAU"] = $("input[name='radTRUYENMAU']:checked").val();
            obj["MANGTHAI"] = $("input[name='radMANGTHAI']:checked").val();
            obj["TIEMVACXIN"] = $("input[name='radTIEMVACXIN']:checked").val();
            obj["THANNHIET"] = $("input[name='radTHANNHIET']:checked").val();
            obj["TRIGIAC"] = $("input[name='radTRIGIAC']:checked").val();
            obj["TIENGTIM"] = $("input[name='radTIENGTIM']:checked").val();
            obj["NHIPTHO"] = $("input[name='radNHIPTHO']:checked").val();
            obj["CHONGCHIDINH"] = $("input[name='radCHONGCHIDINH']:checked").val();
            obj["KETLUAN"] = $("input[name='radKETLUAN']:checked").val();

            var valid = that.validator.validateForm();	
            if (valid) {
            	var vacxin = obj["CHIDINH_VACXIN"];
            	var kq = obj["KETLUAN"];
            	
                if (!kq) {
                	DlgUtil.showMsg('Kết luận chưa được chọn!');
                	return;
                }
                
                if (kq == 1 && !vacxin) {
                	DlgUtil.showMsg('Chỉ định vắc xin chưa được chọn!');
                	return;
                }
                
                var json_par = JSON.stringify(obj);
                var param = [json_par, opt.hosobenhanid];
                var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("TC01K003.I02", param.join('$'));
                if (result == 1) {
					var _params = [{
	      				name : 'hosobenhanid',
	      				type : 'String',
	      				value : opt.hosobenhanid
	      			},
	                 {
	                     name: 'RPT_CODE',
	                     type: 'String',
	                     value: 'RPT_BANGKIEM_TRUOC_TC'
	                 }];
                    _kyCaRpt(_params)
                } else {
                    DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
                    });
                }
            }
        });
    }
    
	// Xử lý sự kiện liên quan ký CA => START
    function _kyCaRpt(_params){
        var msg = CommonUtil.kyCA(_params);
        EventUtil.setEvent("eventKyCA",function(e){ 
        	DlgUtil.showMsg(e.res); initControl(); 
        });
    }
    // Xử lý sự kiện liên quan ký CA => END
}