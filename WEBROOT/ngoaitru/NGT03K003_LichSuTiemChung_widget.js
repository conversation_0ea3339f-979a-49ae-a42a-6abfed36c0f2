(function ($) {
    $.widget("ui.ngt03k003_lstiemchung", {

        //Options to be used as defaults
        options: {
            _grdLSTC: "grdLSTiemChung",
            imgPath: ""
        },
        containerId: '',
        //Setup widget (eg. element creation, apply theming
        // , bind events etc.)
        _create: function () {
            // _create will automatically run the first time this widget is called. Put the initial widget  setup code here, then you can access the element
            // on which the widget was called via this.element. The options defined above can be accessed via this.options this.element.addStuff();
            console.log('_create');
            this.containerId = $(this.element).attr('id');
            this._initWidget();
        },
        _t: function (_id) {
            var newId = '';
            if (_id.indexOf("=") > 0) {
                newId = _id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi, _self.containerId + "$1");
            } else if (_id.indexOf("#") == 0) {
                newId = _id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi, "$1" + _self.containerId + "$2");
            } else {
                newId = _self.containerId + _id;
            }
            return newId;
        },
        _initWidget: function () {
            var _self = this;
            _self.options.checkLoad = false;
            $(_self.element).load('../ngoaitru/NGT03K003_LichSuTiemChung_widget.tpl', function () {
                $(_self.element).find("[id]").each(function () {
                    if (this.id == "pager_" + _self.options._grdLSTC) {
                        this.id = "pager_" + _self.containerId + _self.options._grdLSTC;
                    } else {
                        this.id = _self.containerId + this.id;
                    }
                });

                //$("[data-i18n]").i18n();
                _self._loadData();
                _self._bindEvent();
                height_window = $(window).height();   // returns height of browser viewport
                height_suatan = $('#' + _self.element.attr('id')).height();
                height_divMain = $('#hidDocumentHeight').val();
                console.log('height_window1:' + height_window);
                console.log('height_suatan1:' + height_suatan);
                console.log('height_divMain1:' + height_divMain);
                if (height_suatan + 110 < height_window) {
                    $('#divMain').css('height', height_divMain);
                } else if (height_window < height_suatan + 110) {
                    $("#divMain").css('height', height_suatan + 110);
                } else if (height_suatan + 110 == height_window) {
                    $('#divMain').css('height', height_suatan + 110);
                }
            });
        },
        _loadData: function () {
            var _self = this;

            var _grdLSTCHeader =
                "DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;" +
                "Mã thuốc,MA_DICHVU,100,0,f,l;" +
                "Ngày tiêm,NGAYMAUBENHPHAM,100,0,f,l;" +
                "Tên thuốc,TENDICHVU,250,0,f,l;" +
                "Số lượng,SOLUONG,70,0,f,l;" +
                "Đơn giá,TIEN_CHITRA,75,0,f,l;" +
                "ĐVT,TEN_DVT,50,0,f,l;" +
                "Đường dùng,DUONGDUNG,100,0,f,l;" +
                "Hưỡng dẫn sử dụng,HUONGDANSUDUNG,235,0,f,l;" +
                "Loại TT,LOAIDOITUONG,60,0,f,l;" +
                "MAUBENHPHAMID,MAUBENHPHAMID,235,0,t,l";

            GridUtil.init(_self.containerId + _self.options._grdLSTC, "100%", "400px",
                'Lịch sử tiêm chủng', false, _grdLSTCHeader, true, {rowNum: 20, rowList: [20, 50, 100]});
            $('#' + _self.containerId + _self.options._grdLSTC)[0].toggleToolbar();
            console.log('_self.options._hoSoBenhNhanId', _self.options._hoSoBenhNhanId);
            var lsTiemChung = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT03K001.LS_TCHUNG", _self.options._hoSoBenhNhanId);
            console.log('lsTiemChung', lsTiemChung);
            GridUtil.fetchGridData(_self.containerId + _self.options._grdLSTC, lsTiemChung);
        },
        _bindEvent: function () {
            var _self = this;
        },

        // Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
        destroy: function () {

            // this.element.removeStuff();
            // For UI 1.8, destroy must be invoked from the
            // base widget
            $.Widget.prototype.destroy.call(this);
            // For UI 1.9, define _destroy instead and don't
            // worry about
            // calling the base widget
        },

        methodB: function (event) {
            //_trigger dispatches callbacks the plugin user
            // can subscribe to
            // signature: _trigger( "callbackName" , [eventObject],
            // [uiObject] )
            // eg. this._trigger( "hover", e /*where e.type ==
            // "mouseenter"*/, { hovered: $(e.target)});
            console.log("methodB called");
        },

        methodA: function (event) {
            this._trigger("dataChanged", event, {
                key: "someValue"
            });
        },

        // Respond to any changes the user makes to the
        // option method
        _setOption: function (key, value) {
            switch (key) {
                case "someValue":
                    //this.options.someValue = doSomethingWith( value );
                    break;
                default:
                    //this.options[ key ] = value;
                    break;
            }

            // For UI 1.8, _setOption must be manually invoked
            // from the base widget
            $.Widget.prototype._setOption.apply(this, arguments);
            if (key == '_grdLSTC') {						// chi lay 1 key de hien thi ra;
                this._initWidget();
            }
            // For UI 1.9 the _super method can be used instead
            // this._super( "_setOption", key, value );
        }
    });
})(jQuery);
