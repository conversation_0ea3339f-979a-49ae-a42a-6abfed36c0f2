function ThongBaoBenhNhan(opt) {
    this.load = doLoad;
    var _opt = opt;
    var arrPhong = [];
    var data_ar_phong = []; 
    var phongs = "";

    function doLoad() {
        _initControl();
    }

    function _initControl() {
        var par = ['NGT02K028_COLUMN_NAME'];
        var dt = "SỐ KHÁM;HỌ TÊN;LẦN GỌI;TRẠNG THÁI";
        
        document.onwebkitfullscreenchange = fullscreenChanged;
        document.documentElement.onclick = goFullscreen;
        document.onkeydown = goFullscreen;

        $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham_MP&showMode=dlg");

        var array = dt.split(";");
        // các đơn vị khác;
        var ds_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_DSPHONG_LCD_KB');
        
        if(ds_phong != '0'){
	    	 var sql_par1 = [];
	         sql_par1.push({"name" : "[0]","value" : _opt.phongid});
	         var data_maphong = jsonrpc.AjaxJson.ajaxExecuteQueryO("PHONG.LAYMA", sql_par1);
	         var maphongs = $.parseJSON(data_maphong);
	         var arPhong = ds_phong.split(";");
	         for(i = 0; i< arPhong.length;i++){
	        	 var lst_phong = arPhong[i].split(",");
	        	 for(j = 0; j< lst_phong.length;j++){
	        		 if(maphongs[0].MAPHONG == lst_phong[j]){
	        			 phongs = arPhong[i];
	        			 break;
	        		 }
	        	}
	         }
        	
        }
        if(!phongs){
        	$("#div1").show();
        	$("#div2").hide();
        	$("#div3").hide();
        	$('#phongkham').text(_opt.subdept_name);
        }else{
        	arrPhong = phongs.split(",");
        	var sql_par = [arrPhong];
            data_ar_phong = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K028.DSPHONG", sql_par.join('$'));
        	if(arrPhong.length == 2){
        		$("#div2").show();
            	$("#div1").hide();
            	$("#div3").hide();
            	$('#phongkham1').text(data_ar_phong[0].ORG_NAME);
            	$('#phongkham2').text(data_ar_phong[1].ORG_NAME);
        	}
        	if(arrPhong.length == 3){
        		$("#div3").show();
            	$("#div1").hide();
            	$("#div2").hide();
            	$('#phongkham3').text(data_ar_phong[0].ORG_NAME);
                $('#phongkham4').text(data_ar_phong[1].ORG_NAME);
                $('#phongkham5').text(data_ar_phong[2].ORG_NAME);
        	}
        }
        $('#lbSTT').text(array[0]);
        $('#lbHoTen').text(array[1]);
        $('#lbUT').text(array[2]);
        $('#lbLG').text(array[3]);
        for(i =1;i<=5;i++){
        	$('#lbSTT'+i).text(array[0]);
            $('#lbHoTen'+i).text(array[1]);
            $('#lbUT'+i).text(array[2]);
            $('#lbLG'+i).text(array[3]);
        }
        _loadData();
       
//        window.onload = function onload(){
//        	 goFullscreen();
//		};
        var interval = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_LCD_KB_INTERVAL')
        setInterval(function () {
            _loadData();
            
        }, interval);
        setInterval(function () {
			$('[id^=lbDangKham]').fadeOut(200)
			$('[id^=lbDangKham]').fadeIn(200)
		},100)
    }

    function _loadData() {
        var th = 0;
        var html = '';
        if(!phongs){
        	var sql_par = [_opt.phongid];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {
                	 html += '<tr style="height:95px;">';
                     var _danhdau = '';
                     if(data_ar[i].TRANGTHAI_CLS == '1')
                     	_danhdau = 'setFontUT" ';
                     if(data_ar[i].TRANGTHAI_CLS == '2')
                     	_danhdau = 'setFont" ';
                     if(data_ar[i].TRANGTHAI_CLS == '3')
                     	_danhdau = 'setFontDK" ';
                     if(data_ar[i].TRANGTHAI_CLS == '4')
                     	_danhdau = 'setFontChoCLS" ';
                     if(data_ar[i].TRANGTHAI_CLS == '5')
                     	_danhdau = 'setFontCoCLS" ';
                     
                     html += '<td style="width:13%;font-size: 35px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></td>';
                     html += '<td style="width:45%;font-size: 35px;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">")+ data_ar[i].TENBENHNHAN + '</span></td>';  
                     html += '<td style="width:10%;font-size: 35px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham3"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></td>';
                     html += '<td style="width:13%;font-size: 35px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham4"+i+"\">"): ">") + data_ar[i].TENTRANGTHAI_CLS + '</span></td>';
                     html += '</tr>';
                }
            }
            $('#list').html(html);
        }
        else if(arrPhong.length == 2){
        	var sql_par = [data_ar_phong[0].ORG_ID];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {
                	for (var i = 0; i < data_ar.length; i++) {
                        html += '<tr style="height:95px;">';
                        var _danhdau = '';
                        if(data_ar[i].TRANGTHAI_CLS == '1')
                        	_danhdau = 'setFontUT" ';
                        if(data_ar[i].TRANGTHAI_CLS == '2')
                        	_danhdau = 'setFont" ';
                        if(data_ar[i].TRANGTHAI_CLS == '3')
                        	_danhdau = 'setFontDK" ';
                        if(data_ar[i].TRANGTHAI_CLS == '4')
                        	_danhdau = 'setFontChoCLS" ';
                        if(data_ar[i].TRANGTHAI_CLS == '5')
                        	_danhdau = 'setFontCoCLS" ';
                        
                        html += '<td style="width:13%;font-size: 23px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></td>';
                        html += '<td style="width:45%;font-size: 23px;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">")+ data_ar[i].TENBENHNHAN + '</span></td>';  
                        html += '<td style="width:10%;font-size: 23px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham3"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></td>';
                        html += '<td style="width:13%;font-size: 23px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham4"+i+"\">"): ">") + data_ar[i].TENTRANGTHAI_CLS + '</span></td>';
                        html += '</tr>';
                    }
                }
            }
        	$('#list1').html(html);
            html = "";
            var sql_par1 = [data_ar_phong[1].ORG_ID];
            data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par1.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {
                    html += '<tr style="height:95px;">';
                    var _danhdau = '';
                    if(data_ar[i].TRANGTHAI_CLS == '1')
                    	_danhdau = 'setFontUT" ';
                    if(data_ar[i].TRANGTHAI_CLS == '2')
                    	_danhdau = 'setFont" ';
                    if(data_ar[i].TRANGTHAI_CLS == '3')
                    	_danhdau = 'setFontDK" ';
                    if(data_ar[i].TRANGTHAI_CLS == '4')
                    	_danhdau = 'setFontChoCLS" ';
                    if(data_ar[i].TRANGTHAI_CLS == '5')
                    	_danhdau = 'setFontCoCLS" ';
                    
                    html += '<td style="width:13%;font-size: 23px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham21"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></td>';
                    html += '<td style="width:45%;font-size: 23px;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham22"+i+"\">"): ">")+ data_ar[i].TENBENHNHAN + '</span></td>';  
                    html += '<td style="width:10%;font-size: 23px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham23"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></td>';
                    html += '<td style="width:13%;font-size: 23px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham24"+i+"\">"): ">") + data_ar[i].TENTRANGTHAI_CLS + '</span></td>';
                    html += '</tr>';
                   
                    html += '</tr>';
                }
            }
            $('#list2').html(html);
        }
        else if(arrPhong.length == 3){
        	var sql_par = [data_ar_phong[0].ORG_ID];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {

                        html += '<tr style="height:80px;">';
                        var _danhdau = '';
                        if(data_ar[i].TRANGTHAI_CLS == '1')
                        	_danhdau = 'setFontUT" ';
                        if(data_ar[i].TRANGTHAI_CLS == '2')
                        	_danhdau = 'setFont" ';
                        if(data_ar[i].TRANGTHAI_CLS == '3')
                        	_danhdau = 'setFontDK" ';
                        if(data_ar[i].TRANGTHAI_CLS == '4')
                        	_danhdau = 'setFontChoCLS" ';
                        if(data_ar[i].TRANGTHAI_CLS == '5')
                        	_danhdau = 'setFontCoCLS" ';
                        html += '<td style="width:13%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></td>';
                        html += '<td style="width:45%;font-size: 16px;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">")+ data_ar[i].TENBENHNHAN + '</span></td>';  
                        html += '<td style="width:10%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham3"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></td>';
                        html += '<td style="width:13%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham4"+i+"\">"): ">") + data_ar[i].TENTRANGTHAI_CLS + '</span></td>';
                        html += '</tr>';
                }
            }
        	$('#list3').html(html);
            html = "";
            var sql_par1 = [data_ar_phong[1].ORG_ID];
            data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par1.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {
                    html += '<tr style="height:80px;">';
                    var _danhdau = '';
                    if(data_ar[i].TRANGTHAI_CLS == '1')
                    	_danhdau = 'setFontUT" ';
                    if(data_ar[i].TRANGTHAI_CLS == '2')
                    	_danhdau = 'setFont" ';
                    if(data_ar[i].TRANGTHAI_CLS == '3')
                    	_danhdau = 'setFontDK" ';
                    if(data_ar[i].TRANGTHAI_CLS == '4')
                    	_danhdau = 'setFontChoCLS" ';
                    if(data_ar[i].TRANGTHAI_CLS == '5')
                    	_danhdau = 'setFontCoCLS" ';
                    
                    html += '<td style="width:13%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham21"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></td>';
                    html += '<td style="width:45%;font-size: 16px;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham22"+i+"\">"): ">")+ data_ar[i].TENBENHNHAN + '</span></td>';  
                    html += '<td style="width:10%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham23"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></td>';
                    html += '<td style="width:13%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham24"+i+"\">"): ">") + data_ar[i].TENTRANGTHAI_CLS + '</span></td>';
                    html += '</tr>';
                   
                    html += '</tr>';
                }
            }
            $('#list4').html(html);
            
            html = "";
            var sql_par1 = [data_ar_phong[2].ORG_ID];
            data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par1.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {
                    html += '<tr style="height:80px;">';
                    var _danhdau = '';
                    if(data_ar[i].TRANGTHAI_CLS == '1')
                    	_danhdau = 'setFontUT" ';
                    if(data_ar[i].TRANGTHAI_CLS == '2')
                    	_danhdau = 'setFont" ';
                    if(data_ar[i].TRANGTHAI_CLS == '3')
                    	_danhdau = 'setFontDK" ';
                    if(data_ar[i].TRANGTHAI_CLS == '4')
                    	_danhdau = 'setFontChoCLS" ';
                    if(data_ar[i].TRANGTHAI_CLS == '5')
                    	_danhdau = 'setFontCoCLS" ';
                    
                    html += '<td style="width:13%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham31"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></td>';
                    html += '<td style="width:45%;font-size: 16px;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham32"+i+"\">"): ">")+ data_ar[i].TENBENHNHAN + '</span></td>';  
                    html += '<td style="width:10%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham33"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></td>';
                    html += '<td style="width:13%;font-size: 16px;text-align: center;background-color:#005AAB "><span class="'+ _danhdau + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham34"+i+"\">"): ">") + data_ar[i].TENTRANGTHAI_CLS + '</span></td>';
                    html += '</tr>';
                   
                    html += '</tr>';
                }
            }
            $('#list5').html(html);
        }
        
//        var constant = parseFloat(getSpeedFromCauHinh()); // cang lon thi cang nhanh chong mat
//        if(constant != 0){
//        	var x = $('#list').prop('scrollHeight') -  $('#list').height();
//            var speed = Math.round(x/constant*130);
//            if(x>0){
//                scroll(speed,x , 'list');
//            }
//            
//            var x = $('#list1').prop('scrollHeight') -  $('#list1').height();
//            var speed = Math.round(x/constant*130);
//            if(x>0){
//                scroll(speed,x , 'list1');
//            }
//            var x = $('#list2').prop('scrollHeight') -  $('#list2').height();
//            var speed = Math.round(x/constant*130);
//            if(x>0){
//                scroll(speed,x , 'list2');
//            }
//            var x = $('#list3').prop('scrollHeight') -  $('#list3').height();
//            var speed = Math.round(x/constant*130);
//            if(x>0){
//                scroll(speed,x , 'list3');
//            }
//            var x = $('#list4').prop('scrollHeight') -  $('#list4').height();
//            var speed = Math.round(x/constant*130);
//            if(x>0){
//                scroll(speed,x , 'list4');
//            }
//            var x = $('#list5').prop('scrollHeight') -  $('#list5').height();
//            var speed = Math.round(x/constant*130);
//            if(x>0){
//                scroll(speed,x , 'list5');
//            }
//        }
    }

    function goFullscreen() {
        var isFirefox = typeof InstallTrigger !== 'undefined';
        mf = document.getElementById("main_frame");
        if (!isFirefox) {
            mf.webkitRequestFullscreen();
        } else {
            mf.mozRequestFullScreen();
        }
    }

    function fullscreenChanged() {
        if (document.webkitFullscreenElement == null) {
            mf = document.getElementById("main_frame");
            mf.style.display = "none";
        }
    }
    function scroll(speed, x, name) {
        $('#' + name).animate(
            {scrollTop: x}
            , speed
            , function() {
                $(this).animate({ scrollTop: 0 }, speed, function(){
                    window.location.reload(true);
                });

            }
        );
    }

    function getSpeedFromCauHinh(){
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'NTU02D021_LCD_SPEED_SCROLL');
        if (data_ar != null && data_ar.length > 0) {
            return data_ar[0].NTU02D021_LCD_SPEED_SCROLL;
        } else {
            return 1; // default
        }
    }
}

