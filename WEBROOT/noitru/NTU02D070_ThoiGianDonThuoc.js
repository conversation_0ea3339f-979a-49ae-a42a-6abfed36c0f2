/* 
Mã màn hình  : NTU01H007
File mã nguồn : NTU01H007_ChuyenMoCapCuu.js
<PERSON><PERSON><PERSON> đích  : <PERSON><PERSON> lý nghiệp vụ chuyển mổ cấp cứu, chuy<PERSON><PERSON> mổ phiên, chuyể<PERSON> điều trị kết hợp 
Tham số vào : Input 1 : benhnhanid
			  Input 2 : khambenhid
			  Input 3 : hosobenhanid
			  Input 4 : doituongbenhnhanid
			  Input 5 : hinhthucvaovienid
			  Input 6 : loaibenhanid
			  Input 7 : phongid
			  Input 8 : mode - 0:chuyển mổ cấp cứu,1:chuyển mổ phiên,2:đi<PERSON>u trị kết hợp
Ng<PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nhật  Ghi chú
HUNGNT	- 03092016 - Comment
*/
function NTU02D070_ThoiGianDonThuoc(_opt) {
	this.load = doLoad;
	opt = $.extend({}, _opt);
	var dateCount = 0;
	var copy_multi_thuthuat;//L2PT-33207
	var show_guiphieu;//L2PT-253
	var _req_soto_dt = false;
	var HIS_SOKETDTRI_DICHVU = '0'; //BVTM-5642
	var copy_hoanthanh_pttt;//L2PT-7696
	var copy_nhi_hdg = false;//L2PT-11533
	var check_sl_khadung = false;//L2PT-12522
	var check_trung_tg_phieu = false;
	var NTU_PDT_CHECK_TVT = 0; //L2PT-35804
	var cf = new Object();
	var isluutiep = 0;//L2PT-105434
	function doLoad() {
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		$('#txtSONGAY').val('1');
		$('#txtSOLANDV').val('1');
		//L2PT-33207
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_COPY_THUTHUAT_MULTI;NTU_COPY_PDT_GUIPHIEU;NTU_COPY_PDT_THUOC;NTU_COPY_PDT_TACHTHUOC;HIS_SOKETDTRI_DICHVU;"
				+ "NTU_COPY_PDT_REQ;NTU_COPY_PDT_HT_PTTT;NTU_COPY_PDT_NHIHDG;NTU_PDT_CHECK_SLKHADUNG;NTU_PDT_CHECK_TVT;NTU_COPY_PDT_TGM;NTU_COPY_PDT_TRUNGTG;NTU_COPY_PTTT_SHOW_SLDV;"
				+ "NTU_COPY_PDT_AN_TTDT;"//L2PT-50438
				+ "NTU_COPY_PDT_BB_TTDT;"// L2PT-51293
				+ "NTU_COPY_PDT_THUOC_MAX;"// L2PT-61387
				+ "NTU_COPY_PDT_VPC;"// L2PT-105434
				+ "NTU_COPY_TVT_PDT;"// L2PT-114636
				+ "DTDT_DAY_DONTHUOC;"// L2PT-52652
				+ "NTU_PDT_TGCHIDINH;"// L2PT-122006
				+ "COPY_CLS_SHOW_PDT;NTU_PTTT_TRUNGGIO_HUE");
		if (data_ar != null && data_ar.length > 0) {
			cf = data_ar[0];
			// L2PT-41047 duonghn start
			if (cf.NTU_COPY_PDT_TGM != 0 && _opt._type == '4' && _opt.ngaymaubenhpham != null) {
				var ngayphieu_moi = stringToDateTime(_opt.ngaymaubenhpham);
				ngayphieu_moi.setDate(ngayphieu_moi.getDate() + 1);
				var ngayphieumoi_str = ngayphieu_moi.format('dd/MM/yyyy');
				if (cf.NTU_COPY_PDT_TGM == 1) {
					$('#txtTHOIGIAN').val(ngayphieumoi_str + " 07:30:00");
				} else if (cf.NTU_COPY_PDT_TGM == 2) {//HaNv_030723: L2PT-46553
					$('#txtTHOIGIAN').val(ngayphieumoi_str + " 08:00:00");
				}
				$('#txtTHOIGIAN_DEN').val(ngayphieumoi_str + " 23:59:59");
			} else {
				if ($('#company_id').val() != null && $('#company_id').val() == '10284' && _opt.ngaymaubenhpham != null && _opt._type == '5') {
					$('#txtTHOIGIAN').val(_opt.ngaymaubenhpham);
				} else {
					$('#txtTHOIGIAN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
				}
				$('#txtTHOIGIAN_DEN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));//L2PT-3978
			}
			// L2PT-41047 duonghn end
			if (data_ar[0].NTU_PDT_YLENH_THUUOC_DKLSN == '1') {
				$('#divGiuYlenhThuoc').show();
			}
			// L2PT-35446 start
			if (data_ar[0].NTU_COPY_PDT_AN_TTDT == '1') {
				$('#divTT_Dieutri').hide();
			}
			// L2PT-35446 end
			// L2PT-51293 start
			if (data_ar[0].NTU_COPY_PDT_BB_TTDT == '1') {
				setEnabled([], [ 'chkPDT_OLD' ])
			}
			// L2PT-51293 end
			if (data_ar[0].NTU_COPY_PTTT_SHOW_SLDV == 1 && _opt._type == '5') {
				$('#div_SOLAN').show();
			}
			NTU_PDT_CHECK_TVT = data_ar[0].NTU_PDT_CHECK_TVT; //L2PT-35804
			//L2PT-13786
			if (data_ar[0].NTU_COPY_PDT_TRUNGTG == 1) {
				check_trung_tg_phieu = true;
			}
			//L2PT-11533
			if (data_ar[0].NTU_COPY_PDT_NHIHDG == 1) {
				copy_nhi_hdg = true;
			}
			//L2PT-12522
			if (data_ar[0].NTU_PDT_CHECK_SLKHADUNG == 1) {
				check_sl_khadung = true;
			}
			if (data_ar[0].NTU_COPY_THUTHUAT_MULTI == 1) {
				if (_opt._type == '5') {
					var check_par = [];
					check_par.push({
						"name" : "[0]",
						"value" : _opt._maubenhphamId
					});
					var check_thuthuat = jsonrpc.AjaxJson.getOneValue("NT.026.CHECK_TT", check_par);
					if (check_thuthuat == "0") {
						copy_multi_thuthuat = true;
					} else {
						$('#div_SONGAY').hide();
					}
				}
			}
			//L2PT-253
			if (data_ar[0].NTU_COPY_PDT_GUIPHIEU == 1) {
				show_guiphieu = true;
			}
			//L2PT-2419
			if (cf.NTU_COPY_PDT_THUOC != '0') {
				$('#divCopyThuoc').show();
				if (cf.NTU_COPY_PDT_THUOC == 2) {//HaNv_051223: L2PT-59845
					if (data_ar[0].NTU_COPY_PDT_TACHTHUOC == 1) {
						$("#chkCopyThuocTanDuoc").prop('checked', true);
						$("#chkCopyThuocDongy").prop('checked', true);
					} else {
						$("#chkCopyPhieuThuoc").prop('checked', true);
					}
					$("#chkCopyPhieuVattu").prop('checked', true);
				}
			}
			//L2PT-6595
			if (data_ar[0].NTU_COPY_PDT_TACHTHUOC == 1) {
				$('#divThuoc_Tach').show();
				$('#divThuoc').hide();
			}
			//BVTM-5642
			HIS_SOKETDTRI_DICHVU = data_ar[0].HIS_SOKETDTRI_DICHVU;
			//L2PT-7481
			if (data_ar[0].NTU_COPY_PDT_REQ == 1) {
				$('#divSotoDT').addClass('required');
				$('#chkCopyPhieuThuoc').prop('checked', true);
				_req_soto_dt = true;
			}
			//L2PT-7696
			if (data_ar[0].NTU_COPY_PDT_HT_PTTT == 1) {
				copy_hoanthanh_pttt = true;
			}
		}
		if (_opt.todieutri == '1' || data_ar[0].COPY_CLS_SHOW_PDT == '1') {//HaNv_030123: L2PT-31987
			$('#divtodieutri').css('display', '');
		}
		if (_opt._type == '4') {
			$('#div_SONGAY').hide();
		}
		sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : _opt._khambenhid
		});
		ComboUtil.getComboTag("cboPHIEU_CD", "NTU02D010.08", sql_par, "", {
			value : '',
			text : '-- Chọn --'
		}, "sql", '', '');
		//L2PT-3978
		if (_opt._type == '4') {
			$('#divCHECKCLS').show();
			//L2PT-11533
			if (copy_nhi_hdg) {
				$('#divDENNGAY').hide();
				$('#divNOTE').hide();
				$('#divPDT_NhiHDG').show();
			} else {
				$('#lblTUNGAY').show();
				$('#divDENNGAY').show();
				countdate_SLphieu();
				//L2PT-253
				if (show_guiphieu) {
					$('#divCHECK_GUIPHIEU').show();
				}
			}
			//L2PT-105434
			if(cf.NTU_COPY_PDT_VPC == '1'){
				$('#divDENNGAY').hide();
				$('#divDIENBIEN').show();
				$('#divNGAYSD').show();
				$('#divNGAYSD').show();
				$('#btnSaveTiep').show();
				$('#txtNGAY_SD').val($('#txtTHOIGIAN').val());
				var dv_par = [];
				dv_par.push({
					"name" : "[0]",
					"value" : _opt._maubenhphamId
				});
				var dienbien = jsonrpc.AjaxJson.getOneValue("NTU02D070.DBDT", dv_par);
				$('#txtDIENBIEN').val(dienbien);
			}
			
		}
		//L2PT-3978
	}
	function _bindEvent() {
		save();
		closePage();
		//L2PT-3978
		$('#txtTHOIGIAN_DEN').on('change', function(e) {
			if (_opt._type == '4' && $('#txtTHOIGIAN_DEN').val() != '' && $('#txtTHOIGIAN').val() != '') {
				countdate_SLphieu();
			}
		});
		$('#txtTHOIGIAN').on('change', function(e) {
			if (_opt._type == '4' && $('#txtTHOIGIAN_DEN').val() != '' && $('#txtTHOIGIAN').val() != '') {
				countdate_SLphieu();
			}
			//L2PT-105434
			if (_opt._type == '4' && cf.NTU_COPY_PDT_VPC == '1') {
				$('#txtTHOIGIAN_DEN').val($('#txtTHOIGIAN').val());
			}
		});
		//L2PT-3978
		//L2PT-114636
		$('#cboPHIEU_CD').on('change', function(e) {
			if ((_opt._type == '7' || _opt._type == '8') && cf.NTU_COPY_TVT_PDT =='1') {
				$('#txtTHOIGIAN').val($('#cboPHIEU_CD'+ " option:selected").text().split('-')[1].trim());
			}
		});
		
		//L2PT-253
		$("#chkCopyCLS_TATCA").change(function() {
			if (this.checked) {
				$("#chkCopyCLS_XN").prop('checked', true);
				$("#chkCopyCLS_CDHA").prop('checked', true);
				$("#chkCopyCLS_PTTT").prop('checked', true);
				if (cf.NTU_COPY_PDT_THUOC != '0') {
					$("#chkCopyPhieuThuoc").prop('checked', true);
					$("#chkCopyPhieuVattu").prop('checked', true);
				}
			} else {
				$("#chkCopyCLS_XN").prop('checked', false);
				$("#chkCopyCLS_CDHA").prop('checked', false);
				$("#chkCopyCLS_PTTT").prop('checked', false);
				if (cf.NTU_COPY_PDT_THUOC != '0') {
					$("#chkCopyPhieuThuoc").prop('checked', false);
					$("#chkCopyPhieuVattu").prop('checked', false);
				}
			}
		});
	}
	function datediff(first, second) {
		// Take the difference between the dates and divide by milliseconds per day.
		// Round to nearest whole number to deal with DST.
		return Math.round((second - first) / (1000 * 60 * 60 * 24));
	}
	//L2PT-3978
	function checkDate(first, second) {
		var d1 = moment(first, 'DD/MM/YYYY HH:mm:ss');
		var d2 = moment(second, 'DD/MM/YYYY HH:mm:ss');
		if (Math.abs(d2 - d1) >= 0) {
			return true;
		} else {
			return false;
		}
	}
	//L2PT-3978
	function save() {
		$('#btnSave').bindOnce("click", function() {
			var _validator = new DataValidator("divMain");
			var valid = _validator.validateForm();
			if (!valid) {
				return false;
			}
			if (!compareDate(_opt._thoigianvaovien, $('#txtTHOIGIAN').val(), 'DD/MM/YYYY HH:mm:ss')) {
				DlgUtil.showMsg('Thời gian đơn thuốc trước thời gian vào viện');
				return;
			}
			//L2PT-3978
			if (_opt._type == '4' && !checkDate($('#txtTHOIGIAN').val(), $('#txtTHOIGIAN_DEN').val())) {
				return DlgUtil.showMsg('Từ ngày phải nhỏ hơn đến ngày');
			}
			// L2PT-122006 start
			if (cf.NTU_PDT_TGCHIDINH && cf.NTU_PDT_TGCHIDINH != '0') {
				var tgcd = $('#txtTHOIGIAN_DEN').val().split(" ")[0];
				var tght = moment().format('DD/MM/YYYY');
				var diffDay = diffDate(tgcd, tght, 'DD/MM/YYYY', 'days');
				if (diffDay > cf.NTU_PDT_TGCHIDINH) {
					DlgUtil.showMsg("Thời gian tạo phiếu không được vượt quá ngày hiện tại " + cf.NTU_PDT_TGCHIDINH + " ngày");
					return false;
				}
				
			}
			// L2PT-122006 end
			//L2PT-61387
			if(_opt._type == '4' && $('#chkCopyPhieuThuoc').is(':checked') && cf.NTU_COPY_PDT_THUOC_MAX == '1'){
				var sql_par = $('#hidTIEPNHANID').val() + '$' + _opt._maubenhphamId + '$0$' + $('#txtTHOIGIAN').val() + '$' + $('#txtTHOIGIAN_DEN').val();
				var check_msg  = jsonrpc.AjaxJson.ajaxCALL_SP_S('KT.PDT.SOLUONG', sql_par);
				if(check_msg != '1' && check_msg != '-1')
					DlgUtil.showMsg(check_msg);
					
			}
			//L2PT-11533
			if (copy_nhi_hdg) {
				if ($('#txtSOLUONGPHIEU').val().trim() == '' || $('#txtKHOANGTHOIGIAN').val().trim() == '') {
					return DlgUtil.showMsg('Số lượng phiếu và khoảng thời gian không được để trống');
				}
			}
			//L2PT-12522
			if (check_sl_khadung && ($('#chkCopyPhieuThuoc').is(':checked') || $('#chkCopyThuocTanDuoc').is(':checked') || $('#chkCopyThuocDongy').is(':checked'))) {
				var pars = [ _opt._maubenhphamId ];
				var result_tg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D070.CHECK_TVT", pars.join('$'));
				if (result_tg != null && result_tg != '') {
					DlgUtil.showConfirm("Phiếu Thuốc " + result_tg + " có số lượng khả dụng không đủ, bạn có muốn tiếp tục sao chép các phiếu khác không?", function(flag) {
						if (flag) {
							saveData();
						}
					});
				} else {
					saveData();
				}
			} else {
				saveData();
			}
		}, 1000);
		//L2PT-105434
		$('#btnSaveTiep').bindOnce("click", function() {
			isluutiep = 1;
			$("#btnSave").trigger("click");
		}, 1000);
	}
	function saveData() {
		//L2PT-3978
		var _return = '';
		if (_opt._type == '7' || _opt._type == '8') {
			//L2PT-296
//			var _par = [_opt._maubenhphamId,$('#txtTHOIGIAN').val(), $('#cboPHIEU_CD').val()];
//			_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.COPY.TH.01",_par.join('$'));
			var sql_par_count = [];
			var sophieu = '';
			sql_par_count.push({
				"name" : "[0]",
				value : _opt._type
			}, {
				"name" : "[1]",
				value : _opt._khambenhid
			}, {
				"name" : "[2]",
				value : $('#txtTHOIGIAN').val().substring(0, 10) + ' 00:00:00'
			}, {
				"name" : "[3]",
				value : $('#txtTHOIGIAN').val().substring(0, 10) + ' 23:59:59'
			});
			var data_ar_count = jsonrpc.AjaxJson.ajaxExecuteQueryO('NTU02D070.CHECKTG', sql_par_count);
			var count_tg = JSON.parse(data_ar_count);
			if (count_tg != null && count_tg != 'null' && count_tg.length > 0) {
				for (i = 0; i < count_tg.length; i++) {
					sophieu = sophieu + count_tg[i].SOPHIEU + ', ';
				}
				DlgUtil.showConfirm("Có phiếu " + sophieu + " đã kê trùng ngày , bạn có muốn tiếp tục?", function(flag) {
					if (flag) {
						copy_thuoc_cbtonkho();
					} else
						return;
				});
			} else {
				copy_thuoc_cbtonkho();
			}
			return;
		} else if (_opt._type == '1' || _opt._type == '2' || _opt._type == '5') {
			//L2PT-33207
			/*if(copy_multi && _opt._type == '5'
				){*/
			var objData = new Object();
			FormUtil.setFormToObject("", "", objData);
			objData["MAUBENHPHAMID"] = _opt._maubenhphamId;
			_return = jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.026.COPY_MUL", JSON.stringify(objData));
			/*}else{
				var _par = [_opt._maubenhphamId,$('#txtTHOIGIAN').val()];
				_return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.026.COPY",_par.join('$'));
			}*/
		} else if (_opt._type == '4') {
			
			//L2PT-253
			var checkCopyCLS = '';
			if (show_guiphieu && $('#chkGUI_PHIEU').is(':checked')) {
				checkCopyCLS = checkCopyCLS + 'GP';
			}
			if ($('#chkCopyCLS_XN').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '1';
			}
			if ($('#chkCopyCLS_CDHA').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '2';
			}
			if ($('#chkCopyCLS_PTTT').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '5';
			}
			if ($('#chkCopyPhieuThuoc').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '7';
			}
			if ($('#chkCopyPhieuVattu').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '8';
			}
			//L2PT-6595
			if ($('#chkCopyThuocTanDuoc').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '7_TD';
			}
			if ($('#chkCopyThuocDongy').is(':checked')) {
				checkCopyCLS = checkCopyCLS + '7_DY';
			}
			//L2PT-7481 batbuoc nhap so to dt
			//L2PT-40051
			if ($('#chkPDT_OLD').is(':checked')) {
				checkCopyCLS = checkCopyCLS + 'PDT_OLD';
			}
			if (_req_soto_dt == true && $('#txtSOTO_DT').val().trim() == '') {
				return DlgUtil.showMsg("Chưa nhập số tờ điều trị");
			}
			//L2PT-105434
			if(cf.NTU_COPY_PDT_VPC == '1'){
				if($('#txtDIENBIEN').val().trim() == ''){
					$("#txtDIENBIEN").focus();
					return DlgUtil.showMsg("Chưa nhập diễn biến");
				}
				if (!compareDate($('#txtTHOIGIAN').val().trim(), $('#txtNGAY_SD').val(), 'DD/MM/YYYY HH:mm:ss')) {
					return DlgUtil.showMsg('Ngày dùng phải lớn hơn thời gian chỉ định');
				}
			}
			//L2PT-3978
			var p1 = [];
			var objj = null;
			//L2PT-11533
			if (copy_nhi_hdg) {
				for (i = 0; i < $('#txtSOLUONGPHIEU').val(); i++) {
					objj = new Object();
					var tgTungay = moment($('#txtTHOIGIAN').val(), 'DD/MM/YYYY HH:mm:ss');
					tgTungay.add(i * Number($('#txtKHOANGTHOIGIAN').val()), 'minutes').calendar();
					objj.THOIGIAN = tgTungay.format('DD/MM/YYYY HH:mm:ss').toString();
					objj.GIU_YLENH_THUOC = $('#chkGiuYlenhThuoc').is(":checked") ? '1' : '0';
					p1.push(objj);
				}
			} else {
				for (i = 0; i < dateCount; i++) {
					objj = new Object();
					var tgTungay = moment($('#txtTHOIGIAN').val(), 'DD/MM/YYYY HH:mm:ss');
					tgTungay.add(i, 'days').calendar();
					objj.THOIGIAN = tgTungay.format('DD/MM/YYYY HH:mm:ss').toString();
					objj.GIU_YLENH_THUOC = $('#chkGiuYlenhThuoc').is(":checked") ? '1' : '0';
					p1.push(objj);
				}
			}
			var _par = [ _opt._maubenhphamId, JSON.stringify(p1), $('#txtSOTO_DT').val(), checkCopyCLS ];//START HISL2TK-1101
			_return = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D070.EV001", _par.join('$')); //L2PT-83092
			//L2PT-105434
			if(cf.NTU_COPY_PDT_VPC == '1' && parseInt(_return) > 0){
				var obj = new Object();
				obj.DIENBIEN = $('#txtDIENBIEN').val();
				obj.NGAYSD = $('#txtNGAY_SD').val();
				obj.MAUBENHPHAMID = _return;
				var _rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D070.UPDDB", JSON.stringify(obj));
				var _return = 1;
			}
		}
		if (_return != 0) {
			//L2PT-33207
			if (copy_multi_thuthuat && _opt._type == '5') {
				if (_return == '-1') {
					return DlgUtil.showMsg("Phiếu có dịch phụ không phải loại thủ thuật");
				} else if (_return == '-2') {
					return DlgUtil.showMsg("Chỉ được copy phiếu có trạng thái đã hoàn thành");
				} else if (_return == '-3') {
					return DlgUtil.showMsg("Sao chép số lượng ngày < 100");
				}
			}
			//L2PT-7696
			if (copy_hoanthanh_pttt == true && _opt._type == '4' && $('#chkCopyCLS_PTTT').is(':checked')) {
				if (_return == '-1') {
					return DlgUtil.showMsg("Phiếu PTTT có dịch phụ không phải loại thủ thuật");
				} else if (_return == '-2') {
					return DlgUtil.showMsg("Chỉ được copy phiếu PTTT có trạng thái đã hoàn thành");
				}
			}
			if (_opt._type == '4') {
				if (_return == '-4') {
					return DlgUtil.showMsg("Đã có tờ điều trị trùng giờ ( " + $("#txtTHOIGIAN").val().trim() + " ) đề nghị kiểm tra lại.");
				}
				//L2PT-11310
				if (_return == '-5') {
					//neu co y/c hien thi thuoc thi se viet ham get ra o day
					return DlgUtil.showMsg("Có thuốc số lượng khả dụng không đủ, đề nghị kiểm tra lại.");
				}
				
				//L2PT-83092
				if (!parseInt(_return)) {
					return DlgUtil.showMsg(_return);
				}
				
			}
			var s1 = _opt._thoigianvaovien;
			var s2 = $('#txtTHOIGIAN').val();
			var thoiGianVaoVien = moment(s1, 'DD/MM/YYYY'); // yyyy mm dd
			var thoiGianChiDinh = moment(s2, 'DD/MM/YYYY'); // yyyy mm dd
			//bo sung TH check neu da co phieu so ket 10/07/2019
			var sql_par = [];
			var ngayKetthucSKDT;
			sql_par.push({
				"name" : "[0]",
				"value" : _opt._khambenhid
			});
			var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K015.SKPDT", sql_par);
			//BVTM-5642 Tính số ngày sơ kết điều trị theo số lần SD dịch vụ
			var tong_dichvu = 0;
			if (HIS_SOKETDTRI_DICHVU != '0') {
				var dv_par = [];
				dv_par.push({
					"name" : "[0]",
					"value" : _opt._khambenhid
				});
				dv_par.push({
					"name" : "[1]",
					"value" : HIS_SOKETDTRI_DICHVU
				});
				var _tong_dichvu = jsonrpc.AjaxJson.getOneValue("NTU02D070.COUNT.DV", dv_par);
				tong_dichvu = Number(_tong_dichvu);
			}
			if (tong_dichvu > 0) {
				var paramSoKetDieuTri = null;
				var msg = "Tạo bản sao thành công";
				var tongSKDT = jsonrpc.AjaxJson.getOneValue("NGT02K015.SKPDT.01", sql_par);
				tongSKDT = Number(tongSKDT);
				var openSKDT = tong_dichvu / 15 - tongSKDT;
				if (openSKDT >= 1) {
					paramSoKetDieuTri = {
						khambenhid : _opt._khambenhid,
						bacsydieutriid : _opt._bacsidieutri,
						benhnhanid : _opt._benhnhanid,
						thoigianvaovien : _opt._thoigianvaovien,
						startDate : thoiGianVaoVien.format('DD/MM/YYYY 00:00:00'),
						endDate : thoiGianChiDinh.format('DD/MM/YYYY 00:00:00')
					};
					msg = "Tạo bản sao thành công. Đồng thời số lần sử dụng dịch vụ là bội của 15 nên có thể nhập thông tin sơ kết điều trị";
				}
			} else if (data_ar != null && data_ar != "[]") {
				var msg = "Tạo bản sao thành công";
				data_ar = JSON.parse(data_ar);
				ngayKetthucSKDT = moment(data_ar[0].DEN_NGAY, "DD.MM.YYYY");
				ngayKetthucSKDT.add(1, 'days');//+1 la ngay bat dau dot dieu tri tiep theo
				if ((thoiGianChiDinh - ngayKetthucSKDT) / 86400000 >= 14) {
					paramSoKetDieuTri = {
						khambenhid : _opt._khambenhid,
						bacsydieutriid : _opt._bacsidieutri,
						benhnhanid : _opt._benhnhanid,
						thoigianvaovien : _opt._thoigianvaovien,
						startDate : ngayKetthucSKDT.format('DD/MM/YYYY 00:00:00'),
						endDate : ngayKetthucSKDT.add(14, 'days').format('DD/MM/YYYY 00:00:00')
					};
					/*dlgPopup=DlgUtil.buildPopupUrl("divDlgSoKetDieuTri","divDlgCT","manager.jsp?func=../noitru/NTU02D055_SoKetDieuTri",paramSoKetDieuTri,"Sơ kết 15 ngày điều trị",1200,580);
					DlgUtil.open("divDlgSoKetDieuTri");//
					return false;*/
					msg = "Tạo bản sao thành công. Đồng thời số ngày là bội của 15 nên có thể nhập thông tin sơ kết điều trị";
				}
			} else {
				var d = datediff(thoiGianVaoVien, thoiGianChiDinh);
				// neu la boi so cua 15 thi goi form so ket dieu tri
				var paramSoKetDieuTri = null;
				var msg = "Tạo bản sao thành công";
				if (d !== 0 && d % 14 == 0) {
					paramSoKetDieuTri = {
						khambenhid : _opt._khambenhid,
						bacsydieutriid : _opt._bacsidieutri,
						benhnhanid : _opt._benhnhanid,
						thoigianvaovien : _opt._thoigianvaovien,
						startDate : thoiGianVaoVien.format('DD/MM/YYYY 00:00:00'),
						endDate : thoiGianChiDinh.format('DD/MM/YYYY 00:00:00')
					};
					msg = "Tạo bản sao thành công. Đồng thời số ngày là bội của 15 nên có thể nhập thông tin sơ kết điều trị";
				}
			}
			//L2PT-16695
			var lst_sophieu = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D070.CHECKGP", _return);
			if (lst_sophieu != null && lst_sophieu != '') {
				msg = 'Tạo bản sao thành công. Có phiếu chưa gửi, anh/chị thực hiện gửi các phiếu sau <br>' + lst_sophieu;
			}
			//L2PT-39499 //L2PT-79473 
			//if (copy_multi_thuthuat && _opt._type == '5' && cf.NTU_PTTT_TRUNGGIO_HUE == '1') {
			if ( _opt._type == '5' && cf.NTU_PTTT_TRUNGGIO_HUE == '1') {
				var lst_sophieu = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D070.NGAYTRUNG", _return);
				if (lst_sophieu != null && lst_sophieu != '') {
					msg = 'Tạo bản sao thành công. Phiếu PTTT của ngày  ' + lst_sophieu + ' có thời gian thực hiện đã bị trùng';
				}
			}
			//L2PT-35804
			if (($('#chkCopyPhieuThuoc').is(':checked') || $('#chkCopyPhieuVattu').is(':checked')) && NTU_PDT_CHECK_TVT == '1') {
				var objDataCk = new Object();
				objDataCk["PHIEUDIEUTRIID"] = _opt._maubenhphamId;
				objDataCk["PHIEUDIEUTRIID_NEW"] = _return;
				objDataCk["THUOC"] = $('#chkCopyPhieuThuoc').is(':checked') == true ? '1' : '0';
				objDataCk["VATTU"] = $('#chkCopyPhieuVattu').is(':checked') == true ? '1' : '0';
				var _check = jsonrpc.AjaxJson.ajaxCALL_SP_S("KT.CHECKCPY.KHOA", JSON.stringify(objDataCk));
				if (_check && _check != '0' && check_sl_khadung == '0')
					msg = msg + '. Thuốc ' + _check + ' đã được khóa hoạc hết trong kho. Vui lòng liên hệ khoa Dược.';
			}
			
			//L2PT-105434
			if(_opt._type == '4' && cf.NTU_COPY_PDT_VPC == '1' && isluutiep == 1){
				DlgUtil.showMsg(msg);
			}else{
				var objReturn = new Object();
				objReturn.msg = msg;
				var evFunc = EventUtil.getEvent("assignSevice_SaveCopyMbp");
				objReturn.type = _opt._type;
				objReturn.phieudieutriid_new = _return;//HaNv_111023: L2PT-51576
				objReturn.paramSoKetDieuTri = paramSoKetDieuTri;
				EventUtil.raiseEvent("assignSevice_SaveCopyMbp", objReturn);
			}
			isluutiep = 0;
			
//  			 if(typeof evFunc==='function') {				
//				evFunc(objReturn);
//  			 }
//  			 else {
//				console.log('evFunc not a function');
//  			 }
//  			DlgUtil.showMsg("Tạo bản sao phiếu thành công");
		} else if (_return == 0) {
			DlgUtil.showMsg("Tạo bản sao phiếu không thành công");
		}
	}
	// ham xu ly close trang
	function closePage() {
		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close('divDlgCopyMbp');
		})
	}
	function countdate_SLphieu() {
		var textcountdate = '';
		var d1 = moment($('#txtTHOIGIAN').val().substring(0, 10), 'DD/MM/YYYY');
		var d2 = moment($('#txtTHOIGIAN_DEN').val().substring(0, 10), 'DD/MM/YYYY');
		dateCount = datediff(d1, d2) + 1;
		//dateCount = Math.abs(d1-d2)/86400000 + 1; 
		//L2PT-13786
		if (check_trung_tg_phieu) {
			var sql_par_count = [];
			var sophieu = '';
			sql_par_count.push({
				"name" : "[0]",
				value : _opt._type
			}, {
				"name" : "[1]",
				value : _opt._khambenhid
			}, {
				"name" : "[2]",
				value : $('#txtTHOIGIAN').val().substring(0, 10) + ' 00:00:00'
			}, {
				"name" : "[3]",
				value : $('#txtTHOIGIAN_DEN').val().substring(0, 10) + ' 23:59:59'
			});
			var data_ar_count = jsonrpc.AjaxJson.ajaxExecuteQueryO('NTU02D070.CHECKTG', sql_par_count);
			var count_tg = JSON.parse(data_ar_count);
			if (count_tg != null && count_tg != 'null' && count_tg.length > 0) {
				for (i = 0; i < count_tg.length; i++) {
					sophieu = sophieu + count_tg[i].SOPHIEU + ', ';
				}
				textcountdate = ' <br>-CÓ PHIẾU ĐIỀU TRỊ ' + sophieu + ' TRÙNG NGÀY TẠO';
			}
		}
		$('#lblNOTE').html(
				'-Sao chép ' + dateCount + ' phiếu điều trị cho ' + dateCount + ' ngày, từ ' + $('#txtTHOIGIAN').val().substring(0, 10) + ' đến ' + $('#txtTHOIGIAN_DEN').val().substring(0, 10) +
						textcountdate);
	}
	function copy_thuoc_cbtonkho() {
		var COPY_THUOC_CB_TONKHO = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'COPY_THUOC_CB_TONKHO');
		if (COPY_THUOC_CB_TONKHO == '1') {
			var pars = [ _opt._maubenhphamId ];
			var result_tg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D070.TON.TVT", pars.join('$'));
			if (result_tg != null && result_tg != '') {
				DlgUtil.showConfirm("Thuốc " + result_tg + " có số lượng khả dụng không đủ, bạn có muốn tiếp tục sao chép không?", function(flag) {
					if (flag) {
						copy_thuocvt(COPY_THUOC_CB_TONKHO);
					}
				});
			} else {
				copy_thuocvt(COPY_THUOC_CB_TONKHO);
			}
		} else {
			copy_thuocvt(COPY_THUOC_CB_TONKHO);
		}
	}
	function copy_thuocvt(check_sl_khadung) {
		var objData = new Object();
		FormUtil.setFormToObject("", "", objData);
		objData["MAUBENHPHAMID"] = _opt._maubenhphamId;
		objData["PHONGID"] = _opt.phongid;//L2PT-9384
		_returns = jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.COPY.TH.MUL", JSON.stringify(objData));
		_return = _returns.split(",");
		//L2PT-10775
		if (parseInt(_return[0]) > 0) {
			var objDataCk = new Object();
			FormUtil.setFormToObject("", "", objDataCk);
			objDataCk["MAUBENHPHAMID"] = _opt._maubenhphamId;
			objDataCk["MAUBENHPHAMID_NEW"] = _return[0];
			objDataCk["PHONGID"] = _opt.phongid;
			//L2PT-52652
			if (cf.DTDT_DAY_DONTHUOC == '1') {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU007.LAYDT", _return[0]);
				if (data_ar != null && data_ar.length > 0)
					_dayDonThuocOnline_new('1', _return[0], data_ar[0].OPTIONS, data_ar[0].LOAITIEPNHANID, "", "Add", '', '', '');
			}
			var _check = jsonrpc.AjaxJson.ajaxCALL_SP_S("KT.CHECKCPY.KHOA", JSON.stringify(objDataCk));
			var objReturn = new Object();
			objReturn.msg = 'Tạo bản sao thành công';
			if (_check && _check != '0' && check_sl_khadung == '0')
				objReturn.msg = 'Tạo bản sao thành công. Thuốc ' + _check + ' đã được khóa hoạc hết trong kho. Vui lòng liên hệ khoa Dược.';
			var evFunc = EventUtil.getEvent("assignSevice_SaveCopyMbp");
			objReturn.type = _opt._type;
			EventUtil.raiseEvent("assignSevice_SaveCopyMbp", objReturn);
			return;
		} else if (_return[0] == '-7') {
			return DlgUtil.showMsg("Các thuốc/vật tư trong phiếu đã khóa hoạc hết trong kho. Vui lòng liên hệ khoa Dược.");
		} else {
			return DlgUtil.showMsg("Tạo bản sao phiếu không thành công: " + _returns);
		}
	}
}