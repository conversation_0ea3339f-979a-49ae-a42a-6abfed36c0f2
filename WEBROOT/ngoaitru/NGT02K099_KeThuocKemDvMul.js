function dSCHList(_opts) {
	
	var _gridDSCHId="grdDSBN"; 
	var _gridDSCT="grdDSThuocCT"; 
	var dsThem = [];			// Luu tru cac du lieu tick chon trong grid; 
	var thuocvattuid;
	var mathuoc;
	//khoi tao grid danh sach phieu thuoc      
	var _gridHeader = "NGUOIDUNG_ID,NGUOIDUNG_ID,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;Mã BA,MAHOSOBENHAN,90,0,f,l;Số phiếu,SOPHIEU,80,0,f,l;<PERSON><PERSON><PERSON> b<PERSON><PERSON>h<PERSON>,TENBENHNHAN,180,0,f,l;MA_CHANDOAN,MA_CHANDOAN,0,0,t,l;CHANDOAN,CHANDOAN,0,0,t,l;CHANDOAN_KT,CHANDOAN_KT,0,0,t,l;NGAYMAUBENHPHAM,NGAYMAUBENHPHAM,0,0,t,l;NGAYMAUBENHPHAM_SUDUNG,NGAYMAUBENHPHAM_SUDUNG,0,0,t,l;DICHVUCHA_ID,DICHVUCHA_ID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;SLTHANG,SLTHANG,0,0,t,l;OPTION,OPTION,0,0,t,l;SONGAY_KE,SONGAY_KE,0,0,t,l;I_ACTION,I_ACTION,0,0,t,l;KIEUKEDON,KIEUKEDON,0,0,t,l;SLTHANG,SLTHANG,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;PHONGID,PHONGID,0,0,t,l";	
	var _gridHeaderDetail="THUOCVATTUID,THUOCVATTUID,0,0,t,l;Tên thuốc,TEN_THUOC,300,0,f,l;Hoạt chất,HOATCHAT,130,0,t,l;ĐVT,DONVI_TINH,50,0,t,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,t,r;SL,SO_LUONG,40,0,e,c; SL Tồn,SLKHADUNG,40,0,t,l;Thành tiền,THANH_TIEN,80,0,t,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,80,0,t,l;Loại TT mới,LOAI_DT_MOI,80,0,t,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,250,0,t,c;Cách dùng,CACHDUNG,130,0,f,l;Ghi chú,GHICHUCANHBAO,130,0,e,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;LIEUDUNG,LIEUDUNG,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l; ,ACTION,30,d,f,l"; 
	var _loaitt = "-1";
	flagLoading = false;
	_param=[];
	
	this.load=doLoad; 
	
	function doLoad(_hosp_id) {
		_loaitt = '9';
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		_initControl();
		_bindEvent();	

	}
	function _initControl(){  
		GridUtil.init(_gridDSCHId,"100%","352","Danh sách bệnh nhân",false,_gridHeader, false, { rowNum: 30,rowList: [30,100, 200]});
		GridUtil.init(_gridDSCT,"100%","380","Danh sách chi tiết thuốc đi kèm ",false,_gridHeaderDetail, false, { rowNum: 100,rowList: [100, 200, 300]});
		$("#grdDSThuocCT")[0].toggleToolbar(); 
		
		var _filter = ' loaikho IN (8,9,13) ';
		var _par_kho = "0$"+ _opts.khoaId +"$0$"+_filter;
		ComboUtil.getComboTag("cboKHO", "DS.DVCLS.DSKHO" , _par_kho, [], {value:0, text:'Chọn tủ trực'},"sp",'','');
		
		loadGridData();
		
	};
	
	function _bindEvent() {
		$("#btnLuu").click(function(){
			if($('#cboKHO').val() == '0'){
				$("#cboKHO").trigger("focus");
				DlgUtil.showMsg('Hãy chọn tủ trực');				
				return;
			}
			
			var _jSonMbp = [];
			var rowKeyCheck = jQuery("#"+_gridDSCHId).jqGrid('getRowData');
			for(var i=0; i<rowKeyCheck.length; i++) {
				var rowData = $("#" + _gridDSCHId).jqGrid('getRowData', rowKeyCheck[i]);	
				rowData.KHO_THUOCID = $('#cboKHO').val();
				rowData.KHOAID = _opts.khoaId;
				rowData.PHONGID = _opts.phongId;
				rowData.DICHVUCHA_ID = rowKeyCheck[i].DICHVUCHA_ID;
				rowData.KETUGOI = '1';
				rowData.KHAMBENHID = rowKeyCheck[i].KHAMBENHID;
				rowData.ID_DT_MOI = '9';
				_jSonMbp.push(rowData); 
			}
			
			var _jSonThuoc = jQuery("#"+_gridDSCT).jqGrid('getRowData');
			if(_jSonThuoc == null || _jSonThuoc.length <= 0){
				DlgUtil.showMsg("Không có thông tin thuốc trong danh sách.");
				return;
			}else{
				for(var j = 0; j < _jSonThuoc.length; j++){
					_jSonThuoc[j].KHO_THUOCID = $('#cboKHO').val();
					_jSonThuoc[j].LOAI_DT_MOI = _loaitt;
					_jSonThuoc[j].ID_DT_MOI = _loaitt;
				}
			}
			if($('#'+_gridDSCT).find("input[id*='GHICHUCANHBAO']").length > 0){
				DlgUtil.showMsg('Dữ liệu đang sửa không thể thao tác!');
				return false;
			}
			
//			for(var k = 0; k < _jSonMbp.length; k++){
//				_jSonMbp[k].KHO_THUOCID = $('#cboKHO').val();
//				_jSonMbp[k].KHOAID = _opts.dept_id;
//				_jSonMbp[k].PHONGID = _opts.subdept_id;
//				_jSonMbp[k].KETUGOI = '1';
//				
//				if(_loaitt == '9'){
//					_jSonMbp[k].ID_DT_MOI = '9';
//				}
//			}
//			
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("KETHUOC_DVKT", JSON.stringify(_jSonMbp)+"$"+JSON.stringify(_jSonThuoc));
			var ret = result.split(',');
			ret = ret[0];
			if(ret[0] >= 1){
				DlgUtil.showMsg("Cập nhật thành công");
				loadGridData();
				EventUtil.raiseEvent("assignDrug_cancel",{option:_opts.option,type:'1', badaingay:""});
			}
			else if (ret == '-1'){
				DlgUtil.showMsg("Lưu không thành công!");
			}
			else if (ret == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện đã cấp thuốc thành công nhưng chưa kết thúc khám');
			}
			else if (ret == '-2'){
				DlgUtil.showMsg("Có lỗi khi chuyển phiếu!");
			}
			else if (ret == '-4'){
				DlgUtil.showMsg("Lỗi khi hủy"+_lbl_text+"!");
			}else if (ret == '-5'){
				DlgUtil.showMsg("Lỗi khi tính giá dịch vụ cao vượt mức trần Bảo hiểm!");
			}else if (ret == '-6'){
				DlgUtil.showMsg("Lỗi khi yêu cầu trả"+_lbl_text+"!");
			}else if (ret == '-7'){
				DlgUtil.showMsg("Lỗi do nhập sai mã ICD10!");
				$("#txtMACHANDOANICD").trigger("focus");
			}else if (ret == 'cophieudangsua'){
				DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
			}else if (ret == 'daduyetvp'){
				DlgUtil.showMsg('Bệnh nhân đã duyệt thanh toán, không kê đơn được.');
			} 
			else if (ret == 'loikhoaphong'){
				DlgUtil.showMsg('Thiết lập lại khoa phòng khi kê thuốc!');
			} 
			else if(ret == 'saibenhphu'){
				DlgUtil.showMsg('Định dạng mã bệnh kèm theo không đúng.');
			}
			else{
				DlgUtil.showMsg(ret);
			}
		});
		$("#btnCLOSE").on("click", function(e) {
			EventUtil.raiseEvent("assignDrug_cancel",{option:_opts.option,type:'0', badaingay:""});
		});
		$("#btnChonMau").on("click", function(e) {
			
			if(($('#cboKHO').val() == '0' || $('#cboKHO').val() == null)){
				DlgUtil.showMsg('Chọn kho thuốc trước khi chọn đơn mẫu');
				return;
			}
			EventUtil.setEvent("temp_presc_success",function(e){
				_maubenhpham_temp_id = e.id;
				loadGridDonThuoc(_maubenhpham_temp_id);
				DlgUtil.close("dlgCDDV");
			});
			var myVar={
				loainhom : '-1',
				//tuyennx_add_start_20180924  L2HOTRO-10386
				loadkhotheo: '2',
				option: _opts.option,
				//tuyennx_add_end_20180924  L2HOTRO-10386
				dichvuchaid: _opts.list_dvkbid.split(',')[0],
				khothuocid : $("#cboKHO").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K018_DonThuocMau",myVar,"Đơn thuốc/vật tư mẫu",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		
		$("#btnHuy").click(function(){
			parent.DlgUtil.close("dlgTHUOCPHU");
		});
	
		$("#grdDSThuocCT").bind("CustomAction", function(e,act,rid){ 
			if(act == 'del'){
				$("#"+_gridDSCT).jqGrid('delRowData',rid);
			}
			return false;
		});
	}
	
	function loadGridData() {
		var sql_par=[];		
		sql_par.push({"name":"[0]","value":_opts.list_dvkbid});
		GridUtil.loadGridBySqlPage(_gridDSCHId,"DS.DVCLS.KETHUOC",sql_par);
	}
	
	function loadGridDonThuoc(_maubenhpham_temp_id) {
		var sql_par1=[];
		sql_par1.push({"name" : "[0]","value" : _maubenhpham_temp_id});
		GridUtil.loadGridBySqlPage(_gridDSCT,"DS.DVCLS.CTTHUOC",sql_par1);
	}
}
