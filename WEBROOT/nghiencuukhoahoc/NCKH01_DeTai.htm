<!--  
<PERSON><PERSON> màn hình  : NTU02D040
File mã nguồn : NTU02D040_DanhSachPhieuPhuThu.html
Mục đích  : <PERSON><PERSON><PERSON> cuu khoa hoc
Tham số vào :
<PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON>t  <PERSON> chú
TUYENNX	- 08022017 - Comment
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
       value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
        src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
      rel="stylesheet"/>
<script type="text/javascript"
        src="../common/script/xlsx/xlsx.full.min.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>

<script type="text/javascript" src="../common/script/UIUtil.js"></script>

<link href="../common/script/tree/skin-win8/ui.fancytree.css"
      rel="stylesheet"/>
<script src="../common/script/tree/jquery.fancytrees.js"></script>
<script src="../common/script/tree/jquery.fancytree.filter.js"></script>
<script type="text/javascript" src="../nghiencuukhoahoc/NCKH01_DeTai.js?v=0095"
        charset="utf-8"></script>

<div id="divMain" class="container">
    <div class="form-inpfo mgt3" style="text-align: right">Nghiên cứu
        khoa học - Đề tài
    </div>
    <div id="divMain1" class="col-xs-12 low-padding mgt5">
        <div class="col-xs-4 low-padding">
            <div id="inputForm" class="panel panel-default">
                <input type="hidden" name="hidMEDIA_ID" id="hidMEDIA_ID"
                       value="" />
                <div class="panel-heading" style="height: 30px">ĐỀ TÀI</div>
                <div class="panel-body">
                    <div class="col-xs-12 " style="display: none">
                        <div class="col-xs-3 low-padding"></div>
                        <div class="col-xs-9 low-padding">
                            <input type="text" class="form-control input-sm kb-i-col-m"
                                   style="width: 100%;" id="txtDETAIID" name="txtDETAIID" title=""
                                   style="width:100%;">
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding mgt5">
                        <div class="col-xs-4 required">
                            <label>Chuyên mục</label>
                        </div>
                        <div class="col-xs-8">
                            <select class="form-control input-sm" id="cboCHUYENMUCID"
                                    msgErr=" Hãy chọn chuyên mục," reffld="TENCHUYENMUC"
                                    valrule="Chuyên mục,required" style="width: 100%; height: 24px"
                                    disabled>
                            </select>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4 required">
                            <label>Số thứ tự</label>
                        </div>
                        <div class="col-xs-8">
                            <input class="form-control input-sm kb-i-col-m"
                                   style="width: 100%;" id="txtSOTT"
                                   valrule="Số thứ tự,required|is_natural|max_length[3]"
                                   name="txtSOTT" title="" disabled>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4  required">
                            <label class="mgt10">Tên đề tài</label>
                        </div>
                        <div class="col-xs-8">
							<textarea class="form-control input-sm kb-i-col-m"
                                      style="width: 100%;" id="txtTENDETAI"
                                      valrule="Tên đề tài,required|max_length[500]" rows="2"
                                      name="txtTENDETAI" title="" disabled></textarea>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding mgt2">
                        <div class="col-xs-4 required">
                            <label>Tên tác giả</label>
                        </div>
                        <div class="col-xs-8">
                            <input class="form-control input-sm kb-i-col-m"
                                   style="width: 100%;" id="txtTENTACGIA"
                                   valrule="Tên tác giả,required|max_length[100]"
                                   name="txtTENTACGIA" title="" disabled>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding ">
                        <div class="col-xs-4">
                            <label>Xếp loại</label>
                        </div>
                        <div class="col-xs-8">
                            <input class="form-control input-sm kb-i-col-m"
                                   style="width: 100%;" id="txtXEPLOAI"
                                   valrule="Xếp loại,max_length[50]"
                                   name="txtXEPLOAI" title="" disabled>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding mgt3">
                        <div class="col-xs-4">
                            <label>Ngày bắt đầu</label>
                        </div>
                        <div class="col-xs-8">
                            <div class="input-group" style="width: 100%;">
                                <input class="form-control input-sm" id="txtNGAYBD"
                                       name="txtNGAYBD" title="" valrule="Ngày bắt đầu,date"
                                       data-mask="00/00/0000" placeholder="dd/MM/yyyy"
                                       maxlength="19" disabled> <span
                                    class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                    type="sCal"
                                    onclick="NewCssCal('txtNGAYBD','ddMMyyyy','dropdown',false,'24',true)"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Ngày kết thúc</label>
                        </div>
                        <div class="col-xs-8">
                            <div class="input-group" style="width: 100%;">
                                <input class="form-control input-sm"
                                       valrule="Ngày kết thúc,date" id="txtNGAYKT" name="txtNGAYKT"
                                       title="" data-mask="00/00/0000"
                                       placeholder="dd/MM/yyyy" maxlength="19" disabled> <span
                                    class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                    type="sCal"
                                    onclick="NewCssCal('txtNGAYKT','ddMMyyyy','dropdown',false,'24',true)"></span>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4 required">
                            <label>Ngày duyệt</label>
                        </div>
                        <div class="col-xs-8">
                            <div class="input-group" style="width: 100%;">
                                <input class="form-control input-sm" id="txtNGAYPHEDUYET"
                                       name="txtNGAYPHEDUYET" title="" data-mask="00/00/0000"
                                       placeholder="dd/MM/yyyy" maxlength="19" disabled
                                       valrule="Ngày phê duyệt,required|date"> <span
                                    class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                    type="sCal"
                                    onclick="NewCssCal('txtNGAYPHEDUYET','ddMMyyyy','dropdown',false,'24',true)"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Nghiệm thu</label>
                        </div>
                        <div class="col-xs-8">
                            <div class="input-group" style="width: 100%;">
                                <input class="form-control input-sm" id="txtNGAYNGHIEMTHU"
                                       name="txtNGAYNGHIEMTHU" title=""
                                       data-mask="00/00/0000" placeholder="dd/MM/yyyy"
                                       valrule="Ngày nghiệm thu,date" maxlength="19" disabled>
                                <span
                                        class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                        type="sCal"
                                        onclick="NewCssCal('txtNGAYNGHIEMTHU','ddMMyyyy','dropdown',false,'24',true)"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4 required">
                            <label>Ngày tạo</label>
                        </div>
                        <div class="col-xs-8">
                            <div class="input-group" style="width: 100%;">
                                <input class="form-control input-sm" id="txtNGAYTAO"
                                       name="txtNGAYTAO" title=""
                                       data-mask="00/00/0000" placeholder="dd/MM/yyyy"
                                       valrule="Ngày tạo,required|date" disabled>
                                <span
                                        class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                        onclick="NewCssCal('txtNGAYTAO','ddMMyyyy','dropdown',false,'24',true)"></span>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Hiện trạng</label>
                        </div>
                        <div class="col-xs-8">
                            <select class="form-control input-sm" id="cboHIENTRANG"
                                    name="cboHIENTRANG" modeDis="" style="width: 100%; height: 24px"
                                    disabled>
                                <option value="">-- Lựa chọn --</option>
                                <option value="1">Đang thực hiện</option>
                                <option value="2">Báo cáo lần 1</option>
                                <option value="3">Báo cáo lần 2</option>
                                <option value="4">Báo cáo tổng kết</option>
                                <option value="5">Sinh hoạt KHTH tuần</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Xuất bản</label>
                        </div>
                        <div class="col-xs-8">
                            <select class="form-control input-sm" id="cboXUATBAN" modeDis=""
                                    style="width: 100%; height: 24px" disabled>
                                <option value="">-- Lựa chọn --</option>
                                <option value="1">Tài liệu nội bộ</option>
                                <option value="2">In sách nội bộ</option>
                                <option value="3">In sách phát hành</option>
                            </select>
                        </div>

                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Cấp quản lý</label>
                        </div>
                        <div class="col-xs-8">
                            <select class="form-control input-sm" id="cboCAPQUANLY"
                                    modeDis="" style="width: 100%; height: 24px" disabled>
                                <option value="">-- Lựa chọn --</option>
                                <option value="1">Khoa</option>
                                <option value="2">Bệnh viện</option>
                                <option value="3">Bộ y tế</option>
                                <option value="4">Nhà nước</option>
                            </select>

                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Kinh phí</label>
                        </div>
                        <div class="col-xs-8">
                            <input class="form-control input-sm kb-i-col-m"
                                   style="width: 100%;" id="txtKINHPHITHUCHIEN"
                                   valrule="Kinh phí,max_length[15]|is_natural"
                                   name="txtKINHPHITHUCHIEN" title="" disabled>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>Nguồn kinh phí</label>
                        </div>
                        <div class="col-xs-8">
                            <input class="form-control input-sm kb-i-col-m"
                                   style="width: 100%;" id="txtNGUONKINHPHI"
                                   valrule="Nguồn kinh phí,max_length[200]" name="txtNGUONKINHPHI"
                                   title="" disabled>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label>File đính kèm</label>
                        </div>
                        <div class="col-xs-8 ">
                            <input type="file" id="fileUpload" multiple="" style="display: none;">
                            <pre id="txtFILENAME" class="raw">Đính kèm tài liệu...</pre>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding">
                        <div class="col-xs-4">
                            <label> </label>
                        </div>
                        <div class="col-xs-8 ">
                            <button type="button" class="btn btn-sm btn-primary" id="btnDownload">
                                <span class="glyphicon glyphicon-download-alt"></span> Dowload file
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnDelete">
                                <span class="glyphicon glyphicon-remove"></span> Xóa file
                            </button>
                        </div>
                    </div>
                    <div class="col-xs-12 low-padding mgt10 mgb15" style="text-align: center;">
                        <button type="button" class="btn btn-sm btn-primary" id="btnThem">
                            <span class="glyphicon glyphicon-pencil"></span> Thêm
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" id="btnSua"
                                disabled>
                            <span class="glyphicon glyphicon-edit"></span> Sửa
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" id="btnXoa"
                                disabled>
                            <span class="glyphicon glyphicon-remove"></span> Xóa
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" id="btnLuu"
                                disabled>
                            <span class="glyphicon glyphicon-floppy-disk"></span> Lưu
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" id="btnHuy"
                                disabled>
                            <span class="glyphicon glyphicon-remove-circle"></span> Hủy
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xs-8 low-padding mgt-10" style="padding-left: 10px !important;">
            <table id="grdDanhSachDV"></table>
            <div id="pager_grdDanhSachDV"></div>
        </div>
        <div class="col-md-8 low-padding mgt5" hidden="true" id="divError">
            <div id="dvExcelError">
                <table id="grdDanhSachError"></table>
            </div>
        </div>
    </div>
    <div class="contextMenu" id="contextMenu"
         style="display: none; width: 250px;">
        <ul style="width: 250px; font-size: 65%;">
            <li id="editNhanSu" class="menulevel2"><span
                    class="ui-icon ui-icon-pencil" style="float: left"></span> <span
                    style="font-size: 130%; font-family: Verdana">Cập nhật nhân sự cho đề tài</span></li>
        </ul>
    </div>

</div>
<script>
    var opt = [];
    var schema = '{db_schema}';
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var uuid = '{uuid}';

    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");

    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid;

    parent.DlgUtil.tunnel(DlgUtil.moveEvent);
    objVar = EventUtil.getVar("dlgVar");
    var list = new NCKhoaHoc(_opts);
    list.load();
</script>