<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC_BAOCAO_VLOAIDIEUCHINH_MATHANG_BVBD_10284" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" uuid="e9432a68-162d-495b-9257-7d9122b9c3cf">
	<property name="ireport.zoom" value="2.3579476910000454"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="VPARS_loaitvt_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_khoid_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_coso_TEXT" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_BAOCAO_VLOAIDCMATHANG_BVBD($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{tungay},$P{denngay},$P{VPARS_loaitvt_VALUE},$P{VPARS_coso_TEXT},$P{VPARS_khoid_VALUE},$P{ora_cursor})}]]>
	</queryString>
	<field name="TEN" class="java.lang.String"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="MA" class="java.lang.String"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="HANSUDUNG" class="java.lang.String"/>
	<field name="TEN_NUOC" class="java.lang.String"/>
	<field name="DONGIA_VAT" class="java.math.BigDecimal"/>
	<field name="LOAI_VAT_TU" class="java.lang.String"/>
	<field name="NGAY_NX" class="java.lang.String"/>
	<field name="KHOBC" class="java.lang.String"/>
	<field name="NOIGIAO" class="java.lang.String"/>
	<variable name="TONG_SL" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{TEN}]]></variableExpression>
	</variable>
	<variable name="TONG_TIEN" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<variable name="TONG_PART" class="java.math.BigDecimal" resetType="Group" resetGroup="nhacungcaptt" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<group name="nhacungcaptt">
		<groupExpression><![CDATA[$F{NOIGIAO}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="802" height="20" uuid="1ced2703-64eb-4ea3-9b88-ad02bf1c85fd"/>
					<box leftPadding="5">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NOIGIAO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField>
					<reportElement x="0" y="0" width="624" height="20" uuid="6089753b-d94b-4035-b77d-012bdae4a6f3"/>
					<box leftPadding="5">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng cộng: "]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement x="624" y="0" width="118" height="20" uuid="addfafd8-b493-4b29-a28b-486766b050d4"/>
					<box rightPadding="5">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($V{TONG_PART}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="742" y="0" width="60" height="20" uuid="548d007b-3bf2-4cd0-9a37-ddc08640227b"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
					</textElement>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="132" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="372" height="45" uuid="58f85e5d-74d8-4bfd-9a6f-ca57e77be4c9"/>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}+"<br>"+$P{org_name}+"<br>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="91" width="802" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="9e3235a6-4af6-4735-a5a2-77e3a67f203b"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + " đến ngày "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="110" width="802" height="16" uuid="4d22c1e1-a9d0-40c6-abda-2ef103c1cee1"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Kho nhập: "+$F{NOIGIAO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="45" width="802" height="26" uuid="e50e7a5a-0c9e-424e-af2b-5e10e42d22eb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAI_VAT_TU}==null?"BÁO CÁO NHẬP ĐIỀU CHỈNH THEO MẶT HÀNG":("BÁO CÁO NHẬP " + $F{LOAI_VAT_TU}.toUpperCase() + " ĐIỀU CHỈNH THEO MẶT HÀNG")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="71" width="802" height="20" uuid="4afdf848-3346-42d4-a868-dbac66ab2d61"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Cơ sở: " + $P{VPARS_coso_TEXT}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="45" splitType="Stretch">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="232" y="0" width="140" height="45" uuid="7173e713-0985-4451-b855-8d9fe2842840"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tên thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="572" y="0" width="70" height="45" uuid="d3f28c3f-7904-418d-b9c5-2afc7a9aa771"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn giá đã có VAT]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="372" y="0" width="60" height="45" uuid="77f23329-c58f-4cab-b002-266c934808c2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn vị]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="642" y="0" width="100" height="45" uuid="dde0fd47-**************-4e1da254a767"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[THÀNH TIỀN]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="432" y="0" width="80" height="45" uuid="80c3b374-144a-497a-9426-0d510dc4dbff"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Nước SX]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="742" y="0" width="60" height="45" uuid="21f73764-e723-4771-9e89-d895cbe5cafb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Hạn SD]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="202" y="0" width="30" height="45" uuid="82feb1f4-c680-454f-aaef-9cc495fdc760"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="512" y="0" width="60" height="45" uuid="2d21012a-e585-4c53-a620-b9543c577f17"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="45" uuid="fb648da8-163c-402e-82b9-af626fc91546"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Ngày nhập]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="60" y="0" width="142" height="45" uuid="d48ca0b9-91df-4c2a-9314-2db7858999a1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số HĐ]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="232" y="0" width="140" height="20" uuid="36d360ad-6bbb-41e5-9768-17f4c498c3a4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="372" y="0" width="60" height="20" uuid="5542a6f3-df4c-4b10-b40d-1429669e4c3a"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="512" y="0" width="60" height="20" uuid="ff23063f-18d2-4db5-88c2-9219c043c203"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($F{SOLUONG}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="202" y="0" width="30" height="20" uuid="2adca175-22aa-4c41-97af-4ed306e9e83e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isPdfEmbedded="true"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="572" y="0" width="70" height="20" uuid="ceb540a5-f1be-4974-aac8-94b2c6413683"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($F{DONGIA_VAT}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="642" y="0" width="100" height="20" uuid="11699aff-3011-4f50-b45d-2025fad117da"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($F{THANH_TIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="742" y="0" width="60" height="20" uuid="223873a6-57f9-493f-93c2-9f17d8e3f6a2"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HANSUDUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="60" y="0" width="142" height="20" uuid="2c007739-7161-4234-a3de-aa4e14006980"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="432" y="0" width="80" height="20" uuid="a8cad2e2-30d9-454f-92aa-479ab3932b59"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_NUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="916dfe7e-70d2-483a-b6a7-74741f9f23e8"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_NX}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20">
			<textField>
				<reportElement x="642" y="3" width="160" height="14" uuid="16d4314b-cb15-4409-b906-a758a1ccf201"/>
				<box>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss")).format(new Date())]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="232" y="3" width="340" height="14" uuid="f5e31b27-f30a-490a-a39c-3a73b97fb08c"/>
				<box>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="96" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="624" height="20" uuid="9904a47b-0959-447c-9c7e-957c1bf01a2f"/>
				<box leftPadding="5">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng cộng: "]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement x="624" y="0" width="118" height="20" uuid="f74f1977-bdac-427a-a730-1c111255642b"/>
				<box rightPadding="5">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($V{TONG_TIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="742" y="0" width="60" height="20" uuid="b3ccc7a2-1973-4da0-ac4f-1b531e177d59"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="594" y="30" width="208" height="20" uuid="40eb8ea1-49d3-4849-b4dd-e0f9406902a3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+(new SimpleDateFormat("dd").format(new java.util.Date()))+" tháng "+(new SimpleDateFormat("MM").format(new java.util.Date()))+" năm " + (new SimpleDateFormat("yyyy").format(new java.util.Date()))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="594" y="50" width="208" height="20" uuid="6815dad1-a7a4-4162-a2dc-a6fbbaa2d90e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI LẬP BÁO CÁO]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
