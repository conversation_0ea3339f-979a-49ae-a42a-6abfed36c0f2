<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20170105" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../canlamsang/ParaclinicalUtility.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS01X003_UPDATE_MAMAY.js?v=20180419"></script>

<style>
	.ui-jqgrid tr.jqgrow td {
		white-space: nowrap !important;
		height:auto;
		vertical-align:text-top;
		padding-top:2px;
	}
</style>

<div id="divMain">
	
	<div class="row">
  		<div class="col-md-12 bs-linebreak" style="height:12px;">
  		</div>
  	</div>
	
	<div class="col-xs-12 low-padding panel panel-info" >
		<h1 style="color: blue; font-size: large;""></h1>
		<div class="mgt5" id="divDSVKSoidom"  >
			<div class="col-xs-12 low-padding">
				<div class="col-md-4 low-padding">
					<label class="mgl10">Chọn mã máy</label> 
				</div>
				<div class="col-md-4 low-padding">
					<select class="form-control input-sm" id="cboMAMAY" style="width:50%">
						<option value=""></option>
					</select>
				</div>	
			</div>
			
			<div class="row">
		  		<div class="col-md-12 bs-linebreak" style="height:5px;">
		  		</div>
		  	</div>
		</div>
		
		<div class="col-md-12 low-padding" style="margin-top: 20px;" ></div>
		
		<div class="col-md-12 low-padding"  padding-top: 15px !important; >
			<div class="col-md-10 low-padding" >
				<div class="col-md-10 low-padding">
					<div class="col-md-10 low-padding">
						<button class="btn btn-sm btn-primary" id="btnLuu" style="width:110px">
							<span class="glyphicon glyphicon-floppy-disk"></span> Lưu lại 
						</button>								
						<button class="btn btn-sm btn-primary" id="btnHuy2" style="width:110px;padding-left: 4px !important;">
							<span class="glyphicon glyphicon-log-out"></span> Đóng 
						</button>								
					</div>
					
					<div class="col-md-2 low-padding">
						<button class="btn btn-sm btn-primary" id="btnLuuTatCa" style="width:110px;padding-left: 4px !important;display: none;">
							<span class="glyphicon glyphicon-edit"></span> Cập nhật tất cả
						</button>
					</div>
				</div>
			</div>					
		</div>	
	</div>	
	
	<div id="divKetLuan">
			
	</div>
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDDichVuID" value="">
<input type="hidden" id="hdfIDShowBtnEditTime" value="">
<input type="hidden" id="hdfIDKQ1" value="">
<input type="hidden" id="hdfIDKQ2" value="">
<input type="hidden" id="hdfIDKQ1Xoa" value="">
<input type="hidden" id="hdfIDKQ2Xoa" value="">

<div id="divTraKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmView" style="width:1000px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script type="text/javascript">	
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var ten_khoa = '{dept_name}';
	var lang = "vn";
	var type = "{type}";
	var userRights = "{q}";
	var user_group_id = '{user_group_id}';
	console.log("userright="+userRights+",usergroup="+user_group_id);
	var session_par=[];
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	session_par[0]=hospital_id;
	var table_name='{table}';

	var idmaubenhpham = '{idmaubenhpham}';
	var iddichvukb = '{iddichvukb}';
	var iddichvu = '{dichvuid}';
	var idkqcls = '{dskqclsid}';
	//nghiant 26062017
	var showNutSuaNgayTraKQ='{showNutSuaNgayTraKQ}';
	//var vikhuanid = '{vikhuanid}';
	//var loaivikhuanid = '{loaivikhuanid}';
	var show='{show}';
	var tiepnhanid='{tiepnhanid}';
	var rolePTH='{rolePTH}';
	var mode = '{showMode}';
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	}
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	_opts._idmaubenhpham=idmaubenhpham;
	_opts._showNutSuaNgayTraKQ=showNutSuaNgayTraKQ;
	_opts._iddichvukb=iddichvukb;
	_opts._iddichvu=iddichvu;
	_opts._idkqcls=idkqcls;//nghiant 20042018
	console.log('_idkqcls: '+idkqcls);
	_opts._show=show;
	_opts._tiepnhanid=tiepnhanid;
	
	_opts._rolePTH = rolePTH;

	console.log('rolePTH: '+_opts._rolePTH);
	
	
	initRest(_opts._uuid);
	
	var tt = new UpdateMamay(_opts);
	tt.load();
</script>