<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>
<script type="text/javascript" src="../noitru/NTU02D126_PhieuTinhTrangDinhDuong.js?v=002"></script>
<div width="100%" id="divMain" class="container">
	<div class="col-md-12 low-padding mgt5">
		<div id="inputForm">
			<div class="mgt10">
				<label class="mgl15">
					<strong>Thông tin bệnh nhân</strong>
				</label>
			</div>
			<div class="form-inline mgt5">
				<div class="col-md-12 ">
					<div class="col-md-2 low-padding">
						<label class="mgl5 ">Họ và tên</label>
					</div>
					<div class="col-md-6 low-padding">
						<input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtTENBENHNHAN" name="txtTENBENHNHAN" title="" style="width:100%;" disabled>
					</div>
					<div class="col-md-2 low-padding">
						<label class="mgl5 ">Năm sinh</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtNAMSINH" name="txtNAMSINH" title="" style="width:100%;" disabled>
					</div>
				</div>
			</div>
			<!-- L2PT-13486 TTLINH start -->
			<div class="form-inline mgt5">
				<div class="col-md-12 ">
					<div class="col-md-2 low-padding">
						<label class="mgl5">Thời gian tạo phiếu</label>
					</div>
					<div class="col-md-2 low-padding">
						<div class="input-group" style="width: 95%">
							<input class="form-control input-sm" valrule="Thời gian tạo phiếu,datetime" id="txtTHOIGIANTAOPHIEU" data-mask="00/00/0000" disabled>
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtTHOIGIANTAOPHIEU','ddMMyyyy','dropdown',true,'24',true)"></span>
						</div>
					</div>
				</div>
			</div>
			<!--L2PT-13486 TTLINH end -->
			<div class="form-inline mgt5">
				<div class="col-md-12 ">
					<div class="col-md-2 low-padding" id="lblChanDoan">
						<label class="mgl5 ">Chẩn đoán</label>
					</div>
					<div class="col-md-10 low-padding">
						<div class="col-md-12 low-padding">
							<div class="col-md-2 low-padding">
								<input class="form-control input-sm kb-i-col-m" id="txtCHANDOAN_ID" title="" style="width: 100%;">
							</div>
							<div class="col-md-9 low-padding">
								<input class="form-control input-sm kb-i-col-m" id="txtCHANDOAN" disabled="disabled" style="width: 100%;">
							</div>
							<div class="col-md-1 low-padding">
								<button type="button" class="btn btn-sm btn-danger" id="btnHuy" style="width: 100%; height: 24px; display: none">
									<span class="glyphicon glyphicon-remove"></span>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="form-inline mgt5">
				<div class="col-md-12 ">
					<div class="col-md-2 low-padding" id="lblCanNangVV">
						<label class="mgl5">Cân nặng vào viện</label>
					</div>
					<div class="col-md-10 low-padding">
						<div class="col-md-12 low-padding">
							<div class="col-md-2 low-padding">
								<input class="form-control input-sm kb-i-col-m" type="number" style="width: 100%; text-align: center" id="txtCANNANG_VAOVIEN" title="" maxlength="5">
							</div>
							<div class="col-md-1 low-padding">
								<label class="mgl5 ">kg</label>
							</div>
							<div class="col-md-1 low-padding" id="lblChieuCaoVV">
								<label class="mgl5 ">Chiều cao</label>
							</div>
							<div class="col-md-2 low-padding">
								<input class="form-control input-sm kb-i-col-m" type="number" style="width: 100%; text-align: center" id="txtCHIEUCAO" title="" maxlength="5">
							</div>
							<div class="col-md-1 low-padding">
								<label class="mgl5 ">cm</label>
							</div>
							<div class="col-md-1 low-padding" id="lblBMIVV">
								<label class="mgl5 ">BMI</label>
							</div>
							<div class="col-md-2 low-padding">
								<input class="form-control input-sm kb-i-col-m" type="number" style="width: 100%; text-align: center" id="txtBMI_VAOVIEN" title="" maxlength="5">
							</div>
							<div class="col-md-1 low-padding">
								<label class="mgl5 ">kg/m2</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="form-inline mgt5">
				<div class="col-md-12 ">
					<div class="col-md-2 low-padding">
						<label class="mgl5 ">Cân nặng ra viện</label>
					</div>
					<div class="col-md-10 low-padding">
						<div class="col-md-12 low-padding">
							<div class="col-md-2 low-padding">
								<input class="form-control input-sm kb-i-col-m" type="number" style="width: 100%; text-align: center" id="txtCANNANG_RAVIEN" title="" maxlength="5">
							</div>
							<div class="col-md-1 low-padding">
								<label class="mgl5 ">kg</label>
							</div>
							<div class="col-md-offset-4 col-md-1 low-padding">
								<label class="mgl5 ">BMI</label>
							</div>
							<div class="col-md-2 low-padding">
								<input class="form-control input-sm kb-i-col-m" type="number" style="width: 100%; text-align: center" id="txtBMI_RAVIEN" title="" maxlength="5">
							</div>
							<div class="col-md-1 low-padding">
								<label class="mgl5 ">kg/m2</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="divSangLoc">
				<div class="mgt10">
					<label class="mgl15">
						<strong>1. SÀNG LỌC NGUY CƠ SUY DINH DƯỠNG</strong>
					</label>
				</div>
				<table class="table table-bordered">
					<tbody>
						<tr>
							<td style="width: 50%" id="td_sl_bmi"><div class="low-padding" attrreq>
									<label>- BMI < 20.5 kg/m2</label>
								</div></td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_BMI" id="radSL_BMI_CO" value='1'> <label class="mgl5 ">Có</label></td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_BMI" id="radSL_BMI_KHONG" value='0'> <label class="mgl5 ">Không</label></td>
						</tr>
						<tr>
							<td style="width: 50%" id="td_sl_sutcan"><div class="low-padding" attrreq>
									<label>- Sụt cân trong 1 tháng qua</label></td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_SUTCAN" id="radSL_SUTCAN_CO" value='1'> <label class="mgl5 ">Có</label></td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_SUTCAN" id="radSL_SUTCAN_KHONG" value='0'> <label class="mgl5 ">Không</label>
							</td>
						</tr>
						<tr>
							<td style="width: 50%" id="td_sl_luongan"><div class="low-padding" attrreq>
									<label>- Lượng ăn giảm trong tuần qua</label></td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_LUONGAN" id="radSL_LUONGAN_CO" value='1'> <label class="mgl5 ">Có</label>
							</td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_LUONGAN" id="radSL_LUONGAN_KHONG" value='0'> <label class="mgl5 ">Không</label>
							</td>
						</tr>
						<tr id="tr_sl_benhnang">
							<td style="width: 50%"><div class="low-padding" attrreq>
									<label>- Bệnh nặng đi lại hạn chế</label></td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_BENHNANG" id="radSL_BENHNANG_CO" value='1'> <label class="mgl5 ">Có</label>
							</td>
							<td style="width: 25%; text-align: center"><input style="margin: 0px !important;" type="radio" name="radSL_BENHNANG" id="radSL_BENHNANG_KHONG" value='0'> <label class="mgl5 ">Không</label>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="mgt10" attrreq>
					<label class="mgl15">Kết luận nguy cơ suy dinh dưỡng</label>
				</div>
				<table class="table table-bordered">
					<tbody>
						<tr>
							<td style="width: 50%"><input style="margin: 0px !important;" type="radio" name="radSL_KETLUAN" id="radSL_KETLUAN_KHONG" value='0'> <label class="mgl5 ">Không</label></td>
							<td id='divSL_KETLUAN_KHONG'><label class="mgl5 ">&rarr; Tái sàng lọc sau 1 tuần</label></td>
						</tr>
						<tr>
							<td style="width: 50%"><input style="margin: 0px !important;" type="radio" name="radSL_KETLUAN" id="radSL_KETLUAN_CO" value='1'> <label class="mgl5 ">Có >= 1 yếu tố nguy cơ</label>
							</td>
							<td><label class="mgl5 ">&rarr; Tiếp tục bước 2, đánh giá</label></td>
						</tr>
					</tbody>
				</table>
			</div>
			<div id="divDanhGia">
				<div class="mgt10">
					<label class="mgl15">
						<strong>2. ĐÁNH GIÁ TÌNH TRẠNG DINH DƯỠNG</strong>
					</label>
				</div>
				<table class="table table-bordered" id="tbl_dg_1">
					<thead>
						<tr>
							<th scope="col" style="text-align: center;">Tiêu chí</th>
							<th scope="col" style="text-align: center;">1 điểm</th>
							<th scope="col" style="text-align: center;">2 điểm</th>
							<th scope="col" style="text-align: center;">Điểm</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td style="width: 10%" id="td_bmi1"><div class="low-padding" attrreq>
									<label>BMI</label></td>
							<td style="width: 40%" id="td_bmi2">18.5 – 20.5 kg/m2</td>
							<td style="width: 40%" id="td_bmi3">< 18.5 kg/m2</td>
							<td style="width: 10%; text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_BMI" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td style="width: 10%"><div class="low-padding" attrreq>
									<label>Sụt Cân</label></td>
							<td style="width: 40%">5% - 9.9 % trong 1 tháng qua</td>
							<td style="width: 40%">>= 10% trong 1 tháng qua</td>
							<td style="width: 10%; text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_SUTCAN" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td style="width: 10%"><div class="low-padding" attrreq>
									<label>Lượng ăn</label></td>
							<td style="width: 40%">Giảm >= 50% trong tuần qua</td>
							<td style="width: 40%" id="td_luongan3">Giảm >= 75% trong tuần qua</td>
							<td style="width: 10%; text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_LUONGAN" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td style="width: 10%"><div class="low-padding" attrreq>
									<label>Bệnh lý</label></td>
							<td style="width: 40%">Bệnh nặng: Đại phẩu, tai biến mạch máu não, nhiễm trùng nặng, ung thư</td>
							<td style="width: 40%">Bệnh rất nặng: Chấn thương nặng, đang được chăm sóc tích cực</td>
							<td style="width: 10%; text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_BENHLY" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td style="width: 10%">Tổng điểm</td>
							<td style="width: 40%"></td>
							<td style="width: 40%"></td>
							<td style="width: 10%; text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_TONGDIEM" title="" style="width:100%;" disabled></td>
						</tr>
						<tr>
							<td style="width: 10%"><div class="low-padding" attrreq>
									<label>Kết luận:</label></td>
							<td colspan="3">
								<div class="row" style="display: grid; grid-template-columns: 2% 30% 30%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radDG_KETLUAN" id="radDG_KETLUAN_KHONG" value='0'>
										<label class="mgl5 ">Không suy dinh dưỡng(< 2 điểm);</label>
									</div>
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radDG_KETLUAN" id="radDG_KETLUAN_CO" value='1'>
										<label class="mgl5 ">Suy dinh dưỡng(>=2 điểm)</label>
									</div>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
				<table class="table table-bordered" id="tbl_dg_2">
					<thead>
						<tr>
							<th scope="col" style="text-align: center; width: 15%">Tiêu chí</th>
							<th scope="col" style="text-align: center; width: 25%">0 điểm</th>
							<th scope="col" style="text-align: center; width: 25%">1 điểm</th>
							<th scope="col" style="text-align: center; width: 25%">2 điểm</th>
							<th scope="col" style="text-align: center; width: 10%">Điểm</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td id="td_bmi1">BMI</td>
							<td id="td_bmi0">≥ 18.5 kg/m2</td>
							<td id="td_bmi2">18.5 – 20.5 kg/m2</td>
							<td id="td_bmi3">< 18.5 kg/m2</td>
							<td style="text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_BMI" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td>Sụt Cân</td>
							<td>Giảm dưới 5%</td>
							<td>5% - 9.9%</td>
							<td>>= 10%</td>
							<td style="text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_SUTCAN" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td>Ăn đường miệng</td>
							<td>Ăn bình thường</td>
							<td>Giảm >= 50% trong tuần qua</td>
							<td id="td_luongan3">Giảm >= 75% trong tuần qua</td>
							<td style="text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_LUONGAN" title="" style="width:100%;"></td>
						</tr>
						<tr>
							<td>Tổng điểm</td>
							<td></td>
							<td></td>
							<td></td>
							<td style="text-align: center"><input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtDG_TONGDIEM" title="" style="width:100%;" disabled></td>
						</tr>
						<tr>
							<td>Kết luận:</td>
							<td colspan="4">
								<div class="row" style="display: grid; grid-template-columns: 2% 98%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radDG_KETLUAN" id="radDG_KETLUAN_KHONG" value='0'>
										<label class="mgl5 ">Không có nguy cơ: ≤ 1 điểm hoặc người bệnh có phù ăn bình thường</label>
									</div>
								</div>
								<div class="row" style="display: grid; grid-template-columns: 2% 98%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radDG_KETLUAN" id="radDG_KETLUAN_CO" value='1'>
										<label class="mgl5 ">Nguy cơ dinh dưỡng nhẹ/vừa: 2-3 điểm hoặc người bệnh có phù ăn giảm trên 50% so với bình thường</label>
									</div>
								</div>
								<div class="row" style="display: grid; grid-template-columns: 2% 98%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radDG_KETLUAN" id="radDG_KETLUAN_NANG" value='2'>
										<label class="mgl5 ">Nguy cơ dinh dưỡng nặng: ≥ 4 điểm hoặc người bệnh có phù không tự ăn được bằng đường miệng</label>
									</div>
								</div>
								<div class="row" style="display: grid; grid-template-columns: 2% 98%;">
									<div class="col">
										<label class="mgl5 ">Dấu * Phân loại nguy cơ dinh dưỡng: (Chỉ dùng tiêu chí ăn đường miệng để đánh giá bệnh nhân có phù)</label>
									</div>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div id="divPhanLoai">
				<div class="mgt10">
					<label class="mgl15">
						<strong>3. PHÂN LOẠI TÌNH TRẠNG DINH DƯỠNG DÀNH CHO NGƯỜI TRƯỞNG THÀNH</strong>
					</label>
				</div>
				<table class="table table-bordered">
					<thead>
						<tr>
							<th scope="col" style="text-align: center">Tình trạng dinh dưỡng</th>
							<th scope="col" style="text-align: center">Chỉ số BMI</th>
							<th scope="col" style="text-align: center">Tình trạng dinh dưỡng của bệnh nhân</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td style="width: 40%; text-align: center">Dọa Suy dinh dưỡng</td>
							<td style="width: 30%; text-align: center" id="tdDOASDD">< 18,50</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_DOASDD"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Suy dinh dưỡng độ 1</td>
							<td style="width: 30%; text-align: center" id="tdSDD_DO1">17,00 - 18,49</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_DO1"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Suy dinh dưỡng độ 2</td>
							<td style="width: 30%; text-align: center" id="tdSDD_DO2">16,00 - 16,99</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_DO2"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Suy dinh dưỡng độ 3</td>
							<td style="width: 30%; text-align: center" id="tdSDD_DO3">< 16,00</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_DO3"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Bình thường</td>
							<td style="width: 30%; text-align: center" id="tdSDD_BT">18,50 - 24,99</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_BT"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Thừa cân</td>
							<td style="width: 30%; text-align: center" id="tdSDD_TC">≥ 25,00</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_TC"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Tiền béo phì</td>
							<td style="width: 30%; text-align: center" id="tdSDD_TBP">25,00 - 29,99</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_TBP"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Béo phì</td>
							<td style="width: 30%; text-align: center" id="tdSDD_BP">≥ 30,00</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_BP"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Béo phì độ 1</td>
							<td style="width: 30%; text-align: center" id="tdSDD_BP_DO1">30,00 - 34,99</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_BP_DO1"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Béo phì độ 2</td>
							<td style="width: 30%; text-align: center" id="tdSDD_BP_DO2">35,00 - 39,99</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_BP_DO2"></td>
						</tr>
						<tr>
							<td style="width: 40%; text-align: center">Béo phì độ 3</td>
							<td style="width: 30%; text-align: center" id="tdSDD_BP_DO3">≥ 40,00</td>
							<td style="text-align: center"><input type="checkbox" id="chkPL_SDD_BP_DO3"></td>
						</tr>
					</tbody>
				</table>
			</div>
			<div id="divKeHoach">
				<div class="mgt10">
					<label class="mgl15">
						<strong>4. KẾ HOẠCH CAN THIỆP</strong>
					</label>
				</div>
				<table class="table table-bordered">
					<thead>
						<tr>
							<th scope="col" style="text-align: center">NỘI DUNG</th>
							<th scope="col" style="text-align: center">CHỈ ĐỊNH</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td style="width: 20%"><div class="low-padding" attrreq>
									<label>Chỉ định chế độ ăn (mã số)</label></td>
							<td>
								<div id="divSUATANTXT">
									<input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtKH_CHIDINH" name="txtKH_CHIDINH" title="" style="width:100%;">
								</div>
								<div class="col-md-12" id="divSUATANCBO" style="display: none">
									<div class="col-md-4 low-padding">
										<input class="form-control input-sm kb-i-col-m" style="width: 100%;" id="txtTK_SUATAN" name="txtTK_SUATAN" title="" style="width:100%;">
									</div>
									<div class="col-md-8 low-padding">
										<select class="form-control input-sm kb-i-col-m " id="cboKH_CHIDINH" style="width: 100%;" filterLike="txtTK_SUATAN">
										</select>
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td style="width: 30%"><div class="low-padding" attrreq>
									<label>Đường nuôi ăn</label></td>
							<td>
								<div class="row" style="display: grid; grid-template-columns: 2% 30% 30% 30%;">
									<div class="col">
										<input type="checkbox" id="chkKH_MIENG">
										<label class="mgl5 ">Miệng</label>
									</div>
									<div class="col">
										<input type="checkbox" id="chkKH_ONGTHONG">
										<label class="mgl5 ">Ống thông</label>
									</div>
									<div class="col">
										<input type="checkbox" id="chkKH_TINHMACH">
										<label class="mgl5 ">Tĩnh mạch</label>
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td style="width: 30%"><div class="low-padding" attrreq>
									<label>Mời hội chẩn dinh dưỡng</label></td>
							<td>
								<div class="row" style="display: grid; grid-template-columns: 2% 50% 50%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radKH_MOIHCDD" id="radKH_MOIHCDD_CO" value='1'>
										<label class="mgl5 ">Có</label>
									</div>
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radKH_MOIHCDD" id="radKH_MOIHCDD_KHONG" value='0'>
										<label class="mgl5 ">Không</label>
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td style="width: 30%"><div class="low-padding" attrreq>
									<label>Tái đánh giá</label></td>
							<td>
								<div class="row" style="display: grid; grid-template-columns: 2% 98%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radKH_TAIDANHGIA" id="radKH_TAIDANHGIA7" value='7'>
										<label class="mgl5 " id='divKH_TAIDANHGIA7'>Sau 7 ngày (Ở người bệnh không suy dinh dưỡng)</label>
									</div>
								</div>
								<div class="row" style="display: grid; grid-template-columns: 2% 98%;">
									<div class="col">
										<input style="margin: 0px !important;" type="radio" name="radKH_TAIDANHGIA" id="radKH_TAIDANHGIA3" value='3'>
										<label class="mgl5 " id='divKH_TAIDANHGIA3'>Sau 3 ngày (Ở người bệnh suy dinh dưỡng)</label>
									</div>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
				<!-- Beg_HaNv_211221: Điều chỉnh form - L2PT-11875 -->
				<div class="col-md-12 low-padding mgt5" id='divNLKN' style="display: none">
					<div class="col-md-4 low-padding">
						<label class="mgl20">Năng lượng khuyến nghị</label>
					</div>
					<div class="col-md-8 low-padding">
						<input class="form-control input-sm" id="txtNANGLUONG_KHUYENNGHI" style="width: 100%;">
					</div>
				</div>
				<div class="col-md-12 low-padding mgt5" id='divTVDD' style="display: none">
					<div class="col-md-4 low-padding">
						<label class="mgl20">Tư vấn dinh dưỡng</label>
					</div>
					<div class="col-md-8 low-padding">
						<label class="radio-inline">
							<input type="radio" value="1" name="rdoTUVAN_DINHDUONG" id="rdoTUVAN_DINHDUONG_CO">
							Có
						</label>
						<label class="radio-inline">
							<input type="radio" value="0" name="rdoTUVAN_DINHDUONG" id="rdoTUVAN_DINHDUONG_KHONG">
							Không
						</label>
					</div>
				</div>
				<!-- End_HaNv_211221 -->
			</div>
			<div class="col-md-12 low-padding mgt5 mgb10">
				<div class="col-md-12 low-padding" style="text-align: center;">
					<button type="button" class="btn btn-default btn-primary" id="btnThem">
						<span class="glyphicon glyphicon-pencil"></span>
						Thêm
					</button>
					<button type="button" class="btn btn-default btn-primary" id="btnLuu">
						<span class="glyphicon glyphicon-floppy-disk"></span>
						Sửa
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnIn">
						<span class="glyphicon glyphicon-print"></span>
						In phiếu
					</button>
					<!-- L2PT-13074 start -->
					<button type="button" class="btn btn-sm btn-primary" id="btnRefresh">
						<span class="glyphicon glyphicon-refresh"></span>
						Refresh
					</button>
					<!-- L2PT-13074 end-->
					<!-- L2PT-13227 start-->
					<button type="button" class="btn btn-sm btn-primary" id="btnXoa">
						<span class="glyphicon glyphicon-remove"></span>
						Xóa
					</button>
					<!-- L2PT-13227 end-->
					<button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
						<span class="glyphicon glyphicon-ok"></span>
						Lưu & Ký
					</button>
					<button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
						<span class="glyphicon glyphicon-remove-circle"></span>
						Hủy ký
					</button>
					<button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
						<span class="glyphicon glyphicon-print"></span>
						In ký số
					</button>
					<button type="button" class="btn btn-sm btn-primary" id="btnDong">
						<span class="glyphicon glyphicon-remove-circle"></span>
						Đóng
					</button>
				</div>
			</div>
			<div class="col-md-12 low-padding mgb10">
				<table id="grdDSSangLoc"></table>
				<div id="pager_grdDSSangLoc"></div>
			</div>
		</div>
	</div>
</div>
<script>
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	initRest(uuid, "/vnpthis");
	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;
	parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	objVar = EventUtil.getVar("dlgVar");
	_opts._hosobenhanid = objVar.hosobenhanid;
	_opts._khambenhid = objVar.khambenhid;
	_opts._mode = objVar.mode; //L2PT-27784
	var DS = new dSCHList(_opts);
	DS.load(hospital_id);
</script>