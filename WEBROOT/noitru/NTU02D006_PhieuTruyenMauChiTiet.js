/*
 Mã màn hình  : NTU02D006
 File mã nguồn : NTU02D006_PhieuTruyenMau.js
 Mục đích  : <PERSON>ia<PERSON> diện màn hình
 	+ <PERSON>hập thông tin bệnh án
 	+ <PERSON>hập thông tin hỏi bệnh
 Tham số vào : 
 	truyenmau_id 	: ID mẫu bệnh phẩm
 	khambenh_id		: ID khám bệnh
	
 Ng<PERSON>ời lập trình	 <PERSON> cập nhật  	<PERSON>hi chú
 linhvv				 1/9/2016			Sửa bổ xung tính năng 
 */
function phieuTruyenMauChiTiet(_opts) {
	var _user_id;
	var _company_id = _opts.hospital_id;
	var _khambenh_id = _opts.khambenh_id;
	var _benhnhan_id=_opts.benhnhan_id;
	var _truyenmau_id = _opts.truyenmau_id;
	var _truyenmau_chitiet_id=_opts.truyenmau_chitiet_id;
	var _dept_id = _opts.dept_id;

	var _ngaytao = moment(_opts.ngaytao,'DD/MM/YYYY HH:mm:ss');
	var _trangthaikhambenh = _opts.trangthaikhambenh;
	
	var _action = "";
	var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	//var _sql="select icd10code,icd10name from dmc_icd where upper(icd10code) like upper('%[FILTER]%')";
	var sql_par=[];
	var that=this;
	this.load=doLoad;
	var nhiptho_text = '';
	var truyenmauK74 = '0';
	var timeRange = '';//L2PT-19132
	var isCopy = _opts.isCopy;//L2PT-28173
	var NTU_PHIEUTRUYENMAU_HNI = '0';
	
	function addNumberOnly(element){
		$(element).keypress(function (e) {
		     if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
		        return false;
		    }
		});
	}
	addNumberOnly($("#txtTOCDO_GIOT"));
	addNumberOnly($("#txtMACH"));
	addNumberOnly($("#txtNHIP_THO"));
	addNumberOnly($("#txtHUYETAP_LOW"));
	addNumberOnly($("#txtHUYETAP_HIGHT"));

	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		$('#txtNGAYDO').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		
		//START L2PT-19132
		var data_CH = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","NTU_PHIEUTRUYENMAU_K74;NTU_PTM_K74_NHIPTHO;" +
				"NTU_PTM_RANGE_DATE;NTU_PHIEUTRUYENMAU_BDHCM;NTU_PTM_BOBATBUOC_HA;NTU_PHIEUTRUYENMAU_HNI");
		if(data_CH != null && data_CH.length > 0){
			truyenmauK74 = data_CH[0].NTU_PHIEUTRUYENMAU_K74;
			nhiptho_text = data_CH[0].NTU_PTM_K74_NHIPTHO;
			timeRange = data_CH[0].NTU_PTM_RANGE_DATE;
			//BVTM-999
			if(data_CH[0].NTU_PHIEUTRUYENMAU_BDHCM == '1'){
				$('#ptmct_BDHCM').show();
				$('#ptmct_BDHCM1').show();
				$('#lblTextTGBATDAU').text('Bắt đầu truyền hồi');
				$('#lblTextTGKETTHUC').text('Ngưng truyền hồi');
				$('#txtABO_NGUOICHO').val(_opts.DINHNHOMDV_MAU==null?'':_opts.DINHNHOMDV_MAU);
				$('#txtABO_NGUOINHAN').val(_opts.DINHNHOMNGUOINHAN==null?'':_opts.DINHNHOMNGUOINHAN);
				ComboUtil.getComboTag("cboBS_CHIDINH","NTU02D006.DSBS",sql_par, "", {value:'',text:'-- Chọn Bác sĩ--'},"sql","", false);
				ComboUtil.getComboTag("cboYT_CHIDINH","NTU02D006.DSYT",sql_par, "", {value:'',text:'-- Chọn y tá--'},"sql","", false);
			}
			//L2PT-3345
			if(data_CH[0].NTU_PTM_BOBATBUOC_HA == '1'){
				$('#lblHA').removeClass('required');
				$("#txtHUYETAP_HIGHT").removeAttr("valrule");
				$("#txtHUYETAP_LOW").removeAttr("valrule");
			}
			if(data_CH[0].NTU_PHIEUTRUYENMAU_HNI == '1'){
				NTU_PHIEUTRUYENMAU_HNI = '1';
			}
			
		}
		
		//START  L2HOTRO-11751
		if(_opts.hospital_id == '965'){
			$('#divTHOIGIAN').hide();
			$('#divTHOIDIEM').show();
		}else{
			$('#divTHOIGIAN').show();
			$('#divTHOIDIEM').hide();
		}
		//END  L2HOTRO-11751
		
		//L2PT-7450
		//truyenmauK74 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_PHIEUTRUYENMAU_K74');
		if(truyenmauK74 == '1'){
			$('#divTHOIGIAN').hide();
		}
		
		//L2PT-12061
		//nhiptho_text = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_PTM_K74_NHIPTHO');
		if(nhiptho_text == '1'){
			$('#divNT_NUM').hide();
			$('#divNT_CHAR').show();
		}else{
			$('#divNT_NUM').show();
			$('#divNT_CHAR').hide();
		}
		
		//START L2PT-19132
		if(timeRange == '1'){
			$('#divRangeDate').show();
			$('#divTHOIGIAN').hide();
		}
	
		
		if(_truyenmau_id != "")
			_action = "Upd";
		else{
			_action = "Add";
			_truyenmau_id = 0;
		}
		
		if (NTU_PHIEUTRUYENMAU_HNI == '1') {
			$('#divTHOIGIAN').hide();
			$('#divTHOIDIEM').hide();
			$('#divRangeDate').show();
			$('#ptmct_BDHCM').show();
			$('#ptmct_BDHCM1').show();
			$('#txtTHOIGIAN').removeAttr("valrule");
			$('#txtTG_BATDAUDO').removeAttr("valrule");
			$('#lblTextTGBATDAU').text('Bắt đầu truyền hồi');
			$('#lblTextTGKETTHUC').text('Ngưng truyền hồi');
			$('#lblTextTGKETTHUC').removeClass('mgl10');
			$('#txtTG_KETTHUCDO').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#txtABO_NGUOICHO').val(_opts.DINHNHOMDV_MAU==null?'':_opts.DINHNHOMDV_MAU);
			$('#txtABO_NGUOINHAN').val(_opts.DINHNHOMNGUOINHAN==null?'':_opts.DINHNHOMNGUOINHAN);
			ComboUtil.getComboTag("cboBS_CHIDINH","NTU02D006.DSBS",sql_par, "", {value:'',text:'-- Chọn Bác sĩ--'},"sql","", false);
			ComboUtil.getComboTag("cboYT_CHIDINH","NTU02D006.DSYT",sql_par, "", {value:'',text:'-- Chọn y tá--'},"sql","", false);
			$('#cboYT_CHIDINH').val(_opts.dieuduongid==null?'':_opts.dieuduongid).trigger('change');
			$('#cboBS_CHIDINH').select2();
			$('#cboYT_CHIDINH').select2();
			$('.non-hni').hide();
		}
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU.TTCHITIET.PTM", _truyenmau_chitiet_id,[]);
    	if(data_ar != null){
    		var _row = data_ar[0];
    		
    		$('#hidTRUYENMAU_CHITIET_ID').val(FormUtil.unescape(_row.TRUYENMAU_CHITIET_ID));
    	    $('#txtTOCDO_GIOT').val(FormUtil.unescape(_row.TOCDO_GIOT));
    	    //START  L2HOTRO-11751
    	    if(_row.THOIGIAN != null){
    	    	$('#txtTHOIGIAN').val(FormUtil.unescape(_row.THOIGIAN));
    	    } 
    	    if(_row.THOIDIEM != null){
    	    	$('#txtTHOIDIEM').val(FormUtil.unescape(_row.THOIDIEM));
    	    }
    	    //END  L2HOTRO-11751
    	    
    	    /*$('#txtMACH').val(FormUtil.unescape(_row.MACH));
    	    $('#txtSACDA_NIEMMAC').val(FormUtil.unescape(_row.SACDA_NIEMMAC));
    	    $('#txtHUYETAP_HIGHT').val(FormUtil.unescape(_row.HUYETAP_HIGHT));
    	    $('#txtHUYETAP_LOW').val(FormUtil.unescape(_row.HUYETAP_LOW));
    	    $('#txtNHIP_THO').val(_row.NHIP_THO);
    	    $('#txtTHANNHIET').val(FormUtil.unescape(_row.THANNHIET));
    	    $('#txtDIENBIENKHAC').val(FormUtil.unescape(_row.DIENBIENKHAC));
    	    $('#txtNHIP_THO_TEXT').val(_row.NHIP_THO_TEXT);//L2PT-12061*/
    	}
    	sql_par.push({"name":"[0]","value":_dept_id});
   
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU.TTCHITIET.PTM", _truyenmau_chitiet_id,[]);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			FormUtil.setObjectToForm("","",row);
			if(row.MLH == '1'){
    			$("#chkMLH").prop('checked', true);
    			$("#lblTocdo").text('Tốc độ (ml/h)');//L2PT-51378
    		}
			$('#cboBS_CHIDINH').val(row.BS_CHIDINH).trigger('change');
			$('#cboYT_CHIDINH').val(row.YT_CHIDINH).trigger('change');
		}
		
		//L2PT-28173
		if(isCopy && isCopy == '1'){
			$("#btnLuu").text('Lưu');
		}
		
	
		this.validator = new DataValidator("divMain");
		bindEvent();
	
		//L2PT-41187
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NTU02D006_DINH_DANG') == "1") { 
			$('#txtTHOIGIAN').attr('maxlength','5');
			$('#txtTHOIGIAN').mask('00:00');
			$('#txtTHOIGIAN').parent().prev().find('label').eq(0).html('Thời gian (giờ:phút)');
		}
	}
	function bindEvent(){
		$("#btnHuy").on("click",function(e){
			EventUtil.raiseEvent("assignTruyenMauChiTiet_cancel");			
		});
		$("#btnLuu").on("click",function(e){
			doIns();
		});
		$("#btnMoi").on("click", function(e) {
			FormUtil.clearForm("PTM","");		
			$('#txtNGAYDO').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
	
		});
		//L2PT-43629
		$("#chkMLH").change(function() {
			if(this.checked) {
				$("#lblTocdo").text('Tốc độ (ml/h)');
			}else{
				$("#lblTocdo").text('Tốc độ (giọt/phút)');
			}
		});
	}
	
	
	
	
	
	function doIns(){
		var objData = new Object();
		FormUtil.setFormToObject("","",objData);
		
		var valid = that.validator.validateForm();
		if(valid){
			//L2PT-12061
			if(nhiptho_text == '1' && $("#txtNHIP_THO_TEXT").val() == ''){
				return DlgUtil.showMsg("Nhịp thở không được để trống.");
			}
			if(nhiptho_text == '0' && $("#txtNHIP_THO").val() == ''){
				return DlgUtil.showMsg("Nhịp thở không được để trống.");
			}
			
			 
		//L2PT-41187
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NTU02D006_DINH_DANG') == "1") { 
					if(!$("#txtTHOIGIAN").val() || $("#txtTHOIGIAN").val() == ''){
						return DlgUtil.showMsg("Thời gian không được để trống.");
					}
		}
		else{
				//START  L2HOTRO-11751
				if(_opts.hospital_id != '965' && _opts.hospital_id != '10284'){
					if(timeRange == '0'){
						if(truyenmauK74 == '0'){
							if($("#txtTHOIGIAN").val() == ''){
								return DlgUtil.showMsg("Thời gian không được để trống.");
							}
							if($("#txtTHOIGIAN").val() != '' && isNaturalNumber($("#txtTHOIGIAN").val())){
								return DlgUtil.showMsg("Giá trị Thời gian là số nguyên dương.");
							}
						}
					}
				}else{
					if($("#txtTHOIDIEM").val() == '' && _opts.hospital_id != '10284'){
						return DlgUtil.showMsg("Thời điểm không được để trống.");
					}
				}
				//END  L2HOTRO-11751
		}
			
			//START L2PT-19132
			if(timeRange == '1'){
				if($("#txtTG_BATDAUDO").val() == ''){
					return DlgUtil.showMsg("T/g bắt đầu không được để trống.");
				}
				if($("#txtTG_KETTHUCDO").val() == ''){
					return DlgUtil.showMsg("T/g kết thúc không được để trống.");
				}
				if(compareDate($("#txtTG_KETTHUCDO").val(),$("#txtTG_BATDAUDO").val(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg($('#lblTextTGBATDAU').text() + " không được lớn hơn " + $('#lblTextTGKETTHUC').text());								
					$('#txtTG_BATDAUDO').focus();
					return false;	
				}
			}
			
			var _NGAYDO = moment($("#txtNGAYDO").val().trim(),'DD/MM/YYYY HH:mm:ss');
	    	
	    	var b_ngayhientai =jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
	    	if(compareDate(b_ngayhientai,$("#txtNGAYDO").val().trim(),'DD/MM/YYYY HH:mm:ss')){
			    DlgUtil.showMsg("Ngày đo không được lớn hơn ngày hiện tại!");								
				$('#txtNGAYDO').focus();
				return false;	
			}
		  else if(_NGAYDO > _ngaytao  && NTU_PHIEUTRUYENMAU_HNI != '1'){
				$("#txtNGAYDO").focus();
				DlgUtil.showMsg("Ngày đo không được lớn hơn ngày tạo phiếu truyền máu!");
				return false;
			} 
		  else if (Number($("#txtHUYETAP_LOW").val().trim())>Number($("#txtHUYETAP_HIGHT").val().trim())){
			  $("#txtHUYETAP_LOW").focus();
				DlgUtil.showMsg("Số đo huyết áp không hợp lệ!");
				return false; 
		  }
	
		 else{
			    //L2PT-28173
			 	if(isCopy && isCopy == '1'){
			 		_par = [JSON.stringify(objData),_truyenmau_id];
					var ret=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.THEM.PTMCT",_par.join('$'));
			 	}else{
			 		_par = [JSON.stringify(objData),_truyenmau_chitiet_id];
					var ret=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.CAPNHAT.PTMCT",_par.join('$'));	
			 	}
			 	
				if(ret == 1){		
					//L2PT-28173
					if(isCopy && isCopy == '1'){
					    _msg = "Sao chép phiếu truyền máu thành công!";
					}
				    else{
				    	_msg = "Cập nhật phiếu truyền máu thành công!";
				    }
					var evFunc=EventUtil.getEvent("assignSevice_SaveTruyenMauCT");
					evFunc({msg:_msg});
					
				}else{
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
					return false;
				}
		}
		}
	}
	
	
	$('.input-sm').keydown(function (e) {
	     if (e.which === 13) {
	         var index = $('.input-sm').index(this) + 1;
	         $('.input-sm').eq(index).focus();
	     }
	 });
	//START  L2HOTRO-11751
	function isNaturalNumber (str) {
	    var pattern = /^(0|([1-9]\d*))$/;
	    return pattern.test(str);
	}
	//END  L2HOTRO-11751
}