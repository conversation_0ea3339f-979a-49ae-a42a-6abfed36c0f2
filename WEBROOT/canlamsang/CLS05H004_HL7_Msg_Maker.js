/*
 * Log LIS
 * 
 * VietDA 	05/08/2016		Tạo mới
 * 
 */

var ctl_ar=[
	{type:'button',id:'btnConfig',icon:'glyphicon glyphicon-cog',text:'HL7 Config'},
	{type:'button',id:'btnAuto',icon:'glyphicon glyphicon-tasks',text:'Automator'},
	{type:'button',id:'btnMaker',icon:'glyphicon glyphicon-envelope',text:'Msg Maker'},
	{type:'button',id:'btnLog',icon:'glyphicon glyphicon-list-alt',text:'Msg Log'},
	{type:'label',id:'lblInfo',icon:'',text:'Truyền tin HL7'}
];

function LogCLS(opt) {
	this.load = doLoad;
	
	function doLoad(){
		
		initControl();
		bindEvent();
		
//		$("#txtSoPhieu").val("201102000001");
//		$("#txtMaBN").val("20.000316");		
//		$("#txtMaBN1").val("20.000316");
//		$("#txtMaBN2").val("20.000310");
		
	}
	
	function initControl(){		
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		ToolbarUtil.build('toolbarId', ctl_ar);
		var _toolbar=ToolbarUtil.getToolbar('toolbarId');
		
			
	}
	
	function bindEvent(){
		$('#btnSendADT08').on('click', function () {
			var msg = ajaxSvc.HL7_Gateway.updatePatientInfo($("#txtMaBN").val());
			DlgUtil.showMsg(msg,undefined,6000);
		});		
		$("#btnSendADT40").click(function(){
			var msg = ajaxSvc.HL7_Gateway.mergePatient($("#txtMaBN1").val(),$("#txtMaBN2").val());
			DlgUtil.showMsg(msg,undefined,6000);
		});
		
		$("#btnSendORMO01").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendRequest($("#txtSoPhieu").val(),"NW");
			DlgUtil.showMsg(msg,undefined,6000);
		});
		
		$("#btnSendMFNM02").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendMFNUser();
			DlgUtil.showMsg(msg,undefined,6000);
		});
		$("#btnSendMFNM05D").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendMFNDepartment();
			DlgUtil.showMsg(msg,undefined,6000);
		});
		$("#btnSendMFNM05E").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendMFNRoom();
			DlgUtil.showMsg(msg,undefined,2000);
		});
		$("#btnSendMFNM08").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendMFNService();
			DlgUtil.showMsg(msg,undefined,6000);
		});
		$("#btnSendMFNM09").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendMFNDevice();
			DlgUtil.showMsg(msg,undefined,6000);
		});
		$("#btnSendCustomMes").click(function(){
			var msg = ajaxSvc.HL7_Gateway.sendMessageByString($("#msgCustom").val());
			DlgUtil.showMsg(msg,undefined,6000);
		});

		$('#toolbarIdbtnAuto').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H003_HL7_Automator");	
		});

		$('#toolbarIdbtnMaker').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H004_HL7_Msg_Maker");	
		});
		
		$('#toolbarIdbtnConfig').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H001_HL7_System_Config");	
		});

		$('#toolbarIdbtnLog').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H002_HL7_System_Log");	
		});
	}
	
}
