function dSSoList(_opts) {
	var _gridDSSoId = "grdDanhSachDV";
	var jsonsuaphong;
	var type;
	var loainhomdv;
	var checkRequired;
	var checkRequiredsuagia;
	var validator = null;
	var _user_id = -1;
	var _hospital_id;
	flagLoading = false;
	var isSuagia = false;
	var v_suagiadv = -1;
	var i_chuyenkhoaid = -1;
	var i_nhomdichvuid = -1;
	_param = [];
	_soptId = -1;
	var json_pth = [ "" ];
	var valid_ar = [];
	var dsThem = []; // Luu tru cac du lieu tick chon trong grid; 
	var dsThem1 = []; // Luu tru danh sach chi tiet cac row duoc day vao; 
	var dsThemDVPhongL = []; // mang luu tru DV phu duoc lua chon ban dau; 
	var dsThem_DVPhong = []; // mang luu tru DV phu duoc tick chon
	var dsXoa_DVPhong = []; // mang luu tru DV phu da xoa; 
	//L2DKBD-1025
	var dsThem = []; // mang du lieu thay doi trong qua trinh lua chon
	var dsThemBD = []; // mang du lieu id ban dau; 
	var dsThemOP1 = []; // mang luu tru du lieu da cap nhat
	var dsThemGiaKsk = []; // mang du lieu gia ksk
	var dsThemSoLuong = []; // mang du lieu so luong dich vu
	var dsThemPhong = []; // mang du lieu luu tru ID dich vu va html phong thuc hien dich vu; 
	var loadFirst = true;
	//L2DKBD-1025
	var loadFirstDVPHU = false;
	var showGiachenhlech = false;
	var lydo_khoa_req = false;
	var that = this;
	this.load = doLoad;
	function addNumberOnly(element) {
		$(element).keypress(function(e) {
			if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
				return false;
			}
		});
	}
	addNumberOnly($("#txtTHUTUIN"));
	addNumberOnly($("#txtKHOANGCACH"));
	addNumberOnly($("#txtGIA_2016"));
	addNumberOnly($("#txtTHOIGIANCHIDINH"));
	addNumberOnly($("#txtSOPHIM"));
	addNumberOnly($("#txtGIAVIP"));
	addNumberOnly($("#txtGIAGIUONGVIP"));
	addNumberOnly($("#txtGIAVIP_BDHN"));
	addNumberOnly($("#txtLTPT"));
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		validator = new DataValidator("inputForm");
		var _options = $.extend({}, _opts);
		var uuid = _options._uuid;
		var _param = _options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		type = _options.type;
		loainhomdv = _options.loainhomdv;
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "OPT_HOSPITAL_ID;HIS_DMC_DV_GIACHENHLECH;TILE_TT_NHANLUC_NHIHDG;" + "HIS_SHOW_TK_HDDT;HIS_SHOW_NGAYSD_GIA;"
				+ "DMC53_SHOWSTT_DICHVU;HIS_DMC_SHOW_LOAITT" + ";DMC_UDGNDV" + ";TILE_TT_NHANLUC_NHIHDG;" +
						"HIS_DMC_NGUONCT_DM;DMC_DV_CANHBAO_TGCHIDINH;HIS_DMCDV_SHOW_ITEMS;HIS_DMCDV_TAPDOANTT;HIS_DMCDV_MAXANGDAU"); //L2PT-32141 dongpt 20201214 L2PT-105368
		if (data_ar != null && data_ar.length > 0) {
			_hospital_id = data_ar[0].OPT_HOSPITAL_ID;
			showGiachenhlech = data_ar[0].HIS_DMC_DV_GIACHENHLECH;
			
			if (data_ar[0].HIS_DMCDV_SHOW_ITEMS != '0') {
				var ch = data_ar[0].HIS_DMCDV_SHOW_ITEMS;
				var ctrids = ch.split(',');
				for (var h = 0; h < ctrids.length; h++) {
					var ctrid = ctrids[h];
					$('div').children().each(function (i, obj) {
						if ($(obj).attr('id') != null && $(obj).attr('id').indexOf(ctrid) > -1){
							$(obj).show();
						}
					});
				}
			}
			
			//BVTM-7087
			if (data_ar[0].DMC_DV_CANHBAO_TGCHIDINH == 1) {
				$('#divCanhbaoTGCD').show();
			}
			if (data_ar[0].TILE_TT_NHANLUC_NHIHDG == 1) {
				$('#idTILE_TT_NHANLUC_NHIHDG').show();
			}
			//L2PT-28289
			if (data_ar[0].HIS_SHOW_TK_HDDT == 1) {
				$('#idTK_HDDT').show();
			}
			if (data_ar[0].HIS_SHOW_NGAYSD_GIA == 1) {
				$('#divNSD_GIA_VP').show();
			}
			//L2PT-31037
			if (data_ar[0].HIS_DMC_SHOW_LOAITT == 1) {
				$('#divLoai_thanhtoan').show();
			}
			if (data_ar[0].DMC_UDGNDV == '1') {
				$('#btnCapNhatGiaNhieuDV').show();
			}
			if(data_ar[0].TILE_TT_NHANLUC_NHIHDG == 1){
				$('#idTILE_TT_NHANLUC_NHIHDG').show();
			}
			if (data_ar[0].HIS_DMC_NGUONCT_DM == '1') {
				$('#divng_ct_dm').show();
				$('#divng_ct').hide();
			}
			if (data_ar[0].HIS_DMCDV_TAPDOANTT == '1') { //L2PT-53422
				$('#divTAPDOAN_TT').show();
			}
			if (data_ar[0].HIS_DMCDV_MAXANGDAU == '1' && loainhomdv == 14) { //L2PT-105368
				$('#divMAXANGDAU').show();
				$('#divTT4350').removeClass().addClass('col-md-2 low-padding');
			}
		}
		var _hospital_id = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
		if (loainhomdv == 2) {
			jQuery("label[for='myalue']").html("Danh mục dịch vụ khám bệnh");
			//L2PT-4787
			if (_hospital_id == '1203') {
				$('#divTLCK').show();
			} else {
				$('#divTLCK').hide();
			}
			//L2PT-23233
//			if (_hospital_id == '1111') {
			if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'DMC_CK_KTINHTL') == "1"){
				$('#divCongkhamTile').show();
			} else {
				$('#divCongkhamTile').hide();
			}
		} else if (loainhomdv == 3)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ xét nghiệm");
		else if (loainhomdv == 4)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ CDHA");
		else if (loainhomdv == 5)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ phẫu thuật thủ thuật");
		else if (loainhomdv == 9)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ điều trị");
		else if (loainhomdv == 7)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ vật tư");
		else if (loainhomdv == 10)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ chăm sóc");
		else if (loainhomdv == 11)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ áo vàng");
		else if (loainhomdv == 12)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ suất ăn");
		else if (loainhomdv == 13)
			jQuery("label[for='myalue']").html("Danh mục dịch vụ ngày giường");
		if (_hospital_id == '915'){
			$('#divSearchTT').show();
		}
		//START L2HOTRO-12500
		if (loainhomdv == 14 && _hospital_id == '915') {
			$('#loaiAD_NVV').prop('disabled', true);
		} else {
			$('#loaiAD_NVV').prop('disabled', false);
		}
		//END L2HOTRO-12500
		if (loainhomdv == 14) { //L2PT-107327
			$('#divMATT4350').addClass("required");
		}
		if (loainhomdv == 5 && _hospital_id == '915') {
			$('#labelSLN').show();
			$('#txtSLNGUOIClass').show();
			$('#tilePTTT').show();
			addNumberOnly($("#txtSOLUONGNGUOI"));
		} else {
			$('#labelSLN').hide();
			$('#txtSLNGUOIClass').hide();
			$('#tilePTTT').hide();
		}
		if (loainhomdv == 12 && _hospital_id == '951') {
			$('#divGio_SA').show();
		} else {
			$('#divGio_SA').hide();
		}
		if (loainhomdv == 5 && (_hospital_id == '1014' || _hospital_id == '1077')) {
			$('#divKIP').show();
		}
		if (_hospital_id == '30360') {
			$('#divlydokhoa').show();
			$('#divKhoa_vv').hide();
		}
		//START L2PT-5595
		if (_hospital_id == '10284') {
			$('#divGIAVIP').show();
			$('#divGHEP_MT').show();//BVTM-4914
			//ductx -bvtm-6514
			addRole();
			$('#btnTclog').show();
            //end bvtm-6514
			//L2PT-19438
			$('#divkedikem').show();
		} else {
			$('#divGIAVIP').hide();
		}
		//START L2PT-6482
		if (_hospital_id == '993') {
			$('#divNoisoiSN').show();
		} else {
			$('#divNoisoiSN').hide();
		}
		//START L2PT-6298
		if (showGiachenhlech == '1') {
			$('#divGiaChenhlech').show();
		} else {
			$('#divGiaChenhlech').hide();
		}
		/*if(loainhomdv == 5){
			$("#lblphu_cap").show();
			$("#txtPHUCAP").show();
		}else{
			$("#lblphu_cap").hide();
			$("#txtPHUCAP").hide();
		}*/
		//START L2PT-19527
		if (_hospital_id == '1133' || _hospital_id == '24750') {
			$('#divChenh_BHYT').show();
		} else {
			$('#divChenh_BHYT').hide();
		}
		if (_hospital_id == '10284') {
			$('#divDuyetMo').show();
		} else {
			$('#divDuyetMo').hide();
		}
		if (loainhomdv == 2) {
			$('#divloaiksk').show();
		}else {
			$('#divloaiksk').hide();
		}
		var _gridDSSoHeader = "id,dichvuid,0,0,t,l,1,2;" + "Mã Nhóm DV,nhomdichvuid,90,0,t,l,1,2;" + "Mã dịch vụ,madichvu,100,0,f,l,1,2;" + "Mã DMBYT,MADMBYT,100,0,f,l,1,2;"
				+ "Tên dịch vụ,tendichvu,450,0,f,l,1,2;" + "Tên nhóm,tennhom,200,0,f,l,1,2;" + "Phòng TH,phongcombo,200,0,f,l,1,2,ES;" + "Đơn vị,donvi,100,0,f,l,1,2;"
				+ "Giá DV,giadichvu,100,number,f,l,1,2;" + "Giá BHYT,giabhyt,100,number,f,l,1,2;" + "Giá Nước ngoài,gianuocngoai,100,number,f,l,1,2;" + "Ngày cập nhật,ngaycapnhat,100,0,f,l,1,2;"
				+ "Người cập nhật,nguoicapnhat,100,0,f,l,1,2;" + "TT37,matt37,100,0,f,l,1,2;" + "Trạng thái dịch vụ,Trangthai,100,0,f,l,1,2;" + "Loại Thanh toán,loaithanhtoan,100,0,f,l,1,2;khoa,khoa,100,0,t,l,1,2";
		var _gridDSSoHeader_BDHN = "id,dichvuid,0,0,t,l,1,2;" + "Mã Nhóm DV,nhomdichvuid,90,0,t,l,1,2;" + "Mã dịch vụ,madichvu,100,0,f,l,1,2;" + "Mã DMBYT,MADMBYT,100,0,f,l,1,2;"
				+ "Tên dịch vụ,tendichvu,450,0,f,l,1,2;" + "Tên nhóm,tennhom,200,0,f,l,1,2;" + "Đơn vị,donvi,100,0,f,l,1,2;" + "Giá DV,giadichvu,100,number,f,l,1,2;"
				+ "Giá BHYT,giabhyt,100,number,f,l,1,2;" + "Giá Nước ngoài,gianuocngoai,100,number,f,l,1,2;" + "Ngày cập nhật,ngaycapnhat,100,0,f,l,1,2;"
				+ "Người cập nhật,nguoicapnhat,100,0,f,l,1,2;" + "TT37,matt37,100,0,f,l,1,2;" + "Trạng thái dịch vụ,Trangthai,100,0,f,l,1,2;" + "Loại Thanh toán,loaithanhtoan,100,0,f,l,1,2";
		//GridUtil.init(_gridDSSoId,"100%","300","",true,_gridDSSoHeader, false, { rowNum: 500,rowList: [500, 800, 1000]});
		var _group = {
			groupField : [ 'tennhom' ],
			groupColumnShow : [ false ],
			groupText : [ '<b>{0}</b>' ]
		};
		if (_hospital_id == '10284' && loainhomdv == 5) {
			GridUtil.initGroup("grdDanhSachDV", "100%", "300", "Danh sách dịch vụ", true, _group, _gridDSSoHeader_BDHN, false, {
				rowNum : 100,
				rowList : [ 100, 200, 300 ]
			});
			//BVTM-6731
			$('#divNhieuTVTPTTT').show();
		} else {
			GridUtil.initGroup("grdDanhSachDV", "100%", "300", "Danh sách dịch vụ", true, _group, _gridDSSoHeader, false, {
				rowNum : 100,
				rowList : [ 100, 200, 300 ]
			});
		}
		loadGridData();
		var _gridKQDVHeader = "dichvuthuchienid,dichvuthuchienid,0,0,t,l,1,2;Mã dịch vụ,madichvu,100,0,f,l,1,2;Tên dịch vụ,tendichvu,300,0,f,l,1,2";
		GridUtil.init("grdKQDV", "100%", "349", "Danh sách kết quả dịch vụ thực hiện", false, _gridKQDVHeader);
		var _gridDVDKGKHeader = "dichvu_id,dichvu_id,0,0,t,l,1,2;dichvu_con_id,dichvu_con_id,100,0,t,l,1,2;Mã dịch vụ,madichvu,100,0,f,l,1,2;Tên dịch vụ,tendichvu,300,0,f,l,1,2;Phòng dịch vụ,org_name,200,0,f,l,1,2";
		GridUtil.init("grdDVDKGK", "100%", "315", "Danh sách dịch vụ đi kèm gói khám", false, _gridDVDKGKHeader);
		var _gridDVDKGKHeader = "org_id,org_id,0,0,t,l,1,2;Mã phòng,org_code,40,0,f,l,1,2;Tên phòng,org_name,90,0,f,l,1,2;Loại,dvbatbuoc,50,0,f,l,1,2,ES";
		GridUtil.init("grdCHPhongDV", "100%", "315", "Danh sách phòng theo dịch vụ", true, _gridDVDKGKHeader, true, {
			rowNum : 1000,
			rowList : [ 200, 500, 1000 ]
		});
		$("#cboCHUYENKHOAID1").focus();
		ComboUtil.getComboTag("cboREPORT_CODE_KQ", "DMC01.REPORT_KQ", [], "", "", "sql");
		//L2PT-28289
		ComboUtil.getComboTag("cboTKHDID", "DMC01.TKHDDT", [], "", "", "sql");
		//START L2PT-21103
		if (_opts.modeAction != null && _opts.modeAction == '1') {
			$("#divAction").hide();
		}
		ComboUtil.getComboTag("cboPPVCID",'PTTT.VOCAM', [],'',{value:'',text:'--Lựa chọn--'},"sql");
		
		ComboUtil.getComboTag("cboLOAIMAUBENHPHAM", "DMC01.LOAIMBP", [], "", "", "sql");
		//L2PT-9015 lay nguon ct tu danh muc
		ComboUtil.getComboTag("cboNGUONCHUONGTRINH_DM", "DMC01.NGUONCT", [], "", {value:'-1',text:'---Chọn---'}, "sql");
	};
	function _bindEvent() {
		//ductx -bvtm-6514
		$("#btnTclog").on("click", function name(params) {
            var loai = "";
			var name = "";
			if(_opts.loainhomdv == "2" && _opts.type == "2"){
				loai = "2";
				name = "khám bệnh";
			}else if(_opts.loainhomdv == "3" && _opts.type == "6"){
				loai = "3";
				name = "xét nghiệm";
			}else if(_opts.loainhomdv == "4" && _opts.type == "7"){
				loai = "4";
				name = "cđha";
			}else if(_opts.loainhomdv == "5"){
				loai = "5";
				name = "pttt";
			}else if(_opts.loainhomdv == "11"){
				loai = "11";
				name = "áo vàng";
			}else if(_opts.loainhomdv == "12"){
				loai = "12";
				name = "suất ăn";
			}else if(_opts.loainhomdv == "13"){
				loai = "13";
				name = "Ngày giường";
			}else if(_opts.loainhomdv == "14"){
				loai = "14";
				name = "Vận chuyển";
			}else if(_opts.loainhomdv == "1"){
				loai = "20";
				name = "Thu khác";
			}
			var row = $("#grdDanhSachDV").jqGrid("getGridParam", "selrow");
            var _txt = $("#grdDanhSachDV").jqGrid("getRowData", row).madichvu;
            if (_txt == null) return DlgUtil.showMsg("Chưa chọn dịch vụ", undefined, 2000);			
            var myVar = {LOAI: loai, NAME: name, TXT: _txt };
            dlgPopup = DlgUtil.buildPopupUrl("dlgTracuulog", "divDlg", "manager.jsp?func=../danhmuc/DMC58_TraCuuLog_Dichvu", myVar, "Log lịch sử dịch vụ", window.innerWidth*0.79,window.innerHeight*0.75);
            dlgPopup.open("dlgTracuulog");
        });
        //end bvtm-6514
		// an hien button
		$("#btnMORONG").on("click", function() {
			$('#divTab1').removeClass().addClass('col-md-12 low-padding mgt-10');
			$('#divTab2').hide();
			$('#btnMORONG').hide();
			$('#btnTHUNHO').show();
			_initControl();
		});
		$("#btnTHUNHO").on("click", function() {
			$('#divTab1').removeClass().addClass('col-md-6 low-padding mgb5');
			$('#divTab2').show();
			$('#btnMORONG').show();
			$('#btnTHUNHO').hide();
			_initControl();
		});
		$("li[id=tabDVDK]").on("click", function(e) {
			$('#tabDVDK').addClass("active");
			$('#divDVDK').addClass("active");
			$('#tabKQDV').removeClass("active");
			$('#divKQDV').removeClass("active");
			$('#tabCHPhongDV').removeClass("active");
			$('#divCHPhongDV').removeClass("active");
		});
		$("li[id=tabCHPhongDV]").on("click", function(e) {
			$('#tabCHPhongDV').addClass("active");
			$('#divCHPhongDV').addClass("active");
			$('#tabDVDK').removeClass("active");
			$('#divDVDK').removeClass("active");
			$('#tabKQDV').removeClass("active");
			$('#divKQDV').removeClass("active");
		});
		ComboUtil.getComboTag("cboPHANLOAI", "DMC01.PL", [], "", "", "sql");
		var parLoaiDV = -1;
		if (loainhomdv == '3') {
			parLoaiDV = 1;
		} else if (loainhomdv == '4') {
			parLoaiDV = 2;
		} else if (loainhomdv == '5') {
			parLoaiDV = 3;
		}
		ComboUtil.getComboTag("cboMA_NHOM", "DMC01.MA_NHOM", [ {
			"name" : "[0]",
			"value" : parLoaiDV
		} ], "", {
			text : "--- Chọn ---",
			value : -1
		}, "sql");
		ComboUtil.getComboTag("cboREPORT_CODE", "DMC01.REPORT", [], "", "", "sql");
		ComboUtil.getComboTag("cboREPORT_CODE_KQ", "DMC01.REPORT_KQ", [], "", "", "sql");
		ComboUtil.getComboTag("cboNHOMDICHVUID", "DMC.05", [ {
			"name" : "[0]",
			"value" : loainhomdv
		} ], "", "", "sql");
		ComboUtil.getComboTag("cboNHOM_MABHYT_ID", "DMC.06", [], "", "", "sql");
		ComboUtil.getComboTag("cboKHOANMUCID", "DMC.07", [], "", "", "sql");
		var sql_parNG = user_id + '$' + schema + '$' + province_id + '$' + hospital_id;
		ComboUtil.getComboTag("cboMADICHVU_NG", "DMC.08", sql_parNG, "", "", "sp", '');
		ComboUtil.getComboTag("cboMANHOMBAOCAO", "DMC.MNBC01", [], "", "", "sql");
		ComboUtil.getComboTag("cboCHUYENKHOAID", "DMC.CBCK.01", [], "", "", "sql");
		ComboUtil.getComboTag("cboMAXANGDAU", "DMC.MAXANGDAU", [], "", "", "sql"); //L2PT-105368
		ComboUtil.getComboTag("cboCHUYENKHOAID1", "DMC.CBCK.01", [], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '');
		ComboUtil.getComboTag("cboLOAIKSKID", "DMC01.LOAIKSK", [], "", {
			text : "--- Chọn ---",
			value : 0
		}, "sql"); //L2PT-105368

		$("#cboMAXANGDAU").on("change", function(e) { //L2PT-105368
			$("#txtMATT4350").val($('#cboMAXANGDAU option:selected').text().split('@')[1]);
		});
		$('#cboMA_NHOM').on('change', function(e) {
			ComboUtil.getComboTag("cboREPORT_CODE", "DMC01.REPORT_1", [ {
				"name" : "[0]",
				"value" : $('#cboMA_NHOM').val()
			} ], "", "", "sql");
			ComboUtil.getComboTag("cboREPORT_CODE_KQ", "DMC01.RP_KQ", [ {
				"name" : "[0]",
				"value" : $('#cboMA_NHOM').val()
			} ], "", "", "sql");
		});
		$("#cboCHUYENKHOAID1").on("change", function(e) {
			i_chuyenkhoaid = $("#cboCHUYENKHOAID1").val();
			loadGridData();
		});
		ComboUtil.getComboTag("cboNHOMDICHVUID1", "DMC.CBNDV.01", [ {
			"name" : "[0]",
			"value" : loainhomdv
		} ], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '');
		$("#cboNHOMDICHVUID1").on("change", function(e) {
			i_nhomdichvuid = $("#cboNHOMDICHVUID1").val();
			loadGridData();
		});
		//L2PT-8763
		$("#cboTRANGTHAITK").on("change", function(e) {
			loadGridData();
		});
		ComboUtil.getComboTag("cboPTTTHANGID", "DMC.CBPTTT.01", [], "", "", "sql");
		// phong thuc hien xet nghiem
		$('#btnTraKetQua').on('click', function() {
			//======================
//			var rowKeys_select = $("#grdDanhSachDV").getGridParam('selarrrow');
			var par = [];
			par = dsThem1;
			var param_select = JSON.stringify(par);
			//======================
			var jsonArr_check = JSON.parse(param_select);
			if (jsonArr_check.length == 0) {
				DlgUtil.showMsg("Yêu cầu chọn bản ghi trước khi tiếp tục");
				return;
			}
			var obj_firt = jsonArr_check[0];
			console.log(obj_firt.dichvuid);
			//==============================
			var test;
			/* dữ liệu nhận từ form cha */
			test = obj_firt.dichvuid;
			var paramInput = {
				dichvuid : test,
				param_select : param_select
			};
			var url2 = "manager.jsp?func=../danhmuc/DMC01_SuaPhongThucHien&type=" + type;
			console.log("open url2: " + url2);
			// thiết lập sự kiện khi nhấn nút ở popup con
			EventUtil.setEvent("CLS01X003_THOAT", function(e) {
				DlgUtil.close("dlgTraKetQua");
			});
			EventUtil.setEvent("btnLuu_PTH", function(e) {
				DlgUtil.close("dlgTraKetQua");
				$("#grdDanhSachDV").jqGrid('resetSelection');
				dsThem = [];
				dsThem1 = [];
			});
			var popup = DlgUtil.buildPopupUrl("dlgTraKetQua", "divTraKetQua", url2, paramInput, "Chọn phòng thực hiện", 1200, 440);
			popup.open("dlgTraKetQua");
			//loadGridData();
		});
		// phong thuc hien goi kham
		$('#btnTraKetQuagoikham').on('click', function() {
			//======================
//			var rowKeys_select = $("#grdDanhSachDV").getGridParam('selarrrow');
			var par = [];
			par = dsThem1;
			var param_select = JSON.stringify(par);
			//======================
			var jsonArr_check = JSON.parse(param_select);
			if (jsonArr_check.length == 0) {
				DlgUtil.showMsg("Yêu cầu chọn bản ghi trước khi tiếp tục");
				return;
			}
			var obj_firt = jsonArr_check[0];
			console.log(obj_firt.dichvuid);
			//==============================
			var test;
			/* dữ liệu nhận từ form cha */
			test = obj_firt.dichvuid;
			var paramInput = {
				dichvuid : test,
				param_select : param_select
			};
			var url2 = "manager.jsp?func=../danhmuc/DMC01_PhongThucHienGoiKham";
			console.log("open url2: " + url2);
			// thiết lập sự kiện khi nhấn nút ở popup con
			EventUtil.setEvent("CLS01X003_THOAT", function(e) {
				DlgUtil.close("dlgTraKetQua");
			});
			var popup = DlgUtil.buildPopupUrl("dlgTraKetQua", "divTraKetQua", url2, paramInput, "Chọn Khoa thực hiện", 1200, 440);
			popup.open("dlgTraKetQua");
		});
		// Chi dinh dich vu goi kham
		$('#btnDVDKGK').on('click', function() {
			var paramInput = {
				dichvuid : _soptId
			};
			var url2 = "manager.jsp?func=../danhmuc/DM01_CDDV_GOIKHAM&type=" + type;
			console.log("open url2: " + url2);
			// thiết lập sự kiện khi nhấn nút ở popup con
			EventUtil.setEvent("luu_DSDVGK", function(e) {
				DlgUtil.close("dlgTraKetQuaGK");
				loadGridDataKQDV_DVGK()
			});
			var popup = DlgUtil.buildPopupUrl("dlgTraKetQuaGK", "divTraKetQuaGK", url2, paramInput, "Chọn dịch vụ đi kèm gói khám", 1200, 580);
			popup.open("dlgTraKetQuaGK");
		});
//		dichvuid
		$("#grdDanhSachDV").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
			var ids = $("#grdDanhSachDV").getGridParam("reccount");
			var lstDichvuID = "";
			for (var i = 1; i <= ids; i++) {
				var _row = $("#grdDanhSachDV").jqGrid('getRowData', i);
				if (dsThem.indexOf(_row.dichvuid) != -1) {
					$("#grdDanhSachDV").jqGrid('setSelection', i, false);
				}
				//START Combobox phong thuc hien - hongdq 20180125
				lstDichvuID = lstDichvuID + "," + _row.dichvuid;
				//END Combobox phong thuc hien - hongdq 20180125
			}
//			DlgUtil.showMsg("DsThem: " + JSON.stringify(dsThem1));
			//START L2DKBD-1025 Combobox phong thuc hien - hongdq 20180125
			lstDichvuID = lstDichvuID.substr(1, lstDichvuID.length);
			var rowIds = $('#grdDanhSachDV').jqGrid('getDataIDs');
			dsThem = [];
			dsThemSoLuong = [];
			dsThemGiaKsk = [];
			dsThemBD = [];
			dsThemOP1 = [];
			var html = '';
			var htmlBegin = '<select class="form-control input-sm"  >';
			var htmlEnd = '</select>';
			if (_hospital_id == '10284' && loainhomdv != 5) {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC01.GETPTH", lstDichvuID);
				for (var i = 0; i < rowIds.length; i++) {
					var _row1 = $("#grdDanhSachDV").jqGrid('getRowData', rowIds[i]);
					var htmlBody = "";//'<option value="-1">--Chọn phòng--</option>';
					var checkSelected = false;
					for (var j = 0; j < data_ar.length; j++) {
						var html = '';
						//					/var htmlBody = '<option value="-1">--Chọn phòng--</option>';
						if (_row1.dichvuid == data_ar[j].DICHVUID) {
							if (!checkSelected) {
								htmlBody += '<option value="' + data_ar[j].ID + '" selected>' + data_ar[j].NAME + '</option>'
							} else {
								htmlBody += '<option value="' + data_ar[j].ID + '">' + data_ar[j].NAME + '</option>'
							}
							checkSelected = true;
						}
					}
					html = htmlBegin + htmlBody + htmlEnd;
					$("#grdDanhSachDV").jqGrid('setCell', rowIds[i], 'phongcombo', html);
				}
			}
			/*$('.input-sm').multiselect({
			    columns: 1,
			    placeholder: '---Chọn phòng---',
			    search: true
			});*/
			//loadFirst = false; 					// lan dau da load xong, khong load lai nua; 
			//END L2DKBD-1025 Combobox phong thuc hien - hongdq 20180125
		});
		GridUtil.setGridParam(_gridDSSoId, {
			onSelectRow : function(id, status) {
				GridUtil.unmarkAll(_gridDSSoId);
				GridUtil.markRow(_gridDSSoId, id);
				if (id) {
					if (flagLoading)
						return;
					var _row = $("#" + _gridDSSoId).jqGrid('getRowData', id);
					_soptId = _row.dichvuid;
					loadGridDataCT(_soptId);
					var _sql_par = [ {
						"name" : "[0]",
						"value" : _soptId
					} ];
					_sql_par = RSUtil.setSysParam(_sql_par, _param);
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC.04", _soptId);
					if (data_ar != null && data_ar.length > 0) {
						var row = data_ar[0];
						FormUtil.clearForm("inputForm", "");
						FormUtil.setObjectToForm("inputForm", "", row);
					}
//	    		    var _result = jsonrpc.AjaxJson.ajaxExecuteQueryO("DMC.04", _sql_par);
//				    var _ds_sopt = JSON.parse(_result);
//				    var _sopt = _ds_sopt[0];
//				    FormUtil.clearForm("inputForm","");
//				    FormUtil.setObjectToForm("inputForm","",_sopt);
					setEnabled([ "btnThem", "btnSua", 'btnSuaGia', "btnXoa", "btnCopy", "btnTraKetQua", "btnTraKetQuagoikham" ], [ "btnLuu", "btnHuy" ]);
				    //ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514 
					//L2PT-15637
					lydo_khoa_req = false;
				}
				// reset lai 1 so bien he thong; 
				loadFirstDVPHU = false;
				dsThemDVPhongL = [];
				dsThem_DVPhong = [];
				dsXoa_DVPhong = [];
				//========================================
				if (status && _row.dichvuid != null && _row.dichvuid != 'null' && dsThem.indexOf(_row.dichvuid) == -1) {
					dsThem.push(_row.dichvuid);
					dsThem1.push(_row);
				}
				if (!status) {
					var j = dsThem.indexOf(_row.dichvuid);
					if (j != -1) {
						dsThem.splice(j, 1);
						dsThem1.splice(j, 1);
					}
				}
				//========================================
				loadGridDataKQDV_DVGK();
				loadGridPhongDV(dsThem1);
//	            var _sql_par_kqdv =[{"name":"[0]", "value":_soptId},{"name":"[1]", "value":loainhomdv}];
//	    		_sql_par_kqdv = RSUtil.setSysParam(_sql_par_kqdv, _param);
//	    		GridUtil.loadGridBySqlPage("grdKQDV","DMC.KQDVXN.1",_sql_par_kqdv);
//	    		GridUtil.loadGridBySqlPage("grdDVDKGK","DMC.CDDVGK.05",_sql_par_kqdv);
//	    		DlgUtil.showMsg("DsThem: " + JSON.stringify(dsThem1));
			},
			ondblClickRow : function(id) {
				var rowData = $('#' + _gridDSSoId).jqGrid('getRowData', id);
				if (rowData != null) {
					var paramInput = {
						dichvuid : rowData.dichvuid,
						loainhom : loainhomdv
					};
					// thiết lập sự kiện khi nhấn nút ở popup con
					EventUtil.setEvent("luu_KQXNDV", function(e) {
						loadGridDataKQDV_DVGK()
					});
					dlgPopup = DlgUtil.buildPopupUrl("divDlgCXN", "divDlg", "manager.jsp?func=../danhmuc/DM01_CDKQXN", paramInput, "HIS - Nhập kết quả xét nghiệm", 1100, 500);
					DlgUtil.open("divDlgCXN");
				}
			},
			onSelectAll : function(id, status) {
				var rowIds = $('#' + _gridDSSoId).jqGrid('getDataIDs');
				for (i = 0; i < rowIds.length; i++) {
					rowData = $('#' + _gridDSSoId).jqGrid('getRowData', rowIds[i]);
					if (status && dsThem.indexOf(rowData["dichvuid"]) == -1 && rowData["dichvuid"] != null) {
						dsThem.push(rowData.dichvuid);
						dsThem1.push(rowData);
					}
					if (!status) {
						var j = dsThem.indexOf(rowData["dichvuid"]);
						if (j != -1) {
							dsThem.splice(j, 1);
							dsThem1.splice(j, 1);
						}
					}
				}
				loadGridPhongDV(dsThem1);
				//$("#lblThongBao").text("Có " + dsThem.length + " bản ghi được chọn.");
//				DlgUtil.showMsg(" DsThem1: " + JSON.stringify(dsThem) );
			},
			gridComplete : function(id) {
				$(".jqgrow", "#" + _gridDSSoId).contextMenu('contextMenu', {
					bindings : {
						'deleteDV' : function(t) {
							var rowId = $(t).attr("id");
							var rowData = $('#' + _gridDSSoId).jqGrid('getRowData', rowId);
							if (rowData != null) {
								var par_xoa = [];
								par_xoa.push(rowData);
								var param_select_xoa = JSON.stringify(par_xoa);
								DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko?", function(flag) {
									if (flag) {
										var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMCXN.02", param_select_xoa);
										if (fl == 1) {
											DlgUtil.showMsg("Xóa thành công!");
											loadGridData()
										} else if (fl == 0) {
											DlgUtil.showMsg("không được xóa!");
										} else {
											DlgUtil.showMsg("Xảy ra lỗi!");
										}
									}
								});
							}
						},
						'updateDVName' : function(t) {
							var rowId = $(t).attr("id");
							var rowData = $('#' + _gridDSSoId).jqGrid('getRowData', rowId);
							if (rowData != null) {
								var paramInput = {
									dichvuid : rowData.dichvuid,
									madichvu : rowData.madichvu,
									tendichvu : rowData.tendichvu
								};
								// thiết lập sự kiện khi nhấn nút ở popup con
								EventUtil.setEvent("update_tendv", function(e) {
									loadGridData();
								});
								var url = "manager.jsp?func=../danhmuc/DMC01_XN_POPUPEDIT";
								var popup = DlgUtil.buildPopupUrl("divDlgICD", "divDlg", url, paramInput, "Cập nhật tên dịch vụ", 600, 200);
								popup.open("divDlgICD");
							}
						},
						'updateDVCHDA' : function(t) {
							var rowId = $(t).attr("id");
							var rowData = $('#' + _gridDSSoId).jqGrid('getRowData', rowId);
							//----to cdha------
							var par_to_cdha = [];
							par_to_cdha = dsThem1;
							//[START] update ma nhom DVCDHA
							EventUtil.setEvent("update_tendv", function(e) {
								loadGridData();
							});
							var url = "manager.jsp?func=../danhmuc/DMC01_POPUP_UPDATE_DICHVU";
							var paramInput = {
								param_input : par_to_cdha,
								changeTo : 'DVCHDA'
							};
							var popup = DlgUtil.buildPopupUrl("divDlgCDHA", "divDlg", url, paramInput, "Cập nhật danh mục CDHA", 600, 300);
							popup.open("divDlgCDHA");
							//[END] update ma nhom DVCDHA
						},
						'updateDVPTTT' : function(t) {
							var rowId = $(t).attr("id");
							var rowData = $('#' + _gridDSSoId).jqGrid('getRowData', rowId);
							//----to pttt------
							var par_to_pttt = [];
							par_to_pttt = dsThem1;
							//[START] update ma nhom DVPTTT
							EventUtil.setEvent("update_tendv", function(e) {
								loadGridData();
							});
							var url = "manager.jsp?func=../danhmuc/DMC01_POPUP_UPDATE_DICHVU";
							var paramInput = {
								param_input : par_to_pttt,
								changeTo : 'PTTT'
							};
							var popup = DlgUtil.buildPopupUrl("divDlgPTTT", "divDlg", url, paramInput, "Cập nhật danh mục PTTT", 600, 300);
							popup.open("divDlgPTTT");
							//[END] update ma nhom DVCDHA
						},
						'updateDVPXN' : function(t) {
							var rowId = $(t).attr("id");
							var rowData = $('#' + _gridDSSoId).jqGrid('getRowData', rowId);
							//----to Xetnghiem------
							var par_to_xn = [];
							par_to_xn = dsThem1;
							//[START] update ma nhom DV XN
							EventUtil.setEvent("update_tendv", function(e) {
								loadGridData();
							});
							var url = "manager.jsp?func=../danhmuc/DMC01_POPUP_UPDATE_DICHVU";
							var paramInput = {
								param_input : par_to_xn,
								changeTo : 'XN'
							};
							var popup = DlgUtil.buildPopupUrl("divDlgXN", "divDlg", url, paramInput, "Cập nhật danh mục Xét nghiệm", 600, 300);
							popup.open("divDlgXN");
							//[END] update ma nhom DV XN
						}
					},
					onContextMenu : function(event, menu) {
						var rowId = $(event.target).parent("tr").attr("id");
						var grid = $("#" + _gridDSSoId);
						//grid.setSelection(rowId);
						GridUtil.unmarkAll(_gridDSSoId);
						GridUtil.markRow(_gridDSSoId, rowId);
						return true;
					}
				});
			}
		});
		//-----Grid cau hinh phong dich vu -----
		//====================================== Grid grdCHPhongDV ===============================
		$("#grdCHPhongDV").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
			//lay ra danh sach da danh dau, va tick theo danh sach nay; 
			var ids = $("#grdCHPhongDV").getGridParam("reccount");
			if (loadFirstDVPHU == false) {
				// mang lua chon tu dau; 
				var fstThem;
				if (dsThem1 != null && dsThem1.length != 0) {
					fstThem = dsThem1[0].dichvuid;
				} else {
					fstThem = -1;
				}
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC01.CHECK.PHONG", fstThem);
				for (i = 0; i < data_ar.length; i++) {
					if (dsThemDVPhongL.indexOf(data_ar[i].ORG_ID) == -1) {
						dsThemDVPhongL.push(data_ar[i].ORG_ID);
					}
				}
				for (var i = 1; i <= ids; i++) {
					var _row = $("#grdCHPhongDV").jqGrid('getRowData', i);
					if (dsThemDVPhongL.indexOf(_row.org_id) != -1) {
						$("#grdCHPhongDV").jqGrid('setSelection', i, false);
					}
					//set gia tri cho column dvbatbuoc
					var html = '<select class="form-control input-sm" id="' + (_row.org_id) + "_" + 'cboLOAI">';
					html = html + '<option value="0"' + (_row.dvbatbuoc == '0' ? 'selected="selected"' : "") + '>Không bắt buộc</option>';
					html = html + '<option value="1"' + (_row.dvbatbuoc == '1' ? 'selected="selected"' : "") + '>Bắt buộc</option></select>';
					$("#grdCHPhongDV").jqGrid('setCell', i, 'dvbatbuoc', html);
				}
				loadFirstDVPHU = true;
			} else {
				for (var i = 1; i <= ids; i++) {
					var _row = $("#grdCHPhongDV").jqGrid('getRowData', i);
					if (dsThemDVPhongL.indexOf(_row.org_id) != -1) {
						$("#grdCHPhongDV").jqGrid('setSelection', i, false);
					}
					if (dsThem_DVPhong.indexOf(_row.org_id) != -1) {
						$("#grdCHPhongDV").jqGrid('setSelection', i, false);
					}
				}
			}
		});
		//====================================== Grid grddvkt_phu ===============================
		GridUtil.setGridParam("grdCHPhongDV", {
			onSelectRow : function(id, status) {
				GridUtil.unmarkAll("grdCHPhongDV");
				GridUtil.markRow("grdCHPhongDV", id);
				var _row = $("#grdCHPhongDV").jqGrid('getRowData', id);
				if (status) {
					var k = dsXoa_DVPhong.indexOf(_row.org_id);
					if (k != -1) {
						dsThemDVPhongL.push(_row.org_id);
						dsXoa_DVPhong.splice(k, 1);
					} else {
						dsThem_DVPhong.push(_row.org_id);
					}
				} else {
					var k = dsThemDVPhongL.indexOf(_row.org_id);
					if (k != -1) {
						dsXoa_DVPhong.push(_row.org_id)
						dsThemDVPhongL.splice(k, 1);
					} else {
						var j = dsThem_DVPhong.indexOf(_row.org_id);
						if (j != -1) {
							dsThem_DVPhong.splice(j, 1);
						}
					}
				}
			},
			onSelectAll : function(id, status) {
				var rowIds = $("#grdCHPhongDV").jqGrid('getDataIDs');
				for (i = 0; i < rowIds.length; i++) {
					_row = $("#grdCHPhongDV").jqGrid('getRowData', rowIds[i]);
					if (status) {
						var k = dsXoa_DVPhong.indexOf(_row.org_id);
						if (k != -1) {
							dsThemDVPhongL.push(_row.org_id);
							dsXoa_DVPhong.splice(k, 1);
						} else {
							var j = dsThem_DVPhong.indexOf(_row.org_id);
							if (j == -1) {
								var l = dsThemDVPhongL.indexOf(_row.org_id);
								if (l == -1) {
									dsThem_DVPhong.push(_row.org_id);
								}
							}
						}
					} else {
						var k = dsThemDVPhongL.indexOf(_row.org_id);
						if (k != -1) {
							dsXoa_DVPhong.push(_row.org_id)
							dsThemDVPhongL.splice(k, 1);
						} else {
							var j = dsThem_DVPhong.indexOf(_row.org_id);
							if (j != -1) {
								dsThem_DVPhong.splice(j, 1);
							}
						}
					}
				}
			}
		});
		//====================================== ====== ===============================
		$("#btnCHPhongDV").click(function() {
			var objj = null;
			var objn = null;
			//----------start DV PHU-------------------
			var p1 = []; // mang bo chon DV PHU;
			var p2 = []; // mang chon DV PHU;
			var p3 = []; // mang chon ds dichvu;
			var p4 = []; // mang cap nhat cot loai;
			for (t = 0; t < dsXoa_DVPhong.length; t++) {
				objj = new Object();
				objj.org_id = dsXoa_DVPhong[t];
				p1.push(objj);
			}
			for (t = 0; t < dsThem_DVPhong.length; t++) {
				objj = new Object();
				objj.org_id = dsThem_DVPhong[t];
				p2.push(objj);
			}
			//start xu ly rieng phan cap nhat cot loai L2PT-32484
			var idss = $('#grdCHPhongDV').jqGrid('getGridParam', 'selarrrow');
			for (i = 0, n = idss.length; i < n; i++) {
				var rowSelected = $('#grdCHPhongDV').jqGrid('getRowData', idss[i]);
				objn = new Object();
				objn.org_id = rowSelected.org_id;
				objn.dvbatbuoc = $('#' + rowSelected.org_id + '_cboLOAI').val();
				p4.push(objn);
			}
			//end L2PT-32484
			for (t = 0; t < dsThem1.length; t++) {
				objj = new Object();
				objj.dichvuid = dsThem1[t].dichvuid;
				p3.push(objj);
			}
			// HAM SQL CAP NHAT
			var param = [ _soptId, JSON.stringify(p1), JSON.stringify(p2) ];
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC01.UPD_DVPHONG", param.join('$'));
			// sql cap nhat cot loai L2PT-32484		
			var paramn = [ _soptId, JSON.stringify(p4) ];
			var rsn = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC01_01.UPD_DVPHONG", paramn.join('$'));
			//end sql L2PT-32484
			if (rs == '1' && rsn == '1') {
				DlgUtil.showMsg("cập nhật phòng dịch vụ thành công!");
				loadFirstDVPHU = false;
				dsThemDVPhongL = [];
				dsThem_DVPhong = [];
				dsXoa_DVPhong = [];
				//loadGridPhongDV();
			} else {
				DlgUtil.showMsg("Lỗi cập nhật chức năng.");
			}
		});
		$("#btnThem").click(
				function() {
					isEdit = false;
					isCopy = false;
					flagLoading = true;
					FormUtil.clearForm('inputForm', "");
					if (loainhomdv == 5 && _hospital_id == '915') {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN',
								'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID',
								'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
								'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG', 'btnTraKetQua',
								'btnTraKetQuagoikham', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN',
								'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'cboLOAIAPDUNGGIAMOI', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016',
								'txtLTPT', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtSOLUONGNGUOI', 'cboTILE_PTTT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI',
								'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH',
								'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ',
								'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT',
								'txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ], [
								'btnThem', 'btnSua', 'btnXoa', 'btnCopy', 'btnTraKetQua', 'btnTraKetQuagoikham', 'btnSuaGia', 'chkLOAIDICHVU' ]);
					} else {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN',
								'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID',
								'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
								'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG', 'btnTraKetQua',
								'btnTraKetQuagoikham', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID','cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN',
								'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'cboLOAIAPDUNGGIAMOI', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016',
								'txtLTPT', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP',
								'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI',
								'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9',
								'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ], [ 'btnThem', 'btnSua', 'btnXoa', 'btnCopy', 'btnTraKetQua',
								'btnTraKetQuagoikham', 'btnSuaGia', 'chkLOAIDICHVU', 'chkTAPDOAN_TT' ]);
					}
					//ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514
					$("#txtMADICHVU").focus();
				});
		$("#btnLuu").click(
				function() {
					var valid = validator.validateForm();
					if (valid) {
						checkdulieu();
						if (checkRequired == 0) {
							var _validator = new DataValidator([ {
								region : 'divMain',
								fields : valid_ar
							} ]);
							var valid = _validator.validateForm();
							if (!valid) {
								return false;
							}
							//L2PT-15637
							if(lydo_khoa_req && $("#txtLYDO_KHOA").val() == ''){
								$("#txtLYDO_KHOA").focus();
								return DlgUtil.showMsg("Chưa nhập lý do khóa");
							}
							objData = new Object();
							if (isEdit)
								objData["DICHVUID"] = _soptId;
							objData["LOAINHOMDICHVU"] = loainhomdv;
							if (isSuagia) {
								checkdulieusuagia();
								v_suagiadv = 1;
							} else {
								v_suagiadv = 0;
							}
							if (isCopy) {
								objData["DICHVUID"] = _soptId;//dichvuid cua dich vu sao chep
							}
							var thutuin = $("#txtTHUTUIN").val().trim();
							var khoangcach = $("#txtKHOANGCACH").val().trim();
							var LTPT = $("#txtLTPT").val().trim();
							if (thutuin != "" && Number(thutuin) < 0) {
								DlgUtil.showMsg("Thứ tự in phải là số nguyên dương !");
							}
							if (khoangcach != "" && Number(khoangcach) < 0) {
								DlgUtil.showMsg("Khoảng cách phải là số nguyên dương !");
							}
							if (LTPT != "" && Number(LTPT) < 0) {
								DlgUtil.showMsg("Số tháng lưu trữ phải là số nguyên dương !");
							}
							if (checkRequiredsuagia != 1) {
								FormUtil.setFormToObject("inputForm", "", objData);
								var _par = [ JSON.stringify(objData), v_suagiadv ];
								var fl;
								if (isCopy) {
									fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMCXN.01.COPY", _par.join('$'));
								} else {
									fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMCXN.01", _par.join('$'));
								}
								flagLoading = false;
								isSuagia = false;
								if (fl == 1 && isCopy) {
									DlgUtil.showMsg("Sao chép thành công");
									loadGridData()
								} else if (fl == 1) {
									DlgUtil.showMsg("Thêm mới thành công");
									loadGridData()
								} else if (fl == 2) {
									DlgUtil.showMsg("Cập nhật thành công");
									loadGridData()
								} else if (fl == 3) {
									DlgUtil.showMsg("Cập nhật giá mới thành công");
									loadGridData()
								} else {
									DlgUtil.showMsg("Không thành công");
								}
								ComboUtil.getComboTag("cboNHOMDICHVUID", "DMC.05", [ {
									"name" : "[0]",
									"value" : loainhomdv
								} ], "", "", "sql");
								FormUtil.clearForm("inputForm", "");
								if (loainhomdv == 5 && _hospital_id == '915') {
									setEnabled([ "btnThem" ], [ 'btnSuaGia', 'btnSua', 'btnXoa', 'btnLuu', 'btnHuy', 'btnCopy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'chkLOAIDICHVU', 'txtTENDICHVUBHYT',
											'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU',
											'txtNGAYSD_GIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC',
											'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
											'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG',
											'btnTraKetQua', 'btnTraKetQuagoikham', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID',
											'chkKHOA', 'cboLOAIAPDUNGGIAMOI', 'txtTHUTUIN', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtLTPT', 'txtTEN_TT37',
											'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtSOLUONGNGUOI', 'cboTILE_PTTT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM',
											'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC',
											'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON',
											'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN',
											'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ]);
								} else {
									setEnabled([ "btnThem" ], [ 'btnSuaGia', 'btnSua', 'btnXoa', 'btnLuu', 'btnHuy', 'btnCopy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'chkLOAIDICHVU', 'txtTENDICHVUBHYT',
											'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU',
											'txtNGAYSD_GIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC',
											'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
											'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG',
											'btnTraKetQua', 'btnTraKetQuagoikham', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID',
											'chkKHOA', 'cboLOAIAPDUNGGIAMOI', 'txtTHUTUIN', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtLTPT', 'txtTEN_TT37',
											'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM',
											'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH',
											'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI',
											'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','txtTENDICHVUBHYTMOI',
										'chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'chkTAPDOAN_TT' ]);
								}
								checkRequiredsuagia = 0;
							}
						}
					}
					//ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514
				});
		$("#btnSua").click(
				function() {
					flagLoading = true;
					isEdit = true;
					isCopy = false;
					if (loainhomdv == 5 && _hospital_id == '915') {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN',
								'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID',
								'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
								'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG', 'txtQUYETDINH',
								'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT',
								'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'txtLTPT', 'cboLOAIAPDUNGGIAMOI', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE',
								'txtSOLUONGNGUOI', 'cboTILE_PTTT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP',
								'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI',
								'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9',
								'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ], [ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy',
								'btnTraKetQua', 'btnTraKetQuagoikham', 'chkLOAIDICHVU' ]);
					} else {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN',
								'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID',
								'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
								'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG', 'txtQUYETDINH',
								'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT',
								'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'txtLTPT', 'cboLOAIAPDUNGGIAMOI', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE',
								'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH',
								'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID',
								'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID',
								'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH', 'txtTENDICHVUBHYTMOI',
							'txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'chkTAPDOAN_TT' ], [ 'btnThem', 'btnSua', 'btnCopy', 'btnSuaGia', 'btnXoa', 'btnTraKetQua', 'btnTraKetQuagoikham','chkLOAIDICHVU' ]);
					}
					//ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514
					$("#txtMADICHVU").focus();
				});
		$("#btnSuaGia").click(
				function() {
					flagLoading = true;
					isEdit = true;
					isSuagia = true;
					isCopy = false;
					if (loainhomdv == 5 && _hospital_id == '915') {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN',
								'cboLOAIAPDUNGGIAMOI' ], [ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy', 'btnTraKetQua', 'btnTraKetQuagoikham', 'txtMADICHVU', 'cboNHOMDICHVUID',
								'chkLOAIDICHVU', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO',
								'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU',
								'chkLUONCHUYENDOITUONGHAOPHI', 'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN',
								'cboMADICHVU_NG', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN',
								'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtLTPT', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtSOLUONGNGUOI',
								'cboTILE_PTTT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI',
								'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE',
								'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9',
								'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ]);
					} else {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN',
								'cboLOAIAPDUNGGIAMOI' ], [ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy', 'btnTraKetQua', 'btnTraKetQuagoikham', 'txtMADICHVU', 'cboNHOMDICHVUID',
								'chkLOAIDICHVU', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO',
								'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU',
								'chkLUONCHUYENDOITUONGHAOPHI', 'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN',
								'cboMADICHVU_NG', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN',
								'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtLTPT', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtTHOIGIANCHIDINH',
								'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU',
								'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI',
								'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID',
								'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY', 'txtTENDICHVUBHYTMOI',
							'txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH' , 'chkTAPDOAN_TT']);
					}
					//ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514
					$("#txtGIABHYT").focus();
				});
		$("#btnXoa").click(function() {
			DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko ?", function(flag) {
				if (flag) {
					var rowKeys_select_xoa = $("#grdDanhSachDV").getGridParam('selarrrow');
					var par_xoa = [];
					for (var i = 0; i < rowKeys_select_xoa.length; i++) {
						var rowObject_xoa = $('#grdDanhSachDV').getRowData(rowKeys_select_xoa[i]);
						par_xoa.push(rowObject_xoa);
					}
					var param_select_xoa = JSON.stringify(par_xoa);
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMCXN.02", param_select_xoa);
					if (fl == 1) {
						DlgUtil.showMsg("Xóa thành công");
						loadGridData()
					} else if (fl == 0) {
						DlgUtil.showMsg("Bản nghi đã sử dụng, không được xóa");
					} else {
						DlgUtil.showMsg("Xảy ra lỗi", "Thông báo");
					}
					FormUtil.clearForm('inputForm', "");
					loadGridData();
					setEnabled([ 'btnThem' ], [ 'btnSua', 'btnXoa', 'btnHuy' ]);
				}
			});
		});
		$("#btnHuy")
				.click(
						function() {
							if (isEdit || isCopy)
								if (loainhomdv == 5 && _hospital_id == '915') {
									setEnabled([ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy', 'btnTraKetQua', 'btnTraKetQuagoikham' ], [ 'btnLuu', 'btnHuy', 'txtMADICHVU',
											'cboNHOMDICHVUID', 'chkLOAIDICHVU', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU',
											'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO',
											'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU',
											'chkLUONCHUYENDOITUONGHAOPHI', 'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN',
											'cboMADICHVU_NG', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN',
											'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtSOLUONGNGUOI',
											'cboTILE_PTTT', 'txtLTPT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 
											'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA',
											'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE',
											'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ]);
								} else {
									setEnabled([ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy', 'btnTraKetQua', 'btnTraKetQuagoikham' ], [ 'btnLuu', 'btnHuy', 'txtMADICHVU',
											'cboNHOMDICHVUID', 'chkLOAIDICHVU', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU',
											'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO',
											'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU',
											'chkLUONCHUYENDOITUONGHAOPHI', 'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN',
											'cboMADICHVU_NG', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN',
											'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE', 'txtLTPT',
											'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH',
											'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE',
											'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9',
											'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM', 'txtTENDICHVUBHYTMOI',
											'txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH' , 'chkTAPDOAN_TT']);
								}
							else {
								if (loainhomdv == 5 && _hospital_id == '915') {
									setEnabled([ 'btnThem' ], [ 'btnSua', 'btnSuaGia', 'btnXoa', 'btnLuu', 'btnHuy', 'btnCopy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'chkLOAIDICHVU', 'txtTENDICHVUBHYT',
											'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU',
											'txtNGAYSD_GIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC',
											'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
											'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG',
											'btnTraKetQua', 'btnTraKetQuagoikham', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID',
											'chkKHOA', 'txtTHUTUIN', 'txtLTPT', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI',
											'txtNOTE', 'txtSOLUONGNGUOI', 'cboTILE_PTTT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN',
											'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA',
											'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE',
											'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ]);
								} else {
									setEnabled([ 'btnThem' ], [ 'btnSua', 'btnSuaGia', 'btnXoa', 'btnLuu', 'btnHuy', 'btnCopy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'chkLOAIDICHVU', 'txtTENDICHVUBHYT',
											'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN', 'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtNGAYGIACU',
											'txtNGAYSD_GIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID', 'txtDICHVU_BHYT_DINHMUC',
											'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
											'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG',
											'btnTraKetQua', 'btnTraKetQuagoikham', 'txtQUYETDINH', 'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID',
											'chkKHOA', 'txtTHUTUIN', 'txtLTPT', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI',
											'txtNOTE', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI',
											'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI',
											'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC','txtTENDICHVUBHYTMOI',
											'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT',
										'chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH' , 'chkTAPDOAN_TT']);
								}
							}
							flagLoading = false;
							isSuagia = false;
							FormUtil.clearForm("inputForm", "");
							//ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514
						});
		$('#btnexport_DSDV').on("click", function(event) {
			var _sql_par = [ loainhomdv ];
			var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC01.EXPORT01", _sql_par.join('$'));
			//var mya_dv=JSON.stringify(mya);
			var excel_name = '';
			if (loainhomdv == 3) {
				excel_name = 'xetnghiem';
			} else if (loainhomdv == 4) {
				excel_name = 'cdha';
			} else if (loainhomdv == 5) {
				excel_name = 'pttt';
			} else
				excel_name = 'dsdv';
			//JSONToCSVConvertor(mya_dv, excel_name, true);
			var _DataType = {
				"DICHVUID" : "s",
				"MADICHVU" : "s",
				"TENDICHVU" : "s",
				"TEN_TT37" : "s",
				"NHOMDICHVUID" : "s",
				"MANHOMDICHVU" : "s",
				"MANHOM_BHYT" : "s",
				"TENNHOM_BHYT" : "s",
				"MADMBYT" : "s",
				"KHOANMUCID" : "s",
				"NHOMKETOANID" : "s",
				"TENNHOMKETOAN" : "s",
				"DONVI" : "s",
				"LOAI_GOI" : "s",
				"DVXHH" : "s",
				"DVKTCTINHLAIGIAPTTT" : "s",
				"BHYTTRATRAN" : "s",
				"GIABHYT" : "s",
				"NGAYSUDUNGGIABHYT" : "s",
				"GIAYEUCAU" : "s",
				"GIAVIENPHI" : "s",
				"GIAVIP" : "s",
				"GIANUOCNGOAI" : "s",
				"NGAYSUDUNGGIAAVIP" : "s",
				"QUYETDINH" : "s",
				"NGAYCONGBO" : "s",
				"STTMAU21" : "s",
				"MABYTMAU21" : "s",
				"LOAI" : "s",
				"MATT37" : "s",
				"MATT4350" : "s",
				"CHUYENKHOAID" : "s",
				"TENDICHVUBHYT" : "s",
				"TRANGTHAIDICHVU" : "s",
				"THOIGIANCHIDINH" : "s",
				"LOAITHANHTOAN" : "s",
				"TENCHUYENKHOA" : "s",
				"GHICHU" : "s",
				"PHONGTHUCHIEN" : "s",
				"KHOATHUCHIEN" : "s", // L2PT-29510 duonghn
				"LOAIPTTT" : "s",
				"KHOANGCACH" : "s",
				"NGAYCAPNHAT" : "s",
				"NGUOICAPNHAT" : "s",
				"NGAYTAO" : "s",
				"NGUOITAO" : "s",
				"GIACHENHLECH" : "s",
				"GIACHENHLECH_CU" : "s",
				"ID_LOAIMBP" : "s",
				"TEN_LOAIMBP" : "s",
				"XAHOIHOA" : "s",
				"TENDICHVUBHYTMOI" : "s"
			};
			exportXLSX(mya, _DataType, excel_name, excel_name + ".xlsx");
		});
		$('#btnCapNhatGiaNhieuDV').on(
				"click",
				function(event) {
					var par = {
						thamso1 : "123"
					};
					var dlgPopup = DlgUtil.buildPopupUrl("DMC01_UpdateGiaNhieuDV", "DMC01_UpdateGiaNhieuDV", "manager.jsp?func=../danhmuc/DMC01_UpdateGiaNhieuDV", par, "Cập nhật giá nhiều dịch vụ",
							1200, 600);
					dlgPopup.open();
				});
		$('#btnCopy').on(
				"click",
				function(event) {
					flagLoading = true;
					isCopy = true;
					isEdit = false;
					isSuagia = false;
					if (loainhomdv == 5 && _hospital_id == '915') {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN',
								'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID',
								'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
								'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG', 'txtQUYETDINH',
								'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT',
								'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'txtLTPT', 'cboLOAIAPDUNGGIAMOI', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE',
								'txtSOLUONGNGUOI', 'cboTILE_PTTT', 'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP',
								'chkNOISOI', 'txtGIACHENHLECH', 'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI',
								'chkKTINHTYLE', 'cboTKHDID', 'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9',
								'txtTEN_ICD9', 'chkXN_COVID', 'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY','txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'txtTENDICHVUBHYTMOI' ], [ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy',
								'btnTraKetQua', 'btnTraKetQuagoikham', 'chkLOAIDICHVU' ]);
					} else {
						setEnabled([ 'btnLuu', 'btnHuy', 'txtMADICHVU', 'cboNHOMDICHVUID', 'txtTENDICHVUBHYT', 'txtTENDICHVU', 'txtTENDICHVUNUOCNGOAI', 'txtDONVI', 'txtGIABHYT', 'txtGIANHANDAN',
								'txtGIADICHVU', 'txtGIANUOCNGOAI', 'txtGIAVIP_BDHN', 'txtBHYTQUYDOI', 'txtBHYTQUYDOI_TT', 'cboNHOM_MABHYT_ID', 'cboMANHOMBAOCAO', 'cboKHOANMUCID',
								'txtDICHVU_BHYT_DINHMUC', 'cboPTTTHANGID', 'txtPTTT_DINHMUCVTTH', 'txtPTTT_DINHMUCTHUOC', 'chkDVKTC', 'chkPHONGTHUCHIEN_LAYMAU', 'chkLUONCHUYENDOITUONGHAOPHI',
								'chkKHONGCHUYENDOITUONGHAOPHI', 'txtCDHA_SOLUONGTHUOC', 'txtCDHA_SOLUONGVATTU', 'txtTYLELAICHIDINH', 'txtTYLELAITHUCHIEN', 'cboMADICHVU_NG', 'txtQUYETDINH',
								'txtNGAYCONGBO', 'txtSTTMAU21', 'txtMABYTMAU21', 'txtMATT37', 'txtMATT4350', 'cboCHUYENKHOAID', 'cboLOAIKSKID', 'chkKHOA', 'txtTHUTUIN', 'txtKHOANGCACH', 'cboLOAIGOI', 'txtMADMBYT',
								'txtNGAYGIACU', 'txtNGAYSD_GIAVIP_BDHN', 'txtLTPT', 'cboLOAIAPDUNGGIAMOI', 'txtTENDICHVUNHANDAN', 'txtTEN_TT37', 'txtGIA_2016', 'chkTHUTIEN_THEOGOI', 'txtNOTE',
								'txtTHOIGIANCHIDINH', 'chkTACHPHIEU', 'txtPHUCAP', 'txtSOPHIM', 'cboPHANLOAI', 'cboMA_NHOM', 'cboGIO_SUATAN', 'txtGIAVIP', 'txtGIAGIUONGVIP', 'chkNOISOI', 'txtGIACHENHLECH',
								'txtGIACHENHLECH_CU', 'cboLOAIMAUBENHPHAM', 'chkKTC', 'chkHOANTHANH', 'cboNGUONCHUONGTRINH', 'chkKTBA_BOQUA', 'txtHANGPTTT_HAOPHI', 'chkKTINHTYLE', 'cboTKHDID',
								'chkPHOITHAI', 'chkXAHOIHOA', 'chkTACHHOADON', 'cboREPORT_CODE_KQ', 'txtGIA_THEO_GOI', 'MAMAY_NONE', 'chkGHEP_MA_THUOC', 'txtMA_ICD9', 'txtTEN_ICD9', 'chkXN_COVID',
								'txtCANHBAO_XUATTOAN', 'cboLOAI_LTPT', 'cboNGUONCHUONGTRINH_DM','chkPTTT_NHIEUGOI_TVT','txtCANHBAO_TGCHIDINH','chkCHENH_BHYT','chkKEDIKEM','txtTYLE_VAT','txtMODALITY', 'txtTENDICHVUBHYTMOI',
								'txtSL_NHANLUCCHINH','txtSL_NHANLUCPHU','txtSL_GIUPVIEC','txtSL_PHUME','txtSL_GAYMECHINH', 'chkTAPDOAN_TT' ], [ 'btnThem', 'btnSua', 'btnSuaGia', 'btnXoa', 'btnCopy', 'btnTraKetQua', 'btnTraKetQuagoikham',
								'chkLOAIDICHVU' ]);
					}
					//ductx -bvtm-6514
					if(_hospital_id == '10284'){
			        addRole();
					}
                    //end bvtm-6514
					$("#txtMADICHVU").focus();
		});
		//L2PT-15637
		$("#chkKHOA").change(function() {
			if(_hospital_id == '30360'){
				lydo_khoa_req = true;
			}
			
		});
	}
	function loadGridData() {
		if (flagLoading)
			return;
		/*var _sql_par = [ {
			"name" : "[0]",
			"value" : loainhomdv
		}, {
			"name" : "[1]",
			"value" : i_chuyenkhoaid
		}, {
			"name" : "[2]",
			"value" : i_nhomdichvuid
		} ];
		_sql_par = RSUtil.setSysParam(_sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridDSSoId, "DMC.03", _sql_par);*/
		//L2PT-8763
		var _sql_par=[];
		var objData = new Object();
		objData["LOAINHOMDV"] = loainhomdv + '';
		objData["CHUYENKHOAID"] = i_chuyenkhoaid + '';
		objData["NHOMDICHVUID"] = i_nhomdichvuid + '';
		objData["TRANGTHAITK"] = $("#cboTRANGTHAITK").val(); 
		
		_sql_par.push({"name":"[0]","value":objData})
		GridUtil.loadGridBySqlPage(_gridDSSoId,"DMC.03_V2",_sql_par);	
	}
	function loadGridDataCT(_nhomptId) {
		if (flagLoading)
			return;
		var _lookup_sql = "";
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			"value" : _nhomptId
		});
		_sql_par = RSUtil.setSysParam(_sql_par, _param);
	}
	function loadGridDataKQDV_DVGK() {
		if (flagLoading)
			return;
		var _sql_par_kqdv = [ {
			"name" : "[0]",
			"value" : _soptId
		}, {
			"name" : "[1]",
			"value" : loainhomdv
		} ];
		_sql_par_kqdv = RSUtil.setSysParam(_sql_par_kqdv, _param);
		GridUtil.loadGridBySqlPage("grdKQDV", "DMC.KQDVXN.1", _sql_par_kqdv);
		GridUtil.loadGridBySqlPage("grdDVDKGK", "DMC.CDDVGK.05", _sql_par_kqdv);
	}
	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
		}
	}
	function checkdulieu() {
		checkRequired = 0;
		var check_edit;
		if (isEdit) {
			check_edit = 1;
		} else {
			check_edit = 0;
		}
		if ($("#txtMADICHVU").val().trim() == "" || $("#txtMADICHVU").val().trim() == null) {
			checkRequired = 1;
			DlgUtil.showMsg("Bạn chưa nhập Mã dịch vụ!", function() {
				$("#txtMADICHVU").focus();
			});
		} else if ($("#cboNHOMDICHVUID").val() == "" || $("#cboNHOMDICHVUID").val() == null) {
			checkRequired = 1;
			DlgUtil.showMsg("Bạn chưa chọn mã nhóm!", function() {
				$("#cboNHOMDICHVUID").focus();
			});
		} else if ($("#cboNHOM_MABHYT_ID").val() == "" || $("#cboNHOM_MABHYT_ID").val() == null) {
			checkRequired = 1;
			DlgUtil.showMsg("Bạn chưa chọn nhóm BHYT!", function() {
				$("#cboNHOM_MABHYT_ID").focus();
			});
		} else if ($("#cboKHOANMUCID").val() == "" || $("#cboKHOANMUCID").val() == null) {
			checkRequired = 1;
			DlgUtil.showMsg("Bạn chưa chọn nhóm TK!", function() {
				$("#cboKHOANMUCID").focus();
			});
		} else if (jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.CODE.DMDV", $("#txtMADICHVU").val() + '$' + check_edit + '$' + _soptId) == 1) {
			checkRequired = 1;
			DlgUtil.showMsg("Mã DV này đã tồn tại!", function() {
				$("#txtMADICHVU").focus();
			});
		} else if (($("#txtGIABHYT").val().trim() == "" || $("#txtGIABHYT").val().trim() == 0) && ($("#txtDICHVU_BHYT_DINHMUC").val().trim() != "" && $("#txtDICHVU_BHYT_DINHMUC").val().trim() > 0)) {
			checkRequired = 1;
			DlgUtil.showMsg("Giá BHYT trả(trần) không hợp lệ so với giá BHYT!", function() {
				$("#txtDICHVU_BHYT_DINHMUC").focus();
			});
		} else if ($("#txtGIABHYT").val().trim() < $("#txtDICHVU_BHYT_DINHMUC").val().trim()) {
			checkRequired = 1;
			DlgUtil.showMsg("Giá BHYT trả(trần) không hợp lệ so với giá BHYT!", function() {
				$("#txtDICHVU_BHYT_DINHMUC").focus();
			});
		} else if (($("#txtGIABHYT").val().trim() != 0 && ($("#txtGIABHYT").val().trim() != "" || $("#txtGIABHYT").val().trim() != null)) &&
				(($("#txtMADMBYT").val().trim() == "" || $("#txtMADMBYT").val().trim() == null) || ($("#txtTENDICHVUBHYT").val().trim() == "" || $("#txtTENDICHVUBHYT").val().trim() == null) || ($(
						"#txtMABYTMAU21").val().trim() == "" || $("#txtMABYTMAU21").val().trim() == null))) {
			checkRequired = 1;
			DlgUtil.showMsg("Nếu dịch vụ kĩ thuật có giá BHYT thì phải bắt buộc khai báo Mã(DMBYT), Tên BHYT, Mã BYT mẫu 21!", function() {
				$("#txtGIABHYT").focus();
			});
		} else if (loainhomdv == 14 && ($("#txtMATT4350").val().trim() == "" || $("#txtMATT4350").val().trim() == null)) {
			checkRequired = 1;
			DlgUtil.showMsg("Bạn chưa nhập Mã TT 4350 cho dịch vụ vận chuyển này!", function() {
				$("#txtMATT4350").focus(); //L2PT-107327
			});
		}
	}
	function checkdulieusuagia() {
		checkRequiredsuagia = 0;
		if ($("#txtNGAYGIACU").val().trim() == "" || $("#txtNGAYGIACU").val().trim() == null) {
			checkRequiredsuagia = 1;
			DlgUtil.showMsg("Bạn chưa nhập Ngày sử dụng giá mới!", function() {
				$("#txtNGAYGIACU").focus();
			});
		}
	}
	function loadGridPhongDV(dsThemDV) {
		var sql_par = [];
		var fstThem;
		if (dsThemDV != null && dsThemDV.length != 0) {
			fstThem = dsThemDV[0].dichvuid;
		} else {
			fstThem = -1;
		}
		sql_par = [ {
			"name" : "[0]",
			"value" : fstThem
		} ];
		sql_par = RSUtil.setSysParam(sql_par, _param);
		GridUtil.loadGridBySqlPage('grdCHPhongDV', "DMC01.DSPHONGDV", sql_par);
	}
	
	//ductx -bvtm-6514
	function addRole(){
		    checkRole('btnSua');
		    checkRole('btnSuaGia');
		    checkRole('btnXoa');
		    checkRole('btnTraKetQua');
            checkRole('btnTraKetQuagoikham');
		    checkRole('btnLuu');
	}
	
	function checkRole(control){	
//		that.opt.ht
        var _parPQ = '';
        var sqlID = "";
        var chek = typeof _opts.type == 'undefined' ? '' : _opts.type;
        if(chek != ''){
	       _parPQ = 'DMC01_xetnghiem' +'%type='+ _opts.type + '$';
           sqlID = "DUC.PQSCREEN.03";
        }else{
	       _parPQ = 'DMC01_xetnghiem' +'%loainhomdv='+ _opts.loainhomdv + '$';
           sqlID = "DUC.PQSCREEN.BVBD"; 
        }

		  var result = jsonrpc.AjaxJson.ajaxCALL_SP_O(sqlID, _parPQ	);
          if(typeof result != 'undefined'){
	          for(var i =0; i< result.length ;i++){
		    	if(result[i].ELEMENT_ID == control){
		    	if (result[i].ROLES  =='1') 
			    	{
//						$('#'+result[i].ELEMENT_ID).show(); 
						$('#'+result[i].ELEMENT_ID).prop("disabled",false); 
		    		}
//		    	if (result[i].ROLES  =='0'||result[i].ROLES  =='') 
                else
			    	{
//						$('#'+result[i].ELEMENT_ID).hide();
						$('#'+result[i].ELEMENT_ID).prop("disabled",true); 
		    		}
		    	}	    
		    }
          }     
		   
	};
	//end bvtm-6514
	//-------------------start xuat excel---------------------------
	function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {
		//If JSONData is not an object then JSON.parse will parse the JSON string in an Object
		var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
		//var CSV = '';    
		//Set Report title in first row or line
		var CSV = "\uFEFF";
		CSV += ReportTitle + '\r\n\n';
		//This condition will generate the Label/Header
		if (ShowLabel) {
			var row = "";
			//This loop will extract the label from 1st index of on array
			for ( var index in arrData[0]) {
				//Now convert each value to string and comma-seprated
				row += index + ',';
			}
			row = row.slice(0, -1);
			//append Label row with line break
			CSV += row + '\r\n';
		}
		//1st loop is to extract each row
		for (var i = 0; i < arrData.length; i++) {
			var row = "";
			//2nd loop will extract each column and convert it in string comma-seprated
			for ( var index in arrData[i]) {
				row += '"' + arrData[i][index] + '",';
			}
			row.slice(0, row.length - 1);
			//add a line break after each row
			CSV += row + '\r\n';
		}
		if (CSV == '') {
			alert("Invalid data");
			return;
		}
		//Generate a file name
		var fileName = "";
		//this will remove the blank-spaces from the title and replace it with an underscore
		fileName += ReportTitle.replace(/ /g, "_");
		//Initialize file format you want csv or xls
		//var uri = 'data:text/csv;charset=utf-8,' + escape(CSV);
		var uri = 'data:text/csv;charset=utf-8,' + encodeURI(CSV);
		// Now the little tricky part.
		// you can use either>> window.open(uri);
		// but this will not work in some browsers
		// or you will not get the correct file extension    
		//this trick will generate a temp <a /> tag
		var link = document.createElement("a");
		link.href = uri;
		//set the visibility hidden so it will not effect on your web-layout
		link.style = "visibility:hidden";
		link.download = fileName + ".csv";
		//this part will append the anchor tag and remove it after automatic click
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
	//-------------------end xuat excel-----------------------
	function exportXLSX(_jsonObject, _DataType, _wsname, _filename) {
		var wb = new Workbook();
		var ws = objectToXLSX(_jsonObject, _DataType);
		/* add worksheet to workbook */
		wb.SheetNames.push(_wsname);
		wb.Sheets[_wsname] = ws;
		var wbout = XLSX.write(wb, {
			bookType : 'xlsx',
			bookSST : true,
			type : 'binary'
		});
//		download(s2ab(wbout), _filename, 'application/vnd.ms-excel', 'chkEXP_REPORT_20');
		saveAs(new Blob([ s2ab(wbout) ], {
			type : "application/octet-stream"
		}), _filename);
	}
	function s2ab(s) {
		var buf = new ArrayBuffer(s.length);
		var view = new Uint8Array(buf);
		for (var i = 0; i != s.length; ++i)
			view[i] = s.charCodeAt(i) & 0xFF;
		return buf;
	}
	function Workbook() {
		if (!(this instanceof Workbook))
			return new Workbook();
		this.SheetNames = [];
		this.Sheets = {};
	}
	objectToXLSX = function(_jsonObject, _DataType) {
		var ws = {};
		var row;
		var col;
		var xml;
		var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
		var range = {
			s : {
				c : 10000000,
				r : 10000000
			},
			e : {
				c : 0,
				r : 0
			}
		};
		var col_arr = [];
		if (_DataType) {
			col_arr = Object.keys(_DataType);
			for (row = 0; row <= data.length; row++) {
				for (var col = 0; col < col_arr.length; col++) {
					if (range.s.r > row)
						range.s.r = row;
					if (range.s.c > col)
						range.s.c = col;
					if (range.e.r < row)
						range.e.r = row;
					if (range.e.c < col)
						range.e.c = col;
					_col = col_arr[col];
					var cell = row > 0 ? {
						v : data[row - 1][_col]
					} : {
						v : _col
					};
					if (cell.v == null)
						continue;
					var cell_ref = XLSX.utils.encode_cell({
						c : col,
						r : row
					});
					if (row == 0)
						cell.t = 's';
					else
						cell.t = _DataType[_col];
					ws[cell_ref] = cell;
				}
			}
		} else if (data.length > 0) {
			col_arr = Object.keys(data[0]);
			for (row = 0; row <= data.length; row++) {
				for (var col = 0; col < col_arr.length; col++) {
					if (range.s.r > row)
						range.s.r = row;
					if (range.s.c > col)
						range.s.c = col;
					if (range.e.r < row)
						range.e.r = row;
					if (range.e.c < col)
						range.e.c = col;
					_col = col_arr[col];
					var cell = row > 0 ? {
						v : data[row - 1][_col]
					} : {
						v : _col
					};
					if (cell.v == null)
						continue;
					var cell_ref = XLSX.utils.encode_cell({
						c : col,
						r : row
					});
					if (row == 0) {
						cell.t = 's';
					} else if (typeof cell.v === 'number')
						cell.t = 'n';
					else if (typeof cell.v === 'boolean')
						cell.t = 'b';
					else if (cell.v instanceof Date) {
						cell.t = 'n';
						cell.z = XLSX.SSF._table[14];
						cell.v = datenum(cell.v);
					} else
						cell.t = 's';
					ws[cell_ref] = cell;
				}
			}
		}
		if (range.s.c < 10000000)
			ws['!ref'] = XLSX.utils.encode_range(range);
		return ws;
	};
}
