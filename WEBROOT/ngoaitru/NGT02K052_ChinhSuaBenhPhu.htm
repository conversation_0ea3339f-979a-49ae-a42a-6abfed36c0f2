<!--  
<PERSON><PERSON> màn hình  : NTU01H001
File mã nguồn : NTU01H001_PhongHanhChinh.js
<PERSON><PERSON><PERSON> đích  : <PERSON><PERSON> lý nghiệp vụ hành chính của nội trú 
Tham số vào :
<PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nhật  <PERSON> chú
HUNGNT	- 03092016 - Comment
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT02K052_ChinhSuaBenhPhu.js?v=002"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>

<div width="1100px" class="container">
	<div class="row form-inline mgt10" id="dvSOLUONGICD" style="display: none">
		<div class="col-xs-12 low-padding">
				<label class="mgl15" style="color: red"> Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH!</label>
		</div>
	</div>
	<div class="row form-inline mgt10">
		<div class="col-xs-12 low-padding">
			<div class="col-xs-3 low-padding">
				<label class="mgl15">Mã bệnh kèm theo</label>
			</div>
			<div class="col-xs-3 low-padding">
				<input class="form-control input-sm" id="txtMACHANDOANRAVIEN_KEMTHEO" style="width: 90%;">
			</div>
		</div>
	</div>
	<div class="row form-inline">
		<div class="mgl15 col-xs-12 low-padding">
			<table id="grdBP"></table>
		</div>
		<div class="col-xs-12 low-padding" id="showCDKTBD">
			<div class="col-xs-3 low-padding">
				<label class="mgl15">Chuẩn đoán kèm theo</label>
			</div>
			<div class="col-xs-9 low-padding">
				<textarea class="form-control i-col-full form-inline" rows="3" id="txtCHANDOAN_KEMTHEO_BD" style="width: 430px !important;"></textarea>
			</div>
		</div>
	</div>
	<div class=" col-md-12 low-padding slim_solid_line mgt10 mgb5">
		<div class="col-xs-4 low-padding"></div>
		<div class="col-xs-2 low-padding">
			<button class="btn btn-sm btn-primary" id="btnLuu">
				<span class="glyphicon glyphicon-floppy-remove"></span>
				Lưu
			</button>
		</div>
		<div class="col-xs-2 low-padding">
			<button class="btn btn-sm btn-primary" id="btnThoat">
				<span class="glyphicon glyphicon-remove-circle"></span>
				Đóng
			</button>
		</div>
	</div>
</div>
<script>
	var opt = [];
	var userInfo = CommonUtil.decode('{userData}');
	var paramInfo = CommonUtil.decode('{paramData}');
	var subdept_id = paramInfo.subdept_id;
	var dept_id = paramInfo.dept_id;
	var mode = '{showMode}';
	var _opts = new Object();
	_opts._deptId = dept_id;
	initRest(_opts._uuid);
	if (mode == 'dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		objVar = EventUtil.getVar("dlgVar");
		_opts.benhphu = objVar.benhphu;
		_opts.benhphu1 = objVar.benhphu1;
		_opts.chandoan_kt_bd = objVar.chandoan_kt_bd;
		_opts.type = objVar.type; //L2PT-19260
		_opts.benhchinh = objVar.benhchinh;
		_opts.yhct = objVar.yhct;
	}
	var bp = new ChinhSuaBenhPhu(_opts);
	bp.load();
</script>
