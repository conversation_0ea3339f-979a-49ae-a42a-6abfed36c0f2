function DataBDCD() {
	/*------ Thong tin chung -----*/
	this.soyte = '';
	this.ten_benhvien = '';
	this.khoa = '';
	/*------ Thong tin ca nhan benh nhan -----*/
	this.hoten = '';
	this.tuoi = '';
	this.sovaovien = '';
	this.lan_thai = '';
	this.lan_de = '';
	this.ngay_vao = '';
	this.gio_vao = '';
	this.oi_vo = '';
	this.gio_oi_vo = '';
	/*------ Thong tin phieu bieu do chuyen da -----*/
	this.id_bv_bieudochuyenda = '';
	this.id_benhan = '';
	this.khoang_theodoi = 4;
	this.giodo = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.mach = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.huyetap = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.nhietdo = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.protein = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.timthai = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.tinhtrang_oi = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.chongkhop = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.domo_ctc = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.ngoithai = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.conco_ctc = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
	this.dienbienkhac = '';
	$('#hidMoveX').val(0);
	var that = this;
}
function BieuDoChuyenDa(_data, opt) { //L2PT-34645 thêm biến opt để check ẩn/hiện các dl khác lưu vào data_json
	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "BDCD_FIX_CSYTID;NTU02D113_BIEUDOCHUYENDA_BS");
	if (data_ar != null && data_ar.length > 0) {
		var cf = data_ar[0];
		if (cf.BDCD_FIX_CSYTID != '0') {//HaNv_070524: L2PT-80604
			$('#hidHospitalId').val(cf.BDCD_FIX_CSYTID);
		}
	}
	if ($('#hidHospitalId').val() == 1007 || $('#hidHospitalId').val() == 1133) {
		$('#divFormIn_Oxytoci').show();
		$('#divFormSua_Oxytoci_Title').show();
		$('#divFormSua_Oxytoci').show();
		$('#divFormIn_GiotPhut').show();
		$('#divFormSua_GiotPhut_Title').show();
		$('#divFormSua_GiotPhut').show();
		$('#divFormIn_Thuoc').show();
		$('#divFormSua_Thuoc_Title').show();
		$('#divFormSua_Thuoc').show();
		$('#divFormIn_Protein').show();
		$('#divFormSua_Protein_Title').show();
		$('#divFormSua_Protein').show();
		$('#divFormIn_Axeton').show();
		$('#divFormSua_Axeton_Title').show();
		$('#divFormSua_Axeton').show();
		$('#divFormIn_Duong').show();
		$('#divFormSua_Duong_Title').show();
		$('#divFormSua_Duong').show();
		//L2PT-118717 sua ngoi thai = do lot, hide divFormIn_DoLot divFormSua_DoLot_Title divFormSua_DoLot
		//$('#divFormIn_DoLot').show();
		//$('#divFormSua_DoLot_Title').show();
		//$('#divFormSua_DoLot').show();
		$('#lblNgoiThai').html('Độ lọt');
		$('#divFormIn_Thuoc').detach().insertBefore($('#divFormIn_GiotPhut'));//HaNv_261023: L2PT-56736
	}
	//người theo dõi
	if (cf.NTU02D113_BIEUDOCHUYENDA_BS == '1') {
		$('#divFormSua_Nguoi_Title').show();
		if ($('#hidHospitalId').val() == 10284) {//HaNv_091223: L2PT-61993
			$('#lblNguoiTd').html('Người thực hiện');
		}
		$('#divFormSua_Nguoi').show();
		$('#divFormSua_NguoiTK').show();
	}
	if ($('#hidHospitalId').val() == 932) {//HaNv_080524: L2PT-81292 L2PT-90903 L2PT-96870
		$('#trTENNGUOI').css("height", "50px");
		$('#dulieu_dienbien_xutry').css("height", "150px");
		$('#td_dienbien').css("height", "150px");
		$('#txtDienBienXuTri2').css("height", "150px");
		$('#txtDienBienXuTri2').attr('rows', 2);
		$('#txtDienBienXuTri2').attr('class', '');
		$('#txt_btd').html('Bắt đầu theo dõi từ khi cổ tử cung mở 4cm');
	}
	this.data = _data;
	this.ajxUrl = '../Ajax.aspx/';
	var that = this;
	var cach = 5;
	var moveX = 0; //L2PT-81693: Tịnh tiến theo giá trị Độ mở CTC
	// In
	this.in_trang = function() {
		var panel = document.getElementById("print-page");
		var printWindow = window.open('', '', 'left=50000,top=50000,width=0,height=0');
		printWindow.document.write('<html moznomarginboxes mozdisallowselectionprint>');
		printWindow.document.write('<head><style type="text/css" media="print">@page {size: auto;margin: 5px 5px 5px 5px;}</style></head>');
		printWindow.document.write('<body >');
		printWindow.document.write(panel.innerHTML);
		printWindow.document.write('</body></html>');
		printWindow.document.close();
		setTimeout(function() {
			printWindow.print();
		}, 500);
		return false;
	}
	// Hiển thị thông báo
	this.showThongBao = function(loai, thongbao) {
		var _type = 'info';
		switch (loai) {
			case 1:
				_type = 'success';
			break;
			case 2:
				_type = 'danger';
			break;
			case 3:
				_type = 'warning';
			break;
			default:
				_type = 'info';
			break;
		}
		$("#notThongBao").attr('class', 'alert alert-' + _type + ' no-alert-styled-left');
		$('#notThongBao .text-semibold').html(thongbao);
		$('#notThongBao').show();
	}
	/* -------------------------------------- BIỂU ĐỒ CHUYỂN DẠ ----------------------------------- */
	// Fill thông tin chung
	this.fillThongTinChung = function() {
		$('#soyte').html(that.data.soyte);
		$('#ten_benhvien').html(that.data.ten_benhvien);
		$('#khoa').html(that.data.khoa);
	}
	// Fill thông tin bệnh nhân
	this.fillThongTinBenhNhan = function() {
		$('#ngaycapnhat').html(that.data.ngaycapnhat_chuoi); // L2PT-47772 duonghn
		$('#hoten').html(that.data.hoten);
		$('#tuoi').html(that.data.tuoi);
		$('#sovaovien').html(that.data.sovaovien);
		$('#lan_thai').html(that.data.lan_thai);
		$('#lan_de').html(that.data.lan_de);
		$('#para').html(that.data.para);//HaNv_091223: L2PT-61993
		$('#ngay_vao').html(that.data.ngay_vao);
		$('#gio_vao').html(that.data.gio_vao);
		$('#oi_vo').html(that.data.oi_vo == 'CON' ? 'Còn' : that.data.oi_vo == 'VO' ? 'Đã vỡ' : '');
		$('#gio_oi_vo').html(that.data.gio_oi_vo);
	}
	// Fill giờ trong ngày
	this.fill_cv_giotrongngay = function() {
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 30;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * 2;
		var _cvwidth = _lineWidth * 24 + 2;
		$("#cv_giotrongngay").attr('width', _cvwidth + cach);
		$("#cv_giotrongngay").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i < 3; _i++) {
			$('#cv_giotrongngay').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= 24; _i++) {
			$('#cv_giotrongngay').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
			$('#cv_giotrongngay').drawText({
				fillStyle : '#000',
				strokeStyle : '#000',
				strokeWidth : 1,
				fontSize : 11,
				fontFamily : 'Verdana, sans-serif',
				x : _i * _lineWidth + 14,
				y : _startY + 44,
				text : _i + 1
			});
		}
		// Điền giờ trong ngày
		var _giodo = that.data.giodo;
		// L2PT-69382 duonghn start
		_giodo_cp = chuyenMang(_giodo, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		_startCell = 1;
		for (var _i = 0; _i <= 24; _i++) {
			var _txtgio = '';
			if ($('#hidHospitalId').val() == 10284 || $('#hidHospitalId').val() == 121860) {//HaNv_091223: L2PT-61993 L2PT-115852
				if (_giodo_cp[_i]) {
					var _val = _giodo_cp[_i].split(':');
					_txtgio = _val[0] + 'h' + '\n' + _val[1];
				} else {
					_txtgio = '--';
				}
			} else if ($('#hidHospitalId').val() == 1007) {//L2PT-118717
				var _gio;
				if (_giodo_cp[_i]) {
					_gio = parseInt(_giodo_cp[_i]) - moveX;
				} else
					_gio = parseInt(_giodo_cp[8]) + 16 + _i - moveX;
				if (_gio >= 24) {
					_gio = _gio - 24;
				} else if (_gio < 0) {
					_gio = _gio + 24;
				}
				_txtgio = _gio + 'h';
			} else {
				var _gio = parseInt(_giodo_cp[_i]) - moveX;//HaNv_100424: L2PT-81693
				if (_gio > 24) {
					_gio = _gio - 24;
				} else if (_gio < 0) {
					_gio = _gio + 24;
				}
				_txtgio = !_giodo_cp[_i] ? '--' : _gio + 'h'; //L2PT-41758 thêm h và màu #25a
			}
			$('#cv_giotrongngay').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 9,
				fontFamily : 'Verdana, sans-serif',
				x : (_startCell + _i - 1) * _lineWidth + 12 + cach,
				y : _startY + 20,
				text : _txtgio
			});
		}
	}
	// Fill theo dõi người mẹ
	this.fill_cv_theodoime = function() {
		var _numLine = 6;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 24 + 2;
		$("#cv_theodoime").attr('width', _cvwidth + cach);
		$("#cv_theodoime").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cv_theodoime').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cv_theodoime').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
	}
	// Fill dữ liệu mạch
	this.fillMach = function() {
		var _mach = that.data.mach; // mạch
		var _huyetap = that.data.huyetap; // Huyết áp
		var _haThu = new Array();
		var _haTruong = new Array();
		//console.log(_huyetap);
		var _numLine = 6;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvWidth = _lineWidth * 24 + 2;
		var _lastMachX = 0;
		var _lastMachY = 0;
		var _startNum = 0;
		// L2PT-69382 duonghn start
		var objCP = new chuyenPha(that.data.domo_ctc);
		_mach_cp = chuyenMang(_mach, that.data.domo_ctc);
		_huyetap_cp = chuyenMang(_huyetap, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		for (var _i = 0; _i < 24; _i++) {
			// Vẽ biểu đồ mạch
			if (_mach_cp[_i] != null && _mach_cp[_i] != '') {
				var _xPointMach = (_i + moveX) * _lineWidth + 1 + cach;//(_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25 +10;  //L2PT-37193 +10
				var _yPointMach = (180 - _mach_cp[_i]) * (_cvHeight / (180 - 60));
				// Vẽ điểm mạch
				$('#cv_theodoime').drawEllipse({
					fillStyle : '#AA1233',
					x : _xPointMach,
					y : _yPointMach,
					width : 6,
					height : 6
				});
				// Điền mạch text
				$('#cv_theodoime').drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 7,
					fontFamily : 'Verdana, sans-serif',
					x : _xPointMach + (_i == 0 ? 9 : 0),
					y : _yPointMach + 8,
					text : _mach_cp[_i]
				});
				// Vẽ đường nối - mạch
				if (_i > 0 && _startNum > 0) {
					if (objCP.CHUYEN && _i == objCP.INDX_DIEMCUOI_CHUYEN) {
						$('#cv_theodoime').drawQuadratic({
							strokeStyle : '#03A147',
							strokeWidth : 2,
							x1 : _lastMachX + moveX,
							y1 : _lastMachY,
							cx1 : (_lastMachX + moveX + _xPointMach) / 2,
							cy1 : _lastMachY - (_xPointMach - _lastMachX) / 2,
							x2 : _xPointMach,
							y2 : _yPointMach,
							strokeDash : [ 5 ],
							strokeDashOffset : 0
						});
						$('#cv_theodoime').drawText({
							fillStyle : '#000000',
							strokeStyle : '#000000',
							strokeWidth : 1,
							fontSize : 10,
							fontFamily : 'Verdana, sans-serif',
							x : (_lastMachX + moveX + _xPointMach) / 2,
							y : _lastMachY - (_xPointMach - _lastMachX) / 3,
							text : 'chuyển'
						});
					} else {
						$('#cv_theodoime').drawLine({
							strokeStyle : '#AA1233',
							strokeWidth : 2,
							x1 : _lastMachX + moveX,
							y1 : _lastMachY,
							x2 : _xPointMach,
							y2 : _yPointMach
						});
					}
				}
				_lastMachX = _xPointMach;
				_lastMachY = _yPointMach;
				_startNum++;
			}
			// Tách huyết áp
			var _tmp = _huyetap_cp[_i];
			_tmp = _tmp.split('/');
			_haTruong[_i] = _tmp[0] ? _tmp[0] : '';
			_haThu[_i] = _tmp[1] ? _tmp[1] : '';
		}
		// Vẽ huyết áp
		for (var _i = 0; _i < 24; _i++) {
			// Huyết áp trương
			var _xPointHaTruong = (_i + moveX) * _lineWidth + 1 + cach;//(_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25 +10;  //L2PT-37193 +10
			var _yPointHaTruong = (180 - _haTruong[_i]) * (_cvHeight / (180 - 60));
			$('#cv_theodoime').drawText({
				fillStyle : '#FF1B4D',
				strokeStyle : '#FF1B4D',
				strokeWidth : 1,
				fontSize : 11,
				fontFamily : 'Verdana, sans-serif',
				x : _xPointHaTruong,
				y : _yPointHaTruong,
				text : '^'
			});
			$('#cv_theodoime').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 7,
				fontFamily : 'Verdana, sans-serif',
				x : _xPointHaTruong + 12,
				y : _yPointHaTruong,
				text : _haTruong[_i]
			});
			// Huyết áp thu
			var _xPointHaThu = (_i + moveX) * _lineWidth + 1 + cach;//(_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25 +10;  //L2PT-37193 +10
			var _yPointHaThu = (180 - _haThu[_i]) * (_cvHeight / (180 - 60));
			$('#cv_theodoime').drawText({
				fillStyle : '#FF1B4D',
				strokeStyle : '#FF1B4D',
				strokeWidth : 1,
				fontSize : 11,
				fontFamily : 'Verdana, sans-serif',
				x : _xPointHaThu,
				y : _yPointHaThu,
				text : 'v'
			});
			$('#cv_theodoime').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 7,
				fontFamily : 'Verdana, sans-serif',
				x : _xPointHaThu + 12,
				y : _yPointHaThu,
				text : _haThu[_i]
			});
			// Vẽ dải huyết áp
			$('#cv_theodoime').drawLine({
				strokeStyle : '#095519',
				strokeWidth : 2,
				x1 : _xPointHaTruong,
				y1 : _yPointHaTruong,
				x2 : _xPointHaThu,
				y2 : _yPointHaThu
			});
		}
	}
	// Fill nhiệt độ
	this.fill_cv_nhietdo = function() {
		var _numLine = 1;
		var _numCol = 6;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23 * 4;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 6 + 2;
		$("#cv_nhietdo").attr('width', _cvwidth + cach);
		$("#cv_nhietdo").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cv_nhietdo').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cv_nhietdo').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
	}
	// Fill dữ liệu nhiệt độ
	this.fillNhietDo = function() {
		var _nhietdo = that.data.nhietdo; // dữ liệu nhiệt độ
		//console.log(_huyetap);
		var _numLine = 6;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvWidth = _lineWidth * 24 + 2;
		var _lastMachX = 0;
		var _lastMachY = 0;
		//console.log(_nhietdo);
		// L2PT-69382 duonghn start
		_nhietdo_cp = chuyenMang(_nhietdo, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		for (var _i = 0; _i < 24; _i++) {
			// Điền nhiệt độ
			if (_nhietdo_cp[_i] != null && _nhietdo_cp[_i] != '') {
				var _xPoint = (_i + moveX) * _lineWidth + 1 + 12;
				var _yPoint = _startY + 10;
				$('#cv_nhietdo').drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 9,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint + cach,
					y : _yPoint,
					text : _nhietdo_cp[_i]
				});
			}
		}
	}
	// Fill protein nước tiểu
	this.fillCvProteinNuocTieu = function() {
		var _protein = that.data.protein; // dữ liệu protein
		var _numLine = 1;
		var _numCol = 6;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23 * 4;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 6 + 2;
		$("#cvProteinNuocTieu").attr('width', _cvwidth + cach);
		$("#cvProteinNuocTieu").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvProteinNuocTieu').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvProteinNuocTieu').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
		// Fill dữ liệu
		// L2PT-69382 duonghn start
		_protein_cp = chuyenMang(_protein, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		var _lineWidth = 23;
		//console.log(_nhietdo);
		for (var _i = 0; _i < 24; _i++) {
			// Điền nhiệt độ
			if (_protein_cp[_i] != null && _protein_cp[_i] != '') {
				var _xPoint = (_i + moveX) * _lineWidth + 1 + 12 + cach;
				var _yPoint = _startY + 10;
				$('#cvProteinNuocTieu').drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 9,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint,
					y : _yPoint,
					text : _protein_cp[_i]
				});
			}
		}
	}
	// Fill nhịp tim thai
	this.fillCvNhipTimThai = function() {
		var _timthai = that.data.timthai; // dữ liệu timthai
		var _numLine = 4;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvWidth = _lineWidth * 24 + 2;
		$("#cvNhipTimThai").attr('width', _cvWidth + cach);
		$("#cvNhipTimThai").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvNhipTimThai').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvNhipTimThai').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
		// Fill dữ liệu
		var _lastX = 0;
		var _lastY = 0;
		var _startNum = 0;
		// L2PT-69382 duonghn start
		var objCP = new chuyenPha(that.data.domo_ctc);
		_timthai_cp = chuyenMang(_timthai, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		for (var _i = 0; _i < 24; _i++) {
			if (_timthai_cp[_i] != null && _timthai_cp[_i] != '') {
				var _xPoint = (_i + moveX) * _lineWidth + 1 + cach;//(_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25+cach;
				var _yPoint = (180 - _timthai_cp[_i]) * (_cvHeight / (180 - 100)) - 10;//L2PT-LOI Tim thai đang hiển thị sai dòng(thêm -10)
				// Vẽ điểm mạch
				$('#cvNhipTimThai').drawEllipse({
					fillStyle : '#5A8300',
					x : _xPoint,
					y : _yPoint + 10,
					width : 7,
					height : 7
				});
				// Vẽ đường nối - mạch
				if (_i > 0 && _startNum > 0) {
					var strokeDash = 0;
					if (objCP.CHUYEN && _i == objCP.INDX_DIEMCUOI_CHUYEN) {
						$('#cvNhipTimThai').drawQuadratic({
							strokeStyle : '#03A147',
							strokeWidth : 2,
							x1 : _lastX,
							y1 : _lastY + 10,
							cx1 : (_lastX + _xPoint) / 2,
							cy1 : _lastY + 10 - (_xPoint - _lastX) / 2,
							x2 : _xPoint,
							y2 : _yPoint + 10,
							strokeDash : [ 5 ],
							strokeDashOffset : 0
						});
						$('#cvNhipTimThai').drawText({
							fillStyle : '#000000',
							strokeStyle : '#000000',
							strokeWidth : 1,
							fontSize : 10,
							fontFamily : 'Verdana, sans-serif',
							x : (_lastX + _xPoint) / 2,
							y : _lastY + 10 - (_xPoint - _lastX) / 3,
							text : 'chuyển'
						});
					} else {
						$('#cvNhipTimThai').drawLine({
							strokeStyle : '#AA1233',
							strokeWidth : 2,
							x1 : _lastX,
							y1 : _lastY + 10,
							x2 : _xPoint,
							y2 : _yPoint + 10
						});
					}
				}
				// Điền mạch text
				$('#cvNhipTimThai').drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 8,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint,
					y : _yPoint - 15 + 15,
					text : _timthai_cp[_i],//L2PT-LOI Tim thai đang hiển thị sai dòng(thêm +15)
					fromCenter : false
				});
				_lastX = _xPoint;
				_lastY = _yPoint;
				_startNum++;
			}
		}
	}
	// Fill tình trạng ối, chống khớp
	this.fillCvOiChongKhop = function() {
		var _tinhtrang_oi = that.data.tinhtrang_oi;
		var _chongkhop = that.data.chongkhop;
		var _numLine = 2;
		var _numCol = 6;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23 * 4;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 6 + 2;
		$("#cvOiChongKhop").attr('width', _cvwidth + cach);
		$("#cvOiChongKhop").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvOiChongKhop').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvOiChongKhop').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
		// Fill dữ liệu tình trạng ối
		var _lineWidth = 23;
		// Fill tình trạng ối
		// L2PT-69382 duonghn start
		_tinhtrang_oi_cp = chuyenMang(_tinhtrang_oi, that.data.domo_ctc);
		_chongkhop_cp = chuyenMang(_chongkhop, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		for (var _i = 0; _i < 24; _i++) {
			// Điền nhiệt độ
			//if (_tinhtrang_oi[_i] != null && _tinhtrang_oi[_i] != '' && _chongkhop[_i] != null && _chongkhop[_i] != '') {
			if (_tinhtrang_oi_cp[_i] != null && _tinhtrang_oi_cp[_i] != '') {//L2PT-LOI hiển thị Ối và Chống khớp ko phụ thuộc vào nhau
				var _xPoint = (_i + moveX) * _lineWidth + 1 + 12 + cach;
				var _yPoint = _startY + 10;
				$('#cvOiChongKhop').drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 9,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint,
					y : _yPoint,
					text : _tinhtrang_oi_cp[_i]
				});
			}
			if (_chongkhop_cp[_i] != null && _chongkhop_cp[_i] != '') {//L2PT-LOI hiển thị Ối và Chống khớp ko phụ thuộc vào nhau
				$('#cvOiChongKhop').drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 9,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint,
					y : _yPoint + 20,
					text : _chongkhop_cp[_i]
				});
			}
		}
	}
	// Fill giờ trong ngày 2
	this.fillCvGioTrongNgay2 = function() {
		var _numLine = 1;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 24 + 2;
		$("#cvGioTrongNgay2").attr('width', _cvwidth + cach);
		$("#cvGioTrongNgay2").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvGioTrongNgay2').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvGioTrongNgay2').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
			if (_i == 3 || _i == 7 || _i == 11 || _i == 15 || _i == 19 || _i == 23) {
				$('#cvGioTrongNgay2').drawText({
					fillStyle : '#000',
					strokeStyle : '#000',
					strokeWidth : 1,
					fontSize : 11,
					fontFamily : 'Verdana, sans-serif',
					x : _i * _lineWidth + 14 + cach,
					y : _startY + 12,
					text : _i + 1
				});
			}
		}
	}
	this.fillDoMoCTC = function() {
		var _domo_ctc = that.data.domo_ctc;
		var _ngoithai = that.data.ngoithai;
		var _numLine = 10;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 24 + 2;
		$("#cvDoMoCTC").attr('width', _cvwidth + cach);
		$("#cvDoMoCTC").attr('height', _cvHeight);
		// Validate
		if (_domo_ctc.length == 0) {
			console.log('#WARNING: Data empty!');
			return;
		}
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
		// Fill pha tiềm tàng - pha tích cực
		if ($('#hidHospitalId').val() == 28880) {//HaNv_280324: L2PT-79631
			// Pha tích cực
			$('#cvDoMoCTC').drawRect({
				fillStyle : '#FFF',
				x : _startX + 25 + cach,
				y : _lineHeight - 19,
				width : 113,
				height : 18,
				fromCenter : false
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 9,
				fontFamily : 'Verdana, sans-serif',
				x : _startX + 70 + cach,
				y : _lineHeight - 10,
				text : 'PHA TÍCH CỰC '
			});
			//Đường dọc tô đậm đen
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#333333',
				strokeWidth : 2,
				x1 : 0 + cach,
				y1 : 0,
				x2 : 0 + cach,
				y2 : _numLine * _lineHeight,
			});
			// Đường báo động
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#25a',
				strokeWidth : 2,
				x1 : 160 + cach,
				y1 : 0,
				x2 : 0 + cach,
				y2 : 140,
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 8,
				fontFamily : 'Verdana, sans-serif',
				x : 100 + cach,
				y : 25,
				text : 'ĐƯỜNG BÁO ĐỘNG',
				fromCenter : false,
				rotate : -42
			});
			// Đường hành động
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#25a',
				strokeWidth : 2,
				x1 : 255 + cach,
				y1 : 0,
				x2 : 95 + cach,
				y2 : 140
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 8,
				fontFamily : 'Verdana, sans-serif',
				x : 195 + cach,
				y : 25,
				text : 'ĐƯỜNG HÀNH ĐỘNG',
				fromCenter : false,
				rotate : -42
			});
		} else {
			// Pha tiềm tàng
			$('#cvDoMoCTC').drawRect({
				fillStyle : '#FFF',
				x : _startX + 25 + cach,
				y : _lineHeight - 19,
				width : 113,
				height : 18,
				fromCenter : false
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 9,
				fontFamily : 'Verdana, sans-serif',
				x : _startX + 70 + cach,
				y : _lineHeight - 10,
				text : 'PHA TIỀM TÀNG'
			});
			// Pha tích cực
			$('#cvDoMoCTC').drawRect({
				fillStyle : '#FFF',
				x : _startX + 215 + cach,
				y : _lineHeight - 19,
				width : 113,
				height : 18,
				fromCenter : false
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 9,
				fontFamily : 'Verdana, sans-serif',
				x : _startX + 275 + cach,
				y : _lineHeight - 10,
				text : 'PHA TÍCH CỰC '
			});
			//Đường ngang tô đậm đen
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#333333',
				strokeWidth : 2,
				x1 : 0 + cach,
				y1 : $('#hidHospitalId').val() == 932 ? 120 : 140, //L2PT-96870
				x2 : 185 + cach,
				y2 : $('#hidHospitalId').val() == 932 ? 120 : 140, //L2PT-96870
			});
			//Đường dọc tô đậm đen
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#333333',
				strokeWidth : 2,
				x1 : 185 + cach,
				y1 : 0,
				x2 : 185 + cach,
				y2 : _numLine * _lineHeight,
			});
			// Đường báo động
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#25a',
				strokeWidth : 2,
				x1 : 345 + cach,
				y1 : 0,
				x2 : 185 + cach,
				y2 : $('#hidHospitalId').val() == 932 ? 120 : 140, //L2PT-96870
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 8,
				fontFamily : 'Verdana, sans-serif',
				x : 285 + cach,
				y : 25,
				text : 'ĐƯỜNG BÁO ĐỘNG',
				fromCenter : false,
				rotate : -42
			});
			// Đường hành động
			$('#cvDoMoCTC').drawLine({
				strokeStyle : '#25a',
				strokeWidth : 2,
				x1 : 440 + cach,
				y1 : 0,
				x2 : 280 + cach,
				y2 : $('#hidHospitalId').val() == 932 ? 120 : 140
			//L2PT-96870
			});
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 8,
				fontFamily : 'Verdana, sans-serif',
				x : 380 + cach,
				y : 25,
				text : 'ĐƯỜNG HÀNH ĐỘNG',
				fromCenter : false,
				rotate : -42
			});
		}
		if ($('#hidHospitalId').val() == 10284) {//HaNv_091223: L2PT-61993
			var words = that.data.ghichu;
			var ghichu = '';
			var maxlength = 75;
			var line = 1;
			for (var n = 0; n < words.length; n++) {
				if (n < maxlength * line) {
					ghichu = ghichu + words[n];
				} else {
					ghichu = ghichu + '\n' + words[n];
					line = line + 1;
				}
			}
			$('#cvDoMoCTC').drawText({
				fillStyle : '#25a',
				strokeStyle : '#25a',
				strokeWidth : 1,
				fontSize : 8,
				fontFamily : 'Verdana, sans-serif',
				x : 200 + cach,
				y : 150,
				text : ghichu,
				fromCenter : false,
				textAlign : 'left',
			});
		}
		// Fill dữ liệu
		var Co_Chuyen = false;
		var bat_dau_chuyen = -1;
		for (var _i = 23; _i >= 0; _i--)
			if (_domo_ctc[_i] != null && _domo_ctc[_i].trim() != '') {
				if (parseInt(_domo_ctc[_i]) > 3)
					bat_dau_chuyen = _i;
				else if (parseInt(_domo_ctc[_i]) == 3) {
					bat_dau_chuyen = _i;
					break;
				}
			}
		var diem_cuoi_chuyen = 0;
		if ($('#hidHospitalId').val() != 28880) {//HaNv_280324: L2PT-79631
			if (bat_dau_chuyen > -1) {
				diem_cuoi_chuyen = 8 + (parseInt(_domo_ctc[bat_dau_chuyen]) - 3);
				Co_Chuyen = true;
				if ($('#hidHospitalId').val() == 1007)
					Co_Chuyen = false; //L2PT-118717
			} else {
				bat_dau_chuyen = 0;
			}
		} else {//HaNv_100424: L2PT-81693
			if (bat_dau_chuyen > -1) {
				diem_cuoi_chuyen = parseInt(_domo_ctc[bat_dau_chuyen]) - 3;
				moveX = diem_cuoi_chuyen;
				$('#hidMoveX').val(moveX);
				Co_Chuyen = false;
			} else {
				bat_dau_chuyen = 0;
			}
		}
		var _xPoint = diem_cuoi_chuyen * _lineWidth + 1 + cach;
		//Vẽ từ điểm chuyển sang pha tích cực (trong cả trường hợp ko có điểm chuyển)
		var _lastX = -1;
		var _lastY = 0;
		var _lastX_Ngoithai = -1;
		var _lastY_Ngoithai = 0;
		for (var _i = bat_dau_chuyen; _i < 24; _i++) {
			if (_domo_ctc[_i] != null && _domo_ctc[_i].trim() != '') {
				var _yPoint = (10 - _domo_ctc[_i]) * (_cvHeight / 10);
				// Vẽ độ mở
				$('#cvDoMoCTC').drawText({
					fillStyle : '#03A147',
					strokeStyle : '#03A147',
					strokeWidth : 1,
					fontSize : 10,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint,
					y : _yPoint,
					text : 'X'
				});
				$('#cvDoMoCTC').drawText({
					fillStyle : '#03A147',
					strokeStyle : '#03A147',
					strokeWidth : 1,
					fontSize : 8,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint + 15,
					y : _yPoint + 5,
					text : _domo_ctc[_i] + ' cm'
				});
				if (_lastX > -1)// Vẽ đường nối
					$('#cvDoMoCTC').drawLine({
						strokeStyle : '#03A147',
						strokeWidth : 2,
						x1 : _lastX,
						y1 : _lastY,
						x2 : _xPoint,
						y2 : _yPoint
					});
				_lastX = _xPoint;
				_lastY = _yPoint;
			}
			if (_ngoithai[_i] != null && _ngoithai[_i] != '') {
				var _yPoint = (10 - _ngoithai[_i]) * (_cvHeight / 10);
				// Vẽ ngôi thai
				$('#cvDoMoCTC').drawText({
					fillStyle : '#AA1233',
					strokeStyle : '#AA1233',
					strokeWidth : 1,
					fontSize : 10,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint,
					y : _yPoint,
					text : 'O'
				});
				$('#cvDoMoCTC').drawText({
					fillStyle : '#AA1233',
					strokeStyle : '#AA1233',
					strokeWidth : 1,
					fontSize : 8,
					fontFamily : 'Verdana, sans-serif',
					x : _xPoint + 8,
					y : _yPoint + 8,
					text : _ngoithai[_i] + ''
				});
				if (_lastX_Ngoithai > -1)// Vẽ đường nối
					$('#cvDoMoCTC').drawLine({
						strokeStyle : '#AA1233',
						strokeWidth : 1,
						x1 : _lastX_Ngoithai,
						y1 : _lastY_Ngoithai,
						x2 : _xPoint,
						y2 : _yPoint
					});
				_lastX_Ngoithai = _xPoint;
				_lastY_Ngoithai = _yPoint;
			}
			// tăng
			_xPoint = _xPoint + _lineWidth;
		}
		if (Co_Chuyen) {
			//Vẽ từ điểm đầu đến điểm chuyển
			var _tu = 0;
			var _den = bat_dau_chuyen;
			if (bat_dau_chuyen > 8)
				_tu = bat_dau_chuyen - 8;
			var _xPoint = _tu * _lineWidth + 1 + cach;
			var _lastX = -1;
			var _lastY = 0;
			var _lastX_Ngoithai = -1;
			var _lastY_Ngoithai = 0;
			for (var _i = _tu; _i <= _den; _i++) {
				if (_domo_ctc[_i] != null && _domo_ctc[_i].trim() != '') {
					var _yPoint = (10 - _domo_ctc[_i]) * (_cvHeight / 10);
					// Vẽ độ mở
					$('#cvDoMoCTC').drawText({
						fillStyle : '#03A147',
						strokeStyle : '#03A147',
						strokeWidth : 1,
						fontSize : 10,
						fontFamily : 'Verdana, sans-serif',
						x : _xPoint,
						y : _yPoint,
						text : 'X'
					});
					$('#cvDoMoCTC').drawText({
						fillStyle : '#03A147',
						strokeStyle : '#03A147',
						strokeWidth : 1,
						fontSize : 9,
						fontFamily : 'Verdana, sans-serif',
						x : _xPoint + 15,
						y : _yPoint + 5,
						text : _domo_ctc[_i] + ' cm'
					});
					if (_lastX > -1)// Vẽ đường nối
						$('#cvDoMoCTC').drawLine({
							strokeStyle : '#03A147',
							strokeWidth : 2,
							x1 : _lastX,
							y1 : _lastY,
							x2 : _xPoint,
							y2 : _yPoint
						});
					_lastX = _xPoint;
					_lastY = _yPoint;
				}
				if (_ngoithai[_i] != null && _ngoithai[_i] != '') {
					var _yPoint = (10 - _ngoithai[_i]) * (_cvHeight / 10);
					// Vẽ ngôi thai
					$('#cvDoMoCTC').drawText({
						fillStyle : '#AA1233',
						strokeStyle : '#AA1233',
						strokeWidth : 1,
						fontSize : 10,
						fontFamily : 'Verdana, sans-serif',
						x : _xPoint,
						y : _yPoint,
						text : 'O'
					});
					$('#cvDoMoCTC').drawText({
						fillStyle : '#AA1233',
						strokeStyle : '#AA1233',
						strokeWidth : 1,
						fontSize : 8,
						fontFamily : 'Verdana, sans-serif',
						x : _xPoint + 8,
						y : _yPoint + 8,
						text : _ngoithai[_i] + ''
					});
					if (_lastX_Ngoithai > -1)// Vẽ đường nối
						$('#cvDoMoCTC').drawLine({
							strokeStyle : '#AA1233',
							strokeWidth : 1,
							x1 : _lastX_Ngoithai,
							y1 : _lastY_Ngoithai,
							x2 : _xPoint,
							y2 : _yPoint
						});
					_lastX_Ngoithai = _xPoint;
					_lastY_Ngoithai = _yPoint;
				}
				// tăng
				_xPoint = _xPoint + _lineWidth;
			}
			//Vẽ nét đứt chuyển
			if (bat_dau_chuyen < 8 && diem_cuoi_chuyen > bat_dau_chuyen) {
				var _xPoint = bat_dau_chuyen * _lineWidth + 1 + cach;
				var _yPoint = (10 - _domo_ctc[bat_dau_chuyen]) * (_cvHeight / 10);
				var _yThem = 5;
				var _xThem = _lineWidth / 3;
				var _max = (diem_cuoi_chuyen - bat_dau_chuyen) * 3;
				for (var i = 0; i <= _max; i++) {
					$('#cvDoMoCTC').drawText({
						fillStyle : '#03A147',
						strokeStyle : '#03A147',
						strokeWidth : 2,
						fontSize : 10,
						fontFamily : '',
						x : _xPoint,
						y : _yPoint,
						text : '.'
					});
					if (i == Math.round(_max / 2))
						$('#cvDoMoCTC').drawText({
							fillStyle : '#000000',
							strokeStyle : '#000000',
							strokeWidth : 1,
							fontSize : 10,
							fontFamily : 'Verdana, sans-serif',
							x : _xPoint,
							y : _yPoint - 6,
							text : 'chuyển'
						});
					_xPoint = _xPoint + _xThem;
					_yPoint = _yPoint - Math.cos((Math.PI) * (i / _max)) * 5;
				}
			}
		}
	}
	// Fill cơn co CTC
	this.fillConCoCTC = function() {
		var _conco_ctc = that.data.conco_ctc;
		var _numLine = 5;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 100;
		var _lineHeight = 20;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 24 + 2;
		$("#cvConCoCTC").attr('width', _cvwidth + cach);
		$("#cvConCoCTC").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvConCoCTC').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvConCoCTC').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
		// Fill dữ liệu
		// L2PT-69382 duonghn start
		_conco_ctc_cp = chuyenMang(_conco_ctc, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		for (var _i = 0; _i < 24; _i++) {
			if (_conco_ctc_cp[_i] != null && _conco_ctc_cp[_i] != '') {
				var _xPoint = (_cvwidth / 24) * (_i + moveX) - (_cvwidth / (24 * 2)) + 25 + cach;
				var _yPoint = 0;
				// Tách chỉ số
				var _tmp = _conco_ctc_cp[_i];
				_tmp = _tmp.split('-');
				_conco = _tmp[0] ? _tmp[0] : '';
				_loaico = _tmp[1] ? _tmp[1] : '';
				if ($('#hidHospitalId').val() == 28880) {//HaNv_110324: L2PT-75635	
					_loaico = (_loaico >= 3) ? 3 : _loaico;
				}
				//console.log('Thời gian: ' + _i + 'h; Số con co: ' + _conco + '; Loại co: ' + _loaico);
				for (var j = 0; j < _conco; j++) {
					if (_loaico != '') {//L2PT-LOI sửa lỗi khi ko có file png (_loaico rỗng)
						$('#cvConCoCTC').drawImage({
							source : '../common/image/NTU02D113_BieuDoChuyenDa/conco_ctc_' + _loaico + '.png',
							fromCenter : false,
							x : _xPoint - 13,
							y : 100 - (_yPoint + j * _lineHeight) - 20
						});
					}
				}
			}
		}
	}
	// Fill diễn biến khác & xử trí
	this.fillCvDienBienXuTri = function() {
		var _dienbienkhac = that.data.dienbienkhac;
		var _numLine = 1;
		var _numCol = 1;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 50;
		var _lineWidth = 23 * 24;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth + 2;
		$("#cvDienBienXuTri").attr('width', _cvwidth + cach);
		$("#cvDienBienXuTri").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvDienBienXuTri').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvDienBienXuTri').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
		// Fill dữ liệu
		// L2PT-69382 duonghn start
		_dienbienkhac_cp = chuyenMang(_dienbienkhac, that.data.domo_ctc);
		// L2PT-69382 duonghn end
		$('#cvDienBienXuTri').drawText({
			fillStyle : '#25a',
			strokeStyle : '#25a',
			strokeWidth : 1,
			fontSize : 10,
			fontFamily : 'Verdana, sans-serif',
			x : 10 + cach,
			y : 10,
			text : _dienbienkhac_cp,
			fromCenter : false
		});
	}
	// Fill người theo dõi
	this.fillCvNguoiTheoDoi = function() {
		var _numLine = 1;
		var _numCol = 24;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 644;
		var _stopY = 380;
		var _lineHeight = 40;
		var _lineWidth = 23;
		var _cvHeight = _lineHeight * _numLine;
		var _cvwidth = _lineWidth * 24 + 2;
		$("#cvNguoiTheoDoi").attr('width', _cvwidth + cach);
		$("#cvNguoiTheoDoi").attr('height', _cvHeight);
		// Fill dòng
		for (var _i = 0; _i <= _numLine; _i++) {
			$('#cvNguoiTheoDoi').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1.5,
				x1 : _startX + 2 + cach,
				y1 : _i * _lineHeight,
				x2 : _stopX + cach,
				y2 : _i * _lineHeight
			});
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			$('#cvNguoiTheoDoi').drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1 + cach,
				y1 : _startY,
				x2 : _i * _lineWidth + 1 + cach,
				y2 : _stopY - 2
			});
		}
	}
	// Fill data
	this.fillData = function() {
		that.fillThongTinChung();
		that.fillThongTinBenhNhan();
		if ($('#hidHospitalId').val() == 28880) {//HaNv_280324: L2PT-79631
			$('#dulieu_giotrongngay_2').remove();
			$('#lblngay_vao').html('......; GIỜ THEO DÕI:');
			$('#lblgiochuyenda').html('Giờ theo dõi: ');
			that.fillDoMoCTC();
			that.fill_cv_giotrongngay();
			that.fill_cv_theodoime();
			that.fill_cv_nhietdo();
			that.fillNhietDo();
			that.fillCvProteinNuocTieu();
			that.fillCvNhipTimThai();
			that.fillCvOiChongKhop();
			that.fillConCoCTC();
		} else {
			that.fill_cv_giotrongngay();
			that.fill_cv_theodoime();
			that.fill_cv_nhietdo();
			that.fillNhietDo();
			that.fillCvProteinNuocTieu();
			that.fillCvNhipTimThai();
			that.fillCvOiChongKhop();
			that.fillCvGioTrongNgay2();
			that.fillDoMoCTC();
			that.fillConCoCTC();
		}
		//that.fillCvDienBienXuTri();
		$('#txtDienBienXuTri2').val(that.data.dienbienkhac);
		//that.fillCvNguoiTheoDoi();
		// Fill biểu đồ
		that.fillMach();
		//L2PT-34645 form in: hiển thị/vẽ biều đồ
		// L2PT-69382 duonghn start
		draw_json_data(that.data.json_data, that.data.domo_ctc);
		// L2PT-69382 duonghn end
	}
	this.fillFormInPut = function() {
		$('#txtNGAYCAPNHAT').val(that.data.ngaycapnhat_chuoi); // L2PT-47772 duonghn
		$('#txtLanThai').val(that.data.lan_thai);
		$('#txtLanDe').val(that.data.lan_de);
		$('#sel_giochuyenda').val(that.data.gio_vao);
		$('#txtPara').val(that.data.para);//HaNv_091223: L2PT-61993
		$('#txtGHICHU').val(that.data.ghichu);//HaNv_091223: L2PT-61993
		$('#sel_tinhtrangoi').val(that.data.oi_vo);
		$('#sel_giooivo').val(that.data.gio_oi_vo);
		$('#txtDienBienXuTri').val(that.data.dienbienkhac);
		for (var i = 1; i <= 24; i++) {
			$('#txt_giodo_' + i).val(that.data.giodo[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_mach_' + i).val(that.data.mach[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_huyetap_' + i).val(that.data.huyetap[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_nhietdo_' + i).val(that.data.nhietdo[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#sel_protein_' + i).val(that.data.protein[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_timthai_' + i).val(that.data.timthai[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#sel_tinhtrangoi_' + i).val(that.data.tinhtrang_oi[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#sel_chongkhop_' + i).val(that.data.chongkhop[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_domoctc_' + i).val(that.data.domo_ctc[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_ngoithai_' + i).val(that.data.ngoithai[i - 1]);
		}
		for (var i = 1; i <= 24; i++) {
			$('#txt_concoctc_' + i).val(that.data.conco_ctc[i - 1]);
		}
		//L2PT-34645 form sửa: set dl
		set_json_data(that.data.json_data);
	}
	/*----------------- Gen html ----------------*/
	this.genInputForm = function() {
		that.genGioTrongNgay();
		that.genGioDo();
		that.genMach();
		that.genHuyetAp();
		that.genNhietDo();
		that.genProtein();
		that.genTimThai();
		that.genTinhTrangOi();
		that.genChongKhop();
		that.genDoMoCTC();
		that.genNgoiThai();
		that.genConCoCTC();
		//L2PT-34645  tạo html form sửa
		genHTML_json_data(opt);
		// Mask
		$('.txtGioDo').mask('00:00');
		$('.txtMach').mask('000');
		$('.txtHuyetAp').mask('000/000');
		$('.txtNhietDo').mask('00.0');
		$('.txtConCoCTC').mask('0-Z', {
			translation : {
				'Z' : {
					pattern : /[1-5]/
				}
			}
		});
		$('.txtNgoiThai').mask('00');
		$('.txtTimThai').mask('000');
		$('.txtDoMoCTC').mask('00');
	}
	this.genGioTrongNgay = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += "<td align='center'>" + i + "<\/td>";
		}
		$('.inGioTrongNgay').html(_html);
	}
	this.genGioDo = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_giodo_' + i + '" class="form-control txtGioDo"  value="" \/><\/td>';
		}
		$('.inGioDo').html(_html);
	}
	this.genMach = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_mach_' + i + '" class="form-control txtMach"  value="" \/><\/td>';
		}
		$('.inMeMach').html(_html);
	}
	this.genHuyetAp = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_huyetap_' + i + '" class="form-control txtHuyetAp" value="" \/><\/td>';
		}
		$('.inMeHuyetAp').html(_html);
	}
	this.genNhietDo = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_nhietdo_' + i + '" class="form-control txtNhietDo" value="" \/><\/td>';
		}
		$('.inMeNhietDo').html(_html);
	}
	this.genProtein = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><select class="form-control" id="sel_protein_' + i + '">';
			_html += '	<option value=""><\/option>';
			_html += '	<option value="(-)">(-)<\/option>';
			_html += '	<option value="(+)">(+)<\/option>';
			_html += '	<option value="(++)">(++)<\/option>';
			_html += '	<option value="(+++)">(+++)<\/option>';
			_html += '<\/select></td>';
		}
		$('.inProtein').html(_html);
	}
	this.genTimThai = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_timthai_' + i + '" class="form-control txtTimThai" value="" \/><\/td>';
		}
		$('.inTimThai').html(_html);
	}
	this.genTinhTrangOi = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><select class="form-control" id="sel_tinhtrangoi_' + i + '">';
			_html += '	<option value=""><\/option>';
			//HaNv_261023: L2PT-56736
			if ($('#hidHospitalId').val() == 1007) {
				_html += '	<option value="C">Còn<\/option>';
				_html += '	<option value="X">Vỡ bẩn<\/option>';
				_html += '	<option value="T">Vỡ trong<\/option>';
			} else {
				_html += '	<option value="C">C<\/option>';
				_html += '	<option value="T">T<\/option>';
				_html += '	<option value="X">X<\/option>';
				_html += '	<option value="VO">Vỡ<\/option>';
			}
			_html += '	<option value="BAM">Bấm ối<\/option>';
			_html += '<\/select></td>';
		}
		$('.inTinhTrangOi').html(_html);
	}
	this.genChongKhop = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><select class="form-control" id="sel_chongkhop_' + i + '">';
			_html += '	<option value=""><\/option>';
			_html += '	<option value="0">0<\/option>';
			_html += '	<option value="+">+<\/option>';
			_html += '	<option value="++">++<\/option>';
			_html += '	<option value="+++">+++<\/option>';
			_html += '<\/select></td>';
		}
		$('.inChongKhop').html(_html);
	}
	this.genDoMoCTC = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_domoctc_' + i + '" class="form-control txtDoMoCTC" value="" \/><\/td>';
		}
		$('.inDoMoCTC').html(_html);
	}
	this.genNgoiThai = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_ngoithai_' + i + '" class="form-control txtNgoiThai" value="" \/><\/td>';
		}
		$('.inNgoiThai').html(_html);
	}
	this.genConCoCTC = function() {
		var _num = 24;
		var _html = '';
		for (var i = 1; i <= _num; i++) {
			_html += '<td><input type="text" id="txt_concoctc_' + i + '" class="form-control txtConCoCTC" value="" \/><\/td>';
		}
		$('.inConCoCTC').html(_html);
	}
	/*----------------- Data ----------------*/
	this.getDateFromString = function(pStr) {
		if (pStr == '')
			return '';
		var _nam = pStr.substring(0, 4);
		var _thang = pStr.substring(4, 6);
		var _ngay = pStr.substring(6, 8);
		return _ngay + '/' + _thang + '/' + _nam;
	}
	this.getGioDo = function() {
		var _aGioDo = new Array();
		for (var i = 1; i <= 24; i++) {
			_aGioDo.push($('#txt_giodo_' + i).val());
		}
		return _aGioDo;
	}
	this.getMach = function() {
		var _aMach = new Array();
		for (var i = 1; i <= 24; i++) {
			_aMach.push($('#txt_mach_' + i).val());
		}
		return _aMach;
	}
	this.getHuyetAp = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#txt_huyetap_' + i).val());
		}
		return _rs;
	}
	this.getNhietDo = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#txt_nhietdo_' + i).val());
		}
		return _rs;
	}
	this.getProtein = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#sel_protein_' + i).val());
		}
		return _rs;
	}
	this.getTimThai = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#txt_timthai_' + i).val());
		}
		return _rs;
	}
	this.getTinhTrangOi = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#sel_tinhtrangoi_' + i).val());
		}
		return _rs;
	}
	this.getChongKhop = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#sel_chongkhop_' + i).val());
		}
		return _rs;
	}
	this.getDoMoCTC = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#txt_domoctc_' + i).val());
		}
		return _rs;
	}
	this.getNgoiThai = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#txt_ngoithai_' + i).val());
		}
		return _rs;
	}
	this.getConCoCTC = function() {
		var _rs = new Array();
		for (var i = 1; i <= 24; i++) {
			_rs.push($('#txt_concoctc_' + i).val());
		}
		return _rs;
	}
	this.convertArray2String = function(_array, _split) {
		var _rs = '';
		if (_array == null || _array.length == 0) {
			return '';
		}
		_rs = _array.join('#');
	}
	this.getUrlParrams = function() {
		var vars = [], hash;
		var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
		for (var i = 0; i < hashes.length; i++) {
			hash = hashes[i].split('=');
			vars.push(hash[0]);
			vars[hash[0]] = hash[1];
		}
		return vars;
	}
	this.save = function(luu_mau) { //L2PT-37193 luu_mau
		// L2PT-47772 duonghn start
		/*var validator = new DataValidator("modCapNhat");
		var valid = validator.validateForm();
		if (!valid)
			return false;*/
		// L2PT-47772 duonghn end
		that.data.ngaycapnhat_chuoi = $('#txtNGAYCAPNHAT').val();
		that.data.lan_thai = $('#txtLanThai').val() != '' ? $('#txtLanThai').val() : '0';
		that.data.lan_de = $('#txtLanDe').val() != '' ? $('#txtLanDe').val() : '0';
		that.data.para = $('#txtPara').val();//HaNv_091223: L2PT-61993
		that.data.ghichu = $('#txtGHICHU').val();//HaNv_091223: L2PT-61993
		that.data.gio_vao = $('#sel_giochuyenda').val();
		that.data.oi_vo = $('#sel_tinhtrangoi').val();
		that.data.gio_oi_vo = $('#sel_giooivo').val();
		that.data.dienbienkhac = $('#txtDienBienXuTri').val();
		for (var i = 1; i <= 24; i++) {
			that.data.giodo[i - 1] = $('#txt_giodo_' + i).val();
		}
		// Mạch
		for (var i = 1; i <= 24; i++) {
			that.data.mach[i - 1] = $('#txt_mach_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.huyetap[i - 1] = $('#txt_huyetap_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.nhietdo[i - 1] = $('#txt_nhietdo_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.protein[i - 1] = $('#sel_protein_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.timthai[i - 1] = $('#txt_timthai_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.tinhtrang_oi[i - 1] = $('#sel_tinhtrangoi_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.chongkhop[i - 1] = $('#sel_chongkhop_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.domo_ctc[i - 1] = $('#txt_domoctc_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.ngoithai[i - 1] = $('#txt_ngoithai_' + i).val();
		}
		for (var i = 1; i <= 24; i++) {
			that.data.conco_ctc[i - 1] = $('#txt_concoctc_' + i).val();
		}
		//L2PT-34645  lấy dl để lưu
		that.data.json_data = get_json_data();
		that.Array2String();
		var bdcd = JSON.stringify({
			'bdcd' : that.data
		});
		//L2PT-37193
		if (luu_mau && luu_mau == "mau") {
			var data_mau = that.data;
			data_mau.MAU = '1';
			var json_data = JSON.parse(data_mau.json_data);
			json_data.MAU = '1';
			json_data.ID_BENHAN = that.data.id_benhan;
			json_data.TENMAU = $('#txtTENMAU').val();
			data_mau.json_data = JSON.stringify(json_data);
			var _par = JSON.stringify(data_mau);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D113.02", _par);
			that.String2Array();
			var data = $.parseJSON(result);
			if (data != '-1') {
				DlgUtil.showMsg('Lưu mẫu thành công!', function() {});
				$('#txtTENMAU').val('');
				that.loadDsMau();
			} else {
				DlgUtil.showMsg('Lưu mẫu thất bại!', function() {});
			}
		} else {
			// start laphm custom
			var _par = JSON.stringify(that.data);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D113.02", _par);
			that.String2Array();
			var data = $.parseJSON(result);
			if (data != '-1') {
				//that.showThongBao(1, 'Lưu thành công!');
				DlgUtil.showMsg('Lưu thành công!', function() {});
				that.fillData();
			} else {
				//that.showThongBao(2, 'Lưu thất bại!');
				DlgUtil.showMsg('Lưu thất bại!', function() {});
			}
			// end laphm custom
		}
	}
	// Convert data biểu đồ từ mảng  sang string
	this.Array2String = function() {
		that.data.giodo = that.data.giodo.join('#');
		that.data.mach = that.data.mach.join('#');
		that.data.huyetap = that.data.huyetap.join('#');
		that.data.nhietdo = that.data.nhietdo.join('#');
		that.data.protein = that.data.protein.join('#');
		that.data.timthai = that.data.timthai.join('#');
		that.data.tinhtrang_oi = that.data.tinhtrang_oi.join('#');
		that.data.chongkhop = that.data.chongkhop.join('#');
		that.data.domo_ctc = that.data.domo_ctc.join('#');
		that.data.ngoithai = that.data.ngoithai.join('#');
		that.data.conco_ctc = that.data.conco_ctc.join('#');
		//var xxx = that.data.mach.join('#');
		//console.log(that.data);
		//console.log(that.data.mach);
	}
	// Convert data biểu đồ từ string sang Array
	this.String2Array = function() {
		that.data.giodo = that.data.giodo.split('#');
		that.data.mach = that.data.mach.split('#');
		that.data.huyetap = that.data.huyetap.split('#');
		that.data.nhietdo = that.data.nhietdo.split('#');
		that.data.protein = that.data.protein.split('#');
		that.data.timthai = that.data.timthai.split('#');
		that.data.tinhtrang_oi = that.data.tinhtrang_oi.split('#');
		that.data.chongkhop = that.data.chongkhop.split('#');
		that.data.domo_ctc = that.data.domo_ctc.split('#');
		that.data.ngoithai = that.data.ngoithai.split('#');
		that.data.conco_ctc = that.data.conco_ctc.split('#');
		//console.log(that.data);
	}
	/*------------------------  Load thong tin  ---------------------------*/
	this.requestAjax = function(pTask, pData) {
		var _result = null;
		var _url = that.ajxUrl + pTask;
		try {
			$.ajax({
				type : "POST",
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				url : _url,
				data : pData,
				async : false,
				//if received a response from the server
				success : function(data, textStatus, jqXHR) {
					_result = $.parseJSON(data.d);
				},
				//If there was no resonse from the server
				error : function(jqXHR, textStatus, errorThrown) {
					console.log('[' + jqXHR.status + '] - ' + jqXHR.statusText);
				},
				beforeSend : function(jqXHR, settings) {},
				complete : function(jqXHR, textStatus) {}
			});
		} catch (e) {
			console.log('#ERR from Client: ' + e.stack);
		}
		return _result;
	}
	this.loadBDCD = function(mauID) { //L2PT-37193 mauID
		var _url = '../Ajax.aspx/loadBDCD';
		var _idBenhAn = $('#hdfIdBenhAn').val();
		if (_idBenhAn == null || _idBenhAn == '') {
			console.log('#ERR: hdfIdBenhAn null');
			return;
		}
		var _data = {
			pIdBenhAn : _idBenhAn
		}
		_data = JSON.stringify(_data);
		// --- Thông tin chung ----
		//var _bdcd = that.requestAjax('getBDCDByIdBenhAn', _data);
		// start laphm custom
		var obj = new Object();
		obj.hosobenhanid = _idBenhAn;
		//L2PT-37193
		if (mauID && mauID != "") {
			obj.MAU_ID = mauID;
		}
		var _par = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D113.01", _par);
		if (result.length > 0) {
			var _bdcd = result[0];
			// Thông tin chung
			that.data.id_bv_bieudochuyenda = _bdcd.ID_BV_BIEUDOCHUYENDA;
			that.data.id_benhan = _bdcd.ID_BENHAN;
			if (!mauID) {
				that.data.soyte = _bdcd.SOYTE;
				that.data.ten_benhvien = _bdcd.TEN_BENHVIEN;
				that.data.khoa = _bdcd.KHOA;
				// Thông tin bệnh nhân
				that.data.hoten = _bdcd.HOTEN;
				that.data.sovaovien = _bdcd.SOVAOVIEN;
				that.data.tuoi = _bdcd.TUOI;
				that.data.ngay_vao = _bdcd.NGAY_VAO;
			}
			that.data.ngaycapnhat_chuoi = _bdcd.NGAYCAPNHAT_CHUOI; // L2PT-47772 duonghn
			that.data.lan_thai = _bdcd.LAN_THAI;
			that.data.lan_de = _bdcd.LAN_DE;
			that.data.gio_vao = _bdcd.GIO_VAO;
			that.data.gio_oi_vo = _bdcd.GIO_OI_VO;
			that.data.oi_vo = _bdcd.OI_VO;
			that.data.giodo = _bdcd.GIODO != '' ? _bdcd.GIODO.split('#') : that.data.giodo;
			that.data.mach = _bdcd.MACH != '' ? _bdcd.MACH.split('#') : that.data.mach;
			that.data.huyetap = _bdcd.HUYETAP != '' ? _bdcd.HUYETAP.split('#') : that.data.huyetap;
			that.data.nhietdo = _bdcd.NHIETDO != '' ? _bdcd.NHIETDO.split('#') : that.data.nhietdo;
			that.data.protein = _bdcd.PROTEIN != '' ? _bdcd.PROTEIN.split('#') : that.data.protein;
			that.data.timthai = _bdcd.TIMTHAI != '' ? _bdcd.TIMTHAI.split('#') : that.data.timthai;
			that.data.tinhtrang_oi = _bdcd.TINHTRANG_OI != '' ? _bdcd.TINHTRANG_OI.split('#') : that.data.tinhtrang_oi;
			that.data.chongkhop = _bdcd.CHONGKHOP != '' ? _bdcd.CHONGKHOP.split('#') : that.data.chongkhop;
			that.data.domo_ctc = _bdcd.DOMO_CTC != '' ? _bdcd.DOMO_CTC.split('#') : that.data.domo_ctc;
			that.data.ngoithai = _bdcd.NGOITHAI != '' ? _bdcd.NGOITHAI.split('#') : that.data.ngoithai;
			that.data.conco_ctc = _bdcd.CONCO_CTC != '' ? _bdcd.CONCO_CTC.split('#') : that.data.conco_ctc;
			that.data.dienbienkhac = _bdcd.DIENBIENKHAC != '' ? _bdcd.DIENBIENKHAC : that.data.dienbienkhac;
			//L2PT-34645 dl lấy từ DB
			that.data.json_data = _bdcd.JSON_DATA;
			that.data.para = _bdcd.PARA;//HaNv_091223: L2PT-61993
			that.data.ghichu = _bdcd.GHICHU;//HaNv_091223: L2PT-61993
			// Fill Data
			that.fillData();
			that.fillFormInPut();
			$('#notThongBao').hide();
		}
		// end laphm custom
	}
	//L2PT-37193
	this.loadDsMau = function() {
		var id = $('#hdfIdBenhAn').val();
		$('#cboMAU').find('option').remove();
		var _sql_par = [ 'NTU02D113_MAU|' + id + '|' ];
		var ds_phieu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.DSPHIEU", _sql_par.join('$'));
		$("#cboMAU").append(new Option('-- Chọn mẫu --', ''));
		for (var i = 0; i < ds_phieu.length; i++) {
			var _item = new Option(ds_phieu[i].NAME, ds_phieu[i].ID);
			$("#cboMAU").append(_item);
		}
	}
}
//L2PT-34645 CÁC HÀM THÊM (thêm biến json_data)
var objName = [ 'Oxytoci', 'GiotPhut', 'Thuoc', 'Protein', 'Axeton', 'Duong', 'Nguoi', 'DoLot' ]; // các dl điền dạng text trong 24h    //người theo dõi thêm 'Nguoi'
var cach = 5;
function get_json_data() { // form sửa: lấy dl từ form -> obj
	var _obj = new Object();
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			var _val = [];
			var _valId = [];
			for (var i = 1; i <= 24; i++) {
				_val[i - 1] = $('#txt_' + _name + '_' + i).val();
				if (_name == 'Nguoi') {//HaNv_110324: L2PT-75635
					_valId[i - 1] = _val[i - 1] != '' ? $('#txt_IdNguoi_' + i).val() : '';
				}
			}
			_obj[_name] = _val;
			if (_valId) {
				_obj['IdNguoi'] = _valId;
			}
		}
	}
	console.log('=====Lấy dl từ form sửa:', _obj);
	return JSON.stringify(_obj);
}
function set_json_data(data) { // form sửa: set dl
	if (!data)
		return;
	var _data = JSON.parse(data);
	console.log('=====Điền dl vào form sửa:', data);
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			console.log('  --- set ' + _name);
			if (_data[_name]) {
				for (var i = 1; i <= 24; i++) {
					$('#txt_' + _name + '_' + i).val(_data[_name][i - 1]);
					if (_name == 'Nguoi' && _data['IdNguoi'] && _data['IdNguoi'].length > 0) {//HaNv_110324: L2PT-75635
						$('#txt_IdNguoi_' + i).val(_data['IdNguoi'][i - 1]);
					}
				}
			}
		}
	}
}
function genHTML_json_data(opt) { // form sửa: tạo html
	var _num = 24;
	console.log('=====genHTML vào form sửa.');
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		var _html = '';
		if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			console.log('  --- genHTML ' + _name);
			for (var i = 1; i <= _num; i++) {
				//HaNv_261023: L2PT-56736
				if ($('#hidHospitalId').val() == 1007 && _name == 'Thuoc') {
					_html += '<td><input type="text" maxlength=15 id="txt_' + _name + '_' + i + '" class="form-control txt' + _name + '" value="" \/><\/td>';
				} else {
					_html += '<td><input type="text" maxlength=5 id="txt_' + _name + '_' + i + '" class="form-control txt' + _name + '" value="" \/><\/td>';
				}
			}
			$('#divFormSua_' + _name).html(_html);
		}
	}
	//người theo dõi
	$('[id^=txt_Nguoi_]').attr('maxlength', '50');
	var _html = '';
	for (var i = 1; i <= _num; i++) {
		_html += '<td><input class="form-control input-sm i-col-99 ui-autocomplete-input" type="text" id="txt_NguoiTK_' + i +
				'" placeholder="tìm kiếm" style="width: 100%" exttype="cbg" autocomplete="off" role="textbox" aria-autocomplete="list" aria-haspopup="true">' +
				'<input type="hidden" id="txt_IdNguoi_' + i + '"> <\/td>';
	}
	$('#divFormSua_NguoiTK').html(_html);
	for (var i = 1; i <= _num; i++)
		pickerNhanVien('txt_NguoiTK_' + i, 'txt_Nguoi_' + i, 'txt_IdNguoi_' + i);
}
//người theo dõi
function pickerNhanVien(txtSearch, txtTEN, idNguoi) {
	var hearder = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,33,0,f,l;Tên bác sỹ,FULLNAME,22,0,f,l;Chức danh/Khoa phòng,CHUCDANH,42,0,f,l";
	ComboUtil.initComboGrid(txtSearch, "PTTT.LOAD_USER_BDID", [ {
		name : '[0]',
		value : 0
	}, {
		name : '[1]',
		value : '-1'
	} ], "600px", hearder, function(event, ui) {
		var s = ui.item.FULLNAME;
		//var t = $('#' + txtTEN).val();
		//$('#' + txtTEN).val(t == '' ? s : t + ';' + s); cộng thêm tên
		$('#' + txtTEN).val(s);
		$('#' + idNguoi).val(ui.item.USER_ID);//HaNv_110324: L2PT-75635
	});
	$('#' + txtSearch).change(function() {
		$('#' + txtSearch).val('');
	});
}
function draw_Data_Nguoi(divID, data, dataId) {
	try {
		if ($('#hidHospitalId').val() == 28880) {//HaNv_100424: L2PT-81693
			var data1 = [];
			var dataId1 = [];
			var moveX = $('#hidMoveX').val();
			for (var i = 0; i < 23; i++) {
				if (i < moveX) {
					data1[i] = '';
					dataId1[i] = '';
				} else if (i >= moveX) {
					data1[i] = data[i - moveX];
					dataId1[i] = dataId[i - moveX];
				}
			}
			data = data1;
			dataId = dataId1;
		}
		if ($('#hidHospitalId').val() == 1007) { //L2PT-118717
			var data1 = [];
			var dataId1 = [];
			var moveX = 8;
			for (var i = 0; i < 24; i++) {
				if (i < moveX) {
					data1[i] = '';
					dataId1[i] = '';
				} else if (i >= moveX) {
					data1[i] = data[i];
					dataId1[i] = dataId[i];
				}
			}
			data = data1;
			dataId = dataId1;
		}
		$('#tblBS').find("td").each(function(index) {
			var _html = data[index];
			if ($('#hidHospitalId').val() == 28880 && dataId && dataId.length > 0 && dataId[index]) {//HaNv_110324: L2PT-75635
				var src = jsonrpc.AjaxJson.ajaxCALL_SP_C("DMC12.GET_ANH", dataId[index] + "$");
				if (src) {
					_html = _html + '<span><img class = "imgKy" src="' + src + '"></span>';
				}
			}
			$(this).html(_html);
		});
	} catch (ex) {}
	return;
}
function draw_json_data(data, _domo_ctc) { // form in: hiển thị/vẽ biểu đồ
	if (!data)
		return;
	var _data = JSON.parse(data);
	console.log('=====draw_json_data vào form IN.');
	// Oxytoci GiotPhut Thuoc Protein Axeton Duong
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		// L2PT-69382 duonghn start
		var _data_to_draw = chuyenMang(_data[_name], _domo_ctc);
		var data_IdNguoi = [];
		if (_data['IdNguoi'] && _data['IdNguoi'].length > 0) {
			if (_name == 'Nguoi') {
				data_IdNguoi = chuyenMang(_data['IdNguoi'], _domo_ctc);
			} else {
				data_IdNguoi = _data['IdNguoi'];
			}
		}
		// L2PT-69382 duonghn end
		if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			console.log('  --- draw ' + _name);
			if (_name == 'Nguoi') //người theo dõi
				draw_Data_Nguoi('divFormIn_' + _name, _data_to_draw, data_IdNguoi);
			else
				draw_Data('divFormIn_' + _name, _data_to_draw);
		}
	}
}
function draw_Data(divID, data) {
	var _conco_ctc = data;
	var _numLine = 1;
	var _numCol = 24;
	var _startX = 0;
	var _startY = 0;
	var _stopX = 644;
	var _stopY = 100;
	var _lineHeight = 20;
	var _lineWidth = 23;
	var _cvHeight = _lineHeight * _numLine;
	var _cvwidth = _lineWidth * 24 + 2;
	var _canvas = $("#" + divID).find('canvas').eq(0);
	_canvas.attr('width', _cvwidth + cach);
	_canvas.attr('height', _cvHeight);
	// Fill dòng
	for (var _i = 0; _i <= _numLine; _i++) {
		_canvas.drawLine({
			strokeStyle : '#7F7F83',
			strokeWidth : 1.5,
			x1 : _startX + 2 + cach,
			y1 : _i * _lineHeight,
			x2 : _stopX + cach,
			y2 : _i * _lineHeight
		});
	}
	// Fill cột
	for (var _i = 0; _i <= _numCol; _i++) {
		_canvas.drawLine({
			strokeStyle : '#7F7F83',
			strokeWidth : 1,
			x1 : _i * _lineWidth + 1 + cach,
			y1 : _startY,
			x2 : _i * _lineWidth + 1 + cach,
			y2 : _stopY - 2
		});
	}
	//điền dữ liệu
	if (data) {
		_startCell = 1;
		for (var _i = 0; _i <= 24; _i++) {
			_canvas.drawText({
				fillStyle : '#000',
				strokeStyle : '#000',
				strokeWidth : 1,
				fontSize : 9,
				fontFamily : 'Verdana, sans-serif',
				x : (_startCell + _i - 1) * _lineWidth + 12 + cach,
				y : _startY + 10,
				text : data[_i] ? data[_i] : ''
			});
		}
	}
}
function NTU02D113_BieuDoChuyenDa(opt) {
	this.load = doLoad;
	var _data = new DataBDCD();
	var BDCD = new BieuDoChuyenDa(_data, opt);//L2PT-34645 thêm biến opt để check ẩn/hiện các dl khác lưu vào data_json
	function doLoad() {
		if ($('#hidHospitalId').val() == 993) {
			var hosobenhanid = $('#hdfIdBenhAn').val();
			window.location.replace('manager.jsp?func=../noitru/NTU02D113_BieuDoChuyenDa_993&showMode=dlg', hosobenhanid, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			return;
		}
		// jira 32302
		if ($('#hidHospitalId').val() == 1040) {
			var hosobenhanid = $('#hdfIdBenhAn').val();
			window.location.replace('manager.jsp?func=../noitru/NTU02D113_BieuDoChuyenDa_1040&showMode=dlg', hosobenhanid, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			return;
		}
		// jira L2PT-2013
		if ($('#hidHospitalId').val() == 939) {
			var hosobenhanid = $('#hdfIdBenhAn').val();
			window.location.replace('manager.jsp?func=../noitru/NTU02D113_BieuDoChuyenDa_939&showMode=dlg', hosobenhanid, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			return;
		}
		// can giuoc
		if ($('#hidHospitalId').val() == 40480) {
			var hosobenhanid = $('#hdfIdBenhAn').val();
			window.location.replace('manager.jsp?func=../noitru/NTU02D113_BieuDoChuyenDa_40480&showMode=dlg', hosobenhanid, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			return;
		}
		//L2PT-102276
		if ($('#hidHospitalId').val() == 118180) {
			var hosobenhanid = $('#hdfIdBenhAn').val();
			window.location.replace('manager.jsp?func=../noitru/NTU02D113_BieuDoChuyenDa_118180&showMode=dlg', hosobenhanid, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			return;
		}
		// get by conf
		let
		BieuDoChuyenDaTemplate = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU02D113_TEMPLATE');
		if (BieuDoChuyenDaTemplate && BieuDoChuyenDaTemplate !== '0' && BieuDoChuyenDaTemplate.indexOf("NTU02D113_") === 0 && BieuDoChuyenDaTemplate !== 'NTU02D113_BieuDoChuyenDa') {
			window.location.replace('manager.jsp?func=../noitru/' + BieuDoChuyenDaTemplate + '&showMode=dlg', $('#hdfIdBenhAn').val(),
					'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			return;
		}
		if ($('#hidHospitalId').val() == 10284) {//HaNv_091223: L2PT-61993
			$('#divLanThaiDe').hide();
			$('#divTTCLanThaiDe').hide();
			$('#divPara').show();
			$('#divTTCPara').show();
			$('#divGhiChu').show();
		}
		initControl();
		bindEvent();
	}
	function initControl() {
		loadBieuDoChuyenDa();
	}
	function bindEvent() {
		$("#print_task").on("click", "#btnNhapPhieu", function() {
			$('#modCapNhat').modal('show');
		});
		$("#print_task").on("click", "#btn_in_trang", function() {
			window.print();
		});
		$("#modCapNhat").on("click", "#btnLuu", function() {
			BDCD.save();
		});
		// phim tat
		$(document).unbind('keydown').keydown(function(e) {
			var f7 = 118;
			var f8 = 119;
			if (e.keyCode == f7) {
				$('#modCapNhat').modal('show');
			}
			if (e.keyCode == f8) {
				window.print();
			}
		});
		//L2PT-37193
		$("#btnLuuThanhMau").on("click", function(e) {
			if ($('#txtTENMAU').val().trim() == '') {
				DlgUtil.showMsg('Tên mẫu không được để trống', function() {});
				return;
			}
			BDCD.save('mau');
		});
		$("#btnXoaMau").on("click", function(e) {
			if ($('#cboMAU').val() == '') {
				DlgUtil.showMsg('Vui lòng chọn mẫu', function() {});
				return;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa mẫu này không?", function(flag) {
				if (flag) {
					var _sql_par = [ 'NTU02D113_MAU_XOA|' + $('#cboMAU').val() + '|' ];
					var ds_phieu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.DSPHIEU", _sql_par.join('$'));
					if (ds_phieu && ds_phieu.length > 0 && ds_phieu[0].RET == '1') {
						DlgUtil.showMsg('Đã xóa mẫu', function() {
							BDCD.loadDsMau();
						});
					} else {
						DlgUtil.showMsg('Đã có lỗi xảy ra', function() {});
					}
				}
			});
		});
		$('#cboMAU').change(function() {
			$('#txtTENMAU').val('');
			if ($('#cboMAU').val() != '') {
				BDCD.loadBDCD($('#cboMAU').val());
			}
		});
		$('.inGioDo').find("td").each(function(index) {
			if ($('#hidHospitalId').val() == 10284 || $('#hidHospitalId').val() == 121860) {//HaNv_091223: L2PT-61993 L2PT-115852
				$(this).find('input').attr('disabled', false);
			} else {
				$(this).find('input').attr('disabled', true);
			}
		});
		$('#sel_giochuyenda').change(function() {
			var batdau = $(this).val();
			if ($('#hidHospitalId').val() == 10284 || $('#hidHospitalId').val() == 121860) {//HaNv_091223: L2PT-61993 L2PT-115852
				var gio = batdau ? (String(parseInt(batdau)).padStart(2, '0') + ':00') : '00:00';
				$('#txt_giodo_1').val(gio);
			} else {
				$('.inGioDo').find("td").each(function(index) {
					var gio = parseInt(batdau) + index;
					if ($('#hidHospitalId').val() == 28880) {//HaNv_110324: L2PT-75635
						var idx = 0;
						for (var i = 1; i <= 24; i++) {
							if ($('#txt_domoctc_' + i).val() >= 3) {
								idx = i - 1;
								break;
							}
						}
						if (idx > 0) {
							gio = parseInt(batdau) + index - idx;
						}
					}
					if (gio >= 24) {
						gio = gio - 24;
					}
					if (gio < 0) {
						gio = gio + 24;
					}
					$(this).find('input').val(gio);
				});
			}
		});
		$("#btnXoaPhieu").bindOnce("click", function() {
			//check ký CA
			var params = [ {
				name : 'HOSOBENHANID',
				type : 'String',
				value : $('#hdfIdBenhAn').val()
			}, {
				name : 'RPT_CODE',
				type : 'String',
				value : 'RPT_BIEUDO_CHUYENDA_A4'
			} ];
			var checkky = CommonUtil.checkKyCaByParam(params);
			if (checkky == '1') {
				DlgUtil.showMsg('Phiếu đã thực hiện ký số, ký điện tử');
				return;
			} else {
				DlgUtil.showConfirm("Bạn có muốn xóa phiếu này không?", function(flag) {
					if (flag) {
						var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D113.05", $('#hdfIdBenhAn').val());
						if (fl == '1') {
							DlgUtil.showMsg('Xóa phiếu thành công');
							setTimeout(callback, 2000);
							function callback() {
								window.close();
							}
						} else {
							DlgUtil.showMsg('Đã có lỗi xảy ra');
						}
					}
				});
			}
		});
		$("#btnKySo").bindOnce("click", function() {
			_caRpt('1');
		});
		$("#btnHuyKy").bindOnce("click", function() {
			_caRpt('2');
		});
		$("#btnInKySo").bindOnce("click", function() {
			_caRpt('0');
		});
	}
	function loadBieuDoChuyenDa() {
		BDCD.genInputForm();
		BDCD.loadBDCD();
		//L2PT-37193 
		BDCD.loadDsMau();
	}
	function _caRpt(signType) {
		var params = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : $('#hdfIdBenhAn').val()
		}, {
			name : 'RPT_CODE',
			type : 'String',
			value : 'RPT_BIEUDO_CHUYENDA_A4'
		} ];
		if (signType == '0') {
			CommonUtil.openReportGetCA2(params, false);
		} else {
			CommonUtil.kyCA(params, signType, true);
			EventUtil.setEvent("eventKyCA", function(e) {
				DlgUtil.showMsg(e.res);
			});
		}
	}
}
//L2PT-69382 duonghn start
function chuyenPha(_domo_ctc) {
	var Co_Chuyen = false;
	var bat_dau_chuyen = -1;
	for (var _i = 23; _i >= 0; _i--)
		if (_domo_ctc[_i] != null && _domo_ctc[_i].trim() != '') {
			if (parseInt(_domo_ctc[_i]) > 3)
				bat_dau_chuyen = _i;
			else if (parseInt(_domo_ctc[_i]) == 3) {
				bat_dau_chuyen = _i;
				break;
			}
		}
	var diem_cuoi_chuyen = 0;
	if ($('#hidHospitalId').val() != 28880) {//HaNv_280324: L2PT-79631
		if (bat_dau_chuyen > -1) {
			diem_cuoi_chuyen = 8 + (parseInt(_domo_ctc[bat_dau_chuyen]) - 3);
			Co_Chuyen = true;
		} else {
			bat_dau_chuyen = 0;
		}
		if ($('#hidHospitalId').val() == 1007) { //L2PT-118717
			bat_dau_chuyen = 0;
			diem_cuoi_chuyen = 8;
			Co_Chuyen = true;
		}
	} else {//HaNv_100424: L2PT-81693
		if (bat_dau_chuyen > -1) {
			diem_cuoi_chuyen = parseInt(_domo_ctc[bat_dau_chuyen]) - 3;
			moveX = diem_cuoi_chuyen;
			$('#hidMoveX').val(moveX);
			Co_Chuyen = false;
		} else {
			bat_dau_chuyen = 0;
		}
	}
	return {
		INDX_DIEMDAU_CHUYEN : bat_dau_chuyen,
		INDX_DIEMCUOI_CHUYEN : diem_cuoi_chuyen,
		OFFSET : diem_cuoi_chuyen - bat_dau_chuyen,
		CHUYEN : Co_Chuyen
	};
}
function chuyenMang(arr, _arr_domo_ctc) {
	var arrOut = $.extend([], arr);
	if (!_arr_domo_ctc || _arr_domo_ctc.length == 0)
		return arrOut;
	var objCP = chuyenPha(_arr_domo_ctc);
	var arrOffset = [];
	if (objCP.CHUYEN) {
		if ($('#hidHospitalId').val() == 1007) { //L2PT-118717
			for (var indx = 0; indx < objCP.OFFSET; indx++) {
				arrOut.splice(objCP.INDX_DIEMDAU_CHUYEN + indx, 0, "");
			}
		} else {
			for (var indx = 1; indx < objCP.OFFSET; indx++) {
				arrOut.splice(objCP.INDX_DIEMDAU_CHUYEN + indx, 0, "");
			}
			arrOut.splice(objCP.INDX_DIEMCUOI_CHUYEN, 0, arrOut[objCP.INDX_DIEMDAU_CHUYEN]);
		}
	}
	return arrOut;
}
// L2PT-69382 duonghn end