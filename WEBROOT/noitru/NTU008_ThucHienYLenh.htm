<!--  
<PERSON><PERSON> màn hình  : NTU01H001
File mã nguồn : NTU01H001_PhongHanhChinh.js
<PERSON><PERSON><PERSON> đích  : <PERSON><PERSON> lý nghiệp vụ hành chính của nội trú 
Tham số vào :
<PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nhật  <PERSON> chú
HUNGNT	- 03092016 - Comment
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link href="../common/script/select2/dist/css/select2.css" rel="stylesheet" />
<script src="../common/script/select2/dist/js/select2.full.js"></script>
<script src="../common/script/mobile/hammer.js"></script>
<script src="../common/script/mobile/hammer.jquery.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
<link href="../common/script/lightbox2/css/lightbox.css" rel="stylesheet" />
<script src="../common/script/lightbox2/js/lightbox.js"></script>

<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../noitru/NTU008_ThucHienYLenh.js?v=202410176"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=20240115"></script>
<style>
    .sec-all {
        display: none !important;
    }

    #divMenuCus {
        display: none !important;
    }

    #divMain .ui-jqgrid tr.jqgrow td {
        vertical-align: middle !important;
    }
</style>
<div id="divDlg" style="width: 100%; display: none">
    <iframe src="" id="ifmView"
            style="width: 1200px; height: 800px; border: dotted 1px red"
            frameborder="0"></iframe>
</div>
<div id="loading" class="hide">
    <span class="spinner"></span>
    <br/>
    <h3 id="loadding_title">Đang thực hiện...</h3>
</div>
<div width="100%" id="divMain" class="row">
    <div class="col-xs-12 low-padding border-group-1 mgr-1 mgl1 mgt5" style="background-color: #e9f3f8;">
        <div class="col-xs-12">
            <div class="col-xs-12 low-padding mgt3">
                <div class="col-xs-3" style="padding: 7px 0px">
                    <strong id="lblHoTen">Họ và tên: Đào Thị Hồng Điệp - Nữ</strong><br/>
                    <label id="lblNgaySinh">Ngày sinh: 15/11/2020</label>
                </div>
                <div class="col-xs-3" style="padding: 7px 0px">
                    <label id="lblChanDoan" data-toggle="tooltip" data-placement="bottom" title="Tooltip on bottom">Chẩn đoán: R10.4 - Đau bụng không xác định và đau bụng khác; K29.1-Viêm dạ dày cấp khác</label>
                </div>
                <div class="col-xs-6 low-padding">
                    <div class="col-xs-12">
                        <div class="col-xs-6" style="display: flex;">
                            <label class="" style="width: 42%;">Ngày y lệnh:</label>
                            <div class="input-group" style="width: 60%">
                                <input disabled="disabled" class="form-control input-sm" id="txtNGAYYLENH" name="txtNGAYYLENH" title=""
                                       data-mask="00/00/0000" placeholder="Đến ngày">
                                <span style="line-height: 0;" class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"
                                      onclick="NewCssCal('txtNGAYYLENH','ddMMyyyy','dropdown',false,'24',true)"></span>
                            </div>
                        </div>
                        <div class="col-xs-6" style="display: flex;">
                            <label class="" style="width: 45%;">Đường dùng</label>
                            <select class="form-control input-sm" id="cboDUONGDUNG" style="width: 60%;">
                            </select>
                        </div>
                    </div>
                    <div class="col-xs-12" style="margin-top: 10px">
                    	<!-- L2PT-50181 duonghn start -->
                        <!-- <div class="col-xs-6">
                            <label style="font-weight: 700 !important; font-size: 0.9em;">
                                <input type="radio" name="radLOAIYLENH" value="1" id="radTHUOCVATTU" checked="checked">
                                Thuốc/vật tư
                            </label>
                        </div> -->
                        <div class="col-xs-4">
                            <label style="font-weight: 700 !important; font-size: 0.9em;">
                                <input type="radio" name="radLOAIYLENH" value="3" id="radTHUOC" checked="checked">
                                Thuốc
                            </label>
                        </div>
                        <div class="col-xs-4">
                            <label style="font-weight: 700 !important; font-size: 0.9em;">
                                <input type="radio" name="radLOAIYLENH" value="4" id="radVATTU">
                                Vật tư
                            </label>
                        </div>
                        <!-- L2PT-50181 duonghn end -->
                        <div class="col-xs-4">
                            <label style="font-weight: 700 !important; font-size: 0.9em;">
                                <input type="radio" name="radLOAIYLENH" value="2" id="radDVKT">
                                Dịch vụ kỹ thuật
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <!-- L2PT-56962 start-->
            <div class="col-xs-12 low-padding mgt3" id='divTT_PDT' style="display:none;">
                <div class="col-xs-12" style="padding: 7px 0px">
                    <label id="lblThongTinPDT" data-toggle="tooltip" data-placement="bottom" title="Tooltip on bottom"><strong>Thông tin phiếu điều trị:</strong></label>
                </div>
            </div>
            <!-- L2PT-56962 end-->
        </div>
    </div>
    <div class="col-xs-12 low-padding mgt-5">
        <table id="grdDanhSachYLenh"></table>
        <div id="pager_grdDanhSachYLenh"></div>
    </div>
    <div class="col-xs-12 low-padding border-group-1 mgr-1 mgl1 mgt5" style="background-color: #e9f3f8;">

    </div>
</div>

<div class="modal fade" id="modalNote" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 50%" role="document">
        <div class="modal-content" style="background-color: #e9f3f8;">
            <div class="row" style="padding: 35px;">
                <div class="col-xs-12" style="padding: 20px;">
                    <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox" id="chkSANG2" style="zoom: 1.5;">
                                &nbsp;&nbsp;Sáng
                            </span>
                    </div>
                    <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox"  id="chkTRUA2" style="zoom: 1.5;">
                                &nbsp;&nbsp;Trưa
                            </span>
                    </div>
                    <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox"  id="chkCHIEU2" style="zoom: 1.5;">
                                &nbsp;&nbsp;Chiều
                            </span>
                    </div>
                    <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox"  id="chkTOI2" style="zoom: 1.5;">
                                &nbsp;&nbsp;Tối
                            </span>
                    </div>
                </div>
                <div class="col-xs-12">
                    <textarea  style="width: 100%;height: 200px;padding: 10px;background-color: #e9f3f8;" id="txtGHICHU2"></textarea>
                </div>
                <div class="col-xs-12">
                    <pre style="width: 100%;padding: 10px;background-color: #e9f3f8;" id="txtCACHDUNG2"></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row" style="padding: 10px 35px 10px 35px;position: fixed;bottom: 0;left: 0;right: 0;background-color: #e9f3f8;border-top: 1px solid #8d4242;">
    <div class="col-xs-4 text-center">
        <button type="button" class="btn btn-sm btn-primary mgl10 disabled" id="btnHOANTHANH2" style="line-height: 1;">
            HOÀN THÀNH
        </button>
        <button type="button" class="btn btn-sm btn-primary mgl10 disabled" id="btnTHUCHIEN2" style="line-height: 1;">
            THỰC HIỆN
        </button>
        <button type="button" class="btn btn-sm btn-primary mgl10 disabled" id="btnTHUCHIEN_NHANH" style="line-height: 1;">
            THỰC HIỆN NHANH
        </button>
    </div>
    <div class="col-xs-4 text-center">
        <button type="button" class="btn btn-sm btn-primary mgl10" id="btnBENHNHANKY2" style="line-height: 1;">
            BỆNH NHÂN KÝ
        </button>
        <img id="imageSign" style="height: 44px;margin-left: 20px;border: 1px solid;" src="../common/image/sign_blank.png">
        <a style="display: none" id="imageSignPreview" href="images/image-1.jpg" data-lightbox="image-1">Image #1</a>
    </div>
    <div class="col-xs-4 text-center">
        <button type="button" class="btn btn-sm btn-primary mgl10" id="btnXACNHANDIEUDUONG" style="line-height: 1;">
            XÁC NHẬN
        </button>
        <img id="imageSignDD" style="height: 44px;margin-left: 20px;border: 1px solid;" src="../common/image/sign_blank.png">
        <a style="display: none" id="imageSignPreviewDD" href="images/image-1.jpg" data-lightbox="image-1">Image #1</a>
    </div>
</div>

<div class="modal fade" id="modalTHT" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #5f68bc !important;padding: 10px !important;">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="modalLabel">THỰC HIỆN THUỐC</h4>
            </div>
            <div class="modal-body" style="padding: 10px !important;background-color:white !important;">
                <div class="row">
                    <div class="col-xs-12" style="padding: 20px;">
                        <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox" id="chkSANG" style="zoom: 1.5;">
                                &nbsp;&nbsp;Sáng
                            </span>
                        </div>
                        <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox" id="chkTRUA" style="zoom: 1.5;">
                                &nbsp;&nbsp;Trưa
                            </span>
                        </div>
                        <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox" id="chkCHIEU" style="zoom: 1.5;">
                                &nbsp;&nbsp;Chiều
                            </span>
                        </div>
                        <div class="col-xs-3">
                            <span class="body-chk">
                                <input type="checkbox" id="chkTOI" style="zoom: 1.5;">
                                &nbsp;&nbsp;Tối
                            </span>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <textarea style="width: 100%;height: 200px;padding: 10px;" id="txtGHICHU"></textarea>
                    </div>
                    <div class="col-xs-12">
                        <pre style="width: 100%;padding: 10px;background-color: white;" id="txtCACHDUNG"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="text-align: center !important;">
                <button type="button" class="btn btn-sm btn-primary mgl10" id="btnHOANTHANH_THUOC" style="line-height: 1;">
                    HOÀN THÀNH
                </button>
                <button type="button" class="btn btn-sm btn-primary mgl10" id="btnTHUCHIEN_THUOC" style="line-height: 1;">
                    THỰC HIỆN
                </button>
                <button type="button" class="btn btn-sm btn-danger mgl10" id="btnHUYTHUCHIEN" style="line-height: 1;">
                    HỦY/ĐÓNG
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalXNDD" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #5f68bc !important;padding: 10px !important;">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="modalXNDDLabel">XÁC NHẬN ĐIỀU DƯỠNG</h4>
            </div>
            <div class="modal-body" style="padding: 10px !important;background-color:white !important;text-align: center;">
                <img id="imageCKNV" style="width: 540px;margin-left: 20px;border: 1px solid;" src="../common/image/sign_blank.png">
            </div>
            <div class="modal-footer" style="text-align: center !important;">
                <button type="button" class="btn btn-sm btn-primary mgl10" id="btnXNDD_OK" style="line-height: 1;">
                    XÁC NHẬN
                </button>
                <button type="button" class="btn btn-sm btn-danger mgl10" id="btnXNDD_CANCEL" style="line-height: 1;">
                    HỦY/ĐÓNG
                </button>
            </div>
        </div>
    </div>
</div>
<style>
    .sec-all {
        display: none !important;
    }

    .site-footer {
        display: none !important;
    }

    #smartcaicon {
        display: none !important;
    }

    #divMain {
        padding: 0 25px 25px 25px;
        height: unset !important;
    }

    .body-chk {
        font-weight: 700 ;
        font-size: 15px;
        display: flex;
    }

    .ui-search-toolbar {
        display: none;
    }

    .ui-jqgrid-labels {
        height: 40px !important;
    }

    .ui-jqgrid-labels th {
        background-color: #e9f3f8 !important;
        color: black !important;
        border-top: 1px solid #979797 !important;
        border-right: none !important;
    }

    #grdDanhSachYLenh_TENDICHVU {
        border-right: 1px solid #979797 !important;
    }

    .ui-jqgrid-btable tr.jqgrow {
        height: 50px !important;
    }

    pre {
        white-space: pre-wrap;
        border: none;
        font-family: inherit;
    }

    .modal-body, pre {
        background-color: #d9d9d9;
    }

    #modalLabel {
        text-align: center;
        color: white;
    }

    td[aria-describedby="grdDanhSachYLenh_TENBENHNHAN"] {
        font-size: 14px !important;
        font-weight: bold !important;
    }

    #grdDanhSachYLenh tr.ui-widget-content > td {
        font-size: 13px !important;
    }

    .ui-jqgrid tr.ui-row-ltr td {
        border-right-width: 0 !important;
    }

    .btn {
        border-radius: 4px !important;
        font-size: 14px !important;
    }

    button {
        background-color: #1d1f1f;
        padding: 14px !important;
    }

    input[name="radLOAIYLENH"] {
        margin-top: -3px;
        zoom: 1.5;
    }

    #txtNGAYYLENH {
        height: 27px;
    }

    .ui-jqgrid .ui-jqgrid-htable th div {
        height: 20px !important;
    }

    #cb_grdDanhSachYLenh {
        font-size: 16px;
        margin-top: 1px !important;
    }

    td[aria-describedby="grdDanhSachYLenh_cb"] {
        padding-top: 14px !important;
    }

    td[aria-describedby="grdDanhSachYLenh_cb"] input[type="checkbox"] {
        font-size: 18px;
    }

    input[type="checkbox"] {
        /* Add if not using autoprefixer */
        -webkit-appearance: none;
        font-size: 18px;
        /* Remove most all native input styles */
        appearance: none;
        /* For iOS < 15 */
        background-color: #e5e5e5;
        /* Not removed via appearance */
        margin: 0;

        font: inherit;
        color: #979797;
        width: 1.15em;
        height: 1.15em;
        border: 1px solid #979797;
        border-radius: 0.15em;
        transform: translateY(-0.075em);

        display: grid;
        place-content: center;
    }

    input[type="checkbox"]::before {
        content: "";
        width: 0.65em;
        height: 0.65em;
        clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
        transform: scale(0);
        transform-origin: bottom left;
        transition: 120ms transform ease-in-out;
        box-shadow: inset 1em 1em var(--form-control-color);
        /* Windows High Contrast Mode */
        background-color: CanvasText;
    }
    :root {
        --form-control-color: rebeccapurple;
        --form-control-disabled: #959495;
    }

    input[type="checkbox"]:checked::before {
        transform: scale(1);
    }

    input[type="checkbox"]:focus {
        outline: 0 solid currentColor;
    }

    /*input[type="checkbox"]:disabled {*/
    /*    --form-control-color: var(--form-control-disabled);*/

    /*    color: var(--form-control-disabled);*/
    /*    cursor: not-allowed;*/
    /*}*/

    .jqgrid-rownum {
        text-indent: 999%;
    }

    .jBox-Modal {
        top: 250px !important;
    }

    .jBox-Modal.jBox-closeButton-title .jBox-title, .jBox-Confirm.jBox-closeButton-title .jBox-title {
        color: #fff;
        background-color: #5f68bc;
        height: 40px;
        text-align: center;
    }

    .jBox-Modal.jBox-closeButton-title .jBox-title, .jBox-Confirm.jBox-closeButton-title .jBox-title>div {
        padding-top: 9px;
        padding-right: 0 !important;
        font-size: 18px;
        font-weight: normal;
    }

    .jBox-content {
        overflow: hidden;
    }

    .modal-header .close {
        margin-top: -11px !important;
    }

    .ui-jqgrid tr.jqgrow td[aria-describedby="grdDanhSachYLenh_GHICHU"] {
        white-space: nowrap !important;
        text-overflow: ellipsis;
    }

    #lblChanDoan {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .icon_menu {
        display: none !important;
    }
</style>
<style>
    @keyframes spinner {
        to {transform: rotate(360deg);}
    }

    #loading {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #0000007a;
    }

    #loading.show{
        display: block;
        z-index: 999;
    }

    #loading.hide{
        display: none;
        z-index: -1;
    }

    .spinner {
        position: fixed;
        left: 50%;
    }

    .spinner:before {
        content: '';
        box-sizing: border-box;
        position: absolute;
        width: 40px;
        height: 40px;
        margin-top: -20px;
        margin-left: -20px;
        border-radius: 50%;
        border: 4px solid #000;
        border-top-color: #fff;
        animation: spinner 1s linear infinite;
    }

    #loadding_title {
        position: absolute;
        left: 0;
        right: 0;
        text-align: center;
        color: white;
    }
</style>
<script>

    var opt = [];
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var uuid = '{uuid}';
    var dept_id = '{dept_id}';
    var subdept_id = '{subdept_id}';
    var dept_name = '{dept_name}';
    var subdept_name = '{subdept_name}';
    var lang = "vn";
    var mode = '{showMode}';
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';
    var khambenhid = getParameterByName('khambenhid', window.location.search.substring(1));
    var tiepnhanid = getParameterByName('tiepnhanid', window.location.search.substring(1));
    var _opts = new Object();

    _opts._param = session_par;
    _opts._uuid = uuid;
    _opts._deptId = dept_id;
    _opts._subdept_id = subdept_id;
    _opts._user_id = user_id;
    _opts._khambenhid = khambenhid;
    _opts._tiepnhanid = tiepnhanid;
    initRest(_opts._uuid);

    var dsbn = new daybenhan(_opts);
    dsbn.load();

</script>
