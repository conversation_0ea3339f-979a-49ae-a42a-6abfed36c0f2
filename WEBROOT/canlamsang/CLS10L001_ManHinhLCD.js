// VietDA 08/10/2016
// <PERSON><PERSON>n hình LCD hiển thị danh sách chờ lấy mẫu bệnh phẩm
// 

function XuatManHinh(opt) {
	this.load=doLoad;
	
	var opts = opt;	
	
	function doLoad() {
		_initControl();
	}
	
	function _initControl(){			
		window.moveTo(0, 0);
        window.resizeTo(screen.width, screen.height);
		
		$('#tenbenhvien').text("BỆNH VIỆN HỮU NGHỊ LẠC VIỆT");
		$('#tenphong').text("DANH SÁCH CHỜ THỰC HIỆN");
		$("#ngayhientai").text(moment().format('DD/MM/YYYY'));
		loadData();
		
		setInterval(function(){
			loadData();
		}, 5000);
	}
	
	function loadData(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					opts.phongid+'$'+
					opts.tungay+'$'+
					opts.denngay+'$'+
					'2$'+ 	// trang thai
					'10'		// 5 ban ghi tren trang
					; 	
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS03L001.LCD", param);
		
		var html = '';

		if (data_ar != null && data_ar.length > 0) {
			for(var i = 0; i < data_ar.length; i++){
				html += '<tr>';
				html += '<td><h2><center>'+data_ar[i].SOTHUTU+'</center></h2></td>';
//				html += '<td><h2><center>'+data_ar[i].SOPHIEU+'</center></h2></td>';
				html += '<td><h2>'+data_ar[i].TENBENHNHAN+'</h2></td>';
				html += '<td><h2><center>'+data_ar[i].NGAYSINH+'</center></h2></td>';
				html += '<td><h2><center>'+'Chờ thực hiện'+'</center></h2></td>';
				html += '</tr>';
			}
			$('#tenphong').text(data_ar[0].PHONGTHUCHIEN);
		}

		$('#list').html(html);
		
	}
}

