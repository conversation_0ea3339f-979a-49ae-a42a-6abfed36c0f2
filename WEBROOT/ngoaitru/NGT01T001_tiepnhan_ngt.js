var ctl_ar = [{
    type: 'buttongroup', id: 'btnPrint', icon: 'print', text: 'In'
    , children: [
        {id: 'print_1', icon: 'print', text: 'Phiếu khám bệnh', hlink: '#'},
        {id: 'print_2', icon: 'print', text: 'In Barcode', hlink: '#'},
        {id: 'print_3', icon: 'print', text: 'Phiếu trả thẻ', hlink: '#'},
        {id: 'print_4', icon: 'print', text: 'In phiếu CLS chung', hlink: '#'},
        {id: 'print_xetnghiemchung', icon: 'print', text: 'In phiếu xét nghiệm chung', hlink: '#'},
        {id: 'print_cdhachung', icon: 'print', text: 'In phiếu CDHA chung', hlink: '#'},
        {id: 'print_5', icon: 'print', text: 'In thẻ khám bệnh', hlink: '#'},
        {id: 'print_6', icon: 'print', text: 'In giấy chứng nhận sức khỏe', hlink: '#'},
        {id: 'inBangKe', icon: 'print', text: 'In bảng kê', hlink: '#'}
    ]
}
    , {type: 'button', id: 'btnThongBao', icon: 'thongbao', text: 'Thông báo'}
    , {type: 'button', id: 'treat_2', icon: 'dichvu', text: 'Dịch vụ CLS'}
    , {
        type: 'buttongroup', id: 'btnhandling', icon: 'xutri', text: 'Xử trí', hlink: '#'
        , children: [
            {id: 'handling_1', icon: 'xutri', text: 'Chuyển phòng khám', hlink: '#'},
            {id: 'handling_2', icon: 'xutri', text: 'Thêm phòng khám', hlink: '#'}
        ]
    }
    , {type: 'button', id: 'btnHoaHong', icon: 'hoahong', text: 'Hoa hồng', hlink: '#'}
    , {type: 'button', id: 'btnThuTien', icon: 'thutien', text: 'Thu tiền', hlink: '#'}
    , {type: 'button', id: 'btnThuTienKhac', icon: 'thutien', text: 'Thu khác', hlink: '#'}
    , {
        type: 'buttongroup', id: 'btnLSKB', icon: 'kham', text: 'Lịch sử'
        , children: [
            {id: 'btnLSKHAMCONGBHYT', icon: 'kham', text: ' Lịch sử theo cổng BHYT', hlink: '#'}
            //,{id:'btnLSKCB',icon:'kham',text:' Lịch sử cổng BYT',hlink:'#'}
            //,{id:'btnLSKCBHISL3',icon:'kham',text:' Lịch sử KCB HIS L3',hlink:'#'}
            , {id: 'btnLSKCBGW', icon: 'kham', text: ' Lịch sử KCB trong tỉnh', hlink: '#'}
            , {id: 'btnHSSK', icon: 'kham', text: ' HSSK Cá nhân', hlink: '#'}
        ]
    }
    , {type: 'button', id: 'btnScanFile', icon: 'nhapkho', text: 'Scan file', hlink: '#'}
    , {type: 'button', id: 'btnTNTT', icon: 'treat_2', text: 'Nhập TNTT', hlink: '#'}
    , {type: 'button', id: 'btnTHANHTOANVP', icon: 'thutien', text: 'Thanh toán', hlink: '#'}
];

var integerRegex = /^\-?[0-9]+$/;
var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
var intalphabetRegex = /^\-?[A-Z0-9]+$/;

var objChuyenVien = new Object();
var objSinhTon = new Object();
var _b_flag = false;
var _b_flag_taothe_trem = false;
var _mabhytgoc = ""; 				// su dung khi BN chuyen tu the cu sang the moi;
var _nghenghiepdef = "12";
var _bochecktrungtt = "0";
var _ngayhientai = "";
var _ngayhientaict = "";
var _macoso = "CS1"; 					// mac dinh la CS1;
var _dongbodiachibhyt = "1"; 			// mac dinh dong bo;
var _ngtTinhTuoi = 0; 				// SONDN 06/01/2019 L2PT-13842
var _checkDungTuyenCapCuu = "0"; 	// SONDN 08/01/2019	L2PT-14814
var _tkbnf3 = "0"; 					// SONDN 10/01/2020 L2PT-14752
var _ttquannhan = "0";
var _checkBatBuocTE = "0";
var _batbuocSinhTon = "0";
var _modeGoiKham = "1";
var _timeOutGoiKham = "5000";
var _chedogoikham = "0";
var _tuyenTE = "0";
var _nhapTenNguoiThan = "0";
var _thoiGianTheTE = "Y6"; 				// 6 Years from DOB
var _timeOutRefresh = "10000";
var _batBuocCLSTiepNhan = 0;
var checkSLKham = 0;
var _dkkcbbd_nothe = "";
var _doicongkhamphongkham = "0";
var _nghenghiepbhyt = "0";
var _logs_qrcode = "";
var _anthongtinchuyentuyen = "0";
var _tiepnhantraingay = "0";
var _checkcongquetthe = "0";
var _canhbaotraituyen = "0";
var _thukhacvpi = "0";
var _batbuocsocv = "0";
var _macdinhdtgt = "0";
var _hisl3api_checkldt = "0";
var _dungtuyengioithieuldg = "0";
var _batbuocsdtbn = "0";
var _fillTheBHXHMoi = "0";
var _uutientuoi = "0";
var _uutienBHYT = "0";
var _thebhytksudung = "0";
var _tuyenmacdinhqt = "2"; 						// dung tuyen gioi thieu;
var _kskcnhl7 = "0";							// sondn L2PT-5119
var _dtrituyenduoingay = "0";
var _tkbngioitinh = "0"; 						// tim kiem benh nhan khi nhap xong thong tin gioi tinh;
var _cbktk = "0";
var _goikhamkcb = "0";
var _goikhamkcbid = "0";
var _dachongoikham = "0";
var _chedogoiloa = "0";
var _thutugoikham = "0";
var _sudungiqms = "0";
var _bhytdtmienphi = "0";
var _benhnhansource = "tiepnhan1"; 								// tiepnhan1, tiepnhan2, caykios, kios, iqms, tech
var _iqms_dmdp = "0";
var _iqms_capnhatsauqr = "0";
var _sinhthete = "0";
var _nhapthongtin_bnhd = "0";

var _checkthe10so = "0";
var b_MaBenhNhan1 = "";
var b_MaBenhNhan2 = "";
var b_Loai1 = "";
var _ngtsettuyen = "0";
var _bhytnghenghiep = "0";
var _clickchonbn = "0";
var _ansautiepdon = "0";
var _popupconthuoc = "0";
var _sudungnguonct = "0";
var NGT_HIENTHIMAU_DATLICH = "0";
var NGT_TN_SET_TUYEN_KCBBD = "0";
var NGT_GOIKHAM_HANGDOI = "0";
var check_note = ""; //L2PT-30940
var _checkconthuocct = "0";
var qd130 = 0;
var QD_4750 = 0;

var check_dangkham = 0;
var tk_madantoc = 0;
var _sql_yckham = "NGTDV.002"; //L2PT-53765 nang cap bvnt
var tuoi_checkttnguoinha = 0;
var check_dkkcbbdtuyen2 = 0;  //L2PT-50754

var _focus_mabn = 0;
var qrcode_vpi = '';

var sdt_number = 0;
var anthukhac = 0;
var CHECKTTHCBN = 0;
var noisong = "";
var _moiquanhe = "0";
var cfObj = new Object();
var NGT01T001_TIEPNHAN_NGT_CHON_TUYEN;
var lastChecked = null;
var _canhbaodungtuyengt = 0 ;
var _lichsudt = 0;

function NGT01T001(_opts) {
    this.load = doLoad;
    var _options = $.extend({}, _opts);
    var _param = _options._param;
    var _hd = "";								// =1: tiep nhan hop dong; =2: tiep nhan cap cuu;

    var intervalCall = null;
    var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NGT_TUOI_BATBUOCNGUOINHA;NGT_CHEDO_GOILOA;NGT_THUTU_GOIKHAM;OPT_HOSPITAL_ID;NGT_TN_EXCEL" +
        ";NGT_BUTTON_CHECKTHE;CONTROL_TIEPNHAN_HIDE;HIS_HIENTHI_GOIKHAM;NGT_LCD_TIEPDON_NHO;NGT_NGUONCT;NGT_LOADFIRST_PK;NGT_THEMTRANGTHAI_DSTN;NGT_TN_LOADPKTIMKEM_PHANQUYEN;" +
        "NGT_TG_TIEPNHAN1;HIS_QD130;QD_4750;NGT_TIMKIEM_MADANTOC;SDT_BN_NUMBER;NGT_TN_ANTHUKHAC;NGT_TN_CHECKNOISONG;NGT_TN_GHICHU;NGT_TN_DSDVGUIMAU;" +
        "NGT_TN_KHAMGIAMDINH;NGT_DANHDAU_GUISMS;NGT_TN_HIENTHITTCMND;NGT_LOADMADIADANH;NGT_TN1_KENHDK_SHOW;NGT_HIENTHI_QUYCHEBAOVEDLCN;NGT_TN_BATBUOC_VSSID;NGT_TN_SHOWINBARCODE;" +
        "NGT_TN_SHOWSOGIAYHENKHAM;NGT_TN1_NGHENGHIEP_SEARCHTEXT;IQMS_DMDP;IQMS_CAPNHATSAUQR;NGT_TN_BATBUOC_LYDOVAOVIEN;NGT_TN_SHOWTHONGTINBOME;LOAD_YEUCAUKHAM_THEO_DT;" +
        "HIS_SUDUNG_DOITUONG_KHAIBAO;NGT_TN_CHECKCC_NGOAIGIOHC;NGT_HIENTHITAB_THONGKE_TN2;NGT_SHOWTAB_THUOCVATTU_TN;NGT_HIENTHIMAU_DATLICH;NGT_TN_SET_TUYEN_KCBBD;NGT_TIEPNHAN_INDAMLABEL;" +
        "NGT_TIMKIEM_THEO_SDT;NGT_TN_DOITAB_DT_NS;NGT_TN_CAPNHAPTTHC;NGT_TN_HIDE_XOABN;NGT_CHECK_SOLUONG_PK_ALL;HIS_NOIDUNG_QUYCHEBAOVEDLCN;NGT_TN2_EMAIL;NGT_DANTOC_MACDINH;" +
        "DKHNM_LOAD_DV_DT;NGT_DSPHONGKHAM;NGT_PHONG_CD_TIEPNHAN;NGT_LCD_TIEPDON;FILL_TT_THE_TUCONG;NGT_CANHBAOKCBBD_DTP;NGT_THONGBAO_DULIEUTUYEN;KIEU_THONG_BAO_CHECKDK;" +
        "NGT_TN_CHECKCHUYENVIEN_DTGT;NGT_TN_CHUADUYETKT_CT;NGT_CHECKKHAMTRONGNGAY_NHIND;NGT_TN_CAPCUU;NGT_DAY_DL_EMR;NGT_IN_2_PHIEUKHAM");
    var config_ar1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "AUTO_LIENKET_IQMS_TN;CHAN_TN_DANGKHAM;NGT_POPUPCTY_TN2;NGT_TN_BATBUOC_LYDOVAOVIEN;NGT_TIEPNHAN_IN_NHANTRABHYT;" +
        "NGT_TN2_MH_THE_BHYT;NGT_UUTIENTUOI_SOTUOI;NGT_TN_CHECKKCBBD_TUYEN2;NGT_TIEPNHAN_SDT;NGT_SOTUOI_BATBUOCSDT;NGT_TENVIETTATDP_BNVODANH;NGT_TN_BATBUOC_VSSID;NGT_TN_GIUSOCCCD;" +
        "NGT_TN_REQ_5NAM6THANG;NGT_CANHBAOKCBBD_DTP;NGT_CHECK_CONGKHAMNHI;NGT_CHECK_TTHC_BN;NGT_TN_DISABLE_MABENHAN;NGT_LOADTHUKHAC_THEOPHONG;NGT_TN_CHECKLAMDUNG;NGT_TIEPNHAN_BNCU;" +
        "HIS_SHOW_DOITUONGTRUNGCAO;HIS_SHOW_TTBENHAN_WIDGET;CHECKTN65BHYT;VPI_ID_DVTT;NGT_NGHENGHIEP_THEOTUOI;NGT_CHECKTUOI_CONGKHAM;NGT_SINHQRCODEVPI;HIS_BATBUOCGIOSINH_SONGAY;" +
        "NGT_TN_BATBUOC_NGAYCAPGCT;KIOSK_SHOW_GOISOTN;NGT_MACDINHTHE;NGT_TN_DYNAMICGRID;NGT_TN_CHECKBHYT_NGOAITINH;NGT_AN_CD_CLS_BHYT;IQMS_MAKCBBD;HIS_HIENTHI_GOIKHAM_HT;HIS_DISABLED_DIACHIBN;" +
        "NGT_TNTK_GIOPHUTGIAY;NGT_TN_BATBUOC_HTCLDC;NGT_TIEPNHAN_TK_TG");

    if (config_ar != null && config_ar.length > 0) {
        cfObj = $.extend(config_ar[0], config_ar1[0]);
    } else {
        DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
        return;
    }
    _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
    _ngayhientaict = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
    tuoi_checkttnguoinha = cfObj.NGT_TUOI_BATBUOCNGUOINHA;
    _chedogoiloa = cfObj.NGT_CHEDO_GOILOA;
    _thutugoikham = cfObj.NGT_THUTU_GOIKHAM;
    NGT01T001_TIEPNHAN_NGT_CHON_TUYEN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT01T001_TIEPNHAN_NGT_CHON_TUYEN');

    function _replaceNullZero(vl, defaultValue, newValue) {
        return vl == null || vl == 'null'
        || vl == "" || typeof vl == 'undefined'
            ? defaultValue : newValue;
    }

    // 2 ham de xoa trang form, 2 ham nay co scope khac voi scope cua ham _refreshForm va ham resetThongTinCoBan
    function _refreshFormOut() {
        _b_flag = false;
        _mabhytgoc = "";
        _b_flag_taothe_trem = false;
        $("#lblSTTDonTiep").text("");
        $("#txtTENBENHNHAN").val("");
        $("#txtMABENHNHAN").val("");
        $("#txtNGAYSINH").val("");
        $("#txtNAMSINH").val("");
        $("#txtTUOI").val("");
        //$("#cboDVTUOI").val("1");

        //$("#cboGIOITINHID").val("");
        //$("#cboNGHENGHIEPID").val("");
        //$("#cboDANTOCID").val("");
        //$("#cboQUOCGIAID").val("");

        $("#txtSONHA").val("");
        $('#cboDIAPHUONGID').find('option').remove();
        $('#cboDIAPHUONGID').text("");
        //$("#cboHC_TINHID").val("");
        $("#cboHC_HUYENID").val("");
        $("#cboHC_XAID").val("");
        $("#cboDIABANID").val("");
        $("#txtNOILAMVIEC").val("");
        $("#txtNGUOITHAN").val("");
        $("#txtTENNGUOITHAN").val("");
        $("#txtDIACHINGUOITHAN").val("");
        $("#txtDIENTHOAINGUOITHAN").val("");
        $("#txtSDTBENHNHAN").val("");
        $("#txtDIACHI").val("");

        $("#txtSOCAPCUU").val("");
        $('#chkUUTIENKHAMID').attr('checked', false);
        $('#chkCHECKBHYTDV').attr('checked', false);
        //$("#txtNGAYTIEPNHAN").val("");
        $("#txtNGAYTIEPNHAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
        $("#cboPHONGKHAMID").val("");
        //$("#cboDTBNID").val("");
        $("#txtMA_BHYT").val("");
        $("#txtBHYT_BD").val("");
        $("#txtBHYT_KT").val("");
        $("#txtMA_KCBBD").val("");
        $("#cboMAKCBBD").find('option').remove();
        $('#cboMAKCBBD').text("");
        $("#txtDIACHI_BHYT").val("");
        //$("#cboBHYT_LoaiID").val("");
        $("#cboDT_SINHSONG").val(0);
        $('#chkDU5NAM6THANGLUONGCOBAN').attr('checked', false);
        $('#chkTRADU6THANGLCB').attr('checked', false);
        $('#chkDAGIUTHEBHYT').attr('checked', false);
        $('#chkCOGIAYKS').attr('checked', false);
        $("#txtQUYENLOI").val("");
        $("#txtMUCHUONG").val("Ngoại (0%)-Nội (0%)");
        $("#cboDICHVUID").val("");
        $("#txtTKMACHANDOANTUYENDUOI").val("");
        $('#txtCHANDOANTUYENDUOI').val('');
        $("#txtTKMANOIGIOITHIEU").val("");
        $("#cboMANOIGIOITHIEU").find('option').remove();
        $('#cboMANOIGIOITHIEU').text("");
        $("#hidPHONGID_CU").val("");
        $("#hidTIEPNHANID").val("");
        $("#hidBENHNHANID").val("");
        $("#hidBHYTID").val("");
        $("#hidPHONGKHAMDANGKYID").val("");
        $("#hidKHAMBENHID").val("");
        $("#hidXUTRIKHAMBENHID").val("");
        $("#hidMAUBENHPHAMID").val("");
        $("#hidHOSOBENHANID").val("");
        $("#hidDICHVUKHAMBENHID").val("");
        $("#hidMAKCBBD").val("");
        $("#grdDanhSachTiepNhan").jqGrid('resetSelection');
        $("#grdDSPhongDuocChon").jqGrid("clearGridData", true);
        //$("#"+_gridLS).jqGrid("clearGridData", true);
        $("#txtTENBENHNHAN").focus();

        $("#btnChuyenTuyen").attr("disabled", "disabled");
        GridUtil.unmarkAll('grdDanhSachTiepNhan');

        $("#txtTRANSACTIONID6").val("");
        $("#txtCUSTOMERID6").val("");
        $("#txtSTTKHAM6").val("");
        noisong = "";
    }

    function resetThongTinCoBanOut() {
        $("#hidTIEPNHANID").val("");
        $("#hidBENHNHANID").val("");
        $("#hidBHYTID").val("");
        $("#hidPHONGKHAMDANGKYID").val("");
        $("#hidKHAMBENHID").val("");
        $("#hidXUTRIKHAMBENHID").val("");
        $("#hidMAUBENHPHAMID").val("");
        $("#hidHOSOBENHANID").val("");
        $("#hidDICHVUKHAMBENHID").val("");
    }

    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        //tuyennx_add_start_20190424 L1PT-664
        if (cfObj.OPT_HOSPITAL_ID != null && cfObj.OPT_HOSPITAL_ID != 0) {
            opt.hospital_id = cfObj.OPT_HOSPITAL_ID;
        }
        //tuyennx_add_end_20190424 L1PT-664
        if (opt.hospital_id != 924) {
            $("#toolbarIdprint_3").addClass("hidden");
        }
        if (opt.hospital_id == 902) { //L2PT-53765 nang cap bvnt
            _sql_yckham = "NGTDV.002.NTI";
        }
        if (_opts.phongid == "0") {
            DlgUtil.showMsg("Yêu cầu thực hiện thiết lập phòng.", function () {
                window.location.replace("/vnpthis/main/manager.jsp?func=../admin/SelDept");
            });
            return;
        }


        if (cfObj.NGT_TN_EXCEL == 0) {
            $("#divExportExcel").hide();
        } else {
            $("#divExportExcel").show();
        }

        if (cfObj.NGT_BUTTON_CHECKTHE == "1") {
            $('#btnCheckThe').show();
        }

        if (cfObj.HIS_DISABLED_DIACHIBN == "1") {
            $('#txtDIACHI').prop('disabled', true);
        }

        _initControl(_opts);
        _bindEvent();
        _bindEventGoiKham();

        loadRISConfig();					// sondn L2PT-5119
    }

    function _initControl(_options) {
        var objTab1 = new NGT01T001_Tab1(_options);
        objTab1.load(_options.hospital_id);

        removeControl(cfObj.CONTROL_TIEPNHAN_HIDE);
    }

    function _bindEvent() {
        var sql_par = [];
        sql_par = RSUtil.setSysParam(sql_par, _param);
    }

    //======== added by SONDN CHO PHEP LOAD DU LIEU VAO STT GOI KHAM, CAP NHAT STT GOI KHAM ...
    function _bindEventGoiKham() {
        $("#txtSTTKHAM4").val("1");
        $("#txtSTTKHAM4").on('click', function () {
            this.select();
        });

        $("#txtstt_bd1").on('click', function () {
            this.select();
        });

        $("#txtstt_bd1").on('click', function () {
            this.select();
        });

        function formatNumber(str, num) {
            var str1 = str + "";
            while (str1.length < num) {
                str1 = "0" + str1;
            }
            return str1;
        }

        // cac ham cap nhat du lieu goi kham  lay theo phongid va user dang nhap;
        function goiKhamHienThi(source) {
            var par = [];
            var dt = null;
            var phongid = _opts.phongid;

            if (isNaN($("#txtstt_bd1").val().trim())) {
                DlgUtil.showMsg("Số thứ tự cần đúng định dạng");
                return;
            }

            var sql_par = [
                $("#txtstt_bd1").val().trim(),
                phongid,
                source
            ];

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('NGT02K053.LCD', sql_par.join('$'));
            if (data_ar != null && data_ar.length > 0) {
                for (i = 0; i < data_ar.length; i++) {
                    if (data_ar[i].CHECKED == "1") {
                        $("#txtTenQuay").text(data_ar[i].ORG_NAME);
                        $("#txtstt_bd1").val(formatNumber(data_ar[i].STT, 4));
                        $("#txtstt_kt1").val(formatNumber((Number(data_ar[i].STT) + 1), 4));
                        break;
                    }
                }
            }
        }

        $("#btnGoiLai1").on('click', function () {
            goiKhamHienThi("1");
        });

        // load du lieu ban dau va khoi tao can thiet;
        var dt = cfObj.HIS_HIENTHI_GOIKHAM;
        $("#hidHIENTHIGOIKHAM").val(dt);

        var dt1 = cfObj.HIS_HIENTHI_GOIKHAM_HT;

        var par10 = [_opts.phongid];
        var dtt = jsonrpc.AjaxJson.ajaxCALL_SP_S('NGT01T001.CHECKHT', par10.join('$'));

        if (dt == "0") {
            $("#dvSec5").hide();
            $("#dvCall").hide();
            $("#dvCall1").hide();
            $("#dvCall4").hide();
            $("#dvCall5").hide();
            $("#dvCall_KIOSK").hide();
        } else if (dt == "1") {					// DUNG CHUNG TAT CA CAC DON VI;
            $("#dvCall").show();
            $("#dvCall1").hide();
            $("#dvCall4").hide();
            $("#dvCall5").hide();
            $("#dvCall_KIOSK").hide();
        } else if (dt == "2") {					// BV NGUYEN TRAI;
            if (dt1 == "0" || (dt1 == "1" && dtt != "0")) {
                $("#dvCall").hide();
                $("#dvCall1").show();
                $("#dvCall4").hide();
                $("#dvCall5").hide();
                $("#dvCall_KIOSK").hide();
                goiKhamHienThi("0");
            } else {
                $("#dvSec5").hide();
                $("#dvCall").hide();
                $("#dvCall1").hide();
                $("#dvCall4").hide();
                $("#dvCall5").hide();
                $("#dvCall_KIOSK").hide();
            }
        } else if (dt == "5") {
            $("#dvCall").hide();
            $("#dvCall1").hide();
            $("#dvCall4").hide();
            $("#dvCall5").show();
            $("#dvCall_KIOSK").hide();
        } else {									// BUU DIEN;
            if (dt1 == "0" || (dt1 == "1" && dtt != "0")) {
                $("#dvCall").hide();
                $("#dvCall1").hide();
                $("#dvCall4").show();
                $("#dvCall5").hide();
                $("#txtSTTKHAM4").val("");
                $("#dvCall_KIOSK").hide();
            } else {
                $("#dvSec5").hide();
                $("#dvCall").hide();
                $("#dvCall1").hide();
                $("#dvCall4").hide();
                $("#dvCall5").hide();
                $("#dvCall_KIOSK").hide();
            }
        }
        if (cfObj.KIOSK_SHOW_GOISOTN == 1) {
            $("#dvSec5").show();
            $("#dvCall").hide();
            $("#dvCall1").hide();
            $("#dvCall4").hide();
            $("#dvCall5").hide();
            $("#dvCall6").hide();
            $("#dvCall17").hide();
            $("#dvCall_KIOSK").show();
        }
    }

    //======== end added by SONDN
    //======== BDHCM: LAY DU LIEU TU TECH;
    function getStandardDate(str) {
        if (str.length < 10) {
            return "";
        }
        return str.substring(8, 10) + "/" + str.substring(5, 7) + "/" + str.substring(0, 4);
    }

    function getStandardString(str) {
        var arr = str.split("-");
        var str1 = "";
        for (i = 0; i < arr.length; i++) {
            str1 += arr[i].trim();
        }
        return str1;
    }

    function goiKhamTech(source) {
        //goi kham cach cu bdhcm
        var stt = $("#txtSTTKHAM4").val().trim();
        var deptid = _opts.phongid;
        var par = [source, deptid, stt];
        var dt = jsonrpc.AjaxJson.ajaxCALL_SP_I('NGT02K088.STT', par.join('$'));
        return dt;

//		// goi kham cach moi bdhcm
//		var stt = $("#txtSTTKHAM4").val().trim();
//		var deptid = _opts.phongid;
//		var par = [deptid];
//		var dt = jsonrpc.AjaxJson.ajaxCALL_SP_I('NGT02K088.STT1', par.join('$'));
//		return dt;
    }

    $("#btnLAYSO4").on("click", function () {
        // goi kham bdhcm cu;
        var newstt = goiKhamTech("1");
        $("#txtSTTKHAM4").val(newstt);

        // goi kham bdhcm moi
//		var newstt = goiKhamTech("1");
//		if(Number(newstt) < 0){
//			DlgUtil.showMsg("Chưa có bệnh nhân mới");
//		}else{
//			$("#txtSTTKHAM4").val(newstt);
//		}
    });

    $("#btnSTART4").on("click", function () {
        $("#btnSTART4").prop('disabled', true);
        $("#btnFINISH4").prop('disabled', false);
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBN");
        if (data_ar != null && data_ar.length > 0) {
            call.goi_mot_bn_dangky(parseInt(data_ar[0].STT), parseInt(data_ar[0].SOPHONG), $("#txtNGTGOIBENHNHAN").val());
        }
        intervalCall = setInterval(function () {
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBN");
            if (data_ar != null && data_ar.length > 0) {
                call.goi_mot_bn_dangky(parseInt(data_ar[0].STT), parseInt(data_ar[0].SOPHONG), $("#txtNGTGOIBENHNHAN").val());
            }
        }, 15000);// set 15s
    });

    $("#btnFINISH4").on("click", function () {
        clearInterval(intervalCall);
        $("#btnSTART4").prop('disabled', false);
        $("#btnFINISH4").prop('disabled', true);
        interval = 0;
    });


    $("#btnLAYTHONGTIN4").on("click", function () {
        if ($('#hidTIEPNHANID').val() != "" || $('#txtMABENHNHAN').val() != "") {
            DlgUtil.showMsg("Yêu cầu làm mới trang trước khi nhập thông tin bệnh nhân mới. ");
            return;
        }

        var host = $("#txtURLWEBSITE").val().trim(); 			// link upload img
        var host1 = $("#txtURLWEBSITE1").val().trim(); 			// link local bd
        var stt = $("#txtSTTKHAM4").val().trim();

        if (stt == "" || isNaN(stt)) {
            DlgUtil.showMsg("Chưa nhập STT / STT không phải dạng số. ");
            return;
        }
        // dang nhap de lay API key:
        var user = $("#txtTECHUSER").val().trim();
        var pass = $("#txtTECHPASS").val().trim();
        if (user == "") {
            DlgUtil.showMsg("Chưa cấu hình thông tin đăng nhập TECH.");
            return;
        }

        // login vao lay thong tin dang nhap;
        var dt = null;
        var dt2 = null;
        // comment thong tin xac nhan;
        try {
            dt = ajaxSvc.TECHSvc.getLogin(host1, user, pass);
            dt2 = JSON.parse(dt);
        } catch (e2) {
            DlgUtil.showMsg("Lỗi không xác thực được thông tin đăng nhập", undefined, undefined, "error");
            return;
        }
        if (dt2.status.statusCode != 1) {
            DlgUtil.showMsg("Thông tin đăng nhập không đúng: " + dt2.status.statusDesc);
            return;
        }

        var userid = dt2.data.userId;
        var apikey = dt2.data.apiKey;
        // comment thong tin xac nhan;
//		var userid = "2";
//		var apikey = "";

        // var dt = "ImageLink###$$${\"patientSeq\":11,\"fullName\":\"NGUYỄN VĂN NỘI TRÚ 1\",\"birthday\":\"1981-01-28\",\"gender\":1,\"address\":\"Hà nội phố\",\"hasInsurrance\":1,\"insurranceId\":\"CA2123456789012\",\"hospitalId\":\"01 - C02\",\"startDate\":\"2018-01-01\",\"expirationDate\":\"2019-01-01\"}";
//		var dt = "newlink###$$${\"status\":{\"statusCode\":1,\"statusDesc\":\"success\"},\"data\":{\"patientSeq\":\"33\",\"fullName\":\"NGUYỄN VĂN NỘI TRÚ 1\",\"birthday\":\"1981-01-28\",\"gender\":\"1\",\"address\":\"Hà nội phố\",\"hasInsurrance\":\"1\",\"insurranceId\":\"CA2123456789012\",\"hospitalId\":\"87 - 079\",\"startDate\":\"2018-01-01\",\"expirationDate\":\"2019-01-01\",\"poorId\":\"\",\"UUTIENKHAMID\":\"null\",\"lydouutien\":\"null\"}}";
        // SONDN 20190513 L2PT-4267
        var dt = ajaxSvc.TECHSvc.getPatientInfo(host1, host, userid, apikey, stt, _macoso);

        try {
            var dtt = JSON.parse(dt);
            DlgUtil.showMsg("Có lỗi xảy ra, không lấy được thông tin. ", undefined, undefined, "error");
        } catch (e) {
            var dt1 = dt.substring(0, dt.indexOf("###$$$"));
            var dt2 = dt.substring(dt.indexOf("###$$$") + 6, dt.length);
            try {
                var dt3 = JSON.parse(dt2);
                if (dt3.status.statusCode != "1") {
                    if (dt3.status.statusCode == "6") {
                        DlgUtil.showMsg("Tham số truyền vào không phù hợp. ");
                    } else if (dt3.status.statusCode == "7") {
                        DlgUtil.showMsg("Thông tin xác thực sử dụng không đúng. ");
                    } else if (dt3.status.statusCode == "8") {
                        DlgUtil.showMsg("Không tìm thấy thông tin xác thực phù hợp. ");
                    } else if (dt3.status.statusCode == "200") {
                        DlgUtil.showMsg("Lỗi chức năng, yêu cầu kiểm tra lại ", undefined, undefined, "error");
                    } else {
                        DlgUtil.showMsg("Không tìm thấy STT, hoặc có lỗi chức năng. Vui lòng kiểm tra lại. ", undefined, undefined, "error");
                    }
                    return;
                }
                // TUY VAO BENH VIEN SE CO CACH SET MAC DINH DOI TUONG KHAI BAO DUOC CHON KHAC NHAU.
                var doituong_khaibao = $("#txtDTKHAIBAO").val();
                if (dt3.data.hasInsurrance == 1) {
                    if (doituong_khaibao == "1") {
                        $("#cboDTBNID").find("option[extval0='2']").attr("selected", "selected");		// doi tuong BHYT ngoài ngành
                    } else {
                        $("#cboDTBNID").val("1");
                    }
                    $('#cboDTBNID').change();

                    $("#txtMA_BHYT").val(dt3.data.insurranceId);
                    $("#txtMA_BHYT").trigger("focusout");
                    $("#txtMA_KCBBD").val(getStandardString(dt3.data.hospitalId));
                    $('#txtMA_KCBBD').combogrid("setValue", getStandardString(dt3.data.hospitalId));
                    $("#txtBHYT_BD").val(getStandardDate(dt3.data.startDate));
                    $("#txtBHYT_KT").val(getStandardDate(dt3.data.expirationDate));
                    $("#txtDIACHI_BHYT").val(dt3.data.address);
                } else {
                    if (doituong_khaibao == "1") {
                        $("#cboDTBNID").find("option[extval0='6']").attr("selected", "selected");
                    } else {
                        $("#cboDTBNID").val("2");
                    }
                    $('#cboDTBNID').change();
                }

                $("#txtTENBENHNHAN").val(dt3.data.fullName);
                if (dt3.data.birthday.length < 10) {
                    $("#txtNAMSINH").val(dt3.data.birthday);						// nam sinh;
                    $("#txtNAMSINH").change();
                } else {
                    $("#txtNGAYSINH").val(getStandardDate(dt3.data.birthday));
                    $('#txtNGAYSINH').change();
                }

                $("#cboGIOITINHID").val(dt3.data.gender);
                $("#txtTKGIOITINHID").val(dt3.data.gender);
                $("#txtDIACHI").val(dt3.data.address);
                $("#txtDIACHI_BHYT").val(dt3.data.address);

//				imgTag.src = "../common/image/patientImage.jpg";
                var imgTag = document.getElementById('imgBN');
                imgTag.src = dt1;
                _benhnhansource = "tech";
            } catch (e1) {
                DlgUtil.showMsg("Lỗi dữ liệu trả về. Yêu cầu kiểm tra lại.", undefined, undefined, "error");
                var imgTag = document.getElementById('imgBN');
                imgTag.src = "../common/image/patientImage.jpg";
            }
        }
    });

    $("#btnLAYBN4").on('click', function () {
        var param = "";
        window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCDBD1&showMode=dlg', '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
    });

    $("#btnGOILAI4").on('click', function () {
        var vl = $("#txtGOILAI4").val().trim();
        if (isNaN(vl) || vl == "") {
            DlgUtil.showMsg("Giá trị gọi lại phải là số", function () {
                $("#txtGOILAI4").focus();
            });
            return;
        }
        var deptid = _opts.phongid;
        var par = [deptid, vl];
        var dt = jsonrpc.AjaxJson.ajaxCALL_SP_I('NGT02K088.GL', par.join('$'));
        if (Number(dt) < 0) {
            DlgUtil.showMsg("Lỗi không gọi lại được STT. ", undefined, undefined, "error");
        } else {
            $("#txtGOILAI4").val("");
        }
    });

    //======== END LAY DU LIEU TU TECH;

    //======== START SU KIEN CHO BV BACH MAI 2;
    $("#btnGOITIEP5").on('click', function () {
        _goisttbm2_1("3", "1", _chedogoiloa, "0", _opts.phongid, _thutugoikham);
    });

    $("#btnGOITIEPUT5").on('click', function () {
        _goisttbm2_1("3", "1", _chedogoiloa, "1", _opts.phongid, _thutugoikham);
    });

    $("#btnGOILAI5").on('click', function () {
        _goisttbm2_1("3", "2", _chedogoiloa, "0", _opts.phongid, _thutugoikham); 								// goi lai k quan tam uu tien nua;
    });
    //DANND_GOISOKIOSKK
    $("#btnGoiBN").bindOnce("click", function () {
        var obj = new Object();
        obj.PHONGID = _opts.phongid;
        var r = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.CALLKIOSK", JSON.stringify(obj));
        var rs = r.split('@');
        if (rs[1] && r != -1) {
            var _text = rs[0] + " " + rs[1] + " chưa tiếp nhận , vui lòng đến " + rs[2] + " " +  rs[3] ;
            $('#txtGoiSo').val(_text);
            if (_text) {
                goiKhamGG(_text, "0", 5000);
            }
        } else {
            $('#txtGoiSo').val("Hết số");
        }
    });

    $("#btnLCDNHO5").on("click", function () {
        /*var param = "";
		if (_options.hospital_id == "923"){				// DKLCI
			// danh sach BN chan le;
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCD_923_DS&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		}else{
			// Chi tiet BN dang kham;
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCDBM21&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		}*/
        var dt = cfObj.NGT_LCD_TIEPDON_NHO;
        window.open('manager.jsp?func=..' + dt + '&showMode=dlg', '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');

    });

    $("#btnDSGOILAI5").on("click", function () {
        var myVar = {
            phongid: _opts.phongid
        };
        dlgPopup = DlgUtil.buildPopupUrl("dlgCV", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K053_BM2_BNLOHEN"
            , myVar, "Danh sách bệnh nhân gọi lại", 750, 400);
        DlgUtil.open("dlgCV");

    });

    EventUtil.setEvent("evt_kios_bnlohen", function (e) {
        if (typeof (e) != 'undefined') {
            DlgUtil.close("dlgCV");
//			DlgUtil.showMsg("Đã gọi lại bệnh nhân " + e.tenbenhnhan);
            $("#txtID5").val(e.id);
            _goisttbm2_1("3", "2", _chedogoiloa, "0", _opts.phongid, _thutugoikham);				// goi lai k quan tam dai uu tien nua;
        }
    });


    //========= END SU KIEN CHO BV BACH MAI 2

    //========= HIS_HIENTHI_GOIKHAM = 6: IQMS BDHCM
    function _goikhamIQMS(_counterId, _counterHISL2Id, _ticketNumber) {
        /*if ($("#hidBENHNHANID").val() != ""){
			DlgUtil.showMsg("Đã có thông tin bệnh nhân trên form, yêu cầu refresh trước khi gọi số");
			return;
		}

		if ($("#txtTRANSACTIONID6").val() != ""){
			DlgUtil.showMsg("Đã có thông tin bệnh nhân iQMS, yêu cầu refresh trước khi gọi số");
			return;
		}*/

        _refreshFormOut();
        resetThongTinCoBanOut();

        var ret = _IQMS_GOISO("5eba6815584cbf0009bfd026", _counterHISL2Id, _ticketNumber);
        if (ret.statusCode == "000") {
            try {
                var _kq = JSON.parse(ret.data);
                if (_kq.errorCode == "0") {
                    var _id = _kq.data.id;
                    _id = _replaceNullZero(_id, "", _id);
                    $("#txtTRANSACTIONID6").val(_id); 								// thiet lap id giao dich;
                    var _ticketNumber = _kq.data.ticketNumber;
                    _ticketNumber = _replaceNullZero(_ticketNumber, "", _ticketNumber);
                    $("#txtSTTKHAM6").val(_ticketNumber);
                    var _plevel = _kq.data.plevel;
                    _plevel = _replaceNullZero(_plevel, "3", _plevel); 		// 1 UT;2 Dat lich;3 Thuong

                    var _genderNumber = _kq.data.customer.genderNumber;
                    _genderNumber = _replaceNullZero(_genderNumber, "-1", _genderNumber);
                    var _birthday = _kq.data.customer.birthday;
                    _birthday = _replaceNullZero(_birthday, "", _birthday);
                    /*var _birthyear = "";
					if (_birthday.length == 10){
						_birthyear = _birthday.substring(6);
					}*/
                    var _birthyear = "";
                    if (_kq.data.customer.birthyear) {
                        _birthyear = _kq.data.customer.birthyear;
                    } else {
                        if (_birthday.length == 10) {
                            _birthyear = _birthday.substring(6);
                        }
                    }
                    var _firstName = _kq.data.customer.firstName;
                    _firstName = _replaceNullZero(_firstName, "", _firstName);
                    var _address = _kq.data.customer.addressFull;				// dia chi BN
                    _address = _replaceNullZero(_address, "", _address);
                    var _customerId = _kq.data.customer.id;
                    _customerId = _replaceNullZero(_customerId, "", _customerId);
                    $("#txtCUSTOMERID6").val(_customerId);
//					var _phone = _kq.data.customer.phone; 													// BDHCM
                    var _phone = _kq.data.customer.localPhone; 												// LVVPC
                    _phone = _replaceNullZero(_phone, "", _phone);
                    var _idHIS = _kq.data.customer.idHIS; 												// LVVPC
                    _idHIS = _replaceNullZero(_idHIS, "", _idHIS);
                    var _socialInsurance = _kq.data.customer.socialInsurance.data;
                    _socialInsurance = _replaceNullZero(_socialInsurance, "", _socialInsurance);

                    var _ethnicId = "25";  									// dan toc kinh;
                    if (_kq.data.customer.hasOwnProperty("ethnicInfo")) {
                        if (_kq.data.customer.ethnicInfo.hasOwnProperty("idHisL2")) {
                            _ethnicId = _kq.data.customer.ethnicInfo.idHisL2;
                            _ethnicId = _replaceNullZero(_ethnicId, "", _ethnicId);
                        }
                    }

                    var _provinceId = "000"; 				// TP HCM
                    var _sonha = "";
                    if (_kq.data.customer.hasOwnProperty("addressInfo")) {
                        if (_kq.data.customer.addressInfo.hasOwnProperty("province")) {
                            if (_iqms_dmdp == "0") {
                                // bdhcm;
                                if (_kq.data.customer.addressInfo.province.hasOwnProperty("idHisL2")) {
                                    _provinceId = _kq.data.customer.addressInfo.province.idHisL2;
                                    _provinceId = _replaceNullZero(_provinceId, "", _provinceId);
                                }
                            } else {
                                // bdakhoa
                                if (_kq.data.customer.addressInfo.province.hasOwnProperty("provinceId")) {
                                    _provinceId = _kq.data.customer.addressInfo.province.provinceId;
                                    _provinceId = _replaceNullZero(_provinceId, "", _provinceId);
                                }
                            }
                        }
                        if (_kq.data.customer.addressInfo.hasOwnProperty("address")) {
                            _sonha = _kq.data.customer.addressInfo.address;
                        }
                    }
                    var _countryId = "0"; 					// Viet Nam
                    if (_kq.data.customer.hasOwnProperty("countriesInfo")) {
                        if (_kq.data.customer.countriesInfo.hasOwnProperty("idHisL2")) {
                            _countryId = _kq.data.customer.countriesInfo.idHisL2;
                            _countryId = _replaceNullZero(_countryId, "", _countryId);
                        }

                    }

                    // ghi logs iQMS truong hop thanh cong;
                    var _objj = new Object();
                    _objj.JOBTYPE = "GOISO THANH CONG";
                    _objj.TRANSACTIONID = _id;
                    _objj.CUSTOMERID = _customerId;
                    _objj.JSONDATA = ret.data;
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("IQMS.LOGS", JSON.stringify(_objj));
                    _benhnhansource = "iqms"; 															// set benh nhan tiep nhan iqms;
                    // ket thuc ghi logs;

                    if (_idHIS != "") {										// neu co ma BN: load ma BN len truoc;
                        $("#txtMABENHNHAN").val(_idHIS);
                        var e = jQuery.Event("keydown");
                        e.which = 13; // Enter
                        $("#txtMABENHNHAN").trigger(e);
                    }

                    if (_socialInsurance != "") {
//						$("#cboDTBNID").find("option[extval0='2']").attr("selected","selected");			// bhyt ngoai nganh
                        $("#cboDTBNID").val(1); 			// bhyt
                        $('#cboDTBNID').change();

                        $("#txtMA_BHYT").val(_socialInsurance);
                        if (_idHIS == "") {												// neu k co ma BN thi load theo the BHYT;
                            $("#txtMA_BHYT").trigger("focusout");
                        }

                        // neu chi co ma the BHYT thi load them thong tin bo sung;
                        if (_socialInsurance.length <= 15 || _iqms_capnhatsauqr == "1") {
                            $("#cboGIOITINHID").val(_genderNumber);
                            $("#txtTENBENHNHAN").val(_firstName);
                            $("#txtDIACHI").val(_address);
                            $("#txtSONHA").val(_sonha);
                            $("#txtSDTBENHNHAN").val(_phone);
                            $('#chkUUTIENKHAMID').prop('checked', _plevel == "1" ? true : false);
                            $("#cboDANTOCID").val(_ethnicId);
                            $("#txtTKDANTOCID").val(_ethnicId);
                            $("#cboQUOCGIAID").val(_countryId);
                            $("#txtTKQUOCGIAID").val(_countryId);

                            if (_idHIS == "") {
                                $("#txtTKHC_TINHID").val(_provinceId);
                                ComboUtil.findByExtra("cboHC_TINHID", $('#txtTKHC_TINHID').val(), 0);
                            }

                            $("#txtNGAYSINH").val(_birthday);
                            $("#txtNAMSINH").val(_birthyear);
                            $("#txtNGAYSINH").change();
                        }
                    } else {
//						$("#cboDTBNID").find("option[extval0='6']").attr("selected","selected");			// vien phi;
                        $("#cboDTBNID").val(2); 							// vien phi;
                        $('#cboDTBNID').change();

                        $("#cboGIOITINHID").val(_genderNumber);
                        $("#txtTENBENHNHAN").val(_firstName);
                        $("#txtDIACHI").val(_address);
                        $("#txtSONHA").val(_sonha);
                        $("#txtSDTBENHNHAN").val(_phone);
                        $('#chkUUTIENKHAMID').prop('checked', _plevel == "1" ? true : false);
                        $("#cboDANTOCID").val(_ethnicId);
                        $("#txtTKDANTOCID").val(_ethnicId);
                        $("#cboQUOCGIAID").val(_countryId);
                        $("#txtTKQUOCGIAID").val(_countryId);

                        if (_idHIS == "") {
                            $("#txtTKHC_TINHID").val(_provinceId);
                            ComboUtil.findByExtra("cboHC_TINHID", $('#txtTKHC_TINHID').val(), 0);
                        }

                        $("#txtNGAYSINH").val(_birthday);
                        $("#txtNAMSINH").val(_birthyear);
                        $("#txtNGAYSINH").change();
                    }
                } else {
                    DlgUtil.showMsg("Thông báo: " + _kq.errorMsg);
                }
            } catch (e) {
                DlgUtil.showMsg("Lỗi lấy thông tin từ API. Yêu cầu kiểm tra lại. ", undefined, undefined, "error");
                return;
            }

        } else if (ret.statusCode == "099") {
            // qua thoi gian quy dinh;
            DlgUtil.showMsg("Không kết nối được tới API lấy dữ liệu (API Timeout). ", undefined, undefined, "error");
            return;
        } else {
            // khong co du lieu;
            DlgUtil.showMsg("Không kết nối được tới API lấy dữ liệu. ", undefined, undefined, "error");
            return;
        }
    }

    $("#btnLAYTHONGTIN6").on("click", function () {
        _goikhamIQMS("5eba6815584cbf0009bfd026", _opts.phongid, "");
    });

    $("#btnLAYSO6").on("click", function () {
        var vl = $("#txtSTTKHAM6").val().trim();
        if (isNaN(vl) || vl == "") {
            DlgUtil.showMsg("Giá trị gọi lại phải là số", function () {
                $("#txtSTTKHAM6").focus();
            });
            return;
        }
        _goikhamIQMS("5eba6815584cbf0009bfd026", _opts.phongid, vl);
    });

//	$("#btnGOILAI6").on("click", function(){
//		var vl = $("#txtGOILAI6").val().trim();
//		if (isNaN(vl) || vl == ""){
//			DlgUtil.showMsg("Giá trị gọi lại phải là số", function(){
//				$("#txtGOILAI6").focus();
//			});
//			return;
//		}
//		var deptid = _opts.phongid;
//		var par = [deptid, vl];
//		var dt = jsonrpc.AjaxJson.ajaxCALL_SP_I('NGT02K088.GL', par.join('$'));
//		if(Number(dt) < 0) {
//			DlgUtil.showMsg("Lỗi không gọi lại được STT. ");
//		}else{
//			$("#txtGOILAI4").val("");
//		}
//	});
    //========= END HIS_HIENTHI_GOIKHAM = 6: IQMS BDHCM
}

//------------------------------------------------------------------------------------------------------------------------------------
//DANH SÁCH TIẾP NHẬN
//------------------------------------------------------------------------------------------------------------------------------------
function NGT01T001_Tab1(opt) {
    _hd = getParameterByName('hd', window.location.search.substring(1));
    _sudungnguonct = cfObj.NGT_NGUONCT;

    if (_hd != undefined && _hd === '1') {
        // sondn L2PT-24929
        ComboUtil.getComboTag("cboHOPDONGID", "DS.HOPDONG1", [{"name": "[0]", "value": "0"}], "", {
            value: 0,
            text: 'Chọn hợp đồng'
        }, "sql", "", "");
        $('#hdkhambenh').css('display', '');

        if (_sudungnguonct == "1") {
            $("#dvNGUONCT").show();
            ComboUtil.getComboTag("cboNGUONCT", "DMC01.NGUONCT", [], '', {
                value: '0',
                text: 'Chọn Nguồn CT'
            }, "sql", "", false);
        }
    }

    var LNMBP_XetNghiem = 1;
    var LNMBP_CDHA = 2;
    var _flgModeView = '0';
    var LNMBP_ChuyenKhoa = 5;
    var LNMBP_Phieuvattu = 8;
    var LNMBP_Phieuthuoc = 7;

    this.load = doLoad;
    var flagLoading = false;
    var _options = $.extend({}, opt);
    var uuid = _options._uuid;
    var _param = _options._param;
    var that = this;
    var _gridLS = "grdLSU_DIEUTRI";
    var _colDIAPHUONG = "Tên viết tắt,TENVIETTATDAYDU,30,0,f,l;Địa phương,NAME,70,0,f,l";
    var _colDKKBD = "Mã bệnh viện,BENHVIENKCBBD,15,0,f,l;Tên bệnh viện,TENBENHVIEN,30,0,f,l;Địa chỉ,DIACHI,50,0,f,l;Ghi chú,GHICHU,50,0,t,l";
    var _colDKKCBBD = "Mã BV,BENHVIENKCBBD,10,0,f,l;Bệnh viện,TENBENHVIEN,40,0,f,l;Địa chỉ,DIACHI,50,0,f,l";
    var _colICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
    var docmathe = false;
    var _capnhathd = "";
    var _sophongtiepdon = "0";
    var _hinhthucvaovien = "3";
    var _songayluiBHYT = "1";
    var _inphieukhamtudong = "0";
    var _mopopupphongkham = '0';
    var i_u = ""; 					// BHXH
    var i_p = "";
    var i_u1 = "";					// BYT
    var i_p1 = "";
    var i_macskcb = "";
    var _check_the_bhyt_bhxh = 0;
    var _maKetQuaCheckBHXK = '000';
    var _chuaduyetkt = 0;
    var _loadyeucaukhamtheodt = 0;
    var _yeucaukhammacdinh = '';
    var doituong_khaibao = 0;
    var _trangthaikhambenhcheck = 0;

    var _loadFirst = 1;
    var _loadFirstDTBN = 1;
    var _loadFirstYCKham = 1;
    var _loadFirstPhongKham = Number(cfObj.NGT_LOADFIRST_PK); 					// DEM SO LUOT LOAD PHONG KHAM
    var _loadFirstDoiTuongKCB = 1;
    var _thaoTacHientai = "";

    var _sttsangchieu = "0";
//	var _transidCHECKIN = "";
    var _transidLAMDUNGTHE = "";
    var _setkcbbd = "0"; 				// set/ bo set noi kcbbd sau khi load form;
    var _sudungsocapcuu = "0";
    var _nhaptinhhuyenxa = "0";
    var hosobenhanid_compare = "0";
    var _tgtiepnhan = "0";
    //dannd_L2PT-34927
    var nhanthuochen = "0";
    var loadtuyen = 0;
    var dantocdef = 0;
    var _sqldiaphuong = "DMDP.001";
    var check_madauthengoaitinh = 0;
    var check_dieutritrongkhoangtg = 0;
    var NGT_TN_CHECKNOISONG = 0;
    var showTTBNWidget = false;

    function doLoad(_hosp_id, _id) {
        $("#hidUserID").val(opt.user_id);

        if (opt.hospital_id != 924) {
            $("#toolbarIdgroup_2_6").addClass("hidden");
        }

        _initControl();
        initPopup_TTBN_NHIHDG();
        initPopup_QUYCHE();
        this.validator = new DataValidator("divContent");
        _bindEvent();
    }

    function _initControl() {
        $("#hidHisId").val(opt.hospital_id);

        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _ngayhientai});
        var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT", sql_par);
        vsothutu = JSON.parse(vsothutu);
        sql_par = [];
        var vsothutu2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("LAY.CAUHINH1", sql_par.join('$'));
        var _kqJSONBHXH = ""; 									// luu thong tin chuoi tra ve bhxh;
        $("#hidTUYENMACDINH").val(vsothutu[0].TUYENMACDINH == "0" ? "1" : vsothutu[0].TUYENMACDINH);
        var _sql_pr = [];
        _sql_pr.push({
            "name": "[0]",
            value: _opts.phongid
        });
        check_note = jsonrpc.AjaxJson.getOneValue("NGT.PHONG.NOTE", _sql_pr) //L2PT-30940
        $("#hidCHECKNOTE").val(check_note);//L2PT-43429

        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _ngayhientai});
        var vstt_goi = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_GOI", sql_par);
        vstt_goi = JSON.parse(vstt_goi);

        var _obj1 = new Object();
        if (vsothutu2[0].HIENTHIGOIKHAM == "1") {
            _obj1.MODE = "0"; 				// CHE DO HIEN THI;
            _obj1.PHONGID = _opts.phongid;
            _obj1.KHOAID = _options._khoaid;
            _obj1.STTBD = $("#txtstt_bd").val() == "" ? "0" : $("#txtstt_bd").val();
            _obj1.SLGOI = $("#txtsl_goi").val() == "" ? "1" : $("#txtsl_goi").val();

            var vstt_goi1 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T001.STT1", JSON.stringify(_obj1));
            if (typeof vstt_goi1 != 'undefined') {
                var row1 = vstt_goi1.split('@');
                $('#txtstt_bd').val(row1[1]);
                $('#txtstt_kt').val(row1[2]);
                $('#txtsl_goi').val(row1[3]);
            }
        }

        // SONDN 20190513 L2PT-4267
        sql_par = [_opts._khoaid];
        _macoso = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T001.COSO", sql_par.join('$'));			// mac dinh cs1;

        docmathe = false;
        var par_ctl = ['TOOLBAR_MENU_TIEPNHAN1'];
        var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', par_ctl.join('$'));
        var ctl_ar1 = JSON.parse(ctl_par);
        if (ctl_ar1 != '0') {
            ToolbarUtil.build('toolbarId', ctl_ar1);
        } else {
            ToolbarUtil.build('toolbarId', ctl_ar);
        }
        _setgiatribatbuoc(['lblHuyen', 'lblXa', 'lblSoThe', 'lblTuNgay', 'lblDenNgay', 'lblTuyen', 'lblKCBBD', 'lblDCBHYT'], '(*)');
        _setgiatribatbuoc(['lblCDTD', 'lblNoiChuyen'], '');

        var d = new Date();
        var year = d.getFullYear();
        $('#txtBHYT_BD').val('01/01/' + year);
        //$('#txtBHYT_KT').val('31/12/'+year);

        // doi tab
        var dt10 = cfObj.NGT_TN_DOITAB_DT_NS;
        if (dt10 == "1") {
            $("#divNOISONG1").remove();
            $("#divDOITUONG2").remove();
            $("#divDOITUONG22").remove();
        } else {
            $("#divDOITUONG1").remove();
            $("#divNOISONG2").remove();
            $("#divNOISONG22").remove();
        }

        // an nut chuyen tuyen
        $("#btnChuyenTuyen").attr("disabled", "disabled");

        // load cbo trang thai trong dsbn tiếp nhận
        ComboUtil.getComboTag("cboTRANGTHAI", "NGT02K009.RV005", [], "", {
            value: 0,
            text: '--Trạng thái--'
        }, "sql", "", "");

        if (parseInt(cfObj.NGT_THEMTRANGTHAI_DSTN) == 1) {
            $('#cboTRANGTHAI').append("<option value='6'>Nhập viện</option>");
            $('#cboTRANGTHAI').append("<option value='7'>Chuyển viện</option>");
        }
        if (parseInt(cfObj.NGT_TN_LOADPKTIMKEM_PHANQUYEN) == 1) {
            ComboUtil.getComboTag("cboTKPK", "LOADPK.TIMKIEM3", [], "", {
                value: 0,
                text: '--Phòng khám--'
            }, "sql", "", "");
        } else {
            ComboUtil.getComboTag("cboTKPK", "LOADPK.TIMKIEM", [], "", {
                value: 0,
                text: '--Phòng khám--'
            }, "sql", "", "");
        }
        ComboUtil.getComboTag("cboDT_SINHSONG", "NT.0010", [{"name": "[0]", "value": "76"}], "", {
            value: '0',
            text: 'Chọn'
        }, "sql", 'dt_sinhsong', '');
        ComboUtil.getComboTag("cboDOITUONGDB", "DMC.DTDACBIET", [], "", {
            extval: true,
            value: 0,
            text: ''
        }, "sql", "", "");
        $("#cboDVTHUKHAC").select2();
        $("#cboNGUONKHACHID").select2();
        $("#cboNHOMNGUON_ID").select2();
        ComboUtil.getComboTag("cboDVTHUKHAC", "DMC.DVTHUKHAC", [], "", {value: 0, text: ''}, "sql", "", "");
        ComboUtil.getComboTag("cboKENHDK", "NGTHK.KIEUHENKHAM", [], "", {
            extval: true,
            value: '-1',
            text: '---Kênh DK---'
        }, 'sql', '', ''); // BVTM-4058
        var sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": 82});
        ComboUtil.getComboTag("cboCV_CHUYENVIEN_HINHTHUCID", "NGT02K009.RV002", sql_par1, "", {
            extval: true,
            value: '',
            text: '-- Chọn hình thức chuyển --'
        }, 'sql');
        sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": 84});
        ComboUtil.getComboTag("cboCV_CHUYENVIEN_LYDOID", "NGT02K009.RV002", sql_par1, "", {
            extval: true,
            value: '',
            text: '-- Chọn lý do chuyển --'
        }, 'sql');

        var sql_par = [];
        ComboUtil.initComboGrid("txtTKDIAPHUONGID", "DMDP.002", [], "600px", _colDIAPHUONG, function (event, ui) {
            var option = $('<option value="' + ui.item.VALUE + '">' + ui.item.NAME + '</option>');
            $("#cboDIAPHUONGID").empty();
            $("#cboDIAPHUONGID").append(option);
            getDiaChi(ui.item.VALUE, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', docmathe);
            var sonha = "";
            if ($('#txtSONHA').val() != "" && $('#txtSONHA').val() != null) {
                sonha = $('#txtSONHA').val() + "-";
            }
            $('#txtDIACHI').val(sonha + ui.item.NAME);
            // SONDN L2PT-14425 01/01/2020
            if ($("#cboNGHENGHIEPID").val() == "3") {
                $('#txtDIACHINGUOITHAN').val(sonha + ui.item.NAME);
            }
            // END SONDN L2PT-14425 01/01/2020
            return false;
        }, [20, 100, 200, 300], 20);

        ComboUtil.initComboGrid("txtTKMANOIGIOITHIEU", "NT.009", [], "800px", _colDKKBD, function (event, ui) {
            $("#txtTKMANOIGIOITHIEU").val(ui.item.BENHVIENKCBBD);
            var option = '<option value="' + ui.item.BENHVIENKCBBD + '">' + ui.item.TENBENHVIEN + '</option>';
            $("#cboMANOIGIOITHIEU").empty();
            $("#cboMANOIGIOITHIEU").append(option);

            // sondn L2PT-30168
            if (_dungtuyengioithieuldg == "1" && $("#cboBHYT_LoaiID").val() == "2") {
                if (ui.item.BENHVIENKCBBD == "68650") {
                    $("#txtSOCHUYENVIEN").val("Hẹn");
                    $("#cboCV_CHUYENVIEN_HINHTHUCID").val("4");
                    $("#cboCV_CHUYENVIEN_LYDOID").val("0");
                } else if (ui.item.TUYEN == "4" || (ui.item.TUYEN == "3" && ui.item.GHICHU == "PK")) {			// TYT, PK
                    $("#cboCV_CHUYENVIEN_HINHTHUCID").val("2");
                    $("#cboCV_CHUYENVIEN_LYDOID").val("1");
                } else if (ui.item.TUYEN == "3" && (ui.item.GHICHU == "TTYT" || ui.item.GHICHU == "BV")) {		// TTYT
                    $("#cboCV_CHUYENVIEN_HINHTHUCID").val("1");
                    $("#cboCV_CHUYENVIEN_LYDOID").val("1");
                } else {
                    $("#cboCV_CHUYENVIEN_HINHTHUCID").val("4");
                    $("#cboCV_CHUYENVIEN_LYDOID").val("1");
                }
            }
            // end sondn L2PT-30168
            return false;
        });

        ComboUtil.initComboGrid("txtDV_CAPGIAY", "NT.009", [], "800px", _colDKKBD, function (event, ui) {
            $("#txtDV_CAPGIAY").val(ui.item.BENHVIENKCBBD);
            var option = '<option value="' + ui.item.BENHVIENKCBBD + '">' + ui.item.TENBENHVIEN + '</option>';
            $("#cboTEN_DV_CAPGIAY").empty();
            $("#cboTEN_DV_CAPGIAY").append(option);
            return false;
        });

        ComboUtil.initComboGrid("txtMA_KCBBD", "NT.009", [], "800px", _colDKKBD, function (event, ui) {
            if ($("#txtKSUDUNGKCBBD").val().indexOf(ui.item.BENHVIENKCBBD + ";") != -1) {
                DlgUtil.showMsg("Không cho phép chọn nơi KCBBD này. ");
                return;
            }

            $("#txtMA_KCBBD").val(ui.item.BENHVIENKCBBD);
            var option = '<option value="' + ui.item.BENHVIENKCBBD + '">' + ui.item.TENBENHVIEN + '</option>';
            $("#cboMAKCBBD").empty();
            $("#cboMAKCBBD").append(option);
            var loaibhyt = 0;
            var kcbbd = $("#txtKCBBD").val().trim();
            if (loadtuyen == 0) {
                if (NGT_TN_SET_TUYEN_KCBBD == "0") {
                    if (_ngtsettuyen == "0") {
                        if (kcbbd == "0" || kcbbd == "") {
                            if (ui.item.BENHVIENKCBBD != _options.hospital_code) {
                                loaibhyt = 2;			// dung tuyen gioi thieu;
                            } else {
                                loaibhyt = 1;
                            }
                        } else {
                            if (kcbbd.indexOf(ui.item.BENHVIENKCBBD) != -1) {
                                loaibhyt = 1;			// dung tuyen;
                            } else {
                                if ($("#txtTKBHYT_LoaiID").val() == "" || $("#txtTKBHYT_LoaiID").val() == "1") { // khi chua set, hoac dang set chua dung
                                    loaibhyt = 2;			// trai tuyen;
                                }
                            }
                        }

                        if (loaibhyt != 0) {
                            if ((opt.hospital_id == 1014 || opt.hospital_id == 951) && loaibhyt == 1) {
                                loaibhyt = 2;
                            }
                            $('#cboBHYT_LoaiID').val(loaibhyt);
                            $('#cboBHYT_LoaiID').change();
                        }
                    } else {
                        var _objjj = new Object();
                        _objjj.MAKCBBD = $('#txtMA_KCBBD').val();
                        _objjj.BHYTLOAIID = $('#cboBHYT_LoaiID').val();
                        _objjj.NGTSETTUYEN = _ngtsettuyen;
                        _objjj.MA_BHYT = $("#txtMA_BHYT").val();

                        var _tuyenmdd = jsonrpc.AjaxJson.ajaxCALL_SP_S('NGT01T001.TUYENMD', JSON.stringify(_objjj));
                        if (Number(_tuyenmdd) > 0) {
                            $("#cboBHYT_LoaiID").val(_tuyenmdd); 				// dung tuyen;
                            $("#cboBHYT_LoaiID").change();
                        } else {
                            DlgUtil.showMsg("Lỗi lấy thông tin tuyến cho thẻ BHYT này. ", undefined, undefined, "error");
                        }
                    }
                } else {
                    if ($.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value == '2' && opt.hospital_id != 32620) {
                        $("#cboBHYT_LoaiID").val("3");
                        $("#cboBHYT_LoaiID").change();
                        return false;
                    }
                    var par = [ui.item.BENHVIENKCBBD];
                    var Tuyen = jsonrpc.AjaxJson.ajaxCALL_SP_I('GET_TUYENCH', par.join('$'));
                    $("#cboBHYT_LoaiID").val(Tuyen);
                    $("#cboBHYT_LoaiID").change();
                }
                loadtuyen = 0;
            }
            return false;
        });

        ComboUtil.initComboGrid("txtTKMACHANDOANTUYENDUOI", "NT.008", [], "600px", _colICD, function (event, ui) {
            var str = $("#txtCHANDOANTUYENDUOI").val();
            if (str != '')
                str += ";";
            $("#txtCHANDOANTUYENDUOI").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
            $("#txtTKMACHANDOANTUYENDUOI").val('');
            return false;
        });

        $("#txtNGAYTIEPNHAN").val(_ngayhientaict);
        if (cfObj.NGT_TNTK_GIOPHUTGIAY == "1") {
            $("#spTUTK").removeAttr("onclick");
            $("#spDENTK").removeAttr("onclick");
            $("#spTUTK").attr("onclick","NewCssCal('txtTUTK','ddMMyyyy','dropdown',true,'24',true)");
            $("#spDENTK").attr("onclick","NewCssCal('txtDENTK','ddMMyyyy','dropdown',true,'24',true)");
            $("#txtTUTK").val(_ngayhientai + " 00:00:00");
            $("#txtDENTK").val(_ngayhientai + " 23:59:59");
        } else {
        $("#txtTUTK").val(_ngayhientai);
        $("#txtDENTK").val(_ngayhientai);
        }
        $("#txtMUCHUONG").val("Ngoại (0%)-Nội (0%)");

        var row = vstt_goi[0];
        _yeucaukhammacdinh = row.SETYEUCAUKHAM;
        _setfocusmacdinh = row.TSFOCUS;
        _doituongmacdinh = row.TSFOCUSDT;
        _doituongmacdinh1 = row.TSFOCUSDT1;
        $("#hidDTBN11").val(row.TSFOCUSDT);
        //===========================

        var _DSTNHeader = " ,ICON,30,0,ns,l; ,ICONCLS,30,0,ns,l;KQCLS,KQCLS,0,0,t,l;" +
            "TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,0,0,t,l;" +
            "KHAMBENHID,KHAMBENHID,0,0,t,l;" +
            "Mã BA,MAHOSOBENHAN,100,0,f,l!s;" +
            "Mã BN,MABENHNHAN,100,0,f,l!s;" +
            "Tên bệnh nhân,TENBENHNHAN,180,0,f,l!s;" +
            "Ngày sinh,NGAYSINH,80,0,ns,c;" +
            "Giới tính,GIOITINH,60,0,f,c;" +
            "Mã BHYT,MA_BHYT,110,0,f,l;" +
            "CMT/CCCD,SOCMTND,80,0,f,l;" +
            "Ngày tiếp nhận,NGAYTIEPNHAN,120,0,ns,l;" +
            "Phòng khám,ORG_NAME,190,0,ns,l;" +
            "Mã KCBBĐ,MAKCBBD,60,0,ns,l!s;" +
            "Mã Nơi GT,MANOIGIOITHIEU,60,0,ns,l;" +
            "SĐT BN,SDTBENHNHAN,80,0,f,l;" +
            "Ngày ra viện,NGAYRAVIEN,120,0,ns,l;" +
            "Người TN,NGUOITN,200,0,f,l;" +
            "Nơi làm việc,NOILAMVIEC,200,0,f,l;" +
            "Đ/c BN,DIACHI,300,0,ns,l;" +
            "Mã BA Dài ngày,MABENHANDAINGAY,120,0,f,l;" +
            "XUTRIKHAMBENHID,XUTRIKHAMBENHID,0,0,t,l;" +
            "MADANGKY,MADANGKY,0,0,t,l;" +
            "DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l";
        var _thongkeHeader = "Phòng khám ,PHONGKHAM,200,0,f,l;" +
            "Chờ khám,CHOKHAM,100,0,f,l;" +
            "Đang khám,DANGKHAM,100,0,f,l;" +
            "Cấp toa cho về,CAPTOACHOVE,100,0,f,l;" +
            "Điều trị ngoại trú,DIEUTRINGOAITRU,100,0,f,l;" +
            "Hẹn khám,HENKHAM,100,0,f,l;" +
            "Nhập viện,NHAPVIEN,100,0,f,l;" +
            "Chuyển viện,CHUYENVIEN,100,0,f,l;" +
            "Tử vong,TUVONG,100,0,f,l;" +
            "Khác,KHAC,100,0,f,l";
        // sondn L2PT-7638
        if (cfObj.NGT_TN_DYNAMICGRID == "1") {
            var par_ctl = ['GRID_TABLE_TN'];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
            if (data_ar != undefined && data_ar.length > 100) {
                _DSTNHeader = data_ar;
            }
        }
        // end sondn L2PT-7638

        GridUtil.init("grdDanhSachTiepNhan", "1136", "385px", "", false, _DSTNHeader, false, {
            rowNum: 20,
            rowList: [20, 100, 200, 300, 1000]
        });
        GridUtil.addExcelButton("grdDanhSachTiepNhan", 'Xuất excel', true);
        GridUtil.init("grdThongKe", "1136", "385px", "", false, _thongkeHeader, false, {
            rowNum: 20,
            rowList: [20, 100, 200, 300, 1000]
        });
        GridUtil.addExcelButton("grdThongKe", 'Xuất excel', true);
        var sql_par = [];
        sql_par = RSUtil.setSysParam(sql_par, _param);
        ComboUtil.getComboTag("cboHC_TINHID", "NGTTI.002", sql_par, _options._province_id, {
            extval: true,
            value: '',
            text: 'Chọn'
        }, 'sql', '', '');
        ComboUtil.findByExtra("cboHC_TINHID", $('#txtTKHC_TINHID').val(), 0);

        if (_options._province_id != "") {
            _setDIAPHUONG(1);
        }

        $('#txtTKHC_TINHID').val(
            typeof $('#cboHC_TINHID' + " option:selected").attr('extval0') != 'undefined'
                ? $('#cboHC_TINHID' + " option:selected").attr('extval0') : "");

        //ComboUtil.getComboTag("cboNGHENGHIEPID","NGTNN.002",sql_par,"",{},'sql','', function(){
        //	$("#cboNGHENGHIEPID").val(vsothutu[0].NGTNGHENGHIEPDEF == "" ? "12" : vsothutu[0].NGTNGHENGHIEPDEF);
        //	fillValueDisplay('cboNGHENGHIEPID','txtTKNGHENGHIEPID');
        //});

        // sondn L2PT-24858
        sql_par = [_opts.user_id, _opts.db_schema, "1.1.1.1", _opts.hospital_id, "0"];
        ComboUtil.getComboTag("cboNGHENGHIEPID", "NGTNN.090", sql_par.join('$'), "", {extval: true}, "sp", "", function () {
            $("#cboNGHENGHIEPID").val(vsothutu[0].NGTNGHENGHIEPDEF == "" ? "12" : vsothutu[0].NGTNGHENGHIEPDEF);
            var _vl = $('#cboNGHENGHIEPID' + " option:selected").attr('extval0');
            $("#txtTKNGHENGHIEPID").val(_vl);
        });
        dantocdef = cfObj.NGT_DANTOC_MACDINH;
        ComboUtil.getComboTag("cboDANTOCID", "COM.DANTOC5", sql_par, "", {extval: true}, 'sql', '', function () {// 25: dan toc kinh
            $("#cboDANTOCID").val(dantocdef == "0" ? "25" : dantocdef);
            if (tk_madantoc == 1) {
                var _vl = $('#cboDANTOCID' + " option:selected").attr('extval0');
                $("#txtTKDANTOCID").val(_vl);
            } else {
                fillValueDisplay('cboDANTOCID', 'txtTKDANTOCID');
            }
        });

        ComboUtil.getComboTag("cboQUOCGIAID", "NGTQG.002", sql_par, "", {}, 'sql', '', function () {
            fillValueDisplay('cboQUOCGIAID', 'txtTKQUOCGIAID');
        });

        /*if(vsothutu[0].BSIYCKHAM == "0"){
			$("#dvBACSYKHAM").hide();
		}else{
			$("#dvBACSYKHAM").show();
			var _sqlCTPar=[];
			var _col="ID,USER_ID,30,0,t,l;Tên bác sỹ,OFFICER_NAME,80,0,f,l";
			ComboUtil.initComboGrid("txtBACSYKHAM1","COM.CHONBACSY",_sqlCTPar, "600px", _col, function(event, ui) {
				$("#txtBACSYYCID").val(ui.item.USER_ID);
				$("#txtBACSYKHAM2").val(ui.item.OFFICER_NAME);
				$("#txtBACSYKHAM1").val("");
				return false;
			});
		}

		$("#btnRESETBS").on("click", function(){
			$("#txtBACSYYCID").val("");
			$("#txtBACSYKHAM1").val("");
			$("#txtBACSYKHAM2").val("");
		});*/

        if (vsothutu[0].BSIYCKHAM == "0") {
            $("#dvBACSYKHAM").hide();
        } else {
            $("#dvBACSYKHAM").show();
            ComboUtil.getComboTag("cboBACSYYCID", "COM.CHONBACSY1", [], "", {
                value: '-1',
                text: '--Chọn--'
            }, "sql", "", "");
        }

        $("#txtTKBACSYYCID").on('change', function (e) {
            var sl = $("#txtTKBACSYYCID").val().trim().toLowerCase();
            var idx = -1;
            $("#cboBACSYYCID > option").each(function () {
                if ((this.text.toLowerCase()).indexOf(sl) != -1) {
                    idx = this.value;
//			    	break;
                }
            });
            $("#cboBACSYYCID").val(idx);
            $("#cboBACSYYCID").change();
        });


        if (vsothutu[0].SUDUNGSOCAPCUU == "0") {
            _sudungsocapcuu = "0";
            $("#dvSOCAPCUU").hide();
        } else {
            _sudungsocapcuu = "1";
            $("#dvSOCAPCUU").show();

        }
        _nhaptinhhuyenxa = vsothutu[0].NHAPTINHHUYENXA;
        _nghenghiepdef = vsothutu[0].NGTNGHENGHIEPDEF;
        $('#chkDAGIUTHEBHYT').attr('checked', vsothutu2[0].GIUTHEBHYT == "0" ? false : true);
        _dongbodiachibhyt = vsothutu2[0].DONGBODIACHIBHYT;
        _ngtTinhTuoi = Number(vsothutu2[0].NGTTINHTUOI); 				// SONDN 06/01/2019 L2PT-13842
        _checkDungTuyenCapCuu = Number(vsothutu2[0].CHECKDUNGTUYENCAPCUU);	// SONDN 08/01/2020 L2PT-14814
        _modeGoiKham = typeof vsothutu2[0].MODEGOIKHAM == 'undefined' ? "1" : vsothutu2[0].MODEGOIKHAM;
        _timeOutGoiKham = typeof vsothutu2[0].TIMEOUTGOIKHAM == 'undefined' ? "5000" : vsothutu2[0].TIMEOUTGOIKHAM;
        _chedogoikham = vsothutu2[0].CHEDOGOIKHAM;
        _tuyenTE = vsothutu2[0].TUYENTE;
        _thoiGianTheTE = vsothutu2[0].THOIGIANTHETE;
        _doicongkhamphongkham = vsothutu2[0].DOICONGKHAMPHONGKHAM;
        _batBuocCLSTiepNhan = vsothutu2[0].BATBUOCCLSTIEPNHAN;
        _nghenghiepbhyt = vsothutu2[0].NGHENGHIEPBHYT;
        _anthongtinchuyentuyen = vsothutu2[0].ANTHONGTINCHUYENTUYEN;
        _tiepnhantraingay = vsothutu2[0].TIEPNHANTRAINGAY;
        _checkcongquetthe = vsothutu2[0].CHECKCONGQUETTHE;
        _canhbaotraituyen = vsothutu2[0].CANHBAOTRAITUYEN;
        _bochecktrungtt = vsothutu2[0].BOCHECKTRUNGTT;
        _tkbnf3 = Number(vsothutu2[0].TKBNF3);
        _thukhacvpi = vsothutu2[0].THUKHACVPI;
        _batbuocsocv = vsothutu2[0].BATBUOCSOCV;
        _macdinhdtgt = vsothutu2[0].MACDINHDTGT;
        _hisl3api_checkldt = vsothutu2[0].HISL3API_CHECKLDT;
        _batbuocsdtbn = vsothutu2[0].BATBUOCSDTBN;
        _dungtuyengioithieuldg = vsothutu2[0].DTGTLDG;
        _fillTheBHXHMoi = vsothutu2[0].FILLTHEBHXHMOI;
        _uutientuoi = vsothutu2[0].UUTIENTUOI;
        _uutienBHYT = vsothutu2[0].UUTIENBHYT;
        _thebhytksudung = vsothutu2[0].THEBHYTKSUDUNG;
        _tuyenmacdinhqt = vsothutu2[0].TUYENMACDINHQT;
        _kskcnhl7 = vsothutu2[0].KSKCNHL7;
        _dtrituyenduoingay = vsothutu2[0].DTRITUYENDUOINGAY;
        _tkbngioitinh = vsothutu2[0].TKBNGIOITINH;
        _cbktk = vsothutu2[0].CBKTK;
        _checkBatBuocTE = vsothutu2[0].CHECKBATBUOCTE;
        _bhytdtmienphi = vsothutu2[0].BHYTDTMIENPHI; 							// sondn L2PT-6385
        _checkthe10so = vsothutu2[0].CHECKTHE10SO;
        _ngtsettuyen = vsothutu2[0].NGTSETTUYEN;
        _bhytnghenghiep = vsothutu2[0].BHYTNGHENGHIEP;
        _sinhthete = vsothutu2[0].SINHTHETE;
        _ansautiepdon = vsothutu2[0].ANSAUTIEPDON;
        _nhapthongtin_bnhd = vsothutu2[0].NHAPTHONGTIN_BNHD; 							// controlA@controlB@controlC@
        _popupconthuoc = vsothutu2[0].POPUPCONTHUOC;
        NGT_GOIKHAM_HANGDOI = vsothutu2[0].NGT_GOIKHAM_HANGDOI;
        $("#hidGOIKHAMHANGDOI").val(NGT_GOIKHAM_HANGDOI);//L2PT-43429
        _checkconthuocct = vsothutu2[0].CHECKCONTHUOCCT;
        _batbuocSinhTon = vsothutu2[0].BATBUOCSINHTON;
        _focus_mabn = vsothutu2[0].FOCUSMABN;
        _moiquanhe = vsothutu2[0].MOIQUANHE;
        _tgtiepnhan = cfObj.NGT_TG_TIEPNHAN1;
        qd130 = cfObj.HIS_QD130;
        QD_4750 = cfObj.QD_4750;
        tk_madantoc = cfObj.NGT_TIMKIEM_MADANTOC;

        sdt_number = parseInt(cfObj.SDT_BN_NUMBER);
        anthukhac = parseInt(cfObj.NGT_TN_ANTHUKHAC);
        NGT_TN_CHECKNOISONG = parseInt(cfObj.NGT_TN_CHECKNOISONG);
        if (_sinhthete != "0") {
            $('#chkSinhTheTE').css('display', 'block');
        }

        if (vsothutu2[0].GIUTHEBHYT_TN1 != "0") {
            $('#dvDAGIUTHEBHYT').show();
        }
        if (anthukhac == 1) {
            $('#toolbarIdbtnThuTienKhac').hide();
            $('#tabPhieuThuKhacTab').hide();
        }
        //dannd_L2PT-34927
        nhanthuochen = vsothutu2[0].NHANTHUOCTHEOHEN;
        if (nhanthuochen == "1") {
            $("#dvNHANTHUOCTHEOHEN").show();
            $("#dvLINHTHUOCNGOAIVIEN").show();
            $("#dvNGAY_CAPGIAY").show();
            $("#dvDV_CAPGIAY").show();
        } else if (nhanthuochen == '2') {
            $("#dvNHANTHUOCTHEOHEN").hide();
            $("#dvLINHTHUOCNGOAIVIEN").hide();
            $("#dvNGAY_CAPGIAY").hide();
            $("#dvDV_CAPGIAY").hide();
            $('#divNhanThuoc1').show();
            $("#txtBACSIHOICHAN").prop("disabled", true);
            $("#cboBACSIHOICHAN").prop("disabled", true);
            $("#btnCLEARBACSIHOICHAN").prop("disabled", true);
            $("#cboNOILINHTHUOC").prop("disabled", true);
            $("#txtNGAY_CAPGIAY1").prop("disabled", true);
            $("#txtDV_CAPGIAY1").prop("disabled", true);
            $("#cboTEN_DV_CAPGIAY1").prop("disabled", true);
            ComboUtil.initComboGrid("txtDV_CAPGIAY1", "NT.009", [], "800px", _colDKKCBBD, function (event, ui) {
                $("#txtDV_CAPGIAY1").val(ui.item.BENHVIENKCBBD);
                var option = '<option value="' + ui.item.BENHVIENKCBBD + '">' + ui.item.TENBENHVIEN + '</option>';
                $("#cboTEN_DV_CAPGIAY1").empty();
                $("#cboTEN_DV_CAPGIAY1").append(option);
                return false;
            });
            var sql_par = [];
            var _colNv = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên NV,OFFICER_NAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";
            sql_par.push({
                "name": "[0]",
                "value": "-1"
            }, {
                "name": "[0]",
                "value": "-1"
            });
            //End_HaNv_050121
            ComboUtil.initComboGrid("txtBACSIHOICHAN", "CDDV.USERBYLOAINV", sql_par, "600px", _colNv, function (event, ui) {
                $("#txtBACSIHOICHAN").val("");
                var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
                $("#cboBACSIHOICHAN").empty();
                $("#cboBACSIHOICHAN").append(option);
                return false;
            });
        }

        if (cfObj.NGT_TN_GHICHU == 1) {
            $("#divGhichuTN").show();
        }
        //dannd_L2PT-73827
        if (cfObj.NGT_TN_DSDVGUIMAU != "0") {
            $("#dvDVGM").show();
            $("#dvGHICHUTN1").removeClass("col-xs-12 low-padding");
            $("#dvGHICHUTN1").addClass("col-xs-8 low-padding");
            var data_dvgm = cfObj.NGT_TN_DSDVGUIMAU.split(';');
            for (var i = 0; i < data_dvgm.length; i++) {
                var newOption = $('<option value="' + data_dvgm[i] + '" >' + data_dvgm[i] + '</option>');
                $("#cboDONVIGUIMAU").append(newOption);
            }
        }
        if (vsothutu2[0].BVSK == "1") {
            $('#dvBVSK').show();
        }
        if (cfObj.NGT_TN_KHAMGIAMDINH == "1") {
            $('#dvKHAMGIAMDINH').show();
        }
        if (cfObj.NGT_DANHDAU_GUISMS == "1") {
            $('#dvGUISMS').show();
        }
        if (cfObj.NGT_TN_HIENTHITTCMND == "1") {
            $('#divTTCMND').show();
            $('#txtNOICAPCMND').val('Cục cảnh sát quản lý hành chính về trật tự xã hội');
        }
        if (cfObj.NGT_LOADMADIADANH == "1") {
            _sqldiaphuong = 'DMDP.003';
        }
        if (cfObj.NGT_TN1_KENHDK_SHOW == "1") {
            $('#dvKENHDK').show();
            $("#dvTRANGTHAI").removeClass("col-xs-2 low-padding mgt5");
            $("#dvTRANGTHAI").addClass("col-xs-1 low-padding mgt5");
        }
        if (cfObj.NGT_HIENTHI_QUYCHEBAOVEDLCN == "1") {
            $('#dvQUYCHE').show();
            $('#lblQUYCHE').text('Bệnh nhân đã đọc và đồng ý Quy chế bảo vệ dữ liệu cá nhân của ' + _opts.hospital_name);
        }
        //L2PT-112094
        if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_TN_HIENTHI_NGUONKHACH') == "1"){
            $('#divNGUONKHACH').show();
        }
        if (cfObj.NGT_TN2_EMAIL == 1) {
            $("#dvEmail").show();
        }
        if (cfObj.NGT_TN_BATBUOC_VSSID == "1") {
            $("#dvVSSID").addClass("required");
            $('#dvVSSID').css('width', '25%');
        }
        if (cfObj.NGT_TN_SHOWINBARCODE == "1") {
            $('#btnInBarcode').show();
        }
        if (cfObj.NGT_TN_SHOWSOGIAYHENKHAM == "1") {
            $('#dvGIAYHENKHAM').show();
        }
        if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_TN1_QUANHAM_DVCT_SEARCHTEXT') == "1") {
            $("#cboQUANHAMID").select2();
            $("#cboDVCTID").select2();
        }
        if (cfObj.NGT_TN1_NGHENGHIEP_SEARCHTEXT == "1") {
            $("#txtTKNGHENGHIEPID").hide();
            $("#cboNGHENGHIEPID").removeAttr("style")
            $("#cboNGHENGHIEPID").attr('style', 'width: 100% !important');
            $("#cboNGHENGHIEPID").select2({
                language: {
                    noResults: function (params) {
                        return "Không có kết quả";
                    }
                }
            });
        }
        if (cfObj.HIS_SHOW_DOITUONGTRUNGCAO == "1") {
            $('#dvDOITUONGTRUNGCAO').show();
        }
        _sudungiqms = vsothutu2[0].SUDUNGIQMS;
        if (_sudungiqms == "1") {
            $("#dvCall6").show();
            _iqms_dmdp = cfObj.IQMS_DMDP;
            _iqms_capnhatsauqr = cfObj.IQMS_CAPNHATSAUQR;
        }

        // HIEN THI DANH SACH NUT KIOS
        if (vsothutu2[0].KIOSSHOWBUTTON != "0") {
            var _controlArr = vsothutu2[0].KIOSSHOWBUTTON.split('@');
            for (i = 0; i < _controlArr.length; i++) {
                $("#" + _controlArr[i]).show();
            }
        }

        _timeOutRefresh = vsothutu2[0].TIMEOUTREFRESH;
        // sondn L2PT-30163
        if (_timeOutRefresh == "0") {
            _timeOutRefresh = vsothutu2[0].TIMEOUTREFRESH1;
        }
        // end sondn L2PT-30163


        // sondn L2PT-4440
        _goikhamkcb = vsothutu2[0].GOIKHAMKCB;
        if (_goikhamkcb != "0") {
            $("#btnGOIKHAMVPC").show();
        }
        // end sondn L2PT-4440

        if (vsothutu2[0].MATHEGIFT != "0") {
            var _arr = vsothutu2[0].MATHEGIFT.split('@');
            if (_arr[0] != "0") {
                $("#dvMATHEGIFT").show();
                $("#lblTENMATHEGIFT").text(_arr[0]);
            }
            if (_arr[1] != "0") {
                $("#dvLANSDTHEGIFT").show();
                $("#lblLANSDMATHEGIFT").text(_arr[1]);
            }
            if (_arr[2] != "0") {
                $("#dvMATHEGIFTHANTHE").show();
            }
        }

        // SONDN L2PT-30169
        if (vsothutu2[0].MABENHANNDH == "1") {
            $("#dvMABENHANNDH").show();
            $("#dvMABENHNHAN").remove();
        } else {
            $("#dvMABENHANNDH").remove();
            $("#dvMABENHNHAN").show();
        }
        // END SONDN L2PT-30169

        if (_doicongkhamphongkham == "0") {
            $("#dvCONGKHAMPHONGKHAM2").remove(); 			// giu nguyen k doi
        } else {
            $("#dvCONGKHAMPHONGKHAM1").remove(); 			// doi vi tri;
        }

        if (vsothutu2[0].GOIYDTBNID == "1") {
            $("#dvTKDTBNID").show();
            $("#dvDTBNID").removeClass("col-xs-10 low-padding");
            $("#dvDTBNID").addClass("col-xs-8 low-padding");
        }

        if (vsothutu2[0].MOIQUANHE == "1") {
            $("#dvMOIQUANHE").show();
        }

        _ttquannhan = vsothutu2[0].TTQUANNHAN;
        if (_ttquannhan != "0") {
            $("#dvQUANNHAN").show();
            ComboUtil.getComboTag("cboQUANHAMID", "NGT01T001.QUANHAM", [], "", {
                value: -1,
                text: '--Chọn--'
            }, "sql", "", "");
            // ComboUtil.getComboTag("cboDVCTID", "NGT01T001.DVCT", [], "", {value:-1, text:'--Chọn--'}, "sql", "","");
            ComboUtil.getComboTag("cboDVCTID", "NGT01T001.DVCT", [{"name": "[0]", "value": "0"}], "", {
                value: -1,
                text: '--Chọn--'
            }, "sql", "", "");
        } else {
            $("#dvQUANNHAN").hide();
        }

        if (vsothutu2[0].TNGT == "1") {
            $("#dvTNGT").show(); 				// hien thi check box tai nan thuong tich;
        }

        _dkkcbbd_nothe = vsothutu2[0].KCBBDNOTHE;

        if (vsothutu2[0].NOTHETIEPDON == "1") {
            $("#dvNOTHE").show();
        }

        if (vsothutu2[0].VSSID == "1") {
            $("#dvVSSID").show();
        }
        if (vsothutu2[0].VODANH == "1") {
            $('#dvVODANH').show();
        }
        if (qd130 == 1) {
            $("#dvTHONGTINBOME").show();
            $("#lblNGUOINN").text('Họ tên NN/ người giám hộ');
            $("#divTuoi2").css('display', '');
            $("#divTuoi1").remove();
            $("#divLYDOVAOVIEN").show();
            // $("#dvDOITUONGKCB").show();
        } else {
            $("#divTuoi2").remove();
        }
        if (QD_4750 == 1) {
            $("#lblSINHTHETE").text('Thẻ tạm');
            $('#dvTHETAM').show();
        }
        if (cfObj.NGT_TN_BATBUOC_LYDOVAOVIEN == 1) {
            $("#divLYDOVAOVIEN").addClass("required");
        }
        if (cfObj.NGT_TN_SHOWTHONGTINBOME == 1) {
            $("#dvTHONGTINBOME").show();
        }
        // load cấu hình sử dụng load yêu cầu khám theo đối tượng BN
        _loadyeucaukhamtheodt = cfObj.LOAD_YEUCAUKHAM_THEO_DT;

        doituong_khaibao = cfObj.HIS_SUDUNG_DOITUONG_KHAIBAO;

        if (doituong_khaibao != "1") {
            ComboUtil.getComboTag("cboDTBNID1", "NT.007", sql_par, "", {
                value: 0,
                text: '--Đối tượng--'
            }, "sql", '', '');
            ComboUtil.getComboTag("cboDTBNID", "NT.007", sql_par, "", "", "sql", '', function () {
                if (_doicongkhamphongkham == "0") {
                    // _sql_yckham
                    var sql_par = [];
                    if (_loadyeucaukhamtheodt == "0") {
                        sql_par.push({"name": "[0]", "value": 0});
                    } else {
                        sql_par.push({"name": "[0]", "value": $("#cboDTBNID").val()});
                    }

                    ComboUtil.getComboTag("cboDICHVUID", _sql_yckham, sql_par, "", {
                        extval: true,
                        value: '',
                        text: 'Chọn yêu cầu khám'
                    }, 'sql', '', function () {
                        //							ComboUtil.findByExtra("cboDICHVUID",_yeucaukhammacdinh,0);
                        if (_loadFirstYCKham == "1" && _yeucaukhammacdinh != "" && _yeucaukhammacdinh != "0") {
                            _loadFirstYCKham = 0; 															// da load xong y/c kham ban dau;
                            $("#cboDICHVUID").val(_yeucaukhammacdinh);
                            $("#cboDICHVUID").change();
                        }

                    });
                } else {
                    // load phong kham truoc
                    var sql_par1 = [];
                    sql_par1.push({"name": "[0]", "value": 2});				// 2: phong kham benh;
                    sql_par1.push({"name": "[1]", "value": _options._khoaid});
                    ComboUtil.getComboTag("cboPHONGKHAMID", "NGTPK.DV.01", sql_par1, "0", {extval: true}, "sql", "", function () {
                        // set phong kham mac dinh;
                        var _phongkhammd = $("#hidSETPHONGKHAM").val();
                        if (_loadFirstPhongKham != 0 && _phongkhammd != '' && _phongkhammd != '0') {
                            $("#cboPHONGKHAMID").val(_phongkhammd);
                            _loadFirstPhongKham--;
                            $("#cboPHONGKHAMID").change();
                        }
                    });
                }

                if (vsothutu[0].ANCHECKBOXBHYTDV != '1') {
                    $('#cboDTBNID').append("<option value='6'>BHYT + DV</option>");
                    if (_doituongmacdinh == '6') {
                        $(this).val(6);
                    }
                    $("#divPhieuthukhac").hide();
                }

                $('#cboDTBNID').val(_doituongmacdinh);
                $('#cboDTBNID').change();
            });
        } else {
            ComboUtil.getComboTag("cboDTBNID", "NT.007.01", sql_par, "", {extval: true}, "sql", '', function () {
                if (_doicongkhamphongkham == "0") {
                    // _sql_yckham
                    var sql_par = [];
                    if (_loadyeucaukhamtheodt == "0") {
                        sql_par.push({"name": "[0]", "value": 0});
                    } else {
                        sql_par.push({"name": "[0]", "value": $("#cboDTBNID").val()});
                    }

                    ComboUtil.getComboTag("cboDICHVUID", _sql_yckham, sql_par, "", {
                        extval: true,
                        value: '',
                        text: 'Chọn yêu cầu khám'
                    }, 'sql', '', function () {
                        //							ComboUtil.findByExtra("cboDICHVUID",_yeucaukhammacdinh,0);
                        if (_loadFirstYCKham == "1" && _yeucaukhammacdinh != "" && _yeucaukhammacdinh != "0") {
                            _loadFirstYCKham = 0; 															// da load xong y/c kham ban dau;
                            $("#cboDICHVUID").val(_yeucaukhammacdinh);
                            $("#cboDICHVUID").change();
                        }

                    });
                } else {
                    // load phong kham truoc
                    var sql_par1 = [];
                    sql_par1.push({"name": "[0]", "value": 2});				// 2: phong kham benh;
                    sql_par1.push({"name": "[1]", "value": _options._khoaid});
                    ComboUtil.getComboTag("cboPHONGKHAMID", "NGTPK.DV.01", sql_par1, "0", {extval: true}, "sql", "", function () {
                        // set phong kham mac dinh;
                        var _phongkhammd = $("#hidSETPHONGKHAM").val();
                        if (_loadFirstPhongKham != 0 && _phongkhammd != '' && _phongkhammd != '0') {
                            $("#cboPHONGKHAMID").val(_phongkhammd);
                            _loadFirstPhongKham--;
                            $("#cboPHONGKHAMID").change();
                        }
                    });
                }

                $("#cboDTBNID").find("option[extval0='" + _doituongmacdinh1 + "']").attr("selected", "selected");
                $('#cboDTBNID').change();
            });
            ComboUtil.getComboTag("cboDTBNID1", "NT.007.01", sql_par, "", {
                extval: true,
                value: 0,
                text: '--Đối tượng--'
            }, "sql", '', '');
            // END SONDN NEW VERSION
        }
        // sql cu NGT.TN.DSNGUONKHACH
        ComboUtil.getComboTag("cboNHOMNGUON_ID", "DMC172.01", [{name: '[0]', value: -1}, {
            name: '[1]',
            value: 0
        }], "0", {value: 0, text: 'Nhóm nguồn'}, "sql", "", function () {
            ComboUtil.getComboTag("cboNGUONKHACHID", "NGT.TN.NGUONKHACH", [{
                name: '[0]',
                value: $('#cboNHOMNGUON_ID').val()
            }], "0", {extval: true, value: 0, text: 'Nguồn khách hàng'}, "sql");
        });
        var sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": "58"});
        ComboUtil.getComboTag("cboBHYT_LoaiID", "DV.BHYT.001", sql_par1, "", {}, 'sql', '', function () {
            $("#cboBHYT_LoaiID").val(vsothutu[0].TUYENMACDINH == "0" ? "1" : vsothutu[0].TUYENMACDINH);
            fillValueDisplay('cboBHYT_LoaiID', 'txtTKBHYT_LoaiID');
            //dannd_L2PT-73662
            if (isOutsideWorkingHours() && parseInt(cfObj.NGT_TN_CHECKCC_NGOAIGIOHC) == 1) {
                $('input[name=radHINHTHUCVAOVIENID][value=2]').attr('checked', true);
                $("#cboBHYT_LoaiID").val(3);
            }
            $("#cboBHYT_LoaiID").change();
        });

        sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": "1"});

        ComboUtil.getComboTag("cboGIOITINHID", "COM.GT", sql_par1, "2", {
            value: -1,
            text: 'Chọn'
        }, 'sql', 'gioitinh', function () {
            $("#cboGIOITINHID").val(vsothutu2[0].NGTGIOITINHDEF == "" ? "1" : vsothutu2[0].NGTGIOITINHDEF);
            fillValueDisplay('cboGIOITINHID', 'txtTKGIOITINHID');
        });

        _disableControl(['toolbarIdbtnTreat', 'toolbarIdbtnhandling', 'toolbarIdbtnHoaHong',
            'toolbarIdbtnThuTien', 'txtTKMACHANDOANTUYENDUOI', 'txtCHANDOANTUYENDUOI',
            'txtTKMANOIGIOITHIEU', 'cboMANOIGIOITHIEU', 'btnSinhTon', 'toolbarIdtreat_2',
            'toolbarIdbtnThuTienKhac', 'chkCHECKBHYTDV'], true);

        $("#txtTENBENHNHAN").focus();
        $('#btnInPhieuKham').blur();
        $('#btnTiepTheo').blur();
        $('#btnChupHinh').blur();
        $('#btnSinhTon').blur();
        $('#btnChuyenTuyen').blur();

        // SONDN: MOVE STT_DT
//		if (vsothutu != null && vsothutu.length > 0){
        $('#divPhieuthukhac').css('display', '');
        $('#lblSTTDonTiep').text(vsothutu[0].SOTHUTU);
        $("#hidYCKHAMTHEODT").val(vsothutu[0].YCKHAMTHEODT);

        if (vsothutu[0].NGAYTN == "0") {
            $('#btnngaytn').attr("disabled", true);
        } else {
            $('#btnngaytn').attr("disabled", false);
            _songayluiBHYT = vsothutu[0].SONGAYBHYT;
        }

        if (vsothutu[0].BTNTHUTIEN == "0") {
            $('#toolbarIdbtnThuTien').hide();
        } else {
            $('#toolbarIdbtnThuTien').show();
        }

        if (vsothutu[0].DTUUTIEN == "1") {
            $('#chkUUTIENKHAMID').attr('checked', true);
        }

        // L2PT-8640 - SONDN - 09/09/2019
        if (vsothutu[0].CAPCUU == "1" || _hd == "2") {
            _hinhthucvaovien = "2";
        } else {
            _hinhthucvaovien = "3";
        }

        if (vsothutu[0].CHANNHIEUPHONG == "0") {
            $("#toolbarIdhandling_2").removeClass("disabled");
            $("#toolbarIdbtnhandling").show();
        } else {
            $("#toolbarIdhandling_2").addClass("disabled");
            $("#toolbarIdbtnhandling").hide();
        }

        $('input[name=radHINHTHUCVAOVIENID][value=' + _hinhthucvaovien + ']').attr('checked', true);

        if (_hinhthucvaovien != '2' && _hinhthucvaovien != '3') {
            $('input[name=radHINHTHUCVAOVIENID][value=3]').attr('checked', true);
        }
        _capnhathd = vsothutu[0].CAPNHATHD;

//			$("#hidDTMIENGIAM").val(vsothutu[0].DTMIENGIAM);
        if (vsothutu[0].DTMIENGIAM != "1") {
//				$('#cboDOITUONGDB').attr("disabled", true);
//				$("#txtMADOITUONGNGHEO").prop('disabled', true);
            $("#divMIENGIAM").hide();
        }

        if (vsothutu[0].SINHTON == "1") {
            $('#btnSinhTon').css('display', '');
        }

        if (vsothutu[0].CHUPANH == "1") {
            $('#btnChupHinh').css('display', '');
            $('#divAnhBN').css('display', '');
            $('#cboLS').css('width', '65%');
        }

        if (vsothutu[0].GOIKHAM == "1") {
            $('#dvCall').css('display', '');
        }

        if (vsothutu[0].AN_DOI_CONGKHAM_PK == "1") {
            $('#lichuyenpyckham').remove();
        }

        if (vsothutu[0].AN_SINHSO_UUTIEN == "1") {
            $('#lisinhsttkham').remove();
        }

        if (vsothutu[0].ANPHIEU == "0") {
            $("#btnInPhieuKham").css('display', '');
        }
        // sondn IT360-344162
        if (vsothutu2[0].TGTIEPNHAN == "1") {
            if ($.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value == 2 && _tgtiepnhan == "1") {
                $("#dvNGAYTIEPNHAN").show();
                $("#dvNGAYTIEPNHANPLACEHOLDER").hide();
            } else {
                $("#dvNGAYTIEPNHAN").hide();
                $("#dvNGAYTIEPNHANPLACEHOLDER").show();
            }
        }
        if (cfObj.NGT_HIENTHITAB_THONGKE_TN2 == "1") {
            $('#liTabThongke').show();
        }
        // end sondn IT360-344162
        if (cfObj.NGT_SHOWTAB_THUOCVATTU_TN == "1") {
            $('#liTabThuoc').show();
            $('#liTabVatTu').show();
        }
        _sttsangchieu = vsothutu[0].STTSANGCHIEU;
        _inphieukhamtudong = vsothutu[0].INPHIEU;
        _check_the_bhyt_bhxh = vsothutu[0].CHECK_BHYT_CONG;
        _mopopupphongkham = vsothutu[0].MOPOPUPPHONGKHAM;
        $("#txtKHONGSUDUNG5NAM6THANG").val(vsothutu[0].KHONGSUDUNG5NAM6THANG);
        if (vsothutu[0].KHONGSUDUNG5NAM6THANG == "0") {
            $("#chkDU5NAM6THANGLUONGCOBAN").prop('disabled', false);
            $("#chkTRADU6THANGLCB").prop('disabled', true);
        } else {
            $("#chkDU5NAM6THANGLUONGCOBAN").prop('disabled', true);
            $("#chkTRADU6THANGLCB").prop('disabled', true);
        }

        /*// set mặc định đối tượng thu khác.
		if(vsothutu[0].THUKHAC == "1"){
			$('#cboDTBNID').val(6);
		}*/

        $('#cboDTBNID').change();
        $("#txtTECHUSER").val(vsothutu[0].TECHUSER);
        $("#txtTECHPASS").val(vsothutu[0].TECHPASS);
        $("#txtKCBBD").val(vsothutu[0].TECHKCBBD);
        $("#txtURLWEBSITE").val(vsothutu[0].URLWEBSITE); 							// link upload image;
        $("#txtURLWEBSITE1").val(vsothutu[0].URLWEBSITE1); 							// link local bd
        $("#txtMAHONGHEO").val(vsothutu[0].MAHONGHEO);
        $("#txtBATCHECKNGHEO").val(vsothutu[0].BATCHECKNGHEO);
        $("#txtDTKHAIBAO").val(vsothutu[0].DTKHAIBAO);
        $("#hidTUDONGFILLBHXH").val(vsothutu[0].TUDONGFILLBHXH);
        $("#txtTRAVEMABENHAN").val(vsothutu[0].TRAVEMABENHAN);
        $("#hidTUDONGINPHIEUKHAM").val(vsothutu[0].TUDONGINPHIEUKHAM);
        $("#txtFILEEXPORTTYPE").val(vsothutu[0].FILEEXPORTTYPE);
        $("#hidANTHONGTINTUDONG").val(vsothutu[0].ANTHONGTINTUDONG);
        $("#hidVIENPHIHIENTHIPOPUP").val(vsothutu[0].VIENPHIHIENTHIPOPUP);
        $("#txtDOITUONGCHIDINHCLS").val(vsothutu[0].DOITUONGCHIDINHCLS);
        $("#hidSETPHONGKHAM").val(vsothutu[0].SETPHONGKHAM);
        $("#hidNHAPCHUYENTUYEN").val(vsothutu[0].NHAPCHUYENTUYEN);
        $("#hidTINHTHUKHAC").val(vsothutu[0].TINHTHUKHAC);
        _setkcbbd = vsothutu[0].SETKCBBD;
        $("#txtGOIBENHNHAN").val(vsothutu[0].GOIBENHNHAN);
        $("#txtCHECKDUNGTUYEN").val(vsothutu[0].CHECKDUNGTUYEN);
        $("#txtDIEUHUONGTIEPNHAN").val(vsothutu[0].DIEUHUONGTIEPNHAN);
        $("#txtNGTGOIBENHNHAN").val(vsothutu[0].NGTGOIBENHNHAN);
        $("#txtKSUDUNGKCBBD").val(vsothutu[0].KSUDUNGKCBBD);

        $("#hidSHOWCHECKCONGBHXH").val(vsothutu[0].SHOWCHECKCONGBHXH);
        if (vsothutu[0].SHOWCHECKCONGBHXH == "1") {
            $("#btnCHECKLICHSUKCB").show();
        } else {
            $("#btnCHECKLICHSUKCB").hide();
        }


        $("#txtBYTURL").val(vsothutu[0].BYTURL);
        $("#txtBYTLAYDL").val(vsothutu[0].BYTDAYDL);
        $("#txtBYTSTOPCHUCNANG").val(vsothutu[0].BYTSTOPCHUCNANG);

        if (_check_the_bhyt_bhxh == "1") {
            $('#chkCHECKCONG').attr('checked', true);
            $("#lblCHECKCONG").show();
            $("#divBHYTT").addClass("col-xs-5 low-padding");
        } else {
            $('#chkCHECKCONG').attr('checked', false);
            $("#lblCHECKCONG").hide();
            $("#divBHYTT").addClass("col-xs-6 low-padding");
        }

        $("#hidDENNGAYBHYT").val(vsothutu[0].ANDENNGAYBHYT);
        _dsphongcapcuu = vsothutu[0].DSPHONGCAPCUU;

        if (vsothutu2[0].HIDECLS == "1") {
            $("#toolbarIdtreat_2").hide();
        }
        //dannd_L2PT-27856
        if (vsothutu2[0].NBN_DEFAULT_TKDIAPHUONG != '0') {
            $('#txtTKDIAPHUONGID').val(vsothutu2[0].NBN_DEFAULT_TKDIAPHUONG);
        }

        // LAY THONG TIN CHECK CONG BHYT ;
        var _parram = ["1"];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", _parram.join('$'));
        if (data_ar != null && data_ar.length > 0) {
            i_u = data_ar[0].I_U;
            i_p = data_ar[0].I_P;
            i_u1 = data_ar[0].I_U1;
            i_p1 = data_ar[0].I_P1;
            i_macskcb = data_ar[0].I_MACSKCB;
        }

        // lay số phòng gọi tiep don
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _options._khoaid});
        sql_par.push({"name": "[1]", "value": _options.phongid});
        var vsophong = jsonrpc.AjaxJson.ajaxExecuteQueryO("SOPHONG.TIEPDON", sql_par);
        vphong = JSON.parse(vsophong);
        _sophongtiepdon = vphong[0].SOPHONG;

        NGT_HIENTHIMAU_DATLICH = cfObj.NGT_HIENTHIMAU_DATLICH;
        if (NGT_HIENTHIMAU_DATLICH != "" && NGT_HIENTHIMAU_DATLICH != "0") {
            $("#spGhichuDatLich").css("color", NGT_HIENTHIMAU_DATLICH);
            $("#spGhichuDatLich").css("display", "");
        }
        NGT_TN_SET_TUYEN_KCBBD = cfObj.NGT_TN_SET_TUYEN_KCBBD;
        if (parseInt(cfObj.NGT_TIEPNHAN_INDAMLABEL) == 1) {
            $("#lblTUYEN1").attr('style', 'font-weight: 700 !important');
            $("#lblHOTEN").attr('style', 'font-weight: 700 !important');
            $("#lblGIOITINH").attr('style', 'font-weight: 700 !important');
            $("#lblDANTOC").attr('style', 'font-weight: 700 !important');
            $("#lblSOTHEBHYT").attr('style', 'font-weight: 700 !important');
        }
        if (cfObj.NGT_MACDINHTHE != "0") {
            $("#cboVSSID").val(cfObj.NGT_MACDINHTHE);
        }
        hienIdsTuCauHinh('NGT_DSCTL_AN_TIEPNHAN', 'VIS', 'HIDE'); // L2PT-58374 duonghn
    }

    //In phieu tra the bhyt
    function _inPhieuTraTheBHYT() {
        var objData = new Object();
        FormUtil.setFormToObject("tabTiepNhan", "", objData);
        var par = [{
            name: 'khambenhid',
            type: 'String',
            value: objData["KHAMBENHID"]
        }];
        openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par, true);
    }

    function _bindEvent() {
        // click tab CDHA
        $("#tabCDHATab").on("click", function (e) {
            //widget khoi tao grid danh sach CDHA
            $('#tabCDHA').ntu02d025_cdha({
                _gridCDHA: "grdCDHA",
                _gridCDHADetail: "grdCDHAChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_CDHA,
                _formCall: "TN_NGT1",
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        // click tab xet nghiem
        $("#tabXetNghiemTab").on("click", function (e) {
            //widget khoi tao grid danh sach xet nghiem
            $('#tabXetNghiem').ntu02d024_ttxn({
                _gridXnId: "grdXetNghiem",
                _gridXnDetailId: "grdXetNghiemChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_XetNghiem,
                _formCall: "TN_NGT1",
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        // click tab chuyen khoa
        $("#tabPTTTTab").on("click", function (e) {
            //widget khoi tao grid danh sach chuyen khoa
            $('#tabPTTT').ntu02d026_ck({
                _gridCK: 'grdCK',
                _gridCKDetail: 'grdCKChitiet',
                _grdCKketQua: 'grdCKketQua',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_ChuyenKhoa,
                _formCall: "TN_NGT1",
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        //widget cho tab phieu van chuyen
        $("#tabPhieuThuKhacTab").on("click", function (e) {
            $('#tabPhieuThuKhac').ntu02d029_pdv({
                _grdSuatAn: 'grdSuatAn',
                _grdSuatAnChitiet: 'grdSuatAnChitiet',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '17',
                _loaidichvu: "1",
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        //widget cho tab phieu van chuyen
        $("#tabCongKhamTab").on("click", function (e) {
            $('#tabCongKham').ntu02d029_pdv({
                _grdSuatAn: 'grdSuatAn',
                _grdSuatAnChitiet: 'grdSuatAnChitiet',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '3',
                _loaidichvu: "2",
                _modeView: 1, // =1 chi view; !=1 la update
                _hosobenhanid: "",
                _modeXoaphieu: "1"//START L2PT-1357
            });
        });
        $("#tabThuocTab").on("click", function (e) {
            $('#tabThuoc').ntu02d033_pt({
                _grdPhieuthuoc: 'grdThuoc',
                _gridPhieuthuocDetail: 'grdChiTietThuoc',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_Phieuthuoc,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: "",
                _loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                _doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val()
            });
        });
        $("#tabVatTuTab").on("click", function (e) {
            $('#tabVatTu').ntu02d034_pvt({
                _grdVatTu: 'grdVatTu',
                _gridVatTuDetail: 'grdChiTietVatTu',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_Phieuvattu,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: "",
                _loaitiepnhanid: $("#hidLOAITIEPNHANID").val()
            });
        });

        $("#btnCONGTYBENHNHAN").on('click', function () {
            if (cfObj.NGT_POPUPCTY_TN2 == 1) {
                var _par_benhnhanid = [$("#hidBENHNHANID").val()];
                var data_bn_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.TTBN.HDDT", _par_benhnhanid.join('$'));
                //$("#txtMAPHIEUTHUVIEW").val(_mapthu);
                // lay ma benh nhan -- viet ham lay thong tin bn -- chuyen du lieu bn vao popup -- xu ly xu kien popup
                $("#txtMABENHNHAN_HDDT").val(data_bn_ar[0].MABENHNHAN);
                // L2PT-28833 end sửa fn lấy ttbn
                $("#txtTENCONGTYBN_HDDT").val(data_bn_ar[0].TENCONGTYBN);
                $("#txtDIACHI_CTYBN_HDDT").val(data_bn_ar[0].DIACHI_CTYBN);
                $("#txtMASOTHUE_CTYBN_HDDT").val(data_bn_ar[0].MASOTHUE_CTYBN);
                $("#txtEMAIL_CTYBN_HDDT").val(data_bn_ar[0].EMAIL_CTYBN);
                $("#txtSOTK_CTYBN_HDDT").val(data_bn_ar[0].SOTAIKHOAN);
                $("#txtTENNH_CTYBN_HDDT").val(data_bn_ar[0].TEN_NGANHANG);
                DlgUtil.open("dlgNhapThongTinTT_NHIHDG");
            } else {
                var paramInput = {
                    tencongty: $("#txtTENCONGTYBN").val(),
                    diachicongty: $("#txtDIACHI_CTYBN").val(),
                    masothuecongty: $("#txtMASOTHUE_CTYBN").val(),
                    mode: "CONGTYBN"
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgCongTyBenhNhan", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T001_CONGTYBN", paramInput, "NHẬP CÔNG TY", 600, 400);
                DlgUtil.open("dlgCongTyBenhNhan");
            }
        });
        $("#dvQUYCHE").on('click', function () {
            DlgUtil.open("dlgThongTinQUYCHE");
        });

        EventUtil.setEvent("assignSevice_loadcongtybenhnhan", function (e) {
            if (typeof (e) != 'undefined') {
                DlgUtil.close("dlgCongTyBenhNhan");
                $("#txtTENCONGTYBN").val(e.tencongty);
                $("#txtDIACHI_CTYBN").val(e.diachicongty);
                $("#txtMASOTHUE_CTYBN").val(e.masothuecongty);
            }
        });

        // gioi tinh
        $('#cboGIOITINHID').on('change', function (e) {
            fillValueDisplay('cboGIOITINHID', 'txtTKGIOITINHID');
        });

        // sondn L2PT-2944
        $("#cboGIOITINHID").focusout(function () {
            if (_tkbngioitinh == "1" && $("#hidBENHNHANID").val() == "") {
                var _objBenhNhan = new Object();
                _objBenhNhan["tenbenhnhan"] = $("#txtTENBENHNHAN").val().toUpperCase().trim();
                _objBenhNhan["ngaysinh"] = $("#txtNGAYSINH").val() != "" ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val().trim();
                _objBenhNhan["gioitinhid"] = $("#cboGIOITINHID").val();
                if (_tkbnf3 == "1") {
                    _objBenhNhan["ngaysinh"] = $("#txtNAMSINH").val();
                }

                if (_tkbnf3 == "2") {
                    _objBenhNhan["gioitinhid"] = "-1";
                }
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN", '' + '$' + '3' + '$' + JSON.stringify(_objBenhNhan));
                if (data_ar != null && data_ar.length > 0) {
                    var _ns = ($("#txtNGAYSINH").val() == "" && $("#txtNAMSINH").val() == "")
                        ? "-1" :
                        ($("#txtNGAYSINH").val() != ""
                                ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val()
                        );
                    var param = {
                        ten: $("#txtTENBENHNHAN").val() == '' ? '-1' : $("#txtTENBENHNHAN").val().toUpperCase().trim(),
                        ngaysinh: _ns,
                        gioitinh: $("#cboGIOITINHID").val(),
                        mabhyt: '-1',
                        type: '1' // ko cap nhat
                    };

                    if (_tkbnf3 == "1") {
                        param.ngaysinh = $("#txtNAMSINH").val();
                    }

                    if (_tkbnf3 == "2") {
                        param.gioitinh = "-1";
                    }
                    _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'TÌM KIẾM BỆNH NHÂN', 980, 510, {closeButton: false});
                }

            }
        });
        // end sondn L2PT-2944

        $("#txtSDTBENHNHAN").focusout(function () {
            $("#txtSDTBENHNHAN").val($("#txtSDTBENHNHAN").val().replace(/\s+/g, ""));
            var _tkbnsdt = cfObj.NGT_TIMKIEM_THEO_SDT;
            if (_tkbnsdt == "1" && $("#hidBENHNHANID").val() == "") {
                var _strSDT = jsonrpc.AjaxJson.ajaxExecuteQueryO("GET_BN_SDT", [{
                    "name": "[0]",
                    "value": $("#txtSDTBENHNHAN").val()
                }]);
                if (_strSDT && _strSDT.length > 0) {
                    rows = JSON.parse(_strSDT)
                    $("#txtTENBENHNHAN").val(rows[0].TENBENHNHAN);
                    $("#txtNGAYSINH").val(rows[0].NGAYSINH);
                    $("#cboGIOITINHID").val(rows[0].GIOITINHID);
                    $("#txtNAMSINH").val(rows[0].NAMSINH);

                    var _objBenhNhan = new Object();
                    _objBenhNhan["tenbenhnhan"] = $("#txtTENBENHNHAN").val().toUpperCase().trim();
                    _objBenhNhan["ngaysinh"] = $("#txtNGAYSINH").val() != "" ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val().trim();
                    _objBenhNhan["gioitinhid"] = $("#cboGIOITINHID").val();
                    if (_tkbnf3 == "1") {
                        _objBenhNhan["ngaysinh"] = $("#txtNAMSINH").val();
                    }

                    /*if (_tkbnf3 == "2"){
		            	_objBenhNhan["gioitinhid"] = "-1";
		            }*/
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN", '' + '$' + '3' + '$' + JSON.stringify(_objBenhNhan));
                    if (data_ar != null && data_ar.length > 0) {
                        var _ns = ($("#txtNGAYSINH").val() == "" && $("#txtNAMSINH").val() == "")
                            ? "-1" :
                            ($("#txtNGAYSINH").val() != ""
                                    ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val()
                            );
                        var param = {
                            ten: $("#txtTENBENHNHAN").val() == '' ? '-1' : $("#txtTENBENHNHAN").val().toUpperCase().trim(),
                            ngaysinh: _ns,
                            gioitinh: $("#cboGIOITINHID").val(),
                            mabhyt: '-1',
                            type: '1' // ko cap nhat
                        };

                        if (_tkbnf3 == "1") {
                            param.ngaysinh = $("#txtNAMSINH").val();
                        }

                        if (_tkbnf3 == "2") {
                            param.gioitinh = "-1";
                        }
                        _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'TÌM KIẾM BỆNH NHÂN', 980, 510, {closeButton: false});
                    }
                }
            }
        });

        $('#cboDT_SINHSONG').on('change', function (e) {
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
        });

        $('input[type=radio][name=radHINHTHUCVAOVIENID]').change(function () {
            if (this.value == '2' && $("#cboDTBNID").val() == "1") {
                $("#cboBHYT_LoaiID").val(3);
                $("#cboBHYT_LoaiID").change();
                $('#chkNHANTHUOCTHEOHEN').prop('checked', false);
                $('input[type=radio][name=radNHANTHUOC][value=1]').prop('checked', false);
                $("[name='radNHANTHUOC']").change();
            }
            if (this.value == '2'){
                $('#chkKCB_HIVAIDS').prop('checked', false);
            }
            if (_tgtiepnhan == 1) {
                if (this.value == '2') {
                    $("#dvNGAYTIEPNHAN").show();
                    $("#dvNGAYTIEPNHANPLACEHOLDER").hide();
                } else {
                    $("#dvNGAYTIEPNHAN").hide();
                    $("#dvNGAYTIEPNHANPLACEHOLDER").show();
                }
            }
        });
        $('#chkNHANTHUOCTHEOHEN').on('change', function (e) {
            if ($('#chkNHANTHUOCTHEOHEN').is(":checked") ) {
                $('input[type=radio][name=radHINHTHUCVAOVIENID][value=3]').prop('checked', true);
            }
        });
        /*
		$('#cboHD').on('change', function (e) {
			$('#hidHOPDONGID').val($(this).val());
		});*/

        $('#txtTKGIOITINHID').on('change', function (e) {
            fillValueDisplay('txtTKGIOITINHID', 'cboGIOITINHID');
        });

        //nghe nghiep
        //$('#cboNGHENGHIEPID').on('change', function (e) {
        //	fillValueDisplay('cboNGHENGHIEPID','txtTKNGHENGHIEPID');
        //});
        //$('#txtTKNGHENGHIEPID').on('change', function (e) {
        //	fillValueDisplay('txtTKNGHENGHIEPID','cboNGHENGHIEPID');
        //});

        //sondn L2PT-24858
        $('#cboNGHENGHIEPID').on('change', function (e) {
            var _vl = $('#cboNGHENGHIEPID' + " option:selected").attr('extval0');
            $('#txtTKNGHENGHIEPID').val(_vl);
        });
        $('#txtTKNGHENGHIEPID').on('change', function (e) {
            var _vl = $('#txtTKNGHENGHIEPID').val().trim();
            var i = $("#cboNGHENGHIEPID").find("option[extval0='" + _vl + "']").val();
            $("#cboNGHENGHIEPID").val(i);
        });

        // dan toc
        $('#cboDANTOCID').on('change', function (e) {
            if (tk_madantoc == 1) {
                var _vl = $('#cboDANTOCID' + " option:selected").attr('extval0');
                $('#txtTKDANTOCID').val(_vl);
            } else {
                fillValueDisplay('cboDANTOCID', 'txtTKDANTOCID');
            }
        });
        $('#txtTKDANTOCID').on('change', function (e) {
            if (tk_madantoc == 1) {
                var _vl = $('#txtTKDANTOCID').val().trim();
                var i = $("#cboDANTOCID").find("option[extval0='" + _vl + "']").val();
                $("#cboDANTOCID").val(i);
            } else {
                fillValueDisplay('txtTKDANTOCID', 'cboDANTOCID');
            }
        });

        // quoc gia
        $('#cboQUOCGIAID').on('change', function (e) {
            fillValueDisplay('cboQUOCGIAID', 'txtTKQUOCGIAID');

            //L2PT-43164
            try {
                if ($(this).val() != '0') $('#cboDANTOCID').val('99').change();
            } catch (ex) {
            }
        });

        $('#txtTKQUOCGIAID').on('change', function (e) {
            fillValueDisplay('txtTKQUOCGIAID', 'cboQUOCGIAID');
        });

        // kham nhieu phong
        $("#btnKhamNhieuPhong").on("click", function (e) {
            var param = {
                khambenhid: $('#hidKHAMBENHID').val()
            };
            _showDialog("ngoaitru/NGT01T007_khamnhieuphong", param, 'ĐĂNG KÝ KHÁM NHIỀU PHÒNG', 450, 490);
        });

        // click view anh lon
        $("#imgBN").on("click", function (e) {
            var param = {
                url: $('#imgBN').attr('src'),
            };
            _showDialog("ngoaitru/NGT02K059_show_img", param, 'THÔNG TIN ẢNH', 650, 540);
        });

        $("#toolbarIdbtnTNTT").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidBENHNHANID").val() == "" || $("#hidHOSOBENHANID").val() == "") {
                DlgUtil.showMsg("Chọn bệnh nhân để chỉnh sửa thông tin này. ");
                return;
            }
            var paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgTaiNanThuongTich", "divDlg", "manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich", paramInput, "Thông tin tai nạn thương tích", 1000, 460);
            DlgUtil.open("dlgTaiNanThuongTich");
        });
        $("#toolbarIdbtnTHANHTOANVP").on("click", function () {
            var selRowId = $('#grdDanhSachTiepNhan').jqGrid('getGridParam', 'selrow');
            var rowData = $("#grdDanhSachTiepNhan").jqGrid('getRowData', selRowId);
            var paramInput = {
                mahosobenhan: $('#txtMABENHNHAN').val(),
                tiepnhanid: $('#hidTIEPNHANID').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgVP", "divDlg", "manager.jsp?func=../vienphi/VPI01T001_thuvienphi", paramInput, "Thanh toán viện phí", $(window).width() - 50, $(window).height() - 100);
            DlgUtil.open("divDlgVP");
        });
        //dannd_L2PT-34927
        $('input:radio[name="radNHANTHUOC"]').change(function () {
            if ($(this).is(':checked')) {
                if ($(this).val() == '1') {
                    $("#txtBACSIHOICHAN").val("");
                    $("#cboBACSIHOICHAN").val("");
                    $("#txtBACSIHOICHAN").prop("disabled", true);
                    $("#cboBACSIHOICHAN").prop("disabled", true);
                    $("#btnCLEARBACSIHOICHAN").prop("disabled", true);
                    $("#cboNOILINHTHUOC").prop("disabled", false);
                    $("#txtNGAY_CAPGIAY1").prop("disabled", false);
                    $("#txtDV_CAPGIAY1").prop("disabled", false);
                    $("#cboTEN_DV_CAPGIAY1").prop("disabled", false);
                    $('input[type=radio][name=radHINHTHUCVAOVIENID][value=3]').prop('checked', true);
                } else {
                    $("#cboNOILINHTHUOC").val('0');
                    $("#txtNGAY_CAPGIAY1").val('');
                    $("#txtDV_CAPGIAY1").val("");
                    $("#cboTEN_DV_CAPGIAY1").val("");
                    $("#txtBACSIHOICHAN").prop("disabled", false);
                    $("#cboBACSIHOICHAN").prop("disabled", false);
                    $("#btnCLEARBACSIHOICHAN").prop("disabled", false);
                    $("#cboNOILINHTHUOC").prop("disabled", true);
                    $("#txtNGAY_CAPGIAY1").prop("disabled", true);
                    $("#txtDV_CAPGIAY1").prop("disabled", true);
                    $("#cboTEN_DV_CAPGIAY1").prop("disabled", true);
                }
                $('#chkKCB_HIVAIDS').prop('checked', false);
            }else {
                $("#txtBACSIHOICHAN").val("");
                $("#cboBACSIHOICHAN").val("");
                $("#txtBACSIHOICHAN").prop("disabled", true);
                $("#cboBACSIHOICHAN").prop("disabled", true);
                $("#btnCLEARBACSIHOICHAN").prop("disabled", true);
                $("#cboNOILINHTHUOC").val('0');
                $("#txtNGAY_CAPGIAY1").val('');
                $("#txtDV_CAPGIAY1").val("");
                $("#cboTEN_DV_CAPGIAY1").val("");
                $("#cboNOILINHTHUOC").prop("disabled", true);
                $("#txtNGAY_CAPGIAY1").prop("disabled", true);
                $("#txtDV_CAPGIAY1").prop("disabled", true);
                $("#cboTEN_DV_CAPGIAY1").prop("disabled", true);
            }
        });
        $("[name='radNHANTHUOC']").each(function () {
            $(this).on("click", function () {
                if (lastChecked ==  $(this).val()) {
                    $(this).prop("checked", false);
                    $('input:radio[name="radNHANTHUOC"]').change();
                    lastChecked = null;
                } else {
                    lastChecked = $(this).val();
                }
            });
        });
        $('#chkKCB_HIVAIDS').on('change', function (e) {
            if ($('#chkKCB_HIVAIDS').is(":checked") ) {
                $('input[type=radio][name=radHINHTHUCVAOVIENID][value=3]').prop('checked', true);
                $("[name='radNHANTHUOC']").each(function () {
                    $(this).prop('checked', false);
                });
                $("[name='radNHANTHUOC']").change();
                $('#chkNHANTHUOCTHEOHEN').prop('checked', false);
                $('#cboBHYT_LoaiID').val(1);
                $('#cboBHYT_LoaiID').change();
            }
        });
        $("#btnCLEARBACSIHOICHAN").on("click", function () {
            $("#txtBACSIHOICHAN").val("");
            var option = $('<option value="">--Lựa chọn--</option>');
            $("#cboBACSIHOICHAN").empty();
            $("#cboBACSIHOICHAN").append(option);
        });
        //click button scanfile.
        $("#toolbarIdbtnScanFile").on("click", function (e) {
            if ($("#hidHOSOBENHANID").val() == "") {
                DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
                return;
            }
            _scanfile($("#hidHOSOBENHANID").val());
        });

        function _scanfile(_hosobenhanid) {
            var myVar = {
                hosobenhanid: _hosobenhanid
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgScanFile", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K096_ScanFile", myVar, "Scan file", 1000, 600);
            DlgUtil.open("dlgScanFile");
        }

        $("#toolbarIdbtnThuTien").on("click", function (e) {
            var myVar = {
                tiepnhanid: $('#hidTIEPNHANID').val(),
                dichvukhambenhid: $('#hidDICHVUKHAMBENHID').val(),
                modetiepnhan: "0"							// che do hien thi binh thuong, khong refresh lai trang;
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgCV", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T009_thutien"
                , myVar, "Thu tiền khám", 1200, 600);
            DlgUtil.open("dlgCV");
        });

        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {pattern: /\d/},
                '9': {pattern: /\d/, optional: true},
                '#': {pattern: /\d/, recursive: true},
                'A': {pattern: /[a-zA-Z0-9]/},
                'S': {pattern: /[a-zA-Z]/}
            }
        };

        $(':input').keydown(function (e) {
            var id = $(this).attr('id');
            if (e.which === 13 || e.which === 9) {				// tab hoac enter
                if (id == "txtMABENHNHAN") {
                    _timKiemBenhNhan($("#txtMABENHNHAN").val().trim(), '1');
                    $("#txtNGAYTIEPNHAN").val(_ngayhientaict);
                } else if (id == "txtMA_BHYT" && $('#txtMA_BHYT').val().trim().length == 15) {
                    _trangthaikhambenhcheck = 0; 				// thong bao trang thai kham;
                    _timKiemBenhNhan($("#txtMA_BHYT").val().toUpperCase().trim(), '2');
                    _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
                    $("#txtBHYT_BD").focus();
                    popUpTrangThaiKham();
                } else if (id == "txtMABENHANNDH") {									// sondn L2PT-30169
                    _timKiemBenhNhan($("#txtMABENHANNDH").val().trim(), '5');
                    $("#txtNGAYTIEPNHAN").val(_ngayhientaict);
                } else if (id == "cboDIAPHUONGID") {
                    if ($("#txtDIEUHUONGTIEPNHAN").val() == "1") {				// cau hinh thay doi dieu huong
                        $('#txtTKHC_TINHID').focus();
                    } else {
                        if ($('#cboDIAPHUONGID option:selected').text() != "") {
                            // $('#txtNOILAMVIEC').focus();
                            $('#txtSDTBENHNHAN').focus();
                        } else {
                            $('#txtTKHC_TINHID').focus();
                        }
                    }
                }else if (id == "txtDIENTHOAINGUOITHAN") { //L2PT-102094
                    $("#txtLYDOVAOVIEN").focus();
                }else if (id == "txtLYDOVAOVIEN") {
                    $("#cboDT_SINHSONG").focus();
                } else if (id == "txtDIACHI_BHYT") {
                    $('#txtTKBHYT_LoaiID').focus();
                } else if (id == "txtNGAYDU5NAM") {
                    if ($('#cboBHYT_LoaiID').val() == "2") {
                        $('#txtTKMACHANDOANTUYENDUOI').focus();
                    }
                } else if (id == "txtTUOI") {
                    if ($("#txtDIEUHUONGTIEPNHAN").val() == "1") {		// cau hinh thay doi dieu huong
                        $("#cboGIOITINHID").focus();
                    } else {
                        $("#txtTKGIOITINHID").focus();
                    }
                } else if (id == "chkUUTIENKHAMID") {
                    if (_doicongkhamphongkham == "0") {
                        $("#txtTKDICHVUID").focus();
                    } else {
                        $("#txtTKPHONGID").focus();
                    }
                } else {
                    if (id == "txtTUOI") {
                        $('#cboGIOITINHID').focus();
                    } else if (id == "cboGIOITINHID") {
                        $('#cboNGHENGHIEPID').focus();
                    } else if (id == "cboNGHENGHIEPID") {
                        $('#cboDANTOCID').focus();
                    } else if (id == "txtTENNGUOITHAN") {
                        if (_moiquanhe == "1") {
                            $('#cboMOIQUANHEID').focus();
                        } else {
                            $('#txtDIACHINGUOITHAN').focus();
                        }
                    } else if (id == "txtSDTBENHNHAN") {
                        $('#txtDIENTHOAINGUOITHAN').focus();
                    } else {
                        var index = $(':input').index(this) + 1;
                        $(':input').eq(index).focus();
                    }

                }
                if (e.which === 9) {						// Tab
                    e.preventDefault(); 					// chan cac event ung voi phim Tab
                }

            }
        });

        var f3 = 114;
        var f4 = 115;
        var f5 = 116;
        var fesc = 27;
        $(document).unbind('keydown').keydown(function (e) {
            if (e.keyCode == f3) {
                var focused = $(':focus');
                focused.blur();
                e.preventDefault();

                var param = {
                    ten: $("#txtTENBENHNHAN").val() == '' ? '-1' : $("#txtTENBENHNHAN").val(),
                    ngaysinh: $("#txtNGAYSINH").val() == '' ? '-1' : $("#txtNGAYSINH").val(),
                    gioitinh: $("#txtTENBENHNHAN").val() == '' ? '-1' : $("#cboGIOITINHID").val(),
                    mabhyt: $("#txtMA_BHYT").val() == '' ? '-1' : $("#txtMA_BHYT").val(),
                    type: '0' // ko cap nhat
                };

                // SONDN 10/01/2020 L2PT-14752
                if (_tkbnf3 == "1") {
                    param.ngaysinh = $("#txtNAMSINH").val();
                }
                // END SONDN 10/01/2020 L2PT-14752

                if (param.ngaysinh == "") {
                    param.ngaysinh = "-1"; 				// k chon, set = -1;
                }

                // sondn L2PT-26846
                if (_tkbnf3 == "2") {
                    param.gioitinh = "-1";
                }
                // end sondn L2PT-26846

                // SONDN 19/05/2020 L2PT-20994
                if (_bochecktrungtt == "2") {
                    param.diaphuongid = $("#cboDIAPHUONGID").val();
                }
                // END SONDN 19/05/2020 L2PT-20994

                _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'TÌM KIẾM BỆNH NHÂN', 980, 510, {closeButton: false});
            }

            if (e.keyCode == f4) {
                var focused = $(':focus');
                focused.blur();
                e.preventDefault();
                _capnhat(0);				// luu co tim kiem ttbn cu
            }

            if (e.keyCode == f5) {
                e.preventDefault();
                window.location.reload(true);
            }
        });

        $("#btnInPhieuKham").on("click", function (e) {
            _inPhieuKham(3);
        });

        $("#btnDSHENKHAM").on("click", function (e) {
            var focused = $(':focus');
            focused.blur();
            e.preventDefault();

            var param = {
                ten: '-1',
                ngaysinh: '-1',
                gioitinh: '-1',
                mabhyt: '-1',
                ishenkham: 1,
                type: '0' // ko cap nhat
            };
            _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'DANH SÁCH BỆNH NHÂN HẸN KHÁM', 980, 510, {closeButton: false});
        });
        $("#btnDSHENKHAMKIOS").on("click", function (e) {
            var param = {
                ten: '-1',
                ngaysinh: '-1',
                gioitinh: '-1',
                mabhyt: '-1',
                ishenkham: 1,
                type: '0',
                func : "KIOSDONGTHAP"
            };
            _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'DANH SÁCH BỆNH NHÂN KIOS', 980, 510, {closeButton: false});
        });
        $("#btnDSBNIMPORT").on("click", function (e) {
            var focused = $(':focus');
            focused.blur();
            e.preventDefault();
            var param = {
                hopdongid: $("#cboHOPDONGID").val()
            };
            _showDialog("ngoaitru/NGT03K011_DSBN_IMPORT", param, 'DANH SÁCH BỆNH NHÂN ', 1000, 600, {closeButton: false});
        });

        $("#btnGoiTiep").on('click', function () {
            var _obj1 = new Object();
            _obj1.MODE = "1"; 				// CHE DO GOI SO
            _obj1.PHONGID = _opts.phongid;
            _obj1.KHOAID = _options._khoaid;
            _obj1.STTBD = $("#txtstt_bd").val() == "" ? "0" : $("#txtstt_bd").val();
            _obj1.SLGOI = $("#txtsl_goi").val() == "" ? "1" : $("#txtsl_goi").val();

            var vstt_goi1 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T001.STT1", JSON.stringify(_obj1));
            if (typeof vstt_goi1 != 'undefined') {
                var row = vstt_goi1.split('@');
                $('#txtstt_bd').val(row[1]);
                $('#txtstt_kt').val(row[2]);
                $('#txtsl_goi').val(row[3]);
            }

            if (_thutugoikham == "0") {										// k su dung hang doi;
                var delay = 0;
                if (parseInt($('#txtstt_bd').val()) == parseInt($('#txtstt_kt').val())) {
                    //L2PT-28376 L2PT-31615
                    if (NGT_GOIKHAM_HANGDOI == '1' && check_note) {
                        var _texts = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " vào " + _sophongtiepdon;
                        var _jsonGoiKham = new Object();
                        _jsonGoiKham.ORG_ID = _opts.phongid;
                        _jsonGoiKham.STT = $('#txtstt_bd').val();
                        _jsonGoiKham.BENHNHANID = "";
                        _jsonGoiKham.LOAI = "0";
                        _jsonGoiKham.GOIKHAM_TEXT = _texts;
                        ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.GOIKHAM.INS", JSON.stringify(_jsonGoiKham));
                    } else {
                        delay = 13;
                        if (_checkGoiKham(delay) === "0") {
                            DlgUtil.showMsg('Loa đang được sử dụng, bạn gọi lại sau giây lát.');
                            return false;
                        }

                        if (_chedogoikham == "2") {
                            if (opt.hospital_id == 35600) {
                                var _texts = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " vào phòng 105";
                            }else{
                                var _texts = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " vào " + _sophongtiepdon;
                            }
                            goiKhamGG(_texts, _modeGoiKham, _timeOutGoiKham);
                        } else {
                            _sophongtiepdon = isNaN(_sophongtiepdon) || _sophongtiepdon == "" ? "0" : _sophongtiepdon;
                            call.goi_mot_bn_dangky(parseInt($('#txtstt_bd').val()), _sophongtiepdon, $("#txtNGTGOIBENHNHAN").val());
                        }
                    }
                } else {
                    delay = 20;
                    if (_checkGoiKham(delay) === "0") {
                        DlgUtil.showMsg('Loa đang được sử dụng, bạn gọi lại sau giây lát.');
                        return false;
                    }
                    if (_chedogoikham == "2") {
                        var _texts = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " tới số " + $('#txtstt_kt').val() + " vào " + _sophongtiepdon;
                        goiKhamGG(_texts, _modeGoiKham, _timeOutGoiKham);
                    } else {
                        call.goidangky(parseInt($('#txtstt_bd').val()) + parseInt($('#txtsl_goi').val()),
                            parseInt($('#txtstt_bd').val()) + (parseInt($('#txtsl_goi').val()) - 1),
                            $("#txtNGTGOIBENHNHAN").val());
                    }

                }
            } else {											// su dung hang doi;
                var obj = new Object();
                obj.STRGOIKHAM = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " vào " + _sophongtiepdon;
                obj.MODE = "0"; 								// 0: day du lieu vao;
                obj.PHONGID = _opts.phongid;
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBNHD", JSON.stringify(obj));
                if (data_ar != null || data_ar.length > 0) {
                    $.bootstrapGrowl('Đã đẩy bệnh nhân vào hàng đợi chờ gọi', {
                        type: 'warning',
                        delay: 3000,
                    });
                } else {
                    $.bootstrapGrowl('Lỗi không đẩy được BN vào hàng đợi chờ gọi.', {
                        type: 'warning',
                        delay: 3000,
                    });
                }
            }
        });

        $("#btnSetLai").on('click', function () {
            if ($("#txtstt_bd").val() == "" || isNaN($("#txtstt_bd").val())) {
                DlgUtil.showMsg("Yêu cầu điền STT để set lại", function () {
                    $("#txtstt_bd").focus();
                });
                return;
            }
            DlgUtil.showConfirm("Thao tác sẽ thiết lập lại số bắt đầu gọi bắt đầu từ " + $("#txtstt_bd").val() + ", bạn có muốn tiếp tục? ", function (flag) {
                if (flag) {
                    var _obj1 = new Object();
                    _obj1.MODE = "2"; 				// CHE DO SET LAI
                    _obj1.PHONGID = _opts.phongid;
                    _obj1.KHOAID = _options._khoaid;
                    _obj1.STTBD = $("#txtstt_bd").val() == "" ? "0" : $("#txtstt_bd").val();
                    _obj1.SLGOI = $("#txtsl_goi").val() == "" ? "1" : $("#txtsl_goi").val();

                    var vstt_goi1 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T001.STT1", JSON.stringify(_obj1));
                    if (typeof vstt_goi1 != 'undefined') {
                        var row = vstt_goi1.split('@');
                        $('#txtstt_bd').val(row[1]);
                        $('#txtstt_kt').val(row[2]);
                        $('#txtsl_goi').val(row[3]);
                        DlgUtil.showMsg("Đã thiết lập thành công về STT " + row[1]);
                    }
                }
            });
        });

        $("#btnGoiLai").on('click', function () {
            if (_thutugoikham == "0") {										// k su dung hang doi;
                var delay = 0;
                if (parseInt($('#txtstt_bd').val()) == parseInt($('#txtstt_kt').val())) {
                    delay = 13;
                    if (_checkGoiKham(delay) === "0") {
                        DlgUtil.showMsg('Loa đang được sử dụng, bạn gọi lại sau giây lát.');
                        return false;
                    }
                    if (_chedogoikham == "2") {
                        var _texts = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " vào " + _sophongtiepdon;
                        goiKhamGG(_texts, _modeGoiKham, _timeOutGoiKham);
                    } else {
                        _sophongtiepdon = isNaN(_sophongtiepdon) || _sophongtiepdon == "" ? "0" : _sophongtiepdon;
                        call.goi_mot_bn_dangky($('#txtstt_bd').val(), _sophongtiepdon, $("#txtNGTGOIBENHNHAN").val());
                    }

                } else {
                    delay = 20;
                    if (_checkGoiKham(delay) === "0") {
                        DlgUtil.showMsg('Loa đang được sử dụng, bạn gọi lại sau giây lát.');
                        return false;
                    }
                    if (_chedogoikham == "2") {
                        var _texts = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " tới số " + $('#txtstt_kt').val() + " vào " + _sophongtiepdon;
                        goiKhamGG(_texts, _modeGoiKham, _timeOutGoiKham);
                    } else {
                        call.goidangky($('#txtstt_bd').val(), $('#txtstt_kt').val(), $("#txtNGTGOIBENHNHAN").val());
                    }

                }
            } else {											// su dung hang doi;
                var obj = new Object();
                obj.STRGOIKHAM = "Mời bệnh nhân số " + $('#txtstt_bd').val() + " vào " + _sophongtiepdon;
                obj.MODE = "0"; 								// 0: day du lieu vao;
                obj.PHONGID = _opts.phongid;
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBNHD", JSON.stringify(obj));
                if (data_ar != null || data_ar.length > 0) {
                    $.bootstrapGrowl('Đã đẩy bệnh nhân vào hàng đợi chờ gọi', {
                        type: 'warning',
                        delay: 3000,
                    });
                } else {
                    $.bootstrapGrowl('Lỗi không đẩy được BN vào hàng đợi chờ gọi.', {
                        type: 'warning',
                        delay: 3000,
                    });
                }
            }
        });

        $("#btnTiepNhanByMK").click(function () {
            let myVar = {};
            dlgPopup = DlgUtil.buildPopupUrl("ICAOFinger", "divDlg", "manager.jsp?func=../danhmuc/ICAOFinger_Interface", myVar, "Sinh trắc học", 1000, 600);

            DlgUtil.open("ICAOFinger");
            EventUtil.setEvent("ICAOFinger_onClose", function (e) {
                try {
                    if (window.websocket.MK_FINGER) {
                        window.websocket.MK_FINGER.close();
                    }
                } catch (err) {
                    console.log(err);
                }
            });
            EventUtil.setEvent("iCaoFingerAccept", function (e) {
                DlgUtil.close("ICAOFinger");
                try {
                    if (window.websocket.MK_FINGER) {
                        window.websocket.MK_FINGER.close();
                    }
                } catch (err) {
                    console.log(err);
                }
                setTimeout(function () {
                    $("#txtTENBENHNHAN").val(e.ttbn.hoten).change();
                    $("#txtNGAYSINH").val(e.ttbn.ngaysinh).change();
                    if (e.ttbn.gioitinh.toLowerCase() == "nam") {
                        $("#txtTKGIOITINHID").val("1").change();
                    } else if (e.ttbn.gioitinh.toLowerCase() == "nu") {
                        $("#txtTKGIOITINHID").val("2").change();
                    } else {
                        $("#txtTKGIOITINHID").val("3").change();
                    }

                    $("#txtSOCMTND").val(e.ttbn.cccd).change();
                    $("#txtSOCMTND").trigger("focus");
                    $("#txtSOCMTND").trigger("focusout");
                }, 100);
            });
        });

        $("#txtstt_bdtang").on("click", function (e) {
            _tanggiam_stt('txtstt_bd', 1);
        });

        $("#txtstt_bdgiam").on("click", function (e) {
            _tanggiam_stt('txtstt_bd', 2);
        });

        $("#txtstt_kttang").on("click", function (e) {
            _tanggiam_stt('txtstt_kt', 1);
        });

        $("#txtstt_ktgiam").on("click", function (e) {
            _tanggiam_stt('txtstt_kt', 2);
        });

        $("#txtsl_goitang").on("click", function (e) {
            _tanggiam_stt('txtsl_goi', 1);
        });

        $("#txtsl_goigiam").on("click", function (e) {
            _tanggiam_stt('txtsl_goi', 2);
        });

        $("#toolbarIdprint_1").on("click", function (e) {
            _inPhieuKham(0);
        });
        $("#toolbarIdinBangKe").on("click", function (e) {
            vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#hidDOITUONGBENHNHANID").val(), '1');
        });

        $("#toolbarIdprint_2").on("click", function (e) {
            var objData = new Object();
            FormUtil.setFormToObject("tabTiepNhan", "", objData);
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: objData["KHAMBENHID"]
            }];
            var rpName = "NGT_BARCODE_A6";
            CommonUtil.inPhieu('window', 'NGT_BARCODE_A6', 'pdf', par, rpName);
        });
        $("#btnInBarcode").on("click", function (e) {
            var objData = new Object();
            FormUtil.setFormToObject("tabTiepNhan", "", objData);
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: objData["KHAMBENHID"]
            }];
            var rpName = "NGT_BARCODE_A6";
            CommonUtil.inPhieu('window', 'NGT_BARCODE_A6', 'pdf', par, rpName);
        });

        $("#toolbarIdprint_5").on("click", function (e) {
            var objData = new Object();
            FormUtil.setFormToObject("tabTiepNhan", "", objData);
            var par = [{
                name: 'benhnhanid',
                type: 'String',
                value: $("#hidBENHNHANID").val()
            }];

            // sondn L2PT-12458
            if (_options.hospital_id == "919") {
                openReport('window', "IN_THEKHAMBENH_A6", "pdf", par);
            } else {
                var rpName = "IN_THEKHAMBENH_A6";
                CommonUtil.inPhieu('window', 'IN_THEKHAMBENH_A6', 'pdf', par, rpName);
            }
            // end sondn L2PT-12458

        });

        $("#toolbarIdprint_6").on("click", function (e) {
            var objData = new Object();
            FormUtil.setFormToObject("tabTiepNhan", "", objData);
            if ($("#hidTIEPNHANID").val() == "") {
                DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để in.");
                return;
            }
            var par = [{
                name: 'i_tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }];
            openReport('window', "GIAYCHUNGNHANSUCKHOE", "pdf", par);
        });

        $("#toolbarIdprint_3").on("click", function (e) {
            _inPhieuTraTheBHYT();
        });

        $("#toolbarIdprint_BV_05").on("click", function (e) {
            var objData = new Object();
            FormUtil.setFormToObject("tabTiepNhan", "", objData);
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", "pdf", par);
        });

        // IN PHIEU CLS CHUNG;
        function _openReportClsLan(param, reportName, i_loainhommaubenhpham) {
            param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
            param.push({name: 'i_loainhommaubenhpham', type: 'String', value: i_loainhommaubenhpham});
            CommonUtil.openReportGet('window', reportName, "pdf", param);
        }

        $("#toolbarIdprint_4").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "") {
                DlgUtil.showMsg("Yêu cầu chọn bệnh nhân cần in phiếu. ");
                return;
            }

            var par = [];
            if (opt.hospital_id == 922) {
                _openReportClsLan(par, "PHIEU_CLSC_922", 1); 		// in xet nghiem rieng
                _openReportClsLan(par, "PHIEU_CLSC_922", 2); 		// in cdha rieng
                _openReportClsLan(par, "PHIEU_CLSC_922", 5);			//in pttt rieng
            } else {
                par.push({name: 'i_hid', type: 'String', value: opt.hospital_id});
                par.push({name: 'i_sch', type: 'String', value: opt.db_schema});
                par.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
                openReport('window', "PHIEU_CLSC", "pdf", par);
            }
        });

        $("#toolbarIdprint_xetnghiemchung").on("click", function () {
            if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
                return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
            } else {
                var _benhnhanid = $("#hidBENHNHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _self = this;
                paramInput = {
                    benhnhanid: _benhnhanid,
                    khambenhid: _khambenhid,
                    hosobenhanid: _hosobenhanid,
                    lnmbp: LNMBP_XetNghiem,
                    ingop: '1'//In gộp cùng phiếu
                };

                dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu XN", 1100, 600);
                DlgUtil.open("divDlgDeleteXN");
            }
        });

        $("#toolbarIdprint_cdhachung").on("click", function () {
            if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
                return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
            } else {
                var _benhnhanid = $("#hidBENHNHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _self = this;
                paramInput = {
                    benhnhanid: _benhnhanid,
                    khambenhid: _khambenhid,
                    hosobenhanid: _hosobenhanid,
                    lnmbp: LNMBP_CDHA,
                    ingop: '1'//In gộp cùng phiếu
                };

                dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In CĐHA chung", 1100, 600);
                DlgUtil.open("divDlgDeleteXN");
            }
        });

        $("#btnLuu").bindOnce("click", function () {
            _capnhat(0);
        }, 5000);

        $("#btnThemMoi").bindOnce("click", function () {
            _themmoiBenhAn();
        }, 5000);

        $("#btnTiepTheo").on("click", function (e) {
            window.location.reload(true);
        });

        $("#btnChupHinh").on("click", function () {
            var _hsbaid = $('#hidHOSOBENHANID').val();
            if (_hsbaid == "" || _hsbaid == null || _hsbaid == 'null') {
                _hsbaid = "0";
            }
            var myVar = {
                hosobenhanid: _hsbaid
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgChupAnh", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K026_ChupAnhBenhNhan", myVar, "CHỤP ẢNH", 450, 460);
            DlgUtil.open("dlgChupAnh");
        });

        $("#btnTimKiem").on("click", function () {
            _dsTiepNhan();
        });

        $("#btnDsExcel").on("click", function () {
            var sql_par = [];
            var _subdtbnid = "0";
            if (doituong_khaibao == 1 && $("#cboDTBNID1").val() != "0") {
                _subdtbnid = $('#cboDTBNID1' + " option:selected").attr('extval0');
            }
            sql_par.push({"name": "[6]", "value": _subdtbnid});
            var tungay;
            var denngay;
            if (cfObj.NGT_TNTK_GIOPHUTGIAY == "1") {
                tungay = $("#txtTUTK").val().length <= 10 ? $("#txtTUTK").val() + " 00:00:00" : $("#txtTUTK").val()
                denngay = $("#txtDENTK").val().length <= 10 ? $("#txtDENTK").val() + " 23:59:59" : $("#txtDENTK").val()
            } else {
                tungay = $("#txtTUTK").val();
                denngay = $("#txtDENTK").val();
            }
            var par = [{
                name: 'ngaybatdau',
                type: 'String',
                value: tungay
            }, {
                name: 'ngayketthuc',
                type: 'String',
                value: denngay
            }, {
                name: 'trangthaiid',
                type: 'String',
                value: $("#cboTRANGTHAI").val()
            }, {
                name: 'phongkhamid',
                type: 'String',
                value: $("#cboTKPK").val()
            }, {
                name: 'dtbnid',
                type: 'String',
                value: $("#cboDTBNID1").val()
            }, {
                name: 'subdtbnid',
                type: 'String',
                value: _subdtbnid
            }];
            var typeIn = 'XLS';
            CommonUtil.inPhieu('window', 'BAOCAO_DS_BN_TRANGTHAIKB', typeIn, par, 'ds_tiepnhan.xls');
        });

        //click tab danh sach tiep nhan
        $("#tabDanhSachTab").on("click", function () {
            _dsTiepNhan();
        });
        $("#tabThongKeTab").on("click", function () {
            loadGridThongke();
        });
        //click tab danh sach tiep nhan
        $("#tabTiepNhanTab").on("click", function () {
            height_divMain = $('#hidDocumentHeight').val();
            $('#divMain').css('height', height_divMain);
            $('#txtTENBENHNHAN').focus();
        });

        //click tab phieu kham
        $("#tabPhieuKhamTab").on("click", function () {
            _loadPhieukham();
        });
        $("#toolbarIdthemPhieu").on("click", function () {
            var selRowId = $('#grdDanhSachTiepNhan').jqGrid('getGridParam', 'selrow');
            var rowData = $("#grdDanhSachTiepNhan").jqGrid('getRowData', selRowId);
            if (rowData != null) {
                var obj = new Object();
                obj.HOSOBENHANID = $("#hidHOSOBENHANID").val();
                obj.TIEPNHANID = $("#hidTIEPNHANID").val();
                obj.KHAMBENHID = $("#hidKHAMBENHID").val();
                obj.MAHOSOBENHAN = $('#txtMABENHNHAN').val();
                obj.BENHNHANID = $("#hidBENHNHANID").val();
                obj.MAUBENHPHAMID = 1111;
                obj.DICHVUKHAMBENHID = 2222;
                obj.KHOAID = _opt.khoaid;
                obj.PHONGID = _opt.phongid;
                obj.LOAI_PHIEU = "NGT_TIEPNHAN";
                dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../noitru/NTU02D204_ThemPhieu", obj, "HIS-Thêm phiếu",
                    window.innerWidth * 0.95, window.innerHeight * 0.95);
                DlgUtil.open("divDlgThemPhieu");
            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân!');
                return;
            }
        });
        // thay doi trạng thái tự động load lại danh sách.
        $('#cboTRANGTHAI').change(function () {
            _dsTiepNhan();
        });

        $('#cboTKPK').change(function () {
            _dsTiepNhan();
        });
        $('#cboKENHDK').change(function () {
            _dsTiepNhan();
        });

        function _dsTiepNhan() {
            if (_kiemTraLietKe()) {
                loadGridData();
            } else {
                $("#grdDanhSachTiepNhan").jqGrid('resetSelection');
                $("#grdDanhSachTiepNhan").jqGrid("clearGridData", true);
            }
        }

        $("#grdDanhSachTiepNhan").jqGrid('setGridParam', {
            onSelectRow: function (id, selected) {
                loadtuyen = 1;
                if (selected === false) {
                    unRowSelect(id, "grdDanhSachTiepNhan");
                    _disableControl(['toolbarIdbtnTreat', 'toolbarIdbtnhandling', 'toolbarIdbtnHoaHong', 'toolbarIdThuTien', 'toolbarIdtreat_2', 'toolbarIdbtnThuTienKhac'], true);
                } else {
                    _clickchonbn = "1";
                    loadRowSelect(id, "grdDanhSachTiepNhan");
                    _disableControl(['btnChupHinh', 'btnSinhTon'], false);

                }
                GridUtil.unmarkAll('grdDanhSachTiepNhan');
                GridUtil.markRow('grdDanhSachTiepNhan', id);

                _loadLSDT($("#hidBENHNHANID").val());
                $('#txtTENBENHNHAN').focus();

                // RESET LAI THONG TIN GOI STT TRUONG HOP BN CAP NHAT LAI THONG TIN
                if ($("#hidHIENTHIGOIKHAM").val() == "5") {
                    $("#txtSTT5").val("");
                    $("#txtHOTEN5").val("");
                    $("#txtID5").val("");
                    $("#txtLANGOI5").val("");
                    $("#txtMABHYT5").val("");
                    $("#txtUUTIEN5").val("");
                    $("#txtMABENHNHAN5").val("");
                    $("#txtSOQUAY5").val("");
                    $("#txtMSGSTT").val("");
                }
            },
            ondblClickRow: function (id) {
                loadtuyen = 1;
                $("#tabNGT01T001 li, #tabDanhSach").removeClass("active");
                $("#tabNGT01T001 li:first, #tabTiepNhan").addClass("active");
                $('#txtTENBENHNHAN').focus();
                _clickchonbn = "2";

                // RESET LAI THONG TIN GOI STT TRUONG HOP BN CAP NHAT LAI THONG TIN
                if ($("#hidHIENTHIGOIKHAM").val() == "5") {
                    $("#txtSTT5").val("");
                    $("#txtHOTEN5").val("");
                    $("#txtID5").val("");
                    $("#txtLANGOI5").val("");
                    $("#txtMABHYT5").val("");
                    $("#txtUUTIEN5").val("");
                    $("#txtMABENHNHAN5").val("");
                    $("#txtSOQUAY5").val("");
                    $("#txtMSGSTT").val("");
                }
                $("#select2-cboNGHENGHIEPID-container").text($("#cboNGHENGHIEPID option:selected").text());

                popUpTrangThaiKham();
//			 	alert("final dbl click=" + $("#cboDTBNID").val() + "=> sub= " + $('#cboDTBNID'+" option:selected").attr('extval0'))
            },
            gridComplete: function (id) {
                if (_capnhathd != '1') {
                    $("#capnhathdkb").remove();
                }
                if (cfObj.NGT_TN_CAPNHAPTTHC != "1") {
                    $("#liUpdateTTHC").remove();
                }
                if (cfObj.NGT_TN_HIDE_XOABN == "1") {
                    $("#lixoabenhnhan").remove();
                }
                $(".jqgrow", '#grdDanhSachTiepNhan').contextMenu('contextMenu', {
                    bindings: {
                        'capnhathdkb': function (t) {
                            var myVar = {
                                act: 'cnhd',
                                hosobenhanid: $('#hidHOSOBENHANID').val()
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgHD", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K037_HDBN", myVar, "CẬP NHẬT BỆNH NHÂN VÀO HỢP ĐỒNG", 950, 460, {closeButton: false});
                            DlgUtil.open("dlgHD");
                        },
                        'lichuyenphongkham': function (t) {
                            _showPopupPhongKham(0, "Chuyển phòng khám", 700, 300);
                        },
                        'lichuyenpyckham': function (t) {
                            //tuyennx_add_20170906_start yc  HISL2BVDKHN-425
                            if ($('#hidXUTRIKHAMBENHID').val() == "6" || $('#hidXUTRIKHAMBENHID').val() == "2") {
                                DlgUtil.showMsg('Bệnh nhân đã xử trí nhập viện không thể Đổi công khám/phòng khám.');
                                return false;
                            }
                            //tuyennx_add_20170906_end
                            //tuyennx_add_20170906_start yc  HISL2NT-896
                            if ($('#hidTRANGTHAIKHAMBENH').val() == '4' || $('#hidTRANGTHAIKHAMBENH').val() == '9') {
                                DlgUtil.showMsg('Bệnh nhân đang khám hoặc đã khám xong. Không thể đổi công khám phòng khám.');
                                return false;
                            }
                            //tuyennx_add_20170906_end
                            _showPopupCongKham("Cập nhật công khám/phòng khám", 900, 300);
                        },
                        'lisinhsttkham': function (t) {
                            _showPopupSinhSTT(1);
                        },
                        'lixoabenhnhan': function (t) {
                            DlgUtil.showConfirm("Bạn chắc chắn muốn xóa bệnh nhân.?", function (flag) {
                                if (flag) {
                                    _xoabenhnhan();
                                    ;
                                }
                            });
                        },
                        'liinphieu': function (t) {
                            _showPopupSinhSTT(2);
                        },
                        'liinbarcode': function (t) {
                            $("#toolbarIdprint_5").click();
                        },
                        'li_in_TEM': function (t) {
                            var par = [{
                                name: 'khambenhid',
                                type: 'String',
                                value: $('#hidKHAMBENHID').val()
                            }, {
                                name: 'phongid',
                                type: 'String',
                                value: $('#cboPHONGKHAMID').val()
                            }, {
                                name: 'i_sch',
                                type: 'String',
                                value: _options.db_schema
                            }];
                            openReport('window', "NGT_STT_IN_TEM", "pdf", par);
                        },
                        'liUpdateTenTN': function (t) {
                            var row = $("#grdDanhSachTiepNhan").jqGrid('getRowData', t.id);
                            var myVar = {
                                mabenhan: row.MAHOSOBENHAN
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgUpdateTN", "divDlg", "manager.jsp?func=../ngoaitru/NGT06K003_UPDATE_TEN_TAYNGUYEN", myVar, "CẬP NHẬT THÔNG TIN BỆNH NHÂN", 950, 460, {closeButton: false});
                            DlgUtil.open("dlgUpdateTN");
                        },
                        'liUpdateTTHC': function (t) {
                            var row = $("#grdDanhSachTiepNhan").jqGrid('getRowData', t.id);
                            var paramInput = {
                                tiepnhanid: $('#hidTIEPNHANID').val(),
                                khambenhid: row.KHAMBENHID,
                                trangthaikhambenh: row.TRANGTHAIKHAMBENH
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("divDlgSuaBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân", 1100, 650);
                            DlgUtil.open("divDlgSuaBenhNhan");
                        },
                        'btnLS_1': function (t) {
                            paramInput = {
                                benhnhanId: $("#hidBENHNHANID").val()
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri", paramInput, "LỊCH SỬ ĐIỀU TRỊ", 1350, 600);
                            DlgUtil.open("dlgLichSuDieuTri");
                        },
                        'btnLayChuKyMau': function (t) {
                            var row = $("#grdDanhSachTiepNhan").jqGrid('getRowData', t.id);
                            paramInput = {
                                hosobenhanid: row.HOSOBENHANID,
                                mahosobenhan: row.MAHOSOBENHAN,
                                mabenhnhan: row.MABENHNHAN
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("divDlgLayChuKyBenhNhan",
                                "divDlg",
                                "manager.jsp?func=../ngoaitru/NGT01T001_LayChuKyMau",
                                paramInput,
                                "Lấy chữ ký bệnh nhân, thân nhân",
                                window.innerWidth * 0.95,
                                window.innerHeight * 0.95);
                            DlgUtil.open("divDlgLayChuKyBenhNhan");
                        }
                    },
                    onContextMenu: function (event, menu) {
                        var rowId = $(event.target).parent("tr").attr("id");
                        var grid = $('#grdDanhSachTiepNhan');
                        grid.setSelection(rowId);
                        return true;
                    }
                });

                // sau khi load xong moi remove phan tu di;
                if (_sttsangchieu == "0") {
                    $("#lisinhsttkham").remove();
                    $("#liinphieu").remove();
                }

                var ids = $("#grdDanhSachTiepNhan").getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#grdDanhSachTiepNhan").jqGrid('getRowData', id);
                    var _icon = '';
                    var _iconcls = '';

                    if (row.TRANGTHAIKHAMBENH == 1) {
                        if (row.DOITUONGBENHNHANID == 1) {
                            _icon = '<center><img src="' + _options.imgPath[0] + '" width="15px"></center>';
                        } else {
                            _icon = '<center><img src="' + _options.imgPath[5] + '" width="15px"></center>';
                        }
                    } else if (row.TRANGTHAIKHAMBENH == 4) {
                        _icon = '<center><img src="' + _options.imgPath[1] + '" width="15px"></center>';
                    } else if (row.TRANGTHAIKHAMBENH == 9) {
                        _icon = '<center><img src="' + _options.imgPath[2] + '" width="15px"></center>';
                    }

                    if (row.KQCLS == "1") {
                        _iconcls = '<center><img src="' + _options.imgPath[3] + '" width="15px"></center>';
                    } else if (row.KQCLS == "2") {
                        _iconcls = '<center><img src="' + _options.imgPath[4] + '" width="15px"></center>';
                    }
                    if (row.MADANGKY != 'undefined' && row.MADANGKY != '' && row.MADANGKY != '0') {
                        $("#grdDanhSachTiepNhan").jqGrid('setRowData', id, "", {color: NGT_HIENTHIMAU_DATLICH});
                    }
                    $("#grdDanhSachTiepNhan").jqGrid('setCell', id, 1, _icon);
                    $("#grdDanhSachTiepNhan").jqGrid('setCell', id, 2, _iconcls);
                }
                /*if($("#hidINDEX").val() != "-1"){
	    			$('#grdDanhSachTiepNhan').find("tr[id='" + $("#hidINDEX").val() + "']").find('td').trigger( "click" );
	    		}*/
            }
        });
        // sql cu NGT.TN.DSNGUONKHACH
        $('#cboNHOMNGUON_ID').on('change', function (e) {
            ComboUtil.getComboTag("cboNGUONKHACHID", "NGT.TN.NGUONKHACH", [{
                name: '[0]',
                value: $('#cboNHOMNGUON_ID').val()
            }], "", {extval: true, value: 0, text: 'Nguồn khách hàng'}, "sql");
        });

        //T/H/Xa
        $('#cboDIAPHUONGID').on('change', function (e) {
            getDiaChi($('#cboDIAPHUONGID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', docmathe);
        });

        // tinh
        $('#cboHC_TINHID').on('change', function (e) {
            _setDIAPHUONG(1);
        });

        // huyen
        $('#cboHC_HUYENID').on('change', function (e) {
            _setDIAPHUONG(2);
        });

        // xa
        $('#cboHC_XAID').on('change', function (e) {
            _setDIAPHUONG(3);
        });

        $('#txtNGAYSINH').on('change', function (e) {
            var b_ngaysinh = $('#txtNGAYSINH').val();
            var t = tinhTuoi(b_ngaysinh, 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');

            if ($('#cboDVTUOI').val() != "1") {
                $('#cboNGHENGHIEPID').val(3);
                $('#cboNGHENGHIEPID').change();
            } else {
                if (parseInt($('#txtTUOI').val()) < 7) {
                    $('#cboNGHENGHIEPID').val(3);
                    $('#cboNGHENGHIEPID').change();
                }
            }
            if (typeof (t) != 'undefined') {
                $("#cboDVTUOI").prop('disabled', true);
            }
            if (cfObj.NGT_NGHENGHIEP_THEOTUOI == 1) {
                if (($("#cboDVTUOI").val() == 2 && Number($('#txtTUOI').val()) < 6 * 12
                    || $("#cboDVTUOI").val() == 1 && Number($('#txtTUOI').val()) < 6
                    || $("#cboDVTUOI").val() == 3 && Number($('#txtTUOI').val()) < 6 * 365)) {
                    $('#cboNGHENGHIEPID').val(3);
                    $('#cboNGHENGHIEPID').change();
                    $("#cboDVTUOI").prop('disabled', true);
                }
                if (($("#cboDVTUOI").val() == 1 && Number($('#txtTUOI').val()) >= 6) &&
                    ($("#cboDVTUOI").val() == 1 && Number($('#txtTUOI').val()) <= 16)) {
                    $('#cboNGHENGHIEPID').val(4);
                    $('#cboNGHENGHIEPID').change();
                    $("#cboDVTUOI").prop('disabled', true);
                }
                if ($('#cboDVTUOI').val() == "3") {
                    var songaybatbuoc = cfObj.HIS_BATBUOCGIOSINH_SONGAY;
                    if (parseInt($('#txtTUOI').val()) <= songaybatbuoc && songaybatbuoc != 0) {
                        DlgUtil.showMsg('Bệnh nhân nhỏ hơn ' + songaybatbuoc + ' ngày bắt buộc nhập giờ sinh ');
                        $('#txtGIO_SINH').focus();
                    }
                }
            }
            _setUuTienDOB();
        });

        $('#txtTKHC_TINHID').on('change', function (e) {
            ComboUtil.findByExtra("cboHC_TINHID", $('#txtTKHC_TINHID').val(), 0);
        });

        $('#txtTKHC_HUYENID').on('change', function (e) {
            ComboUtil.findByExtra("cboHC_HUYENID", $('#txtTKHC_HUYENID').val(), 0);
        });

        $('#txtTKHC_XAID').on('change', function (e) {
            ComboUtil.findByExtra("cboHC_XAID", $('#txtTKHC_XAID').val(), 0);
        });

        $('#txtSONHA').on('change', function (e) {
            _setDiaChiBN('cboDTBNID', docmathe);
        });
        EventUtil.setEvent("assignSevice_loadData", function (e) {
            if (e.type == 1) {
                DlgUtil.close("divDlgSuaBenhNhan");
                DlgUtil.showMsg('Cập nhật bệnh nhân thành công');
            }
            if (e.type == 2) {
                DlgUtil.close("divDlgSuaBenhNhan");
                DlgUtil.showMsg('Tách bệnh nhân thành công');
            }
            loadGridData();
        });
        $('#txtTUOI').on('change', function (e) {
            var namht = jsonrpc.AjaxJson.getSystemDate('YYYY');
            var date;
            if ($("#cboDVTUOI").val() == 1) {
                $('#txtNAMSINH').val(namht - $(this).val());

                if (parseInt($('#txtTUOI').val()) > 150) {
                    DlgUtil.showMsg('Bệnh nhân lớn hơn 150 tuổi');
                    $('#txtTUOI').focus();
                }
            }
            if ($("#cboDVTUOI").val() == 2) {
                date = new Date(new Date().setTime(new Date().setMonth(new Date().getMonth() - $(this).val())));
                $('#txtNAMSINH').val(date.getFullYear());
                var bNgayHT = date.getDate();
                var bThangHT = date.getMonth() + 1; //January is 0!
                if (bNgayHT < 10) bNgayHT = "0" + bNgayHT.toString();
                if (bThangHT < 10) bThangHT = "0" + bThangHT.toString();
                $('#txtNGAYSINH').val(bNgayHT + "/" + bThangHT + "/" + date.getFullYear());
            }
            if ($("#cboDVTUOI").val() == 3) {
                date = new Date(new Date().setTime(new Date().getTime() - ($(this).val() * 24 * 60 * 60 * 1000)));
                $('#txtNAMSINH').val(date.getFullYear());
                var bNgayHT = date.getDate();
                var bThangHT = date.getMonth() + 1; //January is 0!
                if (bNgayHT < 10) bNgayHT = "0" + bNgayHT.toString();
                if (bThangHT < 10) bThangHT = "0" + bThangHT.toString();
                $('#txtNGAYSINH').val(bNgayHT + "/" + bThangHT + "/" + date.getFullYear());
                var songaybatbuoc = cfObj.HIS_BATBUOCGIOSINH_SONGAY;
                if (parseInt($('#txtTUOI').val()) <= songaybatbuoc && songaybatbuoc != 0) {
                    DlgUtil.showMsg('Bệnh nhân nhỏ hơn ' + songaybatbuoc + ' ngày bắt buộc nhập giờ sinh ');
                    $('#txtGIO_SINH').focus();
                }
            }
            if ($("#cboDVTUOI").val() == 4) {
                date = new Date(new Date().setTime(new Date().getTime() - ($(this).val() * 60 * 60 * 1000)));
                $('#txtNAMSINH').val(date.getFullYear());
                var bNgayHT = date.getDate();
                var bThangHT = date.getMonth() + 1; //January is 0!
                if (bNgayHT < 10) bNgayHT = "0" + bNgayHT.toString();
                if (bThangHT < 10) bThangHT = "0" + bThangHT.toString();
                $('#txtNGAYSINH').val(bNgayHT + "/" + bThangHT + "/" + date.getFullYear());
            }
            $('#cboDVTUOI').addClass('disabled');
            $('#txtTKGIOITINHID').focus();
        });
        $('#txtGIO_SINH').on('change', function (e) {
            if ($('#txtGIO_SINH').val().trim().length > 0) {
                if ($('#txtGIO_SINH').val().length < 5) {
                    DlgUtil.showMsg("Giờ sinh không đúng định dạng!");
                    $('#txtGIO_SINH').val("");
                    $('#txtGIO_SINH').focus();
                    return false;
                } else {
                    var _tgs = $('#txtGIO_SINH').val().split(':');
                    var _hh = _tgs[0];
                    var _mi = _tgs[1];
                    if (parseInt(_hh) > 24) {
                        DlgUtil.showMsg("Giờ sinh không được vượt quá 24 giờ");
                        $('#txtGIO_SINH').val("");
                        $('#txtGIO_SINH').focus();
                        return false;
                    }
                    if (parseInt(_mi) > 59) {
                        DlgUtil.showMsg("Phút sinh không được vượt quá 59 phút");
                        $('#txtGIO_SINH').val("");
                        $('#txtGIO_SINH').focus();
                        return false;
                    }
                    $('#cboGIOITINHID').focus();
                }
            }
        });

        $('#txtNAMSINH').on('change', function (e) {
            var b_namsinh = $('#txtNAMSINH').val();
            if (isNaN(b_namsinh)) {
                $('#txtNAMSINH').val("");
                $('#txtTUOI').val("");
                setErrValidate('txtNAMSINH');
                DlgUtil.showMsg('Năm sinh phải là số nguyên dương');
                return;
            }

            if (b_namsinh.length == 4) {
                var bNamHT = jsonrpc.AjaxJson.getSystemDate('YYYY');
                if (b_namsinh > bNamHT) {
                    setErrValidate('txtNAMSINH');
                    DlgUtil.showMsg('Năm sinh phải là số nguyên dương và Năm sinh không được lớn năm hiện tại');
                    $('#txtNAMSINH').val("");
                    return;
                } else {
                    // SONDN 06/01/2019 L2PT-13842
                    var _diffYear = bNamHT - b_namsinh;
                    if (_diffYear * 12 < _ngtTinhTuoi) {				// so thang < cau hinh
                        $('#txtTUOI').val(_diffYear * 12);
                        $('#cboDVTUOI').val("2");
                    } else {
                        $('#txtTUOI').val(bNamHT - b_namsinh);
                        $('#cboDVTUOI').val("1");
                    }
                    $("#cboDVTUOI").prop('disabled', true);
                }

            }

            if (cfObj.NGT_NGHENGHIEP_THEOTUOI == 1) {
                if (($("#cboDVTUOI").val() == 2 && Number($('#txtTUOI').val()) < 6 * 12
                    || $("#cboDVTUOI").val() == 1 && Number($('#txtTUOI').val()) < 6
                    || $("#cboDVTUOI").val() == 3 && Number($('#txtTUOI').val()) < 6 * 365)) {
                    $('#cboNGHENGHIEPID').val(3);
                    $('#cboNGHENGHIEPID').change();
                    $("#cboDVTUOI").prop('disabled', true);
                }
                if (($("#cboDVTUOI").val() == 1 && Number($('#txtTUOI').val()) >= 6) &&
                    ($("#cboDVTUOI").val() == 1 && Number($('#txtTUOI').val()) <= 16)) {
                    $('#cboNGHENGHIEPID').val(4);
                    $('#cboNGHENGHIEPID').change();
                    $("#cboDVTUOI").prop('disabled', true);
                }
            }
            _setUuTienDOB();
        });

        $('#txtTUOI').on('change', function (e) {
            // nam sinh khac voi nam cua ngay sinh thi clear
            var b_ngays = $('#txtNGAYSINH').val();
            if (b_ngays.length == 10) {
                if ($('#txtTUOI').val() != b_ngays.substr(6, 4)) {
                    $('#txtNGAYSINH').val("");
                }
            }
            tinhNgaySinh('txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');


        });

        /*$('#cboDVTUOI').on('change', function (e) {
			tinhNgaySinh('txtNGAYSINH', 'txtNAMSINH','txtTUOI','cboDVTUOI');
			if($('#cboDVTUOI').val() == "1"){
				$('#txtNGAYSINH').val("");
			}
		});*/

        $('#txtTKPHONGID').on('change', function (e) {
            _setPHONGKHAMID($('#cboDICHVUID').val(), $('#txtTKPHONGID').val(), 'cboPHONGKHAMID');
        });

        $("#txtTKDICHVUID").on('change', function (e) {
            var sl = $("#txtTKDICHVUID").val().trim().toLowerCase();
            var idx = -1;
            $("#cboDICHVUID > option").each(function () {
                if ((this.text.toLowerCase()).indexOf(sl) != -1) {
                    idx = this.value;
                }
            });
            if (idx != -1) {
                $("#cboDICHVUID").val(idx);
            } else {
                $("#cboDICHVUID").val("-1");
            }
            $("#cboDICHVUID").change();
        });

        $('#txtMA_BHYT').on('change', function (e) {
            if ($("#cboDOITUONGDB").val() != '0') {
                $("#cboDOITUONGDB").attr('style', 'background-color:aquamarine;');
            } else {
                $("#cboDOITUONGDB").attr('style', 'background-color:white;');
            }
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3), $("#cboBHYT_LoaiID").val());

            if (_bhytnghenghiep == "0") {							// don vi dong do khong set lai;
                setNghenghiep($('#txtMA_BHYT').val());
            }
            $("#txtBHYT_BD").focus();
        });

        $('#txtBHYT_BD').on('change', function (e) {
            var bstr = $('#txtBHYT_BD').val();
            $('#txtBHYT_BD').val(stringToDateFormat(bstr));
        });

        $('#txtBHYT_KT').on('change', function (e) {
            var bstr = $('#txtBHYT_KT').val();
            $('#txtBHYT_KT').val(stringToDateFormat(bstr));
        });

        $('#txtTKDTBNID').on('change', function (e) {
            fillValueDisplay('txtTKDTBNID', 'cboDTBNID');
        });

        $('#cboDTBNID').on('change', function (e) {
            fillValueDisplay('cboDTBNID', 'txtTKDTBNID');
            var makcbbdd = $("#txtMA_KCBBD").val().trim();
            var sql_par = [];
            _changeDOITUONG();

            if (_loadyeucaukhamtheodt == "0") {
                sql_par.push({"name": "[0]", "value": 0});
            } else {
                sql_par.push({"name": "[0]", "value": $("#cboDTBNID").val()});
            }

            if (_thaoTacHientai != "loadDetail") {												// neu k chay vao ham nay thi moi goi doan code nay (tranh lap doan set cong kham phong kham 2 lan)
                if (_doicongkhamphongkham == "0") {
                    if ($("#hidYCKHAMTHEODT").val() == "1") {
                        // lan dau khi load du lieu khong chay nhanh nay; tu lan thu 2 tro di moi chay; (tranh de vao su kien luong chinh)
                        if (_loadFirstDTBN == "1") {
                            _loadFirstDTBN = "0";
                            // load danh sach cong kham lan dau voi doi tuong khai bao
                            if (doituong_khaibao == "1") {
                                ComboUtil.getComboTag("cboDICHVUID", _sql_yckham, sql_par, "", {
                                    extval: true,
                                    value: '',
                                    text: 'Chọn yêu cầu khám'
                                }, 'sql', '', function () {
                                    $("#cboDICHVUID").val(_yeucaukhammacdinh);
                                    $("#cboDICHVUID").change();
                                });
                            } else {
                                if (cfObj.DKHNM_LOAD_DV_DT == "1") {
                                    ComboUtil.getComboTag("cboDICHVUID", _sql_yckham, sql_par, "", {
                                        extval: true,
                                        value: '',
                                        text: 'Chọn yêu cầu khám'
                                    }, 'sql', '', function () {
                                        $("#cboDICHVUID").val(_yeucaukhammacdinh);
                                        $("#cboDICHVUID").change();
                                    });
                                }
                            }
                        } else {
                            //ComboUtil.getComboTag("cboDICHVUID", "NGTDV.004", sql_par.join('$'), "", {extval: true, value:'-1',text:'Chọn yêu cầu khám'}, "sp", "", function(){
                            ComboUtil.getComboTag("cboDICHVUID", _sql_yckham, sql_par, "", {
                                extval: true,
                                value: '',
                                text: 'Chọn yêu cầu khám'
                            }, 'sql', '', function () {
                                $("#cboDICHVUID").val(_yeucaukhammacdinh);
                                $("#cboDICHVUID").change();
//								ComboUtil.findByExtra("cboDICHVUID",_yeucaukhammacdinh,0);
                            });
                        }

                    } else {
                        if (_loadFirstYCKham == 1) {													// chi lan dau tien vao nhanh nay;
                            ComboUtil.getComboTag("cboDICHVUID", _sql_yckham, sql_par, "", {
                                extval: true,
                                value: '',
                                text: 'Chọn yêu cầu khám'
                            }, 'sql', '', function () {
                                _loadFirstYCKham = 0;
//								ComboUtil.findByExtra("cboDICHVUID",_yeucaukhammacdinh,0);
                                $("#cboDICHVUID").val(_yeucaukhammacdinh);
                                $("#cboDICHVUID").change();
                            });
                        }
                    }
                }
            }

            // khi chuyen doi BHYT hoac BHYT dich vu k doi gia tri kcbbd;
            if ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6") {
                if (makcbbdd != "") {
                    $("#txtMA_KCBBD").val(makcbbdd);
                    $('#txtMA_KCBBD').combogrid("setValue", makcbbdd);
                }
            }

            // focus doi tuong BHYT hay Ho ten benh nhan;
            if (_loadFirst == 1) {
                if (_setfocusmacdinh == "0") {
                    $('#txtTENBENHNHAN').focus();
                } else if (_setfocusmacdinh == "1") {
                    if ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6") {
                        $('#txtMA_BHYT').focus();
                    } else {
                        $('#txtTENBENHNHAN').focus();
                    }
                } else if (_setfocusmacdinh == "2") {
                    if ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6") {
                        $('#cboDTBNID').focus();
                    } else {
                        $('#txtTENBENHNHAN').focus();
                    }
                }else {
                    $('#' + _setfocusmacdinh).focus();
                }
                if (_focus_mabn == 1) {
                    $("#txtMABENHNHAN").focus();
                }
                if (cfObj.NGT_MACDINHTHE != "0") {
                    if (cfObj.NGT_MACDINHTHE  == 2){
                        $("#txtSOCMTND").focus();
                    }
                }
                _loadFirst = 0;
            }

            // SONDN L2PT-5232
            $('#cboBHYT_LoaiID').change(); 			// cap nhat lai thong tin;

            // an / hien thong tin doi voi benh nhan vien phi;
            if ($("#hidANTHONGTINTUDONG").val() == "1" && $("#cboDTBNID").val() != "1" && $("#cboDTBNID").val() != "6") {
                $("#divANTHONGTINTUDONG1").hide();
                $("#divANTHONGTINTUDONG2").hide();
                $("#divANTHONGTINTUDONG3").show();					// div text ls kham, dieu tri;
                $("#divANTHONGTINTUDONG4").removeClass("col-xs-6 low-padding");
                $("#divANTHONGTINTUDONG4").addClass("col-xs-12 low-padding");
            } else {
                $("#divANTHONGTINTUDONG1").show();
                $("#divANTHONGTINTUDONG2").show();
                $("#divANTHONGTINTUDONG3").hide();
                $("#divANTHONGTINTUDONG4").removeClass("col-xs-12 low-padding");
                $("#divANTHONGTINTUDONG4").addClass("col-xs-6 low-padding");
            }
        });

        $('input[type="radio"]').change(function () {
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
        });

        // sondn L2PT-24929
        $("#cboLOAIKHAM").on('change', function (e) {
            _loadHopDong($(this).val(), 0);
        });
        $('#cboBHYT_LOAIID1').on('change', function (e) {
            var value = $(this).val();
            if (value == 1) {
                _setgiatribatbuoc(['lblCDTD', 'lblNoiChuyen'], '');
            } else {
                _setgiatribatbuoc(['lblCDTD', 'lblNoiChuyen'], '(*)');
            }
        });
        // THONG TIN CHUYEN TUYEN;
        $('#cboBHYT_LoaiID').on('change', function (e) {
            fillValueDisplay('cboBHYT_LoaiID', 'txtTKBHYT_LoaiID');
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
            var value = $(this).val();

            if (value != 2) {
                if (QD_4750 == 1) {
                    $("#dvBHYT_LOAIID").removeClass("col-xs-6 low-padding");
                    $("#dvBHYT_LOAIID").addClass("col-xs-8 low-padding");
                    $('#dvBHYT_LOAIID1').hide();
                }
                $("#btnChuyenTuyen").attr("disabled", "disabled");
                _setgiatribatbuoc(['lblCDTD', 'lblNoiChuyen'], '');
                if (cfObj.NGT_TN_BATBUOC_HTCLDC == 1){
                    _setgiatribatbuoc(['lblHinhThucChuyen','lblLyDoChuyen'], '');
                }
                _disableControl(['txtTKMACHANDOANTUYENDUOI', 'txtCHANDOANTUYENDUOI', 'txtTKMANOIGIOITHIEU', 'txtTGIANTD_TN', 'txtTGIANTD_DN',
                    'cboMANOIGIOITHIEU', 'cboCV_CHUYENVIEN_HINHTHUCID', 'cboCV_CHUYENVIEN_LYDOID'], _anthongtinchuyentuyen == "0" ? true : false);
                $('#txtTKMACHANDOANTUYENDUOI').val('');
                $('#txtCHANDOANTUYENDUOI').val('');
                $('#txtTKMANOIGIOITHIEU').val('');
                $('#txtTGIANTD_TN').val('');
                $('#txtTGIANTD_DN').val('');
                $('#cboMANOIGIOITHIEU').find('option').remove();
                // SONDN L2PT-26614

                /*
				// sondn L2PT-5332
				if (_dtrituyenduoingay == "1"){
					$("#dvDTRITUYENDUOINGAY").hide();
					$("#txtDTRI_TD_TUNGAY").val("");
					$("#txtDTRI_TD_DENNGAY").val("");
				}
				// end sondn L2PT-5332
				*/
            } else {
                if (QD_4750 == 1) {
                    $("#dvBHYT_LOAIID").removeClass("col-xs-8 low-padding");
                    $("#dvBHYT_LOAIID").addClass("col-xs-6 low-padding");
                    $('#dvBHYT_LOAIID1').show();
                }
                $("#btnChuyenTuyen").removeAttr("disabled");
                _setgiatribatbuoc(['lblCDTD', 'lblNoiChuyen'], '(*)');
                if (cfObj.NGT_TN_BATBUOC_HTCLDC == 1){
                    _setgiatribatbuoc(['lblHinhThucChuyen','lblLyDoChuyen'], '(*)');
                }
                _disableControl(['txtTKMACHANDOANTUYENDUOI', 'txtCHANDOANTUYENDUOI', 'txtTKMANOIGIOITHIEU', 'txtTGIANTD_TN', 'txtTGIANTD_DN',
                    'cboMANOIGIOITHIEU', 'cboCV_CHUYENVIEN_HINHTHUCID', 'cboCV_CHUYENVIEN_LYDOID'], false);
                /*
				// sondn L2PT-5332
				if (_dtrituyenduoingay == "1"){
					$("#dvDTRITUYENDUOINGAY").show();
				}
				// end sondn L2PT-5332
				*/
                // sondn L2PT-27643
                if (_macdinhdtgt != "0") {
                    var _arr = _macdinhdtgt.split('@');
                    var _hinhthucidcb = $("#cboCV_CHUYENVIEN_HINHTHUCID").val();
                    if (_hinhthucidcb == null || _hinhthucidcb == 'null' || _hinhthucidcb == "") {
                        _hinhthucidcb = _arr[0];
                    }
                    var _lydoidcb = $("#cboCV_CHUYENVIEN_LYDOID").val();
                    if (_lydoidcb == null || _lydoidcb == 'null' || _lydoidcb == "") {
                        _lydoidcb = _arr[1];
                    }
                    $("#cboCV_CHUYENVIEN_HINHTHUCID").val(_hinhthucidcb);
                    $("#cboCV_CHUYENVIEN_LYDOID").val(_lydoidcb);
                }
            }
            if (value != 1) {
                $('#chkKCB_HIVAIDS').prop('checked', false);
            }

            // sondn L2PT-5332
            if (_dtrituyenduoingay == "1") {
                $("#dvDTRITUYENDUOINGAY").show();
            }
            // end sondn L2PT-5332

            //L2PT-45101
            if (NGT01T001_TIEPNHAN_NGT_CHON_TUYEN == "1") {
                if (value == '3' && $.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value != '2')
                    $('input[type=radio][name=radHINHTHUCVAOVIENID][value=2]').prop('checked', true);
                if (value == '2') {
                    $("#txtTKMANOIGIOITHIEU").val($("#cboMAKCBBD").val());
                    var option = '<option value="' + $("#cboMAKCBBD").val() + '">' + $("#cboMAKCBBD").text() + '</option>';
                    $("#cboMANOIGIOITHIEU").empty();
                    $("#cboMANOIGIOITHIEU").append(option);
                }
            }
        });

        $('#txtTKBHYT_LoaiID').on('change', function (e) {
            fillValueDisplay('txtTKBHYT_LoaiID', 'cboBHYT_LoaiID');
        });

        // dich vu, yeu cau kham
        $('#cboDICHVUID').on('change', function (e) {
            if (_doicongkhamphongkham == "0") {
                var _phongkhammd = $("#hidSETPHONGKHAM").val();
                if (_loadFirstPhongKham != 0 && _phongkhammd != "" && _phongkhammd != "0") {
                    _loadphongkham($(this).val(), _phongkhammd);				// load va set mac dinh;
                    _loadFirstPhongKham--;
                } else {
                    _loadphongkham($(this).val(), "0");				// load tu do;
                }

                if (($('#hidBENHNHANID').val() == '' || $('#hidBENHNHANID').val() == null) && _mopopupphongkham == '1') {
                    var param = {
                        yeucaukhamid: $(this).val(),
                        tenyeucaukham: $("#cboDICHVUID option:selected").text()
                    };
                    _showDialog("ngoaitru/NGT02K045_DanhSachPhongKhamYCK", param, 'DANH SÁCH PHÒNG KHÁM', 1100, 600);
                }
                EventUtil.setEvent("assignsetphongkham", function (e) {
                    $("#cboPHONGKHAMID").val(e.option);
                    DlgUtil.close("dlgCV");
                });

                //_checksoluongmaxphongkham($('#cboPHONGKHAMID').val());
            }
        });

        $('#btnSetPK').on('click', function (e) {
            var param = {
                yeucaukhamid: $('#cboDICHVUID').val(),
                tenyeucaukham: $("#cboDICHVUID option:selected").text()
            };
            _showDialog('ngoaitru/' + cfObj.NGT_DSPHONGKHAM, param, 'DANH SÁCH PHÒNG KHÁM', 1100, 600);
        });

        $('#cboPHONGKHAMID').on('change', function (e) {
            if ($('#hidTIEPNHANID').val() == '' || $('#hidTIEPNHANID').val() == null) {
                if ($('#cboPHONGKHAMID').val() != null && $('#cboPHONGKHAMID').val() != '') {
                    if (_checksoluongmaxphongkham($(this).val()) == '-1') {
                        if (_popupconthuoc == "0") {
                            DlgUtil.showMsg("Phòng khám hết số");
                        } else {
                            $.bootstrapGrowl('Phòng khám hết số', {
                                type: 'danger',
                                delay: 3000,
                            });
                        }
                    }
                }
            }
            if (($("#hidTINHTHUKHAC").val() == "1" || cfObj.NGT_LOADTHUKHAC_THEOPHONG == '1') && ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6" || $("#cboDTBNID").val() == "2")) {
                // set mac dinh thu khac neu co;
                var _thukhacid = $('#cboPHONGKHAMID' + " option:selected").attr('extval1');
                $("#cboDVTHUKHAC").val(Number(_thukhacid) > 0 ? _thukhacid : 0);
                $("#select2-cboDVTHUKHAC-container").text($("#cboDVTHUKHAC option:selected").text());
            }

            if (_doicongkhamphongkham == "1") {
                var _ckmd = $('#cboPHONGKHAMID' + " option:selected").attr('extval3');
                if (_loadFirstYCKham == "1" && _yeucaukhammacdinh != "" && _yeucaukhammacdinh != "0") {
                    _ckmd = _yeucaukhammacdinh;
                    _loadFirstYCKham = "0";
                }
                if (_ckmd == "" || _ckmd == "0") {
                    _ckmd = null;
                }
                if (_ckmd == undefined) {
                    var sql_par = [];
                    sql_par.push({"name": "[0]", "value": $('#cboPHONGKHAMID').val()});
                    var CKMDS = jsonrpc.AjaxJson.ajaxExecuteQueryO("GET_CKMD", sql_par);
                    var CKMD = $.parseJSON(CKMDS);
                    if (CKMD[0].CONGKHAM_MACDINH == "0") {
                        _ckmd = null;
                    } else {
                        _ckmd = CKMD[0].CONGKHAM_MACDINH;
                    }
                }
                _loadYCKham($("#cboPHONGKHAMID").val(), _ckmd);
            }

//			if(_sudungsocapcuu == "1" && _loadFirstPhongKham == "0"){
//				var _maphonghientai = $('#cboPHONGKHAMID'+" option:selected").attr('extval2');
//				var _check = $.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value;
//				if (_dsphongcapcuu.indexOf(_maphonghientai + ";") != -1 && _check == "3"){
//					DlgUtil.showMsg("Hình thức vào viện khám bệnh không phù hợp với phòng khám này. ");
//				}
//				if (_dsphongcapcuu.indexOf(_maphonghientai + ";") == -1 && _check == "2"){
//					DlgUtil.showMsg("Hình thức vào viện cấp cứu không phù hợp với phòng khám này. ");
//				}
//			}
        });

        $('#chkCHECKCONG').on('change', function (e) {
            if ($('#chkCHECKCONG').is(":checked") && $("#hidSHOWCHECKCONGBHXH").val() == "1") {
                openPopUpCheckLSKCB(0.95, 0.93);
            }
        });
        $('#chkVODANH').on('change', function (e) {
            if ($('#chkVODANH').is(":checked")) {
                var _sql_pr = [];
                var stt_vodanh;
                stt_vodanh = jsonrpc.AjaxJson.getOneValue("GET_STTVODANH", _sql_pr)
                $('#txtTENBENHNHAN').val('VÔ DANH ' + stt_vodanh);
                var sql_par = [];
                var tenviettat = cfObj.NGT_TENVIETTATDP_BNVODANH;
                sql_par.push({
                    "name": "[0]",
                    "value": tenviettat
                });
                var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("GETDP_BNVODANH", sql_par);
                if (data_ar != null && data_ar.length > 0) {
                    $('#txtTKDIAPHUONGID').val(tenviettat);
                    var rows = JSON.parse(data_ar);
                    $('#cboDIAPHUONGID').find('option').remove();
                    var newOption = $('<option value="' + rows[0].DIAPHUONGID + '" selected>' + rows[0].TENDIAPHUONGDAYDU + '</option>');
                    $("#cboDIAPHUONGID").append(newOption);
                    getDiaChi(rows[0].DIAPHUONGID, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', docmathe);
                }

            } else {
                _refreshForm();
            }
        });
        $('#chkCOGIAYHEN').on('change', function (e) {
            if ($('#chkCOGIAYHEN').is(":checked")) {
                $("#txtSOGIAYHENKHAM").prop("disabled", false);
            } else {
                $("#txtSOGIAYHENKHAM").prop("disabled", true);
            }
        });

        $("#btnCHECKLICHSUKCB").on('click', function () {
            openPopUpCheckLSKCB(0.95, 0.93);
        });

        // sinh so the bao hiem no cho benh nhan
        function _sinhSoNoThe() {
            var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
            if (matinh == '') {
                DlgUtil.showMsg('Chưa chọn tỉnh');
                $("#chkNOTHE").prop("checked", false);
                return;
            }
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.SINHTHE.NO", matinh);
            return ret;
        }

        // sondn L2PT-32838
        $("#txtSDTBENHNHAN").focusout(function () {
            if (_batbuocsdtbn != "0") {
                if (_batbuocsdtbn.split('@')[0] == "2") {
                    if ($("#txtSDTBENHNHAN").val().trim().length == "0" || isNaN($("#txtSDTBENHNHAN").val().trim())) {
                        if (_hd === '1' && _nhapthongtin_bnhd.indexOf("txtSDTBENHNHAN@") != -1) {
                            // bo qua k check;
                        } else {
                            DlgUtil.showMsg("Chưa nhập số điện thoại bệnh nhân/ SĐT phải có dạng số. ");
                            return;
                        }
                    }
                }
            }
        });
        // end sondn L2PT-32838

        // sondn L2PT-33770
        $("#txtDIENTHOAINGUOITHAN").focusout(function () {
            if (_batbuocsdtbn != "0") {
                if (_batbuocsdtbn.split('@')[2] == "2") {
                    if ($("#txtDIENTHOAINGUOITHAN").val().trim().length == "0" || isNaN($("#txtDIENTHOAINGUOITHAN").val().trim())) {
                        if (_hd === '1' && _nhapthongtin_bnhd.indexOf("txtDIENTHOAINGUOITHAN@") != -1) {
                            // bo qua k check;
                        } else {
                            DlgUtil.showMsg("Chưa nhập số điện thoại người thân/ SĐT phải có dạng số. ");
                            return;
                        }
                    }
                }
            }
        });
        // sondn L2PT-33770

        $('#chkNOTHE').on('change', function (e) {
            if ($('#chkNOTHE').is(":checked")) {
                var config_ndkkcbbd = "";

                if (_dkkcbbd_nothe.length > 0) {
                    config_ndkkcbbd = _dkkcbbd_nothe;
                } else if (config_ndkkcbbd <= 0) {
                    config_ndkkcbbd = '35148';
                }
                $('#txtBHYT_BD').val('');
                $('#txtBHYT_KT').val('');
                $('#txtMA_BHYT').val('');
                $('#txtDIACHI_BHYT').val('');
                $('#hidSINHTHEBHYT').val('0');
                $('#txtMA_KCBBD').val('');

                $("#chkCHECKCONG").prop("checked", false);
                $("#chkNOTHE").prop("checked", true);
                $("#chkNOTHE").attr("disabled", false);
                $('#txtMA_BHYT').val(_sinhSoNoThe());
                $("#txtMA_KCBBD").val(config_ndkkcbbd);
                $('#txtMA_KCBBD').combogrid("setValue", config_ndkkcbbd);
                $('#cboBHYT_LoaiID').val(1);
                $('#cboBHYT_LoaiID').change();
                $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
                // sondn L2PT-32242
                $("#txtBHYT_BD").val(_ngayhientai);
                $("#txtBHYT_KT").val(_ngayhientai.substring(0, 6) + (Number(_ngayhientai.substring(6)) + 1));
                // end sondn L2PT-32242
            } else {
                $('#txtMA_BHYT').prop("disabled", false);
                $('#txtMA_BHYT').val("");
            }
        });

        $('#cboTHETAM').on('change', function (e) {
            if ($('#chkCOGIAYKS').is(":checked")) {
                if ($('#cboTHETAM').val() == 1) {
                    if (!_kiemTraSinhSoTheBHYT()) {
                        $("#chkCOGIAYKS").prop("checked", false);
                        $("#cboTHETAM").val(0);
                        return false;
                    }
                    var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
                    $('#txtMA_BHYT').val('TE1' + matinh);
                    $('#cboBHYT_LoaiID').change();
                    var kcbbd = $('#cboHC_TINHID' + " option:selected").attr('extval1') + "000"; 			// ma tinh + 000;
                    if (_tuyenTE == "1") {
                        kcbbd = _options.hospital_code;
                    }
                    $('#txtMA_KCBBD').val(kcbbd);
                    $('#txtMA_KCBBD').combogrid("setValue", kcbbd);
                    $('#hidSINHTHEBHYT').val('1');
                    $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
                } else {
                    var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
                    $('#txtMA_BHYT').val('HG4' + matinh);
                }
            } else {
                $("#cboTHETAM").val(0);
            }
        });
        $('#chkCOGIAYKS').on('change', function (e) {
            if ($('#chkCOGIAYKS').is(":checked")) {
                if (_sinhthete != "3") {									// =3: check sinh the, ma the tu nhap va co check cong binh thuong;
                    if (QD_4750 == 1) {
                        $("#cboTHETAM").val(1);
                        $("#cboTHETAM").change();
                    } else {
                        if (!_kiemTraSinhSoTheBHYT()) {
                            $("#chkCOGIAYKS").prop("checked", false);
                            $("#cboTHETAM").val(0);
                            return false;
                        }
                        if (_thoiGianTheTE == "0") {
                            _thoiGianTheTE = "Y6";
                        }
                        var ngaysinh = $('#txtNGAYSINH').val().split('/');
                        var dtTu = new Date(ngaysinh[1] + "/" + ngaysinh[0] + "/" + ngaysinh[2]);
                        var dtDen = null;
                        var _t1 = _thoiGianTheTE.substring(0, 1); 								// Y
                        var _t2 = Number(_thoiGianTheTE.substring(1, _thoiGianTheTE.length)); 	// 6

                        if (_t1 == "M") {
                            dtDen = new Date(dtTu.setMonth(dtTu.getMonth() + _t2));
                        } else if (_t1 == "D") {
                            dtDen = new Date(dtTu.setDate(dtTu.getDate() + _t2));
                        } else {
                            dtDen = new Date(dtTu.setFullYear(dtTu.getFullYear() + _t2));
                        }
                        // tru di 1 ngay;
                        dtDen = new Date(dtDen.setDate(dtDen.getDate() - 1));

                        var vday = dtDen.getDate() + "";
                        if (vday.length == 1) {
                            vday = '0' + vday;
                        }
                        var vmonth = (dtDen.getMonth() + 1) + "";
                        if (vmonth.length == 1) {
                            vmonth = '0' + vmonth;
                        }
                        var vyear = dtDen.getFullYear();

                        $("#txtBHYT_BD").val($('#txtNGAYSINH').val());
                        $('#txtBHYT_KT').val(vday + "/" + vmonth + "/" + vyear);

                        // neu set =1: lay theo ngay hien tai;
                        if (_thoiGianTheTE == "1") {
                            $("#txtBHYT_BD").val(_ngayhientai);
                            $("#txtBHYT_KT").val(_ngayhientai);
                        }

                        $('#txtMA_BHYT').val(_sinhSoTheBHYT());
                        //$("#txtMA_BHYT").attr("disabled", true);
                        $('#cboBHYT_LoaiID').change();

                        // SONDN L2PT-18413 08/04/2020
                        var kcbbd = $('#cboHC_TINHID' + " option:selected").attr('extval1') + "000"; 			// ma tinh + 000;
                        if (_tuyenTE == "1") {
                            kcbbd = _options.hospital_code;
                        }
                        // END SONDN L2PT-18413 08/04/2020

                        $('#txtMA_KCBBD').val(kcbbd);
                        $('#txtMA_KCBBD').combogrid("setValue", kcbbd);
                        $('#hidSINHTHEBHYT').val('1');
                        $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
                    }
                } else {
                    $('#hidSINHTHEBHYT').val('1');
                }
            } else {
                $("#cboTHETAM").val(0);
                $('#txtBHYT_BD').val('');
                $('#txtBHYT_KT').val('');
                $('#txtMA_BHYT').val('');
                $('#txtDIACHI_BHYT').val('');
                $('#hidSINHTHEBHYT').val('0');
                $('#txtMA_KCBBD').val('');
            }
        });

        $('#chkDU5NAM6THANGLUONGCOBAN').on('change', function (e) {
            if ($('#chkDU5NAM6THANGLUONGCOBAN').is(":checked")) {
                $('#chkTRADU6THANGLCB').prop("disabled", false);
            } else {
                $('#chkTRADU6THANGLCB').prop("disabled", true);
                $('#chkTRADU6THANGLCB').prop('checked', false);
            }
        });

        $('#btnChuyenTuyen').on('click', function (e) {
            var param = {
                chandoantuyenduoi: $('#txtCHANDOANTUYENDUOI').val(),
                tkmanoigioithieu: $('#txtTKMANOIGIOITHIEU').val(),
                cv_chuyenvien_hinhthucid: $('#cboCV_CHUYENVIEN_HINHTHUCID').val(),
                cv_chuyenvien_lydoid: $('#cboCV_CHUYENVIEN_LYDOID').val(),
                cv_chuyendungtuyen: $('#hidCV_CHUYENDUNGTUYEN').val()
            };

            _showDialog("ngoaitru/NGT01T001_chuyenvien", param, 'THÔNG TIN CHUYỂN TUYẾN', 820, 280);

            EventUtil.setEvent("assignSevice_saveCv", function (e) {
                objChuyenVien = e.msg;

                $('#txtCHANDOANTUYENDUOI').val(objChuyenVien.CHANDOANTUYENDUOI);
                $('#txtTKMANOIGIOITHIEU').val(objChuyenVien.TKMANOIGIOITHIEU);
                if (objChuyenVien.TKMANOIGIOITHIEU != null)
                    $('#txtTKMANOIGIOITHIEU').combogrid("setValue", objChuyenVien.TKMANOIGIOITHIEU);

                $('#cboCV_CHUYENVIEN_HINHTHUCID').val(objChuyenVien.CV_CHUYENVIEN_HINHTHUCID);
                $('#cboCV_CHUYENVIEN_LYDOID').val(objChuyenVien.CV_CHUYENVIEN_LYDOID);
                $('#hidCV_CHUYENDUNGTUYEN').val(objChuyenVien.CV_CHUYENDUNGTUYEN);

                DlgUtil.close("dlgCV");
            });
        });

        $('#btnSinhTon').on('click', function (e) {
            if (_batbuocSinhTon.indexOf("100,") != "-1") {
                _openSinhTon(100);
            } else {
                _openSinhTon(0);
            }
        });

        function _openSinhTon(batbuocsinhton) {
            var param = {
                //tuyennx_add_start_20180222 L2DKHN-744
                tuoi: $('#txtTUOI').val(),
                dvtuoi: $('#cboDVTUOI').val(),
                //Tuyennx_add_end
                khambenhid: $('#hidKHAMBENHID').val(),
                trangthaikhambenh: $('#hidTRANGTHAIKHAMBENH').val(),
                phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                batbuocsinhton: batbuocsinhton //,
                //chieucao : chieucaocu,
                //diungthuoc : diungthuoccu
            };

            EventUtil.setEvent("assignSevice_savetv", function (e) {
                objSinhTon = e.msg;
            });

            _showDialog("ngoaitru/NGT01T001_sinhton", param, 'THÔNG TIN DẤU HIỆU SINH TỒN', 800, 600);
        }

        // $('#btnSinhTon').on('click', function (e) {
        // 	var param = {
        // 		//tuyennx_add_start_20180222 L2DKHN-744
        // 		tuoi : $('#txtTUOI').val(),
        // 		dvtuoi : $('#cboDVTUOI').val(),
        // 		//Tuyennx_add_end
        // 		khambenhid : $('#hidKHAMBENHID').val(),
        // 		trangthaikhambenh : $('#hidTRANGTHAIKHAMBENH').val(),
        // 		phongkhamdangkyid : $('#hidPHONGKHAMDANGKYID').val()
        // 	};

        // 	EventUtil.setEvent("assignSevice_savetv",function(e){
        // 		objSinhTon = e.msg;
        // 	});

        // 	_showDialog("ngoaitru/NGT01T001_sinhton", param, 'THÔNG TIN DẤU HIỆU SINH TỒN',800,600);
        // });

        // sondn L2PT-4440
        $("#btnGOIKHAMVPC").on('click', function (e) {
            if ($("#txtMABENHNHAN").val() == "" || $("#txtTENBENHNHAN").val() == ""
                || $("#hidBENHNHANID").val() == "" || $("#hidHOSOBENHANID").val() == "") {
                DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để thêm gói khám");
                return;
            }

            var myVar = {
                mabenhnhan: $("#txtMABENHNHAN").val(),
                tenbenhnhan: $("#txtTENBENHNHAN").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val()

            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgGOIKHAMLVVPC", "divDlg", "manager.jsp?func=../noitru/NTU02D192_GoikhamBenhnhan", myVar, "Chỉ định gói KCB", 1000, 400);
            DlgUtil.open("dlgGOIKHAMLVVPC");
        });
        // end sondn L2PT-4440

        // manhnv S
        $('#toolbarIdhandling_1').on('click', function (e) {
            _showPopupPhongKham(0, "Chuyển phòng khám", 700, 300);
        });

        $('#toolbarIdhandling_2').on('click', function (e) {
            _showPopupPhongKham(2, "Thêm phòng khám", 700, 300);
        });

        //click button hoa hồng.
        $("#toolbarIdbtnHoaHong").on("click", function (e) {
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                mabenhan: $("#hidMABENHAN").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgHoaHong", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T006_hoahong", myVar, "HOA HỒNG", 600, 230, style = "overflow: hidden;");
            DlgUtil.open("dlgHoaHong");
        });

        $('#lblHopDong').on('click', function (e) {
            if (_hd === '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgHD", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K037_HDBN", {}, "THÔNG TIN HỢP ĐỒNG", 950, 460, {closeButton: false});
                DlgUtil.open("dlgHD");
            }
        });

        $('#toolbarIdtreat_1').on('click', function (e) {
            var _phongcd = $("#cboPHONGKHAMID").val();
            if (cfObj.NGT_PHONG_CD_TIEPNHAN == '1') {
                var _sql_par = [];
                _sql_par.push({"name": "[0]", value: $("#cboPHONGKHAMID").val()});
                _sql_par.push({"name": "[1]", value: $("#cboDICHVUID").val()});
                _phongcd = jsonrpc.AjaxJson.getOneValue("TN_GETPHONGCD", _sql_par);
            }

            var myVar = {
                chidinhdichvu: "1",
                loaidichvu: "2",
                loaiphieumbp: "3",
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _phongcd,
                modeFunction: '0'
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 2, myVar, "Tạo phiếu chỉ định dịch vụ", 1300, 600);
            DlgUtil.open("divDlgDichVu");
        });

        $('#toolbarIdtreat_2').on('click', function (e) {
            var _dtbnid = null;
            if ($("#txtDOITUONGCHIDINHCLS").val() != "" && $("#txtDOITUONGCHIDINHCLS").val() != "0") {
                _dtbnid = $("#txtDOITUONGCHIDINHCLS").val();
            } else {
                _dtbnid = $("#hidDOITUONGBENHNHANID").val();
            }

            var _phongcd = $("#cboPHONGKHAMID").val();
            if (cfObj.NGT_PHONG_CD_TIEPNHAN == '1') {
                var _sql_par = [];
                _sql_par.push({"name": "[0]", value: $("#cboPHONGKHAMID").val()});
                _sql_par.push({"name": "[1]", value: $("#cboDICHVUID").val()});
                _phongcd = jsonrpc.AjaxJson.getOneValue("TN_GETPHONGCD", _sql_par);
            }

            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanid: _dtbnid,
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _phongcd,
                modeFunction: '0'
            };

            //ngocnva L2PT-7963 start
            if (_batBuocCLSTiepNhan == 1) {
                delete myVar.modeFunction;
            }
            //ngocnva L2PT-7963 end

            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, myVar, "Tạo phiếu chỉ định dịch vụ", 1300, 600);
            DlgUtil.open("divDlgDichVu");
        });

        $('#toolbarIdbtnThuTienKhac').on('click', function (e) {
            if ($("#hidTRANGTHAI_STT").val() == '-1') {
                DlgUtil.showMsg("Bệnh nhân chưa xác thực tới khám không thể tạo phiếu thu khác!");
                return;
            }
            paramInput = {
                chidinhdichvu: '1',
                loaidichvu: '1',
                loaiphieumbp: '17',
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: $("#cboPHONGKHAMID").val()
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 1, paramInput, "Phiếu thu khác", 1300, 600);
            DlgUtil.open("divDlgDichVu");
        });

        $('#toolbarIdbtnLSKCB').on('click', function (e) {
            var paramInput = {
                MATHE: $('#txtMA_BHYT').val(),
                GIOITINH: $('#cboGIOITINHID').val(),
                NGAYSINH: $('#txtNGAYSINH').val() == "" ? $("#txtNAMSINH").val().trim() : $('#txtNGAYSINH').val(),
                LAYTUNGAY: '01/01/2018',
                LAYDENNGAY: '31/12/2018',
                MACSYT: i_u1,
                BYTURL: $("#txtBYTURL").val()
            };

            dlgPopup = DlgUtil.buildPopupUrl(
                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K049_TraCuuCongBYT",
                paramInput, "Thông tin lịch sử điều trị bệnh nhân", window.innerWidth * 0.95, window.innerHeight * 0.93);

            var parent = DlgUtil.open("divDlgDDT");
        });

        $('#toolbarIdbtnLSKCBHISL3').on('click', function (e) {
            var paramInput = {
                MATHE: $('#txtMA_BHYT').val(),
                HOTEN: $("#txtTENBENHNHAN").val(),
                NGAYSINH: $("#txtNGAYSINH").val() == "" ? $("#txtNAMSINH").val() : $("#txtNGAYSINH").val()
            };

            dlgPopup = DlgUtil.buildPopupUrl(
                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K049_TraCuuLSKCBHISL3",
                paramInput, "Thông tin lịch sử bệnh nhân HISL3", window.innerWidth * 0.95, window.innerHeight * 0.93);

            var parent = DlgUtil.open("divDlgDDT");
        });

        $('#toolbarIdbtnLSKCBGW').on('click', function (e) {
            var paramInput = {
                MABHYT: $('#txtMA_BHYT').val(),
                NGAYKHAM: $('#txtNGAYTIEPNHAN').val(),
                MAKCBBD: _options.hospital_code															// MA DON VI DUNG CHECK ;
            };

            dlgPopup = DlgUtil.buildPopupUrl(
                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB_GW",
                paramInput, "Thông tin lịch sử KCB Trong Tỉnh", window.innerWidth * 0.95, window.innerHeight * 0.93);

            var parent = DlgUtil.open("divDlgDDT");
        });


        //HungNd - L2PT-64988
        $('#toolbarIdbtnHSSK').on('click', function (e) {
            var paramInput = {
                MABHYT: $('#txtMA_BHYT').val(),
                NGAYKHAM: $('#txtNGAYTIEPNHAN').val(),
                MAKCBBD: _options.hospital_code															// MA DON VI DUNG CHECK ;
            };

            dlgPopup = DlgUtil.buildPopupUrl(
                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K112_HSSK_PORTAL",
                paramInput, "Hồ sơ sức khỏe cá nhân", window.innerWidth * 0.95, window.innerHeight * 0.93);

            var parent = DlgUtil.open("divDlgDDT");
        });
        //End HungNd - L2PT-64988

        $('#toolbarIdbtnLSKHAMCONGBHYT').on('click', function (e) {
            openPopUpCheckLSKCB(0.95, 0.93);
        });

        //calback cho man hinh chi dinh dich vu
        EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function (e) {
            DlgUtil.showMsg(e.msg);

            _loadPhieukham();
            DlgUtil.close("divDlgDichVu");
        });

        EventUtil.setEvent("assignSevice_cancelTK", function (e) {
            if (e.msg == '1') {
                // thuc hien check cong, kiem tra cac thong tin can thiet truoc khi luu
                _capnhat(1); 				// cap nhat bo qua ttbn cu, vi lay thong tin hien tai;
//				_capnhat3();
            }
            DlgUtil.close("dlgCV");
        });

        EventUtil.setEvent("assignSevice_cancelHD", function (e) {
            var obj = e.msg;
            if (obj.hopdongid == '-1') {
                //$("#divButton button").addClass('disabled');
                //$('#lblHopDong').text("Hợp Đồng: Chưa chọn hợp đồng.");
                DlgUtil.close("dlgHD");
                return false;
            }
            $('#cboHD').val(obj.hopdongid);
            $('#hidHOPDONGID').val(obj.hopdongid);
            //$('#lblHopDong').text("Hợp Đồng: " + obj.tenhd);

            if (obj.mabenhnhan.trim() != '') {
                _timKiemBenhNhan(obj.mabenhnhan, '1');
            }

            $('#cboDTBNID').val(obj.dtbnid);

            if (obj.dtbnid < 1) {
                $('#cboDTBNID').val(2);
            } else {
                $('#cboDTBNID').val(obj.dtbnid);
                _disableControl(['cboDTBNID'], true);
            }
            $('#cboDTBNID').change();

            $("#txtNGAYTIEPNHAN").val(_ngayhientaict);

            DlgUtil.close("dlgHD");
        });

        //calback cho man hinh chup anh
        EventUtil.setEvent("assignSevice_saveChupAnh", function (e) {
            if (e.msg != null) {
                var imgTag = document.getElementById('imgBN');
                imgTag.src = e.msg;
            } else {
                $('#imgBN').removeAttr('src');
            }
            DlgUtil.close("dlgChupAnh");
        });
        EventUtil.setEvent("assignSevice_loadTTHCBenhNhan", function (e) {
            if (typeof (e) != 'undefined') {
                DlgUtil.close("dlgCV");
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT03K011.LOADTTHC", e.mabenhnhan);
                if (data_ar != null && data_ar.length > 0) {
                    loadDetail1(data_ar);
                }
            }
        });
        EventUtil.setEvent("assignSevice_loadBenhNhan", function (e) {
            if (typeof (e) != 'undefined') {
                DlgUtil.close("dlgCV");
                $('#hidLICHHENID').val(e.lichhenid);

                var _dtbnidmoi = $("#cboDTBNID").val();
                var _mabhytmoi = $("#txtMA_BHYT").val();
                var _tungaymoi = $("#txtBHYT_BD").val();
                var _denngaymoi = $("#txtBHYT_KT").val();
                var _dtsinhsongmoi = $("#cboDT_SINHSONG").val();
                var _kcbbdmoi = $("#txtMA_KCBBD").val();
                var _diachibhytmoi = $("#txtDIACHI_BHYT").val();

                _timKiemBenhNhan(e.mabenhnhan, '1');

                var _mabhytcu = $("#txtMA_BHYT").val();
                var _dtbnidcu = $("#cboDTBNID").val();
                _mabhytgoc = _mabhytmoi;
                _b_flag = true;

                // giu nguyen doi tuong benh nhan bac sy da chon truoc do;
                if (_dtbnidmoi == "1" || _dtbnidmoi == "6") {
                    if (_dtbnidcu != "1" && _dtbnidcu != "6") {		// moi BH, cu VP: reload lai truoc;
                        $("#cboDTBNID").val(_dtbnidmoi);
                        $("#cboDTBNID").change();
                    }

                    if (((_dtbnidcu == "1" || _dtbnidcu == "6")
                        && _mabhytmoi != _mabhytcu && _mabhytmoi != "")			// moi BH, cu BH, moi # cu
                        || (_dtbnidcu != "1" && _dtbnidcu != "6")					// moi BH, cu VP
                    ) {
                        DlgUtil.showMsg("Thông tin mã BHYT đã được cập nhật theo mã mới.", function () {
                            $("#txtMA_BHYT").val(_mabhytmoi);
                            $("#txtBHYT_BD").val(_tungaymoi);
                            $("#txtBHYT_KT").val(_denngaymoi);
                            $("#cboDT_SINHSONG").val(_dtsinhsongmoi);
                            $("#txtMA_KCBBD").val(_kcbbdmoi);
                            $('#txtMA_KCBBD').combogrid("setValue", _kcbbdmoi);
                            $("#txtDIACHI_BHYT").val(_diachibhytmoi);
                            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3), $("#cboBHYT_LoaiID").val());
                            $('#btnLuu').attr("disabled", false);
                        });
                    }
                } else {
                    if (_dtbnidcu == "1" || _dtbnidcu == "6") {			// moi = VP, cu = BH: change doi tuong thanh vpi
                        $("#cboDTBNID").val(_dtbnidmoi);
                        $("#cboDTBNID").change();
                    }
                }
            }
        });

        $("#toolbarIdbtnThongBao").on("click", function (e) {
            /*var par = ['HIS_HIENTHI_GOIKHAM'];
			var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			var par2 = ['HIS_HIENTHI_GOIKHAM2'];
			var dt2 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par2.join('$'));

			if(dt == "2"){					// BVNT
				var param = "";
				window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCD&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			} else if (dt == "4"){			// BDHCM
				var param = "";
				window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCDBD&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			} else if (dt == "5"){			// BM2
				var param = "";
				if (dt2 == "0"){
					window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCDBM2&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}else{
					window.open('manager.jsp?func=../ngoaitru/NGT02K053_PK_LCD_923&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}
			} else {
				window.open('manager.jsp?func=../ngoaitru/NGT02K027_ThongbaoDangKyKham&showMode=dlg','','width=880,height=660');
			}*/
            window.open('manager.jsp?func=..' + cfObj.NGT_LCD_TIEPDON + '&showMode=dlg', '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');

        });
        $("#btnCheckThe").bindOnce("click", function () {
            $("#txtMA_BHYT").trigger('focusout');
        });
    }

    function openPopUpCheckLSKCB(ngang, doc) {
        var paramInput = {
            MABHYT: $('#txtMA_BHYT').val(),
            TENBENHNHAN: $('#txtTENBENHNHAN').val(),
            NGAYSINH: $('#txtNGAYSINH').val() == "" ? $("#txtNAMSINH").val().trim() : $('#txtNGAYSINH').val(),
            QRCODE: '',
            GIOITINH: $("#cboGIOITINHID").val(),
            MAKCBBD: $("#cboMAKCBBD").val() == null || $("#cboMAKCBBD").val() == 'null' ? "" : $("#cboMAKCBBD").val(),
            TUNGAY: $('#txtBHYT_BD').val(),
            DENNGAY: $('#txtBHYT_KT').val(),
            DIACHIBHYT: $("#txtDIACHI_BHYT").val()
        };

        dlgPopup = DlgUtil.buildPopupUrl(
            "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB",
            paramInput, "Thông tin lịch sử điều trị bệnh nhân", window.innerWidth * ngang, window.innerHeight * doc);

        var parent = DlgUtil.open("divDlgDDT");
    }

    function _checkGoiKham(delay) {
        objData = new Object();
        objData['delay'] = delay;
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.GOIBN", JSON.stringify(objData));
        return ret;
    }

    // Check so luong phong kham - TUYENNX, SONDN sua doi;
    function _checksoluongmaxphongkham(_phongid) {

        if (($('#cboDTBNID').val() == 2 || $('#cboDTBNID').val() == 3) && parseInt(cfObj.NGT_CHECK_SOLUONG_PK_ALL) == 0) {
            return 1;
        }
        var rets = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.MAXPHONGKHAM", _phongid + '$');
        return rets;
    }

    function _showPopupPhongKham(kieu, title, w, h) {
        var myVar = {
            kieu: kieu, //thêm phòng
            khambenhid: $('#hidKHAMBENHID').val(),
            dichvuid: $('#cboDICHVUID').val(),
            phongid: $('#cboPHONGKHAMID').val(),
            phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
            maubenhphamid: $('#hidMAUBENHPHAMID').val(),
            tiepnhanid: $('#hidTIEPNHANID').val(),
            doituongbenhnhanid: $("#cboDTBNID").val()
        };
        dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham", myVar, title, w, h);
        DlgUtil.open("dlgPhieuKham");
    }

    function _showPopupCongKham(title, w, h) {
        var myVar = {
            khambenhid: $('#hidKHAMBENHID').val(),
            tiepnhanid: $("#hidTIEPNHANID").val(),
            hosobenhanid: $("#hidHOSOBENHANID").val(),
            maubenhphamid: $("#hidMAUBENHPHAMID").val(),
            dichvukhambenhid: $("#hidDICHVUKHAMBENHID").val(),
            doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val()
        };
        dlgPopup = DlgUtil.buildPopupUrl("dlgCongKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K054_CHUYENYCKHAM", myVar, title, w, h);
        DlgUtil.open("dlgCongKham");
    }

    EventUtil.setEvent("ngt02k054_chuyenyckham_close", function (e) {
        loadGridData();
        DlgUtil.close("dlgCongKham");
    });

    function _showPopupSinhSTT(check) {
        var myVar = {
            khambenhid: $('#hidKHAMBENHID').val(),
            kieu: check
        };
        dlgPopup = DlgUtil.buildPopupUrl("dlgsinhstt", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K044_sinhsothutumoi", myVar, "Sinh số thứ tự ưu tiên mới", 400, 200);
        DlgUtil.open("dlgsinhstt");
    }

    function _loadYCKham(phongkhamid, dichvuid) {
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": phongkhamid});
        ComboUtil.getComboTag("cboDICHVUID", "NGTDV.009.01", sql_par, '', {
            value: '',
            text: 'Chọn yêu cầu khám'
        }, 'sql', '', function () {
            $("#cboDICHVUID").val(dichvuid);
        });
    }

    // sondn L2PT-24929
    function _loadHopDong(loaikhamid, hopdongid) {
        ComboUtil.getComboTag("cboHOPDONGID", "DS.HOPDONG1", [{"name": "[0]", "value": loaikhamid}],
            "", {value: 0, text: 'Chọn hợp đồng'}, "sql", "", function () {
                $("#cboHOPDONGID").val(hopdongid);
            });
    }

    function _loadphongkham(dichvuid, phongkhamid) {
        var sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": dichvuid});
        var _sql_pk = "NGTPK.DV"; //L2PT-53765 nang cap bvnt
        if (opt.hospital_id == 902) {
            _sql_pk = "NGTPK.DV.NTI";
        }
        // cau hinh duoc su dung cung voi cau hinh HIS_SET_YEUCAUKHAM, kiem tra so lan load cua ham de dieu chinh phu hop;
        ComboUtil.getComboTag("cboPHONGKHAMID", _sql_pk, sql_par1, "", {extval: true}, "sql", "", function () {
            if (phongkhamid != "0") {
                $("#cboPHONGKHAMID").val(phongkhamid);
            } else {
                var _yckham = $("#hidSETPHONGKHAM").val();
                if (_yckham != '' && _yckham != '0' && _loadFirstPhongKham > 0) {
                    $("#cboPHONGKHAMID").val(_yckham);
                    if (doituong_khaibao == "1") {
                        _loadFirstPhongKham = 1;
                    }
                    _loadFirstPhongKham = _loadFirstPhongKham - 1;
                }
            }
            $("#cboPHONGKHAMID").change();
        });
    }

    // sondn L2PT-26313
    function _callCheckCong(cccd) {
        //var mathe = $("#txtMA_BHYT").val().trim();
        if (cccd) {
            mathe = cccd;
        } else {
            mathe = $("#txtMA_BHYT").val().trim();
        }
        var tenbn = $("#txtTENBENHNHAN").val().trim();
        var namsinh = $("#txtNGAYSINH").val().trim() != "" ? $("#txtNGAYSINH").val().trim() : $("#txtNAMSINH").val().trim();
        var gioitinhid = $("#cboGIOITINHID").val().trim();
        var noidk = $("#cboMAKCBBD").val() == null ? "" : $("#cboMAKCBBD").val();
        var ngaybd = $("#txtBHYT_BD").val().trim();
        var ngaykt = $("#txtBHYT_KT").val().trim();

        if (mathe == "" || tenbn == "" || namsinh == "") {
            return "0";
        }

        var ret1 = _checkCongBHYT(i_u, i_p, mathe.trim(), tenbn, namsinh.trim(), gioitinhid, noidk, ngaybd, ngaykt, "0");
        if (ret1.maKetQua == "000") {
            localStorage.setItem('responseCheckCong', JSON.stringify(ret1));
        }
        return ret1;
    }

    // end sondn L2PT-26313

    function _replaceValuesNullZero(vl, defaultValue, newValue) {
        return vl == null || vl == 'null' || vl == "" || typeof vl == 'undefined' ? defaultValue : newValue;
    }

    function _setUuTienDOB() {
        var _checked = false;
        var tuoiuutien = cfObj.NGT_UUTIENTUOI_SOTUOI;
        var tuoi = tuoiuutien.split(',');
        if ($("#cboDVTUOI").val() == "1"
            && (Number($("#txtTUOI").val()) < parseInt(tuoi[0]) || Number($("#txtTUOI").val()) >= parseInt(tuoi[1]))
            && $("#txtTUOI").val() != ""
            && _uutientuoi == "1") {
            _checked = true;
            $('#chkUUTIENKHAMID').attr("disabled", true);
        } else if ($("#cboDVTUOI").val() != "1" && _uutientuoi == "1") {
            if (parseInt(tuoi[0]) == 1 && $("#cboDVTUOI").val() == 2) {
                if (Number($("#txtTUOI").val()) < 12) {
                    _checked = true;
                    $('#chkUUTIENKHAMID').attr("disabled", true);
                } else {
                    _checked = false;
                    $('#chkUUTIENKHAMID').attr("disabled", false);
                }
            } else if (parseInt(tuoi[0]) == 0) {
                if (parseInt(tuoi[2]) != '' || tuoi[2] != undefined) {
                    if (Number($("#txtTUOI").val()) <= parseInt(tuoi[2])) {
                        _checked = true;
                        $('#chkUUTIENKHAMID').attr("disabled", true);
                    } else {
                        _checked = false;
                        $('#chkUUTIENKHAMID').attr("disabled", false);
                    }
                } else {
                    _checked = false;
                    $('#chkUUTIENKHAMID').attr("disabled", false);
                }
            } else {
                _checked = true;
                $('#chkUUTIENKHAMID').attr("disabled", true);
            }
        } else if ($("#hidHIENTHIGOIKHAM").val() == "5" && $("#txtUUTIEN5").val() == "1") {
            _checked = true;
            // $('#chkUUTIENKHAMID').attr("disabled", true);
        } else {
            $('#chkUUTIENKHAMID').attr("disabled", false);
        }

        // SONDN L2PT-31261
        if (_uutienBHYT != "0") {
            var _arr = _uutienBHYT.split(',');
            var _dautheBHYT2 = $("#txtMA_BHYT").val().substring(0, 2);
            var _dautheBHYT3 = $("#txtMA_BHYT").val().substring(0, 3);
            if ($.inArray(_dautheBHYT2, _arr) >= 0 || $.inArray(_dautheBHYT3, _arr) >= 0) {
                _checked = true;
            }
        }
        // END SONDN L2PT-31261

        //$('#chkUUTIENKHAMID').attr('checked', _checked);
        $("#chkUUTIENKHAMID").prop('checked', _checked);

        // sondn L2PT-32825
        if (_thebhytksudung != "0") {
            var _arr = _thebhytksudung.split(';');
            var _dautheBHYT2 = $("#txtMA_BHYT").val().substring(0, 2);
            var _dautheBHYT3 = $("#txtMA_BHYT").val().substring(0, 3);
            if ($.inArray(_dautheBHYT2, _arr) >= 0 || $.inArray(_dautheBHYT3, _arr) >= 0) {
                DlgUtil.showMsg("Kiểm tra lại giá trị thẻ BHYT - Đối tượng thẻ BHYT");
            }
        }
        // end sondn L2PT-32825
    }

    // convert tu dinh dang yyyymmdd thành format quy dinh;
    function _convertDateHISL3(_date, _format) {
        if (_date == null || _date == 'null' || _date == "") {
            return "";
        }
        if (_date.length > 0) {
            if (_format == "dd/MM/yyyy") {
                return _date.substring(6, 8) + "/" + _date.substring(4, 6) + "/" + _date.substring(0, 4);
            } else if (_format == "dd/MM/yyyy hh24:mi") {
                return _date.substring(6, 8) + "/" + _date.substring(4, 6) + "/" + _date.substring(0, 4) + " " + _date.substring(8, 10) + ":" + _date.substring(10, 12);
            }
        }
        return _date;
    }

    // mode: mode = 0: co tim kiem ttbn cu; mode = 1: khong tim kiem ttbn cu;
    function _capnhat(mode1) {
        if ($("#cboDTBNID").val() == 1 && $("#hidTIEPNHANID").val() == "" && checkSLKham == 0) {
            var soluong = cfObj.CHECKTN65BHYT;
            if (parseInt(soluong) > 0) {
                var par = [];
                par.push({
                    "name": "[0]",
                    "value": $("#cboPHONGKHAMID").val()
                });
                var so_bn_bhyt = 0;
                var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGTCHECK.65BHYT", par);
                var row = JSON.parse(data);
                if (row != null && row.length > 0) {
                    so_bn_bhyt = row[0].DEM; // là số bn bhyt đã khám trong ngày
                }
                if (parseInt(so_bn_bhyt) >= parseInt(soluong)) {
                    DlgUtil.showConfirm("Phòng khám vượt quá " + soluong + " BN/BHYT trong 1 ngày. Có muốn tiếp tục tiếp nhận hay không?", function (flag) {
                        if (flag) {
                            checkSLKham = 1;
                            _capnhat(mode1);
                        } else {
                            return false;
                        }
                    });
                    return false;
                }
            }
        }
        checkSLKham = 0;
        if ($("#cboDTBNID").val() == 1 && check_madauthengoaitinh == 0 && cfObj.NGT_TN_CHECKBHYT_NGOAITINH != 0) {
            if ($("#txtMA_BHYT").val().substring(3, 5) != _options._province_id.substring(0, 2) && $("#cboBHYT_LoaiID").val() != '4') {
                if (cfObj.NGT_TN_CHECKBHYT_NGOAITINH == 1) {
                    DlgUtil.showConfirm("Thẻ BHYT của BN là thẻ ngoại tỉnh và loại tuyến không phải trái tuyến, bạn có muốn tiếp đón BN ? ", function (flag) {
                        if (flag) {
                            check_madauthengoaitinh = 1;
                            _capnhat(mode1);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else {
                    DlgUtil.showMsg("Thẻ BHYT của BN là thẻ ngoại tỉnh và loại tuyến không phải trái tuyến nên không thể tiếp nhận!")
                    return false;
                }
            }
        }
        check_madauthengoaitinh = 0;
        var checkdieutritrongtg = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_TN_CHECKDIEUTRITRONGKHOANGTG');
        if (check_dieutritrongkhoangtg == 0 && checkdieutritrongtg != 0) {
            var par = [];
            par.push({
                "name": "[0]",
                "value": $("#txtNGAYTIEPNHAN").val()
            });
            par.push({
                "name": "[1]",
                "value": $("#hidBENHNHANID").val()
            });
            var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("CHECK_TGDIEUTRI", par);
            var row = JSON.parse(data);
            if (row != null && row.length > 0) {
                if (checkdieutritrongtg == '1') {
                    DlgUtil.showConfirm("Thời gian đăng ký khám có nằm trong khoảng thời gian khám/ điều trị của đợt khám/điều trị liền kề trước đó!" +
                        " Mã bệnh án :" + row[0].MAHOSOBENHAN + " thời gian ra viện: " + row[0].NGAYRAVIEN, function (flag) {
                        if (flag) {
                            check_dieutritrongkhoangtg = 1;
                            _capnhat(mode1);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else {
                    DlgUtil.showMsg("Thời gian đăng ký khám có nằm trong khoảng thời gian khám/ điều trị của đợt khám/điều trị liền kề trước đó " +
                        "Mã bệnh án :" + row[0].MAHOSOBENHAN + " thời gian ra viện: " + row[0].NGAYRAVIEN + ", không thể tiếp nhận!");
                    return false;
                }
            }
        }
        check_dieutritrongkhoangtg = 0;
        if ($("#cboDTBNID").val() == 1 && check_dkkcbbdtuyen2 == 0
            && cfObj.NGT_TN_CHECKKCBBD_TUYEN2 != 0) {
            var checkkcbbd = cfObj.NGT_TN_CHECKKCBBD_TUYEN2;
            var value = checkkcbbd.split(",");
            if (value.includes($("#txtMA_KCBBD").val())) {
                DlgUtil.showConfirm("Bệnh nhân có đơn vị KCB ban đầu thuộc tuyến 2, bạn có muốn tiếp tục hay không ", function (flag) {
                    if (flag) {
                        check_dkkcbbdtuyen2 = 1;
                        _capnhat(mode1);
                    } else {
                        return false;
                    }
                });
                return false;
            }
        }
        check_dkkcbbdtuyen2 = 0;
        if ($("#hidBENHNHANID").val() != "" && mode1 == 0) {
            var CHAN_TN_DANGKHAM = cfObj.CHAN_TN_DANGKHAM;
            if (CHAN_TN_DANGKHAM != '0' && check_dangkham == 0) {
                var par = [];
                par.push({
                    "name": "[0]",
                    "value": $("#hidBENHNHANID").val()
                });
                var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("CHECK_DANGKHAM2", par);
                var row = JSON.parse(data);
                if (row != null && row.length > 0) {
                    if (CHAN_TN_DANGKHAM == '2') {
                        DlgUtil.showConfirm("Bệnh nhân đang khám hoặc đang điều trị với mã BA " + row[0].MAHOSOBENHAN + ", tại khoa " + row[0].ORG_NAME + " ngày " + row[0].NGAYTIEPNHAN + " bạn có muốn tiếp đón BN?", function (flag) {
                            if (flag) {
                                check_dangkham = 1;
                                _capnhat(mode1);
                            } else {
                                return false;
                            }
                        });
                        return false;
                    } else {
                        DlgUtil.showMsg("Bệnh nhân đang khám hoặc đang điều trị với mã BA " + row[0].MAHOSOBENHAN + ", tại khoa " + row[0].ORG_NAME + " ngày " + row[0].NGAYTIEPNHAN + ", không thể tiếp nhận.");
                        return false;
                    }
                }
            }
        }
        check_dangkham = 0;
        // kiem tra trung thong tin benh nhan
        if ($("#hidBENHNHANID").val() == "" && mode1 == 0) {
            //var data_ar;
            var _objBenhNhan = new Object();
            _objBenhNhan["tenbenhnhan"] = $("#txtTENBENHNHAN").val().toUpperCase().trim();
            _objBenhNhan["ngaysinh"] = $("#txtNGAYSINH").val() != "" ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val().trim();
            _objBenhNhan["gioitinhid"] = $("#cboGIOITINHID").val();

            // SONDN 10/01/2020 L2PT-14752
            if (_tkbnf3 == "1") {
                _objBenhNhan["ngaysinh"] = $("#txtNAMSINH").val();
            }
            // END SONDN 10/01/2020 L2PT-14752

            // sondn L2PT-26846
            if (_tkbnf3 == "2") {
                _objBenhNhan["gioitinhid"] = "-1";
            }
            // end sondn L2PT-26846

            // cho phep bo check trung thong tin co ban;
            if (_bochecktrungtt == "1" || _bochecktrungtt == "2") {
                data_ar = null;
            } else {
                data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN", '' + '$' + '3' + '$' + JSON.stringify(_objBenhNhan));
            }

            if (data_ar != null && data_ar.length > 0 && !_b_flag) {
                var _ns = ($("#txtNGAYSINH").val() == "" && $("#txtNAMSINH").val() == "")
                    ? "-1" :
                    ($("#txtNGAYSINH").val() != ""
                            ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val()
                    );
                var param = {
                    ten: $("#txtTENBENHNHAN").val() == '' ? '-1' : $("#txtTENBENHNHAN").val().toUpperCase().trim(),
                    ngaysinh: _ns,
                    gioitinh: $("#cboGIOITINHID").val(),
                    mabhyt: '-1',
                    type: '1' // ko cap nhat
                };

                // SONDN 10/01/2020 L2PT-14752
                if (_tkbnf3 == "1") {
                    param.ngaysinh = $("#txtNAMSINH").val();
                }
                // END SONDN 10/01/2020 L2PT-14752'

                // sondn L2PT-26846
                if (_tkbnf3 == "2") {
                    param.gioitinh = "-1";
                }
                // end sondn L2PT-26846

                _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'TÌM KIẾM BỆNH NHÂN', 980, 510, {closeButton: false});
                return false;
            }
        }

        // CAP NHAT CHECK CONG BHYT MOI THEO CONG VAN BYT-CNTT NGAY 31/1/2018
        var checkCongBhyt = $("#chkCHECKCONG").prop('checked');

        if (checkCongBhyt == true && $('#hidSINHTHEBHYT').val().trim() != "1"
            && ($('#cboDTBNID').val().trim() == "1" || $('#cboDTBNID').val().trim() == "6")) {
            var ret1 = _callCheckCong();
            if (ret1.hasOwnProperty("maKetQua") && ret1.hasOwnProperty("gtTheDen") && ret1.hasOwnProperty("gtTheTu")
                && ret1.maThe != null && ret1.maThe != 'null' && ret1.maThe != '') {
                if (ret1.gioiTinh == null || ret1.gioiTinh == 'null' || ret1.gioiTinh == '') {
                    $("#txtTKGIOITINHID").val(-1);
                    $("#cboGIOITINHID").val(-1);
                } else {
                    $("#txtTKGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                    $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                }
                $('#txtBHYT_BD').val(ret1.gtTheTu);
                $('#txtBHYT_KT').val(ret1.gtTheDen);
                $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
                $("#txtDIACHI_BHYT").val(ret1.diaChi);
                // SONDN 19/03/2020 L2PT-17643
                if (ret1.maKV != "") {
                    noisong = ret1.maKV;
                }
                for (i = 1; i <= 3; i++) {
                    if (ret1.maKV == "K" + i) {
                        $("#cboDT_SINHSONG").val(i);
                    }
                }
                // END SONDN 19/03/2020 L2PT-17643
                if(ret1.dsLichSuKCB2018 != null){
                    _lichsudt =  ret1.dsLichSuKCB2018[0].tinhTrang;
                }
            } else if (ret1 == "0") {
                DlgUtil.showMsg("Thiếu thông tin họ tên / mã BHYT / ngày sinh, yêu cầu bổ sung để check cổng");
                return;
            } else if (!ret1.hasOwnProperty("maKetQua")) {
                if (ret1.maKetQua == "CAQD") {
                    DlgUtil.showMsg(ret1.noiDungKetQua);
                    $("#chkCHECKCONG").prop("checked", false);
                } else {
                    DlgUtil.showMsg("Không kết nối được với cổng BHXH, yêu cầu kiểm tra lại" + ret1.ghiChu);
                    return;
                }
            }

            // Gia lap chuc nang;
            _maKetQuaCheckBHXK = ret1.maKetQua;
//			_maKetQuaCheckBHXK = "003";
            msg1 = ret1.ghiChu;

            // 004: Thẻ cũ còn giá trị sử dụng nhưng đã được cấp thẻ mới.
            msg = _maKetQuaCheckBHXK == "004" ? "" : ret1.noiDungKetQua; 			// ma 004 mac dinh dung;

            if (msg == "" && ret1.errKCBBD != "") {
                msg = "Nơi KCBBĐ không hợp lệ. ";
                msg1 = "Nơi KCBBĐ không hợp lệ. ";
            }

            // 003: Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới.
            if (_maKetQuaCheckBHXK == "003") {
                if (ret1.hasOwnProperty("maTheMoi")) {
                    // sondn L2PT-31738
                    if (_fillTheBHXHMoi == "1") {						// tu dong fill va cho tiep don binh thuong;
                        $("#txtMA_BHYT").val(ret1.maTheMoi);
                        $('#txtBHYT_BD').val(ret1.gtTheTuMoi);
                        $('#txtBHYT_KT').val(ret1.gtTheDenMoi);
                        $("#txtMA_KCBBD").val(ret1.maDKBDMoi);
                        $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBDMoi);

                        ret1.errKCBBD = "";
                        msg = "";
                        DlgUtil.showMsg("Mã thẻ được cập nhật mới " + ret1.maTheMoi + " thay cho mã cũ " + ret1.maThe + ". Bệnh nhân sẽ được tiếp đón bình thường. ");
                    } else {										// chi fill khi maThe = maTheMoi
                        if (ret1.maThe == ret1.maTheMoi) {
                            // neu cung the, tu dong cap nhat lai thong tin
                            $('#txtBHYT_BD').val(ret1.gtTheTuMoi);
                            $('#txtBHYT_KT').val(ret1.gtTheDenMoi);
                            $("#txtMA_KCBBD").val(ret1.maDKBDMoi);
                            $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBDMoi);

                            ret1.errKCBBD = ""; 				// truong hop nay tu dong cap nhat lai kcbbd, k can thong bao lai;
                            msg = ""; 							// pass
                        } else {
                            msg = "Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới. Thẻ mới của bệnh nhân là " + ret1.maTheMoi + ". ";
                            msg1 = "Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới. Thẻ mới của bệnh nhân là " + ret1.maTheMoi + ". ";
                        }
                    }
                    // end sondn L2PT-31738
                } else {
                    msg = "Mã thẻ mới không tồn tại, yêu cầu kiểm tra lại thông tin cổng BHXH. ";
                    msg1 = "Mã thẻ mới không tồn tại, yêu cầu kiểm tra lại thông tin cổng BHXH. ";
                }
            }
            //==========================================

            if (msg != "") {							// khong hop le, cap nhat lai roi cho tiep don
                if ($("#hidTUDONGFILLBHXH").val() == "0") {
                    DlgUtil.showMsg(msg1);
                    return;
                }

                if (ret1.maKetQua != "" && ret1.gtTheDen != "" && ret1.gtTheDen != 'null' && ret1.gtTheDen != null) {
                    // check sai ho ten, ngay sinh
                    if (_maKetQuaCheckBHXK == "060" || _maKetQuaCheckBHXK == "061" || _maKetQuaCheckBHXK == "070" || ret1.errKCBBD != "") {
                        DlgUtil.showConfirm(msg1 + "Bạn có muốn cập nhật lại thông tin từ Cổng BHXH ? ", function (flag) {
                            if (flag) {
                                _capnhat2(_maKetQuaCheckBHXK, msg, msg1, ret1);
                            }
                        });
                    } else {
                        DlgUtil.showMsg(msg1);
                        return;
                    }
                } else {
                    DlgUtil.showMsg(msg1);
                    return;
                }
            } else {						// hop le, cho tiep don;
                if (_maKetQuaCheckBHXK == "004" || _maKetQuaCheckBHXK == "003") {
                    DlgUtil.showMsg(msg1 + ". Bệnh nhân sẽ được tiếp đón bình thường.", function () {
                        _capnhat2(_maKetQuaCheckBHXK, msg, msg1, ret1);
                    });
                } else {
                    _capnhat2(_maKetQuaCheckBHXK, msg, msg1, ret1);
                }
            }
        } else {
            var msg = "BOCHECK";
            var _maKetQuaCheckBHXK = "";
            var ret1 = "";
            _capnhat2(_maKetQuaCheckBHXK, msg, "", ret1);
        }

    }

    function _capnhat2(_maKetQuaCheckBHXK, msg, msg1, ret1) {
        if ($("#cboDTBNID").val() == 1 && $("#cboBHYT_LoaiID").val() == 2 
            && _canhbaodungtuyengt == 0 && _lichsudt != 2 && cfObj.NGT_TN_CHECKCHUYENVIEN_DTGT == 1) {
            DlgUtil.showConfirm("Chưa có thông tin chuyển tuyến của BN, có tiếp tiếp tục tiếp nhận không?", function (flag) {
                if (flag) {
                    _canhbaodungtuyengt = 1;
                    _capnhat2(_maKetQuaCheckBHXK, msg, msg1, ret1)
                } else {
                    return false;
                }
            });
            return false;
        }
        _canhbaodungtuyengt = 0;
        if (msg != "BOCHECK") {
            if ($("#hidTUDONGFILLBHXH").val() != "0") {
                if (msg != "") {
                    if (_maKetQuaCheckBHXK == "060" || _maKetQuaCheckBHXK == "061" || _maKetQuaCheckBHXK == "070" || ret1.errKCBBD != "") {
                        $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                        $("#txtMA_KCBBD").val(ret1.maDKBD);
                        $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBD);

                        if (ret1.ngaySinh.length == "4") {
                            $("#txtNAMSINH").val(ret1.ngaySinh);
                            $("#txtNGAYSINH").val("");
                            $("#txtNAMSINH").change();
                        } else {
                            $("#txtNGAYSINH").val(ret1.ngaySinh);
                            $("#txtNGAYSINH").change();
                        }
                    } else {
                        DlgUtil.showMsg(msg1);
                        return;
                    }
                }

                if (cfObj.FILL_TT_THE_TUCONG == "1") {
                    $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                }
            } else {
                if (msg != "") {
                    DlgUtil.showMsg(msg1);
                    return;
                }
            }
        }

        // kiem tra tinh hop le cua the
        if (!_kiemTra()) return false;

        var _checkthekhoa = _checkMaTheKhoa();
        if (_checkthekhoa != "") {
            DlgUtil.showMsg(_checkthekhoa, function () {
                $("#txtMA_BHYT").select();
            });
            return;
        }

        if (_checksoluongmaxphongkham($('#cboPHONGKHAMID').val()) == '-1') {
            DlgUtil.showMsg('Phòng khám hết số');
            return;
        }

        // check dung tuyen / trai tuyen;
        var kcbbd = $("#txtKCBBD").val().trim();
        if (kcbbd == "") {
            kcbbd = "0";
        }
        var _dtbnid1 = $('#cboDTBNID').val();
        var _makcbbd = $('#txtMA_KCBBD').val();
        var _bhytloaiid = $('#cboBHYT_LoaiID').val();
        var _hid = _options.hospital_code;

        if (cfObj.NGT_CANHBAOKCBBD_DTP == 1 && (
            $('#txtMA_KCBBD').val() == ($('#cboHC_TINHID' + " option:selected").attr('extval1') + "000") ||
            $('#txtMA_KCBBD').val() == _options.hospital_code) && $('#cboBHYT_LoaiID').val() == '3') {
            DlgUtil.showMsg('Mã KCBBD: ' + $('#txtMA_KCBBD').val() + ' chỉ được chọn loại đúng tuyến!');
            return;
        }
        // sondn IT360-241757
        if (_ngtsettuyen == "0") {
            if (
                (kcbbd == "0" && (_dtbnid1 == "1" || _dtbnid1 == "6") 					// doi tuong bhyt
                    && ((_hid != _makcbbd && _bhytloaiid != '4') || (_hid == _makcbbd && _bhytloaiid == '4'))	// trai tuyen
                )
                ||
                (
                    kcbbd != "0" && (_dtbnid1 == "1" || _dtbnid1 == "6") 	// doi tuong bhyt
                    && (((kcbbd.indexOf(_makcbbd) != -1) && _bhytloaiid == '4') || ((kcbbd.indexOf(_makcbbd) == -1) && _bhytloaiid != '4'))			// trai tuyen
                )
            ) {
                if ($('#chkCOGIAYKS').is(":checked") || $("#chkHENKHAM").prop('checked') == true || $("#txtCHECKDUNGTUYEN").val() == "1") {
                    _checkLDTHISL3();
                } else {
                    if (cfObj.NGT_THONGBAO_DULIEUTUYEN == 1) {
                        _checkLDTHISL3();
                    } else {
                        var strThongBao = opt.hospital_id != 965 ?
                            "Dữ liệu đúng tuyến/trái tuyến không hợp lệ. Có tiếp tục?"
                            : "Mã nơi đăng ký khám chữa bệnh ban đầu không phải của Bệnh viện Đa khoa Bưu Điện. Có tiếp tục?";
                        DlgUtil.showConfirm(strThongBao, function (flag) {
                            if (flag) {
                                _checkLDTHISL3();
                            }
                        });
                    }
                }

            } else {
                _checkLDTHISL3();
            }
        } else {
            var _objjj = new Object();
            _objjj.MAKCBBD = $('#txtMA_KCBBD').val();
            _objjj.BHYTLOAIID = $('#cboBHYT_LoaiID').val();
            _objjj.NGTSETTUYEN = _ngtsettuyen;
            _objjj.MA_BHYT = $("#txtMA_BHYT").val();

            var _tuyenmdd = jsonrpc.AjaxJson.ajaxCALL_SP_S('NGT01T001.TUYENMD', JSON.stringify(_objjj));
            if (Number(_tuyenmdd) > 0) {
                if (_tuyenmdd != $('#cboBHYT_LoaiID').val()) {
                    if ($('#chkCOGIAYKS').is(":checked") || $("#chkHENKHAM").prop('checked') == true || $("#txtCHECKDUNGTUYEN").val() == "1") {
                        _checkLDTHISL3();
                    } else {
                        if (cfObj.NGT_THONGBAO_DULIEUTUYEN == 1) {
                            _checkLDTHISL3();
                        } else {
                            var strThongBao = opt.hospital_id != 965 ?
                                "Dữ liệu đúng tuyến/trái tuyến không hợp lệ. Có tiếp tục?"
                                : "Mã nơi đăng ký khám chữa bệnh ban đầu không phải của Bệnh viện Đa khoa Bưu Điện. Có tiếp tục?";
                            DlgUtil.showConfirm(strThongBao, function (flag) {
                                if (flag) {
                                    _checkLDTHISL3();
                                }
                            });
                        }
                    }
                } else {
                    _checkLDTHISL3();
                }
            } else {
                DlgUtil.showMsg("Lỗi check thông tin tuyến cho thẻ BHYT này. ", undefined, undefined, "error");
                return;
            }
        }
        // end sondn IT360-241757
    }

    function _checkLDTHISL3() {
        if (($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6") && _hisl3api_checkldt == "1") {
            // CHECK CONG HISL3 API
            var maThe = $("#txtMA_BHYT").val().trim();
            var ngayKham = $("#txtNGAYTIEPNHAN").val();
            ngayKham = ngayKham.substring(6, 10) + ngayKham.substring(3, 5) + ngayKham.substring(0, 2);

            var ret = _HISL3API_LDT(maThe, ngayKham);
            if (ret.statusCode == "000") {
                try {
                    var _kq = JSON.parse(ret.data);
                    if (_kq.MA_CSKCB == null || _kq.MA_CSKCB == 'null' || _kq.MA_CSKCB == "") {
                        // khong lam dung the;
                        _capnhat3();
                    } else {
                        DlgUtil.showConfirm("Tồn tại thông tin lạm dụng thẻ của Bệnh nhân này." +
                            "Mã CS:" + _kq.MA_CSKCB + ",Tên CS:" + _kq.TEN_CSKCB +
                            ", Ngày vào: " + _convertDateHISL3(_kq.NGAY_VAO, "dd/MM/yyyy") +
                            ", Ngày ra: " + _convertDateHISL3(_kqq.NGAY_RA, "dd/MM/yyyy hh24:mi") +
                            ",Ngày ra:" + _convertDateHISL3(_kq.NGAY_RA, "dd/MM/yyyy") + ",Tên bệnh" + _kq.TEN_BENH +
                            ". Bạn có muốn tiếp tục? ", function (flag) {
                            if (flag) {
                                _capnhat3();
                            } else {
                                DlgUtil.showMsg("HIS L3: Không tiếp nhận bệnh nhân do tồn tại thông tin lạm dụng thẻ. ");
                                return;
                            }
                        });
                    }
                } catch (e) {
                    DlgUtil.showMsg("HIS L3: Lỗi thông tin trả về Lạm dụng thẻ không đúng định dạng. Yêu cầu kiểm tra lại API. ", undefined, undefined, "error");
                    return;
                }

            } else if (ret.statusCode == "099") {
                // qua thoi gian quy dinh;
                DlgUtil.showMsg("HIS L3: Không kết nối được tới API lấy dữ liệu (API Timeout). 099");
                return;
            } else {
                // khong co du lieu;
                DlgUtil.showMsg("HIS L3: Không kết nối được tới API lấy dữ liệu. UNKNOWN");
                return;
            }
        } else if (($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6") && _hisl3api_checkldt == "2") {
            // CHECK GATEWAY
            var _hasData = "0";
            var maThe = $("#txtMA_BHYT").val().replace(/[-]/g, "").trim();
            var ngayKham = $("#txtNGAYTIEPNHAN").val();
            var maKCBBD = _options.hospital_code; 															// MA DON VI DUNG CHECK ;

            ngayKham = ngayKham.substring(8, 10) + ngayKham.substring(3, 5) + ngayKham.substring(0, 2);
            var ret = _HISL3API_LDT_GW(maThe, maKCBBD, ngayKham);
            if (ret.statusCode == "000") {
                try {
                    var _kq = JSON.parse(ret.data);
                    if (_kq.hasOwnProperty("result") && _kq.result.length > 0) {
                        for (i = 0; i < _kq.result.length && _hasData == "0"; i++) {
                            if (_kq.result[i].state == true && _kq.result[i].result.length > 0) {
                                for (j = 0; j < _kq.result[i].result.length && _hasData == "0"; j++) {
                                    var _kqq = _kq.result[i].result[j];
                                    if (_kqq.MA_CSKCB != _opts.hospital_code) {
                                        _hasData = "1";
                                        /*DlgUtil.showConfirm("Tồn tại thông tin lạm dụng thẻ của Bệnh nhân này." +
												"Mã CS:" + _kqq.MA_CSKCB + ",Tên CS:" + _kqq.TEN_CSKCB +
												",Ngày vào:" + _convertDateHISL3(_kqq.NGAY_VAO, "dd/MM/yyyy") +
												",Ngày ra:" + _convertDateHISL3(_kqq.NGAY_RA, "dd/MM/yyyy") + ",Tên bệnh: " + _kqq.TEN_BENH +
												". Bạn có muốn tiếp tục? ", function(flag){
											if (flag){
												_capnhat3();
											}else{
												DlgUtil.showMsg("GATEWAY: Không tiếp nhận bệnh nhân do tồn tại thông tin lạm dụng thẻ. ");
												return;
											}
										});
										*/
                                        DlgUtil.showConfirm("Tồn tại thông tin lạm dụng thẻ của bệnh nhân: " + _kqq.HO_TEN +
                                            ", Ngày vào: " + _convertDateHISL3(_kqq.NGAY_VAO, "dd/MM/yyyy") +
                                            ", Ngày ra: " + _convertDateHISL3(_kqq.NGAY_RA, "dd/MM/yyyy hh24:mi") +
                                            ", Mã CS: " + _kqq.MA_CSKCB +
                                            ", Tên CS: " + _kqq.TEN_CSKCB +
                                            ", Khoa khám: " + _kqq.KHOA_PHONG +
                                            ", Tên bệnh: " + _kqq.TEN_BENH +
                                            ". Bạn có muốn tiếp tục? ", function (flag) {
                                            if (flag) {
                                                _capnhat3();
                                            }/*else{
												DlgUtil.showMsg("GATEWAY: Không tiếp nhận bệnh nhân do tồn tại thông tin lạm dụng thẻ. ");
												return;
											}*/
                                        });
                                    }

                                }
                            }
                        }
                    }
                    if (_hasData == "0") {
                        _capnhat3();					// k có du lieu lam dung the, cho phep tiep tuc;
                    }
                } catch (e) {
                    DlgUtil.showConfirm("GATEWAY: Lỗi thông tin trả về Lạm dụng thẻ không đúng định dạng. Yêu cầu kiểm tra lại API. Bạn có muốn tiếp tục? ", function (flag) {
                        if (flag) {
                            _capnhat3();
                        }
                    });
//					DlgUtil.showMsg("GATEWAY: Lỗi thông tin trả về Lạm dụng thẻ không đúng định dạng. Yêu cầu kiểm tra lại API. ");
//					return;
                }

            } else if (ret.statusCode == "099") {
                //L2PT-31615
                // qua thoi gian quy dinh;
                DlgUtil.showConfirm("GATEWAY: Không tổng hợp được lịch sử kcb tại các csyt khác trong tỉnh, vui lòng liên hệ admin kiểm tra. Bạn có muốn tiếp tục? ", function (flag) {
                    if (flag) {
                        _capnhat3();
                    }
                });
//				DlgUtil.showMsg("GATEWAY: Không tổng hợp được lịch sử kcb tại các csyt khác trong tỉnh, vui lòng liên hệ admin kiểm tra! 099");
//				return;
            } else {
                // khong co du lieu;
                DlgUtil.showConfirm("GATEWAY: Không tổng hợp được lịch sử kcb tại các csyt khác trong tỉnh, vui lòng liên hệ admin kiểm tra. Bạn có muốn tiếp tục? ", function (flag) {
                    if (flag) {
                        _capnhat3();
                    }
                });
//				DlgUtil.showMsg("GATEWAY: Không tổng hợp được lịch sử kcb tại các csyt khác trong tỉnh, vui lòng liên hệ admin kiểm tra! Unknown");
//				return;
            }

        } else {
            // cau hinh tat, hoac doi tuong BN # BHYT
            _capnhat3();
        }
    }

    function _capnhat3() {
        var objData = new Object();
        if ($("#cboHC_TINHID").val().trim() != "") {
            $("#hidBNDIAPHUONGID").val($("#cboHC_TINHID option:selected").val());
        }
        if ($("#cboHC_HUYENID").val().trim() != "") {
            $("#hidBNDIAPHUONGID").val($("#cboHC_HUYENID option:selected").val());
        }
        if ($("#cboHC_XAID").val().trim() != "") {
            $("#hidBNDIAPHUONGID").val($("#cboHC_XAID option:selected").val());
        }

        FormUtil.setFormToObject("tabTiepNhan", "", objData);

        if (objData["DIABANID"] == null)
            objData["DIABANID"] = "";
        if ($("#txtTKMACHANDOANTUYENDUOI").val().trim() == "") {
            objData["MACHANDOANTUYENDUOI"] = "";
        }
        if ($("#txtTKMANOIGIOITHIEU").val().trim() == "") {
            objData["MANOIGIOITHIEU"] = "";
            objData["TENNOIGIOITHIEU"] = "";
        }
        if (objData["UUTIENKHAMID"] == "1") {
            objData["UUTIENKHAMID"] = "3";
        }

        var tyle = $('#cboDOITUONGDB option:selected').attr('extval0');
        if (tyle != undefined) {
            objData["TYLE_MIENGIAM"] = tyle;
        }
        objData["KHOAID"] = _options._khoaid;
        objData["PHONGIDTIEPNHAN"] = _opts.phongid;

        // THONG TIN CHUYEN VIEN
        if (objChuyenVien.hasOwnProperty("CV_CHUYENVIEN_HINHTHUCID")
            && objChuyenVien.hasOwnProperty("CV_CHUYENVIEN_HINHTHUCID")
            && objChuyenVien.hasOwnProperty("CHANDOANTUYENDUOI")
            && objChuyenVien.hasOwnProperty("MANOIGIOITHIEU")) {
            objData["CV_CHUYENVIEN_HINHTHUCID"] = objChuyenVien["CV_CHUYENVIEN_HINHTHUCID"] != null ? objChuyenVien["CV_CHUYENVIEN_HINHTHUCID"] : "";
            objData["CV_CHUYENVIEN_LYDOID"] = objChuyenVien["CV_CHUYENVIEN_LYDOID"] != null ? objChuyenVien["CV_CHUYENVIEN_LYDOID"] : "";
            if (objChuyenVien["CV_CHUYENDUNGTUYEN"] == "1") {
                objData["CV_CHUYENDUNGTUYEN"] = "1";
                objData["CV_CHUYENVUOTTUYEN"] = "0";
            } else {
                objData["CV_CHUYENDUNGTUYEN"] = "0";
                objData["CV_CHUYENVUOTTUYEN"] = "1";
            }
        }

        // Khi cau hinh bat, benh nhan dung tuyen gioi thieu bat buoc nhap thong tin chuyen tuyen;
        if ($("#hidNHAPCHUYENTUYEN").val() == "1" && $("#cboBHYT_LoaiID").val() == "2") {
            if ($('#txtCHANDOANTUYENDUOI').val() == "" || $('#cboCV_CHUYENVIEN_HINHTHUCID').val() == ""
                || $('#cboCV_CHUYENVIEN_LYDOID').val() == "" || $('#txtTKMANOIGIOITHIEU').val() == "") {
                if ((QD_4750 == '1' && $("#cboBHYT_LOAIID1").val() != "1") || QD_4750 != '1') {
                    DlgUtil.showMsg("Yêu cầu nhập thông tin chuyển tuyến với bệnh nhân đúng tuyến giới thiệu. ");
                    return;
                }
            }
        }

        // THONG TIN SINH TON
        if (objSinhTon != null) {
            objData["KHAMBENH_MACH"] = objSinhTon["KHAMBENH_MACH"] != null ? objSinhTon["KHAMBENH_MACH"] : "";
            objData["KHAMBENH_NHIETDO"] = objSinhTon["KHAMBENH_NHIETDO"] != null ? objSinhTon["KHAMBENH_NHIETDO"] : "";
            objData["KHAMBENH_HUYETAP_LOW"] = objSinhTon["KHAMBENH_HUYETAP_LOW"] != null ? objSinhTon["KHAMBENH_HUYETAP_LOW"] : "";
            objData["KHAMBENH_HUYETAP_HIGH"] = objSinhTon["KHAMBENH_HUYETAP_HIGH"] != null ? objSinhTon["KHAMBENH_HUYETAP_HIGH"] : "";
            objData["KHAMBENH_NHIPTHO"] = objSinhTon["KHAMBENH_NHIPTHO"] != null ? objSinhTon["KHAMBENH_NHIPTHO"] : "";
            objData["KHAMBENH_CANNANG"] = objSinhTon["KHAMBENH_CANNANG"] != null ? objSinhTon["KHAMBENH_CANNANG"] : "";
            objData["KHAMBENH_CHIEUCAO"] = objSinhTon["KHAMBENH_CHIEUCAO"] != null ? objSinhTon["KHAMBENH_CHIEUCAO"] : "";
        }

        // SONDN OLD VERSION
        if ($('#cboDTBNID').val() == 6) {
            objData.CHECKBHYTDV = "1";
            objData.DTBNID = "1";
        } else {
            objData.CHECKBHYTDV = "0";
        }

        if (doituong_khaibao == '1') {
            objData.SUB_DTBNID = $("#cboDTBNID").find(':selected').attr('extval0');
        } else {
            objData.SUB_DTBNID = "0";
        }
        var imgTag = document.getElementById('imgBN');
        objData["ANHBENHNHAN1"] = imgTag.src;

        objData["MADOITUONGNGHEO"] = $("#txtMADOITUONGNGHEO").val().trim();

        if ($("#hidDENNGAYBHYT").val() == "1" || $("#txtBHYT_KT").val() == $("#hidNGAYMAX").val()) {
            objData["BHYT_KT"] = "";
        }

        objData.HENKHAM = $("#chkHENKHAM").prop('checked') == true ? "1" : "0";

        if ($("#hidTINHTHUKHAC").val() == "1") {
            // sondn L2PT-27834
            if (_thukhacvpi == "0") {
                if ($("#cboDTBNID").val() != "1" && $("#cboDTBNID").val() != "6") {
                    objData.DVTHUKHAC = "0";
                }
            }
            // end sondn L2PT-27834
        }

        objData["BACSYYCID"] = _replaceValuesNullZero(objData["BACSYYCID"], "-1", objData["BACSYYCID"]);
        objData["THANNHAN"] = _replaceValuesNullZero(objData["THANNHAN"], "0", objData["THANNHAN"]);
        objData["THANNHAN1"] = _replaceValuesNullZero(objData["THANNHAN1"], "0", objData["THANNHAN1"]);
        objData["QUANHAMID"] = _replaceValuesNullZero(objData["QUANHAMID"], "-1", objData["QUANHAMID"]);
        objData["DVCTID"] = _replaceValuesNullZero(objData["DVCTID"], "-1", objData["DVCTID"]);
        objData["HOPDONGID"] = _replaceValuesNullZero(objData["HOPDONGID"], "0", objData["HOPDONGID"]);
        objData["cboTAINAN_NGUYENNHANID"] = _replaceValuesNullZero(objData["cboTAINAN_NGUYENNHANID"], "-1", objData["cboTAINAN_NGUYENNHANID"]);

        var param_arr_pk = [];
        //======================= Day len cong BYT de check thong tin lam dung the;
        if ($("#txtBYTLAYDL").val() == "1"
            && ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6")) {
            var objFirst = createObjectLAMDUNGTHE(i_u1);
            var objLAMDUNGTHE = XML_BYT_TaoTheLAMDUNGTHE(objFirst); 							// tao the
            var objHeader = XML_BYT_TaoHeader(objFirst); 										// tao doi tuong header;
            var obj3 = XML_BYT_TaoKhung(objHeader, objLAMDUNGTHE, "2"); 						// tao JSON full => XML

            var resultCongBYT = XML_BYT_lamdungthe($("#txtBYTURL").val(), i_u1, i_p1, obj3);

            if ($("#txtBYTSTOPCHUCNANG").val() != "1") {
                _checkDKDB(objData, param_arr_pk);
            } else {
                if (resultCongBYT.length > 0) {
                    var arr = resultCongBYT.split(';');
                    if (arr.length >= 2) {
                        _transidLAMDUNGTHE = arr[2];
                    }					// luu transid lam dung the;
                    if (arr[0] != "0") {
                        // co lam dung the, xac nhan
                        var sttr = "";
                        for (i = 0; i < arr.length; i++) {
                            if (i != 0 && i != 2) {
                                sttr += arr[i];				// 0 ma loi; 2 transid loi;
                            }
                        }
                        DlgUtil.showConfirm("Cổng BYT: [" + sttr + "]" + ". Bạn có muốn tiếp nhận bệnh nhân này ? ", function (flag) {
                            if (flag) {
                                _checkDKDB(objData, param_arr_pk);
                            } else {
                                DlgUtil.showMsg("Không lưu thông tin do thao tác hủy của người dùng. ");
                                return;
                            }
                        });
                    } else {
                        // khong lam dung the, luu luon;
                        _checkDKDB(objData, param_arr_pk);
                    }
                } else {
                    // loi check cong;
                    DlgUtil.showMsg("Gửi thông tin Cổng BYT thất bại, yêu cầu kiểm tra lại thông tin. ");
                    return;
                }
            }
        } else {
            // k check lam dung the, luu thong tin luon;
            _checkDKDB(objData, param_arr_pk);
        }
    }

    function _checkDKDB(objData, param_arr_pk) {
        var retdk = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.CHECKDK", JSON.stringify(objData) + '$' + JSON.stringify(param_arr_pk));
        if (typeof retdk != 'undefined') {
            if (retdk.length > 1) {
                var strErr = "";
                var _counter = 0;
                for (i = 0; i < retdk.length; i++) {
                    var retdks = retdk[i].RES.split('@');

                    if (retdks[0] == "1") {
                        if (_options.hospital_id == 923) {								// sondn L2PT-2862
                            strErr += (++_counter) + '/Bệnh nhân có bệnh án ' + retdks[3] + ' đến KCB từ ngày ' + retdks[2] + ' - ' + retdks[4] + ' chưa thanh toán viện phí!';
                        } else {
                            strErr += (++_counter) + '/Bệnh nhân chưa thanh toán lần khám trước. Số tiền còn nợ: ' + retdks[1] + 'VNĐ. Ngày giờ nợ: ' + retdks[2] + ". Mã bệnh án liên quan " + retdks[3] + ". ";
                        }
                    }
                    if (retdks[0] == "2") {
                        strErr += (++_counter) + '/Bệnh nhân đã có thời gian ra viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". ";
                    }
                    if (retdks[0] == "3") {
                        strErr += (++_counter) + '/Tồn tại bệnh nhân trùng thông tin cơ bản. Mã bệnh án liên quan: ' + retdks[1] + ". ";
                    }
                    if (retdks[0] == "4") {
                        strErr += (++_counter) + '/Bệnh nhân đang có thông tin nhập viện.  ';
                    }
                    if (retdks[0] == "5") {
                        strErr += (++_counter) + '/Bệnh nhân đang có thông tin điều trị ngoại trú. ';
                    }
                    if (retdks[0] == "6") {
                        strErr += (++_counter) + '/Bệnh nhân chưa thanh toán lần khám trước. Số tiền còn nợ: ' + retdks[1] + 'VNĐ. Ngày giờ nợ: ' + retdks[2] + ". Mã bệnh án liên quan " + retdks[3] + ". ";
                    }
                    if (retdks[0] == "7") {
                        if (retdks[2] == "1" || retdks[1] == "2") {
                            strErr += retdks[2] == "1" ? (++_counter) + "/Bệnh nhân đã duyệt kế toán. " : "";
                            strErr += retdks[1] == "2" ? (++_counter) + "/Bệnh nhân đã duyệt bảo hiểm. " : "";
                            strErr += (++_counter) + "/Nếu tiếp nhận BN sẽ tính tiếp vào lần khám đã kết thúc trong ngày. "
                        }
                    }
                    if (retdks[0] == "8") {
                        strErr += (++_counter) + '/Bệnh nhân đã ra viện trong ngày đã có đơn thuốc. ';
                    }
                    if (retdks[0] == "9") {
                        strErr += (++_counter) + '/Bệnh nhân này đã trốn viện với mã BA ' + retdks[1] + ", Ngày vào viện " + retdks[2] + ".";
                    }
                    if (retdks[0] == "11") {
                        strErr += (++_counter) + '/Bệnh nhân đã có phát sinh lần khám trong vòng ' + retdks[1] + " ngày gần đây.";
                    }
                    if (retdks[0] == "12") {
                        strErr += (++_counter) + '/Phòng đang/đã khám ' + retdks[1] + ' bệnh nhân BHYT. Giới hạn tối đa ' + retdks[2] + ' bệnh nhân.';
                    }
                    if (retdks[0] == "13") {
                        strErr += (++_counter) + 'Bệnh nhân đã có thời gian ra viện ngày hôm nay. Mã hồ sơ bệnh án liên quan:' + retdks[2] + ' ngày ra viện : ' + retdks[3];
                    }
                    if (retdks[0] == "20") {
                        strErr += (++_counter) + '/Bệnh nhân đang tiếp nhận là Trái tuyến.';
                    }
                    if (retdks[0] == "21") {
                        if (retdks[1] == '6') {
                            DlgUtil.showMsg('Bệnh nhân đã nhập viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". Không thể tiếp nhận lại!");
                            return false;
                        } else if (retdks[1] == '7') {
                            DlgUtil.showMsg('Bệnh nhân đã chuyển viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". Không thể tiếp nhận lại!");
                            return false;
                        }
                        if (retdks[4] == '1' && retdks[5] == '4') {
                            strErr += (++_counter) + '/Bệnh nhân đã có thời gian ra viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". ";
                        } else if (retdks[4] == '1' && retdks[5] != '4') {
                            if ($("#cboDTBNID").val() == '1' && $("#cboBHYT_LoaiID").val() !== '4' && $.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value == '3') {
                                DlgUtil.showMsg('Bệnh nhân đã có thời gian ra viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". Không thể tiếp nhận lại!");
                                return false;
                            } else {
                                strErr += (++_counter) + '/Bệnh nhân đã có thời gian ra viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". ";
                            }
                        } else {
                            strErr += (++_counter) + '/Bệnh nhân đã có thời gian ra viện ngày hôm nay. Mã hồ sơ bệnh án liên quan: ' + retdks[2] + ". ";
                        }
                    }
                }
                /*if(strErr != ""){
					strErr += " Bạn có muốn tiếp đón BN này ?";
				}*/

                if (strErr == "") {
                    _checkDKDB1(objData, param_arr_pk);
                } else {

                    if (cfObj.KIEU_THONG_BAO_CHECKDK == "1") {
                        DlgUtil.showMsg(strErr);
                    } else if (cfObj.KIEU_THONG_BAO_CHECKDK == "2") {
                        if (cfObj.NGT_CHECKKHAMTRONGNGAY_NHIND == '1') {
                            if ($.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value == '2' ||
                                (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_KHONGCHAN_BNNOITRU_TRONGNGAY') == "1"
                                    && retdk[1].RES.split('@')[0] == "21" && retdk[1].RES.split('@')[3] == "0")) {
                                if (strErr != "") {
                                    strErr += " Bạn có muốn tiếp đón BN này ?";
                                }
                                DlgUtil.showConfirm(strErr, function (flag) {
                                    if (flag) {
                                        _checkDKDB1(objData, param_arr_pk);
                                    }
                                });
                            } else {
                                DlgUtil.showMsg(strErr);
                            }
                        } else {
                            if ($.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value == '2' ||
                                (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_KHONGCHAN_BNNOITRU_TRONGNGAY') == "1"
                                    && retdk[1].RES.split('@')[0] == "2" && retdk[1].RES.split('@')[3] == "0")) {
                                if (strErr != "") {
                                    strErr += " Bạn có muốn tiếp đón BN này ?";
                                }
                                DlgUtil.showConfirm(strErr, function (flag) {
                                    if (flag) {
                                        _checkDKDB1(objData, param_arr_pk);
                                    }
                                });
                            } else {
                                DlgUtil.showMsg(strErr);
                            }
                        }

                    } else {
                        if (strErr != "") {
                            strErr += " Bạn có muốn tiếp đón BN này ?";
                        }
                        DlgUtil.showConfirm(strErr, function (flag) {
                            if (flag) {
                                _checkDKDB1(objData, param_arr_pk);
                            }
                        });
                    }

                    /*DlgUtil.showConfirm(strErr, function(flag){
						if(flag){
							_checkDKDB1(objData, param_arr_pk);
						}
					});*/
                }
            } else {
                DlgUtil.showMsg("Không lấy được dữ liệu check thông tin BN, yêu cầu kiểm tra lại");
            }
        } else {
            // neu undefined, van cho luu tiep thong tin binh thuong; dong thoi logs lai trong ghi chu
            objData["GHICHU"] = "_checkDKDB Undefined ";
            _checkDKDB1(objData, param_arr_pk);
        }
    }

    // ham cho phep kham tiep doi voi benh nhan da ket thuc kham BHYT trong ngay; cau hinh da duoc cai dat trong chuc nang;
    function _checkDKDB1(objData, param_arr_pk) {
        var retdk = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T002.CHECKDK1", JSON.stringify(objData) + '$' + JSON.stringify(param_arr_pk));
        if (retdk == "1") {									// benh nhan chua kt kham, chua bat cau hinh, nhap moi;
            _capnhat4(objData, param_arr_pk);
        } else if (retdk.length > 10 && retdk.substring(0, 1) == "1") {			// mo benh an benh nhan da ket thuc kham thanh cong; cap nhat lai JSON:
            var _arr = retdk.split('@');
            objData.TIEPNHANID = _arr[1];
            objData.KHAMBENHID = _arr[2];
            objData.HOSOBENHANID = _arr[3];
            objData.BHYTID = _arr[4];
            objData.PHONGKHAMDANGKYID = _arr[5];
            objData.MAUBENHPHAMID = _arr[6];
            objData.DICHVUKHAMBENHID = _arr[7];
            _capnhat4(objData, param_arr_pk);
        } else if (retdk == "lagoidichvu") {
            DlgUtil.showMsg("Công khám này là gói dịch vụ / gói KSK, không cho phép tiếp nhận lại. ");
        } else if (retdk == "duyetbhloi") {
            DlgUtil.showMsg("Lỗi tự động hủy duyệt BH không thành công. ", undefined, undefined, "error");
        } else if (retdk == "duyetktdaxuatthuoc") {
            DlgUtil.showMsg("Bệnh nhân đã xuất thuốc ngoại trú, không thể gỡ duyệt. ");
        } else if (retdk == "duyetktloi") {
            DlgUtil.showMsg("Lỗi tự động hủy duyệt KT không thành công. ", undefined, undefined, "error");
        } else if (retdk == "loisothutu") {
            DlgUtil.showMsg("Lỗi sinh số thứ tự mẫu bệnh phẩm không thành công. ");
        } else if (retdk == "loimaubenhpham") {
            DlgUtil.showMsg("Lỗi sinh mẫu bệnh phẩm không thành công. ", undefined, undefined, "error");
        } else if (retdk == "loiphongkhamdangky") {
            DlgUtil.showMsg("Lỗi sinh phòng khám đăng ký không thành công. ", undefined, undefined, "error");
        } else if (retdk == "loidichvukhambenh") {
            DlgUtil.showMsg("Lỗi sinh dịch vụ khám bệnh không thành công. ", undefined, undefined, "error");
        } else if (retdk == "chanxutri") {
            DlgUtil.showMsg('Bệnh nhân đã có xử trí tử vong, không thể tiếp nhận được. ');
        } else if (retdk == "loingaytiepnhan") {
            DlgUtil.showMsg('Ngày tiếp nhận của BN đang nhỏ hơn ngày tiếp nhận thực tế');
        } else if (retdk == "dakhamtrongngay_chuathanhtoan") {
            DlgUtil.showMsg('Bệnh nhân viện phí đã đăng ký khám trong ngày và chưa thanh toán');
            return false;
        } else {
            DlgUtil.showMsg("Lỗi trong quá trình xử lý, không thể tiếp nhận bệnh nhân này. ", undefined, undefined, "error");
        }
    }

    function _taoiqms_hoanthanh() {
        var _tungay = $("#txtBHYT_BD").val();
        var _denngay = $("#txtBHYT_KT").val();
        var _makcbbd = $("#cboMAKCBBD").val();

        var _obj = {
            "id": $("#txtTRANSACTIONID6").val(),
            "idHIS": $('#txtMABENHNHAN').val(),
            "hospitalId": cfObj.IQMS_MAKCBBD,

            "c_id": $("#txtCUSTOMERID6").val(),
            "firstName": $("#txtTENBENHNHAN").val(),
            "phone": $("#txtSDTBENHNHAN").val(),
            "email": "",
            "localPhone": $("#txtSDTBENHNHAN").val(),
            "genderNumber": $("#cboGIOITINHID").val(),
            "birthday": $("#txtNGAYSINH").val() == "" ? $("#txtNAMSINH").val() : $("#txtNGAYSINH").val(),

            "socialInsurance_insuranceNumber": $("#txtMA_BHYT").val(),
            "socialInsurance_primaryHealthcareUnitId": $("#txtMA_BHYT").val() == "" ? "" : _makcbbd.substring(0, 2) + '-' + _makcbbd.substring(2, 5),
            "socialInsurance_primaryHealthcareUnitName": $("#txtMA_BHYT").val() == "" ? "" : $('#cboMAKCBBD option:selected').text(),
            "socialInsurance_validDateFrom": $("#txtMA_BHYT").val() == "" ? "" : _tungay.substring(6, 10) + "-" + _tungay.substring(3, 5) + "-" + _tungay.substring(0, 2),							// yyyy-mm-dd
            "socialInsurance_validDateTo": $("#txtMA_BHYT").val() == "" ? "" : _denngay.substring(6, 10) + "-" + _denngay.substring(3, 5) + "-" + _denngay.substring(0, 2),								// yyyy-mm-dd
            "socialInsurance_data": $("#txtMA_BHYT").val() == "" ? "" : _logs_qrcode,

            "addressInfo_ward_wardId": $('#cboHC_XAID' + " option:selected").attr('extval0'),
            "addressInfo_ward_name": $('#cboHC_XAID option:selected').text(),

            "addressInfo_district_districtId": $('#cboHC_HUYENID' + " option:selected").attr('extval0'),
            "addressInfo_district_name": $('#cboHC_HUYENID option:selected').text(),

            "addressInfo_province_provinceId": $('#cboHC_TINHID' + " option:selected").attr('extval0'),
            "addressInfo_province_name": $('#cboHC_TINHID option:selected').text(),
            "addressInfo_province_idHisL2": $("#cboHC_TINHID").val(),

            "ethnicInfo_fullCode": $("#cboDANTOCID").val(),
            "ethnicInfo_idHisL2": $("#cboDANTOCID").val(),
            "ethnicInfo_name": $('#cboDANTOCID option:selected').text(),						// 1 qua portal; 2 qua app mobile

            "countriesInfo_fullCode": $("#cboQUOCGIAID").val(),
            "countriesInfo_idHisL2": $("#cboQUOCGIAID").val(),
            "countriesInfo_name": $('#cboQUOCGIAID option:selected').text(),

            "personalCards": ""
        };

        return _obj;
    }

    function _capnhat4(objData, param_arr_pk) {
        if ($("#hidHIENTHIGOIKHAM").val() == "5" && !$('#chkUUTIENKHAMID').is(":checked")) {
            var _checkuutien = $("#txtUUTIEN5").val() == "1" ? true : false;
            $('#chkUUTIENKHAMID').attr('checked', _checkuutien);
            objData["UUTIENKHAMID"] = _checkuutien == true ? "1" : "0";
        }
        // bo sung thong tin ghi chu sau khi check thanh cong thong tin;
        objData["GHICHU"] = "BHXH Check: " + $("#chkCHECKCONG").prop('checked')
            + ";BYT DAYDL: " + $("#txtBYTLAYDL").val()
            + ";BYT LDT: " + _transidLAMDUNGTHE;
//		objData["JSONBHXH"] = _kqJSONBHXH;

        objData["LOGS_QRCODE"] = _logs_qrcode.replaceAll('$', '');

        objData["BENHNHANSOURCE"] = _benhnhansource;

        if (_sudungnguonct == "1") {
            // NEU NHAP NGUON CT ROI THI K NHAP LOAI KHAM, HOPDONGID NUA;
            if ($("#cboNGUONCT").val() != "0") {
                objData.LOAIKHAM = "0";
                objData.HOPDONGID = "0";
            }
        }
        objData["CMTND_CAPNGAY"] = $("#txtNGAYCAPCMND").val();
        objData["CMTND_DIADIEMCAP"] = $("#txtNOICAPCMND").val();
        //dannd_L2PT-34927
        if (nhanthuochen == '1') {
            objData["NGAY_CAPGIAY"] = $('#txtNGAY_CAPGIAY').val();
            objData["NHANTHUOCTHEOHEN"] = $('#chkNHANTHUOCTHEOHEN').is(":checked") ? "1" : "0";
            objData["BACSIHOICHAN"] = '';
        } else if (nhanthuochen == '2') {
            objData["DV_CAPGIAY"] = $('#txtDV_CAPGIAY1').val();
            objData["NGAY_CAPGIAY"] = $('#txtNGAY_CAPGIAY1').val();
            if ($('input:radio[name="radNHANTHUOC"]').is(':checked')) {
                objData["NHANTHUOCTHEOHEN"] = $.find("[name='radNHANTHUOC']:checked")[0].value;
            }
            objData["BACSIHOICHAN"] = $('#cboBACSIHOICHAN').val();
        }
        objData["CHECKTTHCBN"] = CHECKTTHCBN;
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT01T002.LUUTT", JSON.stringify(objData) + '$' + JSON.stringify(param_arr_pk));
        var rets = ret.split(',');

        if (rets.length > 1) {
            _disableBHYT($('#cboDTBNID').val());

            //============= Day len cong BYT thong tin tiep nhan moi;
            if ($("#txtBYTLAYDL").val() == "1") {
                var actionType = $('#hidBENHNHANID').val() == "" ? "0" : "1";
                var objFirst = createObjectCHECKIN(rets[4], "001", i_u1, actionType); 			// tao dau vao day du du lieu
                var objHeader = XML_BYT_TaoHeader(objFirst); 										// tao doi tuong header;
                var objCHECKIN = XML_BYT_TaoTheCHECKIN(objFirst); 									// tao the
                var obj3 = XML_BYT_TaoKhung(objHeader, objCHECKIN, "1"); 											// tao JSON full => XML
                var resultCongBYT = XML_BYT_guiHSNV($("#txtBYTURL").val(), i_u1, i_p1, i_u1, "01", "1", obj3);
                // 82023/123a@
                if (resultCongBYT.length > 0) {
                    var arr = resultCongBYT.split(';');
                    if (arr[0] != "0") {
                        DlgUtil.showMsg("Cổng BYT: " + arr[1]);
                    }
                } else {
                    DlgUtil.showMsg("Gửi thông tin Cổng BYT thất bại, yêu cầu kiểm tra lại thông tin. ");
                }
            }

            _kqJSONBHXH = ""; 			// reset thong tin sau khi luu xong;
            $("#txtNGAYTIEPNHAN").val(_ngayhientaict);
            $('#hidKHAMBENHID').val(rets[0]);
            $('#txtMABENHNHAN').val(rets[1]);
            $('#hidPHONGKHAMDANGKYID').val(rets[2]);
            $('#hidTIEPNHANID').val(rets[3]);
            $('#hidHOSOBENHANID').val(rets[4]);
            $('#hidBENHNHANID').val(rets[5]);
            $('#hidBHYTID').val(rets[6]);
            $('#hidMAUBENHPHAMID').val(rets[7]);
            $('#hidDICHVUKHAMBENHID').val(rets[8]);
            $('#hidDOITUONGBENHNHANID').val($('#cboDTBNID').val());
            $("#txtSOCAPCUU").val(rets[11]);
            $("#hidLOAIGOIID").val(rets[13]);
            $('#hidTRANGTHAIKHAMBENH').val(rets[14]);
            cbThanhToanVncare($("#hidTIEPNHANID").val());

            _disableControl(['txtMABENHNHAN'], true);
            _disableControl(['toolbarIdbtnTreat', 'toolbarIdbtnhandling', 'toolbarIdbbtnLSKB',
                'toolbarIdbtnHoaHong', 'toolbarIdbtnThuTien', 'btnSinhTon', 'btnChupHinh',
                'btnKhamNhieuPhong', 'toolbarIdbtnThuTienKhac', 'toolbarIdtreat_2'], false);
            if (_ansautiepdon == "1") {
                _disableControl(['btnLuu', 'btnKyLuuIn'], true);
            }
            setErrValidate('txtTHOIGIANLICHHEN');

            if (_goikhamkcb != "0" && _goikhamkcbid != "0") {
                var objData = new Object();

                objData["HOSOBENHANID"] = $("#hidHOSOBENHANID").val();
                objData["GKBNID"] = _goikhamkcbid;
                objData["TYPE"] = "2";

                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D192.UPD", JSON.stringify(objData));
                if (ret == "1") {
                    DlgUtil.showMsg("Cập nhật gói khám cho bệnh nhân thành công");
                } else if (ret == "2") {
                    DlgUtil.showMsg("Bệnh nhân này đã có thông tin gói khám được gán trước đó. ");
                } else if (ret == "-3") {
                    DlgUtil.showMsg("Tạo dịch vụ thu khác gói khám không thành công. ", undefined, undefined, "error");
                } else {
                    DlgUtil.showMsg("Cập nhật thông tin không thành công. ", undefined, undefined, "error");
                }

            }

            // hien thi form thu vien phi ngay sau khi tiep nhan chi khi: benh nhan la vien phi thuan, cau hinh dc bat;
            if (cfObj.NGT_SINHQRCODEVPI == 1) {
                var opts = new Object();
                var DVTTID = cfObj.VPI_ID_DVTT;
                opts.TIEPNHANID = rets[3];
                opts.KHOAID_DN = _opts._khoaid; // khoa đăng nhập
                opts.PHONGID_DN = _opts.phongid; // phòng đăng nhập
                opts.LOAIPHIEUTHUID = "6"; // tạm thời fix cứng
                opts.HINHTHUCTHANHTOAN = "12"; // tạm thời fix cứng
                opts.TEN_HTTT = "Quét QRCODE"; // tạm thời fix cứng
                opts.DOITUONGDV = "0"; // tạm thời fix cứng
                opts.THUKHAC = "0"; // tạm thời fix cứng
                opts.CHILAPHD = "0"; // tạm thời fix cứng
                opts._loaihd = "0"; // tạm thời fix cứng
                opts._nhomtt_pt = "0"; // tạm thời fix cứng
                opts._id_dvtt = DVTTID; // cấu hình: VPI_ID_DVTT
                opts._sotaikhoan_bv = ""; // để trống
                opts.CashierID = _opts.user_id; // người thu id
                opts.CashierName = _opts.full_name; // tên người thu
                qrcode_vpi = getQRCode(opts);
            }
            if ($("#hidVIENPHIHIENTHIPOPUP").val() == "1"
                && $("#cboDTBNID").val() == "2"
                && $('#cboDTBNID' + " option:selected").attr('extval0') == "2") {
                var myVar = {
                    tiepnhanid: $('#hidTIEPNHANID').val(),
                    dichvukhambenhid: $('#hidDICHVUKHAMBENHID').val(),
                    modetiepnhan: "1"							// che do refresh lai trang;
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgCV", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T009_thutien"
                    , myVar, "Thu tiền khám", 1200, 600);
                DlgUtil.open("dlgCV");
            } else if (_batbuocSinhTon.indexOf("100,") != "-1") {
                // Tiep nhan lan dau tien: bat popup sinh ton sau khi luu thanh cong;
                var param = {
                    tuoi: $('#txtTUOI').val(),
                    dvtuoi: $('#cboDVTUOI').val(),
                    khambenhid: $('#hidKHAMBENHID').val(),
                    trangthaikhambenh: "1", 		// cho kham;
                    phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                    batbuocsinhton: 100 //,
                    //chieucao : chieucaocu,
                    //diungthuoc : diungthuoccu
                };
                _showDialog("ngoaitru/NGT01T001_sinhton", param, 'THÔNG TIN DẤU HIỆU SINH TỒN', 800, 600);
            } else {
                var msggg = 'Cập nhật thông tin bệnh nhân thành công. ';
                msggg = $("#txtTRAVEMABENHAN").val() == "1" ? msggg + "Mã bệnh án: " + rets[9] : msggg;
                $('#msgCapnhat').text(msggg);
                DlgUtil.showMsg(msggg);
                $('#tabCongKhamTab').text("Tiền công khám(1)");

                // L2PT-8640 - SONDN - 09/09/2019
                if (_hd == "2") {
                    // sondn L2PT-31554
                    if (cfObj.NGT_TN_CAPCUU == "0") {
                        setTimeout(function () {
                            window.location.replace("/vnpthis/main/manager.jsp?func=../ngoaitru/NGT02K001_KB_MHC&type=2");
                        }, 5000);
                    }
                    // end sondn L2PT-31554
                } else {
                    _inPhieuKham(1);
                    setTimeout(function () {
                        $('#btnTiepTheo').trigger('click');
                    }, _timeOutRefresh);

                    // Tu dong in phieu thu khac truong hop bat cau hinh va tra ve maubenhphamid cua thu khac;
                    if ($("#hidTINHTHUKHAC").val() == "1"
                        && ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6")
                        && Number(rets[10]) > 0) {
                        var par = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: rets[10]
                        }, {
                            name: 'i_hid',
                            type: 'String',
                            value: opt.hospital_id
                        }, {
                            name: 'i_sch',
                            type: 'String',
                            value: opt.db_schema
                        }];
                        openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5", "pdf", par);
                    }
                }

            }

            //hunglv L2PT-8058 in tự động cho đk lạng sơn
            if ((_options.hospital_id == 987 || _options.hospital_id == 28600 || _options.hospital_id == 924)
                && $("#chkDAGIUTHEBHYT").prop('checked') == true && $("#chkDAGIUTHEBHYT").prop('disabled') == false) {
                var par = [{
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];
                //openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par);
                CommonUtil.openReportGet('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par, true, false);
            }

            // sondn L2PT-5119: tiep nhan thanh cong goi ksk ca nhan thi day hl7;
            if (_kskcnhl7 == "1" && RIS_CONNECTION_TYPE == "7" && $("#hidLOAIGOIID").val() == "1") {
                var _obj = new Object();
                _obj.KHAMBENHID = $('#hidKHAMBENHID').val();
                _obj.PHONGKHAMDANGKYID = $('#hidPHONGKHAMDANGKYID').val();

                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01.KSKCNHL7", JSON.stringify(_obj));
                if (data_ar != null && data_ar.length > 0) {
                    for (i = 0; i < data_ar.length; i++) {
                        if (data_ar[i].LOAINHOMMAUBENHPHAM == "2") {						// chi day CĐHA
                            ajaxSvc.HL7_Gateway.sendRequest(data_ar[i].SOPHIEU, "NW");
                        }
                    }
                }
            }
            // end sondn L2PT-5119

            // BVTM-5459
            if (cfObj.NGT_DAY_DL_EMR == '1') {
                //var _msg = day_emr($("#hidHOSOBENHANID").val(), false);
                CommonUtil.sendAllEmr($("#hidHOSOBENHANID").val(), false);
            }

            // Goi so QMS Thanh Cong;
            if (_sudungiqms == "1" && $("#txtTRANSACTIONID6").val() != "" && $("#txtCUSTOMERID6").val() != "") {
                var _objHoanThanh = _taoiqms_hoanthanh();
                var ret = _IQMS_HOANTHANH($("#txtTRANSACTIONID6").val(), $("#txtCUSTOMERID6").val(), $('#txtMABENHNHAN').val(), JSON.stringify(_objHoanThanh));
//				var ret = _IQMS_HOANTHANH ($("#txtTRANSACTIONID6").val(), $("#txtCUSTOMERID6").val(), $('#txtMABENHNHAN').val());

                // ghi logs iQMS truong hop thanh cong;
                var _objj = new Object();
                _objj.JOBTYPE = "HOANTHANH " + $('#txtMABENHNHAN').val();
                _objj.TRANSACTIONID = $("#txtTRANSACTIONID6").val();
                _objj.CUSTOMERID = $("#txtCUSTOMERID6").val();
                _objj.JSONDATA = ret.data;
                var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("IQMS.LOGS", JSON.stringify(_objj));

            }
            // End Goi so QMS Thanh Cong;
            // if(_benhnhansource == "iqms"
            // 	&& $("#txtSDTBENHNHAN").val()!= ''
            // 	&& $("#txtSDTBENHNHAN").val()!= 'null'){
            // 	if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'AUTO_LIENKET_IQMS_TN') == 1){
            // 		lienketVncare($("#hidBENHNHANID").val(),$("#txtSDTBENHNHAN").val());
            // 	}
            // }
            if (cfObj.AUTO_LIENKET_IQMS_TN == 1) {
                lienketVncare($("#hidBENHNHANID").val(), $("#txtSDTBENHNHAN").val());
            }
            gw_tiepnhankham($('#hidTIEPNHANID').val());//L2PT-40477
        } else if (ret == "the_ko_hop_le") {
            $('#msgCapnhat').text('Thẻ BHYT không hợp lệ');
            setErrValidate('txtMA_BHYT');
            DlgUtil.showMsg('Thẻ BHYT không hợp lệ');
        } else if (ret == "da_tiepnhan_pk") {
            setErrValidate('txtMA_BHYT');
            DlgUtil.showMsg('Bệnh nhân đã tiếp nhận vào phòng khám trong ngày');
            $('#msgCapnhat').text('Bệnh nhân đã tiếp nhận vào phòng khám trong ngày');
        } else if (ret == "dakhamtrongngay") {
            setErrValidate('txtMA_BHYT');
            DlgUtil.showMsg('Bệnh nhân đã đăng ký khám trong ngày');
            $('#msgCapnhat').text('Bệnh nhân đã đăng ký khám trong ngày');
        } else if (ret == "kocodvcon") {
            DlgUtil.showMsg('Không có dịch vụ con trong goi');
        } else if (ret == "vi_pham_tt_kham") {
            DlgUtil.showMsg('Bệnh nhân đang khám hoặc đang điều trị, không tiếp nhận lại được');
        } else if (ret == "dvdathanhtoan") {
            DlgUtil.showMsg('Dịch vụ yêu cầu đã thanh toán, phải hủy hóa đơn nếu muốn cập nhật dịch vụ khác');
            $('#msgCapnhat').text('Dịch vụ yêu cầu đã thanh toán, phải hủy hóa đơn nếu muốn cập nhật dịch vụ khác');
        } else if (ret == "loitenbn") {
            $('#txtTENBENHNHAN').focus();
            $('#txtTENBENHNHAN').select();
            DlgUtil.showMsg('Tên bệnh nhân có chứa ký tự đặc biệt, hãy kiểm tra lại');
            $('#msgCapnhat').text('Tên bệnh nhân có chứa ký tự đặc biệt, hãy kiểm tra lại');
        } else if (ret == "loidiachibn") {
            $('#txtDIACHI').focus();
            $('#txtDIACHI').select();
            DlgUtil.showMsg('Địa chỉ bệnh nhân có chứa ký tự đặc biệt, hãy kiểm tra lại');
            $('#msgCapnhat').text('Địa chỉ bệnh nhân có chứa ký tự đặc biệt, hãy kiểm tra lại');
        } else if (ret == "loidiachibhyt") {
            $('#txtDIACHI_BHYT').focus();
            $('#txtDIACHI_BHYT').select();
            DlgUtil.showMsg('Địa chỉ BHYT bệnh nhân có chứa ký tự đặc biệt, hãy kiểm tra lại');
            $('#msgCapnhat').text('Địa chỉ BHYT bệnh nhân có chứa ký tự đặc biệt, hãy kiểm tra lại');
        } else if (ret == "kocapnhatbncu") {
            DlgUtil.showMsg('Không thể cập nhật lại thông tin bệnh nhân đã tiếp đón vào ngày khác hiện tại');
            $('#msgCapnhat').text('Không thể cập nhật lại thông tin bệnh nhân đã tiếp đón vào ngày khác hiện tại');
        } else if (ret.length > 21 && ret.substring(0, 20) == "chantiepnhannhapkhoa") {
            var retts = ret.split('@');
            DlgUtil.showMsg('Bệnh nhân này đang chuẩn bị nhập khoa, không được tiếp nhận. Mã bệnh án liên quan: ' + retts[1]);
        } else if (ret.length > 16 && ret.substring(0, 15) == 'dieutringoaitru') {
            var retts = ret.split('@');
            var msggg = $("#hidHisId").val() == retts[1]
                ? "Bệnh nhân chưa kết thúc khám bệnh tại đơn vị này. "
                : "Bệnh nhân chưa kết thúc khám bệnh tại đơn vị: " + retts[2];
            DlgUtil.showMsg(msggg);
            $('#msgCapnhat').text(msggg);
        } else if (ret.length > 14 && ret.substring(0, 13) == 'dieutrinoitru') {
            var retts = ret.split('@');
            var msggg = $("#hidHisId").val() == retts[1]
                ? "Bệnh nhân chưa kết thúc điều trị nội trú tại đơn vị này. "
                : "Bệnh nhân chưa kết thúc điều trị nội trú tại đơn vị: " + retts[2];
            DlgUtil.showMsg(msggg);
            $('#msgCapnhat').text(msggg);
        } else if (ret.length > 16 && ret.substring(0, 15) == 'vi_pham_tt_kham') {
            var retts = ret.split('@');
            if (retts[2] == 'tbchuaktkham') {
                var msggg = 'Bệnh nhân đang khám hoặc đang điều trị, không tiếp nhận lại được, mã bệnh án liên quan: ' + retts[1] + ' , ngày điều trị: '+ retts[3];
            } else {
                var msggg = $("#hidHisId").val() == retts[1]
                    ? "Bệnh nhân chưa kết thúc khám tại đơn vị này. "
                    : "Bệnh nhân chưa kết thúc khám tại đơn vị: " + retts[2];
            }
            DlgUtil.showMsg(msggg);
            $('#msgCapnhat').text(msggg);
        } else if (ret.length > 13 && ret.substring(0, 12) == 'dieutribvcon') {
            var retts = ret.split('@');
            var msggg = $("#hidHisId").val() == retts[1]
                ? "Bệnh nhân đang điều trị ngoại trú / nhập viện tại đơn vị này. "
                : "Bệnh nhân đang điều trị ngoại trú / nhập viện tại đơn vị: " + retts[2];
            DlgUtil.showMsg(msggg);
            $('#msgCapnhat').text(msggg);
        } else if (ret == "da_thu_tien") {
            DlgUtil.showMsg('Công khám đã thu tiền, không cho phép cập nhật. ');
            $('#msgCapnhat').text('Công khám đã thu tiền, không cho phép cập nhật. ');
        } else if (ret.substring(0, 10) == "datronvien") {
            var retts = ret.split('@');
            DlgUtil.showMsg('Bệnh nhân đã trốn viện lần trước với mã bệnh án ' + retts[1] + '. Yêu cầu nộp tiền để tiếp tục khám. ');
            $('#msgCapnhat').text('Bệnh nhân đã trốn viện lần trước với mã bệnh án ' + retts[1] + '. Yêu cầu nộp tiền để tiếp tục khám.');
        } else if (ret == "kothuphieudc") {
            DlgUtil.showMsg('Tiếp nhận bệnh nhân thành công nhưng chưa thu được tiền công khám, bệnh nhân qua quầy viện phí thanh toán');
            $('#msgCapnhat').text('Tiếp nhận bệnh nhân thành công nhưng chưa thu được tiền công khám, bệnh nhân qua quầy viện phí thanh toán');
        } else if (ret == "hetphieuthu") {
            DlgUtil.showMsg('Tiếp nhận bệnh nhân thành công nhưng chưa thu được tiền công khám, do hết phiếu thu/hóa đơn');
            $('#msgCapnhat').text('Tiếp nhận bệnh nhân thành công nhưng chưa thu được tiền công khám, do hết phiếu thu/hóa đơn');
        } else if (ret == "chanxutri") {
            DlgUtil.showMsg('Bệnh nhân đã có xử trí tử vong, không thể tiếp nhận được. ');
            $('#msgCapnhat').text('Bệnh nhân đã có xử trí tử vong, không thể tiếp nhận được. ');
        } else if (ret == "loikcbbd") {
            DlgUtil.showMsg('Mã KCBBĐ chưa có trong dữ liệu danh mục, yêu cầu kiểm tra bổ sung. ');
            $('#msgCapnhat').text('Mã KCBBĐ chưa có trong dữ liệu danh mục, yêu cầu kiểm tra bổ sung. ');
        } else if (ret == "loimanoichuyen") {
            DlgUtil.showMsg('Mã nơi chuyển chưa có trong dữ liệu danh mục, yêu cầu kiểm tra bổ sung. ');
            $('#msgCapnhat').text('Mã nơi chuyển chưa có trong dữ liệu danh mục, yêu cầu kiểm tra bổ sung. ');
        } else if (ret == "loikskcn_phongktontai") {
            DlgUtil.showMsg('Tồn tại phòng khám trong gói đang không hoạt động / đã xóa. Yêu cầu kiểm tra lại');
            $('#msgCapnhat').text('Tồn tại phòng khám trong gói đang không hoạt động / đã xóa. Yêu cầu kiểm tra lại');
        } else if (ret == "loikskcn") {
            DlgUtil.showMsg('Lỗi tiếp nhận KSK cá nhân. ', undefined, undefined, "error");
            $('#msgCapnhat').text('Lỗi tiếp nhận KSK cá nhân. ');
        } else if (ret == "loikskcn_capnhat") {
            DlgUtil.showMsg('Đã tiếp nhận loại khám Khám Sức Khỏe cả nhân. Yêu cầu cập nhật lại trong các chức năng khác.');
            $('#msgCapnhat').text('Đã tiếp nhận loại khám Khám Sức Khỏe cả nhân. Yêu cầu cập nhật lại trong các chức năng khác.');

        } else if (ret == "loicongkhambdhni") {
            DlgUtil.showMsg('Lỗi cập nhật tỷ lệ công khám.', undefined, undefined, "error");
            $('#msgCapnhat').text('Lỗi cập nhật tỷ lệ công khám.');
        } else if (ret == "chuaduyetketoan") {
            DlgUtil.showMsg('Bệnh nhân chưa duyệt kế toán, không thể tiếp nhận. ');
            $('#msgCapnhat').text('Bệnh nhân chưa duyệt kế toán, không thể tiếp nhận. ');
        } else if (ret == "loityledv") {
            DlgUtil.showMsg('Lỗi tỷ lệ công khám 30%', undefined, undefined, "error");
            $('#msgCapnhat').text('Lỗi tỷ lệ công khám 30%');
        } else if (ret.substring(0, 9) == 'trung_the') {
            var retts = ret.split('@');
            setErrValidate('txtMA_BHYT');
            DlgUtil.showMsg('Trùng thẻ BHYT, mã thẻ hiện đang sử dụng cho bệnh nhân ' + retts[1] + '. Đề nghị thực hiện map mã bệnh nhân.', undefined, undefined, "error");
            $('#msgCapnhat').text('Trùng thẻ BHYT, mã thẻ hiện đang sử dụng cho bệnh nhân ' + retts[1] + '. Đề nghị thực hiện map mã bệnh nhân.');
        } else if (ret == 'trungthoigian') {
            DlgUtil.showMsg('Thời gian tiếp nhận trùng với bệnh nhân khác yêu cầu chỉnh sửa lại thời gian tiếp nhận hoặc thử lại trong ít phút!', undefined, undefined, "error");
            $('#msgCapnhat').text('Thời gian tiếp nhận trùng với bệnh nhân khác yêu cầu chỉnh sửa lại thời gian tiếp nhận hoặc thử lại trong ít phút!');
        } else if (ret.substring(0, 10) == "loi_tthcbn") {
            var retts = ret.split('@');
            if (cfObj.NGT_CHECK_TTHC_BN == 1) {
                DlgUtil.showMsg(retts[1]);
                $('#msgCapnhat').text(retts[1]);
            } else {
                DlgUtil.showConfirm(retts[1] + " Bạn có muốn tiếp nhận bệnh nhân này?", function (flag) {
                    if (flag) {
                        CHECKTTHCBN = 1;
                        _capnhat4(objData, param_arr_pk);
                    } else {
                        return false;
                    }
                });
                return false;
            }
        } else {
            DlgUtil.showMsg('Cập nhật thông tin không thành công ' + ret, undefined, undefined, "error");
            $('#msgCapnhat').text('Cập nhật thông tin không thành công');
        }
    }

    function _changeDOITUONG() {
        _disableBHYT($('#cboDTBNID').val());
        if ($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == "6"
            || (_bhytdtmienphi == "1" && $('#cboDTBNID').val() == "5")) {	// sondn L2PT-6385

            //$('#chkDAGIUTHEBHYT').attr('checked', true);
            _setgiatribatbuoc(['lblSoThe', 'lblTuNgay', 'lblDenNgay', 'lblTuyen', 'lblKCBBD', 'lblDCBHYT'], '(*)');

            sql_par = [];
            sql_par.push({"name": "[0]", "value": _options.hospital_code});

            if (_setkcbbd == "0") {
                $('#txtMA_KCBBD').val(_options.hospital_code);
                ComboUtil.getComboTag("cboMAKCBBD", "DMC.BVKCBBD", sql_par, "", "", "sql", "", "");
            }
        } else {
            var d = new Date();
            var year = d.getFullYear();
            $('#txtBHYT_BD').val('01/01/' + year);
            //$('#txtBHYT_KT').val('31/12/'+year);
            $('#txtDIACHI_BHYT').val('');
            $('#cboDT_SINHSONG').val(0);

            $('#txtTKBHYT_LoaiID').val($("#hidTUYENMACDINH").val());
            $('#cboBHYT_LoaiID').val($("#hidTUYENMACDINH").val());

            $('#txtQUYENLOI').val('');
            $('#txtMUCHUONG').val('Ngoại (0%)-Nội (0%)');
            $('#txtMA_KCBBD').val('');
            $('#cboMAKCBBD').text('');
            $('#txtMA_BHYT').val('');
            $('#txtTKMACHANDOANTUYENDUOI').val('');
            $('#txtCHANDOANTUYENDUOI').val('');
            $('#txtTKMANOIGIOITHIEU').val('');
            $('#cboMANOIGIOITHIEU').text('');
            $('#hidMUCHUONG_NGT').val('0');
            $('#chkDU5NAM6THANGLUONGCOBAN').attr('checked', false);
            $('#chkTRADU6THANGLCB').attr('checked', false);
            $('#chkDAGIUTHEBHYT').attr('checked', false);
            $('#chkCOGIAYKS').attr('checked', false);
            $('#chkNOTHE').attr('checked', false);
            $("#cboDOITUONGDB").val(0);
            $("#txtMADOITUONGNGHEO").val("");
            _setgiatribatbuoc(['lblSoThe', 'lblTuNgay', 'lblDenNgay', 'lblTuyen', 'lblKCBBD', 'lblDCBHYT'], '');
            _setgiatribatbuoc(['lblHuyen', 'lblXa'], '(*)');
        }
    }

    function _tanggiam_stt(id, kieu) { // kieu 1: tang, 2: giam.
        var value = $('#' + id).val();
        if (value < 1) {
            $('#' + id).val(1);
        } else {
            if (kieu == 1) {
                value = parseInt(value) + 1;
            } else {
                value = parseInt(value) - 1;
                if (value < 1) {
                    value = 1;
                }
            }
            $('#' + id).val(value);
        }
    }

    function _setgiatribatbuoc(ids, value) {
        for (var i = 0; i < ids.length; i++) {
            $('#' + ids[i]).text(value);
        }
    }

    function _inPhieuKham(mode) {
        var objData = new Object();
        FormUtil.setFormToObject("tabTiepNhan", "", objData);
        var par = [{
            name: 'khambenhid',
            type: 'String',
            value: objData["KHAMBENHID"]
        }, {
            name: 'phongid',
            type: 'String',
            value: $('#cboPHONGKHAMID').val()
        }, {
            name: 'i_sch',
            type: 'String',
            value: _options.db_schema
        }, {
            name: 'barcode',
            type: 'String',
            value: qrcode_vpi
        }];

        var par1 = [{
            name: 'khambenhid',
            type: 'String',
            value: objData["KHAMBENHID"]
        }];

        if (mode == 0) {
            // Only Preview
            if (objData["KHAMBENHID"] != null && objData["KHAMBENHID"].length > 0) {
                var dt = cfObj.NGT_IN_2_PHIEUKHAM;
                var value = dt.split(";");
//							if(_options.hospital_id==951){
                if (value.includes(_options.hospital_id)) {
                    openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                    if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                        openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                    }
                } else if (_options.hospital_id == 1014) {
                    if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                        openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT", "pdf", par1);
                    } else {
                        openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par1);
                    }
                } else if (_options.hospital_id == 1077) {
                    openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1077', 'pdf', par1);
                } else if (_options.hospital_id == 996) {
                    openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {															//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                    if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//						openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4", "pdf", par1);
                        // SONDN 231019 L2PT-9789
                        openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                        // END SONDN 231019 L2PT-9789
                    }
                } else if (_options.hospital_id == 1020 || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                    // sondn L2PT-24707,L2PT-24716
                    CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                    // dannd L2PT-L2PT-1573
                    CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                    CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                    // sondn L2PT-28825
                    if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                        CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                    }
                } else if (_options.hospital_id == 30680) {
                    if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                        CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                    } else {
                        openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                    }
                } else if (_options.hospital_id == 1007) {				// snvpc;
                    CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                } else if (_options.hospital_id == 1216) {
                    rpName = "VNPTHIS_IN_A6_D_NGT_STT";
                    rpName += ".pdf";
                    CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                } else {
                    var par_sql = ['NGT_IN_PREVIEW'];
                    var ch1 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                    if (ch1 == 1) {
                        CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                    } else {
                        CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                    }
                }
            } else {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
            }
            return;
        }

        if (mode == 1) {
            // Xuat ra may in, preview hoac khong in;
            if (_inphieukhamtudong == "1") {									// cau hinh he thong;
                if ($("#hidTUDONGINPHIEUKHAM").val() == "1") {				// cau hinh nguoi dung;
                    if (objData["KHAMBENHID"] != null && objData["KHAMBENHID"].length > 0) {
                        var rpName = "VNPTHIS_IN_A7_PhieuKB_";
                        rpName += objData.MABENHNHAN;
                        rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));

                        if ($("#txtFILEEXPORTTYPE").val() == "docx" || $("#txtFILEEXPORTTYPE").val() == "rtf") {
                            rpName += "." + $("#txtFILEEXPORTTYPE").val();
                            var par_sql = ['NGT_IN_2_PHIEUKHAM'];
                            var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                            var value = dt.split(";");
                            //							if(_options.hospital_id==951){
                            if (value.includes(_options.hospital_id)) {
                                openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                                if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                    openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                                }
                            } else if (_options.hospital_id == 1014) {
                                if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                    CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT', 'pdf', par);
                                } else {
                                    CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par);
                                }
                            } else if (_options.hospital_id == 1077) {
                                CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par);
                            } else if (_options.hospital_id == 996) {
                                openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                            } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                                if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//									CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par);
                                    // SONDN 231019 L2PT-9789
                                    openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                                    // END SONDN 231019 L2PT-9789
                                }
                            } else if (_options.hospital_id == 1020 || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                                // sondn L2PT-24707,L2PT-24716
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                                // dannd L2PT-L2PT-1573
                                CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                            } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                                CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                            } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                                // sondn L2PT-28825
                                if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                    CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                                }
                            } else if (_options.hospital_id == 30680) {
                                if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                    CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                                } else {
                                    openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                                }
                            } else if (_options.hospital_id == 1007) {				// snvpc;
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else {
                                CommonUtil.inPhieu('window', 'NGT_STT', $("#txtFILEEXPORTTYPE").val(), par, rpName);
                            }
                        } else {
                            rpName += ".pdf";
                            var par_sql = ['NGT_IN_2_PHIEUKHAM'];
                            var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                            var value = dt.split(";");
                            //							if(_options.hospital_id==951){
                            if (value.includes(_options.hospital_id)) {
                                openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                                if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                    openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                                }
                            } else if (_options.hospital_id == 1014) {
                                if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                    openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT", "pdf", par1);
                                } else {
                                    openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par1);
                                }
                            } else if (_options.hospital_id == 1077) {
                                openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1077', 'pdf', par1);
                            } else if (_options.hospital_id == 996) {
                                openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                            } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {									//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                                if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//									openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4", "pdf", par1);
                                    // SONDN 231019 L2PT-9789
                                    openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                                    // END SONDN 231019 L2PT-9789
                                }
                            } else if (_options.hospital_id == 1020 || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                                // sondn L2PT-24707,L2PT-24716
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                                // dannd L2PT-L2PT-1573
                                CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                            } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                                CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                            } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                                // sondn L2PT-28825
                                if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                    CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                                }
                            } else if (_options.hospital_id == 30680) {
                                if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                    CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                                } else {
                                    openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                                }
                            } else if (_options.hospital_id == 1007) {				// snvpc;
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else if (_options.hospital_id == 1216) {
                                rpName = "VNPTHIS_IN_A6_D_NGT_STT";
                                rpName += ".pdf";
                                CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                            } else {
                                var par_sql = ['NGT_IN_PREVIEW'];
                                var ch1 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                                if (ch1 == 1) {
                                    CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                                } else {
                                    CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                                }
                            }
                        }
                    } else {
                        DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                    }
                } else {
                    if (objData["KHAMBENHID"] != null && objData["KHAMBENHID"].length > 0) {
                        var par_sql = ['NGT_IN_2_PHIEUKHAM'];
                        var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                        var value = dt.split(";");
                        //							if(_options.hospital_id==951){
                        if (value.includes(_options.hospital_id)) {
                            openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                            if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                            }
                        } else if (_options.hospital_id == 1014) {
                            if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT', 'pdf', par);
                            } else {
                                CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par);
                            }
                        } else if (_options.hospital_id == 1077) {
                            CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par);
                        } else if (_options.hospital_id == 996) {
                            openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                        } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {								//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//								CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par);
                                // SONDN 231019 L2PT-9789
                                openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                                // END SONDN 231019 L2PT-9789
                            }
                        } else if (_options.hospital_id == 1020 || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                            // sondn L2PT-24707,L2PT-24716
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                            // dannd L2PT-L2PT-1573
                            CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                        } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                            CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                        } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                            // sondn L2PT-28825
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                            }
                        } else if (_options.hospital_id == 30680) {
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else {
                                openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                            }
                        } else if (_options.hospital_id == 1007) {				// snvpc;
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else if (_options.hospital_id == 1216) {
                            rpName = "VNPTHIS_IN_A6_D_NGT_STT";
                            rpName += ".pdf";
                            CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                        } else {
                            var par_sql = ['NGT_IN_PREVIEW'];
                            var ch1 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                            if (ch1 == 1) {
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else {
                                CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                            }
                        }
                    } else {
                        DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                    }
                }
            }
            //hunglv L2PT-9144
            if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NGT_TIEPNHAN_IN_NHANTRABHYT') == 1)
                if ($('#hidDOITUONGBENHNHANID').val() == "1" || $('#hidDOITUONGBENHNHANID').val() == "6") {
                    _inPhieuTraTheBHYT();
                }

            return;
        }

        if (mode != 0 && mode != 1) {
            // Xuat ra may in hoac cho preview;
            if (_inphieukhamtudong == "1" && $("#hidTUDONGINPHIEUKHAM").val() == "1") {
                if (objData["KHAMBENHID"] != null && objData["KHAMBENHID"].length > 0) {
                    var rpName = "VNPTHIS_IN_A7_PhieuKB_";
                    rpName += objData.MABENHNHAN;
                    rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));

                    if ($("#txtFILEEXPORTTYPE").val() == "docx" || $("#txtFILEEXPORTTYPE").val() == "rtf") {
                        rpName += "." + $("#txtFILEEXPORTTYPE").val();

                        var par_sql = ['NGT_IN_2_PHIEUKHAM'];
                        var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                        var value = dt.split(";");
                        //							if(_options.hospital_id==951){
                        if (value.includes(_options.hospital_id)) {
                            openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                            if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                            }
                        } else if (_options.hospital_id == 1014) {
                            if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT', 'pdf', par);
                            } else {
                                CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par);
                            }
                        } else if (_options.hospital_id == 1077) {
                            CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par);
                        } else if (_options.hospital_id == 996) {
                            openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                        } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {										//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//								CommonUtil.inPhieu('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par);
                                // SONDN 231019 L2PT-9789
                                openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                                // END SONDN 231019 L2PT-9789
                            }
                        } else if (_options.hospital_id == 1020
                            || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                            // sondn L2PT-24707,L2PT-24716
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                            // dannd L2PT-L2PT-1573
                            CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                        } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                            CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                        } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                            // sondn L2PT-28825
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                            }
                        } else if (_options.hospital_id == 30680) {
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else {
                                openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                            }
                        } else if (_options.hospital_id == 1007) {				// snvpc;
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else {
                            CommonUtil.inPhieu('window', 'NGT_STT', $("#txtFILEEXPORTTYPE").val(), par, rpName);
                        }
                    } else {
                        rpName += ".pdf";
                        var par_sql = ['NGT_IN_2_PHIEUKHAM'];
                        var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                        var value = dt.split(";");
                        //							if(_options.hospital_id==951){
                        if (value.includes(_options.hospital_id)) {
                            openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                            if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                            }
                        } else if (_options.hospital_id == 1014) {
                            if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                                openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT", "pdf", par1);
                            } else {
                                openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par1);
                            }
                        } else if (_options.hospital_id == 1077) {
                            openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1077', 'pdf', par1);
                        } else if (_options.hospital_id == 996) {
                            openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                        } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {										//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//								openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par1);
                                // SONDN 231019 L2PT-9789
                                openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                                // END SONDN 231019 L2PT-9789
                            }
                        } else if (_options.hospital_id == 1020 || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                            // sondn L2PT-24707,L2PT-24716
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                            // dannd L2PT-L2PT-1573
                            CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                        } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                            CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                        } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                            // sondn L2PT-28825
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                            }
                        } else if (_options.hospital_id == 30680) {
                            if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                                CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                            } else {
                                openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                            }
                        } else if (_options.hospital_id == 1007) {				// snvpc;
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else {
                            CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                        }
                    }
                } else {
                    DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                }
            } else {
                if (objData["KHAMBENHID"] != null && objData["KHAMBENHID"].length > 0) {
                    var par_sql = ['NGT_IN_2_PHIEUKHAM'];
                    var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                    var value = dt.split(";");
                    //							if(_options.hospital_id==951){
                    if (value.includes(_options.hospital_id)) {
                        openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_951", "pdf", par1);
                        if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                            openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par1);
                        }
                    } else if (_options.hospital_id == 1014) {
                        if ($('#hidDOITUONGBENHNHANID').val() == "1") {
                            openReport('window', "NGT005_PHIEUDANGKYKHAMBENH_A4_1014_BHYT", "pdf", par1);
                        } else {
                            openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1014_DV', 'pdf', par1);
                        }
                    } else if (_options.hospital_id == 1077) {
                        openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4_1077', 'pdf', par1);
                    } else if (_options.hospital_id == 996) {
                        openReport('window', "PHIEU_KHAMBENH_A4", "pdf", par1);
                    } else if (_options.hospital_id == 987 || _options.hospital_id == 28600) {								//Đa Khoa Lạng Sơn //hunglv L2PT-8669
                        if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
//							openReport('window', 'NGT005_PHIEUDANGKYKHAMBENH_A4', 'pdf', par1);
                            // SONDN 231019 L2PT-9789
                            openReport('window', "PHIEU_KHAMBENH_THUPHI_A4", "pdf", par1, true);
                            // END SONDN 231019 L2PT-9789
                        }
                    } else if (_options.hospital_id == 1020 || _options.hospital_id == 923 || _options.hospital_id == 24560) {
                        // sondn L2PT-24707,L2PT-24716
                        CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                    } else if (_options.hospital_id == 1133 || _options.hospital_id == 24750) {
                        // dannd L2PT-L2PT-1573
                        CommonUtil.openReportGet('window', 'NGT_IN_PHIEUKHAM', 'pdf', par, true, true);
                    } else if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_IN_PHIEUKHAM') == '1') {//L2PT-121266
                        CommonUtil.inPhieu('window', 'NGT_IN_PHIEUKHAM', 'pdf', par);
                    } else if (_options.hospital_id == 26720 || _options.hospital_id == 32240) {
                        // sondn L2PT-28825
                        if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                            CommonUtil.openReportGet('window', 'PHIEU_KHAMBENH_THUPHI_A4_26720', 'pdf', par, true, true);
                        }
                    } else if (_options.hospital_id == 30680) {
                        if ($('#hidDOITUONGBENHNHANID').val() == "2" || $('#hidDOITUONGBENHNHANID').val() == "3") {
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else {
                            openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par1, true);
                        }
                    } else if (_options.hospital_id == 1007) {				// snvpc;
                        CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                    } else if (_options.hospital_id == 1216) {
                        rpName = "VNPTHIS_IN_A6_D_NGT_STT";
                        rpName += ".pdf";
                        CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                    } else {
                        var par_sql = ['NGT_IN_PREVIEW'];
                        var ch1 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par_sql.join('$'));
                        if (ch1 == 1) {
                            CommonUtil.openReportGet('window', 'NGT_STT', 'pdf', par, true, true);
                        } else {
                            CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par, rpName);
                        }
                        //CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par);
                    }
                } else {
                    DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                }
            }
        }
    }

    function _loadPhieukham() {
        //widget cho tab phieu khám
        $('#tabPhieuKham').ntu02d029_pdv({
            _grdSuatAn: 'grdSuatAn',
            _grdSuatAnChitiet: 'grdSuatAnChitiet',
            _khambenhid: $("#hidKHAMBENHID").val(),
            _benhnhanid: $("#hidBENHNHANID").val(),
            _lnmbp: '3',
            _loaidichvu: "1",
            _modeView: "0", // =1 chi view; !=1 la update
            _hosobenhanid: ""
        });

        //widget khoi tao grid danh sach CDHA
        $('#tabCDHA').ntu02d025_cdha({
            _gridCDHA: "grdCDHA",
            _gridCDHADetail: "grdCDHAChiTiet",
            _khambenhid: $("#hidKHAMBENHID").val(),
            _benhnhanid: $("#hidBENHNHANID").val(),
            _lnmbp: LNMBP_CDHA,
            _modeView: _flgModeView, // =1 chi view; !=1 la update
            _hosobenhanid: ""
        });

        //widget khoi tao grid danh sach xet nghiem
        $('#tabXetNghiem').ntu02d024_ttxn({
            _gridXnId: "grdXetNghiem",
            _gridXnDetailId: "grdXetNghiemChiTiet",
            _khambenhid: $("#hidKHAMBENHID").val(),
            _benhnhanid: $("#hidBENHNHANID").val(),
            _lnmbp: LNMBP_XetNghiem,
            _modeView: _flgModeView, // =1 chi view; !=1 la update
            _hosobenhanid: ""
        });

        //widget cho tab thu khac
        $("#tabPhieuThuKhacTab").on("click", function (e) {
            $('#tabPhieuThuKhac').ntu02d029_pdv({
                _grdSuatAn: 'grdSuatAn',
                _grdSuatAnChitiet: 'grdSuatAnChitiet',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '17',
                _loaidichvu: "1",
                _modeView: '0', // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        loadGridData();
    }

    // kiem tra sinh so the tre em
    function _kiemTraSinhSoTheBHYT() {
        var namsinh = $("#txtNGAYSINH").val().slice(-4);
        var namsinhht = jsonrpc.AjaxJson.getSystemDate('YYYY');
        if ((parseInt(namsinhht) - parseInt(namsinh)) >= 6 && $("#cboDVTUOI").val() == "1") {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Bệnh nhân không phải đối tượng trẻ em dưới 6 tuổi');
            return false;
        } else if ($("#txtNGAYSINH").val() == "") {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Bệnh nhân chưa có ngày sinh, hãy nhập ngày sinh');
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && !datetimeRegex.test($('#txtNGAYSINH').val())) {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Ngày sinh không đúng');
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && !checkDate($('#txtNGAYSINH').val())) {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Ngày sinh không đúng');
            return false;
        } else if ($("#cboHC_TINHID").val() == "" || $('#cboHC_TINHID').val() == null) {
            setErrValidate('cboHC_TINHID');
            DlgUtil.showMsg('Bệnh nhân chưa có mã tỉnh');
            return false;
        } else if ($('#cboHC_HUYENID').val() == "" || $('#cboHC_HUYENID').val() == null) {
            setErrValidate('cboHC_HUYENID');
            DlgUtil.showMsg('Hãy chọn huyện');
            return false;
        } else if (_b_flag_taothe_trem) {
            DlgUtil.showMsg('Bệnh nhân đã tạo mã thẻ một lần');
            return false;
        }

        // sondn L2PT-6385
        if (!($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6"
            || (_bhytdtmienphi == "1" && $("#cboDTBNID").val() == "5"))) {
            setErrValidate('cboDTBNID');
            DlgUtil.showMsg('Đối tượng này không cho phép nhập thẻ BHYT');
            return false;
        }

        return true;
    }

    // sinh so the bao hiem tre em
    function _sinhSoTheBHYT() {
        var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
        var mahuyen = (typeof $('#cboHC_HUYENID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_HUYENID' + " option:selected").attr('extval1') : "");
        var namsinh = $('#txtNGAYSINH').val();
        namsinh = namsinh.substring(0, 2) + namsinh.substring(3, 5) + namsinh.substring(namsinh.length - 1, namsinh.length);
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.SINHTHE.BHYT", matinh + '$' + mahuyen + '$' + namsinh);
        return ret;
    }

    function loadGridData() {
        var _tungay ;
        var _denngay;
        if (cfObj.NGT_TNTK_GIOPHUTGIAY == "1") {
            _tungay =  $("#txtTUTK").val().length <= 10 ? $("#txtTUTK").val() + " 00:00:00" : $("#txtTUTK").val();
            _denngay = $("#txtDENTK").val().length <= 10 ? $("#txtDENTK").val() + " 23:59:59" : $("#txtDENTK").val();
        } else {
            _tungay =  $("#txtTUTK").val();
            _denngay = $("#txtDENTK").val();
        }
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _tungay});
        sql_par.push({"name": "[1]", "value": _denngay});
        sql_par.push({"name": "[3]", "value": $("#cboTRANGTHAI").val()});
        sql_par.push({"name": "[4]", "value": $("#cboTKPK").val()});
        sql_par.push({"name": "[5]", "value": $("#cboDTBNID1").val()});

        var _subdtbnid = "0";
        if (doituong_khaibao == 1 && $("#cboDTBNID1").val() != "0") {
            _subdtbnid = $('#cboDTBNID1' + " option:selected").attr('extval0');
        }
        sql_par.push({"name": "[6]", "value": _subdtbnid});
        sql_par.push({"name": "[7]", "value": $("#cboKENHDK").val()});
        GridUtil.loadGridBySqlPage("grdDanhSachTiepNhan", "NGT.002", sql_par);
    }

    function _loadLSDT(b_benhnhanid) {
        var sql_par = b_benhnhanid + "$";
        $("#cboLS").empty();
        ComboUtil.getComboTag("cboLS", "NGT01T001.LSDT", sql_par, "", "", 'sp', "", false);
    }

    function loadGridThongke() {
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _options._khoaid});
        GridUtil.loadGridBySqlPage("grdThongKe", "NGT.GETTHONGKEPK", sql_par);
    }

//--------------------------------------------------------------------------------------------
//BIZ FUNCTIONS
//--------------------------------------------------------------------------------------------
    // kiểm tra control trước khi lưu thông tin
    function _kiemTra() {
        var ngaytn = moment($('#txtNGAYTIEPNHAN').val(), "DD-MM-YYYY");
        var ngayht = jsonrpc.AjaxJson.getSystemDate('DD-MM-YYYY');
        var exdate = moment(ngayht, "DD-MM-YYYY").add(-parseInt(_songayluiBHYT), 'days').format('YYYYMMDD');
        var tndate = moment(ngaytn).format('YYYYMMDD');

        var b_tu, b_den, b_ngaykham, b_ngaysinh, b_ngayhientai, b_ngaykham_daydu;
        if ($('#txtBHYT_BD').val().length >= 10)
            b_tu = $('#txtBHYT_BD').val().substr(6, 4) + $('#txtBHYT_BD').val().substr(3, 2) + $('#txtBHYT_BD').val().substr(0, 2);
        if ($('#txtBHYT_KT').val().length >= 10)
            b_den = $('#txtBHYT_KT').val().substr(6, 4) + $('#txtBHYT_KT').val().substr(3, 2) + $('#txtBHYT_KT').val().substr(0, 2);
        if ($('#txtNGAYTIEPNHAN').val().length >= 10) {
            b_ngaykham = $('#txtNGAYTIEPNHAN').val().substr(6, 4) + $('#txtNGAYTIEPNHAN').val().substr(3, 2) + $('#txtNGAYTIEPNHAN').val().substr(0, 2);
            b_ngaykham_daydu = $('#txtNGAYTIEPNHAN').val().substr(6, 4) + $('#txtNGAYTIEPNHAN').val().substr(3, 2) + $('#txtNGAYTIEPNHAN').val().substr(0, 2);
            //+ $('#txtNGAYTIEPNHAN').val().substr(11,2) + $('#txtNGAYTIEPNHAN').val().toString().substr(14,2);
        }

        if ($('#txtNGAYSINH').val().length >= 10)
            b_ngaysinh = $('#txtNGAYSINH').val().substr(6, 4) + $('#txtNGAYSINH').val().substr(3, 2) + $('#txtNGAYSINH').val().substr(0, 2);
        b_ngayhientai = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');

        var ngaysinh = $('#txtNGAYSINH').val();
        var tmp = ngaysinh.substring(ngaysinh.length - 4, ngaysinh.length) + ngaysinh.substring(3, 5) + ngaysinh.substring(0, 2);

        if ($('#txtTENBENHNHAN').val().trim().length == 0) {
            $('#txtTENBENHNHAN').val("");
            setErrValidate('txtTENBENHNHAN');
            DlgUtil.showMsg('Hãy nhập họ tên');
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length == 0 && $('#txtNAMSINH').val().trim().length == 0) {
            $('#txtNGAYSINH').val("");
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Hãy nhập ngày sinh');
            return false;
        } else if ($('#txtNGAYSINH').val() != "" && tmp < 19000101) {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Ngày sinh không được nhỏ hơn ngày 01/01/1900');
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && !datetimeRegex.test($('#txtNGAYSINH').val())) {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Ngày sinh không đúng');
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && !checkDate($('#txtNGAYSINH').val())) {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Ngày sinh không đúng');
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && b_ngaysinh > b_ngaykham) {
            setErrValidate('txtNGAYSINH');
            DlgUtil.showMsg('Ngày sinh nhỏ hơn ngày khám');
            return false;
        } else if ($('#txtNAMSINH').val().trim().length == 0) {
            $('#txtNAMSINH').val("");
            setErrValidate('txtNAMSINH');
            DlgUtil.showMsg('Hãy nhập năm sinh');
            return false;
        } else if ($('#txtNAMSINH').val().trim().length > 0 && !isNumberInterger($('#txtNAMSINH').val())) {
            $('#txtNAMSINH').val("");
            setErrValidate('txtNAMSINH');
            DlgUtil.showMsg('Năm sinh là số nguyên dương/ năm sinh không nhỏ hơn năm 1900');
            return false;
        } else if ($('#txtTUOI').val().trim().length == 0) {
            $('#txtTUOI').val("");
            setErrValidate('txtTUOI');
            DlgUtil.showMsg('Hãy nhập tuổi');
            return false;
        } else if ($('#txtTUOI').val().trim().length > 0 && !integerRegex.test($('#txtTUOI').val())) {
            $('#txtTUOI').val("");
            setErrValidate('txtTUOI');
            DlgUtil.showMsg('Tuổi là số nguyên dương');
            return false;
        } else if ($('#cboDVTUOI').val() == "3" && (qd130 == 1 || cfObj.HIS_BATBUOCGIOSINH_SONGAY != 0) && $('#txtGIO_SINH').val() == '') {
            var songaybatbuoc = cfObj.HIS_BATBUOCGIOSINH_SONGAY;
            if (parseInt($('#txtTUOI').val()) <= songaybatbuoc) {
                DlgUtil.showMsg('Bệnh nhân nhỏ hơn ' + songaybatbuoc + ' ngày bắt buộc nhập giờ sinh ');
                $('#txtGIO_SINH').focus();
                return false;
            }
        } else if ($('#cboDANTOCID').val() == null || $('#cboDANTOCID').val() == "") {
            setErrValidate('cboDANTOCID');
            DlgUtil.showMsg('Hãy nhập dân tộc');
            return false;
        } else if ($('#txtTENNGUOITHAN').val() == "" && ($('#cboDVTUOI').val() != '1' || parseInt($('#txtTUOI').val()) < tuoi_checkttnguoinha && $('#cboDVTUOI').val() == '1')) {
            setErrValidate('txtTENNGUOITHAN');
            DlgUtil.showMsg('Hãy nhập thông tin người nhà.');
            return false;
        } else if ($('#txtDIACHI').val() == null || $('#txtDIACHI').val() == "") {
            setErrValidate('txtDIACHI');
            DlgUtil.showMsg('Hãy nhập địa chỉ');
            return false;
        } else if ($('#txtNGAYTIEPNHAN').val().trim().length == 0) {
            $('#txtNGAYTIEPNHAN').val("");
            setErrValidate('txtNGAYTIEPNHAN');
            DlgUtil.showMsg('Hãy nhập ngày khám');
            return false;
        } else if (!checkDate($('#txtNGAYTIEPNHAN').val())) {
            setErrValidate('txtNGAYTIEPNHAN');
            DlgUtil.showMsg('Ngày khám không đúng');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && tndate < exdate) {
            setErrValidate('txtNGAYTIEPNHAN');
            DlgUtil.showMsg('Ngày đến khám không được nhỏ hơn ' + _songayluiBHYT + ' ngày');
            return false;
        } else if ($('#cboDICHVUID').val() == null || $('#cboDICHVUID').val() == "") {
            setErrValidate('cboDICHVUID');
            DlgUtil.showMsg('Hãy nhập yêu cầu khám');
            return false;
        } else if ($('#cboPHONGKHAMID').val() == null || $('#cboPHONGKHAMID').val() == "") {
            setErrValidate('cboPHONGKHAMID');
            DlgUtil.showMsg('Hãy nhập phòng khám');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtMA_BHYT').val().trim().length == 0) {
            $('#txtMA_BHYT').val("");
            setErrValidate('txtMA_BHYT');
            DlgUtil.showMsg('Hãy nhập Mã thẻ BHYT');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtMA_BHYT').val().trim().length != 15) {
            setErrValidate('txtMA_BHYT');
            DlgUtil.showMsg('Sai Mã thẻ BHYT');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtBHYT_BD').val().trim().length == 0) {
            $('#txtBHYT_BD').val("");
            setErrValidate('txtBHYT_BD');
            DlgUtil.showMsg('Hãy nhập Từ ngày BHYT');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && !datetimeRegex.test($('#txtBHYT_BD').val())) {
            $('#txtBHYT_BD').val("");
            setErrValidate('txtBHYT_BD');
            DlgUtil.showMsg('Sai từ ngày');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtBHYT_BD').val().trim().length > 0 && !checkDate($('#txtBHYT_BD').val())) {
            setErrValidate('txtBHYT_BD');
            DlgUtil.showMsg('Sai từ ngày');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && datetimeRegex.test($('#txtBHYT_BD').val()) && b_tu > b_ngaykham) {
            setErrValidate('txtBHYT_BD');
            DlgUtil.showMsg('Thời gian BHYT chưa đến hạn');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtBHYT_KT').val().trim().length == 0) {
            $('#txtBHYT_KT').val("");
            setErrValidate('txtBHYT_KT');
            DlgUtil.showMsg('Hãy nhập đến ngày BHYT');
            return false;
        } else if ($('#txtBHYT_KT').val().trim().length > 0 && ($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && !datetimeRegex.test($('#txtBHYT_KT').val())) {
            $('#txtBHYT_KT').val("");
            setErrValidate('txtBHYT_KT');
            DlgUtil.showMsg('Sai đến ngày BHYT');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtBHYT_KT').val().trim().length > 0 && !checkDate($('#txtBHYT_KT').val())) {
            setErrValidate('txtBHYT_KT');
            DlgUtil.showMsg('Sai đến ngày BHYT');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && datetimeRegex.test($('#txtBHYT_KT').val()) && b_den < b_ngaykham) {
            setErrValidate('txtBHYT_KT');
            DlgUtil.showMsg('Thời gian BHYT hết hạn');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && datetimeRegex.test($('#txtBHYT_BD').val()) && datetimeRegex.test($('#txtBHYT_KT').val()) && b_tu > b_den) {
            setErrValidate('txtBHYT_BD');
            DlgUtil.showMsg('Từ ngày nhỏ hơn đến ngày');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && ($('#cboMAKCBBD').val() == null || $('#cboMAKCBBD').val() == "")) {
            setErrValidate('txtMA_KCBBD');
            DlgUtil.showMsg('Hãy nhập Nơi ĐKKCB');
            return false;
        } else if (($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') && $('#txtDIACHI_BHYT').val().trim() == "") {
            setErrValidate('txtDIACHI_BHYT');
            DlgUtil.showMsg('Hãy nhập địa chỉ BHYT');
            return false;
        } else if ($('#hidTRANGTHAIKHAMBENH').val() != '1' && $('#hidTRANGTHAIKHAMBENH').val() != '') {
            var msg = '';
            ($('#hidTRANGTHAIKHAMBENH').val() == '4') ? msg = 'Bệnh nhận đang khám, không thể cập nhật thông tin' : msg = 'Bệnh nhận kết thúc khám, không thể cập nhật thông tin';
            DlgUtil.showMsg(msg);
            return false;
        } else if ($("#cboNGHENGHIEPID").val() == null || $("#cboNGHENGHIEPID").val() == 'null') {
            DlgUtil.showMsg("Chưa chọn thông tin nghề nghiệp. ", function () {
                $("#cboNGHENGHIEPID").focus();
            });
            return false;
        } else if ($("#cboGIOITINHID").val() == null || $("#cboGIOITINHID").val() == 'null' || $("#cboGIOITINHID").val() == '-1') {
            DlgUtil.showMsg("Chưa chọn thông tin giới tính. ", function () {
                $("#cboGIOITINHID").focus();
            });
            return false;
        } else if ($("#cboDT_SINHSONG").val() == null || $("#cboDT_SINHSONG").val() == 'null' || typeof $("#cboDT_SINHSONG").val() == 'undefined') {
            DlgUtil.showMsg("Chưa có thông tin nơi sống.", function () {
                $("#cboDT_SINHSONG").focus();
            });
            return false;
        } else if ($("#txtTKMACHANDOANTUYENDUOI").val().length > 10) {
            DlgUtil.showMsg("Mã chẩn đoán tuyến dưới quá 10 ký tự ", function () {
                $("#txtTKMACHANDOANTUYENDUOI").focus();
            });
            return false;
        } else if ($("#cboDTBNID").val() == "1" && !intalphabetRegex.test($("#txtMA_KCBBD").val())) {
            DlgUtil.showMsg("Mã KCBBĐ sai định dạng", function () {
                $("#txtMA_KCBBD").focus();
            });
            return false;
        } else if (!checkErrorChar($('#txtDIACHI_BHYT').val()) || !checkErrorChar($('#txtDIACHI').val())) {
            DlgUtil.showMsg("Địa chỉ BHYT/địa chỉ BN chứa ký tự đặc biệt. Vui lòng chỉnh sửa", function () {
                $('#txtDIACHI_BHYT').focus();
            });
            return false;
        } else if ($("#cboDTBNID").val() == "1" && $("#cboBHYT_LoaiID").val() != "4"
            && ($("#hidMUCHUONG_NGT").val() == ""
                || $("#hidMUCHUONG_NGT").val() == null
                || $("#hidMUCHUONG_NGT").val() == "null"
                || Number($("#hidMUCHUONG_NGT").val()) < 0)
        ) {
            DlgUtil.showMsg("Bệnh nhân BHYT đang có mức hưởng chưa đúng. Vui lòng kiểm tra nhập lại thẻ. ");
            return false;
        } else if (_ttquannhan.indexOf($('#cboDTBNID' + " option:selected").attr('extval0')) != -1
            && ($("#cboQUANHAMID").val() == "-1" || $("#cboDVCTID").val() == "-1")
        ) {
            DlgUtil.showMsg("Yêu cầu nhập thông tin bổ sung Quân Nhân cho đối tượng BN này.", function () {
                $("#cboQUANHAMID").focus();
            });
            return false;
        }
        if ($('#txtNGAYCAPCMND').val().trim().length > 0 && !datetimeRegex.test($('#txtNGAYCAPCMND').val())) {
            setErrValidate('txtNGAYCAPCMND');
            DlgUtil.showMsg('Ngày cấp CCCD không đúng');
            return false;
        }
        if ($('#cboVSSID').val() == 0 && cfObj.NGT_TN_BATBUOC_VSSID == 1) {
            setErrValidate('cboVSSID');
            DlgUtil.showMsg('Chưa chọn thông tin thẻ');
            return false;
        }
        if ($("#txtDIENTHOAINGUOITHAN").val() == "" && cfObj.NGT_SOTUOI_BATBUOCSDT != 0
            && ($('#cboDVTUOI').val() != '1' || parseInt($('#txtTUOI').val()) <= cfObj.NGT_SOTUOI_BATBUOCSDT && $('#cboDVTUOI').val() == '1')) {
            DlgUtil.showMsg("Số điện thoại không được để trống!");
            $("#txtDIENTHOAINGUOITHAN").focus();
            return false;
        }
        if (cfObj.NGT_TN_BATBUOC_LYDOVAOVIEN == '1') {
            if ($("#txtLYDOVAOVIEN").val().trim() == "") {
                DlgUtil.showMsg("Chưa nhập lý do vào viện!");
                $("#txtLYDOVAOVIEN").focus();
                return false;
            }
        }
        if (QD_4750 == 1) {
            if ($("#txtMA_BHXH_CHA").val().trim() != '' &&
                ($("#txtMA_BHXH_CHA").val().trim().length != 10 || isNaN($("#txtMA_BHXH_CHA").val().trim()))) {
                DlgUtil.showMsg("Mã BHXH bố không đúng định dạng!");
                $("#txtMA_BHXH_CHA").focus();
                return false;
            }
            if ($("#txtMA_BHXH_ME").val().trim() != '' &&
                ($("#txtMA_BHXH_ME").val().trim().length != 10 || isNaN($("#txtMA_BHXH_ME").val().trim()))) {
                DlgUtil.showMsg("Mã BHXH mẹ không đúng định dạng!");
                $("#txtMA_BHXH_ME").focus();
                return false;
            }
            if ($("#txtSOCMTND").val().trim() != "" && !checkCMND($('#txtSOCMTND').val())) {
                DlgUtil.showMsg("Số CMTND/CCCD không đúng ký tự hoặc có chứa ký tự đặc biệt, vui lòng kiểm tra lại! ");
                $("#txtSOCMTND").focus();
                return false;
            }
            if ($("#txtSDTBENHNHAN").val().trim() != '' &&
                ($("#txtSDTBENHNHAN").val().trim().length > 11 || $("#txtSDTBENHNHAN").val().trim().length < 9 || isNaN($("#txtSDTBENHNHAN").val().trim()))) {
                DlgUtil.showMsg("Số điện thoại không đúng yêu cầu nhập lại!");
                $("#txtSDTBENHNHAN").focus();
                return false;
            }
            if ($("#txtDIENTHOAINGUOITHAN").val().trim() != '' &&
                ($("#txtDIENTHOAINGUOITHAN").val().trim().length > 10 || $("#txtDIENTHOAINGUOITHAN").val().trim().length < 9 || isNaN($("#txtDIENTHOAINGUOITHAN").val().trim()))) {
                DlgUtil.showMsg("Số điện thoại người nhà không đúng yêu cầu nhập lại!");
                $("#txtDIENTHOAINGUOITHAN").focus();
                return false;
            }
        } else {
            if ($("#txtSOCMTND").val() != "" && $('#txtSOCMTND').val().trim().length > 12) {
                DlgUtil.showMsg("Số CMTND/sCCCD không được vượt quá 12 số");
                $("#txtSOCMTND").focus();
                return false;
            }
        }
        if (cfObj.NGT_CHECK_CONGKHAMNHI != '0') {
            var check_cknhi = cfObj.NGT_CHECK_CONGKHAMNHI.split(',');
            var _sql_pr = [];
            _sql_pr.push({
                "name": "[0]",
                value: $('#cboDICHVUID').val()
            });
            var madichvu = jsonrpc.AjaxJson.getOneValue("NGT.GETMADV", _sql_pr)
            if (parseInt($('#txtTUOI').val()) > check_cknhi[0] && $('#cboDVTUOI').val() == '1' && madichvu == check_cknhi[1]) {
                DlgUtil.showMsg("Bệnh nhân lớn hơn " + check_cknhi[0] + " tuổi không thể tiếp nhận đối với công khám này!");
                return false;
            }
        }
        if (cfObj.NGT_TN_BATBUOC_NGAYCAPGCT == '1') {
            if ( $('#cboBHYT_LoaiID').val() == '2' && $('#txtNGAYCAP_GIAYCHUYENTUYEN').val() == '' ) {
                DlgUtil.showMsg("Chưa nhập ngày cấp giấy chuyển tuyến của tuyến dưới đối với bệnh nhân đúng tuyến giới thiệu!");
                return false;
            }
        }
        if (cfObj.NGT_TN_BATBUOC_HTCLDC == 1 && $('#cboDTBNID').val() == "1" && $('#cboBHYT_LoaiID').val() == '2'){
            if (   $('#cboCV_CHUYENVIEN_HINHTHUCID').val() == '' ) {
                DlgUtil.showMsg("Chưa chọn hình thức chuyển đối với bệnh nhân đúng tuyến giới thiệu!");
                return false;
            }
            if (  $('#cboCV_CHUYENVIEN_LYDOID').val() == '' ) {
                DlgUtil.showMsg("Chưa chọn lý do chuyển đối với bệnh nhân đúng tuyến giới thiệu!");
                return false;
            }
        }
        if (cfObj.NGT_CHECKTUOI_CONGKHAM != '0') {
            var check_ck = cfObj.NGT_CHECKTUOI_CONGKHAM.split(';');
            var _sql_pr = [];
            _sql_pr.push({
                "name": "[0]",
                value: $('#cboDICHVUID').val()
            });
            var madichvu = jsonrpc.AjaxJson.getOneValue("NGT.GETMADV", _sql_pr)
            var check_ck1 = check_ck[1].split(',');
            if (((parseInt($('#txtTUOI').val()) < check_ck[0] && $('#cboDVTUOI').val() == '1') || $('#cboDVTUOI').val() != '1') && check_ck[1].indexOf(madichvu) != -1) {
                DlgUtil.showMsg("Bệnh nhân nhỏ hơn " + check_ck[0] + " tuổi không thể tiếp nhận đối với công khám này!");
                return false;
            }
        }
        if (sdt_number = 1) {
            if ($("#txtSDTBENHNHAN").val().trim() != '' && isNaN($("#txtSDTBENHNHAN").val().trim())) {
                DlgUtil.showMsg("Số điện thoại không đúng yêu cầu nhập lại!");
                setErrValidate('txtSDTBENHNHAN');
                return false;
            }
            if ($("#txtDIENTHOAINGUOITHAN").val().trim() != '' && isNaN($("#txtDIENTHOAINGUOITHAN").val().trim())) {
                DlgUtil.showMsg("Số điện thoại người nhà không đúng yêu cầu nhập lại!");
                setErrValidate('txtDIENTHOAINGUOITHAN');
                return false;
            }
        }
        if (NGT_TN_CHECKNOISONG != '0' && ( (noisong != "" && noisong.split("")[1] != $("#cboDT_SINHSONG").val()) || ( noisong == "" &&  $("#cboDT_SINHSONG").val() != 0)) ) {
            if (NGT_TN_CHECKNOISONG == 2) {
                DlgUtil.showMsg("Thông tin khu vực chưa chính xác, yêu cầu kiểm tra và cập nhật lại!");
                $("#cboDT_SINHSONG").focus();
                return false;
            } else if (NGT_TN_CHECKNOISONG == 3) {
                DlgUtil.showMsg("Thông tin khu vực chưa chính xác, hệ thống đã tự động cập nhật lại mã khu vực!");
                $("#cboDT_SINHSONG").val(noisong.split("")[1]);
            } else {
                DlgUtil.showMsg("Thông tin khu vực chưa chính xác, yêu cầu kiểm tra và cập nhật lại!");
                $("#cboDT_SINHSONG").focus();
            }
        }
        var ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
        if($("#txtNGAYCAPCMND").val() != "" && compareDate(ngayhientai,$("#txtNGAYCAPCMND").val().trim(),'DD/MM/YYYY')){
            DlgUtil.showMsg("Ngày cấp CCCD không được lớn hơn ngày hiện tại!");
            $('#txtNGAYCAPCMND').focus();
            return false;
        }
        // sondn L2PT-2031
        if (_options.hospital_id == 1022) {
            if ($('#txtNGAYDU5NAM').val().length == 10 && $('#txtNGAYHUONG_5NAM6THANG').val().length == 10) {
                var _ngaydu5nam = $('#txtNGAYDU5NAM').val().substr(6, 4) + $('#txtNGAYDU5NAM').val().substr(3, 2) + $('#txtNGAYDU5NAM').val().substr(0, 2);
                var _ngaydct = $('#txtNGAYHUONG_5NAM6THANG').val().substr(6, 4) + $('#txtNGAYHUONG_5NAM6THANG').val().substr(3, 2) + $('#txtNGAYHUONG_5NAM6THANG').val().substr(0, 2);
                if (_ngaydu5nam > _ngaydct) {
                    DlgUtil.showMsg("Ngày đủ 5 năm phải nhỏ hơn ngày đồng chi trả. ");
                    return;
                }
            }
        }
        // end sondn L2PT-2031

        // sondn L2PT-25729
        if (_tiepnhantraingay == "0" && b_ngaykham_daydu > b_ngayhientai) {
            setErrValidate('txtNGAYTIEPNHAN');
            DlgUtil.showMsg('Thời gian khám không được lớn hơn thời gian hiện tại');
            return false;
        }

        // sondn L2PT-5332
        if (_dtrituyenduoingay == "1" && $("#txtDTRI_TD_TUNGAY").val() != "" && $("#txtDTRI_TD_DENNGAY").val() != "") {
            var _tn = $('#txtDTRI_TD_TUNGAY').val().substr(6, 4)
                + $('#txtDTRI_TD_TUNGAY').val().substr(3, 2) + $('#txtDTRI_TD_TUNGAY').val().substr(0, 2);
            var _dn = $('#txtDTRI_TD_DENNGAY').val().substr(6, 4)
                + $('#txtDTRI_TD_DENNGAY').val().substr(3, 2) + $('#txtDTRI_TD_DENNGAY').val().substr(0, 2);
            if (_tn > _dn) {
                DlgUtil.showMsg('Thời gian Từ ngày Điều trị tuyến dưới phải nhỏ hơn Thời gian Đến ngày Điều trị tuyến dưới. ');
                return false;
            }
        }
        // end sondn L2PT-5332
        //dannd_L2PT-124539
        if ($('#cboDTBNID').val() == "1" && cfObj.NGT_TN_REQ_5NAM6THANG == '1') {
            if ($('#chkDU5NAM6THANGLUONGCOBAN').is(':checked')) {
                if ($('#txtNGAYHUONG_5NAM6THANG').val() == '') {
                    DlgUtil.showMsg("Thiếu thông tin thời gian hưởng ĐCT!", function() {
                        $('#txtNGAYHUONG_5NAM6THANG').focus();
                    });
                    return false;
                }
            }
        }
        if (cfObj.NGT_TIEPNHAN_SDT == 1 || $('#chkVODANH').is(":checked")) {
            if ($("#txtSDTBENHNHAN").val().trim() == "") {
                DlgUtil.showMsg("Số điện thoại không được để trống!");
                $("#txtSDTBENHNHAN").focus();
                return false;
            }
            if ($("#txtSDTBENHNHAN").val().trim().length > 11 || $("#txtSDTBENHNHAN").val().trim().length < 8 || isNaN($("#txtSDTBENHNHAN").val().trim())) {
                DlgUtil.showMsg("Số điện thoại không đúng yêu cầu nhập lại!");
                $("#txtSDTBENHNHAN").focus();
                return false;
            }
        }
        // sondn L2PT-878 : cau truc cau hinh: 1:txtTENNGUOITHAN,txtSOCMDNDNLH
        if (_checkBatBuocTE != "0" && _checkBatBuocTE != "") {
            var _mess = "";
            var _checked = false;
            var _checkBatBuocTEArr = _checkBatBuocTE.split(':');
            var _checkBatBuocTEArr1 = _checkBatBuocTEArr[1].split(',');

            if (_checkBatBuocTEArr[0] == "1") {										// benh nhan co nghe nghiep la tre em;
                var _tennghenghiep = $("#cboNGHENGHIEPID option:selected").text().toLowerCase();
                if (_tennghenghiep.indexOf('trẻ <') != -1 || _tennghenghiep.indexOf('trẻ dưới') != -1) {
                    _checked = true;
                }
            } else if (_checkBatBuocTEArr[0] == "2") {								// benh nhan <= 72 thang;
                if ($("#cboDVTUOI") != "1" || ($("#cboDVTUOI") == "1" && parseInt($('#txtTUOI').val()) < 7)) {
                    _checked = true;
                }
            }

            if (_checked == true) {
                for (i = 0; i < _checkBatBuocTEArr1.length; i++) {
                    var _ttt = _checkBatBuocTEArr1[i];
                    if (_ttt.substring(0, 3) == "cbo") {
                        if ($("#" + _ttt).val() == "0" || $("#" + _ttt).val() == "-1") {
                            if (_hd === '1' && _nhapthongtin_bnhd.indexOf(_ttt + "@") != -1) {
                                // khong check
                            } else {
                                _mess += $("#" + _ttt).attr("title") + ",";
                            }
                        }
                    } else {
                        if ($("#" + _ttt).val().trim() == "") {
                            if (_hd === '1' && _nhapthongtin_bnhd.indexOf(_ttt + "@") != -1) {
                                // khong check
                            } else {
                                _mess += $("#" + _ttt).attr("title") + ",";
                            }

                        }
                    }
                }
            }

            if (_mess != "") {
                _mess = "Yêu cầu điền các thông tin sau: " + _mess;
                DlgUtil.showMsg(_mess);
                return false;
            }
        }
        // end sondn L2PT-878

        if ($('#cboBHYT_LoaiID').val() == 2 && ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6")) {
            if ((QD_4750 == '1' && $("#cboBHYT_LOAIID1").val() != "1") || QD_4750 != '1') {

                if ($('#txtCHANDOANTUYENDUOI').val() == null || $('#txtCHANDOANTUYENDUOI').val() == "") {
                    setErrValidate('txtTKMACHANDOANTUYENDUOI');
                    DlgUtil.showMsg('Hãy nhập Chẩn đoán TD');
                    return false;
                }

                if ($('#cboMANOIGIOITHIEU').val() == null || $('#cboMANOIGIOITHIEU').val() == "") {
                    setErrValidate('txtTKMANOIGIOITHIEU');
                    DlgUtil.showMsg('Hãy nhập Nơi chuyển');
                    return false;
                }
            }
            if (_batbuocsocv == "1") {
                if ($("#txtSOCHUYENVIEN").val().trim() == "") {
                    DlgUtil.showMsg("Yêu cầu nhập thông tin số chuyển viện");
                    return false;
                }
            }
        }

        if (_checkDungTuyenCapCuu == "0") {
            //L2PT-7180
            var hinhthucvaovienid = $.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value;
            if (cfObj.NGT_CANHBAOKCBBD_DTP == 1) {
                if (hinhthucvaovienid == 2 && $("#cboBHYT_LoaiID").val() != 3 && $("#cboDTBNID").val() == 1
                    && ($('#txtMA_KCBBD').val() != ($('#cboHC_TINHID' + " option:selected").attr('extval1') + "000") &&
                        $('#txtMA_KCBBD').val() != _options.hospital_code)) {
                    DlgUtil.showMsg("Bệnh nhân cấp cứu chỉ được chọn Tuyến là Đúng tuyến cấp cứu");
                    setErrValidate('cboBHYT_LoaiID');
                    return false;
                }
            } else {
                if (hinhthucvaovienid == 2 && $("#cboBHYT_LoaiID").val() != 3 && $("#cboDTBNID").val() == 1) {
                    DlgUtil.showMsg("Bệnh nhân cấp cứu chỉ được chọn Tuyến là Đúng tuyến cấp cứu");
                    setErrValidate('cboBHYT_LoaiID');
                    return false;
                }
            }
            if (hinhthucvaovienid != 2 && $("#cboBHYT_LoaiID").val() == 3 && $("#cboDTBNID").val() == 1) {
                DlgUtil.showMsg("Bệnh nhân Khám bệnh không được chọn Tuyến là Đúng tuyến cấp cứu");
                setErrValidate('cboBHYT_LoaiID');
                return false;
            }
            // end L2PT-7180
        }

        if (_hd != undefined && _hd === '1') {
            if ($("#cboHOPDONGID").val() == "0" || $("#cboLOAIKHAM").val() == "0") {
                DlgUtil.showMsg("Yêu cầu chọn loại khám và hợp đồng khám bệnh");
                setErrValidate('cboLOAIKHAM');
                return false;
            }
        }

        if (_batbuocsdtbn != "0") {						// che do chan cung;
            // sondn L2PT-32838
            if (_batbuocsdtbn.split('@')[0] == "1") {
                if ($("#txtSDTBENHNHAN").val().trim().length < Number(_batbuocsdtbn.split('@')[1])
                    || isNaN($("#txtSDTBENHNHAN").val().trim())) {
                    if (_hd === '1' && _nhapthongtin_bnhd.indexOf("txtSDTBENHNHAN@") != -1) {
                        // bo qua k check;
                    } else {
                        DlgUtil.showMsg("SĐT BN chưa đúng định dạng số /Chưa nhập đủ số ký tự yêu cầu (>= " + _batbuocsdtbn.split('@')[1] + " ký tự)");
                        return false;
                    }
                }
            }
            if (_batbuocsdtbn.split('@')[2] == "1") {
                if ($("#txtDIENTHOAINGUOITHAN").val().trim().length < Number(_batbuocsdtbn.split('@')[3])
                    || isNaN($("#txtDIENTHOAINGUOITHAN").val().trim())) {
                    if (_hd === '1' && _nhapthongtin_bnhd.indexOf("txtDIENTHOAINGUOITHAN@") != -1) {
                        // bo qua k check;
                    } else {
                        DlgUtil.showMsg("SĐT người thân chưa đúng định dạng số / Chưa nhập đủ số ký tự yêu cầu (>= " + _batbuocsdtbn.split('@')[3] + " ký tự)");
                        return false;
                    }
                }
            }
            // END sondn L2PT-32838

            // sondn IT360-141829
            if (_batbuocsdtbn.split('@')[0] == "3" && $("#txtSDTBENHNHAN").val().trim() != "") {
                if ($("#txtSDTBENHNHAN").val().trim().length < Number(_batbuocsdtbn.split('@')[1])
                    || isNaN($("#txtSDTBENHNHAN").val().trim())) {
                    if (_hd === '1' && _nhapthongtin_bnhd.indexOf("txtSDTBENHNHAN@") != -1) {
                        // bo qua k check;
                    } else {
                        DlgUtil.showMsg("SĐT BN chưa đúng định dạng số /Chưa nhập đủ số ký tự yêu cầu (>= " + _batbuocsdtbn.split('@')[1] + " ký tự)");
                        return false;
                    }
                }
            }
            if (_batbuocsdtbn.split('@')[2] == "3" && $("#txtDIENTHOAINGUOITHAN").val().trim() != "") {
                if ($("#txtDIENTHOAINGUOITHAN").val().trim().length < Number(_batbuocsdtbn.split('@')[3])
                    || isNaN($("#txtDIENTHOAINGUOITHAN").val().trim())) {
                    if (_hd === '1' && _nhapthongtin_bnhd.indexOf("txtDIENTHOAINGUOITHAN@") != -1) {
                        // bo qua k check;
                    } else {
                        DlgUtil.showMsg("SĐT người thân chưa đúng định dạng số / Chưa nhập đủ số ký tự yêu cầu (>= " + _batbuocsdtbn.split('@')[3] + " ký tự)");
                        return false;
                    }
                }

            }
            // END sondn IT360-141829
        }

        // SONDN L2PT-34565
        if ($("#cboDTBNID").val() != "4") {										// doi tuong khac nguoi nuoc ngoai moi check;
            if ($('#cboHC_TINHID').val() == null || $('#cboHC_TINHID').val() == "") {
                setErrValidate('cboHC_TINHID');
                DlgUtil.showMsg('Hãy chọn tỉnh/TP');
                return false;
            }
        }
        // END SONDN L2PT-34565

        // SONDN 23/03/2020 L2PT-18512
        if ($("#cboQUOCGIAID").val() == "0") {
            var _tinhid1 = $("#cboHC_TINHID").val();
            var _huyenid1 = $("#cboHC_HUYENID").val();
            var _xaid1 = $("#cboHC_XAID").val();

            if (_nhaptinhhuyenxa == "2"
                && (_tinhid1 == "" || _tinhid1 == null || _tinhid1 == 'null'
                    || _huyenid1 == "" || _huyenid1 == null || _huyenid1 == 'null')) {
                DlgUtil.showMsg("Yêu cầu nhập tỉnh / huyện");
                return false;
            }

            if (_nhaptinhhuyenxa == "3"
                && (_tinhid1 == "" || _tinhid1 == null || _tinhid1 == 'null'
                    || _huyenid1 == "" || _huyenid1 == null || _huyenid1 == 'null'
                    || _xaid1 == "" || _xaid1 == null || _xaid1 == 'null')) {
                DlgUtil.showMsg("Yêu cầu nhập tỉnh / huyện / xã");
                return false;
            }
        }
        // END SONDN 23/03/2020 L2PT-18512

        if ($('#cboQUOCGIAID').val() == null || $('#cboQUOCGIAID').val() == "") {
            setErrValidate('cboQUOCGIAID');
            DlgUtil.showMsg('Hãy nhập quốc tịch');
            return false;
        }

        // sondn L2PT-12609
        if (_hd != undefined && _hd === '1') {
            var ischeck = true;
            if (_sudungnguonct == "1") {
                if ($("#cboNGUONCT").val() != "0") {
                    ischeck = false; 														// neu chon nguon ct roi, khong check hopdongid, loaikhamnua neu da nhap nguonct;
                }
            }
            if (ischeck == true) {
                if ($("#cboHOPDONGID").val() == "0" || $("#cboLOAIKHAM").val() == "0") {
                    DlgUtil.showMsg("Yêu cầu chọn loại khám và hợp đồng khám bệnh");
                    setErrValidate('cboLOAIKHAM');
                    return false;
                }
            }
        }
        // end sondn L2PT-12609

        return true;
    }

    // kiểm tra ngày tháng hợp lệ khi liệt kê lưới danh sách bệnh nhân
    function _kiemTraLietKe() {

        if ($('#txtTUTK').val().trim().length == 0) {
            $('#txtTUTK').val("");
            setErrValidate('txtTUTK');
            DlgUtil.showMsg('Hãy nhập Từ ngày');
            return false;
        }
        else if ($('#txtDENTK').val().trim().length == 0) {
            $('#txtDENTK').val("");
            setErrValidate('txtDENTK');
            DlgUtil.showMsg('Hãy nhập đến ngày');
            return false;
        }
        var b_tu, b_den;

        b_tu = $('#txtTUTK').val().substr(6, 4) + $('#txtTUTK').val().substr(3, 2) + $('#txtTUTK').val().substr(0, 2);
        b_den = $('#txtDENTK').val().substr(6, 4) + $('#txtDENTK').val().substr(3, 2) + $('#txtDENTK').val().substr(0, 2);

        var thoigian = getDaysDiff($('#txtTUTK').val(), $('#txtDENTK').val());

        if (b_tu > b_den) {
            setErrValidate('txtTUTK');
            DlgUtil.showMsg('Sai điều kiện tìm kiếm, từ ngày không thể lớn hơn đến ngày');
            return false;
        }
        else  if (cfObj.NGT_TIEPNHAN_TK_TG != 0 && thoigian > parseInt(cfObj.NGT_TIEPNHAN_TK_TG) ) {
            DlgUtil.showMsg("Khoảng thời gian tìm kiếm vượt quá giới hạn để đảm bảo hiệu năng tìm kiếm là " + cfObj.NGT_TIEPNHAN_TK_TG +
                " ngày. Đề nghị chỉ tìm kiếm trong khoảng ngày kiến nghị!");
            return false;
        }
        return true;
    }


    function loadRowSelect(item_id) {
        if (item_id != null && item_id.length > 0) {
            _refreshForm();
            var bKhamBenhID = $("#grdDanhSachTiepNhan").jqGrid('getCell', item_id, 5);
            $("#hidKHAMBENHID").val(bKhamBenhID);

            //tuyennx_add_start_20170908 HISL2BVDKHN-425
            var bXutri = $("#grdDanhSachTiepNhan").jqGrid('getCell', item_id, 14);
            $("#hidXUTRIKHAMBENHID").val(bXutri);
            //tuyennx_add_end_20170908

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.LECT", bKhamBenhID);
            if (data_ar != null && data_ar.length > 0) {
                loadDetail(data_ar, 1);
            }
            $("#hidINDEX").val(item_id);
            //tuyennx_add_start_20170908
            var trangthai = $("#grdDanhSachTiepNhan").jqGrid('getCell', item_id, 4);
            if (trangthai == 1) {
                $("#toolbarIdbtnThuTien").attr("disabled", false);
            } else {
                $("#toolbarIdbtnThuTien").attr("disabled", true);
            }
            //tuyennx_add_end_20170908
        }
    }

    function _refreshForm() {
        _b_flag = false;
        _mabhytgoc = "";
        _b_flag_taothe_trem = false;
        $("#lblSTTDonTiep").text("");
        $("#txtTENBENHNHAN").val("");
        $("#txtMABENHNHAN").val("");
        $("#txtNGAYSINH").val("");
        $("#txtNAMSINH").val("");
        $("#txtTUOI").val("");
        $("#cboDVTUOI").val("1");
        $("#cboGIOITINHID").val("");
        $("#cboNGHENGHIEPID").val("");
        $("#cboDANTOCID").val("");
        $("#cboQUOCGIAID").val("");
        $("#txtSONHA").val("");
        $('#cboDIAPHUONGID').find('option').remove();
        $('#cboDIAPHUONGID').text("");
        $("#cboHC_TINHID").val("");
        $("#cboHC_HUYENID").val("");
        $("#cboHC_XAID").val("");
        $("#cboDIABANID").val("");
        $("#txtNOILAMVIEC").val("");
        $("#txtNGUOITHAN").val("");
        $("#txtTENNGUOITHAN").val("");
        $("#txtSOCMTND").val("");
        $("#txtNGAYCAPCMND").val("");
        $("#txtNOICAPCMND").val("");
        $("#txtDIACHINGUOITHAN").val("");
        $("#txtDIENTHOAINGUOITHAN").val("");
        $("[name='radHINHTHUCVAOVIENID']").each(function () {
            $(this).prop('checked', $(this).val() == "0");
        });
        $("#txtSOCAPCUU").val("");
        $('#chkUUTIENKHAMID').attr('checked', false);
        $('#chkTNGT').attr('checked', false);
        $('#chkCHECKBHYTDV').attr('checked', false);
        $("#txtNGAYTIEPNHAN").val("");
        $("#cboPHONGKHAMID").val("");
        $("#cboDTBNID").val("");
        $("#txtMA_BHYT").val("");
        $("#txtBHYT_BD").val("");
        $("#txtBHYT_KT").val("");
        $("#txtMA_KCBBD").val("");
        $("#cboMAKCBBD").find('option').remove();
        $('#cboMAKCBBD').text("");
        $("#txtDIACHI_BHYT").val("");
        $("#cboBHYT_LoaiID").val("");
        $("#cboDT_SINHSONG").val(0);
        $('#chkDU5NAM6THANGLUONGCOBAN').attr('checked', false);
        $('#chkTRADU6THANGLCB').attr('checked', false);
        $('#chkDAGIUTHEBHYT').attr('checked', false);
        $('#chkCOGIAYKS').attr('checked', false);
        $('#chkNOTHE').attr('checked', false);
        $("#txtQUYENLOI").val("");
        $("#txtMUCHUONG").val("Ngoại (0%)-Nội (0%)");
        $("#cboDICHVUID").val("");
        $("#txtTKMACHANDOANTUYENDUOI").val("");
        $('#txtCHANDOANTUYENDUOI').val('');
        $("#txtTKMANOIGIOITHIEU").val("");
        $("#cboMANOIGIOITHIEU").find('option').remove();
        $('#cboMANOIGIOITHIEU').text("");
        $("#hidPHONGID_CU").val("");
        $("#hidTIEPNHANID").val("");
        $("#hidBENHNHANID").val("");
        $("#hidBHYTID").val("");
        $("#hidPHONGKHAMDANGKYID").val("");
        $("#hidKHAMBENHID").val("");
        $("#hidXUTRIKHAMBENHID").val("");
        $("#hidMAUBENHPHAMID").val("");
        $("#hidHOSOBENHANID").val("");
        $("#hidDICHVUKHAMBENHID").val("");
        $("#hidMAKCBBD").val("");
        $("#grdDanhSachTiepNhan").jqGrid('resetSelection');
        $("#grdDSPhongDuocChon").jqGrid("clearGridData", true);
        $("#" + _gridLS).jqGrid("clearGridData", true);
        $("#txtTENBENHNHAN").focus();

        $("#btnChuyenTuyen").attr("disabled", "disabled");
        GridUtil.unmarkAll('grdDanhSachTiepNhan');
        //resetObject(objChuyenVien);
        //resetObject(objSinhTon);
    }

    function _themmoiBenhAn() {
        $("#hidTIEPNHANID").val("");
        //$("#hidBENHNHANID").val("");
        $("#hidBHYTID").val("");
        $("#hidPHONGKHAMDANGKYID").val("");
        $("#hidKHAMBENHID").val("");
        $("#hidXUTRIKHAMBENHID").val("");
        $("#hidMAUBENHPHAMID").val("");
        $("#hidHOSOBENHANID").val("");
        $("#hidDICHVUKHAMBENHID").val("");
        $("#cboDTBNID").val("2");
        $("#cboPHONGKHAMID").removeAttr("disabled");
        $("#cboDICHVUID").removeAttr("disabled");
        $("#btnLuu").removeAttr("disabled");
        $("#hidTRANGTHAIKHAMBENH").val("1");
    }

    function resetThongTinCoBan() {
        $("#hidTIEPNHANID").val("");
        $("#hidBENHNHANID").val("");
        $("#hidBHYTID").val("");
        $("#hidPHONGKHAMDANGKYID").val("");
        $("#hidKHAMBENHID").val("");
        $("#hidXUTRIKHAMBENHID").val("");
        $("#hidMAUBENHPHAMID").val("");
        $("#hidHOSOBENHANID").val("");
        $("#hidDICHVUKHAMBENHID").val("");
    }

    // tim kiem thong tin kham benh moi nhat cua benh nhan theo ma benh nhan/ ma bhyt/ ho ten, ngay sinh, gioi tinh
    function _timKiemBenhNhan(b_MaBenhNhan, b_Loai) {
        var _objData = new Object();
        _objData["tenbenhnhan"] = $("#txtTENBENHNHAN").val();
        _objData["ngaysinh"] = $("#txtNGAYSINH").val() != "" ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val().trim();
        _objData["gioitinhid"] = $("#cboGIOITINHID").val();

        // start cau hinh bat, chi check theo che do the 10 so;
        b_MaBenhNhan2 = b_MaBenhNhan; 				// ma truyen vao day du: vd DN401 1234567899
        b_MaBenhNhan1 = b_MaBenhNhan;				// ma truyen vao 10 so: vd: 1234567899
        b_Loai1 = b_Loai;
        if (_checkthe10so == "1" && b_Loai == "2") {
            b_Loai1 = "6";
            b_MaBenhNhan1 = b_MaBenhNhan.substring(5, 15); 								// lay ra 10 so cuoi;
        }

        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN", b_MaBenhNhan1 + '$' + b_Loai1 + '$' + JSON.stringify(_objData));
        // end cau hinh bat, chi check theo che do the 10 so;
        if (data_ar != null && data_ar.length > 0) {
            if (data_ar[0].NGAYTHUOC != "" && data_ar[0].NGAYTHUOC != null) {
                var t_ngaythuoc = data_ar[0].NGAYTHUOC;
                var ngay = t_ngaythuoc.split('/');
                var ngaythuoc = ngay[2].substring(0, 4) + ngay[1] + ngay[0];
                var ngaytn = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');

                if (parseInt(ngaythuoc) > parseInt(ngaytn)) {
                    // sondn BVTM-3340
                    if (_checkconthuocct == "1") {
                        var _objj = new Object();
                        _objj.BENHNHANID = data_ar[0].BENHNHANID;
                        _objj.MA_BHYT = data_ar[0].MA_BHYT;
                        var data_ar11 = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.CONTHUOC", JSON.stringify(_objj));
                        if (data_ar11 != null && data_ar11.length > 0) {
                            DlgUtil.showMsg("Bệnh nhân mã bệnh án: " + data_ar11[0].MAHOSOBENHAN + " có chỉ định thuốc ngày " + data_ar11[0].NGAYCHIDINH
                                + ", hết thuốc ngày: " + data_ar11[0].NGAYTHUOC + ", của PK " + data_ar11[0].TENPHONG, function () {
                                $("#txtMA_KCBBD").focus();
                            });
                        }
                    } else {
                        //L2PT-31350
                        if (_popupconthuoc == "0") {
                            DlgUtil.showMsg("Bệnh nhân còn thuốc của lần khám trước. ");
                        } else {
                            $.bootstrapGrowl('Bệnh nhân còn thuốc của lần khám trước. ', {
                                type: 'danger',
                                delay: 3000,
                            });
                        }
                    }
                }
            }
            if (parseInt(data_ar[0].CHUADUYETKT) > 0 && _chuaduyetkt == 0) {
                if (cfObj.NGT_TN_CHUADUYETKT_CT == "1") {
                    var _objj = new Object();
                    _objj.BENHNHANID = data_ar[0].BENHNHANID;
                    _objj.CHECKDUYETKT = "1";
                    var data_ar11 = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.CONTHUOC", JSON.stringify(_objj));
                    if (data_ar11 != null && data_ar11.length > 0) {
                        var txtMsg = "";
                        for (var i = 0; i < data_ar11.length; i++) {
                            txtMsg = txtMsg + " " + data_ar11[i].MAHOSOBENHAN + " , ngày khám: " + data_ar11[i].NGAYKHAM;
                        }
                        DlgUtil.showMsg("Bệnh nhân có mã bệnh án: " + txtMsg +" chưa thanh toán!",
                            function () {
                                $("#txtMA_KCBBD").focus();
                            });
                    }
                }else {
                    if (_popupconthuoc == "0") {
                        DlgUtil.showMsg("Bệnh nhân chưa thanh toán của lần khám trước");
                    } else {
                        $.bootstrapGrowl('Bệnh nhân chưa thanh toán của lần khám trước', {
                            type: 'danger',
                            delay: 3000,
                        });
                    }
                }
                _chuaduyetkt = 1;
            }

            // SONDN L2PT-31261
            if (_uutienBHYT != "0") {
                var _arr = _uutienBHYT.split(',');
                var _dautheBHYT2 = $("#txtMA_BHYT").val().substring(0, 2);
                var _dautheBHYT3 = $("#txtMA_BHYT").val().substring(0, 3);
                if ($.inArray(_dautheBHYT2, _arr) >= 0 || $.inArray(_dautheBHYT3, _arr) >= 0) {
                    data_ar[0].UUTIENKHAMID = "3";
                }
            }
            // END SONDN L2PT-31261

            _refreshForm();
            loadDetail(data_ar, 2);
            $("#txtNGAYTIEPNHAN").val(_ngayhientaict);
            _loadLSDT($("#hidBENHNHANID").val());

            // check thông tin gói khám
            if (_goikhamkcb != "0" && _dachongoikham == "0") {						// cau hinh bat, chua gan lan nao;
                var objData = new Object();
                objData.BENHNHANID = $("#hidBENHNHANID").val();
                var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T001.CHECKGK", JSON.stringify(objData));
                if (dtar != null && dtar.length > 0) {
                    if (dtar.length == "1") {
                        _dachongoikham = "1";
                        DlgUtil.showConfirm("Bệnh nhân này đã từng khám gói " + dtar[0].TENGOI + ". Bạn có muốn tiếp tục gán gói khám này cho bệnh nhân? ", function (flag) {
                            if (flag) {
                                _goikhamkcbid = dtar[0].GKBNID; 										// gan id goi kham; gkbnid
                            }
                        });
                    } else {
                        DlgUtil.showMsg("Bệnh nhân đang sử dụng nhiều gói KCB dài ngày, hãy chọn gói cụ thể cho lần khám nếu muốn sử dụng gói.");
                    }

                }
            }

            if (_cbktk == "1") {
                var objData = new Object();
                objData.BENHNHANID = $("#hidBENHNHANID").val();
                var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T001.CBKTK", JSON.stringify(objData));
                if (dtar != null && dtar.length > 0) {
                    DlgUtil.showMsg("Lưu ý: Bệnh nhân có lượt tiếp nhận " + dtar[0].LOAITIEPNHANTEXT + " chưa kết thúc, thời gian: " + dtar[0].NGAYTIEPNHAN);
                }
            }
        } else if ($("#cboDTBNID").val() == "1" && b_Loai == "2") {
            // truong hop lay thong tin the bhyt k lay dc du lieu, van cho phep popup lai;
            if (cfObj.NGT_TIEPNHAN_BNCU == "1") {
                if ($("#hidBENHNHANID").val() == '') {
                    resetThongTinCoBan();
                }
            } else {
                resetThongTinCoBan();
            }
            if ($("#txtMA_BHYT").val().length == 15 && _mabhytgoc.length == 15
                && $("#txtMA_BHYT").val() != _mabhytgoc) {
                _b_flag = false;
            } else {
                _b_flag = true;
                _mabhytgoc = $("#txtMA_BHYT").val();
            }
        }
    }

    function loadDetail1(data_ar) {
        docmathe = true;
        _b_flag = true;
        resetThongTinCoBan();
        FormUtil.setObjectToForm("dvSec3", "", data_ar[0]);
        if (data_ar[0].NGAY_SINH != null && data_ar[0].NGAY_SINH != "") {
            $("#txtNGAYSINH").val(data_ar[0].NGAY_SINH);
            tinhTuoi(data_ar[0].NGAY_SINH, 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
        } else {
            if (data_ar[0].NAMSINH != null && data_ar[0].NAMSINH != "") {				// chi co nam sinh
                var bNamHT = Number(jsonrpc.AjaxJson.getSystemDate('YYYY'));
                var _diffYear = bNamHT - Number(data_ar[0].NAMSINH);

                if (_diffYear * 12 < _ngtTinhTuoi) {
                    $('#txtTUOI').val(_diffYear * 12);
                    $('#cboDVTUOI').val("2");
                } else {
                    $('#txtTUOI').val(_diffYear);
                    $('#cboDVTUOI').val("1");
                }
            }
        }
        $("#txtTKGIOITINHID").val(data_ar[0].GIOITINHID);
        $('#cboDIAPHUONGID').find('option').remove();
        $("#txtDIACHI").val(data_ar[0].DIACHI);
        var newOption = $('<option value="' + data_ar[0].DIAPHUONGID + '" selected>' + data_ar[0].TENDIAPHUONG + '</option>');
        $("#cboDIAPHUONGID").append(newOption);
        getDiaChi(data_ar[0].DIAPHUONGID, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID',
            data_ar[0].DIABANID, 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID',
            'txtTKDIABANID', 'cboDTBNID', docmathe, false);
        $('#chkTNGT').prop('checked', data_ar[0].TNGT == "1");
        $('#cboNGHENGHIEPID').change();
        $('#cboDANTOCID').change();
        $("#cboBHYT_LoaiID").change();
        if ($("#txtMABENHNHAN").val() != '' && $("#txtMABENHNHAN").val() != null && cfObj.NGT_TN_DISABLE_MABENHAN == 1) {
            $("#txtMABENHNHAN").attr("disabled", true);
        }
    }

    // loai {1: Liet ke chi tiet; 2: Tim kiem, phongkhamdangkyid is null}
    function loadDetail(data_ar, loai) {
        _thaoTacHientai = "loadDetail"; 										// set thao tac hien tai de danh dau ham dang su dung;
        if (cfObj.HIS_SHOW_TTBENHAN_WIDGET == "1") {
            showTTBNWidget = true;
        }
        // SONDN L2PT-5232
        if (data_ar[0].BHYT_DV == "1") {
            data_ar[0].DTBNID = "6";
        }

        $("[name='radHINHTHUCVAOVIENID']").each(function () {
            $(this).prop('checked', $(this).val() == data_ar[0].HINHTHUCVAOVIENID);
        });
        $("#cboDTBNID").val(data_ar[0].DTBNID);
        $("#cboDTBNID").change();

        docmathe = true;
        _b_flag = true;

        resetThongTinCoBan();
        FormUtil.setObjectToForm("tabTiepNhan", "", data_ar[0]);
        if ($("#txtMABENHNHAN").val() != '' && $("#txtMABENHNHAN").val() != null && cfObj.NGT_TN_DISABLE_MABENHAN == 1) {
            $("#txtMABENHNHAN").attr("disabled", true);
        }
        if (_checkthe10so == "1") {
            if (b_Loai1 == "6" && b_MaBenhNhan1 != "" && data_ar[0].MA_BHYT != "") {
                if (b_MaBenhNhan1 == data_ar[0].MA_BHYT.substring(5, 15)) {
                    if (b_MaBenhNhan2.length == "15") {
                        $("#txtMA_BHYT").val(b_MaBenhNhan2);									// neu truyen vao 15 so thi giu nguyen
                    } else {
                        $("#txtMA_BHYT").val(data_ar[0].MA_BHYT);								// neu truyen vao 10 so thi lay tu db ra
                    }

                }
            }
        }
        //dannd_L2PT-34927
        if (nhanthuochen == '2') {
            $("[name='radNHANTHUOC']").each(function () {
                $(this).prop('checked', $(this).val() == data_ar[0].NHANTHUOCTHEOHEN);
            });
            if (data_ar[0].DV_CAPGIAY) {
                $('#txtDV_CAPGIAY1').val(data_ar[0].DV_CAPGIAY);
                $('#txtDV_CAPGIAY1').combogrid("setValue", data_ar[0].DV_CAPGIAY);
            } else {
                $('#txtDV_CAPGIAY1').val("");
                $("#cboTEN_DV_CAPGIAY1").val("");
            }
            $('#txtNGAY_CAPGIAY1').val(data_ar[0].NGAY_CAPGIAY);

            if (data_ar[0].BACSIHOICHAN) {
                var sql_par = [];
                sql_par.push({
                    "name": "[0]",
                    "value": "-1"
                }, {
                    "name": "[0]",
                    "value": "-1"
                }, {
                    "name": "[1]",
                    "value": data_ar[0].BACSIHOICHAN
                });
                var _row = jsonrpc.AjaxJson.getFirstRowO("CDDV.USER_LOAD", sql_par);
                if (_row != null && _row.length > 0) {
                    $("#txtBACSIHOICHAN").val("");
                    option = $('<option value="' + _row[0].USER_ID + '">' + _row[0].OFFICER_NAME + '</option>');
                    $("#cboBACSIHOICHAN").empty();
                    $("#cboBACSIHOICHAN").append(option);
                }
            }
        }
        if ($("#cboDTBNID").val() == "1" && _mabhytgoc == "") {
            _mabhytgoc = $("#txtMA_BHYT").val().trim(); 			// gan ma the bhyt goc neu co;
        }

        if (data_ar[0].NGAY_SINH != null && data_ar[0].NGAY_SINH != "") {
            $("#txtNGAYSINH").val(data_ar[0].NGAY_SINH);
            tinhTuoi(data_ar[0].NGAY_SINH, 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
        } else {
            if (data_ar[0].NAMSINH != null && data_ar[0].NAMSINH != "") {				// chi co nam sinh
                var bNamHT = Number(jsonrpc.AjaxJson.getSystemDate('YYYY'));
                var _diffYear = bNamHT - Number(data_ar[0].NAMSINH);

                if (_diffYear * 12 < _ngtTinhTuoi) {
                    $('#txtTUOI').val(_diffYear * 12);
                    $('#cboDVTUOI').val("2");
                } else {
                    $('#txtTUOI').val(_diffYear);
                    $('#cboDVTUOI').val("1");
                }
            }
        }

        $("#txtTKGIOITINHID").val(data_ar[0].GIOITINHID);
        $("#chkCOGIAYKS").prop("checked", data_ar[0].SINHTHEBHYT == "1" ? true : false);
        $("#chkNOTHE").prop("checked", data_ar[0].NOTHE == "1" ? true : false);
        $('#chkHENKHAM').prop('checked', data_ar[0].HENKHAM == "1" ? true : false);
        $('#chkBVSK').prop('checked', data_ar[0].BVSK == "1" ? true : false);
        $('#chkKHAMGIAMDINH').prop('checked', data_ar[0].KHAMGIAMDINH == "1" ? true : false);
        $('#chkGUISMS').prop('checked', data_ar[0].GUISMS == "1" ? true : false);
        $('#chkNHANTHUOCTHEOHEN').prop('checked', data_ar[0].NHANTHUOCTHEOHEN == "1" ? true : false);
        $('#chkLINHTHUOCNGOAIVIEN').prop('checked', data_ar[0].LINHTHUOCNGOAIVIEN == "1" ? true : false);
        $('#chkDONGYQUYCHEBVDLCN').prop('checked', data_ar[0].DONGYQUYCHEBVDLCN == "1" ? true : false);
        $('#chkCOGIAYHEN').prop('checked', data_ar[0].SOGIAYHENKHAM == "1" ? true : false);
        $("#select2-cboNGUONKHACHID-container").text($("#cboNGUONKHACHID option:selected").text());
        $("#cboNHOMNGUON_ID").val($('#cboNGUONKHACHID option:selected').attr('extval0'));
        $("#select2-cboNHOMNGUON_ID-container").text($("#cboNHOMNGUON_ID option:selected").text());
        // thay doi thong tin dich vu
        if (_doicongkhamphongkham == "0") {
            if (data_ar[0].DICHVUID != null && data_ar[0].DICHVUID != "") {
                if (data_ar[0].PHONGKHAMID != null && data_ar[0].PHONGKHAMID != 'null') {
                    _loadphongkham(data_ar[0].DICHVUID, data_ar[0].PHONGKHAMID);
                } else {
                    _loadphongkham(data_ar[0].DICHVUID, "0");
                }
                //			$('#cboPHONGKHAMID').val(data_ar[0].PHONGID);
            }
        } else {
            if (data_ar[0].PHONGKHAMID != null && data_ar[0].PHONGKHAMID != "") {
                _loadYCKham($("#cboPHONGKHAMID").val(), data_ar[0].DICHVUID);
            }
        }

        if (data_ar[0].ANHBENHNHAN1 != null && data_ar[0].ANHBENHNHAN1 != "") {
            var imgTag = document.getElementById('imgBN');
            imgTag.src = data_ar[0].ANHBENHNHAN1;
        } else {
            //$('#imgBN').removeAttr('src');
            var imgTag = document.getElementById('imgBN');
            imgTag.src = '../common/image/patientImage.jpg';
        }

        $('#cboDIAPHUONGID').find('option').remove();
        var newOption = $('<option value="' + data_ar[0].DIAPHUONGID + '" selected>' + data_ar[0].TENDIAPHUONG + '</option>');
        $("#cboDIAPHUONGID").append(newOption);
        getDiaChi(data_ar[0].DIAPHUONGID, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID',
            data_ar[0].DIABANID, 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID',
            'txtTKDIABANID', 'cboDTBNID', docmathe, false);

        $("[name='radHINHTHUCVAOVIENID']").each(function () {
            $(this).prop('checked', $(this).val() == data_ar[0].HINHTHUCVAOVIENID);
        });
        $('#chkUUTIENKHAMID').prop('checked', data_ar[0].UUTIENKHAMID == "3");
        $('#chkTNGT').prop('checked', data_ar[0].TNGT == "1");

        if (data_ar[0].BHYT_DV == '1') {
            $('#chkCHECKBHYTDV').attr('checked', true);
        } else {
            $('#chkCHECKBHYTDV').attr('checked', false);
        }

        if ($('#cboDTBNID').val() == "1" || $('#cboDTBNID').val() == '6') {
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3), $("#cboBHYT_LoaiID").val());
        }

        _disableBHYT($('#cboDTBNID').val());

        // an hien nut chuyen tuyen
        if ($("#cboBHYT_LoaiID").val() != "2") {
            $("#btnChuyenTuyen").attr("disabled", "disabled");
        } else {
            $("#btnChuyenTuyen").removeAttr("disabled");
        }

        $('#cboNGHENGHIEPID').change();
        $('#cboDANTOCID').change();
        $("#cboBHYT_LoaiID").change();

        // fill data to objChuyenVien
        objChuyenVien["TKMANOIGIOITHIEU"] = data_ar[0].TKMANOIGIOITHIEU;
        objChuyenVien["MACHANDOANTUYENDUOI"] = data_ar[0].MACHANDOANTUYENDUOI;
        objChuyenVien["TKMACHANDOANTUYENDUOI"] = data_ar[0].TKMACHANDOANTUYENDUOI;
        objChuyenVien["CHANDOANTUYENDUOI"] = data_ar[0].CHANDOANTUYENDUOI;
        objChuyenVien["CV_CHUYENVIEN_HINHTHUCID"] = data_ar[0].CV_CHUYENVIEN_HINHTHUCID;
        objChuyenVien["CV_CHUYENVIEN_LYDOID"] = data_ar[0].CV_CHUYENVIEN_LYDOID;
        if (data_ar[0].CV_CHUYENDUNGTUYEN == "1") {
            objChuyenVien["CV_CHUYENDUNGTUYEN"] = "1";
        } else {
            objChuyenVien["CV_CHUYENDUNGTUYEN"] = "2";
        }
        // fill data to objSinhTon
        objSinhTon["KHAMBENH_MACH"] = data_ar[0].KHAMBENH_MACH;
        objSinhTon["KHAMBENH_NHIETDO"] = data_ar[0].KHAMBENH_NHIETDO;
        objSinhTon["KHAMBENH_HUYETAP_LOW"] = data_ar[0].KHAMBENH_HUYETAP_LOW;
        objSinhTon["KHAMBENH_HUYETAP_HIGH"] = data_ar[0].KHAMBENH_HUYETAP_HIGH;
        objSinhTon["KHAMBENH_NHIPTHO"] = data_ar[0].KHAMBENH_NHIPTHO;
        objSinhTon["KHAMBENH_CANNANG"] = data_ar[0].KHAMBENH_CANNANG;
        objSinhTon["KHAMBENH_CHIEUCAO"] = data_ar[0].KHAMBENH_CHIEUCAO;
        _trangthaikhambenhcheck = data_ar[0].TRANGTHAIKHAMBENH;

        if (data_ar[0].TRANGTHAIKHAMBENH != 9) {
            _flgModeView = '0';
            _disableControl(['toolbarIdbtnTreat', 'toolbarIdbtnhandling', 'toolbarIdbtnHoaHong', 'toolbarIdbtnThuTien', 'toolbarIdtreat_2', 'toolbarIdbtnThuTienKhac'], false);

            if (data_ar[0].TRANGTHAIKHAMBENH == 1) {
                _disableControl(['btnKhamNhieuPhong', 'btnSinhTon', 'btnChupHinh', 'btnLuu', 'btnKyLuuIn'], false);
                if (cfObj.NGT_AN_CD_CLS_BHYT == "1" && data_ar[0].DTBNID == "1") {
                    $('#toolbarIdtreat_2').hide();
                } else {
                    $('#toolbarIdtreat_2').show();
                }
            } else {
                _disableControl(['btnKhamNhieuPhong', 'btnSinhTon', 'btnChupHinh', 'btnLuu', 'btnKyLuuIn'], true);
            }


        } else {
            _flgModeView = '1';
            _disableControl(['toolbarIdbtnTreat', 'toolbarIdbtnhandling', 'toolbarIdbtnHoaHong', 'toolbarIdbtnThuTien', 'toolbarIdtreat_2', 'toolbarIdbtnThuTienKhac'], true);
            _disableControl(['btnKhamNhieuPhong', 'btnSinhTon', 'btnChupHinh', 'btnLuu'], true);
        }

        if (data_ar[0].SLXN != 0) {
            $('#tabXetNghiemTab').text("Xét nghiệm(" + data_ar[0].SLXN + ")");
        } else {
            $('#tabXetNghiemTab').text("Xét nghiệm");
        }

        if (data_ar[0].SLCDHA != 0) {
            $('#tabCDHATab').text("CĐHA(" + data_ar[0].SLCDHA + ")");
        } else {
            $('#tabCDHATab').text("CĐHA");
        }

        if (data_ar[0].THUKHAC != 0) {
            $('#tabPhieuThuKhacTab').text("Phiếu thu khác(" + data_ar[0].THUKHAC + ")");
        } else {
            $('#tabPhieuThuKhacTab').text("Phiếu thu khác");
        }

        if (data_ar[0].SLCHUYENKHOA != 0) {
            $('#tabPTTTTab').text("Phẫu thuật thủ thuật(" + data_ar[0].SLCHUYENKHOA + ")");
        } else {
            $('#tabPTTTTab').text("Phẫu thuật thủ thuật");
        }
        if (data_ar[0].CONGKHAM != 0) {
            $('#tabCongKhamTab').text("Tiền công khám(" + data_ar[0].CONGKHAM + ")");
        } else {
            $('#tabCongKhamTab').text("Tiền công khám");
        }
        if (data_ar[0].SLTHUOC != 0) {
            $('#tabThuocTab').text("Thuốc(" + data_ar[0].SLTHUOC + ")");
        } else {
            $('#tabThuocTab').text("Thuốc");
        }
        if (data_ar[0].SLVATTU != 0) {
            $('#tabVatTuTab').text("Vật tư(" + data_ar[0].SLVATTU + ")");
        } else {
            $('#tabVatTuTab').text("Vật tư");
        }

        $('#msgCapnhat').text('');
        if ($('#hidTIEPNHANID').val().trim() != "") {
            _disableControl(['cboDICHVUID', 'cboPHONGKHAMID', 'btnSetPK'], true);
        }

        if (doituong_khaibao == 1) {
            $('#cboDTBNID option').removeAttr('selected').filter('[value=val1]').attr('selected', true);
            $("#cboDTBNID").find("option[extval0='" + data_ar[0].SUB_DTBNID + "']").prop("selected", true);
        }

        // sondn L2PT-24929
        if (_hd != undefined && _hd === '1') {
            _loadHopDong(data_ar[0].LOAIKHAM, data_ar[0].HOPDONGID);
        }

        _thaoTacHientai = "";
        //_setUuTienDOB();

        //L2PT-19096
        var par = ['HIS_QUET_CCCD'];
        var _cccd = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
        if ($("#txtSOCMTND").val() == "" && _cccd == "1" && $("#hidQUETSOCMTND").val() != "") {
            $("#txtSOCMTND").val($("#hidQUETSOCMTND").val())
        }

        if (_canhbaotraituyen == "1" && data_ar[0].BHYT_LOAIID == "4" && hosobenhanid_compare !== data_ar[0].HOSOBENHANID) {
            hosobenhanid_compare = data_ar[0].HOSOBENHANID;
            DlgUtil.showMsg("[LƯU Ý]: Tuyến của thẻ BHYT đang là Trái Tuyến");
        }
        if (showTTBNWidget) {
            $('#divMsg').show();
            $('#lblMSG_MABENHAN').html(data_ar[0].MABENHAN);
            $('#lblMSG_TENBENHNHAN').html(data_ar[0].TENBENHNHAN);
            $('#lblMSG_NGAYSINH').html(data_ar[0].NGAY_SINH);
            $('#lblMSG_GIOITINH').html(data_ar[0].GIOITINHID == "1" ? "Nam" : "Nữ" );
            $('#lblMSG_DIACHI').html(data_ar[0].DIACHI);
        }
    }

    function unRowSelect(item_id) {
        if (item_id != null && item_id.length > 0) {
            $("#grdDanhSachTiepNhan").jqGrid("resetSelection");
        }
    }

    // SONDN L2PT-14425 01/01/2020
    $("#txtDIACHI").focusout(function () {
        if ($("#txtDIACHI").val().trim() != "" && $("#cboNGHENGHIEPID").val() == "3") {
            $("#txtDIACHINGUOITHAN").val($("#txtDIACHI").val());
        }
    });
    // END SONDN L2PT-14425 01/01/2020

    // SONDN L2PT-15754 07/02/2020
    $("#txtSONHA").focusout(function () {
        var _str = $("#txtSONHA").val().trim();
        _str = _str.substring(0, 1).toUpperCase() + _str.substring(1, _str.length);
        $("#txtSONHA").val(_str);
    });
    // END SONDN L2PT-15754 07/02/2020

    $("#txtTENNGUOITHAN").focusout(function () {
        var str = $("#txtTENNGUOITHAN").val();
        str = ucWorl(str);
        $("#txtTENNGUOITHAN").val(str);
    });

    //L2PT-102355 start format ho ten cha me
    $("#txtHO_TEN_CHA").focusout(function () {
        var str = $("#txtHO_TEN_CHA").val();
        str = ucWorl(str);
        $("#txtHO_TEN_CHA").val(str);
    });

    $("#txtHO_TEN_ME").focusout(function () {
        var str = $("#txtHO_TEN_ME").val();
        str = ucWorl(str);
        $("#txtHO_TEN_ME").val(str);
    });
    //L2PT-102355 end

    $("#txtMATHEGIFT").focusout(function () {
        var _stt = $("#txtMATHEGIFT").val().trim();
        if (_stt != "") {
            $("#txtMATHEGIFT").val(_stt);
            var _objj = new Object();
            _objj.MATHEGIFT = _stt;
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT01T001.MATHEGIFT", JSON.stringify(_objj));
            $("#lblLANSDGIFT").text(ret);
        }
    });

    // sondn L2PT-1038
    function isNumeric(str) {
        if (typeof str != "string") return false // we only process strings!
        return !isNaN(str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
            !isNaN(parseFloat(str)) // ...and ensure strings of whitespace fail
    }

    // end sondn L2PT-1038

    //L2PT-16155
    $("#txtSOCMTND").focusout(function () {
        var par = ['HIS_QUET_CCCD'];
        var _checkCCCD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
        if (_checkCCCD == '1') {
            if ($("#txtSOCMTND").val()) {
                var _cccd = $("#txtSOCMTND").val().split("|");
                $("#hidQUETSOCMTND").val(_cccd[0]);
                if (_cccd.length > 1) {
                    $("#txtSOCMTND").val(_cccd[0]);
                    $("#txtTENBENHNHAN").val(_cccd[2]);
                    $("#hidQUETSOCMTND").val(_cccd[0]); // L2PT-19096

                    if (_cccd[3] && _cccd[3].length == "4") {
                        $("#txtNAMSINH").val(_cccd[3]);
                        $("#txtNGAYSINH").val("");
                        $("#txtNAMSINH").trigger("change");
                    } else {
                        $("#txtNGAYSINH").trigger("focusin");
                        $("#txtNGAYSINH").val(_cccd[3].substring(0, 2) + "/" + _cccd[3].substring(2, 4) + "/" + _cccd[3].substring(4, 8));
                        $("#txtNGAYSINH").change();
                    }

                    $("#txtTKGIOITINHID").val(_cccd[4] == "Nam" ? 1 : 2);
                    $("#cboGIOITINHID").val(_cccd[4] == "Nam" ? 1 : 2);
                    $("#select2-cboGIOITINHID-container").text($("#cboGIOITINHID option:selected").text());
                    if (_cccd[5] != 'null null' && _cccd[5] != 'null' && _cccd[5] != '') {
                        $("#txtDIACHI").val(_cccd[5]);
                        getDiaChi_BHYT(_cccd[5], _options._province_id);
                    }
                    $("#txtNGAYCAPCMND").val(_cccd[6].substring(0, 2) + "/" + _cccd[6].substring(2, 4) + "/" + _cccd[6].substring(4, 8))
                }

                var ret1 = _callCheckCong($("#txtSOCMTND").val());

                if (ret1.hasOwnProperty("maKetQua") && ret1.hasOwnProperty("gtTheDen") && ret1.hasOwnProperty("gtTheTu")
                    && ret1.maThe != null && ret1.maThe != 'null' && ret1.maThe != '') {
                    // end sondn L2PT-1038
                    $("#txtMA_BHYT").val(ret1.maThe);
                    docmathe = true;
                    _timKiemBenhNhan($("#txtMA_BHYT").val().toUpperCase(), '2');
                    if (cfObj.NGT_TN_GIUSOCCCD == '1') {
                        $("#txtSOCMTND").val(_cccd[0]);
                    }
                    _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());

                    if (ret1.gioiTinh == null || ret1.gioiTinh == 'null' || ret1.gioiTinh == '') {
                        $("#txtTKGIOITINHID").val(-1);
                        $("#cboGIOITINHID").val(-1);
                    } else {
                        $("#txtTKGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                        $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                    }
                    $('#txtBHYT_BD').val(ret1.gtTheTu);
                    $('#txtBHYT_KT').val(ret1.gtTheDen);
                    $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
                    $("#txtNGAYDU5NAM").change();
                    $("#txtDIACHI_BHYT").val(ret1.diaChi);
                    if ($("#txtDIACHI").val() == "") {
                        $("#txtDIACHI").val(ret1.diaChi); 					// sondn L2PT-28917
                    }
                    getDiaChi_BHYT(ret1.diaChi, ret1.maThe.trim().substring(3, 5));
                    if (ret1.maKV != "") {
                        noisong = ret1.maKV;
                    }
                    // SONDN 19/03/2020 L2PT-17643
                    for (i = 1; i <= 3; i++) {
                        if (ret1.maKV == "K" + i) {
                            $("#cboDT_SINHSONG").val(i);
                        }
                    }
                    // END SONDN 19/03/2020 L2PT-17643

                    $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                    $("#txtMA_KCBBD").val(ret1.maDKBD);
                    $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBD);

                    if (ret1.ngaySinh.length == "4") {
                        $("#txtNAMSINH").val(ret1.ngaySinh);
                        $("#txtNGAYSINH").val("");
                        $("#txtNAMSINH").change();
                    } else {
                        $("#txtNGAYSINH").val(ret1.ngaySinh);
                        $("#txtNGAYSINH").change();
                    }
                    setNghenghiep($('#txtMA_BHYT').val());
                    if(ret1.dsLichSuKCB2018 != null){
                       _lichsudt =  ret1.dsLichSuKCB2018[0].tinhTrang;
                    }                    
                    // sondn L2PT-27548
                    // 003: Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới.
                    if (ret1.maKetQua == "003") {
                        if (ret1.hasOwnProperty("maTheMoi")) {
                            $("#txtMA_BHYT").val(ret1.maTheMoi);
                            $('#txtBHYT_BD').val(ret1.gtTheTuMoi);
                            $('#txtBHYT_KT').val(ret1.gtTheDenMoi);
                            $("#txtMA_KCBBD").val(ret1.maDKBDMoi);
                            $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBDMoi);
                            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().replace(/[-]/g, "").substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
                            DlgUtil.showMsg("Bệnh nhân đã được cấp thẻ mới, mã thẻ là: " + ret1.maTheMoi);
                        } else {
                            DlgUtil.showMsg("Trong dữ liệu trả về mã thẻ mới không tồn tại, yêu cầu kiểm tra lại thông tin cổng BHXH. ");
                        }
                    }
                    // end sondn L2PT-27548

                } else if (ret1 == "0" || ret1.maKetQua == "050") {
                    var par1 = ['NGT_TIMKIEM_THEO_CCCD'];
                    var _tkbnsdt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par1.join('$'))
                    if (_tkbnsdt == "1" && $("#hidBENHNHANID").val() == "") {
                        var _strSDT = jsonrpc.AjaxJson.ajaxExecuteQueryO("GET_BN_CCCD", [{
                            "name": "[0]",
                            "value": $("#txtSOCMTND").val()
                        }]);
                        if (_strSDT && _strSDT.length > 0) {
                            rows = JSON.parse(_strSDT)
                            $("#txtTENBENHNHAN").val(rows[0].TENBENHNHAN);
                            $("#txtNGAYSINH").val(rows[0].NGAYSINH);
                            $("#cboGIOITINHID").val(rows[0].GIOITINHID);
                            $("#txtNAMSINH").val(rows[0].NAMSINH);

                            var _objBenhNhan = new Object();
                            _objBenhNhan["tenbenhnhan"] = $("#txtTENBENHNHAN").val().toUpperCase().trim();
                            _objBenhNhan["ngaysinh"] = $("#txtNGAYSINH").val() != "" ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val().trim();
                            _objBenhNhan["gioitinhid"] = $("#cboGIOITINHID").val();
                            if (_tkbnf3 == "1") {
                                _objBenhNhan["ngaysinh"] = $("#txtNAMSINH").val();
                            }

                            if (_tkbnf3 == "2") {
                                _objBenhNhan["gioitinhid"] = "-1";
                            }
                            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN", '' + '$' + '3' + '$' + JSON.stringify(_objBenhNhan));
                            if (data_ar != null && data_ar.length > 0) {
                                var _ns = ($("#txtNGAYSINH").val() == "" && $("#txtNAMSINH").val() == "")
                                    ? "-1" :
                                    ($("#txtNGAYSINH").val() != ""
                                            ? $("#txtNGAYSINH").val() : $("#txtNAMSINH").val()
                                    );
                                var param = {
                                    ten: $("#txtTENBENHNHAN").val() == '' ? '-1' : $("#txtTENBENHNHAN").val().toUpperCase().trim(),
                                    ngaysinh: _ns,
                                    gioitinh: $("#cboGIOITINHID").val(),
                                    mabhyt: '-1',
                                    type: '1' // ko cap nhat
                                };

                                if (_tkbnf3 == "1") {
                                    param.ngaysinh = $("#txtNAMSINH").val();
                                }

                                if (_tkbnf3 == "2") {
                                    param.gioitinh = "-1";
                                }
                                _showDialog("noitru/NTU01H013_TimKiemBenhNhan", param, 'TÌM KIẾM BỆNH NHÂN', 980, 510, {closeButton: false});
                            }
                        }
                    } else {
                        return;
                    }
                } else {
                    if (ret1.maKetQua == "CAQD") {
                        DlgUtil.showMsg(ret1.noiDungKetQua);
                        $("#chkCHECKCONG").prop("checked", false);
                    } else {
                        DlgUtil.showMsg("Không kết nối được với cổng BHXH, yêu cầu kiểm tra lại" + ret1.ghiChu);
                        return;
                    }
                }
            }
        }
    });

    //DOC QR CODE THE BHYT
    $("#txtMA_BHYT").focusout(function () {
        if (cfObj.NGT_TIEPNHAN_BNCU == "1") {
            if ($("#hidBENHNHANID").val() == '') {
                resetThongTinCoBan();
            }
        } else {
            resetThongTinCoBan();
        }
        _trangthaikhambenhcheck = 0;
        var sobhyt = $("#txtMA_BHYT").val().trim().toUpperCase();
        // sondn L2PT-1038
        if ((sobhyt.length == 10) && (sobhyt.indexOf("_") < 0)) {
            // TH1: Nhap tay ma so BHXH 10 ky tu
            _timKiemBenhNhan($("#txtMA_BHYT").val().toUpperCase(), '6');
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
        } else
            // end sondn L2PT-1038
        if ((sobhyt.length == 15) && (sobhyt.indexOf("_") < 0)) {
            // nhap tay;
            if (_mabhytgoc == "") {
                _mabhytgoc = sobhyt;
            }
            _timKiemBenhNhan($("#txtMA_BHYT").val().toUpperCase(), '2');
            _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
        } else if ((sobhyt.length > 15) && (sobhyt.indexOf("|") > -1)) {
            // nhap barcode
            docmathe = true;
            _logs_qrcode = sobhyt;
            var sobhyt_catchuoi = sobhyt.split("|");
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKMABHYT", sobhyt_catchuoi[0].trim() + "$");
            if (_mabhytgoc == "") {
                _mabhytgoc = sobhyt_catchuoi[0].trim();
            }
            if (data_ar.length > 0) {
                // sondn L2PT-1038
                if (sobhyt_catchuoi[0].trim().length == 10) {
                    _timKiemBenhNhan(sobhyt_catchuoi[0].trim(), '6');
                    _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
                } else {
                    _timKiemBenhNhan(sobhyt_catchuoi[0].trim(), '2');
                    _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
                }
                // end sondn L2PT-1038
            } else {
                $("#txtMA_BHYT").val(sobhyt_catchuoi[0].trim());
                $("#txtTENBENHNHAN").val(convert_utf8totext(sobhyt_catchuoi[1]));
                if (sobhyt_catchuoi[2].trim().length > 4) {
                    $("#txtNGAYSINH").val(sobhyt_catchuoi[2].trim());
                    $("#txtNGAYSINH").trigger("change");
                } else {
                    $("#txtNAMSINH").val(sobhyt_catchuoi[2].trim());
                    $("#txtNAMSINH").trigger("change");
                }

                $("#txtBHYT_BD").val(sobhyt_catchuoi[6]);
                $("#txtBHYT_KT").val(sobhyt_catchuoi[7]);

                var kv = 0;
                switch (sobhyt_catchuoi[11]) {
                    case "5":
                        kv = 1;
                        break;
                    case "6":
                        kv = 2;
                        break;
                    case "7":
                        kv = 3;
                        break;
                    default:
                        kv = 0;
                        break;
                }
                noisong = kv;
                $("#cboDT_SINHSONG").val(kv);

                var diachi = convert_utf8totext(sobhyt_catchuoi[4]);
                var sql_par1 = [];

                // sondn L2PT-1038 : phan load thong tin su dung ma tinh, ma huyen cua the BHYT không ton tai voi the 10 so;
                if (sobhyt_catchuoi[0].trim().length == 15) {
                    // var matinh = sobhyt_catchuoi[0].trim().substring(3,5);
                    // $("#cboHC_TINHID").find("option[extval1='"+matinh+"']").attr("selected","selected");
                    // $("#txtTKHC_TINHID").val($('#cboHC_TINHID'+" option:selected").attr('extval0'));
                    // sql_par1.push({"name":"[0]","value":$("#cboHC_TINHID").val()});
                    // ComboUtil.getComboTag("cboHC_HUYENID", _sqldiaphuong,sql_par1,"",{extval: true,value:'',text:'Chọn'},"sql","",function(){
                    // 	var mahuyen = sobhyt_catchuoi[0].trim().substring(5,7);
                    // 	$("#cboHC_HUYENID").find("option[extval1='"+mahuyen+"']").attr("selected","selected");
                    // 	$("#txtTKHC_HUYENID").val($('#cboHC_HUYENID'+" option:selected").attr('extval0'));

                    // 	sql_par1=[];
                    // 	sql_par1.push({"name":"[0]","value":$("#cboHC_HUYENID").val()});
                    // 	ComboUtil.getComboTag("cboHC_XAID",_sqldiaphuong,sql_par1, "",{extval: true,value:'',text:'Chọn'},"sql","",function(){
                    // 		$("#cboHC_XAID option").filter(function() {
                    // 			var sd = diachi.split('-');
                    // 			return this.text == (sd[sd.length-3]='undefined'?'':sd[sd.length-3].trim());
                    // 		}).attr('selected', true);
                    // 		$("#txtTKHC_XAID").val($('#cboHC_XAID'+" option:selected").attr('extval0'));
                    // 	});
                    // });
                    getDiaChi_BHYT(diachi, sobhyt_catchuoi[0].trim().substring(3, 5));
                }
                // end sondn L2PT-1038

                $("#txtDIACHI_BHYT").val(diachi);

                if (_dongbodiachibhyt == "1") {
                    $("#txtDIACHI").val(diachi);
                }


                $("#cboGIOITINHID").val(sobhyt_catchuoi[3] == "1" ? "1" : "2");
                $("#txtTKGIOITINHID").val(sobhyt_catchuoi[3] == "1" ? "1" : "2");

                var noidk = sobhyt_catchuoi[5].trim().replace(" – ", "").replace("-", "").replace(" ", "").replace(" ", "");
                $("#txtMA_KCBBD").val(noidk);
                $('#txtMA_KCBBD').combogrid("setValue", noidk);

                // sondn IT360-241757
                if (_ngtsettuyen == "0") {
                    var _kcbbd1 = $("#txtKCBBD").val().trim();
                    if (_kcbbd1.indexOf(noidk) != -1) {
                        $("#cboBHYT_LoaiID").val('1'); 				// dung tuyen;
                        $("#cboBHYT_LoaiID").change();
                    } else {
                        $("#cboBHYT_LoaiID").val(_tuyenmacdinhqt); 				// IT360-350302
                        $("#cboBHYT_LoaiID").change();
                    }
                }
                // end sondn IT360-241757


                // muc huong bhyt
                _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3), $("#cboBHYT_LoaiID").val());
                tinhTuoi(sobhyt_catchuoi[2], 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
                _setgiatribatbuoc(['lblHuyen', 'lblXa'], '');
                $("#txtTENNGUOITHAN").val(sobhyt_catchuoi[10]);
            }
        }

        // override 1 so thong tin trong form tiep nhan benh nhan;
        var sobhyt_catchuoi = sobhyt.split("|");
        if (sobhyt_catchuoi[0] != "") {
            if ((sobhyt.length > 15) && (sobhyt.indexOf("|") > -1)) {
                var tenbn = convert_utf8totext(sobhyt_catchuoi[1]);
                var diachibh = convert_utf8totext(sobhyt_catchuoi[4]);

                $("#txtTENBENHNHAN").val(tenbn);
                $("#txtMA_BHYT").val(sobhyt_catchuoi[0].trim());
                if (sobhyt_catchuoi[2].trim().length > 4) {
                    $("#txtNGAYSINH").val(sobhyt_catchuoi[2].trim());
                    $("#txtNGAYSINH").trigger("change");
                } else {
                    $("#txtNAMSINH").val(sobhyt_catchuoi[2].trim());
                    $("#txtNAMSINH").trigger("change");
                }

                $("#txtDIACHI_BHYT").val(diachibh);
                $("#txtBHYT_BD").val(sobhyt_catchuoi[6]);
                $("#txtBHYT_KT").val(sobhyt_catchuoi[7]);

                var noidk = sobhyt_catchuoi[5].trim().replace(" – ", "").replace("-", "").replace(" ", "").replace(" ", "");
                $("#txtMA_KCBBD").val(noidk);
                $('#txtMA_KCBBD').combogrid("setValue", noidk);

                $("#cboGIOITINHID").val(sobhyt_catchuoi[3] == "1" ? "1" : "2");
                $("#txtTKGIOITINHID").val(sobhyt_catchuoi[3] == "1" ? "1" : "2");
                $('#chkDU5NAM6THANGLUONGCOBAN').attr('checked', false);
                $('#chkTRADU6THANGLCB').attr('checked', false);
            }
        }

        if ($("#txtMA_BHYT").val().length > 15) {
            DlgUtil.showMsg("Mã thẻ không được quá 15 ký tự", function () {
                $("#txtMA_BHYT").select();
            });
            return;
        }

        var _checkthekhoa = _checkMaTheKhoa();
        if (_checkthekhoa != "") {
            DlgUtil.showMsg(_checkthekhoa);
            return;
        }

        _checkMaHoNgheo(); 					// tu dong check ma ho ngheo combobox va label;
        popUpTrangThaiKham();
        _setUuTienDOB();

        if ($("#hidSHOWCHECKCONGBHXH").val() == "2"
            && _clickchonbn == "0"
            && $("#cboDTBNID").val() == "1"
            && $('#chkCOGIAYKS').is(":checked") == false
            && $("#txtMA_BHYT").val().trim() != ""
            && $("#txtTENBENHNHAN").val().trim() != ""
            && ($("#txtNGAYSINH").val() != "" || $("#txtNAMSINH").val() != "")) {
            openPopUpCheckLSKCB(0.85, 0.83);
        }

        // sondn L2PT-26313
        if (_checkcongquetthe == "1"
            && ($("#cboDTBNID").val() == "1" || $("#cboDTBNID").val() == "6")
            && ($('#hidSINHTHEBHYT').val().trim() != "1" || ($('#hidSINHTHEBHYT').val().trim() == "1" && _sinhthete == "3"))
        ) {

            var ret1 = _callCheckCong();

            if (ret1.hasOwnProperty("maKetQua") && ret1.hasOwnProperty("gtTheDen") && ret1.hasOwnProperty("gtTheTu")
                && ret1.maThe != null && ret1.maThe != 'null' && ret1.maThe != '') {
                docmathe = true;
                // sondn L2PT-1038 : Neu ma the 10 ky tu, check cong xong lay ve the 15 ky tu fill lai;
                if ($("#txtMA_BHYT").val().length == 10) {
                    DlgUtil.showMsg("Mã số:" + ret1.maSoBHXH + ". Check từ cổng mã BHYT: " + ret1.maThe);
                    $("#txtMA_BHYT").val(ret1.maThe);
                    _timKiemBenhNhan($("#txtMA_BHYT").val().toUpperCase(), '2');
                    _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
                }
                // end sondn L2PT-1038
                setNghenghiep($('#txtMA_BHYT').val());
                if (ret1.gioiTinh == null || ret1.gioiTinh == 'null' || ret1.gioiTinh == '') {
                    $("#txtTKGIOITINHID").val(-1);
                    $("#cboGIOITINHID").val(-1);
                } else {
                    $("#txtTKGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                    $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                }
                $('#txtBHYT_BD').val(ret1.gtTheTu);
                $('#txtBHYT_KT').val(ret1.gtTheDen);
                $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
                $("#txtDIACHI_BHYT").val(ret1.diaChi);
                if ($("#txtDIACHI").val() == "") {
                    $("#txtDIACHI").val(ret1.diaChi); 					// sondn L2PT-28917
                    getDiaChi_BHYT(ret1.diaChi, ret1.maThe.trim().substring(3, 5));
                }
                if(ret1.dsLichSuKCB2018 != null){
                    _lichsudt =  ret1.dsLichSuKCB2018[0].tinhTrang;
                } 

                // SONDN 19/03/2020 L2PT-17643
                for (i = 1; i <= 3; i++) {
                    if (ret1.maKV == "K" + i) {
                        $("#cboDT_SINHSONG").val(i);
                    }
                }
                // END SONDN 19/03/2020 L2PT-17643

                $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                $("#txtMA_KCBBD").val(ret1.maDKBD);
                $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBD);

                if (ret1.ngaySinh.length == "4") {
                    $("#txtNAMSINH").val(ret1.ngaySinh);
                    $("#txtNGAYSINH").val("");
                    $("#txtNAMSINH").change();
                } else {
                    $("#txtNGAYSINH").val(ret1.ngaySinh);
                    $("#txtNGAYSINH").change();
                }

                // sondn L2PT-27548
                // 003: Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới.
                if (ret1.maKetQua == "003") {
                    if (ret1.hasOwnProperty("maTheMoi")) {
                        $("#txtMA_BHYT").val(ret1.maTheMoi);
                        $('#txtBHYT_BD').val(ret1.gtTheTuMoi);
                        $('#txtBHYT_KT').val(ret1.gtTheDenMoi);
                        $("#txtMA_KCBBD").val(ret1.maDKBDMoi);
                        $('#txtMA_KCBBD').combogrid("setValue", ret1.maDKBDMoi);
                        _muchuong_bhyt(_options._khoaid, $("#txtMA_BHYT").val().substr(0, 3).toUpperCase(), $("#cboBHYT_LoaiID").val());
                        DlgUtil.showMsg("Bệnh nhân đã được cấp thẻ mới, mã thẻ là: " + ret1.maTheMoi);
                    } else {
                        DlgUtil.showMsg("Trong dữ liệu trả về mã thẻ mới không tồn tại, yêu cầu kiểm tra lại thông tin cổng BHXH. ");
                    }
                }
                // end sondn L2PT-27548

            } else if (ret1 == "0") {
                // DlgUtil.showMsg("Thiếu thông tin họ tên / mã BHYT / ngày sinh, yêu cầu bổ sung để check cổng");
                return;
            } else {
                if (ret1.maKetQua == "CAQD") {
                    DlgUtil.showMsg(ret1.noiDungKetQua);
                    $("#chkCHECKCONG").prop("checked", false);
                } else {
                    DlgUtil.showMsg("Không kết nối được với cổng BHXH, yêu cầu kiểm tra lại" + ret1.ghiChu);
                    return;
                }
            }
        }
        // end sondn L2PT-26313

        //HungNd - L2PT-65749
        if (cfObj.NGT_TN_CHECKLAMDUNG == '1') {
            var maThe = $("#txtMA_BHYT").val().replace(/[-]/g, "").trim();
            var ngayKham = $("#txtNGAYTIEPNHAN").val();
            var maKCBBD = _options.hospital_code; 															// MA DON VI DUNG CHECK ;

            ngayKham = ngayKham.substring(8, 10) + ngayKham.substring(3, 5) + ngayKham.substring(0, 2);
            var ret = _HISL3API_LDT_GW(maThe, maKCBBD, ngayKham);
            if (ret.statusCode == "000") {
                try {
                    var _kq = JSON.parse(ret.data);
                    if (_kq.hasOwnProperty("result") && _kq.result.length > 0) {
                        for (i = 0; i < _kq.result.length; i++) {
                            if (_kq.result[i].state == true && _kq.result[i].result.length > 0) {
                                for (j = 0; j < _kq.result[i].result.length; j++) {
                                    var _kqq = _kq.result[i].result[j];
                                    if (_kqq.MA_CSKCB != _opts.hospital_code) {
                                        $("#toolbarIdbtnLSKCBGW").trigger('click');
                                        return;
                                    }

                                }
                            }
                        }
                    }

                } catch (e) {
                    DlgUtil.showConfirm("GATEWAY: Lỗi thông tin trả về Lạm dụng thẻ không đúng định dạng. Yêu cầu kiểm tra lại API. Bạn có muốn tiếp tục? ", function (flag) {
                    });

                }

            } else if (ret.statusCode == "099") {
                // qua thoi gian quy dinh;
                DlgUtil.showConfirm("GATEWAY: Không tổng hợp được lịch sử kcb tại các csyt khác trong tỉnh, vui lòng liên hệ admin kiểm tra. Bạn có muốn tiếp tục? ", function (flag) {
                });
            } else {
                // khong co du lieu;
                DlgUtil.showConfirm("GATEWAY: Không tổng hợp được lịch sử kcb tại các csyt khác trong tỉnh, vui lòng liên hệ admin kiểm tra. Bạn có muốn tiếp tục? ", function (flag) {
                });
            }
        }
        //End HungNd
    });

    $("#txtDIACHI_BHYT").focusout(function () {
        _checkMaHoNgheo(); 					// tu dong check ma ho ngheo combobox va label;
    });

    function _checkMaHoNgheo() {
        var mabhyt = $("#txtMA_BHYT").val().trim().toUpperCase();
        if (mabhyt.substr(0, 2) == 'GD' || mabhyt.substr(0, 2) == 'CN') {
            $("#cboDOITUONGDB").prop('disabled', false);
            $("#txtMADOITUONGNGHEO").prop('disabled', false);
        } else {
            $("#cboDOITUONGDB").prop('disabled', true);
            $("#txtMADOITUONGNGHEO").prop('disabled', true);
        }

        var macheckngheo = $("#txtBATCHECKNGHEO").val();
        if (macheckngheo == "00" || macheckngheo == "" || macheckngheo == "0") {
            return;
        }
        $("#txtMADOITUONGNGHEO").val("");
        // Chi cho phep ma the: GD4 + matinh & CNx + matinh;
        if (!(
            (mabhyt.substr(0, 5) == 'GD4' + macheckngheo)
            || (mabhyt.substr(0, 2) == 'CN' && mabhyt.substr(3, 2) == macheckngheo)
        )) {
            return;
        }

        var dcbhyt = $("#txtDIACHI_BHYT").val().trim().toUpperCase();
        if (dcbhyt.length < 14) {
            return;
        }

        var madtncn = dcbhyt.substring(dcbhyt.length - 3, dcbhyt.length); 		// 3 ky tu cuoi;
        var dsdtncn = $("#txtMAHONGHEO").val().split(';');
        var dsdtn = dsdtncn[0];

        /*$("#cboDOITUONGDB").val("0");
        if(dsdtn.indexOf(madtncn) != -1){
        	$("#cboDOITUONGDB").val(1);
        }else{
        	$("#cboDOITUONGDB").val(2);
        }*/

        var strdoituongngheo = dcbhyt.substring(dcbhyt.length - 14, dcbhyt.length);
        if (strdoituongngheo != "" && (dsdtncn[0].indexOf(madtncn) != -1 || dsdtncn[1].indexOf(madtncn) != -1)) {
            $("#txtMADOITUONGNGHEO").val(strdoituongngheo);

            $("#cboDOITUONGDB").val(0);
            ComboUtil.findByExtra("cboDOITUONGDB", mabhyt.substr(0, 2), 1);

            if ($("#cboDOITUONGDB").val() != '0') {
                $("#cboDOITUONGDB").attr('style', 'background-color:aquamarine;');
            } else {
                $("#cboDOITUONGDB").attr('style', 'background-color:white;');
            }
        }
    }

    function _checkMaTheKhoa() {
        // sondn L2PT-1038 : ma the BHXH 10 ky tu nen k check ;
        var _mabhyt = $("#txtMA_BHYT").val().trim();
        if (_mabhyt.length == 10 && isNumeric(_mabhyt)) {
            return '';
        }
        // end sondn L2PT-1038

        var _parKhoa = [$("#txtMA_BHYT").val().trim().toUpperCase().substring(0, 3)];
        if ($("#txtMA_BHYT").val().trim() == "") {
            return '';
        }
        var resultKhoa = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CHECK.THE.KHOA", _parKhoa.join('$'));
        if (resultKhoa == '1') {
            return 'Mã đầu thẻ đã bị khóa';
        } else if (resultKhoa == '2') {
            return 'Mã đầu thẻ không tồn tại';
        }
        return '';
    }

    function popUpTrangThaiKham() {
        if (_trangthaikhambenhcheck == "4") {
            if (_popupconthuoc == "0") {
                DlgUtil.showMsg("Bệnh nhân đang khám.");
            } else {
                $.bootstrapGrowl('Bệnh nhân đang khám.', {
                    type: 'danger',
                    delay: 3000,
                });
            }
        } else if (_trangthaikhambenhcheck == "9") {
            if (_popupconthuoc == "0") {
                DlgUtil.showMsg("Bệnh nhân đã khám xong.");
            } else {
                $.bootstrapGrowl('Bệnh nhân đã khám xong.', {
                    type: 'danger',
                    delay: 3000,
                });
            }
        }
    }

    //tinh toan muc huong bhyt
    function _muchuong_bhyt(i_phongid, i_madoituong, i_tuyen) {
        var hinhthucvaovienid = $.find("[name='radHINHTHUCVAOVIENID']:checked")[0].value;
        var objBH = new Object();
        objBH.MATHE = $("#txtMA_BHYT").val();
        objBH.DOITUONGSONG = $('#cboDT_SINHSONG').val();

        // WAIT FOR SYNCHRONIZATION BVNT
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.MUCHUONG.BHYT", i_phongid + '$' + i_madoituong.toUpperCase() + '$' + i_tuyen + '$' + JSON.stringify(objBH) + '$' + hinhthucvaovienid);
        // END WAIT FOR SYNCHRONIZATION BVNT

        if (data_ar != null && data_ar.length > 0) {
            var _bstr = "Ngoại (" + data_ar[0].MUCHUONG_NGOAI + "%)-Nội (" + data_ar[0].MUCHUONG_NOI + "%)";
            $("#txtMUCHUONG").val(_bstr);
            $("#hidMUCHUONG_NGT").val(data_ar[0].MUCHUONG_NGOAI);
            $("#txtQUYENLOI").val(i_madoituong.substr(2, 1));
            $("#hidBHYT_DOITUONG_ID").val(data_ar[0].BHYT_DOITUONG_ID);
        }
    }

    // disable thong tin bhyt
    function _disableBHYT(b_doituong_bhyt) {
        if (b_doituong_bhyt == '1' || b_doituong_bhyt == '6' || (_bhytdtmienphi == "1" && b_doituong_bhyt == "5")) {		// sondn L2PT-6385
            if (_loadFirst == 1) {
                var ID1 = ['txtTKMACHANDOANTUYENDUOI', 'txtCHANDOANTUYENDUOI', 'txtTKMANOIGIOITHIEU', 'cboMANOIGIOITHIEU'];
                _disableControl(ID1, _anthongtinchuyentuyen == "0" ? true : false);			// lan dau tien van disable phan nay;
            }

            var ID = ['txtMA_BHYT', 'chkCOGIAYKS', 'txtBHYT_BD', 'txtBHYT_KT', 'txtMA_KCBBD', 'cboMAKCBBD',
                'txtDIACHI_BHYT', 'txtTKBHYT_LoaiID', 'cboBHYT_LoaiID', 'cboDT_SINHSONG',
                'chkDAGIUTHEBHYT', 'cboDOITUONGDB', 'chkCHECKBHYTDV', 'cboDVTHUKHAC', 'chkCHECKCONG', 'chkHENKHAM'];
            if ($("#txtKHONGSUDUNG5NAM6THANG").val() == "0") {
                ID.push('chkDU5NAM6THANGLUONGCOBAN');
                ID.push('txtNGAYDU5NAM');
            }
            _disableControl(ID, false);
        } else {
            var ID1 = ['txtTKMACHANDOANTUYENDUOI', 'txtCHANDOANTUYENDUOI', 'txtTKMANOIGIOITHIEU', 'cboMANOIGIOITHIEU'];
            var ID2 = ['cboDVTHUKHAC'];
            var ID = ['txtMA_BHYT', 'chkCOGIAYKS', 'txtBHYT_BD', 'txtBHYT_KT', 'txtMA_KCBBD', 'cboMAKCBBD',
                'txtDIACHI_BHYT', 'txtTKBHYT_LoaiID', 'cboBHYT_LoaiID', 'cboDT_SINHSONG',
                'chkDAGIUTHEBHYT', 'cboDOITUONGDB', 'chkCHECKBHYTDV', 'chkCHECKCONG', 'chkHENKHAM'];

            if ($("#txtKHONGSUDUNG5NAM6THANG").val() == "0") {
                ID.push('chkDU5NAM6THANGLUONGCOBAN');
                ID.push('txtNGAYDU5NAM');
            }
            _disableControl(ID, true);
            _disableControl(ID1, _anthongtinchuyentuyen == "0" ? true : false);
            // sondn L2PT-27834
            _disableControl(ID2, _thukhacvpi == "0" ? true : false);
            // end sondn L2PT-27834
        }
    }

    // popup
    function _showDialog(url, param, title, w, h, close) {
        dlgPopup = DlgUtil.buildPopupUrl("dlgCV", "divDlg", "manager.jsp?func=../" + url, param, title, w, h, close);
        DlgUtil.open("dlgCV");
    }

    function _disableControl(arrID, value) {
        for (var i = 0; i < arrID.length; i++) {
            $("#" + arrID[i]).attr("disabled", value);
        }
    }

    function _setDIAPHUONG(donvi) { // 1:tinh; 2: huyen; 3: xa
        if (donvi == 1) {
            $('#txtTKHC_HUYENID').val('');
            $('#txtTKHC_XAID').val('');
            $('#txtTKHC_TINHID').val($('#cboHC_TINHID option:selected').attr('extval0'));
            _clearOption('cboHC_HUYENID', {value: '', text: 'Chọn'});
            _clearOption('cboHC_XAID', {value: '', text: 'Chọn'});
            if ($('#cboHC_TINHID').val() != '') {
                getDiaChi($('#cboHC_TINHID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', docmathe);
            } else {
                _setDiaChiBN('cboDTBNID', docmathe);
            }
        }

        if (donvi == 2) {
            if ($('#cboHC_HUYENID').val() != '') {
                getDiaChi($('#cboHC_HUYENID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', docmathe);
            } else {
                _clearOption('cboHC_XAID', {value: '', text: 'Chọn'});
                _setDiaChiBN('cboDTBNID', docmathe);
            }
            $('#txtTKHC_XAID').val('');
            $('#txtTKHC_HUYENID').val($('#cboHC_HUYENID option:selected').attr('extval0'));
        }

        if (donvi == 3) {
            getDiaChi($('#cboHC_XAID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', docmathe);
            $('#txtTKHC_XAID').val($('#cboHC_XAID option:selected').attr('extval0'));
        }

        _setDiaChiBN('cboDTBNID', docmathe);
    }

    function getDiaChi_BHYT(diachi, matinh) {
        var _diachi = diachi.split(/[-,]+/);
        var _matinh = _diachi[_diachi.length - 1].trim();
        $("#cboHC_TINHID option").filter(function () {
            return $(this).text() === _matinh;
        }).attr("selected", "selected");
        if ($("#cboHC_TINHID option").filter(function () {
            return $(this).text() === _matinh;
        }).attr("selected", "selected").length > 0) {
            $("#txtTKHC_TINHID").val($('#cboHC_TINHID' + " option:selected").attr('extval0'));
        } else {
            $("#cboHC_TINHID").find("option[extval1='" + '0' + matinh + "']").attr("selected", "selected");
            $("#txtTKHC_TINHID").val($('#cboHC_TINHID' + " option:selected").attr('extval0'));
            $("#select2-cboHC_TINHID-container").text($("#cboHC_TINHID option:selected").text());
        }
        _setDIAPHUONG(1);
        var sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": $("#cboHC_TINHID").val()});
        ComboUtil.getComboTag("cboHC_HUYENID", _sqldiaphuong, sql_par1, "", {
            extval: true,
            value: '',
            text: 'Chọn'
        }, "sql", "", function () {
            if (cfObj.NGT_TN2_MH_THE_BHYT == '1') {
                var mahuyen = _diachi[_diachi.length - 2].trim();
                $("#cboHC_HUYENID option").filter(function () {
                    return $(this).text() === mahuyen;
                }).attr("selected", "selected");
                if ($("#cboHC_HUYENID option").filter(function () {
                    return $(this).text() === mahuyen;
                }).attr("selected", "selected").length > 0) {
                    $("#txtTKHC_HUYENID").val($('#cboHC_HUYENID' + " option:selected").attr('extval0'));
                }
                _setDIAPHUONG(2);
                sql_par1 = [];
                sql_par1.push({"name": "[0]", "value": $("#cboHC_HUYENID").val()});
                ComboUtil.getComboTag("cboHC_XAID", _sqldiaphuong, sql_par1, "", {
                    extval: true,
                    value: '',
                    text: 'Chọn'
                }, "sql", "", function () {

                    var maxa = _diachi[_diachi.length - 3].trim();
                    $("#cboHC_XAID option").filter(function () {
                        return $(this).text() === maxa;
                    }).attr("selected", "selected");
                    if ($("#cboHC_XAID option").filter(function () {
                        return $(this).text() === maxa;
                    }).attr("selected", "selected").length > 0) {
                        $("#txtTKHC_XAID").val($('#cboHC_XAID' + " option:selected").attr('extval0'));
                    }
                    _setDIAPHUONG(3);
                });
            }
        });
    }

    function setNghenghiep(mathebhyt) {
        var ma = mathebhyt.substring(0, 2).toUpperCase();
        var ma1 = mathebhyt.substring(0, 3).toUpperCase();
        var value = 12;

        if (_nghenghiepbhyt == "0") {
            switch (ma) {
                case 'HT':
                    value = 5;
                    break;
                case 'HC':
                    value = 8;
                    break;
                case 'SV':
                    value = 4;
                    break;
                case 'HS':
                    value = 4;
                    break;
                case 'DN':
                    value = 1;
                    break;
                case 'TE':
                    value = 3;
                    break;
                case 'NN':
                    value = 18;
                    break;
                case 'CA':
                    value = 2;
                    break;
                case 'QN':
                    value = 2;
                    break;
                case 'CK':
                    value = 5;
                    break;
                default:
                    value = _nghenghiepdef;
            }
            if (ma1 == "CC1") {
                value = 14;
            }
        } else if (_nghenghiepbhyt == "1" || _nghenghiepbhyt == "2") {
            switch (ma) {
                // su dung cho cum K74
//				case 'QN':value = 6;break;
//				case 'CA':value = 6;break;
//				case 'CY':value = 6;break;
//				case 'XN':value = 8;break;
//				case 'MS':value = 5;break;
//				case 'CC':value = 5;break;
//				case 'CK':value = 5;break;
//				case 'CB':value = 5;break;
//				case 'KC':value = 5;break;
//				case 'HD':value = 7;break;
//				case 'TE':value = 1;break;
//				case 'BT':value = 5;break;
//				case 'HN':value = 5;break;
//				case 'DT':value = 5;break;
//				case 'DK':value = 5;break;
//				case 'XD':value = 5;break;
//				case 'TS':value = 5;break;
//				case 'TC':value = 5;break;
//				case 'TQ':value = 5;break;
//				case 'TA':value = 5;break;
//				case 'TY':value = 5;break;
//				case 'LS':value = 7;break;
//				case 'HC':value = 8;break;
//				case 'DN':value = 4;break;
//				case 'HS':value = 2;break;
//				case 'HT':value = 3;break;
//				case 'GD':value = 5;break;
//				default:value = 5;
                case 'QN':
                    value = 2;
                    break;
                case 'CA':
                    value = 2;
                    break;
                case 'CY':
                    value = 2;
                    break;
                case 'XN':
                    value = 8;
                    break;
                case 'MS':
                    value = 1;
                    break;
                case 'CC':
                    value = 1;
                    break;
                case 'CK':
                    value = 1;
                    break;
                case 'CB':
                    value = 1;
                    break;
                case 'KC':
                    value = 1;
                    break;
                case 'HD':
                    value = 7;
                    break;
                case 'TE':
                    value = 3;
                    break;
                case 'BT':
                    value = 1;
                    break;
                case 'HN':
                    value = 1;
                    break;
                case 'DT':
                    value = 1;
                    break;
                case 'DK':
                    value = 1;
                    break;
                case 'XD':
                    value = 1;
                    break;
                case 'TS':
                    value = 1;
                    break;
                case 'TC':
                    value = 1;
                    break;
                case 'TQ':
                    value = 1;
                    break;
                case 'TA':
                    value = 1;
                    break;
                case 'TY':
                    value = 1;
                    break;
                case 'LS':
                    value = 7;
                    break;
                case 'HC':
                    value = 8;
                    break;
                case 'DN':
                    value = 6;
                    break;
                case 'HS':
                    value = 4;
                    break;
                case 'HT':
                    value = 5;
                    break;
                case 'GD':
                    value = 1;
                    break;
                default:
                    value = _nghenghiepdef;
            }
        } else {
            // che do dinh nghia thu cong;
            var _nnArr = _nghenghiepbhyt.split('@'); 								//GD-1@CH-2@TE-3@0-4
            var _nnId = "0";
            // tim kiem dau the 3 ky tu;
            for (i = 0; i < _nnArr.length; i++) {
                if (ma1 == _nnArr[i].split('-')[0]) {
                    _nnId = _nnArr[i].split('-')[1];
                    break;
                }
            }
            if (_nnId == "0") {
                // tim kiem dau the 2 ky tu neu 3 ky tu k tim thay;
                for (i = 0; i < _nnArr.length; i++) {
                    if (ma == _nnArr[i].split('-')[0] || _nnArr[i].split('-')[0] == "0") {
                        _nnId = _nnArr[i].split('-')[1];
                        break;
                    }
                }
            }

            value = _nnId;

        }

        $('#cboNGHENGHIEPID').val(value);
        $('#cboNGHENGHIEPID').change();
    }

    function _setPHONGKHAMID(dichvuid, matimkiem, id) {
        var sql_par = [dichvuid, matimkiem];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.PHONGID", sql_par.join('$'));
        if (data_ar != null && data_ar.length > 0) {
            $('#' + id).val(data_ar[0].ORG_ID);
            $('#' + id).change();
        }
    }

    function _xoabenhnhan() {
        if ($('#hidTRANGTHAIKHAMBENH').val() != '1') {
            DlgUtil.showMsg('Bệnh nhân đang khám hoặc kết thúc khám không được xóa bệnh nhân');
            return;
        }
        var myVar = {
            kieu: '0',									// CHI XOA 1 PHONG KHAM;
            khambenhid: $('#hidKHAMBENHID').val(),
            phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
            tiepnhanid: $('#hidTIEPNHANID').val(),
            hosobenhanid: $('#hidHOSOBENHANID').val()
        };
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CN.XOA.KETTHUC", JSON.stringify(myVar));
        if (ret == 1) {
            DlgUtil.showMsg('Xóa thành công');
            loadGridData();
        } else if (ret == 'dacophitt') {
            DlgUtil.showMsg('Bệnh nhân đã có tiền thanh toán dịch vụ/phiếu đã hủy. Không được phép xóa');
        } else if (ret == 'dangkhamdakham') {
            DlgUtil.showMsg('Bệnh nhân đang khám / đã khám xong. Không được xóa. ');
        } else if (ret == 'dacotamung') {
            DlgUtil.showMsg('Bệnh nhân đã có tạm ứng/phát sinh phiếu thu, không được xóa bệnh nhân.');
        } else if (ret == 'nhieuphongkham') {
            DlgUtil.showMsg('Bệnh nhân đã khám nhiều phòng, yêu cầu sử dụng tool để xóa phòng khám');
        } else {
            DlgUtil.showMsg('Xóa không thành công. ', undefined, undefined, "error");
        }
    }

    function initPopup_TTBN_NHIHDG() {
        dlgTTBN_NHIHDG = DlgUtil.buildPopup("dlgNhapThongTinTT_NHIHDG", "dlgTTBN_NHIHDG", "Nhập thông tin bệnh nhân", 550, 300);
        var btnOK = $('#btn_CapNhatBN_OK_NHIHDG');
        var btnClose = $('#btn_CapNhatBN_Close_NHIHDG');
        btnOK.click(function () {
            var _mabenhnhan = $("#txtMABENHNHAN_HDDT").val().trim();
            var _tencty = $("#txtTENCONGTYBN_HDDT").val().trim();
            var _dc_cty = $("#txtDIACHI_CTYBN_HDDT").val().trim();
            var _masothue = $("#txtMASOTHUE_CTYBN_HDDT").val().trim();
            var _email_ctybn = $("#txtEMAIL_CTYBN_HDDT").val().trim();
            var _sotk_ctybn = $("#txtSOTK_CTYBN_HDDT").val().trim();
            var _tennh_ctybn = $("#txtTENNH_CTYBN_HDDT").val().trim();
            //L2PT-18053 ttlinh start
            var validator = new DataValidator("dlgTTBN_NHIHDG");
            var valid = validator.validateForm();
            if (!valid)
                return false;
            //L2PT-18053 end
            //var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.PT_UPDBN",_mabenhnhan+'$'+_tencty+'$'+_dc_cty+'$'+_masothue);
            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.UPDATE.BNHDDT", _mabenhnhan + '$' + _tencty + '$' + _dc_cty + '$' + _masothue + '$' + _email_ctybn + '$' + _sotk_ctybn + '$' + _tennh_ctybn);
            if (fl == 1) {
                //$("#"+_gridId_PT).trigger("reloadGrid");
                dlgTTBN_NHIHDG.close();
                DlgUtil.showMsg("Cập nhật thành công");
            } else if (fl == -1) {
                DlgUtil.showMsg("Xảy ra lỗi", undefined, undefined, "error");
            }
        });
        btnClose.click(function () {
            $("#txtMABENHNHAN_HDDT").val("");
            $("#txtTENCONGTYBN_HDDT").val("");
            $("#txtDIACHI_CTYBN_HDDT").val("");
            $("#txtMASOTHUE_CTYBN_HDDT").val("");
            $("#txtEMAIL_CTYBN_HDDT").val("");
            $("#txtSOTK_CTYBN_HDDT").val("");
            $("#txtTENNH_CTYBN_HDDT").val("");
            dlgTTBN_NHIHDG.close();
        });
    }

    function initPopup_QUYCHE() {
        dlgQUYCHE = DlgUtil.buildPopup("dlgThongTinQUYCHE", "dlgQUYCHE", "Quy chế bảo vệ dữ liệu cá nhân", 1000, 700);
        var noidungquyche = cfObj.HIS_NOIDUNG_QUYCHEBAOVEDLCN;
        $('#txtQUYCHE').text(noidungquyche);
        var btnDongY = $('#btnDONGY');
        var btnKhongDongY = $('#btnKHONGDONGY');
        btnDongY.click(function () {
            $('#chkDONGYQUYCHEBVDLCN').prop('checked', true);
            dlgQUYCHE.close();
        });
        btnKhongDongY.click(function () {
            $('#chkDONGYQUYCHEBVDLCN').prop('checked', false);
            dlgQUYCHE.close();
        });
    }

    function _clearOption(strId, _defOpt) {
        $("#" + strId).empty();
        if (typeof _defOpt.value !== "undefined") {
            $("#" + strId).append('<option value="' + _defOpt.value + '">' + _defOpt.text + '</option>');
        }
    }

    // callback sau khi thu vien phi; in phieu roi refresh lai form;
    EventUtil.setEvent("ngt_vienphihienthipopup", function (e) {
        var msggg = 'Cập nhật thông tin bệnh nhân thành công. ';
        // msggg = $("#txtTRAVEMABENHAN").val() == "1" ? msggg + "Mã bệnh án: " + rets[9] : msggg;
        $('#msgCapnhat').text(msggg);
        DlgUtil.showMsg(msggg);
        $('#tabCongKhamTab').text("Tiền công khám(1)");

        if (_hd == "2") {
            setTimeout(function () {
                window.location.replace("/vnpthis/main/manager.jsp?func=../ngoaitru/NGT02K001_KB_MHC&type=2");
            }, 5000);
        } else {
            _inPhieuKham(1);
            setTimeout(function () {
                $('#btnTiepTheo').trigger('click');
            }, 10000);
        }
    });

    //calback cho MA HINH SUA PHONG THUC HIEN
    EventUtil.setEvent("assignSevice_SavePhongThucHien", function (e) {
        DlgUtil.showMsg(e.msg);
        //reload danh sach xet nghiem
        if (e.loaiPhieu == LNMBP_XetNghiem) {
            $('#tabXetNghiem').ntu02d024_ttxn({
                _gridXnId: "grdXetNghiem",
                _gridXnDetailId: "grdXetNghiemChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_XetNghiem,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        } else if (e.loaiPhieu == LNMBP_CDHA) {
            $('#tabCDHA').ntu02d025_cdha({
                _gridCDHA: "grdCDHA",
                _gridCDHADetail: "grdCDHAChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_CDHA,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        }
        DlgUtil.close("divDlgEditOrgDone");
    });

    function isOutsideWorkingHours() {
        var now = new Date();
        var dayOfWeek = now.getDay(); // 0: Sunday, 1: Monday, ..., 6: Saturday
        var hour = now.getHours();
        // Kiểm tra xem là thứ 7 hoặc chủ nhật
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            return true;
        }
        // Kiểm tra xem có trước 7 giờ sáng hoặc sau 17 giờ chiều không
        if (hour < 7 || hour >= 17) {
            return true;
        }
        return false;
    }

    function checkCMND(str) {
        var length = str.length;
        if (length !== 8 && length !== 9 && length !== 12) {
            return false;
        }
        var specialCharPattern = /[!@#$%^&*()<>?:,]/;
        if (specialCharPattern.test(str)) {
            return false;
        }
        if (length === 8 || (length === 9 && $("#cboDTBNID").val() != 1)) {
            var firstChar = str.charAt(0);
            var remainingChars = str.slice(1);
            var numberPattern = /^[0-9]+$/;
            if (!firstChar.match(/[A-Z]/) || !numberPattern.test(remainingChars)) {
                return false;
            }
        }
        if (length === 12 || (length === 9 && $("#cboDTBNID").val() == 1)) {
            var numberPattern = /^[0-9]+$/;
            if (!numberPattern.test(str)) {
                return false;
            }
        }
        return true;
    }

    function lienketVncare(benhnhanid, taikhoanlienket) {
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT04K001.02", benhnhanid);
        if (data_ar != null && data_ar.length > 0) {
            var text = '{ "phone":"' + taikhoanlienket
                + '", "maCSYT":"' + opt.hospital_code
                + '","maBN":"' + data_ar[0].MABENHNHAN
                + '","tenBN":"' + data_ar[0].TENBENHNHAN
                + '","ngaySinh":"' + data_ar[0].NGAYSINH
                + '","gioiTinh":"' + (data_ar[0].GIOITINHID == 1 ? 1 : 0)
                + '","quanHe":"'
                + '","MANGHENGHIEP":"' + data_ar[0].MANGHENGHIEP
                + '","MADT":"' + data_ar[0].MADANTOC
                + '","MAQT":"' + data_ar[0].MAQT
                + '","MATINH":"' + data_ar[0].MATINH
                + '","MAHUYEN":"' + data_ar[0].MAHUYEN
                + '","MAXA":"' + data_ar[0].MAXA
                + '","DIACHI":"' + data_ar[0].DIACHI
                + '","DIENTHOAI":"' + data_ar[0].SDTBENHNHAN
                + '","CMT":"' + data_ar[0].CMND
                + '","SOTHE_BHYT":"' + data_ar[0].MA_BHYT + '"}';
            strReturn = ajaxSvc.PortalWS.lienket_tk_vncare(text, 1);

            if (strReturn == "-1" || strReturn == "-2" || strReturn == "-3") {
                //DlgUtil.showMsg("Có lỗi xảy ra trong quá trình liên kết tài khoản!");
            } else {
                ret = JSON.parse(strReturn);
                if (ret.errorCode == '0') {
                    var objData = new Object();
                    objData["BENHNHANID"] = benhnhanid;
                    objData["TK_LIENKET"] = taikhoanlienket;
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT04K001.UPD", JSON.stringify(objData));
                } else {
                    DlgUtil.showMsg(ret.errorMessage);
                }
            }
        }
    }
}

$(document).ready(function () {
    $('#cboBHYT_LoaiID').change();
    document.addEventListener('keydown', function (event) {
        if (event.altKey && event.code == 'KeyF') {
            event.preventDefault();
        }

        if (event.altKey && event.code == 'KeyE') {
            event.preventDefault();
        }

        if (event.altKey && event.code == 'KeyD') {
            event.preventDefault();
        }
    });
});
