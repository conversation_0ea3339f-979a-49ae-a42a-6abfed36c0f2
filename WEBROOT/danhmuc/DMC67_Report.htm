<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../danhmuc/DMC67_Report.js?v=20230417"></script>

<div width="100%" id="divMain" class="container">
    <div class="col-md-12 low-padding mgt5 mgb5">

        <div class="col-md-2 low-padding">
            <label style="color: red; font-size: 100%; text-align: left"><b
                    id="lblThongBao"></b></label>
        </div>
    </div>
    <div class="col-md-12 low-padding mgt-5">
        <div class="col-md-6 low-padding">
            <div class="col-md-12 low-padding mgt-10">
                <table id="grddvkt_chinh"></table>
                <div id="pager_grddvkt_chinh"></div>
            </div>
            <div class="col-md-12 low-padding mgt-10">
                <label class="mgl5" style="width: 100%; display:none; color: Tomato;" id="lbInform">
                    Double click vào report để xem danh sách user đang được phân quyền
                </label>
            </div>
        </div>


        <div class="col-md-6 low-padding"
             style="padding-left: 15px !important;">
            <div id="inputForm" class="panel panel-default">
                <div class="panel-heading">Thông tin Report</div>
                <div class="panel-body">
                    <div class="col-md-12 low-padding mgt5">
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Report name </label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtREPORT_NAME" name="txtREPORT_NAME"
                                       title="" disabled="disabled" valrule="Report name,required" maxlength="255">
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Report path</label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtREPORT_PATH" name="txtREPORT_PATH"
                                       title="" disabled="disabled" valrule="Report path,required" maxlength="500">
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Report Code </label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtREPORT_CODE" name="txtREPORT_CODE"
                                       title="" disabled="disabled" valrule="Report Code,required" maxlength="100">
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Table List</label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtTABLE_LIST" name="txtTABLE_LIST"
                                       title="" disabled="disabled" valrule="Table List,required" maxlength="255">
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Group ID </label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <!--<input type="text" class="form-control input-sm kb-i-col-m"
                                    style="width: 100%" id="txtGROUP_ID" name="txtGROUP_ID"
                                    title=""  disabled="disabled" valrule="Group ID,required" maxlength="11">-->
                                <select class="form-control input-sm kb-i-col-m "
                                        id="cboGROUP_ID" valrule="Group ID ,required"
                                        style="width: 100%;" disabled>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Company ID </label>
                            </div>
                            <div class="col-md-9 low-padding">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtCOMPANY_ID" name="txtCOMPANY_ID"
                                       title="" disabled="disabled" valrule="Company ID,required" maxlength="15">
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding required">
                                <label class="mgl5" style="width: 100%;">Is Public </label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtIS_PUBLIC" name="txtIS_PUBLIC"
                                       title="" disabled="disabled" valrule="Is public,required" maxlength="5">
                            </div>
                        </div>
                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">Not Pdf </label>
                            </div>
                            <div class="col-md-9 low-padding ">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtNOT_PDF" name="txtNOT_PDF"
                                       title="" disabled="disabled" maxlength="1">
                            </div>
                        </div>
                        <div class="col-md-12 " id="divPATH" style="display:none;">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">Đường dẫn</label>
                            </div>
                            <div class="col-md-9 low-padding ">
								<textarea class="form-control input-md"
                                          maxlength="2000" id="txtPATH_DIRECT" name="txtPATH_DIRECT" rows="2"
                                          placeholder="Ví dụ: Nội trú-> điều trị nội trú -> In ấn->Giấy ra viện"
                                          disabled></textarea>
                            </div>
                        </div>
                        <div class="col-md-12 hmis_element" id="divURL_HMIS" style="display:none;">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">URL API HMIS</label>
                            </div>
                            <div class="col-md-9 low-padding ">
								<textarea class="form-control input-md"
                                          maxlength="2000" id="txtURL_HMIS" name="txtURL_HMIS" rows="2"
                                          disabled></textarea>
                            </div>
                        </div>
                        <div class="col-md-12 hmis_element" style="display:none;">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">ORDER BY KEY(HMIS)</label>
                            </div>
                            <div class="col-md-9 low-padding ">
								<textarea class="form-control input-md"
                                          maxlength="2000" id="txtORDER_BY_KEY" name="txtORDER_BY_KEY" rows="2"
                                          disabled></textarea>
                            </div>
                        </div>
                        <div class="col-md-12 hmis_element" style="display:none;">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">SQL FUNC(HMIS)</label>
                            </div>
                            <div class="col-md-9 low-padding">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtSQL_FUNC" name="txtSQL_FUNC"
                                       title="" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-md-12 hmis_element" style="display:none;">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">SQL PAR KEY(HMIS)</label>
                            </div>
                            <div class="col-md-9 low-padding">
                                <input type="text" class="form-control input-sm kb-i-col-m"
                                       style="width: 100%" id="txtSQL_PAR_KEY" name="txtSQL_PAR_KEY"
                                       title="" disabled="disabled">
                            </div>
                        </div>

                        <div class="col-md-12 ">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">Description</label>
                            </div>
                            <div class="col-md-9 low-padding ">
								<textarea class="form-control input-md"
                                          maxlength="5000" id="txtDESCRIPTION" name="txtDESCRIPTION"
                                          placeholder="Hãy nhập để biết thông tin về báo cáo, phiếu in! Ví dụ: ĐK lọc, đầu vào , đầu ra , khổ giấy, nội dung chi tiết"
                                          rows="4" disabled></textarea>
                            </div>
                        </div>

                        <div class="col-md-12" style="margin-top: 5px;">
                            <div class="col-md-3 low-padding">
                                <label class="mgl5" style="width: 100%;">Thời gian chặn từ</label>
                            </div>
                            <div class="col-md-8 low-padding">
                                <div class="input-group input-daterange" style="padding-left: 20px;">
									<span class="input-group-addon">
										<input disabled type="checkbox" id="chkIS_LOCK_BY_TIME"
                                               name="chkIS_LOCK_BY_TIME">
									</span>
                                    <input disabled type="text" id="txtLOCK_TIME_FROM" maxlength="5" minlength="5"
                                           name="txtLOCK_TIME_FROM" class="form-control timepicker" placeholder="00:00"
                                           value="00:00">
                                    <div class="input-group-addon">đến</div>
                                    <input disabled type="text" id="txtLOCK_TIME_TO" maxlength="5" minlength="5"
                                           name="txtLOCK_TIME_TO" class="form-control timepicker" placeholder="00:00"
                                           value="00:00">
                                </div>
                                <div class="input-group input-daterange" style="padding-left: 20px; margin-top: 2px;">
									<span class="input-group-addon">
										<input disabled type="checkbox" id="chkIS_LOCK_BY_TIME2"
                                               name="chkIS_LOCK_BY_TIME2">
									</span>
                                    <input disabled type="text" id="txtLOCK_TIME_FROM2" maxlength="5" minlength="5"
                                           name="txtLOCK_TIME_FROM2" class="form-control timepicker" placeholder="00:00"
                                           value="00:00">
                                    <div class="input-group-addon">đến</div>
                                    <input disabled type="text" id="txtLOCK_TIME_TO2" maxlength="5" minlength="5"
                                           name="txtLOCK_TIME_TO2" class="form-control timepicker" placeholder="00:00"
                                           value="00:00">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 low-padding mgt10 mgb15">
                        <div class="col-md-12 low-padding" style="text-align: center;">
                            <button type="button" class="btn btn-sm btn-primary" id="btnThem">
                                <span class="glyphicon glyphicon-pencil"></span> Thêm
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnSua"
                                    disabled>
                                <span class="glyphicon glyphicon-edit"></span> Sửa
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnCopy"
                                    disabled>
                                <span class="glyphicon glyphicon-edit"></span> Sao chép
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnXoa"
                                    disabled>
                                <span class="glyphicon glyphicon-edit"></span> Xóa
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnLuu"
                                    disabled>
                                <span class="glyphicon glyphicon-floppy-disk"></span> Lưu
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnHuy"
                                    disabled>
                                <span class="glyphicon glyphicon-remove-circle"></span> Hủy
                            </button>
                        </div>
                        <div class="col-md-12 low-padding" style="text-align: center; margin-top: 5px;">
                            <button type="button" class="btn btn-sm btn-primary" id="btnImage"
                                    disabled>
                                <span class="glyphicon glyphicon-picture"></span> CH ảnh mẫu
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnConnector"
                                    disabled>
                                <span class="glyphicon glyphicon-picture"></span> CH liên thông
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnReloadCachePQ"
                                    disabled>
                                <span class="glyphicon glyphicon-picture"></span> Reload Cache PQ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="popupId"></div>
</div>
<script>
    //var userInfo=CommonUtil.decode('{userData}');
    let paramInfo = CommonUtil.decode('{paramData}');
    let opt = [];
    let hospital_id = '{hospital_id}';
    let user_id = '{user_id}';
    let user_type = '{user_type}';
    let province_id = '{province_id}';
    let uuid = '{uuid}';
    let session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    session_par[4] = paramInfo.rpt_schema;
    initRest(uuid, "/vnpthis");
    let _opts = new Object();
    _opts.lang = lang;
    _opts._param = session_par;
    _opts._uuid = uuid;
    let DS = new dSCHList(_opts);
    DS.load(hospital_id);
</script>