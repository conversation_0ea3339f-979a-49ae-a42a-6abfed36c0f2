<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BAOCAOTH_BOIDUONG_PTTT_957" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="545" leftMargin="30" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="02afe4c9-87bb-4263-9fdc-5ca4328b421b">
	<property name="ireport.zoom" value="1.948717100000013"/>
	<property name="ireport.x" value="114"/>
	<property name="ireport.y" value="96"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="dt_tungay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dt_denngay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_khoaid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoaid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call report_tien_boiduong_pttt($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{dt_tungay},$P{dt_denngay},$P{VPARS_khoaid_VALUE},$P{ora_cursor})}]]>
	</queryString>
	<field name="LOAINHOM" class="java.lang.Integer"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="LOAI1" class="java.lang.Long"/>
	<field name="LOAI2" class="java.lang.Long"/>
	<field name="LOAI3" class="java.lang.Long"/>
	<field name="DACBIET" class="java.lang.Long"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="TIENPHUCAP" class="java.math.BigDecimal"/>
	<field name="TONGTIENCHU" class="java.lang.String"/>
	<variable name="tong_db" class="java.lang.Long" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{DACBIET}]]></variableExpression>
	</variable>
	<variable name="tong_loai1" class="java.lang.Long" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{LOAI1}]]></variableExpression>
	</variable>
	<variable name="tong_loai2" class="java.lang.Long" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{LOAI2}]]></variableExpression>
	</variable>
	<variable name="tong_loai3" class="java.lang.Long" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{LOAI3}]]></variableExpression>
	</variable>
	<variable name="tong_thanhtien" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENPHUCAP}]]></variableExpression>
	</variable>
	<variable name="tong_thanhtien_g" class="java.math.BigDecimal" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENPHUCAP}]]></variableExpression>
	</variable>
	<group name="loai">
		<groupExpression><![CDATA[$F{LOAINHOM}]]></groupExpression>
		<groupHeader>
			<band height="21">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="545" height="21"  uuid="60dbfe44-5392-4d93-b94f-7750d9aadd8d"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOAINHOM}==1?"I. PHẪU THUẬT":"II. THỦ THUẬT"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="18">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="304" y="0" width="50" height="18" uuid="f9016a64-4b1a-4bd9-b364-1ec5c732d1fe"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tong_loai2}==0?"":$V{tong_loai2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="354" y="0" width="50" height="18" uuid="e0e52741-8051-4b29-b7a7-670f17baf324"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tong_loai3}==0?"":$V{tong_loai3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="204" height="18" uuid="508f3f15-c1bd-47dc-9c77-0b3acab631b2"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng "+($F{LOAINHOM}==1?"PT":"TT")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="204" y="0" width="50" height="18" uuid="b30d3e3f-30cb-4a28-a209-0ba987c53efa"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tong_db}==0?"":$V{tong_db}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="254" y="0" width="50" height="18" uuid="124751fd-9a1e-486d-b747-e177c352ad96"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tong_loai1}==0?"":$V{tong_loai1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="466" y="0" width="79" height="18" uuid="796d1695-dd13-4b3b-ad97-89b4173eaa8b"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tong_thanhtien_g}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="404" y="0" width="62" height="18" uuid="2422f092-7007-4359-b0c9-7f3a8a3cd490"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="147" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="254" height="18" uuid="fe9609a1-f2f2-498f-9c2c-cf69497e2d47"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="36" width="545" height="25" uuid="c2914e67-8ae5-477d-94f6-0771cbfc059e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BẢNG TỔNG HỢP BỒI DƯỠNG PHẪU THUẬT, THỦ THUẬT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="105" width="31" height="42" uuid="ad24f7ab-b4f4-4cc7-8022-b46af62622e2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="31" y="105" width="173" height="42" uuid="8f7b3567-575a-4064-af8b-a26cd86904de"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên phẫu thuật, thủ thuật]]></text>
			</staticText>
			<textField>
				<reportElement x="-1" y="61" width="546" height="22" uuid="5e4837d6-21f9-4489-87eb-32cae203ca75"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format( $P{dt_tungay})  + " đến ngày " +  new SimpleDateFormat("dd/MM/yyyy").format( $P{dt_denngay})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="204" y="105" width="200" height="18" uuid="0eba7f80-725c-466c-b31e-9f41b8d1a0a4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Số ca]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="204" y="123" width="50" height="24" uuid="119863a4-1101-4b4f-a392-d4beec14e690"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐB]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="254" y="123" width="50" height="24" uuid="5a7a987c-8de5-4ee8-b68d-0276edaeec11"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Loại I]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="354" y="123" width="50" height="24" uuid="6a09e4cd-c2e5-40af-b31b-6320fe41d92c"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Loại III]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="304" y="123" width="50" height="24" uuid="80f39709-0ae5-4ca3-8a4c-f4e9c56bb684"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Loại II]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="404" y="105" width="62" height="42" uuid="27cae59a-d5a5-4907-a3a9-aba0b322fd99"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Mức hưởng/ ca]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="18" width="254" height="18" uuid="e502fe02-99fe-45ea-9c14-243bbc29d0a2"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHÒNG TC - KT"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="466" y="105" width="79" height="42" uuid="489d6829-b9dd-4dd1-af7c-847f19cc43a0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="83" width="545" height="22" uuid="5cb376e5-dc71-4321-b299-15325440fa99"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{VPARS_khoaid_VALUE}.contains(",")?"":$P{khoaid_TEXT}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="18" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="31" height="18" uuid="79bfbda9-80a1-400a-b628-2871c6fd1268"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{loai_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="204" y="0" width="50" height="18" uuid="673d8b8e-3671-49ec-827a-4928c9bd8e2e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DACBIET}==0?"":$F{DACBIET}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="254" y="0" width="50" height="18" uuid="c89d016e-c1f3-43c5-8596-4eef703b8dd3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAI1}==0?"":$F{LOAI1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="354" y="0" width="50" height="18" uuid="915ae4d2-fc5d-42f7-8b6a-9f0ce31dd007"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAI3}==0?"":$F{LOAI3}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="304" y="0" width="50" height="18" uuid="3659265c-d2e3-469d-bf48-58adc613f485"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAI2}==0?"":$F{LOAI2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="404" y="0" width="62" height="18" uuid="0c301597-9661-4392-b06c-9b5475e46280"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONGIA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="31" y="0" width="173" height="18" uuid="ae1a58b5-6be4-4fce-a074-d0cb4982bdb3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENDICHVU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="466" y="0" width="79" height="18" uuid="66fac1fd-6642-47c0-9f4a-828a5539ba6a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIENPHUCAP}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="16">
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="254" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="e68976ad-53d1-47e6-a5e3-9db9667996a2"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("dd/MM/yyyy, hh:mm")).format(new Date())]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement positionType="Float" x="466" y="0" width="79" height="16" uuid="7c4ea796-ca9b-404d-8335-e352025da47b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["/"+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="404" y="0" width="62" height="16" uuid="4ced0a85-8d6d-4ca1-beb9-f8e6794e643c"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="83">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="354" y="38" width="191" height="24"  uuid="91509fca-a4bc-4465-9fe9-463977fccdbd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Hải Dương, ngày " + (new SimpleDateFormat("dd")).format(new Date())
+ " tháng " + (new SimpleDateFormat("MM")).format(new Date())
+ " năm " + (new SimpleDateFormat("yyyy")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="-1" y="62" width="205" height="21" uuid="6da01ea9-ac66-4cbf-9666-e8d5fa393cb5"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Người lập]]></text>
			</staticText>
			<staticText>
				<reportElement x="204" y="62" width="150" height="21" uuid="46784e91-ef73-4038-9bee-3d24e39e0a17"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Phụ trách phòng TCKT]]></text>
			</staticText>
			<staticText>
				<reportElement x="354" y="62" width="191" height="21" uuid="b14c916d-278b-4fe0-9de1-a3aec80e3b4b"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[ Giám đốc]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="466" height="18" uuid="2e83ff10-a61c-480e-bd5b-52df792a7d79"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng cộng I + II"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="466" y="0" width="79" height="18" uuid="4761ddfd-03e6-4269-811d-5fb91f400afe"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tong_thanhtien}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="18" width="545" height="20" uuid="89b17631-f1b1-47f3-8e6b-0ff2334ebd21"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["Số tiền bằng chữ: ("+($F{TONGTIENCHU}==null?"":$F{TONGTIENCHU})+" đồng)."]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
