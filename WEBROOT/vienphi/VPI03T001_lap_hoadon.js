/*
 <PERSON><PERSON> màn hình  : VPI01T002
 File mã nguồn : VPI01T002_sophieuthu.js
 <PERSON><PERSON><PERSON> đích  : <PERSON><PERSON><PERSON><PERSON> lý sổ phiếu thu
 Tham số vào :
 <PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON>t  	<PERSON><PERSON>				 5/10/2016		comment
*/
function VPI03T001_lap_hoadon(_opts) {
	var _gridDSDichVu = "grdDanhSachDV";
	var _gridDSHoaDon = "grdDanhSachHD";
	//tuyennx_edit_start_20171121 yc HISL2CORE-599
	var _gridDSDVHeader = " ,ACTION,30,d,f,l;id,DICHVUID,0,0,t,l" + ";STT,STT,50,0,f,l" + ";Tên dịch vụ,TENDICHVU,120,0,f,l" + ";ĐVT,DONVITINH,50,0,f,l" + ";<PERSON><PERSON> l<PERSON>,SOLUONG,60,decimal!3,f,l"
			+ ";<PERSON><PERSON><PERSON> giá,DONGIA,60,decimal!3,f,l" + ";Th<PERSON>nh tiền,THANHTIEN,60,decimal!2,f,l" + ";VAT,VAT,60,decimal!2,f,l" + ";Tiền VAT,TIEN_VAT,60,decimal!2,f,l"
			+ ";TONGTIEN,TONGTIEN,60,decimal!2,f,l";
	var _gridHoaDonHeader = " ,ICON,20,0,ns,l" + ";id,HOADONID,0,0,t,l" + ";Trạng thái,TRANGTHAI,100,0,t,l" + ";Trạng thái HĐĐT,TRANGTHAI_HDDT,100,0,t,l" + ";Số HĐ,MA_HOADON,60,0,f,l"
			+ ";Pattern,PATTERN_CODE,80,0,f,l" + ";Serial,SERIAL_CODE,70,0,f,l" + ";Tên đơn vị,TEN_CONGTY,150,0,f,l" + ";Tên khách hàng,TEN_KH,150,0,f,l" + ";Người tạo,NGUOITAO,120,0,f,l"
			+ ";Ngày tạo,NGAYTAO,90,0,f,c" + ";Người phát hành,NGUOIPHATHANH,120,0,f,c" + ";Ngày phát hành,NGAYPHATHANH,90,0,f,c" + ";HT thanh toán,PAYMENT_METHOD,90,0,f,l"
			+ ";Số tiền,AMOUNT,80,0,f,l" + ";Ngày hủy phát hành,NGAYHUYPHATHANH,90,0,t,c" + ";Người hủy phát hành,NGUOIHUYPHATHANH,90,0,t,c" + ";Người xóa,NGUOIXOA,90,0,t,c"
			+ ";Người hủy phát hành,NGUOIHUYPHATHANH,90,0,t,c" + ";Mã bệnh nhân,MABENHNHAN,120,0,f,l" + ";Log,LOG_CAPNHAT,350,0,t,l";
	var isEdit = false;
	var flagLoading = false;
	var that = this;
	var dsCH = null;
	this.load = doLoad;
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		var _options = $.extend({}, _opts);
		var uuid = _options._uuid;
		var _param = _options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_phong_id = _param[4];
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		var str_ch = "INVOICES_URL_IMPORT;INVOICES_URL_VIEW;INVOICES_URL_CANCEL;INVOICES_WS_USER_ACC;INVOICES_WS_PWD_ACC;INVOICES_WS_USER;INVOICES_WS_PWD;INVOICES_WS_PATTERN;INVOICES_WS_SERIAL"
				+ ";VPI_PS_BANLE;VPI_LHD_REMOVE_REQ"; //L2PT-103760
		var arr_dsCH = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		// L2PT-40509 start
		if (arr_dsCH && arr_dsCH.length > 0) {
			dsCH = arr_dsCH[0];
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
			if (rs && rs.length > 0 && rs[0].INVOICES_PASS != "" && rs[0].INVOICES_USER != "") {
				dsCH.INVOICES_WS_USER_ACC = rs[0].INVOICES_USER;
				dsCH.INVOICES_WS_PWD_ACC = rs[0].INVOICES_PASS;
			}
		} else {
			DlgUtil.showMsg("Lỗi lấy cấu hình hệ thống");
		}
		// L2PT-40509 end
		//L2PT-95506 ttlinh start
		if (_hospital_id == '46560') {
			$('#lblTenKhachHang').removeClass('required');
			$('#txtTEN_KH').removeAttr("valrule");
		}
		//L2PT-95506 end
		if (dsCH.VPI_LHD_REMOVE_REQ != '0') {
			var ch = dsCH.VPI_LHD_REMOVE_REQ;
			var ctrids = ch.split(',');
			for (var h = 0; h < ctrids.length; h++) {
				var ctrid = ctrids[h];
				$('[confreq]').each(function(index, element) {
					if (ctrid == 'ALL') {
						$(this).removeClass("required");
						$(this).removeAttr("valrule");
					} else if ($(element).attr('id').indexOf(ctrid) > -1) {
						$(this).removeClass("required");
						$(this).removeAttr("valrule");
					}
				});
			}
		}
		$('#txtTU').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY') + " 00:00:00");
		$('#txtDEN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY') + " 23:59:59");
		var _colDMDVBanLe = "Mã dịch vụ,MADICHVU,20,0,f,l;Tên dịch vụ,TENDICHVU,60,0,f,l;Đơn giá,DONGIA,20,0,f,l";
		ComboUtil.initComboGrid("txtTENDICHVU", "DMC.DV.BANLE", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "600px", _colDMDVBanLe, function(event, ui) {
			var uItem = ui.item;
			FormUtil.setObjectToForm("divNhapDV", "", ui.item);
			return false;
		});
		ComboUtil.getComboTag("cboKHOAID", 'VPI.KHOA.KCB', [], "", {
			text : "Tất cả",
			value : -1
		}, 'sql', '', '');
		ComboUtil.getComboTag("cboPHONGID", 'VPI.PHONG.KCB', [ {
			name : "[0]",
			value : -1
		} ], "", {
			text : "Tất cả",
			value : -1
		}, 'sql', '', '');
		ComboUtil.getComboTag("cboGIOITINHID", 'COM.TRANGTHAI', [ {
			"name" : "[0]",
			value : 1
		} ], "", "", 'sql', '');
		ComboUtil.getComboTag("cboDANTOCID", 'COM.DANTOC2', [], "", "", 'sql', '');
		GridUtil.init(_gridDSDichVu, "100%", "228px", "", false, _gridDSDVHeader, true);
		GridUtil.init(_gridDSHoaDon, "100%", "255px", "", false, _gridHoaDonHeader, false);
		$("#txtMA_KH").focus();
		if (dsCH.VPI_PS_BANLE && dsCH.VPI_PS_BANLE != 0) {
			var dsMSKH = [];
			var ind_ms = '';
			var ind_kh = '';
			var def_kh = [];
			try {
				var chMSKH = FormUtil.unescape(dsCH.VPI_PS_BANLE);
				dsMSKH = JSON.parse(chMSKH);
				$.each(dsMSKH, function(i, _ms) {
					$("#cboPATTERN_CODE").append($('<option>', {
						value : _ms.INV_PATTERN,
						text : _ms.INV_PATTERN
					}));
					$.each(_ms.INV_SERIAL, function(j, _kh) {
						if (i == 0 && j == 0) {
							ind_kh = _kh.SERIAL;
							ind_ms = _ms.INV_PATTERN;
							def_kh = _ms.INV_SERIAL;
						}
						if (_kh.PHONGID == _phong_id && ind_ms == '') {
							ind_kh = _kh.SERIAL;
							ind_ms = _ms.INV_PATTERN;
							def_kh = _ms.INV_SERIAL;
						}
					});
				});
				$("#cboPATTERN_CODE").val(ind_ms);
				$.each(def_kh, function(k, d_kh) {
					$("#cboSERIAL_CODE").append($('<option>', {
						value : d_kh.SERIAL,
						text : d_kh.SERIAL
					}));
				});
				$("#cboSERIAL_CODE").val(ind_kh);
				$("#cboPATTERN_CODE").change(function() {
					$("#cboSERIAL_CODE").empty();
					var _ms = $("#cboPATTERN_CODE").prop('selectedIndex');
					$.each(dsMSKH[_ms].INV_SERIAL, function(i, _kh) { // L2PT-3827
						$("#cboSERIAL_CODE").append($('<option>', {
							value : _kh.SERIAL,
							text : _kh.SERIAL
						}));
					});
				});
				// L2PT-3827 end
			} catch (err) {
				DlgUtil.showMsg("Thiết lập cấu hình không đúng");
				console.log(err.message);
			}
		}
		loadDSHoaDon();
	}
	function _bindEvent() {
		// xu ly tim kiem
		$('#btnTimKiem').click(function() {
			if (flagLoading) {
				return false;
			}
			loadDSHoaDon();
		});
		$('#btnThemDVBL').click(function() {
			if (!flagLoading) {
				return false;
			}
			var validator = new DataValidator("divNhapDV");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			$("." + "money").css('background-color', 'white');
			var objDichVu = new Object();
			var ids = $("#" + _gridDSDichVu).getDataIDs();
			var stt = ids.length == 0 ? 1 : parseInt(ids[ids.length - 1]) + 1;
			FormUtil.setFormToObject('divNhapDV', '', objDichVu);
			objDichVu.STT = stt + "";
			$("#" + _gridDSDichVu).jqGrid('addRowData', stt, objDichVu, 'last');
			tinhhoadon();
		});
		$('#txtSOLUONG').change(function() {
			tinhtien();
		});
		$('#txtDONGIA').change(function() {
			tinhtien();
		});
		function tinhtien() {
			if (!flagLoading) {
				return false;
			}
			var sl = parseFloat($('#txtSOLUONG').val());
			var dg = parseFloat($('#txtDONGIA').val());
			var tt = null;
			if (sl && dg) {
				tt = sl * dg;
				tt = parseFloat(tt.toFixed(4)); //L2PT-97954
			}
			var vat = parseInt($('#cboTAX_RATE').val());
			var tien_vat = vat == -1 ? 0 : Math.round(tt * vat) / 100; //L2PT-95506
			var tongtien = tt + tien_vat;
			tongtien = parseFloat(tongtien.toFixed(2));
			$('#txtTHANHTIEN').val(tt);
			$('#hidVAT').val(vat);
			$('#hidTIEN_VAT').val(tien_vat);
			$('#hidTONGTIEN').val(tongtien);
		}
		// xu ly tuy chon dich vu
		GridUtil.setGridParam(_gridDSDichVu, {
			onSelectRow : function(id) {
				if (id) {}
			}
		});
		// xu ly tuy chon phieu thu
		GridUtil.setGridParam(_gridDSHoaDon, {
			beforeSelectRow : function(rowid, e) {
				if (flagLoading) {
					return false; // not allow select the row
				} else {
					return true; // allow select the row
				}
			},
			onSelectRow : function(id) {
				GridUtil.unmarkAll(_gridDSHoaDon);
				GridUtil.markRow(_gridDSHoaDon, id);
				setEnabled([ 'btnSua', 'btnXoa' ], []);
				var trangthai_hddt = $("#" + _gridDSHoaDon).jqGrid('getCell', id, 'TRANGTHAI_HDDT');
				$('#divThaoTacHDDT').find(':input').prop('disabled', false);
				if (trangthai_hddt == '' || trangthai_hddt == '2') {
					setEnabled([ 'btnPhatHanh' ], []);
				} else if (trangthai_hddt == '1') {
					setEnabled([ 'btnHuyHDDT' ], [ 'btnPhatHanh' ]);
				} else {
					setEnabled([], [ 'btnPhatHanh' ]);
				}
				var hoadonid = $("#" + _gridDSHoaDon).jqGrid('getCell', id, 'HOADONID');
				var objHoadon = new Object();
				objHoadon.HOADONID = hoadonid;
				var inputLayHoaDon = JSON.stringify(objHoadon);
				var arrHD = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI03T001.03', inputLayHoaDon);
				if (arrHD && arrHD.length > 0) {
					FormUtil.setObjectToForm("divNhapTTHoaDon", "", arrHD[0]);
					FormUtil.setObjectToForm("divTinhTien", "", arrHD[0]);
					$("#cboPATTERN_CODE").change();
					$("#cboSERIAL_CODE").val(arrHD[0].SERIAL_CODE);
					loadDSDichVu();
				}
			}
		});
		// xu ly nut "them"
		$("#btnThem").click(function() {
			if (flagLoading) {
				return false;
			}
			flagLoading = true;
			isEdit = false;
			hienThiFormNhap(false, 'C');
		});
		// xu ly nut "Luu" - them hoac sua phieu thu
		$("#btnLuu").click(function() {
			if (!flagLoading) {
				return false;
			}
			var _validator = new DataValidator('divNhapTTHoaDon');
			var valid = _validator.validateForm();
			if (!valid) {
				return false;
			}
			_validator = new DataValidator('divTinhTien');
			valid = _validator.validateForm();
			if (!valid) {
				return false;
			}
			isEdit = false;
			var objHoadon = new Object();
			FormUtil.setFormToObject('divNhapTTHoaDon', '', objHoadon);
			FormUtil.setFormToObject('divTinhTien', '', objHoadon);
			var arrDSDichVu = $('#' + _gridDSDichVu).getGridParam('data');
			if (arrDSDichVu.length == 0) {
				DlgUtil.showMsg("Chưa nhập dịch vụ");
				return;
			}
			objHoadon.DSDICHVU = arrDSDichVu;
			console.log(objHoadon);
			var inputLuuHoaDon = JSON.stringify(objHoadon);
			var strResult = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI03T001.01', inputLuuHoaDon);
			var rs = JSON.parse(strResult);
			if (parseInt(rs.HOADONID) > 0) {
				if (rs.LOAITHAOTAC == 'INS') {
					DlgUtil.showMsg("Thêm mới thành công");
				} else {
					DlgUtil.showMsg("Cập nhật thành công");
				}
				hienThiFormNhap(true, 'S');
				loadDSHoaDon();
				flagLoading = false;
			} else {
				DlgUtil.showMsg(rs.ERR);
			}
			//
		});
		// xu ly nut "sua"
		$("#btnSua").click(function() {
			if (flagLoading) {
				return false;
			}
			flagLoading = true;
			isEdit = true;
			hienThiFormNhap(false, 'U');
		});
		// xoa so phieu thu
		$("#btnXoa").click(function() {
			if (flagLoading) {
				return false;
			}
			DlgUtil.showConfirm("Xác nhận xóa", function xacnhan(flag) {
				if (!flag)
					return false;
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I(_sql_xoaso, _soptId);
				if (fl == 1) {
//					DlgUtil.showMsg("Xóa thành công");
					loadDSDichVu();
				} else if (fl == 0) {
					DlgUtil.showMsg("Sổ đã sử dụng, không được xóa");
				} else {
					DlgUtil.showMsg("Xảy ra lỗi");
				}
				isEdit = false;
				hienThiFormNhap(true, 'D');
			});
		});
		// xu ly nut "huy"
		$("#btnHuy").click(function() {
			if (!flagLoading) {
				return false;
			}
			isEdit = false;
			hienThiFormNhap(true, 'H');
			flagLoading = false;
		});
		$("#cboKHOAID").change(function() {
			ComboUtil.getComboTag("cboPHONGID", 'VPI.PHONG.KCB', [ {
				name : "[0]",
				value : $("#cboKHOAID").val()
			} ], "", {
				text : "Tất cả",
				value : -1
			}, 'sql', '', '');
		});
		$("#txtNGAYSINH").change(function() {
			$("#txtNAMSINH").val($("#txtNGAYSINH").val().substr(6));
		});
		$("#" + _gridDSDichVu).bind("CustomAction", function(e, act, rid) {
			if (!flagLoading) {
				return false;
			}
			/*var arrRow = [];
			if (act == 'del') {
				var ids = $("#" + _gridDSDichVu).getDataIDs();
				for (var i = rid - 1; i < ids.length; i++) {
					id = ids[i];
					if (id != rid) {
						arrRow.push($("#" + _gridDSDichVu).jqGrid('getRowData', id));
					}
					$("#" + _gridDSDichVu).jqGrid('delRowData', id);
				}
				for (var j = 0; j < arrRow.length; j++) {
					arrRow[j].STT = arrRow[j].STT - 1;
					$("#" + _gridDSDichVu).jqGrid('addRowData', arrRow[j].STT, arrRow[j], 'last');
				}
			}*/
			if (act == 'del') {
				$("#" + _gridDSDichVu).jqGrid('delRowData', rid);
				tinhhoadon();
			}
		});
		$('#cboTAX_RATE').change(function() {
			tinhtien();
			tinhhoadon();
		});
		$('#btnPhatHanh').click(function() {
			if (flagLoading) {
				return false;
			}
			guiHDDT('GUI');
		});
		$('#btnXemHDDT').click(function() {
			var selRowId = $("#" + _gridDSHoaDon).jqGrid("getGridParam", "selrow");
			if (!selRowId) {
				DlgUtil.showMsg("Chưa chọn hóa đơn");
				return;
			}
			var hoadonid_hddt = $("#" + _gridDSHoaDon).jqGrid('getCell', selRowId, 'HOADONID');
			xemHoaDon(hoadonid_hddt);
		});
		$('#btnXuatXMLHDDT').click(function() {
			var xml_hoadon = guiHDDT('XML');
			if (!xml_hoadon) {
				return;
			}
			initPopupMsg(xml_hoadon, {
				dlgTitle : 'XML HĐĐT',
				dlgWidth : 1024,
				dlgHeight : 720,
				dlgRow : 30
			});
		});
		$('#btnHuyHDDT').click(function() {
			var selRowId = $("#" + _gridDSHoaDon).jqGrid("getGridParam", "selrow");
			if (!selRowId) {
				DlgUtil.showMsg("Chưa chọn hóa đơn");
				return;
			}
			var hoadonid_hddt = $("#" + _gridDSHoaDon).jqGrid('getCell', selRowId, 'HOADONID');
			huyHoaDon(hoadonid_hddt);
		});
	}
	function hienThiFormNhap(disable, code) {
		$('#divNhapTTHoaDon').find(':input').prop('disabled', disable);
		$('#divTinhTien').find(':input').prop('disabled', true);
		if (disable) {
			setEnabled([ 'btnThem' ], []);
		} else {
			setEnabled([], [ 'btnThem', 'btnSua', 'btnXoa' ]);
		}
		if (code == 'C' || code == 'U') {
			$('#divNhapDV').find(':input').prop('disabled', false);
			setEnabled([ 'cboTAX_RATE' ], []);
		} else {
			$('#divNhapDV').find(':input').prop('disabled', true);
		}
		setEnabled([], [ 'txtTHANHTIEN' ]);
		if (code != 'U') {
			$("#" + _gridDSDichVu).jqGrid("clearGridData");
			FormUtil.clearForm('divNhapTTHoaDon', "");
			FormUtil.clearForm('divNhapDV', "");
			$("#cboKHOAID").prop("selectedIndex", 0);
			$("#cboPHONGID").prop("selectedIndex", 0);
			$("#cboGIOITINHID").prop("selectedIndex", 0);
			$("#cboDANTOCID").prop("selectedIndex", 0);
			$("#cboPATTERN_CODE").prop("selectedIndex", 0);
			$("#cboSERIAL_CODE").prop("selectedIndex", 0);
			$("#cboPAYMENT_METHOD").prop("selectedIndex", 0);
		}
	}
	// lay du lieu cho gird so phieu thu o trang chinh
	function loadDSDichVu() {
		$("#" + _gridDSDichVu).jqGrid("clearGridData");
		var selRowId = $("#" + _gridDSHoaDon).jqGrid("getGridParam", "selrow");
		var hoadonid = $("#" + _gridDSHoaDon).jqGrid('getCell', selRowId, 'HOADONID');
		var objHoadonDV = new Object();
		objHoadonDV.HOADONID = hoadonid;
		var inputLayHoaDonDV = JSON.stringify(objHoadonDV);
		var arrHDDV = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI03T001.04', inputLayHoaDonDV);
		if (arrHDDV && arrHDDV.length > 0) {
			GridUtil.fetchGridData(_gridDSDichVu, arrHDDV);
			$("#" + _gridDSDichVu).trigger("reloadGrid");
			tinhhoadon();
		}
	}
	// lay du lieu cho grid hoa don
	function loadDSHoaDon() {
		var _sql_par = [];
		var _param = [];
		var objTimKiem = new Object();
		FormUtil.setFormToObject('divSearch', '', objTimKiem);
		_param = [ JSON.stringify(objTimKiem) ];
		_sql_par = RSUtil.setSysParam(_sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridDSHoaDon, 'VPI03T001.02', _sql_par, function() {
			$('#divThaoTacHDDT').find(':input').prop('disabled', true);
			var grid = $("#" + _gridDSHoaDon);
			var ids = grid.getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _icon = '';
				var trangthai_hddt = parseInt(grid.jqGrid('getCell', id, 'TRANGTHAI_HDDT'));
				if (isNaN(trangthai_hddt)) {
					_icon = '<center><img src="' + _opts.imgPath[0] + '" width="15px"></center>';
				} else if (trangthai_hddt == 2) {
					_icon = '<center><img src="' + _opts.imgPath[1] + '" width="15px"></center>';
				} else if (trangthai_hddt == 1) {
					_icon = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
				} else if (trangthai_hddt == 0) {
					_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
				}
				$("#" + _gridDSHoaDon).jqGrid('setCell', id, 'ICON', _icon);
			}
			$(".jqgrow", '#' + _gridDSHoaDon).contextMenu('contextMenu', {
				bindings : {
					'rCapNhatPhieuThu' : function(t) {}
				},
				onContextMenu : function(event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#' + _gridDSHoaDon);
					grid.setSelection(rowId);
					return true;
				},
			});
		});
	}
	function tinhhoadon() {
		var grid = $("#" + _gridDSDichVu);
		var ids = grid.getDataIDs();
		var total = 0;
		var tax_rate = 0;
		var tax_amount = 0;
		var amount = 0;
		for (var i = 0; i < ids.length; i++) {
			var id = ids[i];
			var _thanhtien = parseFloat(grid.jqGrid('getCell', id, 'THANHTIEN'));
			var _vat = parseFloat(grid.jqGrid('getCell', id, 'VAT'));
			var _tien_vat = parseFloat(grid.jqGrid('getCell', id, 'TIEN_VAT'));
			var _tongtien = parseFloat(grid.jqGrid('getCell', id, 'TONGTIEN'));
			total += _thanhtien;
			tax_amount += _tien_vat;
			amount += _tongtien;
		}
		total = parseFloat(total.toFixed(2));
		tax_amount = parseFloat(tax_amount.toFixed(2));
		amount = parseFloat(amount.toFixed(2));
		var tax_rate = parseFloat($("#cboTAX_RATE").val());
		$("#txtTOTAL").val(total);
		$("#txtTAX_AMOUNT").val(tax_amount);
		$("#txtAMOUNT").val(amount);
	}
	// ham gui hd ban led
	function guiHDDT(mode) {
		var selRowId = $("#" + _gridDSHoaDon).jqGrid("getGridParam", "selrow");
		if (!selRowId) {
			DlgUtil.showMsg("Chưa chọn hóa đơn");
			return false;
		}
		var hoadonid_hddt = $("#" + _gridDSHoaDon).jqGrid('getCell', selRowId, 'HOADONID');
		var pattern = $("#" + _gridDSHoaDon).jqGrid('getCell', selRowId, 'PATTERN_CODE');
		var serial = $("#" + _gridDSHoaDon).jqGrid('getCell', selRowId, 'SERIAL_CODE');
		var xml_hoadon = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI03T001.05", hoadonid_hddt);
		if (mode == 'XML') {
			return xml_hoadon;
		}
		var objGuiHDDT = new Object();
		objGuiHDDT.XML_HOADON = xml_hoadon;
		objGuiHDDT.INVOICES_URL_IMPORT = dsCH.INVOICES_URL_IMPORT;
		objGuiHDDT.INVOICES_WS_USER_ACC = dsCH.INVOICES_WS_USER_ACC;
		objGuiHDDT.INVOICES_WS_PWD_ACC = dsCH.INVOICES_WS_PWD_ACC;
		objGuiHDDT.INVOICES_WS_USER = dsCH.INVOICES_WS_USER;
		objGuiHDDT.INVOICES_WS_PWD = dsCH.INVOICES_WS_PWD;
		objGuiHDDT.INVOICES_WS_PATTERN = pattern;
		objGuiHDDT.INVOICES_WS_SERIAL = serial;
		objGuiHDDT.HOADONID = hoadonid_hddt;
		DlgUtil.showMsg(guiHDDTtheoXML(objGuiHDDT));
	}
	// ham gui hd ban le
	function guiHDDTtheoXML(objGuiHDDT) {
		try {
			var kqCapNhatHDDT = capNhatHDDT("2", "", "", "", objGuiHDDT.HOADONID);
			var ret = ajaxSvc.InvoicesWS.importHoaDon(objGuiHDDT.XML_HOADON, objGuiHDDT.INVOICES_URL_IMPORT, objGuiHDDT.INVOICES_WS_USER_ACC, objGuiHDDT.INVOICES_WS_PWD_ACC,
					objGuiHDDT.INVOICES_WS_USER, objGuiHDDT.INVOICES_WS_PWD, objGuiHDDT.INVOICES_WS_PATTERN, objGuiHDDT.INVOICES_WS_SERIAL);
			if (ret == "ERR:13") {
				var ret1 = ajaxSvc.InvoicesWS.listInvByCusFkey(hoadonid_hddt, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
				if (ret1 == "" || ret1.toUpperCase().includes("ERR")) {
					return "Hóa đơn đã phát hành cập nhật số hóa đơn nhưng không lấy được thông tin để cập nhật";
				}
				var obj = convertXml2JSon(ret1);
				obj = JSON.parse(obj);
				var invoi_nb = obj.Data.Item.invNum;
				var kqCapNhatHDDT = capNhatHDDT("1", objGuiHDDT.INVOICES_WS_PATTERN, objGuiHDDT.INVOICES_WS_SERIAL, ret.split("_")[1], objGuiHDDT.HOADONID);
				return "Hóa đơn đã phát hành cập nhật số hóa đơn thành công";
			} else if (ret.substring(0, 3) != "OK:") {
				var err_sendInv = "";
				if (ret == "ERR:1") {
					err_sendInv = "Tài khoản đăng nhập sai hoặc không có quyền";
				} else if (ret == "ERR:3") {
					err_sendInv = "Dữ liệu xml đầu vào không đúng chuẩn quy định";
				} else if (ret == "ERR:5") {
					err_sendInv = "Không phát hành được hóa đơn";
				} else if (ret == "ERR:6") {
					err_sendInv = "Không đủ số hóa đơn cho lô phát hành";
				} else if (ret == "ERR:7 ") {
					err_sendInv = "User name không phù hợp, không tìm thấy company tương ứng cho user";
				} else if (ret == "ERR:10") {
					err_sendInv = "Lô có hóa đơn vượt quá max cho phép";
				} else if (ret == "ERR:20") {
					err_sendInv = "Pattern và Serial không phù hợp, hoặc không tồn tại hóa đơn đã đăng kí có sử dụng Pattern và Serial truyền vào";
				} else {
					err_sendInv = "Lỗi đẩy HDDT";
				}
				return err_sendInv;
			} else {
				var kqCapNhatHDDT = capNhatHDDT("1", objGuiHDDT.INVOICES_WS_PATTERN, objGuiHDDT.INVOICES_WS_SERIAL, ret.split("_")[1], objGuiHDDT.HOADONID);
				setTimeout(function() {
					xemHoaDon(hoadonid_hddt);
				}, 1000);
				return "1";
			}
		} catch (err) { // BVTM-4343 start
			return "Có lỗi xảy ra, chưa gửi được HDDT: " + err;
		}
	}
	function capNhatHDDT(trangthai_hddt, pattern_code, serial_code, ma_hoadon, hoadonid) {
		var objCapNhatHDDT = new Object();
		objCapNhatHDDT.TRANGTHAI_HDDT = trangthai_hddt;
		objCapNhatHDDT.PATTERN_CODE = pattern_code;
		objCapNhatHDDT.SERIAL_CODE = serial_code;
		objCapNhatHDDT.MA_HOADON = ma_hoadon;
		objCapNhatHDDT.HOADONID = hoadonid;
		var inputCapNhatHDDT = JSON.stringify(objCapNhatHDDT);
		return jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI03T001.06", inputCapNhatHDDT);
	}
	// xem hóa đơn
	function xemHoaDon(hoadonid_hddt) {
		var result = ajaxSvc.InvoicesWS.viewHoaDon(hoadonid_hddt, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
		if (result.includes("ERR:6"))
			DlgUtil.showMsg("Không tìm thấy hóa đơn");
		else if (result == "" || result.includes("ERR:")) {
			DlgUtil.showMsg("Lỗi trong quá trình xử lý");
		} else {
			var randomnumber = Math.floor((Math.random() * 100) + 1);
			var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
			win.document.write(result);
		}
	}
	// hủy hóa đơn
	function huyHoaDon(hoadonid_hddt) {
		var ret = ajaxSvc.InvoicesWS.cancelHoaDon(hoadonid_hddt, dsCH.INVOICES_URL_CANCEL, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
		if (ret == "OK:") {
			var _return = capNhatHDDT("0", "", "", "", hoadonid_hddt)
			if (_return == '-1') {
				DlgUtil.showMsg("Cập nhật trạng thái hủy HDDT không thành công");
				return false;
			}
			return true;
		} else {
			if (ret == "ERR:1") {
				DlgUtil.showMsg("Tài khoản đăng nhập sai hoặc không có quyền");
			} else if (ret == "ERR:2") {
				DlgUtil.showMsg("Không tồn tại hóa đơn cần hủy");
			} else if (ret == "ERR:8") {
				DlgUtil.showMsg("Hóa đơn đã được thay thế rồi, hủy rồi");
			} else if (ret == "ERR:9") {
				DlgUtil.showMsg("Trạng thái hóa đơn không được hủy");
			}
			return false;
		}
	}
}
