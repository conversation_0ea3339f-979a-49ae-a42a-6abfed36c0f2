(function($) {
	$.widget( "ui.ngt02k090_badtngt" , {
        //Options to be used as defaults
        options: {        
        	_grdDSTN: "grdDanhSachTiep<PERSON>han",
        	hosobenhanid : "", 
        	benhnhanid : "", 
        	khambenhid : "", 
        	phongkhamdangkyid : ""
        },
        containerId: '',
        //Setup widget (eg. element creation, apply theming
        // , bind events etc.)
        _create: function () {
            // _create will automatically run the first time this widget is called. Put the initial widget  setup code here, then you can access the element
            // on which the widget was called via this.element. The options defined above can be accessed via this.options this.element.addStuff();
        	console.log('_create');
        	this.containerId=$(this.element).attr('id');        	
        	this._initWidget();         
        },
        _t : function(_id) {
        	var newId='';
        	if(_id.indexOf("=")>0) {
        		newId=_id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi,_self.containerId+"$1");
        	}
        	else if(_id.indexOf("#")==0) {
        		newId=_id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi,"$1"+_self.containerId+"$2");
        	}
        	else {
        		newId=_self.containerId+_id;
        	}
        	return newId;
        },
        _initWidget: function() {
        	var _self=this;
        	_self.options.checkLoad = false;
        	$(_self.element).load('../ngoaitru/NGT02K090_BADTNGT_widget.tpl', function() {
        		$(_self.element).find("[id]").each(function() {        			
        			if(this.id == "pager_" + _self.options._grdDSTN){
        				this.id = "pager_" + _self.containerId + _self.options._grdDSTN;
        			}else if(this.id == "pager_" + _self.options._grdDSTNDetail){
        				this.id = "pager_" + _self.containerId+_self.options._grdDSTNDetail;
        			}else{
        				this.id = _self.containerId+this.id;	
        			}  
        	    })
        	    
    			//$("[data-i18n]").i18n();
    			
                height_suatan = $('#' + _self.element.attr('id')).height();
                height_divMain = $('#hidDocumentHeight').val();
	            console.log('height_window1:' + height_window );
	            console.log('height_suatan1:' + height_suatan );
	            console.log('height_divMain1:' + height_divMain );
	            if(height_suatan + 110 < height_window){
	            	$('#divMain').css('height',height_divMain);
	            } else if(height_window < height_suatan + 110){
		             $("#divMain").css('height',height_suatan + 110);  
	            } else if(height_suatan + 110 == height_window) {
	            	$('#divMain').css('height',height_suatan + 110);
	            }
	            
	            _self._initControl();
	            _self._loadData();
    			_self._bindEvent();
    			height_window = $(window).height();   // returns height of browser viewport
    			
    		});
        },
        
        _initControl: function(){ 
        	var _self=this;  
        	var _grdDSTNHeader =
	        		"pkdkid,phongkhamdangkyid,100,0,t,l;" +
	        		"kbid,khambenhid,100,0,t,l;" +
	        		"Đang ĐTNGT,trangthai,30,0,f,c;" +
	        		"Tên phòng,tenphong,200,0,f,l;" +
	        		"Ngày bắt đầu,ngay_bd,80,0,f,l;" +
	        		"Ngày kết thúc,ngay_kt,80,0,f,l;" +
	        		"Trạng thái,trangthai_stt,90,0,f,l;" +
	        		"STT,sothutu,40,0,f,l;" +
	        		"Mã chẩn đoán,machandoanravien,50,0,f,l;" +
	        		"Chẩn đoán,chandoanravien,220,0,f,l;" +
	        		"Yêu câu khám,yeucaukham,180,0,f,l"
	    			; 
 
        	GridUtil.init(_self.containerId+_self.options._grdDSTN,"100%","400px",
        				'Thông tin bệnh án điều trị ngoại trú',false, _grdDSTNHeader,false,{rowNum: 20,rowList: [20, 50, 100]});
        }, 
        
        _loadData: function() {   
        	var _self=this;  
        	var _look_sql="NGT02T001.GETBADTNGT"; 
    		var _sql_par=[];
			
			_sql_par.push({"name":"[0]","value": _self.options.khambenhid});
			_sql_par.push({"name":"[0]","value": _self.options.hosobenhanid});
			_sql_par.push({"name":"[0]","value": _self.options.benhnhanid});
			_sql_par.push({"name":"[0]","value": _self.options.phongkhamdangkyid});
		    
//			alert(JSON.stringify(_sql_par)); 
			$("#"+_self.containerId+_self.options._grdDSTN).jqGrid("clearGridData", true);
			GridUtil.loadGridBySqlPage(_self.containerId+_self.options._grdDSTN,_look_sql,_sql_par);
        },
        
        
        _bindEvent: function() {  
        	var _self=this;
        	GridUtil.setGridParam(_self.containerId+_self.options._grdDSTN,{ 
    	    	onSelectRow: function(id, selected) {    	    		  	    		
    	    		GridUtil.unmarkAll(_self.containerId+_self.options._grdDSTN);
    	    		GridUtil.markRow(_self.containerId+_self.options._grdDSTN,id);
    	    	}
    	    });
        }, 
        
        // Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
        destroy: function () {

            // this.element.removeStuff();
            // For UI 1.8, destroy must be invoked from the
            // base widget
            $.Widget.prototype.destroy.call(this);
            // For UI 1.9, define _destroy instead and don't
            // worry about
            // calling the base widget
        },

        methodB: function ( event ) {
            //_trigger dispatches callbacks the plugin user
            // can subscribe to
            // signature: _trigger( "callbackName" , [eventObject],
            // [uiObject] )
            // eg. this._trigger( "hover", e /*where e.type ==
            // "mouseenter"*/, { hovered: $(e.target)});
            console.log("methodB called");
        },

        methodA: function ( event ) {
            this._trigger("dataChanged", event, {
                key: "someValue"
            });
        },

        // Respond to any changes the user makes to the
        // option method
        _setOption: function ( key, value ) {
        	
            switch (key) {
            case "someValue":
                //this.options.someValue = doSomethingWith( value ); 
                break;
            default:
                //this.options[ key ] = value; 
                break;
            }
            
            // For UI 1.8, _setOption must be manually invoked
            // from the base widget
            $.Widget.prototype._setOption.apply( this, arguments );
            
//            alert("ahi161 " + key + "=>" + value); 
             if(key=='_grdDSTN'){						// chi lay 1 key de hien thi ra; 
            	this._initWidget();	
            }       
            // For UI 1.9 the _super method can be used instead
            // this._super( "_setOption", key, value );
        }
    });
})(jQuery);
