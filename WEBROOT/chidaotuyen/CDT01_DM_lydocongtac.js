function dSCHList(_opts) {
	
	var _gridDSCHId="grdDanhSachND"; 
	var _gridDSCHHeader= "lydocongtacid,LYDOCONGTACID,60,0,t,l,1,2;"
							+ "<PERSON><PERSON> lý do,MALYDO,120,0,f,l,1,2;"
							+ "<PERSON>h<PERSON> tự,THUTUSAPXEP,70,0,f,l,1,2;"
							+ "Tên lý do,TENLYDO,330,0,f,l,1,2";

	var _user_id=-1;
	var _hospital_id;
	_param=[];
	_cauhinhId = -1;
	var checkRequired;
	var validator = null;
	var valid_ar = [];
	
	var that=this;
	this.load=doLoad;
	
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		validator = new DataValidator("inputForm");
		var _options=$.extend({},_opts);
		var uuid = _options._uuid;
		var _param=_options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_initControl();
		_bindEvent();
	}
	
	function addNumberOnly(element){
		$(element).keypress(function (e) {
		     if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
		        return false;
		    }
		});
	}
	addNumberOnly($("#txtTHUTUSAPXEP"));
	
	function _initControl(){
		GridUtil.init(_gridDSCHId,"100%","468","",false,_gridDSCHHeader, 
							false, { rowNum: 500,rowList: [500, 800, 1000]});
		loadGridData();
	};
	
	function _bindEvent() {
		GridUtil.setGridParam(_gridDSCHId,{
			onSelectRow: function(id) {
				GridUtil.unmarkAll(_gridDSCHId);
	    		GridUtil.markRow(_gridDSCHId,id);
	            if (id) {
	            	var _row = $("#"+_gridDSCHId).jqGrid('getRowData', id);
	            	FormUtil.clearForm("inputForm","");
	            	FormUtil.setObjectToForm("inputForm","", _row);
	            	
	            	setEnabled(["btnHuy","btnSua", "btnXoa","btnThem"],[ "btnLuu", 
	            	                  'txtMALYDO', 'txtTENLYDO', 'txtTHUTUSAPXEP']);
	            }
	        }
		});
		
		$("#btnThem").click(function(){
			
			FormUtil.clearForm('inputForm',"");
			
			setEnabled(['btnLuu', 'btnHuy','txtMALYDO', 'txtTENLYDO', 'txtTHUTUSAPXEP'], 
					['btnThem', 'btnSua', 'btnXoa']);
		});
		
		$("#btnLuu").click(function(){
			var valid1 = validator.validateForm();
			if(valid1){
				var _validator=new DataValidator([{region:'inputForm',fields:valid_ar}]);	
				var valid= _validator.validateForm();
				if(!valid){
					return false;
				}
				
				objData = new Object();		
				FormUtil.setFormToObject("inputForm", "", objData);
//				DlgUtil.showMsg(JSON.stringify(objData));
				
				var fl = null;
				var kythuathotroid = $("#txtLYDOCONGTACID").val();
				if(kythuathotroid == ""){
					fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.LDCTTHEM", JSON.stringify(objData));
					if(Number(fl) == 1 ){
						DlgUtil.showMsg("Thêm mới thành công !");
						loadGridData()
						$("#btnHuy").click();
					}else if(fl == 2){
						DlgUtil.showMsg("Mã lý do này đã tồn tại !");
					}else{
						DlgUtil.showMsg("Lỗi thêm mới thông tin !");
					}
				}else{
					fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.LDCTSUA", JSON.stringify(objData));
					if(Number(fl) == 1 ){
						DlgUtil.showMsg("Cập nhật thành công !");
						loadGridData()
						$("#btnHuy").click();
					}
					else if(fl == 2){
						DlgUtil.showMsg("Mã lý do này đã tồn tại !");
					}else{
						DlgUtil.showMsg("Lỗi cập nhật thông tin !");
					}
				}
			}
		});
		
		
		$("#btnSua").click(function(){
			flagLoading = true;
			isEdit = true;
			setEnabled(['btnLuu', 'btnHuy','txtMALYDO', 'txtTENLYDO', 'txtTHUTUSAPXEP'], 
							['btnThem', 'btnSua', 'btnXoa']);
		});
		
		$("#btnXoa").click(function(){
			DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này không?",function(flag) {
				if(flag){		
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.LDCTXOA", $("#txtLYDOCONGTACID").val());
					if(Number(fl) > 0){
						DlgUtil.showMsg("Xóa thành công !");
						FormUtil.clearForm('inputForm',"");
					    loadGridData();
					    setEnabled(['btnThem'], ['btnSua', 'btnXoa', 'btnHuy',
					                             'txtMALYDO', 'txtTENLYDO', 'txtTHUTUSAPXEP']);
					}
					else{
						DlgUtil.showMsg("Không thể xóa lý do đang được sử dụng !");
					}
				 } 
			});
		});

		
		$("#btnHuy").click(function(){
			FormUtil.clearForm("inputForm","");			
			setEnabled(['btnThem'], 
					['btnSua', 'btnXoa','btnLuu', 'btnHuy',
					 'txtMALYDO', 'txtTENLYDO', 'txtTHUTUSAPXEP']);
		});
		
	}
	
	function loadGridData() {
	
		var _sql_par =[];

		_sql_par = RSUtil.setSysParam(_sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridDSCHId,"DMC.LDCTLAYDL",_sql_par);
	}
	
	function setEnabled(_ena, _dis) {
		for (var i =0; i<_ena.length; i++) {
			$("#"+_ena[i]).attr('disabled', false);
		}
		for (var i =0; i<_dis.length; i++) {
			$("#"+_dis[i]).attr('disabled', true);
		}
	}
	
}
