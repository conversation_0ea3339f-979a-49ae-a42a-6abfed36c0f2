<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI_10284" language="groovy" pageWidth="595" pageHeight="842" columnWidth="546" leftMargin="39" rightMargin="10" topMargin="20" bottomMargin="20" uuid="062ad1cf-ad8c-4843-a0c7-e03f0f0cbd08">
	<property name="ireport.zoom" value="1.9487171000000123"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="i_benhnhanid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="i_khoaid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_khambenhid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_maubenhphamids" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call ntu_todieutri_multi_10284($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{i_benhnhanid},$P{i_khoaid},$P{i_khambenhid},$P{i_maubenhphamids},$P{ora_cursor})}]]>
	</queryString>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="NAMSINH" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="CMND" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="BUONG" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="TENBENHVIEN" class="java.lang.String"/>
	<field name="SOYTE" class="java.lang.String"/>
	<field name="SO" class="java.lang.String"/>
	<field name="MAGIUONG" class="java.lang.String"/>
	<field name="MAUBENHPHAMID" class="java.math.BigDecimal"/>
	<field name="HOSOBENHANID" class="java.math.BigDecimal"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="NGAYMAUBENHPHAM" class="java.lang.String"/>
	<field name="DIENBIENBENH" class="java.lang.String"/>
	<field name="YLENH" class="java.lang.String"/>
	<field name="PDT_CLS" class="java.lang.String"/>
	<field name="CHEDOAN" class="java.lang.String"/>
	<field name="CHEDOCHAMSOC" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="BSDIEUTRI" class="java.lang.String"/>
	<field name="SOTO_DT" class="java.lang.String"/>
	<field name="XULY" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="130" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="41" y="83" width="155" height="20" uuid="6571e4ca-2758-4dbb-b3be-368f21332937"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="70" y="104" width="476" height="20" uuid="df373a9c-2c23-47d2-98b0-e23e9bb2b6e3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHANDOAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="440" y="83" width="107" height="20" uuid="43c1503d-16b2-4eda-8795-4a2e68edceac"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAGIUONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="104" width="534" height="20" uuid="5b41fa3a-cd53-49f8-a2df-52d78b494e73"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[- Chẩn đoán:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="243" y="83" width="155" height="20" uuid="47de1537-9cc5-43e2-a168-cfa364c8a6cb"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BUONG}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="20" width="227" height="20" uuid="85819cd1-a4b4-46a2-a5e0-07852646e072"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHVIEN}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="227" height="20" uuid="e55392e6-5c7d-4769-9d64-7cd76da21b84"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOYTE}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="196" y="83" width="47" height="20" uuid="86a27435-5188-4775-8a53-25cf64f0466c"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Buồng: ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="497" y="62" width="49" height="20" uuid="ba149f17-1dd1-4fd9-b3d7-d3c3cc4642c0"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="331" y="62" width="53" height="20" uuid="93ae613d-c125-4e8f-80fc-a826ae314c7f"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Năm sinh:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="83" width="41" height="20" uuid="6dfd8f09-87ec-4caf-946f-12f0ea9d5963"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Khoa: ]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="414" y="40" width="139" height="20" uuid="bd90c642-0ba9-4658-a0dd-2cec299dcc3a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Số: " + ($F{SOPHIEU}==null?"...........":$F{SOPHIEU})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="454" y="20" width="99" height="20" uuid="3e600225-a009-4d07-a38a-114bf9f65635"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="384" y="62" width="63" height="20" uuid="28f9dd63-c070-4f2e-ba9c-bc9f8ed43a7a"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMSINH}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="122" y="62" width="209" height="20" uuid="b7bb1d31-c20a-4676-b5db-2a5d0168c715"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="398" y="83" width="42" height="20" uuid="b5d66f3f-d512-40ce-9654-b61adc307de8"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Giường: ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="243" y="0" width="125" height="20" uuid="d541cd12-fc14-46c1-8588-41e0d4d66110"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[TỜ ĐIỀU TRỊ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="414" y="20" width="62" height="20" uuid="1a1fe54f-a894-4e7a-8cb7-27945ec1ae5f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Mã BA:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="414" y="0" width="78" height="20" uuid="3e51d4ee-15ce-4586-a73a-a49d49b9f407"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[MS: 39/BV-01]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="447" y="62" width="50" height="20" uuid="f74525dc-99ce-48bb-b7c5-c736c9263970"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Giới tính:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="62" width="122" height="20" uuid="0b4c80ec-9763-4f4c-8c33-f913535a746e"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Họ và tên người bệnh:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="243" y="20" width="125" height="20" uuid="eb132372-fd9c-481a-8240-b778b916eef7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["SỐ: " + ($F{SOTO_DT}==null?"...........":$F{SOTO_DT})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="23" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="56" height="23" uuid="697a2aa4-d39b-44eb-ad63-4430ab486d7e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[NGÀY GIỜ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="243" y="0" width="303" height="23" uuid="ad656f89-3457-44a6-8007-8dffe242ea40"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Y LỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="56" y="0" width="187" height="23" uuid="3abd83ff-16ba-475b-9c70-3f5ef3540b34"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[DIỄN BIẾN BỆNH]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="52" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="243" y="0" width="303" height="52" uuid="4a4477ff-25a2-4531-995c-d46b97e5e1c1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{YLENH}==null?"":$F{YLENH}+"<br>") +
($F{XULY}==null?"":"<b>Xử lý: </b>"+$F{XULY}.replaceAll( "\n", "<br>" )+"<br>")+
($F{PDT_CLS}==null?"":"<b>*Cận lâm sàng và Y lệnh khác:</b><br>"+$F{PDT_CLS}+"<br>") +
($F{CHEDOAN}==null?"":$F{CHEDOAN}+"<br>")+
($F{CHEDOCHAMSOC}==null?"":"<b>*Chế độ chăm sóc: </b>"+$F{CHEDOCHAMSOC}+"<br>")+
($F{GHICHU}==null?"":"<b>Ghi chú: </b>"+$F{GHICHU}.replaceAll( "\n", "<br>" ))+
"<br><br>" + "&#9;&#9;&#9;<b>Bác sĩ điều trị</b><br><br><br><br>"+($F{BSDIEUTRI}==null?"":"&#9;&#9;&#9;<b>"+$F{BSDIEUTRI}+"</b><br>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="56" height="52" uuid="018e325d-f0e5-4b15-8840-987471f58af1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYMAUBENHPHAM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="56" y="0" width="187" height="52" uuid="77e5f719-445b-4c33-a2b1-1dc505fb1b10"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIENBIENBENH}.replaceAll( "\n", "<br>" )]]></textFieldExpression>
			</textField>
		</band>
		<band/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
