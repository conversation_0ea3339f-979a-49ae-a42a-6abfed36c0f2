/*
 Mã màn hình  : NTU02D060
 File mã nguồn : NTU02D060_GuiBaoHiem.js
 Mục đích  : <PERSON><PERSON><PERSON> diện màn hình
 	+ <PERSON>hập thông tin bệnh án
 	+ <PERSON>hập thông tin hỏi bệnh
 Tham số vào : 
 	maubenhpham_id 	: ID mẫu bệnh phẩm
 	khambenh_id		: ID khám bệnh
	
 <PERSON> lập trình	 <PERSON> cập nh<PERSON>t  	<PERSON> chú
 linhvv				 1/9/2016			Sửa bổ xung tính năng 
 */
function GuiBaoHiem(_opts) {
	var _user_id = _opts.user_id;
	var _hospital_id = _opts.hospital_id;
	var _schema_name = _opts.schema_name;
	var _hos_code = _opts.hos_code;
	var _action = "";
	var _typeDate = "";
	var _loai = "";
	var _tuyen = "";
	var _mathe = "";
	var _loaithe = "-1"; // duonghn start: cv 130
	var _loaiXML = "4210"; // duonghn start: cv 130
	var ma_lks = "-1";
	var _cskcb = "-1";
	var ds_ba = "";
	var rows = [];
	var sql_DataXls_XML = "NTU02D060.13"; //L2PT-98060
	var path = "../common/img/";
	var _imgPath = [ path + "About.png", path + "OK.png", path + "Problem.png" ]; // L2PT-75979
	var _gridDS_HOSO = "grdDS_HOSO";
	// L2PT-10430 thêm cột MA_BA
	// L2PT-11452 thêm cột NGAYRAVIEN,NGAYQUYETTOAN,NGAYDUYET
	// L2PT-11789 thêm cột MA_LK
	// L2PT-75979 doi ten cot ICON
	var _gridDS_HOSO_Header = " ,TT_GUI_CONG,20,0,ns,l;" + " ,TT_GUI_BHXH,20,0,ns,l;" + "ID_GOC,ID_GOC,20,0,t,l;" + "Cổng BYT,TT_GUI_CONG,0,0,t,l;" + "Cổng BHXH,TT_GUI_BHXH,0,0,t,l;"
			+ "MA_LK,MA_LK,70,0,f,l;" + "STT,STT_DUYET,40,0,f,l;" + "Mã BN XML,MA_BN,120,0,f,l;" + "Bệnh nhân,HO_TEN,180,0,f,l;" + "Ngày ra,NGAY_RAVIEN,120,0,f,c;" + "Ngày ra,NGAYRAVIEN,110,0,t,c;"
			+ "Ngày quyết toán,NGAYQUYETTOAN,110,0,f,c;" + "Ngày duyệt,NGAYDUYET,110,0,f,c;" + "Mã bệnh án,MA_BA,110,0,f,c;" + "Mã thẻ,MA_THE,120,0,f,c;" + "Ngày sinh,NGAY_SINH,80,0,f,c;"
			+ "Giới tính,GIOI_TINH,70,0,f,l;" + "Địa chỉ,DIA_CHI,250,0,ns,l";
	var _gridDS_HOSO_Header1 = " ,TT_GUI_CONG,20,0,ns,l;" + " ,TT_GUI_BHXH,20,0,ns,l;" + "ID_GOC,ID_GOC,20,0,t,l;" + "Cổng BYT,TT_GUI_CONG,0,0,t,l;" + "Cổng BHXH,TT_GUI_BHXH,0,0,t,l;"
			+ "STT,STT_DUYET,40,0,f,l;" + "MA_LK,MA_LK,70,0,f,l;" + "Mã BN XML,MA_BN,120,0,f,l;" + "Mã bệnh án,MA_BA,120,0,f,l;" + "Bệnh nhân,HO_TEN,180,0,f,l;" + "Mã BHYT,MA_THE,120,0,f,c;"
			+ "Ngày vào viện,NGAYTIEPNHAN,120,0,f,c;" + "Ngày ra viện,NGAY_RAVIEN,110,0,f,c;" + "Ngày ra,NGAYRAVIEN,110,0,t,c;" + "Ngày thanh toán,NGAYTHANHTOAN,110,0,f,c;"
			+ "Ngày quyết toán,NGAYQUYETTOAN,110,0,f,c;" + "Ngày duyệt BHYT,NGAYDUYET,110,0,f,c;" + "Ngày sinh,NGAY_SINH,80,0,f,c;" + "Giới tính,GIOI_TINH,70,0,f,l;" + "Địa chỉ,DIA_CHI,250,0,ns,l"; //L2PT-17933
	GridUtil.fm.iconMap = {
		'1' : 'check',
		'0' : 'cancel'
	};
	var _lstExp = "chkEXP_REPORT_19,chkEXP_REPORT_20,chkEXP_REPORT_21,chkEXP_REPORT_79,chkEXP_REPORT_80";
	var _lstExp_CHECK = "chkEXP_REPORT_19_CHECK,chkEXP_REPORT_20_CHECK,chkEXP_REPORT_21_CHECK,chkEXP_REPORT_79_CHECK,chkEXP_REPORT_80_CHECK";
	var _lstReport = "DUC003_TKVATTUTHANHTOANBHYT_19BHYT_QD1399_A4,DUC004_TKTHUOCTHANHTOANBHYT_20BHYT_QD1399_A4,NTU002_THONGKEDVKTTHANHTOANBHYT_21BHYT_QD1399_A4,NTU003_DSNBBHYTKCBNOITRUDENGHITHANHTOAN_C80a_HD_QD1399_A4,NGT002_DSNBBHYTKCBNGOAITRUDENGHITHANHTOAN_C79a_HD_QD1399_A4";
	var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	//var _sql="select icd10code,icd10name from dmc_icd where upper(icd10code) like upper('%[FILTER]%')";
	// Simple type mapping; dates can be hard
	// and I would prefer to simply use `datevalue`
	// ... you could even add the formula in here.
	var _DataType_Rpt19 = {
		"STT" : "n",
		"MA_VTYT" : "s",
		"TEN_VTYT" : "s",
		"TEN_THUONGMAI" : "s",
		"QUY_CACH" : "s",
		"DON_VI" : "s",
		"GIA_MUA" : "n",
		"SL_NOITRU" : "n",
		"SL_NGOAITRU" : "n",
		"GIA_THANHTOAN" : "n",
		"THANH_TIEN" : "n"
	};
	var _DataType_Rpt20 = {
		"STT" : "n",
		"MA_THUOC" : "s",
		"TEN_HOATCHAT" : "s",
		"TEN_THUOC" : "s",
		"DUONG_DUNG" : "s",
		"HAM_LUONG" : "s",
		"SO_DKY" : "s",
		"DON_VI" : "s",
		"SL_NOITRU" : "n",
		"SL_NGOAITRU" : "n",
		"DON_GIA" : "n",
		"THANH_TIEN" : "n"
	};
	var _DataType_Rpt21 = {
		"STT" : "n",
		"MA_DVKT" : "s",
		"TEN_DVKT" : "s",
		"SL_NOITRU" : "n",
		"SL_NGOAITRU" : "n",
		"DON_GIA" : "n",
		"THANH_TIEN" : "n"
	};
	var _DataType_Rpt7980 = {
		"STT" : "n",
		"MA_BN" : "s",
		"HO_TEN" : "s",
		"NGAY_SINH" : "s",
		"GIOI_TINH" : "n",
		"DIA_CHI" : "s",
		"MA_THE" : "s",
		"MA_DKBD" : "s",
		"GT_THE_TU" : "s",
		"GT_THE_DEN" : "s",
		"MA_BENH" : "s",
		"MA_BENHKHAC" : "s",
		"MA_LYDO_VVIEN" : "s",
		"MA_NOI_CHUYEN" : "s",
		"NGAY_VAO" : "s",
		"NGAY_RA" : "s",
		"SO_NGAY_DTRI" : "n",
		"KET_QUA_DTRI" : "n",
		"TINH_TRANG_RV" : "n",
		"T_TONGCHI" : "n",
		"T_XN" : "n",
		"T_CDHA" : "n",
		"T_THUOC" : "n",
		"T_MAU" : "n",
		"T_PTTT" : "n",
		"T_VTYT" : "n",
		"T_DVKT_TYLE" : "n",
		"T_THUOC_TYLE" : "n",
		"T_VTYT_TYLE" : "n",
		"T_KHAM" : "n",
		"T_GIUONG" : "n",
		"T_VCHUYEN" : "n",
		"T_BNTT" : "n",
		"T_BHTT" : "n",
		"T_NGOAIDS" : "n",
		"MA_KHOA" : "s",
		"NAM_QT" : "n",
		"THANG_QT" : "n",
		"MA_KHUVUC" : "s",
		"MA_LOAIKCB" : "s", //L2PT-102997
		"MA_CSKCB" : "s",
		"T_NGUONKHAC" : "n"
	};
	// L2PT-25669 start
	var _DataType_Rpt7980_v2 = {
		"STT" : "n",
		"MA_BN" : "s",
		"HO_TEN" : "s",
		"NGAY_SINH" : "s",
		"GIOI_TINH" : "n",
		"DIA_CHI" : "s",
		"MA_THE" : "s",
		"MA_DKBD" : "s",
		"GT_THE_TU" : "s",
		"GT_THE_DEN" : "s",
		"MA_BENH" : "s",
		"MA_BENHKHAC" : "s",
		"MA_LYDO_VVIEN" : "s",
		"MA_NOI_CHUYEN" : "s",
		"NGAY_VAO" : "s",
		"NGAY_RA" : "s",
		"SO_NGAY_DTRI" : "n",
		"KET_QUA_DTRI" : "n",
		"TINH_TRANG_RV" : "n",
		"T_TONGCHI" : "n",
		"T_XN" : "n",
		"T_CDHA" : "n",
		"T_THUOC" : "n",
		"T_MAU" : "n",
		"T_PTTT" : "n",
		"T_VTYT" : "n",
		"T_DVKT_TYLE" : "n",
		"T_THUOC_TYLE" : "n",
		"T_VTYT_TYLE" : "n",
		"T_KHAM" : "n",
		"T_GIUONG" : "n",
		"T_VCHUYEN" : "n",
		"T_BNTT" : "n",
		"T_BHTT" : "n",
		"T_NGOAIDS" : "n",
		"MA_KHOA" : "s",
		"NAM_QT" : "n",
		"THANG_QT" : "n",
		"MA_KHUVUC" : "s",
		"MA_LOAIKCB" : "s", //L2PT-102997
		"MA_CSKCB" : "s",
		"noi_ttoan" : "s",
		"giam_dinh" : "s",
		"t_xuattoan" : "n",
		"lydo_xt" : "s",
		"t_datuyen" : "s",
		"t_vuottran" : "s"
	};
	// L2PT-25669 end
	var _DataType_Rpt7980_Ct = null;
	/*
		"STT" : "n",
		"MA_LK" : "s",
		"MA_BN" : "s",
		"MA_BA" : "s",
		"SO_VAO_VIEN" : "s",
		"NGAY_VAO_VIEN" : "s",
		"NGAY_VAO_KHOA" : "s",
		"NGAY_RA_VIEN" : "s",
		"NGAY_THANH_TOAN" : "s",
		"SO_NGAY_DTRI" : "n",
		"KET_QUA_DTRI" : "s",
		"MA_KQDT" : "n",
		"TINH_TRANG_RV" : "s",
		"MA_TTRV" : "n",
		"MA_THE" : "s",
		"MA_DKBD" : "s",
		"GT_THE_TU" : "s",
		"GT_THE_DEN" : "s",
		"MA_KHUVUC" : "s",
		"MIEN_CUNG_CT" : "s",
		"DOI_TUONG" : "s",
		"TUYEN" : "s",
		"HO_TEN" : "s",
		"NGAY_SINH" : "s",
		"GIOI_TINH" : "s",
		"DIA_CHI" : "s",
		"MA_BENH" : "s",
		"MA_BENHKHAC" : "s",
		"CHAN_DOAN" : "s",
		"CHAN_DOAN_KHAC" : "s",
		"MA_LYDO_VVIEN" : "n",
		"MA_NOI_CHUYEN" : "s",
		"MA_TAI_NAN" : "s",
		"NOI_CHUYEN_DI" : "s",
		"TAMUNG" : "s",
		"MA_KHOA_CHI_DINH" : "s",
		"MA_BAC_SY" : "s",
		"BAC_SY" : "s",
		"SO_PHIEU" : "s",
		"NGAY_CHI_DINH" : "s",
		"NGAY_THUC_HIEN" : "s",
		"NGUOI_THUC_HIEN" : "s",
		"NGAY_HOAN_THANH" : "s",
		"KHOA" : "s",
		"MA_NHOM" : "s",
		"TEN_NHOM" : "s",
		"MA_BENH_VIEN" : "s",
		"MA_BHYT" : "s",
		"TEN_BENH_VIEN" : "s",
		"TEN_BHYT" : "s",
		"DICH_VU" : "s",
		"HAM_LUONG" : "s",
		"DUONG_DUNG" : "s",
		"MA_DUONG_DUNG" : "s",
		"LIEU_DUNG" : "s",
		"SO_DANG_KY" : "s",
		"MATHAU" : "s",
		"TT_THAU" : "s",
		"PHAM_VI" : "s",
		"DON_VI_TINH" : "s",
		"SO_LUONG" : "n",
		"DON_GIA" : "n",
		"TYLE_TT" : "n",
		"THANH_TIEN" : "n",
		"MUC_HUONG" : "n",
		"T_BHTT" : "n",
		"T_BNCCT" : "n",
		"T_BNTT" : "n",
		"T_NGUONKHAC" : "n",
		"T_NGOAIDS" : "n",
		"T_TRANTT" : "n",
		"MA_GIUONG" : "s",
		"THANH_TIEN2" : "n",
		"MA_LOAI_KCB" : "s",
		"MA_KHOA" : "s",
		"MA_CSKCB" : "s",
		"MA_PTTT_QT" : "s",
		"CAN_NANG" : "s",
		"NGAY_THANH_TOAN2" : "s",
		"NAM_QT" : "n",
		"THANG_QT" : "n",
		"LOAI_VIEN_PHI" : "s"
	};
	*/
	// L2PT-25669 start
	if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HIEN_BAOCAO7980_V2') == '1') {
		$("#chkEXP_REPORT_79_V2_CHECK").show();
	}
	// L2PT-25669 end
	// L2PT-27413 start
	var _str_opt_7980_v2 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', 'VPI_COL_7980_V2');
	_str_opt_7980_v2 = FormUtil.unescape(_str_opt_7980_v2)
	try {
		var _opt_7980_v2 = JSON.parse(_str_opt_7980_v2);
		if (_opt_7980_v2 instanceof Object) {
			_DataType_Rpt7980_v2 = _opt_7980_v2;
		}
	} catch (e) {
		console.log(e);
	}
	// L2PT-27413 end
	var VPI_CH_COL_7980 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CH_COL_7980'); // L2PT-27413
	var _str_opt_7980 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_COL_7980');
	_str_opt_7980 = FormUtil.unescape(_str_opt_7980)
	var _opt_7980 = JSON.parse(_str_opt_7980);
	if (_opt_7980 instanceof Object) {
		// L2PT-27413 start
		if (VPI_CH_COL_7980 == '1') {
			_DataType_Rpt7980 = new Object();
		}
		// L2PT-27413 end
		for ( var idk in _opt_7980) {
			// L2PT-25669 start
			if (_opt_7980[idk] == "r" && _DataType_Rpt7980[idk]) {
				delete _DataType_Rpt7980[idk];
			} else {
				_DataType_Rpt7980[idk] = _opt_7980[idk];
			}
			// L2PT-25669 end
		}
	}
	var sql_par = [];
	var that = this;
	this.load = doLoad;
	var VPI_HIENTHI_DSGUIBH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HIENTHI_DSGUIBH'); // L2PT-12873 
	var vpi_show_gridHeader = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_GRIDHEADER_4210'); // L2PT-17933
	var fConfig = new Object(); // L2PT-43011
	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
		_tu_ngay = _sys_date + " 00:00:00";
		_den_ngay = _sys_date + " 23:59:59";
		$("#txtTU_NGAY").val(_tu_ngay);
		$("#txtDEN_NGAY").val(_den_ngay);
		//$("#"+_gridDS_HOSO)[0].toggleToolbar();
		initControl();
		doCheckedAll();
		bindEvent();
		//initDataXml();
	}
	function initControl() {
		// L2PT-43011 start 
		// lấy cấu hình 
		var str_ch = "VPI_TONGHOP_DULIEU_BH4210"; // L2PT-37472
		str_ch += ';VPI_XUATXML_OFFSET_TG'; // L2PT-43853
		str_ch += ';VPI_XMLDS_FILENAME'; // L2PT-51418
		str_ch += ';VPI_LOAI_XML';// L2PT-53360 L2PT-53592 
		str_ch += ';VPI_TIMKIEM_PHAMVI';// L2PT-98060
		str_ch += ';VPI_SONGAY_XUAT_DL';// L2PT-111325
		var arrFConfig = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		if (Array.isArray(arrFConfig) && arrFConfig.length > 0) {
			fConfig = arrFConfig[0];
		}
		if (fConfig.VPI_TONGHOP_DULIEU_BH4210 == "2") {
			$("#divLoaiHs").remove();
			$('#cboLoaiHSms').SumoSelect({
				search : true,
				searchText : 'Tìm kiếm',
				okCancelInMulti : true,
				selectAll : true
			});
			$('#cboLoaiHSms')[0].sumo.reload();
			// $('#cboLoaiHSms')[0].sumo.selectAll(); // L2PT-85755
		}
		// L2PT-54953 start
		// L2PT-85755 start 
		else if (fConfig.VPI_TONGHOP_DULIEU_BH4210 == "3") {
			$("#divLoaiHsMS").remove();
			$(".loai-hs-v1").hide();
			$(".loai-hs-v2").show();
		}
		// L2PT-85755 end 
		// L2PT-54953 end
		else {
			$("#divLoaiHsMS").remove();
		}
		// L2PT-43011 end
		// L2PT-21340 start
		// L2PT-19490 start
		/*
		var VPI_BH_CBO_NGAY = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_BH_CBO_NGAY');
		if (VPI_BH_CBO_NGAY == '0') {
			$("#cboLOCTHEO option[value=" + 4 + "]").hide();
			$("#cboLOCTHEO option[value=" + 2 + "]").hide();
			$("#cboLOCTHEO option[value=" + 3 + "]").hide();
			$("#cboLOCTHEO option[value=" + 5 + "]").hide();
		}
		*/
		// L2PT-19490 end
		configContextMenu('cboLOCTHEO', 'VPI_BH_CBO_NGAY', 'COMBOBOX');
		$("#cboLOCTHEO").val(1);
		// L2PT-21340 end
		// L2PT-12873 start
		if (VPI_HIENTHI_DSGUIBH == '1') {
			$("#cboLOCTHEO").val(0);
		}
		// L2PT-12873 end
		// L2PT-98060 start
		if (fConfig.VPI_TIMKIEM_PHAMVI == '1') {
			$("#divPHAMVI").show();
			sql_DataXls_XML = "NTU02D060.14";
		}
		// L2PT-98060 end
		// L2PT-17933 ttlinh start
		// L2PT-86385 chỉnh độ dài danh sách hồ sơ
		if (vpi_show_gridHeader == '0') {
			GridUtil.init(_gridDS_HOSO, "100%", "620", "Danh sách bệnh nhân", true, _gridDS_HOSO_Header, false, {
				rowNum : 100,
				rowList : [ 100, 200, 300, 500, 1000 ]
			});
		} else if (vpi_show_gridHeader == '1') {
			GridUtil.init(_gridDS_HOSO, "100%", "620", "Danh sách bệnh nhân", true, _gridDS_HOSO_Header1, false, {
				rowNum : 100,
				rowList : [ 100, 200, 300, 500, 1000 ]
			});
		}
		// L2PT-17933 end
		// L2PT-69647 start
		var result1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("THUTHEO_DOITUONG", '$');
		var str1 = '';
		var ds_doituongbnid = "";
		var doituongbnid;
		var tatca = false;
		if (result1.length > 0) {
			for (var j = 0; j < result1.length; j++) {
				doituongbnid = result1[j].DTBNID;
				if (doituongbnid == 100) {
					tatca = true;
				} else {
					ds_doituongbnid += doituongbnid + ",";
				}
				str1 = str1 + "<option value=" + result1[j].DTBNID + ">" + result1[j].TEN_DTBN + "</option>";
			}
			if (!tatca && ds_doituongbnid.length > 0) {
				ds_doituongbnid = ds_doituongbnid.slice(0, -1);
				str1 = str1 + "<option value=" + ds_doituongbnid + ">" + "-- Chọn --" + "</option>";
			}
		}
		$('#cboDTBNID').html(str1);
		// L2PT-69647 end
		ComboUtil.getComboTag("cboMaThe", 'VPI.DS.MATHEBH', [], "", {}, 'sql', '', '');
		// L2PT-88458 start
		$('#cboMaThe').SumoSelect({
			search : true,
			searchText : 'Tìm kiếm',
			okCancelInMulti : true,
			selectAll : true
		});
		$('#cboMaThe')[0].sumo.reload();
		$('#cboMaThe')[0].sumo.selectAll();
		// L2PT-88458 end
		ComboUtil.getComboTag("cboLoaiThe", 'VPI.LOAITHE', [], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', '');
		// L2PT-39437 start
		// load combobox Khoa KCB
		ComboUtil.getComboTag("cboKhoa", 'VPI.KHOA.KCB', [], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', '');
		// L2PT-39437 end
		ComboUtil.getComboTag("cboCSKCB", 'VPI.CSKCB', [], "", {}, 'sql', '', '');
		// L2PT-54953 L2PT-53360 L2PT-53592 start 
		if (typeof fConfig.VPI_LOAI_XML == "string") {
			var chLoaiXML = fConfig.VPI_LOAI_XML.split(";");
			var typeXMLDef = chLoaiXML[0];
			var hideXMLOpt = chLoaiXML[1] ? chLoaiXML[1] : 0;
			moveOptionByValue('cboLoaiXML', typeXMLDef, 0);
			if (hideXMLOpt == 1) {
				$('#cboLoaiXML').find('option').not(':first').hide();
			}
		}
		showXML130(); // L2PT-86385
		// L2PT-54953 L2PT-53360 L2PT-53592 end
		doSearch();
//		DlgUtil.buildPopup ("dlgPROCESS","dlgTIENTRINHGUI","Thông báo",500,50);
	}
	function doSearch(ds_ba) {
		// L2PT-131223 start
		if(!gioihan_thoigian("VPI_SONGAY_TIMKIEM", "txtTU_NGAY", "txtDEN_NGAY" )){
			return false;
		}
		// L2PT-131223 end
		$("#dLoading").removeClass("hidden");
		if (!ds_ba)
			ds_ba = "";
		_tu_ngay = $("#txtTU_NGAY").val();
		_den_ngay = $("#txtDEN_NGAY").val();
		// L2PT-43011 start
		if (fConfig.VPI_TONGHOP_DULIEU_BH4210 == "2") {
			_loai = $("#cboLoaiHSms").val().join();
		} else {
			_loai = $("#cboLoaiHS").val();
		}
		// L2PT-43011 end
		_tuyen = $("#cboTuyen").val();
		_mathe = $("#cboMaThe").val().join(); // L2PT-88458
		_loaithe = $("#cboLoaiThe").val();
		_loaiXML = $("#cboLoaiXML").val(); // duonghn start: cv 130
		_typeDate = $("#cboLOCTHEO").val();
		_cskcb = $("#cboCSKCB").val();
		arr_row = [];
		rows = [];
		//var _tu_ngay = moment($("#txtTU_NGAY").val().substring(0, 10)).format('DD/MM/YYYY');
		//var _den_ngay = moment($("#txtDEN_NGAY").val().substring(0, 10)).format('DD/MM/YYYY');
		console.log("---Tu_ngay: " + _tu_ngay + "---den_ngay: " + _den_ngay);
		if (stringToDateTime(_tu_ngay) <= stringToDateTime(_den_ngay)) {
			console.log("---Tu_ngay: " + _tu_ngay + "---den_ngay: " + _den_ngay);
			ma_lks = "-1";
			// duonghn start: cv 130
			var _sql_par = []
			var objTimKiem = new Object();
			FormUtil.setFormToObject('divSearch', '', objTimKiem);
			objTimKiem.LOAITIEPNHAN = _loai;
			objTimKiem.MATHE = _mathe; // L2PT-88458
			objTimKiem.DS_BA = ds_ba;
			objTimKiem.GUI_CONG = "-1";
			objTimKiem.GUI_BHXH = "-1";
			_sql_par.push({
				"name" : "[0]",
				"value" : JSON.stringify(objTimKiem)
			});
			// duonghn end: cv 130
			// duonghn: function mới
			GridUtil.loadGridBySqlPage(_gridDS_HOSO, 'NTU02D061.03.RG', _sql_par, function() {
				if (ds_ba) {
					$('#cb_' + _gridDS_HOSO).click();
				} else {
					var grid = $("#" + _gridDS_HOSO);
					var offset = 1;
					var ids = grid.getDataIDs();
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						var ma_lk_row = row.MA_LK;
						var id = -1;
						for (var j = 0; j < ids.length; j++) {
							var ma_lk = grid.jqGrid('getCell', ids[j], 'MA_LK');
							if (ma_lk == ma_lk_row) {
								id = ids[j];
							}
						}
						if (id >= 0) {
							grid.jqGrid('delRowData', id);
							grid.jqGrid('addRowData', id, row, 'first');
						} else {
							id = offset + ids.length;
							offset++;
							grid.jqGrid('addRowData', id, row, 'first');
						}
						grid.jqGrid('setSelection', id, false);
						GridUtil.markRow(_gridDS_HOSO, id);
					}
//					var ids = grid.getDataIDs();
//					var arr_row = "["+ma_lks+"]";
//					for (var i = 0; i < ids.length; i++) {
//						// chon row da danh dau 
//						var id = ids[i];
//						var ma_lk = grid.jqGrid('getCell', id, 'MA_LK');
//						if (arr_row.indexOf(ma_lk) != -1) {
//							grid.jqGrid('delRowData',id);
//							grid.jqGrid('addRowData',id, rows[i],'first');
//							grid.jqGrid('setSelection', id, false);
//							GridUtil.markRow(_gridDS_HOSO, id);
//						}
//						
//					}
				}
				tonghop_solieu(ds_ba);
			},'POST'); // L2PT-19490
			$("#dLoading").addClass("hidden");
		} else {
			$("#dLoading").addClass("hidden");
			DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ. Từ ngày phải nhỏ hơn hoặc bằng đến ngày!");
		}
	}
	function doCheckedAll() {
		var _arrExp1 = _lstExp.split(",");
		var _arrExp2 = _lstExp_CHECK.split(",");
		for (var i = 0; i < _arrExp1.length; i++) {
			$('#' + _arrExp1[i]).prop('checked', true);
		}
		for (var i = 0; i < _arrExp2.length; i++) {
			$('#' + _arrExp2[i]).prop('checked', true);
		}
	}
	function bindEvent() {
		$("#txtTU_NGAY").change(function() {
//			doSearch();
		});
		$("#txtDEN_NGAY").change(function() {
//			doSearch();
		});
		$("#cboLoaiHS").change(function() {
//			doSearch();
		});
		GridUtil.setGridParam(_gridDS_HOSO, {
			onSelectRow : function(id) {
				ma_lks = "";
				rows = [];
				var grid = $("#" + _gridDS_HOSO);
				var ids = grid.jqGrid('getGridParam', 'selarrrow');
				var row = grid.jqGrid('getRowData', id);
				if (ids.indexOf(id) != -1) {
					$("#" + _gridDS_HOSO).jqGrid('delRowData', id);
					$("#" + _gridDS_HOSO).jqGrid('addRowData', id, row, 'first');
					$("#" + _gridDS_HOSO).jqGrid('setSelection', id, false);
				} else if (ids.length > 0) {
					$("#" + _gridDS_HOSO).jqGrid('delRowData', id);
					$("#" + _gridDS_HOSO).jqGrid('addRowData', id, row, 'last'); // L2PT-47331
				}
				GridUtil.unmarkAll(_gridDS_HOSO);
				for (var i = 0; i < ids.length; i++) {
					var _id = ids[i];
					var _ma_lk = grid.jqGrid('getCell', _id, 'MA_LK');
					ma_lks += _ma_lk + ",";
					GridUtil.markRow(_gridDS_HOSO, _id);
					var _row = grid.jqGrid('getRowData', _id);
					rows.push(_row);
				}
				ma_lks = ma_lks == "" ? '-1' : ma_lks.slice(0, -1);
				tonghop_solieu();
			},
			onSelectAll : function(ids, status) {
				ma_lks = "";
				rows = [];
				var grid = $("#" + _gridDS_HOSO);
				var ids = grid.jqGrid('getGridParam', 'selarrrow');
				GridUtil.unmarkAll(_gridDS_HOSO);
				if (!status) {} else {
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var _ma_lk = grid.jqGrid('getCell', id, 'MA_LK');
						ma_lks += _ma_lk + ",";
						GridUtil.markRow(_gridDS_HOSO, id);
						var _row = grid.jqGrid('getRowData', id);
						rows.push(_row);
					}
				}
				ma_lks = ma_lks == "" ? '-1' : ma_lks.slice(0, -1);
				tonghop_solieu();
			},
			gridComplete : function(id) {
				var grid = $("#" + _gridDS_HOSO);
				var sl = grid.jqGrid('getGridParam', 'records');
				$("#lblSoLuong").text(sl);
				var ids = grid.getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = grid.jqGrid('getRowData', id);
					var _icon = '';
					//Set icon trang thai gui cong byt
					if (row.TT_GUI_CONG == 0) {
						_icon = '<center><img src="' + _imgPath[0] + '" width="15px"></center>';
					} else if (row.TT_GUI_CONG == 1) {
						_icon = '<center><img src="' + _imgPath[1] + '" width="15px"></center>';
					}
					// L2PT-75979 start
					else {
						_icon = '<center><img src="' + _imgPath[2] + '" width="15px"></center>';
					}
					// L2PT-75979 end
					// L2PT-75979 doi ten cot ICON
					grid.jqGrid('setCell', id, 'TT_GUI_CONG', _icon); // L2PT-10430
					//Set icon trang thai gui bhxh
					var _icon_bhxh = '';
					if (row.TT_GUI_BHXH == 0) {
						_icon_bhxh = '<center><img src="' + _imgPath[0] + '" width="15px"></center>';
					} else if (row.TT_GUI_BHXH == 1) {
						_icon_bhxh = '<center><img src="' + _imgPath[1] + '" width="15px"></center>';
					}
					// L2PT-75979 start
					else {
						_icon_bhxh = '<center><img src="' + _imgPath[2] + '" width="15px"></center>';
					}
					// L2PT-75979 end
					// L2PT-75979 doi ten cot ICON
					grid.jqGrid('setCell', id, 'TT_GUI_BHXH', _icon_bhxh); // L2PT-10430
				}
			}
		});
		$("#btnSearch").on("click", function(e) {
			doSearch();
		});
		$("#btnExportXLS").on("click", function(e) {
		//setParamForPrint("xls");
		});
		// L2PT-2271 start
		// start xls
		$('#chkEXP_REPORT_19').on("click", function(event) {
			initDataXls("chkEXP_REPORT_19", "xls", "NTU02D060.01", "REPORT_19", _DataType_Rpt19, event);
		});
		$('#chkEXP_REPORT_20').on("click", function(event) {
			initDataXls("chkEXP_REPORT_20", "xls", "NTU02D060.02", "REPORT_20", _DataType_Rpt20, event);
		});
		$('#chkEXP_REPORT_21').on("click", function(event) {
			initDataXls("chkEXP_REPORT_21", "xls", "NTU02D060.03", "REPORT_21", _DataType_Rpt21, event);
		});
		$('#chkEXP_REPORT_79').on("click", function(event) {
			var _fileName = _loai == -1 ? "REPORT_79_80" : (_loai == 0 ? "REPORT_80" : "REPORT_79");
			initDataXls("chkEXP_REPORT_79", "xls", "NTU02D060.04", _fileName, _DataType_Rpt7980, event);
		});
		$('#chkEXP_REPORT_80').on("click", function(event) {
			initDataXls("chkEXP_REPORT_80", "xls", "NTU02D060.04", "DATUYEN", _DataType_Rpt7980, event);
		});
		// end xls
		// start xlsx
		// L2PT-54953 start
		$('#chkEXP_REPORT_19_CHECK').on("click", function(event) {
			//initDataXls("chkEXP_REPORT_19_CHECK", "xlsx", "NTU02D060.01", "REPORT_19", _DataType_Rpt19, event);
			initDataXls_XML("chkEXP_REPORT_19_CHECK", "xlsx", sql_DataXls_XML, "REPORT_19", _DataType_Rpt19, event);
		});
		$('#chkEXP_REPORT_20_CHECK').on("click", function(event) {
			//initDataXls("chkEXP_REPORT_20_CHECK", "xlsx", "NTU02D060.02", "REPORT_20", _DataType_Rpt20, event);
			initDataXls_XML("chkEXP_REPORT_20_CHECK", "xlsx", sql_DataXls_XML, "REPORT_20", _DataType_Rpt20, event);
		});
		$('#chkEXP_REPORT_21_CHECK').on("click", function(event) {
			//initDataXls("chkEXP_REPORT_21_CHECK", "xlsx", "NTU02D060.03", "REPORT_21", _DataType_Rpt21, event);
			initDataXls_XML("chkEXP_REPORT_21_CHECK", "xlsx", sql_DataXls_XML, "REPORT_21", _DataType_Rpt21, event);
		});
		$('#chkEXP_REPORT_79_CHECK').on("click", function(event) {
			var _fileName = "REPORT_" + layLoaiHS(_loai);
			//initDataXls("chkEXP_REPORT_79_CHECK", "xlsx", "NTU02D060.04", _fileName, _DataType_Rpt7980, event);
			initDataXls_XML("chkEXP_REPORT_79_CHECK", "xlsx", sql_DataXls_XML, _fileName, _DataType_Rpt7980, event);
		});
		// L2PT-25669 start
		$('#chkEXP_REPORT_79_V2_CHECK').on("click", function(event) {
			var _fileName = "REPORT_V2_" + layLoaiHS(_loai);
			//initDataXls("chkEXP_REPORT_79_V2_CHECK", "xlsx", "NTU02D060.04", _fileName, _DataType_Rpt7980_v2, event);
			initDataXls_XML("chkEXP_REPORT_79_V2_CHECK", "xlsx", sql_DataXls_XML, _fileName, _DataType_Rpt7980_v2, event);
		});
		// L2PT-25669 end
		$('#chkEXP_REPORT_80_CHECK').on("click", function(event) {
			var _fileName = "REPORT_DATUYEN_" + layLoaiHS(_loai);
			//initDataXls("chkEXP_REPORT_80_CHECK", "xlsx", "NTU02D060.04", "DATUYEN", _DataType_Rpt7980, event);
			initDataXls_XML("chkEXP_REPORT_80_CHECK", "xlsx", sql_DataXls_XML, _fileName, _DataType_Rpt7980, event);
		});
		$('#chkEXP_REPORT_DET_CHECK').on("click", function(event) {
			var _fileName = "BAOCAO_DICHVU_" + layLoaiHS(_loai);
			//initDataXls("chkEXP_REPORT_DET_CHECK", "xlsx", "NTU02D060.08", _fileName, _DataType_Rpt7980_Ct, event);
			initDataXls_XML("chkEXP_REPORT_DET_CHECK", "xlsx", sql_DataXls_XML, _fileName, _DataType_Rpt7980_Ct, event);
		});
		$('#chkEXP_REPORT_DV_CHECK').on("click", function(event) {
			var _fileName = "BAOCAO_CHITIET_" + layLoaiHS(_loai);
			//initDataXls("chkEXP_REPORT_DV_CHECK", "xlsx", "NTU02D060.09", _fileName, "", event);
			initDataXls_XML("chkEXP_REPORT_DV_CHECK", "xlsx", sql_DataXls_XML, _fileName, "", event);
		});
		// L2PT-54953 end
		// L2PT-86385 start
		// L2PT-37237 start
		/*$('#btnXML1').on("click", function(event) {
			initDataXls_XML("btnXML1", "xlsx", "NTU02D060.13", 'XML1', "", event);
		});
		$('#btnXML2').on("click", function(event) {
			initDataXls_XML("btnXML2", "xlsx", "NTU02D060.13", 'XML2', "", event);
		});
		$('#btnXML3').on("click", function(event) {
			initDataXls_XML("btnXML3", "xlsx", "NTU02D060.13", 'XML3', "", event);
		});
		$('#btnXML4').on("click", function(event) {
			initDataXls_XML("btnXML4", "xlsx", "NTU02D060.13", 'XML4', "", event);
		});
		$('#btnXML5').on("click", function(event) {
			initDataXls_XML("btnXML5", "xlsx", "NTU02D060.13", 'XML5', "", event);
		});*/
		// L2PT-37237 end
		$("#cboLoaiXML").change(function() {
			showXML130();
		});
		for (var i = 1; i <= 15; i++) {
			$('#btnXML' + i).on("click", function(event) {
				initDataXls_XML(this.id, "xlsx", sql_DataXls_XML, this.id.substring(3), "", event);
			})
		}
		// L2PT-86385 end
		// L2PT-53905 start
		$('#btnXML').on("click", function(event) {
			var loaiXML = $("#cboLoaiXML").val();
			var arrXML = [];
			for (var i = 1; i <= 5; i++) {
				arrXML.push('XML' + i);
			}
			if (loaiXML == '130') {
				for (var i = 6; i <= 15; i++) {
					arrXML.push('XML' + i);
				}
			}
			initDataXls_XML("btnXML", "xlsx", sql_DataXls_XML, arrXML, "", event);
		});
		// L2PT-53905 end
		// end xlsx
		// L2PT-2271 end
		$('#chkEXP_REPORT_XML_BHYT').on("click", function(event) {
			initDataXml("1", "chkEXP_REPORT_XML_BHYT", event);
		});
		$('#chkEXP_REPORT_XML_BHYT_CHECK').on("click", function(event) {
			initDataXml("0", "chkEXP_REPORT_XML_BHYT_CHECK", event);
		});
		$('#chkXML_DS_BN').on("click", function(event) {
			init_XML_patients("XML.4210", 1, "chkXML_DS_BN", event);
		});
		$('#chkXML_DS_BN_KMH').on("click", function(event) {
			init_XML_patients("XML.4210", 0, "chkXML_DS_BN_KMH", event);
		});
		$('#chkXML_4210').on("click", function(event) {
			init_XML_patients("XML.4210", "chkXML_4210", event);
		});
		$('#chkXML_4210_CHECK').on("click", function(event) {
			init_XML_patients("XML.4210.KMH", "chkXML_4210_CHECK", event);
		});
		// L2PT-32303 start
		$('#chkEXP_XML_2076').on("click", function(event) {
			initDataXml2076("1", "chkEXP_XML_2076", event);
		});
		// L2PT-32303 end
	}
	// L2PT-86385 start
	function showXML130() {
		// L2PT-85755 start: show theo loại XML
		if ($('#cboLoaiHSms').length > 0) {
			$('#cboLoaiHSms')[0].sumo.unSelectAll();
		}
		var loaiXML = $("#cboLoaiXML").val();
		if (loaiXML == '130') {
			$("#divXUAT_XML130").show();
			$(".loai-hs-4210").hide();
			$(".loai-hs-130").show();
		} else {
			$("#divXUAT_XML130").hide();
			$(".loai-hs-4210").show();
			$(".loai-hs-130").hide();
		}
		if ($('#cboLoaiHSms').length > 0) {
			$('#cboLoaiHSms')[0].sumo.selectAll();
		}
		// L2PT-85755 end
	}
	// L2PT-86385 end
	// L2PT-47331 start
	// L2PT-54953 start
	function layLoaiHS(valLoaiHs) {
		var _fileName = "79_80";
		if (fConfig.VPI_TONGHOP_DULIEU_BH4210 == '3' && valLoaiHs != -1) {
			_fileName = valLoaiHs == 2 ? "80" : "79";
		} else if (fConfig.VPI_TONGHOP_DULIEU_BH4210 == '2' && valLoaiHs != -1) {
			_fileName = "79_80";
		} else if (valLoaiHs != -1) { // L2PT-62145
			_fileName = valLoaiHs == 0 ? "80" : "79";
		}
		return _fileName;
	}
	// L2PT-54953 end
	function tonghop_solieu(ds_ba) {
		if (!ds_ba)
			ds_ba = "";
		var objTimKiem = new Object();
		FormUtil.setFormToObject('divSearch', '', objTimKiem);
		objTimKiem.LOAITIEPNHAN = _loai; // L2PT-43011
		objTimKiem.MATHE = _mathe; // L2PT-88458
		objTimKiem.DTBNID = $("#cboDTBNID").val();
		objTimKiem.PHAMVI = $("#cboPHAMVI").val();//L2PT-98060
		var strInp = JSON.stringify(objTimKiem);
		var sql_tonghop = "TONGHOP.BH.RG"; //L2PT-98060
		if (fConfig.VPI_TIMKIEM_PHAMVI == '1') {
			sql_tonghop = "TONGHOP.BH.RG.01";
		}
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(sql_tonghop, strInp + "$" + ma_lks + "$" + ds_ba);
		if (data_ar != null && data_ar.length > 0) {
			_row = data_ar[0];
			FormUtil.setObjectToForm("", "", _row);
		}
	}
	// L2PT-47331 end
	// L2PT-32303 start 
	initDataXml2076 = function(_Encode, _idDownload, event) {
		// L2PT-111325 start
		if (!chk_thoigian(fConfig.VPI_SONGAY_XUAT_DL)) {
			return
		}; 
		// L2PT-111325 end
		$("#dLoading").removeClass("hidden");
		if (stringToDateTime(_tu_ngay) <= stringToDateTime(_den_ngay)) {
//			console.log("_tu_ngay: "+ _tu_ngay +"--_den_ngay: "+_den_ngay);
			var func_name = "VPI.HS.CHUNGTU2076";
			//var _param = ma_lks;
			var grid = $("#" + _gridDS_HOSO);
			var ids = grid.jqGrid('getGridParam', 'selarrrow');
			var xml2076s = [];
			for (var i = 0; i < ids.length; i++) {
				var _ma_lk = grid.jqGrid('getCell', ids[i], 'MA_LK');
				var _mode = 1;
				var objInp = new Object();
				objInp.MA_LK = _ma_lk + "";
				objInp.MODE = _mode + "";
				var data = jsonrpc.AjaxJson.ajaxCALL_SP_O(func_name, JSON.stringify(objInp));
				if (data && data.length > 0) {
					xml2076s.push(data[0]);
				}
			}
			if (xml2076s.length > 0) {
				_date_ext = moment().format('YYYYMMDD');
				var zip = new JSZip();
				for (var k = 0; k < xml2076s.length; k++) {
					var _fileName = "XML2076" + _date_ext + "_" + xml2076s[k].MA_LK + "_" + ".xml";
					zip.file(_fileName, xml2076s[k].XML2076);
				}
				zip.generateAsync({
					type : "blob"
				}).then(function(blob) {
					saveAs(blob, "CHUNGTU2076" + _date_ext + ".zip");
				});
			} else {
				DlgUtil.showMsg("Không có hồ sơ theo VB 2076 !");
			}
			$("#dLoading").addClass("hidden");;
		} else {
			$("#dLoading").addClass("hidden");
			DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ. Từ ngày phải nhỏ hơn hoặc bằng đến ngày!");
		}
	};
	// L2PT-32303 end 
	initDataXml = function(_Encode, _idDownload, event) {
		// L2PT-111325 start
		if (!chk_thoigian(fConfig.VPI_SONGAY_XUAT_DL)) {
			return
		}; 
		// L2PT-111325 end
		// L2PT-123534 start
		var tuNgayDate = stringToDateTime(_tu_ngay);
		var denNgayDate = stringToDateTime(_den_ngay);
		// L2PT-123534
		if (tuNgayDate <= denNgayDate) { // L2PT-123534
			$("#dLoading").removeClass("hidden");
//			console.log("_tu_ngay: "+ _tu_ngay +"--_den_ngay: "+_den_ngay);
			// L2PT-4245 start
			var func_name = "";
			var _param = "";
			// L2PT-47331 start
			/*
			var VPI_KIEU_XML = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_KIEU_XML');
			if (VPI_KIEU_XML == '1' || _loaiXML == "130") { // 230417
			*/
			// L2PT-47331 end
			func_name = "XUAT.XML";
			var objXuatXML = new Object();
			objXuatXML.LOCTHEO = _typeDate + "";
			objXuatXML.TU_NGAY = _tu_ngay
			objXuatXML.DEN_NGAY = _den_ngay
			objXuatXML.LoaiHS = _loai + "";
			objXuatXML.Tuyen = _tuyen + "";
			objXuatXML.MaThe = _mathe
			objXuatXML.LoaiThe = _loaithe + "";
			objXuatXML.MAHOA = _Encode + "";
			objXuatXML.DS_MALK = ma_lks;
			objXuatXML.HCODE = _cskcb + "";
			objXuatXML.MODE = "1"
			objXuatXML.DTBN = $("#cboDTBNID").val(); // L2PT-69647
			objXuatXML.LOAIXML = _loaiXML; // duonghn start: cv 130
			// L2PT-123534 start
			var offsetInDays = parseInt(fConfig.VPI_XUATXML_OFFSET_TG);
			var count_part = 0;
			if (ma_lks == '-1' && offsetInDays != 0) {
				// L2PT-43853 st
				var differenceInDays = (denNgayDate - tuNgayDate) / (1000 * 60 * 60 * 24);
				for (idx = 0; idx < differenceInDays; idx = idx + offsetInDays) {
					var tungayTmp = new Date(tuNgayDate);
					tungayTmp.setDate(tuNgayDate.getDate() + idx);
					var denngayTmp = new Date(tuNgayDate);
					denngayTmp.setDate(tuNgayDate.getDate() + idx + offsetInDays);
					denngayTmp.setSeconds(denngayTmp.getSeconds() - 1);
					if (denNgayDate < denngayTmp) {
						denngayTmp = denNgayDate;
					}
					objXuatXML.TU_NGAY = moment(tungayTmp).format('DD/MM/YYYY HH:mm:ss');
					objXuatXML.DEN_NGAY = moment(denngayTmp).format('DD/MM/YYYY HH:mm:ss');
					_param = JSON.stringify(objXuatXML);
					count_part ++;
					var part = "Part_" + count_part;
					getXmlFile(func_name, _param, _idDownload, objXuatXML.TU_NGAY, objXuatXML.DEN_NGAY, part)
				}
			} else {
				_param = JSON.stringify(objXuatXML);
				getXmlFile(func_name, _param, _idDownload, _tu_ngay, _den_ngay, "")
			}
			// L2PT-123534 end
			$("#dLoading").addClass("hidden");
		
			
		} else {
			DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ. Từ ngày phải nhỏ hơn hoặc bằng đến ngày!");
		}
	}
	function getXmlFile(func_name, _param, _idDownload, _tu_ngay, _den_ngay, part){
		var data = jsonrpc.AjaxJson.ajaxCALL_SP_X(func_name, _param);
		if (data && data != "" && data != "null") {
			var _fileName = moment(_tu_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss') + "-" + moment(_den_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss');
			if (_idDownload == "chkEXP_REPORT_XML_BHYT_CHECK")
				_fileName = "KMH_" + _fileName + ".xml";
			else
				_fileName = _fileName + ".xml";
			_fileName = part + "-" +_fileName;
			download_async(data, _fileName, "application/xml", _idDownload + "_LINK");
		} else {
			DlgUtil.showMsg("Không tìm thấy hồ sơ của bệnh nhân trong khoảng thời gian " + _tu_ngay + " - " + _den_ngay + " !");
		}
	}
	init_XML_patients = function(_func, _encode, _idDownload, event) {
		// L2PT-111325 start
		if (!chk_thoigian(fConfig.VPI_SONGAY_XUAT_DL)) {
			return
		}; 
		// L2PT-111325 end
		var grid = $("#" + _gridDS_HOSO);
		var ids = grid.jqGrid('getGridParam', 'selarrrow');
		if (1 == 0 && ma_lks == "-1") {
			DlgUtil.showMsg("Không có hồ sơ nào được chọn");
		} else {
//			DlgUtil.open("dlgPROCESS");
			$("#dLoading").removeClass("hidden");
			setTimeout(function() {
				// L2PT-4245 start
				var _param = "";
				// L2PT-47331 start
				/*
				var VPI_KIEU_XML = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_KIEU_XML');
				if (VPI_KIEU_XML == '1' || _loaiXML == "130") { // 230417
				*/
				// L2PT-47331 end
				_func = "XUAT.XML.DS";
				var objXuatXML = new Object();
				objXuatXML.LOCTHEO = _typeDate + "";
				objXuatXML.TU_NGAY = _tu_ngay
				objXuatXML.DEN_NGAY = _den_ngay
				objXuatXML.LoaiHS = _loai + "";
				objXuatXML.Tuyen = _tuyen + "";
				objXuatXML.MaThe = _mathe
				objXuatXML.LoaiThe = _loaithe + "";
				objXuatXML.MAHOA = _encode + "";
				objXuatXML.DS_MALK = ma_lks;
				objXuatXML.HCODE = _cskcb + "";
				objXuatXML.MODE = "1";
				objXuatXML.LOAIXML = _loaiXML + ""; // duonghn start: cv 130
				objXuatXML.DTBN = $("#cboDTBNID").val(); // L2PT-69647
				_param = JSON.stringify(objXuatXML);
				// L2PT-47331 start
				/*	
				} else {
					_param = _typeDate + '$' + _tu_ngay + '$' + _den_ngay + '$' + _encode + '$' + ma_lks + '$' + _cskcb;
				}
				*/
				// L2PT-4245 end
				// L2PT-47331 end
				var ds_ma_lk = "";
				var grid = $("#" + _gridDS_HOSO);
				var ids = grid.jqGrid('getGridParam', 'selarrrow');
				var xmls = [];
				var VPI_FPD = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'VPI_FPD');
				if (VPI_FPD == 0)
					VPI_FPD = 100;
				if (ids.length > 0) {
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var _ma_lk = grid.jqGrid('getCell', id, 'MA_LK');
						ds_ma_lk += _ma_lk + ",";
						if ((i + 1) % VPI_FPD == 0 || i + 1 == ids.length) {
							if (ds_ma_lk == "") {
								break;
							} else {
								ds_ma_lk = ds_ma_lk.slice(0, -1)
							};
							// L2PT-47331 start
							/*
							// L2PT-4245 start
							if (VPI_KIEU_XML == '0' && _loaiXML != "130") { // 230417
								_param = _typeDate + '$' + _tu_ngay + '$' + _den_ngay + '$' + _encode + '$' + ds_ma_lk + '$' + _cskcb;
							}
							// L2PT-4245 end
							*/
							// L2PT-47331 end
							objXuatXML.DS_MALK = ds_ma_lk;
							_param = JSON.stringify(objXuatXML);
							var data = jsonrpc.AjaxJson.ajaxCALL_SP_O(_func, _param); // L2PT-4245 start
							if (!data || data.length == 0) {
								//console.log(_func + ";" + _typeDate + '$' + _tu_ngay + '$' + _den_ngay + '$' + _encode + '$' + ds_ma_lk + '$' + _cskcb);
								xmls = [];
								break;
							}
							// L2PT-47331 start
							xmls = xmls.concat(data);
							ds_ma_lk = "";
							// L2PT-47331 end
						}
					}
				} else {
					ds_ma_lk = '-1';
					objXuatXML.DS_MALK = ds_ma_lk;
					_param = JSON.stringify(objXuatXML);
					xmls = jsonrpc.AjaxJson.ajaxCALL_SP_O(_func, _param); // L2PT-4245 start
				}
				if (xmls && xmls.length > 0) { // L2PT-43853
					_date_ext = moment().format('YYYYMMDD');
					var zip = new JSZip();
					for (var k = 0; k < xmls.length; k++) {
						var _fileName = _date_ext + "_" + xmls[k].MA_LK + "_" + xmls[k].MA_THE + ".xml";
						// L2PT-51418 start
						if (fConfig.VPI_XMLDS_FILENAME == '1') {
							_fileName = _cskcb + "_" + xmls[k].NGAY_RA + "_" + xmls[k].MA_BN + ".xml";
						}
						// L2PT-51418 end
						zip.file(_fileName, xmls[k].XMLBN);
					}
					zip.generateAsync({
						type : "blob"
					}).then(function(blob) {
						saveAs(blob, (_func == "BH01.XML01" ? "XML_BHXH_MH_" : "XML_BHXH_") + _date_ext + ".zip");
					});
				} else {
					DlgUtil.showMsg("Không tìm thấy hồ sơ của bệnh nhân!");
				}
//				DlgUtil.close("dlgPROCESS");
				$("#dLoading").addClass("hidden");
			}, -1);
		}
	};
	initDataXls = function(_rptExport, _fileType, _sql_code, _filePrefix, _reportType, event) {
		// L2PT-111325 start
		if (!chk_thoigian(fConfig.VPI_SONGAY_XUAT_DL)) {
			return
		}; 
		// L2PT-111325 end
		$("#dLoading").removeClass("hidden");
		var _param = "";
		var _fileName = moment(_tu_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss') + "-" + moment(_den_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss');
		var _appFileType = "";
		var _daTuyen = false;
		var _loaiKcb = false;
		if (_rptExport == 'chkEXP_REPORT_80_CHECK') {
			_daTuyen = true;
			_loaiKcb = true;
		}
		_param = _typeDate + "$" + _tu_ngay + "$" + _den_ngay + "$" + _loai + "$" + _tuyen + "$" + _mathe + "$" + _loaithe + "$" + _cskcb + "$" + ma_lks; // L2PT-50409
		if (_fileType == 'xls') {
			_appFileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
		} else if (_fileType == 'xlsx') {
			_appFileType = "application/vnd.ms-excel";
		}
		if (stringToDateTime(_tu_ngay) <= stringToDateTime(_den_ngay)) {
			// L2PT-22872 start
			var dateExport = _tu_ngay.split(" ")[0].split("/");
			var _fileName_patt = _hos_code + "_" + dateExport[2] + dateExport[1] + "_GUIBHXH";
			switch (_filePrefix) {
				case 'REPORT_19':
					_fileName = "19_" + _fileName_patt;
				break;
				case 'REPORT_20':
					_fileName = "20_" + _fileName_patt;
				break;
				case 'REPORT_21':
					_fileName = "21_" + _fileName_patt;
				break;
				case 'REPORT_79_80':
					_fileName = "7980a_" + _fileName_patt;
				break;
				default:
					_fileName = _filePrefix + "_" + _fileName;
				break;
			}
			_fileName += "." + _fileType;
			// L2PT-22872 end
			var dt_Rpt = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_code, _param, []);
			if (dt_Rpt != null && dt_Rpt.length > 0) {
				$("#dLoading").addClass("hidden");
				if (_fileType == 'xls') {
					download(jsonToSsXml(dt_Rpt, _reportType), _fileName, _appFileType, _rptExport);
				} else if (_fileType == 'xlsx') {
					exportXLSX(dt_Rpt, _reportType, _filePrefix, _fileName, _daTuyen, _loaiKcb);
				}
			} else {
				$("#dLoading").addClass("hidden");
				event.preventDefault();
				DlgUtil.showMsg("Không tìm thấy hồ sơ nào để tạo file Excel!");
			}
		} else {
			$("#dLoading").addClass("hidden");
			event.preventDefault();
			DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ. Từ ngày phải nhỏ hơn hoặc bằng đến ngày!");
		}
	};
	// L2PT-37237 start
	// L2PT-53905 start: cho phép xuất nhiều loại xml ra các sheet trong 1 file excel
	initDataXls_XML = function(_rptExport, _fileType, _sql_code, _filePrefixs, _reportType, event) { //L2PT-98060
		// L2PT-126900
		/*if (fConfig.VPI_TIMKIEM_PHAMVI == '1' &&
				(_rptExport == 'chkEXP_REPORT_19_CHECK' || _rptExport == 'chkEXP_REPORT_20_CHECK' || _rptExport == 'chkEXP_REPORT_21_CHECK' || _rptExport == 'chkEXP_REPORT_79_CHECK' ||
						_rptExport == 'chkEXP_REPORT_79_V2_CHECK' || _rptExport == 'chkEXP_REPORT_80_CHECK' || _rptExport == 'chkEXP_REPORT_DET_CHECK' || _rptExport == 'chkEXP_REPORT_DV_CHECK')) {
			if (!chk_thoigian())
				return;
		}
		// L2PT-111325 start
		else {
			if (!chk_thoigian(fConfig.VPI_SONGAY_XUAT_DL)) {
				return
			};
		}*/
		// L2PT-111325 end
		if (!chk_thoigian(fConfig.VPI_SONGAY_XUAT_DL)) {
			return
		};
		// L2PT-126900 end
		$("#dLoading").removeClass("hidden");
		var _param = "";
		var _fileName = moment(_tu_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss') + "-" + moment(_den_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss');
		var _appFileType = "";
		var _daTuyen = false;
		var _loaiKcb = false;
		// L2PT-62145 start
		if (_rptExport == 'chkEXP_REPORT_80_CHECK') {
			_daTuyen = true;
			_loaiKcb = true;
		}
		// L2PT-62145 end
		var objTimKiem = new Object();
		FormUtil.setFormToObject('divSearch', '', objTimKiem);
		objTimKiem.LOAITIEPNHAN = _loai; // L2PT-43011
		objTimKiem.MATHE = _mathe; // L2PT-88458
		objTimKiem.CSKCB = _cskcb + ""; // L2PT-51273
		objTimKiem.PHAMVI = $("#cboPHAMVI").val(); // L2PT-98060
		if (typeof _filePrefixs == 'string') {
			_filePrefixs = [ _filePrefixs ];
		}
		var arr_Dt_Rpt = [];
		_filePrefixs.forEach(function(_filePrefix) {
			objTimKiem.THEXML = _filePrefix; // L2PT-51273
			if (_fileType == 'xls') {
				_appFileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
			} else if (_fileType == 'xlsx') {
				_appFileType = "application/vnd.ms-excel";
			}
			// L2PT-43853 start
			var tuNgayDate = stringToDateTime(_tu_ngay);
			var denNgayDate = stringToDateTime(_den_ngay);
			if (tuNgayDate <= denNgayDate) {
				var dt_Rpt = [];
				var offsetInDays = parseInt(fConfig.VPI_XUATXML_OFFSET_TG);
				if (ma_lks == '-1' && offsetInDays != 0) {
					// L2PT-43853 st
					var differenceInDays = (denNgayDate - tuNgayDate) / (1000 * 60 * 60 * 24);
					for (idx = 0; idx < differenceInDays; idx = idx + offsetInDays) {
						var tungayTmp = new Date(tuNgayDate);
						tungayTmp.setDate(tuNgayDate.getDate() + idx);
						var denngayTmp = new Date(tuNgayDate);
						denngayTmp.setDate(tuNgayDate.getDate() + idx + offsetInDays);
						denngayTmp.setSeconds(denngayTmp.getSeconds() - 1);
						if (denNgayDate < denngayTmp) {
							denngayTmp = denNgayDate;
						}
						objTimKiem.TU_NGAY = moment(tungayTmp).format('DD/MM/YYYY HH:mm:ss');
						objTimKiem.DEN_NGAY = moment(denngayTmp).format('DD/MM/YYYY HH:mm:ss');
						_param = JSON.stringify(objTimKiem) + "$" + ma_lks;
						var dataPart = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_code, _param, []);
						if (dataPart != null && dataPart.length > 0) {
							dt_Rpt = dt_Rpt.concat(dataPart);
						}
					}
				} else {
					_param = JSON.stringify(objTimKiem) + "$" + ma_lks;
					dt_Rpt = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_code, _param, []);
				}
				// L2PT-43853 end
				if (dt_Rpt != null && dt_Rpt.length > 0) {
					$("#dLoading").addClass("hidden");
					if (_fileType == 'xls') {
						download(jsonToSsXml(dt_Rpt, _reportType), _fileName, _appFileType, _rptExport);
					} else if (_fileType == 'xlsx') {
						arr_Dt_Rpt.push({
							data : dt_Rpt,
							sheetName : _filePrefix
						})
					}
				}
			} else {
				$("#dLoading").addClass("hidden");
				event.preventDefault();
				DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ. Từ ngày phải nhỏ hơn hoặc bằng đến ngày!");
			}
		});
		if (arr_Dt_Rpt.length > 0) {
			// L2PT-67156 start
			var dateExport = _tu_ngay.split(" ")[0].split("/");
			var _fileName_patt = _hos_code + "_" + dateExport[2] + dateExport[1] + "_GUIBHXH";
			if (_filePrefixs.length == 1) {
				switch (_filePrefixs[0]) {
					case 'REPORT_19':
						_fileName = "19_" + _fileName_patt;
					break;
					case 'REPORT_20':
						_fileName = "20_" + _fileName_patt;
					break;
					case 'REPORT_21':
						_fileName = "21_" + _fileName_patt;
					break;
					case 'REPORT_79_80':
						_fileName = "7980a_" + _fileName_patt;
					break;
					default:
						_fileName = _filePrefixs[0] + "_" + _fileName;
					break;
				}
			} else {
				_fileName = "XML" + "_" + _fileName;
			}
			_fileName += "." + _fileType;
			// L2PT-67156 end
			exportXLSX(arr_Dt_Rpt, _reportType, _filePrefixs[0], _fileName, _daTuyen, _loaiKcb);
		} else {
			$("#dLoading").addClass("hidden");
			event.preventDefault();
			DlgUtil.showMsg("Không tìm thấy hồ sơ nào để tạo file Excel!");
		}
	};
	// L2PT-53905 end
	// L2PT-37237 end
	// L2PT-111325 start
	chk_thoigian = function(soNgay) {
		/*if ((parseInt(moment().format('HH')) > 6 && parseInt(moment().format('HH')) < 11) || (parseInt(moment().format('HH')) > 12 && parseInt(moment().format('HH')) < 16)) {
			DlgUtil.showMsg("Hệ thống chỉ cho phép xuất báo cáo khoảng thời gian nhất định (0-7h, 11-13h và sau 16h)!");
			return false;
		}*/
		var msg = "";
		if (!soNgay || soNgay == 0) {
			soNgay = 62;
			msg = "Hệ thống chỉ cho phép xuất báo cáo trong vòng tối đa 2 tháng!"
		} else {
			msg = "Hệ thống chỉ cho phép xuất báo cáo trong vòng tối đa " + soNgay + " ngày!"
		}
		var tuNgayDate = $("#txtTU_NGAY").val();
		var denNgayDate = $("#txtDEN_NGAY").val();
		var thoigian = (stringToDateTime(denNgayDate) - stringToDateTime(tuNgayDate)) / (1000 * 60 * 60 * 24);
		if (thoigian > soNgay) {
			DlgUtil.showMsg(msg);
			return false;
		}
		return true;
	}
	// L2PT-111325 end
	emitXmlHeader = function(_DataType) {
		var headerRow = '<ss:Row>\n';
		for ( var colName in _DataType) {
			headerRow += '  <ss:Cell>\n';
			headerRow += '    <ss:Data ss:Type="String">';
			headerRow += colName + '</ss:Data>\n';
			headerRow += '  </ss:Cell>\n';
		}
		headerRow += '</ss:Row>\n';
		return '<?xml version="1.0"?>\n' + '<ss:Workbook xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">\n' + '<ss:Worksheet ss:Name="Sheet1">\n' + '<ss:Table>\n\n' + headerRow;
	};
	emitXmlFooter = function() {
		return '\n</ss:Table>\n' + '</ss:Worksheet>\n' + '</ss:Workbook>\n';
	};
//	jsonToSsXml = function (_jsonObject, _DataType) {
//	    var row;
//	    var col;
//	    var xml;
//	    var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
//	    
//	    xml = emitXmlHeader(_DataType);
//
//	    for (row = 0; row < data.length; row++) {
//	        xml += '<ss:Row>\n';
//	      
//	        for (var col in data[row]) {
//	            xml += '  <ss:Cell>\n';
//	            xml += '    <ss:Data ss:Type="' + _DataType[col]  + '">';
//	            xml += data[row][col] + '</ss:Data>\n';
//	            xml += '  </ss:Cell>\n';
//	        }
//
//	        xml += '</ss:Row>\n';
//	    }
//	    
//	    xml += emitXmlFooter();
//	    return xml;  
//	};
	jsonToSsXml = function(_jsonObject, _DataType) {
		var row;
		var col;
		var xml;
		var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
		xml = emitXmlHeader(_DataType);
		var col_arr = Object.keys(_DataType);
		for (row = 0; row < data.length; row++) {
			xml += '<ss:Row>\n';
			for (var i = 0; i < col_arr.length; i++) {
				col = col_arr[i];
				xml += '  <ss:Cell>\n';
				xml += '    <ss:Data ss:Type="' + dataType(_DataType[col]) + '">';
				xml += data[row][col] + '</ss:Data>\n';
				xml += '  </ss:Cell>\n';
			}
			xml += '</ss:Row>\n';
		}
		xml += emitXmlFooter();
		return xml;
	};
	dataType = function(type) {
		if (type == 'n')
			return "Number";
		else if (type == 's')
			return "String";
		else
			return "";
	};
	// L2PT-45698: đổi tên func
	objectToXLSXExt = function(_jsonObject, _DataType, _daTuyen, _loaiKcb) {
		var _type = [ 'A. BỆNH NHÂN NỘI TỈNH KHÁM CHỮA BỆNH BAN ĐẦU', 'B. BỆNH NHÂN NỘI TỈNH ĐẾN', 'C. BỆNH NHÂN NGOẠI TỈNH ĐẾN' ];
		var _type2 = [ '0. KHÔNG XÁC ĐỊNH', '1. ĐÚNG TUYẾN', '1. ĐÚNG TUYẾN', '2. TRÁI TUYẾN', '1. ĐÚNG TUYẾN' ];
		var ws = {};
		var row;
		var col;
		var xml;
		var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
		var _current_type = -1;
		var _current_type2 = -1;
		var range = {
			s : {
				c : 10000000,
				r : 10000000
			},
			e : {
				c : 0,
				r : 0
			}
		};
		var col_arr = [];
		var _offset = 1;
		var n = data.length;
		var _grparent_change = false;
		var widthArr = []; // L2PT-86385
		if (_DataType) {
			col_arr = Object.keys(_DataType);
			for (row = 0; row <= n; row++) {
				if (row > 0) {
					var _row_type = data[row - _offset].TUYEN;
					_grparent_change = false;
					if (_daTuyen && _current_type != _row_type) {
						if (range.s.r > row)
							range.s.r = row;
						if (range.s.c > 1)
							range.s.c = 1;
						if (range.e.r < row)
							range.e.r = row;
						if (range.e.c < 1)
							range.e.c = 1;
						var cell = {
							v : _type[_row_type]
						};
						var cell_ref = XLSX.utils.encode_cell({
							c : 0,
							r : row
						});
						cell.t = 's';
						cell.s = {
							font : {
								name : 'Times New Roman',
								sz : 14,
								color : {
									rgb : "#FF000000"
								},
								bold : true,
								italic : false,
								underline : false
							}
						};
						ws[cell_ref] = cell;
						_current_type = _row_type;
						_grparent_change = true;
						row += 1;
						n += 1;
						_offset += 1;
					}
					var _row_type2 = data[row - _offset].MA_LYDO_VVIEN == 3 ? 3 : 1;
					if (_loaiKcb && _current_type2 != _row_type2 || _grparent_change) {
						if (range.s.r > row)
							range.s.r = row;
						if (range.s.c > 1)
							range.s.c = 1;
						if (range.e.r < row)
							range.e.r = row;
						if (range.e.c < 1)
							range.e.c = 1;
						var cell = {
							v : _type2[_row_type2]
						};
						var cell_ref = XLSX.utils.encode_cell({
							c : 0,
							r : row
						});
						cell.t = 's';
						cell.s = {
							font : {
								name : 'Times New Roman',
								sz : 12,
								color : {
									rgb : "#FF0000"
								},
								bold : true,
								italic : false,
								underline : false
							}
						};
						ws[cell_ref] = cell;
						_current_type2 = _row_type2;
						row += 1;
						n += 1;
						_offset += 1;
					}
				}
				for (var col = 0; col < col_arr.length; col++) {
					if (range.s.r > row)
						range.s.r = row;
					if (range.s.c > col)
						range.s.c = col;
					if (range.e.r < row)
						range.e.r = row;
					if (range.e.c < col)
						range.e.c = col;
					_col = col_arr[col];
					var cell = row > 0 ? {
						v : data[row - _offset][_col]
					} : {
						v : _col
					};
					// L2PT-86385 start
					if (row == 0) {
						widthArr[col] = {
							wch : _col.length + 2
						};
					} else {
						var cell_data_len = cell.v ? (cell.v.toString().length + 2) : 0;
						cell_data_len = cell_data_len > 100 ? 100 : cell_data_len;
						if (widthArr[col].wch < cell_data_len) {
							widthArr[col].wch = cell_data_len;
						}
					}
					// L2PT-86385 end
					if (cell.v == null)
						continue;
					var cell_ref = XLSX.utils.encode_cell({
						c : col,
						r : row
					});
					if (row == 0)
						cell.t = 's';
					else
						cell.t = _DataType[_col];
					cell.s = {
						font : {
							name : 'Times New Roman',
							sz : row > 0 ? 12 : 14,
							color : {
								rgb : "#FF0000"
							},
							bold : row > 0 ? false : true,
							italic : false,
							underline : false
						}
					};
					ws[cell_ref] = cell;
				}
			}
		} else if (data.length > 0) {
			col_arr = Object.keys(data[0]);
			for (row = 0; row <= data.length; row++) {
				for (var col = 0; col < col_arr.length; col++) {
					if (range.s.r > row)
						range.s.r = row;
					if (range.s.c > col)
						range.s.c = col;
					if (range.e.r < row)
						range.e.r = row;
					if (range.e.c < col)
						range.e.c = col;
					_col = col_arr[col];
					var cell = row > 0 ? {
						v : data[row - 1][_col]
					} : {
						v : _col
					};
					// L2PT-86385 start
					if (row == 0) {
						widthArr[col] = {
							wch : _col.length + 2
						};
					} else {
						var cell_data_len = cell.v ? (cell.v.toString().length + 2) : 0;
						cell_data_len = cell_data_len > 100 ? 100 : cell_data_len;
						if (widthArr[col].wch < cell_data_len) {
							widthArr[col].wch = cell_data_len;
						}
					}
					// L2PT-86385 end
					if (cell.v == null)
						continue;
					var cell_ref = XLSX.utils.encode_cell({
						c : col,
						r : row
					});
					if (row == 0) {
						cell.t = 's';
					} else if (typeof cell.v === 'number')
						cell.t = 'n';
					// L2PT-50768
					// L2PT-86385 start
					else if (!isNaN(parseFloat(cell.v)) && isFinite(cell.v)) {
						if (cell.v.charAt(0) === '0') {
							cell.t = 's';
						} else {
							cell.t = 'n';
							if (cell.v.indexOf(".") == -1) {
								cell.z = "0";
							}
						}
					}
					// L2PT-86385 end
					else if (typeof cell.v === 'boolean')
						cell.t = 'b';
					else if (cell.v instanceof Date) {
						cell.t = 'n';
						cell.z = XLSX.SSF._table[14];
						cell.v = datenum(cell.v);
					} else
						cell.t = 's';
					ws[cell_ref] = cell;
				}
			}
		}
		if (range.s.c < 10000000)
			ws['!ref'] = XLSX.utils.encode_range(range);
		ws['!cols'] = widthArr; // L2PT-86385
		return ws;
	};
	function datenum(v, date1904) {
		if (date1904)
			v += 1462;
		var epoch = Date.parse(v);
		return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
	}
	function Workbook() {
		if (!(this instanceof Workbook))
			return new Workbook();
		this.SheetNames = [];
		this.Sheets = {};
	}
	function exportXLSX(_jsonObjects, _DataType, _wsname, _filename, _daTuyen, _loaiKcb) {
		var wb = new Workbook();
		// L2PT-45698: đổi tên func
		// L2PT-53905 start: nhiều sheet
		var _arrObjects = _jsonObjects;
		if (!_jsonObjects[0].data) {
			_arrObjects = [ {
				data : _jsonObjects,
				sheetName : _wsname
			} ];
		}
		_arrObjects.forEach(function(_jsonObject) {
			var ws = objectToXLSXExt(_jsonObject.data, _DataType, _daTuyen, _loaiKcb);
			/* add worksheet to workbook */
			wb.SheetNames.push(_jsonObject.sheetName);
			wb.Sheets[_jsonObject.sheetName] = ws;
		});
		// L2PT-53905 end
		var wbout = XLSX.write(wb, {
			bookType : 'xlsx',
			bookSST : true,
			type : 'binary'
		});
		saveAs(new Blob([ s2ab(wbout) ], {
			type : "application/octet-stream"
		}), _filename);
		//XLSX.writeFile(wb, _filename);
	}
	function s2ab(s) {
		var buf = new ArrayBuffer(s.length);
		var view = new Uint8Array(buf);
		for (var i = 0; i != s.length; ++i)
			view[i] = s.charCodeAt(i) & 0xFF;
		return buf;
	}
	download_async = function(content, filename, contentType, _rptExport) {
		if (!contentType)
			contentType = 'application/octet-stream';
		var a = document.getElementById(_rptExport);
		var blob = new Blob([ content ], {
			'type' : contentType
		});
		var url = window.URL.createObjectURL(blob);
		a.href = url;
		a.download = filename;
		a.click();
		window.URL.revokeObjectURL(a.href);
	};
	download = function(content, filename, contentType, _rptExport) {
		if (!contentType)
			contentType = 'application/octet-stream';
		var a = document.getElementById(_rptExport);
		var blob = new Blob([ content ], {
			'type' : contentType
		});
		a.href = window.URL.createObjectURL(blob);
		a.download = filename;
	};
	/*-----------------------------------------------------------------------------*/
	function setParamForPrint(_typeExp) {
		var _arrExp = "";
		//alert(moment($("#txtTU_NGAY").val(),'DD/MM/YYYY').format('YYYY-MM-DD'));
		if (_typeExp == "xls") {
			_arrExp = _lstExp_CHECK.split(",");
			_param = [ {
				name : 'tungay',
				type : 'String',
				value : _tu_ngay
			}, {
				name : 'denngay',
				type : 'String',
				value : _den_ngay
			} ];
		} else if (_typeExp == "xlsx") {
			_arrExp = _lstExp.split(",");
			_param = [ {
				name : 'tungay',
				type : 'String',
				value : _tu_ngay
			}, {
				name : 'denngay',
				type : 'String',
				value : _den_ngay
			} ];
		}
		doPrint(_arrExp, _param, _typeExp);
	}
	function doPrint(_arr, _param, _exp_type) {
		var _report_name;
//		, _report_huongthan, _report_hoachat, _report_thuoctieuhao;
		var _arrReport = _lstReport.split(",");
		for (var i = 0; i < _arr.length; i++) {
			_report_name = _arrReport[i];
			console.log("---------------------------_report_name: " + _report_name);
			if ($('#' + _arr[i]).attr('checked')) {
				openReport('window', _report_name, _exp_type, _param);
			}
		}
	}
	function stringToDate(date) {
		var parts = date.split("/");
		return new Date(parts[2], parts[1] - 1, parts[0]);
	}
	function stringToDateTime(date) {
		var parts = date.split("/");
		var tails = parts[2].split(" ");
		var times = tails[1].split(":");
		var ret = new Date(tails[0], parts[1] - 1, parts[0], times[0], times[1], times[2]);
		return ret;
	}
	var X = XLSX;
	var XW = {
		/* worker message */
		msg : 'xlsx',
		/* worker scripts */
		worker : './xlsxworker.js'
	};
	var process_wb = (function() {
		var to_json = function to_json(workbook) {
			var result = {};
			workbook.SheetNames.forEach(function(sheetName) {
				var roa = X.utils.sheet_to_json(workbook.Sheets[sheetName], {
					header : 1
				});
				if (roa.length)
					result[sheetName] = roa;
			});
			return JSON.stringify(result, 2, 2);
		};
		var to_csv = function to_csv(workbook) {
			var result = "";
			workbook.SheetNames.forEach(function(sheetName) {
				var csv = X.utils.sheet_to_csv(workbook.Sheets[sheetName]);
				csv = csv.replace(/(\r\n|\n|\r)/gm, ",");
				csv = csv.substring(0, csv.length - 1);
				result = csv;
			});
			return result;
		};
		return function process_wb(wb) {
			var ds_ba = to_csv(wb);
			doSearch(ds_ba);
		};
	})();
	var do_file = (function() {
		var rABS = typeof FileReader !== "undefined" && (FileReader.prototype || {}).readAsBinaryString;
		var use_worker = typeof Worker !== 'undefined';
		var xw = function xw(data, cb) {
			var worker = new Worker(XW.worker);
			worker.onmessage = function(e) {
				switch (e.data.t) {
					case 'ready':
					break;
					case 'e':
						console.error(e.data.d);
					break;
					case XW.msg:
						cb(JSON.parse(e.data.d));
					break;
				}
			};
			worker.postMessage({
				d : data,
				b : rABS ? 'binary' : 'array'
			});
		};
		return function do_file(files) {
			rABS = true;
			use_worker = false;
			var f = files[0];
			var reader = new FileReader();
			reader.onload = function(e) {
				if (typeof console !== 'undefined')
					console.log("onload", new Date(), rABS, use_worker);
				var data = e.target.result;
				if (!rABS)
					data = new Uint8Array(data);
				if (use_worker)
					xw(data, process_wb);
				else
					process_wb(X.read(data, {
						type : rABS ? 'binary' : 'array'
					}));
			};
			if (rABS)
				reader.readAsBinaryString(f);
			else
				reader.readAsArrayBuffer(f);
		};
	})();
	(function() {
		var drop = document.getElementById('drop');
		if (!drop.addEventListener)
			return;
		function handleDrop(e) {
			e.stopPropagation();
			e.preventDefault();
			do_file(e.dataTransfer.files);
		}
		function handleDragover(e) {
			e.stopPropagation();
			e.preventDefault();
			e.dataTransfer.dropEffect = 'copy';
		}
		drop.addEventListener('dragenter', handleDragover, false);
		drop.addEventListener('dragover', handleDragover, false);
		drop.addEventListener('drop', handleDrop, false);
	})();
//	(function() {
//		var xlf = document.getElementById('xlf');
//		if(!xlf.addEventListener) return;
//		function handleFile(e) { do_file(e.target.files); }
//		xlf.addEventListener('change', handleFile, false);
//	})();
}
