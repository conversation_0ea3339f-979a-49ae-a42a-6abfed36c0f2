<!-- 
 Mã màn hình  : NTU02D006
 File mã nguồn : NTU02D006_PhieuTruyenMau.js
 Mục đích  : <PERSON><PERSON><PERSON> diện màn hình
 	+ <PERSON>h<PERSON><PERSON> thông tin bệnh án
 	+ <PERSON>h<PERSON><PERSON> thông tin hỏi bệnh
 Tham số vào : 
 	truyenmau_id    : ID truyền máu
 	khambenh_id		: ID khám bệnh
	
 <PERSON>ời lập trình	 <PERSON> cập nhật  	<PERSON> chú
 hanhpt			     13/4/2017 			Phiếu truyền máu
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../noitru/NTU02D006_PhieuTruyenMau_K74.js"></script>
<script type="text/javascript" src="..//noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>

<style>
<!--
.disabledbutton {
    pointer-events: none;
    opacity: 0.4;
}
-->
</style>

<div id="divMain" class="container" style="min-height: 800px; border: 0px !important;">
<!--<div class="col-xs-offset-2 col-xs-8 col-sm-8 low-padding">-->

		<div class="mgt10" id="XNHH">
			<div class="form-inline">
				<div class="col-xs-10 low-padding">					
							<div class="col-xs-6 low-padding">
								<label class="mgl20">XÉT NGHIỆM HÒA HỢP MIỄN DỊCH TRUYỀN MÁU</label>
							</div>
				</div>
			</div>
			<div class="form-inline">
				<div class="col-xs-6 low-padding required">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Ngày giờ tạo phiếu</label>
						</div>
						<div class="col-xs-6 low-padding">
							<div class="input-group" style="width: 100%; ">	
								<input class="form-control input-sm" id="txtNGAYTAO" name="txtNGAYTAO" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19"> 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNGAYTAO','ddMMyyyy','dropdown',true,'24',true)" valrule="Ngày tạo,trim_required"></span>							 
							</div>
						</div>
				</div>	
				<div class="col-xs-6 low-padding required">
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Số phiếu</label>
						</div>		
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtSOPHIEU" style="width:100%;" disabled >	
						</div>
				</div>
			</div>
	
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding required">
							<label class="mgl20">Định nhóm ĐV máu ABO</label>
						</div>
						<div class="col-xs-6 low-padding">
							<select class="form-control input-sm kb-i-col-m " id="cboDINHNHOMDV_MAU_RH" style="width:50%;" valrule="Định nhóm máu,trim_required">
								<option value="Rh+">Rh+</option>
								<option value="Rh-">Rh-</option>
							</select>
							<select class="form-control input-sm kb-i-col-m " id="cboDINHNHOMDV_MAU" style="width:50%;" valrule="Định nhóm máu,trim_required">
								<option value="A">A</option>
								<option value="B">B</option>
								<option value="O">O</option>
								<option value="AB">AB</option>
							</select>	
						</div>
				</div>
				<div class="col-xs-6 low-padding required">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Định nhóm máu người nhận</label>
						</div>
						<div class="col-xs-6 low-padding">
							<select class="form-control input-sm kb-i-col-m " id="cboDINHNHOMNGUOINHAN_RH" style="width:50%;" valrule="Định nhóm người nhận,trim_required">
								<option value="Rh+">Rh+</option>
								<option value="Rh-">Rh-</option>
							</select>
							<select class="form-control input-sm kb-i-col-m " id="cboDINHNHOMNGUOINHAN" style="width:50%;" valrule="Định nhóm người nhận,trim_required">
								<option value="A">A</option>
								<option value="B">B</option>
								<option value="O">O</option>
								<option value="AB">AB</option>
							</select>
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding required">
							<label class="mgl20">Số lượng máu (ml)</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtSOLUONGMAU" style="width:100%;"valrule="Số lượng máu,required|max_length[4]|alpha_numeric|is_natural">	
						</div>
						
				</div>
				<div class="col-xs-6 low-padding required">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Đơn vị tính (túi)</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtDONVIMAU" style="width:100%;"valrule="Đơn vị máu,trim_required|max_length[50]|" >	
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-12 low-padding">					
						<div class="col-xs-3 low-padding">
							<label class="mgl20">Các xét nghiệm khác</label>
						</div>
						<div class="col-xs-9 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtXETNGHIEMKHAC" style="width:100%;"valrule="Định nhóm máu,max_length[2000]">	
						</div>
				</div>
			</div>		
			
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding ">
							<label class="mgl20">Ống 1: MT Muối</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtMTMUOI1" style="width:100%;"valrule="Ống 1: MT Muối,max_length[2000]">	
						
							
						</div>
				</div>
				<div class="col-xs-6 low-padding ">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">37°C/kháng globulin</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtDOKHANG1" style="width:100%;"valrule="Ống 1 37°C/kháng globulin,max_length[2000]|" >	
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding ">
							<label class="mgl20">Ống 2: MT Muối</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtMTMUOI2" style="width:100%;"valrule="Ống 2: MT Muối,max_length[2000]">	
						
							
						</div>
				</div>
				<div class="col-xs-6 low-padding ">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">37°C/kháng globulin</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtDOKHANG2" style="width:100%;"valrule="Ống 2 37°C/kháng globulin,max_length[2000]|" >	
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-12 low-padding">					
						<div class="col-xs-3 low-padding ">
							<label class="mgl20">Loại chế phẩm máu truyền</label>
						</div>
						<div class="col-xs-2 low-padding">	
					   			<input class="form-control input-sm" id="txtTKCHEPHAMMAU" name="txtTKCHEPHAMMAU" style="width: 100%;" maxlength="100">
					   		</div>
						<div class="col-xs-7 low-padding">
					   			<select class="form-control input-sm" style="width: 100%" id="cboCHEPHAMMAU" reffld="TEN_CHEPHAM_MAU">
								</select>
				   		</div>
						<!-- <div class="col-xs-9 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtCHEPHAMMAU" style="width:100%;"valrule="Loại chế phẩm máu truyền,max_length[2000]">	
						</div> -->
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding ">
							<label class="mgl20">Ngày lấy máu/ điều chế</label>
						</div>
						<div class="col-xs-6 low-padding">
							<div class="input-group" style="width: 100%; ">	
								<input class="form-control input-sm" id="txtNGAYLAYMAU" name="txtNGAYLAYMAU" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19" > 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNGAYLAYMAU','ddMMyyyy','dropdown',true,'24',true)" ></span>							 
							</div>
						</div>
						
				</div>
				<div class="col-xs-6 low-padding ">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Hạn dùng</label>
						</div>
						<div class="col-xs-6 low-padding">
							<div class="input-group" style="width: 100%; ">	
								<input class="form-control input-sm" id="txtHANDUNG" name="txtHANDUNG" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19" > 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtHANDUNG','ddMMyyyy','dropdown',true,'24',true)" ></span>							 
							</div>
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-12 low-padding">					
						<div class="col-xs-3 low-padding ">
							<label class="mgl20">Mã số ĐV chế phẩm máu truyền</label>
						</div>
						<div class="col-xs-9 low-padding">
					   		<div class="col-xs-12 low-padding">
								<input class="form-control input-sm i-col-m_fl" id="txtMASO_CHEPHAM_MAU" style="width:100%;"valrule="Mã số ĐV chế phẩm máu truyền,max_length[2000]">	
							</div>
				   			<!-- <div class="col-xs-9 low-padding">
					   			<select class="form-control input-sm" style="width: 100%" id="cboMASO_CHEPHAM_MAU" reffld="TEN_CHEPHAM_MAU">
								</select>
				   			</div> -->
						</div>
				</div>
			</div>
			
			<div class="form-inline mgt30" style="text-align: center;">
		     	<div class="col-xs-12 low-padding slim_solid_line">
		     	    <button type="button" class="btn btn-default btn-primary" id="btnMoi">
		     			<span class="glyphicon glyphicon-refresh"></span> Làm mới</button>
		     		<button type="button" class="btn btn-default btn-primary" id="btnLuu">
		     			<span class="glyphicon glyphicon-floppy-disk"></span> Thêm</button>
					<button type="button" class="btn btn-default btn-primary" id="btnCapnhat1" style="display: none">
		     			<span class="glyphicon glyphicon-floppy-disk"></span> Cập nhật</button>
		     		<button type="button" class="btn btn-default btn-primary" id="btnInphieu1">
		     			<span class="glyphicon glyphicon-floppy-disk"></span> In phiếu</button>
					
					<button type="button" class="btn btn-default btn-primary" id="btnHuy">
						<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
		     	</div>
		     </div>
			
		</div>
		
		<div class="mgt10" id="TPTM">
			<div class="form-inline">
				<div class="col-xs-10 low-padding">					
							<div class="col-xs-6 low-padding">
								<label class="mgl20">TẠO PHIẾU TRUYỀN MÁU</label>
							</div>
				</div>
			</div>
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding required">
							<label class="mgl20">Ngày giờ tạo phiếu</label>
						</div>
						<div class="col-xs-6 low-padding">
							<div class="input-group" style="width: 100%; ">	
								<input class="form-control input-sm" id="txtNGAYTAOPHIEU" name="txtNGAYTAOPHIEU" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19" > 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNGAYTAOPHIEU','ddMMyyyy','dropdown',true,'24',true)"</span>							 
							</div>
						</div>
						
				</div>
				<div class="col-xs-6 low-padding ">					
						<div class="col-xs-6 low-padding required">
							<label class="mgl20">Bắt đầu truyền</label>
						</div>
						<div class="col-xs-6 low-padding">
							<div class="input-group" style="width: 100%; ">	
								<input class="form-control input-sm" id=txtTGBATDAU name="txtTGBATDAU" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19"> 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTGBATDAU','ddMMyyyy','dropdown',true,'24',true)" </span>							 
							</div>
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Lần truyền máu thứ</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtLANTRUYEN" style="width:100%;"valrule="Lần truyền máu,max_length[100]">	
						</div>
				</div>
				<div class="col-xs-6 low-padding ">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Số lượng thực tế truyền (ml)</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtSOLUONGMAU_THUCTE" style="width:100%;"valrule="Số lượng thực tế,max_length[100]|" >	
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-6 low-padding">					
						<div class="col-xs-6 low-padding ">
							<label class="mgl20">Định nhóm máu tại giường</label>
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtDINHNHOMMAU_TAIGIUONG" style="width:100%;"valrule="Định nhóm máu tại giường,max_length[100]">	
						</div>
				</div>
				<div class="col-xs-6 low-padding ">					
						<div class="col-xs-6 low-padding">
							<label class="mgl20">Bác sỹ chỉ định:</label>
						</div>
						<div class="col-xs-6 low-padding">
							<select class="form-control input-sm" style="width: 100%;" id="cboBS_CHIDINH" ></select>
						</div>
				</div>
			</div>
			
			<div class="form-inline">	
				<div class="col-xs-12 low-padding">					
						<div class="col-xs-3 low-padding ">
							<label class="mgl20">Nhận xét quá trình truyền máu</label>
						</div>
						<div class="col-xs-9 low-padding">
						 <div class="col-xs-12 low-padding">
							<input class="form-control input-sm i-col-m_fl" id="txtNHANXET_QUATRINH" maxlength="2000" style="width:100%;">	
							</div>
						</div>
				</div>
			</div>
			
			<div class="form-inline mgt30" style="text-align: center;">
		     	<div class="col-xs-12 low-padding slim_solid_line">
		     	    <button type="button" class="btn btn-default btn-primary" id="btnRefresh">
		     			<span class="glyphicon glyphicon-refresh"></span> Làm mới</button>
		     		<button type="button" class="btn btn-default btn-primary" id="btnSave">
		     			<span class="glyphicon glyphicon-floppy-disk"></span> Thêm</button>
					<button type="button" class="btn btn-default btn-primary" id="btnCapnhat2" style="display: none">
		     			<span class="glyphicon glyphicon-floppy-disk"></span> Cập nhật</button>
		     		<button type="button" class="btn btn-default btn-primary" id="btnInphieu2">
		     			<span class="glyphicon glyphicon-floppy-disk"></span> In phiếu</button>
					<button type="button" class="btn btn-default btn-primary" id="btnKySo">
						<span class="glyphicon glyphicon-ok"></span> Lưu & Ký</button>
					<button type="button" class="btn btn-default btn-primary" id="btnHuyKy">
						<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký</button>
					<button type="button" class="btn btn-default btn-primary" id="btnInKySo">
						<span class="glyphicon glyphicon-print"></span> In Phiếu ký số</button>
					<button type="button" class="btn btn-default btn-primary" id="btnClose">
						<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
		     	</div>
		     </div>
			
		</div>
		
		</div>
	</div>

</div>

<script>

	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var url ='{url}';
	var dept_id ='{dept_id}'; 
	//var khambenh_id = '{khambenh_id}';
	var session_par=[];
	
	initRest(uuid,"/vnpthis");
	var _opts={
			lang: lang,
			_param:session_par,
			_uuid:uuid,
			hospital_id: hospital_id,
			khambenh_id: "",
			thoigianvaovien:"",
			dept_id:dept_id
		}
	
	 var data;	
    var mode = '{showMode}';    	
	if(mode=='dlg') {		
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		
		_opts.khambenh_id = data.khambenh_id;	
		_opts.truyenmau_id = data.truyenmauid;	
		_opts.maubenhpham_id = data.maubenhpham_id;
		_opts.sophieu = data.sophieu;
		_opts.thoigianvaovien=data.thoigianvaovien;
		_opts.modeDisplay = data.modeDisplay;
	}
	
	var ptd = new phieuTruyenMau_K74(_opts);
	ptd.load();	
</script>