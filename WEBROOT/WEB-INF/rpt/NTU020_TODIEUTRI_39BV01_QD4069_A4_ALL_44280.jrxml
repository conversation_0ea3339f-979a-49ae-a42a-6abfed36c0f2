<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL_44280" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="35" rightMargin="5" topMargin="2" bottomMargin="10" uuid="062ad1cf-ad8c-4843-a0c7-e03f0f0cbd08">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="i_benhnhanid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="i_khoaid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_khambenhid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call NTU_TODIEUTRI_ALL_44280($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{i_benhnhanid},$P{i_khoaid},$P{i_khambenhid},$P{ora_cursor})}]]>
	</queryString>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="CMND" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="BUONG" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="TENBENHVIEN" class="java.lang.String"/>
	<field name="SOYTE" class="java.lang.String"/>
	<field name="SO" class="java.lang.String"/>
	<field name="MAGIUONG" class="java.lang.String"/>
	<field name="MAUBENHPHAMID" class="java.math.BigDecimal"/>
	<field name="HOSOBENHANID" class="java.math.BigDecimal"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="NGAYMAUBENHPHAM" class="java.lang.String"/>
	<field name="DIENBIENBENH" class="java.lang.String"/>
	<field name="YLENH" class="java.lang.String"/>
	<field name="PDT_CLS_XN" class="java.lang.String"/>
	<field name="CHANDOAN_KEMTHEO" class="java.lang.String"/>
	<field name="YLENHPLUS" class="java.lang.String"/>
	<field name="BSDIEUTRI" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="XULY" class="java.lang.String"/>
	<field name="TVT_HOANTRA" class="java.lang.String"/>
	<field name="VATTU" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="PDT_CLS_CDHA" class="java.lang.String"/>
	<field name="GHICHUBENHCHINH" class="java.lang.String"/>
	<field name="MACHANDOAN" class="java.lang.String"/>
	<field name="GHICHUBENHPHU" class="java.lang.String"/>
	<field name="GIOMAUBENHPHAM" class="java.lang.String"/>
	<field name="YLENH_YHCT" class="java.lang.String"/>
	<field name="PDT_CLS_PTTT" class="java.lang.String"/>
	<field name="BENHCHINH" class="java.lang.String"/>
	<field name="MABENHCHINH" class="java.lang.String"/>
	<field name="BENHPHU" class="java.lang.String"/>
	<field name="GHICHUBENHCHINH2" class="java.lang.String"/>
	<field name="LICHHEN" class="java.lang.String"/>
	<variable name="V_DIENBIEN" class="java.lang.String" resetType="Column">
		<variableExpression><![CDATA[$F{DIENBIENBENH}.replaceAll("\\n","<br>")]]></variableExpression>
	</variable>
	<variable name="V_CHUKY" class="java.lang.String" resetType="Column">
		<variableExpression><![CDATA[" <br><br>"
+"<pre>"+"                                     <b>"+$F{BSDIEUTRI}+"</b></pre>"]]></variableExpression>
	</variable>
	<variable name="V_YLENH" class="java.lang.String" resetType="Column">
		<variableExpression><![CDATA[($F{XULY}==null?"":"- Xử lý:"+$F{XULY}.replaceAll("\\n","<br>")+"<br>")
+ ($F{PDT_CLS_XN}==null?"":"+ Xét nghiệm, vi sinh:<br>"+$F{PDT_CLS_XN})
+ ($F{PDT_CLS_CDHA}==null?"":"+ CĐHA & TDCN:<br>"+$F{PDT_CLS_CDHA})
+(($F{YLENH}!=null ) ?"- Chỉ định dùng thuốc: <br>":"")
+ ($F{YLENH}==null?"":""+$F{YLENH}+"<br>")
+($F{YLENH_YHCT}==null?"":"+ Vị thuốc y học cổ truyền: <br>"+$F{YLENH_YHCT}+"<br>")
+ ($F{PDT_CLS_PTTT}==null?"":"+ Chỉ định phẫu thuật, thủ thuật:<br>"+$F{PDT_CLS_PTTT})
+ ($F{VATTU}==null?"":"- Vật tư đi kèm: <br>"+$F{VATTU}+"<br>")
+ ($F{TVT_HOANTRA}==null?"":"- Chỉ định hoàn trả thuốc/ vật tư: <br>"+$F{TVT_HOANTRA}+"<br>")
+ ($F{GHICHU}==null?"":"- Ghi chú: <br>"+$F{GHICHU}.replaceAll("\\n","<br>")+"<br>")
+($F{YLENHPLUS}==null?"":$F{YLENHPLUS}.replace("Chọn", "")+"<br>")]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="163" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="281" y="136" width="274" height="27" uuid="de9307e2-358f-4d3c-a38e-5332a998566d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Y LỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1" y="136" width="91" height="27" uuid="241f4ee2-647a-44ef-84ec-761ac17012b8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[NGÀY
GIỜ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="92" y="136" width="189" height="27" uuid="5df0c74f-b817-4d3b-9439-28d81fe3b402"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[DIỄN BIẾN BỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="221" y="49" width="101" height="20" uuid="e408e832-c1b4-4d3c-9170-388238046de2">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[TỜ ĐIỀU TRỊ SỐ ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="397" y="0" width="157" height="14" uuid="6706ef73-a2e2-4a04-9819-58afbb9de831">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[MS: 39/BV-01]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="20" width="217" height="20" uuid="6189167f-e78f-467d-a305-76ae14e3ea47">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="398" y="14" width="62" height="14" uuid="26272cff-ece9-4dc9-8988-7c2cf3a33882">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Số vào viện:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="460" y="14" width="96" height="14" uuid="8b7122a0-10e8-4b7c-8df6-533c02fbe75c">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="1" y="84" width="110" height="15" uuid="ade2e6e2-57f3-42d6-a64b-d84fc15ef085">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[Họ tên người bệnh:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="111" y="84" width="237" height="15" uuid="099d9402-bada-4b7e-9af3-4a51a60f5cb3">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="348" y="84" width="32" height="15" uuid="37fc1384-81c0-4ded-9818-d87c6f7b104a">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Tuổi: ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="380" y="84" width="72" height="15" uuid="c64c3f98-b9e0-47a3-9ff8-91ca06eeb603">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="506" y="84" width="50" height="15" uuid="7977f98d-1d54-4f38-83ab-8ed697659d6a">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="452" y="84" width="54" height="15" uuid="1ce4b91d-d8a2-490c-a7fc-81a67a85b744">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Giới tính: ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="438" y="28" width="118" height="14" uuid="39180844-ef86-4150-9b5b-47737ff3292a">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="397" y="28" width="40" height="14" uuid="a99eb32a-925b-4acd-b151-102247f6b754">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Mã BN:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="115" width="555" height="15" uuid="267b11be-610f-410d-9063-e3e7fc64f19f">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: "+($F{MACHANDOAN}==null?"":$F{MACHANDOAN}+" - ")+($F{CHANDOAN}==null?"":$F{CHANDOAN}) +($F{GHICHUBENHCHINH2}==null?"":"("+$F{GHICHUBENHCHINH2}+")")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="0" width="217" height="20" uuid="7d1cca61-557e-4edc-abde-2a9ef01fcc77">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="41" width="215" height="1" uuid="322f53bc-b42d-4f47-a549-3ec41c320657">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
			</line>
			<frame>
				<reportElement positionType="Float" x="1" y="99" width="555" height="16" uuid="1eb8d6c5-ae81-4c6a-a0a5-8a361c747590">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="38" height="15" uuid="1f9c5631-9795-403f-b109-831db0e0b70f">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13"/>
					</textElement>
					<text><![CDATA[Khoa: ]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="38" y="0" width="137" height="15" uuid="c8e829a4-8860-443d-a32e-88734fa30b5c">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KHOA}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="175" y="1" width="51" height="15" uuid="f0884d93-7049-4d43-9aa1-c91185b6de4f">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13"/>
					</textElement>
					<text><![CDATA[Buồng:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="226" y="1" width="170" height="15" uuid="f19399e1-1465-4c07-8422-f14e358c5a09">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{BUONG}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="445" y="0" width="109" height="15" uuid="f71e924c-3ec3-4988-b2b5-7d0e827e3a0e">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MAGIUONG}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="397" y="0" width="48" height="15" uuid="24041bd9-28bc-447b-a00a-910490fb04d8">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13"/>
					</textElement>
					<text><![CDATA[Giường: ]]></text>
				</staticText>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="77" splitType="Prevent">
			<frame>
				<reportElement x="1" y="0" width="554" height="77" uuid="11ff3822-9bae-4f0b-8126-b7bdd73fdc37"/>
				<box>
					<topPen lineWidth="0.75"/>
					<leftPen lineWidth="0.75"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.75"/>
				</box>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="91" height="77" uuid="48cee7ac-d705-40ad-8d71-1573e5267feb"/>
					<box topPadding="10">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="html">
						<font fontName="Times New Roman" size="10"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYMAUBENHPHAM}+"  "+$F{GIOMAUBENHPHAM}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="91" y="0" width="274" height="77" forecolor="#FFFFFF" uuid="e1c9ab21-6e4d-4676-b657-9e5d0df23608"/>
					<box>
						<topPen lineWidth="0.0" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top" markup="html">
						<font fontName="Times New Roman" size="11"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($V{V_YLENH}==null || $V{V_YLENH}.equals( "" )) ?"":($V{V_YLENH}.trim())+
(($V{V_YLENH}==null || $V{V_YLENH}.equals( "" ))?"":$V{V_CHUKY})]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="91" y="0" width="189" height="77" uuid="311e85cc-d319-4d7b-93bb-cb37fe268ade"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Top" markup="html">
						<font fontName="Times New Roman" size="10"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{V_DIENBIEN}==null?"":($V{V_DIENBIEN} )]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="280" y="0" width="274" height="77" uuid="ddc8b49b-ee37-405b-8ebf-dfc92c859727"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Top" markup="html">
						<font fontName="Times New Roman" size="10"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($V{V_YLENH}==null || $V{V_YLENH}.equals( "" )) ?"":($V{V_YLENH}.trim())+
(($V{V_YLENH}==null || $V{V_YLENH}.equals( "" ))?"":$V{V_CHUKY})]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="16" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{LICHHEN}!= null]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="0" width="280" height="16" uuid="e0ec6332-7e73-4d51-b52a-505e580d8e2f">
					<printWhenExpression><![CDATA[$F{LICHHEN}!= null]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày hẹn khám: "+($F{LICHHEN}==null?"":$F{LICHHEN})]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
