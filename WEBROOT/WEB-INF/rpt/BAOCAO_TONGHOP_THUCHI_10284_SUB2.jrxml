<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BAOCAO_TONGHOP_THUCHI_10284" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" uuid="02afe4c9-87bb-4263-9fdc-5ca4328b421b">
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="ireport.zoom" value="1.7715610000000197"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="VPARS_i_nguoithuid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dt_tungay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dt_denngay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_i_nguoithuid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="loaitiepnhanid" class="java.lang.Long">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="loaitiepnhanid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_hinhthucthanhtoanid_VALUE" class="java.lang.String"/>
	<parameter name="branchid" class="java.lang.String"/>
	<parameter name="VPARS_hinhthucthanhtoanid_TEXT" class="java.lang.String"/>
	<parameter name="VPARS_i_dtbnids_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_i_dtbnids_TEXT" class="java.lang.String"/>
	<parameter name="VPARS_i_dtbnid_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_i_dtbnid_TEXT" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call rpt_tonghop_thutien_10284(
$P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{dt_tungay},$P{dt_denngay},
$P{loaitiepnhanid},
$P{VPARS_i_nguoithuid_VALUE},
$P{VPARS_hinhthucthanhtoanid_VALUE},
$P{branchid},
$P{VPARS_i_dtbnids_VALUE},
$P{VPARS_i_dtbnid_VALUE},
$P{ora_cursor})}]]>
	</queryString>
	<field name="NGAYTHU" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="HINHTHUCTT" class="java.lang.String"/>
	<field name="NOIDUNGTHU" class="java.lang.String"/>
	<field name="KHOAPHONG" class="java.lang.String"/>
	<field name="MAPHIEUTHU" class="java.lang.String"/>
	<field name="TAMUNG" class="java.math.BigDecimal"/>
	<field name="HOANUNG" class="java.math.BigDecimal"/>
	<field name="TIENKCB" class="java.math.BigDecimal"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="manhomphieuthu" class="java.lang.String"/>
	<field name="fullmaphieuthu" class="java.lang.String"/>
	<field name="tennhomdoituong" class="java.lang.String"/>
	<field name="tendoituong" class="java.lang.String"/>
	<field name="tenhinhthuc" class="java.lang.String"/>
	<field name="R_TONGQUY" class="java.lang.String"/>
	<field name="NGUOITHU" class="java.lang.String"/>
	<field name="stk_thanhtoan" class="java.lang.String"/>
	<variable name="V_TAMUNG" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMUNG}]]></variableExpression>
	</variable>
	<variable name="V_KCB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENKCB}]]></variableExpression>
	</variable>
	<variable name="V_HOANTRA" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANUNG}]]></variableExpression>
	</variable>
	<variable name="Tong" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{V_TAMUNG}.add($V{V_KCB})]]></variableExpression>
	</variable>
	<variable name="Chi" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{V_HOANTRA}]]></variableExpression>
	</variable>
	<variable name="Ton" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{Tong}.subtract($V{Chi})]]></variableExpression>
	</variable>
	<variable name="V_TAMUNG_G" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NGUOITHU" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMUNG}]]></variableExpression>
	</variable>
	<variable name="V_HOANTRA_G" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NGUOITHU" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANUNG}]]></variableExpression>
	</variable>
	<variable name="V_KCB_G" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NGUOITHU" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENKCB}]]></variableExpression>
	</variable>
	<variable name="V_KCB_G_STK" class="java.math.BigDecimal" resetType="Group" resetGroup="group_taikhoan" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENKCB}]]></variableExpression>
	</variable>
	<variable name="V_HOANTRA_G_STK" class="java.math.BigDecimal" resetType="Group" resetGroup="group_taikhoan" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANUNG}]]></variableExpression>
	</variable>
	<variable name="V_TAMUNG_G_STK" class="java.math.BigDecimal" resetType="Group" resetGroup="group_taikhoan" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMUNG}]]></variableExpression>
	</variable>
	<group name="group_taikhoan">
		<groupExpression><![CDATA[$F{stk_thanhtoan}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="494" height="18" uuid="2ddf6c2e-a25d-4947-84cd-ba2aabb84209"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["Số tài khoản: "+($F{stk_thanhtoan}==null?" ":$F{stk_thanhtoan})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NGUOITHU" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="494" y="0" width="85" height="18" uuid="d96281cc-ba5b-4357-8879-ece56fe27ed2"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_KCB_G_STK}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NGUOITHU" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="579" y="0" width="86" height="18" uuid="a53b933e-c4de-44e2-8b41-fecc259407d3"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_TAMUNG_G_STK}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NGUOITHU" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="665" y="0" width="82" height="18" uuid="4b255b2e-bc04-4e4a-aeb2-3a0e300a4f64"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_HOANTRA_G_STK}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="747" y="0" width="55" height="18" uuid="84dc3a34-6fe9-488d-afba-8677b2906830"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="GROUP_NGUOITHU">
		<groupExpression><![CDATA[$F{NGUOITHU}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="494" height="18" uuid="528d28ae-51cd-448a-9211-07add1e441bb"/>
					<box leftPadding="10">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITHU}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NGUOITHU" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="494" y="0" width="85" height="18" uuid="99fba14b-d874-4812-b0f5-626791e7477c"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_KCB_G}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NGUOITHU" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="579" y="0" width="86" height="18" uuid="73c14b3e-1b6a-4fae-bcbe-31bfd94e8e9f"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_TAMUNG_G}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NGUOITHU" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="665" y="0" width="82" height="18" uuid="2f5b3786-dddd-4e86-9558-7c731e4d8c95"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_HOANTRA_G}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="747" y="0" width="55" height="18" uuid="80c46c33-6140-45e5-b705-04d5121d412a"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="113">
			<textField>
				<reportElement positionType="Float" x="0" y="1" width="322" height="22" uuid="242863ce-62f6-41ba-b6a2-a5062bbcd2ed"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="23" width="322" height="19" uuid="44bac324-2384-4ea2-b0ae-96c0613ad809"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="80" width="802" height="16" uuid="d17f1ae0-0c52-44df-9d88-f3b4428f7cc6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: "
+ new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format( $P{dt_tungay})
+ "    đến ngày: "
+  new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format( $P{dt_denngay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="64" width="802" height="16" uuid="4d0e2f39-ab87-4f81-9701-658f9da9a172"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Loại khám chữa bệnh: " + ( $P{loaitiepnhanid} == -1L ? "Tất cả": ($P{loaitiepnhanid} == 0L ? "NỘI TRÚ" : "NGOẠI TRÚ"))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="42" width="802" height="22" uuid="a229ae9d-dd9b-49d9-9627-46b32ed7c1b6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tendoituong}==null&&$F{tennhomdoituong}==null&&$F{tenhinhthuc}==null?
"BẢNG KÊ THANH TOÁN THEO CÁN BỘ THU NGÂN":
"BẢNG KÊ THANH TOÁN THEO CÁN BỘ THU NGÂN ("
+($F{tendoituong}==null?($F{tennhomdoituong}==null?"":$F{tennhomdoituong}):$F{tendoituong})
+" - "
+($F{tenhinhthuc}==null?"":$F{tenhinhthuc})
+")"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="96" width="803" height="17" uuid="46458727-dcec-4194-b6ba-9063d47f4415"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Thu ngân: "+ ($P{VPARS_i_nguoithuid_TEXT}==null?"":$P{VPARS_i_nguoithuid_TEXT})]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="53">
			<staticText>
				<reportElement positionType="Float" x="0" y="16" width="30" height="37" uuid="ad24f7ab-b4f4-4cc7-8022-b46af62622e2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="158" y="16" width="70" height="37" uuid="8f7b3567-575a-4064-af8b-a26cd86904de"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Số biên lai]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="99" y="16" width="59" height="37" uuid="1da16f39-cec7-4642-b601-a474525e23c0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="322" y="16" width="96" height="37" uuid="643e6713-9057-4896-b186-83e6c74ad62e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Khoa phòng]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="228" y="16" width="94" height="37" uuid="d150b425-efb0-4326-a05a-d52240d0215a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ và tên]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="418" y="16" width="76" height="37" uuid="143f9322-d6a2-46cd-8d35-6832871b1d48"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nội dung]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="665" y="34" width="82" height="19" uuid="0690baaa-f164-48a2-9f69-2c1ca77945c4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Hoàn trả]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="30" y="16" width="69" height="37" uuid="03573551-f313-4ad5-9d89-ee73986a8706"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã khám]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="494" y="34" width="85" height="19" uuid="36a933f0-906f-4370-aafb-313f2614b2c7"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Thu KCB]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="579" y="34" width="86" height="19" uuid="abe74def-3874-47af-9e75-cee99d78e73a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tạm ứng]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="494" y="16" width="253" height="18" uuid="5245d44c-559f-42cb-a35a-1571e325a69d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="747" y="16" width="55" height="37" uuid="3f2267a3-36dc-4e9d-a9ce-e2aa41df0ffa"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="803" height="16" uuid="45c22bde-f2cc-4a33-933a-84aac0ee61bb"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph rightIndent="10"/>
				</textElement>
				<text><![CDATA[Đơn vị: Đồng]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="18" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="18" uuid="79bfbda9-80a1-400a-b628-2871c6fd1268"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{GROUP_NGUOITHU_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="158" y="0" width="70" height="18" uuid="3fb9f48f-c043-4b5c-9ce2-4f319513404b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{manhomphieuthu}+$F{fullmaphieuthu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="228" y="0" width="94" height="18" uuid="2855f4cd-33a9-4eb3-a71c-bec50273d158"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="99" y="0" width="59" height="18" uuid="1c9fd363-1819-4b49-8edc-b36fe87a54a5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTHU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="30" y="0" width="69" height="18" uuid="3eff213a-cdff-4101-94dd-ab5dfc8bcbdc"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="665" y="0" width="82" height="18" uuid="cf647d1b-3262-41d3-a6bd-f950e1e3be6b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{HOANUNG}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="579" y="0" width="86" height="18" uuid="96828fad-4e1e-4a88-9c83-7f776820e345"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TAMUNG}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="418" y="0" width="76" height="18" uuid="1c07628f-c58c-4365-92f7-7fd47342165b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOIDUNGTHU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="494" y="0" width="85" height="18" uuid="313e85e6-71c4-4ff8-a35a-d5f1e0d1cb10"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TIENKCB}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="322" y="0" width="96" height="18" uuid="e1bf9179-fedb-4927-a841-516123e33429"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOAPHONG}==null?"":$F{KHOAPHONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="747" y="0" width="55" height="18" uuid="3b3e988e-4814-45d5-8fba-555b57e6529d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GHICHU}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="15">
			<textField evaluationTime="Report">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="228" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="dbffd798-42ae-4eda-884b-14d6c0758f2b"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày in: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date())]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" mode="Transparent" x="769" y="0" width="32" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e1c69b4c-e45c-4d18-b21a-44407a2f5ffc"/>
				<box leftPadding="1" rightPadding="0"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" mode="Transparent" x="665" y="0" width="104" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="090a063b-db19-4088-bc62-138dc9fb8087"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang: " + $V{PAGE_NUMBER} + "/"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="111" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="494" height="18" uuid="448f8491-42a3-47d7-aadd-308125afa73f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[TỔNG CỘNG     ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="75" width="158" height="18" uuid="417c9fc3-1432-4945-9c85-c9dfc7a239d3"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Người lập bảng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="665" y="0" width="82" height="18" uuid="57b1bebf-2ec8-4890-b9ff-526998fa2d3a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_HOANTRA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="579" y="0" width="86" height="18" uuid="46d5bfcb-5bd0-4ffd-b2bf-6602c497b936"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_TAMUNG}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="93" width="158" height="18" uuid="d26157ee-b018-48f3-8661-9c3d96a8b68a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="75" width="190" height="18" uuid="3ec0def1-258b-4363-ae61-b0a415667608"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Người thu tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="93" width="190" height="18" uuid="b421851c-1c67-4580-b470-3ac2500de74c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="418" y="75" width="161" height="18" uuid="7c31d90f-fbf3-4927-af82-832f4f24f80e"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Thủ quỹ]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="75" width="223" height="18" uuid="7cfc89c4-b611-4822-968e-62b4b264e00f"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Kế toán trưởng]]></text>
			</staticText>
			<staticText>
				<reportElement x="418" y="93" width="161" height="18" uuid="5b3442b1-6302-4d01-95ee-4efff80006c7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="93" width="223" height="18" uuid="9e9b2840-4579-443d-ae9d-f215b0c9a000"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="18" width="58" height="20" uuid="81735d68-14ca-422c-893d-e7e6aa0fa83d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng thu:]]></text>
			</staticText>
			<staticText>
				<reportElement x="202" y="18" width="56" height="20" uuid="5bfe8a70-2fd3-4a5d-bf2d-3b1e039e4aa1"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng chi:]]></text>
			</staticText>
			<staticText>
				<reportElement x="381" y="18" width="56" height="20" uuid="78d97980-10ea-4164-a8d5-153c756a4730"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tồn quỹ:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="494" y="0" width="85" height="18" uuid="d64e5247-deb6-49da-a9e6-8a1851291b35"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{V_KCB}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="58" y="18" width="144" height="20" uuid="d120b732-27a6-4661-9766-d23c769823f8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{Tong}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="258" y="18" width="123" height="20" uuid="22c0489e-5669-4664-96c3-967af4ae4a77"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{Chi}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="437" y="18" width="225" height="20" uuid="73cb8596-cf7b-4472-9189-c9ea6e05ba0e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{Ton}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="747" y="0" width="55" height="18" uuid="82978e54-0c09-4802-88e2-c6c978424ba2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="dd" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="579" y="57" width="223" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="74eec3d0-90b5-4062-ad9d-bda8309ed19b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("HH:mm:ss").format(new Date())
+" ,ngày " + new SimpleDateFormat("dd").format(new java.util.Date())
+ " tháng " + new SimpleDateFormat("MM").format(new java.util.Date())
+ " năm "+ new SimpleDateFormat("yyyy").format(new java.util.Date())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="0" y="38" width="801" height="18" uuid="7338821f-e7dd-4912-aa89-155f61b7d6de"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<b>Tồn quỹ (bằng chữ):</b> "+($F{R_TONGQUY}==null?"":$F{R_TONGQUY}) +" đồng"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
