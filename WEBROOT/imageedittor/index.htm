<!--<script type="text/javascript" src="../common/script/main.js"></script>-->
<!--<script type="text/javascript" src="{menuJS}"></script>-->
<!--<input type="hidden" name="company_id" id="company_id"-->
<!--       value="{company_id}"/>-->
<!--<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>-->
<!--<script type="text/javascript" src="../common/script/RestService.js"></script>-->
<!--<script type="text/javascript" src="../common/script/dbForm.js"></script>-->
<!--<script type="text/javascript" src="../common/script/dbToolbar.js"></script>-->
<!--<script type="text/javascript" src="../common/script/DataValidator.js"></script>-->
<!--<script type="text/javascript" src="../common/script/CommonUtil.js"></script>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/jquery/jquery.toaster.js"></script>-->
<!--<script src="../common/script/jqgrid/js/context-menu.js	"></script>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/jquery/jquery.mask-plugin.min.js"></script>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>-->
<!--<link rel="stylesheet" href="../common/css/font-awesome.min.css">-->
<!--<link rel="stylesheet"-->
<!--      href="../common/script/bootstrap/css/bootstrap.min.css">-->
<!--<link rel="stylesheet" href="../common/css/custom.css">-->
<!--<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">-->
<!--<link rel="stylesheet" href="../common/css/css_style.css">-->
<!--<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"-->
<!--      rel="stylesheet"/>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/jquery.i18n/jquery.i18n.js"></script>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>-->


<!--<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>-->
<!--<link rel="stylesheet" href="../common/script/dialog/jBox.css">-->

<!--<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>-->
<!--<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>-->
<!--<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>-->
<!--<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>-->
<!--<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>-->
<!--<script type="text/javascript" src="../common/script/UIUtil.js"></script>-->
<!--<script type="text/javascript" src="../common/script/moment.min.js"></script>-->
<!--<script src="../common/script/common.js"></script>-->
<!--<script type="text/javascript"-->
<!--        src="../common/script/jquery.priceformat.2.0.min.js"></script>-->
<script type="text/javascript" src="../imageedittor/index.js"></script>
<script type="text/javascript" src="../common/script/filerobot-image-editor.min.js"></script>

<div width="100%" id="divMainContainer" class="div-main-container">

</div>
<style>
    body {
        background-color: rgb(0 0 0 / 23%);
    }

    .FIE_image-tool-button,
    .FIE_rect-tool-button,
    .FIE_ellipse-tool-button,
    .FIE_polygon-tool-button,
    .FIE_topbar-close-button,
    .FIE_text-tool-options .FIE_annotation-option-triggerer{
        display: none;
    }

    .FIE_text-tool-button,
    .FIE_pen-tool-button,
    .FIE_line-tool-button,
    .FIE_arrow-tool-button {
        border: 1px solid #d7d7d7;
    }
</style>
<script>
    // var opt = [];
    // var hospital_id = '{hospital_id}';
    // var user_id = '{user_id}';
    // var user_type = '{user_type}';
    // var province_id = '{province_id}';
    // var uuid = '{uuid}';
    //
    // console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    // var session_par = [];
    // session_par[0] = hospital_id;
    // session_par[1] = user_id;
    // session_par[2] = user_type;
    // session_par[3] = province_id;
    // var table_name = '{table}';

    // initRest(uuid, "/vnpthis");

    var _opts = new Object();
    // _opts._param = session_par;
    // _opts._uuid = uuid;
    var DS = new dSDTList(_opts);
    DS.load();
</script>