/*
 * Mr <PERSON> is a automator, he send message to remote host automatic
 * 
 * VietDA 	05/12/2020		Tạo mới
 * 
 */

var ctl_ar=[
	{type:'button',id:'btnConfig',icon:'glyphicon glyphicon-cog',text:'HL7 Config'},
	{type:'button',id:'btnAuto',icon:'glyphicon glyphicon-tasks',text:'Automator'},
	{type:'button',id:'btnMaker',icon:'glyphicon glyphicon-envelope',text:'Msg Maker'},
	{type:'button',id:'btnLog',icon:'glyphicon glyphicon-list-alt',text:'Msg Log'},
	{type:'label',id:'lblInfo',icon:'',text:'Máy tự động hóa HL7'}
];

function LogCLS(opt) {
	this.load = doLoad;
	
	function doLoad(){
		initControl();
		bindEvent();

        loadStatus();
		$("#txtTuNgay").val(moment().format('DD/MM/YYYY'));
		var callback = function () {
            if ($.active !== 0) {
                setTimeout(callback, '50');
                return;
            }
            loadMsgQueue();
        };
        callback();
	}
	
	function initControl(){		
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		ToolbarUtil.build('toolbarId', ctl_ar);
		var _toolbar=ToolbarUtil.getToolbar('toolbarId');
		
		$("#grdMsgQueue").jqGrid({
			datatype: 'local',
			colNames: ['Số phiếu','Ngày','Mã BN','Mã BA','Tên BN','Mã dịch vụ','Tên dịch vụ',''],
			colModel: [
			    {name:'SOPHIEU', width:130, formatter:GridUtil.fm.formatStrEncode },
				{name:'NGAYMAUBENHPHAM', width:130, formatter:GridUtil.fm.formatStrEncode },
			    {name:'MABENHNHAN', width: 100, formatter:GridUtil.fm.formatStrEncode },
			    {name:'MAHOSOBENHAN', width: 100, formatter:GridUtil.fm.formatStrEncode },
			    {name:'TENBENHNHAN', width: 200, formatter:GridUtil.fm.formatStrEncode },
			    {name:'MADICHVU', width: 130, formatter:GridUtil.fm.formatStrEncode },
			    {name:'TENDICHVU', width: 300, formatter:GridUtil.fm.formatStrEncode },
			    {name:'ERROR', hidden: true }
			  ],
			  rowNum: 10,
			  rowList: [10, 50, 100],
			  pager: '#pager_grdMsgQueue',
			  gridview: true,
			  ignoreCase: true,
			  rownumbers: true,
			  viewrecords: true,
			  sortorder: 'desc',
			  width : 1136,
			  shrinkToFit: false,
			  editurl: 'clientArray',
			  caption: ''
		});		      
		$("#grdMsgQueue").jqGrid('navGrid', '#pager_grdMsgQueue', {edit: false, add: false, del: false});
		$("#grdMsgQueue").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		
		$('#gview_grdMsgQueue').find('.ui-jqgrid-bdiv').attr("style","height: 420px !important");
		GridUtil.setWidthPercent("grdMsgQueue","100%");
	}
	
	function bindEvent(){
		$('#btnStart').on('click', function () {
			if(validateDate("txtTuNgay","Từ ngày ", true)){
				var msg = ajaxSvc.HL7_Gateway.letsPlayBaby($("#txtTuNgay").val());
				loadStatus();
				DlgUtil.showMsg(msg,undefined,5000);
			}
		});
		
		$("#btnStop").click(function(){
			var msg = ajaxSvc.HL7_Gateway.comeHomeBaby();
			loadStatus();
			DlgUtil.showMsg(msg,undefined,5000);
		});

		$('#btnStartAll').on('click', function () {
			var msg = ajaxSvc.HL7_Gateway.letsPlayBaby("");
			loadStatus();
			DlgUtil.showMsg(msg,undefined,5000);
		});

		$('#txtTuNgay').on('change', function () {
			loadMsgQueue();
		});
		
		$("#btnXem").click(function(){
			loadMsgQueue();
		});
		
		$('#toolbarIdbtnAuto').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H003_HL7_Automator");	
		});

		$('#toolbarIdbtnMaker').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H004_HL7_Msg_Maker");	
		});
		
		$('#toolbarIdbtnConfig').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H001_HL7_System_Config");	
		});

		$('#toolbarIdbtnLog').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H002_HL7_System_Log");	
		});
	}

	function validateDate(ctrId, key, checkNull){
		var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
		
		if(checkNull && ($('#' + ctrId).val() == null || $('#' + ctrId).val() == '')){
			DlgUtil.showMsg(key + $.i18n("require"));
			$('#' + ctrId).focus();
			return false;
		}
		
		if($('#' + ctrId).val().trim().length > 0 && !datetimeRegex.test($('#' + ctrId).val()) && !checkDate($('#' + ctrId).val())){
			DlgUtil.showMsg(key + $.i18n("date_type_invalid"));
			$('#' + ctrId).focus();
			return false;		
		}
		return true;
	}
	
	function loadStatus(){
		var status = ajaxSvc.HL7_Gateway.getSugarBaby();
		if(status=="sugar"){
			var partyDay = ajaxSvc.HL7_Gateway.getPartyDay();
			
			$("#lblStatus").html("RUNNING on "+partyDay);
		} else {
			$("#lblStatus").html("SLEEPING");
		}
	}
	
	function loadMsgQueue(){
		var param = RSUtil.buildParam("",[user_id,schema,province_id,hospital_id, 
			$("#txtTuNgay").val()]);

		GridUtil.loadGridBySqlPage("grdMsgQueue","CLS05H003.QUEUE",param);
	}
}
