
function ThongBaoBenhNhan(opt) {
    this.load=doLoad;
    var _opt = opt;

    function doLoad() {
        var _param = opt._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        _initControl();
    }

    function _initControl(){
        document.onwebkitfullscreenchange = fullscreenChanged;
        document.documentElement.onclick = goFullscreen;
        document.onkeydown = goFullscreen;

        $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K053_LCD_KIOSK_DKLAN&showMode=dlg");

        _loadData();
        setInterval(function(){
            _loadData();
        }, 3000);// set 3s
    }

    function formatNumber(str, num){
        var str1 = str + "";
        while(str1.length < num){
            str1 = "0" + str1;
        }
        return str1;
    }

    function _loadData(){
        var html = '';
        var obj = new Object();
        obj.PHONGID = opt._param[4];

        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('KIOSK.GETLCD',JSON.stringify(obj));				// ngt02k053_kb_lcdbm21
        if (data_ar != null && data_ar.length > 0) {
            var strTenQuay = "";
            $("#lblTENBENHVIEN").text(data_ar[0].TENBENHVIEN);
            strTenQuay = data_ar[0].SOQUAY == 'undefined' || data_ar[0].SOQUAY == null || data_ar[0].SOQUAY == 'null' ? "" : data_ar[0].SOQUAY;
            $("#lblTENQUAY").text(strTenQuay);
            html += '<tr style="height:95px;">';
            html += '<td style="width:30%;text-align: center;background-color:#005AAB "><span class="" style="color: #FFFF33">STT</span></td>';
            html += '<td style="width:70%;background-color:#005AAB"><span class="" style="color: #FFFF33">ĐỐI TƯỢNG</span></td>';
            html += '</tr>';
            for(var i = 0; i < data_ar.length; i++){
                    html += '<tr style="height:95px;">';
                    html += '<td style="width:30%;text-align: center;background-color:#005AAB "><span class="setNSLG">' + data_ar[i].STT + '</span></td>';
                    html += '<td style="width:70%;background-color:#005AAB"><span class="setNSLG ">' + data_ar[i].DOITUONG + '</span></td>';
                    html += '</tr>';
            }
        }
        $('#list').html(html);
    }

    function goFullscreen() {
        var isFirefox = typeof InstallTrigger !== 'undefined';
        mf = document.getElementById("main_frame");
        if(!isFirefox){
            mf.webkitRequestFullscreen();
        }else{
            mf.mozRequestFullScreen();
        }
    }

    function fullscreenChanged() {
        if (document.webkitFullscreenElement == null) {
            mf = document.getElementById("main_frame");
            mf.style.display="none";
        }
    }
}

