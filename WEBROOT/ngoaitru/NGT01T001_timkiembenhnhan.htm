
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">       
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<script src="../common/script/jquery/jquery.storageapi.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT01T001_timkiembenhnhan.js?v=20170525"></script>

<div class="container">
		<div class="col-xs-12 low-padding">
			<table id="grdDSBenhNhan"></table><div id="pager_grdDSBenhNhan"></div>						
		</div>   	  
       	<div class="col-xs-12 low-padding slim_solid_line" style="text-align: center;">
     		<button type="button" class="btn btn-sm btn-primary" id="btnLuu">
			      <span class="glyphicon glyphicon-ok"></span> Chọn
			</button>
			<button type="button" class="btn btn-sm btn-primary" id="btnHuy">
			      <span class="glyphicon glyphicon-remove-circle"></span> Hủy
			</button>
     	</div>
</div>
<script type="text/javascript">
	var uuid = '{uuid}';
	initRest(uuid,"/vnpthis");

	var mode = '{showMode}';	
	var data;
	
	var _opts=new Object();
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		_opts.TENBENHNHAN = data.TENBENHNHAN;		
		_opts.NGAYSINH = data.NGAYSINH;
		_opts.GIOITINHID = data.GIOITINHID;
	}
	
	var bn = new NGT01T001_timkiembenhnhan(_opts);
	bn.load(); 
</script>
