<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">    
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT02K021_DIEUDUONG_KHAMBENH.js?v=201809183"></script>
<script type="text/javascript" src="..//noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>

<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/xlsx.full.min.js"></script>
<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/ods.js"></script>
<script src="chrome://print/print_preview.js"></script>

<style>
<!--
	#toolbarId {
		height: 55px;
		padding: 0;
	}
	
	.cssbtn {
		height: 45px;
		border-radius: 2px;
		width: 140px;
	}
-->
</style>
<div class="container" id="divMain">
	<div class="" id="">
		<div class="col-xs-12 low-padding mgt5" style="display:none">
			<input id="hidTIEPNHANID" type="text"/>
			<input id="hidKHAMBENHID" type="text"/>
			<input id="hidBENHNHANID" type="text"/>
			<input id="hidHOSOBENHANID" type="text"/>
			<input id="hidPHONGKHAMDANGKYID" type="text"/>
			<input id="hidSTTKHAM" type="text"/>
			<input id="hidUUTIENKHAMID" type="text"/>
			<input id="hidTRANGTHAI_STT" type="text"/>
			<input id="hidTUOI" type="text"/>
			<input id="hidDONVI_TUOI" type="text"/>
			<input id="hidDOITUONGBENHNHANID" type="text"/>
			<input id="hidPHONGID" type="text"/>
			<input id="hidNGAYTIEPNHAN" type="text"/>
		</div>
		
		<div class="col-xs-12 low-padding">
			<div class="col-xs-4 low-padding">
				<div class="col-xs-3 low-padding mgt5">
					<label style = "height: 56px;line-height: 2;font-size: 23px">Mã BN</label>
				</div>
				<div class="col-xs-8 low-padding mgt5">
					<input class="form-control input-sm i-col-m_fl" style="width: 100%;height: 56px;font-size: 20px;" id="txtMABENHNHAN" name="txtMABENHNHAN" placeholder="Nhập mã BN" title="" maxlength="10">
				</div>
			</div>
			<div class="col-xs-8 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-3 low-padding mgt5">
							<label>Từ ngày</label>
						</div>
						<div class="col-xs-8 low-padding mgt5">
							<div class="input-group" style="width: 100%;">	
							  <input class="form-control input-sm" id="txtTUNGAY" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTUNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>							 
							</div>
						</div>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-3 low-padding mgt5">
							<label>Đến ngày</label>
						</div>
						<div class="col-xs-8 low-padding mgt5">
							<div class="input-group" style="width: 100%;">	
							  <input class="form-control input-sm" id="txtDENNGAY" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtDENNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>							 
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-8 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-2 low-padding mgt5">
								<label>Phòng khám</label>
							</div>
							<div class="col-xs-10 low-padding mgt5">
								<select class="form-control input-sm" id="cboPHONGKHAMID" style="width: 100%;">
								</select> 
							</div>
						</div>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-6 low-padding">
							<div class="col-xs-4 low-padding mgt5">
								<label>Trạng thái</label>
							</div>
							<div class="col-xs-8 low-padding mgt5">
								<select class="form-control input-sm" id="cboTRANGTHAIID" style="width: 100%;">
								</select> 
							</div>
						</div>
						<div class="col-xs-6 low-padding">
							<div class="col-xs-4 low-padding mgt5">
								<label class="mgl10">TT cấp số</label>
							</div>
							<div class="col-xs-8 low-padding mgt5">
								<select class="form-control input-sm" id="cboSTTKHAMID" style="width: 100%;">
									<option value="-1">Tất cả</option>
									<option value="0">Chưa cấp STT</option>
									<option value="1">Đã cấp STT</option>
								</select> 
							</div>
						</div>
					</div>
				</div>		
			</div>
		</div>
		
		<div class="col-xs-12 low-padding centered">
			<div id="toolbarId">
				<div class="col-xs-12 low-padding mgt3">
					<div class="col-xs-2 low-padding mgt3"></div>
					<div class="col-xs-10 low-padding mgt3">
						<button class="btn btn-sm btn-primary cssbtn" id="btnDSCHOKHAM">
						      <span class="glyphicon glyphicon-list"></span> 
						      MH 55 Inch
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnINLAISTT">
						      <span class="glyphicon glyphicon-print"></span> In lại phiếu
						</button>	
						<button class="btn btn-sm btn-primary cssbtn" id="btnSINHTON">
						      <span class="glyphicon glyphicon-tint"></span> Sinh tồn
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnDOTHILUC">
							<span class="glyphicon glyphicon-eye-open"></span> Đo thị lực
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnDOICONGKHAM">
						      <span class="glyphicon glyphicon-new-window"></span> Đổi PK/YCK
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnTRABN">
						      <span class="glyphicon glyphicon-cog"></span> Trả BN
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnCAPSTT">
						      <span class="glyphicon glyphicon-plus"></span> Cấp STT
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnMANVPI55">
						      <span class="glyphicon glyphicon-plus"></span> Màn 55inch VPI
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnMANTHUOC55">
						      <span class="glyphicon glyphicon-plus"></span> Màn 55inch Thuốc
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnMANXN55">
						      <span class="glyphicon glyphicon-plus"></span> Màn 55inch XN
						</button>
						<button class="btn btn-sm btn-primary cssbtn" id="btnMANCDHA55">
						      <span class="glyphicon glyphicon-plus"></span> Màn 55inch CDHA
						</button>

					</div>
				</div>
			</div>
		</div>
		
		<div style="display:none;">
		      <label>Choose voice</label>
		      <select id="voices"></select>
	    </div>
	    
		<div class="col-xs-12 low-padding">
			<div class="">
				<table id="grdDSBENHNHAN" class="table table-striped jambo_table bulk_action"></table>
				<div id="pager_grdDSBENHNHAN"></div>
			</div>
		</div>
	</div>
</div>
<script>
	var paramInfo = CommonUtil.decode('{paramData}');
	
	
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var url ='{url}';
	var db_schema='{db_schema}';
	var session_par=[];
	var khoaId = '{dept_id}';
	//initRest(uuid,"/vnpthis");
	initRest(uuid);	
	
	var path = "../common/image/";
	var _opts={
		lang: lang,
		_param:session_par,
		_uuid:uuid,
		_hospital_id: hospital_id, 
		db_schema: db_schema,
		khoaid : khoaId,
		imgPath : [path + "metacontact_away.png", path + "Circle_Yellow.png", path + "Circle_Green.png", path + "Flag_Red_New.png",path + "True.png"],
	}
	
    var data;
	var tba = new CAPSO_DIEUDUONG(_opts);
	tba.load();	
</script>

<script>
	$(function(){	
	  if ('speechSynthesis' in window) {
		    speechSynthesis.onvoiceschanged = function() {
		      var $voicelist = $('#voices');
		    }
	  } else {
	    $('#modal1').openModal();
	  }
	});
</script>