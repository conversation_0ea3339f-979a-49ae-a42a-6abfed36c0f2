function PhieuKhamVaTheoDoi(_opts) {
	var _khambenhid = _opts._khambenhid;
	var _hosobenhanid = _opts._hosobenhanid;
	var _user_id=-1;
	var _hospital_id;
	var validator = null;
	this.load=doLoad;

	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		var _param = _opts._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_initControl();
		_bindEvent();

	}
	function _initControl(){

		var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU');
		if(_cauhinh != '0' && typeof _cauhinh !== "undefined") {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
		}
	};

	function _bindEvent() {
		$("#btnIn").on("click", function(e) {
			var par = [{
				name : 'khambenhid',
				type : 'String',
				value : _khambenhid
			},{
				name : 'hosobenhanid',
				type : 'String',
				value : _hosobenhanid
			}];

			openReport('window', "NGT02_PHIEUKHAM_THEODOIBN", "pdf", par);
		});

		$("#btnKyCa").on("click", function(e) {
			//TODO
		});

		$("#btnHuyCa").on("click", function(e) {
			//TODO
		});

		function _caRpt(signType) {
			//TODO
		}

		$("#btnClose").on("click", function(e) {
			parent.DlgUtil.close("divDlgPCKT");
		});
	}
}
