/*
FeatureId:NGT02K029_YeuCauMoBenhAn
Creator: ManhNV
CreateDate: 20/09/2016
Desc: <PERSON><PERSON><PERSON> cầu mở bệnh án
*/
function MOLAIBENHAN(opt) {		
	this.load=doLoad;
	var _opt = opt;
	
	function doLoad() {	
		_initControl();
		_bindEvent();
	}
	
	function _initControl(){
		var sql_par=[];
		sql_par.push({"name":"[0]","value":_opt.phongid});
		ComboUtil.getComboTag("cboNGUOIYEUCAU","NGT.BS",sql_par,"","",'sql','', "");
		
		if(_opt.kieu == 0){
			$("#cboNGUOIYEUCAU").attr("disabled", true);
			$("#cboLYDOMO").css("display", 'none');
		}else{
			$('#txtNOIDUNG').val($('#cboLYDOMO option:selected').text());
		}
		
		var sql_par=[];
		sql_par.push({"name":"[0]","value": _opt.tiepnhanid});
		sql_par.push({"name":"[1]","value": 0});
		sql_par.push({"name":"[2]","value": _opt.phongid});
		if(_opt.kieu != 2){ // == 2 la xu ly cho noi tru
			var result = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT.TTMOBA", sql_par);
			result = JSON.parse(result);		
			if (result != null && result.length > 0){
				result = result[0];
				$('#txtNOIDUNG').val(FormUtil.unescape(result.NOIDUNG));
				$('#hidYEUCAUMOBENHANID').val(result.YEUCAUMOBENHANID);			
			}
		}
	}
	
	function _bindEvent() {			
		$("#btnHuy").on("click",function(e){
			parent.DlgUtil.close("dlgMOLAIBA");
		});
		
		$('#cboLYDOMO').change(function(){
			$('#txtNOIDUNG').val($('#cboLYDOMO option:selected').text());
		});
		
		$("#btnLuu").on("click",function(e){
			
			if($('#txtNOIDUNG').val().trim() == ""){
				DlgUtil.showMsg('Phải nhập nội dung yêu cầu mở');
				return;
			}
			
			var obj = new Object();
			
			if(_opt.kieu == 0){
				obj = $.extend({noidung:FormUtil.escape($('#txtNOIDUNG').val().trim()), kieu : 0},_opt);
			}else{
				obj = $.extend(
						{
							noidung:FormUtil.escape($('#txtNOIDUNG').val().trim()),  
							kieu : 1,
							yeucaumobenhanid : $('#hidYEUCAUMOBENHANID').val()
						},
						_opt);
			}
			
			obj.mode = "1";				// ngoai tru ; 
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.MOBA",JSON.stringify(obj));
			
			if(ret == '-100'){
				DlgUtil.showMsg('Bệnh nhân chưa có yêu cầu mở, hãy gửi yêu cầu mở trước khi mở bệnh án.');
			}else if(ret == '-200'){
				DlgUtil.showMsg('Bệnh nhân đã gửi yêu cầu mở nhưng chưa mở bệnh án, hãy kiểm tra lại');
			}else if(ret == '-500'){
				DlgUtil.showMsg('Bệnh nhân đã nhập khoa bên nội trú không mở được bệnh án');
			}else if(ret == '-600'){
				DlgUtil.showMsg('Yêu cầu đã được mở, hãy gửi yêu cầu mở khác');
			}else if(ret == '-700'){
				DlgUtil.showMsg('Bệnh nhân đã kết thúc điều trị nội/ngoại trú không mở được bệnh án.');
			}else if(ret == '-400'){
				DlgUtil.showMsg('Đã duyệt kế toán/bảo hiểm ko thể mở lại bệnh án');
			}else if(ret == '-301'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện. ');
			}else if(ret == '-302'){
				DlgUtil.showMsg('Chưa hoàn thành dịch vụ CLS. ');
			}else if(ret == '-800'){
				DlgUtil.showMsg('Bệnh nhân có hoàn ứng không được phép mở bệnh án. ');
			}else if(ret == '-900'){
				DlgUtil.showMsg('Bệnh nhân đã trả không khám, không thể thao tác. ');
			}else{
				EventUtil.raiseEvent("assignSevice_mobenhan",{msg:ret});
			}
		});
	}
}