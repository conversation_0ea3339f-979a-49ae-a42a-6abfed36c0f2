<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../canlamsang/RISConnector.js"></script>
<script type="text/javascript" src="../canlamsang/CLS02C022_DanhSachHoiChan.js?v=20191122"></script>
<script type="text/javascript" src="../noitru/NTU02D032_ThongTinHoiChuan.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>
<script src="../common/script/common.js"></script>

<div id="divMain">		
	<ul id="tabs" class="nav nav-tabs" data-tabs="tabs">
		<li id="tabHoiChuan"><a href="#tcHoiChuan" id="idTabHoiChan" data-toggle="tab" class="active">Hội chẩn</a></li>
	</ul>
	
	<div id="my-tab-content" class="tab-content">	
		<div id="tcHoiChuan" class="tab-pane active" style="width:100%"></div>
	</div>	<!-- End my-tab-content  -->
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hidUserID" value="-1" />

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang = "vn";
	var type = "{type}";
	var idmaubenhpham = '{maubenhphamid}';
	var iddichvukb = '{iddichvukb}';
	var mode = '{showMode}';
	
	console.log('user_id='+user_id+', schema='+schema+', province_id='+province_id+', hospital_id='+hospital_id+', type='+type);
	
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	var _opts=new Object();	
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);	
		data=EventUtil.getVar("dlgVar");
		_opts._idmaubenhpham=data.maubenhphamid;
		
	}
	
	initRest(_opts._uuid);
	
	
	var bb = new BBHC(_opts);
	bb.load();
</script>