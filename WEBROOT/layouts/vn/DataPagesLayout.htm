<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>VNPT-HIS</title>
    <script type="text/javascript">
        window.uiVersion = '{UI_VERSION}';
    </script>
    <script type="text/javascript" src="{ROOT}/common/script/jquery/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/bootstrap/bootstrap-tab.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/bootstrap/bootstrap-button.js"></script>
    <link rel="stylesheet" href="{ROOT}/common/script/bootstrap/css/bootstrap.min.css">
    <script type="text/javascript" src="{ROOT}/common/script/jquery/toggles.min.js"></script>
    <link rel="stylesheet" href="{ROOT}/common/css/font-awesome.min.css">
    <link rel="stylesheet" href="{ROOT}/common/css/css_style.css">
    <link rel="stylesheet" href="{ROOT}/common/css/common.css">
    <link rel="stylesheet" href="{ROOT}/common/css/custom.css">
    <script type="text/javascript" src="{ROOT}/common/script/UIUtil.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/main.js"></script>
    {style_by_version}
    <link rel="stylesheet" href="{ROOT}/common/script/jquery.combogrid/jquery.ui.combogrid.css" type="text/css" media="screen"/>
    <!--
    <script src="{ROOT}/common/script/jquery.combogrid/jquery-1.9.1.min.js" type="text/javascript"></script>
    -->
    <link rel="stylesheet" href="{ROOT}/common/script/jquery-ui-1.12.1/jquery-ui.css" type="text/css" media="screen"/>
    <script src="{ROOT}/common/script/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>
    <link rel="stylesheet" href="{ROOT}/common/script/datetimepicker/jquery-ui-timepicker-addon.css" type="text/css" media="all"/>
    <script src="{ROOT}/common/script/datetimepicker/jquery-ui-timepicker-addon.js" type="text/javascript"></script>
    <script src="{ROOT}/common/script/datetimepicker/i18n/jquery-ui-timepicker-vi.js" type="text/javascript"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
    <script src="{ROOT}/common/script/jquery.combogrid/jquery.ui.combogrid-1.6.3.js" type="text/javascript"></script>
    <link rel="stylesheet" href="{ROOT}/common/css/banner.css">
    <link rel="stylesheet" href="{ROOT}/common/css/responsive.css"/>
    <script src="{ROOT}/common/script/mobile/jquery.mmenu.min.all.js" type="text/javascript"></script>
    <script src="{ROOT}/common/script/mobile/mobile-detect.min.js" type="text/javascript"></script>
    <link rel="stylesheet" href="{ROOT}/common/script/mobile/jquery.mmenu.all.css"/>
    <script type="text/javascript" src="{ROOT}/common/script/menu/menubar.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/CryptoJS/rollups/aes.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/CryptoJS/rollups/pbkdf2.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/CryptoJS/AesUtil.js"></script>
    <link rel="stylesheet" href="{ROOT}/common/script/alertify/themes/alertify.core.css"/>
    <link rel="stylesheet" href="{ROOT}/common/script/alertify/themes/alertify.default.css" id="toggleCSS"/>
    <script type="text/javascript" src="{ROOT}/common/script/alertify/alertify.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/mousetrap.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jquery/jquery.binarytransport.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/CommonUtil.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/BrowserUtil.js"></script>
    <script type="text/javascript" src="../common/vienphi/vienphi_tinhtien.js?version=20190625"></script>
    <script type="text/javascript" src="{ROOT}/common/script/moment.min.js"></script>
    <link rel="stylesheet" href="{ROOT}/common/script/dialog/jBox.css">
    <script type="text/javascript" src="{ROOT}/common/script/dialog/jBox.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/xlsx/xlsx.full.min.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jszip/FileSaver.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jquery/jquery.toaster.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jquery/jquery.mask-plugin.min.js"></script>
	<link href="{ROOT}/common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
    <script src="{ROOT}/common/script/jqgrid/js/jquery.jqGrid.min.js" type="text/javascript"></script>
    <script src="{ROOT}/common/script/tree/jquery.fancytrees.js" type="text/javascript"></script>
    <script src="{ROOT}/common/script/tree/jquery.fancytree.filter.js" type="text/javascript"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jquery.priceformat.2.0.min.js"></script>
    <link href="{ROOT}/common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
    <script type="text/javascript" src="{ROOT}/common/script/jquery.i18n/jquery.i18n.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
    <script type="text/javascript" src="{ROOT}/common/i18n/i18n-his.js"></script>
    <script src="{ROOT}/common/script/jqgrid/js/i18n/grid.locale-vi.js" type="text/javascript"></script>
    <script type="text/javascript" src="{ROOT}/common/script/RestService.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/dbForm.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/dbToolbar.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/DataValidator.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/xss.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/CaUtils.js"></script>
<!--    <script type="text/javascript" src="{ROOT}/common/script/tui-grid/tui-grid.min.js"></script>-->
<!--    <link rel="stylesheet" href="{ROOT}/common/script/tui-grid/tui-grid.min.css" type="text/css" media="screen">-->

    <script type="text/javascript">
        $(document).ready(function(){
            let HIS_SMARTUX_ENABLE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SMARTUX_ENABLE');
            if (HIS_SMARTUX_ENABLE == 1) {
                window.VNPT = window.VNPT || {};
                window.VNPT.q = window.VNPT.q || [];

                window.VNPT.app_key = '83c4835cb2fb29fb7493040047d7f13ed59a8af8';

                window.VNPT.url = 'https://console-smartux.vnpt.vn';

                window.VNPT.q.push(['track_sessions']);
                window.VNPT.q.push(['track_pageview']);
                window.VNPT.q.push(['track_clicks']);
                window.VNPT.q.push(['track_scrolls']);
                window.VNPT.q.push(['track_errors']);
                window.VNPT.q.push(['track_links']);
                window.VNPT.q.push(['track_forms']);
                window.VNPT.q.push(['collect_from_forms']);
                (function () {
                    const paths = ['https://console-smartux.vnpt.vn/sdk/web/core-track.js', 'https://console-smartux.vnpt.vn/sdk/web/minify.min.js'];
                    for (let i in paths) {
                        var cly = document.createElement('script'); cly.type = 'text/javascript';
                        cly.async = true;
                        cly.src = paths[i];
                        cly.onload = i == 0 ?
                            function () {
                                setTimeout(function(){
                                    window.VNPT.init();
                                }, 100);
                            }
                            :
                            function() {
                                window.minify = require("html-minifier").minify;
                            };
                        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(cly, s);
                    }
                })();
            }
        })
    </script>
    <script>
        // $(document).ready(function () {
        //     let HIS_CHATBOT_ENABLE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_CHATBOT_ENABLE');
        //     if (HIS_CHATBOT_ENABLE == 1) {
        //         function create_UUID() {
        //             var sender = localStorage.getItem("sb_sender_name");
        //             var uuid = "";
        //             var dt = new Date().getTime();
        //             if (sender) {
        //                 uuid = sender;
        //             } else {
        //                 uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        //                     var r = (dt + Math.random() * 16) % 16 | 0;
        //                     dt = Math.floor(dt / 16);
        //                     return (c == "x" ? r : (r & 0x3 | 0x8)).toString(16);
        //                 });
        //                 try {
        //                     localStorage.setItem("sb_sender_name", uuid);
        //                 } catch (err) {
        //                     console.log("error", err);
        //                 }
        //             }
        //             return uuid;
        //         }
        //
        //         let __protocol = document.location.protocol;
        //         let __baseUrl = __protocol + "//livechat.vnpt.vn";
        //         let prefixNameLiveChat = "";
        //         let objPreDefineLiveChat = {
        //                 appCode: 'dbba3dd0-c853-11ed-83b1-2fedaf740d68',
        //                 themes: "",
        //                 appName: {
        //                     line1: 'VNPT HOSPITAL BOT',
        //                     line2: ''
        //                 },
        //                 thumb: '',
        //                 icon_bot: 'https://livechat.vnpt.vn/Chatbot.svg',
        //                 senderName: create_UUID(),
        //                 styles: {
        //                     head: {
        //                         bgColor: '#53c088',
        //                         text: {
        //                             line: 1,
        //                             line1: {
        //                                 color: '#fff',
        //                                 fontSize: '16px',
        //                                 fontWeight: 700
        //                             },
        //                             line2: {
        //                                 color: '#fff',
        //                                 fontSize: '16px',
        //                                 fontWeight: 700
        //                             }
        //                         }
        //                     },
        //                     border: {},
        //                     floatButton: {
        //                         bgColor: '#53c088',
        //                         width: '50px',
        //                         height: '50px',
        //                         img: {
        //                             height: '40px',
        //                             width: '40px'
        //                         }
        //                     },
        //                     chat: {
        //                         reply: {
        //                             bg: '#EEEEEE',
        //                             fontSize: '13px'
        //                         },
        //                         answer: {
        //                             bg: '#0782e0',
        //                             fontSize: '13px'
        //                         },
        //                         botIcon: 'https://i.ibb.co/ZJgtPhr/Chatbot.png'
        //                     }
        //                 }
        //             },
        //             appCodeHash = window.location.hash.substr(1);
        //         if (appCodeHash.length == 32) {
        //             objPreDefineLiveChat.appCode = +appCodeHash;
        //         }
        //         let vnpt_ai_livechat_script = document.createElement("script");
        //         vnpt_ai_livechat_script.id = "vnpt_ai_livechat_script";
        //         vnpt_ai_livechat_script.src = __baseUrl + "/vnpt_smartbot.js";
        //         document.body.appendChild(vnpt_ai_livechat_script);
        //         let vnpt_ai_livechat_stylesheet = document.createElement("link");
        //         vnpt_ai_livechat_stylesheet.id = "vnpt_ai_livechat_script";
        //         vnpt_ai_livechat_stylesheet.rel = "stylesheet";
        //         vnpt_ai_livechat_stylesheet.href = __baseUrl + "/vnpt_smartbot.css";
        //         document.body.appendChild(vnpt_ai_livechat_stylesheet);
        //         vnpt_ai_livechat_script.onload = function () {
        //             vnpt_ai_render_chatbox(objPreDefineLiveChat, __baseUrl, "livechat.vnpt.ai:443")
        //         }
        //     }
        // });
    </script>
    <style type="text/css">
        #dialog {
            display: none;
            right: 30px;
            bottom: 35px;
            width: 300px;
            height: auto;
            position: fixed;
            z-index: 100;
            background: #c6e2ec;
            padding: 2px;
            font: 10pt tahoma;
            border: 1px solid gray;
            border-radius: 5px;
            max-height: 500px
        }
    </style>

    <script type="text/javascript">
        var lang = '{lang}';
        if (lang == 'en') {
            document.title = "VNPT-HIS";
        }
        var RestInfo = {
            base_url: '{URL_REST}',
            uuid: '{UUID}',
            his_token: '{his_token}'
        };
        var ssid = '{SSID}';
        localStorage.setItem("ssid", '{SSID}')
        CommonUtil.initHelp('help_I_1_Huongdanchung');
        var menuObject = new CMenuMgr("menuObj");
        var _menuData = MENU_OBJECT;

        if (_menuData.length > 0) {
            console.log('load from object array=' + _menuData.length);
            _menuData = _menuData.filter(function (menu) {
                return menu.text != 'Popup';
            });
            _menuData = _menuData.filter(function (menu) {
                return menu.text != 'Dược popup UI';
            });
            menuObject.loadMenu(_menuData);
            var section = $('section').first();
            var height = section.height();
            section.next().find('div').first().removeAttr('style').attr('style', 'height: ' + height + 'px');
            $('#divMenuCus').removeAttr('style').attr('style', 'height: ' + height + 'px');
            console.log('section.' + height);
        } else {
            console.log('load from file');
            menuObject.loadMenu("{ROOT}/common/script/menu/UserMenuFile.js");
        }

        //CommonUtil.disableDebug();
        if (sessionStorage.logged) {
            console.log('sessionStorage.oldTabID=' + sessionStorage.oldTabID + ' sessionStorage.newTabID=' + sessionStorage.newTabID);
            if (sessionStorage.oldTabID == sessionStorage.newTabID) {
                console.log('OK');
            } else {
                console.log('not OK' + ' sessionStorage.oldTabID=' + sessionStorage.oldTabID + ' sessionStorage.newTabID=' + sessionStorage.newTabID);
                //window.location.href ="{ROOT}/login/login.jsp?invalid=1";
            }
        } else {
            //alert('sessionStorage.logged='+sessionStorage.logged);
        }
        $(document).ready(function () {
// 	var heigh_window = $( window ).height() - 35;
//     $("#divMain").css('min-height', heigh_window + "px");

            height_window = $(window).height();   // returns height of browser viewport
            height_document = $(document).height();
            height_divMain = $("#divMain").height();
            console.log('height_window:' + height_window);
            console.log('height_document:' + height_document);
            console.log('height_divMain:' + height_divMain);

            if (height_window < height_document) {
                $("#divMain").css('height', height_document + 40);
                $("#hidDocumentHeight").val(height_document + 40);
            } else if (height_window > height_document) {
                $("#divMain").css('height', height_window - 35);
                $("#hidDocumentHeight").val(height_window - 35);
            } else {
                if (height_divMain + 70 <= height_window) {
                    $("#divMain").css('height', height_window - 40);
                    $("#hidDocumentHeight").val(height_window - 40);
                } else {
                    $("#divMain").css('height', height_window);
                    $("#hidDocumentHeight").val(height_window);
                }
            }

            // $(".dropDownMenu>li:last-child>ul>li>ul").parent().hover(function () {
            //     if ($(this).find("ul > li").length > 0) {
            //         let width = $($(this).find("ul li")[0]).width();
            //         $(this).find("ul").css({
            //             position: "absolute",
            //             left: -width,
            //             width: width,
            //             top: 0
            //         })
            //     }
            // }, function () {
            // });

            $("#101").click(function () {
                let _hlink = "/vnpthis/servlet/login.Logout";
                sessionStorage.removeItem("hisl2_smartca");
                window.location.href = _hlink;
            });

            let HIS_SIGN_SMART_CA_METHOD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SIGN_SMART_CA_METHOD');
            if (HIS_SIGN_SMART_CA_METHOD == 1) {
                $("#smartcaicon").show();
            }

            $("#smartcaicon").click(function () {
                let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                let _height = 650;
                if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
                    _height = 400;
                }
                DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: false}, "Smart Ca Login", 500, _height);
                DlgUtil.open("dlgCA_SMARTCA_LOGIN");
            });

            EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close", function (e) {
                DlgUtil.close("dlgCA_SMARTCA_LOGIN");
                if (e.data && e.data.token && e.data.token.access_token) {
                    alertify.success('Đăng nhập thành công tài khoản Smart CA: ' + e.data.user.uid, 5000);
                } else {
                    alertify.error('Đăng xuất thành công Smart CA', 5000);
                }
            });

            $(window).click(function () {
                $(".combogrid").hide()
            });

            let isPwdCensoringNotified = sessionStorage.getItem("isPwdCensNtfd:" + user_name);
            if (!isPwdCensoringNotified) {
                try {
                    let sysCode = JSON.parse(jsonrpc.AjaxJson.getVariable([{name:'syscode'}])).result["syscode"];
                    if (sysCode != null && sysCode.length > 0 && sysCode.indexOf("VALID_PWD:") === 0) {
                        sysCode = sysCode.substring(10);
                        let message = jsonrpc.AjaxJson.ajaxCALL_SP_S("KEY_CENS_MSG", sysCode + "$");
                        if (message) {
                            alertify.log(message, "warning", 10000);
                            sessionStorage.setItem("isPwdCensNtfd:" + user_name, "1");
                        }
                    }
                } catch (e) {
                }
            }

            setContentFooter();
        });
    </script>


</head>
<div id="smartcaicon">
    <img src="../common/image/smartca.svg" alt="">
</div>
<style>
    #smartcaicon {
        display: none;
        position: fixed;
        left: 10px;
        bottom: 45px;
        cursor: pointer;
    }

    /* width */
    #menuObj ul.dropDownMenu > li > ul > li > ul::-webkit-scrollbar,
    #menuObj ul.dropDownMenu > li > ul::-webkit-scrollbar {
        width: 2px;
    }

    /* Track */
    #menuObj ul.dropDownMenu > li > ul > li > ul::-webkit-scrollbar-track,
    #menuObj ul.dropDownMenu > li > ul::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    /* Handle */
    #menuObj ul.dropDownMenu > li > ul > li > ul::-webkit-scrollbar-thumb,
    #menuObj ul.dropDownMenu > li > ul::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    #menuObj ul.dropDownMenu > li > ul > li > ul::-webkit-scrollbar-thumb:hover,
    #menuObj ul.dropDownMenu > li > ul::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>
<div class="icon_menu" style="display:none;"></div>
<section class="sec-all blue-color">
    <div class="container banner {lang}" style="z-index: 900">
        <!-- <div id="language-changer" class="pointer language-changer {lang}" onclick="changeLanguage('{lang}')"></div> -->
        <div class="blue-menu">
            <div id="headerMenu">
                <div class="block_menu">
                    <div class="menu" id="menuObj">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<body id="{ID_BODY}">
<div class="header-fixedTop--mmMenu">
    <a href="#mmenu"><span class="glyphicon glyphicon-align-justify"></span></a>
    <span>VNPT HIS</span>
</div>
<div class="page-wrap">
    <div id="divMenuCus"></div>
    {content}
</div>
<style>
    .header-fixedTop--mmMenu, .content {
        text-align: center;
    }
    .header-fixedTop--mmMenu {
        background: #004f9e;
        font-size: calc(1vh + 1vw);
        font-weight: bold;
        color: #fff;
        line-height: 40px;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        width: 100%;
        height: calc(2vh + 2vw);
        padding: calc(0.5vh/2 + 0.5vw/2);
        margin: auto;
        position: sticky;
        top: 0;
    }
    .header-fixedTop--mmMenu>span {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .header-fixedTop--mmMenu.fixed {
        position: fixed;
        top: 0;
        left: 0;
    }
    .header-fixedTop--mmMenu span.glyphicon {
        color: white;
        top: 50%;
        transform: translate(-50%, -50%);
        left: 1vh;
    }
    .header-fixedTop--mmMenu a {
        display: block;
        width: calc(2vh + 2vw);
        height: calc(2vh + 2vw);
        position: absolute;
        top: 0;
        left: 0;
    }
    .content {
        padding: 150px 50px 50px 50px;
    }

    #mmenu a[data-hlink="/vnpthis/servlet/login.Logout"] {
        color: red !important;
    }

    #mmenu .mm-search > input {
        font-size: calc(1.5vh + 1.5vw);
        height: calc(2.5vh + 2.5vw);
        padding: calc(0.5vh + 0.5vw) calc(1vh + 1vw);
    }

    .mm-menu.mm-hassearch > .mm-panel {
        padding-top: calc(5vh + 5vw);
    }

    #mmenu .mm-search {
        padding: calc(0.5vh + 0.5vw);
    }

    #mmenu em.mm-counter {
        font-size: calc(1vh + 1vw);
    }

    #mmenu a, #mmenu span {
        font-size: calc(1.5vh + 1.5vw);
        padding: 2vh 1vh;
    }

    #mmenu {
        max-width: 600px;
    }

    #mmenu .mm-list a.mm-subclose:before {
        margin-bottom: calc(-.5vh) !important;
        left: 17px !important;
    }
</style>
<div id="divHidden" style="display:none">

</div>
<div class="clear"></div>
<nav id="mmenu" class="hidden">
</nav>
<footer class="sec-all footer site-footer">
    <div class="container">
        <!-- <div class="mgt10 footer-left {lang}">
            <div class="col-xs-5 low-padding">Người dùng: {user_name}&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp; {full_name}</div>
            <div class="col-xs-5 low-padding">Khoa: {dept_name}&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;Phòng: {subdept_name}</div>
            <div class="col-xs-2 low-padding">Nhấn F2 để xem hướng dẫn</div>
            <input type="hidden" id="hidDocumentHeight" value=""/>
            style="color: #00FF00;"
        </div> -->

        <!-- <div class="mgt10 {lang}">
            <div class="col-xs-5 low-padding" style="margin-left: -90px;"><label style="font-size: 13px !important; margin-bottom: 0px !important; color: #00FF00;">Người dùng :&nbsp;</label> {user_name}&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp; {full_name}</div>
            <div class="col-xs-5 low-padding" style="margin-left: -140px;"><label style="font-size: 13px !important; margin-bottom: 0px !important; color: #00FF00;">Khoa :&nbsp;</label> {dept_name}&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;<label style="font-size: 13px !important; margin-bottom: 0px !important; color: #00FF00;">Phòng :&nbsp;</label> {subdept_name}</div>
            <div class="col-xs-2 low-padding" style="">Nhấn F2 để xem hướng dẫn</div>
            <div class="col-xs-2 low-padding" style="margin-left: 30px; margin-top: -5px; width: 200px;"><marquee id="manhinhThongbao"></marquee></div>
            <input type="hidden" id="hidDocumentHeight" value=""/>
        </div> -->
        <div class="{lang}" id="footer-container">
            <div class="col-xs-10 low-padding">
                <label style="color: rgb(0, 255, 0); font-size: 13px; --darkreader-inline-color: #1aff1a;">Người dùng:&nbsp;</label><span id="footer-content-fullname">--------------------</span>&nbsp;-&nbsp;<span id="footer-content-username">----------</span>&nbsp;&nbsp;&nbsp;
                <label style="color: rgb(0, 255, 0); font-size: 13px; --darkreader-inline-color: #1aff1a;">Khoa:&nbsp;</label><span id="footer-content-deptname">----------</span>&nbsp;-&nbsp;
                <label style="color: rgb(0, 255, 0); font-size: 13px; --darkreader-inline-color: #1aff1a;">Phòng:&nbsp;</label><span id="footer-content-subdeptname">----------</span>
            </div>
            <div class="col-xs-5 low-padding" style="display:none"><input type="hidden" id="txtGLBDEPTID" value="{dept_id}"> </input>
                <input type="hidden" id="txtGLBSUBDEPTID" value="{subdept_id}"></div>
            <div class="col-xs-2 low-padding tthd" id="XXXX">Nhấn F2 để xem hướng dẫn</div>
            <div class="col-xs-2 low-padding" id="btnThongbao"><a id="txtThongbao" style="color: yellow"> Thông báo</a>
            </div>
            <div id="dialog">
                <div id="list_doc">
                    <div style="color: #004f9e; text-align: center; font-weight: bold;">Thông báo</div>
                    <div style="color: #004f9e;text-decoration: underline;text-align: right;"><a id="updateData">Cập nhật dữ liệu</a></div>
                    <li><a href="#" style="margin: 10px; background: #fff; min-height: 50px; font-style: italic;border-radius: 5px">
                        <!-- 					<span id="manhinhThongbao" style="color: #000;"></span> -->
                        <textarea style="color: #120fe8;width: 100%" id="manhinhThongbao" rows="20" disabled></textarea>
                    </a></li>
                </div>
            </div>
            <input type="hidden" id="hidDocumentHeight" value=""/>
        </div>
        <script>
            //START Man hinh Thong bao
            var uuid = '{uuid}';
            var userName = '{user_name}';
            var userInfo = {
                "USER_NAME": "{user_name}",
                "FULL_NAME": `{full_name}`,
                "deptName": `{dept_name}`,
                "subdeptName": `{subdept_name}`,
                "deptId": "{dept_id}",
                "subdeptId": "{subdept_id}"
            }
            if (localStorage.getItem("USER_INFO_OBJ")) {
                try {
                    var uInfo = JSON.parse(localStorage.getItem("USER_INFO_OBJ"));
                    if (uInfo && uInfo['USER_NAME'] && uInfo['USER_NAME'].length > 0 && uInfo['USER_NAME'] == userName) {
                    } else {
                        localStorage.setItem("USER_INFO_OBJ", JSON.stringify(userInfo));
                        localStorage.setItem(userName + "_MENU_OBJ", JSON.stringify(_menuData));
                    }
                } catch (e) {
                    localStorage.setItem("USER_INFO_OBJ", JSON.stringify(userInfo));
                    localStorage.setItem(userName + "_MENU_OBJ", JSON.stringify(_menuData));
                    console.log(e);
                }
            } else {
                localStorage.setItem("USER_INFO_OBJ", JSON.stringify(userInfo));
                localStorage.setItem(userName + "_MENU_OBJ", JSON.stringify(_menuData));
            }
            localStorage.setItem("his-token", RestInfo.his_token);
            initRest(uuid, "/vnpthis");
            let confF2Hidden = '{CONF_F2}';
            if (confF2Hidden != "1") {
                $("#btnTest").click(function () {
                    window.open('/vnpthis/test/test.jsp');
                });
            } else {
                $("#btnTest").hide();
            }

            $("#btnThongbao").click(function () {
                showPopUp('dialog');
            });
            $(".page-wrap").click(function () {
                closePopUp('dialog');
            });

            $("#updateData").click(function () {
                var text = tinhtien();
                $('#manhinhThongbao').text(text);
            });

            var data_DSCH = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', 'CH_THONGBAO_HIENTHI;CH_THONGBAO_NAM');
            //var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC70.04",-1);

            //var isShow = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CH_THONGBAO_HIENTHI');

            if (data_DSCH != null && data_DSCH.length > 0) {
                if (data_DSCH[0].CH_THONGBAO_HIENTHI == '1') {
                    $("#btnThongbao").attr("visibility", "visible");
                } else {
                    $('#btnThongbao').hide();
                }
            }

            function setContentFooter() {
                $("#footer-content-fullname").text("{full_name}");
                $("#footer-content-username").text("{user_name}");
                $("#footer-content-deptname").text("{dept_name}");
                $("#footer-content-subdeptname").text("{subdept_name}");
            }

            function showPopUp(el) {
                var dlg = document.getElementById(el);

                //var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC70.04",-1);
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', 'CH_THONGBAO_HIENTHI;CH_THONGBAO_NAM');

                if (data_DSCH != null && data_DSCH.length > 0) {
                    if (data_DSCH[0].CH_THONGBAO_HIENTHI == 0) {
                        $('#dialog').hide();
                        $('#btnThongbao').hide();
                    } else {
                        //$('#manhinhThongbao').text(row.THONG_BAO);
                        var text = tinhtien();
                        $('#manhinhThongbao').text(text);
                    }
                } else {
                    $('#btnThongbao').hide();
                }

                if (dlg.offsetWidth == '0') {
                    dlg.style.display = "block";
                    var myElement = document.getElementById('list_doc');
                    var topPos = myElement.offsetTop;
                    document.getElementById('dialog').scrollTop = topPos;

                } else {
                    dlg.style.display = "none";
                }
            }

            function tinhtien() {
                var text = '';
                var dutoan_nam = '0';
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC115.01");
                if (data_ar != null && data_ar.length > 0) {
                    var row = data_ar[0];
                    dutoan_nam = row.T_DUTOAN + '.00';
                }

                var _tu_ngay = '01/01/' + new Date().getFullYear() + ' 00:00:00';
                var _den_ngay = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
                var _loai = '-1';
                var _tuyen = '-1';
                var _mathe = '-1';
                var _loaithe = '-1';
                var _typeDate = '1';
                var ds_ba = '';
                var ma_lks = '-1';
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("TONGHOP.4210", _typeDate + "$" + _tu_ngay + "$" + _den_ngay + "$" + _loai + "$" + _tuyen + "$" + _mathe + "$" + _loaithe + "$" + ds_ba + "$" +
                    ma_lks, []);
                if (data_ar != null && data_ar.length > 0) {
                    _t_bhtt = data_ar[0].T_BHTT;
                    var _t_sudung = stringToNumber(dutoan_nam) - stringToNumber(_t_bhtt);
                    var dutoan_conlai = numberToString(vienphi_tinhtien.convertNumToCurrency(_t_sudung, true));
                    var date_dukien = tinhngay(dutoan_nam, _t_bhtt);
                    if (data_DSCH != null && data_DSCH.length > 0) {
                        text = 'Dự toán BHYT ' + data_DSCH[0].CH_THONGBAO_NAM + ' được phân bổ: ' + dutoan_nam + 'đ, đã thực hiện đến thời điểm ngày ' +
                            jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS') + ' là ' + _t_bhtt + 'đ, dự toán còn lại được sử dụng là ' + dutoan_conlai
                            + 'đ, dự kiến với số còn lại là ' + dutoan_conlai + 'đ thì đến ngày ' + date_dukien + ' sẽ hết dự toán.';
                    }
                }
                return text;
            }

            function stringToNumber(str) {
                if (str != null && str.length > 0) {
                    var _temp = str.replaceAll(',', '').slice(0, -3);
                    return _temp;
                }
                return '';
            }

            function numberToString(num) {
                return num.replaceAll('.', ',') + '.00';
            }

            function tinhngay(dutoan_nam, _t_bhtt) {
                //var _t_dutoan = dutoan_nam;//numberToString(dutoan_nam);
                var _t_sudung = stringToNumber(dutoan_nam) - stringToNumber(_t_bhtt);
                var _so_ngay = countDay();
                var _t_temp = stringToNumber(_t_bhtt) / _so_ngay;
                return new_date = moment(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'),
                    "DD/MM/YYYY").add(_t_sudung / _t_temp, 'days').format("DD/MM/YYYY");

                //$("#txtT_DUTOAN").val(_t_dutoan);
                //$("#txtT_SUDUNG").val(numberToString(vienphi_tinhtien.convertNumToCurrency(_t_sudung, true)));
                //$("#txtTHOIGIAN_DUKIEN").val(new_date);
            }

            function countDay() {
                var oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
                var firstDate = new Date(new Date().getFullYear(), 00, 01);
                var secondDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
                var diffDays = Math.round(Math.abs((firstDate.getTime() - secondDate.getTime()) / (oneDay)));
                return diffDays;
            }


            function closePopUp(el) {
                //var cvr = document.getElementById("cover")
                var dlg = document.getElementById(el)
                //cvr.style.display = "none"
                dlg.style.display = "none"
                document.body.style.overflowY = "scroll"
            }

            setInterval(function () {
                blink()
            }, 1000);


            function blink() {
                $("#txtThongbao").fadeTo(100, 0.1).fadeTo(200, 1.0);
            }

            //END Man hinh Thong bao

            // Start bao dong do noi vien

            var user_name = '{user_name}';

            /* timeload = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", _par_timeload
                    .join('$')); */


            /* function checkTrangThai(){
                var _par_Check= ['HIS_CHECK_TURNON_BDD'];
                var HIS_CHECK_TURNON_BDD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                        _par_Check.join('$'));
                if(HIS_CHECK_TURNON_BDD != '0'){
                    var _par;
                    _par = HIS_CHECK_TURNON_BDD + '$';
                    var data_json = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC99.KHBDD.01", _par);
                    if (data_json.length > 0) {
                        for(var i =0; i<data_json.length; i++){
                             if(data_json[i].USER_NAME ==  user_name){
                                 DlgUtil.showMsg("Đang có báo động đỏ. Nội dung: " + data_json[i].NOIDUNG , undefined);
                                 clearInterval(refreshIntervalId);
                                 break;
                             }
                        }
                    }
                }
            }

            var refreshIntervalId = setInterval(function() {
                checkTrangThai();
            }, 5000);		 */
            //checkTrangThai();

            // END bao dong do noi vien
        </script>
    </div>
    <div class="footer-right {lang}">
    </div>
    </div>
    <script type="text/javascript" src="{ROOT}/common/script/common.js"></script>
    <script type="text/javascript" src="{ROOT}/common/script/config.js"></script>
</footer>
</body>
</html>