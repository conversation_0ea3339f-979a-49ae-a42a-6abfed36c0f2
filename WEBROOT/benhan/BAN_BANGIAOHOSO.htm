<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>

<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../benhan/BAN_BANGIAOHOSO.js?v=0092111111"></script>
<link href="../common/script/select2/dist/css/select2.css" rel="stylesheet" />
<script src="../common/script/select2/dist/js/select2.full.js"></script>

<div class="container" id="divMain">		
	<div class="panel-body border-group-1 mgt5">
		<!-- <div class="col-xs-1 low-padding mgt20" style="text-align: center;">
			<label id="lblSoLuong" style="font-size: 30px;">0</label>
		</div>
		 -->
		<div class="col-xs-11 lowp-padding">
			<div class="col-xs-12 low-padding mgt2">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Trạng thái hồ sơ</label>
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboLOAIDIEUTRI">
								<option value="-1">---Tất cả---</option>
								<option value="0">Chưa chuyển lưu trữ</option>
								<option value="9">Đã chuyển lưu trữ</option>
								<option value="2">Chuyển trả</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div id="dvKHOA" class="col-xs-4 low-padding">
							<label class="">Khoa điều trị</label>
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboKHOAID_SEARCH" modeDis=""
									style="width: 100%; ">
							</select>
						</div>
					</div>
				</div>
			</div>

			<div class="col-xs-12 low-padding mgt2">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Lọc theo</label>
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboNGAYLOC">
								<option value="1" selected>Ngày ra viện</option>
								<option value="2">Ngày duyệt</option>
								<option value="3">Ngày gửi HS</option>
								<option value="4">Ngày vào viện</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Phòng điều trị</label>
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboPHONGID_SEARCH" modeDis=""
									style="width: 100%; ">
							</select>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-xs-12 low-padding mgt2">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Từ ngày</label>
						</div>
						<div class="col-xs-7 low-padding">
							<div class="input-group ">
								<input class="form-control input-sm" id="txtTUNGAY" name="txtTUNGAY" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19" > 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTUNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>							 
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Đến ngày</label>
						</div>
						<div class="col-xs-7 low-padding">
							<div class="input-group ">
								<input class="form-control input-sm" id="txtDENNGAY" name="txtDENNGAY" title=""  data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="19" > 
								<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtDENNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>		
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt2"  >
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Loại điều trị</label>
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm"  id="cboLOAITIEPNHANID">
								<option value="-1">-- Tất cả --</option>
								<option value="0">Nội trú</option>
								<option value="3">Điều trị ngoại trú</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-4 low-padding">
							<label class="">Ký bàn giao</label>
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboKYBANGIAO" modeDis="" style="width: 100%; ">
								<option value="-1">-- Chọn --</option>
								<option value="0" selected>Chưa ký bàn giao</option>
								<option value="1">Đã ký bàn giao</option>
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xs-1 low-padding " >
			<div class="col-xs-12 low-padding ">
				<button type="button" class="btn btn-primary mgt3" id="btnTIMKIEM" style=" width: 100% !important;">
					<span class="glyphicon glyphicon-search"></span> Tìm kiếm
				</button>
			</div>
			<div class="col-xs-12 low-padding mgt5">
				<button class="btn btn-sm btn-primary" id="btnInDanhSach" style="width: 100% !important;display: none;">
					<span class="glyphicon glyphicon-print"></span>Excel</button>
			</div>
		</div>
		
	</div>
	
	<div class="col-xs-12 low-padding" style="margin-bottom: 5px;">
	  <div class="col-md-12 low-padding mgt5 mgb5">
		<label style="color:red;font-size:100%;text-align:left"><b id="lblThongBao"></b></label>
	  </div>
	  <div class="col-xs-12">
		<ul id="tabs" class="nav nav-tabs" style="margin-top: 10px;" data-tabs="tabs">
			<li class="active">
				<a id="tabHSBA" href="#tabGrHSBA" aria-controls="home" role="tab"data-toggle="tab">Hồ sơ bệnh án</a>
			</li>
			<li>
				<a id="tabDAINGAY" href="#tabGrHSBADAINGAY" aria-controls="profile" role="tab" data-toggle="tab">HSBA Dài ngày</a>
			</li>
		</ul>
		 <div class="col-xs-12 low-padding tab active" style="margin-top: -5px; margin-bottom: -10px !important;" id ="tabGrHSBA">		  	  
			<table id="grdSOHOSOBENHAN"></table>	
			<div id="pager_grdSOHOSOBENHAN"></div>
		</div>
		<div class="col-xs-12 low-padding tab" style="margin-top: -5px; margin-bottom: -10px !important;" id ="tabGrHSBADAINGAY">		  	  
			<table id="grdSOHOSOBENHANDAINGAY"></table>	
			<div id="pager_grdSOHOSOBENHANDAINGAY"></div>
		</div>
	  </div>
	  <div class="col-xs-12 low-padding input-group" id="divMauchu" style="display: none">
			<label style="height: 16px; margin-left: 20px;">
				<b>Màu chữ :</b>
				<label id="lblPHIEUCHUAKY" style="color : #0000FF; font-weight : bold; margin-left: 20px;"> Còn phiếu chưa thực hiện ký </label>
				<label style="color : #DF01D7; font-weight : bold; margin-left: 20px;"> Chưa hoàn thành ký cấp </label>
			</label>
	  </div>
		<!-- #####-->
	</div> <!-- END FORM INLINE -->
	<div class="col-xs-12 low-padding">
		<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding required">
							<label class="">Người giao</label>
						</div>
						<div class="col-xs-2 low-padding required">
							<input class="form-control input-sm" id="txtTKNGUOIBANGIAO" name="txtTKNGUOIBANGIAO" title=""  >
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboNGUOIBANGIAO" filterLike="txtTKNGUOIBANGIAO"  modeDis="" style="width: 100%; ">
	 		   	 			</select>	
						</div>
					</div>
				</div>
		<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding required">
							<label class="">Chọn mã in</label>
						</div>
						<div class="col-xs-2 low-padding required">
							<input class="form-control input-sm" id="txtTKMABANGIAO" name="txtTKMABANGIAO" title=""  >
						</div>
						<div class="col-xs-7 low-padding">
							<select class="form-control input-sm" id="cboMABANGIAO" modeDis="" filterLike="txtTKMABANGIAO" style="width: 100%; ">						   						   			       		
	 		   	 			</select>	
						</div>
					</div>
			</div>
	</div>
	<div class="col-xs-12 low-padding mgt10"
				style="text-align: center;" id="gridButton">
				<button class="btn btn-sm btn-primary" id="btnKyCA" style="display: none" data-catype="1">
					<span class="glyphicon glyphicon-pencil"></span> Ký bàn giao</button>
				<button class="btn btn-sm btn-primary" id="btnHuyKyCA" style="display: none" data-catype="2">
					<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký</button>
				<button class="btn btn-sm btn-primary" id="btnSua" disabled="disabled" style="display: none">
					<span class="glyphicon glyphicon-edit"></span> Sửa</button>
				<button class="btn btn-sm btn-primary" id="btnEdit" disabled="disabled">
					<span class="glyphicon glyphicon-floppy-disk"></span> Chuyển hồ sơ</button>
				<!-- <button class="btn btn-sm btn-primary" id="btnRefesh">
					<span class="glyphicon glyphicon-refresh"></span> Làm mới</button> -->
				<button class="btn btn-sm btn-primary" id="btnExport">
					<span class="glyphicon glyphicon-print"></span> In phiếu</button>
				<button class="btn btn-sm btn-primary" id="btnExport_HSBADAINGAY" style="display: none">
					<span class="glyphicon glyphicon-print"></span> In phiếu HSBA dài ngày</button>
				<button class="btn btn-sm btn-primary" id="btnExportExcel" style="display: none">
					<span class="glyphicon glyphicon-print"></span> In phiếu Excel</button>
				<button class="btn btn-sm btn-primary" id="btnExportDSHSBG">
					<span class="glyphicon glyphicon-print"></span> In DSHSBG</button>
				<button class="btn btn-sm btn-primary" id="btnKqEmr">
					<span class="glyphicon glyphicon-edit"></span> KQ gửi EMR</button>
			</div>
</div>
<div class="contextMenu" id="contextMenu_TC" style="display: none;">
				<ul style="width: 220px !important; font-size: 65%;">
					<li class="menulevel1" id="rTraCuu"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Tra cứu bệnh nhân</span></li>
					<li class="menulevel1" id="rBAChitiet"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Bệnh án chi tiết</span></li>
					<li class="menulevel1" id="rTOMTATBA"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Tóm tắt bệnh án</span></li>
					<li class="menulevel1" id="rLOAIBA"><span
							class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
							style="font-size: 150%; font-family: Verdana">Loại bệnh án</span></li>
					<li class="menulevel1" id="rQLBenhAn"><span
							class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
							style="font-size: 150%; font-family: Verdana">Quản lý bệnh án</span></li>
					<li class="menulevel1" id="rHoanThanhKy"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Phiếu chưa hoàn thàn ký</span></li>
				</ul>
	</div>
		<div class="contextMenu" id="contextMenu_TC1" style="display: none;">
			<ul style="width: 220px !important; font-size: 65%;">
				<li class="menulevel1" id="rTraCuu1"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Tra cứu bệnh nhân</span></li>
				<li class="menulevel1" id="rBAChitiet1"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Bệnh án chi tiết</span></li>
				<li class="menulevel1" id="rTOMTATBA1"><span
						class="ui-icon ui-icon-folder-open" style="float: left"></span> <span
						style="font-size: 150%; font-family: Verdana">Tóm tắt bệnh án</span></li>
			</ul>
		</div>
		<script>
			var opt = [];
			var hospital_id = '{hospital_id}';
			var user_id = '{user_id}';
			var dept_id = '{dept_id}';
			var user_type = '{user_type}';
			var province_id = '{province_id}';
			var uuid = '{uuid}';
			var db_schema='{db_schema}';
			var lang = "vi";
			console.log('hospital_id=' + hospital_id + ' user_type='
					+ user_type);
			var session_par = [];
			session_par[0] = hospital_id;
			session_par[1] = user_id;
			session_par[2] = user_type;
			session_par[3] = province_id;
			session_par[4] = db_schema;
			var table_name = '{table}';
			var _opts = new Object();			
			_opts._param = session_par;
			_opts._uuid = uuid;
			_opts._userid = user_id;
			_opts._deptId = dept_id;
			_opts.hospital_id = hospital_id;
			initRest(_opts._uuid);
			var HOSOBENHAN = new BAN_LTHSBA(_opts);
			HOSOBENHAN.load();
		</script>