<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BC_TINHHINH_KHAMBENHNOITRU_7282_SUB3" language="groovy" pageWidth="802" pageHeight="555" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="68c38a53-2c9a-4591-ac34-0c4b042f016a">
	<property name="ireport.zoom" value="1.384912682300961"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_tungay" class="java.sql.Timestamp"/>
	<parameter name="i_denngay" class="java.sql.Timestamp"/>
	<parameter name="VPARS_i_khoaid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_i_trangthaiid_VALUE" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call bctt_khambenhnoitru_7282_sub3($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{i_tungay},$P{i_denngay},$P{VPARS_i_khoaid_VALUE},$P{VPARS_i_trangthaiid_VALUE},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="GROUP_HERDER" class="java.math.BigDecimal"/>
	<field name="GROUP_COUNT" class="java.math.BigDecimal"/>
	<field name="ROW_STT" class="java.math.BigDecimal"/>
	<field name="ROW_NAME" class="java.lang.String"/>
	<field name="TONG_SO" class="java.math.BigDecimal"/>
	<field name="TONG_NU" class="java.math.BigDecimal"/>
	<field name="TONG_BHYT_6" class="java.math.BigDecimal"/>
	<field name="TONG_BHYT_15" class="java.math.BigDecimal"/>
	<field name="TONG_BHYT_60" class="java.math.BigDecimal"/>
	<field name="TONG_BHYT_TREN60" class="java.math.BigDecimal"/>
	<field name="TONG_VP_6" class="java.math.BigDecimal"/>
	<field name="TONG_VP_15" class="java.math.BigDecimal"/>
	<field name="TONG_VP_60" class="java.math.BigDecimal"/>
	<field name="TONG_VP_TREN60" class="java.math.BigDecimal"/>
	<variable name="TONG_SO_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_SO}]]></variableExpression>
	</variable>
	<variable name="TONG_NU_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_NU}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_6_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_6}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_15_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_15}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_60_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_60}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_TREN60_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_TREN60}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_6_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_6}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_15_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_15}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_60_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_60}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_TREN60_1" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_HERDER" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_TREN60}]]></variableExpression>
	</variable>
	<variable name="TONG_SO_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_SO}]]></variableExpression>
	</variable>
	<variable name="TONG_NU_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_NU}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_6_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_6}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_15_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_15}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_60_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_60}]]></variableExpression>
	</variable>
	<variable name="TONG_BHYT_TREN60_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_BHYT_TREN60}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_6_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_6}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_15_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_15}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_60_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_60}]]></variableExpression>
	</variable>
	<variable name="TONG_VP_TREN60_2" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_COUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_VP_TREN60}]]></variableExpression>
	</variable>
	<group name="GROUP_HERDER">
		<groupExpression><![CDATA[$F{GROUP_HERDER}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="30" y="0" width="180" height="20" uuid="050d5510-28b0-4df8-9a34-68a5a22a204f"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUP_HERDER} == null ? "" : "Tổng số kỹ thuật bằng YHCT-PHCN"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="30" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="ae5282ce-d1a7-48bd-846f-951025573d50"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA["8"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="210" y="0" width="79" height="20" uuid="b80af507-d5ba-403c-822b-c22ef5ff43ee"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_SO_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="289" y="0" width="62" height="20" uuid="8631c606-c06d-4a9c-94de-ebbb8d8bca7f"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_NU_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="351" y="0" width="50" height="20" uuid="9467624a-da6f-4418-a5d2-f2c4f30b65c2"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_6_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="401" y="0" width="50" height="20" uuid="9e5f4ed9-7799-413d-936a-20969df3b253"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_15_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="451" y="0" width="50" height="20" uuid="5b070a64-e594-42a9-b644-97624c07c085"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_60_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="501" y="0" width="50" height="20" uuid="3fa9327c-d031-4a31-8d3f-cf3240cdede6"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_TREN60_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="551" y="0" width="50" height="20" uuid="98a03f26-18c0-4032-ad42-8df443073d86"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_6_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="601" y="0" width="50" height="20" uuid="699fde27-b990-4ce8-9141-b0b894ea3405"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_15_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="651" y="0" width="50" height="20" uuid="fcb5375c-7b42-484a-9573-5ee290379002"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_60_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="701" y="0" width="50" height="20" uuid="d090bc88-45be-49a4-9d12-30d0c0ed0590"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_TREN60_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_HERDER" isBlankWhenNull="true">
					<reportElement x="751" y="0" width="51" height="20" uuid="e1db0139-d6c3-4e6f-b229-04ba988534a9"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="GROUP_COUNT">
		<groupExpression><![CDATA[$F{GROUP_COUNT}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="30" y="0" width="180" height="20" uuid="b76b0afa-480b-4a0a-9542-823f393f9bd0"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUP_COUNT} == null ? "" : (
    $F{GROUP_COUNT} == 1 ? "8.1 Tổng số kỹ thuật bằng YHCT" : "8.2 Tổng số kỹ thuật bằng PHCN"
)]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="30" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="0f3c373e-d391-4eef-8881-f44c4e29b247"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="210" y="0" width="79" height="20" uuid="1d628992-83a9-438c-a936-f865dc8b4a9e"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_SO_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="289" y="0" width="62" height="20" uuid="559cfd3d-cf8a-43ae-8e6c-b910ab316c25"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_NU_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="351" y="0" width="50" height="20" uuid="efedc320-026d-4724-b4ea-ca8d4ea47877"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_6_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="401" y="0" width="50" height="20" uuid="c575f86f-249f-424b-85b7-4f1a3b140333"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_15_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="451" y="0" width="50" height="20" uuid="e75d3aa1-94d6-4cfc-8300-26a1a1fc02e3"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_60_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="501" y="0" width="50" height="20" uuid="608689a1-ec62-4399-9d48-be60f727f04c"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_BHYT_TREN60_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="551" y="0" width="50" height="20" uuid="3a12d042-cfc7-4b71-8df6-3d656fc4085e"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_6_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="651" y="0" width="50" height="20" uuid="74704629-336d-445e-ba9b-fad21e47b8a6"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_60_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="701" y="0" width="50" height="20" uuid="63664b09-0627-4e70-91d5-3b28aa850339"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_TREN60_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="601" y="0" width="50" height="20" uuid="8821871c-0cb6-46d6-ad7a-4c0341bf6f3a"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_VP_15_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_COUNT" isBlankWhenNull="true">
					<reportElement x="751" y="0" width="51" height="20" uuid="209965f6-dad5-4e47-b71a-3d251ff33cac"/>
					<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="751" y="0" width="51" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c389b4f2-ad65-4a83-875e-5be65e375e81"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="30" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="b8bdf497-ce61-4131-b7a7-277cda567989"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="30" y="0" width="180" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="592044c6-76c8-4722-8ef8-77b15fbbbe64"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ROW_NAME}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="210" y="0" width="79" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="df0107d0-f01f-41e3-b470-2f5125542ee0"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_SO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="289" y="0" width="62" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9586ba4a-6af4-4593-b984-7279f0bb4004"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_NU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="351" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="f609d751-bca5-4f66-919e-0313f2546690"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_BHYT_6}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="401" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="0a2964f2-4025-4f4d-9415-8c71d3a0519d"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_BHYT_15}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="451" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="915b96bf-4d36-4b33-91bf-3a581d17f441"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_BHYT_60}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="501" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="b42704d2-57d4-4bda-bacc-eb41d7012c70"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_BHYT_TREN60}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="551" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="20733cb4-9508-421b-8353-0a228bcd0e68"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_VP_6}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="601" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="dbabbc42-c10e-4541-b853-d81d392a0f62"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_VP_15}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="651" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="68efb642-f3fc-42cd-84b5-569df0a72a4f"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_VP_60}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="701" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="a97fc06a-81a2-45e3-b08d-7e12fb186f6f"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_VP_TREN60}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="141">
			<staticText>
				<reportElement x="0" y="25" width="289" height="20" uuid="33eacf1d-2b35-4923-a5a0-6e9a11e8a31c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI BÁO CÁO]]></text>
			</staticText>
			<staticText>
				<reportElement x="501" y="25" width="301" height="20" uuid="bcd02d83-e723-4a8e-8dcd-34b33e9a3098"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG KHOA]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
