<!--
FeatureId
Creator: HANV
CreateDate: 26/06/2017
UpdateDate: 03/07/2017
Desc: <PERSON><PERSON><PERSON> h<PERSON>nh d<PERSON>ch vụ cận lâm sàng - phân chia các tab
-->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.core.css" />
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.default.css" id="toggleCSS" />
<script type="text/javascript" src="../common/script/alertify/alertify.js" ></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../ngoaitru/NGT05K005_RESET_STT_KIOS.js?v=220614"></script>

<div id="divMain" class="container">
		<div class="col-md-6 low-padding mgt15">
			<table id="grdBANG"></table><div id="pager_grdBANG"></div>	     	
		</div>		
		<div class="col-md-5 low-padding mgl10 mgt30" id="dvinput">
			<div class="form-inline">
	            <div class="col-md-4 required" style="padding-right: 0">
	                <label>Số thứ tự</label>
	            </div>
	            <div class="col-md-8 low-padding">
	            	<input type="hidden" name="hidID" id="hidID" value="" />
	                <input class="form-control input-sm"  style="width:100%;" id="txtSTT_KIOS" name="txtSTT_KIOS" title="">
	            </div>	            
        	</div>			
		    <div class="col-md-12 low-padding mgt5">
		 		<div class="col-md-5 low-padding">
		 		</div>	
			  	<div class="col-md-7 low-padding">
					<button type="button" class="btn btn-sm btn-primary" id="btnCapNhat">
					      <span class="glyphicon glyphicon-floppy-disk"></span> Cập nhật
					</button>
			  	</div>
	     	</div>	
		</div>	
</div>

<script>
var paramInfo=CommonUtil.decode('{paramData}');
var _opts = new Object();
var schema = '{db_schema}';
var hospital_id = '{hospital_id}';
var uuid = '{_uuid}';
initRest(uuid,"/vnpthis");
_opts.hospital_id=hospital_id;
_opts.schema=schema;
_opts._uuid=uuid;

var ls = new NGT05K005_RESET_STT_KIOS(_opts);
ls.load(_opts);
</script>