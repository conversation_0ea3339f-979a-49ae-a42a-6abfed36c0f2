/*
Mã màn hình  : NTU02D055
File mã nguồn : NTU02D055_SoKetDieuTri.js
Mục đích  : <PERSON><PERSON> sach SO KET DIEU TRI
Tham số vào : 
  khambenhid: id của khám bệnh
  BENHNHANID: id cua BENH NHAN
  BACSYDIEUTRIID: ID CUA BAC SY DIEU TRI  
Người lập trình	<PERSON> cập nhật  Ghi chú
HUONGPV	- 13092016 - Comment
*/
function BAN_LTHSBA(opt) {
	var _gridHSBA="grdSOHOSOBENHAN";
	var _gridHSBA_DAINGAY="grdSOHOSOBENHANDAINGAY";
	var that=this;
	var sql_par;
	var _solutru_dalay ='';
	var dsThem = [];			// Luu tru cac du lieu tick chon trong grid;
	var dsBanghi = [];
	var BADAINGAY = 0;
	var batbuoc_chonkhoa = 0 ;
	var tudong_inphieu = 0;
	var Grid_HNI = 0;
	var kyChot = 0;
	var causer = '';
	var capassword = '';
	var smartcauser = '';
	var catype = '';
	var cf = {};
	this.load = doLoad;

	var hienthimabangiao = '';
	var lydotrahs = '';
	var Hienthi_emr = '';
	var hienthi_khoathietlap = '';
	var loaitiepnhan = '';
	var loaitiepnhanid = '';
	function doLoad() {
		this.validator = new DataValidator("divMain");
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		initControl();
		bindEvent();
	}
	function initControl() {
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'BAN_BANGIAO_AUTO_NGUOIBANGIAO;EMR_KYCHOT_KYDIENTU' +
			';BAN_HIENTHI_MA_BG;BAN_TRAHS_LYDO;BGHSBA_TAB_DAINGAY;BAN_BANGIAO_GRID_BDHNI;EMR_DAYTONG_BANGIAOHOSO;EMR_KYCHOT_BANGIAOHOSO' +
			';BAN_BANGIAO_SHOWKYCAPONLY;BAN_HIENTHI_KHOATHIETLAP;BAN_TIMKIEM_LOAITIEPNHAN;BAN_TUDONGIN_BANGIAO;BAN_BATBUOC_CHONKHOA;BAN_GET_DATAINFO_NEW');
		if (data_ar != null && data_ar.length > 0) {
			cf = data_ar[0];
			hienthimabangiao = cf.BAN_HIENTHI_MA_BG;
			lydotrahs = cf.BAN_TRAHS_LYDO;
			BADAINGAY = cf.BGHSBA_TAB_DAINGAY;
			Grid_HNI = cf.BAN_BANGIAO_GRID_BDHNI;
			Hienthi_emr = cf.EMR_DAYTONG_BANGIAOHOSO;
			kyChot = cf.EMR_KYCHOT_BANGIAOHOSO;
			hienthi_khoathietlap = cf.BAN_HIENTHI_KHOATHIETLAP;
			loaitiepnhan = cf.BAN_TIMKIEM_LOAITIEPNHAN;
			tudong_inphieu = cf.BAN_TUDONGIN_BANGIAO;
			batbuoc_chonkhoa = cf.BAN_BATBUOC_CHONKHOA;
		}

		//tao danh sach phieu phu thu
		var _gridHeader="STT,RN,40,0,t,l;KYCAP,KYCAP,60,0,t,l;KYCA,KYCA,60,0,t,l;RPT_CODE,RPT_CODE,60,0,t,l;KY_BANGIAO,KY_BANGIAO,60,0,t,l; ,ICON,25,0,ns,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;" +
			"Số lưu trữ,SOLUUTRU,90,0,f,l;Mã bàn giao,MABANGIAO_VIEW,80,0,"+(hienthimabangiao == 1?"f":"t")+",l;Mã bệnh án,MAHOSOBENHAN,90,0,f,l;Mã bệnh nhân,MABENHNHAN,100,0,f,l;Loại điều trị,LOAIDIEUTRI,80,0,"+(loaitiepnhan == 1 ? "f" : "t") +",l;Họ và Tên,TENBENHNHAN,170,0,f,l;" +
			""+(Grid_HNI==1?"Loại BA RV,LOAIBARV,120,0,f,c,ES;LOAIBARVID,LOAIBARVID,120,0,t,l;Phim CT,BAN_LT_CT,90,0,e,l;Phim MRI,BAN_LT_MRI,90,0,e,l;Phim X-Quang,BAN_LT_XQ,90,0,e,l;":"")+
			""+(Grid_HNI==1?"Địa chỉ,DIACHI,150,0,f,l;Đối tượng,DOITUONGBENHNHAN,90,0,f,l;Giới tính,GIOITINH,90,0,f,l;":"")+"Ngày sinh,NGAYSINH,90,0,f,l;Số vào viên,SOVAOVIEN,70,0,f,l;Mã BHYT,MA_BHYT,110,0,f,l;Khoa điều trị,KHOADIEUTRI,185,0,f,l;KHOADIEUTRIID,KHOADIEUTRIID,185,0,t,l;" +
			"Thời gian lưu trữ,NGAYLUUTRU,120,0,t,l; Thời gian vào viện,NGAYNHAPVIEN,120,0,f,l;Thời gian ra viện,NGAYXUATVIEN,120,0,f,l;Mã ICD RV,MACHANDOANRAVIEN,80,0,f,l" +
			";Chẩn đoán RV,CHANDOANRAVIEN,200,0,f,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;Tình trạng,KETQUADIEUTRI,100,0,f,l;Kết quả,XUTRI,100,0,f,l;Số ngày điều trị,SONGAYDIEUTRI,100,0,f,l;"+(Grid_HNI==1?"" : "Loại BA RV,LOAIBA,120,0,f,l;")+
			"B/sỹ ĐT,BACSYDIEUTRI,90,0,f,l;Người bàn giao,NGUOIBANGIAO,100,0,f,l;LOAIBENHANID,LOAIBENHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,90,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;TRANGTHAI_BANGIAO,TRANGTHAI_BANGIAO,0,0,t,l;Thời gian chuyển hồ sơ,NGAYBANGIAO,200,0,f,l;Lý do trả HS,LYDOTRA_BANGIAO,150,0,"+(lydotrahs == 1?"f":"t")+",l;" +
			"BENHNHANID,BENHNHANID,0,0,t,l"+((Hienthi_emr !== '0')?";Mã bàn giao,MABANGIAO_VIEW,80,0,"+(hienthimabangiao == 1?"f":"t")+",l;Người duyệt,EMR_NGUOIDUYET,150,0,f,l;Ngày duyệt,EMR_NGAYDUYET,150,0,f,l;Nội dung duyệt phiếu,EMR_NOIDUNG,150,0,f,l;Trạng thái,TT_DUYET_EMR,150,0,f,l":"");

		if(Grid_HNI == 1){
			$("#cboNGUOIBANGIAO").select2();
			$("#cboKHOAID_SEARCH").select2();
			$("#cboPHONGID_SEARCH").select2();
		}

		var par_ctl = ['BGHSBA_GRID'];
		var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', par_ctl.join('$'));
		if(ctl_par != '0'){
			_gridHeader = ctl_par;
		}

		if(kyChot != '0') {
			$("#divMauchu").show();
			if (cf.BAN_BANGIAO_SHOWKYCAPONLY == 1) {
				$("#lblPHIEUCHUAKY").hide();
			}
			GridUtil.init(_gridHSBA,"100%","370px","",true, _gridHeader,false,{rowNum: 50,rowList: [50,100,150,200,250,300]});
		} else {
			GridUtil.init(_gridHSBA,"100%","370px","",true, _gridHeader,false,{rowNum: 100,rowList: [50,100,300,500,1000]});
		}
		GridUtil.addExcelButton(_gridHSBA,'Xuất excel',true);
		if(BADAINGAY == 1){
			$("#tabHSBA").css("display","");
			$("#tabDAINGAY").css("display","");
			$("#btnExport_HSBADAINGAY").css("display","");
			var _gridHeaderDaiNgay="STT,RN,40,0,t,l;Số lưu trữ,SOLUUTRU,60,0,f,l;Mã bàn giao,MABANGIAO_VIEW,80,0,"+(hienthimabangiao == 1?"f":"t")+",l;Mã bệnh án,MAHOSOBENHAN,90,0,f,l;Mã bệnh nhân,MABENHNHAN,100,0,f,l;Họ và Tên,TENBENHNHAN,170,0,f,l;" +
				"Ngày sinh,NGAYSINH,90,0,f,l;KHAMBENHID,KHAMBENHID,90,0,t,l;HOSOBENHANID_HIENTAI,HOSOBENHANID_HIENTAI,90,0,t,l;" +
				"Thời gian lưu trữ,NGAYLUUTRU,120,0,t,l; Thời gian mở BA,NGAYNHAPVIEN,120,0,f,l;Thời gian đóng BA,NGAYXUATVIEN,120,0,f,l;" +
				"HOSOBENHANID,HOSOBENHANID,0,0,t,l;Người bàn giao,NGUOIBANGIAO,100,0,f,l;Người lưu trữ,NGUOILUU,100,0,f,l;TRANGTHAI_BANGIAO,TRANGTHAI_BANGIAO,0,0,t,l;Thời gian chuyển hồ sơ,NGAYBANGIAO_VIEW,200,0,f,l;Lý do trả HS,LYDOTRA_BANGIAO,150,0,"+(lydotrahs == 1?"f":"t")+",l;BENHNHANID,BENHNHANID,0,0,t,l";
			GridUtil.init(_gridHSBA_DAINGAY,"100%","370px","",true, _gridHeaderDaiNgay,false,{rowNum: 100,rowList: [2,50,100,300,500,1000]});
			GridUtil.setGridParam(_gridHSBA_DAINGAY,{
				onSelectRow: function(id, status) {
					GridUtil.unmarkAll(_gridHSBA_DAINGAY);
					GridUtil.markRow(_gridHSBA_DAINGAY,id);
					var _row = $("#"+_gridHSBA_DAINGAY).jqGrid('getRowData', id);
					_row["ROWID"] = id;
					_row["DAINGAY"] = "1";
					if(status && _row.HOSOBENHANID != null && _row.HOSOBENHANID != 'null'
						&& dsThem.indexOf(_row.HOSOBENHANID) == -1 ){
						dsThem.push(_row.HOSOBENHANID);
						dsBanghi.push(_row);
					}
					if(!status){
						var j = dsThem.indexOf( _row.HOSOBENHANID);
						if (j != -1) {
							dsThem.splice(j, 1);
							dsBanghi.splice(j, 1);
						}
					}
					$("#lblThongBao").text("Có " + dsThem.length + " bản ghi được chọn.");
				},
				onSelectAll : function(id, status) {
					var rowIds = $("#"+_gridHSBA_DAINGAY).jqGrid('getDataIDs');
					for (var i = 0; i < rowIds.length; i++) {
						rowData = $('#' +_gridHSBA_DAINGAY).jqGrid('getRowData', rowIds[i]);
						if (status && dsThem.indexOf(rowData["HOSOBENHANID"]) == -1 &&( rowData["HOSOBENHANID"])!= null) {
							dsThem.push( rowData.HOSOBENHANID);
							rowData["ROWID"] = rowIds[i];
							rowData["DAINGAY"] = "1";
							dsBanghi.push(rowData);
						}
						if (!status) {
							var j = dsThem.indexOf(rowData["HOSOBENHANID"]);
							if (j != -1) {
								dsThem.splice(j, 1);
								dsBanghi.splice(j, 1);
							}
						}
					}
					$("#lblThongBao").text("Có " + dsThem.length + " bản ghi được chọn.");
				},gridComplete: function(id){
					if($("#cboLOAIDIEUTRI").val() == 0 || $("#cboLOAIDIEUTRI").val() == 2 || $("#cboLOAIDIEUTRI").val() == 19){
						$("#btnEdit").attr("disabled", false);
					}
					else{
						$("#btnEdit").attr("disabled", true);
					}
				}

			});
		}
		else{
			$("#tabHSBA").css("display","none");
			$("#tabDAINGAY").css("display","none");
			$("#btnExport_HSBADAINGAY").css("display","none");
		}
		var hienthitrangthai = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_BANGIAO_TRANGTHAI');
		if(hienthitrangthai == 1){
			$('#cboLOAIDIEUTRI').append("<option value='5'> Hồ sơ đã nhận </option>");
			$('#cboLOAIDIEUTRI').append("<option value='4'> Hồ sơ đã chuyển kho </option>");
		}
		var hienthicbodkloc = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_HIENTHI_CBO_DK_LOC');
		if(hienthicbodkloc == 1){
			$("#divLoc").css("display","");
		}
		$("#cboNGAYLOC").val(1);
		$('#cboLOAIDIEUTRI').val(0);
//		$("#"+_gridHSBA)[0].toggleToolbar();
		// khoi tao danh sach nghe nghiep
		//sql_par=[];
//		ComboUtil.getComboTag("cboNGHENGHIEPID","NT.SO_HSBA.NN",sql_par, "", {value:'',text:'--Lựa chọn--'},"sql","","");
//		ComboUtil.getComboTag("cboDANTOCID","NT.SO_HSBA.DT",sql_par, "", {value:'',text:'--Lựa chọn--'},"sql","","");
//		//chan doan chinh
//		ComboUtil.initComboGrid("txtMACHANDOANRAVIEN_SEARCH",_sql,[],"600px",_col,"txtMACHANDOANRAVIEN_SEARCH=ICD10CODE,txtCHANDOANRAVIEN_SEARCH=ICD10NAME");
		//KHOA
		if(Hienthi_emr == "1"){
			var date = new Date();
			var bThangHT = date.getMonth()+1; //January is 0!
			if(bThangHT < 10) bThangHT = "0" + bThangHT.toString();
			$('#txtTUNGAY').val("01" + "/" + bThangHT + "/" + date.getFullYear());
			$('#cboLOAIDIEUTRI').children().remove().end();
			$('#cboLOAIDIEUTRI').append('<option value="-1">---Tất cả---</option>');
			$('#cboLOAIDIEUTRI').append('<option value="19">Chưa bàn giao</option>');
			$('#cboLOAIDIEUTRI').append('<option value="9">Đã bàn giao</option>');
			$('#cboLOAIDIEUTRI').append('<option value="3">Đã lưu trữ</option>');
			$('#cboLOAIDIEUTRI').append('<option value="2">Từ chối</option>');
			$('#cboLOAIDIEUTRI').val(19);
		}
		else{
			$('#txtTUNGAY').val(jsonrpc.AjaxJson.ajaxCALL_SP_S('GETSYSDATE','DD/MM/YYYY'+'$'+10));
		}
		if(jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_TIMKIEM_LOAITIEPNHAN') == 1){
			$("#divLOAITIEPNHAN").show();
			if (opt.hospital_id == 10284) {
				$('#cboLOAITIEPNHANID').append('<option value="1">Ngoại trú</option>');
			}
		}
		$('#txtDENNGAY').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		if (batbuoc_chonkhoa  == 1 ){
			$('#dvKHOA').addClass("col-xs-4 low-padding required");
			var sql_par = [];
			sql_par.push({"name" : "[0]", "value" : opt._userid});
			var dept_id = jsonrpc.AjaxJson.getOneValue('BAN.GETDEPTID', sql_par);
			ComboUtil.getComboTag("cboKHOAID_SEARCH", "DEPT.P01","4$"+opt._param[1]+"$"+opt._param[0],"",{value:'-1',text:'--Lựa chọn--'},'sp','',function(event, ui) {
				$("#cboKHOAID_SEARCH").val(dept_id);
				loadNguoiBanGiao();
				var _val = $('#cboKHOAID_SEARCH').val();
				ComboUtil.getComboTag("cboPHONGID_SEARCH","DMC.NV.06",[{"name":"[0]","value":_val}], "",{text:"--Lựa chọn--", value:-1},"sql");
			});
		}else {
			ComboUtil.getComboTag("cboKHOAID_SEARCH", "DEPT.P01","4$"+opt._param[1]+"$"+opt._param[0],"",{value:'-1',text:'--Lựa chọn--'},'sp','',function(event, ui) {
				if(Hienthi_emr != "0" ){
					$("#cboKHOAID_SEARCH").val(opt._deptId);
				}
				else{
					if(hienthi_khoathietlap == "1") {
						$("#cboKHOAID_SEARCH").val(opt._deptId);
						$("#cboKHOAID_SEARCH").attr("disabled", true);
						ComboUtil.getComboTag("cboPHONGID_SEARCH","DMC.NV.06",[{"name":"[0]","value":opt._deptId}], "",{text:"--Lựa chọn--", value:-1},"sql");
					}else {
						$("#cboKHOAID_SEARCH").val("-1");
					}
				}
				var _val = $('#cboKHOAID_SEARCH').val();
				ComboUtil.getComboTag("cboPHONGID_SEARCH","DMC.NV.06",[{"name":"[0]","value":_val}], "",{text:"--Lựa chọn--", value:-1},"sql");
				loadNguoiBanGiao();
				loadMaBanGiao();
			});
		}

		EventUtil.setEvent("assignSevice_saveHSBADetail", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("divDlgBenhAnDetail");
		});

		GridUtil.setGridParam(_gridHSBA,{
			onSelectRow: function(id, status) {
				GridUtil.unmarkAll(_gridHSBA);
				GridUtil.markRow(_gridHSBA,id);
				var _row = $("#"+_gridHSBA).jqGrid('getRowData', id);
				loaitiepnhanid = _row.LOAITIEPNHANID;
				_row["ROWID"] = id;
				_row["DAINGAY"] = "0";
				if(kyChot != '0' && ( (cf.BAN_BANGIAO_SHOWKYCAPONLY != '1' && _row.KYCA != '0') || _row.KYCAP != '')) {
					//
				} else {
					if(status && _row.HOSOBENHANID != null && _row.HOSOBENHANID != 'null'
						&& dsThem.indexOf(_row.HOSOBENHANID) == -1 ){
						dsThem.push(_row.HOSOBENHANID);
						_row.LOAIBARVID =$('#' + _row.HOSOBENHANID + "_cboLOAIBAID").val();
						dsBanghi.push(_row);
					}
					if(!status){
						var j = dsThem.indexOf( _row.HOSOBENHANID);
						if (j != -1) {
							dsThem.splice(j, 1);
							dsBanghi.splice(j, 1);
						}
					}
					$("#lblThongBao").text("Có " + dsThem.length + " bản ghi được chọn.");
				}
				$("#" +_row.HOSOBENHANID+  "_cboLOAIBAID").on('change', function() {
					if(dsBanghi.length > 0){
						var  index = dsBanghi.findIndex(obj  => obj.HOSOBENHANID == _row.HOSOBENHANID);
						if (index != -1 ) {
							dsBanghi[index].LOAIBARVID = $('#' + _row.HOSOBENHANID + "_cboLOAIBAID").val();
						}
					}
					console.log(dsBanghi);
				});
			},
			onSelectAll : function(id, status) {
				var rowIds = $("#"+_gridHSBA).jqGrid('getDataIDs');
				for (var i = 0; i < rowIds.length; i++) {
					rowData = $('#' +_gridHSBA).jqGrid('getRowData', rowIds[i]);
					if(kyChot != '0' && ( (cf.BAN_BANGIAO_SHOWKYCAPONLY != '1' &&_row.KYCA != '0')  || rowData.KYCAP != '')) {
						//
					} else {
						if (status && dsThem.indexOf(rowData["HOSOBENHANID"]) == -1 &&( rowData["HOSOBENHANID"])!= null) {
							dsThem.push( rowData.HOSOBENHANID);
							rowData["ROWID"] = rowIds[i];
							rowData["DAINGAY"] = "0";
							rowData.LOAIBARVID =$('#' + rowData.HOSOBENHANID + "_cboLOAIBAID").val();
							dsBanghi.push(rowData);
						}
						if (!status) {
							var j = dsThem.indexOf(rowData["HOSOBENHANID"]);
							if (j != -1) {
								dsThem.splice(j, 1);
								dsBanghi.splice(j, 1);
							}
						}

					}
				}
				$("#lblThongBao").text("Có " + dsThem.length + " bản ghi được chọn.");
			},
			gridComplete: function(id){
				var rowIds = $("#" + _gridHSBA).jqGrid('getDataIDs');
				if (kyChot != '0') {
					for (var i = 0; i < rowIds.length; i++) {
						var id = rowIds[i];
						var rowData = $('#' + _gridHSBA).jqGrid('getRowData', rowIds[i]);
						if (cf.BAN_BANGIAO_SHOWKYCAPONLY == 1) {
							if (rowData.KYCAP != '') {
								$("#" + _gridHSBA).jqGrid('setRowData', rowIds[i], "", {color: '#DF01D7'});
								$("#jqg_grdSOHOSOBENHAN_" + rowIds[i]).attr("disabled", true);
							}
						}else {
							if (rowData.KYCA != '0') {
								$("#" + _gridHSBA).jqGrid('setRowData', rowIds[i], "", {color: '#0000FF'});
								$("#jqg_grdSOHOSOBENHAN_" + rowIds[i]).attr("disabled", true);
							} else if (rowData.KYCAP != '') {
								$("#" + _gridHSBA).jqGrid('setRowData', rowIds[i], "", {color: '#DF01D7'});
								$("#jqg_grdSOHOSOBENHAN_" + rowIds[i]).attr("disabled", true);
							}
						}
						var _icon = '';
						if (rowData.KY_BANGIAO && rowData.KY_BANGIAO == '1') {
							_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
						}
						$("#" + _gridHSBA).jqGrid('setCell', id, 'ICON', _icon);
					}
				}
				if($("#cboLOAIDIEUTRI").val() == 0 || $("#cboLOAIDIEUTRI").val() == 2 || $("#cboLOAIDIEUTRI").val() == 19){
					$("#btnEdit").attr("disabled", false);
					$("#btnSua").attr("disabled", false);
				}
				else{
					$("#btnEdit").attr("disabled", true);
					$("#btnSua").attr("disabled", true);
				}
				if(Grid_HNI == 1){
					for (var j = 0; j < rowIds.length; j++) {
						var _row = $('#' + _gridHSBA).jqGrid('getRowData', rowIds[j]);
						if(_row.LOAIBARV.indexOf("select") < 0) {
							var html = '<select class="form-control input-sm" id="'
								+ (_row.HOSOBENHANID)
								+ "_"
								+ 'cboLOAIBAID">';

							var option = '<option value="1">Thông thường</option>\n' +
								'\t\t\t\t\t\t\t\t\t\t\t<option value="2">Tử vong</option>\n' +
								'\t\t\t\t\t\t\t\t\t\t\t<option value="3">Tai nạn</option>\n' +
								'\t\t\t\t\t\t\t\t\t\t\t<option value="4">Tâm thần</option>';
							html = html + option ;
							html = html + '</select>';
							$("#"+_gridHSBA).jqGrid('setCell',rowIds[j],'LOAIBARV', html);
							if (_row.LOAIBARVID != "") {
								$('#' + _row.HOSOBENHANID + "_cboLOAIBAID").val(_row.LOAIBARVID);
							}else{
								$('#' + _row.HOSOBENHANID + "_cboLOAIBAID").val(1);
							}
							if(dsBanghi.length > 0){
								var  index = dsBanghi.findIndex(obj  => obj.HOSOBENHANID == _row.HOSOBENHANID);
								if (index != -1 ) {
									$('#' + _row.HOSOBENHANID + "_cboLOAIBAID").val(dsBanghi[index].LOAIBARVID);
								}
							}
						}
					}
				}
			}

		});
		var hienthiinphieuExcel = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_HIENTHI_INPHIEUEXCEL');
		if(hienthiinphieuExcel == 1){
			$("#btnExportExcel").show();
		}

		var _excel = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_LUUTRU_EXCEL');
		if(_excel == "1"){
			$('#btnInDanhSach').css('display','');
		}
		if (opt.hospital_id == 10284) {
			$("#btnSua").show();
		}
		if (kyChot != '0') {
			$("#btnKyCA").show();
			$("#btnHuyKyCA").show();
			$("#btnEdit").prop( "disabled", true );
		}
	}

	function bindEvent() {
		$.jMaskGlobals = {
			maskElements: 'input,td,span,div',
			dataMaskAttr: '*[data-mask]',
			dataMask: true,
			watchInterval: 300,
			watchInputs: true,
			watchDataMask: true,
			byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
			translation: {
				'0': {pattern: /\d/},
				'9': {pattern: /\d/, optional: true},
				'#': {pattern: /\d/, recursive: true},
				'A': {pattern: /[a-zA-Z0-9]/},
				'S': {pattern: /[a-zA-Z]/}
			}
		};
		$('#cboKHOAID_SEARCH').on('change', function (e) {
			loadNguoiBanGiao();
			loadMaBanGiao();
			var _val = $('#cboKHOAID_SEARCH').val();
			ComboUtil.getComboTag("cboPHONGID_SEARCH","DMC.NV.06",[{"name":"[0]","value":_val}], "",{text:"--Lựa chọn--", value:-1},"sql");
		});
		$('#txtTUNGAY').on('change', function (e) {
			loadMaBanGiao();
		});
		$('#txtDENNGAY').on('change', function (e) {
			loadMaBanGiao();
		});
		$('#cboNGUOIBANGIAO').on('change', function (e) {
			loadMaBanGiao();
		});
		//cap nhat
		_capnhat();
		//lam moi trang
		_refreshPage();
		//in phieu
		_inPhieu();
		//tim kiem
		_searchHosobenhan();
		EventUtil.setEvent("assignSevice_updloaibenhan", function(e) {
			DlgUtil.showMsg(e.msg);
			loadDataGrid();
			DlgUtil.close("divDlgLoaiBA");
		});

		//ky so
		_kyCA();

		$('#btnKqEmr').on('click', function (e) {
			var _width = $(document).width() - 250;
			var _height = $(window).height() - 100;
			dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H100_DayBenhAnEMR", [], "HIS - Đẩy hồ sơ bệnh án điện tử", _width, _height);
			DlgUtil.open("divDlgBA");
		});

		$('#cboKYBANGIAO').on('change', function (e) {
			loadDataGrid();
			if ($('#cboKYBANGIAO').val() == '1') {
				$('#btnEdit').prop( "disabled", false );
			} else {
				$('#btnEdit').prop( "disabled", true );
			}
		});
	}
	function loadDetail(mabenhnhan,sovaovien,hosobenhanid,mahosobenhan){
		var _sql_par=[mabenhnhan,sovaovien,hosobenhanid,mahosobenhan];
		var data_ba_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.SO_HSBA",_sql_par.join('$'));
		if (data_ba_ar != null && data_ba_ar.length > 0) {
			var data = data_ba_ar[0];
			FormUtil.clearForm("divLTHS", "");
			FormUtil.setObjectToForm("divLTHS", "", data);
			//lay so luu tru benh an
			var _soluutru = $('#txtSOLUUTRU').val();
			if(_soluutru == ''){
				var _parram = [mabenhnhan,sovaovien,hosobenhanid,mahosobenhan];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.LAYMA_HSBA",_parram.join('$'));
				if (data_ar != null && data_ar.length > 0) {
					var _data = data_ar[0];
					$('#txtSOLUUTRU').val(_data.SOLUUTRU);
					var _namhsluutru = Number(_data.SOLUUTRU.substring(0,2));
					if(_namhsluutru>=18){
						$('#txtSOLUUTRU').prop('disabled', true);//k cho phep sua so luu tru
						_solutru_dalay = _data.SOLUUTRU; //luu lai gia tri da lay de check lai khi luu
					}else
						$('#txtSOLUUTRU').prop('disabled', false);
				}
			}
		}
	}
	function loadNguoiBanGiao(){
		var sql_par1=[];
		sql_par1.push({"name":"[0]","value":$("#cboKHOAID_SEARCH").val()});
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			value : opt._userid
		});
		var rs = jsonrpc.AjaxJson.getOneValue("GET.OFFICERID", _sql_par);
		var rs1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("GET.OFFICERID", _sql_par);
		var rows = $.parseJSON(rs1);
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_LOADDSNV_TOANVIEN') == 1){
			ComboUtil.getComboTag("cboNGUOIBANGIAO","BAN_DSNV_TOANVIEN", sql_par,"",{value:'0',text:'--Lựa chọn--'},"sql");
		}else {
			ComboUtil.getComboTag("cboNGUOIBANGIAO","BAN.BGBA.NBG_DK1",sql_par1,"",{value:'0',text:'--Lựa chọn--'},'sql','',function(event, ui) {
				if (opt.hospital_id == 930) {
					$("#cboNGUOIBANGIAO").prop('disabled',true);
					if (rows[0].DEPT_ID == $("#cboKHOAID_SEARCH").val()){
						$("#cboNGUOIBANGIAO").val(rs);
					}else {
						$("#cboNGUOIBANGIAO").val('');
					}
				} else if (cf.BAN_BANGIAO_AUTO_NGUOIBANGIAO == '1') {
					$("#cboNGUOIBANGIAO").val(rs);
				}
			});
		}
	}

	function loadMaBanGiao(){
		var sql_par1=[];
		sql_par1.push({"name":"[0]","value":nvl($("#txtTUNGAY").val(),'1')});
		sql_par1.push({"name":"[1]","value":nvl($("#txtDENNGAY").val(),'1')});
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_LOADMABG_KHOATL') == 1){
			sql_par1.push({"name":"[2]","value":opt._deptId });
		}else{
			sql_par1.push({"name":"[2]","value":$("#cboKHOAID_SEARCH").val()});
		}
		sql_par1.push({"name":"[3]","value":$("#cboNGUOIBANGIAO").val()});
		ComboUtil.getComboTag("cboMABANGIAO","BAN.BANGIAO.MA.NEW2",sql_par1,"",{value:'0',text:'--Lựa chọn--'},'sql','','');
	}

	function _viewDetailHSBA(rowId){
		FormUtil.clearForm("divLTHS","");
		var rowData = $('#' + _gridHSBA).jqGrid('getRowData', rowId);
		loadDetail("","",rowData.HOSOBENHANID,"");
	}

	function _validateParam(){
		if(dsThem.length == 0){
			DlgUtil.showMsg("Bạn chưa chọn hồ sơ bệnh án của bệnh nhân, không thể thực hiện!");
			return false;
		}
		if($('#cboNGUOIBANGIAO').val()=='0'){
			DlgUtil.showMsg("Bạn chưa chọn người bàn giao, không thể thực hiện!");
			$('#cboNGUOIBANGIAO').focus();
			return false;
		}
		if(batbuoc_chonkhoa == 1 ) {
			if($('#cboKHOAID_SEARCH').val()=='-1'){
				DlgUtil.showMsg("Bạn chưa chọn khoa không thể thực hiện!");
				$('#cboKHOAID_SEARCH').focus();
				return false;
			}
		}
		if (kyChot != '0') {
			if ($('#cboKHOAID_SEARCH').val() != '-1' && loaitiepnhanid == '1') {
				//ngoaitru cho ban giao ko can ky
			} else if ($('#cboKYBANGIAO').val() != '1') {
				DlgUtil.showMsg("Bệnh án chưa ký bàn giao không thể bàn giao!");
				$('#cboKYBANGIAO').focus();
				return false;
			}
		}
		return true;
	}

	function _searchHosobenhan(){
		$("#btnTIMKIEM").bindOnce("click", function() {
			loadDataGrid();
		},500)
	}

	function loadDataGrid(){
		dsThem = [];
		dsBanghi = [];
		// var lookup_sql = "BAN.BANGIAO.TIMKIEM2";
	
		var objData = new Object();
		objData.TUNGAY = nvl($("#txtTUNGAY").val(),'-1');
		objData.DENNGAY = nvl($("#txtDENNGAY").val(),'-1');
		objData.LOAIDIEUTRI = nvl($("#cboLOAIDIEUTRI").val(),'-1');
		objData.SOVAOVIEN = nvl($("#txtSOVAOVIEN_SEARCH").val(),'');
		objData.KHOAID = $("#cboKHOAID_SEARCH").val();
		objData.PHONGID = $("#cboPHONGID_SEARCH").val();
		objData.NGAYLOC = $("#cboNGAYLOC").val();
		objData.LOAITIEPNHANID = $("#cboLOAITIEPNHANID").val();
		objData.KYBANGIAO = $("#cboKYBANGIAO").val();
		var lookup_sql = "BAN.BANGIAO.TIMKIEM4";
		if (cf.BAN_GET_DATAINFO_NEW == '1') {
			lookup_sql = "BAN.BANGIAO.TIMKIEM5";
		}
		var _sql_par = [ {
			"name" : "[0]",
			"value" : JSON.stringify(objData)
		} ];
		GridUtil.loadGridBySqlPage(_gridHSBA,lookup_sql,_sql_par,function(){
			$(".jqgrow", '#' + _gridHSBA).contextMenu('contextMenu_TC', {
				bindings: {
					'rTraCuu': function (t) {
						var selRowId = $("#"+_gridHSBA).jqGrid("getGridParam","selrow");
						var maba = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'MAHOSOBENHAN');
						maba = nvl(maba,"");
						//window.open("/vnpthis/main/manager.jsp?func=../noitru/NTU02D059_TraCuuBenhNhan&type=" +maba, '_blank');
						window.open('manager.jsp?func=../noitru/NTU02D059_TraCuuBenhNhan', maba);
					},
					'rBAChitiet': function (t) {
						var selRowId = $("#"+_gridHSBA).jqGrid("getGridParam","selrow");
						var hosobenhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'HOSOBENHANID');
						var benhnhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'BENHNHANID');
						var loaibenhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'LOAIBENHANID');
						var khambenhid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'KHAMBENHID');
						var object = {};
						object["HOSOBENHANID"] = hosobenhanid;
						object["BENHNHANID"] = benhnhanid;
						var _loaibadaingay=null;
						var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
						// if (!dataDaingay) {
						// 	DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
						// 	return;
						// }
						// _hosobenhanid = dataDaingay.HOSOBENHANID;
						var dataDaingay = {};
						if (dataDaingays && dataDaingays.length > 0) {
							dataDaingay = dataDaingays[0];
							_loaibadaingay = dataDaingay.LOAIBENHANID;
						}

						var _sql_par1 = [];
						if(_loaibadaingay!=null && _loaibadaingay==36){
							_sql_par1=RSUtil.buildParam("",[36]);
							_hosobenhanid=dataDaingay.HOSOBENHANID;
							_loaibenhanid=_loaibadaingay;
						}else{
							_sql_par1=RSUtil.buildParam("",[loaibenhanid]);
							_hosobenhanid=hosobenhanid;
							_loaibenhanid=loaibenhanid;
						}

						var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
						var _rows1 = JSON.parse(_data1);
						var _sreenName=_rows1[0].URL;
						var _tenloaibenhan=_rows1[0].TENLOAIBENHAN
						var _maloaibenhan=_rows1[0].MALOAIBENHAN;

						paramInput={
							khambenhid : khambenhid,
							hosobenhanid : _hosobenhanid,
							benhnhanid :   benhnhanid,
							loaibenhanid : _loaibenhanid,
							maloaibenhan : _maloaibenhan
						};
						dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+_sreenName,paramInput,"Cập nhật " +_tenloaibenhan,1300,700);
						DlgUtil.open("divDlgBenhAnDetail");
					},
					'rTOMTATBA': function (t) {
						var selRowId = $("#"+_gridHSBA).jqGrid("getGridParam","selrow");
						var hosobenhanid_data = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'HOSOBENHANID');
						var benhnhanid_data = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'BENHNHANID');
						var khambenhid_data = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'KHAMBENHID');
						var paramInput={
							khambenhid : khambenhid_data,
							benhnhanid : benhnhanid_data,
							hosobenhanid: hosobenhanid_data
						};
						dlgPopup=DlgUtil.buildPopupUrl("divDlgTomTatBA","divDlg","manager.jsp?func=../noitru/NTU02D0113_DLG_TONGKETBENHAN",paramInput,"Tóm tắt bệnh án",1300,650);//L2PT-76964
						DlgUtil.open("divDlgTomTatBA");//
					},
					'rLOAIBA': function (t) {
						var selRowId = $("#"+_gridHSBA).jqGrid("getGridParam","selrow");
						var hosobenhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'HOSOBENHANID');
						var benhnhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'BENHNHANID');
						var loaibenhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'LOAIBENHANID');
						var khambenhid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'KHAMBENHID');
						var object = {};
						object["HOSOBENHANID"] = hosobenhanid;
						object["BENHNHANID"] = benhnhanid;
						var _loaibadaingay=null;
						var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
						var dataDaingay = {};
						if (dataDaingays && dataDaingays.length > 0) {
							dataDaingay = dataDaingays[0];
							_loaibadaingay = dataDaingay.LOAIBENHANID;
						}

						var _sql_par1 = [];
						if(_loaibadaingay!=null && _loaibadaingay==36){
							_sql_par1=RSUtil.buildParam("",[36]);
							_hosobenhanid=dataDaingay.HOSOBENHANID;
							_loaibenhanid=_loaibadaingay;
						}else{
							_sql_par1=RSUtil.buildParam("",[loaibenhanid]);
							_hosobenhanid=hosobenhanid;
							_loaibenhanid=loaibenhanid;
						}

						var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
						var _rows1 = JSON.parse(_data1);
						var _tenloaibenhan=_rows1[0].TENLOAIBENHAN

						var paramInput={
							khambenhid : khambenhid,
							hosobenhanid: hosobenhanid,
							loaibenhanid  : loaibenhanid,
							tenloaibenhan :  _tenloaibenhan
						};
						dlgPopup=DlgUtil.buildPopupUrl("divDlgLoaiBA","divDlg","manager.jsp?func=../benhan/BAN_LOAIBENHAN",paramInput,"Loại bệnh án",700,200);
						DlgUtil.open("divDlgLoaiBA");
					},
					'rQLBenhAn': function (t) {
						var selRowId = $("#"+_gridHSBA).jqGrid("getGridParam","selrow");
						var hosobenhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'HOSOBENHANID');
						var khambenhid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'KHAMBENHID');
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H101.01", hosobenhanid);
						if (data_ar != null && data_ar.length > 0) {
							var rowData = data_ar[0];
							var paramInput = {
								khambenhid: khambenhid,
								tiepnhanid: rowData.TIEPNHANID,
								hosobenhanid: hosobenhanid,
								mabenhan: rowData.MABENHAN,
								tenbenhnhan: rowData.TENBENHNHAN,
								tuoi: rowData.TUOI,
								diachi: rowData.DIACHI
							};
							var _width = $(document).width() - 50;
							var _height = $(window).height() - 50;
							DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Quản lý bệnh án", _width, _height);
							DlgUtil.open("divDlgBA");
						}
					},
					'rHoanThanhKy': function (t) {
						var selRowId = $("#"+_gridHSBA).jqGrid("getGridParam","selrow");
						var hosobenhanid = $("#"+_gridHSBA).jqGrid('getCell', selRowId, 'HOSOBENHANID');
						var paramInput = {
							hosobenhanid: hosobenhanid
						};
						var _width = $(document).width() - 250;
						var _height = $(window).height() - 100;
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDSP", "divDlg", "manager.jsp?func=../noitru/NTU01H100_DanhSachPhieu", paramInput, "HIS - Phiếu chưa hoàn thành ký", _width, _height);
						DlgUtil.open("divDlgDSP");
					},
				},
				onContextMenu: function (event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#' + _gridHSBA);
					_setSelectionOnContextMenu(_gridHSBA, rowId);
					return true;
				},
			});
		});
		if(BADAINGAY == 1){
			var sql_par=[];
			sql_par.push({"name":"[0]","value":nvl($("#txtTUNGAY").val(),'-1')});
			sql_par.push({"name":"[1]","value":nvl($("#txtDENNGAY").val(),'-1')});
			sql_par.push({"name":"[2]","value":''});
			sql_par.push({"name":"[3]","value":''});
			sql_par.push({"name":"[4]","value":''});
			sql_par.push({"name":"[5]","value":nvl($("#cboLOAIDIEUTRI").val(),'-1')});
			sql_par.push({"name":"[6]","value":nvl($("#txtSOVAOVIEN_SEARCH").val(),'')});
			sql_par.push({"name":"[7]","value":$("#cboKHOAID_SEARCH").val()});
			sql_par.push({"name":"[8]","value":$("#cboNGAYLOC").val()});
			sql_par.push({"name":"[9]","value":'0'});
			sql_par.push({"name":"[10]","value":$("#cboPHONGID_SEARCH").val()});
			sql_par.push({"name":"[11]","value":$("#cboLOAITIEPNHANID").val()});
			sql_par.push({"name":"[12]","value": -1});
			sql_par.push({"name":"[13]","value": -1});
			var lookup_sql2 = "BAN.BG.DN.TIMKIEM.1";
			GridUtil.loadGridBySqlPage(_gridHSBA_DAINGAY,lookup_sql2,sql_par,function(){
				$(".jqgrow", '#' + _gridHSBA_DAINGAY).contextMenu('contextMenu_TC1', {
					bindings: {
						'rTraCuu1': function (t) {
							var selRowId = $("#"+_gridHSBA_DAINGAY).jqGrid("getGridParam","selrow");
							var maba = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'MAHOSOBENHAN');
							maba = nvl(maba,"");
							window.open('manager.jsp?func=../noitru/NTU02D059_TraCuuBenhNhan', maba);
						},
						'rBAChitiet1': function (t) {
							var selRowId = $("#"+_gridHSBA_DAINGAY).jqGrid("getGridParam","selrow");
							var hosobenhanid = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'HOSOBENHANID');
							var benhnhanid = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'BENHNHANID');
							var khambenhid = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'KHAMBENHID');
							var _hosobenhanid_hientai = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'HOSOBENHANID_HIENTAI');
							var _sql_par1 = [];
							_sql_par1=RSUtil.buildParam("",[36]);
							_hosobenhanid=hosobenhanid;
							_loaibenhanid=36;
							var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
							var _rows1 = JSON.parse(_data1);
							var _sreenName=_rows1[0].URL;
							var _tenloaibenhan=_rows1[0].TENLOAIBENHAN
							var _maloaibenhan=_rows1[0].MALOAIBENHAN;

							paramInput={
								khambenhid : khambenhid,
								hosobenhanid : _hosobenhanid,
								benhnhanid :   benhnhanid,
								loaibenhanid : _loaibenhanid,
								maloaibenhan : _maloaibenhan,
								hosobenhanid_hientai :  _hosobenhanid_hientai
							};
							dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+_sreenName,paramInput,"Cập nhật " +_tenloaibenhan,1300,700);
							DlgUtil.open("divDlgBenhAnDetail");
						},
						'rTOMTATBA1': function (t) {
							var selRowId = $("#"+_gridHSBA_DAINGAY).jqGrid("getGridParam","selrow");
							var hosobenhanid_data = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'HOSOBENHANID');
							var benhnhanid_data = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'BENHNHANID');
							var khambenhid_data = $("#"+_gridHSBA_DAINGAY).jqGrid('getCell', selRowId, 'KHAMBENHID');
							var paramInput={
								khambenhid : khambenhid_data,
								benhnhanid : benhnhanid_data,
								hosobenhanid: hosobenhanid_data
							};
							dlgPopup=DlgUtil.buildPopupUrl("divDlgTomTatBA","divDlg","manager.jsp?func=../noitru/NTU02D0113_DLG_TONGKETBENHAN",paramInput,"Tóm tắt bệnh án",1000,450);
							DlgUtil.open("divDlgTomTatBA");//
						},
					},
					onContextMenu: function (event, menu) {
						var rowId = $(event.target).parent("tr").attr("id");
						var grid = $('#' + _gridHSBA_DAINGAY);
						_setSelectionOnContextMenu(_gridHSBA_DAINGAY, rowId);
						return true;
					},
				});
			});
		}
	}
	//cap nhat
	function _capnhat(){
		$("#btnEdit").bindOnce("click", function() {
			if (_validateParam()) {
				_banGiao();
			}
		},500);


		$("#btnSua").on("click", function(e) {
			var rowKeys_select = $("#"+_gridHSBA).getGridParam('selarrrow');
			var rowKeysDA_select = 0;
			if(BADAINGAY == 1){
				rowKeysDA_select = $("#"+_gridHSBA_DAINGAY).getGridParam('selarrrow');
			}
			if(rowKeys_select.length == 0 && rowKeysDA_select == 0){
				DlgUtil.showMsg("Chọn 1 hồ sơ bệnh án để thực hiện!");
				return;
			}
			if($("#cboNGUOINHAN").val() == 0){
				DlgUtil.showMsg("Chưa chọn người nhận hồ sơ!");
				return;
			}
			var par_send = [];

			for(var i=0; i<rowKeys_select.length; i++) {
				var rowObject = $("#"+_gridHSBA).getRowData(rowKeys_select[i]);
				rowObject.LOAIBENHANRV  = $('#' + rowObject.HOSOBENHANID + "_cboLOAIBAID").val();
				par_send.push(rowObject);
			}
			if(BADAINGAY == 1){
				for(var i=0; i<rowKeysDA_select.length; i++) {
					var rowObject = $("#"+_gridHSBA_DAINGAY).getRowData(rowKeysDA_select[i]);
					rowObject.LOAIBENHANRV = $('#' + rowObject.HOSOBENHANID + "_cboLOAIBAID").val();

					par_send.push(rowObject);
				}
			}
			var param_select = JSON.stringify(par_send);
			var _result=jsonrpc.AjaxJson.ajaxCALL_SP_S("BAN.BANGIAO.CAPNHAT",param_select);

			if(_result == '1' ){
				DlgUtil.showMsg("Cập nhật hồ sơ bệnh án thành công");
				loadDataGrid();
			}
			else if(_result == '-1'){
				DlgUtil.showMsg("Cập nhật hồ sơ bệnh án không thành công!");
			}
		});
	}

	function _kyCA(){
		$("#btnKyCA, #btnHuyKyCA").click(function (e) {
			var signType = $(e.currentTarget).data("catype") + "";
			if (signType == '2') {
				//validate hủy ký số
				if (dsThem.length != '1') {
					DlgUtil.showMsg("Chỉ cho phép hủy từng bệnh án!");
					return
				}
				var selRowId = $('#' + _gridHSBA).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridHSBA).jqGrid('getRowData', selRowId);
				if (rowData.KY_BANGIAO == '0') {
					DlgUtil.showMsg("Bệnh án chưa thực hiện ký bàn giao!");
					return
				}
			}
			if (dsThem.length == 0) {
				DlgUtil.showMsg("Bạn chưa chọn hồ sơ bệnh án của bệnh nhân, không thể thực hiện!");
				return false;
			}
			if (cf.EMR_KYCHOT_KYDIENTU == '1' || kyChot == '99') {
				catype = '0';
				EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
					causer = e.username;
					capassword = e.password;
					DlgUtil.close("divCALOGIN");
					_caRpt(signType);
				});
				EventUtil.setEvent("dlgCaLogin_close", function (e) {
					DlgUtil.close("divCALOGIN");
				});
				var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", 'Ký điện tử', 505, 268);
				popup.open("divCALOGIN");
			} else {
				catype = kyChot;
				if (dsThem.length > 1 && catype == '3') {
					DlgUtil.showMsg("Chỉ được phép chọn 1 bệnh án để ký bàn giao");
					return false;
				}
				var kieuky = '';
				if (catype == '1') {
					kieuky = 'VNPT – ký số';
				} else if (catype == '2') {
					kieuky = 'Ký số - signserver';
				} else if (catype == '3') {
					kieuky = 'Ký Smart CA';
				} else if (catype == '4') {
					kieuky = 'Ký vân tay';
				} else if (catype == '5') {
					kieuky = 'Ký SIGN_TOKEN';
				} else if (catype == '6') {
					kieuky = 'Smart CA(ESeal)';
				} else if (catype == '7') {
					kieuky = 'Smart CA (ESeal 769)';
				} else if (catype == '8') {
					kieuky = 'Smart CA 769';
				}
				if (catype == '3' || catype == '6') {
					var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					var _paramInput = {
						params: null,
						smartca_method: 0
					};
					EventUtil.setEvent("dlgCaLogin_confirm", function () {
						DlgUtil.close("divCALOGIN");
						var _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
						causer = _hisl2SmartCa.token.refresh_token;
						capassword = _hisl2SmartCa.token.access_token;
						smartcauser = _hisl2SmartCa.user.uid;
						_caRpt(signType);
					});
					var hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
						_paramInput.smartca_method = 1;
						var _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
						_popup.open("divCALOGIN");
						return;
					} else {
						EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function (e) {
							if (e.data && e.data.token && e.data.token.access_token) {
								_paramInput.smartca_method = 1;
							}
							DlgUtil.close("dlgCA_SMARTCA_LOGIN");
							var _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						});
						DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {
							isSignPopup: true
						}, "Smart Ca Login", 500, 650);
						DlgUtil.open("dlgCA_SMARTCA_LOGIN");
						return;
					}
				} else {
					EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
						causer = e.username;
						capassword = e.password;
						DlgUtil.close("divCALOGIN");
						_caRpt(signType);
					});
					EventUtil.setEvent("dlgCaLogin_close", function (e) {
						DlgUtil.close("divCALOGIN");
					});
					var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
					popup.open("divCALOGIN");
				}
			}
		});
	}
	function _refreshPage() {
		$("#btnRefesh").on("click", function(e) {
			FormUtil.clearForm("divLTHS","");
		})
	}
	$("#grdSOHOSOBENHAN").bind("jqGridAfterLoadComplete", function (e, rowid, orgClickEvent) {
		var ids=$("#grdSOHOSOBENHAN").getGridParam("reccount");
		for(var i = 1; i <= ids; i++){
			var _row = $("#grdSOHOSOBENHAN").jqGrid('getRowData', i);
			if(dsThem.indexOf(_row.HOSOBENHANID) != -1  ){
				$("#grdSOHOSOBENHAN").jqGrid('setSelection', i, false);
			}
		}
	});

	function nvl(value1,value2)
	{
		if (value1 != null && value1 != '')
			return value1;
		return value2.trim();
	}

	function _inPhieu() {
		$("#btnExport").on("click", function(e) {
			var  _mabangiao = $("#cboMABANGIAO").val();
			if(_mabangiao == 0){
				DlgUtil.showMsg("Chưa chọn mã in!");
				return;
			}
			var par = [
				{
					name : 'i_mabangiao',
					type : 'String',
					value : $("#cboMABANGIAO").val()
				},
				{
					name : 'i_type',
					type : 'String',
					value : '1'
				}];
			openReport('window', "NTU_SOLUUTRU_HOSOBENHAN", "pdf", par);
		});
		$("#btnExport_HSBADAINGAY").on("click", function(e) {
			var  _mabangiao = $("#cboMABANGIAO").val();
			if(_mabangiao == 0){
				DlgUtil.showMsg("Chưa chọn mã in!");
				return;
			}
			var par = [
				{
					name : 'i_mabangiao',
					type : 'String',
					value : $("#cboMABANGIAO").val()
				},
				{
					name : 'i_type',
					type : 'String',
					value : '1'
				}];
			openReport('window', "NTU_HOSOBENHAN_DAINGAY", "pdf", par);
		});
		$("#btnExportExcel").on("click", function(e) {
			var  _mabangiao = $("#cboMABANGIAO").val();
			if(_mabangiao == 0){
				DlgUtil.showMsg("Chưa chọn mã in!");
				return;
			}
			var par = [
				{
					name : 'i_mabangiao',
					type : 'String',
					value : $("#cboMABANGIAO").val()
				},
				{
					name : 'i_type',
					type : 'String',
					value : '1'
				}];
			var typeIn = 'XLS';
			CommonUtil.inPhieu('window', 'NTU_SOLUUTRU_HOSOBENHAN', typeIn, par, 'BGHSBA.xls');
		});
	}

	$("#btnExportDSHSBG").on("click", function(e) {
		var par = [ {
			name : 'TU_NGAY',
			type : 'String',
			value : nvl($("#txtTUNGAY").val(),'-1')
		}, {
			name : 'DEN_NGAY',
			type : 'String',
			value : nvl($("#txtDENNGAY").val(),'-1')
		}, {
			name : 'MABENHNHAN',
			type : 'String',
			value : ''
		}, {
			name : 'TENBENHNHAN',
			type : 'String',
			value : ''
		}, {
			name : 'MABENHAN',
			type : 'String',
			value : ''
		}, {
			name : 'TRANGTHAI_BG',
			type : 'String',
			value : '-1'
		}, {
			name : 'SOVAOVIEN',
			type : 'String',
			value : nvl($("#txtSOVAOVIEN_SEARCH").val(),'')
		}, {
			name : 'KHOA_ID',
			type : 'String',
			value : $("#cboKHOAID_SEARCH").val()
		}, {
			name : 'TYPE',
			type : 'String',
			value : '1'
		}, ];
		openReport('window', "rpt_DS_BAN_BANGIAOHOSO", 'pdf', par);
	});

	$("#btnInDanhSach").on("click", function(e) {
		var _mabangiao = $("#cboMABANGIAO").val();
		var par = [ {
			name : 'i_dtrv_tungay',
			type : 'String',
			value : nvl($("#txtTUNGAY").val(),'-1')
		}, {
			name : 'i_dtrv_denngay',
			type : 'String',
			value : nvl($("#txtDENNGAY").val(),'-1')
		}, {
			name : 'i_mabenhnhan',
			type : 'String',
			value : ''
		}, {
			name : 'i_tenbenhnhan',
			type : 'String',
			value : ''
		}, {
			name : 'i_mabenhan',
			type : 'String',
			value : ''
		}, {
			name : 'i_trangthai_bg',
			type : 'String',
			value : nvl($("#cboLOAIDIEUTRI").val(),'-1')
		}, {
			name : 'i_sovaovien',
			type : 'String',
			value : nvl($("#txtSOVAOVIEN_SEARCH").val(),'')
		}, {
			name : 'i_khoaid',
			type : 'String',
			value : $("#cboKHOAID_SEARCH").val()
		}, {
			name : 'i_type',
			type : 'String',
			value : $("#cboNGAYLOC").val()
		}, {
			name : 'i_kholuu',
			type : 'String',
			value : '0'
		},
		{
			name : 'i_phongid',
			type : 'String',
			value : '-1'
		},
		{
			name : 'i_loaitiepnhanid',
			type : 'String',
			value : '-1'
		},
		{
			name : 'i_doituongbenhnhanid',
			type : 'String',
			value : '-1'
		},
		{
			name : 'i_xutrikhambenhid',
			type : 'String',
			value : '-1'
		}];
		var typeIn = 'XLS';
		CommonUtil.inPhieu('window', 'RPT_BAN_BANGIAO_EXCELL', typeIn, par, 'ds_bangiao.xls');
	});

	function  _banGiao() {
		var par_send = [];
		var dsBanghi_dayEMR = dsBanghi;

		for (var i = 0; i < dsBanghi.length; i++) {
			if (Grid_HNI == 1) {
				if (dsBanghi[i].TRANGTHAI_BANGIAO == 0 || dsBanghi[i].TRANGTHAI_BANGIAO == 2) {
					if((dsBanghi[i].LOAIBARVID == '' || dsBanghi[i].LOAIBARVID == null || dsBanghi[i].LOAIBARVID == undefined) && Grid_HNI == 1){
						DlgUtil.showMsg("Tồn tại hồ sơ chưa chọn Loại BA RV !");
						return false;
					}
					dsBanghi[i].KHOAID = opt._deptId ;
					par_send.push(dsBanghi[i]);
				}
			} else {
				dsBanghi[i].KHOAID = opt._deptId ;
				var rowObject = dsBanghi[i];
				if (rowObject.TRANGTHAI_BANGIAO == 0 || rowObject.TRANGTHAI_BANGIAO == 2) {
					par_send.push(rowObject);
				}
			}

		}
		if (par_send.length == 0) {
			DlgUtil.showMsg("Danh sách hồ sơ được chọn đã được bàn giao, không thể bàn giao lại!");
			return false;
		}
		var param_select = JSON.stringify(par_send);
		var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S("BAN.BANGIAO.CHUYENHS", $('#cboNGUOIBANGIAO').val() + "$" + param_select);
		var ret = _result.split(';');
		if (ret.length > 1 && ret[0] == 1) {
			//gửi EMR khi bàn giao hồ sơ
			var _dayTong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'EMR_DAYTONG_BANGIAOHOSO');
			if (_dayTong !== '0') {
				if (_dayTong == '3') {
					var objData = new Object();
					objData.LSTHOSOBENHANID = dsThem;
					var oData = {
						FUNCTION: 'HIS_PATIENT_HANDOVER_EMR',
						DATA: objData
					};
					var result = $.ajax({
						url: '/vnpthis/api/v1/provider',
						type: "POST",
						data: JSON.stringify(oData),
						contentType: 'application/json; charset=utf-8',
						dataType: "json",
						async: false
					}).done(function (_response) {
						_rs = _response;
					});
					if (_rs.code == 0) {
						DlgUtil.showMsg("Chuyển lưu trữ hồ sơ bệnh án thành công");
					} else {
						DlgUtil.showMsg(_rs.message);
					}
				} else {
					var callback = function () {
						if ($.active !== 0) {
							setTimeout(callback, '100');
							return;
						}
						for (var i = 0; i < dsBanghi_dayEMR.length; i++) {
							if (dsBanghi_dayEMR[i].TRANGTHAI_BANGIAO == 0 || dsBanghi_dayEMR[i].TRANGTHAI_BANGIAO == 2) {
								if (dsBanghi_dayEMR[i].DAINGAY == 1) {
									var rowObject = $("#" + _gridHSBA_DAINGAY).jqGrid('getRowData', dsBanghi_dayEMR[i].ROWID);
								} else {
									var rowObject = $("#" + _gridHSBA).jqGrid('getRowData', dsBanghi_dayEMR[i].ROWID);
								}
								CommonUtil.sendAllEmr2(rowObject.HOSOBENHANID);
							}
						}
					};
					callback();
				}
			} else {
				DlgUtil.showMsg("Chuyển lưu trữ hồ sơ bệnh án thành công");
			}

			loadDataGrid();
			dsThem = [];
			dsBanghi = [];
			loadMaBanGiao();
			if (tudong_inphieu == 1) {
				var _mabangiao = ret[1];
				var par = [
					{
						name: 'i_mabangiao',
						type: 'String',
						value: _mabangiao
					},
					{
						name: 'i_type',
						type: 'String',
						value: '1'
					}];
				openReport('window', "NTU_SOLUUTRU_HOSOBENHAN", "pdf", par);
			}
			//lam moi trang
			FormUtil.clearForm("divLTHS", "");

		}else if(ret.length > 1 && ret[0] == 'tontaidabangiao') {
			DlgUtil.showMsg("Danh sách bàn giao hồ sơ bệnh án tồn tại bệnh án "+ ret[1] +" đã bàn giao, vui lòng kiểm tra lại!");
			return;
		} else if (ret.length > 1 && ret[0] !== 1) {
			DlgUtil.showMsg("Danh sách bàn giao hồ sơ bệnh án có : " + _result +
				" chưa duyệt kế toán không thể bàn giao !");
		} else {
			DlgUtil.showMsg("Chuyển lưu trữ hồ sơ bệnh án không thành công!");
		}
	}

	function _caRpt(signType) {
		var objData = new Object();
		objData.LSTHOSOBENHANID = dsThem;
		var oData = {
			sign_type: signType,
			causer: causer,
			capassword: capassword,
			smartcauser: smartcauser,
			level_ky: '9',//them để xuất đúng xml cuối
			catype: catype,
			data: objData
		};
		var result = $.ajax({
			url: '/vnpthis/apicabghs',
			type: "POST",
			data: JSON.stringify(oData),
			contentType: 'application/json; charset=utf-8',
			dataType: "json",
			async: false
		}).done(function (_response) {
			_rs = _response;
		});
		dsThem = [];
		loadDataGrid();
		if (_rs == null) {
			DlgUtil.showMsg("Thất bại!");
		} else if (_rs.CODE == 0) {
			if (signType == '1') {
				var _msg = 'Ký bàn giao thành công!' + '</br>' + _rs.MESSAGE;
				DlgUtil.showMsg(_msg);
			} else {
				DlgUtil.showMsg('Hủy ký bàn giao thành công!');
			}
		} else {
			DlgUtil.showMsg("Có lỗi xảy ra!");
		}
	}
	//Beg_HaNv_290324: setSelection onContextMenu(chuột trái) với grid checkbox không bỏ chọn với dòng đã được chọn - L2PT-74241
	function _setSelectionOnContextMenu(_gridId, _rowId) {
		var grid = $('#' + _gridId);
		var rowDatas = grid.jqGrid("getGridParam", "selarrrow");
		var check = true;
		if (rowDatas.length > 0) {
			for (var j = 0; j < rowDatas.length; j++) {
				if (rowDatas[j] == _rowId) {
					check = false;
				}
			}
			if (check) {
				grid.setSelection(_rowId);
			}
		} else {
			grid.setSelection(_rowId);
		}
	}
}