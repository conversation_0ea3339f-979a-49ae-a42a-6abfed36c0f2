/*
 Mã màn hình  : VPI01T001
 File mã nguồn : VPI01T001_thuvienphi.js
 <PERSON><PERSON><PERSON> đích  : Thu tiền dịch vụ
 <PERSON>ham số vào :
 <PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON><PERSON><PERSON>				 5/10/2016		comment
*/
var _THUTIEN = 1;
var _TAMUNG = 3;
var _HOANUNG = 2;
var _THUTHEM = 6;
var _HOANTRA = 7;
var _HOANDICHVU = 8;
var _DSBARCODE = [];
var showExtInf = false; // L2PT-48810
function VPI01T012(_opts) {
	var _gridSQL_BN = "VPI01T012.DS.DT";
	var _gridSQL_DV = "VPI01T012.02.MBP";
	var _gridSQL_PT = "VPI01T012.03";
	var _sql_sophieuthu = "VPI01T001.13";// chung
	var _sql_huyphieuthu = "VPI01T001.09";// chung
	var _sql_thuvienphi = "VPI02T001.10";
	var _sql_tt_phieuthu = "VPI01T001.14.1";// chung // L2PT-56749
	var _gridId_BN = "grdDSBenhNhan";
	var _gridId_DV = "grdDSDichVu";
	var _gridId_PT = "grdDSPhieuThu";
	var _gridId_BT = "grdDSBanThuoc";
	// L2PT-22913 thêm KHOID
	// L2PT-48810 Hiển thị tổng tiền và bác sĩ TONGCONG, BACSI
	// Thêm CHANDOAN, KHOA, PHONG, SDTBENHNHAN, KHAMBENH_CANNANG
	// L2PT-56749 fix lỗi TENKHO -> KHOA
	var _gridHeader_BN = " ,ICON_LOAIPHIEU,20,0,ns,l;" + " ,ICON_TRANGTHAI,20,0,ns,l;" + " ,ICON_THUTIEN,20,0,ns,l;" + "XUATID,XUATID,0,0,t,l;" + "nhapxuatid,NHAPXUATID,0,0,t,l;"
			+ "trangthaiid,TRANGTHAIID,0,0,t,l;" + "hinhthucid,HINHTHUCID,0,0,t,l;" + "Phiếu,MA,90,0,f,l;" + "Loại,TEN_KIEU,60,0,f,l;" + "Đơn thuốc/VT,SOPHIEU,90,0,f,l;"
			+ "Họ tên,TENBENHNHAN,150,0,f,l;" + "Mã BA,MAHOSOBENHAN,100,0,f,l;" + "Ghi chú,GHICHU_PT,150,0,f,l;" + "Mã BN,MABENHNHAN,100,0,f,l;" + "Năm sinh,NAMSINH,70,0,t,c;"
			+ "Tổng tiền,TONGCONG,70,0,f,c;" + "Bác sĩ,BACSI,120,0,f,l;" + "CHIETKHAU,CHIETKHAU,70,0,t,c;" + "NGUOINX,NGUOINX,70,0,t,c;" + "Ngày sinh,NGAYSINH,70,0,f,c;" + "GT,GIOITINH,40,0,f,l;"
			+ "Khoa,KHOA,110,0,f,l;" + "Phòng,PHONG,110,0,f,l;" + "Trạng thái,TRANGTHAI,80,0,f,l;" + "Ngày NX,NGAYNX,90,0,f,c;" + "Số CT,SOCHUNGTU,50,0,f,l;" + "CHANDOAN,CHANDOAN,0,0,t,l;"
			+ "kieu,KIEU,0,0,t,l;" + "doiungid,DOIUNGID,0,0,t,l;" + "KHOID,KHOID,0,0,t,l;" + "nhapid,NHAPID,0,0,t,l;" + "xuatid,XUATID,0,0,t,l;" + "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;"
			+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;" + "ttphieunx,TTPHIEUNX,0,0,t,l;" + "Đã thu tiền,DACODICHVUTHUTIEN,0,0,t,l;" + "Trạng thái,TRANGTHAIMAUBENHPHAM,0,0,t,l;"
			+ "Loại phiếu,LOAIPHIEUMAUBENHPHAM,0,0,t,l;" + "Khoa id,KHOAID,0,0,t,l;" + "Phòng id,PHONGID,0,0,t,l;" + "Hồ sơ bệnh án ID,HOSOBENHANID,0,0,t,l;" + "Tiếp nhận id,TIEPNHANID,100,0,t,l,0;"
			+ "Bệnh nhân id,BENHNHANID,0,0,t,l;" + "DIACHIBN,DIACHI,0,0,t,l;" + "Số phiếu bán,SOPHIEUBAN,100,0,f,l;" + "Số phiếu BP,SOPHIEU_BP,100,0,f,l" + ";Chẩn đoán,CHANDOAN,100,0,t,l"
			+ ";Khoa,KHOA,100,0,t,l" + ";Phòng,PHONG,100,0,t,l" + ";Số điện thoại,SDTBENHNHAN,100,0,f,l" + ";Cân nặng,KHAMBENH_CANNANG,100,0,t,l";
	// L2PT-63280: thêm LOAI_TVT
	var _gridHeader_DV = "id,DICHVUKHAMBENHID,0,0,t,l;" + "Tiếp nhận ID,TIEPNHANID,0,0,t,l;" + "Khoa id,KHOAID,0,0,t,l;" + "Phòng id,PHONGID,0,0,t,l;" + "Khoản mục ID,KHOANMUCID,0,0,t,l;"
			+ "Mã khoản mục,MAKHOANMUC,0,0,t,l;" + "Tên khoản mục,TENKHOANMUC,0,0,t,l;" + "Mẫu bệnh phẩm id,MAUBENHPHAMID,0,0,t,l;" + "Loại phiếu,LOAIPHIEUMAUBENHPHAM,0,0,t,l;"
			+ "Đã thu tiền,DATHUTIEN,0,0,t,l;" + "Loại đối tượng,LOAIDOITUONG,0,0,t,l;" + "Mã thuốc,MA,80,0,f,l;" + "Tên thuốc,TENDICHVU,180,0,f,l;" + "Đơn vị tính,DVT,75,0,f,l;"
			+ "Số lượng,SOLUONG,60,0,f,l;" + "Giá tiền,DONGIA_VAT,80,number,f,r;" + "VAT,VAT_D,40,number,f,r;" + "VAT,VAT,0,0,t,l;" + "LOAI_TVT,LOAI_TVT,0,0,t,l;" + "Giá tiền,DONGIA,80,number,t,r;"
			+ "Thành tiền,THANHTIEN_VAT,100,number,t,r;" + "Thành tiền,THANHTIEN,100,number,f,r;" + "Đã nộp,DANOP,0,number,t,r;" + "Check Barcode,CHECKBARCODE,100,number,f,r;"
			+ "Hàm lượng,LIEULUONG,80,0,f,l;" + "Hoạt chất,HOATCHAT,80,0,f,l;" + "Số lô,SOLO,80,0,f,c;" + "HSD,HANSUDUNG,80,0,f,c;" + "CHUY,CHUY,60,0,t,r;" + "SOLUONG_CT,SOLUONG_CT,60,0,t,r;"
			+ "GIABAN,GIABAN,60,0,t,r;" + "THANHTIEN_NX,THANHTIEN_NX,60,0,t,r;" + "XUATVAT,XUATVAT,60,0,t,r;" + "TONKHOCTID,TONKHOCTID,60,0,t,r";
	//L2PT-18469: thêm cột SYNC_FLAG
	// L2PT-115115 thêm cột INVOICES_NUMBER
	var _gridHeader_PT = " ,ICON,20,0,ns,l;" + "id,PHIEUTHUID,100,0,t,l,0;" + "Mặc định,MACDINH,90,0,t,l,0;" + "Đã hủy phiếu,DAHUYPHIEU,90,0,t,l,0;" + "Tiếp nhận id,TIEPNHANID,100,0,t,l,0;"
			+ "Mã phiếu thu,MAPHIEUTHU,80,0,f,l,0;" + "Số HĐĐT,INVOICES_NUMBER,80,0,f,l,0;" + "Số tiền,DATRA,80,number,f,r,0;" + "Loại phiếu thu,LOAIPHIEUTHU,0,0,t,l,0;"
			+ "Loại phiếu thu id,LOAIPHIEUTHUID,80,0,t,l,0;" + "Loại phiếu,LOAIPHIEUTHUID_2,100,0,f,l,0;" + "Ngày thanh toán,NGAYTHU,120,0,f,c,0;" + "SYNC_FLAG,SYNC_FLAG,80,0,t,c,0;"
			+ "Trạng thái,TRANGTHAI_HDDT,80,0,f,c,0;" + "Người thu,NGUOITHU,100,0,f,l,0;" + "Lý do hủy,LYDOHUYPHIEU,100,0,f,l,0;" + "TENCONGTYBN,TENCONGTYBN,100,0,t,l,0;"
			+ "DIACHI_CTYBN,DIACHI_CTYBN,100,0,t,l,0;" + "MASOTHUE_CTYBN,MASOTHUE_CTYBN,100,0,t,l,0;" + "SOTAIKHOAN,SOTAIKHOAN,100,0,t,l,0;" + "TEN_NGANHANG,TEN_NGANHANG,100,0,t,l,0;"
			+ "EMAIL_CTYBN,EMAIL_CTYBN,100,0,t,l,0";
	var _gridHeader_MG = "LOAI,LOAI,100,0,t,l,0;" + "Tên,TEN,150,0,f,l,0;" + "Số tiền,SOTIEN,50,0,e,r,0"; // L2PT-23349
	//L2PT-19304 start
	var _gridHeader_HTTT = "LOAI,TRANGTHAI_ID,100,0,t,l,0;" + "Loại HTTT,TEN_TRANGTHAI,100,0,f,l,0;" + "Số tiền,SOTIEN,50,decimal!2,e,r,0;" + "Đối tượng,DOITUONG,128,0,f,l,ES;"
			+ "Loại,LOAI,128,0,t,l,0";
	//L2PT-19304 end
	var _gridHeader_BT = "THUOCVATTUID,THUOCVATTUID,0,0,t,l,0;" + "Mã,MA_THUOC,60,0,f,l,0;" + "Tên,TEN_THUOC,200,0,f,l,0;" + "Đơn vị,DON_VI_TINH,50,0,f,l,0;" + "Số ĐK,SDK,75,0,f,l,0;"
			+ "Số lượng,SL_YC_ORG,40,number,f,r;" + "Sl YC,SL_YC,40,number,e,r;" + "Đơn giá,DON_GIA,70,number,f,r;" + "VAT%,VAT,40,decimal!3,f,r;" + "Thành tiền,THANH_TIEN,70,decimal!3,f,r;"
			+ "SL khả dụng,SL_KHADUNG,80,0,f,l,0;" + "DONGIAVAT,DON_GIA_VAT,0,0,t,l,0;" + "Số lô,SO_LO,50,0,f,l,0;" + "Hạn sử dụng,HSD,80,0,f,l,0;"
			+ "Gói thầu,GOI_THAU,0,0,t,l,0;Số đăng ký,SDK,0,0,t,l,0;" + "GIA_BHYT,GIABHYT,0,0,t,l,0;" + "GIA_NHANDAN,GIANHANDAN,0,0,t,l,0;" + "GIA_DICHVU,GIADICHVU,0,0,t,l,0;"
			+ "GIA_TRANBHYT,GIATRANBHYT,0,0,t,l,0;" + "CHECKBARCODE,CHECKBARCODE,80,0,t,l,0;" + "THUOCKHO,THUOCKHO,0,0,t,l,0;" + "THUOCHT,THUOCHT,0,0,t,l,0;" + "THUOCKEDON,THUOCKEDON,0,0,t,l,0;"
			+ "ROWNUM,ROWNUM,30,0,t,l,0;" + "ACT,ACT,40,d,f,l,0"
	var _phong_id = -1;
	var flagLoading = false;
	var _fl_tinh = true;
	var _phieuthuid = -1;
	var _maubenhphamid = -1;
	var _khoid = -1; // L2PT-24280
	rowIdMarked = -1;
	rowIdMarked_DV = -1;
	rowIdMarked_PT = -1;
	var _vpData = new Object();
	var _phieuInfo = [];
	// L2PT-23349 start 
	var _arrMG = [];
	var _DMMG = [];
	// L2PT-23349 end
	//L2PT-19304 start
	var _arrHTTT = [];
	var _DM_HTTT = [];
	//L2PT-19304 end
	var _tien_hoadon = 0;
	var obj = new Object();
	var _ds_nhomtt = [];
	var _dsSo_2 = [];
	var obj_2 = new Object();
	var _focus_element = "#gs_SOPHIEU";
	var _hetphieu_2 = false;
	var _hetphieu = false;
	var _co_dv = false;
	var macsyt = ""; // L2PT-56749
	// ds cau hinh
	var VPI_TAO_PT_THUOC = 0; //L2PT-8931
	var VPI_XACNHAN_BANTHUOC = 0;//L2PT-9086
	var PHARMA_BANTHUOC_GODUYET = 0;//L2PT-126934
	var PHARMA_BANTHUOC_TUDONGDUYET = 0;//L2PT-126987
	var SL_BANGHI_HD = 10;
	var VPI_KHONGTACH_HD_VAT = 0;
	var VPI_SUDUNG_DMGC = 0; //L2PT-21476 start
	var HIS_IN_HOADONCHITIET = 0; // in hd ct
	var VPI_HIEN_IDPOST = 0; // L2PT-18894
	// tuyennx_add_start
	var VPI_DAY_HOADONDT = 0;
	var INVOICES_URL_IMPORT = 0;
	var INVOICES_URL_VIEW = 0;
	var INVOICES_URL_CANCEL = 0;
	var INVOICES_WS_USER = 0;
	var INVOICES_WS_PWD = 0;
	var INVOICES_WS_USER_ACC = 0;
	var INVOICES_WS_PWD_ACC = 0;
	var INVOICES_WS_PATTERN = 0;
	var INVOICES_WS_SERIAL = 0;
	var INVOICES_IMPORT_AUTO = 0;
	var VPI_THUTIEN_INLOAIPHIEU = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_THUTIEN_INLOAIPHIEU'); // L2PT-17557
	var VPI_THUTIENTHUOC_DUYETDON = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_THUTIENTHUOC_DUYETDON'); // L2PT-17770
	var VPI_NHAP_GHICHU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_NHAP_GHICHU'); // L2PT-28406
	var VPI_BANTHUOC_MF = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_BANTHUOC_MF'); // L2PT-28406
	var VPI_BANTHUOC_KHO_DF = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_BANTHUOC_KHO_DF'); // L2PT-28406
	var PHARMA_KETNOI_CONG_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PHARMA_KETNOI_CONG_BYT');
	var PHARMA_NT_CHONCONGDUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PHARMA_NT_CHONCONGDUOC');
	var PHARMA_BANTHUOC_GODUYET = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PHARMA_BANTHUOC_GODUYET');
	var PHARMA_BANTHUOC_TUDONGDUYET = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PHARMA_BANTHUOC_TUDONGDUYET');
	var PHARMA_DAYCONG_NEW = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PHARMA_DAYCONG_NEW');
	var VPI_VAT_TACH_HD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_VAT_TACH_HD'); // L2PT-30897
	var VPI_PAY_PMM_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_PAY_PMM_URL');
	var VPI_LOAITHUOC_TACH_HD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_LOAITHUOC_TACH_HD'); // L2PT-63280
	var fConfig = new Object(); // L2PT-63283
	// L2PT-105772 start
	var dsCH = new Object();
	var _config_hddt = [];
	// L2PT-105772 end
	// HUONGPV ADD
	// tuyennx_add_end
	// var that=this;
	this.load = doLoad;
	function doLoad() {
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		var _options = $.extend({}, _opts);
		var _param = _options._param;
		_hospital_id = _param[0];
		_user_name = _param[7];
		_user_id = _param[1];
		_sch = _param[4];
		_hospital_code = _param[5];
		_phong_id = _param[6];
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		// L2PT-63283 start
		// L2PT-76907: them cau hinh VPI_HUYPT_DUYETDT;VPI_KHOIPHUCPHIEU
		var str_ch = 'VPI_LAY_NHIEU_LOAISO;VPI_HUYPT_DUYETDT;VPI_KHOIPHUCPHIEU;VPI_HIENTHI_QRCODE'; // L2PT-65730 // L2PT-77877
		str_ch += ';VPI_IN_HDCT_BT'; // L2PT-83983
		str_ch += ';VPI_FN_IMPORT_HDDT'; // L2PT-80540
		str_ch += ';VPI_HDDT_MTT;INVOICES_WS_PATTERN_BT;INVOICES_WS_SERIAL_BT'; // L2PT-89335: dong bo tu banthuoc 1
		var arrFConfig = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		if (Array.isArray(arrFConfig) && arrFConfig.length > 0) {
			fConfig = arrFConfig[0];
		}
		// L2PT-63283 end
		// L2PT-105772 start
		dsCH = layDsCH(_hospital_id);
		if (dsCH.VPI_N_TKHD == '1') {
			var objT = new Object();
			objT.TNAME = 'TAIKHOAN_HOADON_DIENTU';
			//objT.TMODE = '0';
			//objT.TROW = '1';
			objT.TSTS = 'STATUS';
			objT.THID = 'CSYTID';
			var input = JSON.stringify(objT);
			_config_hddt = jsonrpc.AjaxJson.ajaxCALL_SP_O('T.GET.DATA', input);
		}
		// L2PT-105772 end
		// L2PT-56749 start
		macsyt = _hospital_code;
		$('#txtTU').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		$('#txtDEN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		// L2PT-23641 start
		var par_ctl = [ 'TOOLBAR_MENU_BANTHUOC' ];
		var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', par_ctl.join('$'));
		var ctl_ar = JSON.parse(ctl_par);
		ToolbarUtil.buildVs2('toolbarId', ctl_ar);
		sql_par = RSUtil.buildParam("", [ '4' ]);
		ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO2", sql_par, "", "", "sql", "", false);
		if (VPI_BANTHUOC_KHO_DF != 0) {
			$("#cboKho").val(VPI_BANTHUOC_KHO_DF);
		}
		// L2PT-23641 end
		initPopup_HuyPT();
		loadGridDataBN();
		initPopup_TTBN();
		$(_focus_element).focus();
		// tuyennx_add_start_20181107 L2HOTRO-11895
		var _configArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.LAY.CAUHINH", '$');
		if (_configArr && _configArr.length > 0) {
			_config = _configArr[0];
			VPI_TAO_PT_THUOC = _config.VPI_TAO_PT_THUOC; //L2PT-8931
			VPI_XACNHAN_BANTHUOC = _config.VPI_XACNHAN_BANTHUOC;
			SL_BANGHI_HD = _config.SL_BANGHI_HD;
			VPI_KHONGTACH_HD_VAT = _config.VPI_KHONGTACH_HD_VAT;
			VPI_SUDUNG_DMGC = _config.VPI_SUDUNG_DMGC; //L2PT-21476 
			HIS_IN_HOADONCHITIET = _config.HIS_IN_HOADONCHITIET; // in hd ct
			VPI_HIEN_IDPOST = _config.VPI_HIEN_IDPOST;//L2PT-18894
			VPI_DAY_HOADONDT = _config.VPI_DAY_HOADONDT;
			INVOICES_URL_IMPORT = _config.INVOICES_URL_IMPORT;
			INVOICES_URL_VIEW = _config.INVOICES_URL_VIEW;
			INVOICES_URL_CANCEL = _config.INVOICES_URL_CANCEL;
			INVOICES_WS_USER = _config.INVOICES_WS_USER;
			INVOICES_WS_PWD = _config.INVOICES_WS_PWD;
			INVOICES_WS_USER_ACC = _config.INVOICES_WS_USER_ACC;
			INVOICES_WS_PWD_ACC = _config.INVOICES_WS_PWD_ACC;
			INVOICES_WS_PATTERN = _config.INVOICES_WS_PATTERN;
			INVOICES_WS_SERIAL = _config.INVOICES_WS_SERIAL;
			INVOICES_IMPORT_AUTO = _config.INVOICES_IMPORT_AUTO;
		}
		if (VPI_DAY_HOADONDT == 1) {
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
			if (rs[0].INVOICES_PASS != "" && rs[0].INVOICES_USER != "") {
				INVOICES_WS_USER_ACC = rs[0].INVOICES_USER;
				INVOICES_WS_PWD_ACC = rs[0].INVOICES_PASS;
			}
		}
		// tuyennx_add_end_20181107
		// L2PT-89335 start: dong bo tu banthuoc 1
		if (fConfig.INVOICES_WS_PATTERN_BT && fConfig.INVOICES_WS_PATTERN_BT != 0 && fConfig.INVOICES_WS_SERIAL_BT && fConfig.INVOICES_WS_SERIAL_BT != 0) {
			INVOICES_WS_PATTERN = fConfig.INVOICES_WS_PATTERN_BT;
			INVOICES_WS_SERIAL = fConfig.INVOICES_WS_SERIAL_BT;
		}
		// L2PT-89335 end
		//L2PT-19304 start
		ComboUtil.getComboTag("cboHINHTHUCTHANHTOAN", "VPI.DM.HTTT", [], "", "", "sp", "", "");
		//L2PT-19304 end
		//L2PT-21476 start
		//ComboUtil.getComboTag("cboHINHTHUCTHANHTOAN", "VPI.DM.HTTT", [], "", "", "sp", "", "");
		if (VPI_SUDUNG_DMGC == 1) {
			$("#txtGHICHU").hide();
			$("#cboGHICHU").show();
			ComboUtil.getComboTag("cboGHICHU", "VPI01T012.DMGC", [], "", {
				text : "--- Chọn ---",
				value : ""
			}, "sql", "", "");
		} else {
			$("#txtGHICHU").show();
			$("#cboGHICHU").hide();
		}
		//L2PT-21476 end
		// L2PT-23349 cau hinh ly do mien giam (text or combobox)
		var VPI_MG_HTTT_BANTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_MG_HTTT_BANTHUOC');
		if (VPI_MG_HTTT_BANTHUOC == '1') {
			$("#btnLYDO").show();
			$("#btnHTTT").show();
		}
		// L2PT-23349 end
		// L2PT-15260 start
		var check_kl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI012.CHECKKL', "$");
		if (check_kl != 0) {
			$("#divThemKL").hide();
		}
		// L2PT-15260 end
		//L2PT-7489 start // L2PT-31437 fix // L2PT-18894
		if (VPI_HIEN_IDPOST == 1) {
			$('#divIDPOST').show();
		} else {
			$('#divIDPOST').hide();
		}
		//L2PT-7489 end // L2PT-31437 fix // L2PT-18894
		//L2PT-31437: Start HungND // L2PT-18894
		if (VPI_HIEN_IDPOST == '2') {
			//Init select POS
			var url = VPI_PAY_PMM_URL + "/api/v1.0/public/test/payment/pos"
			//var url = "http://payment.vncare.vn:8088/api/v1.0/public/test/payment/pos"
			var selIDCache = localStorage.getItem('IDPOST') ? localStorage.getItem('IDPOST') : "";
			// Init select POS
			var RESULT = null;
			$.ajax({
				url : url,
				type : 'GET',
				success : function(res) {
					RESULT = res.RESULT;
					var option = ""
					for (var i = 0; i < RESULT.length; i++) {
						if (RESULT[i].posterminalid === selIDCache) {
							option = option + "<option value='" + RESULT[i].posterminalid + "' selected>" + RESULT[i].name + "</option>";
						} else {
							option = option + "<option value='" + RESULT[i].posterminalid + "'>" + RESULT[i].name + "</option>";
						}
					}
					$("#cboPOS").html(option);
				}
			});
		}
		//L2PT-31437: End HungND // L2PT-18894
		// L2PT-48808 start
		var hGridDonThuoc = 300 + 30 + 26 + 26 + 28;
		var hRight = 200 + $("#divRight").height();
		var offsetReserve = 88;
		var hLast = Math.floor(hRight - hGridDonThuoc) + offsetReserve;
		GridUtil.init(_gridId_BN, "100%", "300", "Danh sách đơn thuốc", false, _gridHeader_BN, false);
		var _group = {
			groupField : [ "VAT" ],
			groupColumnShow : [ false ],
			groupText : [ '<b>{0}</b>' ]
		};
		GridUtil.initGroup(_gridId_DV, "100%", "200", "", true, _group, _gridHeader_DV, false, {
			rowNum : 999999999,
			rowList : [ 999999999 ]
		});
		$("#" + _gridId_DV)[0].toggleToolbar();
		// L2PT-56749 căn chỉnh
		GridUtil.init(_gridId_PT, "100%", hLast, "", false, _gridHeader_PT, false, {
			rowNum : 100,
			rowList : [ 100, 200, 300 ]
		});
		$("#" + _gridId_PT)[0].toggleToolbar();
		$("#" + _gridId_DV).hideCol('cb');
		GridUtil.init(_gridId_BT, "100%", "200", "", false, _gridHeader_BT, false, {
			rowNum : 999999999,
			rowList : [ 999999999 ]
		});
		// L2PT-48808 end
		// L2PT-82624: kiểm tra quyền theo user
		checkRole('btnDuyet', 1);
		// L2PT-31978 L2PT-27729 start
		hienIdsTuCauHinh('VPI_DSCTL_HIEN_BANTHUOC', 'VIS', 'SHOW');
		hienIdsTuCauHinh('VPI_DSCTL_AN_BANTHUOC', 'VIS', 'HIDE'); // L2PT-31978
		// L2PT-31978 L2PT-27729 end
	};
	function _bindEvent() {
		var f2 = 113;
		var cancel = 37;
		var add = 39;
		var save = 40;
		$(document).unbind('keydown').keydown(function(e) {
			if (e.keyCode == f2) {
				var $focused = $(':focus');
				var $help = $focused.parents("div").find('[help]');
				var attrLink = $help.attr('help');
				window.open(window.location.pathname.substring(0, window.location.pathname.indexOf("/", 2)) + attrLink + ".htm");
			}
		});
		$("#btnTimKiem").on("click", function(e) {
			_DSBARCODE = []; // refresh array
			if (flagLoading)
				return;
			var validator = new DataValidator("divSearch");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			var _tungay = stringToDate($("#txtTU").val());
			var _denngay = stringToDate($("#txtDEN").val());
			if (_tungay && _denngay) {
				if (_tungay > _denngay) {
					onFocus([ "txtTU", "txtDEN" ]);
					DlgUtil.showMsg("Trường từ ngày không được lớn hơn trường đến ngày");
					return false;
				}
			}
			_maubenhphamid = -1;
			// L2PT-24280 start
			_khoid = -1;
			// L2PT-24280 end
			loadGridDataBN();
		});
		$("#btnThemKL").on("click", function(e) {
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI012.THEMKL');
			DlgUtil.showMsg(result);
		});
		// L2PT-13993 start
		$("#btnXuatDSThuoc").on("click", function(e) {
			var _tu_ngay = $("#txtTU").val();
			var _den_ngay = $("#txtDEN").val();
			rpName = 'DSDONTHUOC_' + _tu_ngay + '_' + _den_ngay + '.xlsx';
			var par = [ {
				name : "dt_tungay",
				type : "String",
				value : _tu_ngay
			}, {
				name : "dt_denngay",
				type : "String",
				value : _den_ngay
			} ];
			CommonUtil.inPhieu('window', "DS_CHITIET_THUOCBAN_947", 'xlsx', par, rpName);
		});
		// L2PT-13993 end
		// xu ly khi chon mot hang trong danh sach vien phi
		$("#txtBARCODE").change(function() {
			var phieu = $("#txtBARCODE").val().trim();
			var par = [];
			par.push({
				"name" : "[0]",
				"value" : _maubenhphamid
			});
			par.push({
				"name" : "[1]",
				"value" : $("#txtBARCODE").val()
			});
			var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC_CHECK3CODE', par);
			var phieunk = JSON.parse(data_ar);
			if (phieunk.length > 0) {
				var DVKBID = phieunk[0].DICHVUKHAMBENHID;
				var rowIds = $('#grdDSDichVu').jqGrid('getDataIDs');
				for (var i = 0; i < rowIds.length; i++) {
					rowData = $('#grdDSDichVu').jqGrid('getRowData', rowIds[i]);
					var _color = '#FF9900';
					var ThongBao = 'Thuốc đã được check Barcode';
					if (rowData['DICHVUKHAMBENHID'] == DVKBID) {
						$("#grdDSDichVu").jqGrid('setCell', rowIds[i], 'CHECKBARCODE', '1');
						$('#grdDSDichVu').find("tr[id='" + rowIds[i] + "']").find("td").css("background-color", _color);
						$('#grdDSDichVu').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', ThongBao);
						_DSBARCODE.push(DVKBID);
					}
				}
			}
			$("#txtBARCODE").val('');
		});
		GridUtil.setGridParam(_gridId_BN, {
			beforeSelectRow : function(rowid, e) {
				if (flagLoading)
					return false;
				else
					return true;
			},
			onSelectRow : function(id) {
				if (flagLoading)
					return;
				if (id) {
					_DSBARCODE = [];
					FormUtil.clearForm('tongVP', "");
					FormUtil.clearForm('ttThuTien', "");
					GridUtil.unmarkRow(_gridId_BN, rowIdMarked);
					GridUtil.markRow(_gridId_BN, id);
					rowIdMarked = id;
					var _ret = $("#" + _gridId_BN).jqGrid('getRowData', id);
					_maubenhphamid = _ret.MAUBENHPHAMID;
					//L2PT-22913 start : lấy khoid của phiếu
					_khoid = _ret.KHOID;
					//L2PT-22913 end
					_phieuthuid = -1;
					FormUtil.setObjectToForm('divBanThuocDuoc', "", _ret);
					if (typeof _ret.NHAPXUATID == 'undefined' || _ret.NHAPXUATID == "") {
						$("#divDSDichVu").hide();
						$("#divDSBanThuoc").show();
						loadGridDataBT();
						// L2PT-126914: disable btnDuyet
						setEnabled([ 'btnBanThuoc' ], [ 'btnThem', 'btnDuyet', 'btnGoDuyet', 'btnHuyGuiDuyet', 'btnIn', 'btnLuu', 'btnLuuIn', 'btnHuyBo', 'btnHuy', 'btnInHDSD' ]);
						return;
					} else {
						$("#divDSDichVu").show();
						$("#divDSBanThuoc").hide();
						setEnabled([], [ 'btnBanThuoc' ]);
					}
					loadGridDataDV(_maubenhphamid);
					loadGridDataPT(_maubenhphamid);
					$("#cboHINHTHUCTHANHTOAN").val(1);
					setEnabled([ 'btnThem' ], [ 'btnHuy' ]);
					if (_ret.LOAIPHIEUMAUBENHPHAM == 2) {
						$("#lTHUCTHU").text("Hoàn trả");
						$("#lNOPTHEM").text("Trả lại");
					} else {
						$("#lTHUCTHU").text("Thực thu");
						$("#lNOPTHEM").text("Nộp thêm");
					}
					if (_ret.DACODICHVUTHUTIEN == 1) {
						if (_ret.TRANGTHAIMAUBENHPHAM != 6 && _ret.TRANGTHAIID == '5') {
							setEnabled([ 'btnDuyet' ], [ 'btnThem' ]);
						} else {
							setEnabled([], [ 'btnThem', 'btnDuyet' ]);
						}
					} else {
						// L2PT-41587 start
						if (parseFloat(_ret.TONGCONG) == 0) {
							setEnabled([ 'btnDuyet' ], [ 'btnThem' ]);
						}
						// L2PT-41587 end
						else if (_ret.TRANGTHAIID == '5') {
							setEnabled([ 'btnThem' ], [ 'btnDuyet' ]);
						} else {
							setEnabled([], [ 'btnThem', 'btnDuyet' ]);
						}
					}
					if (_ret.TRANGTHAIID == 6) {
						setEnabled([ 'btnGoDuyet' ], []);
					} else {
						setEnabled([], [ 'btnGoDuyet' ]);
					}
					if (_ret.TRANGTHAIID == 5) {
						setEnabled([ 'btnHuyGuiDuyet' ], []);
					} else {
						setEnabled([], [ 'btnHuyGuiDuyet' ]);
					}
				}
			},
			gridComplete : function(id) {}
		});
		// xu ly khi tai xong danh sach dich vu tuong ung voi mot ma vien phi
		$("#" + _gridId_DV).setGridParam({
			// xu ly khi chon/ bo chon dich vu de thanh toan
			onSelectRow : function(id) {
				var grid = $("#" + _gridId_DV);
				if (flagLoading && id) {
					GridUtil.markRow(_gridId_DV, id);
					loadDV();
				} else {}
			},
			onSelectAll : function(ids, status) {
				var grid = $("#" + _gridId_DV);
				if (flagLoading) {
					var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
					GridUtil.markRow(_gridId_DV, arr_row[i]);
					loadDV();
				} else {}
			},
			// xu ly khi tai xong danh sach dichvu
			gridComplete : function() {
				checkThuocBarCode();
			}
		});
		// xu ly su kien chon mot hang trong grid phieu thu
		GridUtil.setGridParam(_gridId_PT, {
			onSelectRow : function(id) {
				if (flagLoading)
					return;
				if (id) {
					var _row = $("#" + _gridId_PT).jqGrid('getRowData', id);
					_phieuthuid = _row.PHIEUTHUID;
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						"value" : _phieuthuid
					});
					_fl_tinh = false;
					GridUtil.loadGridBySqlPage(_gridId_DV, "VPI02T001.22", sql_par);// ,
					// function(){});
					var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', id, 'DAHUYPHIEU');
					var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
					var _tt_mbp = $("#" + _gridId_BN).jqGrid('getCell', selrow, 'TRANGTHAIMAUBENHPHAM');
					// L2PT-76907 start
					if (_dahuyphieu != 1 && (_tt_mbp != 6 || fConfig.VPI_HUYPT_DUYETDT == '1')) {
						$("#rHuyPhieuThu").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						setEnabled([ 'btnHuy' ], []);
					} else {
						$("#rHuyPhieuThu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						setEnabled([], [ 'btnHuy' ]);
					}
					if (_dahuyphieu == 1 && _tt_mbp != 6 && fConfig.VPI_KHOIPHUCPHIEU == '1') {
						$("#rKhoiPhuc").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rKhoiPhuc").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					if (_dahuyphieu != 1 && _tt_mbp != 6) {
						$("#rCapNhatPhieuThu").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rCapNhatPhieuThu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					if (_dahuyphieu != 1) {
						$("#rNhapThongTinTT").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rNhapThongTinTT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					// L2PT-76907 end
					var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', id, 'LOAIPHIEUTHUID');
					var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.LAYCH", []);
					var rows = JSON.parse(data);
					var check = null;
					if (rows != null && rows.length > 0) {
						check = rows[0]["GIATRI"];
					}
					if (_dahuyphieu == 1 || _loaiphieuthuid != 1 || check == 0) {
						$("#rXemHDDT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-19604 start
						// L2PT-21660 start 
						$("#rXemHDDTCD").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-21660 end
						// L2PT-24260 start
						$("#rInHDDTCD").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-24260 end
						// L2PT-19604 end
						$("#rGuiHDDT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rHuyHDDT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					} else {
						$("#rXemHDDT").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-19604 start
						// L2PT-21660 start
						$("#rXemHDDTCD").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-21660 end
						// L2PT-24260 start
						$("#rInHDDTCD").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-24260 end
						// L2PT-19604 end
						$("#rGuiHDDT").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						$("#rHuyHDDT").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					}
					if (_dahuyphieu == 1 && _loaiphieuthuid == 1) {
						$("#rInBBHUY").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rInBBHUY").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					GridUtil.unmarkRow(_gridId_PT, rowIdMarked_PT);
					GridUtil.markRow(_gridId_PT, id, '');
					rowIdMarked_PT = id;
					// L2PT-56749 start
					/*var _sql_par = [ {
						name : "[0]",
						value : _phieuthuid
					} ];
					var _tt_phieuthu = jsonrpc.AjaxJson.ajaxExecuteQueryO(_sql_tt_phieuthu, _sql_par);
					var _obj_ttpt = JSON.parse(_tt_phieuthu);*/
					var _obj_ttpt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuid);
					// L2PT-56749 end
					if (_obj_ttpt.length > 0) {
						var _ttpt = _obj_ttpt[0];
						var _ttpt_dis = clone(_ttpt);
						_ttpt_dis.TONGTIEN = vienphi_tinhtien.convertNumToCurrency(_ttpt_dis.TONGTIEN);
						_ttpt_dis.MIENGIAM_PT = vienphi_tinhtien.convertNumToCurrency(_ttpt_dis.MIENGIAM_PT);
						_ttpt_dis.THUCTHU = vienphi_tinhtien.convertNumToCurrency(_ttpt_dis.THUCTHU);
						FormUtil.setObjectToForm('ttThuTien', "", _ttpt_dis);
					}
					$('#cboMANHOMPHIEUTHU').empty();
					$("#cboMANHOMPHIEUTHU").append($('<option>', {
						value : 0,
						text : _obj_ttpt[0].MANHOMPHIEUTHU
					}));
					$("#cboMANHOMPHIEUTHU").val(0);
					$("#cboLOAIPHIEUTHUID").val(_obj_ttpt[0].LOAIPHIEUTHUID == 1 ? 6 : _obj_ttpt[0].LOAIPHIEUTHUID);
					setEnabled([ 'btnIn', 'btnInHDSD' ], []);
				}
			}
		});
		// xu ly su kien khi tai xong grid phieu thu
		GridUtil.setGridParam(_gridId_PT, {
			gridComplete : function() {}
		});
		// xu ly nut "them"
		$("#btnThem").click(function() {
			$("#txtBARCODE").attr('disabled', true);
			// var _arr_xnban = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC33X032.05',
			// _maubenhphamid);
			// if(_arr_xnban && _arr_xnban.length > 0) {
			// var _xnban = _arr_xnban[0];
			// console.log(_xnban);
			// var ret = ajaxSvc.CongDLYTWS.XacNhanBanThuoc(_xnban.WS_URL,_xnban.WS_USR,
			// _xnban.WS_PWD, _xnban.XNBT);
			// console.log(ret);
			// var resultText = $(ret).find("Error > Error_Message").text();
			// var result = $(ret).find("Error > Error_Number").text();
			// if(result.length >0 && result == 0)
			// DlgUtil.showMsg("Gửi xác nhận bán thuốc thành công");
			// else {
			// DlgUtil.showMsg(resultText);
			// return 0;
			//	    			
			// }
			// }
			if (!_maubenhphamid)
				return false;
			flagLoading = true;
			loadGridDataDV(_maubenhphamid);
		});
		$("#cboMANHOMPHIEUTHU").change(function() {
			if (flagLoading) {
				var i = $("#cboMANHOMPHIEUTHU").val();
				obj_2.MAPHIEUTHU = _dsSo_2[i].MAPHIEUTHU;
				obj_2.NHOMPHIEUTHUID = _dsSo_2[i].NHOMPHIEUTHUID;
				obj_2.MANHOMPHIEUTHU = _dsSo_2[i].MANHOMPHIEUTHU;
				obj_2.LOAIPHIEUTHU = _dsSo_2[i].LOAIPHIEUTHU;
				obj_2.KHOASOPHIEUTU = _dsSo_2[0].KHOASOPHIEUTU;
				obj_2.LOAIPHIEUTHUID = obj.LOAIPHIEUTHUID;
				FormUtil.setObjectToForm("ttThuTien", "", obj_2);
				obj_2.LOAIPHIEUTHUID = _THUTIEN + "";
				$("#cboMANHOMPHIEUTHU").val(i);
			}
		});
		// L2PT-51018 start
		$("#txtMIENGIAM_PT").keyup(function(e) {
			var _miengiam = get_val_m('txtMIENGIAM_PT');
			var before = $("#txtMIENGIAM_PT").val();
			if (!isNaN(_miengiam)) {
				var start = this.selectionStart;
				var end = this.selectionEnd;
				val_m('txtMIENGIAM_PT', _miengiam);
				var after = $("#txtMIENGIAM_PT").val();
				this.setSelectionRange(start + after.length - before.length, end + after.length - before.length);
				nhapMG(_miengiam);
			}
		});
		$("#txtMIENGIAM_PT").change(function() {
			_miengiam = parseFloat(get_val_m('txtMIENGIAM_PT'));
			nhapMG(_miengiam);
		});
		function nhapMG(_miengiam) {
			var _sotien = parseFloat(get_val_m('txtTONGTIEN'));
			_miengiam = parseFloat(_miengiam);
			_miengiam = !_miengiam ? 0 : _miengiam;
			if (_miengiam > _sotien || _miengiam < 0) {
				_miengiam = 0;
				//return false;
			}
			val_m('txtMIENGIAM_PT', _miengiam);
			var _thucthu = _sotien - _miengiam;
			_thucthu = _thucthu.toFixed(2);
			val_m('txtTHUCTHU', _thucthu);
			val_m('txtDANOP', (parseFloat(_vpData.DANOP) + parseFloat(_thucthu)).toFixed(2));
			val_m('txtMIENGIAM', (parseFloat(_vpData.MIENGIAM) + _miengiam).toFixed(2));
			//nhapTienKHDua();
		}
		// L2PT-51018 end
		//L2PT-7489 start // L2PT-18894
		// xử lý chọn loại hình thanh toán
		$("#cboHINHTHUCTHANHTOAN").change(function() {
			onCboHtttChange();
		});
		//L2PT-7489 end // L2PT-18894
		// xy ly su kien nut "Luu"
		$("#btnLuu").bindOnce("click", function() {
			if (_hetphieu_2) {
				DlgUtil.showMsg("Hết phiếu Hóa đơn");
				return;
			}
			if (_hetphieu) {
				DlgUtil.showMsg("Hết phiếu Hoàn trả/Thu tiền");
				return;
			}
			thutien(1, false);
		}, 5000);
		// xy ly su kien nut "Lưu và in"
		$("#btnLuuIn").bindOnce("click", function() {
			if (_hetphieu_2) {
				DlgUtil.showMsg("Hết phiếu Hóa đơn");
				return;
			}
			if (_hetphieu) {
				DlgUtil.showMsg("Hết phiếu Hoàn trả/Thu tiền");
				return;
			}
			thutien(1, true);
		}, 3000);
		// Thu tien
		function thutien(_loaipt, luu_in) {
			var validator = new DataValidator("valDiv");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			if (flagLoading && (_loaipt == _TAMUNG || _loaipt == _HOANDICHVU || _loaipt == _HOANUNG || _co_dv)) {
				var ngay_thu = $("#txtNGAYLAP").val();
				var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
				var _donthuoc = $("#" + _gridId_BN).jqGrid('getRowData', selrow);
				if (_maubenhphamid != _donthuoc.MAUBENHPHAMID) {
					DlgUtil.showMsg("Dữ liệu đã thay đổi , vui lòng chọn lại phiếu");
					return false;
				}
				var _selRowIdsDV = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
				if (_loaipt != _TAMUNG && _selRowIdsDV.length == 0) {
					DlgUtil.showMsg("Chưa chọn thuốc");
					return false;
				} else {
					var _dvFirstRow = $("#" + _gridId_DV).jqGrid('getRowData', _selRowIdsDV[0]);
					if (_maubenhphamid != _dvFirstRow.MAUBENHPHAMID) {
						DlgUtil.showMsg("Dữ liệu đơn thuốc đã thay đổi , vui lòng thao tác lại");
						return false;
					}
				}
				objData["BENHNHANID"] = parseFloat(_donthuoc.BENHNHANID) + "";
				objData["HOSOBENHANID"] = parseFloat(_donthuoc.HOSOBENHANID) + "";
				objData["TIEPNHANID"] = parseFloat(_donthuoc.TIEPNHANID) + "";
				objData["MAUBENHPHAMID"] = parseFloat(_donthuoc.MAUBENHPHAMID) + "";
				objData["NGAYTHU"] = ngay_thu;
				objData["KHOAID"] = parseFloat(_donthuoc.KHOAID) + "";
				objData["PHONGID"] = parseFloat(_donthuoc.PHONGID) + "";
				objData["CHIETKHAU"] = $("#txtCHIETKHAU").val();
				objData["DATRA"] = get_val_m('txtTHUCTHU') + ""; // fix QRCODE
				objData["THUCTHU"] = get_val_m('txtTHUCTHU') + ""; // fix QRCODE
				objData["CONNO"] = '0';
				objData["CONNO_HENTHANHTOAN"] = "01/01/0001 00:00:00";
				objData["MAHOADULIEU"] = '0';
				objData["TENBENHNHAN"] = _donthuoc.TENBENHNHAN;
				objData["DAHUYPHIEU"] = '0';
				objData["DAYEUCAU"] = '0';
				objData["KHAC"] = '0';
				objData["THOIGIANHUYPHIEU"] = "01/01/0001 00:00:00";
				objData["NOIDUNGTHU"] = $("#txtGHICHU").val();
				objData["NOIDUNGIN"] = $("#txtGHICHU").val();
				objData["MIENGIAM"] = get_val_m('txtMIENGIAM_PT');
				objData["LYDOMIENGIAM"] = $("#txtLYDO").val();
				objData["HINHTHUCTHANHTOAN"] = parseFloat($("#cboHINHTHUCTHANHTOAN").val()) + "";
				//objData["LOAIPHIEUTHU"] = _billmode;
				objData["LOAIPHIEUTHUID"] = _loaipt + "";
				objData["NGUONHACHTOAN"] = '0';
				objData["KHOACHUYENDEN"] = '0';
				objData["PHONGHACHTOAN"] = '0';
				//L2PT-8931 start
				obj.TONGTIEN = get_val_m('txtTONGTIEN') + "";
				obj.THUCTHU = get_val_m('txtTHUCTHU') + "";
				obj.NOIDUNGTHU = $("#txtGHICHU").val();
				obj.NOIDUNGIN = $("#txtGHICHU").val();
				obj.NHOMTHANHTOAN = "";
				var _dsPhieu = [];
				//var selRowId = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
				//var _loaiphieu = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'LOAIPHIEUMAUBENHPHAM');
				if (VPI_TAO_PT_THUOC == 1 && _donthuoc.LOAIPHIEUMAUBENHPHAM == 1)
					_dsPhieu.push(obj);
				//L2PT-8931 end	
				var _offset = parseInt($("#txtMAPHIEUTHU").val()) - parseInt(_phieuInfo[_phieuInfo.length - 1].MAPHIEUTHU);
				for (var i = 0; i < _phieuInfo.length; i++) {
					_phieuInfo[i].MAPHIEUTHU = parseInt(_phieuInfo[i].MAPHIEUTHU) + _offset + i + "";
					if (parseInt(_phieuInfo[i].MAPHIEUTHU) > parseInt(_phieuInfo[i].SOPHIEUTO)) {
						DlgUtil.showMsg("Số phiếu lớn hơn số phiếu lớn nhất của quyển sổ");
						return;
					} else if (parseInt(_phieuInfo[i].MAPHIEUTHU) < parseInt(_phieuInfo[i].SOPHIEUFROM)) {
						DlgUtil.showMsg("Số phiếu nhỏ hơn số phiếu nhỏ nhất của quyển sổ");
						return;
						// tuyennx_add_start_20171121 yc HISL2CORE-599
					} else if (parseInt(_phieuInfo[i].MAPHIEUTHU) >= parseInt(_phieuInfo[i].KHOASOPHIEUTU)) {
						DlgUtil.showMsg("Số phiếu bị khóa");
						return;
						// tuyennx_add_end_20171121 yc HISL2CORE-599
					} else {
						_dsPhieu.push(_phieuInfo[i]);
					}
				}
				if (_dsPhieu.length == 0)
					return;
				objData["DSPHIEU"] = _dsPhieu;
				// kiem tra tien tong tien mien giam theo loai va so tien mien giam
				var _sotien_mg = parseFloat(get_val_m('txtMIENGIAM_PT'));
				if (_arrMG.length > 0) {
					var tong_mg = 0;
					for (var idxmg = 0; idxmg < _arrMG.length; idxmg++) {
						tong_mg += parseFloat(_arrMG[idxmg].SOTIEN);
					}
					if (tong_mg != _sotien_mg) {
						DlgUtil.showMsg("Tổng tiền miễn giảm theo loại khác số tiền miễn giảm");
						return;
					}
				}
				// kiem tra tien tong tien mien giam theo nhom va so tien mien giam
				objData["DS_MIENGIAM"] = _arrMG; //L2PT-23349
				//L2PT-19304 start
				if (_arrHTTT.length == 0) {
					var _httt_id = $("#cboHINHTHUCTHANHTOAN").val();
					var _ten_httt = $("#cboHINHTHUCTHANHTOAN option:selected").html();
					var objHTTT = new Object();
					objHTTT.HINHTHUCTHANHTOANID = _httt_id + "";
					objHTTT.TENHINHTHUC = _ten_httt;
					objHTTT.SOTIEN = get_val_m('txtTHUCTHU');
					objHTTT.DOITUONG = "";
					objHTTT.NOIDUNG = "";
					objHTTT.IDTHE = "";
					objHTTT.MATHE = "";
					_arrHTTT.push(objHTTT);
				}
				objData["DS_HTTT"] = _arrHTTT;
				//L2PT-19304 end
				var input = JSON.stringify(objData);
				console.log(input);
				// L2PT-18894 start
				var _httt = $("#cboHINHTHUCTHANHTOAN").val();
				// L2PT-31437 start
				var _id_post = "";
				if (VPI_HIEN_IDPOST == '1') {
					_id_post = $("#txtIDPOST").val();
				} else if (VPI_HIEN_IDPOST == '2') {
					_id_post = $("#cboPOS").val();
				}
				if (VPI_HIEN_IDPOST != '0' && _httt == 5 && _id_post == "") {
					DlgUtil.showMsg("Chưa nhập mã thẻ hoặc POS");
					return;
				}
				// L2PT-54825 BVTM-3849 start
				var _id_dvtt = "";
				if (_httt == 5 || _httt == 12) { // L2PT-4355 : them loai tt = 5
					_id_dvtt = $("#cboDVTT").val();
					if (_id_dvtt == "") {
						DlgUtil.showMsg("Chưa chọn đơn vị thanh toán QR Code");
						return;
					}
				}
				objData["IDDVTT"] = _id_dvtt;
				// L2PT-54825 BVTM-3849 end
				// L2PT-31437 end
				if (_httt == 10) { // L2PT-4355
					var _sql_par = [];
					var _noidung;
					switch (parseInt(_loaipt)) {
						case _THUTHEM:
							_noidung = 'Thu tien vien phi';
						break;
						case _HOANUNG:
							_noidung = 'Hoan tien';
						break;
						case _TAMUNG:
							_noidung = 'Thu tien tam ung';
						break;
						case _HOANDICHVU:
							_noidung = 'Hoan tien dich vu';
						break;
						default:
							_noidung = 'Thu tien';
					}
					_sql_par.push({
						name : 'tiepnhanid',
						type : 'String',
						value : _maubenhphamid
					});
					_sql_par.push({
						name : 'sotien',
						type : 'String',
						value : Math.ceil(get_val_m('txtTHUCTHU'))
					// fix QRCODE
					});
					_sql_par.push({
						name : 'noidung',
						type : 'String',
						value : _noidung
					});
					/*
					_sql_par.push({
						name : 'qrcode',
						type : 'String',
						value : _noidung
					});
					*/
					if (_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) {// HOAN TIEN QUA PAYMENT GATEWAY
						// L2PT-1555 start
						var _loaiPTGoc = _loaipt == _HOANUNG ? _TAMUNG : _THUTHEM;
						var arrHoanUng = [];
						// L2PT-87482: lấy theo maubenphamid
						var arrOrder = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DSTU.VNPTPAY1", _maubenhphamid + "$" + _loaiPTGoc + "$" + "MAUBENHPHAMID");
						var tienHoanUng = get_val_m('txtTHUCTHU');
						var VPI_HOANTRA_VNPT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HOANTRA_VNPT');
						if (VPI_HOANTRA_VNPT == 0) {
							DlgUtil.showMsg('Không hỗ trợ hoàn trả qua VNPT PAY');
						} else if (VPI_HOANTRA_VNPT == 1) { // tự động hoàn trả
							arrHoanUng = [];
							for (var i = 0; i < arrOrder.length; i++) {
								var conLai = tienHoanUng - arrOrder[i].DATRA;
								var objHoanUng = arrOrder[i];
								var refundType = 1;
								if (conLai <= 0) {
									objHoanUng.HOANTRA = tienHoanUng + "";
									if (conLai < 0) {
										refundType = 2;
									}
									objHoanUng.REFUNDTYPE = refundType;
									arrHoanUng.push(objHoanUng);
									break;
								} else {
									objHoanUng.HOANTRA = arrOrder[i].DATRA;
									tienHoanUng = conLai;
									objHoanUng.REFUNDTYPE = refundType;
									arrHoanUng.push(objHoanUng);
								}
							}
							payRefundArr(objData, arrHoanUng);
						} else { // điền số tiền hoàn trả theo từng phiếu
							dlgPopup = DlgUtil.buildPopupGrid("dlgCHONPT", 'DS_PT_VNPTPAY', "Chọn phiếu", 512, 510);
							var _gridHeader_CHONPT = "ORDERID,ORDERID,100,0,t,l,0;" + "TXNID,TXNID,100,0,t,l,0;" + "QRTXNID,QRTXNID,100,0,t,lr,0;" + "Mã phiếu thu,MAPHIEUTHU,100,0,f,l,0;"
									+ "Số tiền,DATRA,100,0,f,r,0;" + "Hoàn Trả,HOANTRA,100,0,e,r,0";
							GridUtil.init('DS_PT_VNPTPAY', "500", "350", "", false, _gridHeader_CHONPT);
							var grid = $("#" + 'DS_PT_VNPTPAY');
							// L2PT-87482: lấy theo maubenphamid
							var arrOrder = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DSTU.VNPTPAY1", _maubenhphamid + "$" + _loaiPTGoc + "$" + "MAUBENHPHAMID");
							GridUtil.fetchGridData('DS_PT_VNPTPAY', arrOrder);
							grid.setSelection(1);
							GridUtil.markRow('DS_PT_VNPTPAY', 1);
							dlgPopup.open();
							// xu ly su kien nhap tien hoan tra
							grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
								var _tien_dt = grid.jqGrid('getCell', rowid, 'DATRA');
								_tien_dt = parseFloat(_tien_dt);
								var _tien_ht = grid.jqGrid('getCell', rowid, 'HOANTRA');
								_tien_ht = parseFloat(_tien_ht);
								if (/^\d+$/i.test(_tien_ht) && _tien_ht > 0 && _tien_ht <= _tien_dt) {
									grid.setSelection(rowid);
									GridUtil.unmarkAll('DS_PT_VNPTPAY');
									GridUtil.markRow('DS_PT_VNPTPAY', rowid);
								} else {
									grid.jqGrid('setCell', rowid, 'HOANTRA', null);
								}
							});
							GridUtil.setGridParam('DS_PT_VNPTPAY', {
								onSelectRow : function(id) {
									GridUtil.unmarkAll('DS_PT_VNPTPAY');
									GridUtil.markRow('DS_PT_VNPTPAY', id);
								},
								gridComplete : function(id) {}
							});
							var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_CHONPT_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
							var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_CHONPT_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
							$('#dlgCHONPT').append(btnOK);
							$('#dlgCHONPT').append(btnClose);
							btnOK.click(function() {
								arrHoanUng = [];
								var ids = grid.getDataIDs();
								for (var i = 0; i < ids.length; i++) {
									var objHoanUng = grid.jqGrid('getRowData', ids[i]);
									var _tien_dt = parseFloat(objHoanUng.DATRA);
									var _tien_ht = parseFloat(objHoanUng.HOANTRA);
									if (_tien_ht && _tien_ht > 0) {
										var refundType = 1;
										if (_tien_dt > _tien_ht) {
											refundType = 2;
										} else if (_tien_dt == _tien_ht) {
											refundType = 1;
										} else {
											DlgUtil.showMsg('Tiền hoàn trả không được lớn hơn tiền đã thu');
											return false;
										}
										objHoanUng.REFUNDTYPE = refundType;
										arrHoanUng.push(objHoanUng);
										console.log(arrHoanUng);
										//DlgUtil.showMsg("GUI SANG GATEWAY");
										//DlgUtil.showMsg("GUI SANG GATEWAY 2: OrderID:" + arrHoanUng[0].ORDERID + "; Tien hoan: " + arrHoanUng[0].HOANTRA );
									} else {
										DlgUtil.showMsg('Tiền hoàn trả không hợp lệ');
										return false;
									}
								}
								if (tongHoanTra != tienHoanUng) {
									return false;
									DlgUtil.showMsg('Số tiền hoàn trả không hợp lệ');
								}
								payRefundArr(objData, arrHoanUng);
							});
							btnClose.click(function() {
								dlgPopup.close();
							});
						}
						// L2PT-1555 end
					} else {// THANH TOAN QUA QRCODE
						var rs = paymentGenQrCode(objData);
						if (!rs) {
							DlgUtil.showMsg('ERR: Return NULL');
							return false;
						}
						if (rs.responseCode == '00') {
							_sql_par.push({
								name : 'qrcode',
								type : 'String',
								value : rs.qrCodeImage
							});
							// L2PT-2161 start
							var rpcodeQRCODE = '';
							if (_loaipt == _TAMUNG) {
								rpcodeQRCODE = 'NGT041_PHIEUTAMUNG_QRCODE_A4';
							} else {
								rpcodeQRCODE = 'NGT041_PHIEUTHUTIEN_QRCODE_A4'
							}
							CommonUtil.openReportGet('window', rpcodeQRCODE, 'pdf', _sql_par);
							// L2PT-2161 end
						}
						// Error
						else {
							DlgUtil.showMsg('Lỗi: ' + rs.responseMessage);
						}
					}
					$("#toolbarIdbtnHuyBo").click();// fix vntpay chuyen tu form 1 sang bi loi
					return;
				}
				// VNPT PAY END
				// L2PT-54825 BVTM-1770 start
				else if (_httt == 12) {
					var _sql_par = [];
					var _noidung;
					switch (parseInt(_loaipt)) {
						case _THUTHEM:
							_noidung = 'Thu tien vien phi';
						break;
						case _HOANUNG:
							_noidung = 'Hoan tien';
						break;
						case _TAMUNG:
							_noidung = 'Thu tien tam ung';
						break;
						case _HOANDICHVU:
							_noidung = 'Hoan tien dich vu';
						break;
						default:
							_noidung = 'Thu tien';
					}
					_sql_par.push({
						name : 'maubenhphamid',
						type : 'String',
						value : _maubenhphamid
					});
					_sql_par.push({
						name : 'sotien',
						type : 'String',
						value : Math.ceil(get_val_m('txtTHUCTHU'))
					// fix QRCODE
					});
					_sql_par.push({
						name : 'noidung',
						type : 'String',
						value : _noidung
					});
					if (_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) { // case hoan tien
						DlgUtil.showMsg('Không hỗ trợ hoàn trả qua QRCODE');
					} else {// case thanh toán
						// BVTM-3849 start
						//var iddvtt = $("#cboDVTT").val();
						var objT = new Object();
						objT.TNAME = 'DMC_DONVITHANHTOAN';
						objT.TMODE = '0';
						objT.TCOL = 'DONVITHANHTOANID-' + _id_dvtt;
						var input = JSON.stringify(objT);
						var tDVTT = jsonrpc.AjaxJson.ajaxCALL_SP_O('T.GET.DATA', input);
						if (!tDVTT || tDVTT.length == 0) {
							DlgUtil.showMsg('Không tồn tại đơn vị thanh toán này'); // BVTM-4061
							return false;
						}
						// L2PT-10089 start: thêm đơn vị thanh toán để in phiếu qrcode
						_sql_par.push({
							name : 'iddvtt',
							type : 'String',
							value : _id_dvtt
						});
						// L2PT-10089 end
						// BVTM-4061 start
						// L2PT-54825 start
						var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
						var _donthuoc = $("#" + _gridId_BN).jqGrid('getRowData', selrow);
						var description = {
							"CashierID" : _opts._param[7],
							"CashierName" : _opts._param[9],
							"MedicalCode" : _donthuoc.SOPHIEU,
							"PatientCode" : _donthuoc.MABENHNHAN,
							"PatientName" : _donthuoc.TENBENHNHAN
						};
						// L2PT-54825 end
						objData.DescriptionQR = description;
						// BVTM-4061 end
						// L2PT-54825: thêm BENHNHANID
						var rs = genQrCode(objData, tDVTT[0].MALOAI, tDVTT[0].MADONVI, _donthuoc.BENHNHANID);
						// BVTM-3849 end
						console.log(rs);
						if (!rs) {
							DlgUtil.showMsg('Lỗi sinh QRCODE');
							return false;
						}
						if (rs.code == '00') {
							var qrBase64 = rs.data.qrCodeBase64
							_sql_par.push({
								name : 'qrcode',
								type : 'String',
								value : qrBase64
							});
							// L2PT-77877 start: phieu in qrcode nhà thuốc
							if (fConfig.VPI_HIENTHI_QRCODE == '1' || fConfig.VPI_HIENTHI_QRCODE == '2') { // L2PT-31763 
								var paramInput = {
									qrCodeBase64 : rs.data.qrCodeBase64,
									LOAIPHIEUTHUID : _loaipt,
									TENBENHNHAN : _donthuoc.TENBENHNHAN,
									MABENHNHAN : _donthuoc.MABENHNHAN,
									MAHOSOBENHAN : _donthuoc.SOPHIEU,
									SOTIEN : Math.ceil(objData.THUCTHU),
									VPI_HIENTHI_QRCODE : fConfig.VPI_HIENTHI_QRCODE
								};
								vienphi_tinhtien.hienThiQR(paramInput);
							} else {
								// L2PT-2161 start
								CommonUtil.openReportGet('window', 'PHIEUTHUTIEN_NHATHUOC_QRCODE_A4', 'pdf', _sql_par);
								// L2PT-2161 end
							}
						}
						// L2PT-77877 end
						// Error
						else {
							DlgUtil.showMsg('Lỗi: ' + rs.message);
						}
					}
					$("#toolbarIdbtnHuyBo").click();// fix vntpay chuyen tu form 1 sang bi loi
					return;
				}
				// L2PT-54825  BVTM-1770 end
				// START: Thanh toán qua POS
				else if (_httt == 5 && VPI_HIEN_IDPOST == '2') {
					// L2PT-1511 start
					if (_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) {
						DlgUtil.showMsg("Không thể hoàn ứng qua POS");
						return false;
					}
					// L2PT-1511 end
					var orderId = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GEN.ORDERID", _hospital_code);
					var hisUser = _opts._user_name;
					var uuid = _opts._uuid;
					var amount = get_val_m('txtTHUCTHU');
					var posterminalid = $("#cboPOS").val();
					// L2PT-1511 start
					//var description = objData.NOIDUNGTHU;
					var description = "Thu tien vien phi " + _benhnhan.SOPHIEU;
					// L2PT-1511 end
					var f1 = 'f1';
					var f2 = 'f2';
					var f3 = 'f3';
					var f4 = 'f4';
					var f5 = 'f5';
					// Gọi tới POS
					localStorage.setItem('IDPOST', posterminalid);
					// L2PT-1511 start
					var objLPOS = new Object();
					objLPOS.ORDERID = orderId + "";
					objLPOS.HISUSER = hisUser;
					// L2PT-87482 start : luu them maubenhphamid
					objLPOS.TIEPNHANID = _donthuoc.TIEPNHANID + "";
					objLPOS.MAUBENHPHAMID = _maubenhphamid + "";
					// L2PT-87482 end
					objLPOS.UUID = uuid;
					objLPOS.POSTERMINALID = posterminalid + "";
					objLPOS.AMOUNT = amount + "";
					objLPOS.DESCRIPTION = description;
					objLPOS.F1 = f1;
					objLPOS.F2 = f2;
					objLPOS.F3 = f3;
					objLPOS.F4 = f4;
					objLPOS.F5 = f5;
					objData.THANHTOAN_POS = "1";
					objData.ORDERID = orderId; //L2PT-31437
					objLPOS.BILL = JSON.stringify(objData);
					luuLogGoiPOS(objLPOS, 'SEND');
					// L2PT-1511 end
					var rs = pmmSale(orderId, hisUser, uuid, posterminalid, amount, description, f1, f2, f3, f4, f5);
					// L2PT-1511 start
					/*
					var rs = {
							CODE : "00",
							RESULT : {
								status : "DONE",
								transInfo : {
									transStatus : 'APPROVE',
									approvalCode : "approvalCode"
								},
								clientId : "clientId"
							}
						};
					*/
					objLPOS.POS_RET = JSON.stringify(rs);
					// L2PT-1511 end
					console.log('POSPayment', rs);
					if (rs.CODE != '00') {
						luuLogGoiPOS(objLPOS, 'GET'); // L2PT-1511
						DlgUtil.showMsg("Lỗi thanh toán POS: " + rs.MESSAGE);
						return false;
					}
					var transReturn = rs.RESULT;
					objLPOS.TRANS_RET_STATUS = transReturn.status; // L2PT-1511
					if (transReturn.status == 'CANCELED') {
						luuLogGoiPOS(objLPOS, 'GET'); // L2PT-1511
						DlgUtil.showMsg('Giao dịch thanh toán đã bị hủy bỏ.');
						return false;
					}
					if (transReturn.status == 'DONE') {
						var transInfo = transReturn.transInfo;
						objLPOS.TRANS_INF_STATUS = transInfo.transStatus; // L2PT-1511
						if (transInfo.transStatus == 'APPROVE') {
							// transInfo.APPROVE_CODE;
							// Nếu thành công thì gọi hàm tạo phieu
							objData.MA_GD_CK = transInfo.approvalCode; // L2PT-34078 // L2PT-1511
							// L2PT-1511 start
							objLPOS.BILL = JSON.stringify(objData);
							objLPOS.TRANS_INF_APPROVALCODE = transInfo.approvalCode;
							luuLogGoiPOS(objLPOS, 'GET');
							// L2PT-1511 end
							taoPhieu(objData, luu_in);
							DlgUtil.showMsg('Giao dịch thanh toán thành công tại ' + transReturn.clientId);
							return true;
						} else {
							DlgUtil.showMsg('Lỗi thanh toán POS. ' + transInfo.transStatus);
							return false;
						}
					}
					luuLogGoiPOS(objLPOS, 'GET'); // L2PT-1511
					DlgUtil.showMsg('Lỗi thanh toán POS. Lỗi khác');
					return false;
				}
				// END: Thanh toán qua POS
				else {
					taoPhieu(objData, luu_in);
				}
				// L2PT-18894 end
			} else {
				DlgUtil.showMsg("Chưa chọn dịch vụ để thanh toán");
				return;
			}
		}
		// L2PT-18894 start
		function taoPhieu(objData, luu_in) {
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S(_sql_thuvienphi, JSON.stringify(objData));
			if (fl == -1) {
				DlgUtil.showMsg("Cập nhật không thành công");
			} else if (fl == -2) {
				DlgUtil.showMsg("Có dịch vụ đã thu tiền, không thể hoàn");
			} else if (fl == -3) {
				DlgUtil.showMsg("Có dịch vụ đã thực hiện, không thể hoàn");
			} else if (fl == -4) {
				DlgUtil.showMsg("Có dịch vụ đã thu tiền");
			} else if (fl == -5) {
				DlgUtil.showMsg("Dữ liệu đã thay đổi, hãy chọn lại bệnh nhân");
			} else if (fl == -6) {
				DlgUtil.showMsg("Hết phiếu");
			} else if (fl == -7) {
				DlgUtil.showMsg("Mã phiếu đã sử dụng");
			} else if (fl == -8) {
				DlgUtil.showMsg("Số phiếu lớn hơn số phiếu lớn nhất của quyển sổ");
			} else if (fl == -9) {
				DlgUtil.showMsg("Không cho phép thu tiền khi bệnh nhân chưa đóng bệnh án");
			}
			// L2PT-76907 start 
			else if (fl == 'PHIEUTHU_CAPTHUOC_DAHUY') {
				DlgUtil.showMsg("Không thể hoàn tiền vì phiếu thu cấp thuốc đã hủy");
			}
			// L2PT-76907 end
			else {
				// L2PT-95857 start
				var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DSPT.LT", fl + "$" + 1);
				_phieuthuid = fl;
				for (var indx = 0; indx < arr_pt.length; indx++) {
					var phieuThu = arr_pt[indx];
					// L2PT-17557 start
					if (luu_in) {
						if (VPI_THUTIEN_INLOAIPHIEU == 0 || VPI_THUTIEN_INLOAIPHIEU == phieuThu.LOAIPHIEUTHUID) {
							inHoaDonThuoc(phieuThu.PHIEUTHUID, phieuThu.LOAIPHIEUTHUID);
						}
					}
					// L2PT-17557 end
					// TUYENNX_ADD_START
					if (phieuThu.LOAIPHIEUTHUID == _THUTIEN) {
						var sql_par = [];
						sql_par.push({
							"name" : "[0]",
							value : phieuThu.PHIEUTHUID
						});
						var checkhu = jsonrpc.AjaxJson.getOneValue('INVOICE.CHECKHU', sql_par);
						if (VPI_DAY_HOADONDT == 1 && INVOICES_IMPORT_AUTO == 1 && checkhu > 0) {
							var kqGuiHD = guiHDDTTheoPhieu(phieuThu.PHIEUTHUID, dsCH, _config_hddt, '', 3, '', '');
							if (kqGuiHD != "1") {
								DlgUtil.showMsg(kqGuiHD);
							}
						}
					}
					// TUYENNX_ADD_END
				}
				// L2PT-95857 end
				// TUYENNX_ADD_START
				/*var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					value : fl
				});
				var checkhu = jsonrpc.AjaxJson.getOneValue('INVOICE.CHECKHU', sql_par);
				if (VPI_DAY_HOADONDT == 1 && INVOICES_IMPORT_AUTO == 1 && ($("#cboLOAIPHIEUTHUID").val() == 6 || ($("#cboLOAIPHIEUTHUID").val() == 2 && checkhu > 0))) {
					// L2PT-105772 start
					daythongtinbn();
					var sql_par = [ fl ];
					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.INVOICES.BT", sql_par.join('$'));
					// L2PT-80540 start
					if (fConfig.VPI_FN_IMPORT_HDDT == 2) {
						ret = ajaxSvc.InvoicesWS.importInvByPattern(rs, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN,
								INVOICES_WS_SERIAL);
					} else {
						ret = ajaxSvc.InvoicesWS.importHoaDon(rs, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN,
								INVOICES_WS_SERIAL);
					}
					// L2PT-80540 end
					if (ret == "" || ret.toUpperCase().includes("ERR")) {
						DlgUtil.showMsg("Lỗi đẩy hóa đơn điện tử");
					} else {
						var _par = [ "1", INVOICES_WS_PATTERN, INVOICES_WS_SERIAL, ret.split("_")[1], fl ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
					}
					var kqGuiHD = guiHDDTTheoPhieu(fl, dsCH, _config_hddt, '', 3, '', '');
					if (kqGuiHD != "1") {
						DlgUtil.showMsg(kqGuiHD);
					}
					// L2PT-105772 end
				}*/
				// TUYENNX_ADD_END
				flagLoading = false;
				FormUtil.clearForm('ttThuTien', '');
				$("#cboHINHTHUCTHANHTOAN").val(1);
				$("#" + _gridId_DV).hideCol('cb');
				loadGridDataPT(_maubenhphamid);
				loadGridDataDV(_maubenhphamid);
				// L2PT-21476 them cbo ghi chu
				// L2PT-23349 thêm btnHTTT, btnLYDO	
				setEnabled([ 'btnThem', 'btnIn', 'btnHuy', 'btnInHDSD' ],
						[ 'btnHuyBo', 'btnLuu', 'btnLuuIn', 'cboHINHTHUCTHANHTOAN', 'txtGHICHU', 'cboGHICHU', 'btnLYDO', 'btnHTTT', 'txtMIENGIAM_PT' ]);
				setEnabled([], [ 'txtMAPHIEUTHU', 'cboMANHOMPHIEUTHU' ]);
				setEnabled([ 'btnDuyet' ], [ 'btnThem' ]);
				// L2PT-17770 start
				if (VPI_THUTIENTHUOC_DUYETDON == '1') {
					duyetDonThuoc();
				}
				// L2PT-17770 end
				loadGridDataBN();
				// if(HIS_FOCUS_MABN == 3) {
				// $("#"+_gridId_BN)[0].clearToolbar();
				// $(_focus_element).focus();
				// }
			}
		}
		// L2PT-18894 end
		// L2PT-87482 start
		function luuLogGoiPOS(objLPOS, LACTION) {
			objLPOS.LACTION = LACTION;
			var retLogPos = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T024.EV004", JSON.stringify(objLPOS));
			if (retLogPos != 1) {
				DlgUtil.showMsg("Lưu log thanh toán qua POS không thành công");
			}
			return retLogPos;
		}
		// L2PT-87482 end
		// xu ly huy bo thu tien
		$("#btnHuyBo").click(
				function() {
					$("#txtBARCODE").attr('disabled', false);
					$("#" + _gridId_DV).hideCol('cb');
					$('#cboMANHOMPHIEUTHU').empty();
					FormUtil.clearForm('ttThuTien', '');
					$("#cboHINHTHUCTHANHTOAN").val(1);
					// L2PT-21476 them cbo ghi chu
					// L2PT-23349 thêm btnHTTT, btnLYDO
					setEnabled([ 'btnThem' ], [ 'btnHuyBo', 'btnLuu', 'btnLuuIn', 'cboHINHTHUCTHANHTOAN', 'txtMAPHIEUTHU', 'cboMANHOMPHIEUTHU', 'txtGHICHU', 'cboGHICHU', 'btnLYDO', 'btnHTTT',
							'txtMIENGIAM_PT' ]);
					flagLoading = false;
					loadGridDataDV(_maubenhphamid);
				});
		// xu ly in hoa don
		$("#btnIn").click(function() {
			var selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
			var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
			var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'LOAIPHIEUTHUID');
			inHoaDonThuoc(_phieuthuid, _loaiphieuthuid);
		});
		// L2PT-46273: thêm tham số report_code
		function inHoaDonThuoc(_phieuthuid, _loaiphieuthuid, rptCodes) {
			var rpt_code = "";
			if (typeof rptCodes == 'object' && !Array.isArray(rptCodes)) {
				rpt_code = _loaiphieuthuid == 1 ? rptCodes.HOADON : rptCodes.PHIEUTHU;
			} else {
				rpt_code = _loaiphieuthuid == 1 ? 'HOADON_BANTHUOC_KHACHLE' : 'PHIEUTHUTIEN_NHATHUOC_A4';
			}
			var par = [];
			par.push({
				name : _loaiphieuthuid == 1 ? 'i_phieuthuid' : 'phieuthuid',
				type : 'String',
				value : _phieuthuid.toString()
			});
			openReport('window', rpt_code, 'pdf', par);
			// in hd ct
			// L2PT-83983: them cấu hình in hóa đơn chi tiết bán thuốc
			if (fConfig.VPI_IN_HDCT_BT != '1' && HIS_IN_HOADONCHITIET == 1) {
				var par_ct = [];
				par_ct.push({
					name : 'phieuthuid',
					type : 'String',
					value : _phieuthuid.toString()
				});
				openReport('window', 'NGT037_BANGKEKEMHDGTGT_A4', 'pdf', par_ct);
			}
			// in hd ct
		}
		// xu ly duyệt đơn thuốc
		// L2PT-17770 start
		$("#btnDuyet").click(function() {
			duyetDonThuoc();
		});
		function duyetDonThuoc() {
			var _duyet = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI_DUYET_BANTHUOC', 'DUYET_BANTHUOC$' + _maubenhphamid);
			// L2PT-24280 start
			var objLog = new Object();
			objLog.DONTHUOCID = _maubenhphamid + "";
			objLog.LOAI = "0";
			objLog.TRANGTHAI = "0";
			objLog.GHICHU = "Duyệt bán thuốc";
			objLog.KETQUADUYET = _duyet + "";
			objLog.KETQUA = "";
			// L2PT-24280 end
			if (_duyet && _duyet > 0) {
				// L2PT-24280 start
				objLog.TRANGTHAI = "1";
				objLog.GHICHU = objLog.GHICHU + ";Duyệt thành công";
				// L2PT-24280 end
				DlgUtil.showMsg("Duyệt thành công");
				if (VPI_XACNHAN_BANTHUOC == 1) {
					// L2PT-24280 start
					var _ketqua = "";
					try {
						_ketqua = xacNhanBanThuoc(_maubenhphamid, 'Ins', _khoid); //L2PT-22913 : truyền khoid vào hàm xác nhận bán thuốc
					} catch (err) {
						_ketqua = err.message;
						DlgUtil.showMsg("Có lỗi xảy ra: " + err.message);
					}
					objLog.LOAI = "1";
					objLog.TRANGTHAI = "2";
					objLog.KETQUA = _ketqua;
					objLog.GHICHU = objLog.GHICHU + ";Gửi xác nhận";
					// L2PT-24280 end
				}
				loadGridDataBN();
			} else {
				DlgUtil.showMsg("Duyệt không thành công");
			}
			// L2PT-24280 start
			var log_dt = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.GHILOG.DONTHUOC', JSON.stringify(objLog));
			if (log_dt == -1) {
				DlgUtil.showMsg("Cập nhật log duyệt/ gửi đơn thuốc không thành công");
			}
			// L2PT-24280 end
		}
		// L2PT-17770 end
		// huy phieu thu
		$("#btnHuy").click(function() {
			if (flagLoading)
				return;
			DlgUtil.open("dlgXacNhan");
		});
		//L2PT-21476 start
		$("#cboGHICHU").change(function() {
			var selectedValue = $("#cboGHICHU").val();
			//var selectedText = selectedValue ? $("#cboGHICHU option:selected").text() : "";
			$("#txtGHICHU").val(selectedValue);
		});
		//L2PT-21476 end
		// L2PT-23349 start
		$("#btnLYDO").click(function() {
			if (!flagLoading)
				return;
			dlgPopup = DlgUtil.buildPopupGrid("dlgLYDO", 'DM_MIENGIAM', "Chọn miễn giảm", 512, 510);
			GridUtil.init('DM_MIENGIAM', "500", "350", "", false, _gridHeader_MG);
			var _sql_par = [];
			var grid = $("#" + 'DM_MIENGIAM');
			if (_DMMG.length > 0) {
				GridUtil.fetchGridData('DM_MIENGIAM', _DMMG);
				var ids = grid.getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var _tien_mg = grid.jqGrid('getCell', id, 'SOTIEN');
					if (_tien_mg && _tien_mg > 0) {
						grid.jqGrid('setRowData', id, "", {
							color : 'red'
						});
					}
				}
				dlgPopup.open();
			} else {
				GridUtil.loadGridBySqlPage('DM_MIENGIAM', "DM.MIENGIAM", _sql_par, function() {
					dlgPopup.open();
				});
			}
			// xu ly su kien nhap tien mien giam
			grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
				var _tien_mg = grid.jqGrid('getCell', rowid, 'SOTIEN');
				if (/^\d+$/i.test(_tien_mg) && _tien_mg > 0) {
					if (_tien_mg && _tien_mg > 0) {
						grid.jqGrid('setRowData', rowid, "", {
							color : 'red'
						});
					} else {
						grid.jqGrid('setRowData', rowid, "", {
							color : 'black'
						});
					}
				} else {
					grid.jqGrid('setCell', rowid, 'SOTIEN', null);
					grid.jqGrid('setRowData', rowid, "", {
						color : 'black'
					});
				}
			});
			GridUtil.setGridParam('DM_MIENGIAM', {
				onSelectRow : function(id) {
					var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
					GridUtil.unmarkAll('DM_MIENGIAM');
					for (var i = 0; i < arr_row.length; i++) {
						GridUtil.markRow('DM_MIENGIAM', arr_row[i]);
					}
				},
				gridComplete : function(id) {}
			});
			var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
			var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
			$('#dlgLYDO').append(btnOK);
			$('#dlgLYDO').append(btnClose);
			btnOK.click(function() {
				_arrMG = [];
				_DMMG = [];
				var _tong_mg = 0;
				var _ms_mg = "";
				var ids = grid.getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var rowData = grid.jqGrid('getRowData', id);
					_DMMG.push(rowData);
					var _sotien_mg = rowData.SOTIEN;
					if (_sotien_mg && _sotien_mg > 0) {
						_tong_mg += parseFloat(_sotien_mg);
						var _loai_mg = rowData.LOAI;
						var _ten_mg = rowData.TEN;
						_ms_mg += _ten_mg + ": " + _sotien_mg + ";";
						var objMG = new Object();
						objMG.LOAI = _loai_mg + "";
						objMG.TEN = _ten_mg;
						objMG.SOTIEN = _sotien_mg + "";
						_arrMG.push(objMG);
					}
				}
				if (_arrMG.length > 0) {
					// kiem tra tong tien mien giam va tong tien hoa don
					if (_tong_mg > _tien_hoadon) {
						DlgUtil.showMsg("Tiền miễn giảm không được lớn hơn số tiền trên hóa đơn");
						_arrMG = [];
						_DMMG = [];
						return;
					}
					// kiem tra tong tien mien giam va tong tien hoa don
					_ms_mg = _ms_mg.slice(0, -1);
//          		DlgUtil.showMsg(_ms_mg+"");
					$("#txtMIENGIAM_PT").val(_tong_mg);
					$("#txtLYDO").val(_ms_mg);
					$("#txtMIENGIAM_PT").keyup();
					dlgPopup.close();
				} else {
					DlgUtil.showMsg("Chưa nhập miễn giảm");
				}
			});
			btnClose.click(function() {
				dlgPopup.close();
			});
			GridUtil.setGridParam('DM_MIENGIAM', {
				gridComplete : function(id) {}
			});
		});
		// L2PT-23349 end
		// L2PT-19304 Xử lý chọn HTTT
		$("#btnHTTT").click(
				function() {
					if (!flagLoading && _phieuthuid > 0) {
						_DM_HTTT = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.PT.HTTT', _phieuthuid);
					}
					dlgPopup = DlgUtil.buildPopupGrid("dlgHTTT", 'DM_HTTT', "Chọn HTTT", 512, 510);
					GridUtil.init('DM_HTTT', "500", "310", "", false, _gridHeader_HTTT);
					var grid = $("#" + 'DM_HTTT');
					var _thucthu = parseFloat(get_val_m('txtTHUCTHU'));
					/*var _tien_doidiem = parseFloat(get_val_m('txtTIENSUDUNG'));*/
					if (_DM_HTTT.length > 0) {
						GridUtil.fetchGridData('DM_HTTT', _DM_HTTT);
						var ids = grid.getDataIDs();
						var _tongtien_httt = 0;
						var _cell_html
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var _sotien_httt = grid.jqGrid('getCell', id, 'SOTIEN');
							_tongtien_httt += parseFloat(_sotien_httt);
							if (_sotien_httt > 0) {
								grid.jqGrid('setRowData', id, "", {
									color : 'red'
								});
							}
							var _httt_id = grid.jqGrid('getCell', id, 'TRANGTHAI_ID');
							var _doituong_httt = grid.jqGrid('getCell', id, 'DOITUONG');
							var _loai_httt = grid.jqGrid('getCell', id, 'LOAI');
							if (_loai_httt == 1) {
								_cell_html = '<select class="form-control input-sm" ' + 'style="width: 100%;" ' + 'id="cboDOITUONGTT' + _httt_id + '" hidden="">' + '</select>';
							} else {
								_cell_html = '<input class="form-control input-sm" ' + 'id="txtDOITUONGTT' + _httt_id + '" ' + 'name="txtDOITUONGTT' + _httt_id + '">';
							}
							grid.jqGrid('setCell', id, 'DOITUONG', _cell_html);
							if (_loai_httt == 1) {
								var _sql_par_bl = [ {
									name : [ 0 ],
									value : _httt_id
								} ];
								ComboUtil.getComboTag("cboDOITUONGTT" + _httt_id, "VPI.DM.BHBL", _sql_par_bl, "", "", "sql", "", "");
								if (_doituong_httt) {
									$("#cboDOITUONGTT" + _httt_id).val(_doituong_httt);
								}
							} else {
								$("#txtDOITUONGTT" + _httt_id).val(_doituong_httt);
							}
						}
						dlgPopup.open();
					} else {
						var _ds_httt = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DM.HTTT", "$");
						GridUtil.fetchGridData('DM_HTTT', _ds_httt);
						var id_httt = $("#cboHINHTHUCTHANHTOAN").val();
						var ids = grid.getDataIDs();
						var _cell_html;
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var _httt_id = grid.jqGrid('getCell', id, 'TRANGTHAI_ID');
							var _loai_httt = grid.jqGrid('getCell', id, 'LOAI');
							if (_httt_id == id_httt) {
								grid.jqGrid('setCell', id, 'SOTIEN', _thucthu);
								/*if (_tien_doidiem && _tien_doidiem > 0) {
									//var _doituong_thetd = $("#txtTHETD").val();
									var _tien_conphainop = _thucthu - _tien_doidiem;
									_tien_conphainop = _tien_conphainop.toFixed(2);
									_tien_conphainop = parseFloat(_tien_conphainop);
									grid.jqGrid('setCell', id, 'SOTIEN', _tien_conphainop);
								}*/
							}
							if (_loai_httt == 1) {
								_cell_html = '<select class="form-control input-sm" ' + 'style="width: 100%;" ' + 'id="cboDOITUONGTT' + _httt_id + '" hidden="">' + '</select>';
							} else {
								_cell_html = '<input class="form-control input-sm" ' + 'id="txtDOITUONGTT' + _httt_id + '" ' + 'name="txtDOITUONGTT' + _httt_id + '">';
							}
							grid.jqGrid('setCell', id, 'DOITUONG', _cell_html);
							if (_loai_httt == 1) {
								var _sql_par_bl = [ {
									name : [ 0 ],
									value : _httt_id
								} ];
								ComboUtil.getComboTag("cboDOITUONGTT" + _httt_id, "VPI.DM.BHBL", _sql_par_bl, "", "", "sql", "", "");
							}
							// nếu có thẻ tích điểm 
							/*if (_tien_doidiem && _tien_doidiem > 0 && _httt_id == 8) {
								grid.jqGrid('setCell', _httt_id, 'SOTIEN', _tien_doidiem);
								//$("#txtDOITUONGTT5").val(_doituong_thetd);
								if (id_httt == _httt_id && _tien_doidiem != _thucthu) {
									var _tien_conphainop = _thucthu - _tien_doidiem;
									_tien_conphainop = _tien_conphainop.toFixed(2);
									_tien_conphainop = parseFloat(_tien_conphainop);
									if (id == 1) {
										grid.jqGrid('setCell', 2, 'SOTIEN', _tien_conphainop);
									} else {
										grid.jqGrid('setCell', 1, 'SOTIEN', _tien_conphainop);
									}
								}
							}*/
						}
						dlgPopup.open();
						//GridUtil.loadGridBySqlPage('DM_HTTT', "VPI.DM.HTTT", [], function() {});
					}
					// xu ly su kien nhap tien theo httt
					grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
						var _tien_httt = grid.jqGrid('getCell', rowid, 'SOTIEN');
						_tien_httt = parseFloat(_tien_httt);
						var _thucthu = parseFloat(get_val_m('txtTHUCTHU'));
						if (/^\d+$/i.test(_tien_httt) && _tien_httt > 0) {
							if (_tien_httt <= _thucthu) {
								grid.jqGrid('setRowData', rowid, "", {
									color : 'blue'
								});
							} else {
								grid.jqGrid('setRowData', rowid, "", {
									color : 'black'
								});
							}
							// tính tiền các httt
							var ids = grid.getDataIDs();
							var id_httt = $("#cboHINHTHUCTHANHTOAN").val();
							var _tongtien_httt_khac = 0;
							var id_goc;
							for (var i = 0; i < ids.length; i++) {
								var _httt_id = grid.jqGrid('getCell', ids[i], 'TRANGTHAI_ID');
								if (_httt_id != id_httt) {
									var _tien_httt_i = grid.jqGrid('getCell', ids[i], 'SOTIEN');
									_tien_httt_i = parseFloat(_tien_httt_i);
									_tongtien_httt_khac += _tien_httt_i;
									_tongtien_httt_khac = parseFloat(_tongtien_httt_khac.toFixed(2));
								} else {
									id_goc = ids[i];
								}
							}
							var _tien_httt_conlai = _thucthu - _tongtien_httt_khac;
							_tien_httt_conlai = parseFloat(_tien_httt_conlai.toFixed(2));
							if (_tien_httt_conlai < 0) {
								grid.jqGrid('setCell', rowid, 'SOTIEN', 0);
							} else {
								grid.jqGrid('setCell', id_goc, 'SOTIEN', _tien_httt_conlai);
							}
							// tính tiền các httt
						} else {
							grid.jqGrid('setCell', rowid, 'SOTIEN', 0);
							grid.jqGrid('setRowData', rowid, "", {
								color : 'black'
							});
						}
					});
					GridUtil.setGridParam('DM_HTTT', {
						onSelectRow : function(id) {
							var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
							GridUtil.unmarkAll('DM_HTTT');
							for (var i = 0; i < arr_row.length; i++) {
								GridUtil.markRow('DM_HTTT', arr_row[i]);
							}
						},
						gridComplete : function(id) {}
					});
					var txtTongTien_HTTT = $('<div class="col-xs-12 low-padding " align="center">' + '<div class="col-xs-5 low-padding">' + '<label class="">Tổng tiền thanh toán</label>' + '</div>'
							+ '<div class="col-xs-5 low-padding">' + '<input class="form-control input-sm money-sm clsfloatcomma"' + 'style="text-align: right; width: 100%;" id="txtTONGTIEN_HTTT"'
							+ 'name="txtTONGTIEN_HTTT" title=""' + 'valrule="Số tiền,trim_required|max_length[20]" disabled>' + '</div>' + '</div>');
					var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
					var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
					$('#dlgHTTT').append(btnOK);
					$('#dlgHTTT').append(btnClose);
					$('#dlgHTTT').append(txtTongTien_HTTT);
					val_m('txtTONGTIEN_HTTT', _thucthu);
					btnOK.click(function() {
						_arrHTTT = [];
						_DM_HTTT = [];
						var _tongtien_httt = 0;
						var _ms_httt = "";
						var ids = grid.getDataIDs();
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var rowData = grid.jqGrid('getRowData', id);
							var _sotien_httt = rowData.SOTIEN;
							_sotien_httt = parseFloat(_sotien_httt)
							var _doituong_httt = "";
							if (_sotien_httt && _sotien_httt > 0) {
								_tongtien_httt += _sotien_httt;
								var _httt_id = rowData.TRANGTHAI_ID;
								var _ten_httt = rowData.TEN_TRANGTHAI;
								var _loai_httt = rowData.LOAI;
								var _id_thetd = "";
								var _ma_thetd = "";
								if (_httt_id == -1) {
									_id_thetd = $("#cboMATHETD").val();
									_ma_thetd = $("#txtTHETD").val();
								}
								if (_loai_httt == 1) {
									_doituong_httt = $("#cboDOITUONGTT" + _httt_id + " option:selected").val();
								} else {
									_doituong_httt = $("#txtDOITUONGTT" + _httt_id).val();
								}
								_ms_httt += _ten_httt + ": " + _sotien_httt + ";";
								var objHTTT = new Object();
								objHTTT.HINHTHUCTHANHTOANID = _httt_id + "";
								objHTTT.TENHINHTHUC = _ten_httt;
								objHTTT.SOTIEN = _sotien_httt + "";
								objHTTT.DOITUONG = _doituong_httt + "";
								objHTTT.NOIDUNG = "";
								objHTTT.IDTHE = _id_thetd + "";
								objHTTT.MATHE = _ma_thetd + "";
								_arrHTTT.push(objHTTT);
							}
							rowData.DOITUONG = _doituong_httt;
							_DM_HTTT.push(rowData);
						}
						var _thucthu = parseFloat(get_val_m('txtTHUCTHU'));
						if (_tongtien_httt != _thucthu) {
							DlgUtil.showMsg("Tổng số tiền của các hình thức thanh toán không được khác số tiền thực thu");
						} else if (_arrHTTT.length > 0) {
							_ms_httt = _ms_httt.slice(0, -1);
							objData.TONGTIEN_HTTT = _tongtien_httt;
							dlgPopup.close();
							/*var _tien_doidiem = parseFloat(get_val_m('txtTIENSUDUNG'));
							if (_tien_doidiem && _tien_doidiem > 0) {
								checkthutien();
							}*/
						} else {
							DlgUtil.showMsg("Chưa nhập hình thức thanh toán");
						}
					});
					btnClose.click(function() {
						dlgPopup.close();
					});
					GridUtil.setGridParam('DM_HTTT', {
						gridComplete : function(id) {}
					});
				});
		//L2PT-19304 Xử lý chọn HTTT
		// ban thuoc cho khach le
		$("#toolbarIdbtnBanThuoc").on("click", function(e) {
			EventUtil.setEvent("banthuocbn", function(e) {
				DlgUtil.close("dlgbanthuoc");
				loadGridDataBN();
			});
			var myVar = {
				khoid : $("#cboKho").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe", myVar, "Bán thuốc", 1200, 580);
			if (_hospital_id == '965') {
				dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe_BDHCM", myVar, "Bán thuốc", 1200, 580);
			}
			dlgPopup.open("dlgbanthuoc");
		});
		// ban thuoc nhanh
		$("#btnBanThuoc").on("click", function(e) {
			guiYeuCau("5");
		});
		// sưa don thuoc
		$("#toolbarIdbtnSuaDT").on("click", function(e) {
			var row = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var _id = $("#" + _gridId_BN).jqGrid('getRowData', row).NHAPXUATID;
			var _kieu = $("#" + _gridId_BN).jqGrid('getRowData', row).KIEU;
			if (_kieu == '3') {
				EventUtil.setEvent("banthuocbn", function(e) {
					DlgUtil.close("dlgbanthuoc");
					loadGridDataBN();
				});
				var myVar = {
					khoid : $("#cboKho").val(),
					nhapxuatid : _id,
					kieu : _kieu
				};
				var title = "Bán thuốc";
				dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe", myVar, "Bán thuốc", 1200, 580);
				dlgPopup.open("dlgbanthuoc");
			} else if (_kieu == '2') {
				EventUtil.setEvent("trathuoc", function(e) {
					DlgUtil.close("dlgtrathuoc");
					loadGridDataBN();
				});
				var myVar = {
					nhapxuatid : _id
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgtrathuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X003_PhieuTraThuoc", myVar, "Trả thuốc khách lẻ", 1200, 580);
				dlgPopup.open("dlgtrathuoc");
			}
		});
		// tra thuoc
		$("#toolbarIdbtnTraThuoc").on("click", function(e) {
			var row = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var _id = $("#" + _gridId_BN).jqGrid('getRowData', row).NHAPXUATID;
			var _xuatid = $("#" + _gridId_BN).jqGrid('getRowData', row).XUATID;
			EventUtil.setEvent("trathuoc", function(e) {
				DlgUtil.close("dlgtrathuoc");
				loadGridDataBN();
			});
			var myVar = {
				nhapxuatid : _xuatid
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgtrathuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X003_PhieuTraThuoc", myVar, "Trả thuốc khách lẻ", 1200, 580);
			dlgPopup.open("dlgtrathuoc");
		});
		//------------------ dự trù thuốc vật tư tiêu hao khoa phòng
		//YC xuat  du tru
		$("#toolbarIdtbYCXuatDTTH").on("click", function(e) {
			EventUtil.setEvent("nhapkho_success", function(e) {
				DlgUtil.close("dlgNhapKho");
				_loadDSPhieu();
				$("#toolbarIdtbSuaDTTH").prop("disabled", true);
			});
			EventUtil.setEvent("nhapkho_cancel", function(e) {
				DlgUtil.close("dlgNhapKho");
				_loadDSPhieu();
				$("#toolbarIdtbSuaDTTH").prop("disabled", true);
			});
			var myVar = {
				khoid : $("#cboKho").val(),
				nhapxuatid : 0,
				tenkho : $("#cboKho option:selected").text(),
				kieu : that.opt.gd,
				loainhapbu : "YC_XUATDTTHKP"
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất dự trù tiêu hao khoa phòng" + that.opt.title, 1200, 620);
			DlgUtil.open("dlgNhapKho");
		});
		// go duyet phieu
		$("#btnGoDuyet").on("click", function(e) {
			var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var _donthuoc = $("#" + _gridId_BN).jqGrid('getRowData', selrow);
			if (PHARMA_BANTHUOC_GODUYET == '1' && _donthuoc.LOAIPHIEUMAUBENHPHAM == 1 ) {
				DlgUtil.showMsg("Cấu hình không cho phép gỡ duyệt!");
				return; // Dừng thực hiện
			}
			else if (confirm("Bạn có thực sự muốn gỡ duyệt?")) {
				var row = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
				var _id = $("#" + _gridId_BN).jqGrid('getRowData', row).NHAPXUATID;
				var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC47T001.CANCEL", _id));
				if (ret.SUCCESS > 0) {
					DlgUtil.showMsg("Gỡ duyệt thành công");
					//$("#" + _gridId_BN).jqGrid('setCell', row, 7, 'Không duyệt');
					//$("#" + _gridId_BN).jqGrid('setCell', row, 3, 'KHONGDUYET');
					loadGridDataBN();
					/*_loadChiTiet(_id, KHONGDUYET);
					_formatRow(row, KHONGDUYET - 1);*/
				} else {
					DlgUtil.showMsg(ret.MESSAGE);
				}
			}
		});
		$("#btnHuyGuiDuyet").on("click", function(e) {
			var rowIndex = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var row = $("#" + _gridId_BN).jqGrid('getRowData', rowIndex);
			var params = [ row.NHAPXUATID ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.0001", params.join('$'));
			if (result == '1') {
				DlgUtil.showMsg('Huỷ gửi duyệt thành công.');
				loadGridDataBN();
				return;
			}
			DlgUtil.showMsg('Huỷ gửi duyệt thất bại.');
		});
		$("#btnInHDSD").on("click", function(e) {
			var selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
			var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
			var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'LOAIPHIEUTHUID');
			inHDSD(_phieuthuid, _loaiphieuthuid);
		});
		function inHDSD(_phieuthuid, _loaiphieuthuid, rptCodes) {
			var rpt_code = "";
			if (typeof rptCodes == 'object' && !Array.isArray(rptCodes)) {
				rpt_code = _loaiphieuthuid == 1 ? rptCodes.HOADON : rptCodes.PHIEUTHU;
			} else {
				rpt_code = _loaiphieuthuid == 1 ? 'PHIEUIN_BANTHUOC_KHACHLE' : 'PHIEUTHUTIEN_NHATHUOC_A4';
			}
			var par = [];
			par.push({
				name : _loaiphieuthuid == 1 ? 'i_phieuthuid' : 'phieuthuid',
				type : 'String',
				value : _phieuthuid.toString()
			});
			openReport('window', rpt_code, 'pdf', par);
			// in hd ct
			// L2PT-83983: them cấu hình in hóa đơn chi tiết bán thuốc
		}
		_loadcontrolgrid(_gridId_BT)
		$("#" + _gridId_BT).bind("jqGridBeforeSelectRow", function(e, rowid, orgClickEvent) {
			var _ret = $("#" + _gridId_BT).jqGrid('getRowData', rowid);
			if ($("#" + _gridId_BT).jqGrid('getCell', rowid, 'SL_YC') != '' && (/^-?\d*(\.\d+)?$/.test($("#" + _gridId_BT).jqGrid('getCell', rowid, 'SL_YC')))) {
				oldValue = _ret.SL_YC;
			}
		});
		$("#" + _gridId_BT).bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
			var _ret = $("#" + _gridId_BT).jqGrid('getRowData', rowid);
			var slyc = _ret.SL_YC;
			var slkhdung = _ret.SL_KHADUNG;
			if (parseFloat(slyc) > parseFloat(slkhdung)) {
				DlgUtil.showMsg("Số lượng yêu cầu không được vượt quá số lượng khả dụng");
				$("#" + _gridId_BT).jqGrid('setCell', rowid, 'SL_YC', oldValue);
			} else {
				var thanhTien = slyc * _ret.DON_GIA;
				$("#" + _gridId_BT).jqGrid('setCell', rowid, 'THANH_TIEN', thanhTien);
				reloadCash();
			}
		});
		// L2PT-46273 start
		$("#toolbarIdbtnInPhieuTT").on("click", function(e) {
			var selRowId = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var _nhapxuatid = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'NHAPXUATID');
			var par = [];
			par.push({
				name : 'nhapxuatid',
				type : 'String',
				value : _nhapxuatid.toString()
			});
			openReport('window', 'PHIEU_BANTHUOC_KHACHLE', 'pdf', par);
		});
		// L2PT-46273 end
		// L2PT-48810 start
		$("#btnSHOWEXTINF").on("click", function(e) {
			if (showExtInf) {
				$("#divTTBN_MORONG").hide();
				showExtInf = false;
			} else {
				$("#divTTBN_MORONG").show();
				showExtInf = true;
			}
		});
		// L2PT-69399 start
		$('#txtTIMKIEM').focusin(function() {
			$("#txtTIMKIEM").select();
		});
		$('#txtTIMKIEM').keyup(function(e) {
			if (e.which === 13) {
				loadGridDataBN();
			}
		});
		$('#txtTIMKIEM').scannerDetection(function(data) {
			$("#txtTIMKIEM").val(data);
			$("#txtTIMKIEM").select();
			if (data && data != "") {
				loadGridDataBN();
			}
		});
		// L2PT-69399 end
	}
	// lay du lieu cac dich vu da chon
	function loadDV() {
		objData = new Object();
		_phieuInfo = [];
		// L2PT-23349 start
		_arrMG = [];
		_DMMG = [];
		// L2PT-23349 end
		//L2PT-19304 start
		_DM_HTTT = [];
		_arrHTTT = [];
		//L2PT-19304 end
		_tien_hoadon = 0;
		obj_2 = new Object();
		_hetphieu = false;
		_hetphieu_2 = false;
		_loaiphieuthu = _THUTIEN;
		selRowId = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
		var _loaiphieu = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'LOAIPHIEUMAUBENHPHAM');
		if (_loaiphieu == 2)
			_loaiphieuthu = _HOANUNG;
		var _loaitt = $("#cboHINHTHUCTHANHTOAN").val();
		var _loaiso = 3;
		var _loaidt = -1;
		_co_dv = false;
		setEnabled([ 'txtMAPHIEUTHU', 'cboMANHOMPHIEUTHU' ], []);
		var grid = $("#" + _gridId_DV);
		var _selRowIds = grid.jqGrid('getGridParam', 'selarrrow');
		for (var j = 0; j < _ds_nhomtt.length; j++) {
			var _tongtien = 0;
			var _thucthu = 0;
			var _miengiamdv = 0;
			var _bhyt_tra = 0;
			var _DSDV = [];
			var _ds_maubpid = [];
			var _ds_dtdvid = [];
			var _ds_dvkbid = "";
			var count = 0;
			for (var i = 0; i < _selRowIds.length; i++) {
				var _dvRow = $("#" + _gridId_DV).jqGrid('getRowData', _selRowIds[i]);
				// L2PT-30897 start
				// L2PT-63280 start
				var _nhomthanhtoan = _dvRow.VAT_D;
				if (VPI_KHONGTACH_HD_VAT == 1) {
					_nhomthanhtoan = -1;
				} else if (VPI_VAT_TACH_HD != -1 && _nhomthanhtoan != VPI_VAT_TACH_HD) {
					_nhomthanhtoan = -1;
				}
				var _loaiTVT = _dvRow.LOAI_TVT;
				if (VPI_LOAITHUOC_TACH_HD != 1) {
					_loaiTVT = "TVT";
				}
				_nhomthanhtoan = _nhomthanhtoan + ";" + _loaiTVT;
				// L2PT-63280 end
				// L2PT-30897 end
				if (_nhomthanhtoan == _ds_nhomtt[j]) {
					var _objRow = new Object();
					_ds_dvkbid += _dvRow.DICHVUKHAMBENHID + ",";
					_objRow.THUCTHU = _dvRow.THANHTIEN;
					_objRow.TIEN_BHYT_TRA = 0;
					_objRow.TIEN_MIENGIAM = 0;
					_objRow.DICHVUKHAMBENHID = _dvRow.DICHVUKHAMBENHID;
					_objRow.KHOANMUCID = _dvRow.KHOANMUCID;
					_objRow.TENKHOANMUC = _dvRow.TENKHOANMUC;
					_objRow.MAKHOANMUC = _dvRow.MAKHOANMUC;
					_objRow.VAT = _dvRow.VAT_D;
					_DSDV.push(_objRow);
					_tongtien += parseFloat(_dvRow.THANHTIEN);
					_thucthu += parseFloat(_dvRow.THANHTIEN);
					var _mbpid = parseInt(_dvRow.MAUBENHPHAMID);
					var _loaidtdv = parseInt(_dvRow.LOAIDOITUONG);
					_loaidt = _loaidtdv;
					if (_ds_maubpid.indexOf(_mbpid) == -1)
						_ds_maubpid.push(_mbpid);
					if (_ds_dtdvid.indexOf(_loaidtdv) == -1)
						_ds_dtdvid.push(_loaidtdv);
					count++;
					if (count == SL_BANGHI_HD) {
						_tongtien = parseFloat(_tongtien.toFixed(2));
						_bhyt_tra = 0;
						_thucthu = parseFloat(_thucthu.toFixed(2));
						_miengiamdv = 0;
						_ds_dvkbid = _ds_dvkbid.slice(0, -1);
						var _ds_maubpid_str = JSON.stringify(_ds_maubpid);
						var _ds_dtdvid_str = JSON.stringify(_ds_dtdvid);
						_ds_maubpid_str = _ds_maubpid_str.slice(1, -1);
						_ds_dtdvid_str = _ds_dtdvid_str.slice(1, -1);
						if (_DSDV.length > 0) {
							_loaiso = 3;
							// L2PT-63283 start
							if (fConfig.VPI_LAY_NHIEU_LOAISO == 1) {
								_loaiso = '3,4';
							}
							// L2PT-63283 end
							// L2PT-63280 start
							if (_ds_nhomtt[j].indexOf(";CPTBC") != -1) {
								_loaiso = 4;
							}
							// L2PT-63280 end
							var _kieuthu_2 = _THUTIEN;
							var _input_2 = _loaiso + "$" + _kieuthu_2 + "$" + _loaitt + "$" + _phong_id;
							var _result_2 = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_sophieuthu, _input_2);
							_dsSo_2 = _result_2;
							if (_dsSo_2.length == 0) {
								_hetphieu_2 = true;
								obj_2.MAPHIEUTHU = "";
								obj_2.NHOMPHIEUTHUID = "";
								obj_2.MANHOMPHIEUTHU = "";
								obj_2.LOAIPHIEUTHU = "";
								obj_2.SOPHIEUTO = "";
								obj_2.SOPHIEUFROM = "";
								obj_2.KHOASOPHIEUTU = "";
							} else {
								$('#cboMANHOMPHIEUTHU').empty();
								$.each(_dsSo_2, function(i, _so) {
									$('#cboMANHOMPHIEUTHU').append($('<option>', {
										value : i,
										text : _so.MANHOMPHIEUTHU
									}));
								});
								$('#cboMANHOMPHIEUTHU').val(0);
								_hetphieu_2 = false;
								obj_2.MAPHIEUTHU = _dsSo_2[0].MAPHIEUTHU;
								obj_2.NHOMPHIEUTHUID = _dsSo_2[0].NHOMPHIEUTHUID;
								obj_2.MANHOMPHIEUTHU = _dsSo_2[0].MANHOMPHIEUTHU;
								obj_2.LOAIPHIEUTHU = _dsSo_2[0].LOAIPHIEUTHU;
								obj_2.SOPHIEUTO = _dsSo_2[0].SOPHIEUTO;
								obj_2.SOPHIEUFROM = _dsSo_2[0].SOPHIEUFROM;
								obj_2.KHOASOPHIEUTU = _dsSo_2[0].KHOASOPHIEUTU;
								obj_2.LOAIPHIEUTHUID = _THUTIEN + "";
								obj_2.LOAIDOITUONG = _loaidt + "";
								obj_2.DS_MAUBENHPHAMID = _ds_maubpid_str;
								obj_2.DANHSACHDOITUONGDICHVU = _ds_dtdvid_str;
								obj_2.TONGTIEN = _thucthu + "";
								obj_2.THUCTHU = _thucthu + "";
								obj_2.TIEN_BHYT_TRA = _bhyt_tra + "";
								obj_2.MIENGIAMDV = _miengiamdv + "";
								obj_2.NHOMTHANHTOAN = _ds_nhomtt[j] + "";
								_tien_hoadon += _thucthu;
								var arr = [];
								var _PTCT = [];
								for (var y = 0; y < _DSDV.length; y++) {
									if (arr.indexOf(_DSDV[y].KHOANMUCID) == -1) {
										arr.push(_DSDV[y].KHOANMUCID);
									}
								}
								for (var m = 0; m < arr.length; m++) {
									var _km = new Object();
									_km.TONGTIEN = 0;
									for (var n = 0; n < _DSDV.length; n++) {
										if (_DSDV[n].KHOANMUCID == arr[m]) {
											_km.KHOANMUCID = _DSDV[n].KHOANMUCID;
											_km.TENKHOANMUC = _DSDV[n].TENKHOANMUC;
											_km.MAKHOANMUC = _DSDV[n].MAKHOANMUC;
											_km.TONGTIEN += parseFloat(_DSDV[n].THUCTHU);
										}
									}
									_km.TONGTIEN += "";
									_PTCT.push(_km);
								}
								var _noidungthu = "";
								for (var x = 0; x < _PTCT.length; x++) {
									_noidungthu += _PTCT[x].TENKHOANMUC + ": " + _PTCT[x].TONGTIEN + ", ";
								}
								_noidungthu = _noidungthu.slice(0, -2);
								_noidungthu = "Thu tiền thuốc";
								// L2PT-28406 start
								if (VPI_NHAP_GHICHU == 2) {
									_noidungthu = "";
								}
								// L2PT-28406 end
								obj_2.NOIDUNGTHU = _noidungthu;
								obj_2.NOIDUNGIN = _noidungthu;
								obj_2.DSDV = _DSDV;
								obj_2.PTCT = _PTCT;
								_phieu = clone(obj_2);
								_phieuInfo.push(_phieu);
							}
							_co_dv = true;
						}
						_tongtien = 0;
						_thucthu = 0;
						_miengiamdv = 0;
						_bhyt_tra = 0;
						_DSDV = [];
						_ds_maubpid = [];
						_ds_dtdvid = [];
						_ds_dvkbid = "";
						count = 0;
					}
				}
			}
			_tongtien = parseFloat(_tongtien.toFixed(2));
			_bhyt_tra = 0;
			_thucthu = parseFloat(_thucthu.toFixed(2));
			_miengiamdv = 0;
			_ds_dvkbid = _ds_dvkbid.slice(0, -1);
			var _ds_maubpid_str1 = JSON.stringify(_ds_maubpid);
			var _ds_dtdvid_str1 = JSON.stringify(_ds_dtdvid);
			_ds_maubpid_str1 = _ds_maubpid_str1.slice(1, -1);
			_ds_dtdvid_str1 = _ds_dtdvid_str1.slice(1, -1);
			// console.log(_DSDV);
			/*objData.LOAIDOITUONG = _loaidt + "";
			objData["DS_MAUBENHPHAMID"] = _ds_maubpid_str1;
			objData["DANHSACHDOITUONGDICHVU"] = _ds_dtdvid_str1;
			objData["TONGTIEN"] = _thucthu + "";
			objData["THUCTHU"] = _thucthu + "";
			objData["TIEN_BHYT_TRA"] = _bhyt_tra + "";
			objData["MIENGIAMDV"] = _miengiamdv + "";
			objData["DOITUONGBENHNHANID"] = 2;
			objData["DSDVKBID"] = "";*/
			if (_DSDV.length > 0) {
				_loaiso = 3;
				// L2PT-63283 start
				if (fConfig.VPI_LAY_NHIEU_LOAISO == 1) {
					_loaiso = '3,4';
				}
				// L2PT-63283 end
				// L2PT-63280 start
				if (_ds_nhomtt[j].indexOf(";CPTBC") != -1) {
					_loaiso = 4;
				}
				// L2PT-63280 end
				var _sql_par_3 = [];
				var _kieuthu_3 = _THUTIEN;
				var _input_3 = _loaiso + "$" + _kieuthu_3 + "$" + _loaitt + "$" + _phong_id;
				var _result_3 = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_sophieuthu, _input_3);
				_dsSo_2 = _result_3;
				if (_dsSo_2.length == 0) {
					_hetphieu_2 = true;
					obj_2.MAPHIEUTHU = "";
					obj_2.NHOMPHIEUTHUID = "";
					obj_2.MANHOMPHIEUTHU = "";
					obj_2.LOAIPHIEUTHU = "";
					obj_2.KHOASOPHIEUTU = "";
					obj_2.SOPHIEUTO = "";
					obj_2.SOPHIEUFROM = "";
				} else {
					$('#cboMANHOMPHIEUTHU').empty();
					$.each(_dsSo_2, function(i, _so) {
						$('#cboMANHOMPHIEUTHU').append($('<option>', {
							value : i,
							text : _so.MANHOMPHIEUTHU
						}));
					});
					$('#cboMANHOMPHIEUTHU').val(0);
					_hetphieu_2 = false;
					obj_2.MAPHIEUTHU = _dsSo_2[0].MAPHIEUTHU;
					obj_2.NHOMPHIEUTHUID = _dsSo_2[0].NHOMPHIEUTHUID;
					obj_2.MANHOMPHIEUTHU = _dsSo_2[0].MANHOMPHIEUTHU;
					obj_2.LOAIPHIEUTHU = _dsSo_2[0].LOAIPHIEUTHU;
					obj_2.KHOASOPHIEUTU = _dsSo_2[0].KHOASOPHIEUTU;
					obj_2.SOPHIEUTO = _dsSo_2[0].SOPHIEUTO;
					obj_2.SOPHIEUFROM = _dsSo_2[0].SOPHIEUFROM;
					obj_2.LOAIPHIEUTHUID = _THUTIEN + "";
					obj_2.LOAIDOITUONG = _loaidt + "";
					obj_2.DS_MAUBENHPHAMID = _ds_maubpid_str1;
					obj_2.DANHSACHDOITUONGDICHVU = _ds_dtdvid_str1;
					obj_2.TONGTIEN = _thucthu + "";
					obj_2.THUCTHU = _thucthu + "";
					obj_2.TIEN_BHYT_TRA = _bhyt_tra + "";
					obj_2.MIENGIAMDV = _miengiamdv + "";
					obj_2.NHOMTHANHTOAN = _ds_nhomtt[j] + "";
					_tien_hoadon += _thucthu;
					var arr1 = [];
					var _PTCT1 = [];
					for (var y1 = 0; y1 < _DSDV.length; y1++) {
						if (arr1.indexOf(_DSDV[y1].KHOANMUCID) == -1) {
							arr1.push(_DSDV[y1].KHOANMUCID);
						}
					}
					for (var m1 = 0; m1 < arr1.length; m1++) {
						var _km1 = new Object();
						_km1.TONGTIEN = 0;
						for (var n1 = 0; n1 < _DSDV.length; n1++) {
							if (_DSDV[n1].KHOANMUCID == arr1[m1]) {
								_km1.KHOANMUCID = _DSDV[n1].KHOANMUCID;
								_km1.TENKHOANMUC = _DSDV[n1].TENKHOANMUC;
								_km1.MAKHOANMUC = _DSDV[n1].MAKHOANMUC;
								_km1.TONGTIEN += parseFloat(_DSDV[n1].THUCTHU);
							}
						}
						_km1.TONGTIEN += "";
						_PTCT1.push(_km1);
					}
					var _noidungthu1 = "";
					for (var x1 = 0; x1 < _PTCT1.length; x1++) {
						_noidungthu1 += _PTCT1[x1].TENKHOANMUC + ": " + _PTCT1[x1].TONGTIEN + ", ";
					}
					_noidungthu1 = _noidungthu1.slice(0, -2);
					_noidungthu1 = "Thu tiền thuốc";
					obj_2.NOIDUNGTHU = _noidungthu1;
					obj_2.NOIDUNGIN = _noidungthu1;
					obj_2.DSDV = _DSDV;
					obj_2.PTCT = _PTCT1;
					_phieu = clone(obj_2);
					_phieuInfo.push(_phieu);
				}
				_co_dv = true;
			}
			_tien_hoadon = parseFloat(_tien_hoadon.toFixed(2));
		}
		//L2PT-8931 start
		_loaiso = 3;
		var _kieuthu = 6;
		var _input = _loaiso + "$" + _kieuthu + "$" + _loaitt + "$" + _phong_id;
		_dsSo = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_sophieuthu, _input);
		if (_dsSo.length == 0) {
			_hetphieu = true;
			obj.MAPHIEUTHU = "";
			obj.NHOMPHIEUTHUID = "";
			obj.MANHOMPHIEUTHU = "";
			obj.LOAIPHIEUTHU = "";
			obj.LOAIPHIEUTHUID = _kieuthu + "";
		}
//	    else if(_size_sopt>1){
//	    	DlgUtil.showMsg("sổ phiếu thu không hợp lệ");
//	    	return;
//	    }
		else {
			_hetphieu = false;
			obj.MAPHIEUTHU = _dsSo[0].MAPHIEUTHU;
			obj.SOPHIEUTO = _dsSo[0].SOPHIEUTO;
			//tuyennx_add_start_20171121 yc HISL2CORE-599
			obj.KHOASOPHIEUTU = _dsSo[0].KHOASOPHIEUTU;
			//tuyennx_add_end_20171121 yc HISL2CORE-599
			obj.SOPHIEUFROM = _dsSo[0].SOPHIEUFROM;
			obj.NHOMPHIEUTHUID = _dsSo[0].NHOMPHIEUTHUID;
			obj.MANHOMPHIEUTHU = _dsSo[0].MANHOMPHIEUTHU;
			obj.LOAIPHIEUTHU = _dsSo[0].LOAIPHIEUTHU;
			obj.LOAIPHIEUTHUID = _kieuthu + "";
			obj.LOAIDOITUONG = "-1";
			obj.LOAITHUTIEN = "-1";
			obj.DS_MAUBENHPHAMID = "";
			obj.DANHSACHDOITUONGDICHVU = "";
			obj.TIEN_BHYT_TRA = "0";
			obj.MIENGIAMDV = "0";
			objData["PHONGID_DANGNHAP"] = _phong_id;
//	    	console.log(JSON.stringify(objData));
		}
		//L2PT-8931 end	
		$("#txtNGUOILAP").val(_user_name);
		$('#txtNGAYLAP').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		if (_co_dv && _loaiphieuthu == _THUTIEN) {
			FormUtil.setObjectToForm("ttThuTien", "", obj_2);
			$("#cboMANHOMPHIEUTHU").val(0);
		}
		if (_loaiphieuthu == _HOANUNG) {
			_hetphieu_2 = false;
			_phieuInfo = [];
			_tien_hoadon = _tien_hoadon * -1;
			var _kieuthu = _loaiphieuthu;
			/*var _sql_par = [];
			_sql_par.push({ name : "[0]", value : _loaiso });
			_sql_par.push({ name : "[1]", value : (_kieuthu) });
			_sql_par.push({ name : "[2]", value : _loaitt });
			_sql_par.push({ name : "[3]", value : _phong_id });*/
			var _input = _loaiso + "$" + _kieuthu + "$" + _loaitt + "$" + _phong_id;
			_dsSo = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_sophieuthu, _input);
			if (_dsSo.length == 0) {
				_hetphieu = true;
				obj.MAPHIEUTHU = "";
				obj.NHOMPHIEUTHUID = "";
				obj.MANHOMPHIEUTHU = "";
				obj.LOAIPHIEUTHU = "";
				obj.LOAIPHIEUTHUID = _loaiphieuthu + "";
			}
//			 else if(_size_sopt>1){
//			 DlgUtil.showMsg("sổ phiếu thu không hợp lệ");
//			 return;
//			 }
			else {
				_hetphieu = false;
				obj.MAPHIEUTHU = _dsSo[0].MAPHIEUTHU;
				obj.SOPHIEUTO = _dsSo[0].SOPHIEUTO;
				// tuyennx_add_start_20171121 yc HISL2CORE-599
				obj.KHOASOPHIEUTU = _dsSo[0].KHOASOPHIEUTU;
				// tuyennx_add_end_20171121 yc HISL2CORE-599
				obj.SOPHIEUFROM = _dsSo[0].SOPHIEUFROM;
				obj.NHOMPHIEUTHUID = _dsSo[0].NHOMPHIEUTHUID;
				obj.MANHOMPHIEUTHU = _dsSo[0].MANHOMPHIEUTHU;
				obj.LOAIPHIEUTHU = _dsSo[0].LOAIPHIEUTHU;
				obj.LOAIPHIEUTHUID = _loaiphieuthu + "";
				obj.LOAIDOITUONG = "-1";
				obj.LOAITHUTIEN = "-1";
				obj.DS_MAUBENHPHAMID = "";
				obj.DANHSACHDOITUONGDICHVU = "";
				obj.TIEN_BHYT_TRA = "0";
				obj.MIENGIAMDV = "0";
				obj.TONGTIEN = _tien_hoadon + "";
				obj.THUCTHU = _tien_hoadon + "";
				obj.NOIDUNGTHU = $("#txtGHICHU").val();
				obj.NOIDUNGIN = $("#txtGHICHU").val();
				obj.NHOMTHANHTOAN = "";
				objData["PHONGID_DANGNHAP"] = _phong_id;
				// console.log(JSON.stringify(objData));
			}
			$("#txtNGUOILAP").val(_user_name);
			$('#txtNGAYLAP').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			if (_dsSo && _dsSo.length > 0) {
				var obj_dis = new Object();
				obj_dis.LOAIPHIEUTHUID = obj.LOAIPHIEUTHUID;
				// obj_dis.TONGTIEN = vienphi_tinhtien.convertNumToCurrency(obj.TONGTIEN, true);
				// obj_dis.THUCTHU = vienphi_tinhtien.convertNumToCurrency(obj.THUCTHU, true);
				obj_dis.MAPHIEUTHU = obj.MAPHIEUTHU;
				FormUtil.setObjectToForm("ttThuTien", "", obj_dis);
				$('#cboMANHOMPHIEUTHU').empty();
				$.each(_dsSo, function(i, _so) {
					$('#cboMANHOMPHIEUTHU').append($('<option>', {
						value : i,
						text : _so.MANHOMPHIEUTHU
					}));
				});
				$('#cboMANHOMPHIEUTHU').val(0);
			}
			_phieuInfo.push(obj);
		}
		cal(_loaiphieuthu);
		// L2PT-21476 enable cbo HTTT
		// L2PT-21476 them cbo ghi chu
		// L2PT-23349 thêm btnHTTT, btnLYDO
		setEnabled([ 'btnLuu', 'btnLuuIn', 'btnHuyBo', 'txtGHICHU', 'cboGHICHU', 'btnLYDO', 'btnHTTT', 'txtMIENGIAM_PT', 'cboHINHTHUCTHANHTOAN' ], [ 'btnThem' ]);
	}
	// tinh toan va hien thi du lieu vien phi
	function cal(_loaiphieuthu) {
		var _sotien = _tien_hoadon;
		var objTien = new Object();
		var objTien_dis = new Object();
		setEnabled([], [ 'txtMIENGIAM_PT' ]);
		var _chietkhau = parseFloat($("#txtCHIETKHAU").val());
		val_m('txtMIENGIAM_PT', _chietkhau);
		_sotien = _sotien - _chietkhau;
		_sotien = parseFloat(_sotien.toFixed(2));
		if (_loaiphieuthu == _THUTIEN) {
			setEnabled([ 'txtMIENGIAM_PT', 'txtLYDO', 'txtTLMIENGIAM' ], [ 'txtTONGTIEN' ]);
			objTien.DANOP = (parseFloat(_vpData.DANOP) + _sotien).toFixed(2);
			objTien.NOPTHEM = (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
			//_noidungthu = VPI_SUDUNG_DMGC == 0 ? "Thu tiền thuốc" : ""; // L2PT-21476
			_noidungthu = $("#cboHINHTHUCTHANHTOAN option:selected").text();
			$("#txtGHICHU").val(_noidungthu);
			$("#txtTONGTIEN").val(vienphi_tinhtien.convertNumToCurrency(_tien_hoadon));
			$("#txtTHUCTHU").val(vienphi_tinhtien.convertNumToCurrency(_sotien));
			$("#lTHUCTHU").text("Thực thu");
			$("#lNOPTHEM").text("Nộp thêm");
		} else if (_loaiphieuthu == _HOANUNG) {
			objTien.HOANTRA = (parseFloat(_vpData.HOANTRA) + _sotien).toFixed(2);
			objTien.NOPTHEM = (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
			//_noidungthu = VPI_SUDUNG_DMGC == 0 ? "Hoàn trả tiền thuốc" : ""; // L2PT-21476
			_noidungthu = $("#cboHINHTHUCTHANHTOAN option:selected").text();
			$("#txtGHICHU").val(_noidungthu);
			$("#txtTONGTIEN").val(vienphi_tinhtien.convertNumToCurrency(_tien_hoadon));
			$("#txtTHUCTHU").val(vienphi_tinhtien.convertNumToCurrency(_sotien));
			$("#lTHUCTHU").text("Hoàn trả");
			$("#lNOPTHEM").text("Trả lại");
		}
		// L2PT-28406 start
		if (VPI_NHAP_GHICHU == 1 || VPI_NHAP_GHICHU == 2) {
			$("#txtGHICHU").val("");
		}
		// L2PT-28406 end
	}
	// lay du lieu cho grid vien phi
	function loadGridDataBN() {
		if (flagLoading)
			return;
		// L2PT-131223 start
		if(!gioihan_thoigian("VPI_SONGAY_TIMKIEM", "txtTU", "txtDEN" )){
			return false;
		}
		// L2PT-131223 end
		var objTimKiem = new Object();
		FormUtil.setFormToObject('divSearch', '', objTimKiem);
		var sql_par = []
		sql_par.push({
			"name" : "[0]",
			"value" : JSON.stringify(objTimKiem)
		});
		GridUtil.loadGridBySqlPage(_gridId_BN, _gridSQL_BN, sql_par, function() {
			// L2PT-56749 start
			$(".jqgrow", '#' + _gridId_BN).contextMenu('contextMenu_DT', {
				bindings : {
					'rSaoChepDT' : function(t) {
						// L2PT-56749 start
						var _khoid = $("#cboKho").val();
						if (typeof _khoid == 'undefined' || _khoid == "" || _khoid == 0 || _khoid == -1) {
							DlgUtil.showMsg("Chưa chọn kho");
							return;
						} else if (_khoid.includes(",") || _khoid.includes(";")) {
							DlgUtil.showMsg("Chỉ được chọn 1 kho");
							return;
						}
						// L2PT-56749 end
						var rowId = $(t).attr("id");
						var rowData = $('#' + _gridId_BN).jqGrid('getRowData', rowId);
						if (typeof rowData.NHAPXUATID == 'undefined' || rowData.NHAPXUATID == "") {
							DlgUtil.showMsg("Phiếu không được phép sao chép !");
							return;
						}
						if (rowData.KIEU == '3') {
							EventUtil.setEvent("banthuocbn", function(e) {
								DlgUtil.close("dlgbanthuoc");
								loadGridDataBN();
							});
							var myVar = {
								khoid : $("#cboKho").val(),
								nhapxuatid : rowData.NHAPXUATID,
								kieu : rowData.KIEU,
								copy : '1'
							};
							var title = "Bán thuốc";
							dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe", myVar, "Bán thuốc", 1200, 580);
							dlgPopup.open("dlgbanthuoc");
						} else if (rowData.KIEU == '2') {
							EventUtil.setEvent("trathuoc", function(e) {
								DlgUtil.close("dlgtrathuoc");
								loadGridDataBN();
							});
							var myVar = {
								nhapxuatid : rowData.NHAPXUATID,
								kieu : rowData.KIEU,
								copy : '1'
							};
							dlgPopup = DlgUtil.buildPopupUrl("dlgtrathuoc", "divDlg", "manager.jsp?func=../duoc/DUC34X003_PhieuTraThuoc", myVar, "Trả thuốc khách lẻ", 1200, 580);
							dlgPopup.open("dlgtrathuoc");
						}
					}
				},
				onContextMenu : function(event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#' + _gridId_BN);
					grid.setSelection(rowId);
					var selRowId = grid.jqGrid("getGridParam", "selrow");
					console.log('SELROW_BN: ' + selRowId);
					return true;
				},
			});
			// L2PT-56749 end
			// build menu DS Viện phí
			var ids = $("#" + _gridId_BN).getDataIDs();
			if (ids.length == 0) {
				$("#" + _gridId_DV).jqGrid("clearGridData");
				$("#" + _gridId_PT).jqGrid("clearGridData");
				FormUtil.clearForm('tongVP', "");
				//FormUtil.clearForm('ttVienphi', "");
				FormUtil.clearForm('ttThuTien', "");
				setEnabled([], [ 'btnThem' ]);
				// L2PT-24280 start
				_maubenhphamid = -1;
				_khoid = -1;
				// L2PT-24280 end
			} else {
				var _currentRowId = -1;
				// set icon
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = $("#" + _gridId_BN).jqGrid('getRowData', id);
					if (row.MAUBENHPHAMID == _maubenhphamid) {
						_currentRowId = id;
					}
					var _icon_loaiphieu = '';
					if (row.LOAIPHIEUMAUBENHPHAM == 2) {
						_icon_loaiphieu = '<center><img src="' + _opts.imgPath[1] + '" width="15px"></center>';
					} else {
						_icon_loaiphieu = '<center><img src="' + _opts.imgPath[0] + '" width="15px"></center>';
					}
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_LOAIPHIEU', _icon_loaiphieu);
					var _icon_trangthai = '';
					if (row.TRANGTHAIID == 1) {
						_icon_trangthai = '<center><img src="' + _opts.imgPath[6] + '" width="15px"></center>';
					} else if (PHARMA_BANTHUOC_TUDONGDUYET == '1' && row.TRANGTHAIID == 5 && row.DACODICHVUTHUTIEN == 1) {
						_icon_trangthai = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
					} else if (row.TRANGTHAIID == 5) {
						_icon_trangthai = '<center><img src="' + _opts.imgPath[5] + '" width="15px"></center>';
					} else if (row.TRANGTHAIID == 6) {
						_icon_trangthai = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
					}
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_TRANGTHAI', _icon_trangthai);
					var _icon_thutien = '';
					if (row.DACODICHVUTHUTIEN == 1) {
						_icon_thutien = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
					}
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_THUTIEN', _icon_thutien);
				}
				if (_currentRowId == -1)
					// select first row
					$("#" + _gridId_BN).setSelection(1);
				else {
					// select current row
					$("#" + _gridId_BN).setSelection(_currentRowId);
				}
			}
		});
	}
	// lay du lieu cho grid dich vu
	function loadGridDataDV(_maubenhphamid) {
		var sql_par = [];
		var _dvData = [];
		var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
		var _ret = $("#" + _gridId_BN).jqGrid('getRowData', selrow);
		if (_ret.TRANGTHAIID == '1') {
			_gridSQL_DV = 'VPI01T012.02.NX'
			sql_par.push({
				"name" : "[0]",
				"value" : _ret.NHAPXUATID
			});
		} else {
			_gridSQL_DV = 'VPI01T012.02.MBP'
			sql_par.push({
				"name" : "[0]",
				"value" : _maubenhphamid
			});
		}
		_fl_tinh = true;
		_ds_nhomtt = [];
		GridUtil.loadGridBySqlPage(_gridId_DV, _gridSQL_DV, sql_par, function() {
			_chot = true;
			_dvData = $("#" + _gridId_DV).jqGrid('getRowData');
			if (_fl_tinh) {
				tinhTongTien(_dvData);
			}
			if (!flagLoading) {
				var grid = $("#" + _gridId_DV);
				var ids = grid.getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var _dathutien = grid.jqGrid('getCell', id, 'DATHUTIEN');
					if (_dathutien == 3) {
						//GridUtil.markRow(_gridId_DV, id);
						$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
							color : 'blue'
						});
					}
				}
			} else {
				FormUtil.clearForm('ttThuTien', "");
				$("#" + _gridId_DV).showCol('cb');
				$("#cboHINHTHUCTHANHTOAN").val(1);
				$("#txtMIENGIAM_PT").val(0);
				var grid = $("#" + _gridId_DV);
				var ids = grid.getDataIDs();
				grid.jqGrid('resetSelection');
				for (var i = 0, il = ids.length; i < il; i++) {
					var _dathutien = grid.jqGrid('getCell', ids[i], 'DATHUTIEN');
					var _nhomthanhtoan = grid.jqGrid('getCell', ids[i], 'VAT_D');
					// L2PT-63280 start
					var _loaiTVT = grid.jqGrid('getCell', ids[i], 'LOAI_TVT');
					// L2PT-30897 start
					if (VPI_KHONGTACH_HD_VAT == 1) {
						_nhomthanhtoan = -1;
					} else if (VPI_VAT_TACH_HD != -1 && _nhomthanhtoan != VPI_VAT_TACH_HD) {
						_nhomthanhtoan = -1;
					}
					// L2PT-30897 end
					if (VPI_LOAITHUOC_TACH_HD != 1) {
						_loaiTVT = "TVT";
					}
					_nhomthanhtoan = _nhomthanhtoan + ";" + _loaiTVT;
					// L2PT-63280 end
					if (_ds_nhomtt.indexOf(_nhomthanhtoan) == -1) {
						_ds_nhomtt.push(_nhomthanhtoan);
					}
					var _thucthu = grid.jqGrid('getCell', ids[i], 'THANHTIEN');
					if (_dathutien == 3 || _thucthu == 0) {
						grid.jqGrid('delRowData', ids[i]);
					} else {
						grid.jqGrid('setSelection', ids[i], false);
						GridUtil.markRow(_gridId_DV, ids[i]);
					}
				}
				loadDV();
				setEnabled([], [ 'btnIn', 'btnInHDSD' ]);
			}
		});
		setEnabled([], [ 'btnIn', 'btnLuu', 'btnLuuIn', 'btnHuyBo', 'btnInHDSD' ]);
	}
	function tinhTongTien(_dvData) {
		var _tongtien = 0;
		var _danop = 0;
		var _hoantra = 0;
		var _nopthem = 0;
		var _miengiam = 0;
		var _vp_giaodich = new Object();
		var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T012.GD', _maubenhphamid);
		if (arr_dagiaodich && arr_dagiaodich.length > 0) {
			_vp_giaodich = arr_dagiaodich[0];
			_danop = parseFloat(_vp_giaodich.DANOP);
			_hoantra = parseFloat(_vp_giaodich.HOANUNG);
			_miengiam = parseFloat(_vp_giaodich.MIENGIAM);
		}
		for (var i = 0; i < _dvData.length; i++) {
			_tongtien += parseFloat(_dvData[i].THANHTIEN);
		}
		_tongtien = Math.abs(parseFloat(_tongtien.toFixed(2)));
		_danop = parseFloat(_danop.toFixed(2));
		var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
		var _donthuoc = $("#" + _gridId_BN).jqGrid('getRowData', selrow);
		var _chietkhau = $("#txtCHIETKHAU").val();
		if (_donthuoc.DACODICHVUTHUTIEN == 1) {
			console.log("Đã thu tiền");
		} else {
			_miengiam = _miengiam + parseFloat(_chietkhau);
		}
		_miengiam = parseFloat(_miengiam.toFixed(2));
		_hoantra = parseFloat(_hoantra.toFixed(2));
		_nopthem = _tongtien - _miengiam - _danop - _hoantra;
		_nopthem = _nopthem.toFixed(2);
		_vpData.TONGTIENDV = _tongtien;
		_vpData.DANOP = _danop + "";
		_vpData.HOANTRA = _hoantra + "";
		_vpData.NOPTHEM = _nopthem;
		_vpData.MIENGIAM = _miengiam + "";
		var _vpData_hienthi = vienphi_tinhtien.convertObjToCurrency(_vpData);
		FormUtil.setObjectToForm('tongVP', '', _vpData_hienthi);
	}
	// lay du lieu cho grid phieu thu
	function loadGridDataPT(_maubenhphamid) {
		if (flagLoading)
			return;
		var lookup_sqlPT = "";
		lookup_sqlPT = _gridSQL_PT;
		var sql_parPT = [];
		sql_parPT.push({
			"name" : "[0]",
			"value" : _maubenhphamid
		});
		// sql_parPT= RSUtil.setSysParam(sql_parPT, _param);
		GridUtil.loadGridBySqlPage(_gridId_PT, lookup_sqlPT, sql_parPT, function() {
			// build menu DS phiếu thu
			$(".jqgrow", '#' + _gridId_PT).contextMenu('contextMenu_PT', {
				bindings : {
					'rXemHDDT' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						// L2PT-105772 start
						/*ret = ajaxSvc.InvoicesWS.viewHoaDon(_phieuthuId, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
						var result = ret;
						if (result.includes("ERR:6"))
							DlgUtil.showMsg("Không tìm thấy hóa đơn");
						else if (result == "" || result.includes("ERR:")) {
							DlgUtil.showMsg("Lỗi trong quá trình xử lý");
						} else {
							var randomnumber = Math.floor((Math.random() * 100) + 1);
							var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
							win.document.write(result);
						}*/
						_inHDDTTheoPhieu(_phieuthuId, dsCH, _config_hddt, '', '');
						// L2PT-105772 end
					},
					// L2PT-19604 start
					//L2PT-21660 start 
					'rXemHDDTCD' : function(t) {
						if (flagLoading)
							return;
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var result = ajaxSvc.InvoicesWS.convertForVerifyFkey(_phieuthuId, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
						if (result.includes("ERR:1")) {
							DlgUtil.showMsg("Sai tài khoản/ mật khẩu");
						} else if (result.includes("ERR:6")) {
							DlgUtil.showMsg("Không tìm thấy hóa đơn");
						} else if (result.includes("ERR:7")) {
							DlgUtil.showMsg("Tài khoản không phù hợp");
						} else if (result.includes("ERR:8")) {
							DlgUtil.showMsg("Hóa đơn đã được chuyển đổi");
						} else if (result == "" || result.includes("ERR:")) {
							DlgUtil.showMsg("Lỗi trong quá trình xử lý(convertForVerifyFkey)"); // 220422
						} else {
							var _par = [ "3", "", "", "", _phieuthuId ];
							jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
							loadGridDataPT(_maubenhphamid);
							var randomnumber = Math.floor((Math.random() * 100) + 1);
							var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
							win.document.write(result);
						}
					},
					//L2PT-21660 end
					//L2PT-24260 start 
					'rInHDDTCD' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var result = ajaxSvc.InvoicesWS.convertForStoreFkey(_phieuthuId, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
						if (result.includes("ERR:6"))
							DlgUtil.showMsg("Không tìm thấy hóa đơn");
						else if (result == "" || result.includes("ERR:")) {
							DlgUtil.showMsg("Lỗi trong quá trình xử lý(convertForStoreFkey)"); // 220422
						} else {
							var randomnumber = Math.floor((Math.random() * 100) + 1);
							var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
							win.document.write(result);
						}
					},
					// L2PT-24260 end
					// L2PT-19604 end
					'rGuiHDDT' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var sql_par = [ _phieuthuId ];
						var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
							"name" : "[0]",
							"value" : _phieuthuId
						} ]);
						var rows = JSON.parse(data);
						if (rows != null && rows.length > 0) {
							var check = rows[0]["SYNC_FLAG"];
							if (check == 1) {
								DlgUtil.showMsg("Hóa đơn đã được gửi");
							} else {
								// L2PT-105772 start
								/*daythongtinbn();
								var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.INVOICES.BT", sql_par.join('$'));
								var ret = "";
								// L2PT-80540 start
								if (fConfig.VPI_FN_IMPORT_HDDT == 2) {
									ret = ajaxSvc.InvoicesWS.importInvByPattern(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD,
											INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
								} else {
									ret = ajaxSvc.InvoicesWS.importHoaDon(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD,
											INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
								}
								// L2PT-80540 end
								if (ret == "ERR:13") {
									var ret1 = ajaxSvc.InvoicesWS.listInvByCusFkey(_phieuthuId, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
									if (ret1 == "" || ret1.toUpperCase().includes("ERR")) {
										DlgUtil.showMsg("Lỗi trong quá trình xử lý");
										return;
									}
									var obj = convertXml2JSon(ret1);
									obj = JSON.parse(obj);
									var invoi_nb = obj.Data.Item.invNum;
									var _par = [ "1", INVOICES_WS_PATTERN, INVOICES_WS_SERIAL, parseInt(invoi_nb), _phieuthuid ];
									var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
									DlgUtil.showMsg("Hóa đơn đã phát hành cập nhật số hóa đơn thành công");
									return;
								}
								if (ret == "" || ret.toUpperCase().includes("ERR")) {
									DlgUtil.showMsg("Lỗi trong quá trình xử lý");
								} else {
									var _par = [ "1", INVOICES_WS_PATTERN, INVOICES_WS_SERIAL, ret.split("_")[1], _phieuthuid ];
									var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
									DlgUtil.showMsg("Gửi hóa đơn điện tử thành công");
								}*/
								// var _par = [_phieuthuid,ret];
								// var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.LOG",_par.join('$'));
								var kqGuiHD = guiHDDTTheoPhieu(_phieuthuId, dsCH, _config_hddt, '', 3, '', '');
								if (kqGuiHD != "1") {
									DlgUtil.showMsg(kqGuiHD);
								} else {
									loadGridDataPT(_maubenhphamid); // L2PT-129800
								}
								// L2PT-105772 end
							}
						}
					},
					'rHuyHDDT' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var sql_par = [ _phieuthuId ];
						var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
							"name" : "[0]",
							"value" : _phieuthuId
						} ]);
						var rows = JSON.parse(data);
						if (rows != null && rows.length > 0) {
							var check = rows[0]["SYNC_FLAG"];
							if (check == 1) {
								if (VPI_DAY_HOADONDT == 1) {
									// L2PT-105772 start
									/*ret = ajaxSvc.InvoicesWS.cancelHoaDon(_phieuthuId, INVOICES_URL_CANCEL, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD);
									if (ret == "OK:") {
										var _par = [ "0", "", "", "", _phieuthuid ];
										var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
										DlgUtil.showMsg("Hủy hóa đơn điện tử thành công");
									} else {
										DlgUtil.showMsg("Hủy hóa đơn điện tử thất bại");
									}*/
									cancelInv(_phieuthuId, dsCH);
									// L2PT-105772 end
								}
							} else {
								DlgUtil.showMsg("Hóa đơn điện tử chưa được gửi");
							}
						}
					},
					'rInBBHUY' : function(t) {
						if (flagLoading)
							return;
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var par = [];
						par.push({
							name : 'phieuthuid',
							type : 'String',
							value : _phieuthuid.toString()
						});
						var typeExport = "pdf";//$("#sltInBieuMau").val();
						CommonUtil.openReportGet('window', 'BIENBAN_HUY_HDDT_A4', typeExport, par, true, true);
					},
					// Hủy phiếu thu trên grid
					'rHuyPhieuThu' : function(t) {
						if (flagLoading)
							return;
						DlgUtil.open("dlgXacNhan");
					},
					// Khôi phục phiếu thu
					'rKhoiPhuc' : function(t) {
						// L2PT-76907
						if (flagLoading || fConfig.VPI_KHOIPHUCPHIEU != '1')
							return;
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.RESTORE', _phieuthuid);
						DlgUtil.showMsg(result);
						loadGridDataPT(_maubenhphamid);
						loadGridDataDV(_maubenhphamid);
						setEnabled([ 'btnHuy' ], []);
					},
					// Cập nhật thông tin phiếu thu
					'rNhapThongTinTT' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _mapthu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'MAPHIEUTHU');
						$("#txtMAPHIEUTHUVIEW").val(_mapthu);
						$("#txtTENCONGTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'TENCONGTYBN'));
						$("#txtDIACHI_CTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DIACHI_CTYBN'));
						$("#txtMASOTHUE_CTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'MASOTHUE_CTYBN'));
						$("#txtSOTAIKHOAN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'SOTAIKHOAN'));
						$("#txtTEN_NGANHANG").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'TEN_NGANHANG'));
						$("#txtEMAIL_CTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'EMAIL_CTYBN'));
						DlgUtil.open("dlgNhapThongTinTT");
					}
				},
				onContextMenu : function(event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#' + _gridId_PT);
					grid.setSelection(rowId);
					var selRowId = grid.jqGrid("getGridParam", "selrow");
					console.log('SELROW_PT: ' + selRowId);
					return true;
				},
			});
			var grid = $("#" + _gridId_PT);
			var ids = grid.getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _dahuyphieu = grid.jqGrid('getCell', id, 'DAHUYPHIEU');
				//L2PT-18469 start 
				var _SYNC_FLAG = grid.jqGrid('getCell', id, 'SYNC_FLAG');
				if (_SYNC_FLAG == 1) {
					grid.jqGrid('setRowData', id, "", {
						color : 'green'
					});
					_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 1, _icon);
					grid.jqGrid('setRowData', id, "", {
						color : 'green'
					});
				}
				//L2PT-18469 End
				if (_dahuyphieu == 1) {
					// GridUtil.markRow(_gridId_PT,id);
					_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 1, _icon);
					grid.jqGrid('setRowData', id, "", {
						color : 'gray'
					});
				}
				var phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', ids[i], 'PHIEUTHUID');
				if (phieuthuid == _phieuthuid) {
					$("#" + _gridId_PT).jqGrid('setSelection', ids[i], false);
					GridUtil.unmarkRow(_gridId_PT, rowIdMarked_PT);
					GridUtil.markRow(_gridId_PT, ids[i], '');
					rowIdMarked_PT = ids[i];
					fl = -1;
				}
			}
		});
	}
	// tạo popup nhập thông tin khách hàng
	function initPopup_TTBN() {
		dlgTTBN = DlgUtil.buildPopup("dlgNhapThongTinTT", "dlgTTBN", "Nhập thông tin khách hàng", 550, 300);
		var btnOK = $('#btn_CapNhatBN_OK');
		var btnClose = $('#btn_CapNhatBN_Close');
		btnOK.click(function() {
			var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
			var _phieuthu_id = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
			var _tencty = $("#txtTENCONGTYBN").val().trim();
			var _dc_cty = $("#txtDIACHI_CTYBN").val().trim();
			var _masothue = $("#txtMASOTHUE_CTYBN").val().trim();
			var _sotk = $("#txtSOTAIKHOAN").val().trim();
			var _ten_bank = $("#txtTEN_NGANHANG").val().trim();
			var _email_ctybn = $("#txtEMAIL_CTYBN").val().trim();
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T012.TTKH", _phieuthu_id + '$' + _tencty + '$' + _dc_cty + '$' + _masothue + '$' + _sotk + '$' + _ten_bank + '$' + _email_ctybn);
			if (fl == 1) {
				$("#" + _gridId_PT).trigger("reloadGrid");
				dlgTTBN.close();
				DlgUtil.showMsg("Cập nhật thành công");
			} else if (fl == -1) {
				DlgUtil.showMsg("Xảy ra lỗi");
			}
		});
		btnClose.click(function() {
			$("#txtMAPHIEUTHUVIEW").val("");
			$("#txtTENCONGTYBN").val("");
			$("#txtDIACHI_CTYBN").val("");
			$("#txtMASOTHUE_CTYBN").val("");
			$("#txtSOTAIKHOAN").val("");
			$("#txtTEN_NGANHANG").val("");
			$("#txtEMAIL_CTYBN").val("");
			dlgTTBN.close();
		});
	}
	// tạo popup hủy phiếu thu
	function initPopup_HuyPT() {
		dlgHuyPT = DlgUtil.buildPopup("dlgXacNhan", "dlgConfirm", "Xác nhận hủy phiếu", 500, 110);
		var textArea = $('#txtLYDOHUYPHIEU');
		var btnOK = $('#btn_HuyPT_OK');
		var btnClose = $('#btn_huyPT_Close');
		textArea.keyup(function() {
			if (textArea.val().length == 0) {
				btnOK.attr('disabled', true);
			} else {
				btnOK.attr('disabled', false);
			}
		});
		btnOK.click(function() {
			var _lydohuyphieu = textArea.val();
			_lydohuyphieu = FormUtil.escape(_lydohuyphieu);
			var fl = huyPhieuThu(_lydohuyphieu);
			if (fl) {
				textArea.val("");
				btnOK.attr('disabled', true);
				dlgHuyPT.close();
			}
		});
		btnClose.click(function() {
			textArea.val("");
			btnOK.attr('disabled', true);
			dlgHuyPT.close();
		});
	}
	// huy phieu thu
	function huyPhieuThu(_lydohuyphieu) {
		var obj = new Object();
		obj["PHIEUTHUID"] = _phieuthuid;
		obj["THOIGIANHUYPHIEU"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		obj["HINHTHUCTHANHTOANHUY"] = $("#cboLOAITHANHTOAN").val();
		obj["LYDOHUYPHIEU"] = _lydohuyphieu;
		// console.log("____________"+ _phieuthuid);
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T012.09', JSON.stringify(obj));
		if (fl == 1) {
			$("#" + _gridId_DV).hideCol('cb');
			FormUtil.clearForm('ttThuTien', '');
			// $("#cboLOAIPHIEUTHUID").val(1);
			$("#cboHINHTHUCTHANHTOAN").val(1);
			setEnabled([ 'btnThem' ], [ 'btnHuyBo', 'btnLuu', 'btnHuy', 'btnLuuIn', 'txtMABENHNHAN', 'cboHINHTHUCTHANHTOAN', 'txtNGAYLAP', 'calNGAYLAP', 'cboLOAIPHIEUTHUID', 'txtLYDO',
					'txtMIENGIAM_PT' ]);
			loadGridDataBN();
			// tuyennx_add_start_20181107 L2HOTRO-11895
			if (VPI_DAY_HOADONDT == 1) {
				var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
					"name" : "[0]",
					"value" : _phieuthuid
				} ]);
				var rows = JSON.parse(data);
				if (rows != null && rows.length > 0) {
					var check = rows[0]["SYNC_FLAG"];
					if (!check && check != 1) {
						return true;
					}
				}
				// L2PT-105772 start
				/*var ret1 = ajaxSvc.InvoicesWS.listInvByCusFkey(_phieuthuid, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
				if (ret1 == "" || ret1.toUpperCase().includes("ERR") || ret1 == "<Data></Data>") {
					DlgUtil.showMsg("Không tìm thấy hóa đơn này trên hệ thống HDDT, không thể hủy HDDT");
					return true;
				}*/
				// L2PT-48808 start
				/*var ret = ajaxSvc.InvoicesWS.cancelHoaDon(_phieuthuid, INVOICES_URL_CANCEL, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD);
				if (ret == "OK:") {
					var _par = [ "0", "", "", "", _phieuthuid ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
					if (_return == '-1') {
						DlgUtil.showMsg("Cập nhật trạng thái hủy HDDT không thành công");
						return true;
					}
					return true;
				} else {
					// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
					DlgUtil.showMsg("Lỗi hủy hóa đơn điện tử");
					// L2PT-37815 end
					return true;
				}*/
				// L2PT-48808 end
				cancelInv(_phieuthuid, dsCH);
				// L2PT-105772 end
			}
			// tuyennx_add_end_20181107
			return true;
		} else if (fl == 0) {
			DlgUtil.showMsg("Bạn không có quyển hủy phiếu thu này");
			return false;
		}
		// L2PT-76907 start
		else if (fl == -3) {
			DlgUtil.showMsg("Đơn thuốc đã duyệt, không thể hủy");
			return false;
		}
		// L2PT-76907 end
		else if (fl == -2) {
			DlgUtil.showMsg("Có dịch vụ viện phí đã thực hiện, không thể hủy phiếu thu");
			return false;
		} else if (fl == -1) {
			DlgUtil.showMsg("Cập nhật không thành công");
			return false;
		}
	}
	// tuyennx_add_start_ day thong tin bn len he thong HDDT
	function daythongtinbn() {
		var objCusData = new Object();
		var objCustomers = new Object();
		var objCustomer = new Object();
		var selrow = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
		var _donthuoc = $("#" + _gridId_BN).jqGrid('getRowData', selrow);
		objCustomer.Name = _donthuoc.TENBENHNHAN;;
		objCustomer.Code = _donthuoc.SOPHIEU;;
		objCustomer.TaxCode = "";
		objCustomer.Address = _donthuoc.DIACHI;
		objCustomer.BankAccountName = "";
		objCustomer.BankNumber = "";
		// start anhkn them thong tin email
		var _par_mabn = [ _phieuthuid ];
		var data_bn_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPT.TTPT.HDDT", _par_mabn.join('$'));
		var _email_info = data_bn_ar[0].EMAIL_CTYBN;
		// end anhkn them thong tin email
		objCustomer.Email = _email_info;
		objCustomer.Fax = "";
		objCustomer.Phone = "";
		objCustomer.ContactPerson = "";
		objCustomer.RepresentPerson = "";
		objCustomer.CusType = '0';
		objCustomers.Customer = objCustomer;
		objCusData.Customers = objCustomers;
		var str = JSON.stringify(objCusData);
		str = convertJSon2XML(str);
		var result = ajaxSvc.InvoicesWS.updateCus(str, INVOICES_URL_IMPORT, INVOICES_WS_USER, INVOICES_WS_PWD);
		if (result != "1") {
			DlgUtil.showMsg("Lỗi đẩy thông tin BN lên hệ thống HĐĐT: " + result);
		};
	}
	// tuyennx_add_end_ day thong tin bn len he thong HDDT
	function onFocus(fields) {
		var type = typeof fields;
		if (type.toLowerCase() == "string") {
			$("#" + id).focus();
			$("#" + id).css('background-color', 'yellow');
		} else if (type.toLowerCase() == "object") {
			var id = "";
			for (var i = 0; i < fields.length; i++) {
				id = fields[i];
				$("#" + id).css('background-color', 'yellow');
			}
			$("#" + id).focus();
		}
	}
	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
		}
	}
	function stringToDate(date) {
		var parts = date.split("/");
		return new Date(parts[2], parts[1] - 1, parts[0]);
	}
	function stringToDateTime(date) {
		var parts = date.split("/");
		var tails = parts[2].split(" ");
		var times = tails[1].split(":");
		var ret = new Date(tails[0], parts[1] - 1, parts[0], times[0], times[1], times[2]);
		return ret;
	}
	function noiDungThu(obj, nhomthanhtoan) {}
	function clone(obj) {
		if (null == obj || "object" != typeof obj)
			return obj;
		var copy = obj.constructor();
		for ( var attr in obj) {
			if (obj.hasOwnProperty(attr))
				copy[attr] = obj[attr];
		}
		return copy;
	}
	//L2PT-8931 start
	function val_m(ctl, val) {
		if (!val && val != 0)
			return;
		var jctl = $("#" + ctl);
		jctl.val(vienphi_tinhtien.convertNumToCurrency(val, true));
	}
	function get_val_m(ctl) {
		var jctl = $("#" + ctl);
		var val = jctl.val();
		if (!val && val != 0)
			return null;
		var ret = val.replace(/\./g, '');
		ret = ret.replace(',', '.');
		if (!isNaN(ret) && ret.indexOf('.') != ret.length - 1)
			val = ret;
		return val;
	}
	//L2PT-8931 end
	// 20190318
	function dayDuLieuKeDonCongBYT(mauBenhPhamID) {
		if (!congBYT)
			return;
		var kq = congBYT.dayDuLieuKeDonTheoMauBenhPhamID(mauBenhPhamID);
	}
	// 20190318
	function dayDuLieuDuyetDonCongBYT(mauBenhPhamID) {
		if (!congBYT)
			return;
		var benhNhanID = Number(congBYT.getBenhNhanID(mauBenhPhamID));
		if (benhNhanID <= 0)
			return;
		var kq = congBYT.dayDuLieuDuyetDonTheoMauBenhPhamID(mauBenhPhamID);
	}
	//check thuoc duyet barcode
	function checkThuocBarCode() {
		var rowIds = $('#grdDSDichVu').jqGrid('getDataIDs');
		for (var i = 0; i < rowIds.length; i++) {
			rowData = $('#grdDSDichVu').jqGrid('getRowData', rowIds[i]);
			var _color = '#FF9900';
			var ThongBao1 = 'Thuốc đã được check Barcode';
			var ThongBao2 = 'Thuốc chưa check Barcode';
			if (_DSBARCODE.indexOf(rowData["DICHVUKHAMBENHID"]) != -1) {
				$("#grdDSDichVu").jqGrid('setCell', rowIds[i], 'CHECKBARCODE', '1');
				$('#grdDSDichVu').find("tr[id='" + rowIds[i] + "']").find("td").css("background-color", _color);
				$('#grdDSDichVu').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', ThongBao1);
				jQuery('#grdDSDichVu').jqGrid('setSelection', i + 1);
			} else
				$('#grdDSDichVu').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', ThongBao2);
		}
	}
	//L2PT-9086 start
	function xacNhanBanThuoc(r_maubenhphamid, r_action, r_khoid) { //L2PT-22913 : thêm biến khoid 
		// L2PT-56749 start
		var PHARMA_NT_MACSYT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_NT_MACSYT');
		//L2PT-22913 start lấy mã cskcb theo cấu hình
		if (PHARMA_NT_MACSYT == '1') {
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC53M001.05", r_khoid);
			// L2PT-24280 start
			if (result.length > 0) {
				macsyt = result[0].MACSYT;
			} else {
				return "Không lấy được mã CSYT của đơn thuốc";
			}
			// L2PT-24280 end
		}
		// L2PT-56749 end
		//L2PT-22913 end
		// L2PT-36807 start
		//var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'BTKD_DAY_BYT');
		//if (BTKD_DAY_BYT == 1 && r_action !== "Upd") {
		// L2PT-36807 end
		if (r_action !== "Upd") {
			var objSend = new Object();
			var objData = new Object();
			var objHeader = new Object();
			var objBody = new Object();
			var objSeccurity = new Object();
			var objDonThuoc = new Object();
			var objDSChiTietThuoc = new Object();
			var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_URL');
			var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_USER');
			var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_PASS');
			var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD", [ r_maubenhphamid ].join('$'));
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", [ r_maubenhphamid ].join('$'));
			//var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC34X001_15",[ nhapxuatid ].join('$'));
			//tao header
			objHeader.SENDER_CODE = macsyt;
			objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
			objHeader.TRANSACTION_TYPE = "M0001";
			objHeader.TRANSACTION_NAME = "FTP";
			objHeader.TRANSACTION_ID = "";
			objHeader.REQUEST_ID = "";
			objHeader.ACTION_TYPE = "1";
			//tao don thuoc
			// check don thuoc lay tu tren cong hay khong
			var byt_donthuocid = data_btkd[0].BYT_DONTHUOCID;
			objDonThuoc.LUOT_BAN_ID = "";
			objDonThuoc.MA_DON_THUOC = (byt_donthuocid && byt_donthuocid > 0 ? "" : (macsyt + ".")) + data_btkd[0].MA_DON_THUOC;
			objDonThuoc.NHA_THUOC_ID = "";
			objDonThuoc.MA_NHA_THUOC = macsyt;
			objDonThuoc.TEN_NHA_THUOC = "";
			objDonThuoc.TEN_DUOC_SY = "";
			objDonThuoc.NGAY_BAN = jsonrpc.AjaxJson.getSystemDate('YYYY-MM-DD');
			objDonThuoc.TINH_TRANG = 3;
			objDonThuoc.SO_PHIEU = data_btkd[0].SO_PHIEU;;
			var objChiTietThuoc = [];
			for (var i = 0; i < data_btkd.length; i++) {
				if (data_btkd[i].SODANGKY != '') {
					var objThuoc = new Object();
					objThuoc.LUOT_BAN_ID = "";
					objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
					objThuoc.CHI_TIET_ID_CLIENT = data_btkd[i].DICHVUKHAMBENHID;
					objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
					objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
					objThuoc.MA_THUOC = data_btkd[i].MA;
					objThuoc.TEN_THUOC = data_btkd[i].TEN;
					objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
					objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
					objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
					objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
					objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
					objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
					objThuoc.SL_TUONG_DUONG = data_btkd[i].SOLUONG;
					objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
					objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
					objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
					objThuoc.HAN_DUNG = data_btkd[i].HAN_DUNG;
					objThuoc.SO_LO = data_btkd[i].SO_LO;
					objChiTietThuoc.push(objThuoc);
				}
			}
			// L2PT-9455(IT360-281165) start: kiểm tra tồn tại thuốc có số đăng ký
			if (objChiTietThuoc.length == 0) {
				return "Không tồn tại thuốc có số đăng ký, không thể đẩy dữ liệu lên cổng";
			}
			// L2PT-9455(IT360-281165) end
			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
			objBody.DON_THUOC = objDonThuoc;
			objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
			objData.HEADER = objHeader;
			objData.BODY = objBody;
			objData.SECURITY = objSeccurity;
			objData.HEADER = objHeader;
			objSend.DATA = objData;
			var x2js = new X2JS();
			objSend = JSON.stringify(objSend);
			var obj = x2js.json2xml_str($.parseJSON(objSend));
			obj = obj.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
			obj = btoa(unescape(encodeURIComponent(obj)));//atob(obj);
			//gui lay ma dinh danh va MK
			var resultCongBTKD = ajaxSvc.CongBTKDWS.xacNhanBanThuoc(BTKD_WS_URL, BTKD_WS_USER, BTKD_WS_PASS, obj);
			var resultText = $(resultCongBTKD).find("Error > Error_Message").text();
			// L2PT-24280 start
			if (!resultText) {
				resultText = "Có lỗi xảy ra";
				resultCongBTKD = "Có lỗi xảy ra";
			}
			DlgUtil.showMsg(resultText);
			var selRowId = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var _nhapXuatId = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'NHAPXUATID');
			var _par_upd_btkd = [ _nhapXuatId, resultText ];
			var _kq_upd_bktd = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC34X001.19", _par_upd_btkd.join('$'));
			return resultCongBTKD;
			// L2PT-24280 end
		}
		return "Cấu hình đang thiết lập là không gửi xác nhận bán thuốc lên cổng"; // L2PT-24280 
	}
	//L2PT-9086 end
	// L2PT-18894 start
	function onCboHtttChange() {
		// L2PT-31437 start
		$('#txtIDPOST').val("");
		$('#txtMA_GD_CK').val(""); //L2PT-28102
		$('#cboSOTAIKHOAN').val(""); // L2PT-17471// L2PT-18311
		var _loaitt = $("#cboHINHTHUCTHANHTOAN").val();
		if (_loaitt == 5) {
			if (VPI_HIEN_IDPOST == '2') {
				var selIDCache = localStorage.getItem('IDPOST') ? localStorage.getItem('IDPOST') : "";
				$("#cboPOS").val(selIDCache);
				$("#divPOS").show();
			}
			setEnabled([ 'txtIDPOST', 'cboPOS' ], []);
		} else {
			$("#cboPOS").val("");
			setEnabled([], [ 'txtIDPOST', 'cboPOS' ]);
			$("#divPOS").hide();
		}
		// L2PT-31437 end
		//L2PT-28102 start
		if (_loaitt == 2) {
			setEnabled([ 'txtMA_GD_CK', 'cboSOTAIKHOAN' ], []); // L2PT-17471// L2PT-18311
		} else {
			setEnabled([], [ 'txtMA_GD_CK', 'cboSOTAIKHOAN' ]); // L2PT-17471// L2PT-18311
		}
		//L2PT-28102 end
		// BVTM-3849 start
		if (_loaitt == 5 || _loaitt == 12) { // L2PT-4355 : them loai tt = 5
			ComboUtil.getComboTag("cboDVTT", 'VPI.DVTT.SP', _loaitt == 12 ? "QRCODE" : "POS", "", "", 'sp', '', '');
			$("#cboDVTT").prop("selectedIndex", 0);
			$("#divDVTT").show();
		} else {
			$("#cboDVTT").val("");
			$("#divDVTT").hide();
		}
		// BVTM-3849 end
		//L2PT-19304 start
		_arrHTTT = [];
		//L2PT-19304 end
		$("#txtGHICHU").val($("#cboHINHTHUCTHANHTOAN option:selected").text());
	}
	// L2PT-18894 end
	// L2PT-1555 start
	function payRefundArr(objData, arrHoanUng) {
		for (var i = 0; i < arrHoanUng.length; i++) {
			var rs = payRefund(objData, arrHoanUng[i]);
			if (!rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// Error
			if (rs.responseCode != '00') {
				DlgUtil.showMsg('Lỗi [' + rs.responseCode + ']: ' + rs.responseMessage);
			}
			return false;
		}
	}
	function payRefund(objData, objHoanUng) {
		console.log('paymentRefund >>', objData);
		try {
			var oData = {
				orderId : objHoanUng.ORDERID,
				amount : objHoanUng.HOANTRA,
				refundType : objHoanUng.REFUNDTYPE,
				description : 'Hoan tra VNPT Pay',
				txnId : objHoanUng.TXNID,
				qrTxnId : objHoanUng.QRTXNID,
				benhnhanId : objHoanUng.BENHNHANID,
				data : JSON.stringify(objData)
			};
			_rs = null;
			var url = '/vnpthis/api/payment/vnptpay/refund?uuid=' + RestInfo.uuid;
			var request = $.ajax({
				url : url,
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				beforeSend : function(xhr) {}
			});
			request.done(function(_response) {
				_rs = _response;
				console.log('_response', _response);
			});
			request.fail(function(jqXHR, textStatus) {
				DlgUtil.showMsg("ERR: " + textStatus);
				return false;
			});
			console.log('Payment.paymentRefund >> ', _rs);
			// Response Null
			if (!_rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// JWT expired 
			if (_rs.responseCode == 0) {
				// Thành công
				return _rs;
			}
			// Error
			if (_rs.responseCode != 0) {
				DlgUtil.showMsg('Lỗi: ' + _rs.responseMessage);
			}
			return _rs;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
	// L2PT-1555 end
	// HieuBD PaymentGateway VNPT PAY
	function paymentGenQrCode(objData) {
		console.log('paymentGenQrCode >>', objData);
		try {
			var oData = {
				orderID : '',
				amount : Math.ceil(objData.THUCTHU), // fix QRCODE
				expDate : '',
				purpose : 'Thanh toan VNPT Pay',
				items : JSON.stringify(objData.DSPHIEU),
				checksum : '',
				data : JSON.stringify(objData)
			};
			_rs = null;
			var url = '/vnpthis/api/payment/genqrcode?uuid=' + RestInfo.uuid + '&benhnhanid=' + objData.BENHNHANID;
			var request = $.ajax({
				url : url,
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				beforeSend : function(xhr) {}
			});
			request.done(function(_response) {
				_rs = _response;
				console.log('_response', _response);
			});
			request.fail(function(jqXHR, textStatus) {
				DlgUtil.showMsg("ERR: " + textStatus);
				return false;
			});
			console.log('Payment.paymentGenQrCode >> ', _rs);
			// Response Null
			if (!_rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// JWT expired 
			if (_rs.responseCode == 0) {
				// Thành công
				return _rs;
			}
			// Error
			if (_rs.responseCode != 0) {
				DlgUtil.showMsg('Lỗi: ' + _rs.responseMessage);
			}
			return _rs;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
	// End HieuBD PaymentGateway VNPT PAY
	// HieuBD PaymentGateway VNPT PAY
	// L2PT-54825 start
	function genQrCode(objData, _hinhThuc, _donViThanhToan, _benhnhanid) { // L2PT-54825
		try {
			var oData = {
				orderID : '',
				amount : Math.ceil(objData.THUCTHU), // fix QRCODE
				expDate : '',
				purpose : 'Thanh toan',
				items : objData.DSPHIEU,
				checksum : '',
				benhnhanid : _benhnhanid, // L2PT-54825
				donvithanhtoan : _donViThanhToan,
				hinhthuc : _hinhThuc,
				data : objData
			};
			console.log(oData);
			var result = '';
			$.ajax({
				url : '/vnpthis/api/payment/qrcode',
				beforeSend : function(xhr) {
					xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
				},
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				timeout : 3 * 60 * 1000,
				success : function(res) {
					result = res;
				},
				error : function() {}
			});
			return result;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
	// L2PT-54825 end
	function refundQrCode(phieuThuId, orderId, soTien, _loaiGiaoDich, _donViThanhToan) {
		try {
			var oData = {
				orderID : orderId,
				donvithanhtoan : _donViThanhToan,
				loaigiaodich : _loaiGiaoDich,
				benhnhanid : _benhnhan.BENHNHANID,
				phieuthuid : phieuThuId,
				sotien : soTien
			};
			console.log(oData);
			var result = '';
			$.ajax({
				url : '/vnpthis/api/payment/refund',
				beforeSend : function(xhr) {
					xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
				},
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				timeout : 3 * 60 * 1000,
				success : function(res) {
					result = res;
				},
				error : function() {}
			});
			return result;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
	// End HieuBD PaymentGateway VNPT PAY
	// Start HungND BIDVPayment
	function pmmSale(orderId, hisUser, uuid, posterminalid, amount, description, f1, f2, f3, f4, f5) {
		var url = VPI_PAY_PMM_URL + '/api/v1.0/public/test/payment/sale';
		try {
			var oData = {
				orderId : orderId,
				hisUser : hisUser,
				uuid : uuid,
				posterminalid : posterminalid,
				amount : Math.ceil(amount),
				description : description,
				f1 : description,
				f2 : hisUser,
				f3 : f3,
				f4 : f4,
				f5 : f5
			};
			_rs = null;
			var request = $.ajax({
				url : url,
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				timeout : 3 * 60 * 1000,
				beforeSend : function(xhr) {}
			});
			request.done(function(_response) {
				_rs = _response;
				console.log('BIDVPayment|response', _response);
			});
			request.fail(function(jqXHR, textStatus) {
				DlgUtil.showMsg("ERR: " + textStatus);
				return false;
			});
			console.log('Payment.paymentGenQrCode >> ', _rs);
			// Response Null
			if (!_rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// JWT expired
			if (_rs.responseCode == 0) {
				// Thành công
				return _rs;
			}
			return _rs;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
	// End HungND Agribank Payment
	function loadGridDataBT() {
		// var sql_par = RSUtil.buildParam("", [ $("#hidMauBenhPhamID").val()
		// ]);
		var rownum = 1;
		var _khoid = $("#cboKho").val();
		if (typeof _khoid == 'undefined' || _khoid == "" || _khoid == 0 || _khoid == -1) {
			DlgUtil.showMsg("Chưa chọn kho");
			return;
		} else if (_khoid.includes(",") || _khoid.includes(";")) {
			DlgUtil.showMsg("Chỉ được chọn 1 kho");
			return;
		}
		var param = _maubenhphamid + '$' + _khoid + '$';
		console.log(param);
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC90S001_04", param);
		var dsthuoc = [];
		for (var i = 0; i < data_ar.length; i++) {
			var param2 = data_ar[i].THUOCVATTUID + '$' + $("#cboKho").val() + '$' + data_ar[i].SL_YC;
			var dsthuocString = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC90S001_05", param2);
			var json_dsthuoc = JSON.parse(dsthuocString);
			if (json_dsthuoc.SUCCESS == '1') {
				var data = json_dsthuoc.MESSAGE;
				for (var k = 0; k < data.length; k++) {
					var objData = new Object();
					objData["ROWNUM"] = rownum;
					objData["THUOCVATTUID"] = data_ar[i].THUOCVATTUID;
					objData["TEN_THUOC"] = data_ar[i].TEN_THUOC;
					objData["MA_THUOC"] = data_ar[i].MA_THUOC;
					objData["DON_VI_TINH"] = data_ar[i].DON_VI_TINH;
					objData["DON_GIA_VAT"] = data[k].XUATGIABAN;
					objData["SDK"] = data_ar[i].SODANGKY;
					objData["GIADICHVU"] = data[k].GIADICHVU;
					objData["DON_GIA"] = data[k].GIANHANDAN;
					objData["GIATRANBHYT"] = data[k].GIATRANBHYT;
					objData["GIANHANDAN"] = data[k].GIANHANDAN;
					objData["GIABHYT"] = data[k].GIABHYT;
					objData["GIANHAP"] = data[k].GIANHAP;
					objData["HSD"] = GetFormattedDate(data[k].HANSUDUNG);
					objData["SL_YC_ORG"] = data[k].SOLUONG;
					objData["SL_YC"] = data[k].SOLUONG;
					objData["SOLUONGDUYET"] = data[k].SOLUONG;
					objData["SL_DON"] = data[k].SOLUONG;
					objData["SO_LO"] = data[k].SOLO;
					objData["THANH_TIEN"] = parseFloat(Number(data[k].GIANHANDAN) * Number(data[k].SOLUONG)).toFixed(3);
					objData["SL_KHADUNG"] = data[k].SLKHADUNG_NEW;
					// duonghn
					objData["TENDICHVU"] = data_ar[i].TEN_THUOC;
					objData["MA"] = data_ar[i].MA_THUOC;
					objData["DVT"] = data_ar[i].DON_VI_TINH;
					objData["DONGIA_VAT"] = data[k].XUATGIABAN;
					objData["HANSUDUNG"] = GetFormattedDate(data[k].HANSUDUNG);
					objData["SOLO"] = data[k].SOLO;
					objData["THANHTIEN"] = data[k].THANH_TIEN;
					// duonghn
					rownum = rownum + 1;
					dsthuoc.push(objData)
				}
			} else {
				var objData = new Object();
				objData["THUOCVATTUID"] = data_ar[i].THUOCVATTUID;
				objData["TEN_THUOC"] = data_ar[i].TEN_THUOC;
				objData["MA_THUOC"] = data_ar[i].MA_THUOC;
				objData["DON_VI_TINH"] = data_ar[i].DON_VI_TINH;
				objData["ROWNUM"] = rownum;
				objData["SL_YC_ORG"] = data_ar[i].SL_YC;
				objData["SL_YC"] = data_ar[i].SL_YC;
				objData["SL_DON"] = data_ar[i].SL_YC;
				objData["SOLUONGDUYET"] = '0';
				objData["SL_KHADUNG"] = '';
				objData["GHICHU"] = json_dsthuoc.MESSAGE;
				// duonghn
				objData["TENDICHVU"] = data_ar[i].TEN_THUOC;
				objData["MA"] = data_ar[i].MA_THUOC;
				objData["DVT"] = data_ar[i].DON_VI_TINH;
				// duonghn
				dsthuoc.push(objData)
				rownum = rownum + 1;
			}
			// dsthuoc.push(a);
		}
		/*$("#" + _gridId_BT).jqGrid("clearGridData");
		$("#" + _gridId_BT)[0].grid.beginReq();
		$("#" + _gridId_BT).jqGrid("setGridParam", {
			data : dsthuoc
		});
		var d = $("#" + _gridId_BT).jqGrid('getRowData');
		$("#" + _gridId_BT)[0].grid.endReq();
		var d = $("#" + _gridId_BT).jqGrid('getRowData');
		$("#" + _gridId_BT).trigger("reloadGrid");
		var d = $("#" + _gridId_BT).jqGrid('getRowData');*/
		$("#" + _gridId_BT).jqGrid("clearGridData");
		GridUtil.fetchGridData(_gridId_BT, dsthuoc);
		reloadCash();
		/*$("#" + _gridId_BT).jqGrid('setGridParam', {
			onSelectRow : function(id) {
			//loadRowSelectThuoc(id);
			//$('#txtSO_LUONG').removeAttr("disabled");
			//$('#cmdOK').removeAttr("disabled");
			//GridUtil.unmarkAll(_gridId_BT);
			//GridUtil.markRow(_gridId_BT, id);
			},
			gridComplete : function(id) {
				var rowIds = $("#" + _gridId_BT).jqGrid('getDataIDs');
				if (rowIds.length > 0) {
					//checkSoLuong();
				}
			}
		});*/
	}
	function GetFormattedDate(date) {
		if (date == '')
			return '';
		var ngay = date.substring(0, 2);
		var thang = date.substring(3, 6);
		var nam = '20' + date.substring(7, 9);
		if (thang == 'JAN')
			thang = '01'
		else if (thang == 'FEB')
			thang = '02'
		else if (thang == 'MAR')
			thang = '03'
		else if (thang == 'APR')
			thang = '04'
		else if (thang == 'MAY')
			thang = '05'
		else if (thang == 'JUN')
			thang = '06'
		else if (thang == 'JUL')
			thang = '07'
		else if (thang == 'AUG')
			thang = '08'
		else if (thang == 'SEP')
			thang = '09'
		else if (thang == 'OCT')
			thang = '10'
		else if (thang == 'NOV')
			thang = '11'
		else if (thang == 'DEC')
			thang = '12'
		else
			thang = '';
		var ketqua = ngay + '/' + thang + '/' + nam;
		return ketqua;
	};
	function guiYeuCau(_trangthaiid) {
		var rowIds = $('#' + _gridId_BT).jqGrid('getDataIDs');
		if (rowIds != null && rowIds.length > 0) {
			var today = new Date();
			var dd = today.getDate();
			var mm = today.getMonth() + 1; // January is 0!
			var yyyy = today.getFullYear();
			var hour = today.getHours();
			var minute = today.getMinutes();
			var second = today.getSeconds();
			if (dd < 10) {
				dd = '0' + dd;
			}
			if (mm < 10) {
				mm = '0' + mm;
			}
			if (hour.toString().length == 1) {
				hour = '0' + hour;
			}
			if (minute.toString().length == 1) {
				minute = '0' + minute;
			}
			if (second.toString().length == 1) {
				second = '0' + second;
			}
			today = dd + '/' + mm + '/' + yyyy + ' ' + hour + ':' + minute + ':' + second;
//			var _ngaylap = $("#txtNGAY_LAP").val() + ' ' + hour + ':' + minute + ':' + second;
			var _kieu;
			var _hinhThuc;
			var _trangThai;
			_kieu = "3";
			_hinhThuc = "15";
			_trangThai = _trangthaiid;
			var param_ar_kho;
			param_ar_kho = {
				"DOIUNGID" : $("#cboKho").val(),// kho cap
				"KHOID" : $("#cboKho").val(), // kho lap
				"MA_PHIEU" : laySTTKhachLe(),//$("#txtMA_PHIEU").val(),
				"NGUOINX" : _user_id, // L2PT-56749
				"TENKHACHHANG" : $("#txtTENBENHNHAN").val(),
				"NGAY_LAP" : today,
				"NGAYCAPNHAT" : today,
				"NGAYTHUCHIEN" : today,
				"SO_CHUNG_TU" : null,
				"NGAY_LAP_SCT" : null,
				"CHIET_KHAU" : "0",
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_CHIET_KHAU" : $("#txtCHIETKHAU").val(), // L2PT-56749
				"TIEN_DON_GIA" : $("#tiendon").val(),
				"KIEU" : _kieu,
				"HINHTHUCID" : _hinhThuc,
				"TRANGTHAIID" : _trangThai,
				"GHI_CHU" : "",
				"NHAPXUATID" : "",
				"NHAPXUATID_CHA" : "0",
				"NHACUNGCAPID" : null,
				"DIA_CHI" : $("#txtDIACHI").val(),
				"LOAIDONTHUOC" : $("#cboLoaiDonThuoc").val(),
				"NGUOI_GIAO" : null,
				"HSD" : null,
				"MAUBENHPHAMID" : _maubenhphamid,
				"DONTHUOCID" : "",
				"BACSY" : $("#txtBACSI").val(), // L2PT-56749
				"NGAYKE" : "",
				"NAMSINH" : $("#txtNAMSINH").val(), // L2PT-56749
				"GIOITINH" : $("#cboGioiTinh").val()
			// L2PT-56749
			// ChuanNT truyen them donthuocid de update nhapxuatid,nhapxuatctid
			};
			var param_str_kho = JSON.stringify(param_ar_kho);
			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]", "}");
			// xml nhap kho thuoc chi tiet
			var param_arr = $("#" + _gridId_BT).jqGrid('getRowData');
			var param_str = JSON.stringify(param_arr);
			// var param_str = JSON.stringify(param_arr);
			var _par = [ str_kho_json, param_str ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC34X001.03", _par.join('$'));
			/*var radioValue = $("input[name='radDoiTuong']:checked").val();
			if (radioValue == '3') {
				result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC33X032.04", _par.join('$'));
			} else {
				if (KETHUOC_NHATHUOC_TRUTON == '1') {
					 chuyen trang thai don thuoc nha thuoc ve tao moi
						cong kha dung lai cho don thuoc da ke
					
					
					var _parHuy = [ "DUC34X001", $("#hidMauBenhPhamID").val() ];
					var resultHuy = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC34X001.27", _parHuy.join('$'));
					var dataHuy = $.parseJSON(resultHuy);
					var _succesHuy = dataHuy.SUCCESS;
					if (_succesHuy == '0') {
						DlgUtil.showMsg("Lưu phiếu lỗi do đơn thuốc" + dataHuy.MESSAGE);
					} else {
						result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC34X001.03", _par.join('$'));
					}
				} else {
					result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC34X001.03", _par.join('$'));
				}
			}*/
			var data = $.parseJSON(result);
			var _succes = data.SUCCESS;
			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);
			} else {
				$("#hid_NHAPXUATID").val(_succes);
				// L2PT-56749 start
				try {
					//DUC34X001.17
					var parmbp = [];
					parmbp.push({
						"name" : "[0]",
						"value" : $("#hid_NHAPXUATID").val()
					});
					var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC34X001.17', parmbp);
					var phieunk = JSON.parse(data_ar);
					var maubenhphamid = phieunk[0].MAUBENHPHAMID;
					if (_trangthaiid == '5') {
						var daycong = $("#ckbDayCong").prop('checked') == true ? "1" : "0";
						if (daycong == '1' && PHARMA_NT_CHONCONGDUOC == '1') {
							_dayDonThuocOnline(maubenhphamid, "Cre")
						}
						if (PHARMA_KETNOI_CONG_BYT == '1') {
							DlgUtil.showMsg('Bán thuốc thành công !');
							if (PHARMA_DAYCONG_NEW == '1') {
								if ($("#hidDonThuocID").val() != "" && $("#hidDonThuocID").val() != "0") {
									_dayDonThuocOnline_new('1', maubenhphamid, '', 1, $("#hidDonThuocID").val(), "Upd", '', '', '')
								} else
									_dayDonThuocOnline_new('1', maubenhphamid, '', 1, $("#hidDonThuocID").val(), "Add", '', '', '')
							} else {
								if ($("#hidDonThuocID").val() != "" && $("#hidDonThuocID").val() != "0") {
									_daybtkdmbp(maubenhphamid, "Upd");
								} else
									_daybtkdmbp(maubenhphamid, "Cre");
							}
						}
					}
				} catch (err) {
					DlgUtil.showMsg('Có lỗi khi đẩy dữ liệu đơn thuốc !');
					console.log(err);
				}
				// L2PT-56749 end
				DlgUtil.showMsg('Lưu phiếu thành công !', function() {
					loadGridDataBN();
				});
			}
		} else {
			DlgUtil.showMsg('Danh sách thuốc không được rỗng !');
		}
	}
	function laySTTKhachLe() {
		var _par = [ "LAY_STT_KHACH_LE" ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC34X001.06", _par.join('$'));
		if (result == '0') {
			DlgUtil.showMsg('Lấy stt lỗi');
			return;
		} else {
			return result;
		}
	}
	function _dayDonThuocOnline_new(cauhinh, ret_maubenhpham_id, _options, _loaitiepnhanid, sophieu, type, user, pass, ca_type) {
		if (cauhinh == '1' || (cauhinh == '2' && user)) {
			var ds_option = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'DTDT_MA_OPTION_DAY');
			var ds_option_ntu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'DTDT_MA_OPTION_DAY_NTU');
			var ds_loaitiepnhan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'DTDT_LOAITIEPNHAN_DAY');
			var ds_options = ds_option.split(',');
//			user = 'bvdkbd.admin';
//			pass = 'Vnpt@123#';
			if ((((ds_option == '0' || $.inArray(_options, ds_options.split(',')) >= 0) && _loaitiepnhanid == '1') || ((ds_option_ntu == '0' || $.inArray(_options, ds_option_ntu.split(',')) >= 0) && _loaitiepnhanid != '1')) &&
					(ds_loaitiepnhan.includes(_loaitiepnhanid))) {
				var func = "tiepNhanDonThuocOnline";
				if (type == "Upd")
					func = "capNhatDonThuocOnline";
				var objData = {
					"FUNC" : func,
					"PARAM" : {
						"i_maubenhphamid" : ret_maubenhpham_id
					},
					"MAUBENHPHAMID" : ret_maubenhpham_id,
					"CAUSER" : user,
					"CAPASSWORD" : pass,
					"CA_TYPE" : ca_type
				};
				var request_url = '/vnpthis/service/donThuocOnline';
				$.ajax({
					type : "POST",
					contentType : "application/json; charset=utf-8",
					data : JSON.stringify(objData),
					url : request_url,
					beforeSend : function(xhr) {
						xhr.setRequestHeader('Authorization', 'Bearer ' + jsonrpc.AjaxJson.uuid);
					},
					success : function(data) {
						console.log("response success=" + JSON.stringify(data));
						if (data.responseCode == '00') {
							DlgUtil.showMsg(data.responseMessage);
							setTimeout(function() {}, 2000);
							return 0;
						}
						if (data.responseCode != '200') {
							if (sophieu)
								DlgUtil.showMsg("Lỗi đẩy dữ liệu lên cổng đơn thuốc điện tử số phiếu: " + sophieu + ':' + data.responseMessage);
							else
								DlgUtil.showMsg("Lỗi đẩy dữ liệu lên cổng đơn thuốc điện tử:" + data.responseMessage);
							setTimeout(function() {}, 2000);
							return 0;
						} else {
							var sql_par = [];
							sql_par.push({
								"name" : "[0]",
								"value" : ret_maubenhpham_id
							});
							jsonrpc.AjaxJson.execute("NGT.UD.DTDT", sql_par);
						}
					},
					error : function(xhr) {
						DlgUtil.showMsg("Lỗi đẩy dữ liệu lên cổng đơn thuốc điện tử!");
					}
				});
			}
		}
		return 1;
	};
	function _dayDonThuocOnline(ret_maubenhpham_id, r_action) {
//		var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA',
//				'BTKD_DAY_BYT');
		if (PHARMA_NT_CHONCONGDUOC == '1') {
			// var sql_par = [];
			// sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
			// jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
			var objSend = new Object();
			var objData = new Object();
			var objHeader = new Object();
			var objBody = new Object();
			var objSeccurity = new Object();
			var objDonThuoc = new Object();
			var objThongTinBN = new Object();
			var objDSChiTietThuoc = new Object();
			var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_URL_DTDT');
			var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_USER');
			var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_PASS');
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", [ ret_maubenhpham_id ].join('$'));
//			if($("#hidDonThuocID").val()!='' ||$("#hidDonThuocID").val()!='0')
//				{
//				var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC34X001.18",
//						[ ret_maubenhpham_id ].join('$'));
//				}
//			else
			var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD", [ ret_maubenhpham_id ].join('$'));
			// tao header
			objHeader.MESSAGE_VERSION = '3.0';
			objHeader.SENDER_CODE = macsyt;
			objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
			objHeader.TRANSACTION_TYPE = "M0002";
			objHeader.TRANSACTION_NAME = "3.0";
			objHeader.TRANSACTION_DATE = getsysdate();
			objHeader.TRANSACTION_ID = "";
			objHeader.REQUEST_ID = "";
			objHeader.PROVIDER = "12";
			if (r_action == "Upd")
				objHeader.ACTION_TYPE = "1";
			else
				objHeader.ACTION_TYPE = "0";
			// tao don thuoc
			objDonThuoc.DON_THUOC_ID = data_btkd[0].MAUBENHPHAMID;
			if (Number(data_btkd[0].BYT_DONTHUOCID) > 0) {
				objDonThuoc.MA_DON_THUOC = data_btkd[0].MA_DON_THUOC;
			} else
				objDonThuoc.MA_DON_THUOC = macsyt + "." + data_btkd[0].MA_DON_THUOC;
			objDonThuoc.MA_LUOT_KHAM = data_btkd[0].HOSOBENHANID;
			objDonThuoc.LOAI_DON_VI = "1";
			objDonThuoc.MA_XAC_THUC = "";
			objDonThuoc.MA_DON_VI = macsyt;
			objDonThuoc.MA_DINH_DANH = data_btkd[0].HSSK_MADD;
			objDonThuoc.HO_TEN_BN = $("#txtTENBENHNHAN").val();
			objDonThuoc.NGAY_SINH = data_btkd[0].NGAYSINH;
			objDonThuoc.GIOI_TINH = data_btkd[0].GIOITINHID;
			objDonThuoc.MA_THE = data_btkd[0].MA_BHYT;
			objDonThuoc.MA_BENH = data_btkd[0].MACHANDOAN;
			objDonThuoc.TEN_BENH = data_btkd[0].CHANDOAN;
			objDonThuoc.SD_TU_NGAY = data_btkd[0].NGAYMAUBENHPHAM_SUDUNG;
			objDonThuoc.SD_DEN_NGAY = data_btkd[0].SD_DEN_NGAY;
			objDonThuoc.HD_SD = data_btkd[0].LOIDANBACSI;
			objDonThuoc.SO_THANG = data_btkd[0].SLTHANG;
			objDonThuoc.NGAY_CAP = data_btkd[0].NGAYMAUBENHPHAM;
			objDonThuoc.MA_BAC_SY = data_btkd[0].MA_BAC_SI;
			objDonThuoc.TEN_BAC_SY = data_btkd[0].OFFICER_NAME;
			objDonThuoc.SO_GPHN = "";
			objDonThuoc.MA_KHOA = data_btkd[0].KHOAID;
			objDonThuoc.NHA_THUOC_ID = "";
			objDonThuoc.MA_NHA_THUOC = "";
			objDonThuoc.TEN_NHA_THUOC = "";
			objDonThuoc.TEN_DUOC_SY = "";
			objDonThuoc.NGAY_BAN = "";
			objDonThuoc.MA_TINH = data_btkd[0].MA_TINH;
			objDonThuoc.LOAI_DON_THUOC = "";
			objDonThuoc.HINH_THUC_DIEU_TRI = "";
			objDonThuoc.DOT_DUNG_THUOC = "";
			objDonThuoc.NGAY_TAI_KHAM = "";
			objDonThuoc.LUU_Y = "";
			objDonThuoc.HD_SD = "";
			// tao obj benh nhan
			objThongTinBN.MABN = data_btkd[0].MABENHNHAN;
			objThongTinBN.MATINH_KHAISINH = data_btkd[0].MATINH_KHAISINH;
			objThongTinBN.SOCMND = data_btkd[0].SOCMTND;
			objThongTinBN.NGAYCAP = data_btkd[0].NGAYCAPCMND;
			objThongTinBN.NOICAP = "";
			objThongTinBN.DIACHI_THUONGTRU = "";
			objThongTinBN.MATINH_THUONGTRU = "";
			objThongTinBN.MAHUYEN_THUONGTRU = "";
			objThongTinBN.MAXA_THUONGTRU = "";
			objThongTinBN.MATHONXOM_THUONGTRU = "";
			objThongTinBN.DIACHI_HIENTAI = data_btkd[0].DIACHI;
			objThongTinBN.MATINH_HIENTAI = data_btkd[0].MATINH_KHAISINH;
			objThongTinBN.MAHUYEN_HIENTAI = data_btkd[0].MAHUYEN_HIENTAI;
			objThongTinBN.MAXA_HIENTAI = data_btkd[0].MAXA_HIENTAI;
			objThongTinBN.MATHONXOM_HIENTAI = "";
			objThongTinBN.DIENTHOAI_CD = data_btkd[0].SDTBENHNHAN;
			objThongTinBN.DIENTHOAI_DD = "";
			objThongTinBN.EMAIL = "";
			objThongTinBN.TUOI_BENH_NHAN = "";
			objThongTinBN.CAN_NANG = "";
			objThongTinBN.THONG_TIN_NGUOI_GIAM_HO = "";
			var j = 0;
			var objChiTietThuoc = [];
			for (var i = 0; i < data_btkd.length; i++) {
				if (data_btkd[i].SODANGKY != '') {
					var objThuoc = new Object();
					objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
					objThuoc.STT = data_btkd[i].THUTU;
					if (_opts.option == '02D010' || _opts.option == '02D017')
						objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
					else
						objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
					objThuoc.CHI_TIET_ID_CLIENT = data_btkd[i].DICHVUKHAMBENHID;
					objThuoc.MA_NHOM = "";
					objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
					objThuoc.MA_THUOC = data_btkd[i].MA;
					objThuoc.TEN_THUOC = data_btkd[i].TEN;
					objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
					objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
					objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
					objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
					objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
					objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
					objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
					objThuoc.TYLE_TT = "100";
					objThuoc.MUC_HUONG = "0";
					objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
					objThuoc.T_NGUON_KHAC = "0";
					objThuoc.T_BNTT = "0";
					objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
					objThuoc.BIET_DUOC = data_btkd[i].TEN;
					objChiTietThuoc.push(objThuoc);
					j = j + 1;
				}
			}
			if (j == 0)
				return;
			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
			objBody.DON_THUOC = objDonThuoc;
			objBody.THONGTINBENHNHAN = objThongTinBN;
			objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
			objData.HEADER = objHeader;
			objData.BODY = objBody;
			objData.SECURITY = objSeccurity;
			objData.HEADER = objHeader;
			objSend.DATA = objData;
			var x2js = new X2JS();
			objSend = JSON.stringify(objSend);
			var obj = x2js.json2xml_str($.parseJSON(objSend));
			obj = obj.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
			obj = btoa(unescape(encodeURIComponent(obj)));// atob(obj);
			// gui lay ma dinh danh va MK
			var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuocOnline(BTKD_WS_URL, BTKD_WS_USER, BTKD_WS_PASS, obj);
			var rs = resultCongBTKD.split(';');
			if (rs[0] != 200) {
				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " + rs[1]);
				setTimeout(function() {}, 2000);
				// return 1;
			}
		}
	};
	function _daybtkdmbp(ret_maubenhpham_id, r_action) {
//		var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA',
//				'BTKD_DAY_BYT');
		if (PHARMA_KETNOI_CONG_BYT == 1) {
			// var sql_par = [];
			// sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
			// jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
			var objSend = new Object();
			var objData = new Object();
			var objHeader = new Object();
			var objBody = new Object();
			var objSeccurity = new Object();
			var objDonThuoc = new Object();
			var objThongTinBN = new Object();
			var objDSChiTietThuoc = new Object();
			var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_URL');
			var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_USER');
			var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BTKD_WS_PASS');
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", [ ret_maubenhpham_id ].join('$'));
//			if($("#hidDonThuocID").val()!='' ||$("#hidDonThuocID").val()!='0')
//				{
//				var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC34X001.18",
//						[ ret_maubenhpham_id ].join('$'));
//				}
//			else
			var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD", [ ret_maubenhpham_id ].join('$'));
			// tao header
			objHeader.SENDER_CODE = macsyt;
			objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
			objHeader.TRANSACTION_TYPE = "M0002";
			objHeader.TRANSACTION_NAME = "FTP";
			objHeader.TRANSACTION_ID = "";
			objHeader.PROVIDER = "12";
			if (r_action == "Upd")
				objHeader.ACTION_TYPE = "1";
			else
				objHeader.ACTION_TYPE = "0";
			// tao don thuoc
			objDonThuoc.DON_THUOC_ID = data_btkd[0].MAUBENHPHAMID;
			if (Number(data_btkd[0].BYT_DONTHUOCID) > 0) {
				objDonThuoc.MA_DON_THUOC = data_btkd[0].MA_DON_THUOC;
			} else
				objDonThuoc.MA_DON_THUOC = macsyt + "." + data_btkd[0].MA_DON_THUOC;
			objDonThuoc.MA_LUOT_KHAM = data_btkd[0].HOSOBENHANID;
			objDonThuoc.LOAI_DON_VI = "1";
			objDonThuoc.MA_XAC_THUC = "";
			objDonThuoc.MA_DON_VI = macsyt;
			objDonThuoc.MA_DINH_DANH = data_btkd[0].HSSK_MADD;
			objDonThuoc.HO_TEN_BN = data_btkd[0].TENBENHNHAN;
			objDonThuoc.NGAY_SINH = data_btkd[0].NGAYSINH;
			objDonThuoc.GIOI_TINH = data_btkd[0].GIOITINHID;
			objDonThuoc.MA_THE = data_btkd[0].MA_BHYT;
			objDonThuoc.MA_BENH = data_btkd[0].MACHANDOAN;
			objDonThuoc.TEN_BENH = data_btkd[0].CHANDOAN;
			objDonThuoc.SD_TU_NGAY = data_btkd[0].NGAYMAUBENHPHAM_SUDUNG;
			objDonThuoc.SD_DEN_NGAY = data_btkd[0].SD_DEN_NGAY;
			objDonThuoc.HD_SD = data_btkd[0].LOIDANBACSI;
			objDonThuoc.SO_THANG = data_btkd[0].SLTHANG;
			objDonThuoc.NGAY_CAP = data_btkd[0].NGAYMAUBENHPHAM;
			objDonThuoc.MA_BAC_SY = data_btkd[0].MA_BAC_SI;
			objDonThuoc.TEN_BAC_SY = data_btkd[0].OFFICER_NAME;
			objDonThuoc.SO_GPHN = "";
			objDonThuoc.MA_KHOA = data_btkd[0].KHOAID;
			objDonThuoc.NHA_THUOC_ID = "";
			objDonThuoc.MA_NHA_THUOC = "";
			objDonThuoc.TEN_NHA_THUOC = "";
			objDonThuoc.TEN_DUOC_SY = "";
			objDonThuoc.NGAY_BAN = "";
			objDonThuoc.MA_TINH = data_btkd[0].MA_TINH;
			// tao obj benh nhan
			objThongTinBN.MABN = data_btkd[0].MABENHNHAN;
			objThongTinBN.MATINH_KHAISINH = data_btkd[0].MATINH_KHAISINH;
			objThongTinBN.SOCMND = data_btkd[0].SOCMTND;
			objThongTinBN.NGAYCAP = data_btkd[0].NGAYCAPCMND;
			objThongTinBN.NOICAP = "";
			objThongTinBN.DIACHI_THUONGTRU = "";
			objThongTinBN.MATINH_THUONGTRU = "";
			objThongTinBN.MAHUYEN_THUONGTRU = "";
			objThongTinBN.MAXA_THUONGTRU = "";
			objThongTinBN.MATHONXOM_THUONGTRU = "";
			objThongTinBN.DIACHI_HIENTAI = data_btkd[0].DIACHI;
			objThongTinBN.MATINH_HIENTAI = data_btkd[0].MATINH_KHAISINH;
			objThongTinBN.MAHUYEN_HIENTAI = data_btkd[0].MAHUYEN_HIENTAI;
			objThongTinBN.MAXA_HIENTAI = data_btkd[0].MAXA_HIENTAI;
			objThongTinBN.MATHONXOM_HIENTAI = "";
			objThongTinBN.DIENTHOAI_CD = data_btkd[0].SDTBENHNHAN;
			objThongTinBN.DIENTHOAI_DD = "";
			objThongTinBN.EMAIL = "";
			var j = 0;
			var objChiTietThuoc = [];
			for (var i = 0; i < data_btkd.length; i++) {
				if (data_btkd[i].SODANGKY != '') {
					var objThuoc = new Object();
					objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
					objThuoc.STT = data_btkd[i].THUTU;
					if (_opts.option == '02D010' || _opts.option == '02D017')
						objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
					else
						objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
					objThuoc.CHI_TIET_ID_CLIENT = data_btkd[i].DICHVUKHAMBENHID;
					objThuoc.MA_NHOM = "";
					objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
					objThuoc.MA_THUOC = data_btkd[i].MA;
					objThuoc.TEN_THUOC = data_btkd[i].TEN;
					objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
					objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
					objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
					objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
					objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
					objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
					objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
					objThuoc.TYLE_TT = "100";
					objThuoc.MUC_HUONG = "0";
					objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
					objThuoc.T_NGUON_KHAC = "0";
					objThuoc.T_BNTT = "0";
					objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
					objChiTietThuoc.push(objThuoc);
					j = j + 1;
				}
			}
			if (j == 0)
				return;
			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
			objBody.DON_THUOC = objDonThuoc;
			objBody.THONGTINBENHNHAN = objThongTinBN;
			objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
			objData.HEADER = objHeader;
			objData.BODY = objBody;
			objData.SECURITY = objSeccurity;
			objData.HEADER = objHeader;
			objSend.DATA = objData;
			var x2js = new X2JS();
			objSend = JSON.stringify(objSend);
			var obj = x2js.json2xml_str($.parseJSON(objSend));
			obj = obj.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
			obj = btoa(unescape(encodeURIComponent(obj)));// atob(obj);
			// gui lay ma dinh danh va MK
			var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuoc(BTKD_WS_URL, BTKD_WS_USER, BTKD_WS_PASS, obj);
			var rs = resultCongBTKD.split(';');
			if (rs[0] != 0) {
				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " + rs[1]);
				setTimeout(function() {}, 2000);
				// return 1;
			}
		}
	}
	function _loadcontrolgrid() {
		$("#" + _gridId_BT).bind("CustomAction", function(e, act, rid) {
			DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko ?", function(flag) {
				if (flag) {
					$('#' + _gridId_BT).jqGrid('delRowData', rid);
					$('#txtSO_LUONG').attr('disabled', 'disabled');
					reloadCash();
					return true;
				}
			});
		});
		$("#" + _gridId_BT).bind("jqGridBeforeSelectRow", function(e, rowid, orgClickEvent) {
			//console.log("xxx="+$("#grdThuoc").jqGrid ('getCell', rowid, 7));
			var _ret = $("#" + _gridId_BT).jqGrid('getRowData', rowid);
			if ($("#" + _gridId_BT).jqGrid('getCell', rowid, 'SL_YC') != '' && (/^-?\d*(\.\d+)?$/.test($("#" + _gridId_BT).jqGrid('getCell', rowid, 'SL_YC')))) {
				oldValue = _ret.SL_YC;
			}
		});
		$("#" + _gridId_BT).bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
			var _ret = $("#" + _gridId_BT).jqGrid('getRowData', rowid);
			var slyc = _ret.SL_YC;
			var slkhdung = _ret.SL_KHADUNG;
			if (parseFloat(slyc) > parseFloat(slkhdung)) {
				DlgUtil.showMsg("Số lượng yêu cầu không được vượt quá số lượng khả dụng");
				$("#" + _gridId_BT).jqGrid('setCell', rowid, 'SL_YC', oldValue);
			} else {
				var thanhTien = slyc * _ret.DON_GIA;
				$("#" + _gridId_BT).jqGrid('setCell', rowid, 'THANH_TIEN', thanhTien);
				reloadCash();
			}
		});
	}
	function reloadCash() {
		var sumAll = 0;
//		
//		var allRowsInGrid = $("#" + _gridId_BT).jqGrid('getGridParam','data');
//		GridUtil.clearGridData("gridList_THUOC");
//        GridUtil.fetchGridData("gridList_THUOC", allRowsInGrid);
		var rowIds = $('#' + _gridId_BT).jqGrid('getDataIDs');
		for (var k = 0; k < rowIds.length; k++) {
			var rowData = $('#' + _gridId_BT).jqGrid('getRowData', rowIds[k]);
			var sumPrice = 0;
			sumPrice = parseFloat(rowData.THANH_TIEN);
			sumAll = (parseFloat(sumAll) + parseFloat(sumPrice)).toFixed(2);
		}
		val_m('txtTONGCONG', sumAll);
	}
	//L2PT-82624 start
	function checkRole(control, mode, sCode) {
		// check role theo mã màn hình
		var _parPQ = 'VPI01T012_banthuoc' + '$';
		if (typeof sCode != 'undefined') {
			_parPQ = sCode + '$';;
		}
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ);
		if (result.length == 0) {
			$('#' + control).hide();
			return false;
		}
		for (var i = 0; i < result.length; i++) {
			if (result[i].ELEMENT_ID == control) {
				if (result[i].ROLES == '1') {
					$('#' + result[i].ELEMENT_ID).show();
					return true;
				} else if (result[i].ROLES == '0' || result[i].ROLES == '') {
					$('#' + result[i].ELEMENT_ID).remove();
					return false;
				}
			}
		}
		// nếu không phân quyền thì không hiển thị
		if (mode == 1) {
			$('#' + control).remove();
			return false;
		}
		return true;
	}
	//L2PT-82624 end
}
