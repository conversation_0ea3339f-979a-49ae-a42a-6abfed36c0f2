/*
 * Nhập kết quả xét nghiệm
 * 
 * VietDA	04/08/2016		tạo mới
 *  
 */

function KetQuaXetNghiem2(opt){
	this.load = doLoad;

	var bnGioiTinh = "Nam";
	var bnTuoi = 0;
	var dsThem = [];
	var dsOld = [];
	var dsThemAll = [];
	var dichvuid ='';
	var amtinh ='';
	var duongtinh ='';
	var dichvukhambenhid ='';
	var vikhuanid ='';
	var vikhuanid_check  ='';
	var tenvikhuan ='';
	
	var _grd_DS_KS_HEADER = "VIKHUAN_ID,VIKHUAN_ID,80,0,f,l;" +
	"KHANGSINHID,KHANGSINHID,150,0,f,l;" +
	"KHANGSINHID_CHECK,KHANGSINHID_CHECK,150,0,f,l;" +
	"Mã kháng sinh,MA_KHANGSINH,150,0,f,l;" +
	"Tên kháng sinh,TEN_KHANGSINH,120,0,f,l;" +
	"S,S,120,0,f,l;" +
	"I,I,120,0,f,l;" +
	"R,R,120,0,f,l;" +
	"USER_NAME,USER_NAME,120,0,f,l;" +
	"NGAYTAO,NGAYTAO,120,0,f,l";
	var _grd_DS_KS_NAME = "grdDSKhangSinh";
	
	var _grd_DS_VK_HEADER = "Chọn,'SELECT_RADIO',50,0,f,l;" +
	"VIKHUAN_ID,VIKHUAN_ID,80,0,t,l;" +
	"vkidck,VIKHUANID_CHECK,50,0,t,l;" +
	"Tên vi khuẩn,TEN_VIKHUAN,120,0,f,l;" +
	"Ngày tạo,NGAYTAO,120,0,f,l;" +
	"Người tạo,USER_NAME,120,0,f,l";
	var _grd_DS_VK_NAME = "grdDSViKhuan";
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		$("#hdfIDMauBenhPham").val(opt._idmaubenhpham);
//		$("#hdfIDvikhuan").val(opt._vikhuanid);
		$("#hdfIDDichVuKB").val(opt._iddichvukb);
		$("#hdfIDDichVuID").val(opt._iddichvu);
		$("#hdfIDShowBtnEditTime").val(opt._showNutSuaNgayTraKQ);
		console.log("id mau benh pham="+opt._idmaubenhpham+", _showNutSuaNgayTraKQ="+opt._showNutSuaNgayTraKQ+", _show: "+opt._show);
		
		initControl();
		bindEvent();
		
		$("#txtNhanXet").focus();

		/* nhận dữ liệu từ form cha Danh sách bệnh phẩm */
		
//		if($("#hdfIDShowBtnEditTime").val()=='1'){
//			if(opt._show =='true'){
//				$("#btnEditTimePopUpX1").show();	
//				$("#btnEditTimePopUpX1").show();	
//			}else{
//				$("#btnEditTimePopUpX2").hide();	
//				$("#btnEditTimePopUpX2").hide();	
//			}
//		}else{
//			$("#btnEditTimePopUpX1").hide();	
//			$("#btnEditTimePopUpX2").hide();	
//		}
		loadThongTinBenhNhan();
//		loadDanhSachKetQua();
		$(parent.document).find(".jBox-closeButton").hide();		
	}

	function initControl(){
		$.extend($.jgrid.inlineEdit, {
			keys: true
		});
		loadViKhuanLaoM1();
		loadViKhuanLaoM2();
		if($("#hdfIDShowBtnEditTime").val()=='1' && opt._show =='true') {
			$("#btnEditTimePopUpX1").show();
		}
		
		$('#txtAMTINH').prop('readonly',true);
		$('#txtNHIEMTRUNG').prop('readonly',true);
		$('#txtMTB').prop('readonly',true);
		$('#txtNTM').prop('readonly',true);
	
		
		//vi khuẩn mẫu 2
		$('#txtNHAYINH').prop('readonly',true);
		$('#txtKHANGINH').prop('readonly',true);
		$('#txtNHAYRMP').prop('readonly',true);
		$('#txtKHANGRMP').prop('readonly',true);
		$('#txtNHAYSM').prop('readonly',true);
		$('#txtKHANGSM').prop('readonly',true);
		$('#txtNHAYEMB').prop('readonly',true);
		$('#txtKHANGEMB').prop('readonly',true);
		$('#txtNHAYPZA').prop('readonly',true);
		$('#txtKHANGPZA').prop('readonly',true);
		
		$('#txtNHAYMOX').prop('readonly',true);
		$('#txtKHANGMOX').prop('readonly',true);
		$('#txtNHAYAMK').prop('readonly',true);
		$('#txtKHANGAMK').prop('readonly',true);
		$('#txtNHAYKM').prop('readonly',true);
		$('#txtKHANGKM').prop('readonly',true);
		$('#txtNHAYCAP').prop('readonly',true);
		$('#txtKHANGCAP').prop('readonly',true);
		
		
		
		// ẩn hiện các nút xử lý
		if(type=="read"){
			$("#btnLuu1").attr('disabled','true');
			$("#btnTraKetQua2").attr('disabled','true');
			$("#btnLuu2").attr('disabled','true');
			$("#btnTraKetQua1").attr('disabled','true');
		} 
		else if(type=="update"){
			$("#btnLuu1").removeAttr('disabled');
			$("#btnLuu2").removeAttr('disabled');
			$("#btnInKQ1").removeAttr('disabled');
			$("#btnInKQ2").removeAttr('disabled');
			$("#btnTraKetQua2").removeAttr('disabled');
			$("#btnTraKetQua1").removeAttr('disabled');
		}
		if(user_group_id=="0" || user_group_id=="1") {
//			$("#btnTraKetQua").removeAttr('disabled');
//			$("#btnHuyKetQua").removeAttr('disabled');
		}
		else if(userRights=="TK") {
//			$("#btnTraKetQua").removeAttr('disabled');
//			$("#btnHuyKetQua").removeAttr('disabled');
		}
		else if(userRights=="KTV"){
//			$("#btnTraKetQua").attr('disabled','true');
//			$("#btnHuyKetQua").attr('disabled','true');
		}
		//nghiant 28082017
		 if(opt._rolePTH != "true" ){
			$("#btnTraKetQua1").attr('disabled','true');
			$("#btnTraKetQua2").attr('disabled','true');
//			$("#btnHuyKetQua").attr('disabled','true');
			$("#btnLuu").attr('disabled','true');
			$("#btnEditTimePopUpX").hide();	
			console.log("abcd=================================");
		}
		//end nghiant 28082017 		
	}
	
	function bindEvent(){		
		$('#cboMAU_MTB').change(function(){
			if($("#cboMAU_MTB").val() == "1" || $("#cboMAU_MTB").val() == "2" || $("#cboMAU_MTB").val() == "5"){
				$("#tblMau34").hide();
				$("#tblMau125").show();
				if($("#cboMAU_MTB").val() == "1" ){
					$(".divPZA").hide();
					$(".divINH").show();
					$(".divEMB").show();
					$(".divRMP").show();
					$(".divSM").show();
					$("#IDINH").html("INH </br>"+"(0.1 µg/ml)");
					$("#IDRMP").html("RMP </br>"+"(1.0 µg/ml)");
					$("#IDEMB").html("EMB </br>"+"(5.0 µg/ml)");
					$("#IDSM").html("SM </br>"+"(1.0 µg/ml)");
				}else if($("#cboMAU_MTB").val() == "2" ){
					$(".divPZA").hide();
					$(".divINH").show();
					$(".divEMB").show();
					$(".divRMP").show();
					$(".divSM").show();
					$("#IDINH").html("INH </br>"+"(0.2 µg/ml)");
					$("#IDRMP").html("RMP </br>"+"(40 µg/ml)");
					$("#IDEMB").html("EMB </br>"+"(2.0 µg/ml)");
					$("#IDSM").html("SM </br>"+"(4.0 µg/ml)");
				}else{
					$(".divPZA").show();
					$(".divINH").hide();
					$(".divEMB").hide();
					$(".divRMP").hide();
					$(".divSM").hide();
				}
			}else if($("#cboMAU_MTB").val() == "3" || $("#cboMAU_MTB").val() == "4"){
				$("#tblMau34").show();
				$("#tblMau125").hide();
				if($("#cboMAU_MTB").val() == "3" ){
					$("#IDMOX").html("MOX </br>"+"(0.25 µg/ml)");
					$("#IDAMK").html("AMK </br>"+"(1.0 µg/ml)");
					$("#IDKM").html("KM </br>"+"(2.5 µg/ml)");
					$("#IDCAP").html("CAP </br>"+"(2.5 µg/ml)");
				}else{
					$("#IDMOX").html("MOX </br>"+"(1.0 µg/ml)");
					$("#IDAMK").html("AMK </br>"+"(30 µg/ml)");
					$("#IDKM").html("KM </br>"+"(30 µg/ml)");
					$("#IDCAP").html("CAP </br>"+"(40 µg/ml)");
				}
				
			}else{
				$("#tblMau34").hide();
				$("#tblMau125").hide();
			}
		});
		$('#btnLuu1').on('click', function () {
			luuKetQua1();
			loadViKhuanLaoM1();
		});
		
		$('#btnLuu2').on('click', function () {
			luuKetQua2();
			loadViKhuanLaoM2();
		});
		
		$('#btnXoaText1').on('click', function () {
			$("#txtNGAYNHANMAU").val('');
			$("#txtNGAYCAY").val('');
			$("#txtAMTINH").val('');
			$("#txtNHIEMTRUNG").val('');
			$("#txtMTB").val('');
			$("#txtNTM").val('');
			$("#txtKQSOI").val('');
			$("#txtTESTDD").val('');
		});
		
		$('#btnXoaText2').on('click', function () {
			$("#txtNHAYINH").val('');
			$("#txtKHANGINH").val('');
			$("#txtNHAYRMP").val('');
			$("#txtKHANGRMP").val('');
			$("#txtNHAYSM").val('');
			$("#txtKHANGSM").val('');
			$("#txtNHAYEMB").val('');
			$("#txtKHANGEMB").val('');
			$("#txtNHAYPZA").val('');
			$("#txtKHANGPZA").val('');
			$("#txtKETLUAN").val('');
		});
		
		$("#btnHuy1").on("click",function(e){
			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
		});
		
		$("#btnHuy2").on("click",function(e){
			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
		});
		
		//click vào text box cua form 1 
		$('#txtAMTINH').on('click', function () {
			if($("#txtAMTINH").val().trim() == 'X'){
				$("#txtAMTINH").val('');
			}else{
				$("#txtAMTINH").val('X');
			}
		});
		$('#txtNHIEMTRUNG').on('click', function () {
			if($("#txtNHIEMTRUNG").val().trim() == 'X'){
				$("#txtNHIEMTRUNG").val('');
			}else{
				$("#txtNHIEMTRUNG").val('X');
			}
		});
		$('#txtMTB').on('click', function () {
			if($("#txtMTB").val().trim() == 'X'){
				$("#txtMTB").val('');
			}else{
				$("#txtMTB").val('X');
			}
		});
		$('#txtNTM').on('click', function () {
			if($("#txtNTM").val().trim() == 'X'){
				$("#txtNTM").val('');
			}else{
				$("#txtNTM").val('X');
			}
		});
		
		
		//click vao textbox của form thứ 2
		//click vào text box cua form 1 
		$('#txtNHAYINH').on('click', function () {
			if($("#txtNHAYINH").val().trim() == 'X'){
				$("#txtNHAYINH").val('');
			}else{
				$("#txtNHAYINH").val('X');
			}
		});
		$('#txtKHANGINH').on('click', function () {
			if($("#txtKHANGINH").val().trim() == 'X'){
				$("#txtKHANGINH").val('');
			}else{
				$("#txtKHANGINH").val('X');
			}
		});
		$('#txtNHAYRMP').on('click', function () {
			if($("#txtNHAYRMP").val().trim() == 'X'){
				$("#txtNHAYRMP").val('');
			}else{
				$("#txtNHAYRMP").val('X');
			}
		});
		$('#txtKHANGRMP').on('click', function () {
			if($("#txtKHANGRMP").val().trim() == 'X'){
				$("#txtKHANGRMP").val('');
			}else{
				$("#txtKHANGRMP").val('X');
			}
		});
		
		$('#txtNHAYSM').on('click', function () {
			if($("#txtNHAYSM").val().trim() == 'X'){
				$("#txtNHAYSM").val('');
			}else{
				$("#txtNHAYSM").val('X');
			}
		});
		$('#txtKHANGSM').on('click', function () {
			if($("#txtKHANGSM").val().trim() == 'X'){
				$("#txtKHANGSM").val('');
			}else{
				$("#txtKHANGSM").val('X');
			}
		});
		$('#txtNHAYEMB').on('click', function () {
			if($("#txtNHAYEMB").val().trim() == 'X'){
				$("#txtNHAYEMB").val('');
			}else{
				$("#txtNHAYEMB").val('X');
			}
		});
		$('#txtKHANGEMB').on('click', function () {
			if($("#txtKHANGEMB").val().trim() == 'X'){
				$("#txtKHANGEMB").val('');
			}else{
				$("#txtKHANGEMB").val('X');
			}
		});
		$('#txtNHAYPZA').on('click', function () {
			if($("#txtNHAYPZA").val().trim() == 'X'){
				$("#txtNHAYPZA").val('');
			}else{
				$("#txtNHAYPZA").val('X');
			}
		});
		$('#txtKHANGPZA').on('click', function () {
			if($("#txtKHANGPZA").val().trim() == 'X'){
				$("#txtKHANGPZA").val('');
			}else{
				$("#txtKHANGPZA").val('X');
			}
		});
		//10/12/2019 
		$('#txtNHAYMOX').on('click', function () {
			if($("#txtNHAYMOX").val().trim() == 'X'){
				$("#txtNHAYMOX").val('');
			}else{
				$("#txtNHAYMOX").val('X');
			}
		});
		$('#txtKHANGMOX').on('click', function () {
			if($("#txtKHANGMOX").val().trim() == 'X'){
				$("#txtKHANGMOX").val('');
			}else{
				$("#txtKHANGMOX").val('X');
			}
		});
		$('#txtNHAYAMK').on('click', function () {
			if($("#txtNHAYAMK").val().trim() == 'X'){
				$("#txtNHAYAMK").val('');
			}else{
				$("#txtNHAYAMK").val('X');
			}
		});
		$('#txtKHANGAMK').on('click', function () {
			if($("#txtKHANGAMK").val().trim() == 'X'){
				$("#txtKHANGAMK").val('');
			}else{
				$("#txtKHANGAMK").val('X');
			}
		});
		
		$('#txtNHAYKM').on('click', function () {
			if($("#txtNHAYKM").val().trim() == 'X'){
				$("#txtNHAYKM").val('');
			}else{
				$("#txtNHAYKM").val('X');
			}
		});
		$('#txtKHANGKM').on('click', function () {
			if($("#txtKHANGKM").val().trim() == 'X'){
				$("#txtKHANGKM").val('');
			}else{
				$("#txtKHANGKM").val('X');
			}
		});
		$('#txtNHAYCAP').on('click', function () {
			if($("#txtNHAYCAP").val().trim() == 'X'){
				$("#txtNHAYCAP").val('');
			}else{
				$("#txtNHAYCAP").val('X');
			}
		});
		$('#txtKHANGCAP').on('click', function () {
			if($("#txtKHANGCAP").val().trim() == 'X'){
				$("#txtKHANGCAP").val('');
			}else{
				$("#txtKHANGCAP").val('X');
			}
		});
		///
		
		
		$(".jBox-closeButton.jBox-noDrag").click(function(){
	      EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
	    });
		
		$('#btnTraKetQua1').on('click', function () {	
			
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
			$("#hdfIDMauBenhPham").val()+'$'+
			$("#hdfIDDichVuKB").val()
			;

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TKQ", param);
			
			if(rs == '1'){				
				DlgUtil.showMsg("Trả kết quả thành công",undefined,1000);
//				$("#hdfIDMauBenhPham").val("");
			} 
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể trả kết quả",undefined,3000);	
			} 
		});
		
		$('#btnHuyKetQua1').on('click', function () {	
			
			if(!rolePTH){//nghiant 05092017 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,1000);	
			}else{
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				$("#hdfIDMauBenhPham").val()+'$'+
				$("#hdfIDDichVuKB").val();//nghiant 01082017 //nghiant 14082017 (bo truyen tiepnhanid)
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HKQ", param);
				
				if(rs == '1'){
					DlgUtil.showMsg("Hủy kết quả thành công",undefined,1000);
					//$("#hdfIDMauBenhPham").val("");
				} 
				//nghiant 01082017
				else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,3000);	
				}
				else {
					DlgUtil.showMsg("Có lỗi xảy ra, không thể hủy kết quả",undefined,3000);	
				} 
			}
			
			
		});
		
		$('#btnTraKetQua2').on('click', function () {	
			
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
			$("#hdfIDMauBenhPham").val()+'$'+
			$("#hdfIDDichVuKB").val()
			;

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TKQ", param);
			
			if(rs == '1'){				
				DlgUtil.showMsg("Trả kết quả thành công",undefined,1000);
//				$("#hdfIDMauBenhPham").val("");
			} 
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể trả kết quả",undefined,3000);	
			} 
		});
		
		$('#btnHuyKetQua2').on('click', function () {	
			
			if(!rolePTH){//nghiant 05092017 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,1000);	
			}else{
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				$("#hdfIDMauBenhPham").val()+'$'+
				$("#hdfIDDichVuKB").val();//nghiant 01082017 //nghiant 14082017 (bo truyen tiepnhanid)
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HKQ", param);
				
				if(rs == '1'){
					DlgUtil.showMsg("Hủy kết quả thành công",undefined,1000);
					//$("#hdfIDMauBenhPham").val("");
				} 
				//nghiant 01082017
				else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,3000);	
				}
				else {
					DlgUtil.showMsg("Có lỗi xảy ra, không thể hủy kết quả",undefined,3000);	
				} 
			}
			
			
		});
		
		$('#btnInKQ1').on('click', function () {	
			inKetQua1();
		});	
		$('#btnInKQ2').on('click', function () {	
			inKetQua2();
		});	
	
		$( document ).ready(function() {
			var myDivElement = $( ".jBox-closeButton.jBox-noDrag" );
			console.log(myDivElement);
		    $("div").remove(".jBox-closeButton.jBox-noDrag");//L2PT-24557 
		});
	}
	
	// load thong tin benh nhan
	function loadThongTinBenhNhan(){
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
		var row = data_ar[0];
		
		$("#lblHoTen").text(row["TENBENHNHAN"]==null?"":row["TENBENHNHAN"]);
		bnGioiTinh = row["GIOITINH"];
		bnTuoi = row["TUOI"];
		$("#chbKhan").prop('checked', row["LOAIMAUBENHPHAM"]=="2"?true:false);		
		$("#lblDiaChi").text(row["GIOITINH"]+" - "+row["NGAYSINH"]+" - "+row["DIACHI"]);
		$("#lblSoPhieu").text(row["SOPHIEU"]==null?"":row["SOPHIEU"]);
		$("#lblNoiGui").text(row["TENPHONG"]==null?"":row["TENPHONG"]);
		$("#lblDoiTuong").text(row["DOITUONG"]==null?"":row["DOITUONG"]);
		$("#lblTGChiDinh").text(row["NGAYDICHVU"]==null?"":row["NGAYDICHVU"]);
		$("#lblTGKetQua").text(row["NGAYMAUBENHPHAM_HOANTHANH"]==null?"":row["NGAYMAUBENHPHAM_HOANTHANH"]);
		$("#lblBarcode").text(row["BARCODE"]==null?"":row["BARCODE"]);
		$("#lblBacSi").text(row["TENBACSI"]==null?"":row["TENBACSI"]);
		$("#lblChanDoan").text(row["CHANDOAN"]==null?"":row["CHANDOAN"]);
		$("#txtNhanXet").val(row["GHICHU"]==null?"":row["GHICHU"]);
		$("#lblNguoiThucHien").text(row["NGUOITRAKETQUA"]==null?"":row["NGUOITRAKETQUA"]);
	}
	
	
	function loadViKhuanLaoM1(){		   	
		var _sql_par=[$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()];
//		var _sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()]);
		var data_ba_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ6",_sql_par.join('$'));
    	if (data_ba_ar != null && data_ba_ar.length > 0) {
			var data = data_ba_ar[0];
			FormUtil.setObjectToForm("divDSVKLaoMau1", "", data);
			
			$("#lblMACHIDINH").text(data.MACHIDINH);
			$("#lblTENCHIDINH").text(data.TENCHIDINH);
			$("#hdfIDKQ1").val(data.ID);
			console.log("data.lblMACHIDINH: "+data.TENCHIDINH);
			
			//isLoadTabA=true;;
			
    	}
    	
	}
	
	function loadViKhuanLaoM2(){		   	
		var _sql_par1=[$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()];
//		var _sql_par1=RSUtil.buildParam("",[$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()]);
		var data_ba_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ7",_sql_par1.join('$'));
    	if (data_ba_ar != null && data_ba_ar.length > 0) {
			var data = data_ba_ar[0];
			FormUtil.setObjectToForm("divDSVKLaoMau2", "", data);
			var maumtb = data.MAU_MTB;
			if (maumtb==""){maumtb="0";}
			$('#cboMAU_MTB option[value='+maumtb+']').attr('selected','selected');
			$("#lblMACHIDINH2").text(data.MACHIDINH);
			$("#lblTENCHIDINH2").text(data.TENCHIDINH);
			$("#lblMACHIDINH3").text(data.MACHIDINH);
			$("#lblTENCHIDINH3").text(data.TENCHIDINH);
			$("#hdfIDKQ2").val(data.ID);
			$("#txtSOXN3").val(data.SOXN2);
			$("#txtKETLUAN2").val(data.KETLUAN);
			console.log("data.lblMACHIDINH2: "+data.TENCHIDINH);
			//AN HIEN LUC BAT LEN
			if(data.MAU_MTB == "1" || data.MAU_MTB == "2" || data.MAU_MTB == "5"){
				$("#tblMau34").hide();
				$("#tblMau125").show();
				if(data.MAU_MTB == "1" ){
					$(".divPZA").hide();
					$(".divINH").show();
					$(".divEMB").show();
					$(".divRMP").show();
					$(".divSM").show();
					$("#IDINH").html("INH </br>"+"(0.1 µg/ml)");
					$("#IDRMP").html("RMP </br>"+"(1.0 µg/ml)");
					$("#IDEMB").html("EMB </br>"+"(5.0 µg/ml)");
					$("#IDSM").html("SM </br>"+"(1.0 µg/ml)");
				}else if(data.MAU_MTB == "2" ){
					$(".divPZA").hide();
					$(".divINH").show();
					$(".divEMB").show();
					$(".divRMP").show();
					$(".divSM").show();
					$("#IDINH").html("INH </br>"+"(0.2 µg/ml)");
					$("#IDRMP").html("RMP </br>"+"(40 µg/ml)");
					$("#IDEMB").html("EMB </br>"+"(2.0 µg/ml)");
					$("#IDSM").html("SM </br>"+"(4.0 µg/ml)");
				}else{
					$(".divPZA").show();
					$(".divINH").hide();
					$(".divEMB").hide();
					$(".divRMP").hide();
					$(".divSM").hide();
				}
			}else if(data.MAU_MTB == "3" || data.MAU_MTB == "4"){
				$("#tblMau34").show();
				$("#tblMau125").hide();
				if(data.MAU_MTB == "3" ){
					$("#IDMOX").html("MOX </br>"+"(0.25 µg/ml)");
					$("#IDAMK").html("AMK </br>"+"(1.0 µg/ml)");
					$("#IDKM").html("KM </br>"+"(2.5 µg/ml)");
					$("#IDCAP").html("CAP </br>"+"(2.5 µg/ml)");
				}else{
					$("#IDMOX").html("MOX </br>"+"(1.0 µg/ml)");
					$("#IDAMK").html("AMK </br>"+"(30 µg/ml)");
					$("#IDKM").html("KM </br>"+"(30 µg/ml)");
					$("#IDCAP").html("CAP </br>"+"(40 µg/ml)");
				}
				
			}else{
				$("#tblMau34").hide();
				$("#tblMau125").hide();
			}
			//an hien
			
    	}
    	
	}
	
	// lưu kết quả khi người dùng nhập trên lưới
	function luuKetQua1(){
		objData = new Object();
		FormUtil.setFormToObject("divDSVKLaoMau1", "", objData);
		objData["ID"] =$("#hdfIDKQ1").val();
		objData["DICHVUKHAMBENHID"] =$("#hdfIDDichVuKB").val();
		objData["DICHVUID"] =$("#hdfIDDichVuID").val();
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLSVK.LKQVKL1", JSON.stringify(objData));
		
		if(fl==1){
			DlgUtil.showMsg("Thêm mới thành công !");
			loadViKhuanLaoM1()
		}
		else if(fl==2){
			DlgUtil.showMsg("Cập nhật thành công !");
			loadViKhuanLaoM1()
		}
	
		else{
			DlgUtil.showMsg("Không thành công !");
		}
		
	}
	
	function luuKetQua2(){
		//$("#IDMOX").html
		
		if($("#cboMAU_MTB").val() =="0"){
			DlgUtil.showMsg("Bạn cần chọn dịch vụ trước khi lưu kết quả !");
		}else{
			objData = new Object();
			FormUtil.setFormToObject("divDSVKLaoMau2", "", objData);
			objData["ID"] =$("#hdfIDKQ2").val();
			objData["DICHVUKHAMBENHID"] =$("#hdfIDDichVuKB").val();
			objData["DICHVUID"] =$("#hdfIDDichVuID").val();
			//if(objData["SOXN2"]=="" || objData["SOXN2"]==null){
				//objData["SOXN2"]=objData["SOXN3"];
			//}
			if($("#cboMAU_MTB").val() =="1" || $("#cboMAU_MTB").val() =="2" ||
					$("#cboMAU_MTB").val() =="5"){
				objData["SOXN2"]=objData["SOXN3"];
				objData["KETLUAN"]=objData["KETLUAN2"];
			}
			
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLSVK.LKQVKL2", JSON.stringify(objData));
			
			if(fl==1){
				DlgUtil.showMsg("Thêm mới thành công !");
				loadViKhuanLaoM2()
			}
			else if(fl==2){
				DlgUtil.showMsg("Cập nhật thành công !");
				loadViKhuanLaoM2()
			}
		
			else{
				DlgUtil.showMsg("Không thành công !");
			}
		}
		
		
	}
	
	function inKetQua1(){
		var par = [ 
			{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
			{name:'dichvukhambenhid',type:'String',value:$("#hdfIDDichVuKB").val()},
			{name:'dichvuid',type:'String',value:$("#hdfIDDichVuID").val()}
		];
		openReport('window', 'CLS_KQ_VK_LAO_LOAI1', 'pdf', par);
	}
	
	function inKetQua2(){
		if($("#cboMAU_MTB").val() =="0"){
			DlgUtil.showMsg("Bạn cần chọn dịch vụ trước khi lưu kết quả !");
		}else{
			var cot1,cot2,cot3,cot4;
			if($("#cboMAU_MTB").val() =="1" || $("#cboMAU_MTB").val() =="2"){
				cot1 = $("#IDINH").html();
				cot2 = $("#IDRMP").html();
				cot3 = $("#IDEMB").html();
				cot4 = $("#IDSM").html();
				
			}else if($("#cboMAU_MTB").val() =="3" || $("#cboMAU_MTB").val() =="4"){
				cot1 = $("#IDMOX").html();
				cot2 = $("#IDAMK").html();
				cot3 = $("#IDKM").html();
				cot4 = $("#IDCAP").html();
			}
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'dichvukhambenhid',type:'String',value:$("#hdfIDDichVuKB").val()},
				//{name:'cot1',type:'String',value:cot1},
				//{name:'cot2',type:'String',value:cot2},
				//{name:'cot3',type:'String',value:cot3},
				//{name:'cot4',type:'String',value:cot4},
				{name:'dichvuid',type:'String',value:$("#hdfIDDichVuID").val()}
			];
			
			//CLS_KQ_VK_LAO_MTB_PZA_LOAI2
			if($("#cboMAU_MTB").val() =="5"){
				openReport('window', 'CLS_KQ_VK_LAO_MTB_PZA_LOAI2', 'pdf', par);
			}else{
				openReport('window', 'CLS_KQ_VK_LAO_LOAI2', 'pdf', par);
			}
		}
		
	}
	
	//nghiant 26062017
	//nghiant 26062017
	$('#btnEditTimePopUpX').on('click', function () {			
		var url = "manager.jsp?func=../canlamsang/CLS02X009_SuaNgayTraKetQua&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+opt._tiepnhanid;
		EventUtil.setEvent("CLS02X009_Thoat",function(e){
			DlgUtil.close("dlgSuaThoiGianTraKQ2");	
//			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
//			reloadAllGrid();
		});
		var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaThoiGianTraKQ2","divNhapKetQua",url,{},"Sửa ngày trả kết quả",300,400);
		dlgPopup.open("dlgSuaThoiGianTraKQ2");
	});
	
	function setEnabled(_ena, _dis) {
		for (var i =0; i<_ena.length; i++) {
			$("#"+_ena[i]).attr('disabled', false);
		}
		for (var i =0; i<_dis.length; i++) {
			$("#"+_dis[i]).attr('disabled', true);
		}
	}
	
	
	
}
