/*
 Mã màn hình  : VPI02G001
 File mã nguồn : VPI02G001_duyetbhyt.js
 <PERSON><PERSON><PERSON> đích  : <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> hiểm y tế theo một viện phí: 
 -đ<PERSON><PERSON> dữ liệu về các dịch vụ bhyt vào 2 bảng kbh_bhyt và kbh_bhyt_dichvu
 -thay đổi trạng thái tiếp nhận = 2;
 Tham số vào :
 Ngư<PERSON><PERSON> lập tr<PERSON>nh	 <PERSON> cậ<PERSON> nhậ<PERSON> ch<PERSON>				 5/10/2016		comment
 */
function DuyetBH(_opts) {
	var _hospital_id;
	var flagLoading = false;
	var _tiepnhanid = -1;
	var _phieuthuid = -1;
	rowIdMarked = -1;
	var _sch = "";
	var _param = [];
	var _defVal = "1=1";
	var _undefVal = "1=0";
	var _gridSQL_BN = "VPI02G001.01.RG"; //L2PT-24615
	var _gridSQL_DV = "VPI01T001.02";
	var _gridSQL_PT = "VPI01T001.03.RG";// L2PT-32061
	var _sql_tinhtongtien = "VPI01T001.05";
	var _sql_dagiaodich = "VPI01T001.06";
	var _sql_thongtintiepnhan = "VPI01T001.11";
	var _sql_duyetbhyt = "VPI02G001.01.4210";
	//var _sql_duyetbhyt = "VPI02G001.01.130";
	var _sql_duyetbhyt_huy = "VPI02G001.02.4210";
	var _sql_dsdv_phieuthu = "VPI01T001.04";
	var _sql_get_xml_checkout = "VPI02G001.03";
	var _gridId_BN = "grdDSBenhNhan";
	var _gridId_DV = "grdDSDichVu";
	var _gridId_PT = "grdDSPhieuThu";
	//L2PT-21167 : thêm MA_GIAO_DICH, NGUOIDUYETBH,
	// L2PT-22267 thêm cột địa chỉ, người duyệt, tổng tiền, bhtt
	// L2PT-11789 thêm cột MA_LK
	// L2PT-93751 thêm ICON_BH_2, TRANGTHAITIEPNHAN130
	var _gridHeader_BN = " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;" + " ,ICON_BH_2,20,0,ns,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;" + "TTTN2,TRANGTHAITIEPNHAN4210,0,0,t,l;"
			+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN130,0,0,t,l;" + "STT,STT_DUYET,40,0,f,l;"
			+ "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Họ tên,TENBENHNHAN,130,0,f,l;" + "MA_LK,TIEPNHANID,70,0,f,l;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "GT,GIOITINH,35,0,f,l;"
			+ "Mã BHYT,MA_BHYT,100,0,f,l;" + "Địa chỉ,DIACHI,250,0,f,l;" + "Người duyệt KT,NGUOIDUYET,125,0,f,l;" + "Tổng tiền,T_TONGCHI,75,0,f,r;" + "BHTT,T_BHTT,75,0,f,r;"
			+ "Mã viện phí,MATIEPNHAN,85,0,f,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;" + "Mã giao dịch,MA_GIAO_DICH,125,0,f,l;" + "Người duyệt,NGUOIDUYETBH,125,0,f,l;"
			+ "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "STT,STT,50,0,t,l;" + "Xử trí,XUTRI,100,0,f,l;" + "Khoa,KHOA,100,0,f,l;"
			+ "Phòng,PHONG,100,0,f,l";
	// L2PT-3622 them ICON1, LOAIDUYETBHYT, LOAIBO
	var _gridHeader_DV = " ,ICON1,20,0,ns,l;" + "id,DICHVUKHAMBENHID,0,0,t,l;" + "Khoa,KHOA,0,0,t,l;" + "Đồng chi trả,TIEN_BHYT_BNTT,0,0,t,l;" + "Loại đối tượng id,LOAIDOITUONG,0,0,t,l;"
			+ "Mẫu bệnh phẩm id,MAUBENHPHAMID,0,0,t,l;" + "Loại nhóm MBP,LOAINHOMMAUBENHPHAM,0,0,t,l;" + "Trạng thái mẫu bệnh phẩm,TRANGTHAIMAUBENHPHAM,0,0,t,l;" + "Khoản mục ID,KHOANMUCID,0,0,t,l;"
			+ "Mã khoản mục,MAKHOANMUC,0,0,t,l;" + "Tên khoản mục,TENKHOANMUC,0,0,t,l;" + "DTBNID,DOITUONGBENHNHANID,100,0,t,l;" + "Nhóm thanh toán,NHOMTHANHTOAN,120,0,t,l;"
			+ "Loại thu tiền,LOAI_DOITUONG,120,0,t,l;" + "Nhóm bhyt,NHOM_MABHYT,0,0,t,l;" + "Đã thu tiền,DATHUTIEN,90,0,t,l;" + "Tên dịch vụ,TENDICHVU,175,0,f,l;" + "SL,SOLUONG,30,0,f,l;"
			+ "Giá tiền,TIENDICHVU,80,number,f,r;" + "Giá BH,DONGIA_BHYT,70,number!3,f,r;" + "Thành tiền,THANHTIEN,80,number,t,r;" + "BHYT trả,TIEN_BHYT_TRA,75,number,f,r;"
			+ "BN trả,THUCTHU,75,number,f,r;" + "Trạng thái,LOAIDUYETBHYT,0,0,t,l;" + "Trạng thái,LOAIBO,60,0,f,l;" + "Tỷ lệ %,TYLE_BHYT_TRA,60,0,f,r;" + "Miễn giảm,TIEN_MIENGIAM,70,number,f,r;"
			+ "Tỷ lệ dịch vụ,TYLE_DV,0,0,t,l;" + "Tỷ lệ thẻ - dịch vụ,TYLE,0,0,t,l;" + "Tỷ lệ miễn giảm - dịch vụ,TYLEMIENGIAM,0,0,t,l;" + "Vật tư 04,VATTU04,0,0,t,l";
	var _gridHeader_PT = " ,ICON,20,0,ns,l;" + "id,PHIEUTHUID,100,0,t,l,0;" + "Đã hủy phiếu,DAHUYPHIEU,90,0,t,l,0;" + "Tiếp nhận id,TIEPNHANID,100,0,t,l,0;" + "Mã phiếu thu,MAPHIEUTHU,80,0,f,l,0;"
			+ "Số tiền,DATRA,90,number,f,r,0;" + "Loại phiếu thu,LOAIPHIEUTHU,90,0,t,l,0;" + "Loại phiếu thu id,LOAIPHIEUTHUID,90,0,t,l,0;" + "Loại phiếu,LOAIPHIEUTHUID_2,90,0,f,l,0;"
			+ "Ngày thanh toán,NGAYTHU,120,0,f,c,0;" + "Lý do hủy,LYDOHUYPHIEU,143,0,f,l,0";
	var that = this;
	this.load = doLoad;
	var _options = $.extend({}, _opts);
	var uuid = _options._uuid;
	_param = _options._param;
	_hospital_id = _param[0];
	_hospital_code = _param[5];
	_dept_id = _param[6];
	_user_id = _param[1];
	_sch = _param[4];
	_phong_id = _param[7];
	_khoa_id = _param[6];
	var _khoa = "";
	var arr_row = [];
	var VP_GUI_DULIEU_KHIDUYET = 0;
	var VPI_GUI_BH = 0;
	var VPI_KTKQ_GUI = 0;
	var VPI_KIEMTRA_TYLE = 0;
	var HIS_TIMKIEM_VIENPHI = 0;
	var VPI_CBO_TRANGTHAI = 0;
	var _hideInPhoi = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HIDE_INPHOI_NOPTHEM') == '1') ? true : false;//HaNv_28042020 - L2PT-19278
	var fConfig = new Object(); // L2PT-43188
	var _mode_scba = 1; // L2PT-27537
	var _vp_giaodich = new Object(); // L2PT-48479
	var _vpData = new Object(); // L2PT-51016
	var _ssImportId = "";
	function doLoad(_hosp_id) {
		_hospital_id = _hosp_id;
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		_initControl();
		_bindEvent();
		$("#txtTU").focus();
	}
	function _initControl() {
		// L2PT-43188 start
		// L2PT-51016 : VPI_CBDBHYT_KHACNGAY
		// L2PT-55208 : VPI_BHXH_KT_NGAYGUI
		// L2PT-69331 : VPI_GDKT_KHI_GDBH
		// L2PT-111074 : VPI_DS_CTLHIEN_DBH
		var str_ch = "VPI_GUI_MA_LOAI_KCB;VPI_CBDBHYT_KHACNGAY;VPI_BHXH_KT_NGAYGUI;VPI_GDKT_KHI_GDBH;VPI_BH_917_4210;VPI_DS_CTLHIEN_DBH"; // L2PT-94422
		str_ch += ';VPI_SOHS_1LANDUYET' // L2PT-123881
		var arrFConfig = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		if (Array.isArray(arrFConfig) && arrFConfig.length > 0) {
			fConfig = arrFConfig[0];
		}
		// L2PT-43188 end
		// L2PT-21340 start
		// L2PT-19490 start
		/*
		var VPI_BH_CBO_NGAY = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_BH_CBO_NGAY');
		if (VPI_BH_CBO_NGAY == '0') {
			$("#cboLOCTHEO option[value=" + 4 + "]").hide();
			$("#cboLOCTHEO option[value=" + 2 + "]").hide();
			$("#cboLOCTHEO option[value=" + 3 + "]").hide();
			$("#cboLOCTHEO option[value=" + 5 + "]").hide();
		}
		*/
		// L2PT-19490 end
		configContextMenu('cboLOCTHEO', 'VPI_BH_CBO_NGAY', 'COMBOBOX');
		$("#cboLOCTHEO").val(2);
		// L2PT-21340 end
		hienIdsTuCauHinh('VPI_DS_CTLHIEN_DBH', 'VIS', 'SHOW');
		$('#txtTU').val(jsonrpc.AjaxJson.ajaxCALL_SP_S('GETSYSDATE', 'DD/MM/YYYY' + '$' + 31) + " 00:00:00");
		$('#txtDEN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY') + " 23:59:59");
		// load combobox loại KCB
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("LOAIVIENPHI", '$');
		var str = '';
		var ds_loaivienphi = "";
		var loaivienphi;
		var tatca_vp = false;
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				loaivienphi = result[i].LOAIVIENPHIID;
				if (loaivienphi == 100) {
					tatca_vp = true;
				} else {
					ds_loaivienphi += loaivienphi + ",";
				}
				str = str + "<option value=" + result[i].LOAIVIENPHIID + ">" + result[i].LOAIVIENPHI + "</option>";
			}
			if (!tatca_vp && ds_loaivienphi.length > 0) {
				ds_loaivienphi = ds_loaivienphi.slice(0, -1);
				str = "<option value=" + ds_loaivienphi + ">" + "-- Chọn --" + "</option>" + str;
			}
		}
		$('#cboDOITUONG').html(str);
		// load combobox Loại thẻ
		ComboUtil.getComboTag("cboLoaiThe", 'VPI.LOAITHE', [], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', '');
		// load combobox Khoa KCB
		ComboUtil.getComboTag("cboKhoa", 'VPI.KHOA.KCB', [], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', '');
		// L2PT-24721 start
		ComboUtil.getComboTag("cboPhong", 'VPI.PHONG.KCB', [ {
			name : "[0]",
			value : -1
		} ], "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', '');
		// L2PT-24721 end
		// check quyền gỡ duyệt
		right = getParameterByName('right', window.location.search.substring(1));
		if (right == 0) {
			$("#btnGoDuyet").remove();
		}
		var _configArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.LAY.CAUHINH", '$');
		if (_configArr && _configArr.length > 0) {
			_config = _configArr[0];
			VP_GUI_DULIEU_KHIDUYET = _config.VP_GUI_DULIEU_KHIDUYET;
			VPI_GUI_BH = _config.VPI_GUI_BH;
			HIS_TIMKIEM_VIENPHI = _config.HIS_TIMKIEM_VIENPHI;
			VPI_KIEMTRA_TYLE = _config.VPI_KIEMTRA_TYLE;
			VPI_KTKQ_GUI = _config.VPI_KTKQ_GUI;
			VPI_CBO_TRANGTHAI = _config.VPI_CBO_TRANGTHAI;
		}
		// cau hinh combo trang thai
		if (VPI_CBO_TRANGTHAI && VPI_CBO_TRANGTHAI != 0) {
			var arr_ch_menu_pt = JSON.parse('[' + VPI_CBO_TRANGTHAI + ']');
			var li = 1;
			$('#cboTRANGTHAI option').each(function() {
				if (arr_ch_menu_pt.indexOf(li) == -1) {
					$(this).remove();
				}
				li++;
			});
		}
		// L2PT-20246 start
		var VPI_GRID_DUYETBHYT4210 = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_GRID_DUYETBHYT4210');
		// L2PT-85581 start
		var VPI_GRIDHEADER_DBHYT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', 'VPI_GRID_DUYETBHYT4210');
		if (VPI_GRIDHEADER_DBHYT && VPI_GRIDHEADER_DBHYT.length > 0 && VPI_GRIDHEADER_DBHYT != 0) {
			_gridHeader_BN = VPI_GRIDHEADER_DBHYT;
		} else
		// L2PT-85581 end
		if (VPI_GRID_DUYETBHYT4210 == 1) {
			// L2PT-93751 thêm ICON_BH_2, TRANGTHAITIEPNHAN130
			_gridHeader_BN = " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;" + " ,ICON_BH_2,20,0,ns,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN4210,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN130,0,0,t,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;"
					+ "STT,STT,50,0,f,l;" + "MA_LK,TIEPNHANID,70,0,f,l;" + "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;" + "Họ tên,TENBENHNHAN,150,0,f,l!s;"
					+ "Mã BHYT,MA_BHYT,100,0,f,l;" + "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Ngày thanh toán,NGAYTHANHTOAN,120,0,f,l;"
					+ "Ngày quyết toán,NGAYQUYETTOAN,120,0,f,l;" + "Ngày duyệt BHYT,DUYET_NGAYDUYET,120,0,f,l;" + "Ngày gửi cổng BHXH ,NGAY_GUI,120,0,f,l;" + "Xử trí,XUTRI,100,0,f,l;"
					+ "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l;" + "Người duyệt kế toán,NGUOIDUYET,125,0,f,l;" + "Người duyệt BHYT,NGUOIDUYETBH,125,0,f,l;" + "Tổng tiền,T_TONGCHI,75,0,f,r;"
					+ "BHYT thanh toán,T_BHTT,75,0,f,r;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;" + "Mã giao dịch,MA_GIAO_DICH,125,0,f,l;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "GT,GIOITINH,35,0,f,l;"
					+ "Địa chỉ,DIACHI,250,0,f,l";
		}
		// L2PT-20246 end
		else if (HIS_TIMKIEM_VIENPHI == 1) {
			// _focus_element = "#gs_MAHOSOBENHAN";
			//L2PT-21167 : thêm MA_GIAO_DICH, NGUOIDUYETBH,
			// L2PT-22267 thêm cột địa chỉ, người duyệt, tổng tiền, bhtt
			// L2PT-11789 thêm cột MA_LK
			// L2PT-23950 thêm cột DUYET_NGAYDUYET
			// L2PT-93751 thêm ICON_BH_2, TRANGTHAITIEPNHAN130
			_gridHeader_BN = " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;" + " ,ICON_BH_2,20,0,ns,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN4210,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN130,0,0,t,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;"
					+ "Họ tên,TENBENHNHAN,150,0,f,l!s;" + "MA_LK,TIEPNHANID,70,0,f,l;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "GT,GIOITINH,35,0,f,l;" + "Mã BHYT,MA_BHYT,100,0,f,l;"
					+ "Địa chỉ,DIACHI,250,0,f,l;" + "Người duyệt KT,NGUOIDUYET,125,0,f,l;" + "Tổng tiền,T_TONGCHI,75,0,f,r;" + "BHTT,T_BHTT,75,0,f,r;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;"
					+ "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Mã giao dịch,MA_GIAO_DICH,125,0,f,l;" + "Người duyệt,NGUOIDUYETBH,125,0,f,l;" + "Ngày duyệt BHYT,DUYET_NGAYDUYET,120,0,f,l;"
					+ "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;" + "STT,STT,50,0,t,l;" + "Xử trí,XUTRI,100,0,f,l;"
					+ "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l";
		} else if (HIS_TIMKIEM_VIENPHI == 2) {
			//L2PT-21167 : thêm MA_GIAO_DICH, NGUOIDUYETBH,
			// L2PT-22267 thêm cột địa chỉ, người duyệt, tổng tiền, bhtt
			// _focus_element = "#gs_MAVIENPHI";
			// L2PT-11789 thêm cột MA_LK
			// L2PT-93751 thêm ICON_BH_2, TRANGTHAITIEPNHAN130
			_gridHeader_BN = " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;" + " ,ICON_BH_2,20,0,ns,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN4210,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN130,0,0,t,l;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;"
					+ "Họ tên,TENBENHNHAN,150,0,f,l;" + "MA_LK,TIEPNHANID,70,0,f,l;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "GT,GIOITINH,35,0,f,l;" + "Mã BHYT,MA_BHYT,100,0,f,l;"
					+ "Địa chỉ,DIACHI,250,0,f,l;" + "Người duyệt KT,NGUOIDUYET,125,0,f,l;" + "Tổng tiền,T_TONGCHI,75,0,f,r;" + "BHTT,T_BHTT,75,0,f,r;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;"
					+ "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Mã giao dịch,MA_GIAO_DICH,125,0,f,l;" + "Người duyệt,NGUOIDUYETBH,125,0,f,l;" + "Vào viện,NGAYTIEPNHAN,120,0,f,l;"
					+ "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;" + "STT,STT,50,0,t,l;" + "Xử trí,XUTRI,100,0,f,l;" + "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l";
		}
		// L2PT-29173 start
		else if (HIS_TIMKIEM_VIENPHI == 3) {
			//L2PT-21167 : thêm MA_GIAO_DICH, NGUOIDUYETBH,
			// L2PT-22267 thêm cột địa chỉ, người duyệt, tổng tiền, bhtt
			// _focus_element = "#gs_MAVIENPHI";
			// L2PT-10107 :thêm cột stt duyệt bhyt
			// L2PT-11789 thêm cột MA_LK
			// L2PT-93751 thêm ICON_BH_2, TRANGTHAITIEPNHAN130
			_gridHeader_BN = " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;" + " ,ICON_BH_2,20,0,ns,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN4210,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN130,0,0,t,l;" + "STT,STT_DUYET,40,0,f,l;"
					+ "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;" + "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Họ tên,TENBENHNHAN,150,0,f,l!s;" + "MA_LK,TIEPNHANID,70,0,f,l;" + "Ngày sinh,NGAYSINH,70,0,f,l;"
					+ "GT,GIOITINH,35,0,f,l;" + "Mã BHYT,MA_BHYT,100,0,f,l;" + "Địa chỉ,DIACHI,250,0,f,l;" + "Người duyệt KT,NGUOIDUYET,125,0,f,l;" + "Tổng tiền,T_TONGCHI,75,0,f,r;"
					+ "BHTT,T_BHTT,75,0,f,r;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;" + "Mã giao dịch,MA_GIAO_DICH,125,0,f,l;" + "Người duyệt,NGUOIDUYETBH,125,0,f,l;"
					+ "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;" + "STT,STT,50,0,t,l;" + "Xử trí,XUTRI,100,0,f,l;"
					+ "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l";
		}
		// L2PT-27537 start
		_gridHeader_BN += ";sửa chữa BA,SUACHUA_BA,0,0,t,l";
		// L2PT-29173 end
		GridUtil.init(_gridId_BN, "100%", "255", "", true, _gridHeader_BN, false, {
			rowNum : 100,
			rowList : [ 20, 50, 100, 200, 300, 400, 500, 5000 ]
		// L2PT-20828
		});
		GridUtil.addExcelButton(_gridId_BN, 'Xuất excel', true); //L2PT-29173
		var _group = {
			groupField : [ 'LOAI_DOITUONG', "NHOM_MABHYT" ],
			groupColumnShow : [ false, false ],
			groupText : [ '<b>{0}</b>' ]
		};
		GridUtil.initGroup(_gridId_DV, "100%", "156", "", true, _group, _gridHeader_DV, false, {
			rowNum : 999999,
			rowList : [ 999999 ]
		});
		$("#" + _gridId_DV)[0].toggleToolbar();
		// L2PT-111074 : chinh sua giao dien
		GridUtil.init(_gridId_PT, "100%", "200", "", false, _gridHeader_PT, false, { // BVTM-5077
			rowNum : 100,
			rowList : [ 100, 200, 300 ]
		});
		$("#" + _gridId_PT)[0].toggleToolbar();
		initPopupLOIDUYET();
		initPopupCHUYENQT();
		loadGridDataBN();
		// L2PT-23459
		// cau hinh combobox trang thai
		var VPI_CBO_TRANGTHAIBH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_CBO_TRANGTHAIBH');
		var VPI_CBO_TRANGTHAIBH = FormUtil.unescape(VPI_CBO_TRANGTHAIBH);
		if (VPI_CBO_TRANGTHAIBH && VPI_CBO_TRANGTHAIBH != 0) {
			var arr_ch_trangthaibh = JSON.parse('[' + VPI_CBO_TRANGTHAIBH + ']');
			$('#cboTRANGTHAI  > option').each(function() {
				if (arr_ch_trangthaibh.indexOf(this.value) == -1) {
					$(this).remove();
				}
			});
		}
		// L2PT-23459 end
		// L2PT-24615 start
		var VPI_THUVP_CBOXUTRI = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THUVP_CBOXUTRI');
		if (VPI_THUVP_CBOXUTRI == 1) {
			$("#divXUTRI").show();
		}
		// L2PT-24615 end
		// L2PT-3622 start
		var VPI_QUYEN_LOAIBODVXML = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_QUYEN_LOAIBODVXML');
		if (VPI_QUYEN_LOAIBODVXML != '0') {
			$("#divAnDVLoaiBo").show();
		}
		// L2PT-3622 end
		// L2PT-19976 start
		var VPI_INPHOI_BO_THUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_INPHOI_BO_THUOC');
		if (VPI_INPHOI_BO_THUOC != '1') {
			$("#btnInBH").hide();
		}
		// L2PT-19976 end
	}
	function initPopupLOIDUYET() {
		var dlgLOI = DlgUtil.buildPopup("dlgLOI", "dlgLOIDUYET", "Lỗi duyệt bảo hiểm", 800, 500);
		var btnClose = $('#btnDONGLOI');
		textArea = $('#txtNOIDUNGLOI');
		btnClose.click(function() {
			textArea.val("");
			dlgLOI.close();
		});
	}
	function initPopupCHUYENQT() {
		var dlgNQT = DlgUtil.buildPopup("dlgChonNgay", "dlgChonNgayQT", "Chọn ngày quyết toán", 330, 80, {
			"zIndex" : 998
		});
		$('#btn_NQT_CLOSE').click(function() {
			$('#txtNQT').val("");
			dlgNQT.close();
		});
		$('#btn_NQT_OK').click(function() {
			//BVTM-5077 start
			DlgUtil.showConfirm("Xác nhận chuyển ngày quyết toán ?", function(flag) {
				if (flag) {
					var validator = new DataValidator("dlgChonNgayQT");
					var valid = validator.validateForm();
					if (!valid) {
						return false;
					}
					var ngay = $('#txtNQT').val();
					var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.CHUYEN.QT', _dstn + "$" + ngay);
					DlgUtil.showMsg(ret);
					$('#txtNQT').val("");
					dlgNQT.close();
					loadGridDataBN();
				}
			});
			//BVTM-5077 end
		});
	}
	function _bindEvent() {
		var view = 86;
		$(document).unbind('keydown').keydown(function(e) {
			if (e.altKey) {
				if (e.keyCode == view) {
					DlgUtil.open("dlgLOI");
				}
			}
		});
		$("#btnTimKiem").on("click", function(e) {
			if (flagLoading)
				return;
			var validator = new DataValidator("divSearch");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			var _tungay = stringToDateTime($("#txtTU").val());
			var _denngay = stringToDateTime($("#txtDEN").val());
			if (_tungay && _denngay) {
				if (_tungay > _denngay) {
					onFocus([ "txtTU", "txtDEN" ]);
					return false;
				}
			}
			arr_row = [];
			loadGridDataBN();
		});
		// GridUtil.setGridParam(_gridId_BN,{
		// onSelectRow: function(id) {
		// if(flagLoading)
		// return;
		// if (id) {
		// var _ret = $("#"+_gridId_BN).jqGrid('getRowData', id);
		// _tiepnhanid = _ret.TIEPNHANID;
		// layTTTiepNhan(_tiepnhanid);
		// GridUtil.unmarkRow(_gridId_BN,rowIdMarked);
		// GridUtil.markRow(_gridId_BN,id);
		// rowIdMarked = id;
		// setEnabled(['btnIn'],[]);
		// }
		// },
		// gridComplete: function(id){}
		//		
		// });
		GridUtil.setGridParam(_gridId_BN, {
			// L2PT-3622 start
			beforeSelectRow : function(rowid, e) {
				if (flagLoading) {
					return false; // not allow select the row
				} else {
					return true; // allow select the row
				}
			},
			// L2PT-3622 end
			onSelectRow : function(id) {
				if (flagLoading) {
					$("#" + _gridId_BN).jqGrid('setSelection', id, false);
					return;
				}
				arr_row = [];
				GridUtil.unmarkAll(_gridId_BN);
				if (id) {
					var ids = $("#" + _gridId_BN).jqGrid('getGridParam', 'selarrrow');
					for (var i = 0; i < ids.length; i++) {
						GridUtil.markRow(_gridId_BN, ids[i]);
						var _ret = $("#" + _gridId_BN).jqGrid('getRowData', ids[i]);
						arr_row.push(_ret.TIEPNHANID);
					}
					// L2PT-55208 fix th khong chon bn nao
					if (arr_row.length > 0) {
						_tiepnhanid = arr_row[arr_row.length - 1];
						layTTTiepNhan(_tiepnhanid);
					}
				}
			},
			onSelectAll : function(ids, status) {
				if (flagLoading) {
					return;
				} else {
					arr_row = [];
					GridUtil.unmarkAll(_gridId_BN);
					if (status) {
						for (var i = 0; i < ids.length; i++) {
							GridUtil.markRow(_gridId_BN, ids[i]);
							var _ret = $("#" + _gridId_BN).jqGrid('getRowData', ids[i]);
							arr_row.push(_ret.TIEPNHANID);
						}
						_tiepnhanid = arr_row[arr_row.length - 1];
						layTTTiepNhan(_tiepnhanid);
					}
				}
			},
			gridComplete : function(id) {}
		});
		// xu ly su kien chon xem Lịch sử TT
		$('#rLichSuThanhToan').on("click", function(e) {
			var myVar = new Object();
			myVar.tiepnhanid = _tiepnhanid;
			var dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuTT", "dlgLichSuThanhToan", "manager.jsp?func=../vienphi/VPI01T004_lichsuthanhtoan", myVar, "Lịch sử thanh toán", 1250, 600);
			DlgUtil.open("dlgLichSuTT");
		});
		// xu ly su kien chon Xac nhan bao hiem
		$('#rXacNhanBH').on("click", function(e) {
			_dstn = arr_row.join(",");
			if (!_dstn || _dstn.length == 0) {
				DlgUtil.showMsg("Chưa chọn hồ sơ");
				return;
			}
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.XACNHAN.BH', _dstn + "$" + 1);
			if (fl != 1) {
				DlgUtil.showMsg(fl);
			} else {
				if (_benhnhan.LOAITIEPNHANID == 1 && arr_row.length == 1) {
					// in phieu kham benh vao vien va giay giu
					// the bhyt
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : _benhnhan.KHAMBENHID
					} ];
					openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_951", "pdf", par);
				}
				//$("#btnIn").click();
			}
			loadGridDataBN();
		});
		// xu ly su kien chon Go xac nhan bao hiem
		$('#rGoXacNhanBH').on("click", function(e) {
			_dstn = arr_row.join(",");
			if (!_dstn || _dstn.length == 0) {
				DlgUtil.showMsg("Chưa chọn hồ sơ");
				return;
			}
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.XACNHAN.BH', _dstn + "$" + 0);
			if (fl != 1)
				DlgUtil.showMsg(fl);
			loadGridDataBN();
		});
		// xu ly su kien chon chuyen ngay quyet toan
		$('#rChuyenQuyetToan').on("click", function(e) {
			_dstn = arr_row.join(",");
			if (!_dstn || _dstn.length == 0) {
				DlgUtil.showMsg("Chưa chọn hồ sơ");
				return;
			}
			var ids = $("#" + _gridId_BN).jqGrid('getGridParam', 'selarrrow');
			for (var i = 0; i < ids.length; i++) {
				var trangthaitiepnhan = $("#" + _gridId_BN).jqGrid('getCell', ids[i], 'TRANGTHAITIEPNHAN');
				if (trangthaitiepnhan == 2) {
					DlgUtil.showMsg("Có hồ sơ đã duyệt bảo hiểm, không thể chuyển quyết toán");
					return;
				}
			}
//			var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
//			$('#txtNQT').val(sysdate);
			DlgUtil.open("dlgChonNgay");
		});
		// L2PT-27537 start : đánh dấu sửa chữa BA
		$('#rXacNhanSuaChua').on("click", function(e) {
			if (!arr_row || arr_row.length == 0) {
				DlgUtil.showMsg("Chưa chọn hồ sơ");
				return;
			}
			if (arr_row.length > 1) {
				DlgUtil.showMsg("Chỉ được đánh dấu/ gỡ bỏ 1 hồ sơ");
				return;
			}
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.XACNHAN.SUACHUA', JSON.stringify({
				MODE : _mode_scba + "",
				TIEPNHANID : arr_row[0]
			}) + "$" + _mode_scba);
			if (fl != 1)
				DlgUtil.showMsg(fl);
			loadGridDataBN();
		});
		// L2PT-27537 end
		// xu ly su kien chon xem Lịch sử điều trị
		$('#rLichSuDieuTri').on("click", function(e) {
			EventUtil.setEvent("assignSevice_cancel", function(e) {
				console.log(e.msg);
				DlgUtil.close("dlgLSDT");
			});
			var myVar = new Object();
			myVar.benhnhanId = _benhnhan.BENHNHANID;
			var dlgPopup = DlgUtil.buildPopupUrl("dlgLSDT", "dlgLichSuDieuTri", "manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri", myVar, "Lịch sử điều trị", 1320, 610);
			DlgUtil.open("dlgLSDT");
		});
		$("#lsCongBHYT").click(
				function() {
					var resultThe = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H001.EV021", _tiepnhanid);
					if (resultThe != null && resultThe.length > 0) {
						var paramInput = {
							MABHYT : resultThe[0].MA_BHYT,
							TENBENHNHAN : resultThe[0].TENBENHNHAN,
							NGAYSINH : resultThe[0].NGAYSINH,
							QRCODE : '',
							GIOITINH : resultThe[0].GIOITINHID,
							MAKCBBD : resultThe[0].MA_KCBBD
						// TUNGAY : $('#txtBHYT_BD').val(),
						// DENNGAY : $('#txtBHYT_KT').val()
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB", paramInput, "Thông tin lịch sử điều trị bệnh nhân",
								window.innerWidth * 0.95, window.innerHeight * 0.93);
						var parent = DlgUtil.open("divDlgDDT");
					}
				});
		// L2PT-17796 start
		$("#rKiemTraLoiCC").click(function() {
			var _selRowIds = $("#" + _gridId_BN).jqGrid('getGridParam', 'selarrrow');
			if (!_selRowIds || _selRowIds.length == 0) {
				DlgUtil.showMsg("Chưa chọn hồ sơ!");
				return;
			} else if (_selRowIds.length > 1) {
				DlgUtil.showMsg("Không được chọn nhiều hồ sơ trong 1 lần thao tác!");
				return;
			}
			var noiDungLoi = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.CHECK.CC.XML", _tiepnhanid + "$" + "0");
			if (noiDungLoi && noiDungLoi != "") {
				DlgUtil.showConfirm(noiDungLoi + "Bạn có muốn xóa bỏ ký tự lỗi trong XML ?", function(flag) {
					if (flag) {
						var ketQuaXoa = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.CHECK.CC.XML", _tiepnhanid + "$" + "1");
						if (ketQuaXoa && ketQuaXoa != "") {
							DlgUtil.showMsg(ketQuaXoa);
						} else {
							DlgUtil.showMsg("Không phát hiện lỗi ký tự điều khiển của hồ sơ này!");
						}
					}
				});
			} else {
				DlgUtil.showMsg("Không phát hiện lỗi ký tự điều khiển của hồ sơ này!");
			}
		});
		// L2PT-17796 end
		// xu ly su kien chon trích sao bệnh án
		$('#rTrichSaoBenhAn').on("click", function(e) {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			} ];
			openReport('window', "PHIEU_TRICHSAO_BENHAN", "pdf", par);
		});
		// xu ly su kien chon duyet
		$('#rDuyetBaoHiem').on("click", function(e) {
			DlgUtil.showMsg("");
		});
		// xu ly su kien chon quyet toan xuat toan
		$('#rQuyetToanXuatToan').on("click", function(e) {
			DlgUtil.showMsg("");
		});
		// lay thong tin benh an tuong ung voi ma vien phi
		function layTTTiepNhan(_tiepnhanid) {
			var _benhnhan_arr = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_thongtintiepnhan, _tiepnhanid);
			// console.log(_benhnhan_arr);
			// L2PT-55208 fix check bn
			if (_benhnhan_arr && _benhnhan_arr.length > 0) {
				_benhnhan = _benhnhan_arr[0];
				FormUtil.clearForm('ttVienphi', "");
				if (_benhnhan.TRANGTHAITIEPNHAN != 2) {
					setEnabled([ 'btnDuyet' ], [ 'btnGoDuyet' ]);
				} else if (_benhnhan.TRANGTHAITIEPNHAN == 2) {
					setEnabled([ 'btnGoDuyet' ], [ 'btnDuyet' ]);
				}
				// _benhnhan.DIACHI =
				// getDiaChi(_benhnhan.TENXA,_benhnhan.TENHUYEN,
				// _benhnhan.TENTINH);
				FormUtil.setObjectToForm('ttVienphi', "", _benhnhan);
				FormUtil.setObjectToForm('ttDuyet', "", _benhnhan);
				// L2PT-111074 start
				var ma_loai_kcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.LAY.MALOAIKCB", _tiepnhanid);
				$("#txtMA_LOAI_KCB").val(ma_loai_kcb);
				// L2PT-111074 end
			}
			loadGridDataDV(_tiepnhanid);
			loadGridDataPT(_tiepnhanid);
		}
		// L2PT-3622 start
		GridUtil.setGridParam(_gridId_DV, {
			beforeSelectRow : function(rowid, e) {
				if (flagLoading) {
					return false; // not allow select the row
				} else {
					return true; // allow select the row
				}
			},
			onSelectRow : function(id) {
				if (flagLoading) {
					return;
				}
				var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
				GridUtil.unmarkAll(_gridId_DV);
				for (var i = 0; i < _selRowIds.length; i++) {
					GridUtil.markRow(_gridId_DV, _selRowIds[i]);
				}
			},
			onSelectAll : function(ids, status) {
				if (flagLoading) {
					return;
				}
				var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
				GridUtil.unmarkAll(_gridId_DV);
				for (var i = 0; i < _selRowIds.length; i++) {
					GridUtil.markRow(_gridId_DV, _selRowIds[i]);
				}
			}
		});
		// L2PT-3622 end
		// xu ly khi grid dich vu tai xong
		GridUtil.setGridParam(_gridId_DV, {
			gridComplete : function() {}
		});
		// xu ly khi chon mot dong tren grid phieu thu
		GridUtil.setGridParam(_gridId_PT, {
			onSelectRow : function(id) {
				if (id) {}
			}
		});
		// xu ly khi grid phieu thu tai xong
		GridUtil.setGridParam(_gridId_PT, {
			gridComplete : function() {}
		});
		// xy ly nut "duyet"
		$("#btnDuyet").bindOnce("click", function() {
			if (flagLoading)
				return;
			$('#txtNGAYDUYET').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			setquyduyet($('#txtNGAYDUYET').val());
			$("#txtNGUOIDUYET").val(_user_id);
			setEnabled([ 'btnLuu', 'btnHuyBo', 'txtNGAYDUYET', 'dpkNGAYDUYET', 'txtSTT' ], [ 'btnDuyet' ]); // L2PT-23670 : cho sửa STT
			flagLoading = true;
		}, 1000);
		$('#txtNGAYDUYET').change(function() {
			setquyduyet($('#txtNGAYDUYET').val());
		});
		function setquyduyet(ngay) {
			var d = stringToDateTime(ngay);
			var month = d.getMonth() + 1;
			if (month <= 3)
				$("#cboQUYDUYET").val(1);
			else if (month <= 6)
				$("#cboQUYDUYET").val(2);
			else if (month <= 9)
				$("#cboQUYDUYET").val(3);
			else
				$("#cboQUYDUYET").val(4);
		}
		// xy ly nut "Luu"
		$("#btnLuu").bindOnce("click", function() {
			if (VPI_KIEMTRA_TYLE == 0) {
				if (_khoa && _khoa.length > 0) {
					DlgUtil.showMsg("Có dịch vụ sai tỷ lệ tại " + _khoa);
					return;
				}
				// L2PT-3951 start: Tuyến 3 trở xuống cho phép trái tuyến
				var VPI_TUYEN_BV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_TUYEN_BV');
				if (parseInt(VPI_TUYEN_BV) < 3 && _benhnhan.LOAITIEPNHANID != 0 && _benhnhan.LYDO_VAOVIEN == 3 && _benhnhan.MUCHUONG > 0) {
					DlgUtil.showMsg("Bệnh nhân ngoại trú trái tuyến đang có mức hưởng , xem lại thông tin hành chính");
					return;
				}
				// L2PT-3951 end
			}
			// L2PT-48479 start : Thêm giá trị cấu hình VPI_DBHDKT_KVP = 2
			var VPI_DBHDKT_KVP = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_DBHDKT_KVP');
			/*if (VPI_DBHDKT_KVP == '1' || VPI_DBHDKT_KVP == '2') {
				var index = VPI_DBHDKT_KVP == '2' ? '9' : '2';
				var check_cp = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GET.CHIPHI", _tiepnhanid + '$' + index);
				if (check_cp == '0') {
					duyetKeToan();
					return;
				}
			}*/
			if (_benhnhan.TRANGTHAITIEPNHAN_VP != 1 && VPI_DBHDKT_KVP != '0') {
				var check_cp = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.CHECK.TTDV", _tiepnhanid);
				if (check_cp == '1') {
					if (VPI_DBHDKT_KVP == '1' && parseFloat(_vp_giaodich.VIENPHI) == 0) {
						duyetKeToan("1");
						return;
					} else if (VPI_DBHDKT_KVP == '2' && parseFloat(_vp_giaodich.CHENHLECH) == 0) {
						duyetKeToan("1");
						return;
					}
				}
			}
			// L2PT-48479 end
			duyetBHYT();
		}, 1000);
		// xu ly nut huy bo
		$("#btnHuyBo").click(function() {
			flagLoading = false;
			FormUtil.clearForm('ttDuyet', "");
			$("#cboQUYDUYET").val(0);
			setEnabled([ 'btnDuyet' ], [ 'btnLuu', 'btnHuyBo', 'cboQUYDUYET', 'txtNGUOIDUYET', 'txtSTT', 'txtNGAYDUYET', 'dpkNGAYDUYET' ]);
		});
		// BVTM-5077 start
		$("#btnDuyetTool").click(function() {
			duyetTool(0, "");
		});
		$("#btnGoDuyetTool").bindOnce("click", function() {
			duyetTool(1, "");
		}, 1000);
		function duyetTool(_mode, _ngay_duyet) {
			// L2PT-123881 start
			var buff = parseInt(fConfig.VPI_SOHS_1LANDUYET);
			var strResult = "";
			var objResult = {
				SOHS_OK : 0,
				DS_CANHBAO : "",
				DS_LOI : ""
			}
			if (buff > 0) {
				var solanduyet = Math.ceil(arr_row.length / buff);
				for (var i = 0; i < solanduyet; i++) {
					var _dstn = "";
					var arr_row_part = arr_row.slice(buff * i, buff * (i + 1));
					_dstn = arr_row_part.join(",");
					if (!_dstn || _dstn.length == 0) {
						_dstn = "-1";
					}
					var strResultPart = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.BH.4210', _mode + '$' + _ngay_duyet + '$' + _dstn);
					try {
						var objResultPart = JSON.parse(strResultPart);
						objResult.SOHS_OK = objResult.SOHS_OK + parseInt(objResultPart.SOHS_OK);
						objResult.DS_CANHBAO = objResult.DS_CANHBAO + objResultPart.DS_CANHBAO;
						objResult.DS_LOI = objResult.DS_LOI + objResultPart.DS_LOI;
					} catch (err) {
						console.log(err);
						objResult.DS_LOI = objResult.DS_LOI + strResultPart;
					}
				}
				strResult = "Duyệt thành công " + objResult.SOHS_OK + " hồ sơ";
				strResult += objResult.DS_LOI ? "\nCác hồ sơ lỗi:\n" + objResult.DS_LOI : "";
				strResult += objResult.DS_CANHBAO ? "\nCác hồ sơ cảnh báo:\n" + objResult.DS_CANHBAO : "";
			} else {
				_dstn = arr_row.join(",");
				if (!_dstn || _dstn.length == 0) {
					_dstn = "-1";
				}
				strResult = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.BH.4210', _mode + '$' + _ngay_duyet + '$' + _dstn);
			}
			// L2PT-123881 end
			$('#txtNOIDUNGLOI').val(strResult);
			DlgUtil.open("dlgLOI");
			loadGridDataBN();
		}
		// BVTM-5077: end
		// xu ly nut "go duyet"
		$("#btnGoDuyet").bindOnce("click", function() {
			// L2PT-69331 start
			if (_benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
				var chiPhiDuoiTran = _benhnhan.DUOI_TRAN
				if (fConfig.VPI_GDKT_KHI_GDBH == '2') {
					duyetKeToan("0");
					return;
				} else if (fConfig.VPI_GDKT_KHI_GDBH == '1' && chiPhiDuoiTran == '1') {
					duyetKeToan("0");
					return;
				}
			}
			// L2PT-69331 end
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I(_sql_duyetbhyt_huy, _tiepnhanid);
			// L2PT-84306 start: check duyệt thuốc 2 - cảnh báo, -6 : chặn
			if (fl == 1 || fl == 2) {
				// DlgUtil.showMsg("Hủy duyệt thành công");
				flagLoading = false;
				setEnabled([ 'btnDuyet' ], [ 'btnLuu', 'btnGoDuyet' ]);
				// loadGridDataBN();
				var id = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
				$("#" + _gridId_BN).jqGrid('setCell', id, 'TRANGTHAITIEPNHAN', 1);
				// L2PT-94422 start
				if (fConfig.VPI_BH_917_4210 && "0,1,2,3,".indexOf(fConfig.VPI_BH_917_4210 + ",") != -1) {
					$("#" + _gridId_BN).jqGrid('setCell', id, 'TRANGTHAITIEPNHAN4210', 1);
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH', null);
				}
				if (fConfig.VPI_BH_917_4210 && "2,3,4,".indexOf(fConfig.VPI_BH_917_4210 + ",") != -1) {
					$("#" + _gridId_BN).jqGrid('setCell', id, 'TRANGTHAITIEPNHAN130', 1);
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH_2', null);
				}
				// L2PT-94422 end
				if (fl == 2) {
					DlgUtil.showMsg("Bệnh nhân đã được duyệt xuất thuốc, nếu cần chỉnh sửa đơn thuốc thì phải yêu cầu hủy đơn trước.");
				}
			} else if (fl == 0) {
				DlgUtil.showMsg("Hồ sơ của bệnh nhân đã khóa, không thể gỡ duyệt");
			} else if (fl == -2) {
				DlgUtil.showMsg("Hết thời gian xử lý hồ sơ, liên hệ với người quản trị");
			} else if (fl == -3) {
				DlgUtil.showMsg("Bạn không có quyền gỡ duyệt hồ sơ này");
			} else if (fl == -4) {
				DlgUtil.showMsg("Hồ sơ chưa được duyệt");
			} else if (fl == -5) {
				DlgUtil.showMsg("Không lưu được log gỡ duyệt");
			} else if (fl == -6) {
				DlgUtil.showMsg("Bệnh nhân đã được duyệt xuất thuốc, nếu cần chỉnh sửa đơn thuốc thì phải yêu cầu hủy đơn trước.");
			} else {
				DlgUtil.showMsg("Hủy duyệt không thành công");
			}
			// L2PT-84306 end
		}, 1000);
		// In phoi thanh toan vien phi
		$("#btnIn").click(function() {
			//Begin_HaNv_01042020:  Chặn In phơi khi BN còn đơn thuốc ngoại trú chưa duyệt - L2PT-18913
			var _chInPhoi = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_CHECK_INPHOI_DONTHUOCNGT') == '1') ? true : false;
			if (_chInPhoi) {
				var _res = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.DONTHUOCNGT", _tiepnhanid);
				if (_res != '0') {
					return DlgUtil.showMsg('Bệnh nhân còn đơn thuốc ngoại trú chưa duyệt. Không được phép In phơi!');
				}
			}
			//End_HaNv_01042020
			vienphi_tinhtien.inBangKe(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID);
		});
		// L2PT-19976 start
		$("#btnInBH").click(function() {
			vienphi_tinhtien.inPhoiVP("", _tiepnhanid, "NGT001_BKCPKCBBHYT_QD6556_THUOC_DOC_A4");
		});
		// L2PT-19976 end
		//Begin_HaNv_27052020: Bổ sung xuất file lỗi duyệt 4210 - L2PT-21345
		$('#btnXuatFileLoi').on("click", function(event) {
			var tu_ngay = $("#txtTU").val();
			var den_ngay = $("#txtDEN").val();
			var _fileName = moment(tu_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss') + "-" + moment(den_ngay, 'DD/MM/YYYY HH:mm:ss').format('YYYYMMDDHHmmss');
			var obj = new Object();
			obj.TUNGAY = tu_ngay;
			obj.DENNGAY = den_ngay;
			obj.KHOAID = $("#cboKhoa").val();
			if (stringToDateTime(tu_ngay) <= stringToDateTime(den_ngay)) {
				var _filePrefix = "FileError4210";
				_fileName = _filePrefix + '_' + _fileName + "." + 'xlsx';
				var dt_Rpt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI02G001_DS_LOI', JSON.stringify(obj));
				if (dt_Rpt != null && dt_Rpt.length > 0) {
					exportXLSX(dt_Rpt, _filePrefix, _fileName);
				} else {
					event.preventDefault();
					DlgUtil.showMsg("Không tìm thấy hồ sơ nào để tạo file Excel!");
				}
			} else {
				event.preventDefault();
				DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ. Từ ngày phải nhỏ hơn hoặc bằng đến ngày!");
			}
		});
		//End_HaNv_27052020
		// L2PT-3622 start
		$("#chkAnLB").change(function() {
			loadGridDataDV(_tiepnhanid);
		});
		// L2PT-3622 end
		// L2PT-24721 start
		$("#cboKhoa").change(function() {
			ComboUtil.getComboTag("cboPhong", 'VPI.PHONG.KCB', [ {
				name : "[0]",
				value : $("#cboKhoa").val()
			} ], "", {
				text : "Tất cả",
				value : -1
			}, 'sql', '', '');
		});
		// L2PT-24721 end
	}
	//Begin_HaNv_27052020: Bổ sung xuất file lỗi duyệt 4210 - L2PT-21345
	objectToXLSX = function(_jsonObject, _DataType) {
		var ws = {};
		var row;
		var col;
		var xml;
		var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
		var range = {
			s : {
				c : 10000000,
				r : 10000000
			},
			e : {
				c : 0,
				r : 0
			}
		};
		var col_arr = [];
		var _offset = 1;
		var n = data.length;
		var _grparent_change = false;
		if (data.length > 0) {
			col_arr = Object.keys(data[0]);
			for (row = 0; row <= data.length; row++) {
				for (var col = 0; col < col_arr.length; col++) {
					if (range.s.r > row)
						range.s.r = row;
					if (range.s.c > col)
						range.s.c = col;
					if (range.e.r < row)
						range.e.r = row;
					if (range.e.c < col)
						range.e.c = col;
					_col = col_arr[col];
					var cell = row > 0 ? {
						v : data[row - 1][_col]
					} : {
						v : _col
					};
					if (cell.v == null)
						continue;
					var cell_ref = XLSX.utils.encode_cell({
						c : col,
						r : row
					});
					if (row == 0) {
						cell.t = 's';
					} else if (typeof cell.v === 'number')
						cell.t = 'n';
					else if (typeof cell.v === 'boolean')
						cell.t = 'b';
					else if (cell.v instanceof Date) {
						cell.t = 'n';
						cell.z = XLSX.SSF._table[14];
						cell.v = datenum(cell.v);
					} else
						cell.t = 's';
					ws[cell_ref] = cell;
				}
			}
		}
		if (range.s.c < 10000000)
			ws['!ref'] = XLSX.utils.encode_range(range);
		return ws;
	};
	function datenum(v, date1904) {
		if (date1904)
			v += 1462;
		var epoch = Date.parse(v);
		return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
	}
	function Workbook() {
		if (!(this instanceof Workbook))
			return new Workbook();
		this.SheetNames = [];
		this.Sheets = {};
	}
	function exportXLSX(_jsonObject, _wsname, _filename) {
		var wb = new Workbook();
		var ws = objectToXLSX(_jsonObject);
		wb.SheetNames.push(_wsname);
		wb.Sheets[_wsname] = ws;
		XLSX.writeFile(wb, _filename);
	}
	//End_HaNv_27052020
	function InPhoiBhyt(_inbangkechuan, _tiepnhanid, _report_code) {
		var par = [ {
			name : 'inbangkechuan',
			type : 'String',
			value : _inbangkechuan.toString()
		}, {
			name : 'tiepnhanid',
			type : 'String',
			value : _tiepnhanid.toString()
		} ];
		var typeExport = "pdf";// $("#sltInBieuMau").val();
		openReport('window', _report_code, typeExport, par);
	}
	// lay du lieu cho grid vien phi
	function loadGridDataBN(ds_ba) {
		if (flagLoading)
			return;
		// L2PT-131223 start
		if(!gioihan_thoigian("VPI_SONGAY_TIMKIEM", "txtTU", "txtDEN" )){
			return false;
		}
		// L2PT-131223 end
		// L2PT-24615 start
		var sql_par = []
		var doituongbn = "1";
		var objTimKiem = new Object();
		//objTimKiem.TYPEIMPORT = "0"; // L2PT-11789 
		if (!ds_ba) {
			ds_ba = "";
		}
		// L2PT-11789 start: luu dsba
		else {
			if (Object.keys(ds_ba[0]).indexOf("MA") == -1) {
				DlgUtil.showMsg('File import cần có cột "MA" (mã bệnh án, mã bệnh nhân ... theo cấu hình VPI_MA_IMPORT)');
				return;
			}
			objImport = new Object();
			objImport.DATA = ds_ba;
			objImport.SSID = _ssImportId;
			ds_ba = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.IMPORT", JSON.stringify(objImport));
			objTimKiem.TYPEIMPORT = "1";
			_ssImportId = ds_ba;
		}
		// L2PT-11789 end
		// L2PT-24721 start
		FormUtil.setFormToObject('divSearch', '', objTimKiem);
		objTimKiem.DOITUONGBENHNHAN = "1";
		objTimKiem.KHOAID_DN = _khoa_id;
		objTimKiem.PHONGID_DN = _phong_id;
		objTimKiem.DS_BA = ds_ba;
		sql_par.push({
			"name" : "[0]",
			"value" : JSON.stringify(objTimKiem)
		});
		// L2PT-24721 end
		// L2PT-24615 end
		_tiepnhanid = -1;
		FormUtil.clearForm('ttVienphi', '');
		FormUtil.clearForm('ttDuyet', '');
		FormUtil.clearForm('tongVP', '');
		$("#" + _gridId_DV).jqGrid("clearGridData");
		$("#" + _gridId_PT).jqGrid("clearGridData");
		GridUtil.loadGridBySqlPage(_gridId_BN, _gridSQL_BN, sql_par, function() {
			var grid = $("#" + _gridId_BN);
			var sl = grid.jqGrid('getGridParam', 'records');
			$("#lblSoLuong").text(sl);
			// build menu
			$(".jqgrow", '#' + _gridId_BN).contextMenu('contextMenu', {
				onContextMenu : function(event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#' + _gridId_BN);
					// L2PT-27537 start
					var _suachua_ba = grid.jqGrid('getCell', rowId, 'SUACHUA_BA');;
					if (_suachua_ba == 1) {
						_mode_scba = 0;
						$("#spaSUACHUA_BA").text('Gỡ bỏ sửa chữa');
					} else {
						_mode_scba = 1;
						$("#spaSUACHUA_BA").text('Đánh dấu sửa chữa');
					}
					// L2PT-27537  end
					// grid.setSelection(rowId);
					return true;
				},
			});
			var ids = grid.getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var row = $("#" + _gridId_BN).jqGrid('getRowData', id);
				var _icon = '';
				if (!row.TRANGTHAITIEPNHAN || row.TRANGTHAITIEPNHAN == 0) {
					_icon = '';
				} else if (row.TRANGTHAITIEPNHAN == 1 || row.TRANGTHAITIEPNHAN == 2) {
					_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
					if (row.TRANGTHAITIEPNHAN_VP == 1 || row.TRANGTHAITIEPNHAN_BH == 1) {
						_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
					}
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KT', _icon);
				}
				if (row.TRANGTHAITIEPNHAN4210 == 2) {
					_icon = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH', _icon);
				}
				// L2PT-93751 start
				if (row.TRANGTHAITIEPNHAN130 == 2) {
					_icon = '<center><img src="' + _opts.imgPath[1] + '" width="15px"></center>';
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH_2', _icon);
				}
				// L2PT-93751 end
				// chon row da danh dau
				if (arr_row.indexOf(row.TIEPNHANID) != -1) {
					grid.jqGrid('setSelection', id, false);
					GridUtil.markRow(_gridId_BN, id);
					_tiepnhanid = row.TIEPNHANID;// DUONGHN FIX
				}
				// L2PT-27537 start: danh dau ho so da xac nhan bao hiem hay chua
				if (row.XACNHANBH == 1) {
					$("#" + _gridId_BN).jqGrid('setRowData', id, "", {
						color : 'blue'
					});
				}
				// L2PT-27537 end
				// danh dau ho so da xac nhan bao hiem hay chua
				if (row.SUACHUA_BA == 1) {
					$("#" + _gridId_BN).jqGrid('setRowData', id, "", {
						color : 'red'
					});
				}
			}
			if (ds_ba) {
				$('#cb_' + _gridId_BN).click();
			}
		}); // L2PT-19490
	}
	// lay du lieu cho grid dich vu
	function loadGridDataDV(_tiepnhanid) {
		if (flagLoading)
			return;
		var sql_par = [];
		_khoa = "";
		sql_par.push({
			"name" : "[0]",
			"value" : _tiepnhanid
		});
		sql_par.push({
			"name" : "[1]",
			"value" : $("#chkAnLB").prop("checked") ? "1" : "0" // L2PT-3622 
		});
		// sql_par=RSUtil.setSysParam(sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridId_DV, "VPI01T001.21", sql_par, function() {
			var grid = $("#" + _gridId_DV);
			var ids = grid.getDataIDs();
			// L2PT-3622 start
			$(".jqgrow", '#' + _gridId_DV).contextMenu('contextMenu2', {
				onContextMenu : function(event, menu) {
					return true;
				},
				bindings : {
					//xu ly su kien loai bo dv ra khoi xml
					'rLoaiBo' : function(t) {
						var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
						var _ds_dvkbid = "";
						for (var i = 0; i < _selRowIds.length; i++) {
							_ds_dvkbid += $("#" + _gridId_DV).jqGrid('getCell', _selRowIds[i], 'DICHVUKHAMBENHID') + ",";
						}
						_ds_dvkbid = _ds_dvkbid.slice(0, -1);
						if (!_ds_dvkbid || _ds_dvkbid.length == 0) {
							DlgUtil.showMsg("Chưa chọn dịch vụ");
							return;
						}
						var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.LOAIBO.DVBH', _ds_dvkbid + '$9');
						loadGridDataDV(_tiepnhanid);
						DlgUtil.showMsg(_result);
					},
					//xu ly su kien go loai bo dv ra khoi xml
					'rGoLoaiBo' : function(t) {
						var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
						var _ds_dvkbid = "";
						for (var i = 0; i < _selRowIds.length; i++) {
							_ds_dvkbid += $("#" + _gridId_DV).jqGrid('getCell', _selRowIds[i], 'DICHVUKHAMBENHID') + ",";
						}
						_ds_dvkbid = _ds_dvkbid.slice(0, -1);
						if (!_ds_dvkbid || _ds_dvkbid.length == 0) {
							DlgUtil.showMsg("Chưa chọn dịch vụ");
							return;
						}
						var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.LOAIBO.DVBH', _ds_dvkbid + '$0');
						loadGridDataDV(_tiepnhanid);
						DlgUtil.showMsg(_result);
					}
				}
			});
			// L2PT-3622 end
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _dathutien = grid.jqGrid('getCell', id, 'DATHUTIEN');
				var _soluong = grid.jqGrid('getCell', id, 'SOLUONG');
				var _tyle_bhyt_tra = grid.jqGrid('getCell', id, 'TYLE_BHYT_TRA');
				var _tyle_the = grid.jqGrid('getCell', id, 'TYLE');
				var _loaidoituong = grid.jqGrid('getCell', id, 'LOAIDOITUONG');
				var _tyle_dv = grid.jqGrid('getCell', id, 'TYLE_DV');
				var _loainhommbp = grid.jqGrid('getCell', id, 'LOAINHOMMAUBENHPHAM');
				var _tien_dv = grid.jqGrid('getCell', id, 'TIENDICHVU');
				var _vattu04 = grid.jqGrid('getCell', id, 'VATTU04');
				if (_dathutien == 3) {
					// GridUtil.markRow(_gridId_DV,id);
					$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
						color : 'blue'
					});
				}
				if ((_loaidoituong == 1 || _loaidoituong == 2 || _loaidoituong == 3) && _loainhommbp != 16 && _tyle_the != _tyle_bhyt_tra && _tyle_dv != 0 && _soluong != 0 &&
						(_loainhommbp != 3 || _tien_dv != 0) && _vattu04 == 0) {
					$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
						color : 'red'
					});
					var _khoadv = grid.jqGrid('getCell', id, 'KHOA');
					if (!_khoa.includes(_khoadv))
						_khoa += _khoadv + ", ";
				}
				// L2PT-3622 start
				var _trangthaimaubenhpham = grid.jqGrid('getCell', id, 'TRANGTHAIMAUBENHPHAM');
				var _loainhommaubenhpham = grid.jqGrid('getCell', id, 'LOAINHOMMAUBENHPHAM');
				var _loaiduyetbhyt = grid.jqGrid('getCell', id, 'LOAIDUYETBHYT');
				if (_loaiduyetbhyt == '8' || _loaiduyetbhyt == '9') {
					_icon = '<center><img src="' + _opts.imgPath[5] + '" width="15px"></center>';
					$("#" + _gridId_DV).jqGrid('setCell', id, 'ICON1', _icon);
				}
				if (_trangthaimaubenhpham != 3 && (_loainhommaubenhpham == 1 || _loainhommaubenhpham == 2)) {
					$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
						color : '#cc0066'
					});
				}
				if (_trangthaimaubenhpham != 6 && _loainhommaubenhpham == 7) {
					$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
						color : '#00cc00'
					});
				}
				// L2PT-3622 end
			}
			_khoa = _khoa.slice(0, -2);
			_dvData = grid.jqGrid('getRowData');
			tinhTongTien(_dvData);
		});
		setEnabled([], [ 'btnLuu', 'btnHuyBo' ]);
	}
	function tinhTongTien(_dvData) {
		var _tran_bhyt = _benhnhan.BHYT_GIOIHANBHYTTRAHOANTOAN;
		var _tyle_tuyen = _benhnhan.TYLE_TUYEN;
		var _tyle_bhyt = _benhnhan.TYLE_THE;
		// var _du5nam = _benhnhan.THAMGIABHYTDU5NAM;
		// var _tradu6thang = _benhnhan.TRADU6THANGLUONGCOBAN;
		_vp_giaodich = new Object(); // L2PT-48479
		var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
		if (arr_dagiaodich && arr_dagiaodich.length > 0)
			_vp_giaodich = arr_dagiaodich[0];
		_vpData = vienphi_tinhtien.tinhtien_dv(_dvData, _vp_giaodich, _tran_bhyt, _tyle_tuyen, _tyle_bhyt);
		// console.log(_vpData);
		var _vpData_hienthi = vienphi_tinhtien.convertObjToCurrency(_vpData);
		FormUtil.setObjectToForm('tongVP', '', _vpData_hienthi);
		//Begin_HaNv_28042020:  Ẩn In phơi khi BN chưa đóng đủ tiền - L2PT-19278
		if (_hideInPhoi && _vpData.NOPTHEM != 0) {
			setEnabled([], [ 'btnIn', 'btnInBH' ]); // L2PT-19976
		} else {
			setEnabled([ 'btnIn', 'btnInBH' ], []); // L2PT-19976
		}
		//End_HaNv_28042020
	}
	// lay du lieu cho grid phieu thu
	function loadGridDataPT(_tiepnhanid) {
		if (flagLoading)
			return;
		var lookup_sqlPT = "";
		lookup_sqlPT = _gridSQL_PT;
		var sql_parPT = [];
		// L2PT-32061 start
		var _tungay_pt = "-1";
		var _denngay_pt = "-1";
		var _key = "-1";
		var _mode = "0";
		var _loaiphieu = "-1";
		var _loaihoadon = "-1";
		var _modeMG = "-1";
		var objLoadDSPT = new Object();
		objLoadDSPT.TIEPNHANID = _tiepnhanid + "";
		objLoadDSPT.LOAIPHIEU = _loaiphieu + "";
		objLoadDSPT.TUNGAY = _tungay_pt + "";
		objLoadDSPT.DENNGAY = _denngay_pt + "";
		objLoadDSPT.KEY = _key + "";
		objLoadDSPT.MODE = _mode + "";
		objLoadDSPT.MODEMG = _modeMG + "";
		objLoadDSPT.LOAIHD = _loaihoadon + "";
		sql_parPT.push({
			"name" : "[0]",
			"value" : JSON.stringify(objLoadDSPT)
		});
		// L2PT-32061 end
//		sql_parPT= RSUtil.setSysParam(sql_parPT, _param);
		GridUtil.loadGridBySqlPage(_gridId_PT, lookup_sqlPT, sql_parPT, function() {
			var grid = $("#" + _gridId_PT);
			var ids = grid.getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _dahuyphieu = grid.jqGrid('getCell', id, 'DAHUYPHIEU');
				if (_dahuyphieu == 1) {
					_icon = '<center><img src="' + _opts.imgPath[5] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 2, _icon);
				}
			}
		});
	}
	/*
	 * func :
	 */
	function getXmlTo_Medical(data_ar) {
		if (data_ar && data_ar.length > 0) {
			var _row = data_ar[0];
			var ret = ajaxSvc.PortalWS.WS_call("guiHSXV", _row.HIS_USER_CSYT, _row.HIS_PASS_CSYT, unescape(_row.XMLBN));
			var resultText = $(ret).find("Error > Error_Message").text();
			var result = parseInt($(ret).find("Error > Error_Number").text());
			var trans_id = $(ret).find("Transaction_ID").text();
			if (result !== 0) {
				DlgUtil.showMsg(resultText);
			}
			var objCDLYT = new Object();
			objCDLYT.MA_LK = _tiepnhanid + "";
			objCDLYT.CONG_DLYT = result === 0 ? "1" : "2";
			objCDLYT.BHXH = "";
			objCDLYT.KETQUA = ret;
			objCDLYT.MA_KET_QUA = result + "";
			objCDLYT.MA_GIAO_DICH = trans_id;
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02", JSON.stringify(objCDLYT));
			if (ret == -1) {
				DlgUtil.showMsg("Cập nhật trạng thái gửi cổng DLYT không thành công");
			}
			return result === 0 ? "1" : "0";
		} else {
			DlgUtil.showMsg("Không tìm thấy hồ sơ, gửi cổng BYT không thành công");
			return null;
		}
	}
	function duyetBHYT() {
		var objData = new Object();
		FormUtil.setFormToObject('ttDuyet', '', objData);
		objData.TIEPNHANID = _tiepnhanid;
		var _hcode = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.GET.CSKCB', _benhnhan.KHOAID);
		objData.HOSPITAL_CODE = _hcode;
		// L2PT-27114 start : Duyệt bảo hiểm trước thời gian ra viện
		var VPI_THOIGIAN_DUYETBH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THOIGIAN_DUYETBH');
		if (VPI_THOIGIAN_DUYETBH == 0) {
			var _ngay_duyet = stringToDateTime(objData.NGAYDUYET);
			var _ngay_ravien = stringToDateTime(_benhnhan.NGAYRAVIEN);
			if (_ngay_duyet < _ngay_ravien) {
				DlgUtil.showMsg("Ngày duyệt phải lớn hơn ngày ra viện");
				return false;
			}
		}
		// L2PT-27114 end
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S(_sql_duyetbhyt, JSON.stringify(objData));
		thongBaoKetQua(fl, _hcode);
	}
	function thongBaoKetQua(fl, _hcode) {
		if (fl == 1 || fl == 3) {
			var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
			var _ngay_ra = _benhnhan.NGAY_RA_VIEN;
			// L2PT-51016 start
			if (fConfig.VPI_CBDBHYT_KHACNGAY == '0' && _ngay_ra != _ngayhientai) {
				DlgUtil.showMsg("Hồ sơ mã " + _benhnhan.MAHOSOBENHAN + " có ngày ra viện khác ngày thanh toán. Yêu cầu: BỔ SUNG HỒ SƠ VÀO NGÀY " + _ngay_ra);
			}
			// L2PT-51016 end
			flagLoading = false;
			setEnabled([ 'btnGoDuyet' ], [ 'btnDuyet', 'btnLuu', 'btnHuyBo', 'cboQUYDUYET' ]);
			// loadGridDataBN();
			var id = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
			$("#" + _gridId_BN).jqGrid('setCell', id, 'TRANGTHAITIEPNHAN', 2);
			// L2PT-94422 start
			if (fConfig.VPI_BH_917_4210 && "0,1,2,3,".indexOf(fConfig.VPI_BH_917_4210 + ",") != -1) {
				$("#" + _gridId_BN).jqGrid('setCell', id, 'TRANGTHAITIEPNHAN4210', 2);
				var _icon = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
				$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH', _icon);
			}
			if (fConfig.VPI_BH_917_4210 && "2,3,4,".indexOf(fConfig.VPI_BH_917_4210 + ",") != -1) {
				$("#" + _gridId_BN).jqGrid('setCell', id, 'TRANGTHAITIEPNHAN130', 2);
				var _icon = '<center><img src="' + _opts.imgPath[1] + '" width="15px"></center>';
				$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH_2', _icon);
			}
			// L2PT-94422 end
			// Gui bao hiem & y te
			guiBHYT(_hcode);
			FormUtil.clearForm('ttDuyet', "");
			$("#cboQUYDUYET").val(1);
			if (fl == 3) {
				DlgUtil.showMsg("Chú ý: Hồ sơ bệnh nhân đã được duyệt nhiều lần");
			}
			// start thêm cấu hình in bảng kê //L2PT-25528
			var VPI_INBK_DUYETBH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_INBK_DUYETBH');
			if (VPI_INBK_DUYETBH == 1) {
				$("#btnIn").click();
			}
			// end thêm cấu hình in bảng kê //L2PT-25528
		} else if (fl == 0) {
			DlgUtil.showMsg("Hồ sơ của bệnh nhân đã khóa, không thể duyệt");
		} else if (fl == 2) {
			DlgUtil.showMsg("Bệnh nhân không có tiền đề nghị thanh toán, không thể duyệt");
		} else if (fl.substr(0, 2) == -1) {
			$('#txtNOIDUNGLOI').val(fl.slice(2));
			DlgUtil.showMsg("Duyệt không thành công");
		} else {
			// DlgUtil.showMsg(fl);
			$('#txtNOIDUNGLOI').val(fl);
			DlgUtil.open("dlgLOI");
		}
	}
	function guiBHYT(_hcode) {
		// L2PT-55208 start: nếu ngày ra viện lớn hơn thời gian hiện tại thì không gửi
		var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		var diffNGNR = diffDate(_benhnhan.NGAYRAVIEN, sysdate, 'DD/MM/YYYY HH:mm:ss')
		if (fConfig.VPI_BHXH_KT_NGAYGUI == 0 && diffNGNR > 0) {
			console.log("Thời gian ra viện lớn hơn thời gian hiện tại, gửi BHXH sau");
		} else
		// L2PT-55208 end
		// L2PT-43188 : gửi theo mã loại KCB
		// L2PT-51016 start
		if (_vpData.TONGTIENBH === 0) {
			console.log("BN  không có tiền BHYT, không gửi cổng BHXH");
		} else
		// L2PT-51016 end
		if (fConfig.VPI_GUI_MA_LOAI_KCB == 0 || (fConfig.VPI_GUI_MA_LOAI_KCB == 2 && _benhnhan.LOAITIEPNHANID == 1) || (fConfig.VPI_GUI_MA_LOAI_KCB == 3 && _benhnhan.LOAITIEPNHANID == 3) ||
				(fConfig.VPI_GUI_MA_LOAI_KCB == 4 && _benhnhan.LOAITIEPNHANID != 0) || (fConfig.VPI_GUI_MA_LOAI_KCB == 5 && _benhnhan.LOAITIEPNHANID == 0)) {
			var _flag_gui = true;
			// L2PT-90986 : them cau hinh = 6
			if (VP_GUI_DULIEU_KHIDUYET == 4 || VP_GUI_DULIEU_KHIDUYET == 6) {
				var _ma_bhyt = _benhnhan.MABHYT;
				if (_ma_bhyt) {
					var _loai_the = _ma_bhyt.substr(2, 3);
					if (_loai_the == "297" || _loai_the == "497" || _loai_the == "597") {
						_flag_gui = false;
					}
				}
			}
			if (VP_GUI_DULIEU_KHIDUYET != 0 && _flag_gui) {
				// L2PT-90986 start
				if (parseInt(VP_GUI_DULIEU_KHIDUYET) > 4) {
					// L2PT-93751  start
					// L2PT-84768 start
					var kq_gui_cong_bhxh = gui_cong_bhxh('', _tiepnhanid, _hcode, "130", "1");
					if (kq_gui_cong_bhxh == "1") {
						DlgUtil.showMsg("Gửi hồ sơ( 4750) tới cổng Bảo hiểm xã hội thành công"); // L2PT-12176
					} else if (kq_gui_cong_bhxh) {
						DlgUtil.showMsg(kq_gui_cong_bhxh);
					}
					// L2PT-84768 end
					// L2PT-93751  end
				} else {
					// L2PT-90986 end
					var data_bh = [];
					var data_byt = [];
					// L2PT-2105 start
					//var _ngayravien = _benhnhan.NGAYRAVIEN;
					var _ngayravien = jsonrpc.AjaxJson.getOneValue("VPI.NGAYHOSO", [ {
						name : "[0]",
						value : _tiepnhanid
					} ]);
					// L2PT-2105 end
					if (VPI_GUI_BH == 1) {
						// L2PT-4245 start
						var func_name = "";
						var _param = "";
						// L2PT-47331 start
						/*var VPI_KIEU_XML = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_KIEU_XML');
						if (VPI_KIEU_XML == '1') {*/
						// L2PT-47331 end
						func_name = "XUAT.XML.DS";
						var objXuatXML = new Object();
						objXuatXML.LOCTHEO = 1 + "";
						objXuatXML.TU_NGAY = _ngayravien
						objXuatXML.DEN_NGAY = _ngayravien
						objXuatXML.LoaiHS = -1 + "";
						objXuatXML.Tuyen = -1 + "";
						objXuatXML.MaThe = -1 + "";
						objXuatXML.LoaiThe = -1 + "";
						objXuatXML.MAHOA = 1 + "";
						objXuatXML.DS_MALK = _tiepnhanid;
						objXuatXML.HCODE = _hcode + "";
						objXuatXML.MODE = "1";
						objXuatXML.DTBN = "1";
						_param = JSON.stringify(objXuatXML);
						// L2PT-47331 start
						/*} else {
							func_name = "XML.4210";
							_param = 1 + '$' + _ngayravien + '$' + _ngayravien + '$' + 1 + '$' + _tiepnhanid + '$' + _hcode;
						}*/
						// L2PT-4245 end
						// L2PT-47331 end
						data_bh = jsonrpc.AjaxJson.ajaxCALL_SP_O(func_name, _param, []); // L2PT-4245
						data_byt = data_bh;
					} else {
						data_bh = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_get_xml_checkout, _tiepnhanid + '$' + _ngayravien + '$' + _ngayravien, []);
						data_byt = jsonrpc.AjaxJson.ajaxCALL_SP_O('BH01.XML01', _ngayravien + '$' + _ngayravien + '$' + _tiepnhanid, []);
					}
					if (VP_GUI_DULIEU_KHIDUYET == 1) {
						getXmlTo_Insr(data_bh);
						getXmlTo_Medical(data_byt);
					} else if (VP_GUI_DULIEU_KHIDUYET == 2) {
						getXmlTo_Insr(data_bh);
					} else if (VP_GUI_DULIEU_KHIDUYET == 3) {
						getXmlTo_Medical(data_byt);
					}
				}
			}
		}
	}
	function getXmlTo_Insr(data_ar) {
		var objBHXH = new Object();
		objBHXH.MA_LK = _tiepnhanid + "";
		objBHXH.CONG_DLYT = "";
		objBHXH.CONG_BHXH = "";
		objBHXH.KETQUA = "";
		objBHXH.MA_KET_QUA = "";
		objBHXH.MA_GIAO_DICH = "";
		if (data_ar && data_ar.length > 0) {
			var ret = "";
			try {
				var _row = data_ar[0];
				/*
				objBHXH.KETQUA = _row.XMLBN;
				var retLogGui = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02", JSON.stringify(objBHXH));
				if (retLogGui == -1) {
					DlgUtil.showMsg("Cập nhật trạng thái gửi cổng BHXH không thành công");
				}
				*/
				if (VPI_GUI_BH == 1) {
					ret = ajaxSvc.InsrWS.sendInsr4210("3", unescape(_row.XMLBN), _row.HIS_USER_INSR, _row.HIS_PASS_INSR, _row.TINH_ID, _row.MA_CSKCB, _row.HUYEN_ID, _row.LOAI_KY_GD);
				} else {
					ret = ajaxSvc.InsrWS.sendInsr("3", unescape(_row.XML_DATA), _row.HIS_USER_INSR, _row.HIS_PASS_INSR, _row.TINH_ID, _row.TEN_CSYT, _row.HUYEN_ID, _row.LOAI_KY_GD);
				}
				var result = $.parseJSON(ret);
				if (result.maKetQua == "200") {
					if (VPI_KTKQ_GUI == 0 || VPI_GUI_BH != 1) {
						DlgUtil.showMsg("Gửi hồ sơ tới cổng Bảo hiểm xã hội thành công. Mã giao dịch để tra cứu trên cổng là : " + result.maGiaoDich);
					} else {
						var retKQG = ajaxSvc.InsrWS.layKQGui(_row.HIS_USER_INSR, _row.HIS_PASS_INSR, _row.HIS_USER_CSYT, result.maGiaoDich);
//        			console.log(ret);
						if (retKQG && retKQG != "") {
							var jret = $.parseJSON(retKQG);
							var trangThaiHS = 0;
							var msgLoi = "";
							if (jret.maKetQua && jret.maKetQua == "200") {
								var dsLoi = jret.dsLoi;
								if (dsLoi.length == 0) {
									trangThaiHS = 1;
									DlgUtil.showMsg("Gửi hồ sơ tới cổng Bảo hiểm xã hội thành công. Mã giao dịch để tra cứu trên cổng là : " + result.maGiaoDich);
								} else {
									trangThaiHS = 2;
									for (var i = 0; i < dsLoi.length; i++) {
										var loi = dsLoi[i];
										msgLoi += msgLoi + "Mã lỗi: " + loi.maLoi + " - " + loi.moTaLoi + ".&#13;&#10;";
									}
									$('#txtNOIDUNGLOI').html(msgLoi);
									DlgUtil.open("dlgLOI");
								}
								var retLogTraKQG = jsonrpc.AjaxJson.ajaxCALL_SP_I("UPDATE_KQ_GUI", _tiepnhanid + "$" + trangThaiHS + "$" + msgLoi + "$" + result.maGiaoDich);
								if (retLogTraKQG == -1) {
									DlgUtil.showMsg("Cập nhật trạng thái không thành công");
								}
							} else if (jret.maKetQua == "401") {
								DlgUtil.showMsg("Lỗi xác thực cổng BHXH");
							} else if (jret.maKetQua == "500") {
								DlgUtil.showMsg("Lỗi từ cổng BHXH");
							} else {
								DlgUtil.showMsg("Kiểm tra giao dịch không thành công");
							}
						}
					}
				} else if (result.maKetQua == "401") {
					DlgUtil.showMsg("Lỗi xác thực cổng BHXH");
				} else if (result.maKetQua == "500") {
					DlgUtil.showMsg("Lỗi từ cổng BHXH");
				} else {
					DlgUtil.showMsg("Gửi BHXH không thành công, mã kết quả: " + result.maKetQua);
				}
				objBHXH.MA_LK = _tiepnhanid + "";
				objBHXH.CONG_DLYT = "";
				objBHXH.CONG_BHXH = result.maKetQua == "200" ? "1" : "2";
				objBHXH.KETQUA = ret;
				objBHXH.MA_KET_QUA = result.maKetQua;
				objBHXH.MA_GIAO_DICH = result.maGiaoDich;
			} catch (err) {
				objBHXH.KETQUA = ret + ' - ' + err.message;
				DlgUtil.showMsg("Có lỗi xảy ra, gửi cổng BHXH không thành công");
			}
		} else {
			objBHXH.KETQUA = "Không tìm thấy hồ sơ, gửi cổng BHXH không thành công";
			DlgUtil.showMsg("Không tìm thấy hồ sơ, gửi cổng BHXH không thành công");
		}
		var retLogKQG = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02", JSON.stringify(objBHXH));
		if (retLogKQG == -1) {
			DlgUtil.showMsg("Cập nhật trạng thái gửi cổng BHXH không thành công");
		}
	}
	function onFocus(fields) {
		var type = typeof fields;
		if (type.toLowerCase() == "string") {
			$("#" + id).focus();
			$("#" + id).css('background-color', 'yellow');
		} else if (type.toLowerCase() == "object") {
			var id = "";
			for (var i = 0; i < fields.length; i++) {
				id = fields[i];
				$("#" + id).css('background-color', 'yellow');
			}
			$("#" + id).focus();
		}
	}
	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
		}
	}
	// ham xu ly in phieu thu
	function stringToDate(date) {
		var parts = date.split("/");
		return new Date(parts[2], parts[1] - 1, parts[0]);
	}
	function stringToDateTime(date) {
		var parts = date.split("/");
		var tails = parts[2].split(" ");
		var times = tails[1].split(":");
		var ret = new Date(tails[0], parts[1] - 1, parts[0], times[0], times[1], times[2]);
		return ret;
	}
	function duyetKeToan(mode) { // L2PT-69331: them tham so mode
		// L2PT-557 start
		if (checkDuyetDLS() >= 1) {
			DlgUtil.showMsg("Có đơn thuốc chưa duyệt dược lâm sàng");
			return;
		}
		var objData_DUYET = new Object();
		objData_DUYET["TIEPNHANID"] = _tiepnhanid;
		// L2PT-4027 start
		objData_DUYET["NGAY"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		// L2PT-4027 end
		objData_DUYET["NGAYRAVIEN"] = _benhnhan.NGAYRAVIEN;
		objData_DUYET["DATRONVIEN"] = 0;
		objData_DUYET["SOLUONGQUYETTOAN"] = 0;
		objData_DUYET["LOAIDUYETBHYT"] = 0;
		objData_DUYET["KHOAID"] = _khoa_id; //L2PT-26804 
		objData_DUYET["PHONGID"] = _phong_id; //L2PT-26804 
		var _hcode = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.GET.CSKCB', _benhnhan.KHOAID);
		objData_DUYET["DUYET"] = mode; // L2PT-69331
		var _ngayDuyet = stringToDateTime(objData_DUYET.NGAY);
		if (!_benhnhan.NGAYRAVIEN) {
			DlgUtil.showMsg("Bệnh nhân chưa có thời gian ra viện");
			return false; // BVTM-3237 fix lỗi return
		}
		var _ngayRa = stringToDateTime(_benhnhan.NGAYRAVIEN);
		var VPI_THOIGIAN_DUYETKT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THOIGIAN_DUYETKT');
		if ((VPI_THOIGIAN_DUYETKT == 1 || (VPI_THOIGIAN_DUYETKT == 2 && _benhnhan.DOITUONGBENHNHANID == 1)) && _ngayDuyet < _ngayRa) { //L2PT-2875
			DlgUtil.showMsg("Không cho phép duyệt kế toán trước thời gian ra viện. Hãy duyệt sau " + _benhnhan.NGAYRAVIEN);
			return false;
		}
		var VPI_KIEMTRA_TYLE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_KIEMTRA_TYLE');
		if (VPI_KIEMTRA_TYLE == 0) {
			if (_khoa && _khoa.length > 0) {
				DlgUtil.showMsg("Có dịch vụ sai tỷ lệ tại " + _khoa);
				return;
			}
			// L2PT-3083 start: Tuyến 3 trở xuống cho phép trái tuyến
			var VPI_TUYEN_BV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_TUYEN_BV');
			if (parseInt(VPI_TUYEN_BV) < 3 && _benhnhan.LOAITIEPNHANID != 0 && _benhnhan.LYDO_VAOVIEN == 3 && _benhnhan.MUCHUONG > 0) {
				DlgUtil.showMsg("Bệnh nhân ngoại trú trái tuyến đang có mức hưởng , xem lại thông tin hành chính");
				return;
			}
			// L2PT-3083 end
		}
		var diff_m = diffDate(_benhnhan.NGAYRAVIEN, _benhnhan.NGAYTIEPNHAN, 'DD/MM/YYYY HH:mm', 'minutes');
		var VPI_CHAN_TGKB = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_CHAN_TGKB'); //L2PT-27570
		VPI_CHAN_TGKB = parseInt(VPI_CHAN_TGKB); //L2PT-27570
		if (VPI_CHAN_TGKB > 0 && diff_m < VPI_CHAN_TGKB && _benhnhan.LOAITIEPNHANID == 1) { //L2PT-27570: thêm cấu hình kiểm tra thời gian khám bệnh (<=5 phút)
			DlgUtil.showMsg("Thời gian ra viện - Thời gian tiếp nhận < 5 phút");
			return;
		}
		objData_DUYET.FLAG_DUYET_BH = "0"; //L2PT-23457
		obj_BH = new Object();
		obj_BH.TIEPNHANID = objData_DUYET.TIEPNHANID;
		obj_BH.NGAYDUYET = objData_DUYET.NGAY;
		var month = objData_DUYET.NGAY.split("/")[1];
		if (month <= 3)
			obj_BH.QUYDUYET = "1";
		else if (month <= 6)
			obj_BH.QUYDUYET = "2";
		else if (month <= 9)
			obj_BH.QUYDUYET = "3";
		else
			obj_BH.QUYDUYET = "4";
		obj_BH.HOSPITAL_CODE = _hcode;
		objData_DUYET.DATA_BH = obj_BH;
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI01T001.08", JSON.stringify(objData_DUYET));
		if (fl.substr(0, 2) == -1) {
			$('#txtNOIDUNGLOI').val(fl.slice(2));
			DlgUtil.showMsg("Duyệt không thành công");
		}
		// L2PT-26804 start
		else if (fl == -89) {
			DlgUtil.showMsg("Có nhiều hơn 1 phiếu duyệt kế toán");
		} else if (fl == -90) {
			DlgUtil.showMsg("Chưa thiết lập khoa phòng");
		} else if (fl == -91) {
			DlgUtil.showMsg("Lỗi lưu dữ liệu dịch vụ");
		}
		// L2PT-26804 end
		// L2PT-21881 start: ngày ra viện không được lớn hơn thời gian hiện tại
		else if (fl == -92) {
			DlgUtil.showMsg("Ngày ra viện không được lớn hơn thời gian hiện tại");
		}
		// L2PT-21881 ngày ra viện không được lớn hơn thời gian hiện tại
		// L2PT-11295 start : không được phân quyền gỡ duyệt
		else if (fl == -93) {
			DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kế toán, liên hệ với người quản trị");
		}
		// L2PT-11295 end : không được phân quyền gỡ duyệt
		else if (fl == -94) {
			DlgUtil.showMsg("Bệnh nhân chưa gỡ duyệt bảo hiểm");
		} else if (fl == -95) {
			DlgUtil.showMsg("Hồ sơ đã khóa");
		} else if (fl == -96) {
			DlgUtil.showMsg("Còn khoa/phòng chưa kết thúc");
		} else if (fl == -97) {
			DlgUtil.showMsg("Còn phòng khám chưa kết thúc");
		} else if (fl == -98) {
			DlgUtil.showMsg("Bệnh nhân chưa thanh toán viện phí");
		}
		// L2PT-3144 start
		else if (fl == -99) {
			DlgUtil.showMsg("Bệnh nhân chưa đóng bệnh án");
		} else if (fl == -88) {
			DlgUtil.showMsg("Bệnh nhân đã được duyệt kế toán trước đó");
		} else if (fl == -87) {
			DlgUtil.showMsg("Bệnh nhân chưa được duyệt kế toán hoặc đã gỡ duyệt");
		}
		// L2PT-3144 end 
		// L2PT-3081 start
		else if (fl == -86) {
			DlgUtil.showMsg("Công khám đầu tiên có tỷ lệ khác 100%");
		}
		// L2PT-3081 end
		// L2PT-4522 start
		else if (fl == -85) {
			DlgUtil.showMsg("Hãy gỡ duyệt bảo hiểm trước");
		}
		// L2PT-4522 end
		else {
			// L2PT-69331 start
			if (mode == "1") {
				thongBaoKetQua(fl, _hcode)
				_benhnhan.TRANGTHAITIEPNHAN_VP = 1;
				_benhnhan.TRANGTHAITIEPNHAN_BH = 1;
				var id = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
				_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
				$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KT', _icon);
				//setEnabled([], [ 'toolbarIdbtnThem', 'toolbarIdbtnHuy' ]);
			} else {
				if (fl == 0) {
					DlgUtil.showMsg("Hồ sơ của bệnh nhân đã khóa, không thể gỡ duyệt bảo hiểm");
					return;
				} else if (fl == -2) {
					DlgUtil.showMsg("Hết thời gian xử lý bảo hiểm của hồ sơ này, liên hệ với người quản trị");
					return;
				} else if (fl == -3) {
					DlgUtil.showMsg("Bạn không có quyền gỡ duyệt bảo hiểm hồ sơ này");
					return;
				} else if (fl == -4) {
					DlgUtil.showMsg("Hồ sơ chưa được duyệt bảo hiểm, không thể gỡ duyệt bảo hiểm");
					return;
				} else if (fl == -5) {
					DlgUtil.showMsg("Không lưu được log gỡ duyệt");
					return;
				} else if (fl != 1) {
					DlgUtil.showMsg(fl);
					return;
				}
				_benhnhan.TRANGTHAITIEPNHAN = 1;
				// neu go duyet kt & bh thanh cong thi cap nhat trang thai
				_benhnhan.TRANGTHAITIEPNHAN_VP = 0;
				_benhnhan.TRANGTHAITIEPNHAN_BH = 0;
				var id = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
				$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH', null); // L2PT-41410
				_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
				$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KT', _icon);
			}
			// L2PT-69331 start
		}
	}
	// L2PT-557 start
	function checkDuyetDLS() {
		var checkDLS = 0;
		if (_benhnhan.LOAITIEPNHANID == '1' && _benhnhan.TRANGTHAITIEPNHAN != 0) {
			checkDLS = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.CHECK.DUYETDLS", _tiepnhanid);
		}
		return checkDLS;
	}
	// L2PT-557 end
	var X = XLSX;
	var XW = {
		/* worker message */
		msg : 'xlsx',
		/* worker scripts */
		worker : './xlsxworker.js'
	};
	var process_wb = (function() {
		var to_json = function to_json(workbook, fS) {
			var result = {};
			var sheetNames = workbook.SheetNames;
			for (var i = 0; i < sheetNames.length; i++) {
				var sheetName = sheetNames[i];
				var roa = X.utils.sheet_to_json(workbook.Sheets[sheetName], {
					header : ""
				});
				if (fS) {
					return roa;
				} else {
					if (roa.length) {
						result[sheetName] = roa;
					}
				}
			};
			return result;
		};
		var to_csv = function to_csv(workbook) {
			var result = "";
			workbook.SheetNames.forEach(function(sheetName) {
				var csv = X.utils.sheet_to_csv(workbook.Sheets[sheetName]);
				csv = csv.replace(/(\r\n|\n|\r)/gm, ",");
				csv = csv.substring(0, csv.length - 1);
				/*
				csv = csv.split(",");
				if (csv && csv.length > 0) {
					var objCsv = [];
					csv.forEach(e => objCsv.push({ "MA" : e }));
					result = objCsv;
				}
				*/
				result = csv;
			});
			return result;
		};
		return function process_wb(wb) {
			var ds_ba = to_json(wb, true);
			loadGridDataBN(ds_ba);
		};
	})();
	var do_file = (function() {
		var rABS = typeof FileReader !== "undefined" && (FileReader.prototype || {}).readAsBinaryString;
		var use_worker = typeof Worker !== 'undefined';
		var xw = function xw(data, cb) {
			var worker = new Worker(XW.worker);
			worker.onmessage = function(e) {
				switch (e.data.t) {
					case 'ready':
					break;
					case 'e':
						console.error(e.data.d);
					break;
					case XW.msg:
						cb(JSON.parse(e.data.d));
					break;
				}
			};
			worker.postMessage({
				d : data,
				b : rABS ? 'binary' : 'array'
			});
		};
		return function do_file(files) {
			rABS = true;
			use_worker = false;
			var f = files[0];
			var reader = new FileReader();
			reader.onload = function(e) {
				if (typeof console !== 'undefined')
					console.log("onload", new Date(), rABS, use_worker);
				var data = e.target.result;
				if (!rABS)
					data = new Uint8Array(data);
				if (use_worker)
					xw(data, process_wb);
				else
					process_wb(X.read(data, {
						type : rABS ? 'binary' : 'array'
					}));
			};
			if (rABS)
				reader.readAsBinaryString(f);
			else
				reader.readAsArrayBuffer(f);
		};
	})();
	(function() {
		var drop = document.getElementById('drop');
		if (!drop.addEventListener)
			return;
		function handleDrop(e) {
			e.stopPropagation();
			e.preventDefault();
			do_file(e.dataTransfer.files);
		}
		function handleDragover(e) {
			e.stopPropagation();
			e.preventDefault();
			e.dataTransfer.dropEffect = 'copy';
		}
		drop.addEventListener('dragenter', handleDragover, false);
		drop.addEventListener('dragover', handleDragover, false);
		drop.addEventListener('drop', handleDrop, false);
	})();
	// (function() {
	// var xlf = document.getElementById('xlf');
	// if(!xlf.addEventListener) return;
	// function handleFile(e) { do_file(e.target.files); }
	// xlf.addEventListener('change', handleFile, false);
	// })();
}
