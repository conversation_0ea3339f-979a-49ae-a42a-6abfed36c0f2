<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../plugin/jquery.textboxLookup.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.storageapi.js" ></script>


<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">       
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>

<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT02K020_ThuocConSuDung.js?v=20170527"></script>


<div id="divMain" style="min-height: 400px !important;"> 
    <!--  <legend>THUỐC CÒN SỬ DỤNG</legend>  -->   
     <div class=" form-inline">
     	<div class="col-md-12 low-padding mgt-5">
     		<table id="grdThuoc"></table><div id="pager_grdThuoc"></div>
     	</div>     	
     </div>  
     <div class=" form-inline">
     	<div class="col-md-12 low-padding mgb10" style="text-align: center;">
     		<button id="btnClose" class="btn btn-sm btn-primary">
     			<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
     	</div>     	
     </div>
</div>
<script>
	//var uuid = '{uuid}';
	var userInfo=CommonUtil.decode('{userData}');
	initRest(userInfo.UUID,"/vnpthis");
	var _opt = new Object();
	//truyen cac tham so tu ngoai vao
	_opt.benhnhanId = 1;
	
	var mode = '{showMode}';
	var objVar;
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		objVar=EventUtil.getVar("dlgVar");
		_opt.benhnhanId = objVar.benhnhanId;
	}
	var th = new NGT02K020_ThuocConSuDung(_opt);
	th.load();
</script>