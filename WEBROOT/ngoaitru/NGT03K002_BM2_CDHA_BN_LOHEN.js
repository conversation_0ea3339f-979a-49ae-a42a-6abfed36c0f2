function danhsachCDHABN(opt) {
	_gridId = "grdBenhNhan";
	this.load = doLoad;
	_gridHeader = "ID,ID1,40,0,t,l;" + "<PERSON><PERSON><PERSON> bệnh nhân,TENBENHNHAN,200,0,f,l;"
			+ "<PERSON>ã bệnh nhân,MABENHNHAN,80,0,f,l;"
			+ "Mã BHYT,MA_BHYT,100,0,f,l;" + "STT,SOTHUTU,70,0,f,l;"
			+ "<PERSON><PERSON><PERSON>,LANGOIKHAM,80,0,f,l;" + "HOSOBENHANID,HOSOBENHANID,80,0,f,l";

	function doLoad() {
		_initControl();
		_bindEvent();
	}

	function _initControl() {
		GridUtil.init(_gridId, "100%", "250px", "", false, _gridHeader, false);
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			"value" : opt.phongid
		});
		_sql_par.push({
			"name" : "[1]",
			"value" : opt.ngaybatdau
		});
		_sql_par.push({
			"name" : "[2]",
			"value" : opt.ngaykethuc
		});
		GridUtil.loadGridBySqlPage(_gridId, "CDHA.BNLOHEN.BM2", _sql_par);
	}

	function _bindEvent() {
		GridUtil.setGridParam(_gridId, {
			onSelectRow : function(index, selected) {
				GridUtil.unmarkAll(_gridId);
				GridUtil.markRow(_gridId, index);
			},
			ondblClickRow : function(rowId, iRow, iCol, e) {
				var rowData = $('#' + _gridId).jqGrid('getRowData', rowId);
				if (rowData != null) {
					EventUtil.raiseEvent("evt_cdha_bnlohen", {
						stt : rowData.SOTHUTU,
						tenbenhnhan : rowData.TENBENHNHAN,
						HOSOBENHANID : rowData.HOSOBENHANID,
						phongid : opt.phongid
					});
				}
			}
		});
	}
}
