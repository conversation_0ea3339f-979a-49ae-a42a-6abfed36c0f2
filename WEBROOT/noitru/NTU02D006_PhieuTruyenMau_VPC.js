/*
 Mã màn hình  : NTU02D006
 File mã nguồn : NTU02D006_PhieuTruyenMau.js
 Mục đích  : <PERSON>ia<PERSON> diện màn hình
 	+ <PERSON>hập thông tin bệnh án
 	+ <PERSON>hập thông tin hỏi bệnh
 Tham số vào : 
 	truyenmau_id 	: ID mẫu bệnh phẩm
 	khambenh_id		: ID khám bệnh
	
 <PERSON>ờ<PERSON> lập trình	 <PERSON> cập nhật  	<PERSON>hi chú
 linhvv				 1/9/2016			Sửa bổ xung tính năng 
 */
function phieuTruyenMau_K74(_opts) {
	var _user_id;
	var _company_id = _opts.hospital_id;
	var _khambenh_id = _opts.khambenh_id;	
	var _thoigianvaovien = moment(_opts.thoigianvaovien,'DD/MM/YYYY HH:mm:ss')
    
    
	var _truyenmau_id = _opts.truyenmau_id;
	var _maubenhphamid = _opts.maubenhpham_id;
	var _dept_id = _opts.dept_id;
	var modeDisplay = _opts.modeDisplay;
	var sophieu = _opts.sophieu;
	var _action = "";
	var _col="Mã thuốc,MA,30,0,f,l;Tên thuốc,TEN,70,0,f,l";
	//var _sql="select icd10code,icd10name from dmc_icd where upper(icd10code) like upper('%[FILTER]%')";
	var sql_par=[];
	var that=this;
	this.load=doLoad;
	var _isKyCA = false;
	var _isXnKyCA = false;
	var _rptCode = 'DUC004_PHIEUTRUYENMAU_A4';
	var _gridDSCT="grdDSThuocCT"; 
	var _isFirt = false;
	var _dt_ngungtruyen ="";
	this.onChangeDate = _onChangeDate;
	var _quydoi = 18;
	var _row = new Object();
	
	var _gridHeaderDetail="THUOCVATTUID,THUOCVATTUID,0,0,t,l;Thời gian,NGAYDO,250,0,f,l;Tốc độ truyền,TOCDO_GIOT_NB,100,0,f,l;Số lượng máu còn,SOLUONG_CONLAI,130,0,f,l;Sắc da niêm mạc,SACDA_NIEMMAC,130,0,f,l;Nhịp thở,NHIP_THO,100,0,f,l;Mạch,MACH,100,0,f,l;Huyết áp cao,HUYETAP_HIGHT,100,0,f,l;Huyết áp thấp,HUYETAP_LOW,100,0,f,l;Thân nhiệt,THANNHIET,100,0,f,l;Diễn biến khác,DIENBIENKHAC,200,0,f,l; ,ACTION,30,d,f,l";

	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		
//		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","NTU_TACH_HHMD_TRUYENMAU");
//		if(data_ar != null && data_ar.length > 0){
//			if(data_ar[0].NTU_TACH_HHMD_TRUYENMAU == '1'){
//				_rptCode = 'DUC004_HHMD_TRUYENMAU_A4';
//			}
//		}
		if(modeDisplay != '1' && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_TACH_HHMD_TRUYENMAU') == '1'){
			_rptCode = 'DUC004_HHMD_TRUYENMAU_A4';
		}
		//START -- L2HOTRO-11760
		var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		var check_par=[];
	   	check_par.push({"name":"[0]","value":"0.01"});
		var _sys_date_add1 = jsonrpc.AjaxJson.getOneValue("COM.SYSDATE_P", check_par);
		
		$("#txtTGBATDAU").val(_sys_date_add1);
		$("#txtTGKETTHUC").val(_sys_date_add1);
		$("#txtNGAYTAOPHIEU").val(_sys_date_add1);
		$('#txtNGAYTAO').val(sysdate);
		$('#cboPU_CHEO').val("1");
		$('#cboPU_SINHVAT').val("1");
		
		GridUtil.init(_gridDSCT,"100%","180","Danh sách chi tiết truyền máu ",false,_gridHeaderDetail, false, { rowNum: 100,rowList: [100, 200, 300]});
		$("#grdDSThuocCT")[0].toggleToolbar(); 
		
		
		sql_par.push({"name":"[0]","value":_dept_id});
		ComboUtil.getComboTag("cboBS_CHIDINH","NTU02D006.02",sql_par, "", {value:'',text:'-- Chọn Bác sĩ--'},"sql","", false);
		ComboUtil.getComboTag("cboYT_CHIDINH","NTU02D006.03",sql_par, "", {value:'',text:'-- Chọn y tá--'},"sql","", false);
		
		//ComboUtil.getComboTag("cboMASO_CHEPHAM_MAU","NTU02D006.THUOC",sql_par, "", {value:'',text:'-- Chọn thuốc--'},"sql","", false);
		ComboUtil.initComboGrid("txtTKCHEPHAMMAU","NTU02D006.THUOC",[], "600px",_col, function(event, ui){
			var _ui = ui.item;
			   
			$("#cboCHEPHAMMAU").empty();
			$("#cboCHEPHAMMAU").append('<option value="'+_ui.MA+'">'+_ui.TEN+'</option>');
		});
	
		this.validator = new DataValidator("divMain");
		bindEvent();
		
		
		if(modeDisplay == '1'){
			$('#XNHH').addClass("disabledbutton");
		}else{
			$('#TPTM').addClass("disabledbutton");
		}
		
		if(_opts.modechitiet == '0'){
			$('#divTMCT').addClass("disabledbutton");
		}
		if(_opts.modechitiet == '1'){
			$('#divTPTM').addClass("disabledbutton");
		}
		
		
		addNumberOnly($("#txtLANTRUYEN"));
		addNumberOnly($("#txtSOLUONGMAU_THUCTE"));
		addNumberOnly($("#txtSOLUONGMAU"));
		addNumberOnly($("#txtDONVIMAU"));
		if(_truyenmau_id != null && _truyenmau_id != ''){
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02.06.TTPTM.VP", _truyenmau_id,[]);
	    	if(data_ar != null){
	    		_row = data_ar[0];
	    		FormUtil.setObjectToForm("","",_row);
	    		
	    		if (_row.MLH =='1') {
					$("#lblTocdo").text("Tốc độ (ml/h)");
					//$("#chkMLH").prop('checked', true);
				}
	    		$('#chkMLH').change();
	    		
				if(!_row.PU_CHEO)
		    		$('#cboPU_CHEO').val("");
				if(!_row.PU_SINHVAT)
		    		$('#cboPU_SINHVAT').val("");
	    		if(!_row.SL_TRUYEN_TT)
	    			$("#txtSL_TRUYEN_TT").val(_row.SOLUONGMAU);
	    		if(_row.BASI_ID != null)
	    		$("#cboBS_CHIDINH").val(FormUtil.unescape(_row.BASI_ID));
	    		if(_row.THOIGIANBATDAU != null)
	    		$("#txtTGBATDAU").val(FormUtil.unescape(_row.THOIGIANBATDAU));
	    		if(_row.NGAYTAOPHIEU != null)
	    		$("#txtNGAYTAOPHIEU").val(FormUtil.unescape(_row.NGAYTAOPHIEU));
	    		
	    		$('#txtSOPHIEU').val(_row.SOPHIEU);
	    		
	    		if(_row.CHEPHAMMAU != '' && _row.TEN_CHEPHAM_MAU != ''){
	    			$("#cboCHEPHAMMAU").empty();
	    			$("#cboCHEPHAMMAU").append('<option value="'+ _row.CHEPHAMMAU +'">'+_row.TEN_CHEPHAM_MAU+'</option>');
	    		}
				if(!_row.LANTRUYEN && modeDisplay != '1')
					$('#txtLANTRUYEN').val(_row.LANTRUYEN_COUNT);
	    	}
	    	$('#btnCapnhat1').show();
	    	$('#btnCapnhat2').show();
			$('#btnKySo2').show();
			$('#btnHuyKy2').show();
			$('#btnInKySo2').show();
	    	$('#btnSave').hide();
	    	$('#btnLuu').hide();
			var sql_par1 = [];
			sql_par1.push({
				"name" : "[0]",
				"value" : _truyenmau_id 
			});
			GridUtil.loadGridBySqlPage(_gridDSCT, "NT.024.M_V2", sql_par1);
		}
		$('#cboPU_CHEO').change();
	
	}
	function bindEvent(){
		$("#btnHuy").on("click",function(e){
			if(modeDisplay == '1'){
				EventUtil.raiseEvent("assignTruyenMau_cancel");	
			}else{
				parent.DlgUtil.close("divDlgPTruyenMau");
			}
		});
		$("#btnClose").on("click",function(e){
			if(modeDisplay == '1'){
				EventUtil.raiseEvent("assignTruyenMau_cancel");	
			}else{
				parent.DlgUtil.close("divDlgPTruyenMau");
			}
					
		});
		$("#btnLuu").on("click",function(e){
			doIns();
		});
		$("#btnSave").on("click",function(e){
			doIns();
		});
		$("#btnMoi").on("click", function(e) {
			FormUtil.clearForm("XNHH","");		
			
			$('#txtNGAYTAO').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#txtNGAYLAYMAU').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#txtHANDUNG').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		});
		$("#btnRefresh").on("click", function(e) {
			FormUtil.clearForm("TPTM","");		
			
			$("#txtNGAYTAOPHIEU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
			$("#txtTGBATDAU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		});
		
		$("#btnCapnhat1").on("click", function(e) {
			doIns();
		});

		$("#btnCapnhat2").on("click", function(e) {
			doIns();
		});
		$("#btnInphieu1").on("click", function(e) {
			doPrint('1');
		});
		$("#btnInphieu2").on("click", function(e) {
			doPrint('2');
		});

		$("#btnKySo").on("click", function (e) {
			_isKyCA = true;
			_isXnKyCA = false;
			doIns();
		});
		$("#btnHuyKy").on("click", function (e) {
			_caRpt('2');
		});
		$("#btnInKySo").on("click", function (e) {
			_caRpt('0');
		});
		$("#btnKySo2").on("click", function (e) {
			_isKyCA = true;
			_isXnKyCA = true;
			doIns();
		});
		$("#btnHuyKy2").on("click", function (e) {
			_caRpt('2');
		});
		$("#btnInKySo2").on("click", function (e) {
			_caRpt('0');
		});

		$("#grdDSThuocCT").bind("CustomAction", function(e,act,rid){ 
			if(act == 'del'){
				$("#"+_gridDSCT).jqGrid('delRowData',rid);
				//tinhsoluongmau();
			}
			return false;
		});
		$("#cboPU_CHEO" ).change(function () {
			if($("#cboPU_CHEO").val() !='2'){
				$('#divTMCT').css('display','');
				
			}else{
				$('#divTMCT').css('display','none');
			}
		});
		
		$("#chkMLH").change(function() {
			if (this.checked) {
				$("#lblTocdo").text("Tốc độ (ml/h)");
			} else {
				$("#lblTocdo").text("Tốc độ (giọt / phút)");
			}
			if ($('#chkMLH').prop("checked") == true) {
				_quydoi = 60;
			}else{
				_quydoi =18;
			}
		});
		
		
		$("#txtNGUNGTRUYEN" ).change(function () {
			var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
			if(jsonGridData.length > 0){
				var tgbd = moment(jsonGridData[jsonGridData.length-1].NGAYDO, 'DD/MM/YYYY HH:mm:ss');
				var tgkt = moment($("#txtNGUNGTRUYEN").val().trim(), 'DD/MM/YYYY HH:mm:ss');
				var diffHour = tgkt.diff(tgbd, 'minute');
				if (diffHour) {
					$("#txtSL_TRUYEN_TT").val($("#cboSOLUONGMAU").val() - (jsonGridData[jsonGridData.length-1].SOLUONG_CONLAI - Math.round((diffHour * jsonGridData[jsonGridData.length-1].TOCDO_GIOT_NB)/_quydoi)));
				}
			}

		});
		$("#txtSL_TRUYEN_TT" ).change(function () {
			//$("#txtNGUNGTRUYEN").val(_dt_ngungtruyen)
			var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
			if(jsonGridData.length > 0){
				if($("#txtSL_TRUYEN_TT" ).val() > $("#cboSOLUONGMAU").val()){
					DlgUtil.showMsg("Số lượng máu thực tế đã truyền phải nhỏ hơn tổng số lượng máu ");								
					$('#txtSL_TRUYEN_TT').focus();
				}
				var min = Math.round(($("#cboSOLUONGMAU").val() - $("#txtSL_TRUYEN_TT" ).val())*_quydoi/jsonGridData[jsonGridData.length-1].TOCDO_GIOT_NB)
				var sql_par1 = [];
				sql_par1.push({"name":"[0]", value:_dt_ngungtruyen});
				sql_par1.push({"name":"[1]", value:min*-1});
				var time_kt = jsonrpc.AjaxJson.getOneValue('ADD.MIN.DATE', sql_par1);
				$("#txtNGUNGTRUYEN").val(time_kt)

			}
			
		});
		$("#grdDSThuocCT").bind("jqGridLoadComplete", function (e, rowid, orgClickEvent) {
			tinhsoluongmau();
		});
		function tinhsoluongmau(){ 
			var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
			var soluongmau = $("#cboSOLUONGMAU").val();
			//jQuery("#grdDSThuocCT").jqGrid("clearGridData");			
			for(var i = 0; i < jsonGridData.length; i++){
				if(i == 0){
					
//					var tgbd = moment($('#txtTGBATDAU').val(), 'DD/MM/YYYY HH:mm:ss');
//					var tgkt = moment(jsonGridData[i].NGAYDO, 'DD/MM/YYYY HH:mm:ss');
//					var diffHour = tgkt.diff(tgbd, 'minute');
//					if (diffHour) {
//						jsonGridData[i].SOLUONG_CONLAI =soluongmau -  Math.round((diffHour * jsonGridData[i].TOCDO_GIOT_NB)/_quydoi);
//						$("#grdDSThuocCT").jqGrid('setCell',i+1,'SOLUONG_CONLAI',soluongmau-Math.round((diffHour * jsonGridData[i].TOCDO_GIOT_NB)/_quydoi));
//						//jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i]);
//					}else{
//						jsonGridData[i].SOLUONG_CONLAI = soluongmau;
//						$("#grdDSThuocCT").jqGrid('setCell',i+1,'SOLUONG_CONLAI',soluongmau);
//					}
					
					jsonGridData[i].SOLUONG_CONLAI = soluongmau;
					$("#grdDSThuocCT").jqGrid('setCell',i+1,'SOLUONG_CONLAI',soluongmau);
					
				}
				if(i > 0 ){
					var tgbd = moment(jsonGridData[i-1].NGAYDO, 'DD/MM/YYYY HH:mm:ss');
					var tgkt = moment(jsonGridData[i].NGAYDO, 'DD/MM/YYYY HH:mm:ss');
					var diffHour = tgkt.diff(tgbd, 'minute');
					if (diffHour) {
						jsonGridData[i].SOLUONG_CONLAI = jsonGridData[i-1].SOLUONG_CONLAI - Math.round((diffHour * jsonGridData[i].TOCDO_GIOT_NB)/_quydoi);
						$("#grdDSThuocCT").jqGrid('setCell',i+1,'SOLUONG_CONLAI',jsonGridData[i-1].SOLUONG_CONLAI -Math.round((diffHour * jsonGridData[i].TOCDO_GIOT_NB)/_quydoi));
						//jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i]);
					}
				}
				if(i+1 == jsonGridData.length){
					var min = Math.round(jsonGridData[i].SOLUONG_CONLAI*_quydoi/jsonGridData[i].TOCDO_GIOT_NB)
					var sql_par1 = [];
					sql_par1.push({"name":"[0]", value:jsonGridData[i].NGAYDO});
					sql_par1.push({"name":"[1]", value:min});
					var time_kt = jsonrpc.AjaxJson.getOneValue('ADD.MIN.DATE', sql_par1);
					_dt_ngungtruyen = time_kt;
					if(_isFirt) {
						$('#txtNGUNGTRUYEN').val(time_kt);
						$('#txtSL_TRUYEN_TT').val(soluongmau);
					} 
						
					_isFirt = true;
				}
				
				//var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
			}
		}
		
		$("#btnTHEMGRID").on("click",function(e){
			if (checkKhongThemChiTietPTMDaKy()) {
				return false;
			}
			var valid = that.validator.validateForm("TPTM");
			_isFirt = true;
			if(valid){
				if($("#txtNGAYDO").val().trim() && $("#txtTGBATDAU").val().trim() 
					&& $("#txtNGAYDO").val().trim() != $("#txtTGBATDAU").val().trim()
					&& compareDate($("#txtNGAYDO").val().trim() ,$("#txtTGBATDAU").val().trim(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Thời gian không được nhỏ hơn thời gian bắt đầu truyền!");								
					$('#txtNGAYDO').focus();
					return false;	
				}
				var json =  jQuery("#grdDSThuocCT").jqGrid('getRowData');
//				if(json.length == 0 &&  $("#txtNGAYDO").val().trim() != $("#txtTGBATDAU").val().trim()){
//					DlgUtil.showMsg("Thời gian truyền máu chi tiết lần đầu phải bằng thời gian bắt đầu truyền!");								
//					$('#txtNGAYDO').focus();
//					return false;	
//				}
				for(var i = 0; i<json.length;i++){
					if(compareDate( $("#txtNGAYDO").val().trim(),json[i].NGAYDO,'DD/MM/YYYY HH:mm:ss')){
						DlgUtil.showMsg("Thời gian không được nhỏ hơn thời gian của các phiếu trước đã tạo!");								
						$('#txtNGAYDO').focus();
						return;
					}
				}
				
				if(json.length > 0){
					var tgbd = moment(json[json.length-1].NGAYDO, 'DD/MM/YYYY HH:mm:ss');
					var tgkt = moment($('#txtNGAYDO').val(), 'DD/MM/YYYY HH:mm:ss');
					var diffHour = tgkt.diff(tgbd, 'minute');
					if (diffHour) {
						var soluongconlai = json[json.length-1].SOLUONG_CONLAI - Math.round((diffHour * $('#txtTOCDO_GIOT').val())/_quydoi);
						if(soluongconlai < 0){
							DlgUtil.showMsg("Tốc độ và thời gian nhập vượt quá với số lượng máu còn lại!");								
							$('#txtTOCDO_GIOT').focus();
							return;
						}
					}
				}

				var _objDrug = new Object();
				_objDrug.TOCDO_GIOT_NB 	= $('#txtTOCDO_GIOT').val();	;
				_objDrug.MACH		= $('#txtMACH').val();	;
				_objDrug.HUYETAP_LOW 			= $('#txtHUYETAP_LOW').val();			    	
				_objDrug.HUYETAP_HIGHT 		=	$('#txtHUYETAP_HIGHT').val();	
				_objDrug.NGAYDO 		= $('#txtNGAYDO').val();	
				_objDrug.SACDA_NIEMMAC 		=	$('#txtSACDA_NIEMMAC').val();	
				_objDrug.NHIP_THO 		= $('#txtNHIP_THO').val();
				_objDrug.THANNHIET 		= $('#txtTHANNHIET').val();
				_objDrug.DIENBIENKHAC 		= $('#txtDIENBIENKHAC').val();	
				
				var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
				jQuery("#grdDSThuocCT").jqGrid('addRowData', jsonGridData.length +1, _objDrug, "last");
				tinhsoluongmau();
				
				$("#txtTOCDO_GIOT").val("");
				$("#txtMACH").val("");
				$("#txtHUYETAP_LOW").val("");
				$("#txtHUYETAP_HIGHT").val("");
				$("#txtNGAYDO").val("");
				$("#txtSACDA_NIEMMAC").val("");
				$("#txtNHIP_THO").val("");
				$("#txtTHANNHIET").val("");
				$("#txtDIENBIENKHAC").val("");
			}
			
		});
//		$(document).ready(function() {
//			_isFirt = false;
//		});
		
	}
	
	function doIns(){
		if (checkKhongThemChiTietPTMDaKy()) {
			return false;
		}
		var objData = new Object();
		FormUtil.setFormToObject("","",objData);
		
//		if ($('#chkMLH').prop("checked") == true) {
//			objData.MLH == '1'
//		}else{
//			objData.MLH == '0'
//		}
		
		var valid = false;
		if(modeDisplay == '0'){
			valid = true;
			if(isEmpty($("#cboSOLUONGMAU").val())){
				DlgUtil.showMsg("Bạn chưa nhập số lượng máu");
				$('#cboSOLUONGMAU').trigger("focus");
				valid = false;
			}
			if(isEmpty($("#txtDONVIMAU").val())){
				DlgUtil.showMsg("Bạn chưa nhập đơn vị tính");
				$('#txtDONVIMAU').trigger("focus");
				valid = false;
			}
			//valid = that.validator.validateForm();
		}else{
			valid = true;
//			if(isEmpty($("#cboPU_CHEO").val())){
//				DlgUtil.showMsg("Bạn chưa nhập phản ứng chéo tại giường");
//				$('#cboPU_CHEO').trigger("focus");
//				valid = false;
//			}
//			if(isEmpty($("#cboPU_SINHVAT").val())){
//				DlgUtil.showMsg("Bạn chưa nhập phản ứng sinh vật");
//				$('#cboPU_SINHVAT').trigger("focus");
//				valid = false;
//			}
			
//	    	if(compareDate(_dt_ngungtruyen,$("#txtNGUNGTRUYEN").val().trim(),'DD/MM/YYYY HH:mm:ss')){
//			    DlgUtil.showMsg("Thời gian ngừng truyền không được lớn hơn ngày: " + _dt_ngungtruyen);								
//				$('#txtNGUNGTRUYEN').focus();
//				valid = false;
//			}
	    	if($("#txtNGUNGTRUYEN").val() && _dt_ngungtruyen && 
	    			_dt_ngungtruyen != $("#txtNGUNGTRUYEN").val() &&
	    			compareDate(_dt_ngungtruyen,$("#txtNGUNGTRUYEN").val().trim(),'DD/MM/YYYY HH:mm:ss')){
			    DlgUtil.showMsg("Thời gian ngừng truyền không được lớn hơn ngày: " + _dt_ngungtruyen);								
				$('#txtNGUNGTRUYEN').focus();
				valid = false;
			}
	    	var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
	    	if( jsonGridData.length > 0 &&
	    			compareDate($("#txtNGUNGTRUYEN").val().trim(),jsonGridData[jsonGridData.length-1].NGAYDO,'DD/MM/YYYY HH:mm:ss')){
			    DlgUtil.showMsg("Thời gian ngừng truyền không được nhỏ hơn ngày: " + jsonGridData[jsonGridData.length-1].NGAYDO);								
				$('#txtNGUNGTRUYEN').focus();
				valid = false;
			}
	    	if($("#txtNGUNGTRUYEN").val() > $("#txtNGUNGTRUYEN").val()){
	    		DlgUtil.showMsg("Số lượng máu thực tế đã truyền không được lớn hơn tổng số lượng truyền!");								
				$('#txtSL_TRUYEN_TT').focus();
				valid = false;
	    	}
	    		
		}
		
		if(valid){
			var _TGBATDAU;
			var _NGAYTAO;
			var b_ngayhientai =jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			
			if(modeDisplay == '1'){
				var _TGBATDAU = moment($("#txtTGBATDAU").val().trim(),'DD/MM/YYYY HH:mm:ss');
		    	var _NGAYTAO = moment($("#txtNGAYTAO").val().trim(),'DD/MM/YYYY HH:mm:ss');
		    	if(compareDate(b_ngayhientai,$("#txtNGAYTAO").val().trim(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Ngày tạo phiếu không được lớn hơn ngày hiện tại!");								
					$('#txtNGAYTAO').focus();
					return false;	
				}
				if($("#txtNGAYPHATMAU").val().trim() && $("#txtTGBATDAU").val().trim() 
					&& compareDate($("#txtTGBATDAU").val().trim() ,$("#txtNGAYPHATMAU").val().trim(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Thời gian bắt đầu truyền không được nhỏ hơn ngày phát máu!");								
					$('#txtTGBATDAU').focus();
					return false;	
				}
				
		    	
			}else{
		    	var _NGAYTAO = moment($("#txtNGAYTAO").val().trim(),'DD/MM/YYYY HH:mm:ss');
		    	if(compareDate(b_ngayhientai,$("#txtNGAYTAO").val().trim(),'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Ngày giờ tạo phiếu không được lớn hơn ngày hiện tại!");								
					$('#txtNGAYTAO').focus();
					return false;
				}
			}
			
			if(_thoigianvaovien > _NGAYTAO){
				$("#txtNGAYTAO").focus();
				DlgUtil.showMsg("Ngày tạo phải lớn hơn thời gian vào viện!");
				return false;
			}
			else{
				if(_truyenmau_id == null || _truyenmau_id == '') _truyenmau_id = -1;
				objData.MAUBENHPHAMID = _maubenhphamid;
				objData.MODE_DISPLAY = modeDisplay+'';
				objData.TRUYENMAUCT = jQuery("#grdDSThuocCT").jqGrid('getRowData');
				_par = [JSON.stringify(objData),_khambenh_id, _truyenmau_id];
				var ret=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.THEM.PTM.VP",_par.join('$'));

				if (ret == '-1') {
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
					return false;
				} else {
					if (_truyenmau_id == '-1') {
						if (_isKyCA) {
							_truyenmau_id = ret;
							_caRpt('1');
						} else {
							if (modeDisplay == '0') {
								parent.DlgUtil.showMsg("Thêm phiếu truyền máu thành công", undefined, 2000);
								parent.DlgUtil.close("divDlgPTruyenMau");
							} else {
								_msg = "Thêm phiếu truyền máu thành công!";
								var evFunc = EventUtil.getEvent("assignSevice_SaveTruyenMau");
								evFunc({msg: _msg});
							}
							$("#btnLuu").prop("disabled", true);
							$("#btnSave").prop("disabled", true);
							$("#btnCapnhat1").prop("disabled", true);
							$("#btnCapnhat2").prop("disabled", true);
							$("#btnKySo2").prop("disabled", true);
							$("#btnHuyKy2").prop("disabled", true);
							$("#btnInKySo2").prop("disabled", true);
						}

					} else {
						if (_isKyCA) {
							_caRpt('1');
						} else {
							if (modeDisplay == '0') {
								DlgUtil.showMsg("Cập nhật phiếu truyền máu thành công!");
							} else {
								_msg = "Cập nhật phiếu truyền máu thành công!";
								var evFunc = EventUtil.getEvent("assignSevice_SaveTruyenMau");
								evFunc({msg: _msg});
							}
						}
					}
				}
			}
		}
	}
	function isEmpty(value){
		return (value == null || value.trim().length === 0);
	}
	function doPrint(type){
		if(_truyenmau_id == null || _truyenmau_id == ''){
			return DlgUtil.showMsg("Chưa có phiếu truyền máu!");
		}
		var par = [ {
			name : 'truyenmauid',
			type : 'String',
			value : _truyenmau_id
		}, {
			name : 'phieuso',
			type : 'String',
			value : type
		}];
		openReport('window', _rptCode, "pdf", par);
	}
	
	
	$('.input-sm').keydown(function (e) {
	     if (e.which === 13) {
	         var index = $('.input-sm').index(this) + 1;
	         $('.input-sm').eq(index).focus();
	     }
	 });
	
	function addNumberOnly(element){
		$(element).keypress(function (e) {
		     if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
		        return false;
		    }
		});
	}

	function checkKhongThemChiTietPTMDaKy() {
		const isDisableEdit = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_PTM_KHONG_THEM_CHITIET_DA_KYSO') == '1';
		const kySoPTMType = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_KYCA_TRUYENMAU_TYPE') == '1';

		if (isDisableEdit) {
			const flagCA = _row.FLAG_CA;
			if (kySoPTMType) {
				if (flagCA && flagCA == '3') {
					DlgUtil.showMsg("Phiếu đã ký số, bạn cần hủy ký số trước khi sửa, xóa.");
					return true;
				}
			} else if (flagCA && flagCA == '1')  {
				DlgUtil.showMsg("Phiếu đã ký số, bạn cần hủy ký số trước khi sửa, xóa.");
				return true;
			}
		}
		return false;
	}

	function _caRpt(signType) {
		_isKyCA = false;
		sql_par = [];
		sql_par.push({"name": "[0]", "value": _truyenmau_id});
		var _dataCa = jsonrpc.AjaxJson.ajaxExecuteQueryO('CA.TM.GET.HSBAID', sql_par);
		var rowCA = JSON.parse(_dataCa);
		var _par = [];
		_par = [{
			name: 'hosobenhanid',
			type: 'String',
			value: rowCA[0].HOSOBENHANID
		}, {
			name: 'truyenmauid',
			type: 'String',
			value: _truyenmau_id
		}, {
			name: 'RPT_CODE',
			type: 'String',
			value: _rptCode
		}];
		//ky
		if (signType == '0') {
			CommonUtil.openReportGetCA2(_par, false);
		} else {
			CommonUtil.kyCA(_par, signType, true, true);
			EventUtil.setEvent("eventKyCA", function (e) {
				var _code = e.res.split("|")[0];
				var _msg = e.res.split("|")[1];
				DlgUtil.showMsg(_msg);
				if (signType == '1' && (_code == '0' || _code == '7' || _code == '8')) {
					var _flagCA = '1';
					if (_isXnKyCA) {
						_flagCA = '3';
					}
					//đánh dấu đã ký số
					var obj = new Object();
					obj.TABLENAME = 'kbh_truyenmau';
					obj.COLUMNAME = 'truyenmau_id';
					obj.COLUMDATA = _truyenmau_id;
					obj.SINGTYPE = _flagCA;
					jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(obj));
				}
			});
		}
	}
	function _onChangeDate(a, b) {
//		var jsonGridData = jQuery("#grdDSThuocCT").jqGrid('getRowData');
//		if(jsonGridData.length > 0){
//			var tgbd = moment(jsonGridData[jsonGridData.length-1].NGAYDO, 'DD/MM/YYYY HH:mm:ss');
//			var tgkt = moment($("#txtNGUNGTRUYEN").val().trim(), 'DD/MM/YYYY HH:mm:ss');
//			var diffHour = tgkt.diff(tgbd, 'minute');
//			if (diffHour) {
//				$("#txtSL_TRUYEN_TT").val($("#cboSOLUONGMAU").val() - (jsonGridData[jsonGridData.length-1].SOLUONG_CONLAI - Math.round((diffHour * jsonGridData[jsonGridData.length-1].TOCDO_GIOT_NB)/_quydoi)));
//				
//				//jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i]);
//			}
//		}
		if($("#txtNGUNGTRUYEN").val() && _dt_ngungtruyen && 
    			_dt_ngungtruyen != $("#txtNGUNGTRUYEN").val() &&
    			compareDate(_dt_ngungtruyen,$("#txtNGUNGTRUYEN").val().trim(),'DD/MM/YYYY HH:mm:ss')){
		    DlgUtil.showMsg("Thời gian ngừng truyền không được lớn hơn ngày: " + _dt_ngungtruyen);								

		}
	  }
}