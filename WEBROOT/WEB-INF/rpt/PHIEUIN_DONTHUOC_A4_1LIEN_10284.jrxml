<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEUIN_DONTHUOC_A4_1LIEN_10284" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="569" leftMargin="13" rightMargin="13" topMargin="12" bottomMargin="12" uuid="7aa9e6e8-b14b-4277-9a60-b444a656ea92">
	<property name="ireport.zoom" value="1.6105100000000012"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="162"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="org_tel" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call PHIEU_DONTHUOC_KHAMKH_10284($P{[UID]},$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="NAMSINH" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="SOVAOVIEN" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="NGAYTAO" class="java.lang.String"/>
	<field name="NGAYHEN" class="java.lang.String"/>
	<field name="LOIDANBACSI" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANRAVIENKT" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="SOPHIEUTHUOC" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA_KEMTHEO" class="java.lang.String"/>
	<field name="GIATRI_KETQUA" class="java.lang.String"/>
	<field name="NGAYMAUBENHPHAM" class="java.lang.String"/>
	<field name="YKIENBACSY" class="java.lang.String"/>
	<field name="PHIEUHEN" class="java.math.BigDecimal"/>
	<field name="NGAYHENTAIKHAM" class="java.lang.String"/>
	<field name="SONGAYHEN" class="java.math.BigDecimal"/>
	<field name="NGAYHENKHAM" class="java.lang.String"/>
	<field name="CHECKVP" class="java.lang.String"/>
	<field name="ORG_ADDRESS" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="ICD10" class="java.lang.String"/>
	<field name="DUNGTUYEN" class="java.lang.String"/>
	<field name="NGUOITAO" class="java.lang.String"/>
	<field name="SDT" class="java.lang.String"/>
	<field name="TENNGUOITHAN" class="java.lang.String"/>
	<field name="KHOTHUOCID" class="java.math.BigDecimal"/>
	<field name="GHICHU_KHAMBENH" class="java.lang.String"/>
	<field name="KHAMBENH_MACH" class="java.lang.String"/>
	<field name="KHAMBENH_NHIETDO" class="java.lang.String"/>
	<field name="KHAMBENH_CANNANG" class="java.lang.String"/>
	<field name="KHAMBENH_HUYETAP_HIGH" class="java.lang.String"/>
	<field name="KHAMBENH_HUYETAP_LOW" class="java.lang.String"/>
	<field name="KHAMBENH_NHIPTHO" class="java.lang.String"/>
	<field name="KHAMBENH_TOANTHAN" class="java.lang.String"/>
	<field name="KHAMBENH_BOPHAN" class="java.lang.String"/>
	<field name="LOAITIEPNHANID" class="java.math.BigDecimal"/>
	<field name="KHAMBENH_CHIEUCAO" class="java.lang.String"/>
	<field name="DONTHUOC_CLS" class="java.lang.String"/>
	<field name="ROWNUM" class="java.math.BigDecimal"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="TENDICHVU_NT" class="java.lang.String"/>
	<field name="TENDICHVU_BVBD" class="java.lang.String"/>
	<field name="TENDICHVU_LAN" class="java.lang.String"/>
	<field name="HOATCHAT" class="java.lang.String"/>
	<field name="DICHVUID" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="HUONGDANSUDUNG" class="java.lang.String"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="THUTUINPHIEU" class="java.math.BigDecimal"/>
	<field name="NDOHLUONG" class="java.lang.String"/>
	<field name="HUONGDANCHUNG" class="java.lang.String"/>
	<field name="HUONGDANCHUNGHCM" class="java.lang.String"/>
	<field name="SANG" class="java.lang.String"/>
	<field name="TRUA" class="java.lang.String"/>
	<field name="CHIEU" class="java.lang.String"/>
	<field name="TOI" class="java.lang.String"/>
	<field name="CACHDUNG" class="java.lang.String"/>
	<field name="HUONGDANCHUNGDKHN" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="92" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="245" height="20" uuid="de1f8352-3930-49dc-be08-42b4a68390f0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="20" width="245" height="20" uuid="01388177-bf2c-472f-8152-6781f7080433"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="40" width="245" height="19" uuid="ae917dab-cbe3-4d32-b62d-0a5f53f74183"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONGKHAM}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="421" y="0" width="148" height="20" uuid="f2d16089-f782-4bdb-8099-dd6b12a0c213"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Mẫu số :BM.08-KB-QT-01]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="421" y="20" width="148" height="20" uuid="061e0561-f564-449a-ad8d-9d7cfb8b1957"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOPHIEU}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement positionType="Float" x="421" y="40" width="133" height="28" uuid="8e65b9af-9daf-4b5e-a5cd-b655e2e5df07"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement positionType="Float" x="422" y="68" width="38" height="18" uuid="ac518b7f-0ce7-4078-b57c-0a66f209c5ed"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<text><![CDATA[Mã BA:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="460" y="68" width="96" height="18" uuid="704fe531-b727-4f17-8b24-d682655b9e8e"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="18" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="569" height="18" uuid="aa58f07f-09f5-4462-a86e-f06a278fd738"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐƠN THUỐC]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="94" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="256" y="0" width="54" height="18" uuid="72a1750b-2f36-47d7-aea2-0d6682280cb6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giới tính:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="477" y="0" width="92" height="18" uuid="b04f80e6-7d3a-49a2-8d59-199ef69c9421"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVTUOI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="59" y="36" width="209" height="18" uuid="05da3974-25ae-475d-886b-19c27c95d844"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="421" y="0" width="56" height="18" uuid="c6462543-d6e4-479d-875f-4c3a5800e7ef"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="72" width="71" height="18" uuid="913570ae-b2eb-4245-b48b-db47d5cdc3bf"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Bệnh kèm theo:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="310" y="0" width="68" height="18" uuid="1fe1aa84-35ee-4629-934c-32920deac179"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="59" height="18" uuid="71c2106b-ab5a-4be9-a583-2e33e1e9f242"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Họ và tên:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="18" width="47" height="18" uuid="da0941dc-eda9-401a-ac29-6ce8093c9677"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Địa chỉ: ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="269" y="36" width="71" height="18" uuid="284f21e0-2cf3-44f0-994d-107d1436cacf"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giá trị thẻ đến:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="378" y="0" width="43" height="18" uuid="6b40c578-0843-4b79-98ff-45a036754dc2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Tuổi: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="47" y="18" width="522" height="18" uuid="086bde73-ec6f-4720-8d1d-5ee6a44611fa"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIACHI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="59" y="54" width="510" height="18" uuid="70c23273-1b30-47a3-ab92-e8245d574e1b"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHANDOANVAOKHOA} + " (" + $F{ICD10} + ")"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="54" width="59" height="18" uuid="e2184d6b-59b2-4470-91ae-d317af6428e0"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Bệnh chính:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="340" y="36" width="229" height="18" uuid="8cf70207-64c4-43da-8d97-5cb0dc164a37"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BHYT_KT}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="36" width="59" height="18" uuid="a7bb8d2b-116d-4c32-80bf-51af66cb69b5"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Số BHYT: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="71" y="72" width="498" height="18" uuid="e8007860-261f-4167-8833-8fa0dc8e22d4"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHANDOANRAVIENKT}==null?"":$F{CHANDOANRAVIENKT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="59" y="0" width="197" height="18" uuid="028fdab9-00f0-4bf6-8b9e-1348f75c43a4"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}.toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="33" splitType="Prevent">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="9" y="20" width="62" height="13" uuid="141984be-1ee3-4123-9a34-ec8b69e73f56"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Cách dùng:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="69" y="20" width="500" height="13" uuid="d9ce0860-652c-4c14-9eef-58ed92e9d3ab"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CACHDUNG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="477" y="0" width="92" height="20" uuid="fefb5622-669d-482e-8c49-1ae5f8fef171"/>
				<box leftPadding="4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DVT}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="378" y="0" width="99" height="20" uuid="31ee0cc2-6a66-4f6d-9fe9-860d53013936"/>
				<box rightPadding="5"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG} < 10 ? "0" + $F{SOLUONG} : $F{SOLUONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="9" y="0" width="26" height="20" uuid="9d97fa8e-cf4b-4672-8c65-891b7f756d2d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ROWNUM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="35" y="0" width="343" height="20" uuid="b81ffef0-5474-4fac-ad44-eb25eee4e18a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="146">
			<staticText>
				<reportElement positionType="Float" x="378" y="52" width="191" height="20" uuid="3891b749-f2aa-4aa1-977e-0e8ae8665219"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[BÁC SĨ KHÁM BỆNH]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="0" y="120" width="115" height="20" uuid="0b2f3121-223e-46b2-9578-4ea00562c68e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="474" y="32" width="30" height="20" uuid="68edc913-4306-4e2e-89b9-1397c221d4af"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[tháng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="540" y="32" width="29" height="20" uuid="fef3f066-86db-4232-91f1-e66625afeb38"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("yyyy")).format(new Date())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="459" y="32" width="16" height="20" uuid="d093e951-9463-4e85-a8d9-c46dc928fd96"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("dd")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="378" y="32" width="55" height="20" uuid="2dea3fdf-ad8b-43b2-ab22-ed601624186c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[BVBĐ,]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="82" y="0" width="487" height="20" uuid="0f76ed87-98e8-4277-a65e-68d0dfb8f49a"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{YKIENBACSY} != null ? $F{YKIENBACSY} : "")+ " " + ($F{NGAYHENKHAM} != null ? "Tái khám ngày: " + $F{NGAYHENKHAM} : "")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="519" y="32" width="22" height="20" uuid="06b34db7-6151-4c76-a23f-66873b0c60d4"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[năm]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="422" y="32" width="38" height="20" uuid="7f31ef6c-c2cc-46b5-be55-75704fac4333"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Ngày]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="52" width="115" height="20" uuid="2ad683dd-23eb-404c-ae9c-7ef04c51a221"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[BỆNH NHÂN]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="503" y="32" width="17" height="20" uuid="ad867812-15b4-4181-abe0-7b5d4ea3133d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("MM")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="82" height="20" uuid="d0ecd0f4-586b-4b3f-9beb-027ddea4e59d"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[Lời dặn của BS:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="378" y="120" width="191" height="20" uuid="eef6153e-c84b-47a3-8496-53e2a5d479e6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOITAO}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="203" y="52" width="107" height="20" uuid="b8f7c913-0ee4-4798-91df-2beeff7a273e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[NGƯỜI PHÁT THUỐC]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
