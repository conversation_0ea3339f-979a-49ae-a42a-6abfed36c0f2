function UtilUploadReport(_opts) {
    var _user_id = -1;
    var _hospital_id;
    this.load = doLoad;
    var files = [];
    var mapFilePath = {
        "CANLAMSANG": "canlamsang",
        "COMMON": "common",
        "DANHMUC": "danhmuc",
        "DUOC": "duoc",
        "NGOAITRU": "ngoaitru",
        "SIGNCA": "signca",
        "BENHAN": "benhan",
        "CHIDAOTUYEN": "chidaotuyen",
        "CLBV": "clbv",
        "KSK": "ksk",
        "NGHIENCUUKHOAHOC": "nghiencuukhoahoc",
        "NOITRU": "noitru",
        "VIENPHI": "vienphi",
        "JASPER": "jasper",
        "EMRXML": "emrxml",
        "COMMONJS": "commonjs",
        "CONF": "conf",
        "CLASSES": "classes"
    }

    var mapFileBrowser = {
        "CANLAMSANG": "canlamsang",
        "DANHMUC": "danhmuc",
        "DUOC": "duoc",
        "NGOAITRU": "ngoaitru",
        "SIGNCA": "signca",
        "NOITRU": "noitru",
        "BENHAN": "benhan",
        "CHIDAOTUYEN": "chidaotuyen",
        "CLBV": "clbv",
        "KSK": "ksk",
        "NGHIENCUUKHOAHOC": "nghiencuukhoahoc",
        "VIENPHI": "vienphi",
        "EMRXML": "emrxml",
        "COMMONJS": "commonjs",
        "CONF": "conf",
        "CLASSES": "classes"
    }

    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        var _options = $.extend({}, _opts);
        var _param = _options._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        rpt_schema = _param[4];
        _bindEvent();

    }

    function _bindEvent() {
        $("#uploadBtn").click(function () {
            uploadFile();
        });

        $("#fileDownload").autocomplete({
            source: function (request, response) {
                let _url = `/vnpthis/api/source-control?fd=${btoa("WEB-INF/rpt")}&fn=${btoa(request.term)}&type=like`
                $.ajax({
                    url: _url,
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
                    },
                    type: "GET",
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    async: false,
                    success: function (res) {
                        if (res.code === 200) {
                            response(res.results)
                        } else {
                            DlgUtil.showMsg("Đã có lỗi xảy ra");
                        }
                    },
                    error: function () {
                        DlgUtil.showMsg("Đã có lỗi xảy ra.");
                    }
                });
            },
            minLength: 5,
            select: function (event, ui) {
                console.log("Selected: " + ui.item.value + " aka " + ui.item.id);
            }
        });

        $("#browserFolderBtn").click(function () {
            if ($("#cboPATH_CHOOSE").val() == "") {
                DlgUtil.showMsg("Chọn loại trước khi truy cập!");
                return;
            }

            if (mapFileBrowser[$("#cboPATH_CHOOSE").val()]) {
                window.location.href = "/vnpthis/main/manager.jsp?func=../danhmuc/UTILS_SE_TLEE&fd=" + mapFileBrowser[$("#cboPATH_CHOOSE").val()];
            } else {
                DlgUtil.showMsg("Không hỗ trợ loại này!");
                return;
            }
        });

        $("#cboPATH_CHOOSE").change(function () {
            if ($("#cboPATH_CHOOSE").val() == "") {
                $("#browserFolderBtn, #uploadBtn").attr("disabled", true);
            } else {
                $("#browserFolderBtn, #uploadBtn").attr("disabled", false);
            }

            if ($("#cboPATH_CHOOSE").val() == "JASPER") {
                $("#downloadArea").show();
            } else {
                $("#downloadArea").hide();
            }
        });

        $("#fileUploadName").click(function () {
            $("#fileUpload").click();
        })

        $("#fileUpload").change(function () {
            files = this.files;
            if (files) {
                if (files.length > 4) {
                    files = [];
                    restartForm();
                    DlgUtil.showMsg("4 files thôi, đừng tham nhé!");
                }
                if (files.length > 0) {
                    var filenames = "";
                    for (var i = 0; i < files.length; i++) {
                        filenames = filenames + files[i].name + "\n<hr>";
                    }
                    $("#fileUploadName").addClass("data");
                    $("#fileUploadName").removeClass("raw");
                    $("#fileUploadName").html(filenames)
                }
            }
        });

        $("#fileDownload").on("keyup", function () {
            let fileName = $(this).val();
            if (fileName && fileName.length > 0) {
                $("#downloadBtn").attr("disabled", false);
            } else {
                $("#downloadBtn").attr("disabled", true);
            }
        });

        $("#downloadBtn").click(function () {
            let fn = $("#fileDownload").val();
            let fd = mapFilePath[$("#cboPATH_CHOOSE").val()];
            if (fn && fd && fn.length > 0 && fd.length > 0) {
                showLoading();
                $.ajax({
                    url: "/vnpthis/JasperUpload?fd=" + fd + "&fn=" + btoa(fn),
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
                    },
                    type: "GET",
                    dataType: 'binary',
                    success: function (result, textStatus, request) {
                        if (request.status === 200) {
                            let blob = new Blob([result], {type: "application/octet-stream"});
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fn;
                            link.click();
                        } else if (request.status === 201) {
                            DlgUtil.showMsg("Dữ liệu file này trống.");
                        } else if (request.status === 205) {
                            DlgUtil.showMsg("Không tồn tại file này.");
                        }
                        hideLoading();
                    },
                    error: function () {
                        DlgUtil.showMsg("Đã có lỗi xảy ra.");
                        hideLoading();
                    }
                });
            }
        });
    }

    function uploadFile() {
        if ($("#cboPATH_CHOOSE").val() == "") {
            DlgUtil.showMsg("Chọn loại upload!");
            return;
        }

        if (mapFilePath[$("#cboPATH_CHOOSE").val()]) {
            if (files && files.length > 0) {
                var formData = new FormData();
                for (var i = 0; i < Math.min(4, files.length); i++) {
                    formData.append("fileContent" + i, $("#fileUpload")[0].files[i]);
                }
                showLoading();
                $("#uploadBtn").attr("disabled", "disabled");
                let area = mapFilePath[$("#cboPATH_CHOOSE").val()];
                formData.append("filePath", area);
                formData.append("jira", "Internal");
                formData.append("note", "Internal");
                formData.append("username", user_name);
                formData.append("donvi", hospital_id);
                var xhr = new XMLHttpRequest();
                xhr.open("POST", window.location.origin + '/vnpthis/JasperUpload');
                xhr.send(formData);
                xhr.onload = function (e) {
                    if (this.status == 200) {
                        let rp = JSON.parse(this.responseText);
                        DlgUtil.showConfirm("Vui lòng xác nhận mã upload [" + rp.message + "] để được áp dụng. Bạn có muốn xem lại thay đổi?", function (flag) {
                            if (flag) {
                                window.location.href = rp.results;
                            }
                        });
                        hideLoading();
                        files = [];
                        restartForm();
                    } else {
                        DlgUtil.showMsg("Ẹc, Lỗi rồi @@");
                        hideLoading();
                        files = [];
                        restartForm();
                    }
                };
            } else {
                DlgUtil.showMsg("Chọn file đi nào!");
                restartForm();
                return;
            }
        } else {
            DlgUtil.showMsg("Đã có lỗi xảy ra với TYPE combobox");
            restartForm();
            return;
        }
    }

    function restartForm() {
        $("#fileUploadName").addClass("raw");
        $("#fileUploadName").removeClass("data");
        $("#fileUploadName").html("Browser...");
        $("#uploadBtn").removeAttr("disabled");
    }

    function showLoading() {
        $("#loading").removeClass("hide");
        $("#loading").addClass("show");
    }

    function hideLoading() {
        $("#loading").addClass("hide");
        $("#loading").removeClass("show");
    }
}
