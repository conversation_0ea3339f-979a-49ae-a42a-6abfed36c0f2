/**
 * <AUTHOR>
 *
 *
 * Create date: 2025/03/20
 * VNPT HIS - EHealth
 *
 */
// EMR.GET.TTHC

class SignPdf extends SignHub {
    constructor(rptCode, params) {
        const urlSh = "https://test-onehealth.vncare.vn/app/mng/sign-hub/";
        super(rptCode, urlSh, params);
        this._getPdfFormForSigning(rptCode, params, ({data}) => {
            if (data.status === 200) {
                this._docData = data.base64;
                this._typeDoc = "PDF";
                this._getAdministrativeInformation();
                this._buildDocuments();
            } else {
                throw new Error("Cannot fetch data pdf for signing!");
            }
        });
    }

    _getPdfFormForSigning(rptCode, paramArrays, callback) {
        if (callback === undefined || typeof callback !== "function") {
            throw new Error("callback function is required!");
        }
        let httpWorker = new Worker("../common/script/workers/https.js");
        httpWorker.onmessage = (e) => {
            console.log("Message received from https worker");
            callback(e);
        };
        let uuid = jsonrpc.AjaxJson.getUUID();
        let paramsString = CommonUtil.replaceAll(window.btoa(unescape(encodeURIComponent(JSON.stringify(paramArrays)))), /\+/, '%2B');
        let _url = "/vnpthis/report/DirectReport?code=" + rptCode + "&filetype=pdf&reportParam=" + paramsString + "&uuid=" + uuid;
        httpWorker.postMessage({
            method: "GET",
            url: _url,
            uuid: uuid
        });
    }

    _getAdministrativeInformation() {
        let tthc = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTHC", this._hosobenhanid);
        if (!tthc || tthc.length < 1) {
            throw new Error("Error when get Administrative Information by hosobenhanid: " + this._hosobenhanid);
        }
        this._tthc = tthc[0];
    }
}