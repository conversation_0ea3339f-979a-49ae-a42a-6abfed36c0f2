function dSCHList(_opts) {
	var _ketquaclsid = _opts._ketquaclsid;
    var _dichvukhambenhid = _opts._dichvukhambenhid;
    var _hosobenhanid = -1;
    var _maubenhphamid = -1;
    var _trangthaimaubenhpham = -1;
    this.load = doLoad;
    var isPtvShowAll = false;
    var option = '';
    var para = _opts._param;
    var user_id = para[1];
    var _isSave = -1;
    var _checkSoGPB = 0;
    
    var _soGPB = false;
    
    //bien cau hinh cho ky so
	var optKySo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SUDUNG_KYSO_KYDIENTU');

    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        _checkSoGPB = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA", 'CLS_CHECK_SO_GPB');
        
        loadComboGrid();
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X013.01", _dichvukhambenhid);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            _hosobenhanid = row.HOSOBENHANID;
            _maubenhphamid = row.MAUBENHPHAMID;
            _trangthaimaubenhpham = row.TRANGTHAIMAUBENHPHAM;
            FormUtil.clearForm("inputForm", "");
            FormUtil.setObjectToForm("inputForm", "", row);
            
            if (row.TENVTST != null && row.TENVTST != '') {
                $("#cboMAVTST").empty();
                $("#cboMAVTST").append('<option value="' + row.MAVTST + '">' + row.TENVTST + '</option>');
            }
            if (row.TENGPB != null && row.TENGPB != '') {
                $("#cboMAGPB").empty();
                $("#cboMAGPB").append('<option value="' + row.MAGPB + '">' + row.TENGPB + '</option>');
            }
            if (row.BS_CLS == null || row.BS_CLS == '') {
                var sql_par = [];
                sql_par.push({
                    "name": "[0]",
                    value: user_id
                });
                var _row = jsonrpc.AjaxJson.getFirstRowO("CLS01X011.05", sql_par);
                option = $('<option value="' + _row[0].OFFICER_ID + '">' + _row[0].OFFICER_NAME + '</option>');
                $("#txtNGAYNHAN").val(row.NGAYTIEPNHAN);
                $("#txtNGAYTRAKETQUA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            } else {
                option = $('<option value="' + row.BS_CLS + '">' + row.BS_CLS_FULLNAME + '</option>');
            }
            $("#cboBS_CLS").empty();
            $("#cboBS_CLS").append(option);

            ComboUtil.getComboTag("cboLOAITHUTHUAT", "DMC.CBPTTT.01", [], "", "", "sql", '', function () {
                $("#cboLOAITHUTHUAT").val(row.LOAITHUTHUAT);
            });
        }

        if (_trangthaimaubenhpham == 3) {
            $("#btnLuu").hide();
            $("#btnXoa").hide();
        } else {
            $("#btnLuu").show();
            $("#btnXoa").show();
        }
        
        //start ẩn hiện nút ký số
		if(optKySo=="1"||optKySo=="2"){
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$('#btnInCa').show();		
		} else {
			$("#btnKyCa").hide();
			$("#btnHuyCa").hide();
			$('#btnInCa').hide();
		}
		// hết ẩn hiện nút ký số

    };

    function _bindEvent() {
        $("#txtSOGPB").change(function () {
            if(_checkSoGPB == 1) {
                checkGPB();
                if(_soGPB) {
                    DlgUtil.showMsg("Số GPB đã được nhập trước đó, xin vui lòng kiểm tra lại!");
                }
            }
        });

        $("#btnLuu").click(function () {
            doIns();
        });
        
        function doIns() {
			var validator = new DataValidator('inputForm');
            var valid = validator.validateForm();
            if (valid) {
                if(_checkSoGPB == 1) {
                    checkGPB();
                    if(_soGPB) {
                        DlgUtil.showMsg("Số GPB đã được nhập trước đó, xin vui lòng kiểm tra lại!");
                        return;
                    }
                }
                var objData = new Object();
                FormUtil.setFormToObject("inputForm", "", objData);
                var objDataKq = tinyMCE.get('txtKETQUA').getContent({format: 'html'});
                objData.KETQUA = objDataKq;
                objData.HOSOBENHANID = _hosobenhanid;
                objData.DICHVUKHAMBENHID = _dichvukhambenhid;
                objData.KETQUACLSID = _ketquaclsid;
                objData.MAUBENHPHAMID = _maubenhphamid;
                var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X013.02", JSON.stringify(objData));
                if (fl == 1) {
                    _isSave = 1;
                    //DlgUtil.showMsg("Lưu thành công !");
                    EventUtil.raiseEvent("assignSevice_saveChangeDev",{msg:'Lưu thành công !'});
                    if ($("#chkPrint").prop("checked") == true) {
                    	var par = [
   		            	 {name: 'dichvukhambenhid',type: 'String',value: _dichvukhambenhid},
   		                 {name:'id_maubenhpham',type:'String',value:_maubenhphamid}
               	 	];
               
		              //huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
		    			var INPHIEU_THEO_DV= jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
		    			if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){	     			  
		     			 var _par_code = [_dichvukhambenhid];	
		 			     var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.RPTCODE.KQ_DVKB",_par_code.join('$'));		     			  
		     			    if(i_report_code!=null && i_report_code.length>0){ 			 
		    	 			   for(var i=0;i<i_report_code.length;i++){
		    	  					var _report_code=i_report_code[i].REPORT_CODE;
		    	  					 var k = _report_code.split(';');  					
		    						  if(k.length > 1){													
		    								for(var j=0;j<k.length;j++){								
		    									openReport('window', k[j], 'pdf', par);				 
		    								}		   						
		    							} else {														
		    								   openReport('window', _report_code, 'pdf', par);							
		    							}
		    	 			   }
		     			    }else
		    			    	{
		    	 			    	openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
		    			    	}
		     			   return;	
		     			   
		    			}else{
		    				openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
		    			}            
                    }
                }
            }
		}

        $("#btnIn").click(function () {
        	var par = [
	           	 {name: 'dichvukhambenhid',type: 'String',value: _dichvukhambenhid},
	             {name:'id_maubenhpham',type:'String',value:_maubenhphamid}
   	 		];
   
         //huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
			var INPHIEU_THEO_DV= jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
			if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){
				 var _par_code = [_dichvukhambenhid];	
 			     var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.RPTCODE.KQ_DVKB",_par_code.join('$'));
			    if(i_report_code!=null && i_report_code.length>0){ 			 
	 			   for(var i=0;i<i_report_code.length;i++){
	  					var _report_code=i_report_code[i].REPORT_CODE;
	  					 var k = _report_code.split(';');  					
						  if(k.length > 1){													
								for(var j=0;j<k.length;j++){								
									openReport('window', k[j], 'pdf', par);				 
								}		   						
							} else {														
								   openReport('window', _report_code, 'pdf', par);							
							}
	 			   }
			    }else
			    	{
	 			    	openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
			    	}
			   return;	
			   
			}else{
				openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
			}           
        });

        $("#btnXoa").click(function () {
            DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này không", function (flag) {
                if (flag) {
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X011.03", _dichvukhambenhid);
                    if (fl == 1) {
                        EventUtil.raiseEvent("assignSevice_saveKetQuaGPBOk", {
                            isSave: 1
                        });
                    } else {
                        DlgUtil.showMsg("Xảy ra lỗi !");
                    }
                }
            })
        });

        $("#btnDong").on("click", function () {
            EventUtil.raiseEvent("assignSevice_saveKetQuaGPBOk", {
                isSave: _isSave
            });
        });

        $('#btnCLEARBS_CLS').on("click", function () {
            var sql_par = [];
            sql_par.push({
                "name": "[0]",
                value: user_id
            });
            var _row = jsonrpc.AjaxJson.getFirstRowO("L002.FR1", sql_par);
            var option = $('<option value="' + user_id + '">' + _row[0].FULL_NAME + '</option>');
            $("#txtBS_CLSID").val("");
            $("#cboBS_CLS").empty();
            $("#cboBS_CLS").append(option);
        });

        $("#btnEditGPB").on("click", function () {
            $('#cboMAGPB').hide();
            $('#txtGPBEDIT').val($('#cboMAGPB option:selected').text());
            $('#txtGPBEDIT').show();
            $('#btnSaveEditGPB').show();
            $("#btnEditGPB").hide();
        });
        $("#btnSaveEditGPB").on("click", function () {
            if ($.trim($('#txtGPBEDIT').val()) != '') {
                var icd = $("#cboMAGPB").val();
                $("#cboMAGPB").empty();
                $("#cboMAGPB").append('<option value="' + icd + '">' + $('#txtGPBEDIT').val().trim() + '</option>');
                $('#txtGPBEDIT').hide();
                $('#btnSaveEditGPB').hide();
                $('#cboMAGPB').show();
                $("#btnEditGPB").show();
            } else {
                $('#txtGPBEDIT').focus();
                return;
            }
        });

        $("#btnEditVTST").on("click", function () {
            $('#cboMAVTST').hide();
            $('#txtVTSTEDIT').val($('#cboMAVTST option:selected').text());
            $('#txtVTSTEDIT').show();
            $('#btnSaveEditVTST').show();
            $("#btnEditVTST").hide();
        });
        $("#btnSaveEditVTST").on("click", function () {
            if ($.trim($('#txtVTSTEDIT').val()) != '') {
                var icd = $("#cboMAVTST").val();
                $("#cboMAVTST").empty();
                $("#cboMAVTST").append('<option value="' + icd + '">' + $('#txtVTSTEDIT').val().trim() + '</option>');
                $('#txtVTSTEDIT').hide();
                $('#btnSaveEditVTST').hide();
                $('#cboMAVTST').show();
                $("#btnEditVTST").show();
            } else {
                $('#txtVTSTEDIT').focus();
                return;
            }
        });

        $("#btnEditPPN").on("click", function () {
            $('#cboPHUONGPHAPNHUOM').hide();
            $('#txtGHICHUPHUONGPHAPNHUOM').val($('#cboPHUONGPHAPNHUOM option:selected').text());
            $('#txtGHICHUPHUONGPHAPNHUOM').show();
            $('#btnSaveEditPPN').show();
            $("#btnEditPPN").hide();
        });
        $("#btnSaveEditPPN").on("click", function () {
            if ($.trim($('#txtGHICHUPHUONGPHAPNHUOM').val()) != '') {
                var icd = $("#cboMAVTST").val();
                $("#cboPHUONGPHAPNHUOM").empty();
                $("#cboPHUONGPHAPNHUOM").append('<option value="' + icd + '">' + $('#txtGHICHUPHUONGPHAPNHUOM').val().trim() + '</option>');
                $('#cboPHUONGPHAPNHUOM').show();
                $('#txtGHICHUPHUONGPHAPNHUOM').hide();
                $('#btnSaveEditPPN').hide();
                $("#btnEditPPN").show();
            } else {
                $('#txtGHICHUPHUONGPHAPNHUOM').focus();
                return;
            }
        });
        
        // các nút liên quan tới ký số
		$('#btnKyCa').on('click', function () {
			doIns();
			_caGetParam('1');
		});

		$('#btnHuyCa').on('click', function () {
			_caGetParam('2');
		});										   
	
		$("#btnInCa").on("click",function(e){
			_caGetParam('0');
		});
		// hết phần ký số
    }
    
    function _caGetParam(_signType) {
		var par = [
	           	 {name: 'dichvukhambenhid',type: 'String',value: _dichvukhambenhid},
	             {name:'id_maubenhpham',type:'String',value:_maubenhphamid}
   	 		];
   
        //huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
		var INPHIEU_THEO_DV= jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
		if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){
			 var _par_code = [_dichvukhambenhid];	
		     var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.RPTCODE.KQ_DVKB",_par_code.join('$'));
		    if(i_report_code!=null && i_report_code.length>0){ 			 
 			   for(var i=0;i<i_report_code.length;i++){
  					var _report_code=i_report_code[i].REPORT_CODE;
  					 var k = _report_code.split(';');  					
					  if(k.length > 1){													
							for(var j=0;j<k.length;j++){								
								par.push({name:'RPT_CODE',type:'String',value:k[j]});	
								_caRpt(par, _signType);		 
							}		   						
						} else {	
								par.push({name:'RPT_CODE',type:'String',_report_code});													
							   _caRpt(par, _signType);							
						}
 			   }
		    } else
		    	{
					par.push({name:'RPT_CODE',type:'String',value:"PHIEU_KQ_GIAIPHAUBENH"});
 			    	_caRpt(par, _signType);
		    	}
		   return;	
		   
		} else {
			 par.push({name:'RPT_CODE',type:'String',value:"PHIEU_KQ_GIAIPHAUBENH"});
			 _caRpt(par, _signType);	
		} 
	}
	
	function _caRpt(_params, _signType) {
		if(_signType == '0') {
			CommonUtil.openReportGetCA2(_params, false);
		} else {
			CommonUtil.kyCA(_params, _signType,true);
			EventUtil.setEvent("eventKyCA", function (e) {
				DlgUtil.showMsg(e.res,undefined,1000);			
			});
		}
	}

    function  checkGPB() {
        var sql_par = [];
        sql_par.push({"name":"[0]","value": _dichvukhambenhid});
        sql_par.push({"name":"[1]","value": $("#txtSOGPB").val()});
        var _check = jsonrpc.AjaxJson.getOneValue('CLS01X011.CHECK.GPB', sql_par);
        if(_check == 1) {
            _soGPB = true;
        } else {
            _soGPB = false;
        }
    }

    function loadComboGrid() {
        var _colICD = 'Mã GPB,MAGPB,20,0,f,l;Mẫu KQ GPB,TENMAU,30,0,f,l;Tên GPB,TENGPB,50,0,f,l';
        ComboUtil.initComboGrid('txtMA_GPB', 'CLS01X011.06', [], '600px', _colICD, function (event, ui) {
            var _ui = ui.item;
            $("#cboMAGPB").empty();
            $("#cboMAGPB").append('<option value="' + _ui.MAGPB + '">' + _ui.TENGPB + '</option>');
            $('#txtKETLUAN').val(_ui.KETLUAN);
            tinyMCE.activeEditor.setContent(_ui.GIATRI, {format: 'html'});
            return false;
        });

        var _colICD = 'Mã VTST,MASINHTHIET,30,0,f,l;Tên VTST,TENSINHTHIET,70,0,f,l';
        ComboUtil.initComboGrid('txtMA_VTST', 'DMC129.07', [], '600px', _colICD, function (event, ui) {
            var _ui = ui.item;
            $("#cboMAVTST").empty();
            $("#cboMAVTST").append('<option value="' + _ui.MASINHTHIET + '">' + _ui.TENSINHTHIET + '</option>');
            return false;
        });

        var _colICD = 'Nội dung,NOIDUNG,30,0,f,l;Ghi chú,GHICHU,70,0,f,l';
        ComboUtil.initComboGrid('txtNOIDUNGPHUONGPHAPNHUOM', 'CLS01X011.07', [], '1000px', _colICD, function (event, ui) {
            var _ui = ui.item;
            $("#cboPHUONGPHAPNHUOM").empty();
            $("#cboPHUONGPHAPNHUOM").append('<option value="' + _ui.NOIDUNG + '">' + _ui.GHICHU + '</option>');
            return false;
        });

        var sql_par = [];
        sql_par.push({"name": "[0]", "value": khoaId});
        var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên bác sỹ,FULL_NAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
        ComboUtil.initComboGrid("txtBS_CLSID", "CLS.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBS_CLSID").val("");
            var _ui = ui.item;
            $("#cboBS_CLS").empty();
            $("#cboBS_CLS").append('<option value="' + _ui.USER_ID + '">' + _ui.FULL_NAME + '</option>');
            return false;
        });
    }
}
