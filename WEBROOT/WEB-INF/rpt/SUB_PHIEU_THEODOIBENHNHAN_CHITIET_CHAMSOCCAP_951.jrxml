<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SUB_PHIEU_THEODOIBENHNHAN_CHITIET_CHAMSOCCAP_951" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="842" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="d8258224-60b6-4c49-b0e9-a411ee2fbd39">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Sans_Normal" isDefault="true" fontName="DejaVu Sans" fontSize="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
	<style name="Sans_Bold" fontName="DejaVu Sans" fontSize="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
	<style name="cell">
		<box>
			<pen lineWidth="0.5"/>
		</box>
	</style>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="chamsocchitietid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call SUB_PHIEU_CS_CHITIET_CAP_951(null,$P{[HID]},$P{[SCH]},null,$P{chamsocchitietid},$P{ora_cursor})}]]>
	</queryString>
	<field name="A_CHAMSOCCHITIETID" class="java.math.BigDecimal"/>
	<field name="NGAYKETQUA" class="java.lang.String"/>
	<field name="TIEUDE" class="java.lang.String"/>
	<field name="GIATRI" class="java.lang.String"/>
	<summary>
		<band height="105">
			<crosstab isRepeatColumnHeaders="false">
				<reportElement x="0" y="0" width="782" height="105" uuid="281d96c8-0720-483d-848a-dc72d3394eae"/>
				<crosstabHeaderCell>
					<cellContents>
						<staticText>
							<reportElement mode="Transparent" x="0" y="0" width="63" height="36" forecolor="#000000" backcolor="#FFFFFF" uuid="439d0adc-818b-46f6-82d8-d024ece469dd"/>
							<box leftPadding="2">
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<text><![CDATA[Giờ]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="TIEUDE" width="63">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TIEUDE}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents style="cell">
							<textField>
								<reportElement style="Sans_Bold" x="0" y="0" width="63" height="21" uuid="fb68d0e8-93dd-4b8b-b047-181243123d41"/>
								<box leftPadding="2">
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="12" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TIEUDE}.substring( 1 )]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="NGAYKETQUA" height="36" headerPosition="Stretch">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{NGAYKETQUA}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents style="cell">
							<textField pattern="" isBlankWhenNull="false">
								<reportElement style="Sans_Bold" mode="Transparent" x="0" y="0" width="55" height="36" forecolor="#000000" backcolor="#FFFFFF" uuid="39a030d0-9bd7-4554-9cfe-afbff1f8cf0c"/>
								<box>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
									<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
									<paragraph lineSpacing="Single"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{NGAYKETQUA}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#FFFF60" mode="Opaque" style="cell"/>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="GIATRI" class="java.lang.String">
					<measureExpression><![CDATA[$F{GIATRI}]]></measureExpression>
				</measure>
				<crosstabCell width="55" height="21">
					<cellContents backcolor="#FFFFFF" mode="Opaque" style="cell">
						<textField pattern="" isBlankWhenNull="false">
							<reportElement mode="Transparent" x="0" y="0" width="55" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="fa0ba179-c762-4c93-a5c2-1380d55a3340"/>
							<box>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{GIATRI}==null?"":$V{GIATRI}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>
