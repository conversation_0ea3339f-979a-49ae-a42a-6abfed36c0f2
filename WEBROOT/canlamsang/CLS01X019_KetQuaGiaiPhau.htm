<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js"></script>
<link rel="stylesheet" href="../noitru/custom_nt.css">
<script type="text/javascript" src="../canlamsang/CLS01X019_KetQuaGiaiPhau.js?v=20230313"></script>
<script src="../common/script/tinymce/tinymce.min.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css" type="text/css"/>

<style>
    .mce-edit-area {
        border: 1px solid #d0d0d0 !important;
    }

    .mce-tinymce.mce-container.mce-panel {
        border: 0 !important;
    }
</style>

<div width="100%" id="divMain" class="container">
    <div class="col-md-12 low-padding mgt5">
        <div id="inputForm">
            <div class="col-md-6 low-padding">
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-2 low-padding required">
                        <label class="mgl5">Số GPB</label>
                    </div>
                    <div class="col-md-4 low-padding">
                        <input class="form-control input-sm kb-i-col-m" id="txtSOGPB"
                               valrule="Số giải phẫu bệnh,required"
                               name="txtSOGPB" title="" style="width:100%; text-align: center;">
                    </div>
                    <div class="col-md-2 low-padding">
                        <label class="mgl5 ">Số BA</label>
                    </div>
                    <div class="col-md-4 low-padding">
                        <input class="form-control input-sm kb-i-col-m" style="width:100%;" id="txtMAHOSOBENHAN"
                               name="txtMAHOSOBENHAN" title="" style="width:100%;" disabled>
                    </div>
                </div>
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-2 low-padding">
                        <label class="mgl5 ">Mã VTST</label>
                    </div>
                    <div class="col-md-10 low-padding">
                        <div class="col-md-2 low-padding">
                            <input class="form-control input-sm" id="txtMA_VTST" attrIcd="0" title="">
                        </div>
                        <div class="col-md-10 low-padding input-group">
                            <select class="form-control input-sm" style="width: 90%" id="cboMAVTST" reffld="TENVTST"
                                    modeDisXT="" attrCl="clear">
                            </select>
                            <input class="form-control input-sm" style="width: 90%; display: none" id="txtVTSTEDIT"
                                   placeholder="Không được để trống tên VTST" maxlength="500"/>
                            <button type="button" class="btn btn-sm btn-primary" id="btnSaveEditVTST" modeDisXT=""
                                    style="display: none">
                                <span class="glyphicon glyphicon-ok" style="height: 13px !important"></span>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnEditVTST">
                                <span class="glyphicon glyphicon-pencil" style="height: 13px !important"></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-2 low-padding">
                        <label class="mgl5 ">ICD-0</label>
                    </div>
                    <div class="col-md-2 low-padding">
                        <input class="form-control input-sm kb-i-col-m"
                               id="txtICDID" name="txtICDID" title="" style="width:100%;">
                    </div>
                    <div class="col-md-8 low-padding">
                        <input class="form-control input-sm kb-i-col-m"
                               id="txtICD" name="txtICD" title="" style="width:100%;" disabled>
                    </div>
                </div>
            </div>
            <div class="col-md-6 low-padding">
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-2 low-padding">
                        <label class="mgl5 ">Ngày nhận</label>
                    </div>
                    <div class="col-md-4 low-padding">
                        <div class="input-group" style="width: 100%">
                            <input class="form-control input-sm" id="txtNGAYNHAN"
                                   valrule="Ngày nhận,datetime|max_length[19]" title=""
                                   data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19"
                                   style="z-index: auto">
                            <span
                                    class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                    type="sCal" id='spNGAYNHAN'
                                    onclick="NewCssCal('txtNGAYNHAN','ddMMyyyy','dropdown',true,'24',true)"></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-2 low-padding">
                        <label class="mgl5 ">Mã GPB</label>
                    </div>
                    <div class="col-md-10 low-padding">
                        <div class="col-md-2 low-padding">
                            <input class="form-control input-sm" id="txtMA_GPB" attrIcd="0" title="">
                        </div>
                        <div class="col-md-10 low-padding input-group">
                            <select class="form-control input-sm" style="width: 90%" id="cboMAGPB" reffld="TENGPB"
                                    modeDisXT="" attrCl="clear">
                            </select>
                            <input class="form-control input-sm" style="width: 90%; display: none" id="txtGPBEDIT"
                                   placeholder="Không được để trống tên GPB" maxlength="500"/>
                            <button type="button" class="btn btn-sm btn-primary" id="btnSaveEditGPB" modeDisXT=""
                                    style="display: none">
                                <span class="glyphicon glyphicon-ok" style="height: 13px !important"></span>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnEditGPB">
                                <span class="glyphicon glyphicon-pencil" style="height: 13px !important"></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 low-padding mgt5 mgl10">
                    <div class="col-md-3 low-padding">
                        <input type="checkbox" id="chkICD"> <label class="label1">Không bắt ICD-0</label>
                    </div>
                </div>
            </div>

            <div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 ">Ph.pháp nhuộm</label>
                </div>
                <div class="col-md-11 low-padding">
                    <input class="form-control input-sm kb-i-col-m" style="width:100%;" id="txtPHUONGPHAPNHUOM"
                           name="txtPHUONGPHAPNHUOM" title="" style="width:100%;">
                </div>
            </div>
            <div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 " id="lblKETQUA"></label>
                </div>
                <div class="col-md-11 low-padding">
                    <textarea class="form-control i-col-full" rows="5" id="txtKETQUA" name="txtKETQUA" title=""
                              style="width:100%;"></textarea>
                </div>
            </div>
            <div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 " id="lblVTST"></label>
                </div>
                <div class="col-md-8 low-padding">
					<textarea class="form-control i-col-full" rows="5" id="txtVTST" name="txtVTST" title=""
                              style="width:100%;"></textarea>                    
                </div>
                <div class="col-md-1 low-padding">
                    <label class="mgl5 ">Loại thủ thuật</label>
                </div>
                <div class="col-md-2 low-padding">
                    <select class="form-control input-sm" id="cboLOAITHUTHUAT" style="width:100%;">
                    </select>
                </div>
            </div>
            <div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 " id="lblKETLUAN"></label>
                </div>
                <div class="col-md-11 low-padding">
                    <textarea class="form-control i-col-full" rows="3" id="txtKETLUAN" name="txtKETLUAN" title=""
                              style="width:100%;"></textarea>
                </div>
            </div>
			<div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">	
						<label class="mgl5 ">Người pha BP</label>
				</div>
				<div class="col-md-2 low-padding">	
						<input class="form-control input-sm kb-i-col-m" type="text" id="txtNGUOIPHABP" style="width:98% !important" />
				</div>
				<div class="col-md-9 low-padding">				  			   
					<select class="form-control input-sm" id="cboNGUOIPHABENHPHAM" filterlike="txtNGUOIPHABP" style="width:30% !important">							
					</select>
				</div>
            </div>
			<div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 ">Số mảnh</label>
                </div>
                <div class="col-md-11 low-padding">
                    <input class="form-control input-sm kb-i-col-m" style="width:100%;" id="txtSOMANH"
                           name="txtSOMANH" title="" style="width:100%;">
                </div>
            </div>
			<div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 ">Sự phù hợp với chẩn đoán CLS</label>
                </div>
                <div class="col-md-11 low-padding">
					<textarea class="form-control i-col-full" rows="3" id="txtPHUHOP" name="txtPHUHOP" title=""
                              style="width:100%;"></textarea>
                </div>
            </div>
            <div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 " id="lblBANLUAN"></label>
                </div>
                <div class="col-md-11 low-padding">
                    <textarea class="form-control i-col-full" rows="3" id="txtBANLUAN" name="txtBANLUAN" title=""
                              style="width:100%;"></textarea>
                </div>
            </div>

            <div class="col-md-4 low-padding">
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-3 low-padding">
                        <label class="mgl5">B.Sỹ CLS</label>
                    </div>
                    <div class="form-inline col-md-9 low-padding">
                        <input class="form-control input-sm i-col-30" id="txtBS_CLSID" title="" attrIcd="0"
                               modeDisXT="">
                        <select class="form-control input-sm i-col-60" style="width:56% !important;"
                                id="cboBS_CLS" modeDisXT="" attrCl="clear">
                            <option value="-1">--Lựa chọn--</option>
                        </select>
                        <button type="button" class="form-control input-sm glyphicon" id="btnCLEARBS_CLS"
                                modeDisXT="" style="top:0px !important;">
                            <span class="glyphicon glyphicon-remove"></span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6 low-padding">
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-2 low-padding">
                        <label class="mgl5">Ngày trả KQ</label>
                    </div>
                    <div class="col-md-4 low-padding">
                        <div class="input-group" style="width: 100%">
                            <input class="form-control input-sm" id="txtNGAYTRAKETQUA"
                                   valrule="Ngày trả kết quả,datetime|max_length[19]" title=""
                                   data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19"
                                   style="z-index: auto">
                            <span
                                    class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                    type="sCal" id='spNGAYTRAKETQUA'
                                    onclick="NewCssCal('txtNGAYTRAKETQUA','ddMMyyyy','dropdown',true,'24',true)"></span>
                        </div>
                    </div>
					<div class="col-md-2 low-padding">
					</div>
					<div class="col-md-2 low-padding">
						<div class="col-md-1 low-padding mgt5">
							<button type="button" class="btn btn-sm btn-primary" id="btnChupAnh">
								<span class="glyphicon glyphicon-camera"></span> CHỤP ẢNH
							</button>
						</div>
					</div>
                </div>
            </div>
            <div class="col-md-2 low-padding">
                <div class="col-md-12 low-padding mgt5">
                    <div class="col-md-offset-4 col-md-8 low-padding mgt5">
                        <input type="checkbox" id="chkPrint"> <label class="label1">In luôn ra giấy</label>
                    </div>
                </div>
            </div>
		
			<div class="col-md-12 low-padding mgt5">
                <div class="col-md-1 low-padding">
                    <label class="mgl5 ">Chọn mẫu</label>
                </div>
				<div class="col-md-3 low-padding">
                    <select class="selectpicker SumoUpper" id="cboMAU" style="width:95%">
                    	<option value=""></option>
					</select>
                </div>
				<div class="col-md-1 low-padding">
                    <label class="mgl5 ">Nhập tên mẫu</label>
                </div>
                <div class="col-md-5 low-padding">
                    <input class="form-control input-sm" id="txtTENMAU" name="txtTENMAU" maxlength="200" style="width:95%" />
                </div>
                
                <div class="col-md-1 low-padding">
                    <button type="button" class="btn btn-sm btn-primary" id="btnLuuMau">
						<span class="glyphicon glyphicon-floppy-disk"></span> Lưu mẫu</button>
                </div>
				<div class="col-md-1 low-padding">
                    <button type="button" class="btn btn-sm btn-primary" id="btnXoaMau">
						<span class="glyphicon glyphicon-remove"></span> Xóa mẫu</button>
                </div>
            </div>
            <div class="col-md-12 low-padding mgt20 mgb20">
                <div class="col-md-12 low-padding" style="text-align: center;">
                    <button type="button" class="btn btn-sm btn-primary" id="btnLuu">
                        <span class="glyphicon glyphicon-floppy-disk"></span> Lưu
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" id="btnXoa">
                        <span class="glyphicon glyphicon-remove-circle"></span> Xóa
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" id="btnIn">
                        <span class="glyphicon glyphicon-print"></span> In
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" id="btnDong">
                        <span class="glyphicon glyphicon-remove-circle"></span> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    tinymce.init({
        selector: '#txtKETQUA',
        theme: 'modern',
        plugins: ['textcolor colorpicker table'],
        toolbar: false,
        menubar: false,
        statusbar: false,
        language: 'vi_VN',
        disabled: true
    });
    var opt = [];
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var db_schema = '{db_schema}';
    var hospital_code = '{HOSPITAL_CODE}';
    var uuid = '{uuid}';
    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    session_par[4] = db_schema;
    session_par[5] = hospital_code;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");

    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid;

    parent.DlgUtil.tunnel(DlgUtil.moveEvent);
    objVar = EventUtil.getVar("dlgVar");
    _opts._dichvukhambenhid = objVar.dichvukhambenhid;
	_opts._idketquacls = objVar.idketquacls;

    var DS = new dSCHList(_opts);
    DS.load(hospital_id);
</script>