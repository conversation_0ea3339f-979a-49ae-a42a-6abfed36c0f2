<?xml version="1.0" encoding="UTF-8"?>
<component inheritJdk="true">
	<exclude-output/>
	<contentEntry url="file://$MODULE_DIR$">
		<excludeFolder url="file://$MODULE_DIR$/WEBROOT/WEB-INF/rpt"/>
	</contentEntry>
	<lib name="servlet.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/servlet.jar!/"/>
	</lib>
	<lib name="jsonrpc-1.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jsonrpc-1.0.jar!/"/>
	</lib>
	<lib name="dbpool.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/dbpool.jar!/"/>
	</lib>
	<lib name="jxl.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jxl.jar!/"/>
	</lib>
	<lib name="acme.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/acme.jar!/"/>
	</lib>
	<lib name="fileupload-progress.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/fileupload-progress.jar!/"/>
	</lib>
	<lib name="CrystalCommon2.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/CrystalCommon2.jar!/"/>
	</lib>
	<lib name="CrystalReportsRuntime.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/CrystalReportsRuntime.jar!/"/>
	</lib>
	<lib name="cvom.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/cvom.jar!/"/>
	</lib>
	<lib name="DatabaseConnectors.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/DatabaseConnectors.jar!/"/>
	</lib>
	<lib name="webreporting-jsf.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/webreporting-jsf.jar!/"/>
	</lib>
	<lib name="webreporting.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/webreporting.jar!/"/>
	</lib>
	<lib name="jrcadapter.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jrcadapter.jar!/"/>
	</lib>
	<lib name="jrcerom.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jrcerom.jar!/"/>
	</lib>
	<lib name="httpcore-4.4.4.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/httpcore-4.4.4.jar!/"/>
	</lib>
	<lib name="httpclient-4.5.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/httpclient-4.5.1.jar!/"/>
	</lib>
	<lib name="jasperreports-5.6.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jasperreports-5.6.0.jar!/"/>
	</lib>
	<lib name="jasperreports-fonts-5.6.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jasperreports-fonts-5.6.0.jar!/"/>
	</lib>
	<lib name="jasperreports-functions-5.6.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jasperreports-functions-5.6.0.jar!/"/>
	</lib>
	<lib name="jasperreports-htmlcomponent-5.0.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jasperreports-htmlcomponent-5.0.1.jar!/"/>
	</lib>
	<lib name="jasperreports-javaflow-5.6.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jasperreports-javaflow-5.6.0.jar!/"/>
	</lib>
	<lib name="commons-collections-3.2.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-collections-3.2.1.jar!/"/>
	</lib>
	<lib name="commons-digester-2.1.jar" scope="COMPILE">
		<srcroot url="file://C:/Users/<USER>/AppData/Local/Temp/.org.sf.feeling.decompiler1615861248619/source/commons-digester-2.1-sources.jar"/>
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-digester-2.1.jar!/"/>
	</lib>
	<lib name="ojdbc6.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/ojdbc6.jar!/"/>
	</lib>
	<lib name="dwr.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/dwr.jar!/"/>
	</lib>
	<lib name="axis.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/axis.jar!/"/>
	</lib>
	<lib name="jaxrpc.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jaxrpc.jar!/"/>
	</lib>
	<lib name="commons-discovery-0.2.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-discovery-0.2.jar!/"/>
	</lib>
	<lib name="itext-4.2.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/itext-4.2.1.jar!/"/>
	</lib>
	<lib name="dreport.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/dreport.jar!/"/>
	</lib>
	<lib name="ftp4j-1.7.2.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/ftp4j-1.7.2.jar!/"/>
	</lib>
	<lib name="slf4j-api-1.7.6.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/slf4j-api-1.7.6.jar!/"/>
	</lib>
	<lib name="slf4j-log4j12-1.7.6.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/slf4j-log4j12-1.7.6.jar!/"/>
	</lib>
	<lib name="mybatis-3.2.5.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/mybatis-3.2.5.jar!/"/>
	</lib>
	<lib name="groovy-all-1.8.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/groovy-all-1.8.0.jar!/"/>
	</lib>
	<lib name="poi-3.14-20160307.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/poi-3.14-20160307.jar!/"/>
	</lib>
	<lib name="poi-ooxml-3.14-20160307.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/poi-ooxml-3.14-20160307.jar!/"/>
	</lib>
	<lib name="poi-ooxml-schemas-3.14-20160307.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/poi-ooxml-schemas-3.14-20160307.jar!/"/>
	</lib>
	<lib name="xmlbeans-2.6.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/xmlbeans-2.6.0.jar!/"/>
	</lib>
	<lib name="log4j-1.2.17.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/log4j-1.2.17.jar!/"/>
	</lib>
	<lib name="commons-fileupload-1.2.2.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-fileupload-1.2.2.jar!/"/>
	</lib>
	<lib name="commons-io-2.3.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-io-2.3.jar!/"/>
	</lib>
	<lib name="commons-lang3-3.3.2.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-lang3-3.3.2.jar!/"/>
	</lib>
	<lib name="comutil.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/comutil.jar!/"/>
	</lib>
	<lib name="commons-codec-1.6.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-codec-1.6.jar!/"/>
	</lib>
	<lib name="gson-2.2.4.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/gson-2.2.4.jar!/"/>
	</lib>
	<lib name="jcaptcha-2.0-alpha-1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jcaptcha-2.0-alpha-1.jar!/"/>
	</lib>
	<lib name="jcaptcha-api-2.0-alpha-1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jcaptcha-api-2.0-alpha-1.jar!/"/>
	</lib>
	<lib name="filters-2.0.235.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/filters-2.0.235.jar!/"/>
	</lib>
	<lib name="jcommon-1.0.15.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jcommon-1.0.15.jar!/"/>
	</lib>
	<lib name="jfreechart-1.0.12.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jfreechart-1.0.12.jar!/"/>
	</lib>
	<lib name="jjwt-0.9.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jjwt-0.9.0.jar!/"/>
	</lib>
	<lib name="apache-log4j-extras-1.2.17.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/apache-log4j-extras-1.2.17.jar!/"/>
	</lib>
	<lib name="mail-1.4.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/mail-1.4.1.jar!/"/>
	</lib>
	<lib name="HikariCP-3.3.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/HikariCP-3.3.1.jar!/"/>
	</lib>
	<lib name="slf4j-api-1.7.25.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/slf4j-api-1.7.25.jar!/"/>
	</lib>
	<lib name="xercesImpl-2.9.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/xercesImpl-2.9.1.jar!/"/>
	</lib>
	<lib name="nekohtml.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/nekohtml.jar!/"/>
	</lib>
	<lib name="zxing-1.7-javase.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/zxing-1.7-javase.jar!/"/>
	</lib>
	<lib name="zxing-1.7-core.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/zxing-1.7-core.jar!/"/>
	</lib>
	<lib name="ajaxutil.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/ajaxutil.jar!/"/>
	</lib>
	<lib name="hash-signature-0.0.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/hash-signature-0.0.1.jar!/"/>
	</lib>
	<lib name="hapi-base-2.3.jar" scope="COMPILE">
		<srcroot url="file://C:/Users/<USER>/AppData/Local/Temp/.org.sf.feeling.decompiler1615875514552/source/hapi-base-2.3-sources.jar"/>
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/hapi-base-2.3.jar!/"/>
	</lib>
	<lib name="hapi-structures-v231-2.3.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/hapi-structures-v231-2.3.jar!/"/>
	</lib>
	<lib name="xalan-2.7.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/xalan-2.7.1.jar!/"/>
	</lib>
	<lib name="jai-imageio-core-1.4.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jai-imageio-core-1.4.0.jar!/"/>
	</lib>
	<lib name="jcommander-1.72.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jcommander-1.72.jar!/"/>
	</lib>
	<lib name="totp-1.7.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/totp-1.7.1.jar!/"/>
	</lib>
	<lib name="handlebars-4.2.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/handlebars-4.2.0.jar!/"/>
	</lib>
	<lib name="handlebars-guava-cache-4.2.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/handlebars-guava-cache-4.2.0.jar!/"/>
	</lib>
	<lib name="handlebars-jackson2-4.2.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/handlebars-jackson2-4.2.0.jar!/"/>
	</lib>
	<lib name="jackson-core-asl-1.9.9.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jackson-core-asl-1.9.9.jar!/"/>
	</lib>
	<lib name="jackson-mapper-asl-1.9.9.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/jackson-mapper-asl-1.9.9.jar!/"/>
	</lib>
	<lib name="kafka-consummer-1.0-jar-with-dependencies.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/kafka-consummer-1.0-jar-with-dependencies.jar!/"/>
	</lib>
	<lib name="caffeine-2.9.3.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/caffeine-2.9.3.jar!/"/>
	</lib>
	<lib name="hapi-fhir-base-4.0.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/hapi-fhir-base-4.0.0.jar!/"/>
	</lib>
	<lib name="hapi-fhir-structures-dstu2-4.0.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/hapi-fhir-structures-dstu2-4.0.0.jar!/"/>
	</lib>
	<lib name="hapi-fhir-structures-r4-4.0.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/hapi-fhir-structures-r4-4.0.0.jar!/"/>
	</lib>
	<lib name="org.hl7.fhir.r4-4.0.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/org.hl7.fhir.r4-4.0.0.jar!/"/>
	</lib>
	<lib name="org.hl7.fhir.utilities-4.0.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/org.hl7.fhir.utilities-4.0.0.jar!/"/>
	</lib>
	<lib name="playwright-1.34.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/playwright-1.34.0.jar!/"/>
	</lib>
	<lib name="commons-text-1.10.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/commons-text-1.10.0.jar!/"/>
	</lib>
	<lib name="httpmime-4.4.1.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/httpmime-4.4.1.jar!/"/>
	</lib>
	<lib name="Utils-1.0.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/Utils-1.0.jar!/"/>
	</lib>
	<lib name="authentication2factor-1.5.jar" scope="COMPILE">
		<relative-module-cls project-related="jar://$PROJECT_DIR$/PM2_VNPTHISL2_CODE/WEBROOT/WEB-INF/lib/authentication2factor-1.5.jar!/"/>
	</lib>
	<levels>
		<level name="fernflower" value="project"/>
		<level name="redisson-3.22.1" value="project"/>
	</levels>
</component>