/** VietDA 
 *  Class cơ sở mô tả dữ liệu gửi và nhận tới LIS TGG
 *  v0.1	06/12/2018		Khởi tạo
 */

function HisService(){
	this.ServiceName = "";
	this.ServiceCode = "";
	this.ServiceDetailList = [];
}

function HisServiceDetail(){
	this.ServiceDetailName = "";
	this.ServiceDetailCode = "";
}

function LabRequestTGG(){
	this.OrderID = "";
	this.OrderDate = "";
	this.OrderTime = "";
	this.OrderDoctor = "";
	this.OrderDoctorCode = "";
	this.PatientCode = "";
	this.PatientName = "";
	this.Sex = "";
	this.Birthday = "";
	this.Address = "";
	this.Phone = "";
	this.SocialNumber = "";
	this.FromDate = "";
	this.ToDate = "";
	this.Department = "";
	this.DepartmentCode = "";	
	this.Ward = "";
	this.WardCode = "";
	this.Bed = "";
	this.Emergency = "";
	this.RightLine = "";
	
	this.SID = "";		
	this.TestCodeList = [];
}

function LabResultTGG(){
	this.OrderID = "";
	this.OrderDate = "";
	this.SID = "";
	this.ApprovedBy = "";
	this.ApprovedTime = "";
	this.ResultDetail = [];
}

function ResultList(){
	this.TestCode = "";
	this.TestResultData = "";
	this.TestResultTime = "";
	this.TestMachine = "";
}