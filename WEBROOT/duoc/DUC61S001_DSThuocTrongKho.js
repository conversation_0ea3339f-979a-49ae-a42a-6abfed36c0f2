/*
 * <PERSON>h muc thuoc vat tu trong kho
 * 
 * Bachnv		06/09/2016		Tạo mới
 * ChienDV		26/10/2023		L2PT-54777
 * 
 */
var ctl_ar = [{
    type: 'button',
    id: 'btnLichSu',
    icon: 'yc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    text: '<PERSON><PERSON><PERSON> sử nhập thuốc',
    cssClass: 'wd150'
}, {
    type: 'button',
    id: 'btnHanSuDung',
    icon: 'hansudung',
    text: 'Hạn sử dụng',
    cssClass: 'wd130'
}, {
    type: 'button',
    id: 'btnChonPhongLuu',
    icon: 'chonphongluu',
    text: 'Chọn nơi lưu trữ',
    cssClass: 'wd130'
}, {
    type: 'label',
    id: 'lblInfo',
    icon: '',
    text: 'Thuốc vật tư tồn kho',
    cssClass: 'wd130'
}
];

// Cau hinh rieng cho site Quang Tri them nut the kho
var ctl_thekho = [{
    type: 'button',
    id: 'btn<PERSON>ichSu',
    icon: 'yc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    text: '<PERSON><PERSON><PERSON> sử nhập thuốc',
    cssClass: 'wd150'
}, {
    type: 'button',
    id: 'btnHanSuDung',
    icon: 'hansudung',
    text: 'Hạn sử dụng',
    cssClass: 'wd130'
}, {
    type: 'button',
    id: 'btnChonPhongLuu',
    icon: 'chonphongluu',
    text: 'Chọn nơi lưu trữ',
    cssClass: 'wd130'
}, {
    type: 'button',
    id: 'btnChonTheKho',
    icon: 'list',
    text: 'Thẻ kho',
    cssClass: 'wd130'
}, {
    type: 'label',
    id: 'lblInfo',
    icon: '',
    text: 'Thuốc vật tư tồn kho',
    cssClass: 'wd130'
}
];

function DSThuocTrongKho(opt) {
    var _param = session_par;
    this.opt = $.extend({}, opt);
    var that = this;
    this.load = doLoad;
    var lk = '';
    var dsTonKhoId = [];
    var _thuovattuid = '';
    var _mathuoc = '';
    var _tenthuoc = '';
    var _hoatchat = '';
    var _nhomthuoc = '-1';
    var _nguonct = '-1';
    var PHARMA_GIAODIEN_NHAP_NCC = 0;
    var PHARMA_TAIFILE_TRACUUTK = 0;
    var PHARMA_TRACUU_MANHOM = '0';
    var PHARMA_XUAT_EXCEL_GRID = '0';
    var PHARMA_DSTRAUCUU_GROUP = '0';
    var PHARMA_NHAP_LYDOKHOA = 0;
    var PHARMA_SHOW_OPTION_KHADUNG = '0';
    var PHARMA_SHOW_CANHBAO_TON = '0';
    var PHARMA_SHOW_SOLUONG_TON = '0';
    var PHARMA_SHOW_LECH_TON = '0';
    var PHARMA_SHOW_CBOX_NHOMBHXH = '0';
    // Cau hinh rieng cho site Quang Tri them nut the kho
    var PHARMA_SHOW_TRACUU_THEKHO = '0';
    var PHARMA_SHOW_XEMCHITIET_DSTHUOC = '0';
    var PHARMA_SHOW_DSTHUOC_HUONGHOA ='0';
    var _flag = 0;

    function doLoad() {
        // khởi tạo các tab dữ liệu
        $("li[id^='tab'").on("click", function (e) {
            var tabName = $(this).attr("id").substr(3);
            $("li[class='active']").removeClass("active");
            $(this).addClass("active");
            $("div[class='tab active']").removeClass("active");
            // console.log(tabName);
            $("#div" + tabName).addClass("active");
        });

        // khởi tạo các control
        $("#guide_color").css("display", "none");
        $("#canhbaosl_color").css("display", "none");
        var _par_ncc = ['PHARMA_GIAODIEN_NHAP_NCC'];
        PHARMA_GIAODIEN_NHAP_NCC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_ncc.join('$'));

        var _par_tk = ['PHARMA_TAIFILE_TRACUUTK'];
        PHARMA_TAIFILE_TRACUUTK = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_tk.join('$'));

        PHARMA_TRACUU_MANHOM = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            ['PHARMA_TRACUU_MANHOM'].join('$'));

        PHARMA_XUAT_EXCEL_GRID = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            ['PHARMA_XUAT_EXCEL_GRID'].join('$'));


        PHARMA_DSTRAUCUU_GROUP = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            ['PHARMA_DSTRAUCUU_GROUP'].join('$'));

        var _par_lydo = ['PHARMA_NHAP_LYDOKHOA'];
        PHARMA_NHAP_LYDOKHOA = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_lydo.join('$'));

        var _par_khadung = ['PHARMA_SHOW_OPTION_KHADUNG'];
        PHARMA_SHOW_OPTION_KHADUNG = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_khadung.join('$'));

        var _par_canhbao = ['PHARMA_SHOW_CANHBAO_TON'];
        PHARMA_SHOW_CANHBAO_TON = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_canhbao.join('$'));

        var _par_soluong = ['PHARMA_SHOW_SOLUONG_TON'];
        PHARMA_SHOW_SOLUONG_TON = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_soluong.join('$'));

        var _par_lechton = ['PHARMA_SHOW_LECH_TON'];
        PHARMA_SHOW_LECH_TON = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_lechton.join('$'));

        var _par_nhombhxh = ['PHARMA_SHOW_CBOX_NHOMBHXH'];
        PHARMA_SHOW_CBOX_NHOMBHXH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_nhombhxh.join('$'));

        // Cau hinh rieng cho site Quang Tri them nut the kho
        var _par_thekho = ['PHARMA_SHOW_TRACUU_THEKHO'];
        PHARMA_SHOW_TRACUU_THEKHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_thekho.join('$'));

        var _par_PHARMA_SHOW_XEMCHITIET_DSTHUOC =  ['PHARMA_SHOW_XEMCHITIET_DSTHUOC'];
			PHARMA_SHOW_XEMCHITIET_DSTHUOC  = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_PHARMA_SHOW_XEMCHITIET_DSTHUOC.join('$'));

         var _par_PHARMA_SHOW_DSTHUOC_HUONGHOA    =  ['PHARMA_SHOW_DSTHUOC_HUONGHOA'];
			PHARMA_SHOW_DSTHUOC_HUONGHOA  = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_PHARMA_SHOW_DSTHUOC_HUONGHOA.join('$'));


        if (PHARMA_SHOW_TRACUU_THEKHO == '1') {
            // Cau hinh rieng cho site Quang Tri them nut the kho
            var toolbar = ToolbarUtil.build('toolbarId', ctl_thekho);
        }else {
            var toolbar = ToolbarUtil.build('toolbarId', ctl_ar);
        }
        initControl();
        bindEvent();
        $("#cboKhoLap").focus();
    }

    function loadGrid() {
        $("#hidTonKhoID").val('-1');
        $("#hidTonKhoCTID").val('-1');
        if ($("#cboNhomThuoc").val().toString() != '') {
            _nhomthuoc = $("#cboNhomThuoc").val().toString()
        } else {
            _nhomthuoc = '-1';
        }
        if ($("#cboNguonCT").val() != '') {
            _nguonct = $("#cboNguonCT").val()
        } else {
            _nguonct = '-1';
        }
        if (PHARMA_SHOW_SOLUONG_TON == '1') {
            if ($('#txtSoLuongTon').val() != '' && !isNumeric(replaceStrtoNum($('#txtSoLuongTon').val()))) {
                DlgUtil.showMsg("Số lượng tồn không đúng!", undefined, 2000);
                $('#txtSoLuongTon').focus();
                return;
            }

            var param = RSUtil.buildParam("", [$("#txtTimKiem").val().trim(),
                ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
                ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
                ($("#cboTonKho").val()),
                ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
                $("#cboKhoLap").val(),
                $("#txtHoatChat").val().trim(),
                ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
                ($("#ckbHSD90").prop('checked') == true ? "1" : "0"),
                _nhomthuoc,
                _nguonct,
                replaceStrtoNum($("#txtSoLuongTon").val().trim())
            ]);


            GridUtil.loadGridBySqlPage("grvDSThuoc", "DUC61S001.01.SL", param);
        } else if (PHARMA_SHOW_LECH_TON == '1') {
            var param = RSUtil.buildParam("", [$("#txtTimKiem").val().trim(),
                ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
                ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
                ($("#cboTonKho").val()),
                ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
                $("#cboKhoLap").val(),
                $("#txtHoatChat").val().trim(),
                ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
                ($("#ckbHSD90").prop('checked') == true ? "1" : "0"),
                _nhomthuoc,
                _nguonct,
                ($("#ckbLechTon").prop('checked') == true ? "1" : "0")
            ]);


            GridUtil.loadGridBySqlPage("grvDSThuoc", "DUC61S001.01.LT", param);
        } else if (PHARMA_SHOW_CBOX_NHOMBHXH == '1') {
            var nhombhxh = '-1';
            if ($("#cboNhomBHXH").val() != '') {
                nhombhxh = $("#cboNhomBHXH").val()
            } else {
                nhombhxh = '-1';
            }
            var param = RSUtil.buildParam("", [$("#txtTimKiem").val().trim(),
                ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
                ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
                ($("#cboTonKho").val()),
                ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
                $("#cboKhoLap").val(),
                $("#txtHoatChat").val().trim(),
                ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
                ($("#ckbHSD90").prop('checked') == true ? "1" : "0"),
                _nhomthuoc,
                _nguonct,
                nhombhxh
            ]);


            GridUtil.loadGridBySqlPage("grvDSThuoc", "DUC61S001.BHXH.01", param);
        } else {
            var param = RSUtil.buildParam("", [$("#txtTimKiem").val().trim(),
                ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
                ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
                ($("#cboTonKho").val()),
                ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
                $("#cboKhoLap").val(),
                $("#txtHoatChat").val().trim(),
                ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
                ($("#ckbHSD90").prop('checked') == true ? "1" : "0"),
                _nhomthuoc,
                _nguonct
            ]);


            GridUtil.loadGridBySqlPage("grvDSThuoc", "DUC61S001.01", param);
        }


        // chuannt lay tong so thuoc va tong tien

        var sql_par = $("#txtTimKiem").val().trim() + '$' +
            ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0") + '$' +
            ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0") + '$' +
            ($("#cboTonKho").val()) + '$' +
            ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0") + '$' +
            $("#cboKhoLap").val() + '$' +
            $("#txtHoatChat").val().trim() + '$' +
            ($("#ckbHSD180").prop('checked') == true ? "1" : "0") + '$' +
            ($("#ckbHSD90").prop('checked') == true ? "1" : "0");
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC61S001.02', sql_par);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            $("#tongsothuoc").text(row.TONGSO);
            //$("#tongtien").text(Number(row.TONGTIEN).format(2, 3, ',') + ' đồng');
            if (that.opt.hospitalId == '10284' || that.opt.hospitalId == '53210') {
                $("#tongtien").text(Number(row.TONGTIEN).format(2, 3, '.').replace(/.([^.]*)$/, ',$1') + ' đồng');
            } else {
                $("#tongtien").text(Number(row.TONGTIEN).format(2, 3, ',') + ' đồng');
            }
        }


        //
        //		var sql_par = [];
        //		var param = $("#txtTimKiem").val() + '$'
        //				+ ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0")
        //				+ '$'
        //				+ ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0")
        //				+ '$' + ($("#cboTonKho").prop('checked') == true ? "1" : "0")
        //				+ '$'
        //				+ ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0")
        //				+ '$' + $("#cboKhoLap").val()
        //				+ '$' + $("#txtHoatChat").val();
        //		console.log(param);
        //
        //		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC61S001.01", param);
        //		// console.log(JSON.stringify(data_ar));
        //		$("#grvDSThuoc").jqGrid("clearGridData");
        //		$("#grvDSThuoc")[0].grid.beginReq();
        //		$("#grvDSThuoc").jqGrid("setGridParam", {
        //			data : data_ar
        //		});
        //		$("#grvDSThuoc")[0].grid.endReq();
        //		$("#grvDSThuoc").trigger("reloadGrid");
    }


    function setSysParam(_par_ar) {
        var v_par = _par_ar;
        for (var i1 = 0; i1 < _param.length; i1++) {
            console.log('_param[' + i1 + ']=' + _param[i1]);
            v_par.push({
                "name": "[S" + i1 + "]",
                "value": _param[i1]
            });
        }
        return v_par;
    }


    function loadThongTinBenhNhan(bid) {
        $('#ttHanhChinh').patientInfo({
            benhnhanId: bid
        });
    }

    function initControl() {
        $("#tongsothuoc").text('0');
        $("#tongtien").text('0 đồng');

        if (_param[0] == 1216) {
            $("#ckbXemChiTiet").prop('checked', true);
            $("#dsthuochethan").show();
        }
        //		ComboUtil.getComboTag("cboKhoLap", "DUC06M001.07", [ {
        //			"name" : "[0]",
        //			"value" : "1"
        //		} ], "", "", "sql", "", "");
        ComboUtil.getComboTag("cboKhoLap", "DUC01S002.DSKHO", [{"name": "[0]", "value": that.opt.lk},
                {"name": "[1]", "value": 't'}], "",
            {
                value: '',
                text: '-- Chọn kho --'
            }, "");
        //		loadKho();
        /*	ComboUtil.getComboTag("cboNhomThuoc", "DUC67T001.26", [ {
                "name" : "[0]",
                "value" : "1"
            } ], "", {
                value : '',
                text : 'Chọn'
            }, "");*/
        //var sql_par=RSUtil.buildParam("",[""]);
        if (PHARMA_TRACUU_MANHOM == '1') {
            ComboUtil.getComboTag("cboNhomThuoc", "DUC61S001.03", [""], "", "", "sql", "", false);
            $('#cboNhomThuoc').SumoSelect({
                search: true,
                searchText: 'Tìm kiếm',
                okCancelInMulti: true,
                selectAll: true
            });

        } else {
            ComboUtil.getComboTag("cboNhomThuoc", "DMC147.01", [""], "", "", "sql", "", false);
            $('#cboNhomThuoc').SumoSelect({
                search: true,
                searchText: 'Tìm kiếm',
                okCancelInMulti: true,
                selectAll: true
            });

        }
        ComboUtil.getComboTag("cboNguonCT", "DUC67T001.28", [{
            "name": "[0]",
            "value": "1"
        }], "", {
            value: '',
            text: 'Chọn'
        }, "");
        lk = that.opt.lk;

        //ductx - bvtm-2813
        if (that.opt.hospitalId == '10284') {
            $("#btnLock").show();
        } else {
            $("#btnLock").hide();
        }

        if (PHARMA_SHOW_OPTION_KHADUNG == '1') {
            $('#cboTonKho').append($('<option>',
                {
                    value: "3",
                    text: "Khả dụng >0"
                }));
            $('#cboTonKho').append($('<option>',
                {
                    value: "4",
                    text: "Khả dụng =0"
                }));
        }
        if (PHARMA_SHOW_SOLUONG_TON == '1') {
            $("#div_SoLuong").show();
        } else {
            $("#div_SoLuong").hide();
        }

        if (PHARMA_SHOW_LECH_TON == '1') {
            $("#div_LenhTon").show();
        } else {
            $("#div_LenhTon").hide();
        }
        // loadGrid();
        if (PHARMA_SHOW_CBOX_NHOMBHXH == '1') {
            var sql_par = RSUtil.buildParam("", ['15,17,18,8,10,11,13,23,25']);
            ComboUtil.getComboTag("cboNhomBHXH", "DUC67T001.15", sql_par, "", {
                value: '',
                text: '--Chọn--'
            }, "sql", "", false);
            $("#divNhomBHXH").show();
        } else {
            $("#divNhomBHXH").hide();
        }

        checkRole('btnDoiLoaiBu');
    }

    function KhoiTaoCot() {
        var _gridLogId = "grvDSThuoc";
        var slchuaxuat = '';
        if (that.opt.hospitalId == '7282') {
            slchuaxuat = "SL CX,SLCHUAXUAT,80,decimal,f,r!f;"
        }
        var mathuocBV = '';
        if (that.opt.hospitalId == '1022') { //1022
            mathuocBV = "Mã thuốc BV,MATHUOCBV,100,0,f,l!s;"
        }
        var slTonCanhBao = '';
        if (PHARMA_SHOW_CANHBAO_TON == '1') {
            slTonCanhBao = "Tồn tối thiểu,TONTOITHIEU,0,0,t,l;" +
                "Tồn tối đa,TONTOIDA,0,0,t,l;";
        }
        var _giadv_nd = '';
        if (PHARMA_SHOW_CBOX_NHOMBHXH == '1') {
            _giadv_nd = "Giá dịch vụ,GIADICHVU,70,decimal!3,f,l!f;" +
                "Giá nhân dân,GIANHANDAN,70,decimal!3,f,l!f;";
        }
        var _gridHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
            + "Nhóm thuốc,TENNHOM,0,0,t,l;"
            + "Mã thuốc,MA,100,0,f,l!s;"
            + mathuocBV
            + "Tên thuốc,TEN,200,0,f,l!s;"
            + "Tên BHYT,TENKHOAHOC,200,0,f,l!s;"
            + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
            + "ĐVT,DONVITINH,50,0,f,l!s;"
            + "Đơn giá,DONGIA,70,decimal!3,f,l!f;"
            + "Thành tiền,THANHTIEN,100,decimal!3,f,l!f;"
            + "SL ĐK,SLDAUKY,80,decimal!3,t,r!f;"
            + "SL TK,SLTONKHO,80,decimal!3,f,r!f;"
            + "SL KD,SLKHADUNG,80,decimal!3,f,r!f;"
            + slchuaxuat
            + "Tên HC,HOATCHAT,200,0,f,l!s;"
            //+ "Biệt dược,BIETDUOC,20,0,f,l;"
            + "Định mức,SLTT,80,0,f,r!f;"
            + "STT,STT,50,0,f,l!s;"
            + "SĐK,SDK,70,0,f,l!s;"
            + "QĐ Thầu,QD_THAU,70,0,f,l!s;"
            + "Ngày công bố,NAMCUNGUNG,70,0,f,l!s;"
            + "Mã HC,MAHOATCHAT,100,0,f,l!s;"

            + "Hãng sản xuất,HANG_SX,200,0,f,l!s;"
            + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
            + "KHOA,KHOA,30,0,f,l;"
            + "Gói Thầu,GOITHAU,70,0,f,l!s;"
            + "Nhóm Thầu,NHOMTHAU,70,0,f,l!s;"
            + "CANHBAOSOLUONG,CANHBAOSOLUONG,0,0,t,l;"
            + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
            + "TONKHOID,TONKHOID,40,0,f,l";
        if (that.opt.hospitalId == '1111') {
            _gridHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
                + "Mã thuốc,MA,100,0,f,l!s;"
                + "Tên thuốc,TEN,200,0,f,l!s;"
                + "Tên BHYT,TENKHOAHOC,200,0,f,l!s;"
                + "Tên HC,HOATCHAT,200,0,f,l!s;"
                + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
                + "ĐVT,DONVITINH,50,0,f,l!s;"
                + "Định mức nhóm,SLDM_NHOM,100,0,f,r!f;"
                + "Định mức,SLTT,80,0,f,r!f;"
                + "SL ĐK,SLDAUKY,80,0,f,r!f;"
                + "SL TK,SLTONKHO,80,0,f,r!f;"
                + "SL KD,SLKHADUNG,80,0,f,r!f;"
                + "Mã HC,MAHOATCHAT,100,0,f,l!s;"
                //+ "Biệt dược,BIETDUOC,20,0,f,l;"
                + "STT,STT,50,0,f,l!s;"
                + "SĐK,SDK,70,0,f,l!s;"
                + "QĐ Thầu,QD_THAU,70,0,f,l!s;"
                + "Đơn giá,DONGIA,70,0,f,l!f;"
                + "Hãng sản xuất,HANG_SX,200,0,f,l!s;"
                + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
                + "KHOA,KHOA,30,0,f,l;"
                + "CANHBAOSOLUONG,CANHBAOSOLUONG,0,0,t,l;"
                + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
                + "TONKHOID,TONKHOID,40,0,f,l";
        }
        var _gridHeaderCT = '';
        if (that.opt.hospitalId == '954') {
            _gridHeaderCT = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
                + "NHAPXUATID,NHAPXUATID,0,0,t,l;"
                + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
                + "Tonkhoid,TONKHOID,0,0,t,l;"
                + "Tonkhoctid,TONKHOCTID,50,0,f,l;"
                + "Mã thuốc,MA,100,0,f,l!s;"
                + "Tên thuốc,TEN,200,0,f,l!s;"
                + "ĐVT,DONVITINH,50,0,f,l!s;"
                + "Hoạt chất,HOATCHAT,200,0,f,l!s;"
                + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
                + "Số lô,SOLO,50,0,f,r!s;"
                + "Hạn sử dụng,HANSUDUNG,70,0,f,l!s;"
                + "SL Tồn,SLTONKHO,80,0,f,r!f;"
                + "Đơn giá,DONGIA,90,0,f,l!f;"
                + "Thành tiền,THANHTIEN,110,0,f,l!f;"
                + "Hãng sản xuất,HANG_SX,200,0,f,l!s;"
                + "SL ĐK,SLDAUKY,80,0,f,r!f;"
                + "SL KD,SLKHADUNG,80,0,f,r!f;"
                + "QĐ Thầu,QD_THAU,70,0,f,l!s;"
                + "Biệt dược,BIETDUOC,100,0,f,l!s;"
                + "Ngày nhập,NGAYNHAP,70,0,f,l!s;"
                + "TT Thầu,STT_THAU,100,0,f,r!f;"
                + "KHOA,KHOA,0,0,t,l;"
                + "KHOACHA,KHOACHA,0,0,t,l;"
                + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
                + "Mã HC,MAHOATCHAT,100,0,f,l!s";
        } else if (that.opt.hospitalId == '10284') {
            _gridHeaderCT = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
                + "NHAPXUATID,NHAPXUATID,0,0,t,l;"
                + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
                + "Tonkhoid,TONKHOID,0,0,t,l;"
                + "Tonkhoctid,TONKHOCTID,50,0,f,l!s;"
                + "Mã thuốc,MA,100,0,f,l!s;"
                + "Tên thuốc,TEN,200,0,f,l!s;"
                + "Tên HC,HOATCHAT,200,0,f,l!s;"
                + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
                + "ĐVT,DONVITINH,50,0,f,l!s;"
                + "SL Đầu kỳ,SLDAUKY,80,decimal!2,f,r!f;"
                + "SL Tồn kho,SLTONKHO,80,decimal!2,f,r!f;"
                + "SL Khả dụng,SLKHADUNG,80,decimal!2,f,r!f;"
                + "Giá BHYT,GIABHYT,70,decimal!2,f,l!f;"
                + "Giá mua vào,DONGIA,70,decimal!2,f,l!f;"
                + "Số đăng ký,SODANGKY,70,0,f,r!s;"
                + "Số lô,SOLO,50,0,f,r!s;"
                + "Hạn sử dụng,HANSUDUNG,70,0,f,l!s;"
                + "Số hóa đơn,HOADON,70,0,f,l!s;"
                + "Số chứng từ,SCT,100,0,f,l!s;"
                + "Ngày nhập,NGAYNHAP,70,0,f,l!s;"
                + "TT Thầu,TTTHAU,100,0,f,l!f;"
                + "KHOA,KHOA,50,0,f,l!f;"
                + "KHOACHA,KHOACHA,0,0,t,l;"
                + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
                + "Mã HC,MAHOATCHAT,100,0,f,l!s";
        } else if (PHARMA_SHOW_XEMCHITIET_DSTHUOC == '1') {
            _gridHeaderCT = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
                + "NHAPXUATID,NHAPXUATID,0,0,t,l;"
                + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
                + "Tonkhoid,TONKHOID,0,0,t,l;"
                + "Tonkhoctid,TONKHOCTID,50,0,f,l!s;"
                + "Nhóm thuốc,TENNHOM,0,0,t,l;"
                + "Mã thuốc,MA,100,0,f,l!s;"
                + mathuocBV
                + "Tên thuốc,TEN,200,0,f,l!s;"
                + "Tên BHYT,TENKHOAHOC,200,0,f,l!s;"
                + "Tên HC,HOATCHAT,200,0,f,l!s;"
                + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
                + "ĐVT,DONVITINH,50,0,f,l!s;"
                + "Số lô,SOLO,50,0,f,r!s;"
                + "Hạn sử dụng,HANSUDUNG,70,0,f,l!s;"
                + _giadv_nd
                + "SL Đầu kỳ,SLDAUKY,80,decimal!3,f,r!f;"
                + "SL Tồn kho,SLTONKHO,80,decimal!3,f,r!f;"
                + "SL Khả dụng,SLKHADUNG,80,decimal!3,f,r!f;"
                + "Hãng sản xuất,HANG_SX,200,0,f,l!s;"
                + slchuaxuat
                + slTonCanhBao
                + "Giá BHYT,GIABHYT,70,decimal!3,f,l!f;"
                + "Giá mua vào,DONGIA,70,decimal!3,f,l!f;"
                + "Thành tiền,THANHTIEN,100,decimal!3,f,l!f;"
                + "QĐ Thầu,QD_THAU,70,0,f,l!s;"
                + "Ngày công bố,NAMCUNGUNG,70,0,f,l!s;"
                + "Biệt dược,BIETDUOC,100,0,f,l!s;"
                + "Ngày nhập,NGAYNHAP,70,0,f,l!s;"
                + "TT Thầu,STT_THAU,100,0,f,r!f;"
                + "KHOA,KHOA,50,0,f,l!f;"
                + "KHOACHA,KHOACHA,0,0,t,l;"
                + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
                + "Mã HC,MAHOATCHAT,100,0,f,l!s;"
                + "Gói Thầu,GOITHAU,70,0,f,l!s;"
                + "Nhóm Thầu,NHOMTHAU,70,0,f,l!s";
        } else if (PHARMA_SHOW_DSTHUOC_HUONGHOA == '1') {
            _gridHeaderCT = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
                + "NHAPXUATID,NHAPXUATID,0,0,t,l;"
                + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
                + "Tonkhoid,TONKHOID,0,0,t,l;"
                + "Tonkhoctid,TONKHOCTID,50,0,f,l!s;"
                + "Nhóm thuốc,TENNHOM,0,0,t,l;"
                + "Mã thuốc,MA,100,0,f,l!s;"
                + mathuocBV
                + "Tên thuốc,TEN,200,0,f,l!s;"
                + "Tên HC,HOATCHAT,200,0,f,l!s;"
                + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
                + "ĐVT,DONVITINH,50,0,f,l!s;"
                + "Số lô,SOLO,50,0,f,r!s;"
                + "Hạn sử dụng,HANSUDUNG,70,0,f,l!s;"
                + _giadv_nd
                + "SL Đầu kỳ,SLDAUKY,80,decimal!3,f,r!f;"
                + "SL Tồn kho,SLTONKHO,80,decimal!3,f,r!f;"
                + "SL Khả dụng,SLKHADUNG,80,decimal!3,f,r!f;"
                + slchuaxuat
                + slTonCanhBao
                + "Giá BHYT,GIABHYT,70,decimal!3,f,l!f;"
                + "Giá mua vào,DONGIA,70,decimal!3,f,l!f;"
                + "Thành tiền,THANHTIEN,100,decimal!3,f,l!f;"
                + "QĐ Thầu,QD_THAU,70,0,f,l!s;"
				+ "Tên BHYT,TENKHOAHOC,200,0,f,l!s;"
				+ "Hãng sản xuất,HANG_SX,200,0,f,l!s;"
                + "Ngày công bố,NAMCUNGUNG,70,0,f,l!s;"
                + "Biệt dược,BIETDUOC,100,0,f,l!s;"
                + "Ngày nhập,NGAYNHAP,70,0,f,l!s;"
                + "TT Thầu,STT_THAU,100,0,f,r!f;"
                + "KHOA,KHOA,50,0,f,l!f;"
                + "KHOACHA,KHOACHA,0,0,t,l;"
                + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
                + "Mã HC,MAHOATCHAT,100,0,f,l!s;"
                + "Gói Thầu,GOITHAU,70,0,f,l!s;"
                + "Nhóm Thầu,NHOMTHAU,70,0,f,l!s";
        }else {
            _gridHeaderCT = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
                + "NHAPXUATID,NHAPXUATID,0,0,t,l;"
                + "DSPHONGTHUCHIEN,DSPHONGTHUCHIEN,0,0,t,l;"
                + "Tonkhoid,TONKHOID,0,0,t,l;"
                + "Tonkhoctid,TONKHOCTID,50,0,f,l!s;"
                + "Nhóm thuốc,TENNHOM,0,0,t,l;"
                + "Mã thuốc,MA,100,0,f,l!s;"
                + mathuocBV
                + "Tên thuốc,TEN,200,0,f,l!s;"
                + "Tên BHYT,TENKHOAHOC,200,0,f,l!s;"
                + "Tên HC,HOATCHAT,200,0,f,l!s;"
                + "Hàm lượng,HAMLUONG,100,0,f,l!s;"
                + "Hãng sản xuất,HANG_SX,200,0,f,l!s;"
                + "ĐVT,DONVITINH,50,0,f,l!s;"
                + "SL Đầu kỳ,SLDAUKY,80,decimal!3,f,r!f;"
                + "SL Tồn kho,SLTONKHO,80,decimal!3,f,r!f;"
                + "SL Khả dụng,SLKHADUNG,80,decimal!3,f,r!f;"
                + slchuaxuat
                + slTonCanhBao
                + "Giá BHYT,GIABHYT,70,decimal!3,f,l!f;"
                + "Giá mua vào,DONGIA,70,decimal!3,f,l!f;"
                + _giadv_nd
                + "Thành tiền,THANHTIEN,100,decimal!3,f,l!f;"
                + "Số lô,SOLO,50,0,f,r!s;"
                + "Hạn sử dụng,HANSUDUNG,70,0,f,l!s;"
                + "QĐ Thầu,QD_THAU,70,0,f,l!s;"
                + "Ngày công bố,NAMCUNGUNG,70,0,f,l!s;"
                + "Biệt dược,BIETDUOC,100,0,f,l!s;"
                + "Ngày nhập,NGAYNHAP,70,0,f,l!s;"
                + "TT Thầu,STT_THAU,100,0,f,r!f;"
                + "KHOA,KHOA,50,0,f,l!f;"
                + "KHOACHA,KHOACHA,0,0,t,l;"
                + "Nhà cung cấp,NHACUNGCAP,200,0,f,l!s;"
                + "Mã HC,MAHOATCHAT,100,0,f,l!s;"
                + "Gói Thầu,GOITHAU,70,0,f,l!s;"
                + "Nhóm Thầu,NHOMTHAU,70,0,f,l!s;"
				+ "Tên nhóm,TENNHOM,200,0,f,l!s;"
				+ "Đóng gói,DONGGOI,200,0,f,l!s";
        }
        var _par = ['PHARMA_COL_TRACUU_TONKHO'];
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par.join('$'));
        if (result != '0' && result != null){
        	_gridHeader = result;
        }
        
        var _parCT = ['PHARMA_COL_TRACUU_TONKHO_CT'];
        var resultCT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
        		_parCT.join('$'));
        if (resultCT != '0' && resultCT != null){
        	_gridHeaderCT = resultCT;
        }
            
        var _group = {
            groupField: ["TENNHOM"],
            groupColumnShow: [false],
            groupText: ['<b>{0}</b>']
        };
        if ($("#ckbXemChiTiet").prop('checked') == true) {
            if (that.opt.hospitalId == '10284') {
                GridUtil.init("grvDSThuoc", "100%", "310",
                    "Danh sách chi tiết thuốc vật tư trong kho", true, _gridHeaderCT, false,
                    {
                        rowNum: 20,
                        rowList: [20, 50, 100, 200, 500, 1000]
                        , loadonce: false
                        , sqltype: 'sp'
                    }
                );
            } else if (PHARMA_DSTRAUCUU_GROUP == '1') {
                GridUtil.initGroup("grvDSThuoc", "100%", "310",
                    "Danh sách chi tiết thuốc vật tư trong kho", false, _group, _gridHeaderCT, false);

            }else if (PHARMA_SHOW_DSTHUOC_HUONGHOA == '1') {
                GridUtil.init("grvDSThuoc", "100%", "310",
                    "Danh sách chi tiết thuốc vật tư trong kho", true, _gridHeaderCT, false,
                    {
                        rowNum: 500,
                        rowList: [500, 1000]
                        , loadonce: false
                        , sqltype: 'sp'
                    }
                );
            } else {
                GridUtil.init("grvDSThuoc", "100%", "350",
                    "Danh sách chi tiết thuốc vật tư trong kho", false, _gridHeaderCT, false, null, "VN");
            }
            GridUtil.addExcelButton("grvDSThuoc", 'Xuất excel', true);
            if (PHARMA_SHOW_OPTION_KHADUNG == '1') {
                $("#canhbaosl_color").show();
            }
        } else if (that.opt.hospitalId == '51540') {
            GridUtil.init("grvDSThuoc", "100%", "310",
                "Danh sách thuốc vật tư trong kho", true, _gridHeader, false,
                {
                    rowNum: 200,
                    rowList: [200, 500, 1000]
                    , loadonce: false
                    , sqltype: 'sp'
                }, "VN"
            );
        } else {
            if (PHARMA_DSTRAUCUU_GROUP == '1') {
                /*GridUtil.initGroup("grvDSThuoc", "100%", "310",
                    "Danh sách thuốc vật tư trong kho", true, _group, _gridHeader, false,
                    {
                        rowNum: 20,
                        rowList: [20, 50, 100, 200, 500, 1000]
                        , loadonce: false
                        , sqltype: 'sp'
                    }
                );*/
				GridUtil.initGroup("grvDSThuoc", "100%", "310",
                        "Danh sách thuốc vật tư trong kho", false, _group, _gridHeader, false);
            } else {
                GridUtil.init("grvDSThuoc", "100%", "310",
                    "Danh sách thuốc vật tư trong kho", true, _gridHeader, false,
                    {
                        rowNum: 20,
                        rowList: [20, 50, 100, 200, 500, 1000]
                        , loadonce: false
                        , sqltype: 'sp'
                    }, "VN"
                );
            }
        }
        GridUtil.addExcelButton("grvDSThuoc", 'Xuất excel', true);

        $("#grvDSThuoc").jqGrid('setGridParam', {
            onSelectRow: function (id) {
                var _ret = $("#grvDSThuoc").jqGrid('getRowData', id);
                _mathuoc = _ret.MA;
                _tenthuoc = _ret.TEN;
                _hoatchat = _ret.HOATCHAT;
                _thuovattuid = _ret.THUOCVATTUID;
                if ($("#ckbXemChiTiet").prop('checked') == true) {
                    $("#hidTonKhoID").val('-1');
                    $("#hidTonKhoCTID").val(_ret.TONKHOCTID);
                } else {
                    $("#hidTonKhoID").val(_ret.TONKHOID);
                    $("#hidTonKhoCTID").val('-1');

                    //lay danh sach thuoc khi duoc chon checkbox
                    var selRows = $(this).jqGrid('getGridParam',
                        'selarrrow');
                    rowData = $('#grvDSThuoc').jqGrid(
                        'getRowData', id);
                    if (selRows.indexOf(id) != -1
                        && dsTonKhoId.indexOf(rowData["TONKHOID"]) == -1) {
                        dsTonKhoId.push(rowData["TONKHOID"]);
                        // alert(dskhobu);
                    }
                    if (selRows.indexOf(id) == -1) {
                        var i = dsTonKhoId.indexOf(rowData["TONKHOID"]);
                        if (i != -1) {
                            dsTonKhoId.splice(i, 1);
                        }
                    }
                }
                GridUtil.unmarkAll('grvDSThuoc');
                GridUtil.markRow('grvDSThuoc', id);


            }, ondblClickRow: function (rowId, iRow, iCol, e) {
                var rowData = $("#grvDSThuoc").jqGrid('getRowData', rowId);
                if (rowData != null) {
                    if ($("#ckbXemChiTiet").prop('checked') == false) {
                        paramInput = {
                            thuocvattuId: rowData.THUOCVATTUID,
                            khoId: $("#cboKhoLap").val(),
                            edit: that.opt.cs
                        };
                        dlgPopup = DlgUtil.buildPopupUrl("divDlgThongTinThuoc", "divDlg", "manager.jsp?func=../duoc/DUC56T001_ThongTinThuoc", paramInput, "Thông tin thuốc", window.innerWidth * 0.95, window.innerHeight * 0.95);
                        DlgUtil.open("divDlgThongTinThuoc");
                    } else {

                        var myVar = {
                            khoid: $("#cboKhoLap").val(),
                            nhapxuatid: rowData.NHAPXUATID,
                            tenkho: $("#cboKhoLap option:selected").text(),
                            kieu: 'THUOC',
                            trangthai: '4',
                            loaiphieu: '0',
                            edit: '1',
                            ht: '1'
                        };
                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, "Xem phiếu Nhập Kho chi tiết", 1210, 610);
                        DlgUtil.open("dlgNhapKho");
                    }

                }
            },
            gridComplete: function (id) {
                if ($("#ckbXemChiTiet").prop('checked') == false) {
                    var _color = '';
                    var rowIds = $('#grvDSThuoc').jqGrid('getDataIDs');

                    for (var i = 1; i <= rowIds.length; i++) {
                        var rowData = $('#grvDSThuoc').jqGrid('getRowData', i);
                        var khoa = rowData['KHOA'];
                        if (khoa == '1') {
                            //    			        	 _color=;
                            $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                                //    	    		    	        $(element).css({'background-color':'#BDBDBD'});
                                $(element).css('text-decoration', 'line-through');
                                // $(element).css.style.text-decoration = "line-through";
                                //    	    		    	        document.getElementById("myP").style.textDecoration = "line-through";
                            });
                        }

                        var dschidinh = rowData['DSPHONGTHUCHIEN'];
                        if (dschidinh != "") {
                            //    			        	 _color=;
                            $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                                //    	    		    	        $(element).css({'background-color':'#BDBDBD'});
                                $(element).css('text-decoration', 'underline');
                                // $(element).css.style.text-decoration = "line-through";
                                //    	    		    	        document.getElementById("myP").style.textDecoration = "line-through";
                            });
                        }
                        if (Number(rowData['SLTONKHO']) < Number(rowData['CANHBAOSOLUONG'])) {
                            var _color = '#FF9900';
                            $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').css("background-color", _color);
                            $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', 'Cảnh báo số lượng');
                        }

                    }

                    $(".jqgrow", "#" + _gridLogId).contextMenu('contextMenu', {
                        bindings: {
                            'selPhongLuu': function (t) {
                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                // delLogBed(rowId);
                                // popup chon phong luu
                                if (rowData != null) {

                                    paramInput = {

                                        tonkhoid: rowData.TONKHOID,
                                        khoid: $("#cboKhoLap").val()

                                    };

                                    dlgPopup = DlgUtil.buildPopupUrl("divDlgPhongLuu", "divDlg", "manager.jsp?func=../duoc/DUC41T001_ChonPhongLuuThuoc", paramInput, "Chọn nơi lưu trữ 1", 900, 340);

                                    DlgUtil.open("divDlgPhongLuu");
                                }
                            },
                            'selKhoa_Mo': function (t) {
                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                var _parPQ = 'DUC61S001_DSThuocTrongKho' + '$';
                                var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ);
                                for (var i = 0; i < result.length; i++) {
                                    if (result[i].ELEMENT_ID == 'selKhoa_Mo') {

                                        if (result[i].ROLES == '0' || result[i].ROLES == '') {
                                            DlgUtil.showMsg('Không có quyền sử dụng');
                                            return;
                                        }
                                    }
                                }
                                if (rowData != null) {
                                    var _par = [rowData.TONKHOID, rowData.KHOA == 1 ? 0 : 1];
                                    if (PHARMA_NHAP_LYDOKHOA == '1') {
                                        paramInput = {
                                            tonkhoid: rowData.TONKHOID,
                                            khoa: rowData.KHOA == 1 ? 0 : 1,
                                            type: 1
                                        };

                                        dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapGhiChu", "divDlg", "manager.jsp?func=../duoc/DUC61S001_NhapGhiChu", paramInput, "Nhập lý do khóa, mở thuốc vật tư", 500, 340);

                                        DlgUtil.open("divDlgNhapGhiChu");
                                    } else {
                                        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC63T001.KHOATHUOC", _par.join('$'));
                                        if (result == '1')
                                            DlgUtil.showMsg(rowData.KHOA == 1 ? 'Mở khóa thuốc/vật tư thành công!' : 'Khóa thuốc/vật tư thành công');
                                        else
                                            DlgUtil.showMsg(rowData.KHOA == 1 ? 'Mở khóa thuốc/vật tư không thành công!' : 'Khóa thuốc/vật tư không thành công');
                                        loadGrid();
                                    }

                                }
                            },
                            'selLogKhoa_Mo': function (t) {
                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                // popup lich su khoa mo tvt
                                if (rowData != null) {

                                    paramInput = {
                                        tonkhoid: rowData.TONKHOID
                                    };

                                    dlgPopup = DlgUtil.buildPopupUrl("divDlgLogKhoaThuoc", "divDlg", "manager.jsp?func=../duoc/DUC61S001_LogKhoaThuoc", paramInput, "Lịch sử khóa thuốc/vật tư", 900, 340);
                                    DlgUtil.open("divDlgLogKhoaThuoc");
                                }
                            }
                        },
                        onContextMenu: function (event, menu) {
                            var rowId = $(event.target).parent("tr").attr("id");
                            var grid = $("#" + _gridLogId);
                            var rowData = grid.jqGrid('getRowData', rowId);
                            if (rowData.KHOA == 1)
                                $('#selKhoa_Mo').find('.khoathuoc').text('Mở khóa thuốc/vật tư');
                            else
                                $('#selKhoa_Mo').find('.khoathuoc').text('Khóa thuốc/vật tư');
                            grid.setSelection(rowId);
                            GridUtil.unmarkAll(_gridLogId);
                            GridUtil.markRow(_gridLogId, rowId);
                            return true;
                        }

                    });

                    $("#guide_color").css("display", "none");


                } else {
                    $(".jqgrow", "#" + _gridLogId).contextMenu('contextMenu', {
                        bindings: {
                            'selPhongLuu': function (t) {

                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                // delLogBed(rowId);
                                // popup chon phong luu
                                if (rowData != null) {

                                    paramInput = {

                                        tonkhoid: rowData.TONKHOID,
                                        khoid: $("#cboKhoLap").val()

                                    };

                                    dlgPopup = DlgUtil.buildPopupUrl("divDlgPhongLuu", "divDlg", "manager.jsp?func=../duoc/DUC41T001_ChonPhongLuuThuoc", paramInput, "Chọn nơi lưu trữ 1", 900, 340);

                                    DlgUtil.open("divDlgPhongLuu");
                                }
                            },
                            'selKhoa_Mo': function (t) {
                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                if (rowData != null) {
                                    if (rowData.KHOACHA == '1') {
                                        DlgUtil.showMsg('Mở khóa tồn kho trước khi thực hiện khóa/mở khóa lô thuốc!');
                                        return;
                                    }

                                    var _param = [];
                                    _param.push({
                                        "TONKHOCTID": rowData.TONKHOCTID,
                                        "TONKHOID": rowData.TONKHOID,
                                        "THUOCVATTUID": rowData.THUOCVATTUID,
                                        "SOLUONG": rowData.SLKHADUNG,
                                        "SOLUONG_TK": rowData.SLTONKHO,
                                        "SOLO": rowData.SOLO,
                                        "CHECK": rowData.KHOA == 0 ? 'TRUE' : 'FALSE',
                                        "KHOA": rowData.KHOA
                                    });
                                    if (PHARMA_NHAP_LYDOKHOA == '1') {
                                        paramInput = {
                                            tonkhoctid: rowData.TONKHOCTID,
                                            tonkhoid: rowData.TONKHOID,
                                            thuocvattuid: rowData.THUOCVATTUID,
                                            soluong: rowData.SLKHADUNG,
                                            sltonkho: rowData.SLTONKHO,
                                            solo: rowData.SOLO,
                                            check: rowData.KHOA == 0 ? 'TRUE' : 'FALSE',
                                            khoa: rowData.KHOA,
                                            khoid: $('#cboKhoLap').val(),
                                            type: 2
                                        };

                                        dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapGhiChu", "divDlg", "manager.jsp?func=../duoc/DUC61S001_NhapGhiChu", paramInput, "Nhập lý do khóa, mở thuốc vật tư", 500, 340);

                                        DlgUtil.open("divDlgNhapGhiChu");
                                    } else {
                                        var json_par = JSON.stringify(_param);
                                        var _par = [json_par, $('#cboKhoLap').val(), rowData.THUOCVATTUID];
                                        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC63T001.03", _par.join('$'));
                                        var data = $.parseJSON(result);
                                        var _succes = data.SUCCESS;
                                        if (_succes == '0')
                                            DlgUtil.showMsg(data.MESSAGE);
                                        else if (rowData.KHOA == 1)
                                            DlgUtil.showMsg('Mở khóa thuốc/vật tư thành công!');
                                        else
                                            DlgUtil.showMsg('Khóa thuốc/vật tư thành công!');
                                        loadGrid();
                                    }

                                }
                            },
                            'selLogKhoa_Mo': function (t) {
                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                // popup lich su khoa mo tvt
                                if (rowData != null) {

                                    paramInput = {
                                        tonkhoid: rowData.TONKHOID,
                                        tonkhoctid: rowData.TONKHOCTID
                                    };

                                    dlgPopup = DlgUtil.buildPopupUrl("divDlgLogKhoaThuocCT", "divDlg", "manager.jsp?func=../duoc/DUC61S001_LogKhoaThuocCT", paramInput, "Lịch sử khóa thuốc/vật tư", 900, 340);
                                    DlgUtil.open("divDlgLogKhoaThuocCT");
                                }
                            }
                        },
                        onContextMenu: function (event, menu) {
                            var rowId = $(event.target).parent("tr").attr("id");
                            var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                            if (rowData.KHOA == 1)
                                $('#selKhoa_Mo').find('.khoathuoc').text('Mở khóa thuốc/vật tư');
                            else
                                $('#selKhoa_Mo').find('.khoathuoc').text('Khóa thuốc/vật tư');
                            var grid = $("#" + _gridLogId);
                            grid.setSelection(rowId);
                            GridUtil.unmarkAll(_gridLogId);
                            GridUtil.markRow(_gridLogId, rowId);
                            return true;
                        }

                    });
                    $("#guide_color").css("display", "");

                    $(document).ready(function () {
                        var callback = function () {
                            if ($.active !== 0) {
                                setTimeout(callback, '500');
                                return;
                            }
                            _formatRow();
                        };
                        callback();

                    });
                }
            }
        });
        if (that.opt.hospitalId != '7282') {
            $("#grvDSThuoc").jqGrid("setColProp", 'SLKHADUNG', {
                formatter: myFormat
            });
        }
        if (that.opt.hospitalId != '7282') {
            $("#grvDSThuoc").jqGrid("setColProp", 'SLTONKHO', {
                formatter: myFormat
            });
        }

        function myFormat(cellvalue, options, rowObject) {
            if (cellvalue.startsWith('.')) {
                return '0' + cellvalue;
            } else {
                return cellvalue;
                //Number(cellvalue).format(2, 3, ',');
                //cellvalue

            }
        }

    };

    function checkStatusLock(arr) {
        arr.sort();
        return arr[0] == arr[arr.length - 1]
    }

    function bindEvent() {
        KhoiTaoCot();

        //	$('.ui-search-toolbar').hide();
        //ductx - bvtm-2813
        $("#btnLock").on("click", function (e) {
            var _gridLogId = "grvDSThuoc";
            var arr = $("#grvDSThuoc").jqGrid('getGridParam', 'selarrrow');
            //var arrct = $("#grvDSThuoc").jqGrid('getGridParam', 'selarrrow');
            if (arr.length == 0) {
                DlgUtil.showMsg("Bạn phải chọn tối thiểu 1 thuốc!");
                return;
            }

            var arrThuocID = [];

            if ($("#ckbXemChiTiet").prop('checked') == false) {
                for (var i = 0; i < arr.length; i++) {
                    var rowData = $('#grvDSThuoc').jqGrid('getRowData', arr[i]);
                    if (rowData != null) {
                        var _par = [rowData.TONKHOID, rowData.KHOA == 1 ? 0 : 1];
                        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC63T001.KHOATHUOC", _par.join('$'));
                        if (result == '1')
                            //DlgUtil.showMsg(rowData.KHOA == 1 ? 'Mở khóa thuốc/vật tư thành công!' : 'Khóa thuốc/vật tư thành công');
                            arrThuocID.push(result);
                        else
                            //DlgUtil.showMsg(rowData.KHOA == 1 ? 'Mở khóa thuốc/vật tư không thành công!' : 'Khóa thuốc/vật tư không thành công');
                            arrThuocID.push(result);

                    }

                }
                if (checkStatusLock(arrThuocID) == true) {
                    arrThuocID = [];
                    DlgUtil.showMsg('Thao tác thành công!');
                } else {
                    arrThuocID = [];
                    DlgUtil.showMsg('Thao tác không thành công!');
                }
            } else {
                for (var i = 0; i < arr.length; i++) {
                    var rowData = $('#grvDSThuoc').jqGrid('getRowData', arr[i]);
                    if (rowData != null) {
                        var _param = [];
                        _param.push({
                            "TONKHOCTID": rowData.TONKHOCTID,
                            "TONKHOID": rowData.TONKHOID,
                            "THUOCVATTUID": rowData.THUOCVATTUID,
                            "SOLUONG": rowData.SLKHADUNG,
                            "SOLUONG_TK": rowData.SLTONKHO,
                            "SOLO": rowData.SOLO,
                            "CHECK": rowData.KHOA == 0 ? 'TRUE' : 'FALSE',
                            "KHOA": rowData.KHOA
                        });
                        var json_par = JSON.stringify(_param);
                        var _par = [json_par, $('#cboKhoLap').val(), rowData.THUOCVATTUID];
                        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC63T001.03", _par.join('$'));
                        var data = $.parseJSON(result);
                        var _succes = data.SUCCESS;
                        if (_succes == '0')
                            DlgUtil.showMsg(data.MESSAGE);
                        else if (rowData.KHOA == 1)
                            arrThuocID.push(_succes);
                        //DlgUtil.showMsg('Mở khóa thuốc/vật tư thành công!');
                        else
                            arrThuocID.push(_succes);
                        //DlgUtil.showMsg('Khóa thuốc/vật tư thành công!');
                    }
                }
                if (checkStatusLock(arrThuocID) == true) {
                    arrThuocID = [];
                    DlgUtil.showMsg('Thao tác thành công!');
                } else {
                    arrThuocID = [];
                    DlgUtil.showMsg('Thao tác không thành công!');
                }

            }
            loadGrid();
        });

        //BVTM-5920: DongNV
        $("#btnDoiLoaiBu").on("click", function (e) {
            var arr = $("#grvDSThuoc").jqGrid('getGridParam', 'selarrrow');
            if (arr.length == 0) {
                DlgUtil.showMsg("Bạn phải chọn tối thiểu 1 thuốc!");
                return;
            }

            if ($("#ckbXemChiTiet").prop('checked') == true) {
                DlgUtil.showMsg("Bạn phải bỏ chọn [Xem chi tiết] khi sử dụng chức năng này!");
                return;
            }

            var dsthuocvattuid = '';
            for (var i = 0; i < arr.length; i++) {
                var rowData = $('#grvDSThuoc').jqGrid('getRowData', arr[i]);
                if (rowData != null) {
                    if (dsthuocvattuid == '') {
                        dsthuocvattuid += rowData.THUOCVATTUID;
                    } else {
                        dsthuocvattuid += ',' + rowData.THUOCVATTUID;
                    }
                }
            }

            paramInput = {
                thuocvattuid: dsthuocvattuid,
                khoid: $("#cboKhoLap").val()

            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgDoiLoaiBu", "divDlg", "manager.jsp?func=../duoc/DUC61S001_DoiLoaiBu", paramInput, "Đổi loại bù", 650, 250);

            DlgUtil.open("divDlgDoiLoaiBu");
        });
        $("#toolbarIdbtnHanSuDung").on(
            "click",
            function (e) {
                // DlgUtil.showMsg("bach viem hong");
                var _par = {
                    khoid: $("#cboKhoLap").val(), loaikho: lk
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNTNCC", "divDlg",
                    "manager.jsp?func=../duoc/DUC39T001_QLHanSDThuoc",
                    _par, " Hạn sử dụng", 1200, 600);
                dlgPopup.open("dlgNTNCC");
            });
        $("#toolbarIdbtnChonPhongLuu").on(
            "click",
            function (e) {
                var tonkho = '';
                for (var i = 0; i < dsTonKhoId.length; i++) {
                    if (tonkho == '')
                        tonkho = dsTonKhoId[i];
                    else
                        tonkho = tonkho + ',' + dsTonKhoId[i];
                }

                paramInput = {
                    tonkhoid: tonkho,
                    khoid: $("#cboKhoLap").val()

                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgPhongLuu", "divDlg", "manager.jsp?func=../duoc/DUC41T001_ChonPhongLuuThuoc", paramInput, "Chọn nơi lưu trữ 1", 900, 340);

                DlgUtil.open("divDlgPhongLuu");
            });
        $("#toolbarIdbtnLichSu").on(
            "click",
            function (e) {
                // DlgUtil.showMsg("bach viem hong");
                var _par = {
                    tonkhoId: $("#hidTonKhoID").val(),
                    tonkhoctId: $("#hidTonKhoCTID").val(),
                    mathuoc: _mathuoc,
                    tenthuoc: _tenthuoc,
                    hoatchat: _hoatchat

                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNTNCC", "divDlg",
                    "manager.jsp?func=../duoc/DUC42T001_LichSuNhapThuoc",
                    _par, " Lịch sử nhập thuốc", 1200, 600);
                dlgPopup.open("dlgNTNCC");
            });

        // Cau hinh rieng cho site Quang Tri them nut the kho
        if (PHARMA_SHOW_TRACUU_THEKHO == '1') {
            $("#toolbarIdbtnChonTheKho").show();

            $("#toolbarIdbtnChonTheKho").on("click", function (e) {
                var _par = {
                    khoid: $("#cboKhoLap").val(),
                    thuocvattuid : _thuovattuid,
                    mathuoc: _mathuoc,
                    check : "1"
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgTheKho", "divDlg", "manager.jsp?func=../duoc/DUC005_THEKHO", _par, 'Tra cứu thẻ kho', 1400, 700);

                DlgUtil.open("divDlgTheKho");
            });
        }

        emitXmlHeader = function (_DataType) {
            var headerRow = '<ss:Row>\n';
            for (var colName in _DataType) {
                headerRow += '  <ss:Cell>\n';
                headerRow += '    <ss:Data ss:Type="String">';
                headerRow += colName + '</ss:Data>\n';
                headerRow += '  </ss:Cell>\n';
            }
            headerRow += '</ss:Row>\n';
            return '<?xml version="1.0"?>\n' +
                '<ss:Workbook xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">\n' +
                '<ss:Worksheet ss:Name="Sheet1">\n' +
                '<ss:Table>\n\n' + headerRow;
        };

        emitXmlFooter = function () {
            return '\n</ss:Table>\n' +
                '</ss:Worksheet>\n' +
                '</ss:Workbook>\n';
        };
        jsonToSsXml = function (_jsonObject, _DataType) {
            var row;
            var col;
            var xml;
            var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;

            xml = emitXmlHeader(_DataType);

            for (row = 0; row < data.length; row++) {
                xml += '<ss:Row>\n';

                for (var col in data[row]) {
                    xml += '  <ss:Cell>\n';
                    xml += '    <ss:Data ss:Type="' + _DataType[col] + '">';
                    xml += data[row][col] + '</ss:Data>\n';
                    xml += '  </ss:Cell>\n';
                }

                xml += '</ss:Row>\n';
            }

            xml += emitXmlFooter();
            return xml;
        };

        download = function (content, filename, contentType, _rptExport, flag) {
            if (!contentType) contentType = 'application/octet-stream';
            var a = document.getElementById(_rptExport);
            var blob = new Blob([content], {
                'type': contentType
            });
            a.href = window.URL.createObjectURL(blob);
            a.download = filename;
            if (flag == 1) {
                a.click();
            }
        };

        onClickXuatExcel();

    }

    function _formatRow() {
        var d = new Date();

        var _month = (d.getMonth() + 1);
        var _date = d.getDate();
        if (_month < 10) {
            _month = '0' + _month;
        }
        if (_date < 10) {
            _date = '0' + _date;
        }

        var strDate = _date + "/" + _month + "/" + d.getFullYear();
        var _datenow = moment(strDate, 'DD/MM/YYYY');


        var rowIds = $('#grvDSThuoc').jqGrid('getDataIDs');

        for (var i = 1; i <= rowIds.length; i++) {
            var rowData = $('#grvDSThuoc').jqGrid('getRowData', i);
            var hsd = rowData['HANSUDUNG'];
            var _hsd = moment(hsd, 'DD/MM/YYYY');
            var _color = '';

            if (90 < Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) && Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) < 181) {
                _color = '#ff8000';

            }
            if (0 < Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) && Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) < 91) {

                _color = '#ffff00';

            }
            if (Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) < 1) {
                _color = '#ff66a3';


            }
            // check khoa lo thuoc
            var khoa = rowData['KHOA'];
            var khoacha = '';
            if ($("#ckbXemChiTiet").prop('checked') == true) {
                khoacha = rowData['KHOACHA'];
            }

            if (khoa == '1' || khoacha == '1') {
                $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                    //    	    		    	        $(element).css({'background-color':'#BDBDBD'});
                    $(element).css('text-decoration', 'line-through');
                    // $(element).css.style.text-decoration = "line-through";
                    //    	    		    	        document.getElementById("myP").style.textDecoration = "line-through";
                });
            }
            $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                $(element).css({'background-color': _color});

            });
        }
    }

    function subtractdays(str1, str2, format, time) {
        var fromDate = moment(str1, format);
        var toDate = moment(str2, format);
        if (typeof time == 'undefined') {
            time = 'seconds';
        }
        var diff = fromDate.diff(toDate, time);
        return diff;
    }

    $('#cboKhoLap').change(function () {
        loadGrid();
    });
    $('#txtTimKiem').change(function () {
        loadGrid();
    });

    $('#txtHoatChat').change(function () {
        loadGrid();
    });

    $('#cboTonKho').change(function () {
        loadGrid();
    });
    $('#ckbThuocDaKhoa').change(function () {
        loadGrid();
    });
    $('#cboNhomThuoc').change(function () {
        loadGrid();
    });
    $('#cboNguonCT').change(function () {
        loadGrid();
    });
    $('#cboNhomBHXH').change(function () {
        loadGrid();
    });
    $('#ckbXemChiTiet').change(function () {
        if ($("#ckbXemChiTiet").prop('checked') == true) {
            $("#dsthuochethan").show();
            $("#btnDoiLoaiBu").hide();
            //daidv 20190401 BDHCM
            _flag = 1;
            if (PHARMA_TAIFILE_TRACUUTK == 1) {
                $('#chk_xuatfiles').trigger("click");
            }
            _flag = 0;
            if (PHARMA_SHOW_CANHBAO_TON == '1') {
                $("#canhbaosl_color").show();
                $("#ckbtoithieu").prop('checked', false);
                $("#ckbtoida").prop('checked', false);
            }
        } else {
            $("#dsthuochethan").hide();
            $("#btnDoiLoaiBu").show();
            if (PHARMA_SHOW_CANHBAO_TON == '1') {
                $("#canhbaosl_color").hide();
                $("#ckbtoithieu").prop('checked', false);
                $("#ckbtoida").prop('checked', false);
            }
        }
        delete $("#grvDSThuoc");
        $('#grvDSThuoc').GridUnload('#grvDSThuoc');
        KhoiTaoCot();
        //		 bindEvent();
        loadGrid();
        while (dsTonKhoId.length) {
            dsTonKhoId.pop();
        }
    });
    $('#ckbDSThuocHetHan').change(function () {
        loadGrid();
    });

    $('#ckbtoithieu').change(function () {
        if ($("#ckbtoithieu").prop('checked') == true) {
            var rowIds = $('#grvDSThuoc').jqGrid('getDataIDs');

            for (var i = 1; i <= rowIds.length; i++) {
                var rowData = $('#grvDSThuoc').jqGrid('getRowData', i);
                var _color = '';
                var tonToiThieu = Number(rowData.TONTOITHIEU);
                var slTonkho = Number(rowData.SLTONKHO);
                if (tonToiThieu > 0 && slTonkho < tonToiThieu) {
                    _color = '#334CFF';
                }

                $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                    $(element).css({'background-color': _color});

                });
            }
        } else {
            loadGrid();
        }
    });

    $('#ckbLechTon').change(function () {
        loadGrid();
    });

    $('#ckbtoida').change(function () {
        if ($("#ckbtoida").prop('checked') == true) {
            var d = new Date();
            var _month = (d.getMonth() + 1);
            var _date = d.getDate();
            if (_month < 10) {
                _month = '0' + _month;
            }
            if (_date < 10) {
                _date = '0' + _date;
            }

            var strDate = _date + "/" + _month + "/" + d.getFullYear();
            var _datenow = moment(strDate, 'DD/MM/YYYY');

            var rowIds = $('#grvDSThuoc').jqGrid('getDataIDs');

            for (var i = 1; i <= rowIds.length; i++) {
                var rowData = $('#grvDSThuoc').jqGrid('getRowData', i);
                var _color = '';
                var tonToiDa = Number(rowData.TONTOIDA);
                var slTonkho = Number(rowData.SLTONKHO);
                var hsd = rowData['HANSUDUNG'];
                var _hsd = moment(hsd, 'DD/MM/YYYY');
                if (tonToiDa > 0 && slTonkho > tonToiDa
                    && 0 < Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) && Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) < 91) {
                    _color = '#46FF33';
                }

                $("#grvDSThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                    $(element).css({'background-color': _color});

                });
            }
        } else {
            loadGrid();
        }
    });

    EventUtil.setEvent("assignSevice_cancel", function (e) {
        // alert('parent event e.ncc= '+e.ncc+' id= '+e.id);
        DlgUtil.close("dlgNTNCC");
        // loadGrid();
        // loadGridCT();
        initControl();

    });
    EventUtil.setEvent("drug_cancel", function (e) {
        DlgUtil.close("divDlgThongTinThuoc");
        loadGrid();
    });
    EventUtil.setEvent("PhongLuu_success", function (e) {
        DlgUtil.close("divDlgPhongLuu");
        loadGrid();
    });
    EventUtil.setEvent("DoiLoaiBu_success", function (e) {
        DlgUtil.close("divDlgDoiLoaiBu");
    });
    EventUtil.setEvent("nhapkho_cancel", function (e) {
        DlgUtil.close("dlgNhapKho");
    });
    EventUtil.setEvent("LogKhoaTVT_success", function (e) {
        DlgUtil.close("divDlgLogKhoaThuoc");
    });
    EventUtil.setEvent("LogKhoaTVTCT_success", function (e) {
        DlgUtil.close("divDlgLogKhoaThuocCT");
    });
    EventUtil.setEvent("CLOSE_DUC61S001_NhapGhiChu", function (e) {
        DlgUtil.close("divDlgNhapGhiChu");
        loadGrid();
    });
    $('#btnTimKiem').on('click', function () {
        if ($("#cboKhoLap").val() == '') {
            DlgUtil.showMsg('Chưa chọn kho!');
        }
        loadGrid();

    });

    function _exportexcel() {
        var _sql_par = [$("#txtTimKiem").val().trim(),
            ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
            ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
            ($("#cboTonKho").val()),
            ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
            $("#cboKhoLap").val(),
            $("#txtHoatChat").val().trim(),
            ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
            ($("#ckbHSD90").prop('checked') == true ? "1" : "0")
        ];
        var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC61S001.XUATEXCEL", _sql_par.join('$'));
        //	    if(mya == null || mya.length == 0){
        //	    	DlgUtil.showMsg('Không tìm thấy hồ sơ nào để tạo file Excel!');
        //
        //	    	return 0;
        //	    }
        var columnNms = $("#grvDSThuoc").jqGrid('getGridParam', 'colNames');
        //var _colModel=$("#"+gridId).jqGrid ('getGridParam', 'colModel');
        var theads = "";
        $("#dLoading").removeClass("hidden");
        //console.log('_colModel='+JSON.stringify(_colModel));
        //  var jon = [{"name":"John"},{"name":"Wick"},{"name":"123"}];
        // create colnum tieu de
        var _colModel = [
            {"label": "MA", "name": "MA", "width": "90", "align": "left"},
            {"label": "TEN", "name": "TEN", "width": "90", "align": "left"},
            {"label": "DONVITINH", "name": "DONVITINH", "width": "90", "align": "left"},
            {"label": "SLDAUKY", "name": "SLDAUKY", "width": "90", "align": "left"},
            {"label": "SLTONKHO", "name": "SLTONKHO", "width": "90", "align": "left"},
            {"label": "SLKHADUNG", "name": "SLKHADUNG", "width": "90", "align": "left"},
            {"label": "MAHOATCHAT", "name": "MAHOATCHAT", "width": "90", "align": "left"},
            {"label": "HOATCHAT", "name": "HOATCHAT", "width": "90", "align": "left"},
            {"label": "HAMLUONG", "name": "HAMLUONG", "width": "90", "align": "left"},
            {"label": "SDK", "name": "SDK", "width": "90", "align": "left"},
            {"label": "QD_THAU", "name": "QD_THAU", "width": "90", "align": "left"},
            {"label": "DONGIA", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "HANG_SX", "name": "HANG_SX", "width": "90", "align": "left"},
            {"label": "NHACUNGCAP", "name": "NHACUNGCAP", "width": "90", "align": "left"}
        ];

        var _DataType = {

            "MA": "String",
            "TEN": "String",
            "DONVITINH": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "MAHOATCHAT": "String",
            "HOATCHAT": "String",
            "HAMLUONG": "String",
            "SDK": "String",
            "QD_THAU": "String",
            "DONGIA": "String",
            "HANG_SX": "String",
            "NHACUNGCAP": "String"
        };


        _appFileType = "application/vnd.ms-excel";
        //	       if(mya != null && mya.length > 0){
        $("#dLoading").addClass("hidden");
        download(jsonToSsXml(mya, _DataType), 'DL_ThuocVatTu.xls', _appFileType, 'chk_xuatfiles');
        //			}
    }


    $('#chk_xuatfiles').on("click", function (event) {
        //_exportexcel();
        if (_param[0] == 965 || _param[0] == 26720) {
            exportExcel2();
        } else if (_param[0] == 10284) {
            _exportexcelBDHNI();
        } else if (PHARMA_XUAT_EXCEL_GRID == '1') {
            _exportexcelGRID();
        } else {
            _exportexcelBDHCM();
        }

    });

    //Begin: DAIDV20190117 - BDHCM
    function exportDmExcel() {

        var params = [{
            name: 'tenvatttu',
            type: 'String',
            value: $("#txtTimKiem").val().trim()
        }, {
            name: 'xemchitiet',
            type: 'String',
            value: $("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"
        }, {
            name: 'thuocdakhoa',
            type: 'String',
            value: $("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"
        }, {
            name: 'tonkho',
            type: 'String',
            value: $("#cboTonKho").val()
        }, {
            name: 'khoid',
            type: 'String',
            value: $("#cboKhoLap").val()
        },
            {
                name: 'hoatchat',
                type: 'String',
                value: $("#txtHoatChat").val().trim()
            }
        ];
        var reportCode = 'DUC061_BCDMTHUOC_BDHCM';
        var reportName = reportCode + "_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
        CommonUtil.inPhieu('window', reportCode, 'xls', params, reportName);
    }

    function onClickXuatExcel() {
        $('#btnXuatEXCS').on("click", function (event) {
            exportDmExcel();
        });
    }

    //End: DAIDV20190117

    // Chuyen sang dung jasper xuat excel --> fix loi du lieu khi parse excel
    function exportExcel2() {

        var params = [{
            name: 'tenvatttu',
            type: 'String',
            value: $("#txtTimKiem").val().trim()
        }, {
            name: 'xemchitiet',
            type: 'String',
            value: $("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"
        }, {
            name: 'thuocdakhoa',
            type: 'String',
            value: $("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"
        }, {
            name: 'tonkho',
            type: 'String',
            value: $("#cboTonKho").val()
        }, {
            name: 'khoid',
            type: 'String',
            value: $("#cboKhoLap").val()
        },
            {
                name: 'hoatchat',
                type: 'String',
                value: $("#txtHoatChat").val().trim()
            }
        ];
        var reportCode = 'DUC061_BCDTVT_TONKHO';
        var reportName = '';
        if ($("#ckbXemChiTiet").prop('checked') == true)
            var reportName = "DL_ThuocVatTu_ChiTiet" + "_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
        else
            var reportName = "DL_ThuocVatTu" + "_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
        //var reportName= reportCode+"_"+ jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xls';
        CommonUtil.inPhieu('window', reportCode, 'xls', params, reportName);
        openReport('window', reportCode, 'pdf', params);
    }

    //daidv 20190401 BDHCM
    function _exportexcelBDHCM() {
        var _sql_par = [$("#txtTimKiem").val().trim(),
            ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
            ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
            ($("#cboTonKho").val()),
            ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
            $("#cboKhoLap").val(),
            $("#txtHoatChat").val().trim(),
            ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
            ($("#ckbHSD90").prop('checked') == true ? "1" : "0")
        ];
        var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC61S001.XUATEXCEL", _sql_par.join('$'));
        //		    if(mya == null || mya.length == 0){
        //		    	DlgUtil.showMsg('Không tìm thấy hồ sơ nào để tạo file Excel!');
        //
        //		    	return 0;
        //		    }
        var columnNms = $("#grvDSThuoc").jqGrid('getGridParam', 'colNames');
        //var _colModel=$("#"+gridId).jqGrid ('getGridParam', 'colModel');
        var theads = "";
        $("#dLoading").removeClass("hidden");
        //console.log('_colModel='+JSON.stringify(_colModel));
        //  var jon = [{"name":"John"},{"name":"Wick"},{"name":"123"}];
        // create colnum tieu de
        var _colModel = [
            {"label": "STT", "name": "STT", "width": "90", "align": "left"},
            {"label": "MA", "name": "MA", "width": "90", "align": "left"},
            {"label": "TEN", "name": "TEN", "width": "90", "align": "left"},
            {"label": "DONVITINH", "name": "DONVITINH", "width": "90", "align": "left"},
//			{ "label": "SLDAUKY", "name": "SLDAUKY", "width": "90", "align": "left" },
            {"label": "SLTONKHO", "name": "SLTONKHO", "width": "90", "align": "left"},
            {"label": "SLKHADUNG", "name": "SLKHADUNG", "width": "90", "align": "left"},
            {"label": "MAHOATCHAT", "name": "MAHOATCHAT", "width": "90", "align": "left"},
            {"label": "HOATCHAT", "name": "HOATCHAT", "width": "90", "align": "left"},
            {"label": "SOLO", "name": "SOLO", "width": "90", "align": "left"},
            {"label": "NGAYLAP", "name": "NGAYLAP", "width": "90", "align": "left"},
            {"label": "HANSUDUNG", "name": "HANSUDUNG", "width": "90", "align": "left"},
            {"label": "NHOMTHUOC", "name": "NHOMTHUOC", "width": "90", "align": "left"},
            {"label": "HAMLUONG", "name": "HAMLUONG", "width": "90", "align": "left"},
            {"label": "SDK", "name": "SDK", "width": "90", "align": "left"},
            {"label": "QD_THAU", "name": "QD_THAU", "width": "90", "align": "left"},
            {"label": "DONGIA", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "GIANHAP", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "GIADICHVU", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "GIANHANDAN", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "GIABHYT", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "HANG_SX", "name": "HANG_SX", "width": "90", "align": "left"},
            {"label": "NHACUNGCAP", "name": "NHACUNGCAP", "width": "90", "align": "left"},
            {"label": "SO_HOA_DON", "name": "SO_HOA_DON", "width": "90", "align": "left"},
            {"label": "SO_CHUNG_TU", "name": "SO_CHUNG_TU", "width": "90", "align": "left"},
			{"label": "TENNHOM", "name": "TENNHOM", "width": "90", "align": "left"},
			{"label": "DONGGOI", "name": "DONGGOI", "width": "90", "align": "left"}
        ];

        var _DataType = {

            "STT": "String",
            "MA": "String",
            "TEN": "String",
            "DONVITINH": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "MAHOATCHAT": "String",
            "HOATCHAT": "String",
            "SOLO": "String",
            "NGAYLAP": "String",
            "HANSUDUNG": "String",
            "NHOMTHUOC": "String",
            "HAMLUONG": "String",
            "SDK": "String",
            "QD_THAU": "String",
            "DONGIA": "String",
            "GIANHAP": "String",
            "GIADICHVU": "String",
            "GIANHANDAN": "String",
            "GIABHYT": "String",
            "HANG_SX": "String",
            "NHACUNGCAP": "String",
            "SO_HOA_DON": "String",
            "SO_CHUNG_TU": "String",
			"TENNHOM": "String",
			"DONGGOI": "String"
        };

        var _DataType1 = {

            "MA": "String",
            "TEN": "String",
            "DONVITINH": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "MAHOATCHAT": "String",
            "HOATCHAT": "String",
            "HAMLUONG": "String",
            "SDK": "String",
            "QD_THAU": "String",
            "DONGIA": "String",
            "HANG_SX": "String",
            "NHACUNGCAP": "String"
        };


        _appFileType = "application/vnd.ms-excel";
        //		       if(mya != null && mya.length > 0){
        $("#dLoading").addClass("hidden");
        if ($("#ckbXemChiTiet").prop('checked') == true)
            //download(jsonToSsXml(mya, _DataType), 'DL_ThuocVatTu_ChiTiet.xls', _appFileType, 'chk_xuatfiles',_flag);
            exportXLSX(mya, _DataType, 'DL_ThuocVatTu_ChiTiet', "DL_ThuocVatTu_ChiTiet.xlsx");

        else
            //download(jsonToSsXml(mya, _DataType1), 'DL_ThuocVatTu.xls', _appFileType, 'chk_xuatfiles',_flag);
            exportXLSX(mya, _DataType, 'DL_ThuocVatTu', "DL_ThuocVatTu.xlsx");
        //				}
    }

    //ChuanNT cau hinh theo grid 30112021
    function _exportexcelGRID() {
        var _sql_par = [$("#txtTimKiem").val().trim(),
            ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
            ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
            ($("#cboTonKho").val()),
            ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
            $("#cboKhoLap").val(),
            $("#txtHoatChat").val().trim(),
            ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
            ($("#ckbHSD90").prop('checked') == true ? "1" : "0")
        ];
        var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC61S001.GRID.EXCEL", _sql_par.join('$'));
        //		    if(mya == null || mya.length == 0){
        //		    	DlgUtil.showMsg('Không tìm thấy hồ sơ nào để tạo file Excel!');
        //
        //		    	return 0;
        //		    }
        var columnNms = $("#grvDSThuoc").jqGrid('getGridParam', 'colNames');
        //var _colModel=$("#"+gridId).jqGrid ('getGridParam', 'colModel');
        var theads = "";
        $("#dLoading").removeClass("hidden");
        //console.log('_colModel='+JSON.stringify(_colModel));
        //  var jon = [{"name":"John"},{"name":"Wick"},{"name":"123"}];
        // create colnum tieu de
        var _colModel = [
            {"label": "STT", "name": "STT", "width": "90", "align": "left"},
            {"label": "MA", "name": "MA", "width": "90", "align": "left"},
            {"label": "TEN", "name": "TEN", "width": "90", "align": "left"},
            {"label": "DONVITINH", "name": "DONVITINH", "width": "90", "align": "left"},
            {"label": "DONGIA", "name": "DONGIA", "width": "90", "align": "left"},
            {"label": "THANHTIEN", "name": "THANHTIEN", "width": "90", "align": "right"},
            {"label": "GIABHYT", "name": "GIABHYT", "width": "90", "align": "right"},
            {"label": "SLDAUKY", "name": "SLDAUKY", "width": "90", "align": "left"},
            {"label": "SLTONKHO", "name": "SLTONKHO", "width": "90", "align": "left"},
            {"label": "SLKHADUNG", "name": "SLKHADUNG", "width": "90", "align": "left"},
            {"label": "MAHOATCHAT", "name": "MAHOATCHAT", "width": "90", "align": "left"},
            {"label": "HOATCHAT", "name": "HOATCHAT", "width": "90", "align": "left"},
            {"label": "DINHMUC", "name": "DINHMUC", "width": "90", "align": "left"},
            {"label": "HAMLUONG", "name": "HAMLUONG", "width": "90", "align": "left"},
            {"label": "SDK", "name": "SDK", "width": "90", "align": "left"},
            {"label": "QD_THAU", "name": "QD_THAU", "width": "90", "align": "left"},
            {"label": "HANG_SX", "name": "HANG_SX", "width": "90", "align": "left"},
            {"label": "NHACUNGCAP", "name": "NHACUNGCAP", "width": "90", "align": "left"},
            {"label": "KHOA", "name": "KHOA", "width": "90", "align": "left"},
            {"label": "GOITHAU", "name": "GOITHAU", "width": "90", "align": "left"},
            {"label": "NHOMTHAU", "name": "NHOMTHAU", "width": "90", "align": "left"},
			{"label": "TENNHOM", "name": "TENNHOM", "width": "90", "align": "left"},
			{"label": "DONGGOI", "name": "DONGGOI", "width": "90", "align": "left"}
        ];

        var _DataType = {

            "STT": "String",
            "MA": "String",
            "TEN": "String",
            "DONVITINH": "String",
            "DONGIA": "String",
            "THANHTIEN": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "MAHOATCHAT": "String",
            "HOATCHAT": "String",
            "DINHMUC": "String",
            "HAMLUONG": "String",
            "SDK": "String",
            "QD_THAU": "String",
            "HANG_SX": "String",
            "NHACUNGCAP": "String",
            "KHOA": "String",
            "GOITHAU": "String",
            "NHOMTHAU": "String"
        };

        var _DataType1 = {

            "STT": "String",
            "MA": "String",
            "TEN": "String",
            "HOATCHAT": "String",
            "HAMLUONG": "String",
            "DONVITINH": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "GIABHYT": "String",
            "DONGIA": "String",
            "THANHTIEN": "String",
            "SOLO": "String",
            "HANSUDUNG": "String",
            "QD_THAU": "String",
            "BIETDUOC": "String",
            "NGAYLAP": "String",
            "STT_THAU": "String",
            "KHOA": "String",
            "NHACUNGCAP": "String",
            "MAHOATCHAT": "String",
            "GOITHAU": "String",
            "NHOMTHAU": "String"
        };


        _appFileType = "application/vnd.ms-excel";
        //		       if(mya != null && mya.length > 0){
        $("#dLoading").addClass("hidden");
        if ($("#ckbXemChiTiet").prop('checked') == true)
            //download(jsonToSsXml(mya, _DataType), 'DL_ThuocVatTu_ChiTiet.xls', _appFileType, 'chk_xuatfiles',_flag);
            exportXLSX(mya, _DataType1, 'DL_ThuocVatTu_ChiTiet', "DL_ThuocVatTu_ChiTiet.xlsx");

        else
            //download(jsonToSsXml(mya, _DataType1), 'DL_ThuocVatTu.xls', _appFileType, 'chk_xuatfiles',_flag);
            exportXLSX(mya, _DataType, 'DL_ThuocVatTu', "DL_ThuocVatTu.xlsx");
        //				}
    }

    //DongNV 20210610 BVTM-3517 START
    function _exportexcelBDHNI() {
        if ($("#cboNhomThuoc").val().toString() != '') {
            _nhomthuoc = $("#cboNhomThuoc").val().toString()
        } else {
            _nhomthuoc = '-1';
        }
        var _sql_par = [$("#txtTimKiem").val().trim(),
            ($("#ckbXemChiTiet").prop('checked') == true ? "1" : "0"),
            ($("#ckbThuocDaKhoa").prop('checked') == true ? "1" : "0"),
            ($("#cboTonKho").val()),
            ($("#ckbDSThuocHetHan").prop('checked') == true ? "1" : "0"),
            $("#cboKhoLap").val(),
            $("#txtHoatChat").val().trim(),
            ($("#ckbHSD180").prop('checked') == true ? "1" : "0"),
            ($("#ckbHSD90").prop('checked') == true ? "1" : "0"),
            _nhomthuoc
        ];
        var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC61S001.EXCELHNI", _sql_par.join('$'));
        $("#dLoading").removeClass("hidden");

        var _DataType = {

            "STT": "String",
            "MA": "String",
            "TEN": "String",
            "DONVITINH": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "DINHMUC": "String",
            "MAHOATCHAT": "String",
            "HOATCHAT": "String",
            "NHOMTHUOC": "String",
            "HAMLUONG": "String",
            "SDK": "String",
            "QD_THAU": "String",
            "DONGIA": "String",
            "HANG_SX": "String",
            "NHACUNGCAP": "String",
            "SO_HOA_DON": "String",
            "SO_CHUNG_TU": "String"
        };

        var _DataTypeChiTiet = {

            "STT": "String",
            "MA": "String",
            "TEN": "String",
            "DONVITINH": "String",
            "SLDAUKY": "String",
            "SLTONKHO": "String",
            "SLKHADUNG": "String",
            "MAHOATCHAT": "String",
            "HOATCHAT": "String",
            "SOLO": "String",
            "NGAYLAP": "String",
            "HANSUDUNG": "String",
            "NHOMTHUOC": "String",
            "HAMLUONG": "String",
            "SDK": "String",
            "QD_THAU": "String",
            "DONGIA": "String",
            "HANG_SX": "String",
            "NHACUNGCAP": "String",
            "SO_HOA_DON": "String",
            "SO_CHUNG_TU": "String",
			"TENNHOM": "String",
			"DONGGOI": "String"
        };


        _appFileType = "application/vnd.ms-excel";
        $("#dLoading").addClass("hidden");
        if ($("#ckbXemChiTiet").prop('checked') == true)
            exportXLSX(mya, _DataTypeChiTiet, 'DL_ThuocVatTu_ChiTiet', "DL_ThuocVatTu_ChiTiet.xlsx");

        else
            exportXLSX(mya, _DataType, 'DL_ThuocVatTu', "DL_ThuocVatTu.xlsx");
        //				}
    }

    //DongNV 20210610 BVTM-3517 END


    //		$('#chk_xuatfiles').on("click",function(event){
    //			_exportexcel();
    //		});

    function loadKho() {

        var sql_par = that.opt.lk + '$' + $("#txtTimKho").val().trim() + '$';
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O(
            "DUC01S002.DSKHO4", sql_par);
        //		var sql_par2 =   $("#txtTimKho").val()+'$';
        //		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O(
        //				"DUC67T001.35", sql_par2);


        ComboUtil.getComboTag("cboKhoLap", "DUC01S002.DSKHO4",
            sql_par, "", "", "sp", '', function () {
            });

    };
    $('#txtTimKho').on('change', function (e) {
        loadKho();
    });

    function exportXLSX(_jsonObject, _DataType, _wsname, _filename) {
        var wb = new Workbook();
        var ws = objectToXLSX(_jsonObject, _DataType);

        /* add worksheet to workbook */
        wb.SheetNames.push(_wsname);
        wb.Sheets[_wsname] = ws;
        var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'binary'});
        //		download(s2ab(wbout), _filename, 'application/vnd.ms-excel', 'chkEXP_REPORT_20');
        saveAs(new Blob([s2ab(wbout)], {type: "application/octet-stream"}), _filename);
    }

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }

    function Workbook() {
        if (!(this instanceof Workbook)) return new Workbook();
        this.SheetNames = [];
        this.Sheets = {};
    }

    objectToXLSX = function (_jsonObject, _DataType) {

        var ws = {};
        var row;
        var col;
        var xml;
        var data = typeof _jsonObject != "object" ? JSON.parse(_jsonObject) : _jsonObject;
        var range = {s: {c: 10000000, r: 10000000}, e: {c: 0, r: 0}};
        var col_arr = [];
        if (_DataType) {
            col_arr = Object.keys(_DataType);

            for (row = 0; row <= data.length; row++) {
                for (var col = 0; col < col_arr.length; col++) {
                    if (range.s.r > row) range.s.r = row;
                    if (range.s.c > col) range.s.c = col;
                    if (range.e.r < row) range.e.r = row;
                    if (range.e.c < col) range.e.c = col;
                    _col = col_arr[col];
                    var cell = row > 0 ? {v: data[row - 1][_col]} : {v: _col};
                    if (cell.v == null) continue;
                    var cell_ref = XLSX.utils.encode_cell({c: col, r: row});
                    if (row == 0) cell.t = 's';
                    if (typeof cell.v === 'number') {
                        cell.t = 'n';
                    } else {
                        cell.t = _DataType[_col]
                    }
                    ;
                    if (row > 0 && (_col == "SLDAUKY" ||
                        _col == "SLTONKHO" ||
                        _col == "SLKHADUNG" ||
                        _col == "DONGIA" ||
                        _col == "GIABHYT" ||
                        _col == "THANHTIEN")) {
                        cell.t = 'n';
                    }

                    ws[cell_ref] = cell;
                }
            }
        } else if (data.length > 0) {
            col_arr = Object.keys(data[0]);

            for (row = 0; row <= data.length; row++) {
                for (var col = 0; col < col_arr.length; col++) {
                    if (range.s.r > row) range.s.r = row;
                    if (range.s.c > col) range.s.c = col;
                    if (range.e.r < row) range.e.r = row;
                    if (range.e.c < col) range.e.c = col;
                    _col = col_arr[col];
                    var cell = row > 0 ? {v: data[row - 1][_col]} : {v: _col};
                    if (cell.v == null) continue;
                    var cell_ref = XLSX.utils.encode_cell({c: col, r: row});
                    if (row == 0) {
                        cell.t = 's';
                    } else if (typeof cell.v === 'number') cell.t = 'n';
                    else if (typeof cell.v === 'boolean') cell.t = 'b';
                    else if (cell.v instanceof Date) {
                        cell.t = 'n';
                        cell.z = XLSX.SSF._table[14];
                        cell.v = datenum(cell.v);
                    } else cell.t = 's';

                    ws[cell_ref] = cell;
                }
            }
        }
        if (range.s.c < 10000000)
            ws['!ref'] = XLSX.utils.encode_range(range);
        return ws;
    };

    function checkRole(control) {
        $('#' + control).prop("disabled", true);
        var _parPQ = 'DUC61S001_DSThuocTrongKho' + '$';
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ);
        for (var i = 0; i < result.length; i++) {
            if (result[i].ELEMENT_ID == control) {
                if (result[i].ROLES == '1') {
                    $('#' + result[i].ELEMENT_ID).prop("disabled", false);
                }
                if (result[i].ROLES == '0' || result[i].ROLES == '') {
                    $('#' + result[i].ELEMENT_ID).prop("disabled", true);
                }
            }
        }
    };

    function replaceStrtoNum(strSoluong) {
        return Number(strSoluong.replace(/[^0-9\.]+/g, ""));
    };

    function isNumeric(n) {
        return !isNaN(parseFloat(n)) && isFinite(n);
    };
}
