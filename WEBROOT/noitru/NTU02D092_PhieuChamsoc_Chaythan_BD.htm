<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../noitru/NTU02D092_PhieuChamsoc_Chaythan_BD.js?v=202206"></script>
<script type="text/javascript" src="..//noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script src="../common/script/highcharts/highcharts.js"></script>
<script src="../common/script/highcharts/highcharts-more.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>

<div id="divMain" class="container">
	<div class="mgt10">
		<div class="container" id='divTTBN'>
			<ul class="nav nav-tabs">
				<li class="active"><a href="#">Thông tin bệnh nhân</a></li>
			</ul>
			<div class="col-md-12 low-padding mgt10">
				<div class="col-md-1 low-padding">
					<label>Mã BA</label>
				</div>
				<div class="col-md-3 low-padding">
					<input class="form-control input-sm" style="width:100%;" id="txtMABENHAN" disabled>	
				</div>
				<div class="col-md-1 low-padding ">
					<label class="mgl20">Mã BN</label>
				</div>
				<div class="col-md-3 low-padding">
					<input class="form-control input-sm" style="width:100%;" id="txtMABENHNHAN" disabled>	
				</div>
				<div class="col-md-1 low-padding">
					<label class="mgl20">Tên BN</label>
				</div>
				<div class="col-md-3">
					<input class="form-control input-sm" style="width:100%;" id="txtTENBENHNHAN" disabled>	
				</div>
			</div>
		</div>
		
		<div class="container">
			<ul class="nav nav-tabs">
				<li role="presentation" id="tabSinhTon" class="active">
					<a href="#divSinhTon" data-toggle="tab">Thông tin lần lọc</a></li>
				<!-- <li id="tabTheLuc">
					<a href="#divTheLuc" data-toggle="tab">Thông tin thể lực</a></li> -->
			</ul>
			<div id="divSinhTon" class="tab active">
			
				<div class="col-xs-12 low-padding mgt3">
			     	<!-- <div class="col-md-2 low-padding required">
						<label>Mạch (lần/phút)</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm clsnumber" style="width:100%;" id="txtMACH" maxlength="3" valrule="Mạch,required|greater_than[0]" disabled>	
					</div>
					<div class="col-md-2 low-padding required">
						<label class="mgl10">Thân nhiệt (&#8451;)</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm clsfloat" style="width:100%;" id="txtNHIETDO" maxlength="4" valrule="Nhiệt độ,required|greater_than[0]" disabled>	
					</div> -->
					<div class="col-md-2 low-padding required">
			     		<label >Ngày lọc</label>
			     	</div>
			     	<div class="col-md-2 low-padding">
			     		<div class="input-group">	  
							<input class="form-control input-sm" valrule="Ngày chạy thận,required|datetime" id="txtNGAYCHAYTHAN" data-mask="00/00/0000" placeholder="dd/MM/yyyy HH:MM:SS" disabled>
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"  id='calNGAYCHAYTHAN' type="sCal"  onclick="NewCssCal('txtNGAYCHAYTHAN','ddMMyyyy','dropdown',true,'24',true)"></span>
						</div> 		     		
			     	</div>
			     	<!-- <div class="col-md-2 low-padding ">
						<label class="mgl10">Thời gian chạy thận</label>
					</div>
					<div class="col-md-2 low-padding">
								<select class="form-control input-sm kb-i-col-m "   id="cboTHOIGIANCHAYTHAN" style="width:100%;" disabled>							
									<option value="1">3H30</option>
									<option value="2">4H</option>
								</select>
					</div> -->
					<div class="col-md-2 low-padding required">
			     		<label class="mgl10" >Thời gian kết thúc</label>
			     	</div>
			     	<div class="col-md-2 low-padding">
			     		<div class="input-group">	  
							<input class="form-control input-sm" valrule="Thời gian kết thúc,required|datetime" id="txtTGKETTHUC" data-mask="00/00/0000" placeholder="dd/MM/yyyy HH:MM:SS" disabled>
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"  id='calTGKETTHUC' type="sCal"  onclick="NewCssCal('txtTGKETTHUC','ddMMyyyy','dropdown',true,'24',true)"></span>
						</div> 		     		
			     	</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Lần sử dụng</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm "   style="width:100%;" id="txtLANSUDUNG" maxlength="100" disabled>	
					</div>
			    </div>
			    
			    
			    <div class="col-md-12 low-padding">
			     	<!-- <div class="col-md-2 low-padding required">
						<label>Nhịp thở (lần/phút)</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm clsnumber" style="width:100%;" id="txtNHIPTHO" maxlength="10" valrule="Nhịp thở,required|greater_than[0]" disabled>	
					</div>
					<div class="col-md-2 low-padding required">
						<label class="mgl10">Huyết áp (mmHg)</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm clsnumber" id="txtHUYETAP_HIGHT" style="width: 45%;" maxlength="3" valrule="Huyết áp cao,required|greater_than[0]" disabled>&nbsp;/&nbsp;												     	
				     	<input class="form-control input-sm clsnumber" id="txtHUYETAP_LOW" style="width: 45%;" maxlength="3" valrule="Huyết áp thấp,required|greater_than[0]" disabled>
					</div> -->
					<!-- <div class="col-xs-2 low-padding">
						<label class="mgl10">Thời gian chỉ định</label>
					</div>
					<div class="col-xs-2 low-padding">
						<div class="input-group" style="width: 100%;">	
						  <input class="form-control input-sm" id="txtTHOIGIAN_CHIDINH" name="txtTHOIGIAN_CHIDINH" valrule="Thời gian chỉ định,datetime" title="" data-mask="00/00/0000 00:00:00" disabled placeholder="dd/MM/yyyy HH:MM:SS">
						  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id='calTHOIGIAN_CHIDINH' type="sCal"  onclick="NewCssCal('txtTHOIGIAN_CHIDINH','ddMMyyyy','dropdown',true,'24',true)"></span>							 
						</div>
					</div> -->
			    </div>
			    
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding ">
						<label>Số máy</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm "    style="width:100%;" id="txtSOMAY" maxlength="100" disabled>	
					</div>
					<!-- <div class="col-md-2 low-padding required">
						<label class="mgl10">Vận tốc máu(ml/p)</label>
					</div> 
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " valrule="Vận tốc máu(ml/p),required|greater_than[0]" id="txtVANTOCMAU" style="width: 100%;" maxlength="100" disabled>											     	
					</div>-->
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Màng lọc</label>
					</div>
					<div class="col-md-2 low-padding">
								<input class="form-control input-sm "    style="width:100%;" id="txtMANGLOC" maxlength="100" disabled>
								<!--<select class="form-control input-sm kb-i-col-m "  id="cboMANGLOC" style="width:100%;" disabled>							
									<option value="1">1.5</option>
									<option value="2">1.7</option>
								</select>-->
								<!-- <div class="">
									<label class="mgl5"><input type="checkbox" id="chkMANGLOC_15" value="" disabled=""> 1.5</label>
								</div>
								<div class="">
									<label class="mgl5"><input type="checkbox" id="chkMANGLOC_17" value="" disabled=""> 1.7</label>
								</div> -->
					</div>		
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Đường máu sử dụng</label>
					</div>
					<div class="col-md-2 low-padding">
								<select class="form-control input-sm kb-i-col-m " id="cboDUONGMAUSUDUNG" style="width:100%;" disabled>							
									<option value="1">Catheter</option>
									<option value="2">FAV</option>
								</select>
					</div>
					<!-- <div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtNUOCTIEU" style="width: 100%;" maxlength="100" disabled>											     	
					</div> -->
			    </div>
			    
			    <div class="col-xs-12 low-padding mgt3">
			     	
					
					
					<div class="col-md-2 low-padding ">
						<label>Trọng lượng khô</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtTRONGLUONG_KHO"  style="width: 100%;" maxlength="100" disabled>											     	
					</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Trọng lượng rút</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtTRONGLUONG_RUT"  style="width: 100%;" maxlength="100" disabled>											     	
					</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Liều tấn công</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtLIEUTANCONG"  style="width: 100%;" maxlength="100" disabled>											     	
					</div>
			    </div>
			    
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding ">
						<label>Kháng đông</label>
					</div>
					<div class="col-md-2 low-padding">
								<select class="form-control input-sm kb-i-col-m "  id="cboKHANGDONG" style="width:100%;" disabled>							
									<option value="1">Heparin</option>
									<option value="2">Free</option>
									<option value="3">Lovenox</option>
								</select>
					</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Dịch lọc:Bicarbonate Na+</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtDICHLOC_BICAR"  style="width: 100%;" maxlength="100" disabled>											     	
					</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Liều duy trì</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtLIEUDUYTRI"  style="width: 100%;" maxlength="100" disabled>											     	
					</div>
			    </div>
			    
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding ">
						<label>Cân nặng: Trước lọc</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm "  style="width:100%;" id="txtCANNANG_TRUOC" maxlength="100" disabled>	
					</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Cân nặng: Sau lọc</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtCANNANG_SAU" style="width: 100%;" maxlength="100" disabled>											     	
					</div>
					<div class="col-md-2 low-padding ">
						<label class="mgl10">Tổng liều</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " id="txtTONGLIEU"  style="width: 100%;" maxlength="100" disabled>											     	
					</div>
			    </div>
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding ">
						<label>Bác sỹ thực hiện</label>
					</div>
					<div class="col-md-1 low-padding">
						<input class="form-control input-sm "  style="width:100%;" id="txtfilerBS" maxlength="2000" >	
					</div>
					<div class="col-xs-3 low-padding">
                        <select class="form-control input-sm" id="cboBACSY_TH" modeDis="" filterLike="txtfilerBS"  style="width: 100%; float: right;" disabled>				   
						</select>
                    </div>
                   
					<div class="col-md-2 low-padding ">
						<label>Điều dưỡng thực hiện</label>
					</div>
					<div class="col-md-1 low-padding">
						<input class="form-control input-sm "  style="width:100%;" id="txtfilerDD" maxlength="2000" >	
					</div>
					<div class="col-xs-3 low-padding">
                        <select class="form-control input-sm" id="cboDIEUDUONG_TH" modeDis="" filterLike="txtfilerDD"  style="width: 100%; float: right;" disabled>				   
						</select>
                    </div>
			    </div>
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding ">
						<label>Ghi chú</label>
					</div>
					<div class="col-md-10 low-padding">
						<input class="form-control input-sm "  style="width:100%;" id="txtGHICHU" maxlength="2000" disabled>	
					</div>
			    </div>
			    
			    <!-- <div class="col-xs-12 low-padding mgt3">
					<div class="col-md-2 low-padding required">
						<label>Tri giác</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " valrule="Tri giác,required"  id="txtTRIGIAC" style="width: 100%;" maxlength="100" disabled>											     	
					</div>
			    </div>
			    
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding required">
						<label>Áp lực ĐM</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm " valrule="Áp lực ĐM,required"  style="width:100%;" id="txtAPLUC_DM" maxlength="100" disabled>	
					</div>
					<div class="col-md-2 low-padding required">
						<label class="mgl10">Áp lực TM</label>
					</div>
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " valrule="Áp lực TM,required" id="txtAPLUC_TM" style="width: 100%;" maxlength="100" disabled>											     	
					</div>
			    </div>
			    
			    <div class="col-xs-12 low-padding mgt3">
			     	<div class="col-md-2 low-padding required">
						<label>TMP</label>
					</div>
					
					<div class="col-md-2 low-padding form-inline">
						<input class="form-control input-sm " valrule="TMP,required" id="txtTMP" style="width: 100%;" maxlength="100" disabled>											     	
					</div>
					<div class="col-md-2 low-padding required">
						<label class="mgl10">Siêu lọc</label>
					</div>
					<div class="col-md-2 low-padding">
						<input class="form-control input-sm " valrule="Siêu lọc,required" style="width:100%;" id="txtSIEULOC" maxlength="100" disabled>	
					</div>
			    </div> -->
			    <div id="divAddST"></div>
			    <div id="divAddXN"></div>
			    <div class="col-xs-12 low-padding ">
					
					<div class="col-xs-2 low-padding">
						<label class="mgt">Theo dõi diễn biến</label>
					</div>
					<div class="col-xs-4 low-padding">
						<textarea class="form-control i-col-full" rows="4" id="txtTHEODOI_DIENBIEN" maxlength="1000" disabled></textarea>
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl10">Thực hiện y lệnh / Chăm sóc</label>
					</div>
					<div class="col-xs-4 low-padding">
						<textarea class="form-control i-col-full" rows="4" id="txtTHUCHIEN_YLENH" maxlength="1000" disabled></textarea>
					</div>
				</div>
			    
			    
			    
			    <!-- <div class="col-xs-12 low-padding">
			    	<div class="col-md-2 low-padding">
						<label>Tạo mẫu</label>
					</div>
					<div class="col-xs-2 low-padding">
						<select class="form-control input-sm mgt3" id="cboMAU" style="width: 98%!important;"></select>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="col-xs-2 low-padding mgt3">
							<label>Nhập tên mẫu</label>
						</div>
						<div class="col-xs-3 low-padding">
							 <input class="form-control input-sm mgt3" id="txtTENMAUBENHPHAM_TEMP" name="txtTENMAUBENHPHAM_TEMP" maxlength="200" style="width:100% !important;">
						</div>
						<div class="col-xs-3 low-padding" style="float: right; margin-right: 15px !important;">
						<div class="col-xs-6 low-padding">
							<div class="col-xs-4 low-padding" style="margin-left: 10px;">
								<button type="button" class="btn btn-sm btn-primary" id="btnLuuMau" style="float:right;">
								<span class="glyphicon glyphicon-floppy-disk"></span> Lưu thành mẫu</button>
							</div>
							<div class="col-xs-4 low-padding" style="margin-left: 10px;">
								<button type="button" class="btn btn-sm btn-primary" id="btnXoaMau" name="btnXoaMau" style="float:right;" disabled>
								<span class="glyphicon glyphicon-floppy-disk"></span> Xóa mẫu</button>
							</div>
						</div>
					</div>
				</div> -->
			    
		     	<div class="col-md-12 low-padding slim_solid_line mgt10" style="text-align: center;">
		     		<button type="button" class="btn btn-default btn-primary" id="btnThem"><span class="glyphicon glyphicon-plus-sign"></span> Thêm</button>
					<button type="button" class="btn btn-default btn-primary" id="btnSua" disabled><span class="glyphicon glyphicon-edit"></span> Sửa</button>
					<button type="button" class="btn btn-default btn-primary" id="btnChiSo" disabled><span class="glyphicon glyphicon-plus-sign"></span> Chỉ số ST</button>
					<button type="button" class="btn btn-default btn-primary" id="btnChiSoXN" disabled><span class="glyphicon glyphicon-plus-sign"></span> Chỉ số XN</button>
					<button type="button" class="btn btn-default btn-primary" id="btnXoa" disabled><span class="glyphicon glyphicon-remove-circle"></span> Xóa</button>
					<button type="button" class="btn btn-default btn-primary" id="btnLuu" disabled><span class="glyphicon glyphicon-floppy-disk"></span> Lưu</button>
					<button type="button" class="btn btn-default btn-primary" id="btnHuy" disabled><span class="glyphicon glyphicon-remove-circle"></span> Hủy</button>
					<button type="button" class="btn btn-default btn-primary" id="btnIn" disabled><span class="glyphicon glyphicon-floppy-disk"></span> In</button>
					<button type="button" class="btn btn-default btn-primary" id="btnClose" ><span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
		     	</div>
				<div class="col-md-12 low-padding">
	     			<table id="grdSinhTon"></table><div id="pager_grdSinhTon"></div>
	     		</div>
			</div>
			
		</div>
		
		
		<!-- <div class="container" id='divBieudo'>
			<ul class="nav nav-tabs">
				<li class="active"><a href="#">Biểu đồ</a></li>
			</ul>
			<div class="col-xs-12 low-padding">
				<div class="panel panel-default mgt10">
				    <div class="panel-heading" style=" font-weight: bold;">Thông tin sinh tồn</div>
				    <div class="panel-body">
				    	<div class="col-xs-12 low-padding">
				    		<div class="col-xs-12 low-padding" id="divChart" style="padding-left: -10px !important;"></div>
				    	</div>
				    </div>
				</div>
			</div>
		</div> -->
		
		
		
	</div>
	
	
</div>

<script>

	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var url ='{url}';
	var dept_id ='{dept_id}'; 
	//var khambenh_id = '{khambenh_id}';
	var session_par=[];
	
	initRest(uuid,"/vnpthis");
	var _opts={
		lang: lang,
		_param:session_par,
		_uuid:uuid,
		hospital_id: hospital_id,
		dept_id:dept_id
	}
	var data;	
    var mode = '{showMode}';    	
	if(mode=='dlg') {		
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		_opts.khambenhid = data.khambenhid;	
		_opts.benhnhanid = data.benhnhanid;	
		_opts.tiepnhanid = data.tiepnhanid;
		_opts.hosobenhanid = data.hosobenhanid;
		_opts.mabenhan = data.mabenhan;
		_opts.mabenhnhan = data.mabenhnhan;
		_opts.tenbenhnhan = data.tenbenhnhan;
		_opts.maubenhphamid = data.maubenhphamid,
		_opts.action = data.action;
	}
	var ptd = new NTU01H059_PhieuChamsoc_Chaythan(_opts);
	ptd.load();	
</script>