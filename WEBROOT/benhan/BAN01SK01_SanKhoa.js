function BAN01SK01_SANKHOA(_opts) {
	this.load = doLoad;
	var benhnhanid = _opts.benhnhanid;
	var loaibenhanid = _opts.loaibenhanid;
	var khambenhid = _opts.khambenhid;
	var hosobenhanid = _opts.hosobenhanid;
	var loaihsba_code = _opts.maloaibenhan;
	var khoaid =  _opts.khoaid;
	var selectnguoitiem = "SK_NGUOITIEM";
	var checkInsert = 1;
	var arrMaHoiKham = [];
	var arrMaHoiKhamTable = [];
	var i_hid = hospital_id;    	
	var checkInput = $(':input');
	var _gridId="grdTTCon";
	var checkupdate = "0";
	var socon = 1 ;
	var html = "";
	var html_con = "";
	var tt_con = "";
	// mang duyet list cac loai ho so phim anh
	var arrSoPhimAnh = [ "txtSOXQUANG", "txtSOSCANNER", "txtSOSIEUAM",
			"txtSOXETNGHIEM", "txtSOKHAC", "txtSOTOANBOHOSO" ];

	var TTHC;//L2PT-39095

	//ham thuc hien load trang ungdung
	function doLoad() {
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";		
		// khoi tao trang ho so benh an
		_initControl();
		// xu ly su kien click tren nut them moi
		_bindEvent();
		// export bieu mau
		if (hosobenhanid != null && hosobenhanid != '0') {
			exportBan(hosobenhanid, loaibenhanid, benhnhanid,_opts._param[4], 'BAN007_SANKHOA_QD4069_A4');
			exportKyCa(hosobenhanid, loaibenhanid, benhnhanid, 'BAN007_SANKHOA_QD4069_A4');
		}
		_calBMI();
		// close trang ung dung
		closePage();

		//L2PT-37315
		if ( ["50860"].includes(_opts._param[0]) ){
			$('#divGDBV_TRK').show();
			$('#divBSDT').show();
		}
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BAN_SANKHOA_CHONTCKN') == '1') {
			$('#dvTCKN_CHON').show();
			$('#dvTCKN_NHAP').hide();
		}
	}
	
	function _initControl() {
		this._gridHeader="BENHAN_TTCON_ID,BENHAN_TTCON_ID,0,0,t,l;"+
						"HOSOBENHANID,HOSOBENHANID,0,0,t,l;"+
						"TTC_DELUCGIO,TTC_DELUCGIO,0,0,t,l;"+
						"TTC_DELUCPHUT,TTC_DELUCPHUT,0,0,t,l;"+
						"Ngày đẻ,TTC_DENGAY,100,0,f,l;"+
						"TTC_AG1PHUT,TTC_AG1PHUT,0,0,t,l;"+
						"TTC_AG5PHUT,TTC_AG5PHUT,0,0,t,l;"+
						"TTC_AG10PHUT,TTC_AG10PHUT,0,0,t,l;"+
						"Giới tính,GIOITINHVIEW,70,0,f,l;"+
						"TTC_GIOITINH,TTC_GIOITINH,0,0,t,l;"+
						"Cân nặng,TTC_TDCANNANG,70,0,f,l;"+
						"Chiều cao,TTC_TDCAO,70,0,f,l;"+
						"TTC_TDVONGDAU,TTC_TDVONGDAU,0,0,t,l;"+
						"TTC_TATBAMSINH,TTC_TATBAMSINH,0,0,t,l;"+
						"TTC_COHAUMON,TTC_COHAUMON,0,0,t,l;"+
						"CT Tật bẩm sinh,TTC_CTTATBAMSINH,180,0,f,l;"+
						"TT trẻ sau đẻ,TTC_TTTRESOSINHSD,180,0,f,l;"+
						"Xử lý kết quả,TTC_XULYKETQUA,180,0,f,l;"+
						"TTC_NGATSINH,TTC_NGATSINH,0,0,t,l;"+
						"TTC_CHETSINH,TTC_CHETSINH,0,0,t,l;"+
						"TTC_CHET22,TTC_CHET22,0,0,t,l;"+
						"TTC_CHETLUU,TTC_CHETLUU,0,0,t,l;"+
						"TTC_VITAMINK,TTC_VITAMINK,0,0,t,l;"+
						"TTC_VIEMGANB,TTC_VIEMGANB,0,0,t,l;"+
						"TTC_SOLO,TTC_SOLO,0,0,t,l;"+
						"TTC_HANDUNG,TTC_HANDUNG,0,0,t,l;"+
						"TTC_NHIETDOTHUOC,TTC_NHIETDOTHUOC,0,0,t,l;"+
						"Người tiêm,TTC_NGUOITIEM,100,0,f,l;"+
						"Ngày tiêm,TTC_NGAYTIEM,100,0,f,l;"+
						"Ghi chú,TTC_GHICHU,180,0,f,l";
		GridUtil.init(_gridId,"100%","100px","",false,this._gridHeader,false, { rowNum: 10,rowList: [10, 20, 30]});
		// xu ly su kien load trang khi moi vao
				
		//L2PT-42752
		ComboUtil.getComboTag("cboMAUBAID", "HSBA.MAU1", [{"name" : "[0]", "value" : loaibenhanid}], [ {"name" : "[0]","value" : "1"} ], {value : '', text : 'Chọn'}, "", ""); 
 
		doLoadCombo("txtMABENHCHINH","txtBENHCHINH");
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BAN_LOADPHANBIET_KEMTHEO') == 1){
			doLoadComboKemTheo("txtMAPHANBIET","txtPHANBIET");
		}else{
		doLoadCombo("txtMAPHANBIET","txtPHANBIET");
		}
		doLoadCombo("txtMACDSAUPT","txtCDSAUPT");
		doLoadCombo("txtMACDTRUOCPT","txtCDTRUOCPT");
       	var sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : khoaid
		});	
		var _col="Người tiêm,TEN,100,0,f,l";
		ComboUtil.initComboGrid("txtNGUOITIEM","SK_NGUOITIEM",sql_par, "600px", _col, function(event, ui) {
			$("#txtNGUOITIEM").val(ui.item.TEN);			
			return false;
		});
		if($("#cldTINHHINHPHAUTHUAT11").val() == '' || $("#txtTINHHINHPHAUTHUAT21").val() == '' ){
			var sql_par=[];
			sql_par.push({"name":"[0]","value": hosobenhanid});
			var data_arr = jsonrpc.AjaxJson.ajaxExecuteQueryO("DLG_KQPTT_L1", sql_par);
			var datas = $.parseJSON(data_arr);
			var _objThongtin = new Object();
			if (datas.length > 0 ){
				for(var i=0; i<datas.length; i++) {
					var rowData = datas[i];
					_objThongtin["TINHHINHPHAUTHUAT1"+(i+1)] = rowData.NGAYPHAUTHUATTHUTHUAT;
					_objThongtin["TINHHINHPHAUTHUAT2"+(i+1)] = rowData.VOCAM;
					_objThongtin["TINHHINHPHAUTHUAT3"+(i+1)] = rowData.BSYPHAUTHUAT;
					_objThongtin["TINHHINHPHAUTHUAT4"+(i+1)] = rowData.GAYME;
				}
				FormUtil.setObjectToForm("bdPPDTPTTT","",_objThongtin);
				$("#cldTINHHINHPHAUTHUAT11").val(_objThongtin.TINHHINHPHAUTHUAT11);
				$("#cldTINHHINHPHAUTHUAT12").val(_objThongtin.TINHHINHPHAUTHUAT12);
				$("#cldTINHHINHPHAUTHUAT13").val(_objThongtin.TINHHINHPHAUTHUAT13);
				$("#cldTINHHINHPHAUTHUAT14").val(_objThongtin.TINHHINHPHAUTHUAT14);
			}
		}
		var _checkTTCon = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA", 'BAN_SANKHOA_TTCON');
		if(_checkTTCon == 0){
			$("#tabTTCon").hide();
			$("#divTTCon").css("display", "");
		}
		else{
			$("#tabTTCon").show();
			$("#divTTCon").css("display", "none");
		}
		$("#divTTConTab :input").attr("disabled", true);
		_disableControl(['btnSearch', 'btnThemTTC'], false);
		_disableControl(['btnLuuTTC', 'btnHuyTTC','btnSuaTTC','btnXoaTTC'], true);
		GridUtil.setGridParam(_gridId,{ 
	    	onSelectRow: function(index, selected) {
	    		if(selected === false){
	    			FormUtil.clearForm("divTTConTab");
	    		}else{
	    			var selRowId = $('#' + _gridId).jqGrid('getGridParam','selrow');
					var rowData = $('#' + _gridId).jqGrid('getRowData',selRowId);
		    		FormUtil.setObjectToForm("tabBenhNhanThongTinCon","",rowData);
					$('input[name=radTTC_GIOITINH][value=' + rowData.TTC_GIOITINH + ']').prop('checked', true);
					_disableControl(['btnSearch','btnThemTTC','btnSuaTTC', 'btnXoaTTC'], false);
					_disableControl(['btnLuuTTC','btnHuyTTC'], true);
	    		}
	    		GridUtil.unmarkAll(_gridId);
	    		GridUtil.markRow(_gridId,index);
	    	}
	    });
		//L2PT-39095
		try{
			if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA", 'BAN_SK_THONGTIN_CON_BDHNI') == 1)
			{
			  $('#divcon2_bdhni').show();
			}
			if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA", 'BAN_SANKHOA_THONGTIN_CON') == 1){  // thật Phúc yên: 28880
				$('#div39095_1').show();
				$('#div39095_2').hide();
				$('#div39095_3').show();
				addCon(socon);
				$("#cldKINHCUOITUNGAY").on("change", function(e) {
					try{
						var dateString = $('#cldKINHCUOITUNGAY').val(); // "23/10/2015";
						var bd_arr = dateString.split('/');
						var date = new Date(+bd_arr[2], bd_arr[1] - 1, +bd_arr[0]);
						date.setDate(date.getDate() + 10);
						date.setMonth(date.getMonth() - 3);
						date.setFullYear(date.getFullYear() + 1);
						var _date = (date.getDate()<10? '0':'') + date.getDate().toString() + '/'
							+ (date.getMonth()+1<10? '0':'') + (date.getMonth()+1).toString() + '/'
							+ date.getFullYear();

						$('#txtTGDUSINH').val( _date );

						var date_bd = new Date(+bd_arr[2], bd_arr[1] - 1, +bd_arr[0]);
						var tn_str = TTHC.NGAYTIEPNHAN;
						if (tn_str.indexOf(' ')>0) tn_str = tn_str.substring(0, tn_str.indexOf(' '));
						var tn_arr = tn_str.split('/');
						var date_tn = new Date(+tn_arr[2], tn_arr[1] - 1, +tn_arr[0]);

						if ( tn_arr[2]+tn_arr[1]+tn_arr[0] >= bd_arr[2]+bd_arr[1]+bd_arr[0] ){
							var diff = Math.abs(date_tn-date_bd)/ 86400000;
							$('#txtTUOITHAI').val( Math.floor((diff-1)/7+1) );
						}
						else{
							$('#txtTUOITHAI').val('');
						}
					}
					catch (ex) {}
				});

				$("#txtTGDUSINH").on("change", function(e) {
					try{
						var dateString = $('#txtTGDUSINH').val(); // "23/10/2015";
						var bd_arr = dateString.split('/');
						var date = new Date(+bd_arr[2], bd_arr[1] - 1, +bd_arr[0]);
						date.setDate(date.getDate() - 10);
						date.setMonth(date.getMonth() + 3);
						date.setFullYear(date.getFullYear() - 1);
						var _date = (date.getDate()<10? '0':'') + date.getDate().toString() + '/'
							+ (date.getMonth()+1<10? '0':'') + (date.getMonth()+1).toString() + '/'
							+ date.getFullYear();

						$('#cldKINHCUOITUNGAY').val( _date );

						var tn_str = TTHC.NGAYTIEPNHAN;
						if (tn_str.indexOf(' ')>0) tn_str = tn_str.substring(0, tn_str.indexOf(' '));
						var tn_arr = tn_str.split('/');
						var date_tn = new Date(+tn_arr[2], tn_arr[1] - 1, +tn_arr[0]);

						if ( date_tn >= date ){
							var diff = Math.abs(date_tn-date)/ 86400000;
							$('#txtTUOITHAI').val( Math.floor((diff-1)/7+1) );
						}
						else{
							$('#txtTUOITHAI').val('');
						}
					}
					catch (ex) {}
				});

				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.005", [khambenhid].join('$'));
				if (data_ar != null && data_ar.length > 0) {
					TTHC = data_ar[0];
				}
			}
		}
		catch (ex) {}
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BAN_LOADPDT_DAUTIEN') == 1) {
			var par=[];
			par.push({"name" : "[0]", "value" : hosobenhanid});
			var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.GETPDT.DAUTIEN", par);
			if (data_ar != null && data_ar.length > 0) {
				var datakq = JSON.parse(data_ar);
				$('#txtMACH').val(datakq[2].GIATRI_KETQUA);
				$('#txtNHIETDO').val(datakq[3].GIATRI_KETQUA);
				$('#txtHUYETAP1').val(datakq[5].GIATRI_KETQUA);
				$('#txtHUYETAP2').val(datakq[4].GIATRI_KETQUA);
				$('#txtNHIPTHO').val(datakq[6].GIATRI_KETQUA);
				$('#txtCANNANG').val(datakq[7].GIATRI_KETQUA);
				$('#txtCHIEUCAO').val(datakq[44].GIATRI_KETQUA);
				$('#txtTTBENHAN').val(datakq[14].GIATRI_KETQUA);
				$('#txtTOANTRANG').val(datakq[0].GIATRI_KETQUA);
				$('#txtBANTHAN').val(datakq[62].GIATRI_KETQUA);
				$('#txtGIADINH').val(datakq[69].GIATRI_KETQUA);
				$('#txtMABENHCHINH').val(datakq[10].GIATRI_KETQUA);
				$('#txtBENHCHINH').val(datakq[9].GIATRI_KETQUA);
				$('#txtBENHKEMTHEO').val(datakq[15].GIATRI_KETQUA);
			}
		}
		// Check xem da co du lieu ho so benh an khong
		loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid);
		if(hosobenhanid != undefined){
			checkInsert = 0;
			loadDataTTC();
		}
		// lấy danh sách mã hỏi khám bệnh của khoa
		arrMaHoiKham = getMaHoiKham(loaibenhanid);
		
		// Lấy danh sách mã hỏi khám bệnh dang table của khoa
		arrMaHoiKhamTable = getMaHoiKhamMaTran(loaibenhanid);
		
		// reset gia tri tong so ho so phim anh khi thay doi cac so
		// lieu(x-quang, scanner, xieu am,...)
		changePhimAnh(arrSoPhimAnh);
		//load do tuoi khi nhap nam cho truong co kinh nam va lay chong nam
		_changeNam();
		//dong trang
		closePage();
		$("#txtLYDOVAOVIEN").focus();
		var _check = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA", 'NTU_CHECK_TT_TRESOSINH');
		if (_check != '1') {
			$("#txtDELUCGIO, #txtTDCANNANG").parent().parent().prev().removeClass("required");
			$("#txtTDCAO").parent().prev().removeClass("required");
			$('#txtDELUCGIO, #txtDELUCPHUT, #cldDENGAY, #txtTDCANNANG, #txtTDCAO').removeAttr("valrule");
		}
		
		var sql_par = [];
		//L2PT-110785 start
		var _sql = "BACSI_BA2"; 
		var _col = "USER_ID,USER_ID,30,0,t,l;CCHN,CCHN,30,0,f,l;Tên bác sỹ,FULL_NAME,30,0,f,l;Ngày sinh,NGAYSINH,30,0,f,l";
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BAN_SANKHOA_CHUCDANH') == '1') {
			_sql = "BACSI_BA3"; 
			_col = "USER_ID,USER_ID,0,0,t,l;CCHN,CCHN,20,0,f,l;Tên bác sỹ,FULL_NAME,30,0,f,l;Chức danh,CHUCDANH,30,0,f,l;Ngày sinh,NGAYSINH,20,0,f,l";
		}
		ComboUtil.initComboGrid("txtTENNGUOITD", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTENNGUOITD").val(ui.item.FULL_NAME);
					if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BAN_SANKHOA_CHUCDANH') == '1') {
						var chucdanh = ui.item.CHUCDANH=='-'?'':ui.item.CHUCDANH;
						$("#txtCDNGUOITD").val(chucdanh);
					}
					return false;
				});
		//L2PT-110785 end
		ComboUtil.initComboGrid("txtTINHHINHPHAUTHUAT31", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTINHHINHPHAUTHUAT31").val(ui.item.FULL_NAME);
					return false;
				});
		
		ComboUtil.initComboGrid("txtTINHHINHPHAUTHUAT32", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTINHHINHPHAUTHUAT32").val(ui.item.FULL_NAME);
					return false;
				});
		
		ComboUtil.initComboGrid("txtTINHHINHPHAUTHUAT33", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTINHHINHPHAUTHUAT33").val(ui.item.FULL_NAME);
					return false;
				});
		
		ComboUtil.initComboGrid("txtTINHHINHPHAUTHUAT41", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTINHHINHPHAUTHUAT41").val(ui.item.FULL_NAME);
					return false;
				});
		
		ComboUtil.initComboGrid("txtTINHHINHPHAUTHUAT42", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTINHHINHPHAUTHUAT42").val(ui.item.FULL_NAME);
					return false;
				});
		
		ComboUtil.initComboGrid("txtTINHHINHPHAUTHUAT43", _sql, sql_par, "600px", _col,
				function(event, ui) {
					$("#txtTINHHINHPHAUTHUAT43").val(ui.item.FULL_NAME);
					return false;
				});
		if ($("#txtTTCON").val() != ""){
		tt_con = parseJson($("#txtTTCON").val());
			for (var i = 0; i < tt_con.length; i++) {
				if (i > 0) {
					socon = socon + 1;
					addCon(socon);
				}
				FormUtil.setObjectToForm("dvCON"+ (i+1), "", tt_con[i]);
			}
		}
		if ($('#txtXNCLXCANLAM').val().trim() != "") {
			$('#btnTONGHOPCLS').text("x");
		}else {
			$('#btnTONGHOPCLS').text("Tổng hợp KQCLS");
		}
	}

	function loadDataTTC(){
		if(hosobenhanid == undefined){
			DlgUtil.showMsg("Chưa có thông tin bệnh án, vui lòng thêm thông tin bệnh án trước khi cập nhật thông tin con !");
			return false;
		}
		var sql_par = [];	
		sql_par.push({
			"name" : "[0]",
			"value" : hosobenhanid
		});
		GridUtil.loadGridBySqlPage(_gridId,"BAN_SK_TTC_1",sql_par);
	}
	
	function _changeNam() {
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.10", [{"name" : "[0]","value" : hosobenhanid}]);
		var rows = JSON.parse(data);
		$("#txtBATDAUTHAYKINHNAM").on("change", function(e) {
			if (rows != null && rows.length > 0 && $("#txtBATDAUTHAYKINHNAM").val().trim() != '') {
				var namsinh = rows[0]["NAMSINH"];
				$("#txtTUOITHAYKINH").val($("#txtBATDAUTHAYKINHNAM").val()-namsinh);
			}
			if($("#txtBATDAUTHAYKINHNAM").val().trim() == '') {
				$("#txtTUOITHAYKINH").val('');
			}
		});
		$("#txtTUOITHAYKINH").on("change", function(e) {
			if (rows != null && rows.length > 0 && $("#txtTUOITHAYKINH").val().trim() != '') {
				var namsinh = rows[0]["NAMSINH"];
				$("#txtBATDAUTHAYKINHNAM").val( parseInt(namsinh) + parseInt($("#txtTUOITHAYKINH").val()));
			}
			if($("#txtTUOITHAYKINH").val().trim() == '') {
				$("#txtBATDAUTHAYKINHNAM").val('');
			}
		});
		$("#txtLAYCHONGNAM").on("change", function(e) {
			if (rows != null && rows.length > 0 && $("#txtLAYCHONGNAM").val().trim() != '') {
				var namsinh = rows[0]["NAMSINH"];
				$("#txtTUOILAYCHONG").val($("#txtLAYCHONGNAM").val()-namsinh);
			}
			if($("#txtLAYCHONGNAM").val().trim() == '') {
				$("#txtTUOILAYCHONG").val('');
			}
		});
		$("#txtTUOILAYCHONG").on("change", function(e) {
			if (rows != null && rows.length > 0 && $("#txtTUOILAYCHONG").val().trim() != '') {
				var namsinh = rows[0]["NAMSINH"];
				$("#txtLAYCHONGNAM").val( parseInt(namsinh) + parseInt($("#txtTUOILAYCHONG").val()));
			}
			if($("#txtTUOITHAYKINH").val().trim() == '') {
				$("#txtLAYCHONGNAM").val('');
			}
		});
	}
	
	//ham xu ly luu va cap nhat thong tin
	function _bindEvent() {
		$("#txtCANNANG").on("blur",function(e){
			_calBMI();
		});
		$("#txtCHIEUCAO").on("blur",function(e){
			_calBMI();
		});
		$("#btnThemMoi").on(
				"click",
				function(e) {
					_save(true);
				});
		$("#btnLuu").on(
				"click",
				function(e) {
					_save(false);
				});
		//TuongLT them luu benh an mau
		$("#btnLuuBAMau").on(
				"click",
				function(e) {
					_saveBAMAU(false);
				});
		//Xoa mau benh an
		$("#btnXoaBAMau").on(
				"click",
				function(e) {
					_delBAMAU(false);
				});
		$("#btnSearch").on(
				"click",
				function(e) {
					loadDataTTC();
					FormUtil.clearForm("divTTConTab", "");
				});
		$("#btnThemTTC").on(
				"click",
				function(e) {
					if(hosobenhanid == undefined){
						DlgUtil.showMsg("Chưa có thông tin bệnh án, vui lòng thêm thông tin bệnh án trước khi cập nhật thông tin con !");
						return false;
					}
					FormUtil.clearForm("divTTConTab", "");
					$("#divTTConTab :input").attr("disabled", false);
					_disableControl(['btnSearch', 'btnThemTTC', 'btnSuaTTC', 'btnXoaTTC'], true);
					_disableControl(['btnLuuTTC', 'btnHuyTTC'], false);
					$('input[name=radTTC_GIOITINH]').prop('checked', false);
					checkupdate = 0 ;
				});
		
		$("#btnSuaTTC").on(
				"click",
				function(e) {
					$("#divTTConTab :input").attr("disabled", false);
					_disableControl(['btnSearch', 'btnThemTTC', 'btnSuaTTC', 'btnXoaTTC'], true);
					_disableControl(['btnLuuTTC', 'btnHuyTTC'], false);
					checkupdate = 1;
				});	
		$("#btnHuyTTC").on(
				"click",
				function(e) {
					FormUtil.clearForm("divTTConTab", "");
					loadDataTTC();
					_disableControl(['btnSearch', 'btnThemTTC'], false);
					_disableControl(['btnLuuTTC', 'btnHuyTTC','btnSuaTTC','btnXoaTTC'], true);
					$("#divTTConTab :input").attr("disabled", true);
					$('input[name=radTTC_GIOITINH]').prop('checked', false);
				});	
		$("#btnXoaTTC").on(
				"click",
				function(e) {					
					DlgUtil.showConfirm("Bạn có muốn xóa thông tin con này ? ", function(flag){
						if(flag){
							var _par=[$("#txtBENHAN_TTCON_ID").val()];
							var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("BAN_SK_TTC_DEL",_par.join('$'));							
							if (resultCheck == "-1"){
								DlgUtil.showMsg("Có lỗi xảy ra !");
							}
							else{
								DlgUtil.showMsg("Xóa thông tin thành công !");
								FormUtil.clearForm("divTTConTab", "");
								loadDataTTC();								
								_disableControl(['btnSearch', 'btnThemTTC'], false);
								_disableControl(['btnLuuTTC', 'btnHuyTTC','btnSuaTTC','btnXoaTTC'], true);
								$("#divTTConTab :input").attr("disabled", true);
								$('input[name=radTTC_GIOITINH]').prop('checked', false);
							}
						}
					});					
				});	
		
		$("#btnLuuTTC").on(
				"click",
				function(e) {
					var object = {};
					FormUtil.setFormToObject('divTTConTab', '', object);
					object["HOSOBENHANID"] = hosobenhanid;
					object["TTC_GIOITINH"] = $("input[name='radTTC_GIOITINH']:checked").val();
					if(validate(object)){
					if(checkupdate == 0){
						ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN_SK_TTC_INS",JSON.stringify(object));
						if(ret != -1){
							DlgUtil.showMsg("Thêm mới thành công!");
							FormUtil.clearForm("divTTConTab", "");
							loadDataTTC();
							_disableControl(['btnSearch', 'btnThemTTC'], false);
							_disableControl(['btnLuuTTC', 'btnHuyTTC','btnSuaTTC','btnXoaTTC'], true);
							$("#divTTConTab :input").attr("disabled", true);
							$('input[name=radTTC_GIOITINH]').prop('checked', false);
						}
						else{
							DlgUtil.showMsg("Có lỗi xảy ra !");
						}
					}
					else{
						ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN_SK_TTC_UPS",JSON.stringify(object));
						if(ret ==1){
							DlgUtil.showMsg("Sửa đổi thành công!");
							FormUtil.clearForm("divTTConTab", "");
							loadDataTTC();
							_disableControl(['btnSearch', 'btnThemTTC'], false);
							_disableControl(['btnLuuTTC', 'btnHuyTTC','btnSuaTTC','btnXoaTTC'], true);
							$("#divTTConTab :input").attr("disabled", true);
							$('input[name=radTTC_GIOITINH]').prop('checked', false);
						}
						else{
							DlgUtil.showMsg("Có lỗi xảy ra !");
						}
					}
					}
				});
		$("#btnChose").on('click', function(){
			EventUtil.setEvent("assignChonPTTT_luu",function(e){
				FormUtil.clearForm("bdPPDTPTTT", "");
				FormUtil.setObjectToForm("bdPPDTPTTT","",e.msg);
				$("#cldTINHHINHPHAUTHUAT11").val(e.msg.TINHHINHPHAUTHUAT11);
				$("#cldTINHHINHPHAUTHUAT12").val(e.msg.TINHHINHPHAUTHUAT12);
				$("#cldTINHHINHPHAUTHUAT13").val(e.msg.TINHHINHPHAUTHUAT13);
				$("#cldTINHHINHPHAUTHUAT14").val(e.msg.TINHHINHPHAUTHUAT14);
				DlgUtil.close("dlgKQPTTT");
			});

			var myVar={
				hosobenhanid : hosobenhanid
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKQPTTT","divDlg","manager.jsp?func=../benhan/DLG_BAN_ChonKQ_PTTT",myVar,"CHỌN KẾT QUẢ PTTT",1000,400);
			DlgUtil.open("dlgKQPTTT");
		});
		$("#btnTHEM").on("click",function(){
			socon = socon + 1;
			addCon(socon);
		});
		$("#btnTRU").on("click",function(){
			if (socon > 1){
			$("#dvCON"+ socon).remove();
			socon = socon - 1;
			}
		});
		$("#btnTONGHOPCLS").on("click", function(e) {
			if ($('#txtXNCLXCANLAM').val().trim() != "") {
				$('#btnTONGHOPCLS').text("Tổng hợp CLS");
				$('#txtXNCLXCANLAM').val("");
				return;
			}
			var objData = new Object();
			objData.HOSOBENHANID = hosobenhanid;
			objData.KHAMBENHID = khambenhid;
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.TONGHOPCLS", JSON.stringify(objData));
			if (data_ar != null && data_ar.length > 0) {
				$('#txtXNCLXCANLAM').val(data_ar[0].LISTCLS);
			}
		});

	}
	function addCon(socon) {
		html_con = "";
		html_con += '<div id="dvCON' + socon + '" class=" form-inline" style="margin-top: 10px">'
		html_con += '<div class="col-xs-12 low-padding">'
		html_con += '<div class="col-xs-2 low-padding"></div>'
		html_con += '<div class="col-xs-1 low-padding">'
		html_con += '<label class=" control-label">- Trẻ thứ ' + socon + ': </label>'
		html_con += '</div>'
		html_con += '<div class="col-xs-9 low-padding">'
		html_con += '<label class=" control-label">- Cân nặng: </label>'
		html_con += '<input class="form-control input-sm" id="txtTRE' + socon + 'TDCANNANG" valrule="Cân nặng,numeric|max_length[6]" maxlength="6" name="txtTRE' + socon + 'TDCANNANG" title="" style="width: 70px;">'
		html_con += '<label class="mgl5">gram &nbsp;</label>'
		html_con += '<label class="mgl5">Cao: </label>'
		html_con += '<input class="form-control input-sm " id="txtTRE' + socon + 'TDCAO" valrule="Cao,numeric|max_length[6]" maxlength="6" name="txtTRE' + socon + 'TDCAO" title="" style="width: 70px;">'
		html_con += '<label class="mgl5">cm</label>'
		html_con += '<label class="mgl-5">Vòng đầu: </label>'
		html_con += '<input class="form-control input-sm " id="txtTRE' + socon + 'TDVONGDAU" valrule="Vòng đầu,numeric|max_length[6]" maxlength="6" name="txtTRE' + socon + 'TDVONGDAU" title="" style="width: 70px;">'
		html_con += '<label class="mgl5">cm</label>'
		html_con += '</div>'
		html_con += '</div>'
		html_con += '<div class="col-xs-12 low-padding">'
		html_con += '<div class="col-xs-3 low-padding"></div>'
		html_con += '<div class="col-xs-9 low-padding">'
		html_con += '<label class="control-label">Giới tính: Trai</label>'
		html_con += '<input class=" input-sm " id="chkTRE' + socon + 'TRAI" name="chkTRE' + socon + 'TRAI" title="" type="checkbox">'
		html_con += '<label class="control-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Gái</label>'
		html_con += '<input class=" input-sm " id="chkTRE' + socon + 'GAI" name="chkTRE' + socon + 'GAI" title="" type="checkbox">'
		html_con += '<label class="control-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Tật bẩm sinh</label>'
		html_con += '<input class=" input-sm " id="chkTRE' + socon + 'TATBAMSINH" name="chkTRE' + socon + 'TATBAMSINH" title="" type="checkbox">'
		html_con += '<label class="control-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Có hậu môn</label>'
		html_con += '<input class=" input-sm " id="chkTRE' + socon + 'COHAUMON" name="chkTRE' + socon + 'COHAUMON" title="" type="checkbox">'
		html_con += '<label class="control-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sống</label>'
		html_con += '<input class=" input-sm " id="chkTRE' + socon + 'SONG" name="chkTRE' + socon + 'SONG" title="" type="checkbox">'
		html_con += '<label class="control-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Chết</label>'
		html_con += '<input class=" input-sm " id="chkTRE' + socon + 'CHET" name="chkTRE' + socon + 'CHET" title="" type="checkbox">'
		html_con += '</div>'
		html_con += '</div>'
		html_con += '</div>'
		$("#dvTTCON").append(html_con);
	}
	function validate(object){
		if(object.TTC_DENGAY==null || object.TTC_DENGAY ==""){
			DlgUtil.showMsg("Ngày đẻ không được để trống !");
			return false;
		}
		if(object.TTC_GIOITINH == null || object.TTC_GIOITINH ==undefined){
			DlgUtil.showMsg("Chưa chọn giới tính !");
			return false;
		}
		return true;
	}
	
	function _disableControl(arrID, value){
		for(var i = 0; i < arrID.length; i++){
			$("#"+arrID[i]).attr("disabled", value);
		}		
	}
	//luu benh an mau
	function _saveBAMAU(checkClose){
		var tenmau=$("#txtTENBENHANMAU").val();
		var validate = new DataValidator("divMain-BA");
		if(validate.validateForm() && _validateFormBAMAU()){			
			// buoc 1:insert thong tin ho so benh an				
			var mauid = insHSBenhAnTemp(_opts.khoaid, _opts.phongid, tenmau, loaibenhanid);			
			// buoc 2: insert thong tin cho ho so phim anh
			var r1=insHSPhimAnhTemp(arrSoPhimAnh, mauid, checkInsert);

			// buoc 3 insert thong tin hoi kham benh va tong ket benh an
			var r2 = insHoiKhamTemp(checkInput, loaihsba_code, arrMaHoiKham, mauid, checkInsert);	
			// insert hoi kham ma tran
			var r3=insHoiKhamMaTranTemp(checkInput, loaihsba_code, arrMaHoiKhamTable, mauid, checkInsert);
				
			if(mauid==-1){
				DlgUtil.showMsg("Tên mẫu bệnh án đã tồn tại trên hệ thống");
				return false;
			}
			//xu ly su kien callback	
			if(r1==1 && r2==1 && r3==1){
				//xu ly su kien callback
				//var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");			
				//if(typeof evFunc==='function') {	
					//if(checkClose){
					//	evFunc({msg:"Cập nhật hồ sơ bệnh án mẫu thành công"});
					//}
					//else{
						DlgUtil.showMsg("Cập nhật hồ sơ bệnh án mẫu thành công");
					//}
				
		//L2PT-42752
		ComboUtil.getComboTag("cboMAUBAID", "HSBA.MAU1", [{"name" : "[0]", "value" : loaibenhanid}], [ {"name" : "[0]","value" : "1"} ], {value : '', text : 'Chọn'}, "", ""); 
 
				//}
				//else {
				//	console.log('evFunc not a function');
				//} 
			}else{
				callBack(r1, checkInsert);				
			}			
		}
	}
	//xoa mau benh an
	function _delBAMAU(checkClose){
		// Xoa mau benh an		
		 var mauid=$("#cboMAUBAID").val();
		
			var r1=deleteBenhAnTemp(mauid);
			
			//xu ly su kien callback	
			if(r1==1){
				//xu ly su kien callback
			//	var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");			
				//if(typeof evFunc==='function') {	
				//	if(checkClose){
					//	evFunc({msg:"Cập nhật hồ sơ bệnh án mẫu thành công"});
					//}
					//else{
						DlgUtil.showMsg("Xóa hồ sơ bệnh án mẫu thành công");
					//}
				
		//L2PT-42752
		ComboUtil.getComboTag("cboMAUBAID", "HSBA.MAU1", [{"name" : "[0]", "value" : loaibenhanid}], [ {"name" : "[0]","value" : "1"} ], {value : '', text : 'Chọn'}, "", ""); 
 
				//}
				//else {
				//	console.log('evFunc not a function');
				//} 
			}else{
				callBack(r1, "Xóa bệnh án mẫu không thành công");
			}		
	}
	$("#cboMAUBAID").change(function() {
		 var mauid=$("#cboMAUBAID").val();		 
		 if(mauid!=''){	
		 	if ($("[href='#tabBenhAnHoiKham']").parent().hasClass('active') && jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'CAUHINH_LOAD_MAU_THEO_TAB') == 1){
			 var _row = new Object(); 
			 for (i=1;i<=14;i++){
			 	for(j=1;j<=6;j++){
			 		_row["TIENSUSANKHOA" + i + j] = ""; 
			 	}
			 }
		 	}
			 if (CAUHINH_BA_LOAD_MAU=='') CAUHINH_BA_LOAD_MAU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'CAUHINH_BA_LOAD_MAU'); //L2PT-41824
			 
			 if (CAUHINH_BA_LOAD_MAU != '1') //L2PT-41824 ==1 thì ko reset matran.
				FormUtil.setObjectToForm("HOIKHAMMATRAN","",_row);
				
			 loadDataTemp(benhnhanid, loaibenhanid, i_hid, loaihsba_code, mauid, khambenhid, hosobenhanid);	
		 }else{
			 loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid);
		 }
		 
		  });
	function _validateFormBAMAU() {	
		var check = true;
		var SELECTOR_ERRORS = '';
		var tenmau1=$("#txtTENBENHANMAU").val();
		var tenmau2 =$("#cboMAUBAID").text();
		if (tenmau1==tenmau2)
		{
			SELECTOR_ERRORS += 'Tên bệnh án mẫu đã có' + '<br>'; 
			onfocus('#txtTENBENHANMAU');
			check =  false;
		}
		if($('#txtTENBENHANMAU').val()==''){
			SELECTOR_ERRORS += 'Tên bệnh án mẫu không được để trống' + '<br>'; 
			onfocus('#txtTENBENHANMAU');
			check =  false;
		}
		
		if(!check){
			DlgUtil.showMsg(SELECTOR_ERRORS);
		}
		return check;
	}	
	function _save(checkClose){
		// validate thong tin khi thuc hien cap nhat
		// them moi
		
		var validate = new DataValidator("divMain-BA");
		if(validate.validateForm() && _validateForm()){
			if (checkInsert == 1) {
				// buoc 1:insert thong tin ho so benh an
				hosobenhanid = insHSBenhAn(benhnhanid, loaibenhanid, khambenhid, i_hid, checkInsert);
			}
			
			// buoc 2: insert thong tin cho ho so phim anh
			var r1=insHSPhimAnh(arrSoPhimAnh, hosobenhanid, checkInsert);
			var arr_con = [];
			for (var i = 1; i <= socon; i++) {
				var objData = new Object();
				FormUtil.setFormToObject("dvCON"+i, "", objData);
				arr_con.push(objData)
			}
			$('#txtTTCON').val(JSON.stringify(arr_con));

			// buoc 3 insert thong tin hoi kham benh va tong ket benh an
			var r2=insHoiKham(checkInput, loaihsba_code, arrMaHoiKham, hosobenhanid, checkInsert);

			// insert thong tin hoi kham benh va tong ket benh an dang ma tran
			var r3=insHoiKhamMaTran(checkInput, loaihsba_code, arrMaHoiKhamTable, hosobenhanid, checkInsert);
			//dong bo du lieu
			var _par = [hosobenhanid, khambenhid];	
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.RIGHT",_par.join('$'));

			if(_return==1 && r1==1 && r2==1 && r3==1){
				//xu ly su kien callback
				var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");
				if(typeof evFunc==='function') {
					if(checkClose){
						evFunc({msg:"Cập nhật hồ sơ bệnh án thành công"});
					}
					else{
						DlgUtil.showMsg("Cập nhật hồ sơ bệnh án thành công");
					}
				}
				else {
					console.log('evFunc not a function');
				}
			}else{
				callBack(_return, checkInsert);
			}
									
		}
	}
	function _calBMI(){
		if($("#txtCHIEUCAO").val() != '' && $("#txtCANNANG").val() != ''){
			if(isNaN(parseFloat($("#txtCANNANG").val())) || isNaN(parseFloat($("#txtCHIEUCAO").val())))
				return;
			var _cannang = parseFloat($("#txtCANNANG").val());
			var _chieucao = parseFloat($("#txtCHIEUCAO").val());

			var bmi = chisobmi(_cannang, _chieucao);

			$("#txtBMI").text(bmi);
		}
	}

	function _validateForm() {
		var comp = $('#cldKINHCUOITUNGAY').val().split('/');
		var d = parseInt(comp[0], 10);
		var m = parseInt(comp[1], 10);
		var y = parseInt(comp[2], 10);
		var tuNgay = new Date(y, m - 1, d);
		var comp2 = $('#cldKINHCUOIDENNGAY').val().split('/');
		var d2 = parseInt(comp2[0], 10);
		var m2 = parseInt(comp2[1], 10);
		var y2 = parseInt(comp2[2], 10);
		var denNgay = new Date(y2, m2 - 1, d2);
		var SELECTOR_ERRORS = '';
		var check = true;
		if (tuNgay.getTime() > denNgay.getTime()) {
			SELECTOR_ERRORS += 'Kinh cuối từ ngày phải nhỏ hơn đến ngày' + '<br>';
			onfocus('#cldKINHCUOITUNGAY');
			check = false;
		}
		if($('#txtCHUYENDATUPHUT').val()<0 || $('#txtCHUYENDATUPHUT').val() > 59){
			SELECTOR_ERRORS += 'Phút chuyển dạ phải từ 0 đến 59' + '<br>';
			onfocus('#txtCHUYENDATUPHUT');
			check = false;
		}
		if($('#txtCHUANDATUGIO').val()<0 || $('#txtCHUANDATUGIO').val() > 23){
			SELECTOR_ERRORS += 'Giờ chuyển dạ phải từ 0 đến 23' + '<br>';
			onfocus('#txtCHUANDATUGIO');
			check =  false;
		}
		if($('#txtCANNANG').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtCANNANG');
			check =  false;
		}
		if($('#txtCANNANG').val().split('.')[1] != null && $('#txtCANNANG').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtCANNANG');
			check =  false;
		}
		if($('#txtNHIETDO').val()<0){
			SELECTOR_ERRORS += 'Nhiệt độ phải lớn hơn 0' + '<br>';
			onfocus('#txtNHIETDO');
			check =  false;
		}
		if($('#txtNHIETDO').val().split('.')[1] != null && $('#txtNHIETDO').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Nhiệt độ chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtNHIETDO');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA121').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTIENSUSANKHOA121');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA121').val().split('.')[1] != null && $('#txtTIENSUSANKHOA121').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTIENSUSANKHOA121');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA122').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTIENSUSANKHOA122');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA122').val().split('.')[1] != null && $('#txtTIENSUSANKHOA122').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTIENSUSANKHOA122');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA123').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTIENSUSANKHOA123');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA123').val().split('.')[1] != null && $('#txtTIENSUSANKHOA123').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTIENSUSANKHOA123');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA124').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTIENSUSANKHOA124');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA124').val().split('.')[1] != null && $('#txtTIENSUSANKHOA124').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTIENSUSANKHOA124');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA125').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTIENSUSANKHOA125');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA125').val().split('.')[1] != null && $('#txtTIENSUSANKHOA125').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTIENSUSANKHOA125');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA126').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTIENSUSANKHOA126');
			check =  false;
		}
		if($('#txtTIENSUSANKHOA126').val().split('.')[1] != null && $('#txtTIENSUSANKHOA126').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTIENSUSANKHOA126');
			check =  false;
		}
		if($('#txtOIVOLUCGIO').val()<0 || $('#txtOIVOLUCGIO').val() > 23){
			SELECTOR_ERRORS += 'Giờ ối vỡ phải từ 0 đến 23' + '<br>';
			onfocus('#txtOIVOLUCGIO');
			check =  false;
		}
		if($('#txtOIVOLUCPHUT').val()<0 || $('#txtOIVOLUCPHUT').val() > 59){
			SELECTOR_ERRORS += 'Phút ối vỡ phải từ 0 đến 59'+ '<br>';
			onfocus('#txtOIVOLUCPHUT');
			check =  false;
		}
		if($('#txtMABENHCHINH').val() != null && $('#txtMABENHCHINH').val() != '' 
			&& !checkIcdCode($('#txtMABENHCHINH').val().trim())){
			SELECTOR_ERRORS += 'Mã bệnh chính ko đúng' + '<br>';
			onfocus('#txtMABENHCHINH');
			check =  false;
		}
		if($('#txtBENHCHINH').val() != null && $('#txtBENHCHINH').val() != '' 
			&& !checkIcdName($('#txtBENHCHINH').val().trim())){
			SELECTOR_ERRORS += 'Tên bệnh chính ko đúng' + '<br>';
			onfocus('#txtBENHCHINH');
			check =  false;
		}
		if($('#txtMAPHANBIET').val() != null && $('#txtMAPHANBIET').val() != '' 
			&& !checkIcdCode($('#txtMAPHANBIET').val().trim())){
			SELECTOR_ERRORS += 'Mã bệnh kèm theo ko đúng' + '<br>';
			onfocus('#txtMAPHANBIET');
			check =  false;
		}
		if($('#txtPHANBIET').val() != null && $('#txtPHANBIET').val() != '' 
			&& !checkIcdName($('#txtPHANBIET').val().trim())){
			SELECTOR_ERRORS += 'Tên bệnh kèm theo ko đúng' + '<br>';
			onfocus('#txtPHANBIET');
			check =  false;
		}
		if($('#txtVBDELUCGIO').val()<0 || $('#txtVBDELUCGIO').val() > 23){
			SELECTOR_ERRORS += 'Vào buồng đẻ lúc giờ phải từ 0 đến 23' + '<br>';
			onfocus('#txtVBDELUCGIO');
			check =  false;
		}
		if($('#txtVBDELUCPHUT').val()<0 || $('#txtVBDELUCPHUT').val() > 59){
			SELECTOR_ERRORS += 'Vào buồng đẻ lúc phút phải từ 0 đến 59'+ '<br>';
			onfocus('#txtVBDELUCPHUT');
			check =  false;
		}
		if($('#txtGIORAUSO').val()<0 || $('#txtGIORAUSO').val() > 23){
			SELECTOR_ERRORS += 'Giờ rau sổ phải từ 0 đến 23' + '<br>';
			onfocus('#txtGIORAUSO');
			check =  false;
		}
		if($('#txtPHUTRAUSO').val()<0 || $('#txtPHUTRAUSO').val() > 59){
			SELECTOR_ERRORS += 'Phút rau sổ phải từ 0 đến 59'+ '<br>';
			onfocus('#txtPHUTRAUSO');
			check =  false;
		}
		if($('#txtDELUCGIO').val()<0 || $('#txtDELUCGIO').val() > 23){
			SELECTOR_ERRORS += 'Đẻ lúc giờ phải từ 0 đến 23' + '<br>';
			onfocus('#txtDELUCGIO');
			check =  false;
		}
		if($('#txtDELUCPHUT').val()<0 || $('#txtDELUCPHUT').val() > 59){
			SELECTOR_ERRORS += 'Đẻ lúc phút phải từ 0 đến 59'+ '<br>';
			onfocus('#txtDELUCPHUT');
			check =  false;
		}
		if($('#txtTDCANNANG').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTDCANNANG');
			check =  false;
		}
		if($('#txtTDCANNANG').val().split('.')[1] != null && $('#txtTDCANNANG').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTDCANNANG');
			check =  false;
		}
		if($('#txtTDCAO').val()<0){
			SELECTOR_ERRORS += 'Chiều cao phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTDCAO');
			check =  false;
		}
		if($('#txtTDCAO').val().split('.')[1] != null && $('#txtTDCAO').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Chiều cao chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTDCAO');
			check =  false;
		}
		if($('#txtTDVONGDAU').val()<0){
			SELECTOR_ERRORS += 'Vòng đầu phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTDVONGDAU');
			check =  false;
		}
		if($('#txtTDVONGDAU').val().split('.')[1] != null && $('#txtTDVONGDAU').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Vòng đầu chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTDVONGDAU');
			check =  false;
		}
		if($('#txtCANNANGTHAI').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtCANNANGTHAI');
			check =  false;
		}
		if($('#txtCANNANGTHAI').val().split('.')[1] != null && $('#txtCANNANGTHAI').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtCANNANGTHAI');
			check =  false;
		}
		if($('#txtCUONGRAUDAI').val()<0){
			SELECTOR_ERRORS += 'Chiều dài cuống rau phải lớn hơn 0' + '<br>'; 
			onfocus('#txtCUONGRAUDAI');
			check =  false;
		}
		if($('#txtCUONGRAUDAI').val().split('.')[1] != null && $('#txtCUONGRAUDAI').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Chiều dài cuống rau chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtCUONGRAUDAI');
			check =  false;
		}
		if($('#txtTKCANNANG').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtTKCANNANG');
			check =  false;
		}
		if($('#txtTKCANNANG').val().split('.')[1] != null && $('#txtTKCANNANG').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTKCANNANG');
			check =  false;
		}
		if($('#txtTKNHIETDO').val()<0){
			SELECTOR_ERRORS += 'Nhiệt độ phải lớn hơn 0' + '<br>';
			onfocus('#txtTKNHIETDO');
			check =  false;
		}
		if($('#txtTKNHIETDO').val().split('.')[1] != null && $('#txtTKNHIETDO').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Nhiệt độ chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtTKNHIETDO');
			check =  false;
		}
		if($('#txtMACDTRUOCPT').val() != null && $('#txtMACDTRUOCPT').val() != '' 
			&& !checkIcdCode($('#txtMACDTRUOCPT').val().trim())){
			SELECTOR_ERRORS += 'Mã chẩn đoán trước phẫu thuật ko đúng' + '<br>';
			onfocus('#txtMACDTRUOCPT');
			check =  false;
		}
		if($('#txtCDTRUOCPT').val() != null && $('#txtCDTRUOCPT').val() != '' 
			&& !checkIcdName($('#txtCDTRUOCPT').val().trim())){
			SELECTOR_ERRORS += 'Tên chẩn đoán trước phẫu thuật ko đúng' + '<br>';
			onfocus('#txtCDTRUOCPT');
			check =  false;
		}
		if($('#txtMACDSAUPT').val() != null && $('#txtMACDSAUPT').val() != '' 
			&& !checkIcdCode($('#txtMACDSAUPT').val().trim())){
			SELECTOR_ERRORS += 'Mã chẩn đoán sau phẫu thuật ko đúng' + '<br>';
			onfocus('#txtMACDSAUPT');
			check =  false;
		}
		if($('#txtCDSAUPT').val() != null && $('#txtCDSAUPT').val() != '' 
			&& !checkIcdName($('#txtCDSAUPT').val().trim())){
			SELECTOR_ERRORS += 'Tên chẩn đoán sau phẫu thuật ko đúng' + '<br>';
			onfocus('#txtCDSAUPT');
			check =  false;
		}
		
		if(!check){
			DlgUtil.showMsg(SELECTOR_ERRORS);
		}
		return check;
	}
}
function toggleCheckbox1(clickedId) {
	const checkboxIds1 = [
		'chkTIENSUSANKHOA41',
		'chkTIENSUSANKHOA51',
		'chkTIENSUSANKHOA61',
		'chkTIENSUSANKHOA71',
		'chkTIENSUSANKHOA81',
		'chkTIENSUSANKHOA91',
		'chkTIENSUSANKHOA101'
	];
	checkboxIds1.forEach(function (id) {
		if (id !== clickedId) {
			document.getElementById(id).checked = false;
		}
	});
}
function toggleCheckbox2(clickedId) {
	const checkboxIds2 = [
		'chkTIENSUSANKHOA42',
		'chkTIENSUSANKHOA52',
		'chkTIENSUSANKHOA62',
		'chkTIENSUSANKHOA72',
		'chkTIENSUSANKHOA82',
		'chkTIENSUSANKHOA92',
		'chkTIENSUSANKHOA102'
	];
	checkboxIds2.forEach(function(id) {
		if (id !== clickedId) {
			document.getElementById(id).checked = false;
		}
	});
}
function toggleCheckbox3(clickedId) {
	const checkboxIds3 = [
		'chkTIENSUSANKHOA43',
		'chkTIENSUSANKHOA53',
		'chkTIENSUSANKHOA63',
		'chkTIENSUSANKHOA73',
		'chkTIENSUSANKHOA83',
		'chkTIENSUSANKHOA93',
		'chkTIENSUSANKHOA103'
	];
	checkboxIds3.forEach(function (id) {
		if (id !== clickedId) {
			document.getElementById(id).checked = false;
		}
	});
}
function toggleCheckbox4(clickedId) {
	const checkboxIds4 = [
		'chkTIENSUSANKHOA44',
		'chkTIENSUSANKHOA54',
		'chkTIENSUSANKHOA64',
		'chkTIENSUSANKHOA74',
		'chkTIENSUSANKHOA84',
		'chkTIENSUSANKHOA94',
		'chkTIENSUSANKHOA104'
	];
	checkboxIds4.forEach(function(id) {
		if (id !== clickedId) {
			document.getElementById(id).checked = false;
		}
	});
}
function toggleCheckbox5(clickedId) {
	const checkboxIds5 = [
		'chkTIENSUSANKHOA45',
		'chkTIENSUSANKHOA55',
		'chkTIENSUSANKHOA65',
		'chkTIENSUSANKHOA75',
		'chkTIENSUSANKHOA85',
		'chkTIENSUSANKHOA95',
		'chkTIENSUSANKHOA105'
	];
	checkboxIds5.forEach(function(id) {
		if (id !== clickedId) {
			document.getElementById(id).checked = false;
		}
	});
}
function toggleCheckbox6(clickedId) {
	const checkboxIds6 = [
		'chkTIENSUSANKHOA46',
		'chkTIENSUSANKHOA56',
		'chkTIENSUSANKHOA66',
		'chkTIENSUSANKHOA76',
		'chkTIENSUSANKHOA86',
		'chkTIENSUSANKHOA96',
		'chkTIENSUSANKHOA106'
	];
	checkboxIds6.forEach(function (id) {
		if (id !== clickedId) {
			document.getElementById(id).checked = false;
		}
	});
}
