<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEU_XETNGHIEM_A4_30160" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="406" leftMargin="10" rightMargin="5" topMargin="5" bottomMargin="5" uuid="18c5e549-94fd-415a-8af9-02c896054c66">
	<property name="ireport.zoom" value="2.4157650000000035"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<queryString language="plsql">
		<![CDATA[{call rpt_phieu_xetnghiem_30160(null,$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="SOTHUTU_LAYMAU" class="java.math.BigDecimal"/>
	<field name="SOTHUTU" class="java.math.BigDecimal"/>
	<field name="BARCODE" class="java.lang.String"/>
	<field name="PHONGLAYMAU" class="java.lang.String"/>
	<field name="PHONGTHUCHIEN" class="java.lang.String"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="TENXETNGHIEM" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="NGAYDICHVU" class="java.sql.Timestamp"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="ORG_ADDRESS" class="java.lang.String"/>
	<field name="DOCTOR_NAME" class="java.lang.String"/>
	<field name="CHUANDOANPHU" class="java.lang.String"/>
	<field name="KHOATHUCHIEN" class="java.lang.String"/>
	<field name="CHANDOANBD" class="java.lang.String"/>
	<field name="NOTE" class="java.lang.String"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="TENGIUONG" class="java.lang.String"/>
	<field name="DONGIA" class="java.lang.Integer"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="TINHTRANG" class="java.lang.Integer"/>
	<field name="THANHTIEN" class="java.lang.Integer"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="GHICHUBENHCHINH" class="java.lang.String"/>
	<field name="GHICHUBENHPHU" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="TEN_NHOM" class="java.lang.String"/>
	<field name="MSPHIEU" class="java.lang.String"/>
	<field name="GHICHU_TITLE" class="java.lang.String"/>
	<field name="KHOACHIDINH" class="java.lang.String"/>
	<field name="LOAITHANHTOAN" class="java.lang.String"/>
	<variable name="TONGTIEN" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<variable name="vchandoan" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{CHANDOAN} == null ? "" : $F{CHANDOAN}]]></initialValueExpression>
	</variable>
	<variable name="vghichu1" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{GHICHUBENHCHINH} == null ? "" : "; " + $F{GHICHUBENHCHINH}]]></initialValueExpression>
	</variable>
	<variable name="vchandoanphu" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{CHUANDOANPHU} == null ? "" : "; " + $F{CHUANDOANPHU}]]></initialValueExpression>
	</variable>
	<variable name="vghichu2" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{GHICHUBENHPHU} == null ? "" : "; " + $F{GHICHUBENHPHU}]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="215">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="89" width="268" height="12" uuid="59734d35-008d-4c1e-9686-3370da334fd3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Họ tên: " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="101" width="406" height="12" uuid="96a0210f-b609-4959-8a4b-150b87807b45"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Địa chỉ: " + ($F{DIACHI}==null?"":$F{DIACHI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="137" width="406" height="12" uuid="2597b26a-b985-4274-9486-ed3398c61659"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Chẩn đoán: " + $V{vchandoan} + $V{vghichu1} + $V{vchandoanphu} + $V{vghichu2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="26" y="188" width="301" height="27" uuid="9e5e0607-d4ed-4fd6-9324-731f7fb09670"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Yêu cầu cận lâm sàng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="135" height="18" uuid="1edea119-13db-454b-9cdd-e92945eafb2a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="18" width="135" height="18" uuid="9482c0f3-328b-4b89-a7d7-91e2cd3aee8f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="188" width="26" height="27" uuid="db7ffd8b-7cfd-4196-bb09-50662faf3701"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="327" y="188" width="79" height="27" uuid="e3b82826-6bdc-4140-acdc-5a597ecef844"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="173" width="55" height="12" uuid="f24736e3-9761-40ed-bdfe-091dcd2575fc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[-Tình trạng:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="55" y="173" width="351" height="12" uuid="56fbaeb6-a774-4235-a5d2-02f8c4c138ff"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TINHTRANG} == 2 ? ": Khẩn" : ": Thường"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="0" width="108" height="14" uuid="d9043d7a-b65e-49b5-b4d4-8ac10058c7c8"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BN: " + $F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="37" y="36" width="60" height="1" uuid="616edd04-87ad-4ac1-9a51-ab6c1d517e50"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="135" y="18" width="163" height="18" uuid="41609797-6a72-4884-abb2-92f41dcca1b4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONGKHAM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="125" width="55" height="12" uuid="0df5992d-6d34-4870-be69-90a091942015"/>
				<textElement>
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[-Số thẻ BH:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="135" y="0" width="163" height="18" uuid="696d9987-0535-4f5c-8681-0d00b3bb6f56"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MSPHIEU}.equals("MS: 30/BV-01")? "PHIẾU CHỈ ĐỊNH XÉT NGHIỆM CHẨN ĐOÁN " : "PHIẾU CHỈ ĐỊNH XÉT NGHIỆM ")+ $F{TEN_NHOM}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="55" y="125" width="213" height="12" uuid="0807648a-7cfb-4198-a430-0f2265943df8"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}]]></textFieldExpression>
			</textField>
			<image hAlign="Center">
				<reportElement positionType="Float" x="0" y="39" width="135" height="40" uuid="839e2919-7550-4674-89a9-850410f92872"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64("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".getBytes()))]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="14" width="108" height="14" uuid="cff2dc05-eaad-4f83-a378-63d4a10eb121"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số phiếu:" + $F{SOPHIEU}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement positionType="Float" x="298" y="28" width="108" height="20" uuid="5e633ddc-bdb1-4ba0-b34e-7a59f611c552"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code39 (Extended)" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$F{BARCODE}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="48" width="108" height="12" uuid="5619e3d8-5f4b-412e-bbdb-b9c8972e0ab0"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BARCODE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="113" width="406" height="12" uuid="4b0cf3e0-7dc1-46fa-b506-949533503e81"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Khoa: "+( $F{KHOACHIDINH}==null?"":$F{KHOACHIDINH})
+"    Buồng: "+($F{PHONGKHAM}==null?"":$F{PHONGKHAM})
+"    Giường: "+($F{TENGIUONG}==null?"":$F{TENGIUONG})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="149" width="406" height="12" isRemoveLineWhenBlank="true" uuid="8b4a7acd-3afa-4ca5-8fa0-6e1c8bf0890a"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Vị trí lấy bệnh phẩm: "+($F{GHICHU_TITLE}==null?"":$F{GHICHU_TITLE})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="161" width="406" height="12" uuid="0ce6d89b-ec36-44d8-8696-347cddaa15fd"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Khoa thực hiện: "+($F{KHOATHUCHIEN}==null?"":$F{KHOATHUCHIEN})+"     Phòng thực hiện: "+($F{PHONGTHUCHIEN}==null?"":$F{PHONGTHUCHIEN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="268" y="89" width="138" height="12" uuid="528c80b2-deca-4f2f-bf81-c5f7254911b3"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: "+$F{TUOI} + " " + ($F{DVTUOI}==null?"":$F{DVTUOI})
+"  "+"Giới tính: "+($F{GIOITINH}==null?"":$F{GIOITINH})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="26" y="0" width="301" height="14" uuid="cd9d34f4-2ee1-44d0-82db-c1619be712f0"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENDICHVU}+($F{GHICHU}==null?"":"\n         "+$F{GHICHU})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="327" y="0" width="79" height="14" uuid="6bfe9b5e-ae19-4d91-a448-35b410c095a4"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONGIA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="26" height="14" uuid="50e99b3b-d734-45ae-9f96-b5a222b014e7"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="76">
			<staticText>
				<reportElement positionType="Float" x="162" y="18" width="244" height="15" uuid="4988b46a-eee3-4cdd-951f-7444cfc8bf07"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[BÁC SĨ CHUYÊN KHOA]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="162" y="3" width="244" height="15" uuid="1cf89fe1-73b9-4773-ba43-95b348282cd4"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("'Lạng sơn. Lúc 'HH:mm', Ngày 'dd' Tháng 'MM' Năm 'yyyy")).format($F{NGAYDICHVU})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="162" y="61" width="244" height="15" uuid="5edf20b1-3f26-4e44-84c8-cac4dc43095b"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DOCTOR_NAME}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
