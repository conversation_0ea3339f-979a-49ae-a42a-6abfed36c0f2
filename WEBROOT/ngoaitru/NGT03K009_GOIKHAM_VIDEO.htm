<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../sanglockham/cominf.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css"> <link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../sanglockham/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script src="../common/script/jqgrid/js/context-menu.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src='https://meet.jit.si/external_api.js'></script>
<script type="text/javascript" src="../ngoaitru/NGT03K009_GOIKHAM_VIDEO.js?v=20200414"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>

<div id="meet">
</div>

<script>
	var opt = [];
	var schema = '{db_schema}';
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var phongid = '{subdept_id}';
	var uuid = '{uuid}';

	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	session_par[4] = phongid;
	var table_name = '{table}';
	
	var _madangky = '{madangky}';
	var _manguoigoi = '{manguoigoi}';
	var _manguoinhan = '{manguoinhan}';	
	var _maCoSoYTe = '{macsyt}';
	var _tenNguoiGoi = '{tennguoigoi}';

	initRest(uuid, "/vnpthis");	
	
	initAjax("/vnpthis");
	ajaxSvc.register("KhamOnlineWS");
	
	parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;
	_opts.madangky = _madangky;
	_opts.manguoigoi = _manguoigoi;
	_opts.manguoinhan = _manguoinhan;
	_opts.maCoSoYTe = _maCoSoYTe;
	_opts.tenNguoiGoi = _tenNguoiGoi;
	
	var video = new viedeo_call_benhnhan(_opts);
	video.load(hospital_id);
	
	
</script>