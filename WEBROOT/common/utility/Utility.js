class Utility {
    constructor() {
    }

    _loadCss(id, cssSource) {
        let cssId = `${id}_Style`;
        if (!document.getElementById(cssId)) {
            let head = document.getElementsByTagName('head')[0];
            let link = document.createElement('link');
            link.id = cssId;
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = cssSource;
            link.media = 'all';
            head.appendChild(link);
        }
    }

    _loadScript(id, jsSource, callback) {
        let prefix = jsSource.substring(jsSource.lastIndexOf("/") + 1).toUpperCase().replace(".", "_");
        prefix = prefix.substring(0, prefix.indexOf("?"));
        let jsId = `${id}_${prefix}`;
        if (!document.getElementById(jsId)) {
            let head = document.getElementsByTagName('head')[0];
            let script = document.createElement('script');
            script.id = jsId;
            script.type = 'text/javascript';
            script.src = jsSource;
            script.async = false;
            script.onload = function () { callback() };
            head.appendChild(script);
        } else {
            callback();
        }
    }

    _createElementFromHTML(htmlString) {
        let div = document.createElement('div');
        div.innerHTML = htmlString.trim();
        return div.firstChild;
    }

    _genRandonString(length) {
        let chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let charLength = chars.length;
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * charLength));
        }
        return result;
    }

    _getDateString(_pdate, _isGetDay) {
        let rdate;
        if (!_pdate) {
            rdate = new Date();
        } else {
            rdate = _pdate;
        }
        let datetime = this._lpadDate(rdate.getDate(), 2)
            + "/" + this._lpadDate(rdate.getMonth() + 1, 2)
            + "/" + rdate.getFullYear();
        if (_isGetDay === true) {
            datetime += " " + this._lpadDate(rdate.getHours(), 2) + ":"
                + this._lpadDate(rdate.getMinutes(), 2) + ":"
                + this._lpadDate(rdate.getSeconds(), 2);
        }
        return datetime;
    }

    _lpadDate(val, length) {
        return ("" + val).padStart(length, '0');
    }

    doSpinButton(element) {
        element.children[0].classList.remove("hide");
        element.children[1].classList.add("hide");
        element.setAttribute("disabled", "disabled");
    }

    unSpinButton(element) {
        element.children[0].classList.add("hide");
        element.children[1].classList.remove("hide");
        element.removeAttribute("disabled");
    }
}

class DataService {
    get type() {
        return this._type;
    }

    set type(value) {
        this._type = value;
    }

    get id() {
        return this._id;
    }

    set id(value) {
        this._id = value;
    }

    get param() {
        return this._param;
    }

    set param(value) {
        this._param = value;
    }

    constructor({
                    type = ServiceType.PLSQL,
                    id = "",
                    param = {}
                }) {
        this._type = type;
        this._param = param;
        this._id = id;
    }
}

const ServiceType = Object.freeze({
    PLSQL:   "PLSQL",
    REST_POST:  "POST",
    REST_PUT:  "PUT"
});