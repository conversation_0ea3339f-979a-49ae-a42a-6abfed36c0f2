/*
 * Nhập kết quả xét nghiệm
 * 
 * VietDA	04/08/2016		tạo mới
 *  
 */

function SarsCov(opt){
	this.load = doLoad;

	var bnGioiTinh = "Nam";
	var bnTuoi = 0;
	var dsThem = [];
	var dsOld = [];
	var dsThemAll = [];
	var dichvuid ='';
	var amtinh ='';
	var duongtinh ='';
	var dichvukhambenhid ='';
	var vikhuanid ='';
	var vikhuanid_check  ='';
	var tenvikhuan ='';
	
	var _grd_DS_KS_HEADER = "VIKHUAN_ID,VIKHUAN_ID,80,0,f,l;" +
	"KHANGSINHID,KHANGSINHID,150,0,f,l;" +
	"KHANGSINHID_CHECK,KHANGSINHID_CHECK,150,0,f,l;" +
	"Mã kháng sinh,MA_KHANGSINH,150,0,f,l;" +
	"Tên kh<PERSON>g sinh,TEN_KHANGSINH,120,0,f,l;" +
	"S,S,120,0,f,l;" +
	"I,I,120,0,f,l;" +
	"R,R,120,0,f,l;" +
	"USER_NAME,USER_NAME,120,0,f,l;" +
	"NGAYTAO,NGAYTAO,120,0,f,l";
	var _grd_DS_KS_NAME = "grdDSKhangSinh";
	
	var _grd_DS_VK_HEADER = "Chọn,'SELECT_RADIO',50,0,f,l;" +
	"VIKHUAN_ID,VIKHUAN_ID,80,0,t,l;" +
	"vkidck,VIKHUANID_CHECK,50,0,t,l;" +
	"Tên vi khuẩn,TEN_VIKHUAN,120,0,f,l;" +
	"Ngày tạo,NGAYTAO,120,0,f,l;" +
	"Người tạo,USER_NAME,120,0,f,l";
	var _grd_DS_VK_NAME = "grdDSViKhuan";
		
	var CLS_HIENTHI_LAYMAUCOVID = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_HIENTHI_LAYMAUCOVID');
	var CLS_MANHANVIEN_LAYMAUCOVID = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_MANHANVIEN_LAYMAUCOVID');
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		$("#hdfIDMauBenhPham").val(opt._idmaubenhpham);
//		$("#hdfIDvikhuan").val(opt._vikhuanid);
		$("#hdfIDDichVuKB").val(opt._iddichvukb);
		$("#hdfIDDichVuID").val(opt._iddichvu);
		$("#hdfIDShowBtnEditTime").val(opt._showNutSuaNgayTraKQ);
		console.log("id mau benh pham="+opt._idmaubenhpham+", _showNutSuaNgayTraKQ="+opt._showNutSuaNgayTraKQ+", _show: "+opt._show);
		$("#txtNGAYCAY").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		$("#txtNGAYNHANMAU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		$("#txtTHOIGIANLAYMAU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		$("#txtTHOIGIANTRA").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		if(CLS_MANHANVIEN_LAYMAUCOVID!="" && CLS_MANHANVIEN_LAYMAUCOVID=="1"){
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBS", CLS_MANHANVIEN_LAYMAUCOVID);
			var userid= "";
			var username = "";
			var fullname = "";
			if(data_ar!=null && data_ar!=undefined){
				userid= data_ar[0]["USER_ID"];
				username = data_ar[0]["USERNAME"];
				fullname = data_ar[0]["FULLNAME"];
			}	
			var option = $('<option value="'+userid+'">'+fullname+' ('+username+')</option>');
			$("#cboNGUOIKIEMTRA").empty();
			$("#cboNGUOIKIEMTRA").append(option);
		}
		
		initControl();
		bindEvent();
		
		$("#txtNhanXet").focus();
		loadThongTinBenhNhan();
//		loadDanhSachKetQua();
		
		var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
		var sql_parKTV=[];
		sql_parKTV.push({"name":"[0]","value":0},{"name":"[1]","value":opt.dept_id});
		ComboUtil.initComboGrid("txtNGUOIKIEMTRA","PTTT.LOAD_USER",sql_parKTV,"600px",_col_loaduser, function(event, ui) {
			  $("#txtNGUOIKIEMTRA").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+' ('+ui.item.USERNAME+')</option>');
		      $("#cboNGUOIKIEMTRA").empty();
		      $("#cboNGUOIKIEMTRA").append(option);
		      return false;
		});

		$('#btnCLEARNGUOIKIEMTRA').on("click",function() {
			$("#txtNGUOIKIEMTRA").val("");
			var option = $('<option value="-1"></option>');
		    $("#cboNGUOIKIEMTRA").empty();
		    $("#cboNGUOIKIEMTRA").append(option);
		});
		
	}

	function initControl(){
		$.extend($.jgrid.inlineEdit, {
			keys: true
		});
		loadViKhuanLaoM1();
		loadTTLANLAYMAU();
		
		if($("#hdfIDShowBtnEditTime").val()=='1' && opt._show =='true') {
			$("#btnEditTimePopUpX1").show();
		}

		$('#txtAMTINH').prop('readonly',true);
		$('#txtDUONGTINH').prop('readonly',true);
		$('#txtNGHINGO').prop('readonly',true);

		
		// ẩn hiện các nút xử lý
		if(type=="read"){
			$("#btnLuu1").attr('disabled','true');
			$("#btnTraKetQua2").attr('disabled','true');
			$("#btnLuu2").attr('disabled','true');
			$("#btnTraKetQua1").attr('disabled','true');
		} 
		else if(type=="update"){
			$("#btnLuu1").removeAttr('disabled');
			$("#btnLuu2").removeAttr('disabled');
			$("#btnInKQ1").removeAttr('disabled');
			$("#btnInKQ2").removeAttr('disabled');
			$("#btnTraKetQua2").removeAttr('disabled');
			$("#btnTraKetQua1").removeAttr('disabled');
		}
		if(user_group_id=="0" || user_group_id=="1") {
//			$("#btnTraKetQua").removeAttr('disabled');
//			$("#btnHuyKetQua").removeAttr('disabled');
		}
		else if(userRights=="TK") {
//			$("#btnTraKetQua").removeAttr('disabled');
//			$("#btnHuyKetQua").removeAttr('disabled');
		}
		else if(userRights=="KTV"){
//			$("#btnTraKetQua").attr('disabled','true');
//			$("#btnHuyKetQua").attr('disabled','true');
		}
		//nghiant 28082017
		 if(opt._rolePTH != "true" ){
			$("#btnTraKetQua1").attr('disabled','true');
			$("#btnTraKetQua2").attr('disabled','true');
//			$("#btnHuyKetQua").attr('disabled','true');
			$("#btnLuu").attr('disabled','true');
			$("#btnEditTimePopUpX").hide();	
			console.log("abcd=================================");
		}
		//end nghiant 28082017 
		 
	}
	
	function bindEvent(){		
		$('#cboMAU_MTB').change(function(){
			if($("#cboMAU_MTB").val() == "1" || $("#cboMAU_MTB").val() == "2" || $("#cboMAU_MTB").val() == "5"){
				$("#tblMau34").hide();
				$("#tblMau125").show();
				if($("#cboMAU_MTB").val() == "1" ){
					$(".divPZA").hide();
					$(".divINH").show();
					$(".divEMB").show();
					$(".divRMP").show();
					$(".divSM").show();
					$("#IDINH").html("INH </br>"+"(0.1 µg/ml)");
					$("#IDRMP").html("RMP </br>"+"(1.0 µg/ml)");
					$("#IDEMB").html("EMB </br>"+"(5.0 µg/ml)");
					$("#IDSM").html("SM </br>"+"(1.0 µg/ml)");
				}else if($("#cboMAU_MTB").val() == "2" ){
					$(".divPZA").hide();
					$(".divINH").show();
					$(".divEMB").show();
					$(".divRMP").show();
					$(".divSM").show();
					$("#IDINH").html("INH </br>"+"(0.2 µg/ml)");
					$("#IDRMP").html("RMP </br>"+"(40 µg/ml)");
					$("#IDEMB").html("EMB </br>"+"(2.0 µg/ml)");
					$("#IDSM").html("SM </br>"+"(4.0 µg/ml)");
				}else{
					$(".divPZA").show();
					$(".divINH").hide();
					$(".divEMB").hide();
					$(".divRMP").hide();
					$(".divSM").hide();
				}
			}else if($("#cboMAU_MTB").val() == "3" || $("#cboMAU_MTB").val() == "4"){
				$("#tblMau34").show();
				$("#tblMau125").hide();
				if($("#cboMAU_MTB").val() == "3" ){
					$("#IDMOX").html("MOX </br>"+"(0.25 µg/ml)");
					$("#IDAMK").html("AMK </br>"+"(1.0 µg/ml)");
					$("#IDKM").html("KM </br>"+"(2.5 µg/ml)");
					$("#IDCAP").html("CAP </br>"+"(2.5 µg/ml)");
				}else{
					$("#IDMOX").html("MOX </br>"+"(1.0 µg/ml)");
					$("#IDAMK").html("AMK </br>"+"(30 µg/ml)");
					$("#IDKM").html("KM </br>"+"(30 µg/ml)");
					$("#IDCAP").html("CAP </br>"+"(40 µg/ml)");
				}
				
			}else{
				$("#tblMau34").hide();
				$("#tblMau125").hide();
			}
		});
		$('#btnLuu1').on('click', function () {
			luuKetQua1();
			
		});
		
		$('#btnLuu2').on('click', function () {
			luuKetQua2();
			loadViKhuanLaoM2();
		});
		
		$('#btnXoaText1').on('click', function () {
			$("#txtTHOIGIANLAYMAU").val('');
			$("#txtNGAYNHANMAU").val('');
			$("#txtNGAYCAY").val('');
			$("#txtAMTINH").val('');
			$("#txtNHIEMTRUNG").val('');
			$("#txtMTB").val('');
			$("#txtNTM").val('');
			$("#txtKQSOI").val('');
			$("#txtTESTDD").val('');
		});
		
		$('#btnXoaText2').on('click', function () {
			$("#txtNHAYINH").val('');
			$("#txtKHANGINH").val('');
			$("#txtNHAYRMP").val('');
			$("#txtKHANGRMP").val('');
			$("#txtNHAYSM").val('');
			$("#txtKHANGSM").val('');
			$("#txtNHAYEMB").val('');
			$("#txtKHANGEMB").val('');
			$("#txtNHAYPZA").val('');
			$("#txtKHANGPZA").val('');
			$("#txtKETLUAN").val('');
		});
		
		$("#btnHuy1").on("click",function(e){
			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
		});
		
		$("#btnHuy2").on("click",function(e){
			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
		});
		
		//click vào text box cua form 1 
		$('#txtAMTINH').on('click', function () {
			if($("#txtAMTINH").val().trim() == 'X'){
				$("#txtAMTINH").val('');
			}else{
				$("#txtAMTINH").val('X');
				$("#txtDUONGTINH").val('');
				$("#txtNGHINGO").val('');
			}
		});
		$('#txtDUONGTINH').on('click', function () {
			if($("#txtDUONGTINH").val().trim() == 'X'){
				$("#txtDUONGTINH").val('');
			}else{
				$("#txtAMTINH").val('');
				$("#txtDUONGTINH").val('X');
				$("#txtNGHINGO").val('');
			}
		});
		$('#txtNGHINGO').on('click', function () {
			if($("#txtNGHINGO").val().trim() == 'X'){
				$("#txtNGHINGO").val('');
			}else{
				$("#txtAMTINH").val('');
				$("#txtDUONGTINH").val('');
				$("#txtNGHINGO").val('X');
			}
		});

		$('#txtNGAYNHANMAU').on('change', function () {
			if(CLS_HIENTHI_LAYMAUCOVID=="1"){
				var timenow = moment($('#txtNGAYNHANMAU').val(),"DD/MM/YYYY HH:mm:ss");
				if(timenow.hour()>=6 && timenow.hour()<11) {
					$("#txtNGAYCAY").val(moment().format("DD/MM/YYYY 11:00:00"));
					
					$("#txtNGAYKY").val(moment().format('DD/MM/YYYY 17:00:00'));
				}
				else if(timenow.hour()>=11 && timenow.hour()<18){	
					$("#txtNGAYCAY").val(moment().format("DD/MM/YYYY 18:00:00"));
					
					$("#txtNGAYKY").val(moment().add(1,'days').format('DD/MM/YYYY 08:00:00'));
				}
				else if(timenow.hour()>=18){
					$("#txtNGAYCAY").val(moment().add(1,'days').format("DD/MM/YYYY 06:00:00"));
					
					$("#txtNGAYKY").val(moment().add(1,'days').format('DD/MM/YYYY 12:00:00'));
				}
				else if(timenow.hour()<6){
					$("#txtNGAYCAY").val(moment().format("DD/MM/YYYY 06:00:00"));
					
					$("#txtNGAYKY").val(moment().format('DD/MM/YYYY 12:00:00'));
				}
			}
		});
		
		///
		
		$('#btnTraKetQua1').on('click', function () {	
			if(CLS_HIENTHI_LAYMAUCOVID=="1"){
				luuKetQua1();
			}
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val()
			;

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TKQ", param);
			
			if(rs == '1'){				
				DlgUtil.showMsg("Trả kết quả thành công",function(){
					if(CLS_HIENTHI_LAYMAUCOVID=="1"){
						inKetQua1();
						EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
					}
				},1000);
//				$("#hdfIDMauBenhPham").val("");		
			}
			else if(rs == "2"){
				DlgUtil.showMsg("Bạn vừa Trả kết quả sau thời điểm Kết thúc bệnh án. Bạn cần điều chỉnh lại thời gian Trả kết quả của phiếu!");
			}
			else if(rs=="4"){
				DlgUtil.showMsg("Chưa thể trả kết quả do bệnh nhân chưa thực hiện thanh toán viện phí hoặc có số tiền tạm ứng nhỏ hơn tổng tiền dịch vụ!",undefined,8500);
			}
			else if(rs == "9"){
				DlgUtil.showMsg("Hệ thống từ chối Trả kết quả sau thời điểm Kết thúc bệnh án! Liên hệ quản trị viên để được hỗ trợ");
			}
			else if(rs == "13"){
				if(CLS_HIENTHI_LAYMAUCOVID=="1"){
                    DlgUtil.showMsg("Trả kết quả thành công",function(){
					
						inKetQua1();
						EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
					},1000);
				} else {
				    DlgUtil.showMsg("Trả kết quả thành công. Chưa nhập mã máy thực hiện.",undefined,10000);
				}
			}
			else if(rs == "14"){
				DlgUtil.showMsg("Chưa nhập mã máy thực hiện. Không cho phép trả kết quả",undefined,10000);
			}
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,2000);	
			} 
		});
		
		$('#btnHuyKetQua1').on('click', function () {			
			if(!rolePTH){//nghiant 05092017 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,1000);	
			}else{
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				$("#hdfIDMauBenhPham").val()+'$'+
				$("#hdfIDDichVuKB").val();//nghiant 01082017 //nghiant 14082017 (bo truyen tiepnhanid)
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HKQ", param);
				
				if(rs == '1'){
					DlgUtil.showMsg("Hủy kết quả thành công",undefined,1000);
					//$("#hdfIDMauBenhPham").val("");
				} 
				//nghiant 01082017
				else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,3000);	
				}
				else {
					DlgUtil.showMsg("Có lỗi xảy ra, không thể hủy kết quả",undefined,3000);	
				} 
			}			
		});
		
		$('#btnTraKetQua2').on('click', function () {			
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
			$("#hdfIDMauBenhPham").val()+'$'+
			$("#hdfIDDichVuKB").val()
			;

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TKQ", param);
			
			if(rs == '1'){				
				DlgUtil.showMsg("Trả kết quả thành công",undefined,1000);
//				$("#hdfIDMauBenhPham").val("");
			} 
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể trả kết quả",undefined,3000);	
			} 
		});
		
		$('#btnHuyKetQua2').on('click', function () {			
			if(!rolePTH){//nghiant 05092017 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,1000);	
			}else{
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				$("#hdfIDMauBenhPham").val()+'$'+
				$("#hdfIDDichVuKB").val();//nghiant 01082017 //nghiant 14082017 (bo truyen tiepnhanid)
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HKQ", param);
				
				if(rs == '1'){
					DlgUtil.showMsg("Hủy kết quả thành công",undefined,1000);
					//$("#hdfIDMauBenhPham").val("");
				} 
				//nghiant 01082017
				else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,3000);	
				}
				else {
					DlgUtil.showMsg("Có lỗi xảy ra, không thể hủy kết quả",undefined,3000);	
				} 
			}
		});
		
		$('#btnInKQ1').on('click', function () {	
			inKetQua1();
		});	
			
		$('#btnInKQ2').on('click', function () {	
			inKetQua2();
		});
		
	}
	
	// load thong tin benh nhan
	function loadThongTinBenhNhan(){
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
		var row = data_ar[0];
		
		$("#lblHoTen").text(row["TENBENHNHAN"]==null?"":row["TENBENHNHAN"]);
		bnGioiTinh = row["GIOITINH"];
		bnTuoi = row["TUOI"];
		$("#chbKhan").prop('checked', row["LOAIMAUBENHPHAM"]=="2"?true:false);		
		$("#lblDiaChi").text(row["GIOITINH"]+" - "+row["NGAYSINH"]+" - "+row["DIACHI"]);
		$("#lblSoPhieu").text(row["SOPHIEU"]==null?"":row["SOPHIEU"]);
		$("#lblNoiGui").text(row["TENPHONG"]==null?"":row["TENPHONG"]);
		$("#lblDoiTuong").text(row["DOITUONG"]==null?"":row["DOITUONG"]);
		$("#lblTGChiDinh").text(row["NGAYDICHVU"]==null?"":row["NGAYDICHVU"]);
		$("#lblTGKetQua").text(row["NGAYMAUBENHPHAM_HOANTHANH"]==null?"":row["NGAYMAUBENHPHAM_HOANTHANH"]);
		$("#lblBarcode").text(row["BARCODE"]==null?"":row["BARCODE"]);
		$("#lblBacSi").text(row["TENBACSI"]==null?"":row["TENBACSI"]);
		$("#lblChanDoan").text(row["CHANDOAN"]==null?"":row["CHANDOAN"]);
		$("#txtNhanXet").val(row["GHICHU"]==null?"":row["GHICHU"]);
		$("#lblNguoiThucHien").text(row["NGUOITRAKETQUA"]==null?"":row["NGUOITRAKETQUA"]);
	}
	
	
	function loadViKhuanLaoM1(){		   	
		var _sql_par=[$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()];
//		var _sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()]);
		var data_ba_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.SCOV1",_sql_par.join('$'));
    	if (data_ba_ar != null && data_ba_ar.length > 0) {
			var data = data_ba_ar[0];
			FormUtil.setObjectToForm("divDSVKLaoMau1", "", data);
			
			$("#lblMACHIDINH").text(data.MACHIDINH);
			$("#lblTENCHIDINH").text(data.TENCHIDINH);
			$("#hdfIDKQ1").val(data.ID);
			if(data.NGAYNHANMAU==""){
				$("#txtNGAYNHANMAU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
			}
			if(data.NGAYCAY==""){
				$("#txtNGAYCAY").val(moment().format('DD/MM/YYYY HH:mm:ss'));
			}
			if(data.THOIGIANLAYMAU==""){
				$("#txtTHOIGIANLAYMAU").val(moment().format('DD/MM/YYYY HH:mm:ss'));
			}
			if(data.THOIGIANTRA==""){
				$("#txtTHOIGIANTRA").val(moment().format('DD/MM/YYYY HH:mm:ss'));
			}
    	}
    	
	}

	function loadTTLANLAYMAU(){	
		if(CLS_HIENTHI_LAYMAUCOVID=="1"){
			document.getElementById('btnTraKetQua1').innerHTML = "<span class='glyphicon glyphicon-check'></span> Lưu & Đóng ";var _sql_par=[$("#hdfIDMauBenhPham").val(),$("#hdfIDDichVuID").val(),$("#hdfIDDichVuKB").val()];

			var data_ba_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTCOVI",_sql_par.join('$'));
			if (data_ba_ar != null && data_ba_ar.length > 0) {
				var data = data_ba_ar[0];			
				
				$("#txtLANLAYMAU").val(data.LANLAYMAU);
				$("#txtLANXN").val(data.LANXN);
				$("#txtMABENHPHAM").val(data.MABENHPHAM);
				if(data.NGUOIKIEMTRA!="" && data.NGUOIKIEMTRA!="-1"){
					var userid= data.USER_ID;
					var username = data.USERNAME;
					var fullname = data.FULLNAME;
					var option = $('<option value="'+userid+'">'+fullname+' ('+username+')</option>');
					$("#cboNGUOIKIEMTRA").empty();
					$("#cboNGUOIKIEMTRA").append(option);
				}				

				var ttthem1 = document.getElementById('ttthem1').style;
				ttthem1.display = 'table-row';
				var ttthem2 = document.getElementById('ttthem2').style;
				ttthem2.display = 'table-row';
				
				var timenow = moment();
				if(timenow.hour()>=6 && timenow.hour()<11) {
					if(data.NGAYNHANMAU=="") $("#txtNGAYNHANMAU").val(moment().format("DD/MM/YYYY HH:MM:SS"));
					else $("#txtNGAYNHANMAU").val(data.NGAYNHANMAU);
					
					if(data.NGAYCAY=="") $("#txtNGAYCAY").val(moment().format("DD/MM/YYYY 11:00:00"));
					else $("#txtNGAYCAY").val(data.NGAYCAY);
					
					if(data.NGAYKY=="") $("#txtNGAYKY").val(moment().format('DD/MM/YYYY 17:00:00'));
					else $("#txtNGAYKY").val(data.NGAYKY);
				} 
				else if(timenow.hour()>=11 && timenow.hour()<18){					
					if(data.NGAYNHANMAU=="") $("#txtNGAYNHANMAU").val(moment().format("DD/MM/YYYY HH:MM:SS"));
					else $("#txtNGAYNHANMAU").val(data.NGAYNHANMAU);

					if(data.NGAYCAY=="") $("#txtNGAYCAY").val(moment().format("DD/MM/YYYY 18:00:00"));
					else $("#txtNGAYCAY").val(data.NGAYCAY);
					
					if(data.NGAYKY=="") $("#txtNGAYKY").val(moment().add(1,'days').format('DD/MM/YYYY 08:00:00'));
					else $("#txtNGAYKY").val(data.NGAYKY);
				}
				else if(timenow.hour()<6 && timenow.hour()>=18){
					if(data.NGAYNHANMAU=="") $("#txtNGAYNHANMAU").val(moment().add(1,'days').format("DD/MM/YYYY HH:MM:SS"));
					else $("#txtNGAYNHANMAU").val(data.NGAYNHANMAU);
					
					if(data.NGAYCAY=="") $("#txtNGAYCAY").val(moment().add(1,'days').format("DD/MM/YYYY 06:00:00"));
					else $("#txtNGAYCAY").val(data.NGAYCAY);
					
					if(data.NGAYKY=="") $("#txtNGAYKY").val(moment().add(1,'days').format('DD/MM/YYYY 12:00:00'));
					else $("#txtNGAYKY").val(data.NGAYKY);
				}
				
				if($("#txtAMTINH").val().trim()=="" && $("#txtDUONGTINH").val().trim()=="" && $("#txtNGHINGO").val().trim()==""){
					$("#txtAMTINH").val("X");
				}			
			}
		}
		else if (CLS_HIENTHI_LAYMAUCOVID=="2"){
			$(".clsThoiGianLayMau").show();

			var ttthem3 = document.getElementById('ttthem3').style;
			ttthem3.display = 'table-row';
			var ttthem4 = document.getElementById('ttthem4').style;
			ttthem4.display = 'table-row';
			
		}		
	}
	
	
	// lưu kết quả khi người dùng nhập trên lưới
	function luuKetQua1(){
		if($("#chkCHECKCHUYEN").is(":checked") && $("#txtPHONGXN").val() =="") {
			DlgUtil.showMsg("Nhập tên phòng Xét nghiệm khẳng định ! ");
			$("#txtPHONGXN").focus();
		} else {
			objData = new Object();
			FormUtil.setFormToObject("divDSVKLaoMau1", "", objData);
			objData["ID"] =$("#hdfIDKQ1").val();
			objData["DICHVUKHAMBENHID"] =$("#hdfIDDichVuKB").val();
			objData["DICHVUID"] =$("#hdfIDDichVuID").val();
			
			if (CLS_HIENTHI_LAYMAUCOVID=="2"){
				var timelaymau = moment($('#txtTHOIGIANLAYMAU').val(),"DD/MM/YYYY HH:mm:ss");
				var timenhanmau = moment($('#txtNGAYNHANMAU').val(),"DD/MM/YYYY HH:mm:ss");
				var timethuchien = moment($('#txtNGAYCAY').val(),"DD/MM/YYYY HH:mm:ss");
				var timetra = moment($('#txtTHOIGIANTRA').val(),"DD/MM/YYYY HH:mm:ss");
				var CLS_THOIGIAN_LAYMAUCOVID = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_THOIGIAN_LAYMAUCOVID');
				var t1 = 15;
				var t2 = 5;
				var t3 = 15;
				if(CLS_THOIGIAN_LAYMAUCOVID!="0" && CLS_THOIGIAN_LAYMAUCOVID.split(",").length==3){
					var t = CLS_THOIGIAN_LAYMAUCOVID.split(",");
					var t1 = t[0];
					var t2 = t[1];
					var t3 = t[2];
				}
				if (timenhanmau.diff(timelaymau, 'minutes') < t1){
					DlgUtil.showMsg("Thời gian nhận mẫu phải sau thời gian lấy mẫu " + t1 + " phút",function(){
						$('#txtNGAYNHANMAU').focus();	
					},5000);
					
					return;
				} else if (timethuchien.diff(timenhanmau, 'minutes') < t2){
					DlgUtil.showMsg("Thời gian thực hiện phải sau thời gian nhận mẫu " + t2 + " phút",function(){
						$('#txtNGAYCAY').focus();	
					},5000);
					
					return;
				} else if (timetra.diff(timethuchien, 'minutes') < t3){
					DlgUtil.showMsg("Thời gian trả phải sau thời gian thực hiện " + t3 + " phút",function(){
						$('#txtTHOIGIANTRA').focus();	
					},5000);
					
					return;
				}
			}
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLSVK.SCOV1", JSON.stringify(objData));
			
			if(fl==1){
				DlgUtil.showMsg("Thêm mới thành công !",undefined,1000);
				loadViKhuanLaoM1();
				loadTTLANLAYMAU();
			}
			else if(fl==2){
				DlgUtil.showMsg("Cập nhật thành công !",undefined,1000);
				loadViKhuanLaoM1()
				loadTTLANLAYMAU();
			}		
			else {
				DlgUtil.showMsg("Không thành công !");
			}
		}
	}
	
	function luuKetQua2(){
		//$("#IDMOX").html
		
		if($("#cboMAU_MTB").val() =="0"){
			DlgUtil.showMsg("Bạn cần chọn dịch vụ trước khi lưu kết quả !");
		}else{
			objData = new Object();
			FormUtil.setFormToObject("divDSVKLaoMau2", "", objData);
			objData["ID"] =$("#hdfIDKQ2").val();
			objData["DICHVUKHAMBENHID"] =$("#hdfIDDichVuKB").val();
			objData["DICHVUID"] =$("#hdfIDDichVuID").val();
			//if(objData["SOXN2"]=="" || objData["SOXN2"]==null){
				//objData["SOXN2"]=objData["SOXN3"];
			//}
			if($("#cboMAU_MTB").val() =="1" || $("#cboMAU_MTB").val() =="2" ||
					$("#cboMAU_MTB").val() =="5"){
				objData["SOXN2"]=objData["SOXN3"];
				objData["KETLUAN"]=objData["KETLUAN2"];
			}
			
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLSVK.LKQVKL2", JSON.stringify(objData));
			
			if(fl==1){
				DlgUtil.showMsg("Thêm mới thành công !");
				loadViKhuanLaoM2()
			}
			else if(fl==2){
				DlgUtil.showMsg("Cập nhật thành công !");
				loadViKhuanLaoM2()
			}		
			else{
				DlgUtil.showMsg("Không thành công !");
			}
		}
	}
	
	function inKetQua1(){
		var par = [ 
			{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
			{name:'dichvukhambenhid',type:'String',value:$("#hdfIDDichVuKB").val()},
			{name:'dichvuid',type:'String',value:$("#hdfIDDichVuID").val()}
		];
		openReport('window', 'CLS_KQ_VK_SCOV2', 'pdf', par);
	}
	
	function inKetQua2(){
		if($("#cboMAU_MTB").val() =="0"){
			DlgUtil.showMsg("Bạn cần chọn dịch vụ trước khi lưu kết quả !");
		}else{
			var cot1,cot2,cot3,cot4;
			if($("#cboMAU_MTB").val() =="1" || $("#cboMAU_MTB").val() =="2"){
				cot1 = $("#IDINH").html();
				cot2 = $("#IDRMP").html();
				cot3 = $("#IDEMB").html();
				cot4 = $("#IDSM").html();
				
			}else if($("#cboMAU_MTB").val() =="3" || $("#cboMAU_MTB").val() =="4"){
				cot1 = $("#IDMOX").html();
				cot2 = $("#IDAMK").html();
				cot3 = $("#IDKM").html();
				cot4 = $("#IDCAP").html();
			}
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'dichvukhambenhid',type:'String',value:$("#hdfIDDichVuKB").val()},
				//{name:'cot1',type:'String',value:cot1},
				//{name:'cot2',type:'String',value:cot2},
				//{name:'cot3',type:'String',value:cot3},
				//{name:'cot4',type:'String',value:cot4},
				{name:'dichvuid',type:'String',value:$("#hdfIDDichVuID").val()}
			];
			
			//CLS_KQ_VK_LAO_MTB_PZA_LOAI2
			if($("#cboMAU_MTB").val() =="5"){
				openReport('window', 'CLS_KQ_VK_LAO_MTB_PZA_LOAI2', 'pdf', par);
			}else{
				openReport('window', 'CLS_KQ_VK_LAO_LOAI2', 'pdf', par);
			}
		}		
	}
	
	//nghiant 26062017
	//nghiant 26062017
	$('#btnEditTimePopUpX').on('click', function () {			
		var url = "manager.jsp?func=../canlamsang/CLS02X009_SuaNgayTraKetQua&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+opt._tiepnhanid;
		EventUtil.setEvent("CLS02X009_Thoat",function(e){
			DlgUtil.close("dlgSuaThoiGianTraKQ2");	
//			EventUtil.raiseEvent("CLS01X002_HUY",{msg:'reloadGrid'});
//			reloadAllGrid();
		});
		var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaThoiGianTraKQ2","divNhapKetQua",url,{},"Sửa ngày trả kết quả",300,400);
		dlgPopup.open("dlgSuaThoiGianTraKQ2");
	});
	
	function setEnabled(_ena, _dis) {
		for (var i =0; i<_ena.length; i++) {
			$("#"+_ena[i]).attr('disabled', false);
		}
		for (var i =0; i<_dis.length; i++) {
			$("#"+_dis[i]).attr('disabled', true);
		}
	}	
}
