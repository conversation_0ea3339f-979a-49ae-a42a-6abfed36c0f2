<!-- 
 M<PERSON> màn hình  : NTU02D010
 File mã nguồn : NTU02D010_CapThuoc.htm
 Mục đích  : <PERSON><PERSON><PERSON> diện màn hình
 	+ Chỉ định thuốc
 	+ Chỉ định vật tư
 	+ <PERSON><PERSON><PERSON>
 	+ <PERSON><PERSON><PERSON> vật tư
 	....(c<PERSON><PERSON> chức năng liên quan chỉ định thuốc, v<PERSON><PERSON> tư)
 Tham số vào : 
 	khambenhid 		: M<PERSON> khám bệnh ID 
	maubenhphamid 	: Mẫu bệnh phẩm ID, để load thông tin về phiếu đã được chỉ định
	opt 			: Tham số định nghĩa loại màn hình tương ứng
		+ 	opt 	= '02D010' -> <PERSON><PERSON><PERSON> hình chỉ định thuốc
		+ 	opt 	= '02D014' -> <PERSON><PERSON><PERSON> hình phiếu trả thuốc
		+ 	opt 	= '02D015' -> <PERSON><PERSON><PERSON> hình phiếu vật tư
		+ 	opt 	= '02D016' -> <PERSON><PERSON><PERSON> hình phiếu trả vật tư
		+ 	opt 	= '02D017' -> <PERSON><PERSON><PERSON> hình thuốc đông y
		+ 	opt 	= '02D018' -> <PERSON><PERSON><PERSON> hình trả thuốc đông y
	loaikedon 		: THam số định nghĩa loại kê đơn thuốc
		+	loaikedon = 1 -> Kê số lượng thuốc tổng hợp
		+	loaikedon = 0 -> Kê đơn chi tiết (sáng, trưa, chiều, tối)
 
 Người lập trình	 Ngày cập nhật  	Ghi chú
 linhvv				 5/9/2016			Sửa bổ xung tính năng
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">        
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>   
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
 
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js "></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.src.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../noitru/NTU02D100_TraThuoc.js?v=20180737"></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>
<script type="text/javascript" src="../ngoaitru/hssk.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/xml2json.js" ></script>
<link href="../common/script/select2/dist/css/select2.css" rel="stylesheet" />
<script src="../common/script/select2/dist/js/select2.full.js"></script>

<style>
.disabled {
	pointer-events: none;
	opacity: 0.4;
}

.setReadonly {
	float: right; 
	width: 100%; 
	background-color: white !important;	
}
</style>
	
<div class="form-horizontal container" id="" style="width: 1280px !important;">
	<div id="divDTCu" style="width: 100%; display: none"> <iframe src="" id="ifmView"></iframe> </div>	
	<input type="hidden" id="hdHUONGDANTHUCHIEN" value="">
	<input type="hidden" id="hdSOLUONGKHADUNG" value="">
	<input type="hidden" id="hdDONVI" value="">
	<input type="hidden" id="hidHOSOBENHANID" value="">
	<input type="hidden" id="hidMAHOSOBENHAN" value=""> <!-- day congboyte -->
	<input type="hidden" id="hidGIOITINHID" value="">
	<input type="hidden" id="hidNGAYSINH" value="">
	<input type="hidden" id="hidBENHNHANID" value="">
	<input type="hidden" id="hidLOAITVTID" value="">
	<input type="hidden" id="hidDIACHI" value="">
	<input type="hidden" id="hidNGHENGHIEP" value="">
	<input type="hidden" id="hidCANHBAOSOLUONG" value="">
	<input type="hidden" id="hidMABYT" value="">
	<input type="hidden" id="hidLIEUDUNGBD" value="">
	<input type="hidden" id="hidKHOTHUOCTHEOTHUOC" value="">
	<input type="hidden" id="hidCHOLANHDAODUYET" value="">
	<input type="hidden" id="hidTHUOCSAO" value="">
	<input type="hidden" id="hidTHUOCVTID" value="">
		<div class="row">
			<div class="col-xs-12 low-padding">
				<div class="col-xs-6 low-padding mgt5">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding required">
							<label class="">Ngày chỉ định</label>
						</div>						
						<div class="col-xs-4 low-padding">
							<div class="input-group">	
							  <input class="form-control input-sm before2" style="background-color: white;" id="txtTHOI_GIAN" name="txtTHOI_GIAN" valrule="TG tạo phiếu,trim_required" title="" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy HH:MM:SS" readonly="readonly">
							  <span id="spTG_CHIDINH" class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTHOI_GIAN','ddMMyyyy','dropdown',true,'24',true)"></span>							 
							</div>
						</div>						
						<div class="col-xs-2 low-padding required mgl10">
							<label id="lblTypeNgay">Ngày trả</label>
						</div>
						
						<div class="col-xs-4 low-padding mgl-10">
							<div class="input-group">	
							  <input class="form-control input-sm before2" style="background-color: white;" id="txtTG_DUNG" name="txtTG_DUNG" valrule="TG tạo phiếu,trim_required" title="" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy HH:MM:SS" readonly="readonly">
							  <span id="spTG_DUNG" class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTG_DUNG','ddMMyyyy','dropdown',true,'24',true)"></span>							 
							</div>
						</div>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding required">
							<label class="">Kho thuốc trả</label>
						</div>						
						<div class="col-xs-4 low-padding">
							<select class="form-control input-sm" id="cboMA_KHO"></select> 
						</div>		
						<div class="col-xs-6 low-padding" id="divPhieuDT"> <!-- L2PT-22531 -->
							<div class="col-xs-4 low-padding" id="lblpdtri">
								<label class="mgl10" >Phiếu điều trị</label>
							</div>
							<div class="col-xs-5 low-padding">
								<select class="form-control input-sm" id="cboPHIEU_CD"></select> 
							</div>
	<!-- 						START-- L2K74TW-605 -- hongdq -->
							<div class="col-xs-3 low-padding">
			     				<div class="input-group">								  
						  			<input class="form-control input-sm" id="txtTGSEARCH" name="txtTGSEARCH" data-mask="00/00/0000" style="display: none">
						  			<span id="calSearch" class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtTGSEARCH','ddMMyyyy','dropdown',false,'24',true)"></span>
						  			<button id="btnCancelSearch" type="button" class="btn btn-sm btn-primary" id="btnCLEARCHANDOANKT" modedisxt="" style="height: 24px;" title="Hủy tìm kiếm">
										<span class="glyphicon glyphicon-remove"></span>
									</button>
								</div> 		     		
			     			</div>
<!-- 						END-- L2K74TW-605 -- hongdq -->
						</div>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding">
							<label>Từ ngày kê</label>
						</div>						
						<div class="col-xs-4 low-padding">
							<div class="input-group">	
							  <input class="form-control input-sm before2" style="background-color: white;" id="txtTUNGAYKE" name="txtTUNGAYKE" valrule="" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" readonly="readonly">
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTUNGAYKE','ddMMyyyy','dropdown',false,'24',true)"></span>							 
							</div>
						</div>						
						<div class="col-xs-2 low-padding mgl10">
							<label>Đến ngày kê</label>
						</div>
						
						<div class="col-xs-4 low-padding mgl-10">
							<div class="input-group">	
							  <input class="form-control input-sm before2" style="background-color: white;" id="txtDENNGAYKE" name="txtDENNGAYKE" valrule="" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" readonly="readonly">
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtDENNGAYKE','ddMMyyyy','dropdown',false,'24',true)"></span>							 
							</div>
						</div>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding">
							<label id="lblLoaiTra">Đơn thuốc/VT trả</label>
						</div>						
						<div class="col-xs-4 low-padding">
							<select class="form-control input-sm" id="cboDONTHUOCVT"></select> 
						</div>	
						<div class="col-xs-1 low-padding" id="btnTraAllPhieu">
							<button class="btn btn-sm btn-primary mgl10" id="btnAllPhieu" style="height:24px;">Trả cả phiếu</button>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding" style="display: none;" id="divICD">
					<div class="col-xs-12 low-padding mgt5" >
						<div class="col-xs-2 low-padding required">
							<label class="">ICD10</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm upper icdchange" id="txtMACHANDOANICD">
						</div>
						<div class="col-xs-8 low-padding" id="divBc">
							<input class="form-control input-sm deletewhennull" style="float: right; width: 100%; background-color: white;" id="txtTENCHANDOANICD" readonly="readonly">
						</div>
						<!-- tuyennx_add_start_20170724 them mo ta benh chinh-->
						<div class="col-xs-2 low-padding" style="display:none" id="divSuaBc">
							<input class="form-control input-sm" style="float: right; width: 100%; background-color: white;" id="txtGHICHU_BENHCHINH" maxlength="500">
						</div>
						<!-- tuyennx_add_end_20170724 -->
					</div>
					
					<div class="col-xs-12 low-padding" >
						<div class="col-xs-4 low-padding">
							<div class="col-xs-12 low-padding">
								<div class="col-xs-6 low-padding">
									<label class="">Bệnh Kèm theo</label>
								</div>
								<div class="col-xs-6 low-padding">
									<input class="form-control input-sm upper" id="txtMATENCHANDOANICD_KT">
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-6 low-padding">
									<label></label>
								</div>
								<div class="col-xs-6 low-padding">
									<button class="btn btn-sm btn-primary" type="button" id="btnBP" style="width: 100%; height: 24px;">Xóa bệnh KT</button>
									<button class="btn btn-sm btn-primary mgt3" type="button" id="btnEDITBP" style="width: 100%;height: 24px;">Sửa BP</button>
								</div>
							</div>
						</div>
						<div class="col-xs-8 low-padding">
							<textarea class="form-control kb-i-col-m mgb3 setReadonly" rows="4" id="txtTENCHANDOANICD_KT" readonly></textarea>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-12 low-padding mgt5">
						<div class="col-xs-2 low-padding">
							<label class="mgl10">Mã BA</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" id="lblPATIENTCODE" readonly>
						</div>						
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm setReadonly" id="lblPATIENTNAME" readonly>
						</div>
						<!-- <div class="col-xs-2 low-padding">
							<input class="form-control input-sm setReadonly" id="lblPATIENTCODE" readonly>
						</div>
						<div class="col-xs-3 low-padding ">
							<label class="mgl10">Năm sinh / Giới tính</label>
						</div>
						<div class="col-xs-3 low-padding">
							<div class="col-xs-6 low-padding">
								<input class="form-control input-sm setReadonly" id="lblBIRTHDAY_YEAR" readonly>
							</div>
							<div class="col-xs-6 low-padding">
								<input class="form-control input-sm setReadonly" id="lblGIOITINHCODE" readonly>
							</div>
						</div> -->
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding ">
							<label class="mgl10">Địa chỉ</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm setReadonly" id="txtDIACHI" readonly>
						</div>
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding ">
							<label class="mgl10">Năm sinh</label>
						</div>
						<div class="col-xs-4 low-padding">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm setReadonly" id="lblBIRTHDAY_YEAR" readonly>
							</div>
							<!-- <div class="col-xs-6 low-padding">
								<input class="form-control input-sm setReadonly" id="lblGIOITINHCODE" readonly>
							</div> -->
						</div>	
						<div class="col-xs-2 low-padding ">
							<label class="mgl10">Giới tính</label>
						</div>
						<div class="col-xs-4 low-padding">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm setReadonly" id="lblGIOITINHCODE" readonly>
							</div>
							<!-- <div class="col-xs-6 low-padding">
								<input class="form-control input-sm setReadonly" id="lblGIOITINHCODE" readonly>
							</div> -->
						</div>					
					</div>
					<div class="col-xs-12 low-padding">
						<div class="col-xs-2 low-padding">
							<label class="mgl10">Số thẻ</label>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm setReadonly" id="lblMA_BHYT" readonly>
						</div>
						<div class="col-xs-2 low-padding ">
							<label class="mgl10">Đối tượng / tỷ lệ</label>
						</div>
						<div class="col-xs-4 low-padding">
							<div class="col-xs-6 low-padding">
								<input class="form-control input-sm setReadonly" id="lblDT_THANHTOAN" readonly>
							</div>
							<div class="col-xs-6 low-padding">
								<input class="form-control input-sm setReadonly" id="lblMUCHUONG_BHYT" readonly>
							</div>
						</div>
					</div>
				</div>
				
				<div class="col-xs-12 low-padding">
					<!-- <div class="col-xs-6 low-padding mgt5">
						<div class="col-xs-2 low-padding required">
							<label class="">Ngày chỉ định</label>
						</div>
						
						<div class="col-xs-4 low-padding">
							<div class="input-group">	
							  <input class="form-control input-sm before2" style="background-color: white;" id="txtTHOI_GIAN" name="txtTHOI_GIAN" valrule="TG tạo phiếu,trim_required" title="" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy HH:MM:SS" readonly="readonly">
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTHOI_GIAN','ddMMyyyy','dropdown',true,'24',true)"></span>							 
							</div>
						</div>
						
						<div class="col-xs-2 low-padding required mgl10">
							<label id="lblTypeNgay">Ngày trả</label>
						</div>
						
						<div class="col-xs-4 low-padding mgl-10">
							<div class="input-group">	
							  <input class="form-control input-sm before2" style="background-color: white;" id="txtTG_DUNG" name="txtTG_DUNG" valrule="TG tạo phiếu,trim_required" title="" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy HH:MM:SS" readonly="readonly">
							  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTG_DUNG','ddMMyyyy','dropdown',true,'24',true)"></span>							 
							</div>
						</div>
					</div> -->
					<div class="col-xs-6 low-padding mgt5">
						<div class="col-xs-12 low-padding ">
							<!-- <div class="col-xs-2 low-padding">
								<label class="mgl10" id="lblloaithuoc" >Loại thuốc</label>
							</div>
							<div class="col-xs-4 form-inline low-padding">
								<select class="form-control input-sm" id="cboLOAITHUOC" style="width:100%;">				
								</select>
							</div> -->
							<div class="col-xs-6 low-padding" style = "display:none;" id="divbske">
								<div class="col-xs-4 low-padding required">
									<label class="mgl10" id="lblloaithuoc" >Bác sỹ kê đơn</label>
								</div>
								<div class="col-xs-8 form-inline low-padding">
									<select class="form-control input-sm" id="cboBACSIID" style="width:100%;">				
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-12 low-padding ">
					<!-- <div class="col-xs-1 low-padding required">
						<label class="" id="lblKho"></label>
					</div>
					<div class="col-xs-2 low-padding">
						<select class="form-control input-sm" id="cboMA_KHO"></select> 
					</div> -->
					<!-- <div class="col-xs-3 low-padding" id="divPhieuDT">
						<div class="col-xs-4 low-padding" id="lblpdtri">
							<label class="mgl10" >Phiếu điều trị</label>
						</div>
						<div class="col-xs-5 low-padding">
							<select class="form-control input-sm" id="cboPHIEU_CD"></select> 
						</div>
						START-- L2K74TW-605 -- hongdq
						<div class="col-xs-3 low-padding">
		     				<div class="input-group">								  
					  			<input class="form-control input-sm" id="txtTGSEARCH" name="txtTGSEARCH" data-mask="00/00/0000" style="display: none">
					  			<span id="calSearch" class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtTGSEARCH','ddMMyyyy','dropdown',false,'24',true)"></span>
					  			<button id="btnCancelSearch" type="button" class="btn btn-sm btn-primary" id="btnCLEARCHANDOANKT" modedisxt="" style="height: 24px;" title="Hủy tìm kiếm">
									<span class="glyphicon glyphicon-remove"></span>
								</button>
							</div> 		     		
		     			</div>
						END-- L2K74TW-605 -- hongdq
					</div> -->
					<!-- <div id="divDonThuocVT" style="display: none;">
						<div class="col-xs-1 low-padding required">
							<label class="mgl10" id="lblLoaiTra">Đơn thuốc/VT trả</label>
						</div>
						<div class="col-xs-2 low-padding">
							<select class="form-control input-sm" id="cboDONTHUOCVT"></select> 
						</div>
					</div> -->
					<!-- 
					<div id="divSLNGAYKE">
						<div class="col-xs-1 low-padding required" id="dvSONGAY_KE_LBL">
							<label class="mgl10" style="width: 100%;">Số ngày kê đơn</label>
						</div>
						
						<div class="col-xs-2 form-inline low-padding" style="display: none; margin-left: 53px;" id="dvSONGAY_KE_TXT">
							<input class="form-control input-sm clsnumber" id="txtSONGAY_KE" style="width: 75%;margin-right: -91px;" title="">
						</div>
					</div> 
					<div id="divSLTHANG">
						<div class="col-xs-1 low-padding required">
							<label class="mgl10">Số thang</label>
						</div>
						
						<div class="col-xs-2 form-inline low-padding">
							<input class="form-control input-sm clsnumber" id="txtSLTHANG" style="width: 50%;" title="" value = "7">
						</div>
					</div> -->
				</div>
			</div>
			<!--<div class="col-xs-12 low-padding">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-2 low-padding">
						<label class="mgt5">Lời dặn bác sỹ</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm" style="width: 100%;" id="txtTKLOIDANBS" valrule="Lời dặn BS,max_length[200]" title="">
					</div>
					<div class="col-xs-8 low-padding">
						<textarea class="form-control input-sm" style="width: 100%;" id="txtLOIDANBS" rows="3" valrule="Lời dặn BS,max_length[1000]" title="" ></textarea>
					</div>
				</div>
				<div class="col-xs-6 low-padding" style="display:none;" id="divhenkham">
					<div class="col-xs-12 low-padding">
						<div class="col-xs-3 low-padding">
							<label class="mgl10">Số ngày hẹn tái khám</label>
						</div>
					
						<div class="col-xs-3 low-padding">		
							<input class="form-control input-sm clsnumber" style="width: 100%;" id="txtTG_HENKHAM" title="">						
						</div>
					
						<div class="col-xs-5 form-inline low-padding mgl25" id="chckNgayhen" >						
							 <label><input type="checkbox" id="chkCapPhieuHenKham" value=""></label><label style="font-weight: 700 !important; font-size: 0.9em;"> Đã cấp phiếu hẹn khám tại bệnh viện</label>
						</div>
					</div>
					<div class="col-xs-12 low-padding">											
						<label id="msgCNKQ" style ="margin-left: 10px;font-size: 20px;color: blue;"></label>
					</div>
				</div> 
			</div>-->
		</div>
			
		<!-- Mới------------------- -->
	
		<div class="row capthuoc">
			<div class="col-xs-12 low-padding searchthuoc">
				<div class="col-xs-2 low-padding required" id="dvSearchName" style="text-align: center;">
					<label class="mgt5" id="lbSearchName"></label>
				</div>
				<div class="col-xs-2 low-padding required" style="text-align: center;display:none;" id="dvTenThuoc">
					<label class="mgt5" id="lbTenThuoc"></label>
				</div>
				<div class="col-xs-1 low-padding required" style="text-align: center;" id="lblDUONGDUNG">
					<label class="mgl5 mgt5">Đường dùng</label>
				</div>
				<div class="col-xs-3 low-padding" id="dvKE_CHITIET_LB">
					<div class="col-xs-2 low-padding required" style="text-align: center;">
						<label class="mgt5">S.ngày</label>
					</div>
					<div class="col-xs-2 low-padding" style="text-align: center;">
						<label class="mgt5">Sáng</label>
					</div>
					<div class="col-xs-2 low-padding" style="text-align: center;">
						<label class="mgt5">Trưa</label>
					</div>
					<div class="col-xs-2 low-padding" style="text-align: center;">
						<label class="mgt5">Chiều</label>
					</div>
					<div class="col-xs-2 low-padding" style="text-align: center;">
						<label class="mgt5">Tối</label>
					</div>
					<div class="col-xs-2 low-padding" style="text-align: center;">
						<label class="mgt5">SL<span class="required1">(*)</span></label>
					</div>
				</div>
				<!-- <div class="col-xs-2 low-padding required" id="dvSONGAY_KE_LBL" style="text-align: center;">
					<label class="mgl5 mgt5">Số ngày kê</label>
				</div> -->
				
				<div class="col-xs-1 low-padding" id="dvKE_TONG_LB">
					<div class="col-xs-12 low-padding" style="text-align: center;">
						<label class="mgt5">Số lượng<span class="required1">(*)</span></label>
					</div>
				</div>
				
				<div class="col-xs-2 low-padding" id="dvlLIEU_DUNG" style="display:none;">
					<div class="col-xs-3 low-padding" style="text-align: center;">
						<label class="mgt5">SL/lần<span class="required1">(*)</span></label>
					</div>
					<div class="col-xs-3 low-padding" style="text-align: center;">
						<label class="mgt5">S.lần/Ngày<span class="required1">(*)</span></label>
					</div>
					<div class="col-xs-6 low-padding" style="text-align: center;">
						<label class="mgt5">Liều dùng<span class="required1">(*)</span></label>
					</div>
				</div>
				
				<div class="col-xs-3 low-padding" style="text-align: center;" id="dvlGhiChu">
					<label class="mgt5" id="lblcachdung">Cách dùng<span class="required1">(*)</span></label>
				</div>
				
				<div class="col-xs-1 low-padding" style="text-align: center;" id="dvlDVQD">
					<label class="mgt5" id="lblcachdung">ĐVQĐ</label>
				</div>
			</div>
		
		<div class="col-xs-12 low-padding mgt5 mgb3">
			<!-- <div class="col-xs-2 low-padding before"> -->
			<div class="col-xs-2 low-padding" id="dvDS_THUOC">
				<input class="form-control input-sm mgl5" style="width: 95%;" id="txtDS_THUOC"/>
			</div>
			<div class="col-xs-2 low-padding" id="dvTENTHUOC" style="display:none;">
				<input class="form-control input-sm" style="width: 98%;" id="txtTENTHUOC" name="txtTENTHUOC" readonly="readonly" valrule="ten thuoc,trim_required" title="">
			</div>
			<div class="col-xs-1 low-padding" id="dvDUONG_DUNG">
				<select class="form-control input-sm" style="width: 98%;" id="cboDUONG_DUNG" valrule="Đường dùng,trim_required"></select>
			</div>
			<div class="col-xs-3 low-padding"  id="dvKE_CHITIET_TXT">
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm clsnumber notnullandlonhon0" msgErr="Số ngày," style="width: 95%;" id="txtSO_NGAY" valrule="Số ngày,trim_required|decimal|max_length[3]" title="">
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm clsnumber" style="width: 95%;" id="txtSANG" valrule="Sáng,decimal|max_length[3]" title="" >
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm clsnumber" style="width: 95%;" id="txtTRUA" valrule="Trưa,decimal|max_length[3]" title="">
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm clsnumber" style="width: 95%;" id="txtCHIEU" valrule="Chiều,decimal|max_length[3]" title="" >
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm clsnumber" style="width: 95%;" id="txtTOI" valrule="Tối,decimal|max_length[3]" title="" style="float: right;">
				</div>
				<div class="col-xs-2 low-padding">
					<input class="form-control input-sm clsnumber notnullandlonhon0 formatdot" msgErr="Số lượng," style="width: 95%;" id="txtSOLUONG_CHITIET" name="txtSOLUONG_CHITIET" valrule="Số lượng,trim_required|decimal|max_length[3]" title="">
				</div>
			</div>
			<div class="col-xs-1 low-padding required" id="dvKE_TONG_TXT">
				<div class="col-xs-12 low-padding">
					<input class="form-control input-sm clsnumber" style="width: 95%;" id="txtSOLUONG_TONG" name="txtSOLUONG_TONG" valrule="Số lượng,trim_required|decimal|max_length[3]" title="">
				</div>
			</div>
			
			<div class="col-xs-2 low-padding" id="dvLIEU_DUNG" style="display:none;">
				<div class="col-xs-3 low-padding">
					<input class="form-control input-sm clsfloat notnullandlonhon0" msgErr="Số lượng/ số lần," style="width: 100%;" id="txtSLSOLAN" placeholder="" title="Số lượng/ số lần" style="float: right;">
				</div>
				<div class="col-xs-3 low-padding">
					<input class="form-control input-sm clsnumber notnullandlonhon0" msgErr="Số lần/Số ngày,"  style="width: 100%;" id="txtSOLANSONGAY" placeholder="" title="Số lần/Số ngày" style="float: right;">
				</div>
				<div class="col-xs-6 low-padding">
					<input class="form-control input-sm isnotnull" style="width: 100%;" msgErr="Liều dùng,"  id="txtLIEUDUNG" placeholder="" title="Liều dùng" style="float: right;">
				</div>
			</div>
			
			<div class="col-xs-1 low-padding" id="dvSearchCD">
				<div class="col-xs-6 low-padding">
					<input class="form-control input-sm" style="width: 100%;" id="txtTGSD" placeholder="tg" title="Thời gian dùng thuốc" style="float: right;">
				</div>
				<div class="col-xs-6 low-padding">
					<input class="form-control input-sm" style="width: 100%;" id="txtSearchCD" placeholder="cách dùng" title="cách dùng thuốc" style="float: right;">
				</div>
			</div>
			<div class="col-xs-2 low-padding" id="dvGhiChu">
				<input class="form-control input-sm" msgErr="Cách dùng," style="width: 99%;" id="txtGHICHU" name="txtGHICHU" valrule="Ghi chú,max_length[200]" title="">
			</div>
			
			<div class="col-xs-1 low-padding required" id="dvQĐ">
				<div class="col-xs-12 low-padding">
					<select class="form-control input-sm" style="width: 100%;" id="cboDVQUYDOI" valrule="Đơn vị quy đổi" disabled></select>
				</div>
			</div>
		</div>
	</div>
		
	<div class="row mgt-5 mgb3">
		<div class="col-xs-12 low-padding mgt-10">
			<table id="grdDONTHUOC"></table>
		</div>
		<div class="contextMenu" id="contextMenu" style="display:none;">
	        <ul style="width:300px !important; font-size: 65%;" id="grMenuRight">
	            <li id="changeBHYT">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển BHYT</span>
	            </li>
	            <li id="changeVP">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển Viện phí</span>
	            </li>
	            <li id="changeYC">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển Dịch vụ</span>
	            </li> 
	            <!-- <li id="changeDK">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển Đi kèm</span>
	            </li>  
	            <li id="changeHPCK">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển Hao phí công khám</span>
	            </li>  
	            <li id="changeHPPTTT">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển Hao phí PTTT</span>
	            </li> -->
	            <li id="changeHPK">
	                <span class="ui-icon ui-icon-pencil" style="float:left"></span>
	                <span style="font-size:130%; font-family:Verdana">Chuyển Hao phí khác</span>
	            </li>
	        </ul>
	    </div>
	</div>
	<div class="col-xs-12 low-padding mgb3">
		<!-- <label class="col-xs-1 low-padding"></label> -->
		<div class="col-xs-9" style="text-align: center;">
			<button id="btnNhapMoi" class="btn btn-sm btn-primary"><span class="fa fa-file-text"></span> Nhập mới</button>
			<button id="btnAdd" class="btn btn-sm btn-primary" style="display:none;"></button>
			<button id="btnSave" class="btn btn-sm btn-primary"></button>
			<button id="btnXuTri" class="btn btn-sm btn-primary"> <span class="glyphicon glyphicon-tasks"></span> Xử trí </button>
			<button id="btnDTMau" class="btn btn-sm btn-primary">
				<span class="fa fa-file-text"></span> ĐT mẫu</button>
			<button id="btnDTCu" class="btn btn-sm btn-primary">
				<span class="fa fa-file-text"></span> ĐT cũ</button>
			<button id="btnTConSD" class="btn btn-sm btn-primary">
				<span class="fa fa-file-text"></span> Thuốc còn SD</button>
			<!--tuyennx_add_start_20170816 y/c L2DKBD-195	-->
			<button id="btnTDiUng" class="btn btn-sm btn-primary">
				<span class="fa fa-file-text"></span> Dị ứng thuốc</button>
			<!--tuyennx_add_end_20170816 y/c L2DKBD-195	-->
			
			<button id="btnPdDt" class="btn btn-sm btn-primary">
				<span class=""></span> Phác đồ ĐT</button>
				
			<button id="btnClose" class="btn btn-sm btn-primary">
				<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>	
		</div>
		<!-- <div class="col-xs-4" style="text-align: center;">
			<div class="input-group">
				<input class="form-control input-sm" style="float:left; width: 98%; margin-top: 2px!important;" id="txtTEXT_TEMP" name="txtTEXT_TEMP" title="">
				<span class="input-group-btn">
					<button id="btnSaveTemp" class="btn btn-primary">
					<span class="glyphicon glyphicon-floppy-disk"></span> Lưu mẫu</button>
				</span>
			</div>
		</div> -->
		<!--<div class="col-xs-1" style="text-align: center;"></div>-->
		<div class="col-xs-3" style="text-align: center;">
			<input class="form-control input-sm" style="float:left; width: 65% !important; margin-top: 2px!important;" id="txtTEXT_TEMP" name="txtTEXT_TEMP" title="">
			<button type="submit" id="btnSaveTemp" class="btn btn-sm btn-primary">
				<span class="glyphicon glyphicon-floppy-disk"></span> Lưu mẫu</button>
		</div>
</div>

	<div id="divDlg" style="width: 100%; display: none">
		<iframe src="" id="ifmView" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
	</div>
<script>
	var paramInfo=CommonUtil.decode('{paramData}');
	var subdept_id = paramInfo.subdept_id;
	var company_id = '{company_id}';
	var hospital_id = '{hospital_id}';
	var hospital_code = '{hospital_code}'; // day congboyte
	var user_id = '{user_id}';
	var uuid = '{uuid}';
	var officer_id = '{officer_id}';
	var sys_par=[];
	var hideMenu = '{hideMenu}';
	var url ='{url}';
	var session_par=[];
	var showDelete = '{showDelete}';
	var khoaId = '{dept_id}';
	var _option = '{opt}';
	var _khambenhid='{khambenhid}';
	initRest(uuid);	
	
	var _opts={
			lang: lang,
			_param:session_par,
			_uuid:uuid,
			user_id:user_id,
			_hospital_id: hospital_id,
			khoaId : khoaId,
			phongId : subdept_id,
			option:_option,
			company_id : company_id,
			phieudieutriid:"",
			macdinh_hao_phi:"",
			khambenh_id:_khambenhid
		}
	
	var mode = '{showMode}';

	var objVar;
	if(mode=='dlg') {
		//console.log('child var1='+var1+' var2='+var2);
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		objVar=EventUtil.getVar("dlgVar");
		console.log('var from parent objVar='+JSON.stringify(objVar));
		//_opts=$.extend(def_opts,objVar);
		_opts.khambenh_id = objVar.khambenhid;
		_opts.maubenhpham_id = objVar.maubenhphamid;
		_opts.option = objVar.opt;
		_opts.loaikedon = objVar.loaikedon;
		
		_opts.dichvuchaid = objVar.dichvuchaid;
		if(objVar.phieudieutriid)
			_opts.phieudieutriid = objVar.phieudieutriid;
		else 
			_opts.phieudieutriid='';
		if(objVar.macdinh_hao_phi)
			_opts.macdinh_hao_phi = objVar.macdinh_hao_phi;
		else 
			_opts.macdinh_hao_phi='0';
		_opts._hospital_id = hospital_id;
		_opts.loadkhotheo = objVar.loadkhotheo;
		
		_opts.hospital_code = paramInfo.HOSPITAL_CODE; // day congboyte
		initAjax("/vnpthis");//them lib ajax
		ajaxSvc.register("CongDLYTWS"); 
		//ajaxSvc.register("CongHSSKWS");
		//ajaxSvc.register("CongBTKDWS"); 
		
		_opts.kieutra = objVar.kieutra;
		_opts.TRATHUOC_DUOC = objVar.TRATHUOC_DUOC;
		_opts._mabenhnhan = objVar.mabenhnhan;
		//tuyennx_add_start_20170112 L2DKBD-880
		if(objVar.phongId !== undefined)
			_opts.phongId = objVar.phongId;
		//tuyennx_add_end_20170112 L2DKBD-880
	}	
	
	var trathuoc = new TraThuoc(_opts);
	trathuoc.load();	
</script>