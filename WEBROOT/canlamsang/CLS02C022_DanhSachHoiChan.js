function BBHC(opt){	
	this.load = doLoad;	

	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;		

		$("#hdfIDMauBenhPham").val(opt._idmaubenhpham);

		$("#hidUserID").val(user_id);
		
		initControl();
		bindEvent();		
	}

	function initControl(){
		$.extend($.jgrid.inlineEdit, {
			keys: true
		});
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",$("#hdfIDMauBenhPham").val());	
		
		$('#tcHoiChuan').ntu02d032_phc({
			_grdHoiChan : 'grdPhieuHoiChuan',
			_khambenhid: data_ar[0].KHAMBENHID,
        	_benhnhanid: data_ar[0].BENHNHANID,
        	_lnmbp:	"15",
        	_modeView: "0", // =1 chi view; !=1 la update
        	_hosobenhanid: ""
        });		
	}	
	
	function bindEvent(){
		
	}
}