<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>


<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT02K089_TOOLSUATTTHUOC1.js?v=201907191"></script>

<div width="100%" id="divMain" class="container">
	<div class="col-md-12 low-padding mgt10">
		<div class="col-md-5 low-padding">
			<div class="col-md-3 low-padding">
				<label>Hồ sơ bệnh án</label>
			</div>
			<div class="col-md-9 low-padding">
				<input type="text" class="form-control input-sm kb-i-col-m"
							style="width: 100%;" id="txtMAHOSOBENHAN" name="txtMAHOSOBENHAN" title="">
			</div>
		</div>
		<div class="col-md-1 low-padding"></div>
		<div class="col-md-1 low-padding">
			<button type="button" class="btn btn-sm btn-primary" id="btnSEARCH">
				<span class="glyphicon glyphicon-floppy-disk"></span> Tìm kiếm
			</button>
		</div>
	</div>
	
	<div class="col-md-12 low-padding">
		<table id="grdPKDK"></table>
		<div id="pager_grdPKDK"></div>
	</div>
	
	<div style="display:none">
		<input type="text" id="txtDICHVUKHAMBENHID" name="txtDICHVUKHAMBENHID">
		<input type="text" id="txtMAUBENHPHAMID" name="txtMAUBENHPHAMID">
	</div>
	<div class="contextMenu" id="contextMenu"
		style="display: none; width: 250px;">
		<ul style="width: 250px; font-size: 65%;"> 
            <li id="updateTTThuoc" class="menulevel2">            
                <span class="ui-icon ui-icon-trash" style="float:left;margin-left:15px;"></span>
                <span style="font-size:130%; font-family:Verdana">Cập nhật TT Thuốc</span>
            </li>
		</ul>
	</div>
	
	<div class="contextMenuPKDK" id="contextMenuPKDK"
		style="display: none; width: 250px;">
		<ul style="width: 250px; font-size: 65%;"> 
             <li id="updateMABSI" class="menulevel2">
                <span class="ui-icon ui-icon-pencil" style="float:left; margin-left:15px;"></span>
                <span style="font-size:130%; font-family:Verdana">Cập nhật thông tin</span>
            </li> 
		</ul>
	</div>
	
	<div id="popupId"></div>
</div>
<script>
	var opt = [];
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	//var lang= "vi";
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';

	initRest(uuid, "/vnpthis");

	var _opts = new Object();

	_opts._param = session_par;
	_opts._uuid = uuid;
	var DS = new dSCHList(_opts);
	DS.load(hospital_id);
</script>