/*
	MUC DICH: DANH SÁCH HẸN KHÁM BỆNH NHÂN, CHO PHÉP CHỌN LỰA BỆNH NHÂN NHẬP HẸN KHÁM, THU VIỆN PHÍ
	NGUOI TAO			NGAY TAO		NOI DUNG SUA DOI 
	SONDN				18/10/2017			TAO MOI; 
	
*/


function danhsachtkbenhnhanList(opt) {
	this.load = doLoad;
	

	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		_initControl();
		_bindEvent();
	}
	
	function _initControl(){
		ComboUtil.getComboTag("cboUSER_ID_SUA","NGTHK.BSKHAM",[],"",{extval: true, value:'', text:'<PERSON><PERSON><PERSON> s<PERSON> kh<PERSON>m'},'sql','', '');
	}
	
	function _bindEvent() {
		$('#btn_Close').on("click",function(e){			
			parent.DlgUtil.close("divDlgSuaHK");
		});
		
		$("#btn_Luu").on('click', function(){
			if(!_kiemTra()) return false;
			_luuData();
			parent.DlgUtil.close("dlgCDDV");
		});
	}
	
	function _luuData(){
		var param_select = opt.objxoa;
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K075.SUANHIEUHK", $('#txtNGAYHENSUA').val() +"$"+$('#cboUSER_ID_SUA').val() +"$"+param_select);
		
		if(fl == 1){
			var evFunc=EventUtil.getEvent("assignSevice_closeSua");
			if(typeof evFunc==='function') {
				evFunc({});
			}
			else {
				console.log('evFunc not a function');
			}
		}
		else{
			DlgUtil.showMsg("Xảy ra lỗi !");
		}
		
		
	}
	// kiểm tra control trước khi lưu thông tin
	function _kiemTra(){
		var    b_ngayhientai, b_ngaykham_daydu;
		b_ngayhientai = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');		
		if ($('#txtNGAYHENSUA').val().length >= 10){
			b_ngaykham_daydu = $('#txtNGAYHENSUA').val().substr(6,4) + $('#txtNGAYHENSUA').val().substr(3,2) + $('#txtNGAYHENSUA').val().substr(0,2);
		}
		
		if($('#txtNGAYHENSUA').val().trim().length == 0){
			$('#txtNGAYHENSUA').val("");
			setErrValidate('txtNGAYHENSUA');
			DlgUtil.showMsg('Hãy nhập ngày hẹn khám');
			return false;
		}	
		else if(!checkDate($('#txtNGAYHENSUA').val())){
			setErrValidate('txtNGAYHENSUA');
			DlgUtil.showMsg('Ngày hẹn khám khám không đúng');
			return false;		
		}
		else if(b_ngaykham_daydu < b_ngayhientai){
			setErrValidate('txtNGAYHENSUA');
			DlgUtil.showMsg('Thời gian hẹn khám không được nhỏ hơn thời gian hiện tại');
			return false;
		} 
		else if($('#cboUSER_ID_SUA').val() == null || $('#cboUSER_ID_SUA').val() == ""){
			setErrValidate('cboUSER_ID_SUA');
			DlgUtil.showMsg('Hãy nhập bác sĩ khám');
			return false;
		}
		return true;
	}

}









