function phieuchidinh_xetnghiem_ngoai(opt) {
	this.load = doLoad;
	var _opt = opt;
	var _loadFirstICD = false;
	var _loadFirstCTY = false;
	var _rpt_code_kyso = "RPT_PHIEUXETNGHIEM_NGOAI";
	function doLoad() {
		_initControl();
		_bindEvent();
	}
	function _initControl() {		
		$('#txtTHOIGIANCHIDINH').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY hh24:mi:ss'));
		$('#hidKHAMBENHID').val(_opt.khambenhid);
		$('#hidHOSOBENHANID').val(_opt.hosobenhanid);
		//$('#hidHOSOBENHANID').val(10316);
		$('#hidBENHNHANID').val(_opt.benhnhanid);
		init_cbo();	
		loadForm();
		var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (kyso_kydientu != "1") {
			$('#' + 'btnKySo').remove();
			$('#' + 'btnHuyKy').remove();
			$('#' + 'btnInKySo').remove();
		}
		
	}
	
	function loadForm(){
		var parr = [$('#hidHOSOBENHANID').val()];
		//var parr = [10316];
		data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT04.XNKHAC.LAYDS", parr.join('$'));							

		if (data != null && data != 'null' && data.length > 0) {
			$('#hidXN_KHAC_ID').val(data[0].XN_KHAC_ID);
			FormUtil.setObjectToForm("ngt_pxn_ngoai", "", data[0]);
			var option = $('<option value="' + data[0].BSDIEUTRIID + '">' + data[0].TENBACSI + '</option>');		
			$("#cboBSDIEUTRIID").empty();
			$("#cboBSDIEUTRIID").append(option);
			
			$("[name='radDCTC']").each(function(){
	        	$(this).prop('checked', $(this).val() == data[0].DCTC)}); 
	        	
        	$("[name='radMANGTHAI']").each(function(){
	        	$(this).prop('checked', $(this).val() == data[0].MANGTHAI)}); 
	          	
        	$("[name='radNOITIETTO']").each(function(){
	        	$(this).prop('checked', $(this).val() == data[0].NOITIETTO)});
	        	
        	$("[name='radVITRILAYBENH']").each(function(){
	        	$(this).prop('checked', $(this).val() == data[0].VITRILAYBENH)}); 
		

		} else {
			FormUtil.clearForm('ngt_pxn_ngoai');
		}		
	}
	
	 $('#btnCLEARBACSI').on("click", function () {
         $("#txtBSDIEUTRI").val("");
         var option = $('<option value="-1">--Lựa chọn--</option>');
         $("#cboBSDIEUTRIID").empty();
         $("#cboBSDIEUTRIID").append(option);         
     });

	function init_cbo() {
		 var _colBsNgoai = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên bác sỹ,OFFICER_NAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";
	        ComboUtil.initComboGrid("txtBSDIEUTRI", "CDDV.BSNGOAIPK", [], "600px", _colBsNgoai, function (event, ui) {
	            $("#txtBSDIEUTRI").val("");
	            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
	            $("#cboBSDIEUTRIID").empty();
	            $("#cboBSDIEUTRIID").append(option);
	            return false;
	        });
	        doLoadComboICD("txtMACHANDOANLAMSANG","txtCHANDOANLAMSANG"); 
	        doLoadComboICD("txtMACHANDOANSOITUCUNG","txtCHANDOANSOITUCUNG"); 
	        doLoadComboICD("txtMACHANDOANSOITUCUNG","txtCHANDOANSOITUCUNG");
	        doLoadComboICD("txtMACHANDOANGIAIPHAUBENH","txtCHANDOANGIAIPHAUBENH");	        
	        doLoadComboICD("txtMACHANDOAN_TBH_TUCUNG","txtCHANDOAN_TBH_TUCUNG");	
	}
	
	//ham load ma icd10
	function doLoadComboICD(_txt,_txtDst){
		var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		var _selfnc=function(event, ui) {
	        $("#"+_txt).val(ui.item.ICD10CODE);
	        $("#"+_txtDst).val(ui.item.ICD10NAME);
	        return false;
	    };
	    ComboUtil.initComboGrid(_txt,'CG.ICD10',[],"600px",_col,_selfnc);
	};	
	
	function _bindEvent() {

		$("#btnCapNhat").on("click", function(e) {
			var validate = new DataValidator("ngt_pxn_ngoai");
			if (validate.validateForm() && _validate()){
				_capNhat();
			}

		});

		$("#btnXoa")
				.on(
						"click",
						function() {
							if ($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="") {
								DlgUtil.showMsg("Bệnh nhân chưa có thông tin chỉ định xét nghiệm ngoài viện để xóa. ");
								return;
							}
							DlgUtil.showConfirm("Bạn có chắc chắn xóa chỉ định xét nghiệm ngoài viện không?", function(flag) {
								if (flag) {
									_delete_xn_guingoai();
								}
							});
						});

		$("#btnInPhieu").on("click", function(e) {
			if ($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="") {
				DlgUtil.showMsg("Bệnh nhân chưa có thông tin chỉ định xét nghiệm ngoài viện để in");
				return;
			}
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			},
			{
				name : 'xn_khac_id',
				type : 'String',
				value : $("#hidXN_KHAC_ID").val()
			}
			];
			openReport('window', "RPT_PHIEUXETNGHIEM_NGOAI", "pdf", par);
		});

		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close("dlgPhieuXetNghiemGuiNgoai");
		});

		$("#btnKySo").on("click", function(e) {
			_kySo();
		});
		$("#btnHuyKy").on("click", function(e) {
			_huyKySo();
		});
		$("#btnInKySo").on("click", function(e) {
			_inPhieuKySo();
		});

	}

	function _kySo() {
		if ($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="") {
			DlgUtil.showMsg("Bệnh nhân chưa có thông tin chỉ định xét nghiệm ngoài viện để ký số");
			return;
		}
		_caRpt('1');
	}

	function _huyKySo() {
		if ($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="") {
			DlgUtil.showMsg("Bệnh nhân chưa có thông tin chỉ định xét nghiệm ngoài viện để hủy ký số");
			return;
		}
        _caRpt('2');
	}

	function _inPhieuKySo() {
		if ($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="") {
			DlgUtil.showMsg("Bệnh nhân chưa có thông tin chỉ định xét nghiệm ngoài viện để in ký số");
			return;
		}

		var par_rpt_KySo = [ {
			name : 'hosobenhanid',
			type : 'String',
			value : $("#hidHOSOBENHANID").val()
		}
		];

		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});

		CommonUtil.openReportGetCA2(par_rpt_KySo,false);
	}

	function _caRpt(signType) {
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : $('#hidHOSOBENHANID').val()
		}];
		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});
		CommonUtil.kyCA(par_rpt_KySo, signType, true, true);
		EventUtil.setEvent("eventKyCA",function(e){
			var _code = e.res.split("|")[0];
			var _msg = e.res.split("|")[1];
			if(_code == '0') {
				
			}
			DlgUtil.showMsg(_msg);
		});
	}

	function _capNhat() {
		var objData = new Object();
		FormUtil.setFormToObject("ngt_pxn_ngoai", "", objData);
		objData.MODE = ($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="") ? "2" : "1";
		
		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04.XNKHAC.CAPNHAT", JSON.stringify(objData));
		  if ($.isNumeric(ret) && ret != -1) {
			if($('#hidXN_KHAC_ID').val() == "0" || $('#hidXN_KHAC_ID').val()=="")
			{
				DlgUtil.showMsg('Thêm mới chỉ định xét nghiệm ngoài viện thành công');
			}else{
				DlgUtil.showMsg('cập nhật chỉ định xét nghiệm ngoài viện thành công');
			}
			$('#hidXN_KHAC_ID').val(ret);
		} else {
			DlgUtil.showMsg('Lưu thông tin thất bại');
		}
	}

	function _delete_xn_guingoai() {
		var objData = new Object();
		FormUtil.setFormToObject("ngt_pxn_ngoai", "", objData);
		objData.MODE = "0"; // xoa dl;

		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04.XNKHAC.CAPNHAT", JSON
				.stringify(objData));
		if ($.isNumeric(ret) && ret != -1 ) {
			FormUtil.clearForm("ngt_pxn_ngoai","");

			$("[name='radDCTC']").each(function(){
				$(this).checked = false;
			});

			$("[name='radMANGTHAI']").each(function(){
				$(this).checked = false;
			});

			$("[name='radNOITIETTO']").each(function(){
				$(this).checked = false;
			});

			$("[name='radVITRILAYBENH']").each(function(){
				$(this).checked = false;
			});

			DlgUtil.showMsg('Xóa thông tin thành công');
			return true;
		} else {
			DlgUtil.showMsg('Xóa thông tin thất bại. ');
			return false;
		}

	}

	function _validate() {

		if ($('#txtTHOIGIANCHIDINH').val() == "") {
			setErrValidate('txtTHOIGIANCHIDINH');
			DlgUtil.showMsg('Hãy nhập thời gian chỉ định');
			return false;
		}

		if ($('#cboBSDIEUTRIID').val() == "") {
			setErrValidate('cboBSDIEUTRIID');
			DlgUtil.showMsg('Chưa nhập bác sĩ điều trị');
			return false;
		}

		if(parseFloat($('#txtCANNANG').val())<0){
			DlgUtil.showMsg('Cân nặng phải lớn hơn 0');
			onfocus('#txtCANNANG');
			check =  false;
		}


		if($('#txtCHIEUCAO').val()<0){
			DlgUtil.showMsg('Chiều cao phải lớn hơn 0');
			onfocus('#txtCHIEUCAO');
			check =  false;
		}

		if(parseFloat($('#txtNHIPTHO').val())<0){
			DlgUtil.showMsg('Nhịp thở phải lớn hơn 0');
			onfocus('#txtNHIPTHO');
			check =  false;
		}

		return true;
	}

}

