function dSCHList(_opts) {

    var _dichvukhambenhid = _opts._dichvukhambenhid;
    var _hosobenhanid = -1;
    var _maubenhphamid = -1;
    var _trangthaimaubenhpham = -1;
    this.load = doLoad;
    var isPtvShowAll = false;
    var option = '';
    var para = _opts._param;
    var user_id = para[1];
    var _isSave = -1;
    var _checkSoGPB = 0;
    var _labelVPC = 0;
    var _soGPB = false;
    var _mode = _opts._mode;

    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        _checkSoGPB = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA", 'CLS_CHECK_SO_GPB');
        
        loadComboGrid();
		loadCboMau();
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X017.01", _dichvukhambenhid);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            _hosobenhanid = row.HOSOBENHANID;
            _maubenhphamid = row.MAUBENHPHAMID;
            _trangthaimaubenhpham = row.TRANGTHAIMAUBENHPHAM;
            FormUtil.clearForm("inputForm", "");
            FormUtil.setObjectToForm("inputForm", "", row);

            if (row.TENVTST != null && row.TENVTST != '') {
                $("#cboMAVTST").empty();
                $("#cboMAVTST").append('<option value="' + row.MAVTST + '">' + row.TENVTST + '</option>');
            }
            if (row.TENGPB != null && row.TENGPB != '') {
                $("#cboMAGPB").empty();
                $("#cboMAGPB").append('<option value="' + row.MAGPB + '">' + row.TENGPB + '</option>');
            }

            if($("#txtPHANGAY").val()=="") 
            	$("#txtPHANGAY").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            if($("#txtTIEUBANXONGNGAY").val()=="") 
            	$("#txtTIEUBANXONGNGAY").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            if($("#txtNGAYNHAN").val()=="")
            	$("#txtNGAYNHAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            if($("#txtNGAYTRAKETQUA").val()=="") 
            	$("#txtNGAYTRAKETQUA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            
            if (row.BS_CLS == null || row.BS_CLS == '') {
                var sql_par = [];
                sql_par.push({
                    "name": "[0]",
                    value: user_id
                });
                var _row = jsonrpc.AjaxJson.getFirstRowO("CLS01X011.05", sql_par);
                option = $('<option value="' + _row[0].OFFICER_ID + '">' + _row[0].OFFICER_NAME + '</option>');                
            } else {
                option = $('<option value="' + row.BS_CLS + '">' + row.BS_CLS_FULLNAME + '</option>');
            }
            $("#cboBS_CLS").empty();
            $("#cboBS_CLS").append(option);

            if (row.NGUOIPHABENHPHAM == null || row.NGUOIPHABENHPHAM == '') {
                var sql_par = [];
                sql_par.push({
                    "name": "[0]",
                    value: user_id
                });
                var _row = jsonrpc.AjaxJson.getFirstRowO("CLS01X011.05", sql_par);
                option = $('<option value="' + _row[0].OFFICER_ID + '">' + _row[0].OFFICER_NAME + '</option>');                
            } else {
                option = $('<option value="' + row.BS_CLS + '">' + row.NGUOIPHABENHPHAM_FULLNAME + '</option>');
            }
            $("#cboNGUOIPHABENHPHAM").empty();
            $("#cboNGUOIPHABENHPHAM").append(option);

            if (row.NGUOILAMTIEUBAN == null || row.NGUOILAMTIEUBAN == '') {
                var sql_par = [];
                sql_par.push({
                    "name": "[0]",
                    value: user_id
                });
                var _row = jsonrpc.AjaxJson.getFirstRowO("CLS01X011.05", sql_par);
                option = $('<option value="' + _row[0].OFFICER_ID + '">' + _row[0].OFFICER_NAME + '</option>');                
            } else {
                option = $('<option value="' + row.BS_CLS + '">' + row.NGUOILAMTIEUBAN_FULLNAME + '</option>');
            }
            $("#cboNGUOILAMTIEUBAN").empty();
            $("#cboNGUOILAMTIEUBAN").append(option);

            ComboUtil.getComboTag("cboLOAITHUTHUAT", "DMC.CBPTTT.01", [], "", "", "sql", '', function () {
                $("#cboLOAITHUTHUAT").val(row.LOAITHUTHUAT);
            });
        }

        if (_trangthaimaubenhpham == 3 || _mode == '1') {
            $("#btnLuu").hide();
            $("#btnXoa").hide();
        } else {
            $("#btnLuu").show();
            $("#btnXoa").show();
        }

    };

    function _bindEvent() {
        $("#txtSOGPB").change(function () {
            if(_checkSoGPB == 1) {
                checkGPB();
                if(_soGPB) {
                    DlgUtil.showMsg("Số GPB đã được nhập trước đó, xin vui lòng kiểm tra lại!");
                }
            }
        });

        $("#btnLuu").click(function () {
            var validator = new DataValidator('inputForm');
            var valid = validator.validateForm();
            if (valid) {
                if(_checkSoGPB == 1) {
                    checkGPB();
                    if(_soGPB) {
                        DlgUtil.showMsg("Số GPB đã được nhập trước đó, xin vui lòng kiểm tra lại!");
                        return;
                    }
                }
                var objData = new Object();
                FormUtil.setFormToObject("inputForm", "", objData);
                var objDataKq = tinyMCE.get('txtKETQUA').getContent({format: 'html'});
                objData.KETQUA = objDataKq;
                objData.HOSOBENHANID = _hosobenhanid;
                objData.DICHVUKHAMBENHID = _dichvukhambenhid;
                objData.MAUBENHPHAMID = _maubenhphamid;
                var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X017.02", JSON.stringify(objData));
                if (fl == 1) {
                    _isSave = 1;
                    DlgUtil.showMsg("Lưu thành công !");
                    if ($("#chkPrint").prop("checked") == true) {
                    	 var par = [
    		            	 {name: 'dichvukhambenhid',type: 'String',value: _dichvukhambenhid},
    		                 {name:'id_maubenhpham',type:'String',value:_maubenhphamid}
                	 	];
                
		              //huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
		    			var INPHIEU_THEO_DV= jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
		    			if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){	
		    			    var _par_code = [_dichvukhambenhid];	
		     			    var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.RPTCODE.KQ_DVKB",_par_code.join('$'));		     			
		     			    if(i_report_code!=null && i_report_code.length>0){ 			 
		    	 			   for(var i=0;i<i_report_code.length;i++){
		    	  					var _report_code=i_report_code[i].REPORT_CODE;
		    	  					 var k = _report_code.split(';');  					
		    						  if(k.length > 1){													
		    								for(var j=0;j<k.length;j++){								
		    									openReport('window', k[j], 'pdf', par);				 
		    								}		   						
		    							} else {														
		    								   openReport('window', _report_code, 'pdf', par);							
		    							}
		    	 			   }
		     			    }else
		    			    	{
		    	 			    	openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
		    			    	}
		     			   return;	
		     			   
		    			}else{
		    				openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
		    			}         
                    }
                }
            }
        });

        $("#btnIn").click(function () {
            var par = [
		            	 {name: 'dichvukhambenhid',type: 'String',value: _dichvukhambenhid},
		                 {name:'id_maubenhpham',type:'String',value:_maubenhphamid}
            	 	];
            
          //huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
			var INPHIEU_THEO_DV= jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
			if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){	
			  var _par_code = [_dichvukhambenhid];	
 			  var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.RPTCODE.KQ_DVKB",_par_code.join('$')); 			  
 			   
 			    if(i_report_code!=null && i_report_code.length>0){ 			 
	 			   for(var i=0;i<i_report_code.length;i++){
	  					var _report_code=i_report_code[i].REPORT_CODE;
	  					 var k = _report_code.split(';');  					
						  if(k.length > 1){													
								for(var j=0;j<k.length;j++){								
									openReport('window', k[j], 'pdf', par);				 
								}		   						
							} else {														
								   openReport('window', _report_code, 'pdf', par);							
							}
	 			   }
 			    }else
			    	{
	 			    	openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
			    	}
 			   return;	
 			   
			}else{
				openReport('window', "PHIEU_KQ_GIAIPHAUBENH", "pdf", par);	
			}          
            
            
        });

        $("#btnXoa").click(function () {
            DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này không", function (flag) {
                if (flag) {
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X011.03", _dichvukhambenhid);
                    if (fl == 1) {
                        EventUtil.raiseEvent("assignSevice_saveKetQuaGPBOk", {
                            isSave: 1
                        });
                    } else {
                        DlgUtil.showMsg("Xảy ra lỗi !");
                    }
                }
            })
        });

        $("#btnDong").on("click", function () {
            EventUtil.raiseEvent("assignSevice_saveKetQuaGPBOk", {
                isSave: _isSave
            });
        });

        $('#btnCLEARBS_PHA').on("click", function () {
            var sql_par = [];
            sql_par.push({
                "name": "[0]",
                value: user_id
            });
            var _row = jsonrpc.AjaxJson.getFirstRowO("L002.FR1", sql_par);
            var option = $('<option value="' + user_id + '">' + _row[0].FULL_NAME + '</option>');
            $("#txtNGUOIPHABENHPHAMID").val("");
            $("#cboNGUOIPHABENHPHAM").empty();
            $("#cboNGUOIPHABENHPHAM").append(option);
        });

        $('#btnCLEARBS_CLS').on("click", function () {
            var sql_par = [];
            sql_par.push({
                "name": "[0]",
                value: user_id
            });
            var _row = jsonrpc.AjaxJson.getFirstRowO("L002.FR1", sql_par);
            var option = $('<option value="' + user_id + '">' + _row[0].FULL_NAME + '</option>');
            $("#txtBS_CLSID").val("");
            $("#cboBS_CLS").empty();
            $("#cboBS_CLS").append(option);
        });

        $('#btnCLEARBS_TB').on("click", function () {
            var sql_par = [];
            sql_par.push({
                "name": "[0]",
                value: user_id
            });
            var _row = jsonrpc.AjaxJson.getFirstRowO("L002.FR1", sql_par);
            var option = $('<option value="' + user_id + '">' + _row[0].FULL_NAME + '</option>');
            $("#txtNGUOILAMTIEUBANID").val("");
            $("#cboNGUOILAMTIEUBAN").empty();
            $("#cboNGUOILAMTIEUBAN").append(option);
        });

        $("#btnEditGPB").on("click", function () {
            $('#cboMAGPB').hide();
            $('#txtGPBEDIT').val($('#cboMAGPB option:selected').text());
            $('#txtGPBEDIT').show();
            $('#btnSaveEditGPB').show();
            $("#btnEditGPB").hide();
        });
        $("#btnSaveEditGPB").on("click", function () {
            if ($.trim($('#txtGPBEDIT').val()) != '') {
                var icd = $("#cboMAGPB").val();
                $("#cboMAGPB").empty();
                $("#cboMAGPB").append('<option value="' + icd + '">' + $('#txtGPBEDIT').val().trim() + '</option>');
                $('#txtGPBEDIT').hide();
                $('#btnSaveEditGPB').hide();
                $('#cboMAGPB').show();
                $("#btnEditGPB").show();
            } else {
                $('#txtGPBEDIT').focus();
                return;
            }
        });

        $("#btnEditVTST").on("click", function () {
            $('#cboMAVTST').hide();
            $('#txtVTSTEDIT').val($('#cboMAVTST option:selected').text());
            $('#txtVTSTEDIT').show();
            $('#btnSaveEditVTST').show();
            $("#btnEditVTST").hide();
        });
        $("#btnSaveEditVTST").on("click", function () {
            if ($.trim($('#txtVTSTEDIT').val()) != '') {
                var icd = $("#cboMAVTST").val();
                $("#cboMAVTST").empty();
                $("#cboMAVTST").append('<option value="' + icd + '">' + $('#txtVTSTEDIT').val().trim() + '</option>');
                $('#txtVTSTEDIT').hide();
                $('#btnSaveEditVTST').hide();
                $('#cboMAVTST').show();
                $("#btnEditVTST").show();
            } else {
                $('#txtVTSTEDIT').focus();
                return;
            }
        });
		//nguyennghia L2PT-31886 
		$("#btnLuuMau").on("click",function(e){
			//var _validator=new DataValidator("divMain");
			//var valid= _validator.validateForm();
			//if(!valid){
				//return false;
			//}
			if($("#txtTENMAU").val().trim() == ""){
				//$.toaster({ title : $.i18n("canh_bao"), priority : 'warning', message : "Tên mẫu không được để trống", settings : { timeout: 3000}});
				DlgUtil.showMsg("Tên mẫu không được để trống");
				$('#txtTENMAU').focus();
				return false;
			}
			/*
			if($("#txtVTST").val().trim() == ""){
				DlgUtil.showMsg("Bạn chưa nhập Chẩn đoán",function(){
					$('#txtVTST').focus();
				});
				return false;
			}
			if($("#txtKETLUAN").val().trim() == ""){
				DlgUtil.showMsg("Bạn chưa nhập NX vi thể",function(){
					$('#txtKETLUAN').focus();
				});
				return false;
			}
			if($("#txtBANLUAN").val().trim() == ""){
				DlgUtil.showMsg("Bạn chưa nhập khuyến nghị",function(){
					$('#txtBANLUAN').focus();
				});
				return false;
			}*/
			
			var objData = new Object();
			FormUtil.setFormToObject("","",objData);
		
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X011.LUUMAU",JSON.stringify(objData));
			if(ret==1){
				DlgUtil.showMsg("Lưu mẫu thành công");
				loadCboMau();
			}else if(ret==2){
				DlgUtil.showMsg("Tên mẫu đã tồn tại!");
			}
			else{
				DlgUtil.showMsg("Lưu mẫu không thành công");
			}
		});
		$("#cboMAU").on("change",function(e){
			var tmp_id = $("#cboMAU").val();
			if(tmp_id>0){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X011.CHONMAU",tmp_id);
				if (data_ar != null && data_ar.length > 0) {
					var row=data_ar[0];
					//FormUtil.setObjectToForm("","",row);
					$("#txtKETLUAN").val(row.KETLUAN);
					$("#txtBANLUAN").val(row.BANLUAN);
					$("#txtVTST").val(row.VTST);
					
				}
			}
		});
		$("#btnXoaMau").on("click",function(e){
			var tmp_id_xoa = $("#cboMAU").val();
			var ten_mau_xoa = $( "#cboMAU option:selected" ).text();
			if(tmp_id_xoa == "-1"){
				DlgUtil.showMsg("Chưa chọn mẫu để xóa");
				$('#cboMAU').focus();
				return false;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa mẫu " +ten_mau_xoa+" ko?",function(flag) {
				if(flag){
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X011.XOAMAU",tmp_id_xoa);
					if (result==1) {
						DlgUtil.showMsg("Xóa mẫu " +ten_mau_xoa+" thành công");
						loadCboMau();
					}else
						DlgUtil.showMsg("Xóa mẫu " +ten_mau_xoa+" không thành công");
				}
			});
		});
		//nguyennghia end L2PT-31886 
    }

    function  checkGPB() {
        var sql_par = [];
        sql_par.push({"name":"[0]","value": _dichvukhambenhid});
        sql_par.push({"name":"[1]","value": $("#txtSOGPB").val()});
        var _check = jsonrpc.AjaxJson.getOneValue('CLS01X011.CHECK.GPB', sql_par);
        if(_check == 1) {
            _soGPB = true;
        } else {
            _soGPB = false;
        }
    }
	
	function loadCboMau(){
		ComboUtil.getComboTag("cboMAU","CLS01X011.DSMAU","","",{value:'-1',text:'---Chọn mẫu---'},'sp');
		//ComboUtil.getComboTag("cboLOAITHUTHUAT", "DMC.CBPTTT.01", [], "", "", "sql", '', "");
	}

    function loadComboGrid() {
        var _colICD = 'Mã GPB,MAGPB,20,0,f,l;Mẫu KQ GPB,TENMAU,30,0,f,l;Tên GPB,TENGPB,50,0,f,l';
        ComboUtil.initComboGrid('txtMA_GPB', 'CLS01X011.06', [], '600px', _colICD, function (event, ui) {
            var _ui = ui.item;
            $("#cboMAGPB").empty();
            $("#cboMAGPB").append('<option value="' + _ui.MAGPB + '">' + _ui.TENGPB + '</option>');
            $('#txtKETLUAN').val(_ui.KETLUAN);
            tinyMCE.activeEditor.setContent(_ui.GIATRI, {format: 'html'});
            return false;
        });

        var _colICD = 'Mã VTST,MASINHTHIET,30,0,f,l;Tên VTST,TENSINHTHIET,70,0,f,l';
        ComboUtil.initComboGrid('txtMA_VTST', 'DMC129.07', [], '600px', _colICD, function (event, ui) {
            var _ui = ui.item;
            $("#cboMAVTST").empty();
            $("#cboMAVTST").append('<option value="' + _ui.MASINHTHIET + '">' + _ui.TENSINHTHIET + '</option>');
            return false;
        });

        var _colICD = 'Nội dung,NOIDUNG,30,0,f,l;Ghi chú,GHICHU,70,0,f,l';
        ComboUtil.initComboGrid('txtPHUONGPHAPNHUOM', 'CLS01X011.07', [], '1000px', _colICD, function (event, ui) {
            var _ui = ui.item;
            $('#txtPHUONGPHAPNHUOM').val(_ui.NOIDUNG);
            return false;
        });

        var _show = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'PTTT_PTV_SHOWALL');
        if (_show == '1') {
            isPtvShowAll = true;
        }
        var sql_par = [];
        if (isPtvShowAll) {
            sql_par.push({"name": "[0]", "value": 0}, {"name": "[1]", "value": _opts.dept_id});
        } else {
            sql_par.push({"name": "[0]", "value": 1}, {"name": "[1]", "value": _opts.dept_id});
        }
        var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
        ComboUtil.initComboGrid("txtBS_CLSID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBS_CLSID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboBS_CLS").empty();
            $("#cboBS_CLS").append(option);
            return false;
        });
        
        ComboUtil.initComboGrid("txtNGUOIPHABENHPHAMID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtNGUOIPHABENHPHAMID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboNGUOIPHABENHPHAM").empty();
            $("#cboNGUOIPHABENHPHAM").append(option);
            return false;
        });
        
        ComboUtil.initComboGrid("txtNGUOILAMTIEUBANID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtNGUOILAMTIEUBANID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboNGUOILAMTIEUBAN").empty();
            $("#cboNGUOILAMTIEUBAN").append(option);
            return false;
        });
    }
}
