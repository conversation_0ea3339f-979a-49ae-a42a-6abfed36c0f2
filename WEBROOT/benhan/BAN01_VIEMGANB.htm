<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jquery/jquery.confirm.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
			<!-- <link rel="stylesheet" href="../noitru/custom_nt.css"> -->
<link rel="stylesheet" href="../common/css/css_style.css">
<link rel="stylesheet" href="../common/css/custom_ban.css"></link>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../benhan/BAN_Common.js?v=08072019"></script>
<script type="text/javascript" src="../noitru/NTU02D023_Thongtinhanhchinh.js"></script> 
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>

<script type="text/javascript" src="../benhan/BAN01_VIEMGANB.js?v=018"></script>


<div width="100%" id="divMain-BA" style="border: 0px;">
	<ul id="tabs" class="nav nav-tabs" style="margin-top: 10px;" data-tabs="tabs">
		<li class="active"><a href="#tabBenhAnHoiKham" data-toggle="tab">A.Bệnh án hỏi khám bệnh</a></li>
		<li><a href="#tabBenhAnTongKet" data-toggle="tab">B.Tổng kết hồ sơ bệnh án</a></li>
		<li id="liBenhNhanThongTin"><a href="#tabBenhNhanThongTin" data-toggle="tab">C.Thông tin bệnh nhân</a></li>
	</ul>

	<div id="my-tab-content" class="tab-content">
		<div><a href="#" class="scrollToTop before"></a></div>   
		<div id="tabBenhAnHoiKham" class="tab-pane active">  
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">A. KHÁM BỆNH HỎI BỆNH</div>  
			</div> 
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">I. Lý do vào viện:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-2 low-padding form-group required" >
							<label class='l-col-3'>Lý do vào viện:</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtLYDOVAOVIEN"
								maxlength="500" name="txtLYDOVAOVIEN" title=""  valrule="Lý do vào viện,trim_required|max_length[500]"
								style="width: 100%;">
						</div>
						
					</div>
				</div>
			</div>
		
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">II. Hỏi bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Quá trình bệnh lý </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLY" valrule="Quá trình bệnh lý,max_length[500]"
								class="form-control input-sm i-col-3" id="txtQTBENHLY"
								style="height: 80px !important; width: 100%" maxlength="500"
								rows="4"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>2. Tiền sử bệnh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Bản thân: </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<input valrule="Bản thân,max_length[500]" style=" height: 70px !important; width: 100%;"
								class="form-control input-sm" id="txtBANTHAN"
								maxlength="500" rows="3" name="txtBANTHAN" title="">		
						</div>
					</div>
					
							
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Gia đình: </label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<input valrule="Gia đình,max_length[500]" style="width: 100%; height: 70px !important;"
								class="form-control input-sm" id="txtGIADINH"
								maxlength="500" rows="3" name="txtGIADINH" title="">
						</div>							
					</div>						
				</div>
			</div><!-- END FORM INLINE -->
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">			
				<div class="panel-heading">III. Khám bệnh:</div>
				<div class="panel-body"> 
					<div class=" form-inline">
						<div class="col-xs-9 low-padding">
							<div class="col-xs-12 low-padding">
								<label class='l-col-3 control-label'>1. Toàn thân:</label>
							</div>
							<div class="col-xs-12 low-padding">
								<textarea name="txtTOANTHAN" valrule="Toàn thân,max_length[500]"
									class="form-control input-sm i-col-3" id="txtTOANTHAN"
									style="height: 90px !important; width: 100%" maxlength="500"
									rows="3"></textarea>
							</div>
							<div class="col-xs-12 low-padding">
								<label class='l-col-3 control-label'>2. Các bộ phận:</label>
							</div>
							<div class="col-xs-12 low-padding">
								<textarea name="txtCACBOPHAN" valrule="Các bộ phận,max_length[500]"
									class="form-control input-sm i-col-3" id="txtCACBOPHAN"
									style="height: 70px !important; width: 100%" maxlength="500"
									rows="3"></textarea> 
							</div>
						</div>
					
						<div class="col-xs-3 low-padding">
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class="mgl10">Mạch</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input class="col-xs-4 form-control input-sm " style="width: 100%;" id="txtMACH" name="txtMACH" valrule="Mạch,is_natural|max_length[3]" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhiệt độ:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhiệt độ,numeric|max_length[6]" maxlength="6"
												class="form-control input-sm " style="width: 100%;"
												id="txtNHIETDO" name="txtNHIETDO" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>C</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Huyết áp:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Huyết áp,is_natural|max_length[3]"
												class="form-control input-sm " style="width: 45%;"
												id="txtHUYETAP1" name="txtHUYETAP1" title=""> /
									 <input class="form-control input-sm " style="width: 45%; float: right;"
												valrule="Huyết áp,is_natural|max_length[3]"
												id="txtHUYETAP2" name="txtHUYETAP2" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>mmHg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhịp thở:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhịp thở,is_natural|max_length[3]"
									class="form-control input-sm " style="width: 100%;"
									id="txtNHIPTHO" name="txtNHIPTHO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Cân nặng:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Cân nặng,numeric|max_length[6]" maxlength="6"
									class="form-control input-sm " style="width: 100%;"
									id="txtCANNANG" name="txtCANNANG" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>kg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chiều cao:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Chiều cao,numeric|max_length[4]"
										   class="form-control input-sm " style="width: 100%;" maxlength="4"
										   id="txtCHIEUCAO" name="txtCHIEUCAO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>cm</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chỉ số BMI:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<label id="txtBMI" name="txtBMI" title=""></label>
								</div>
							</div>
						</div>
					</div>  
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Tóm tắt kết quả cận lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-10 low-padding">
							<textarea name="txtTOMTATKQCLS" valrule="Tóm tắt kết quả cận lâm sàng,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTOMTATKQCLS"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
						<div class="col-xs-2 low-padding" style="text-align: left;">
							<button id="btnKQCLS" class="btn btn-sm btn-primary" style="width: 20% !important;">...</button>
						</div>
					</div>	
					<div class="form-inline">
						<div class="col-xs-2 low-padding">
							<label class='1-col-3'>4. Chẩn đoán ban đầu:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMACDBANDAU" name="txtMACDBANDAU" valrule="Mã chẩn đoán ban đầu,max_length[500]" title="">
						</div>
						<div class="col-xs-8 low-padding" id="divBc">
							<input class="form-control input-sm" style="width: 100%;" id="txtCDBANDAU" name="txtCDBANDAU" valrule="Chẩn đoán ban đầu,max_length[500]" title="">
						</div>					
					</div>				
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Đã xử lý (thuốc, chăm sóc):</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtDXLTUYENDUOI" valrule="Đã xử lý (thuốc chăm sóc),max_length[500]"
								class="form-control input-sm i-col-3" id="txtDXLTUYENDUOI"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
				
					<div class="form-inline">
						<div class="col-xs-2 low-padding">
							<label class='l-col-3'>6. Chẩn đoán khi ra viện: </label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm mgt5" style="width: 100%;" id="txtMACHANDOANRAVIEN" name="txtMACHANDOANRAVIEN" valrule="Mã chẩn đoán ra viện,max_length[500]" title="">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm mgt5" style="width: 100%;" id="txtCHANDOANRAVIEN" name="txtCHANDOANRAVIEN" valrule="Chẩn đoán ra viện,max_length[500]" title="">
						</div>
					</div>
				
					<div class=" form-inline mgb35" style="margin-bottom: 50px !important;">
						<div class="col-xs-2 low-padding  form-inline mgt10">
							<label class='control-label'>7. Điều trị ngoại trú:</label>
						</div>
						<div class="col-xs-2 low-padding  form-inline mgt5">
							<div class="col-xs-4 low-padding">
								<label class=' control-label'>Từ ngày</label>
							</div>
							<div class="col-xs-8 low-padding">
								<div class="input-group">
									<input class="form-control input-sm" valrule="Từ ngày,date"
										style="float: right; z-index: 0!important;" id="cldDTNTTUNGAY"
										name="cldDTNTTUNGAY" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" 
										onclick="NewCssCal('cldDTNTTUNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</div>
						<div class="col-xs-2 low-padding  form-inline mgt5">
							<div class="col-xs-4 low-padding">
								<label class=' control-label mgl10'>Đến</label>
							</div>
							<div class="col-xs-8 low-padding">
								<div class="input-group ">
									<input class="form-control input-sm"  valrule="Đến ngày,date"
										style="float: right; z-index: 0!important;" id="cldDTNTDENNGAY"
										name="cldDTNTDENNGAY" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('cldDTNTDENNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</div>
						<div class="col-xs-3 low-padding  form-inline"></div>
					</div>
				</div>
			</div>
		 						
		
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;"> 
                    <div class="form-inline">  
                        <div class="col-xs-2 low-padding">
                            <label>Giám đốc</label>  
                        </div>  
                        <div class="col-xs-2 low-padding">
                            <input class="form-control input-sm" id="txtGIAMDOC_TK" name="txtGIAMDOC_TK" maxlength="500" style="width: 100%;" placeholder="nhập để tìm kiếm">
                        </div> 
                        <div class="col-xs-4 low-padding"> 
							<input class="form-control input-sm" id="txtGIAMDOC" name="txtGIAMDOC" maxlength="500" style="width: 100%;">
                        </div>  
                    </div> 
                    <div class="form-inline">  
                        <div class="col-xs-2 low-padding">
                            <label>Bác sỹ khám</label>  
                        </div>  
                        <div class="col-xs-2 low-padding">
                            <input class="form-control input-sm" id="txtBSKHAM_TK" name="txtBSKHAM_TK" maxlength="500" style="width: 100%;" placeholder="nhập để tìm kiếm">
                        </div> 
                        <div class="col-xs-4 low-padding"> 
							<input class="form-control input-sm" id="txtBSKHAM" name="txtBSKHAM" maxlength="500" style="width: 100%;">
                        </div>  
                    </div> 
			</div> 
		
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">B. TIỀN SỬ BẢN THÂN</div>
				<div class="panel-body"> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label>1. Các yếu tố nguy cơ:</label>
						</div>
                    </div> 
					
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Mẹ HBsAg (+)</label>
						</div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkME_HBSAG1" name="chkME_HBSAG1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkME_HBSAG2" name="chkME_HBSAG2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkME_HBSAG3" name="chkME_HBSAG3">
                                <label>Không biết</label>
                            </div> 
                        </div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Vợ, chồng, bạn tình HBsAg (+)</label>
						</div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkVC_HBSAG1" name="chkVC_HBSAG1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkVC_HBSAG2" name="chkVC_HBSAG2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkVC_HBSAG3" name="chkVC_HBSAG3">
                                <label>Không biết</label>
                            </div> 
                        </div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Truyền máu</label>
						</div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTRUYENMAU1" name="chkTRUYENMAU1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTRUYENMAU2" name="chkTRUYENMAU2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTRUYENMAU3" name="chkTRUYENMAU3">
                                <label>Không biết</label>
                            </div> 
                        </div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Tổn thương do kim châm</label>
						</div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkKIMCHAM1" name="chkKIMCHAM1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkKIMCHAM2" name="chkKIMCHAM2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkKIMCHAM3" name="chkKIMCHAM3">
                                <label>Không biết</label>
                            </div> 
                        </div>
                    </div> <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Tiêm chích ma túy</label>
						</div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTIEMCHICH1" name="chkTIEMCHICH1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTIEMCHICH2" name="chkTIEMCHICH2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTIEMCHICH3" name="chkTIEMCHICH3">
                                <label>Không biết</label>
                            </div> 
                        </div>
                    </div> 
					
					
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label>2. Đồng nhiễm virut viêm gan khác:</label>
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkDONGNHIEM1" name="chkDONGNHIEM1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkDONGNHIEM2" name="chkDONGNHIEM2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-1 low-padding"> 
                                <label>Cụ thể</label>
                            </div> 
                            <div class="col-xs-5 low-padding">
								<input class="form-control input-sm" id="txtDONGNHIEM" name="txtDONGNHIEM" style="width: 100%;">  
                            </div> 
                        </div>
                    </div> 
                    <div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label>3. Hoàn cảnh phát hiện bệnh:</label>
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding"> 
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding"> 
                                <label>Có triệu chứng</label>
                            </div> 
                            <div class="col-xs-8 low-padding">
								<input class="form-control input-sm" id="txtTRIEUCHUNG" name="txtTRIEUCHUNG" style="width: 100%;"> 
                            </div>  
                        </div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">  
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkHOANCANH1" name="chkHOANCANH1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkHOANCANH2" name="chkHOANCANH2">
                                <label>Có</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkHOANCANH3" name="chkHOANCANH3">
                                <label>KSKĐK</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkHOANCANH4" name="chkHOANCANH4">
                                <label>Tình cờ</label>
                            </div>
                        </div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label>4. Thời điểm phát hiện bệnh:</label>
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">  
                            <div class="col-xs-2 low-padding">
							 </div>
                            <div class="col-xs-2 low-padding"> 
                                <label>HBsAg (+)</label>
                            </div> 
                            <div class="col-xs-2 low-padding"> 
								<div class="input-group">
									<input class="form-control input-sm"  valrule="ngày,date"
										style="float: right; z-index: 0!important;" id="txtNGAY_HBSAG"
										name="txtNGAY_HBSAG" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('txtNGAY_HBSAG','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div> 
                            </div>
                            <div class="col-xs-2 low-padding"> 
                                <label>VGVRB mạn</label>
                            </div>
                            <div class="col-xs-2 low-padding">
								<div class="input-group">
									<input class="form-control input-sm"  valrule="ngày,date"
										style="float: right; z-index: 0!important;" id="txtNGAY_VGVRB"
										name="txtNGAY_VGVRB" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('txtNGAY_VGVRB','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>  
                            </div>
                        </div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label>5. Tiền sử sinh đẻ:</label> 
								<input class="form-control input-sm" id="txtSOCON" name="txtSOCON" maxlength="3" style="width: 50px;"> 
                                <label>con</label> 
						</div> 
                    </div> 
					
					<div class="form-inline"> 
                            <div class="col-xs-2 low-padding">
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTIEMPHONG1" name="chkTIEMPHONG1">
                                <label>Con đã tiêm phòng</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkTIEMPHONG2" name="chkTIEMPHONG2">
                                <label>Con chưa tiêm phòng</label>
                            </div> 
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-3 low-padding">
							<label>6. Bản thân đã tiêm phòng</label>
						</div>
                    </div> 
					<div class="form-inline"> 
                            <div class="col-xs-2 low-padding">
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkDATIEMPHONG1" name="chkDATIEMPHONG1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkDATIEMPHONG2" name="chkDATIEMPHONG2">
                                <label>Có</label>
                            </div> 
                            <div class="col-xs-1 low-padding"> 
                                <label>Năm</label>
                            </div> 
                            <div class="col-xs-2 low-padding"> 
                                <input class="form-control input-sm" id="txtNAMTIEM" name="txtNAMTIEM" maxlength="4" style="width: 100px;">
                            </div> 
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Làm xét nghiệm trước tiêm phòng</label>
						</div>
                    </div> 
					<div class="form-inline"> 
                            <div class="col-xs-2 low-padding">
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkXNTRUOCTIEM1" name="chkXNTRUOCTIEM1">
                                <label>Không</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkXNTRUOCTIEM2" name="chkXNTRUOCTIEM2">
                                <label>Có</label>
                            </div> 
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-12 low-padding">
							<label> - Nếu có: HBsAg</label>
						</div>
                    </div> 
					<div class="form-inline"> 
                            <div class="col-xs-2 low-padding">
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkDUONGTINH" name="chkDUONGTINH">
                                <label>Dương tính</label>
                            </div>
                            <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkAMTINH" name="chkAMTINH">
                                <label>Âm tính</label>
                            </div> 
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-2 low-padding">
							<label>7. Uống rượu</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkRUOUKHONG" name="chkRUOUKHONG">
                                <label>Không</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkRUOUCO" name="chkRUOUCO">
                                <label>Có</label>
						</div> 
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-2 low-padding"> 
						</div>
                        <div class="col-xs-2 low-padding">
							<label>Thời gian uống</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
							<input class="form-control input-sm" id="txtRUOU_TG" name="txtRUOU_TG" style="width: 100%;">
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-2 low-padding"> 
						</div>
                        <div class="col-xs-2 low-padding">
							<label>Năm</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
							<input class="form-control input-sm" id="txtRUOU_NAM" name="txtRUOU_NAM" style="width: 100%;">
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-2 low-padding"> 
						</div> 
                        <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkRUOUHANGNGAY" name="chkRUOUHANGNGAY">
                                <label>Uống hàng ngày</label>
						</div>
                        <div class="col-xs-2 low-padding">
                                <input class="input-sm" type="checkbox" id="chkRUOUTHINHTHOANG" name="chkRUOUTHINHTHOANG">
                                <label>Thỉnh thoảng</label>
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-2 low-padding"> 
						</div>
                        <div class="col-xs-2 low-padding">
							<label>Số lượng/Ngày</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
							<input class="form-control input-sm" id="txtRUOU_SL" name="txtRUOU_SL" style="width: 100%;">
						</div>
                    </div> 
					
					<div class="form-inline">
                        <div class="col-xs-3 low-padding">
							<label>8. Bệnh khác</label>
						</div>
                    </div> 
					
					<div class="form-inline"> 
                            <div class="col-xs-2 low-padding">
                            </div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkBENHKHAC1" name="chkBENHKHAC1">
                                <label>Không</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkBENHKHAC2" name="chkBENHKHAC2">
                                <label>Có</label>
						</div> 
                        <div class="col-xs-8 low-padding"> 
							<input class="form-control input-sm" id="txtBENHKHAC" name="txtBENHKHAC" style="width: 100%;">
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-3 low-padding">
							<label>9. Đã điều trị VGVRB mạn</label>
						</div>
                    </div> 
					<div class="form-inline">
                            <div class="col-xs-2 low-padding">
                            </div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkVGVRB1" name="chkVGVRB1">
                                <label>Không</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkVGVRB2" name="chkVGVRB2">
                                <label>Có</label>
						</div>  
                    </div> 
					<div class="form-inline"> 
                        <div class="col-xs-2 low-padding">
							<label> - Tên thuốc</label>
						</div>
                        <div class="col-xs-9 low-padding"> 
							<input class="form-control input-sm" id="txtVGVRB_THUOC1" name="txtVGVRB_THUOC1" style="width: 100%;">
						</div>
                    </div> 
					<div class="form-inline"> 
                        <div class="col-xs-2 low-padding">
							<label> - Bắt đầu điều trị từ ngày</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
								<div class="input-group" style="width: 140px;">
									<input class="form-control input-sm"  valrule="ngày,date"
										style="float: right; z-index: 0!important;" id="txtNGAY_DTTU1"
										name="txtNGAY_DTTU1" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('txtNGAY_DTTU1','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>   
						</div>
                        <div class="col-xs-1 low-padding" style="width: 40px;">
							<label>đến</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
								<div class="input-group" style="width: 140px;">
									<input class="form-control input-sm"  valrule="ngày,date"
										style="float: right; z-index: 0!important;" id="txtNGAY_DTDEN1"
										name="txtNGAY_DTDEN1" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('txtNGAY_DTDEN1','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div> 
						</div>
                    </div> 
					<div class="form-inline"> 
                        <div class="col-xs-2 low-padding">
							<label> - Tên thuốc</label>
						</div>
                        <div class="col-xs-9 low-padding"> 
							<input class="form-control input-sm" id="txtVGVRB_THUOC2" name="txtVGVRB_THUOC2" style="width: 100%;">
						</div>
                    </div> 
					<div class="form-inline"> 
                        <div class="col-xs-2 low-padding">
							<label> - Bắt đầu điều trị từ ngày</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
								<div class="input-group" style="width: 140px;">
									<input class="form-control input-sm"  valrule="ngày,date"
										style="float: right; z-index: 0!important;" id="txtNGAY_DTTU2"
										name="txtNGAY_DTTU2" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('txtNGAY_DTTU2','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>  
						</div>
                        <div class="col-xs-1 low-padding"  style="width: 40px;">
							<label>đến</label>
						</div>
                        <div class="col-xs-2 low-padding"> 
								<div class="input-group" style="width: 140px;">
									<input class="form-control input-sm"  valrule="ngày,date"
										style="float: right; z-index: 0!important;" id="txtNGAY_DTDEN2"
										name="txtNGAY_DTDEN2" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span 
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal" valrule="Đến ngày,date"
										onclick="NewCssCal('txtNGAY_DTDEN2','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>  
						</div>
                    </div>  
				</div>
			</div> 
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">C. TIỀN SỬ GIA ĐÌNH</div>
				<div class="panel-body">
					<div class="form-inline">
                        <div class="col-xs-3 low-padding">
							<label>1. Có người bị viêm gan</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkGDVIEMGAN1" name="chkGDVIEMGAN1">
                                <label>Không</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkGDVIEMGAN2" name="chkGDVIEMGAN2">
                                <label>Có</label>
						</div> 
                        <div class="col-xs-4 low-padding"> 
							<input class="form-control input-sm" id="txtGDVIEMGAN" name="txtGDVIEMGAN" style="width: 100%;">
						</div>
                    </div> 
					<div class="form-inline">
                        <div class="col-xs-3 low-padding">
							<label>2. Có người bị K gan</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkGDKGAN1" name="chkGDKGAN1">
                                <label>Không</label>
						</div>
                        <div class="col-xs-1 low-padding">
                                <input class="input-sm" type="checkbox" id="chkGDKGAN2" name="chkGDKGAN2">
                                <label>Có</label>
						</div> 
                        <div class="col-xs-4 low-padding"> 
							<input class="form-control input-sm" id="txtGDKGAN" name="txtGDKGAN" style="width: 100%;">
						</div>
                    </div>   
				</div> 
			</div> 
			
			<div class="form-inline panel panel-info" style="margin-bottom: 50px;">
				<div class="panel-heading">&nbsp;&nbsp;&nbsp;</div>
				<div class="panel-body"> 
				</div>
			</div> 
		</div>
		
		
		<div id="tabBenhAnTongKet" class="tab-pane">
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">TỔNG KẾT BỆNH ÁN:</div>
				<div class="panel-body"> 
					<div class=" form-inline" style="display: none;">
						<div class="col-xs-12 low-padding">
							<label>1. Lý do vào viện</label>
						</div>
					</div>
					<div class="form-inline" style="display: none;">
						<div class="col-xs-12 low-padding">
							<textarea id="txtTONGKET1" name="txtTONGKET1" class="form-control input-sm i-col-3" style="width: 100%" maxlength="500" rows="2"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label>1. Quá trình bệnh lý và diễn biến lâm sàng</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea id="txtTONGKET2" name="txtTONGKET2" class="form-control input-sm i-col-3" style="width: 100%" rows="4"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label>2. Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea id="txtTONGKET3" name="txtTONGKET3" class="form-control input-sm i-col-3" style="width: 100%" rows="4"></textarea>
						</div>
					</div>  
					
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label>3. Chẩn đoán ra viện</label>
						</div>
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>- Bệnh chính:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHCHINH" name="txtMABENHCHINH" valrule="Mã bệnh chính,max_length[500]" title="" attrIcd="0" targetCtr="txtBENHCHINH">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHCHINH" name="txtBENHCHINH" valrule="Tên bệnh chính,max_length[500]" title="">
						</div>
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>- Bệnh kèm theo:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHKEMTHEO" name="txtMABENHKEMTHEO" valrule="Mã bệnh kèm theo,max_length[500]" title="" attrIcd="1" targetCtr="txtBENHKEMTHEO">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHKEMTHEO" name="txtBENHKEMTHEO" valrule="Tên bệnh kèm theo,max_length[500]" title="">
						</div>
					</div>
					
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label>4. Phương pháp điều trị</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea id="txtTONGKET4" name="txtTONGKET4" class="form-control input-sm i-col-3" style="width: 100%" rows="4"></textarea>
						</div>
					</div> 
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label>5. Tình trạng người bệnh ra viện</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea id="txtTONGKET5" name="txtTONGKET5" class="form-control input-sm i-col-3" style="width: 100%" rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label>6. Hướng điều trị và các chế độ tiếp theo</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea id="txtTONGKET7" name="txtTONGKET7" class="form-control input-sm i-col-3" style="width: 100%" rows="3"></textarea>
						</div>
					</div>
					 
					<div class=" form-inline"> 
						<div class="col-xs-11 low-padding" style="width: 100%;">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th colspan="2" style="font-size: x-small; text-align: left;">Hồ sơ, phim, ảnh</th>
									</tr>
								</thead>
								<tr>
								<td style="width: 35%;">
								
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>Loại</label>  
                        </div>  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>Số tờ</label>  
                        </div>  
                    </div> 
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>X - quang</label>  
                        </div>  
                        <div class="col-xs-6 low-padding">
                            <input class="form-control input-sm" id="txtSOXQUANG" name="txtSOXQUANG" maxlength="5" style="width: 100%;">
                        </div>  
                    </div> 
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>CT - Scanner</label>  
                        </div>  
                        <div class="col-xs-6 low-padding">
                            <input class="form-control input-sm" id="txtSOSCANNER" name="txtSOSCANNER" maxlength="5" style="width: 100%;">
                        </div>  
                    </div>  
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>Siêu âm</label>  
                        </div>  
                        <div class="col-xs-6 low-padding">
                            <input class="form-control input-sm" id="txtSOSIEUAM" name="txtSOSIEUAM" maxlength="5" style="width: 100%;">
                        </div>  
                    </div> 
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>Xét nghiệm</label>  
                        </div>  
                        <div class="col-xs-6 low-padding">
                            <input class="form-control input-sm" id="txtSOXETNGHIEM" name="txtSOXETNGHIEM" maxlength="5" style="width: 100%;">
                        </div>  
                    </div> 
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>Khác</label>  
                        </div>  
                        <div class="col-xs-6 low-padding">
                            <input class="form-control input-sm" id="txtSOKHAC" name="txtSOKHAC" maxlength="5" style="width: 100%;">
                        </div>  
                    </div> 
                    <div class="form-inline">  
                        <div class="col-xs-6 low-padding" style="text-align: center;">
                            <label>Toàn bộ hồ sơ</label>  
                        </div>  
                        <div class="col-xs-6 low-padding">
                            <input class="form-control input-sm" id="txtSOTOANBOHOSO" name="txtSOTOANBOHOSO" maxlength="5" style="width: 100%;" disabled>
                        </div>  
                    </div> 
									
								</td>
								<td style="width: 65%;">
                    <div class="form-inline" style="padding-top: 10px;">  
                        <div class="col-xs-3 low-padding">
                            <label>Người giao hồ sơ</label>  
                        </div>  
                        <div class="col-xs-3 low-padding">
                            <input class="form-control input-sm" id="txtNGUOIGIAO_TK" name="txtNGUOIGIAO_TK" maxlength="500" style="width: 100%;" placeholder="nhập để tìm kiếm">
                        </div> 
                        <div class="col-xs-6 low-padding"> 
							<input class="form-control input-sm" id="txtNGUOIGIAO" name="txtNGUOIGIAO" maxlength="500" style="width: 100%;">
                        </div>  
                    </div> 
                    <div class="form-inline" style="padding-top: 10px;">  
                        <div class="col-xs-3 low-padding">
                            <label>Người nhận hồ sơ</label>  
                        </div>  
                        <div class="col-xs-3 low-padding">
                            <input class="form-control input-sm" id="txtNGUOINHAN_TK" name="txtNGUOINHAN_TK" maxlength="500" style="width: 100%;" placeholder="nhập để tìm kiếm">
                        </div> 
                        <div class="col-xs-6 low-padding"> 
							<input class="form-control input-sm" id="txtNGUOINHAN" name="txtNGUOINHAN" maxlength="500" style="width: 100%;">
                        </div>   
                    </div> 
                    <div class="form-inline" style="padding-top: 10px;">  
                        <div class="col-xs-3 low-padding">
                            <label>Bác sỹ điều trị</label>  
                        </div>  
                        <div class="col-xs-3 low-padding">
                            <input class="form-control input-sm" id="txtBSDIEUTRI_TK" name="txtBSDIEUTRI_TK" maxlength="500" style="width: 100%;" placeholder="nhập để tìm kiếm">
                        </div> 
                        <div class="col-xs-6 low-padding"> 
							<input class="form-control input-sm" id="txtBSDIEUTRI" name="txtBSDIEUTRI" maxlength="500" style="width: 100%;">
                        </div>    
                    </div> 
					
								</td>
								</tr>
							</table>
						</div>
					</div>  
					
				</div>
			</div>
			<div class="form-inline panel panel-info" style="margin-bottom: 50px;">
				<div class="panel-heading">&nbsp;&nbsp;&nbsp;</div>
				<div class="panel-body"> 
				</div>
			</div> 
		</div>
		
		<div id="tabBenhNhanThongTin" class="tab-pane"></div>
		
		<div class="form-inline btn-fixed">
			<div class="col-xs-12 low-padding mgt20 mgb10" style="text-align: center; width: 80%; margin-bottom: 30px; " id="gridButton">
				<button class="btn btn-sm btn-primary" id="btnLuu">
					<span class="glyphicon glyphicon-pencil"></span> Lưu
				</button>
				<button class="btn btn-sm btn-primary" id="btnThemMoi">
					<span class="glyphicon glyphicon-floppy-remove"></span> Lưu & Đóng
				</button>
				<button class="btn btn-sm btn-primary" id="btnInBieuMau">
					<span class="fixDisplay glyphicon glyphicon-print"></span> In biểu mẫu
				</button>
				<!-- <select class="form-control input-sm i-col-3" id="sltInBieuMau" style="height: 29px !important;">
					<option value="pdf" selected="selected">file pdf</option>
					<option value="rtf">file doc</option>						
					 <option value="xlsx">xlsx</option>
					<option value="xls">doc</option>
				</select> -->
				<button class="btn btn-sm btn-primary" id="btn_Close">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
			</div>
		</div>
		<script>
			var opt = [];
			var hospital_id = '{hospital_id}';
			var user_id = '{user_id}';
			var dept_id = '{dept_id}';
			var user_type = '{user_type}';
			var province_id = '{province_id}';
			var uuid = '{uuid}';
			var db_schema='{db_schema}';
			var lang = "vi";
			console.log('hospital_id=' + hospital_id + ' user_type='
					+ user_type);
			var session_par = [];
			session_par[0] = hospital_id;
			session_par[1] = user_id;
			session_par[2] = user_type;
			session_par[3] = province_id;
			session_par[4] = db_schema;
			var table_name = '{table}';
	
			var _opts = new Object();
			var mode = '{showMode}';				
			if(mode=='dlg') {		
				parent.DlgUtil.tunnel(DlgUtil.moveEvent);
				data=EventUtil.getVar("dlgVar");		
				_opts.khambenhid = data.khambenhid;
				_opts.hosobenhanid = data.hosobenhanid;		
				_opts.benhnhanid = data.benhnhanid;
				_opts.loaibenhanid = data.loaibenhanid;
				_opts.maloaibenhan = data.maloaibenhan;
				_opts.khoaid=dept_id;
			}
	
			_opts._param = session_par;
			_opts._uuid = uuid;
			initRest(_opts._uuid);
			var BAN01_BONG01 = new BAN01_VIEMGANB(_opts);
			BAN01_BONG01.load();
		</script>
	</div>
</div>
<script>
	//initRest();
	if (_mode == 'view') {
		$("#divMain").removeClass("container").css("width", "96%").css(
				"margin", "0 auto");
	}
	var doctorMgr = new CRegDoctoringMgr();
	doctorMgr.load();
</script>