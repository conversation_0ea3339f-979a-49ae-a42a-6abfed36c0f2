<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20170105" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../canlamsang/ParaclinicalUtility.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS01X003_KQXN_VKLM1_K74.js?v=20220829"></script>

<style>
	.ui-jqgrid tr.jqgrow td {
		white-space: nowrap !important;
		height:auto;
		vertical-align:text-top;
		padding-top:2px;
	}
</style>

<div id="divMain">
	<div class="mgt5" id="divThongTinBenhNhan">
		<div class="col-md-12 low-padding">
			<div class="col-md-1 low-padding">
				<label style="font-weight: 700 !important;">Bệnh nhân</label>
			</div>
			<div class="col-md-4 low-padding">
				<label class="text-red" id="lblHoTen" style="text-transform: uppercase; font-size: 1.3em;"></label>
			</div>
			<div class="col-md-1 low-padding">
			 	<input id="chbKhan" class="mgb5" type="checkbox"> <label>  Khẩn </label>
			</div>
			<div class="col-md-1 low-padding">
				<label>Đối tượng</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblDoiTuong"></label>
			</div>
			<div class="col-md-1 low-padding">
				<label>TG chỉ định</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblTGChiDinh"></label> 
			</div>
		</div>
		
		<div class="col-md-12 low-padding">
			<div class="col-md-6 low-padding">
				<label style="font-weight: 700 !important;" id="lblDiaChi"></label>
			</div>
			<div class="col-md-1 low-padding">
				<label>Bác sĩ CĐ</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblBacSi"></label>  
			</div>
			<div class="col-md-1 low-padding">
				<label>TG kết quả</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblTGKetQua"></label>
			</div>
		</div>
		
		<div class="col-md-12 low-padding">
			<div class="col-md-3 low-padding">
				<div class="col-md-3 low-padding">
					<label>Số phiếu</label>
				</div>
				<div class="col-md-8 low-padding"> 
					<label id="lblSoPhieu" style="color: blue; font-size: large;width: 100%"></label> 
				</div>
			</div>
			<div class="col-md-6 low-padding">
				<div class="col-md-3 low-padding">
					<label>Nơi gửi</label>
				</div>
				<div class="col-md-9 low-padding">
					<label id="lblNoiGui"></label>  
				</div>
			</div>
			<div class="col-md-1 low-padding">
				<label>Người trả KQ</label>
			</div>
			<div class="col-md-2 low-padding">
				<label id="lblNguoiThucHien"></label>  
			</div>
		</div>
		
		<div class="col-md-12 low-padding">
			<div class="col-md-3 low-padding">
				<div class="col-md-3 low-padding">
					<label>Barcode</label>
				</div>
				<div class="col-md-8 low-padding"> 
					<label id="lblBarcode" style="color: red; font-size: large;width: 100%"></label> 
				</div>
			</div>
			<div class="col-md-9 low-padding">
				<div class="col-md-2 low-padding">
					<label>Chẩn đoán</label>
				</div>
				<div class="col-md-10 low-padding">
					<label style="font-weight: 700 !important;" id="lblChanDoan"></label>  
				</div>
			</div>
		</div>
	</div>
	<div class="row">
  		<div class="col-md-12 bs-linebreak" style="height:12px;">
  		</div>
  	</div>
	<div class="col-xs-12 low-padding panel panel-info" >
		<h1 style="color: blue; font-size: large;"">Kết quả nuôi cấy vi khuẩn lao(mẫu 1):</h1>
		<div class="mgt5" id="divDSVKLaoMau1" style=" border-style: groove;border-width: 2px;border-color: blue;" >
			
			<table class="table table-bordered" border="2px">
				<tbody>
					<tr>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class=''>Số XN</label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class=''>Mã XN</label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class=''>Tên XN</label>
							</div>
						</td>
	
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Ngày nhận mẫu</label>
							</div>
						</td>
	
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Ngày cấy</label>
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Âm tính</label>
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhiễm trùng</label>
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>MTB</label>
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>NTM</label>
							</div>
						</td>
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Ngày báo (+)</label>
							</div>
						</td>
						<td style="width: 140px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kết quả soi</label>
							</div>
						</td>
						<td style="width: 140px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Test định dạng</label>
							</div>
						</td>
					</tr>
					
					<tr>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtSOXN1" name="txtSOXN1" valrule=""title="">
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblMACHIDINH"></label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblTENCHIDINH"></label>
							</div>
						</td>
	
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding">
								<div class="input-group" style="width: 100% important;">
									<input class="form-control input-sm" id="txtNGAYNHANMAU" name="txtNGAYNHANMAU" title="" data-mask="00/00/0000"> 
									<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtNGAYNHANMAU','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</td>
	
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding">
								<div class="input-group" style="width: 100% important;">
									<input class="form-control input-sm" id="txtNGAYCAY" name="txtNGAYCAY" title="" data-mask="00/00/0000"> 
									<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtNGAYCAY','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtAMTINH" name="txtAMTINH" valrule=""title="">
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHIEMTRUNG" name="txtNHIEMTRUNG" valrule=""title="">
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtMTB" name="txtMTB" valrule=""title="">
							</div>
						</td>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNTM" name="txtNTM" valrule=""title="">
							</div>
						</td>
						<td style="width: 110px;">
							<div class="col-xs-12 low-padding">
								<div class="input-group" style="width: 100% important;">
									<input class="form-control input-sm" id="txtNGAYBAO" name="txtNGAYBAO" title="" data-mask="00/00/0000"> 
									<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" onclick="NewCssCal('txtNGAYBAO','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
						</td>
						<td style="width: 140px;">
							<div class="col-xs-12 low-padding">
								<textarea class="form-control input-sm" style="width: 100%;" rows="2"
									id="txtKQSOI" name="txtKQSOI" valrule=""title="" maxlength="499"></textarea>
							</div>
						</td>
						<td style="width: 140px;">
							<div class="col-xs-12 low-padding">
								<textarea class="form-control input-sm" style="width: 100%;" rows="2"
									id="txtTESTDD" name="txtTESTDD" valrule=""title="" maxlength="499"></textarea>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
			
			
		</div>
		<div class="col-md-12 low-padding"  padding-left: 15px !important;>
					<div class="col-md-10 low-padding" >
						<div class="col-md-10 low-padding">
							<div class="col-md-10 low-padding">
								<button class="btn btn-sm btn-primary" id="btnLuu1" style="width:110px">
									<span class="glyphicon glyphicon-floppy-disk"></span> Lưu lại 
								</button>
								<button class="btn btn-sm btn-primary" id="btnTraKetQua1" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-check"></span> Trả kết quả 
								</button>
								<!-- <button class="btn btn-sm btn-primary" id="btnHuyKetQua1" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-remove-circle"></span> Hủy kết quả  
								</button> -->
								<button class="btn btn-sm btn-primary" id="btnInKQ1" style="width:150px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-print"></span> In kết quả mẫu 1
								</button>
								<button class="btn btn-sm btn-primary" id="btnHuy1" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-log-out"></span> Đóng 
								</button>
								<button class="btn btn-sm btn-primary" id="btnEditTimePopUpX1" style="width:110px;padding-left: 4px !important;display: none;">
									<span class="glyphicon glyphicon-edit"></span> Sửa TG trả KQ
								</button>
							</div>
			
						</div>
					</div>
					<div class="col-md-2 low-padding" >
						<button class="btn btn-sm btn-primary" id="btnXoaText1" style="width:110px;padding-left: 1px !important;">
										<span class="glyphicon glyphicon-remove-circle"></span> Xóa text  
						</button>
					</div>
			</div>	
	
	</div>
	
	
	
	
	<div class="row">
  		<div class="col-md-12 bs-linebreak" style="height:12px;">
  		</div>
  	</div>
	<div class="col-xs-12 low-padding panel panel-info" >
		<h1 style="color: blue; font-size: large;"">Kháng sinh đồ vi khuẩn lao(mẫu 2):</h1>
		<div class="mgt5" id="divDSVKLaoMau2" style=" border-style: groove;border-width: 2px;border-color: blue;" >
			<table class="table table-bordered">
				<tbody>
						<tr>
							<td>
								<div class="col-xs-12 low-padding">
									<input class="input-sm" type="checkbox" id="chkMOITRUONGDAC"
										name="chkMOITRUONGDAC" title=""> <label
										class=' control-label'>Môi trường đặc</label>
								</div>
							</td>
							<td>
								<div class="col-xs-12 low-padding">
									<input class="input-sm" type="checkbox" id="chkMGITBATEC"
										name="chkMGITBATEC" title=""> <label
										class=' control-label'>MGIT - BATEC</label>
								</div>
							</td>
							<td>
								<div class="col-xs-12 low-padding">
									<div class="col-xs-4 low-padding">
										<label class='mgt3 mgl10' style="">Kết quả nuôi cấy</label>
									</div>
									<div class="col-xs-8 low-padding">
										<input class="form-control input-sm"
											style="width: 100%;" id="txtKETQUANUOICAY"
											name="txtKETQUANUOICAY" valrule="" title="">
									</div>
								</div>
							</td>
							<td>
								<div class="col-xs-12 low-padding">
									<div class="col-xs-4 low-padding">
										<label class='mgt3 mgl10' style="">Test Niacin</label>
									</div>
									<div class="col-xs-8 low-padding">
										<input class="form-control input-sm"
											style="width: 100%;" id="txtTESTNIAXIN">
									</div>
								</div>
							</td>
						</tr>
				</tbody>
			</table>
			<table class="table table-bordered">
				<tbody>
						<tr>
							<td>
								<div class="col-xs-12 low-padding ">
									<label class=''> </label>
								</div>
							</td>
							<td>
								<div class="col-xs-12 low-padding">
									<select class="form-control input-sm i-col-m_fl" id="cboMAU_MTB" name="cboMAU_MTB" style="width: 50%;">	
										<option value="0">Chọn dịch vụ MTB</option>
										<option value="1">Mẫu MTB hàng 1 môi trường lỏng </option>
										<option value="2">Mẫu MTB hàng 1 môi trường đặc</option>
										<option value="3">Mẫu MTB hàng 2 môi trường lỏng</option>	
										<option value="4">Mẫu MTB hàng 2 môi trường đặc</option>
										<option value="5">Mẫu MTB PZA môi trường lỏng</option>																
									</select> 
								</div>
							</td>
						</tr>
				</tbody>
			</table>
			<!-- style="border: 3px solid #000000;" -->
			<table class="table table-bordered" id="tblMau34">
				<tbody>
					<tr>
						
						<td rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Số XN</label>
							</div>
						</td>
						<td rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Mã XN</label>
							</div>
						</td>
						<td rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Tên XN</label>
							</div>
						</td>
	
						<td style="width: 100px;" colspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label id = "IDMOX" class=''>MOX</label>
							</div>
						</td>
						<td style="width: 100px;" colspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label id = "IDAMK" class=''>AMK</label>
							</div>
						</td>
						<td style="width: 100px;" colspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label id="IDKM" class=''>KM</label>
							</div>
						</td>
						<td style="width: 100px;" colspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label id ="IDCAP" class=''>CAP</label>
							</div>
						</td>
						<td style="width: 280px;" rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kết luận</label>
							</div>
						</td>
					</tr>
					<tr>
						
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (MOX)</label>
							</div>
						</td>
	
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (MOX)</label>
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (AMK)</label>
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (AMK)</label>
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (KM)</label>
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (KM)</label>
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (CAP)</label>
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (CAP)</label>
							</div>
						</td>
						
					</tr>
					<tr>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtSOXN2" name="txtSOXN2" valrule=""title="">
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblMACHIDINH2"></label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblTENCHIDINH2"></label>
							</div>
						</td>
	
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYMOX" name="txtNHAYMOX" valrule=""title="">
							</div>
						</td>
	
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGMOX" name="txtKHANGMOX" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYAMK" name="txtNHAYAMK" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGAMK" name="txtKHANGAMK" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYKM" name="txtNHAYKM" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGKM" name="txtKHANGKM" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYCAP" name="txtNHAYCAP" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGCAP" name="txtKHANGCAP" valrule=""title="">
							</div>
						</td>
						<td style="width: 280px;">
							<div class="col-xs-12 low-padding">
								<textarea class="form-control input-sm" style="width: 100%;" rows="3"
									id="txtKETLUAN" name="txtKETLUAN" valrule=""title="" maxlength="499"></textarea>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
			<table class="table table-bordered" id="tblMau125">
				<tbody>
					<tr>
						
						<td rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Số XN</label>
							</div>
						</td>
						<td rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Mã XN</label>
							</div>
						</td>
						<td rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Tên XN</label>
							</div>
						</td>
	
						<td style="width: 100px;" colspan="2" align="center" class="divINH">
							<div class="col-xs-12 low-padding ">
								<label id="IDINH" class=''>INH</label>
							</div>
						</td>
	
						<td style="width: 100px;" colspan="2" align="center" class="divRMP">
							<div class="col-xs-12 low-padding ">
								<label id = "IDRMP" class=''>RMP</label>
							</div>
						</td>
						<td style="width: 100px;" colspan="2" align="center" class="divEMB">
							<div class="col-xs-12 low-padding ">
								<label id="IDEMB" class=''>EMB</label>
							</div>
						</td>
						<td style="width: 100px;" colspan="2" align="center" class="divSM">
							<div class="col-xs-12 low-padding ">
								<label id = "IDSM" class=''>SM</label>
							</div>
						</td>
						
						<td style="width: 100px;" colspan="2" align="center" class="divPZA">
							<div class="col-xs-12 low-padding ">
								<label id ="IDPZA" class='' >PZA</label>
							</div>
						</td>
						<td style="width: 280px;" rowspan="2" align="center">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kết luận</label>
							</div>
						</td>
					</tr>
					<tr>
						
						<td style="width: 50px;" class="divINH">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (INH)</label>
							</div>
						</td>
	
						<td style="width: 50px;" class="divINH">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (INH)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divRMP">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (RMP)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divRMP">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (RMP)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divEMB">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (EMB)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divEMB">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (EMB)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divSM">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (SM)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divSM">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (SM)</label>
							</div>
						</td>
						
						<td style="width: 50px;" class="divPZA">
							<div class="col-xs-12 low-padding ">
								<label class=''>Nhạy (PZA)</label>
							</div>
						</td>
						<td style="width: 50px;" class="divPZA">
							<div class="col-xs-12 low-padding ">
								<label class=''>Kháng (PZA)</label>
							</div>
						</td>
					</tr>
					
					
					
					<tr>
						<td style="width: 90px;">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtSOXN3" name="txtSOXN3" valrule=""title="">
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblMACHIDINH3"></label>
							</div>
						</td>
						<td>
							<div class="col-xs-12 low-padding ">
								<label class='' id="lblTENCHIDINH3"></label>
							</div>
						</td>
	
						<td style="width: 50px;" class="divINH">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYINH" name="txtNHAYINH" valrule=""title="">
							</div>
						</td>
	
						<td style="width: 50px;" class="divINH">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGINH" name="txtKHANGINH" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divRMP">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYRMP" name="txtNHAYRMP" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divRMP">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGRMP" name="txtKHANGRMP" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divEMB">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYEMB" name="txtNHAYEMB" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divEMB">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGEMB" name="txtKHANGEMB" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divSM">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYSM" name="txtNHAYSM" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divSM">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGSM" name="txtKHANGSM" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divPZA">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtNHAYPZA" name="txtNHAYPZA" valrule=""title="">
							</div>
						</td>
						<td style="width: 50px;" class="divPZA">
							<div class="col-xs-12 low-padding">
								<input class="form-control input-sm" style="width: 100%;text-align: center;"
								id="txtKHANGPZA" name="txtKHANGPZA" valrule=""title="">
							</div>
						</td>
						<td style="width: 280px;">
							<div class="col-xs-12 low-padding">
								<textarea class="form-control input-sm" style="width: 100%;" rows="3"
									id="txtKETLUAN2" name="txtKETLUAN2" valrule=""title="" maxlength="499"></textarea>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
			<div class="row">
		  		<div class="col-md-12 bs-linebreak" style="height:5px;">
		  		</div>
		  	</div>
			
			
		</div>
		<div class="col-md-12 low-padding"  padding-left: 15px !important;>
					<div class="col-md-10 low-padding" >
						<div class="col-md-10 low-padding">
							<div class="col-md-10 low-padding">
								<button class="btn btn-sm btn-primary" id="btnLuu2" style="width:110px">
									<span class="glyphicon glyphicon-floppy-disk"></span> Lưu lại 
								</button>
								<button class="btn btn-sm btn-primary" id="btnTraKetQua2" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-check"></span> Trả kết quả 
								</button>
								<!-- <button class="btn btn-sm btn-primary" id="btnHuyKetQua2" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-remove-circle"></span> Hủy kết quả  
								</button> -->
								<button class="btn btn-sm btn-primary" id="btnInKQ2" style="width:150px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-print"></span> In kết quả mẫu 2
								</button>
								<button class="btn btn-sm btn-primary" id="btnHuy2" style="width:110px;padding-left: 4px !important;">
									<span class="glyphicon glyphicon-log-out"></span> Đóng 
								</button>
								<button class="btn btn-sm btn-primary" id="btnEditTimePopUpX2" style="width:110px;padding-left: 4px !important;display: none;">
									<span class="glyphicon glyphicon-edit"></span> Sửa TG trả KQ
								</button>
							</div>
			
						</div>
					</div>
					<div class="col-md-2 low-padding" >
						<button class="btn btn-sm btn-primary" id="btnXoaText2" style="width:110px;padding-left: 1px !important;">
										<span class="glyphicon glyphicon-remove-circle"></span> Xóa text  
						</button>
					</div>
			</div>	
	
	</div>
	
	
	<div id="divKetLuan">
			
	</div>
	
	
	
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDDichVuID" value="">
<input type="hidden" id="hdfIDShowBtnEditTime" value="">
<input type="hidden" id="hdfIDKQ1" value="">
<input type="hidden" id="hdfIDKQ2" value="">

<div id="divTraKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmView" style="width:1000px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script type="text/javascript">	
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var ten_khoa = '{dept_name}';
	var lang = "vn";
	var type = "{type}";
	var userRights = "{q}";
	var user_group_id = '{user_group_id}';
	console.log("userright="+userRights+",usergroup="+user_group_id);
	var session_par=[];
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	session_par[0]=hospital_id;
	var table_name='{table}';

	var idmaubenhpham = '{idmaubenhpham}';
	var iddichvukb = '{iddichvukb}';
	var iddichvu = '{dichvuid}';
	//nghiant 26062017
	var showNutSuaNgayTraKQ='{showNutSuaNgayTraKQ}';

	var show='{show}';
	var tiepnhanid='{tiepnhanid}';
	var rolePTH='{rolePTH}';
	var mode = '{showMode}';
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	}
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	_opts._idmaubenhpham=idmaubenhpham;
	_opts._showNutSuaNgayTraKQ=showNutSuaNgayTraKQ;
	_opts._iddichvukb=iddichvukb;
	_opts._iddichvu=iddichvu;
	_opts._show=show;
	_opts._tiepnhanid=tiepnhanid;
	
	//nghiant 01082017 
	//_opts._phong_chuyen_den_id=phong_chuyen_den_id;
	_opts._rolePTH = rolePTH;
	//_opts._vikhuanid = vikhuanid;
	//_opts._loaivikhuanid = loaivikhuanid;
	console.log('rolePTH: '+_opts._rolePTH);
	
	
	initRest(_opts._uuid);
	
	var tt = new KetQuaXetNghiem2(_opts);
	tt.load();
</script>