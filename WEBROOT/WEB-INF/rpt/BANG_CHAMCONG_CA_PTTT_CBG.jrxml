<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BANG_CHAMCONG_CA_PTTT_CBG" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="836" leftMargin="3" rightMargin="3" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="c8298077-8bc5-4286-84c8-8115351568c0">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="33"/>
	<property name="ireport.y" value="0"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoaid" class="java.lang.String"/>
	<parameter name="loaiphauthuat" class="java.lang.Long"/>
	<parameter name="khoaid_TEXT" class="java.lang.String"/>
	<parameter name="i_thangnam" class="java.lang.String"/>
	<parameter name="loaiphauthuat_TEXT" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call bc_chamcong_ca_pttt29050($P{[UID]},$P{[SCH]},$P{[HID]},$P{[IP]},$P{khoaid},$P{i_thangnam},$P{loaiphauthuat},$P{ora_cursor})}]]>
	</queryString>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="NGAY1" class="java.math.BigDecimal"/>
	<field name="NGAY2" class="java.math.BigDecimal"/>
	<field name="NGAY3" class="java.math.BigDecimal"/>
	<field name="NGAY4" class="java.math.BigDecimal"/>
	<field name="NGAY5" class="java.math.BigDecimal"/>
	<field name="NGAY6" class="java.math.BigDecimal"/>
	<field name="NGAY7" class="java.math.BigDecimal"/>
	<field name="NGAY8" class="java.math.BigDecimal"/>
	<field name="NGAY9" class="java.math.BigDecimal"/>
	<field name="NGAY10" class="java.math.BigDecimal"/>
	<field name="NGAY11" class="java.math.BigDecimal"/>
	<field name="NGAY12" class="java.math.BigDecimal"/>
	<field name="NGAY13" class="java.math.BigDecimal"/>
	<field name="NGAY14" class="java.math.BigDecimal"/>
	<field name="NGAY15" class="java.math.BigDecimal"/>
	<field name="NGAY16" class="java.math.BigDecimal"/>
	<field name="NGAY17" class="java.math.BigDecimal"/>
	<field name="NGAY18" class="java.math.BigDecimal"/>
	<field name="NGAY19" class="java.math.BigDecimal"/>
	<field name="NGAY20" class="java.math.BigDecimal"/>
	<field name="NGAY21" class="java.math.BigDecimal"/>
	<field name="NGAY22" class="java.math.BigDecimal"/>
	<field name="NGAY23" class="java.math.BigDecimal"/>
	<field name="NGAY24" class="java.math.BigDecimal"/>
	<field name="NGAY25" class="java.math.BigDecimal"/>
	<field name="NGAY26" class="java.math.BigDecimal"/>
	<field name="NGAY27" class="java.math.BigDecimal"/>
	<field name="NGAY28" class="java.math.BigDecimal"/>
	<field name="NGAY29" class="java.math.BigDecimal"/>
	<field name="NGAY30" class="java.math.BigDecimal"/>
	<field name="NGAY31" class="java.math.BigDecimal"/>
	<field name="CAPBAC" class="java.lang.String"/>
	<field name="THANG" class="java.lang.String"/>
	<field name="NAM" class="java.lang.String"/>
	<variable name="v1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY1}]]></variableExpression>
	</variable>
	<variable name="v2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY2}]]></variableExpression>
	</variable>
	<variable name="v3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY3}]]></variableExpression>
	</variable>
	<variable name="v4" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY4}]]></variableExpression>
	</variable>
	<variable name="v5" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY5}]]></variableExpression>
	</variable>
	<variable name="v6" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY6}]]></variableExpression>
	</variable>
	<variable name="v7" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY7}]]></variableExpression>
	</variable>
	<variable name="v8" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY8}]]></variableExpression>
	</variable>
	<variable name="v9" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY9}]]></variableExpression>
	</variable>
	<variable name="v10" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY10}]]></variableExpression>
	</variable>
	<variable name="v11" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY11}]]></variableExpression>
	</variable>
	<variable name="v12" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY12}]]></variableExpression>
	</variable>
	<variable name="v13" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY13}]]></variableExpression>
	</variable>
	<variable name="v14" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY14}]]></variableExpression>
	</variable>
	<variable name="v15" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY15}]]></variableExpression>
	</variable>
	<variable name="v16" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY16}]]></variableExpression>
	</variable>
	<variable name="v17" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY17}]]></variableExpression>
	</variable>
	<variable name="v18" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY18}]]></variableExpression>
	</variable>
	<variable name="v19" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY19}]]></variableExpression>
	</variable>
	<variable name="v20" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY20}]]></variableExpression>
	</variable>
	<variable name="v21" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY21}]]></variableExpression>
	</variable>
	<variable name="v22" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY22}]]></variableExpression>
	</variable>
	<variable name="v23" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY23}]]></variableExpression>
	</variable>
	<variable name="v24" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY24}]]></variableExpression>
	</variable>
	<variable name="v25" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY25}]]></variableExpression>
	</variable>
	<variable name="v26" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY26}]]></variableExpression>
	</variable>
	<variable name="v27" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY27}]]></variableExpression>
	</variable>
	<variable name="v28" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY28}]]></variableExpression>
	</variable>
	<variable name="v29" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY29}]]></variableExpression>
	</variable>
	<variable name="v30" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY30}]]></variableExpression>
	</variable>
	<variable name="v31" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NGAY31}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="256" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="334" height="25" uuid="82bf1240-632e-40c5-93aa-3e010c7e6d24"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="1" y="125" width="835" height="20" uuid="25121876-1d76-4deb-bb38-b689eb7ab1aa"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tháng "+($F{THANG}==null?"":$F{THANG})+" năm "+($F{NAM}==null?"":$F{NAM})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="23" y="153" width="127" height="103" forecolor="#000000" backcolor="#FFFFFF" uuid="ee9c5f36-1307-4e84-9838-51fe1696d030"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Họ và tên]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="226" y="153" width="558" height="58" forecolor="#000000" backcolor="#FFFFFF" uuid="3c9fe919-4b58-4b97-a438-5fa91945beb3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số ca hưởng phụ cấp phẫu thuật trong tháng]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="226" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="96bbbd42-77c6-40cc-b29d-9e5f7e1c93e9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[1]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="244" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="8e07543c-4c07-4727-9053-ea526ebd7430"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="766" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="2d033e32-7227-4a15-89fa-993149b8a1b4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[31]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="262" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="4908f593-051a-4532-86ea-a9cf9cac6f2b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[3]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="298" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="40779a03-d9c7-4413-9618-533bab31d351"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[5]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="280" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="8f487254-91ee-46d5-9561-45075f94ebf0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[4]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="388" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="1f43438e-6e17-4756-99f1-4f3b60ca6e3b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[10]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="316" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="9e469b4a-408f-4b1d-bfe1-a76ee8d0fcad"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[6]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="352" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="828f5104-eb28-4a47-8d86-3c9ba6911985"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[8]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="370" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="4be074fe-cce5-45fb-9a48-2c0fb59a38a3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[9]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="334" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="656c0b1a-7b97-4d72-9d86-80aba0696d57"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[7]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="406" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="72329d6e-0961-4c68-8e08-00123b779dd2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[11]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="532" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="0fc15564-1659-4dbe-b651-be016c79c15e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[18]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="568" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="bc27e815-da9e-446f-8972-e4a29f5e4c75"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[20]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="604" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="232236a8-b9b4-483c-9f63-83618e7d1f96"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[22]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="496" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="1784d626-a4ea-4fc4-aac3-4f7fc3f8bfdb"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[16]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="586" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="b12e8c49-0869-4d5f-8a10-be1d9f271e92"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[21]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="622" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="1f70695c-e9c3-462c-a93f-aac9a35a540e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[23]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="424" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="6da60281-8ca0-4623-b2d3-64773c21d579"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[12]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="460" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="12bdd092-3584-47b6-b534-88928dada402"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[14]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="478" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="a8df6d2a-70ac-4811-9f31-48ac6f50590b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[15]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="442" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="5a7483d4-b2fc-4e69-a609-ed3bbc08b228"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[13]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="514" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="7c24c491-52d4-47bd-9536-c311c06a3994"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[17]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="550" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="51ea7cb1-d50e-4d09-88c6-e427636d479e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[19]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="658" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="82a6db5c-94d2-4656-8ab0-01ac13c282ed"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[25]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="640" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="dc351cdf-5184-4a8d-aa96-332e19c4d739"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[24]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="712" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="045cb30e-2d82-4e85-a67f-ca5f0f343951"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[28]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="676" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="66712701-91d2-4a87-bf92-b2dc05066b75"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[26]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="748" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="565c4233-0ffd-48e3-9592-eada9a4039ac"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[30]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="730" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="8c7f92f9-2510-4810-b17c-6a472c2f377a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[29]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="694" y="211" width="18" height="24" forecolor="#000000" backcolor="#FFFFFF" uuid="c44c21d7-67c0-412b-9b5c-aa51803eebc8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[27]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="784" y="153" width="52" height="58" forecolor="#000000" backcolor="#FFFFFF" uuid="84e98e32-d8b7-4f5b-9bb1-b06bf342a72f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Cộng ca PC được hưởng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="94" width="835" height="25" uuid="21b91fce-1c82-4cff-9b26-bb108d784ea7"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BẢNG CHẤM CÔNG CA PHẪU THUẬT ĐƯỢC HƯỞNG PHỤ CẤP "+"LOẠI "
+($P{loaiphauthuat}.doubleValue()==1?"1":
    ($P{loaiphauthuat}.doubleValue()==2?"2":
      $P{loaiphauthuat}.doubleValue()==3?"3":" ĐẶC BIỆT"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1" y="25" width="333" height="25" uuid="60a79188-2b5e-4c5e-bce3-183a7f82ffb1"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoaid_TEXT}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="784" y="211" width="52" height="45" forecolor="#000000" backcolor="#FFFFFF" uuid="e8ebe911-e16c-47db-a5bf-0c6d1a06613e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Loại "
+($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="226" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="c27e9eae-2789-40b5-afa8-cae772d2ffa0"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="244" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="ea351307-76d5-4a63-92a5-3d185f10f220"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="280" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="009c5ab7-f80a-47c5-b467-6257e5be4f65"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="262" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="dc29f9ed-3b07-4b9e-986c-49079326f428"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="316" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="43b5a1a2-5c16-42fb-bce7-5e43ff1ac98e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="298" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="40bb179e-7fc9-4268-b013-0976bad7b021"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="352" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="0935f103-8a23-48c4-a057-1652d261158c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="334" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="35842b81-f637-4a3f-9ebe-bf00dec60348"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="424" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="50585e0d-97f3-47e2-96c9-6d5f71610045"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="388" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="7c2c40cc-215a-4115-ab2b-eec12babc498"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="478" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="560c6e57-40e7-4ecf-9c98-90f93bde953c"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="496" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="ebfe4e07-dd75-49c0-8f2a-10cc9ff09f53"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="370" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="55675a01-e3d9-4752-abc7-60306fe20b96"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="460" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="fd875653-288d-4592-97ba-923a3e40f823"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="406" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="ced36a06-708b-4b76-9263-c5a2ac0200df"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="442" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="2b6eb016-0a9a-437a-a0df-96c6f076ae96"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="568" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="d1615cca-0f66-4e26-ac17-6abda8e48145"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="532" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="695226ce-5017-4f1a-9e0f-5767e2ac7bfb"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="622" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="105a3974-9971-441c-a527-96016ce9ed7d"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="514" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="17724852-ef03-4cc8-b85f-908d499d379b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="604" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="d14849d9-d1ed-4642-8b34-6856242a5c68"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="550" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="fca89560-a427-442c-920e-46c6f4978338"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="586" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="0c86099e-5924-4af9-9b37-c44491220abe"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="694" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="82b51295-8c82-48fa-b671-9f2cfc0c7a7f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="658" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="31390f1c-8562-4b17-9da9-562ac4461f41"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="748" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="e4994f06-0c95-45f5-b08c-3383d5eb624a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="766" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="4dca2d46-a235-490d-9786-7568ab0fb9ec"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="640" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="73775a7a-363d-489d-9161-898c1ad76b3a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="730" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="540f62b1-71be-480f-ae5d-b98e2449689f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="676" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="a4fc2ce7-9da5-4257-ab5c-e7b0ad97442e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="712" y="235" width="18" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="8360aae7-6f81-4daf-83bb-45d707463910"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiphauthuat}.doubleValue()==1?"I":
    ($P{loaiphauthuat}.doubleValue()==2?"II":
      $P{loaiphauthuat}.doubleValue()==3?"III":"ĐB"
    )
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="0" y="153" width="23" height="103" forecolor="#000000" backcolor="#FFFFFF" uuid="4f6927e6-04f4-44bd-bd2c-43f49344da36"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[TT]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="150" y="153" width="76" height="103" forecolor="#000000" backcolor="#FFFFFF" uuid="29a9a109-d616-4579-ac2f-e2135231eb52"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[C/bậc mổ chính, phụ mổ, giúp việc]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="478" y="74" width="358" height="20" uuid="9ca62315-26a2-451b-a893-50402f09dc0b"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["10/10/2017 của Bộ Tài Chính)"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="478" y="54" width="358" height="20" uuid="72605549-281d-4231-a2cc-1d11c8b4fc27"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["(Ban hành theo TT số: 107/2017/TT - BCT- Ngày "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="478" y="34" width="358" height="20" uuid="ccad51ae-1821-437c-8b99-a070b190a482"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mẫu số : C01 -HD "]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="30">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="23" y="0" width="127" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="6346df49-eaae-47f8-9fe4-2e7576ec569f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="226" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="fe48ea68-d966-43eb-b0fb-9aee67dd76e0"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY1}.doubleValue()==0?"":$F{NGAY1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="244" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="19f2ed2e-d9f4-4ee2-9fbe-6b2cc75411c3"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY2}.doubleValue()==0?"":$F{NGAY2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="262" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="397e2ae3-1b50-4c92-80c6-781023777a26"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY3}.doubleValue()==0?"":$F{NGAY3}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="316" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="df439fbc-2581-4449-b221-8da5abc68353"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY6}.doubleValue()==0?"":$F{NGAY6}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="280" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="abb460c9-00de-4383-a4b7-c17c14006916"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY4}.doubleValue()==0?"":$F{NGAY4}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="298" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="3b33d8be-8011-4d35-a9ab-e3844cd9dfd0"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY5}.doubleValue()==0?"":$F{NGAY5}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="370" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c32bcf87-b8d2-41ea-b398-a6fd2b5111fc"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY9}.doubleValue()==0?"":$F{NGAY9}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="334" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="a6239142-8ca8-455d-8357-985af75b2762"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY7}.doubleValue()==0?"":$F{NGAY7}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="352" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="85a33a2a-23ae-4aba-9e49-6214bc9246ca"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY8}.doubleValue()==0?"":$F{NGAY8}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="424" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="2907dbec-d8cc-4517-8278-8a512af022af"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY12}.doubleValue()==0?"":$F{NGAY12}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="388" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="d39e81aa-c1ef-4215-8166-************"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY10}.doubleValue()==0?"":$F{NGAY10}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="406" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="fcac7666-ba14-4028-8877-111bc7634a3b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY11}.doubleValue()==0?"":$F{NGAY11}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="478" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="3eebf2f0-e271-4314-a4df-cbc4aadb1a2b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY15}.doubleValue()==0?"":$F{NGAY15}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="442" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="371c2cc3-c1ab-48eb-99ae-9d2daab3ac65"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY13}.doubleValue()==0?"":$F{NGAY13}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="460" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="cba9a42c-8bb4-4df1-843e-6e86819c8fea"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY14}.doubleValue()==0?"":$F{NGAY14}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="532" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="ce7be63f-d4cb-4642-bba6-c7afa7b6f1d4"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY18}.doubleValue()==0?"":$F{NGAY18}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="496" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="d5f85290-1aa1-40ff-90cf-f0cf63080507"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY16}.doubleValue()==0?"":$F{NGAY16}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="514" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="8249352c-0d59-4336-81c6-1119aee1260a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY17}.doubleValue()==0?"":$F{NGAY17}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="586" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="0d353351-a0f8-4f5d-bc83-469e9d336b52"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY21}.doubleValue()==0?"":$F{NGAY21}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="550" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="248ae7fe-e19a-45b0-a13a-c85fee32567e"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY19}.doubleValue()==0?"":$F{NGAY19}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="568" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="6588ba9d-596a-4160-b09e-c69e2c500a60"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY20}.doubleValue()==0?"":$F{NGAY20}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="640" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="0fafafb6-8f7a-43ed-87ee-b3bb6fbcf2a9"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY24}.doubleValue()==0?"":$F{NGAY24}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="604" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="22ad8bee-37b8-46b9-996e-d7c538b0b0c4"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY22}.doubleValue()==0?"":$F{NGAY22}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="622" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c6c83d1e-6673-4644-83cb-40fa1238b9be"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY23}.doubleValue()==0?"":$F{NGAY23}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="694" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="5b5f2e5b-627a-4bba-a193-3c6283dcbb6a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY27}.doubleValue()==0?"":$F{NGAY27}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="658" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="6a5d0664-aac6-4eb9-918c-88916e0e06ed"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY25}.doubleValue()==0?"":$F{NGAY25}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="676" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="1e5d3be4-83b7-4321-8ddb-60a5e917065a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY26}.doubleValue()==0?"":$F{NGAY26}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="748" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="614ffac2-feba-4310-9353-60c914b8a3b8"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY30}.doubleValue()==0?"":$F{NGAY30}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="712" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="f7bb64d0-3eae-475c-8f77-3639d1a330bd"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY28}.doubleValue()==0?"":$F{NGAY28}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="730" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="679393bf-7813-45d5-81e9-b87e47d7ce1b"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY29}.doubleValue()==0?"":$F{NGAY29}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="766" y="0" width="18" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c90dc2e0-6ebf-4a6f-a528-e718eae67b96"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY31}.doubleValue()==0?"":$F{NGAY31}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="784" y="0" width="52" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="6c0aa75e-ed01-4814-ba30-4a7d431f4a88"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY1}+$F{NGAY2}+$F{NGAY3}+$F{NGAY4}+$F{NGAY5}+$F{NGAY6}+$F{NGAY7}+$F{NGAY8}+$F{NGAY9}+$F{NGAY10}+$F{NGAY11}+$F{NGAY12}
+$F{NGAY13}+$F{NGAY14}+$F{NGAY15}+$F{NGAY16}+$F{NGAY17}+$F{NGAY18}+$F{NGAY19}+$F{NGAY20}
+$F{NGAY21}+$F{NGAY22}+$F{NGAY23}+$F{NGAY24}+$F{NGAY25}+$F{NGAY26}+$F{NGAY27}+$F{NGAY28}+$F{NGAY29}
+$F{NGAY30}+$F{NGAY31}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="150" y="0" width="76" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="27dcc854-f9a4-4378-8d84-7cf1aed2fc1a"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPBAC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="23" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="7285cfc2-80f7-467c-9c5a-e783ba9ec7bf"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
