/*
 Mã màn hình  : VPI01T003
 File mã nguồn : VPI01T003_danhsachphieuthu.js
 <PERSON><PERSON><PERSON> đích  : Xem danh sách phiếu thu
 Tham số vào :
 <PERSON><PERSON><PERSON><PERSON> lập tr<PERSON>nh	 <PERSON> cập nh<PERSON>t  	<PERSON><PERSON> ch<PERSON>hn				 5/10/2016		comment
*/
var _THUTIEN = 1;
var _TAMUNG = 3;
var _HOANUNG = 2;
var _THUTHEM = 6;
var _BHBL = 13; // L2PT-26947
function danhmucList() {
	var _hospital_id;
	var _phong_id = -1;
	var _khoa_id = -1;
	var flagLoading = false;
	// L2PT-1852 start
	var dsCH = new Object();
	var _config_hddt = [];
	// L2PT-1852 end
	var _param = [];
	var _gridId = "grdDanhSachPT";
	// L2PT-1852 thêm cột: TRANGTHAI_HDDT,SYNC_FLAG,INVOICES_PATTERN,INVOICES_SERIAL,INVOICES_NUMBER,BENHNHANID,MAHOSOBENHAN
	// L2PT-28078 thêm cột: BANTHUOC
	var _gridHeader = " ,ICON,20,0,ns,l;" + "ID benhnhan,BENHNHANID,100,0,t,l;" + "ID phiếu thu,PHIEUTHUID,0,0,t,l;" + "Đã duyệt,DADUYET,0,0,t,l;" + "ID loại phiếu thu,LOAIPHIEUTHU,0,0,t,l;"
			+ "id,NHOMPHIEUTHUID,0,0,t,l;" + "Đã hủy phiếu,DAHUYPHIEU,0,0,t,l;" + "Mã bệnh án,MAHOSOBENHAN,60,0,f,l;" + "Mã phiếu,MAPHIEUTHU,60,0,f,l;" + "Mã số,MANHOMPHIEUTHU,80,0,f,l;"
			+ "Tên bệnh nhân,TENBENHNHAN,130,0,f,l;" + "Số tiền,DATRA,80,number,f,l;" + "Trạng thái,TRANGTHAI,80,0,f,l;" + "Trạng thái HDDT,TRANGTHAI_HDDT,80,0,f,l,0;"
			+ "SYNC_FLAG,SYNC_FLAG,0,0,t,l,0;" + "PATTERN,INVOICES_PATTERN,80,0,f,l,0;" + "SERIAL,INVOICES_SERIAL,80,0,f,l,0;" + "Số HĐĐT,INVOICES_NUMBER,80,0,f,l,0;" + "Bán thuốc,BANTHUOC,0,0,t,l;"
			+ "Ngày duyệt,NGAYDUYET,80,0,f,c;" + "Miễn giảm,MIENGIAM,60,number,f,l;" + "Lý do,LYDOMIENGIAM,100,0,f,l;" + "Ngày lập,NGAYTHU,100,0,f,c;" + "Người lập,NGUOIDUNGID,130,0,f,l";
	var _gridSQL = "VPI01T003.01.RG";
	var idPTArr = [];
	var luyke = [];
	var VPI_TYPE_EXPORT_PINCT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_TYPE_EXPORT_PINCT'); // L2PT-19748
	var VPI_HIENTHIVP_QTI = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HIENTHIVP_QTI'); // L2PT-20093
	var fConfig = new Object(); // L2PT-56896
	var that = this;
	this.load = doLoad;
	var tungay_old = ""; // L2PT-131223
	var denngay_old = ""; // L2PT-131223
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		var _options = $.extend({}, _opts);
		var uuid = _options._uuid;
		var _param = _options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_phong_id = _param[4];
		_khoa_id = _param[5];
		_initControl();
		_bindEvent();
		_hospital_id = _hosp_id;
	}
	function _initControl() {
		// L2PT-56896 start
		var str_ch = 'VPI_DSPT_SOTIEN_LOCMD;VPI_DSPT_CBO_NGUOITHU'; // L2PT-55208
		var arrFConfig = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		if (Array.isArray(arrFConfig) && arrFConfig.length > 0) {
			fConfig = arrFConfig[0];
		}
		$("#cboSOTIEN").val(fConfig.VPI_DSPT_SOTIEN_LOCMD);
		// L2PT-56896 end
		// L2PT-43586 start
		var result1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("THUTHEO_DOITUONG", '$');
		var str1 = '';
		var ds_doituongbnid = "";
		var doituongbnid;
		var tatca = false;
		if (result1.length > 0) {
			for (var j = 0; j < result1.length; j++) {
				doituongbnid = result1[j].DTBNID;
				if (doituongbnid == 100) {
					tatca = true;
				} else {
					ds_doituongbnid += doituongbnid + ",";
				}
				str1 = str1 + "<option value=" + result1[j].DTBNID + ">" + result1[j].TEN_DTBN + "</option>";
			}
			if (!tatca && ds_doituongbnid.length > 0) {
				ds_doituongbnid = ds_doituongbnid.slice(0, -1);
				str1 = "<option value=" + ds_doituongbnid + ">" + "-- Chọn --" + "</option>" + str1;
			}
		}
		$('#cboDTBNID').html(str1);
		// L2PT-43586 end
		// L2PT-20093 start
		if (VPI_HIENTHIVP_QTI == '1') {
			$("input[type=radio][name=optradio][value=" + 1 + "]").attr('checked', 'checked');
			$("#cboTRANGTHAI_PHIEU").val("0");
		}
		// L2PT-20093 end
		var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
		var from = VPI_HIENTHIVP_QTI == '1' ? _sys_date : "01/" + _sys_date.substring(3); // L2PT-20093
		// L2PT-107050 start
		from += ' 00:00:00';
		_sys_date += ' 23:59:59';
		// L2PT-107050 end
		$('#txtTUNGAY').val(from);
		$('#txtDENNGAY').val(_sys_date);
		GridUtil.init(_gridId, "100%", "452", "", true, _gridHeader, false, {
			rowNum : VPI_HIENTHIVP_QTI == '1' ? 1000 : 100, // L2PT-20093
			rowList : [ 100, 200, 300, 500, 1000 ]
		});
		// L2PT-76736
		GridUtil.addExcelButton(_gridId, 'Xuất excel', true); //L2PT-76929
		_sql_par_94 = [ {
			"name" : "[0]",
			value : 94
		}, {
			"name" : "[1]",
			value : "in"
		}, {
			"name" : "[2]",
			value : "1,2,3"
		} ];
		ComboUtil.getComboTag("cboLOAISO", 'COM.TRANGTHAI', _sql_par_94, "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', ''); // L2PT-30898
		_sql_par_95 = [ {
			"name" : "[0]",
			value : 95
		}, {
			"name" : "[1]",
			value : "not in"
		}, {
			"name" : "[2]",
			value : "-1"
		} ];
		ComboUtil.getComboTag("cboTRANGTHAI", 'COM.TRANGTHAI', _sql_par_95, "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', ''); // L2PT-30898
		_sql_par_11 = [ {
			"name" : "[0]",
			value : 11
		}, {
			"name" : "[1]",
			value : "in"
		}, {
			"name" : "[2]",
			value : "1,2,3"
		} ];
		ComboUtil.getComboTag("cboLOAIPHIEUTHU", 'COM.TRANGTHAI', _sql_par_11, "", "", 'sql', '', '');// L2PT-30898
		_sql_par_spt = [ {
			"name" : "[0]",
			value : _phong_id
		} ];
		ComboUtil.getComboTag("cboQUYENSO", 'VPI02T001.DS.SPT', _sql_par_spt, "", {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', ''); // L2PT-30898
		// L2PT-30898 start
		// L2PT-107050 :  -> VPI02G004.02.01
		// L2PT-112004 start
		var sql_cobo_nguoithu = "VPI02G004.02";
		if (fConfig.VPI_DSPT_CBO_NGUOITHU == '1') {
			sql_cobo_nguoithu = "VPI02G004.02.01";
		}
		ComboUtil.getComboTag("cboNGUOITHU", sql_cobo_nguoithu, [], "", "", 'sql', '', '');
		// L2PT-112004 end
		// L2PT-30898 end
		// L2PT-108434 start
		$('#cboNGUOITHU').SumoSelect({
			search : true,
			searchText : 'Tìm kiếm',
			okCancelInMulti : true,
			selectAll : true
		});
		$('#cboNGUOITHU')[0].sumo.reload();
		$('#cboNGUOITHU')[0].sumo.selectAll();
		// L2PT-108434 end
		// L2PT-102277 start
		ComboUtil.getComboTag("cboHINHTHUCTHANHTOAN", "VPI.DM.HTTT", [], "", {
			text : "--- Tất cả ---",
			value : -1
		}, "sp", "", "");
		// L2PT-102277 end
		// L2PT-1852 start
		// L2PT-17249 thêm cấu hình: VPI_MAKHOA_TCKT;VPI_MAKHOA_DUOC
		dsCH = layDsCH(_hospital_id);
		if (dsCH.VPI_N_TKHD == '1') {
			var objT = new Object();
			objT.TNAME = 'TAIKHOAN_HOADON_DIENTU';
			//objT.TMODE = '0';
			//objT.TROW = '1';
			objT.TSTS = 'STATUS';
			objT.THID = 'CSYTID';
			var input = JSON.stringify(objT);
			_config_hddt = jsonrpc.AjaxJson.ajaxCALL_SP_O('T.GET.DATA', input);
		}
		// L2PT-1852 end
		// L2PT-17249 start
		var _makhoa = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", [ {
			"name" : "[0]",
			"value" : _khoa_id
		} ]);
		if (_makhoa == dsCH.VPI_MAKHOA_TCKT) {
			$("#cboLOAITHU").val("0");
		} else if (_makhoa == dsCH.VPI_MAKHOA_DUOC) {
			$("#cboLOAITHU").val("1");
		}
		// L2PT-17249 end
		$('#txtTIMKIEM').focus();
	}
	function _bindEvent() {
		$('#txtTIMKIEM').keyup(function() {
//			if(input.length>2) alert(input);
//			$("#gs_MANHOMPHIEUTHU").val(input);
//			$("#"+_gridId)[0].triggerToolbar();
			loadGridData();
		});
		// L2PT-131223 start
		$('#txtTUNGAY').change(function() {
			var tungay_new = $('#txtTUNGAY').val();
			if (tungay_old != "" && tungay_old != tungay_new) {
				loadGridData();
			}
			tungay_old = tungay_new;
		});
		$('#txtDENNGAY').change(function() {
			var denngay_new = $('#txtDENNGAY').val();
			if (denngay_old != "" && denngay_old != denngay_new) {
				loadGridData();
			}
			denngay_old = denngay_new;
		});
		// L2PT-131223 end
		$('#cboLOAISO').change(function() {
			loadGridData();
		});
		$('#cboTRANGTHAI').change(function() {
			loadGridData();
		});
		$('#cboLOAIPHIEUTHU').change(function() {
			loadGridData();
		});
		$('#cboQUYENSO').change(function() {
			loadGridData();
		});
		// L2PT-27841 start 
		$('#cboTRANGTHAI_HDDT').change(function() {
			loadGridData();
		});
		// L2PT-27841 end
		$('#cboTRANGTHAI_PHIEU').change(function() {
			loadGridData();
		});
		$('#btnRefresh').click(function() {
			$('#txtTIMKIEM').val("");
			loadGridData();
		});
		$('#btnGoDuyet').click(function() {
			duyetphieu(0);
			loadGridData();
		});
		$('#btnDuyet').click(function() {
			duyetphieu(1);
			loadGridData();
		});
		$('input[type=radio][name=optradio]').change(function() {
			loadGridData();
		});
		// L2PT-13782 start
		$('#cboLOAITHU').change(function() {
			loadGridData();
		});
		// L2PT-13782 end
		// xu ly in hoa don
		$("#btnIn").click(function() {
			var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
			if (_selRowIds.length == 0)
				return false;
			var _selRowId = _selRowIds[_selRowIds.length - 1];
			var loaiphieuthu = $("#" + _gridId).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHU');
			var _phieuthuid = $("#" + _gridId).jqGrid('getCell', _selRowId, 'PHIEUTHUID');
			_sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : _phieuthuid
			});
			_sql_par.push({
				"name" : "[1]",
				value : _phieuthuid
			});
			var _is_dct = jsonrpc.AjaxJson.getOneValue("VPI01T001.18", _sql_par);
			// L2PT-28078 start
			var _banthuoc = $("#" + _gridId).jqGrid('getCell', _selRowId, 'BANTHUOC');
			if (_banthuoc == 1) {
				inHoaDonThuoc(_phieuthuid, loaiphieuthu);
			} else
			// L2PT-28078 end
			if (loaiphieuthu == _THUTIEN) {
				if (_is_dct == 0)
					InHoadonVP(_phieuthuid, 'NGT036_HOADONGTGT_A4', 'NGT037_BANGKEKEMHDGTGT_A4');
				else {
					InHoadonVP(_phieuthuid, 'NGT039_HOADONDCT_A4', 'NGT037_BANGKEKEMHDGTGT_A4');
				}
			} else if (loaiphieuthu == _TAMUNG) {
				InHoadonVP(_phieuthuid, 'NGT034_PHIEUTAMUNG_A4');
			} else if (loaiphieuthu == _THUTHEM) {
				InHoadonVP(_phieuthuid, 'NGT034_PHIEUTAMUNG_A4');
			} else if (loaiphieuthu == _HOANUNG) {
				InHoadonVP(_phieuthuid, 'NGT038_PHIEUHOANUNG_A4');
			}
		});
		// L2PT-28078 start
		function inHoaDonThuoc(_phieuthuid, _loaiphieuthuid) {
			var par = [];
			par.push({
				name : _loaiphieuthuid == 1 ? 'i_phieuthuid' : 'phieuthuid',
				type : 'String',
				value : _phieuthuid.toString()
			});
			openReport('window', _loaiphieuthuid == 1 ? 'HOADON_BANTHUOC_KHACHLE' : 'PHIEUTHUTIEN_NHATHUOC_A4', 'pdf', par);
			// in hd ct
			if (HIS_IN_HOADONCHITIET == 1) {
				var par_ct = [];
				par_ct.push({
					name : 'phieuthuid',
					type : 'String',
					value : _phieuthuid.toString()
				});
				openReport('window', 'NGT037_BANGKEKEMHDGTGT_A4', 'pdf', par_ct);
			}
			// in hd ct
		}
		// L2PT-28078 end
		// L2PT-1852 start
		$("#btnXemHDDT").click(function() {
			var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
			if (_selRowIds.length == 0) {
				DlgUtil.showMsg("Chưa chọn hóa đơn");
				return;
			} else if (_selRowIds.length > 1) {
				DlgUtil.showMsg("Chỉ được chọn 1 hóa đơn");
				return;
			}
			for (var i = 0; i < _selRowIds.length; i++) {
				var phieuthuid_hddt = $("#" + _gridId).jqGrid('getCell', _selRowIds[i], 'PHIEUTHUID');
				var _benhnhanid = $("#" + _gridId).jqGrid('getCell', _selRowIds[i], 'BENHNHANID');
				_inHDDTTheoPhieu(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid);
			}
		});
		// L2PT-1852 end
		// L2PT-1852 start
		$("#btnGuiHDDT").click(function() {
			var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
			if (_selRowIds.length == 0) {
				DlgUtil.showMsg("Chưa chọn hóa đơn");
				return;
			}
			var dsPhieu = [];
			for (var i = 0; i < _selRowIds.length; i++) {
				var rowData = $("#" + _gridId).jqGrid('getRowData', _selRowIds[i]);
				if (rowData.LOAIPHIEUTHU != _THUTIEN && rowData.LOAIPHIEUTHU != _BHBL) { // L2PT-26947 cho phép gửi hđ BHBL
					DlgUtil.showMsg("Có phiếu không phải là hóa đơn");
					return;
				}
				if (rowData.DATRA == 0) {
					DlgUtil.showMsg("Có hóa đơn không đồng");
					return;
				}
				if (rowData.SYNC_FLAG == 1) {
					DlgUtil.showMsg("Có hóa đơn đã gửi");
					return;
				}
				if (rowData.DAHUYPHIEU == 1) {
					DlgUtil.showMsg("Có phiếu thu đã hủy");
					return;
				}
				dsPhieu.push(rowData);
			}
			// L2PT-24425 start: gui hddt don thuoc
			var loaiThu = $("#cboLOAITHU").val();
			var mode = 1;
			if (loaiThu == 0) {
				mode = 0
			} else if (loaiThu == 1) {
				mode = 3
			}
			// L2PT-24425 end
			if (dsCH.VPI_DS_MSKH && dsCH.VPI_DS_MSKH != 0) {
				var myVar = new Object();
				myVar.arrPhieu = dsPhieu;
				myVar.dsCH = dsCH;
				myVar._config_hddt = _config_hddt;
				myVar.mode = mode; // L2PT-24425 start: gửi hddt đơn thuốc
				DlgUtil.buildPopupUrl("dlgChonMSKH", "dlgChonMSKHID", "manager.jsp?func=../vienphi/sendInv", myVar, "Chọn mẫu số/ ký hiệu", 600, 200);
				DlgUtil.open("dlgChonMSKH");
			} else {
				var msgSendInv = guiHDDTTheoDSPhieu(dsPhieu, dsCH, _config_hddt, mode); // L2PT-24425 start: gửi hddt đơn thuốc
				DlgUtil.showMsg(msgSendInv);
				loadGridData();
			}
		});
		EventUtil.setEvent("okDlgChonMSKH", function(e) {
			DlgUtil.showMsg(e.msg);
			loadGridData();
		});
		// L2PT-1852 end
		// L2PT-12841 start
		$("#btnXuatHDDT").click(function() {
			XMLHDDT(0)
		});
		// L2PT-12841 end
		// L2PT-13782 start
		$("#btnXuatHDDTFILE").click(function() {
			XMLHDDT(1)
		});
		// L2PT-13782 end
		GridUtil.setGridParam(_gridId, {
			gridComplete : function(id) {},
			onSelectRow : function(id) {
				checkSel()
			},
			onSelectAll : function(ids, status) {
				checkSel()
			},
		});
		// L2PT-23632 start
		$("#btnCapNhatTTBN_HDDT").click(function() {
			var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
			if (_selRowIds.length == 0) {
				DlgUtil.showMsg("Chưa chọn hóa đơn");
				return;
			}
			if (_selRowIds.length > 1) {
				DlgUtil.showMsg("Chỉ được chọn 1 hóa đơn");
				return;
			}
			var rowDataSel = $("#" + _gridId).jqGrid('getRowData', _selRowIds[0]);
			var url = "manager.jsp?func=../vienphi/VPI01T026_capnhatTTBN_HDDT";
			var popup = DlgUtil.buildPopupUrl("dlgNhapTTBN", "divDlgNhapTTBN", url, rowDataSel, "Nhập thông tin BN", 700, 350);
			popup.open("dlgCapNhatPhieuThu");
		});
		// thiết lập sự kiện khi nhấn nút ở popup con
		EventUtil.setEvent("capnhat_ttbn_thanhcong", function(e) {
			DlgUtil.close("dlgNhapTTBN");
		});
		EventUtil.setEvent("capnhat_ttbn_dong", function(e) {
			DlgUtil.close("dlgNhapTTBN");
		});
		// L2PT-23632 end
	}
	// L2PT-13782 start
	function XMLHDDT(fileType) {
		var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
		if (_selRowIds.length == 0) {
			DlgUtil.showMsg("Chưa chọn hóa đơn");
			return;
		}
		if (fileType == 1 && _selRowIds.length > 1) {
			DlgUtil.showMsg("Chỉ được chọn 1 hóa đơn");
			return;
		}
		var dsPhieu = [];
		for (var i = 0; i < _selRowIds.length; i++) {
			var rowData = $("#" + _gridId).jqGrid('getRowData', _selRowIds[i]);
			if (rowData.LOAIPHIEUTHU != _THUTIEN && rowData.LOAIPHIEUTHU != _BHBL) { // L2PT-26947 cho phép gửi hđ BHBL
				DlgUtil.showMsg("Có phiếu không phải là hóa đơn");
				return;
			}
			if (rowData.DATRA == 0) {
				DlgUtil.showMsg("Có hóa đơn không đồng");
				return;
			}
			if (rowData.DAHUYPHIEU == 1) {
				DlgUtil.showMsg("Có phiếu thu đã hủy");
				return;
			}
			dsPhieu.push(rowData);
		}
		// L2PT-13782 start: XML bán thuốc
		var loaiThu = $("#cboLOAITHU").val();
		var mode = 1;
		if (loaiThu == 0) {
			mode = 1
		} else if (loaiThu == 1) {
			mode = 2
		}
		// L2PT-16575 start
		downloadXML(dsPhieu, dsCH, _config_hddt, mode, fileType);
		// L2PT-16575 end
	}
	// L2PT-13782 end
	// L2PT-1852 start
	function checkSel() {
		var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
		GridUtil.unmarkAll(_gridId);
		for (var i = 0; i < _selRowIds.length; i++) {
			GridUtil.markRow(_gridId, _selRowIds[i]);
		}
		cal(_selRowIds);
		if (_selRowIds.length == 0) {
			$("#btnIn").attr('disabled', true);
			$("#btnDuyet").attr('disabled', true);
			// L2PT-1852 start
			$("#btnGuiHDDT").attr('disabled', true);
			$("#btnXemHDDT").attr('disabled', true);
			$("#btnXuatHDDT").attr('disabled', true); //L2PT-12841
			$("#btnXuatHDDTFILE").attr('disabled', true); //L2PT-13782
			// L2PT-1852 end
			$("#btnCapNhatTTBN_HDDT").attr('disabled', true); //L2PT-23632
			$("#btnGoDuyet").attr('disabled', true);
		} else {
			$("#btnIn").attr('disabled', false);
			$("#btnDuyet").attr('disabled', false);
			// L2PT-1852 start
			$("#btnGuiHDDT").attr('disabled', false);
			$("#btnXemHDDT").attr('disabled', false);
			$("#btnXuatHDDT").attr('disabled', false); //L2PT-12841
			$("#btnXuatHDDTFILE").attr('disabled', false); //L2PT-13782
			// L2PT-1852 end
			// L2PT-23632 start
			if (_selRowIds.length == 1) {
				$("#btnCapNhatTTBN_HDDT").attr('disabled', false);
			} else {
				$("#btnCapNhatTTBN_HDDT").attr('disabled', true);
			}
			// L2PT-23632 end
			$("#btnGoDuyet").attr('disabled', false);
		}
	}
	// L2PT-1852 end
	// in hoa don vien phi
	function InHoadonVP(_phieuthuid, _report_code, _report_code_chitiet, _dahuyphieu) {
		var par = [];
		par.push({
			name : 'phieuthuid',
			type : 'String',
			value : _phieuthuid.toString()
		});
		var typeExport = "pdf";//$("#sltInBieuMau").val();
		if (dsCH.VPI_KIEUIN_HOADON == 1)
			CommonUtil.inNhieuPhieu('window', _report_code, typeExport, par, 'ifmBill');
		else
			CommonUtil.openReportGet('window', _report_code, typeExport, par, 'ifmBill');
		if (dsCH.HIS_IN_HOADONCHITIET == 0 || !_report_code_chitiet || (_dahuyphieu && _dahuyphieu == 1))
			return;
		if (dsCH.VPI_KIEUIN_HOADON == 1)
			CommonUtil.inNhieuPhieu('window', _report_code_chitiet, typeExport, par, 'ifmDetail');
		else
			CommonUtil.openReportGet('window', _report_code_chitiet, typeExport, par, 'ifmDetail');
	}
	function loadGridData() {
		if (flagLoading)
			return;
		// L2PT-131223 start
		if(!gioihan_thoigian("VPI_SONGAY_TIMKIEM", "txtTUNGAY", "txtDENNGAY" )){
			return false;
		}
		// L2PT-131223 end
		var _lookup_sql = "";
		_lookup_sql = _gridSQL;
		var _tu = $("#txtTUNGAY").val();
		var _den = $("#txtDENNGAY").val();
		// L2PT-13782 start: set form to object
		var _tungay = stringToDate(_tu);
		var _denngay = stringToDate(_den);
		var _trangthai = $("#cboTRANGTHAI").val();
		var _trangthai_hddt = $("#cboTRANGTHAI_HDDT").val();
		var _trangthai_phieu = $("#cboTRANGTHAI_PHIEU").val(); // L2PT-1852
		var _loaiso = $("#cboLOAISO").val();
		var _key = $("#txtTIMKIEM").val().trim();
		// L2PT-13782 end
		var _loaiphieu = $("#cboLOAIPHIEUTHU").val();
		var _quyenso = $("#cboQUYENSO").val();
		var radioValue = $("#cboLOAITIMKIEM").val(); // L2PT-129166
		// L2PT-30898 start
		/*if (!_quyenso || !_trangthai || !_loaiso || !_loaiphieu) {
			return;
		}*/
		// L2PT-30898 end
		if (_tungay && _denngay) {
			if (_tungay > _denngay) {
				onFocus([ "txtTUNGAY", "txtDENNGAY" ]);
				DlgUtil.showMsg("Trường từ ngày không được lớn hơn trường đến ngày");
				return false;
			}
		}
		// L2PT-107050 start
		if (_tu && _tu.length == 10)
			_tu += " 00:00:00";
		if (_den && _den.length == 10)
			_den += " 23:59:59";
		// L2PT-107050 end
		luyke = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI03.LUYKE', _phong_id + '$' + _quyenso + '$' + _loaiphieu + '$' + _den);
		console.log(luyke);
		if (luyke && luyke.length > 0) {
			luyke_dis = vienphi_tinhtien.convertObjToCurrency(luyke[0]);
			FormUtil.setObjectToForm('divLUYKE', '', luyke_dis);
		}
		// L2PT-27841 start 
		var objTimKiem = new Object();
		FormUtil.setFormToObject('divSearch', '', objTimKiem); // L2PT-13782
		// L2PT-26949 start
		/*objTimKiem.TUNGAY = _tu + "";
		objTimKiem.DENNGAY = _den + "";
		// L2PT-13782 start
		
		objTimKiem.TUKHOA = _key + "";
		objTimKiem.TRANGTHAI = _trangthai + "";
		objTimKiem.TRANGTHAI_HDDT = _trangthai_hddt + "";
		objTimKiem.TRANGTHAI_PHIEU = _trangthai_phieu + "";// L2PT-1852
		objTimKiem.LOAISO = _loaiso + "";
		objTimKiem.LOAIPHIEU = _loaiphieu + "";
		objTimKiem.QUYENSO = _quyenso + "";
		
		// L2PT-13782 end
		objTimKiem.LOAITIMKIEM = radioValue + "";*/
		objTimKiem.PHONGID = _phong_id + "";
		// L2PT-26949 end
		// L2PT-108434 start
		objTimKiem.NGUOITHU = $("#cboNGUOITHU").val().join();
		// L2PT-108434 end
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			"value" : JSON.stringify(objTimKiem)
		});
		// L2PT-27841 end
		_sql_par = RSUtil.setSysParam(_sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridId, _lookup_sql, _sql_par, function() {
			var grid = $("#" + _gridId);
			var ids = grid.getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var dahuyphieu = grid.jqGrid('getCell', id, 'DAHUYPHIEU');
				var daduyet = grid.jqGrid('getCell', id, 'DADUYET');
//		    	if(dahuyphieu==1) {
////		    		GridUtil.markRow(_gridId,id);
//		    		var _icon = '';
//		    		_icon = '<center><img src="'+ _opts.imgPath[0] +'" width="15px"></center>';
//		    		$("#"+_gridId).jqGrid ('setCell', id, 2, _icon);
//		    	}
				if (daduyet == 1) {
//		    		GridUtil.markRow(_gridId,id);
					var _icon = '';
					_icon = '<center><img src="' + _opts.imgPath[1] + '" width="15px"></center>';
					$("#" + _gridId).jqGrid('setCell', id, 2, _icon);
				}
			}
			var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
			if (_selRowIds.length == 0) {
				$("#btnIn").attr('disabled', true);
				$("#btnDuyet").attr('disabled', true);
				$("#btnGoDuyet").attr('disabled', true);
			} else {
				$("#btnIn").attr('disabled', false);
				$("#btnDuyet").attr('disabled', false);
				$("#btnGoDuyet").attr('disabled', false);
			}
		});
	}
	function cal(_selRowIds) {
		var sum = 0;
		for (var i = 0; i < _selRowIds.length; i++) {
			var daduyet = $("#" + _gridId).jqGrid('getCell', _selRowIds[i], 'DADUYET');
			var sotien = $("#" + _gridId).jqGrid('getCell', _selRowIds[i], 'DATRA');
			if (daduyet == 0) {
				sum += parseFloat(sotien);
			}
		}
		$("#lblTONGNGAY").text(vienphi_tinhtien.convertNumToCurrency(parseFloat(luyke[0].TONGNGAY) + sum));
		$("#lblLUYKE").text(vienphi_tinhtien.convertNumToCurrency(parseFloat(luyke[0].LUYKE) + sum));
	}
	function duyetphieu(val) {
		var idPTs = "";
		var _selRowIds = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
		for (var i = 0; i < _selRowIds.length; i++) {
			var _phieuthuid = $("#" + _gridId).jqGrid('getCell', _selRowIds[i], 'PHIEUTHUID');
			idPTs += _phieuthuid + ",";
		}
		idPTs = idPTs.slice(0, -1);
		if (idPTs && _selRowIds.length > 0) {
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI03.KHOA.PHIEUTHU', idPTs + '$' + val);
			if (result == '0') {
				DlgUtil.showMsg("Bạn không có quyền gỡ duyệt");
				return;
			}
			DlgUtil.showMsg(result);
		}
	}
	function onFocus(fields) {
		var type = typeof fields;
		if (type.toLowerCase() == "string") {
			$("#" + id).focus();
			$("#" + id).css('background-color', 'yellow');
		} else if (type.toLowerCase() == "object") {
			var id = "";
			for (var i = 0; i < fields.length; i++) {
				id = fields[i];
				$("#" + id).css('background-color', 'yellow');
			}
			$("#" + id).focus();
		}
	}
	function stringToDate(date) {
		var parts = date.split("/");
		return new Date(parts[2], parts[1] - 1, parts[0]);
	}
	//in chi tiet vien phi
	$("#btnInctvp").click(function() {
		var _tu = $("#txtTUNGAY").val();
		var _den = $("#txtDENNGAY").val();
		var _trangthai = $("#cboTRANGTHAI").val();
		var _loaiso = $("#cboLOAISO").val();
		var _loaiphieu = $("#cboLOAIPHIEUTHU").val();
		var _quyenso = $("#cboQUYENSO").val();
		var radioValue = $("#cboLOAITIMKIEM").val(); // L2PT-129166
		var _key = $("#txtTIMKIEM").val().trim();
		var ids = $("#" + _gridId).jqGrid('getGridParam', 'selarrrow');
		// L2PT-19748 start
		var arr_row = [];
		for (var i = 0; i < ids.length; i++) {
			var _phieuthuid = $("#" + _gridId).jqGrid('getCell', ids[i], 'PHIEUTHUID');
			arr_row.push(_phieuthuid);
		}
		// L2PT-19748 end
		var par = [ {
			name : 'dt_ngay',
			type : 'String',
			value : _tu
		}, {
			name : 'dt_denngay',
			type : 'String',
			value : _den
		}, {
			name : 'i_key',
			type : 'String',
			value : _key
		}, {
			name : 'i_trangthai',
			type : 'String',
			value : _trangthai
		}, {
			name : 'i_loaiso',
			type : 'String',
			value : _loaiso
		}, {
			name : 'i_loaiphieu',
			type : 'String',
			value : _loaiphieu
		}, {
			name : 'i_quyenso',
			type : 'String',
			value : _quyenso
		}, {
			name : 'i_phongid',
			type : 'String',
			value : _phong_id
		}, {
			name : 'i_type',
			type : 'String',
			value : radioValue
		}, {
			name : 'i_sum_luyke',
			type : 'String',
			value : luyke[0].LUYKE
		}, {
			name : 'i_sum_hientai',
			type : 'String',
			value : luyke[0].TONGNGAY
		}, {
			name : 'i_sum_ngaytruoc',
			type : 'String',
			value : luyke[0].CACNGAY
		},
		// L2PT-19748 start
		{
			name : 'i_list_phieuthuid',
			type : 'String',
			value : arr_row.length == 0 ? '-1' : arr_row.join(',')
		},
		// L2PT-19748 end
		];
		// L2PT-19748 start
		var typeExport = "pdf";
		_report_code = "NGUYENTRAI_DSPHIEUTHUSOCHITIETTHU_A4_902";
		if (VPI_TYPE_EXPORT_PINCT == '1') {
			typeExport = "xlsx";
			CommonUtil.inPhieu('window', _report_code, typeExport, par, _report_code + '.' + typeExport);
		} else {
			typeExport = "pdf";
			CommonUtil.openReportGet('window', _report_code, typeExport, par);
		}
		// L2PT-19748 end
	});
	//in tong hop hoa don vien phi
	$("#btnInthvp").click(function() {
		var _tu = $("#txtTUNGAY").val();
		var _den = $("#txtDENNGAY").val();
		var _trangthai = $("#cboTRANGTHAI").val();
		var _loaiso = $("#cboLOAISO").val();
		var _loaiphieu = $("#cboLOAIPHIEUTHU").val();
		var _quyenso = $("#cboQUYENSO").val();
		var radioValue = $("#cboLOAITIMKIEM").val(); // L2PT-129166
		var _key = $("#txtTIMKIEM").val().trim();
		var par = [ {
			name : 'dt_ngay',
			type : 'String',
			value : _tu
		}, {
			name : 'dt_denngay',
			type : 'String',
			value : _den
		}, {
			name : 'i_key',
			type : 'String',
			value : _key
		}, {
			name : 'i_trangthai',
			type : 'String',
			value : _trangthai
		}, {
			name : 'i_loaiso',
			type : 'String',
			value : _loaiso
		}, {
			name : 'i_loaiphieu',
			type : 'String',
			value : _loaiphieu
		}, {
			name : 'i_quyenso',
			type : 'String',
			value : _quyenso
		}, {
			name : 'i_phongid',
			type : 'String',
			value : _phong_id
		}, {
			name : 'i_type',
			type : 'String',
			value : radioValue
		}, ];
		CommonUtil.openReportGet('window', "NGUYENTRAI_DSPHIEUTHUNGAY_LOCHOADON_NGT_A4_902", "pdf", par);
	});
}
