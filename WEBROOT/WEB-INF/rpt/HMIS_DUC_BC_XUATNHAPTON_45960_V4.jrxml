<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="HMIS_DUC_BC_XUATNHAPTON_45960" language="groovy" pageWidth="1200" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1160" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="137d06cb-f02e-4189-bd9e-bf185d153424">
	<property name="ireport.zoom" value="1.3636363636363635"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="4"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="START_DATE" class="java.sql.Timestamp"/>
	<parameter name="END_DATE" class="java.sql.Timestamp"/>
	<parameter name="KEY_CSYT_TUYEN4" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="MA_DK_KCB_TUYEN4" class="java.lang.String"/>
	<parameter name="LOAI_VT" class="java.lang.String"/>
	<parameter name="urid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ORACLE_REF_CURSOR" class="java.sql.ResultSet"/>
	<queryString language="plsql">
		<![CDATA[{call hmis_xuatnhapton_45960_v4($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{urid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="TEN_KHO" class="java.lang.String"/>
	<field name="NHOM_BAOCAO" class="java.lang.String"/>
	<field name="MA_THUOC" class="java.lang.String"/>
	<field name="TEN_THUOC" class="java.lang.String"/>
	<field name="HOAT_CHAT" class="java.lang.String"/>
	<field name="DON_VI" class="java.lang.String"/>
	<field name="DON_GIA" class="java.math.BigDecimal"/>
	<field name="SL_DAUKY" class="java.math.BigDecimal"/>
	<field name="TT_DAUKY" class="java.math.BigDecimal"/>
	<field name="SL_NHAP" class="java.math.BigDecimal"/>
	<field name="TT_NHAP" class="java.math.BigDecimal"/>
	<field name="SL_XUAT" class="java.math.BigDecimal"/>
	<field name="TT_XUAT" class="java.math.BigDecimal"/>
	<field name="SL_CUOIKY" class="java.math.BigDecimal"/>
	<field name="TT_TONCUOI" class="java.math.BigDecimal"/>
	<field name="NGUOI_LAP" class="java.lang.String"/>
	<field name="KE_TOAN" class="java.lang.String"/>
	<field name="THU_TRUONG" class="java.lang.String"/>
	<field name="TEN_CSYT_TUYEN4" class="java.lang.String"/>
	<field name="TEN_NGUONDUOC" class="java.lang.String"/>
	<field name="NGUON_DUOC" class="java.lang.String"/>
	<field name="NHOM_THUOC" class="java.lang.String"/>
	<field name="SL_HOANTRA" class="java.math.BigDecimal"/>
	<field name="TT_HOANTRA" class="java.math.BigDecimal"/>
	<field name="SOLOHANDUNG" class="java.lang.String"/>
	<variable name="SL_DAUKY_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SL_DAUKY}]]></variableExpression>
	</variable>
	<variable name="TT_DAUKY_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_DAUKY}]]></variableExpression>
	</variable>
	<variable name="SL_NHAP_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SL_NHAP}]]></variableExpression>
	</variable>
	<variable name="TT_NHAP_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_NHAP}]]></variableExpression>
	</variable>
	<variable name="SL_XUAT_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SL_XUAT}]]></variableExpression>
	</variable>
	<variable name="TT_XUAT_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_XUAT}]]></variableExpression>
	</variable>
	<variable name="SL_CUOIKY_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SL_CUOIKY}]]></variableExpression>
	</variable>
	<variable name="TT_TONCUOI_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_TONCUOI}]]></variableExpression>
	</variable>
	<variable name="nguon_dauky" class="java.math.BigDecimal" resetType="Group" resetGroup="GrHeader_NGUON_DUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_DAUKY}]]></variableExpression>
	</variable>
	<variable name="nguon_nhaptrongky" class="java.math.BigDecimal" resetType="Group" resetGroup="GrHeader_NGUON_DUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_NHAP}]]></variableExpression>
	</variable>
	<variable name="nguon_xuattrongky" class="java.math.BigDecimal" resetType="Group" resetGroup="GrHeader_NGUON_DUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_XUAT}]]></variableExpression>
	</variable>
	<variable name="nguon_cuoiky" class="java.math.BigDecimal" resetType="Group" resetGroup="GrHeader_NGUON_DUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_TONCUOI}]]></variableExpression>
	</variable>
	<variable name="nhom_dauky" class="java.math.BigDecimal" resetType="Group" resetGroup="Gr_NHOM_THUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_DAUKY}]]></variableExpression>
	</variable>
	<variable name="nhom_nhaptrongky" class="java.math.BigDecimal" resetType="Group" resetGroup="Gr_NHOM_THUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_NHAP}]]></variableExpression>
	</variable>
	<variable name="nhom_xuattrongky" class="java.math.BigDecimal" resetType="Group" resetGroup="Gr_NHOM_THUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_XUAT}]]></variableExpression>
	</variable>
	<variable name="nhom_cuoiky" class="java.math.BigDecimal" resetType="Group" resetGroup="Gr_NHOM_THUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_TONCUOI}]]></variableExpression>
	</variable>
	<variable name="SL_HOANTRA_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SL_HOANTRA}]]></variableExpression>
	</variable>
	<variable name="TT_HOANTRA_1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_HOANTRA}]]></variableExpression>
	</variable>
	<variable name="nguon_hoantra" class="java.math.BigDecimal" resetType="Group" resetGroup="GrHeader_NGUON_DUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_HOANTRA}]]></variableExpression>
	</variable>
	<variable name="nhom_hoantra" class="java.math.BigDecimal" resetType="Group" resetGroup="Gr_NHOM_THUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TT_HOANTRA}]]></variableExpression>
	</variable>
	<group name="GrHeader_NGUON_DUOC">
		<groupExpression><![CDATA[$F{NGUON_DUOC}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="510" height="20" uuid="95ea4f66-50e0-4c3a-be27-53377e541bbe"/>
					<box leftPadding="0">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUON_DUOC}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrHeader_NGUON_DUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="510" y="0" width="130" height="20" uuid="9ab5959d-122d-4b8c-b7c5-7b85062a504d"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nguon_dauky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrHeader_NGUON_DUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="640" y="0" width="130" height="20" uuid="888b07c9-2ae4-4976-869f-dd519dc6f0a7"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nguon_nhaptrongky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrHeader_NGUON_DUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="900" y="0" width="130" height="20" uuid="b30cb274-ccf7-456a-a6fa-2760a24df1c1"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nguon_xuattrongky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrHeader_NGUON_DUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="770" y="0" width="130" height="20" uuid="cfce3e85-d8f0-4d5d-a1f1-132febd617b6"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nguon_hoantra}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GrHeader_NGUON_DUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="1030" y="0" width="130" height="20" uuid="e74e6f6e-84bc-440a-b9e8-52ac86f45d17"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nguon_cuoiky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="Gr_NHOM_THUOC">
		<groupExpression><![CDATA[$F{NHOM_THUOC}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" uuid="71ba6ded-5a8c-4629-a094-c99f67a47ad4"/>
					<box>
						<leftPen lineWidth="0.75"/>
						<bottomPen lineWidth="0.75"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="7" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="30" y="0" width="480" height="20" uuid="aec49866-982b-4c3d-a9fb-297b3cdbeea5"/>
					<box leftPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NHOM_THUOC}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Gr_NHOM_THUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="510" y="0" width="130" height="20" uuid="779ac4a5-04fc-43dd-aea3-2eac235f9335"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_dauky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Gr_NHOM_THUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="640" y="0" width="130" height="20" uuid="080a8793-d244-43c5-8c5a-f39352d117c3"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_nhaptrongky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Gr_NHOM_THUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="900" y="0" width="130" height="20" uuid="c93f033c-b3dc-43c5-8dab-9820e0708030"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_xuattrongky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Gr_NHOM_THUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="1030" y="0" width="130" height="20" uuid="5a27e2b5-5b65-4ba2-80b0-434f3c9aab7c"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_cuoiky}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Gr_NHOM_THUOC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="770" y="0" width="130" height="20" uuid="8494bf02-2966-4654-adec-fe8db9ff9daa"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_hoantra}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="126" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="40" width="1160" height="30" uuid="8ca143db-3554-430e-b41b-3837ad62905b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁO CÁO NHẬP XUẤT TỒN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="97" width="1160" height="22" uuid="2e1623fb-7f87-4211-8556-9fddf55493ad"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{TEN_NGUONDUOC} == "-1" ? "Tất cả " : $F{TEN_NGUONDUOC}) + "- Từ ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{START_DATE})+" đến ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{END_DATE})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="70" width="1160" height="22" uuid="33372a92-d186-4ff8-b4f9-a60ab89296f9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_CSYT_TUYEN4}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="1160" height="20" uuid="386dc024-0d63-4120-8833-e009c26bccdf"/>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="1160" height="20" uuid="ede6efcc-eee1-4213-990d-a6b602ae01e3"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="0" width="1160" height="20" uuid="85c187bb-d46d-4fc3-a24c-2b47f9e11820"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="20" width="1160" height="20" uuid="9aca08b7-a41f-4378-8627-6afa10a54320"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
		</band>
	</title>
	<columnHeader>
		<band height="44" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="30" height="44" uuid="ba144d13-7a40-40b3-8690-97a020ef297b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="222" y="0" width="188" height="44" uuid="9153a14a-ec66-46b9-af6f-d20f6a8b1c1e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên dược, vật tư]]></text>
			</staticText>
			<staticText>
				<reportElement x="410" y="0" width="40" height="44" uuid="de0dfaa2-a33e-486d-8f1e-747af9c12a52"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐVT]]></text>
			</staticText>
			<staticText>
				<reportElement x="450" y="0" width="60" height="44" uuid="cabb0cfd-b27b-4a7e-9e43-56264d7c106b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="22" width="70" height="22" uuid="36bd635f-e06b-463c-8430-9dc8799a3136"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Trị giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="640" y="22" width="60" height="22" uuid="010879fa-b2ac-4d38-8a8a-0c93ccbc50ac"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="640" y="0" width="130" height="22" uuid="26e1b3b7-e8a7-414b-8ef9-de6d5f3fa57e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Nhập trong kỳ]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="130" height="22" uuid="75a8b98a-b108-445d-8540-0a942bac43ba"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Xuất trong kỳ]]></text>
			</staticText>
			<staticText>
				<reportElement x="960" y="22" width="70" height="22" uuid="80be3fcc-047f-4386-865d-45e7b69595b7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Trị giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="22" width="60" height="22" uuid="d4658237-16de-4fa0-a2e7-ac72999a163e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="1090" y="22" width="70" height="22" uuid="3b8dc4b5-379c-487f-9354-42aa43d3526f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="1030" y="22" width="60" height="22" uuid="10330229-2695-47a0-adfb-9a4a407b0712"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="1030" y="0" width="130" height="22" uuid="85c8bcd0-7e09-4ca1-938f-63c860a4f844"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tồn cuối kỳ]]></text>
			</staticText>
			<staticText>
				<reportElement x="510" y="0" width="130" height="22" uuid="862490f5-387f-4c3d-8826-15e1a5dd8f6a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tồn đầu kỳ]]></text>
			</staticText>
			<staticText>
				<reportElement x="510" y="22" width="60" height="22" uuid="20c0ffc5-4e5a-497d-a267-06ebbfcd8e71"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="570" y="22" width="70" height="22" uuid="f6d4e54c-66f5-43ca-937d-f08a1af797ad"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Trị giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="770" y="0" width="130" height="22" uuid="b6f5cbec-175b-4c9f-a7e7-0a99102bfa63"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Hoàn trả trạm y tế]]></text>
			</staticText>
			<staticText>
				<reportElement x="770" y="22" width="60" height="22" uuid="598adb96-ff92-43a1-a5d0-748de2de9487"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="830" y="22" width="70" height="22" uuid="ae305121-99ba-4d4f-b1bd-6601cb9271ac"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Trị giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="60" height="44" uuid="3e4c20f4-1945-4767-8242-f419ef7e8835"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[MÃ THUỐC]]></text>
			</staticText>
			<staticText>
				<reportElement x="90" y="0" width="132" height="44" uuid="100e3035-67e8-4897-89ab-bc64e41fe0e4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[SỐ LÔ/HẠN DÙNG]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="22" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="22" uuid="e1320c65-0785-45d4-bc8a-370c4199135e"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="222" y="0" width="188" height="22" uuid="ee165139-8b80-46ee-9323-d7ecfce42154"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_THUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="410" y="0" width="40" height="22" uuid="0a28d4cf-beb2-4e90-b101-ddb80b5058f2"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_VI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="450" y="0" width="60" height="22" uuid="fb4cdc65-e610-4531-96bc-e2cd9345e64a"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{DON_GIA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="510" y="0" width="60" height="22" uuid="60601205-a96d-46cc-a7d7-1abbb1592d82"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{SL_DAUKY}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="570" y="0" width="70" height="22" uuid="58edea63-aa42-4f29-9257-268c54596ea6"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TT_DAUKY}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="640" y="0" width="60" height="22" uuid="70f6a88b-8f0d-4281-b172-b90295bd898d"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{SL_NHAP}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="700" y="0" width="70" height="22" uuid="f10a4a3c-810f-4a89-87e8-e8a3d9c97da7"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TT_NHAP}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="900" y="0" width="60" height="22" uuid="6688133f-414d-45d5-869e-712095d15bdc"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{SL_XUAT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="960" y="0" width="70" height="22" uuid="2400eeaa-9674-45c8-a003-b6e488fba545"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TT_XUAT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1030" y="0" width="60" height="22" uuid="a6a20200-ab92-47fc-b494-04b7d96efad7"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{SL_CUOIKY}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1090" y="0" width="70" height="22" uuid="74c2b82b-b8db-457d-b549-890d6c179ace"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TT_TONCUOI}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="770" y="0" width="60" height="22" uuid="42b3b50b-0fca-4a2c-b424-9ad4625eeb13"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{SL_HOANTRA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="830" y="0" width="70" height="22" uuid="55e91faf-2843-4151-be58-c374acb5a061"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{TT_HOANTRA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="30" y="0" width="60" height="22" uuid="15ff8ff1-**************-65ce8c01b960"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_THUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="90" y="0" width="132" height="22" uuid="0ce2db1b-1749-45f0-bca1-054bbc30ad20"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLOHANDUNG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="163" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" x="0" y="22" width="1160" height="141" uuid="e352963c-bc0d-4fd9-91f7-be0b1da45666"/>
				<staticText>
					<reportElement x="378" y="24" width="262" height="20" uuid="ab5a89dd-9696-49bc-b00e-5858897a608d"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Trưởng khoa dược]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="640" y="4" width="520" height="20" uuid="eecaed12-c4f5-43a9-ad5c-7bd53e669ca4"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Ngày " + new SimpleDateFormat("dd").format($P{END_DATE}) + " tháng " + new SimpleDateFormat("MM").format($P{END_DATE}) + " năm " + new SimpleDateFormat("yyyy").format($P{END_DATE})]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="24" width="186" height="20" uuid="51a1add1-a4d5-4daa-b7d6-fa59ac27530d"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Thủ kho dược]]></text>
				</staticText>
				<staticText>
					<reportElement x="186" y="24" width="192" height="20" uuid="b32b81a1-cd82-4f04-a862-614e97045759"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Kế toán dược]]></text>
				</staticText>
				<staticText>
					<reportElement x="640" y="24" width="520" height="20" uuid="90646f7f-bfd0-4e9f-a0f8-0736110cc1fb"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[Giám đốc]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="1160" height="22" uuid="b1e7fd0a-ce92-4f52-afe1-ab876607a21f"/>
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="510" height="22" uuid="8de16b2f-d585-428f-982b-32c686a430dc"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Tổng cộng:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="900" y="0" width="60" height="22" uuid="902c4433-a932-44a0-a87b-5dbf80308595"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SL_XUAT_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="510" y="0" width="60" height="22" uuid="118c3abd-b444-4a19-8d1e-ca2cd0fec8cc"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SL_DAUKY_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="640" y="0" width="60" height="22" uuid="3264d9ef-210f-46be-98ad-ed5fc0d8d8ee"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SL_NHAP_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="570" y="0" width="70" height="22" uuid="6f242c24-d97d-42c9-9cc2-32e5e74120a0"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{TT_DAUKY_1}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="700" y="0" width="70" height="22" uuid="56d2ec0f-ae9a-4259-99d5-91c1ea348014"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{TT_NHAP_1}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="770" y="0" width="60" height="22" uuid="6535af1a-b807-4ac3-a4c3-705b9b3f83bf"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SL_HOANTRA_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="830" y="0" width="70" height="22" uuid="ac053372-78ab-47d1-9802-f9cbd543382e"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{TT_HOANTRA_1}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</frame>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1030" y="0" width="60" height="22" uuid="0598022e-7120-47a5-bda2-18e3a0f991b1"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SL_CUOIKY_1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1090" y="0" width="70" height="22" uuid="0a946016-aa21-446a-9802-af7e54e227f8"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{TT_TONCUOI_1}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="960" y="0" width="70" height="22" uuid="b2f9e468-b849-487c-8f61-5a517f419381"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{TT_XUAT_1}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
