/*
 * T<PERSON><PERSON><PERSON> nhận bệnh phẩm theo lô
 * 
 * VietDA	17/04/2017		tạo mới
 *  
 */

function DanhSachTiepNhan(opt){
	this.load = doLoad;
	var lstTiepNhanBP = [];
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;

		initControl();
		bindEvent();

		// load dữ liệu lên các control
		$("#txtTuNgay").val(moment().format('DD/MM/YYYY'));
		$("#txtDenNgay").val(moment().format('DD/MM/YYYY'));
		
		var sql_par = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+'';
		ComboUtil.getComboTag("cboPhongThuc<PERSON>ien", "CLS01X002.KP", sql_par, "", "", "sp","",function(){
			$('#cboPhongThucHien option[value='+opt.subdept_id+']').attr('selected','selected');	
		});		
		
		$("#txtBarcode").focus();
	}

	function initControl(){
		var grid = $("#grdDanhSachTiepNhan");

		grid.jqGrid({
              datatype: 'local',
              colNames: ['','STT','Số phiếu','Barcode','Mã BN','Tên BN','Ngày sinh','Số thẻ BHYT','Khoa','Phòng',' ','Khẩn',
                  'TG chỉ định','Bác sĩ chỉ định','Thực hiện','Trạng thái',
                  'BENHNHANID','MAUBENHPHAMID','TRANGTHAIMAUBENHPHAM','TRANGTHAIDULIEUMAUBENHPHAM',
                  'GIOITINH','TUOI','PHONGCHUYENDENID'],
		       colModel: [
		  	        {name:'ICON', width: 20, search: false },
					{name:'SOTHUTU', width: 40, align:"center", hidden: true },
					{name:'SOPHIEU', width: 90, align:"center", formatter:GridUtil.fm.formatStrEncode },
					{name:'BARCODE', width: 60, align:"center", formatter:GridUtil.fm.formatStrEncode },
					{name:'MABENHNHAN', width: 85, formatter:GridUtil.fm.formatStrEncode },
					{name:'TENBENHNHAN', width: 150, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGAYSINH', width: 70, align:"center", formatter:GridUtil.fm.formatStrEncode },
					{name:'MA_BHYT', width: 120, formatter:GridUtil.fm.formatStrEncode },
					{name:'KHOADIEUTRI', width: 125, formatter:GridUtil.fm.formatStrEncode },
					{name:'PHONGDIEUTRI', width: 125, formatter:GridUtil.fm.formatStrEncode },					
					{name:'LOAIMAUBENHPHAM', width: 60, hidden: true },
					{name:'NGAYDICHVU', width: 90, hidden: true },
					{name:'BACSY', width: 100, hidden: true },
					{name:'NGAYMAUBENHPHAM_HOANTHANH', width: 90, hidden: true },
					{name:'TRANGTHAI', width: 90, hidden: true },
					{name:'BENHNHANID', hidden: true },
					{name:'MAUBENHPHAMID', hidden: true },
					{name:'TRANGTHAIMAUBENHPHAM', hidden: true },
					{name:'TRANGTHAIDULIEUMAUBENHPHAM', hidden: true },
					{name:'GIOITINH', hidden: true },
					{name:'TUOI', hidden: true },
					{name:'PHONGCHUYENDENID', hidden: true }
		         ],
              rowNum: 50,
			  rowList: [20, 50, 100],
              pager: '#pager_grdDanhSachTiepNhan',
              gridview: true,
              ignoreCase: true,
              rownumbers: true,
              viewrecords: true,
              //sortorder: 'desc',
              width:"100%",
              autowidth:true,
              editurl: 'clientArray',
              caption: 'Danh sách tiếp nhận',
              onSelectRow: function (rowid) {
            	  GridUtil.unmarkAll("grdDanhSachTiepNhan");
				  GridUtil.markRow("grdDanhSachTiepNhan", rowid);
              }
		});          
		grid.jqGrid('navGrid', '#pager_grdDanhSachTiepNhan', {edit: false, add: false, del: false});	    
		grid.jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		$("#grdDanhSachTiepNhan")[0].toggleToolbar();
		$('#gview_grdDanhSachTiepNhan').find('.ui-jqgrid-bdiv').attr("style","height: 328px !important");
		GridUtil.setWidthPercent("grdDanhSachTiepNhan","100%");		
	}
	
	function bindEvent(){
		var esc = 27, f3 = 114, f4 = 115, f5 = 116;
        $(document).unbind('keydown').keydown(function (e) {        	                    
        	if (e.keyCode == f3) {
        		e.preventDefault();
                $("#txtBarcode").focus();
            } 
        	else if (e.keyCode == f4){
        		e.preventDefault();
            	tiepNhanBenhPham(lstTiepNhanBP);
            	lstTiepNhanBP = [];
    			EventUtil.raiseEvent("CLS01X008_TiepNhan",{msg:''});
            } 
        	else if (e.keyCode == f5) {
        		e.preventDefault();
            } 
        	else if (e.keyCode == esc) {
        		e.preventDefault();
        		EventUtil.raiseEvent("CLS01X008_Thoat",{msg:''});
            }
        });

		$('#txtBarcode').keydown(function (e) {
			if (e.keyCode == 13) {
				$('#txtBarcode').val($('#txtBarcode').val().trim());
				themPhieuTiepNhan($('#txtBarcode').val());
			}
		});

		$('#btnThem').on('click', function () {	
			$('#txtBarcode').val($('#txtBarcode').val().trim());
			themPhieuTiepNhan($('#txtBarcode').val());
		});
		
		$("#grdDanhSachTiepNhan").jqGrid("setGridParam",{
			gridComplete: function(){
				var rowids = $("#grdDanhSachTiepNhan").getDataIDs();
				for(var i=0;i < rowids.length;i++){
					var rowid = rowids[i];
					var row =  $('#grdDanhSachTiepNhan').jqGrid('getRowData', rowid);
					
					// chèn nút xóa vào cột cuối cùng grid
					var btnDelete = '<button type="button" id="'+rowids[i]+'"  class="btn btn-default"><span class="glyphicon glyphicon-remove" aria-hidden="true"></span></button>';
					$("#grdDanhSachTiepNhan").jqGrid ('setCell', rowids[i], 11, btnDelete);					
					$('#'+rowids[i] + " :button").bindOnce("click",function(e) {
						$('#grdDanhSachTiepNhan').jqGrid('delRowData', rowid);
						var rowDel =  $('#grdDanhSachTiepNhan').jqGrid('getRowData', rowid);
						removeItemInArrayList(row.SOPHIEU);
					});
					
					// hiển thị các icon trạng thái
	    			var icon = '';
					if(row.TRANGTHAIMAUBENHPHAM == 2){
						icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
					}
					else if(row.TRANGTHAIMAUBENHPHAM == 4){
						if(row.TRANGTHAIDULIEUMAUBENHPHAM == 2){
							icon = '<center><img src="../common/image/Flag_Green.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIDULIEUMAUBENHPHAM == 3){
							icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
						}
						else{
							icon = '<center><img src="../common/image/Misc_calendar.png" width="15px"></center>';
						}
					}
					else if(row.TRANGTHAIMAUBENHPHAM == 3){
						icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
					}				
					$("#grdDanhSachTiepNhan").jqGrid ('setCell', rowid, 1, icon);					
				}
			}
		});

		$('#btnTiepNhan').on('click', function () {	
			tiepNhanBenhPham(lstTiepNhanBP);
			EventUtil.raiseEvent("CLS01X008_TiepNhan",{msg:''});		
		});
		
		$('#btnThoat').on('click', function () {			
			EventUtil.raiseEvent("CLS01X008_Thoat",{msg:''});		
		});
	}
	
	function findItemInArrayList(key){		
		for(var i = 0; i < lstTiepNhanBP.length; i++) {
		    var obj = lstTiepNhanBP[i];
		    if(key.length==12){
			    if(key.indexOf(obj.SOPHIEU) !== -1) {
			    	return i;
			    }
		    }
		    else if(key.length>=3 && key.length<=6) {
		    	if(key.indexOf(obj.BARCODE) !== -1) {
			    	return i;
			    }
		    }
		}
		return -1;
	}

	function removeItemInArrayList(key){		
		for(var i = 0; i < lstTiepNhanBP.length; i++) {
		    var obj = lstTiepNhanBP[i];

		    if(key.indexOf(obj.SOPHIEU) !== -1) {
		    	lstTiepNhanBP.splice(i, 1);
		    }
		}
	}
	
	// load danh sach tiep nhan
	function themPhieuTiepNhan(barcode){
		if(findItemInArrayList(barcode)==-1){
			$("#lblThongBao").text("");
			
			var param = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+barcode;	
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X008.TK", param);			
			if(data_ar.length>0){
				if(data_ar[0]["TRANGTHAIMAUBENHPHAM"]=="4"){
					$("#lblThongBao").text("Phiếu đã được tiếp nhận");	
				} 
				else if(data_ar[0]["TRANGTHAIMAUBENHPHAM"]=="3"){
					$("#lblThongBao").text("Phiếu đã được trả kết quả");
				}
				else {
					lstTiepNhanBP.push(data_ar[0]);
					$("#grdDanhSachTiepNhan").jqGrid('setGridParam', { data: lstTiepNhanBP });
					$("#grdDanhSachTiepNhan").trigger('reloadGrid');
					$("#txtBarcode").val('');
				}
			} else {
				$("#lblThongBao").text("Không tìm thấy phiếu nào với mã được nhập");
			}
		}
		else {
			$("#lblThongBao").text("Phiếu đã có trong danh sách tiếp nhận");
		}
		$("#txtBarcode").focus();
	}
	
	function tiepNhanBenhPham(data){
		if(data.length>0){
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+JSON.stringify(data);
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X008.TNTL", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Tiếp nhận danh sách bệnh phẩm thành công",function(){$("#txtBarcode").focus();},2000);
				lstTiepNhanBP = [];
				$("#grdDanhSachTiepNhan").jqGrid('setGridParam', { data: lstTiepNhanBP });
				$("#grdDanhSachTiepNhan").trigger('reloadGrid');
				$("#grdDanhSachTiepNhan").jqGrid("clearGridData");
				$("#txtBarcode").val('');
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể tiếp nhận",function(){$("#txtBarcode").focus();},2000);
			}
		}
		else {
			DlgUtil.showMsg("Danh sách tiếp nhận trống !<br/>Bạn cần thêm phiếu vào danh sách tiếp nhận trước",function(){$("#txtBarcode").focus();},3000);
		}
	}
}
