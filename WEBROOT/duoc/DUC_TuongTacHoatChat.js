/*
 * <PERSON><PERSON> mục tương tác hoạt chất - edit lại
 * 
 * ChuanNT		20/04/2018		tạo mới
 * 
 */

function DMThuoc(opt) {
	var _param = session_par;
	var _loai = '-1';
	var _kho = '';
	this.opt = $.extend({}, opt);
	var that = this;
	this.load = doLoad;
	var manhom = '';
	var dsThem = [];
	var dsBot = [];
	var PHARMA_KETUONGTAC_TEN_HC = '0';
	var flag = 0;
	var files ;
	function doLoad() {
		// khởi tạo các tab dữ liệu
		PHARMA_KETUONGTAC_TEN_HC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_KETUONGTAC_TEN_HC');
		if(PHARMA_KETUONGTAC_TEN_HC == '1')
		{
			$("#lbl_chidinh").text('C<PERSON> chế: ');
		}
		else{$("#lbl_chidinh").text('Chống chỉ định: ');}
		$("#lbl_hoatchat").text($("#hidMaHoatChat").val());
		$("#lbl_tenhoatchat").text($("#hidTenHoatChat").val());
		$("li[id^='tab'").on("click", function(e) {
			var tabName = $(this).attr("id").substr(3);
			$("li[class='active']").removeClass("active");
			$(this).addClass("active");
			$("div[class='tab active']").removeClass("active");
			// console.log(tabName);
			$("#div" + tabName).addClass("active");
		});
		// $("#hidDuTruID").val("");
		// khởi tạo các control
		
		ComboUtil.getComboTag("cboLoai", "DUC67T002.20", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "", {
			value : '',
			text : 'Tất cả'
		}, "");
		ComboUtil.getComboTag("cboLoaiLuu", "DUC67T002.20", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "", {
			value : '',
			text : 'Chưa xác định'
		}, "");
		
	
		
		
		
		
		
		initControl();
		bindEvent();
	 if($("#hidMaHoatChat").val() ==''){
		 return DlgUtil.showMsg("Không có thuốc được chọn.",
					undefined, 2000);
	 	}
	 	$(document).ready(function() {
			var callback = function() {
				if ($.active !== 0) {
					setTimeout(callback, '20');
					return;
				}
				loadGridDaTT();
				loadGridChuaTT();
				//loadAnhIDG();
			};
			callback();

		});
		
	}
	function loadGridChuaTT() {
		var param1 = RSUtil.buildParam("", [ $("#hidMaHoatChat").val()]);
		
		GridUtil.loadGridBySqlPage("grvDSChuaCo", "DMC33.01", param1);
		
	}
	function loadGridDaTT() {
		
		var param2 = RSUtil.buildParam("", [ $("#hidMaHoatChat").val(),$("#cboLoai").val()]);
		
		GridUtil.loadGridBySqlPage("grvDSDaCo", "DMC33.02", param2);
		if(flag == 0){
			loadAnhIDG();
		}
	}
	function loadAnhIDG(){
		var _par = [ "4",$("#hidHoatChatId").val()]
    	var data_anh = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.GET_ANH",_par.join('$'));
    	if(data_anh != null && data_anh.length > 0){
    		var row=data_anh[0];
    		 var _url = "../upload/getdata.jsp?id=" + row.MEDIA_ID;
			    $('#idImgAtt').attr('src',_url);
			    $("#txtSOQUYETDINH").val(row.SOQUYETDINH);
			    $("#txtNGAYQD").val(row.NGAYQD);
			   
    	}else{
    		$('#idImgAtt').attr('src',"");
			    $("#txtSOQUYETDINH").val("");
			    $("#txtNGAYQD").val("");
    	}
	}
	$('#cboLoai').change(function() {
		loadGridDaTT();
	});
	
	function initControl() {
		
		
		var _gridHeader=
			"Mã hoạt chất,MAHOATCHAT,15,0,f,l,1,2;"
			+ "Tên hoạt chất,TENHOATCHAT,40,0,f,l,1,2"		
			;
		var _gridHeader2 =  "Mã HC tương tác ,MAHOATCHAT,30,0,f,l;" 
				+ "Tên HC tương tác,TENHOATCHAT,60,0,f,l;"				
				+ "Loại tương tác,LOAITUONGTAC,40,0,f,l;"
				+ "Hậu quả,HAU_QUA,30,0,f,l;"
				+ "Xử trí,XU_TRI,30,0,f,l;"
				+ "Chỉ định,CHIDINH,30,0,f,l;"
				+ "TL tham khảo,TLTHAMKHAO,30,0,f,l;"
				+ "Cơ chế TT,COCHETUONGTAC,30,0,f,l;"
				+ "Ghi chú,GHICHU,30,0,f,l;"
				+ "LOAITUONGTACID,LOAITUONGTACID,0,0,t,l";	
		GridUtil.init("grvDSChuaCo", "100%", "350", "Danh sách hoạt chất", true,
				_gridHeader);
		GridUtil.init("grvDSDaCo", "100%", "220", "Danh sách hoạt chất", true,
				_gridHeader2);
	}
	function bindEvent() {		
		$("#grvDSDaCo")
				.jqGrid(
						'setGridParam',
						{
							gridComplete : function(id) {
								checkDaCo();
							},
							onSelectRow : function(id) {
								GridUtil.unmarkAll('grvDSDaCo');
								GridUtil.markRow('grvDSDaCo', id);
								var selRows = $(this).jqGrid('getGridParam',
										'selarrrow');
								rowData = $('#grvDSDaCo').jqGrid('getRowData',
										id);
								 $('#cboLoaiLuu').val(rowData["LOAITUONGTACID"]) ;
								 $('#txtGhiChu').val(rowData["GHICHU"]);
								 $('#txtHAU_QUA').val(rowData["HAU_QUA"]);
								 $('#txtXU_TRI').val(rowData["XU_TRI"]);
								 $('#txtCHIDINH').val(rowData["CHIDINH"]);
								 $('#txtTLTHAMKHAO').val(rowData["TLTHAMKHAO"]);
								 $('#txtCOCHETUONGTAC').val(rowData["COCHETUONGTAC"]);
								
								if (selRows.indexOf(id) != -1
										&& dsBot
												.indexOf(rowData["MAHOATCHAT"]) == -1) {
									dsBot.push(rowData["MAHOATCHAT"]);
								}
								if (selRows.indexOf(id) == -1) {
									var j = dsBot
											.indexOf(rowData["MAHOATCHAT"]);
									if (j != -1) {
										dsBot.splice(j, 1);
									}
								}
							},
							onSelectAll : function(id, status) {
								var rowIds = $('#grvDSDaCo').jqGrid(
										'getDataIDs');
								for (i = 0; i < rowIds.length; i++) {
									rowData = $('#grvDSDaCo').jqGrid(
											'getRowData', rowIds[i]);
									if (status == true
											&& dsBot
													.indexOf(rowData["MAHOATCHAT"]) == -1) {
										dsBot.push(rowData["MAHOATCHAT"]);
									}
									if (status == false) {
										var j = dsBot
												.indexOf(rowData["MAHOATCHAT"]);
										if (j != -1) {
											dsBot.splice(j, 1);
										}
									}
								}
							}
						});
		$("#grvDSChuaCo")
				.jqGrid(
						'setGridParam',
						{
							gridComplete : function(id) {
								checkChuaCo();
							},
							onSelectRow : function(id) {
								GridUtil.unmarkAll('grvDSChuaCo');
								GridUtil.markRow('grvDSChuaCo', id);
								var selRows = $(this).jqGrid('getGridParam',
										'selarrrow');
								rowData = $('#grvDSChuaCo').jqGrid(
										'getRowData', id);
								if (selRows.indexOf(id) != -1
										&& dsThem
												.indexOf(rowData["MAHOATCHAT"]) == -1) {
									dsThem.push(rowData["MAHOATCHAT"]);
								}
								if (selRows.indexOf(id) == -1) {
									var i = dsThem
											.indexOf(rowData["MAHOATCHAT"]);
									if (i != -1) {
										dsThem.splice(i, 1);
									}
								}
							},
							onSelectAll : function(id, status) {
								var rowIds = $('#grvDSChuaCo').jqGrid(
										'getDataIDs');
								for (i = 0; i < rowIds.length; i++) {
									rowData = $('#grvDSChuaCo').jqGrid(
											'getRowData', rowIds[i]);
									if (status == true
											&& dsThem
													.indexOf(rowData["MAHOATCHAT"]) == -1) {
										dsThem.push(rowData["MAHOATCHAT"]);
									}
									if (status == false) {
										var j = dsThem
												.indexOf(rowData["MAHOATCHAT"]);
										if (j != -1) {
											dsThem.splice(j, 1);
										}
									}
								}
							}
						});
		
		$("#gridDsThuoc")
				.jqGrid(
						'setGridParam',
						{
							ondblClickRow : function(id) {
								var _ret = $("#gridDsThuoc").jqGrid(
										'getRowData', id);

								$("#hidMaHoatChat").val(_ret.MAHOATCHAT);
								var _par = {
									MAHOATCHAT : _ret.MAHOATCHAT,
									type : that.opt.type
								};
								dlgPopup = DlgUtil
										.buildPopupUrl(
												"dlgNTNCC",
												"divDlg",
												"manager.jsp?func=../duoc/DUC67T001_NhapThongTinThuocVatTu",
												_par, "Thông tin hoạt chất",
												1300, 605);

								dlgPopup.open("dlgNTNCC");

							}
						});
	}

	
	function setSysParam(_par_ar) {
		var v_par = _par_ar;
		for (var i1 = 0; i1 < _param.length; i1++) {
			console.log('_param[' + i1 + ']=' + _param[i1]);
			v_par.push({
				"name" : "[S" + i1 + "]",
				"value" : _param[i1]
			});
		}
		return v_par;
	}
	

	$('#btnDong').on('click', function(e) {
		EventUtil.raiseEvent("assignSevice_cancel1", {
			ncc : 'NHAP_KHO',
			id : "0"
		});

	});

	// btnThem
	// $(#)
	$('#btnThem').bindOnce("click", function() {
		if (dsThem.length == 0) {
			DlgUtil.showMsg("Chưa chọn hoạt chất.", undefined, 2000);
			return;
		}
		ThemThuoc();
	}, 2000);
	$('#btnBot').bindOnce("click", function() {
		if (dsBot.length == 0) {
			DlgUtil.showMsg("Chưa chọn hoạt chất.", undefined, 2000);
			return;
		}
		BotThuoc();
	}, 2000);
	
	$('#btnLuu').bindOnce("click", function() {
		if (dsBot.length == 0) {
			DlgUtil.showMsg("Chưa chọn hoạt chất danh sách đã tương tác để sửa.", undefined, 2000);
			return;
		}
		EditTT();
	}, 2000);
	
	$("#idImgAtt").click(function(){
		var src = $(this).attr("src");
		window.open(src);
	});
	
	$("#fileUploadName").click(function () {
        $("#fileUpload").click();
    });

    $("#fileUpload").change(function () {
        let pdfRegex = /\.(pdf|gif|jpe?g|bmp|png)$/i;
        files = [...this.files].filter(function (el) {
            return pdfRegex.test(el.name);
        });
        if (files) {
            if (files.length > 0) {
                var filenames = "";
                for (var i = 0; i < files.length; i++) {
                    filenames = filenames + files[i].name + "\n<hr>";
                }
                $("#fileUploadName").addClass("data");
                $("#fileUploadName").removeClass("raw");
                $("#fileUploadName").html(filenames)
            } else {
                $("#fileUploadName").removeClass("data");
                $("#fileUploadName").addClass("raw");
                $("#fileUploadName").html("Đính kèm tài liệu...")
            }
        }
    });
	
	function ThemThuoc() {
		var danhsach = JSON.stringify(dsThem);
		  objData = new Object();
		  objData["MAHOATCHAT"] = $('#hidMaHoatChat').val();
    	  objData["LOAITUONGTAC"] = $('#cboLoaiLuu').val() ;
    	  objData["GHICHU"] = $('#txtGhiChu').val();
    	  objData["HAUQUA"] = $('#txtHAU_QUA').val();
    	  objData["XUTRI"] = $('#txtXU_TRI').val();
    	  objData["CHIDINH"] = $('#txtCHIDINH').val();
    	  objData["TLTHAMKHAO"] = $('#txtTLTHAMKHAO').val();
    	  objData["COCHETUONGTAC"] = $('#txtCOCHETUONGTAC').val();
		/*var loaitt = $('#cboLoaiLuu').val();
		var ghichu = $('#txtGhiChu').val();
		var hauqua = $('#txtHAU_QUA').val();
		var xutri = $('#txtXU_TRI').val();
		var chidinh = $('#txtCHIDINH').val();
		var param_ar_kho =  $('#hidMaHoatChat').val() +'$'
		+ loaitt + '$'
		+ ghichu + '$' 
		+ hauqua + '$' 
		+ xutri + '$' 
		+ chidinh + '$' 
		+ danhsach + '$';*/
    	  var _par = [ JSON.stringify(objData), danhsach ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC33.05",
				_par.join('$'));
		if (result != '-1')
			{	loadGridDaTT();
				loadGridChuaTT();
				dsThem.splice(0,dsThem.length);
			}
//			dsTh.splice(0,A.length)
		else
			DlgUtil.showMsg("Có lỗi xảy ra, không thể gán hoạt chất tương tác.",
					undefined, 2000);

	}
	function BotThuoc() {
		var danhsach = JSON.stringify(dsBot);		
		
		var param_ar_kho = $('#hidMaHoatChat').val() + '$' + danhsach + '$';
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC33.04",
				param_ar_kho);
		if (result != '-1') {			
			loadGridDaTT();
			loadGridChuaTT();
			dsBot.splice(0,dsBot.length);
		} else
			DlgUtil.showMsg("Có lỗi xảy ra, không thể gán hoạt chất tương tác.",
					undefined, 2000);
	}
	
	function EditTT() {
		var danhsach = JSON.stringify(dsBot);		
		  objData = new Object();
		  objData["MAHOATCHAT"] = $('#hidMaHoatChat').val();
    	  objData["LOAITUONGTAC"] = $('#cboLoaiLuu').val() ;
    	  objData["GHICHU"] = $('#txtGhiChu').val();
    	  objData["HAUQUA"] = $('#txtHAU_QUA').val();
    	  objData["XUTRI"] = $('#txtXU_TRI').val();
    	  objData["CHIDINH"] = $('#txtCHIDINH').val();
    	  objData["TLTHAMKHAO"] = $('#txtTLTHAMKHAO').val();
    	  objData["COCHETUONGTAC"] = $('#txtCOCHETUONGTAC').val();
		/*var loaitt = $('#cboLoaiLuu').val();
		var ghichu = $('#txtGhiChu').val();
		var hauqua = $('#txtHAU_QUA').val();
		var xutri = $('#txtXU_TRI').val();
		var chidinh = $('#txtCHIDINH').val();
		var param_ar_kho =  $('#hidMaHoatChat').val() +'$'
		+ loaitt + '$'
		+ ghichu + '$' 
		+ hauqua + '$' 
		+ xutri + '$' 
		+ chidinh + '$' 
		+ danhsach + '$';*/
    	  var _par = [ JSON.stringify(objData), danhsach ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC33.06",
				_par.join('$'));
		if (result != '-1')
			{	
				flag = 1;
				uploadFile();
				loadGridDaTT();
				loadGridChuaTT();
				dsBot.splice(0,dsBot.length);
			}
//			dsTh.splice(0,A.length)
		else
			DlgUtil.showMsg("Có lỗi xảy ra, không sửa hoạt chất tương tác.",
					undefined, 2000);
	}
	
	function checkChuaCo() {
		if(dsThem.length ==0) return;
		var rowIds = $('#grvDSChuaCo').jqGrid('getDataIDs');
		for (i = 1; i <= rowIds.length; i++) {
			rowData = $('#grvDSChuaCo').jqGrid('getRowData', i);

			if (dsThem.indexOf(rowData["MAHOATCHAT"]) != -1) {
				jQuery('#grvDSChuaCo').jqGrid('setSelection', i);
			} // if
		}
	}
	function checkDaCo() {
		if(dsBot.length ==0) return;
		var rowIds = $('#grvDSDaCo').jqGrid('getDataIDs');
		for (i = 1; i <= rowIds.length; i++) {
			rowData = $('#grvDSDaCo').jqGrid('getRowData', i);

			if (dsBot.indexOf(rowData["MAHOATCHAT"]) != -1) {
				jQuery('#grvDSDaCo').jqGrid('setSelection', i);
			} // if
		}
	}
	
	function uploadFile(){
		if (files) {
			var promiseAll = [];
	        for (var i = 0; i < files.length; i++) {
	            var pr = UploadUtil.uploadIDGPromises(files[i], false);
	            promiseAll.push(pr);
	        }
	        Promise.all(promiseAll).then(function (result) {
	            var param = [JSON.stringify(result)];
	            var param_ar_phieu = {
	    				"PHIEUID" : $('#hidHoatChatId').val(), 
	    				"LOAIPHIEU" : "4", 
	    				"SOQUYETDINH" : $("#txtSOQUYETDINH").val(),
	    				"NGAYQD" : $("#txtNGAYQD").val()
	    			};
	            var param_str_phieu = JSON.stringify(param_ar_phieu);
	
				 var str_phieu_json = (param_str_phieu.replace("[", "{")).replace("]",
						"}");
	            var _par = [ str_phieu_json, param ];
	            var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC_ATC.ANH01", _par.join('$'));
	            if (rs == 1) {
	           	 DlgUtil.showMsg("Thêm file thành công !");
	                
	            } else {
	                
	                DlgUtil.showMsg("Thêm file không thành công !");
	            }
	        }, function (error) {
	            DlgUtil.showMsg("Không thành công : " + error);
	        });
		}
	}

}