function Kythuathotro(_opts) {
	
	var _gridKTHT="grdKythuathotro"; 
	var _gridKTHTHeader= "kythuathotroid,KYTHUATHOTROID,60,0,t,l,1,2;"
							+ "<PERSON><PERSON> kỹ thuật,MAKYTHUAT,100,0,f,l,1,2;"
							+ "<PERSON><PERSON><PERSON> tự,THUTUSAPXEP,50,0,t,l,1,2;"
							+ "<PERSON><PERSON><PERSON> kỹ thuật,TENKYTHUAT,330,0,f,l,1,2";
	

	var _user_id=-1;
	var _hospital_id;
	_param=[];
	_cauhinhId = -1;
	var checkRequired;
	var validator = null;
	var valid_ar = [];
	
	var that=this;
	this.load=doLoad;
	var selectedIdKTHT = [];						// mang lua chon, luu tru id nhan vien sau khi lua chon, moi phan tu la 1 object
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		validator = new DataValidator("inputForm");
		var _options=$.extend({},_opts);
		var uuid = _options._uuid;
		var _param=_options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_initControl();
		_bindEvent();	
	}
	
	function addNumberOnly(element){
		$(element).keypress(function (e) {
		     if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
		        return false;
		    }
		});
	}
	addNumberOnly($("#txtTHUTUSAPXEP"));
	
	function _initControl(){
		GridUtil.init(_gridKTHT,"100%","260","",true,_gridKTHTHeader, 
							false, { rowNum: 500,rowList: [500, 800, 1000]});
		loadGridData();
	};
	
	function _bindEvent() {
		GridUtil.setGridParam(_gridKTHT,{
			onSelectRow: function(id) {
				GridUtil.unmarkAll(_gridKTHT);
	    		GridUtil.markRow(_gridKTHT,id);
	            if (id) {
	            	var _row = $("#"+_gridKTHT).jqGrid('getRowData', id);
	            }
	        }
		});

		
		$("#btnLuu").click(function(){
			var counter = 0;
			var str_err = ""; // luu thong tin cua row duoc chon
			//lay mang cac row duoc chon
			var selRows = $("#grdKythuathotro").jqGrid('getGridParam','selarrrow');
			console.log('grdKythuathotro size='+selRows.length);
			for(t=0;t<selRows.length;t++){
				var tt = $("#grdKythuathotro").jqGrid('getRowData', selRows[t]);
				++counter;
				str_err += "[" + tt.KYTHUATHOTROID+ "," + tt.TENKYTHUAT + "];" + "\n";
				var objj = new Object(); 
				objj.KYTHUATHOTROID = tt.KYTHUATHOTROID; 
				objj.TENKYTHUAT = tt.TENKYTHUAT; 
				selectedIdKTHT.push(objj);
				
			}
			console.log('str_err= '+str_err);
			$('#txtTENKYTHUAT').val(str_err);
			console.log('');
			if(str_err == ""){
				/*DlgUtil.showMsg("Thiếu chuyên khoa cho "+counter+" nhân viên sau: " + str_err);*/
				DlgUtil.showMsg("Bạn chưa chọn kỹ thuật nào!");
				return;
			}else{
				// bo sung du lieu cho mang selectedIdKTHT;
				console.log('selectedIdKTHT size= '+selectedIdKTHT.length);
				var objReturn = new Object();
				objReturn.msg='1';
				objReturn.array = selectedIdKTHT;
				var evFunc=EventUtil.getEvent("assignSevice_saveKTHT");			
				if(typeof evFunc==='function') {				
					evFunc(objReturn);
				}
				else {
					console.log('evFunc not a function');
				}
			}
		});
				
		$("#btnClose").on("click",function(e){
			parent.DlgUtil.close("dlgThemKTHT");
		});
		
	}
	
	function loadGridData() {
	
		var _sql_par =[];

		_sql_par = RSUtil.setSysParam(_sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridKTHT,"DMC.KTHTLAYDL",_sql_par);
	}
	
	function setEnabled(_ena, _dis) {
		for (var i =0; i<_ena.length; i++) {
			$("#"+_ena[i]).attr('disabled', false);
		}
		for (var i =0; i<_dis.length; i++) {
			$("#"+_dis[i]).attr('disabled', true);
		}
	}
	
}
