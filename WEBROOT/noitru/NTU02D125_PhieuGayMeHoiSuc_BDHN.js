function NTU02D125_PhieuGayMeHoiSuc(_opts) {
	this.load = doLoad;
	var SO_COT = 24;
	var _rpt_code_kyso = 'PHIEU_CA_GMHS_965';
	// duonghn fix 230509 start
	if (_opts.csytid == "10284") {
		$("#divTieuDeBDHNI").show();
	} else {
		$("#divTieuDe").show();
		var csytInfArr = jsonrpc.AjaxJson.ajaxExecuteQueryO('ORG_GET_CSYT', []);
		var csytInfArr = JSON.parse(csytInfArr);
		var sql_par = [];
		sql_par.push({
			"name" : "[0]",
			value : _opts.hosobenhanid
		});
		var	mabenhan = jsonrpc.AjaxJson.getOneValue('GET.MABENHAN', sql_par);
		if (csytInfArr && csytInfArr.length > 0) {
			var csytInf = csytInfArr[0];
			$("#lblDonViCha").html("Sở Y tế: " + csytInf.PARENT_NAME);
			$("#lblDonVi").html("BV: " + csytInf.ORG_NAME);
			$("#lblKhoa").html("Khoa: " + _opts.dept_name);
			$("#lblSoVaoVien").html("Số vào viện:");
			$("#lblMaBenhAn").html("Mã Bệnh án: " + mabenhan);
		}
	}
	// duonghn fix 230509 end
	var html0 = $('#gmhs-trang-0')[0].outerHTML;
	var NTU_PGMHS_TG_KT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PGMHS_TG_KT');
	//var ARR_DISTICT_THUOC_NGOAI = [];
	//var ARR_DISTICT_DICHTRUYEN_NGOAI = [];
	//var ARR_DISTICT_THUOC = [];
	//var ARR_DISTICT_DICHTRUYEN = [];
	function doLoad() {
		initControl();
		bindEvent();
		setInterface();
	}
	function initControl() {
		fillTTChung2(0); // fill tên tuổi, chẩn đoán
		generateComboBox();
		//generateCanvas();
		bindCanvasWithHeader2(0, 0);
	}
	function generateComboBox() {
		generateComboBoxPhieu();
	}
	function generateCanvas() {
		var pCanvasTruoc = '#gmhs-trang-' + page + ' .cvBieuDo';
		var _numLine = 6;
		var _numCol = SO_COT;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 1820;
		var _stopY = 380;
		var _lineHeight = 18.9;
		var _lineWidth = 43.94;
		var _numLine = 15;
		var _cvHeight = 301;
		var _cvwidth = 1050;
		$(pCanvasTruoc).attr('width', _cvwidth);
		$(pCanvasTruoc).attr('height', _cvHeight);
		for (var _i = 0; _i <= _numLine + 1; _i++) {
			if (((_i - 1) % 5) == 0) {
				$(pCanvasTruoc).drawLine({
					strokeStyle : '#7F7F83',
					strokeWidth : 1.5,
					x1 : _startX + 2,
					y1 : _i * _lineHeight,
					x2 : _stopX,
					y2 : _i * _lineHeight
				});
				$(pCanvasTruoc).drawLine({
					strokeStyle : '#7F7F83',
					strokeWidth : 1.5,
					x1 : _startX + 2,
					y1 : _i * _lineHeight,
					x2 : _stopX,
					y2 : _i * _lineHeight
				});
				$(pCanvasTruoc).drawLine({
					strokeStyle : '#7F7F83',
					strokeWidth : 1.5,
					x1 : _startX + 2,
					y1 : _i * _lineHeight,
					x2 : _stopX,
					y2 : _i * _lineHeight
				});
			} else {
				$(pCanvasTruoc).drawLine({
					strokeDash : [ 2 ],
					strokeStyle : '#7F7F83',
					strokeWidth : 1.5,
					x1 : _startX + 2,
					y1 : _i * _lineHeight,
					x2 : _stopX,
					y2 : _i * _lineHeight
				});
			}
		}
		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			//if (_i % 2 == 0) {
			$(pCanvasTruoc).drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1,
				y1 : _startY + _lineHeight,
				x2 : _i * _lineWidth + 1,
				y2 : _stopY - 2
			});
			//}
		}
		// Cột tiêu đề
		for (var _i = 0; _i <= _numCol - 2; _i++) {
			//if (_i % 2 == 0 && _i != 0) {
			var text = (_i * 10) % 60;
			if (text == 0) {
				continue;
			}
			$(pCanvasTruoc).drawText({
				fillStyle : '#000',
				strokeStyle : '#000',
				strokeWidth : 1,
				fontSize : 11,
				fontFamily : 'Verdana, sans-serif',
				x : _i * _lineWidth,
				y : _startY + 10,
				text : (_i * 10) % 60
			});
			//}
		}
		// fill so gio vao dau
		if (pData) {
			if (pData.DATA_GIO.length > 0) {
				var gio = pData.DATA_GIO[page - 1].GIO;
				$(pCanvasTruoc).drawText({
					fillStyle : '#000',
					strokeStyle : '#f50909',
					strokeWidth : 1,
					fontSize : 15,
					fontFamily : 'Verdana, sans-serif',
					x : 15,
					y : _startY + 10,
					text : gio + "h"
				});
			}
		}
	}
	function generateComboBoxPhieu() {
		var obj = {
			idgmhs : $('#hdf_ID_GAYMEHOISUC').val() + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.19", param);
		if (result && result.length > 0) {
			$('#sel_Trang').html('');
			var _html = '<option value="0">- Chọn tờ -</option>';
			for (var i = 0; i < result.length; i++) {
				var idgmhsct = result[i].ID_GAYMEHOISUC_CT;
				var phienban = result[i].PHIENBAN;
				//check ký CA
				var _params = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : _opts.hosobenhanid
				}, {
					name : 'khambenhid',
					type : 'String',
					value : _opts.khambenhid
				}, {
					name : 'dichvukhambenhid',
					type : 'String',
					value : $('#hdf_ID_DICHVUKHAMBENH').val() == '' ? '-1' : $('#hdf_ID_DICHVUKHAMBENH').val()
				}, {
					name : 'phieuid',
					type : 'String',
					value : idgmhsct
				}, {
					name : 'rpt_code',
					type : 'String',
					value : _rpt_code_kyso
				} ];
				var checkky = CommonUtil.checkKyCaByParam(_params);
				if (checkky == '1') {
					_html += '<option value="' + idgmhsct + '"> Tờ số ' + (i + 1) + ' tạo lúc ' + phienban + ' (Đã ký số)</option>';
				} else {
					_html += '<option value="' + idgmhsct + '"> Tờ số ' + (i + 1) + ' tạo lúc ' + phienban + '</option>';
				}
			}
			$('#sel_Trang').html(_html);
		} else {
			var _html = '<option value="0">- Chọn tờ -</option>';
			$('#sel_Trang').html(_html);
		}
	}
	function cleanPage() {
		for (var i = 1; i <= 24; i++) {
			$('#gmhs-trang-' + i).remove();
		}
	}
	function bindEvent() {
		$("#btnLamMoi").on("click", function(e) {
			generateComboBoxPhieu();
			cleanPage();
			fillTTChung2();
			// neu co du lieu thi chon luon to 1
			// var length = $('#sel_Trang > option').length;
			// if (length > 1){
			$('#sel_Trang option:eq(1)').attr('selected', 'selected');
			$('#sel_Trang').change();
			//}
		});
		$("#btnMatTruoc").on("click", function(e) {});
		$("#btnMatSau").on("click", function(e) {});
		$("#btnEdit").on(
				"click",
				function(e) {
					var paramInput = {
						idGayMeHoiSuc : _opts.idGayMeHoiSuc,
						hosobenhanid : _opts.hosobenhanid,
						khambenhid : _opts.khambenhid,
						dichvukhambenhid : $('#hdf_ID_DICHVUKHAMBENH').val()
					};
					var dlgPopup = DlgUtil.buildPopupUrl("divDlgGMHS", "divDlgGMHS", "manager.jsp?func=../noitru/NTU02D125_PhieuGayMeHoiSuc_Details_BDHN", paramInput, "Chi tiết phiếu GMHS",
							window.innerWidth * 0.9, 700);
					dlgPopup.open();
				});
		$("#btn_in_trang").on("click", function(e) {
			window.print();
		});
		$("#btnKySo").on("click", function(e) {
			if (_opts.khambenhid == "0" || _opts.khambenhid == "") {
				DlgUtil.showMsg("Không lấy được thông tin bệnh nhân để ký số");
				return;
			}
			_caRpt('1');
		});
		$("#btnHuyKy").on("click", function(e) {
			if (_opts.khambenhid == "0" || _opts.khambenhid == "") {
				DlgUtil.showMsg("Không lấy được thông tin bệnh nhân để ký số");
				return;
			}
			_caRpt('2');
		});
		$("#btnInKySo").on("click", function(e) {
			if (_opts.khambenhid == "0" || _opts.khambenhid == "") {
				DlgUtil.showMsg("Không lấy được thông tin bệnh nhân để ký số");
				return;
			}
			_caRpt('0');
		});
		$("#btnHuy").on("click", function(e) {
			window.close();
		});
		$("#sel_Trang").on("change", function(e) {
			// các xử lý luôn luôn (dạng reset)
			cleanPage(); // xóa các trang html mới build thêm, chỉ giữ lại gmhs-trang-0 (dạng chưa có canvas)
			$('#gmhs-trang-0').replaceWith(html0); // xóa dữ liệu đã được fill trước đó (đưa gmhs-trang-0 về dạng trắng chưa có canvas)
			bindCanvasWithHeader2(0, 0); // vẽ lại canvas dạng ko dữ liệu nhưng có header cho page 0 để nhìn cho đỡ trắng xóa
			fillTTChung2(0); // fill tên tuổi, chẩn đoán
			var idgmhsct = $('#sel_Trang').val();
			if (idgmhsct !== '0') {
				var dataASA = getTTGMHSCT(idgmhsct);
				var dataTabThongTinTheoDoi = getTTTDFromDB(idgmhsct);
				tunningObjectSapXepDongDeuChiSo(dataTabThongTinTheoDoi);
				var thoiGianBatDauMe = dataTabThongTinTheoDoi.TGBATDAUME;
				var thoiGianKetThucMo = dataTabThongTinTheoDoi.TGKETTHUCMO;
				// so trang = so phan tu cua dataTabThongTinTheoDoi.DULIEUHANG /SO_COT, lay phan nguyen
				var soLuongPhut = dataTabThongTinTheoDoi.DULIEUHANG.length;
				// L2PT-25770 duonghn start: Số trang = số cột phút / số cột phút 1 trang, làm tròn lên
				var soTrang = Math.ceil(soLuongPhut / (SO_COT - 1));
				/*
				var soTrangNguyen = (soLuongPhut - soLuongPhut % SO_COT) / SO_COT; // chia lay phan nguyen (SO_COT phut 1 trang)
				// 5.6666 -> 6 trang
				// 5.0 -> 5 trang
				var soTrang;
				if (soTrangThapPhan - soTrangNguyen > 0) {
					soTrang = soTrangNguyen + 1
				} else {
					soTrang = soTrangNguyen;
				}
				*/
				// L2PT-25770 duonghn end
				// buil số trang trắng tương ứng
				for (var i = 1; i <= soTrang - 1; i++) {
					// lấy ra html 1 trang dạng giống gmhs-trang-0 nhưng đổi id (trắng, chưa có dữ liệu và chưa có canvas)
					var html = html0.replace('id="gmhs-trang-0"', 'id="gmhs-trang-' + i + '"');
					$('#gmhs-trang-' + (i - 1)).after(html);
				}
				// vẽ các header cột (phút) tương ứng cho canvas
				for (var i = 0; i <= soTrang - 1; i++) {
					bindCanvasWithHeader2(thoiGianBatDauMe, i, dataTabThongTinTheoDoi);
				}
				// fill dữ liệu vào mỗi trang
				for (var i = 0; i < soTrang; i++) {
					fillTTChung2(i);
					bindASANhietDo(i, dataASA, dataTabThongTinTheoDoi);
					bindDataCanvas(i, dataTabThongTinTheoDoi);
					// bindNhietDoMatMau(ttChiTiet, i);
					// bindCanvasWithHeader(ttChiTiet, i);
					// bindCanvasWithData( ttChiTiet, i);
				}
				// fill dữ liệu vào canvas mỗi trang
			}
			//check ký CA
			var _params = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : _opts.hosobenhanid
			}, {
				name : 'khambenhid',
				type : 'String',
				value : _opts.khambenhid
			}, {
				name : 'dichvukhambenhid',
				type : 'String',
				value : $('#hdf_ID_DICHVUKHAMBENH').val() == '' ? '-1' : $('#hdf_ID_DICHVUKHAMBENH').val()
			}, {
				name : 'phieuid',
				type : 'String',
				value : $('#sel_Trang').val()
			}, {
				name : 'rpt_code',
				type : 'String',
				value : _rpt_code_kyso
			} ];
			var checkky = CommonUtil.checkKyCaByParam(_params);
			if (checkky == '1') {
				$("#btnKySo").prop("disabled", true);
				$("#btnHuyKy").prop("disabled", false);
				$("#btnInKySo").prop("disabled", false);
			} else {
				$("#btnKySo").prop("disabled", false);
				$("#btnHuyKy").prop("disabled", true);
				$("#btnInKySo").prop("disabled", true);
			}
		});
		// thiết kế cũ
		/*
		$("#sel_Trang2").on("change", function(e) {
			var idgmhsct = $('#sel_Trang').val();
			cleanPage();
			$('#gmhs-trang-0').replaceWith(html0);
			bindCanvasWithHeader({DATA_GIO: []}, 0);
			fillTTChung();
			if (idgmhsct !== '0') {
				var ttChiTiet = getTTChiTiet(idgmhsct);
				ttChiTiet = themThongTinDataGio(ttChiTiet);
				 //ttChiTiet = mergeThuocDTVaoGio(ttChiTiet, 'thuoc');// mỗi thành phần của DATA_GIO có 1 thuộc tính là THUOC, THUOC là 1 mảng các object có các thông tin như id, tên...
				//ttChiTiet = mergeThuocDTVaoGio(ttChiTiet, 'dichtruyen');

				ttChiTiet = dongDeuGioThuocDichTruyen(ttChiTiet);// chuyển các thuộc tính THUOC, DICHTRUYEN của DATA_GIO thành mảng, đồng thời đồng đều các chỉ số.

				ttChiTiet = mergeThoiGianMeMoVaoGio(ttChiTiet);
				ttChiTiet = orderGio(ttChiTiet);

				var soGioCoDuLieu = ttChiTiet.DATA_GIO.length;
				// 0 -> 4 gio - them 0 trang (mac dinh gmhs-trang-0)
				// 5 -> 8 gio - them 1 trang (gmhs-trang-0, gmhs-trang-1)
				// 9 -> 12 gio -them 2 trang (gmhs-trang-0, gmhs-trang-1, gmhs-trang-2)
				// ...
				var chiaLayPhanNguyen = (soGioCoDuLieu - soGioCoDuLieu%4)/4; // chia lay phan nguyen (4h 1 trang)
				var chiaLayPhanDu = soGioCoDuLieu%4;
				var soTrangCanThem = 0;
				if (chiaLayPhanDu == 0) {
					soTrangCanThem = chiaLayPhanNguyen - 1;
				} else {
					soTrangCanThem = chiaLayPhanNguyen;
				}
				builSoTrang(soTrangCanThem);
				for (var i = 0; i<= soTrangCanThem; i++) {
					bindNhietDoMatMau(ttChiTiet, i);
					bindCanvasWithHeader(ttChiTiet, i);
					bindCanvasWithData( ttChiTiet, i);
				}
			}
		});
		*/
	}
	function getArrMachHuyetAp2(page, dataTabThongTinTheoDoi, prop) {
		// page 0 ung voi SO_COT phut dau tien
		// page 1 ung voi SO_COT phut tiep theo
		// ...
		// lấy ra SO_COT giờ phút tương ứng với page
		var arrGioPhutPage = getArrGioPhutPage(dataTabThongTinTheoDoi.TGBATDAUME, page, dataTabThongTinTheoDoi.DULIEUHANG);
		// mảng chứa SO_COT giá trị để trả về
		var arrValue = new Array(SO_COT).fill("");
		// gán các giá trị phù hợp vào arrValue
		for (var j = 0; j < arrGioPhutPage.length; j++) {
			for (var i = 0; i < dataTabThongTinTheoDoi.DULIEUHANG.length; i++) {
				var objDataGioPhut = dataTabThongTinTheoDoi.DULIEUHANG[i];
				var objGioPhut = arrGioPhutPage[j];
				if (objGioPhut && objGioPhut.gio == objDataGioPhut.GIO && objGioPhut.phut == objDataGioPhut.PHUT) {
					arrValue[j] = objDataGioPhut[prop];
				}
			}
		}
		return arrValue;
	}
	/*
	trả về 1 mảng các object dạng
	[
	{
				IDTHUOC: "12313",
				TENTHUOC: "abc dè",
				GIATRI: "1#2#3#4#5#6#7#8#9#10#11#12#1#2#3#4#5#6#7#8#9#10#11#12#1#2#3#4#5#6#7#8#9#10#11#12#1#2#3#4#5#6#7#8#9#10#11#12#"
	},
	...
	]
	* */
	function getArrThuocDT22(page, dataTabThongTinTheoDoi, classname) {
		var arrThuocDT = [];
		if (classname == "thuoc") {
			arrThuocDT = JSON.parse(JSON.stringify(dataTabThongTinTheoDoi.DSTHUOC));
		} else {
			arrThuocDT = JSON.parse(JSON.stringify(dataTabThongTinTheoDoi.DSDICHTRUYEN));
		}
		// page 0 ung voi SO_COT phut dau tien
		// page 1 ung voi SO_COT phut tiep theo
		// ...
		// update
		// page 0 ứng với SO_COT phần tử đầu tiên của dataTabThongTinTheoDoi
		// page 1 ứng với SO_COT phần tử tiếp theo của dataTabThongTinTheoDoi...
		// lấy ra SO_COT giờ phút tương ứng với page
		var arrGioPhutPage = getArrGioPhutPage(dataTabThongTinTheoDoi.TGBATDAUME, page, dataTabThongTinTheoDoi.DULIEUHANG);
		// gán các giá trị phù hợp vào arrThuocDT
		for (var i = 0; i < arrThuocDT.length; i++) {
			var thuocDT = arrThuocDT[i];
			var giaTri = "";
			for (var j = 0; j < arrGioPhutPage.length; j++) {
				var objGioPhut = arrGioPhutPage[j];
				var giaTriTaiPhut = "#";
				for (var k = 0; k < dataTabThongTinTheoDoi.DULIEUHANG.length; k++) {
					var objDataGioPhut = dataTabThongTinTheoDoi.DULIEUHANG[k];
					if (objGioPhut && objDataGioPhut.GIO == objGioPhut.gio && objDataGioPhut.PHUT == objGioPhut.phut) {
						var arrTDT = [];
						if (classname == "thuoc") {
							arrTDT = objDataGioPhut.THUOC;
						} else {
							arrTDT = objDataGioPhut.DICHTRUYEN;
						}
						for (var z = 0; z < arrTDT.length; z++) {
							if (arrTDT[z].IDTHUOC == thuocDT.IDTHUOC) {
								giaTriTaiPhut = arrTDT[z].GIATRI + "#";
								break;
							}
						}
					}
				}
				giaTri = giaTri + "" + giaTriTaiPhut;
			}
			thuocDT.GIATRI = giaTri;
		}
		return arrThuocDT;
	}
	function fillHTMLThuocDichTruyen22(page, dataTabThongTinTheoDoi, classname) {
		var lengthThuocVT;
		var textHeader;
		if (classname == 'thuoc') {
			textHeader = 'Thuốc';
		} else {
			textHeader = 'Dịch truyền';
		}
		var arrThuocDT = getArrThuocDT22(page, dataTabThongTinTheoDoi, classname);
		if (arrThuocDT.length > 0) {
			let dataDVTS = {};
			let ntu_gmhs_tdt_donvi = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_GMHS_TDT_DONVI');
			if (ntu_gmhs_tdt_donvi == '1') {
				var obj = new Object();
				obj.IDTHUOC = arrThuocDT.map(el => el.IDTHUOC).join(",");
				obj.ID_GAYMEHOISUC_CT = $('#sel_Trang').val();
				dataDVTS = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.DVT.01", JSON.stringify(obj));
				if (dataDVTS != null && dataDVTS.length > 0) {
					dataDVTS = dataDVTS.reduce(
						(accumulator, currentValue) =>
						{accumulator[""+ currentValue.THUOCVATTUID] = currentValue.TEN_DVT; return accumulator}
						, {})
				}
			}
			// hàng đầu tiên
			var firstThuocDT = arrThuocDT[0];
			var html1 = '' + '            <td rowspan="' + arrThuocDT.length + '">' + textHeader + '</td>\n' + '            <td colspan="3">' + firstThuocDT.TENTHUOC + '</td>' + '';
			var arrGiaTri = firstThuocDT.GIATRI.split("#");
			var tongHang = 0;
			for (var i = 0; i < SO_COT; i++) {
				html1 += '' + '<td class="cot-chiso">' + arrGiaTri[i] + '</td>' + '';
				if (validator.isFloat(arrGiaTri[i])) {
					tongHang += parseFloat(arrGiaTri[i]);
				}
			}
			tongHang = Math.round(tongHang * 100) / 100;
			if (tongHang == 0) {
				tongHang = "";
			} else {
				tongHang = tongHang + (dataDVTS["" + firstThuocDT.IDTHUOC] ? (" " + dataDVTS["" + firstThuocDT.IDTHUOC]) : "");
			}
			html1 += '' + '<td class="cot-chiso">' + tongHang + '</td>' + '';
			$('#gmhs-trang-' + page + ' .' + classname).html(html1);
			// các hàng sau
			for (var i = arrThuocDT.length - 1; i > 0; i--) {
				var thuocDT = arrThuocDT[i];
				var html = '' + '        <tr>\n' + '            <td colspan="3">' + thuocDT.TENTHUOC + '</td>' + '';
				var arrGiaTri = thuocDT.GIATRI.split("#");
				var tongHang = 0;
				for (var j = 0; j < SO_COT; j++) {
					html += '' + '            <td class="cot-chiso">' + arrGiaTri[j] + '</td>' + '';
					if (validator.isFloat(arrGiaTri[j])) {
						tongHang += parseFloat(arrGiaTri[j]);
					}
				}
				tongHang = Math.round(tongHang * 100) / 100;
				if (tongHang == 0) {
					tongHang = "";
				} else {
					tongHang = tongHang + (dataDVTS["" + thuocDT.IDTHUOC] ? (" " + dataDVTS["" + thuocDT.IDTHUOC]) : "");
				}
				html += '' + '			<td class="cot-chiso">' + tongHang + '</td>' + '        </tr>';
				$('#gmhs-trang-' + page + ' .' + classname).after(html);
			}
		} else {
			var html = '' + '            <td rowspan="1">' + textHeader + '</td>\n' + '            <td colspan="3"></td>\n' + '';
			for (var i = 0; i < SO_COT; i++) {
				html += '<td class="cot-chiso"></td>\n';
			}
			$('#gmhs-trang-' + page + ' .' + classname).html(html);
		}
	}
	function fillHTML2(page, duLieuHang, thoiGianBatDauMe, prop, classname, text) {
		// page 0 ung voi SO_COT phut dau tien
		// page 1 ung voi SO_COT phut tiep theo
		// ...
		// update
		// page 0 ứng với SO_COT phần tử đầu tiên của dataTabThongTinTheoDoi
		// page 1 ứng với SO_COT phần tử tiếp theo của dataTabThongTinTheoDoi...
		// lấy ra SO_COT giờ phút tương ứng với page
		var arrGioPhutPage = getArrGioPhutPage(thoiGianBatDauMe, page, duLieuHang);
		// mảng chứa SO_COT giá trị để đưa lên giao diện
		var arrValue = new Array(SO_COT).fill("");
		// gán các giá trị phù hợp vào arrValue
		for (var j = 0; j < arrGioPhutPage.length; j++) {
			for (var i = 0; i < duLieuHang.length; i++) {
				var objDataGioPhut = duLieuHang[i];
				var objGioPhut = arrGioPhutPage[j];
				if (objGioPhut && objGioPhut.gio == objDataGioPhut.GIO && objGioPhut.phut == objDataGioPhut.PHUT) {
					arrValue[j] = objDataGioPhut[prop];
				}
			}
		}
		var _html = '';
		if (classname == 'cs_nhiptho') {
			_html += '<td rowspan="7">HÔ HẤP</td><td colspan="3">Nhịp thở</td>';
		} else if (classname == 'hh_ttlt') {
			_html += '' + '            <td rowspan="6">\n' + '                <p>Máy</p>\n' + '                <p>Mê</p>\n' + '            </td>\n'
					+ '            <td colspan="2" rowspan="2" class="background crossOut">\n' + '                <div><span class="top">TTLT2</span>\n'
					+ '                    <span class="bottom">FeCO2</span>\n' + '                </div>\n' + '            </td>' + '';
		} else if (classname == 'hh_feco2') {
			_html += '' + '' + '';
		} else if (classname == 'hh_apluc') {
			_html += '' + '            <td colspan="2" rowspan="2" class="background">\n' + '                <div><span class="top">Áp lực</span>\n'
					+ '                    <span class="bottom">SpO2</span>\n' + '                </div>\n' + '            </td>' + '';
		} else if (classname == 'hh_spo2') {
			_html += '' + '' + '';
		} else if (classname == 'hh_fio2') {
			_html += '' + '            <td colspan="2" rowspan="2" class="background">\n' + '                <div><span class="top">FiO2</span>\n'
					+ '                    <span class="bottom">%Sevorane</span>\n' + '                </div>\n' + '            </td>' + '';
		} else if (classname == 'hh_hal') {
			_html += '' + '' + '';
		} else if (classname == 'quansat') {
			_html += '' + '            <td >Quan sát</td>\n' + '            <td colspan="3"></td>' + '';
		} else {
			_html += '<td colspan="4" class="' + classname + '">' + text + '</td>';
		}
		var tongHang = 0;
		for (var i = 0; i < SO_COT; i++) {
			_html += '<td class="cot-chiso">' + arrValue[i] + '</td>';
			if (classname == 'hh_hal') {
				if (validator.isFloat(arrValue[i])) {
					tongHang += parseFloat(arrValue[i]);
				}
			}
		}
		if (classname == 'hh_fio2') {
			_html += '' + '<td>TỔNG CỘNG</td>' + '';
		} else if (classname == 'quansat') {
			_html += '' + '<td>' + 'TỔNG THỜI GIAN MÊ' + '<p class="tt_tongthoigianme"></p>' + '</td>';
		} else if (classname == 'hh_hal') {
			_html += '' + '<td class="cot-chiso">' + tongHang + '</td>' + '';
		}
		$('#gmhs-trang-' + page + ' .' + classname).html(_html);
	}
	function drawKyHieuThoiGian(pCanvas, firstSymbol, secondSymbol, xPoint) {
		var yPoint = 25; // ngay duoi header
		$(pCanvas).drawText({
			fillStyle : '#FF1B4D',
			strokeStyle : '#FF1B4D',
			strokeWidth : 1,
			fontSize : 11,
			fontFamily : 'Verdana, sans-serif',
			x : xPoint,
			y : yPoint,
			text : firstSymbol
		});
		var xPoint2 = xPoint;
		var yPoint2 = yPoint + 30; // độ dài
		$(pCanvas).drawText({
			fillStyle : '#FF1B4D',
			strokeStyle : '#FF1B4D',
			strokeWidth : 1,
			fontSize : 11,
			fontFamily : 'Verdana, sans-serif',
			x : xPoint2,
			y : yPoint2,
			text : secondSymbol
		});
		$(pCanvas).drawLine({
			strokeStyle : '#FF1B4D',
			strokeWidth : 1,
			x1 : xPoint,
			y1 : yPoint + 2,
			x2 : xPoint2,
			y2 : yPoint2 - 2
		});
	}
	function setInterface() {
		//<BVTM-2260 ladinhtuan 31052021>
		var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (kyso_kydientu != "1") {
			$('#' + 'btnKySo').remove();
			$('#' + 'btnHuyKy').remove();
			$('#' + 'btnInKySo').remove();
		}
		//</BVTM-2260 ladinhtuan 31052021>
		fillTTChung();
	}
	function getTTChung() {
		var ttChung = {
			HO_TEN : "",
			TUOI : "",
			GIOITINH : "" //L2PT-101507
		}
		var obj = {
			idgmhs : $('#hdf_ID_GAYMEHOISUC').val() + "",
			khambenhid : $('#hdf_ID_KHAMBENH').val() + "",
			dichvukhambenhid : $('#hdf_ID_DICHVUKHAMBENH').val() + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.17", param);
		if (result && result.length > 0) {
			ttChung = result[0];
		}
		return ttChung;
	}
	function fillTTChung() {
		var ttChung = getTTChung();
		$('#hoten').html(fillDot(ttChung.HO_TEN, 50));
		$('#namsinh').html(ttChung.TUOI);
		$('#gioitinh').html(ttChung.GIOITINH);
		// if (ttChung.GIOITINH == '1') {
		// 	$('#gioitinh').html('Nam/<del>Nữ</del>');
		// } else if (ttChung.GIOITINH == '2') {
		// 	$('#gioitinh').html('<del>Nam/</del>/Nữ');
		// } else {
		// 	$('#gioitinh').html('Nam//Nữ');
		// }
	}
	//<BVTM-2260 ladinhtuan 31052021>
	function fillTTChung2(page) {
		var ttChung = getTTChung();
		$('#gmhs-trang-' + page + ' .hoten').html(fillDot(ttChung.HO_TEN, 50));
		$('#gmhs-trang-' + page + ' .namsinh').html(ttChung.TUOI);
		$('#gmhs-trang-' + page + ' .gioitinh').html(ttChung.GIOITINH);
		// if (ttChung.GIOITINH == '1') {
		// 	$('#gmhs-trang-' + page + ' .gioitinh').html('Nam/<del>Nữ</del>');
		// } else if (ttChung.GIOITINH == '2') {
		// 	$('#gmhs-trang-' + page + ' .gioitinh').html('<del>Nam/</del>/Nữ');
		// } else {
		// 	$('#gmhs-trang-' + page + ' .gioitinh').html('Nam//Nữ');
		// }
	}
	function _caRpt(signType) {
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'hosobenhanid',
			type : 'String',
			value : _opts.hosobenhanid
		}, {
			name : 'khambenhid',
			type : 'String',
			value : _opts.khambenhid
		}, {
			name : 'dichvukhambenhid',
			type : 'String',
			value : $('#hdf_ID_DICHVUKHAMBENH').val() == '' ? '-1' : $('#hdf_ID_DICHVUKHAMBENH').val()
		}, {
			name : 'phieuid',
			type : 'String',
			value : $('#sel_Trang').val()
		}, {
			name : 'rpt_code',
			type : 'String',
			value : _rpt_code_kyso
		} ];
		if (signType == '0') {
			CommonUtil.openReportGetCA2(par_rpt_KySo, false);
		} else {
			CommonUtil.kyCA(par_rpt_KySo, signType, true, false);
			EventUtil.setEvent("eventKyCA", function(e) {
				// DlgUtil.showMsg(e.res);
				alert(e.res);
			});
		}
	}

	function fillDot(pText, pSpace) {
		var _len = 0;
		var _str = '';
		if (pText != null && pText != '') {
			_len = pText.length;
		} else {
			pText = '';
			_len = 0;
		}
		if (_len < pSpace) {
			_str = new Array(pSpace - _len + 1).join('.');
			return pText + _str;
		} else {
			return pText;
		}
		return _str
	}
	// dựa vào giờ bắt đầu và số trang truyền vào
	// vẽ ra các header cột (phút) tương ứng cho canvas
	// thoiGianBatDauMe = 0 va page = 0 ứng với page 0 và ko có dữ liệu
	// page 0 ứng với SO_COT phần tử đầu tiên của dataTabThongTinTheoDoi
	// page 1 ứng với SO_COT phần tử tiếp theo của dataTabThongTinTheoDoi...
	function bindCanvasWithHeader2(thoiGianBatDauMe, page, dataTabThongTinTheoDoi) {
		var pCanvas = '#gmhs-trang-' + page + ' .cvBieuDo';
		var _numCol = SO_COT;
		var _startX = 0; // bắt đầu từ góc trên bên trái
		var _startY = 0; // bắt đầu từ góc trên bên trái
		var _stopX = 1820; // x là chiều ngang
		var _stopY = 380; // y là chiều dọc
		var _lineHeight = 18.9; // chiều cao 1 hàng (có 15 hoặc 20 hàng + 1 hàng header)
		var _lineWidth = 44; // chiều rộng 1 cột (thay đổi SO_COT thì phải thay đổi _lineWidth theo chiều ngược lại)
		var _numLine = 15; // 15 hàng
		var _cvHeight = 303; // chiều cao canvas
		var _cvwidth = 1058; // chiều rộng canvas
		// L2PT-24678 duonghn start: nếu có bản ghi có chỉ số Huyết Áp hoặc Mạch < 50 thì vẽ 20 hàng
		$(".huyetAp_MachThap").hide();
		if (dataTabThongTinTheoDoi && dataTabThongTinTheoDoi.DULIEUHANG) {
			try {
				for (var idx = 0; idx < dataTabThongTinTheoDoi.DULIEUHANG.length; idx++) {
					var huyetApIdx = dataTabThongTinTheoDoi.DULIEUHANG[idx].HUYETAP;
					var huyetApDuoi = "";
					if (huyetApIdx != "" && huyetApIdx.length > 0) {
						var mangHuyetAp = huyetApIdx.split("/");
						if (mangHuyetAp.length > 1) {
							huyetApDuoi = parseFloat(mangHuyetAp[1]);
						}
					}
					var machIdx = dataTabThongTinTheoDoi.DULIEUHANG[idx].MACH;
					var chiSoMach = "";
					if (machIdx != "" && machIdx.length > 0) {
						chiSoMach = parseFloat(machIdx);
					}
					if ((huyetApDuoi != "" && huyetApDuoi < 50) || (chiSoMach != "" && chiSoMach < 50)) {
						$(".huyetAp_MachThap").show();
						_cvHeight = 404;
						_cvwidth = 1058;
						_stopY = 404;
						_numLine = 20;
						break;
					}
				}
				// L2PT-25770 duonghn start: thêm độ dài trang in nếu có nhiều thuốc/ dịch truyền đi kèm hoặc có mạch / huyết áp dưới 50
				var slThuocDichTruyen = dataTabThongTinTheoDoi.DSTHUOC.length + dataTabThongTinTheoDoi.DSTHUOC.length;
				var moRongTrangIn = (slThuocDichTruyen > 10 ? (slThuocDichTruyen - 10) * _lineHeight : 0) + _cvHeight - 303;
				$("#gmhs-trang-" + page).height($("#gmhs-trang-" + page).height() + moRongTrangIn);
				// L2PT-25770 duonghn end
			} catch (err) {
				console.log(err);
			}
		}
		$(pCanvas).attr('width', _cvwidth);
		$(pCanvas).attr('height', _cvHeight);
		// L2PT-24678 duonghn end
		// vẽ 15 hoặc 20 hàng trong đó có 3 hàng nét liền, còn lại nét đứt
		for (var _i = 1; _i <= _numLine + 1; _i++) { // L2PT-24678 duonghn: bắt đầu vẽ từ hàng 1
			if (((_i - 1) % 5) == 0) {
				$(pCanvas).drawLine({
					strokeStyle : '#7F7F83',
					strokeWidth : 1.5,
					x1 : _startX + 2,
					y1 : _i * _lineHeight,
					x2 : _stopX,
					y2 : _i * _lineHeight
				});
			} else {
				$(pCanvas).drawLine({
					strokeDash : [ 2 ],
					strokeStyle : '#7F7F83',
					strokeWidth : 1.5,
					x1 : _startX + 2,
					y1 : _i * _lineHeight,
					x2 : _stopX,
					y2 : _i * _lineHeight
				});
			}
		}
		// vẽ SO_COT cột (chưa có header giờ phút)
		for (var _i = 0; _i <= SO_COT; _i++) {
			$(pCanvas).drawLine({
				strokeStyle : '#7F7F83',
				strokeWidth : 1,
				x1 : _i * _lineWidth + 1,
				y1 : _startY + _lineHeight,
				x2 : _i * _lineWidth + 1,
				y2 : _stopY - 2
			});
		}
		// page 0 và 0 có dữ liệu (lúc mới vào màn)
		// vẽ 0, 1, 2... thay vì giờ phút vào header
		if (page == 0 && thoiGianBatDauMe == 0) {
			for (var i = 0; i < SO_COT; i++) {
				$(pCanvas).drawText({
					fillStyle : '#000',
					strokeStyle : '#000',
					strokeWidth : 1,
					fontSize : 11,
					fontFamily : 'Verdana, sans-serif',
					x : i * _lineWidth + 10,
					y : _startY + 10,
					text : i
				});
			}
			return;
		}
		// vẽ header SO_COT phút tương ứng với page
		var arrPhutPage = getArrPhutPage(thoiGianBatDauMe, page, dataTabThongTinTheoDoi);
		for (var i = 0; i < arrPhutPage.length; i++) {
			var time = arrPhutPage[i];
			if (time) {
				// L2PT-24004 L2PT-23895 duonghn start
				// giờ thì vẽ màu đỏ
				$(pCanvas).drawText({
					fillStyle : '#000',
					strokeStyle : time.indexOf("h") > 0 ? '#f50909' : '#000',
					strokeWidth : 1,
					fontSize : 11,
					fontFamily : 'Verdana, sans-serif',
					x : i * _lineWidth,
					y : _startY + 10,
					text : time
				});
				// L2PT-24004 L2PT-23895 end
			}
		}
		// vẽ ký hiệu thời gian bắt đầu me, bat dau mo, ket thuc mo
		if (dataTabThongTinTheoDoi) {
			var gioBatDauMe;
			var phutBatDauMe;
			// L2PT-30319 duonghn: thêm thời gian kết thúc mê
			var gioKetThucMe;
			var phutKetThucMe;
			var gioBatDauMo;
			var phutBatDauMo;
			var gioKetThucMo;
			var phutKetThucMo;
			var thoiGianKetThucMe = dataTabThongTinTheoDoi.TGKETTHUCME;
			var thoiGianBatDauMo = dataTabThongTinTheoDoi.TGBATDAUMO;
			var thoiGianKetThucMo = dataTabThongTinTheoDoi.TGKETTHUCMO;
			gioBatDauMe = thoiGianBatDauMe.substring(11, 13); // 14
			phutBatDauMe = thoiGianBatDauMe.substring(14, 16); // 28
			gioKetThucMe = thoiGianKetThucMe.substring(11, 13);
			phutKetThucMe = thoiGianKetThucMe.substring(14, 16);
			gioBatDauMo = thoiGianBatDauMo.substring(11, 13);
			phutBatDauMo = thoiGianBatDauMo.substring(14, 16);
			gioKetThucMo = thoiGianKetThucMo.substring(11, 13);
			phutKetThucMo = thoiGianKetThucMo.substring(14, 16);
			// update
			// page 0 ứng với SO_COT phần tử đầu tiên của dataTabThongTinTheoDoi
			// page 1 ứng với SO_COT phần tử tiếp theo của dataTabThongTinTheoDoi...
			// lấy ra SO_COT giờ phút tương ứng với page
			// L2PT-30319: check theo ngày theo dõi
			var arrGioPhutPage = getArrGioPhutPage(thoiGianBatDauMe, page, dataTabThongTinTheoDoi.DULIEUHANG);
			for (var i = 0; i < arrGioPhutPage.length; i++) {
				var objGioPhut = arrGioPhutPage[i];
				if (typeof objGioPhut != 'object' || typeof objGioPhut.ngaytheodoi == 'undefined') {
					continue;
				}
				if (thoiGianBatDauMe.substring(0, 16) == objGioPhut.ngaytheodoi.substring(0, 16) && objGioPhut.gio == gioBatDauMe && objGioPhut.phut == phutBatDauMe) {
					var xPoint = _lineWidth * i;
					drawKyHieuThoiGian(pCanvas, 'o', 'v', xPoint);
				}
				// L2PT-30319 // L2PT-33068 duonghn start: ve ky hieu ket thuc me
				if (thoiGianKetThucMe.substring(0, 16) == objGioPhut.ngaytheodoi.substring(0, 16) && objGioPhut.gio == gioKetThucMe && objGioPhut.phut == phutKetThucMe) {
					var xPoint = _lineWidth * i;
					drawKyHieuThoiGian(pCanvas, '^', 'o', xPoint);
				}
				// L2PT-30319 // L2PT-33068 end
				if (thoiGianBatDauMo.substring(0, 16) == objGioPhut.ngaytheodoi.substring(0, 16) && objGioPhut.gio == gioBatDauMo && objGioPhut.phut == phutBatDauMo) {
					var xPoint = _lineWidth * i;
					drawKyHieuThoiGian(pCanvas, 'x', 'v', xPoint);
				}
				if (thoiGianKetThucMo.substring(0, 16) == objGioPhut.ngaytheodoi.substring(0, 16) && objGioPhut.gio == gioKetThucMo && objGioPhut.phut == phutKetThucMo) {
					var xPoint = _lineWidth * i;
					drawKyHieuThoiGian(pCanvas, '^', 'x', xPoint);
				}
			}
		}
	}
	// hàm này trả về mảng SO_COT phút ứng với page. nếu rơi vào phút 0 thì thay bằng giờ + "h"
	// "thoiGianBatDauMe": "01/03/2022 07:41:38", -->để fill giờ
	// page 0 ứng với SO_COT phần tử đầu tiên của dataTabThongTinTheoDoi
	// page 1 ứng với SO_COT phần tử tiếp theo của dataTabThongTinTheoDoi...
	// update: thêm giờ vào nếu không có giờ:00
	function getArrPhutPage(thoiGianBatDauMe, page, dataTabThongTinTheoDoi) {
		var arrKetQua = [];
		if (thoiGianBatDauMe == 0 && page == 0) {
			return Array.from(Array(SO_COT + 1).keys());
		}
		var arrAllGioPhut = [];
		var duLieuHang = dataTabThongTinTheoDoi.DULIEUHANG;
		for (var i = 0; i < duLieuHang.length; i++) {
			var obj = duLieuHang[i];
			var gio = obj.GIO;
			var phut = obj.PHUT;
			// lấy giờ đưa vào mảng ở đầu mỗi khoảng giờ
			// phút = 0 thì không đưa vào mảng
			// L2PT-24004 L2PT-23895 duonghn start
			// L2PT-25770 duonghn: thêm dòng trống ở mỗi trang
			if (i % (SO_COT - 1) == 0) {
				arrAllGioPhut.push("");
			}
			// L2PT-30319 duonghn: thêm giờ vào cột đầu tiên của mỗi trang hoặc mỗi giờ mới
			if ((i % (SO_COT - 1) == 0) || (i > 0 && gio != duLieuHang[i - 1].GIO)) {
				arrAllGioPhut.push(gio + "h" + phut);
			} else {
				arrAllGioPhut.push(phut + "");
			}
			//  L2PT-24004L2PT-23895 end
		}
		// 0: 0->23
		// 1: 24 -> 24*2 -1
		// 2: 48 -> 24*3 -1
		// i: i*SO_COT -> (i+1)*SO_COT - 1
		var start = page * SO_COT;
		var end = (page + 1) * SO_COT - 1;
		for (var i = start; i <= end; i++) {
			// L2PT-30319 duonghn start: nếu không có đủ SO_COT phần tử, đẩy phần tử mặc định vào
			var gp = arrAllGioPhut[i] ? arrAllGioPhut[i] : "";
			arrKetQua.push(gp);
			// L2PT-30319 duonghn end
		}
		return arrKetQua;
		// var arr = [];
		// var thoiGianBatDauMeMoment = moment(thoiGianBatDauMe, 'DD/MM/YYYY hh:mm:ss');
		// var thoiGianBatDauMeMomentStartPage = thoiGianBatDauMeMoment.add(SO_COT * page,'minutes');
		// var thoiGianBatDauMeMomentEndPage = thoiGianBatDauMeMomentStartPage.clone();
		// thoiGianBatDauMeMomentEndPage.add(SO_COT,'minutes');
		//
		// var t = thoiGianBatDauMeMomentStartPage.clone();
		// if (page == 0 || t.minute() == 0 ) {
		// 	arr.push(t.hour() + "h");
		// } else {
		// 	arr.push(t.minute() + "");
		// }
		// while (t < thoiGianBatDauMeMomentEndPage ) {
		// 	t.add(1,'minutes');
		// 	// nếu là phút không thì add giờ
		// 	if (t.minute() == 0) {
		// 		arr.push(t.hour() + "h");
		// 	} else {
		// 		arr.push(t.minute() + "");
		// 	}
		// }
		// // xoa di phan tu cuoi cung
		// arr.pop();
		// return arr;
	}
	// tương tư getArrPhutPage nhưng trả về mảng các object gồm giờ và phút
	function getArrGioPhutPage(thoiGianBatDauMe, page, duLieuHang) {
		var arrKetQua = [];
		var arrAllGioPhut = [];
		for (var i = 0; i < duLieuHang.length; i++) {
			var obj = duLieuHang[i];
			var ngaytheodoi = duLieuHang[i].NGAYTHEODOI; // L2PT-30319
			var gio = obj.GIO;
			var phut = obj.PHUT;
			// lấy giờ đưa vào mảng ở đầu mỗi khoảng giờ
			// phút = 0 thì không đưa vào mảng
			// L2PT-24004 L2PT-23895 duonghn start
			if (i % (SO_COT - 1) == 0) { // L2PT-25770 duonghn: thêm dòng trống ở mỗi trang
				arrAllGioPhut.push({
					ngaytheodoi : '', // L2PT-30319
					gio : '',
					phut : ''
				});
			}
			arrAllGioPhut.push({
				ngaytheodoi : ngaytheodoi, // L2PT-30319
				gio : gio,
				phut : phut
			});
			// L2PT-24004 L2PT-23895 end
		}
		// 0: 0->47
		// 1: SO_COT -> SO_COT*2 -1
		// 2: 96 -> SO_COT *3 -1
		// i: i*SO_COT -> (i+1)*SO_COT - 1
		var start = page * SO_COT;
		var end = (page + 1) * SO_COT - 1;
		for (var i = start; i <= end; i++) {
			// L2PT-30319 duonghn start: nếu không có đủ SO_COT phần tử, đẩy phần tử mặc định vào
			var gp = arrAllGioPhut[i] ? arrAllGioPhut[i] : {
				ngaytheodoi : '',
				gio : '',
				phut : ''
			};
			arrKetQua.push(gp);
			// L2PT-30319 duonghn end
		}
		return arrKetQua;
		// var arr = [];
		// var thoiGianBatDauMeMoment = moment(thoiGianBatDauMe, 'DD/MM/YYYY hh:mm:ss');
		// var thoiGianBatDauMeMomentStartPage = thoiGianBatDauMeMoment.add(SO_COT * page,'minutes');
		// var thoiGianBatDauMeMomentEndPage = thoiGianBatDauMeMomentStartPage.clone();
		// thoiGianBatDauMeMomentEndPage.add(SO_COT,'minutes');
		//
		// var t = thoiGianBatDauMeMomentStartPage.clone();
		// arr.push({
		// 	gio: t.hour(),
		// 	phut: t.minute()
		// });
		// while (t < thoiGianBatDauMeMomentEndPage ) {
		// 	t.add(1,'minutes');
		// 	arr.push({
		// 		gio: t.hour(),
		// 		phut: t.minute()
		// 	});
		// }
		// // xoa di phan tu cuoi cung
		// arr.pop();
		// return arr;
	}
	function getTTGMHSCT(idgmhsct) {
		var gmhsct = {
			// DGTM_ASA : "",
			// DGTM_MALL : "",
			ASA : "0",
			MALLAMPATI : "0",
			DADAY : "0",
			CAPCUU : "0",
			TTCB_DIUNG : "",
			TTCB_TIENSU : "",
			DGTM_THUOCDUNG : "",
			DGTM_BATTHUONG : "",
			TTTD_TGBATDAUME : "",
			TTTD_TGKETTHUCME : "",
			TTTD_TGBATDAUMO : "",
			TTTD_TGKETTHUCMO : "",
			THUOC : "",
			DICHTRUYEN : "",
			KLDG_NHANXET : "",
			CHAN_DOAN : "0", //L2PT-101507
			NGAY_GAYME : "",
			TIEN_ME : "",
			TAC_DUNG : "",
			CAN_NANG : "",
			CHIEU_CAO : "",
			LOAIMO : "",
			TU_THE : "",
			NHOM_MAU : "",
			BS_PHAUTHUAT : "",
			BS_GAYME : "",
			NGUOITHUCHEO : "",
			PP_VOCAM : ""
		}
		var obj = {
			idgmhsct : idgmhsct + "",
			khambenhid : _opts.khambenhid + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.21", param);
		if (result && result.length > 0) {
			gmhsct = result[0];
		}
		return gmhsct;
	}
	// lay thong tin theo doi
	function getTTTDFromDB(idgmhsct) {
		var tttd = {
			TGBATDAUME : "",
			TGBATDAUMO : "",
			TGKETTHUCMO : "",
			DSTHUOC : [ {
				IDTHUOC : "",
				TENTHUOC : "",
			}, {
				IDTHUOC : "",
				TENTHUOC : "",
			} ],
			DSDICHTRUYEN : [ {
				IDTHUOC : "",
				TENTHUOC : "",
			}, {
				IDTHUOC : "",
				TENTHUOC : "",
			} ],
			DULIEUHANG : [ {
				ID_GMHS_GIOPHUT : "",
				GIO : "",
				PHUT : "",
				MACH : "",
				HUYETAP : "",
				SPO2 : "",
				THUOC : [ {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				}, {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				} ],
				DICHTRUYEN : [ {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				}, {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				} ],
				NHIETDO : "",
				MATMAU : "",
				NUOCTIEU : "",
				NHIPTHO : "",
				HOHAPTTLT : "",
				HOHAPFECO2 : "",
				APLUC : "",
				FIO2 : "",
				ALDMP : "",
				HAL : "",
				QUANSAT : ""
			}, {
				ID_GMHS_GIOPHUT : "",
				GIO : "",
				PHUT : "",
				MACH : "",
				HUYETAP : "",
				SPO2 : "",
				THUOC : [ {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				}, {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				} ],
				DICHTRUYEN : [ {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				}, {
					IDTHUOC : "",
					TENTHUOC : "",
					GIATRI : ""
				} ],
				NHIETDO : "",
				MATMAU : "",
				NUOCTIEU : "",
				NHIPTHO : "",
				HOHAPTTLT : "",
				HOHAPFECO2 : "",
				APLUC : "",
				FIO2 : "",
				ALDMP : "",
				HAL : "",
				QUANSAT : ""
			} ]
		}
		var obj = {
			idgmhsct : idgmhsct + "",
			khambenhid : _opts.khambenhid + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_C("NTU02D125.31", param);
		if (result) {
			tttd = JSON.parse(result);
			// lay du lieu hang
			tttd.DULIEUHANG = [];
			var duLieuHang = getDuLieuHang(idgmhsct);
			if (duLieuHang) {
				for (var i = 0; i < duLieuHang.length; i++) {
					var chiTiet = duLieuHang[i].CHITIET;
					var obj = JSON.parse(chiTiet);
					tttd.DULIEUHANG.push(obj);
				}
			}
		}
		return tttd;
	}
	function getDuLieuHang(idgmhsct) {
		var obj = {
			idgmhsct : idgmhsct + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.33", param);
		if (result && result.length > 0) {
			return result;
		} else {
			return null;
		}
	}
	function tunningObjectSapXepDongDeuChiSo(dataTabThongTinTheoDoi) {
		// order lại DSTHUOC, DSDICHTRUYEN theo IDTHUOC
		var NTU_PGMHS_SAPXEP_DSTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PGMHS_SAPXEP_DSTHUOC');
		if (NTU_PGMHS_SAPXEP_DSTHUOC == '0') {
			dataTabThongTinTheoDoi.DSTHUOC.sort(function(a, b) {
				var keyA = parseInt(a.IDTHUOC), keyB = parseInt(b.IDTHUOC);
				if (keyA < keyB)
					return -1;
				if (keyA > keyB)
					return 1;
				return 0;
			});
			dataTabThongTinTheoDoi.DSDICHTRUYEN.sort(function(a, b) {
				var keyA = parseInt(a.IDTHUOC), keyB = parseInt(b.IDTHUOC);
				if (keyA < keyB)
					return -1;
				if (keyA > keyB)
					return 1;
				return 0;
			});
		}

		// thời gian bắt đầu mê (= đầu) và kết thúc mổ (= cuối) luôn có
		// nếu chưa có thời gian bắt đầu mổ thì thêm nó vào
		// L2PT-30319 duonghn start: Thêm các mốc thời gian bắt đầu, kết thúc mê, mổ nếu chưa có
		/*var thoiGianBatDauMo = dataTabThongTinTheoDoi.TGBATDAUMO;
		var gioBatDauMo = thoiGianBatDauMo.substring(11, 13);
		var phutBatDauMo = thoiGianBatDauMo.substring(14, 16);
		var isTonTaiTGBDMo = false;
		for (var i = 0; i < dataTabThongTinTheoDoi.DULIEUHANG.length; i++) {
			var obj = dataTabThongTinTheoDoi.DULIEUHANG[i];
			if (obj.GIO == gioBatDauMo && obj.PHUT == phutBatDauMo) {
				isTonTaiTGBDMo = true;
				break;
			}
		}
		if (!isTonTaiTGBDMo) {
			var obj = {
				"ID_GMHS_GIOPHUT" : "",
				"GIO" : gioBatDauMo + "",
				"PHUT" : phutBatDauMo + "",
				"MACH" : "",
				"HUYETAP" : "",
				"NHIETDO" : "",
				"MATMAU" : "",
				"NUOCTIEU" : "",
				"ALDMP" : "",
				"NHIPTHO" : "",
				"HOHAPTTLT" : "",
				"HOHAPFECO2" : "",
				"APLUC" : "",
				"SPO2" : "",
				"FIO2" : "",
				"QUANSAT" : "",
				"HAL" : "",
				"THUOC" : [],
				"DICHTRUYEN" : []
			};
			dataTabThongTinTheoDoi.DULIEUHANG.push(obj);
		}*/
		// L2PT-30319 start
		/*var duLieuHang0 = dataTabThongTinTheoDoi.DULIEUHANG[0];
		for (var i = 0; i < parseInt(duLieuHang0.PHUT); i = i + 10) {
			var p = i + "";
			var t = duLieuHang0.NGAYTHEODOI.substring(0, 14) + p.padStart(2, '0') + ':00';
			themMocTG(t, dataTabThongTinTheoDoi);
		}*/
		// L2PT-30319 end
		themMocTG(dataTabThongTinTheoDoi.TGBATDAUME, dataTabThongTinTheoDoi);
		themMocTG(dataTabThongTinTheoDoi.TGBATDAUMO, dataTabThongTinTheoDoi);
		themMocTG(dataTabThongTinTheoDoi.TGKETTHUCME, dataTabThongTinTheoDoi);
		themMocTG(dataTabThongTinTheoDoi.TGKETTHUCMO, dataTabThongTinTheoDoi);
		// L2PT-30319 duonghn end
		// đồng đều các thông tin THUOC và DICHTRUYEN cho các phần tử của DULIEUHANG
		// backup để tí đưa GIATRI lại
		var duLieuHangBefore = JSON.parse(JSON.stringify(dataTabThongTinTheoDoi.DULIEUHANG));
		// gán ds distinct trực tiếp thay thế từng hàng thuốc
		for (var i = 0; i < dataTabThongTinTheoDoi.DULIEUHANG.length; i++) {
			dataTabThongTinTheoDoi.DULIEUHANG[i].THUOC = JSON.parse(JSON.stringify(dataTabThongTinTheoDoi.DSTHUOC));
			dataTabThongTinTheoDoi.DULIEUHANG[i].DICHTRUYEN = JSON.parse(JSON.stringify(dataTabThongTinTheoDoi.DSDICHTRUYEN));
		}
		// đưa GIATRI lại
		for (var i = 0; i < dataTabThongTinTheoDoi.DULIEUHANG.length; i++) {
			var thuocBefore = duLieuHangBefore[i].THUOC;
			for (var j = 0; j < dataTabThongTinTheoDoi.DULIEUHANG[i].THUOC.length; j++) {
				dataTabThongTinTheoDoi.DULIEUHANG[i].THUOC[j].GIATRI = "";
				for (var k = 0; k < thuocBefore.length; k++) {
					if (thuocBefore[k].IDTHUOC == dataTabThongTinTheoDoi.DULIEUHANG[i].THUOC[j].IDTHUOC) {
						dataTabThongTinTheoDoi.DULIEUHANG[i].THUOC[j].GIATRI = thuocBefore[k].GIATRI;
						break;
					}
				}
			}
			var dichTruyenBefore = duLieuHangBefore[i].DICHTRUYEN;
			for (var j = 0; j < dataTabThongTinTheoDoi.DULIEUHANG[i].DICHTRUYEN.length; j++) {
				dataTabThongTinTheoDoi.DULIEUHANG[i].DICHTRUYEN[j].GIATRI = "";
				for (var k = 0; k < dichTruyenBefore.length; k++) {
					if (dichTruyenBefore[k].IDTHUOC == dataTabThongTinTheoDoi.DULIEUHANG[i].DICHTRUYEN[j].IDTHUOC) {
						dataTabThongTinTheoDoi.DULIEUHANG[i].DICHTRUYEN[j].GIATRI = dichTruyenBefore[k].GIATRI;
						break;
					}
				}
			}
		}
		// order lại các phần tử của DULIEUHANG theo GIO, PHUT
		dataTabThongTinTheoDoi.DULIEUHANG.sort(function(a, b) {
			// L2PT-25770 duonghn start
			var ngayA = a.NGAYTHEODOI;
			var ngayB = b.NGAYTHEODOI;
			if (typeof ngayA != 'undefined' && ngayA != "" && typeof ngayB != 'undefined' && ngayB != "") {
				var dateA = stringToDateTime(ngayA);
				var dateB = stringToDateTime(ngayB);
				if (dateA < dateB) {
					return -1;
				} else if (dateA > dateB) {
					return 1;
				} else {
					return 0;
				}
			} else {
				var gioA = parseInt(a.GIO), gioB = parseInt(b.GIO), phutA = parseInt(a.PHUT), phutB = parseInt(b.PHUT);
				if (gioA < gioB) {
					return -1;
				}
				if (gioA == gioB) {
					if (phutA < phutB) {
						return -1;
					} else if (phutA > phutB) {
						return 1;
					} else {
						return 0;
					}
				}
				if (gioA > gioB) {
					return 1;
				}
				return 0;
			}
			// L2PT-25770 duonghn end
		});
	}
	// L2PT-30319 duonghn start
	function themMocTG(thoigian, dataTabThongTinTheoDoi) {
		// L2PT-30319 duonghn: kiểm tra tồn tại thời gian để so sánh
		if (typeof thoigian == 'undefined' || thoigian == "") {
			return;
		}
		var gio = thoigian.substring(11, 13);
		var phut = thoigian.substring(14, 16);
		var isTonTaiTG = false;
		for (var i = 0; i < dataTabThongTinTheoDoi.DULIEUHANG.length; i++) {
			var obj = dataTabThongTinTheoDoi.DULIEUHANG[i];
			// L2PT-30319 duonghn: kiểm tra thêm theo ngày theo dõi
			if (typeof obj.NGAYTHEODOI != 'undefined' && thoigian.substring(0, 16) == obj.NGAYTHEODOI.substring(0, 16) && obj.GIO == gio && obj.PHUT == phut) {
				isTonTaiTG = true;
				break;
			}
		}
		if (!isTonTaiTG) {
			var obj = {
				"ID_GMHS_GIOPHUT" : "",
				"NGAYTHEODOI" : thoigian, // L2PT-25770 duonghn
				"GIO" : gio + "",
				"PHUT" : phut + "",
				"MACH" : "",
				"HUYETAP" : "",
				"NHIETDO" : "",
				"MATMAU" : "",
				"NUOCTIEU" : "",
				"ALDMP" : "",
				"NHIPTHO" : "",
				"HOHAPTTLT" : "",
				"HOHAPFECO2" : "",
				"APLUC" : "",
				"SPO2" : "",
				"FIO2" : "",
				"QUANSAT" : "",
				"HAL" : "",
				"THUOC" : [],
				"DICHTRUYEN" : []
			};
			dataTabThongTinTheoDoi.DULIEUHANG.push(obj);
		}
	}
	// L2PT-30319 duonghn end
	function bindASANhietDo(page, dataASA, dataTabThongTinTheoDoi) {
		var _asa = '';
		for (var i = 1; i <= 5; i++) {
			_asa += (dataASA.ASA == i) ? ' (' + i + ') ' : ' ' + i + ' ';
		}
		var _daday = dataASA.DADAY == '1' ? '<u> Dạ dày </u> / ' : ' Dạ dày / ';
		var _capcuu = dataASA.CAPCUU == '1' ? '<u> Cấp cứu </u>' : ' Cấp cứu ';
		var _malapati = '';
		for (var i = 1; i <= 5; i++) {
			_malapati += (dataASA.MALLAMPATI == i) ? ' (' + i + ') ' : ' ' + i + ' ';
		}
		var thoiGianKetThucMe = dataASA.TTTD_TGKETTHUCME;
		var thoiGianBatDauMe = dataASA.TTTD_TGBATDAUME;
		var thoiGianKetThucMeMoment = moment(thoiGianKetThucMe, 'DD/MM/YYYY hh:mm:ss');
		var thoiGianBatDauMeMoment = moment(thoiGianBatDauMe, 'DD/MM/YYYY hh:mm:ss');
		var x = thoiGianKetThucMeMoment.diff(thoiGianBatDauMeMoment);
		var tempTime = moment.duration(x);
		var ngay = tempTime._data.days;
		var gio = tempTime._data.hours;
		var phut = tempTime._data.minutes;
		var giay = tempTime._data.seconds;
		var tongThoiGianMe = (ngay == 0 ? "" : ngay + " ngày") + gio + " giờ" + " " + phut + " phút";
		$('#gmhs-trang-' + page + ' .tm_asa').html(_asa);
		$('#gmhs-trang-' + page + ' .tm_daday').html(_daday);
		$('#gmhs-trang-' + page + ' .tm_capcuu').html(_capcuu);
		$('#gmhs-trang-' + page + ' .tm_mallampati').html(_malapati);
		$('#gmhs-trang-' + page + ' .tm_diung').html(dataASA.TTCB_DIUNG);
		var tiensu = dataASA.TTCB_TIENSU;
		var thuocdung = dataASA.DGTM_THUOCDUNG;
		var tiensuthuocdung = tiensu + (thuocdung != "" ? "/" + thuocdung : "");
		$('#gmhs-trang-' + page + ' .tm_tiensuthuoc').html(tiensuthuocdung);
		$('#gmhs-trang-' + page + ' .tm_batthuonglamsang').html(dataASA.DGTM_BATTHUONG);
		$('#gmhs-trang-' + page + ' .tt_nhanxet').html(dataASA.KLDG_NHANXET);
		//$('#gmhs-trang-' + page + ' .tt_tongthoigianme').html(tongThoiGianMe);
		$('#gmhs-trang-' + page + ' .chandoan').html(fillDot(dataASA.CHAN_DOAN, 50));
		var timeGayMe = dataASA.NGAY_GAYME;
		var ngay = '';
		var thang = '';
		var nam = '';
		if (timeGayMe.length > 0) {
			ngay = timeGayMe.substring(0, 2);
			thang = timeGayMe.substring(3, 5);
			nam = timeGayMe.substring(6, 10);
		}
		$('#gmhs-trang-' + page + ' .ngay').html(fillDot(ngay, 5));
		$('#gmhs-trang-' + page + ' .thang').html(fillDot(thang, 5));
		$('#gmhs-trang-' + page + ' .nam').html(fillDot(nam, 5));
		
			//L2PT-44536
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NTU02D125_PHIEUGAYMEHOISUC_AN_NGAY') == "1"){
				$('#gmhs-trang-' + page + ' .ngay').html(fillDot("", 5));
				$('#gmhs-trang-' + page + ' .thang').html(fillDot("", 5));
				$('#gmhs-trang-' + page + ' .nam').html(fillDot("", 10));
			}
		
		$('#gmhs-trang-' + page + ' .tienme').html(fillDot(dataASA.TIEN_ME, 50));
		$('#gmhs-trang-' + page + ' .tacdung').html(fillDot(dataASA.TAC_DUNG, 50));
		$('#gmhs-trang-' + page + ' .nang').html(fillDot(dataASA.CAN_NANG, 10));
		$('#gmhs-trang-' + page + ' .cao').html(fillDot(dataASA.CHIEU_CAO, 10));
		$('#gmhs-trang-' + page + ' .loaimo').html(fillDot(dataASA.LOAIMO, 50));
		$('#gmhs-trang-' + page + ' .tuthemo').html(fillDot(dataASA.TU_THE, 50));
		$('#gmhs-trang-' + page + ' .nhommau').html(fillDot(dataASA.NHOM_MAU, 20));
		$('#gmhs-trang-' + page + ' .nguoigayme').html(fillDot(dataASA.BS_GAYME, 40));
		$('#gmhs-trang-' + page + ' .nguoimo').html(fillDot(dataASA.BS_PHAUTHUAT, 50));
		$('#gmhs-trang-' + page + ' .nguoithucheo').html(fillDot(dataASA.NGUOITHUCHEO, 10));
		$('#gmhs-trang-' + page + ' .ppvocam').html(fillDot(dataASA.PP_VOCAM, 50));
		var duLieuHang = dataTabThongTinTheoDoi.DULIEUHANG;
		if (duLieuHang.length > 0) {
			// page 0 ung voi SO_COT phut dau tien
			// page 1 ung voi SO_COT phut tiep theo
			// ...
			// update
			// page 0 ứng với SO_COT phần tử đầu tiên của dataTabThongTinTheoDoi
			// page 1 ứng với SO_COT phần tử tiếp theo của dataTabThongTinTheoDoi...
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'NHIETDO', 'cs_nhietdo', 'Nhiệt độ');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'MATMAU', 'cs_matmau', 'Mất máu');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'NUOCTIEU', 'cs_nuoctieu', 'Nước tiểu');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'ALDMP', 'cs_aldmp', 'ALĐMP/ALĐMPB/ALTMTU');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'NHIPTHO', 'cs_nhiptho', 'Nhịp thở');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'HOHAPTTLT', 'hh_ttlt', 'TTLT2');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'HOHAPFECO2', 'hh_feco2', 'FeCO2');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'APLUC', 'hh_apluc', 'Áp lực');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'SPO2', 'hh_spo2', 'SpO2');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'FIO2', 'hh_fio2', 'FiO2');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'HAL', 'hh_hal', '%Sevorane');
			fillHTML2(page, duLieuHang, dataTabThongTinTheoDoi.TGBATDAUME, 'QUANSAT', 'quansat', 'Quan sát');
			fillHTMLThuocDichTruyen22(page, dataTabThongTinTheoDoi, 'thuoc');
			fillHTMLThuocDichTruyen22(page, dataTabThongTinTheoDoi, 'dichtruyen');
		}
		$('#gmhs-trang-' + page + ' .tt_tongthoigianme').html(tongThoiGianMe);
	}
	// vẽ canvas mạch, nhiệt dộ tương ứng với trang
	function bindDataCanvas(page, dataTabThongTinTheoDoi) {
		var cvWidth = 1058;
		var _startNum = 0;
		var pcanvas = '#gmhs-trang-' + page + ' .cvBieuDo';
		var duLieuHang = dataTabThongTinTheoDoi.DULIEUHANG;
		if (duLieuHang.length > 0) {
			var machArr = getArrMachHuyetAp2(page, dataTabThongTinTheoDoi, 'MACH');
			var huyetApArr = getArrMachHuyetAp2(page, dataTabThongTinTheoDoi, 'HUYETAP');
			// Vẽ mạch
			var _lastMachX_1 = 0;
			var _lastMachY_1 = 0;
			var _lastMachX_2 = 0;
			var _lastMachY_2 = 0;
			for (var _i = 0; _i < SO_COT; _i++) {
				var mach = machArr[_i];
				if (mach != null && mach != '') {
					//var xPointMach = (cvWidth / SO_COT) * (_i+1) - (cvWidth / (SO_COT * 2));
					var xPointMach = (cvWidth / SO_COT) * _i;
					var yPointMach = (150 - mach) * (191 / (150 - 50)) + 103;
					// Vẽ điểm mạch
					$(pcanvas).drawEllipse({
						fillStyle : '#AA1233',
						x : xPointMach,
						y : yPointMach + 10,
						width : 9,
						height : 9
					});
					// Điền mạch text
					//$(pCanvasTruoc).drawText({
					//    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
					//    x: _xPointMach - 10, y: _yPointMach, text: _machTruoc
					//});
					// Vẽ đường nối - mạch
					if (_i > 0 && _startNum > 0) {
						$(pcanvas).drawLine({
							strokeDash : [ 2 ],
							strokeStyle : '#AA1233',
							strokeWidth : 1,
							x1 : _lastMachX_1,
							y1 : _lastMachY_1 + 10,
							x2 : xPointMach,
							y2 : yPointMach + 10
						});
					}
					_lastMachX_1 = xPointMach;
					_lastMachY_1 = yPointMach;
					_startNum++;
				}
			}
			// Vẽ huyết áp
			for (var _i = 0; _i < SO_COT; _i++) {
				// --------------- Mặt trước ---------------
				// Tách huyết áp
				var tmp = huyetApArr[_i].split('/');
				var haTruong = tmp[0] ? tmp[0] : '';
				var haThu = tmp[1] ? tmp[1] : '';
				// Huyết áp trương
				//var xPointHaTruong = (cvWidth / SO_COT) * (_i+1) - (cvWidth / (SO_COT * 2));
				// L2PT-24678 duonghn start
				if (haTruong == "" || haThu == "") {
					continue;
				}
				// L2PT-24678 duonghn end
				var xPointHaTruong = (cvWidth / SO_COT) * _i;
				var yPointHaTruong = (150 - haTruong) * (191 / (150 - 50)) + 103;
				$(pcanvas).drawText({
					fillStyle : '#FF1B4D',
					strokeStyle : '#FF1B4D',
					strokeWidth : 1,
					fontSize : 12,
					fontFamily : 'Verdana, sans-serif',
					x : xPointHaTruong,
					y : yPointHaTruong + 10,
					text : 'v'
				});
				$(pcanvas).drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 12,
					fontFamily : 'Verdana, sans-serif',
					x : xPointHaTruong,
					y : yPointHaTruong,
					text : haTruong
				});
				// Huyết áp thu
				//var xPointHaThu = (cvWidth / SO_COT) * (_i+1) - (cvWidth / (SO_COT * 2));
				var xPointHaThu = (cvWidth / SO_COT) * _i;
				var yPointHaThu = (150 - haThu) * (191 / (150 - 50)) + 103;
				$(pcanvas).drawText({
					fillStyle : '#FF1B4D',
					strokeStyle : '#FF1B4D',
					strokeWidth : 1,
					fontSize : 12,
					fontFamily : 'Verdana, sans-serif',
					x : xPointHaThu,
					y : yPointHaThu + 13,
					text : '^'
				});
				$(pcanvas).drawText({
					fillStyle : '#25a',
					strokeStyle : '#25a',
					strokeWidth : 1,
					fontSize : 12,
					fontFamily : 'Verdana, sans-serif',
					x : xPointHaThu,
					y : yPointHaThu + 20,
					text : haThu
				});
				// Vẽ dải huyết áp
				$(pcanvas).drawLine({
					strokeStyle : '#095519',
					strokeWidth : 1,
					x1 : xPointHaTruong,
					y1 : yPointHaTruong + 10,
					x2 : xPointHaThu,
					y2 : yPointHaThu + 10
				});
			}
		}
	}
	/*
	function builSoTrang(soTrangThem){
		// clean html
		for (var i = 1; i<=23; i++) {
			$('#gmhs-trang-' + i).remove();
		}
		for (var i = 1; i<= soTrangThem; i++){
			var html = getHtmlOnePage(i);
			$('#gmhs-trang-' + (i-1)).after(html);
		}
	}
	 */
	/*
	function orderGio(gmhsct) {
		var dataGio = gmhsct.DATA_GIO;
		// sort lai theo thu tu gio
		dataGio.sort(function(a, b) {
			var keyA = parseInt(a.GIO),
				keyB = parseInt(b.GIO);
			if (keyA < keyB) return -1;
			if (keyA > keyB) return 1;
			return 0;
		});
		gmhsct.DATA_GIO = dataGio;
		return gmhsct;
	}
	 */
	/*
	function mergeThoiGianMeMoVaoGio(gmhsct){
		var newgmhsct = gmhsct;
		var dataGio = newgmhsct.DATA_GIO;

		var thoiGianBatDauMe = newgmhsct.TTTD_TGBATDAUME;
		var thoiGianKetThucMe = newgmhsct.TTTD_TGKETTHUCME;
		var thoiGianBatDauMo = newgmhsct.TTTD_TGBATDAUMO;
		var thoiGianKetThucMo = newgmhsct.TTTD_TGKETTHUCMO;

		var gioBatDauMe = "";
		if (thoiGianBatDauMe.length == 19) {
			gioBatDauMe = thoiGianBatDauMe.substring(11, 13);
		}

		var gioKetThucMe = "";
		if (thoiGianKetThucMe.length == 19) {
			gioKetThucMe = thoiGianKetThucMe.substring(11, 13);
		}

		var gioBatDauMo = "";
		if (thoiGianBatDauMo.length == 19) {
			gioBatDauMo = thoiGianBatDauMo.substring(11, 13);
		}

		var gioKetThucMo = "";
		if (thoiGianKetThucMo.length == 19) {
			gioKetThucMo = thoiGianKetThucMo.substring(11, 13);
		}

		// them thuoc tinh thoi gian bat dau me, mo, ket thuc me, mo vao dataGio (neu chua co gio tuong ung thi them object gio)

		var arrGioDaCo = [];
		for (var i = 0; i < dataGio.length; i++) {
			var obj = dataGio[i];
			arrGioDaCo.push(obj.GIO);
		}


		if(gioBatDauMe && jQuery.inArray(gioBatDauMe, arrGioDaCo) == -1){
			arrGioDaCo.push(gioBatDauMe);
			var obj = getObjectDataGioBoSung(gioBatDauMe);
			dataGio.push(obj);
		}

		if(gioKetThucMe && jQuery.inArray(gioKetThucMe, arrGioDaCo) == -1){
			arrGioDaCo.push(gioKetThucMe);
			var obj = getObjectDataGioBoSung(gioKetThucMe);
			dataGio.push(obj);
		}

		if(gioBatDauMo && jQuery.inArray(gioBatDauMo, arrGioDaCo) == -1){
			arrGioDaCo.push(gioBatDauMo);
			var obj = getObjectDataGioBoSung(gioBatDauMo);
			dataGio.push(obj);
		}

		if(gioKetThucMo && jQuery.inArray(gioKetThucMo, arrGioDaCo) == -1){
			arrGioDaCo.push(gioKetThucMo);
			var obj = getObjectDataGioBoSung(gioKetThucMo);
			dataGio.push(obj);
		}

		newgmhsct.DATA_GIO = dataGio;
		return newgmhsct;
	}
	*/
	/*
	function getObjectDataGioBoSung(gio){
		var obj = {
			"ID_GAYMEHOISUC_CT": $('#sel_Trang').val(),
			"ID_GAYMEHOISUC": $('#hdf_ID_GAYMEHOISUC').val(),
			"GIO": gio,
			"BIEUDO_MACH": "############",
			"BIEUDO_HUYETAP": "############",
			"CS_NHIETDO": "############",
			"CS_MATMAU": "############",
			"CS_NUOCTIEU": "############",
			"CS_ALDMP": "############",
			"CS_NHIPTHO": "############",
			"HH_TTLT": "############",
			"HH_FECO2": "############",
			"HH_APLUC": "############",
			"HH_SPO2": "############",
			"HH_FIO2": "############",
			"HH_HAL": "############",
			"QUANSAT": "############"
		};
		return obj;
	}
	 */
	/*
	function dongDeuGio(ttChiTiet, prop){
		var arrDistinctThuocDT = [];
		var arrDistinctThuocDTID = [];
		for (var i = 0; i< ttChiTiet.DATA_GIO.length; i++) {
			var thuocDTArr = ttChiTiet.DATA_GIO[i][prop];
			for (var j = 0; j< thuocDTArr.length; j++) {
				var thuocDT = thuocDTArr[j];
				var idThuocDT = thuocDT.IDTHUOC;
				var tenThuocDT = thuocDT.TENTHUOC;

				var obj = {
					IDTHUOC: idThuocDT,
					TENTHUOC: tenThuocDT,
					GIATRI: "############"
				}
				if(jQuery.inArray(idThuocDT, arrDistinctThuocDTID) == -1){
					arrDistinctThuocDTID.push(idThuocDT);
					arrDistinctThuocDT.push(obj);
				}
			}
		}
		// đồng đều các phần tử ( thằng nào cũng có 1,2,3)
		for (var i = 0; i < ttChiTiet.DATA_GIO.length; i++) {
			var thuocDTArr = ttChiTiet.DATA_GIO[i][prop];
			var arrDistinctThuocIDCurrent = [];
			for (var j= 0; j< thuocDTArr.length; j++) {
				arrDistinctThuocIDCurrent.push(thuocDTArr[j].IDTHUOC);
			}
			let difference = arrDistinctThuocDTID.filter(x => !arrDistinctThuocIDCurrent.includes(x));
			for (var j = 0; j< difference.length; j++) {
				for (var k = 0; k< arrDistinctThuocDT.length; k++) {
					if (arrDistinctThuocDT[k].IDTHUOC == difference[j]) {
						ttChiTiet.DATA_GIO[i][prop].push(arrDistinctThuocDT[k]);
					}
				}
			}
		}

		// sắp xếp lại cho 1 thứ tự thuốc thống nhất
		for (var i = 0; i < ttChiTiet.DATA_GIO.length; i++) {
			ttChiTiet.DATA_GIO[i][prop].sort(function(a, b) {
				var keyA = parseInt(a.IDTHUOC),
					keyB = parseInt(b.IDTHUOC);
				if (keyA < keyB) return -1;
				if (keyA > keyB) return 1;
				return 0;
			});
		}

		// lưu biến toàn cục tí vẽ html cho tiện
		if (prop == 'THUOC') {
			ARR_DISTICT_THUOC = arrDistinctThuocDT;
			ARR_DISTICT_THUOC.sort(function(a, b) {
				var keyA = parseInt(a.IDTHUOC),
					keyB = parseInt(b.IDTHUOC);
				if (keyA < keyB) return -1;
				if (keyA > keyB) return 1;
				return 0;
			});
		} else {
			ARR_DISTICT_DICHTRUYEN = arrDistinctThuocDT;
			ARR_DISTICT_DICHTRUYEN.sort(function(a, b) {
				var keyA = parseInt(a.IDTHUOC),
					keyB = parseInt(b.IDTHUOC);
				if (keyA < keyB) return -1;
				if (keyA > keyB) return 1;
				return 0;
			});
		}

		return ttChiTiet;
	}

	 */
	/*
	function dongDeuGioThuocDichTruyen(ttChiTiet){
		// chuyển string sang mảng
		for (var i = 0; i< ttChiTiet.DATA_GIO.length; i++) {
			ttChiTiet.DATA_GIO[i].THUOC = JSON.parse(ttChiTiet.DATA_GIO[i].THUOC);
			ttChiTiet.DATA_GIO[i].DICHTRUYEN = JSON.parse(ttChiTiet.DATA_GIO[i].DICHTRUYEN);
		}

		// lấy ra mảng distinct và mảng id distinct
		// vd:
		// 1,2
		// 1
		//
		// 2,3

		// => 1,2,3

		// thuốc
		ttChiTiet = dongDeuGio(ttChiTiet, 'THUOC');

		// dịch truyền
		ttChiTiet = dongDeuGio(ttChiTiet, 'DICHTRUYEN');

		return ttChiTiet;

	}
	 */
	// merge du lieu tu thuoc/ dich truyen dua vao data_gio
	/*
	function mergeThuocDTVaoGio(gmhsct, loai){
		var arrThuocDT = [];
		if (loai == 'thuoc') {
			arrThuocDT = gmhsct.ARRTHUOC;
		} else {
			arrThuocDT = gmhsct.ARRDICHTRUYEN;
		}


	// [
	//     {
	//         "ID_THUOC": "573446",
	//         "TEN_THUOC": "Natri clorid 0,9%",
	//         "MA": "NAT001",
	//         "SO_LUONG": "1",
	//         "HAM_LUONG": "22",
	//         "TONG_HAMLUONG": "22",
	//         "GIO": "1",
	//         "PHUT": "0"
	//     },
	//     {
	//         "ID_THUOC": "573448",
	//         "TEN_THUOC": "Iodine",
	//         "MA": "IOD001",
	//         "SO_LUONG": "1",
	//         "HAM_LUONG": "6",
	//         "TONG_HAMLUONG": "6",
	//         "GIO": "1",
	//         "PHUT": "5"
	//     },
	//     {
	//         "ID_THUOC": "573448",
	//         "TEN_THUOC": "Iodine",
	//         "MA": "IOD001",
	//         "SO_LUONG": "1",
	//         "HAM_LUONG": "6",
	//         "TONG_HAMLUONG": "6",
	//         "GIO": "1",
	//         "PHUT": "30"
	//     },
	//     {
	//         "ID_THUOC": "573448",
	//         "TEN_THUOC": "Iodine",
	//         "MA": "IOD001",
	//         "SO_LUONG": "1",
	//         "HAM_LUONG": "68",
	//         "TONG_HAMLUONG": "68",
	//         "GIO": "1",
	//         "PHUT": "40"
	//     },
	//     {
	//         "ID_THUOC": "573446",
	//         "TEN_THUOC": "Natri clorid 0,9%",
	//         "MA": "NAT001",
	//         "SO_LUONG": "1",
	//         "HAM_LUONG": "123",
	//         "TONG_HAMLUONG": "123",
	//         "GIO": "4",
	//         "PHUT": "40"
	//     }
	// ]


		var arrThuocDTDistint = [];
		// 0: {ID_THUOC: '573446', TEN_THUOC: 'Natri clorid 0,9%', MA: 'NAT001'}
		// 1: {ID_THUOC: '573448', TEN_THUOC: 'Iodine', MA: 'IOD001'}
		//

		var arrThuocDTIDDistint = [];

		var arrGioDistint = [];
		// 0: "1"
		// 1: "4"

		for (var i = 0; i< arrThuocDT.length; i++) {
			var thuocDT = arrThuocDT[i];
			var idThuocDT = thuocDT.ID_THUOC;
			var tenThuocDT = thuocDT.TEN_THUOC;
			var ma = thuocDT.MA;
			var gio = thuocDT.GIO;

			var obj = {
				ID_THUOC: idThuocDT,
				TEN_THUOC: tenThuocDT,
				MA: ma
			}
			if(jQuery.inArray(idThuocDT, arrThuocDTIDDistint) == -1){
				arrThuocDTIDDistint.push(idThuocDT);
				arrThuocDTDistint.push(obj);
			}

			if(jQuery.inArray(gio, arrGioDistint) == -1){
				arrGioDistint.push(gio + "");
			}
		}


		// Lưu biến bên ngoài để tí build html cho tiện
		if (loai == 'thuoc') {
			ARR_DISTICT_THUOC_NGOAI = arrThuocDTDistint;
		} else {
			ARR_DISTICT_DICHTRUYEN_NGOAI = arrThuocDTDistint;
		}

		var newgmhsct = gmhsct;
		var dataGio = newgmhsct.DATA_GIO;

	// [
	//     {
	//         "ID_GAYMEHOISUC_CT": "303",
	//         "ID_GAYMEHOISUC": "504",
	//         "GIO": "1",
	//         "BIEUDO_MACH": "120#100###########",
	//         "BIEUDO_HUYETAP": "100/90#120/100###########",
	//         "CS_NHIETDO": "############",
	//         "CS_MATMAU": "############",
	//         "CS_NUOCTIEU": "############",
	//         "CS_ALDMP": "############",
	//         "CS_NHIPTHO": "############",
	//         "HH_TTLT": "############",
	//         "HH_FECO2": "############",
	//         "HH_APLUC": "############",
	//         "HH_SPO2": "############",
	//         "HH_FIO2": "############",
	//         "HH_HAL": "############",
	//         "QUANSAT": "############"
	//     },
	//     {
	//         "ID_GAYMEHOISUC_CT": "303",
	//         "ID_GAYMEHOISUC": "504",
	//         "GIO": "3",
	//         "BIEUDO_MACH": "###120#150########",
	//         "BIEUDO_HUYETAP": "############",
	//         "CS_NHIETDO": "############",
	//         "CS_MATMAU": "############",
	//         "CS_NUOCTIEU": "############",
	//         "CS_ALDMP": "############",
	//         "CS_NHIPTHO": "############",
	//         "HH_TTLT": "############",
	//         "HH_FECO2": "############",
	//         "HH_APLUC": "############",
	//         "HH_SPO2": "############",
	//         "HH_FIO2": "############",
	//         "HH_HAL": "############",
	//         "QUANSAT": "############"
	//     }
	// ]
	//

		//=> mục đích thêm vào các thành phần của dataGio thuộc tính THUOC. cái này sẽ là 1 mảng gồm các thuốc của ARRDISTICTHUOC, có thêm thuộc tính TONGHAMLUONG có giá trị dạng #### (12 block phút)

		var arrGioDaCo = []; // [1,3]
		var arrGioChuaCo = []; // [4]

		// lấy ra ds giờ đã có trong DATA_GIO
		for (var i = 0; i< dataGio.length; i++) {
			var gio = dataGio[i].GIO;
			arrGioDaCo.push(gio);
		}

		// lấy ra ds giờ chưa có
		for (var i = 0; i< arrGioDistint.length; i++ ) {
			if(jQuery.inArray(arrGioDistint[i], arrGioDaCo) == -1){
				arrGioChuaCo.push(arrGioDistint[i]);
			}
		}

		// với giờ đã có thì thêm các cột về thuốc
		for (var i = 0; i< arrGioDaCo.length; i++) {
			for (var j = 0; j< dataGio.length; j++) {
				if (arrGioDaCo[i] == dataGio[j].GIO) {
					var arrThuocGio = [];
					for (var k = 0; k < arrThuocDTDistint.length; k++) {
						var obj = Object.assign({}, arrThuocDTDistint[k]);

						var strTongHamLuong = "";
						for (var n = 0; n<=55; n=n+5) {
							var tongHamLuong = "#";
							for (var m = 0; m < arrThuocDT.length; m++) {
								if (arrThuocDT[m].ID_THUOC == obj.ID_THUOC
									&& arrThuocDT[m].GIO == dataGio[j].GIO
									&& arrThuocDT[m].PHUT == n
								) {
									tongHamLuong = arrThuocDT[m].TONG_HAMLUONG + "#";
									break;
								}
							}

							strTongHamLuong += tongHamLuong;
						}

						obj.TONGHAMLUONG = strTongHamLuong;
						arrThuocGio.push(obj);
					}

					if (loai == 'thuoc') {
						dataGio[j].THUOC = arrThuocGio;
					} else {
						dataGio[j].DICHTRUYEN = arrThuocGio;
					}

				}
			}
		}

		// với giờ chưa có thì thêm cả các cột khác
		for (var i = 0; i< arrGioChuaCo.length; i++) {
			var objChiTiet = {
				ID_GAYMEHOISUC_CT: $('#sel_Trang').val()
				,ID_GAYMEHOISUC: $('#hdf_ID_GAYMEHOISUC').val()
				,GIO: arrGioChuaCo[i]
				,BIEUDO_HUYETAP: "############"
				,BIEUDO_MACH: "############"
				,CS_ALDMP: "############"
				,CS_MATMAU: "############"
				,CS_NHIETDO: "############"
				,CS_NHIPTHO: "############"
				,CS_NUOCTIEU: "############"
				,HH_APLUC: "############"
				,HH_FECO2: "############"
				,HH_FIO2: "############"
				,HH_HAL: "############"
				,HH_SPO2: "############"
				,HH_TTLT: "############"
				,QUANSAT: "############"
			}

			var arrThuocDTGio = [];

			for (var k = 0; k < arrThuocDTDistint.length; k++) {
				var obj = Object.assign({}, arrThuocDTDistint[k]);

				var strTongHamLuong = "";
				for (var n = 0; n<=55; n=n+5) {
					var tongHamLuong = "#";
					for (var m = 0; m < arrThuocDT.length; m++) {
						if (arrThuocDT[m].ID_THUOC == obj.ID_THUOC
							&& arrThuocDT[m].GIO == arrGioChuaCo[i]
							&& arrThuocDT[m].PHUT == n
						) {
							tongHamLuong = arrThuocDT[m].TONG_HAMLUONG + "#";
							break;
						}
					}

					strTongHamLuong += tongHamLuong;
				}

				obj.TONGHAMLUONG = strTongHamLuong;
				arrThuocDTGio.push(obj);
			}

			if (loai == 'thuoc') {
				objChiTiet.THUOC = arrThuocDTGio;
			} else {
				objChiTiet.DICHTRUYEN = arrThuocDTGio;
			}

			dataGio.push(objChiTiet);
		}

		// sort lại mang thuoc/ dich truyen cua cac datagio
		for (var i = 0; i< dataGio.length; i++) {
			if (loai == 'thuoc') {
				dataGio[i].THUOC.sort(function(a, b) {
					var keyA = parseInt(a.ID_THUOC),
						keyB = parseInt(b.ID_THUOC);
					if (keyA < keyB) return -1;
					if (keyA > keyB) return 1;
					return 0;
				});
			} else {
				dataGio[i].DICHTRUYEN.sort(function(a, b) {
					var keyA = parseInt(a.ID_THUOC),
						keyB = parseInt(b.ID_THUOC);
					if (keyA < keyB) return -1;
					if (keyA > keyB) return 1;
					return 0;
				});
			}

		}


		newgmhsct.DATA_GIO = dataGio;

		return newgmhsct;

	}
	*/
	/*
	function getHtmlOnePage(i){
		var html = $('#gmhs-trang-0')[0].outerHTML;
		html = html.replace('id="gmhs-trang-0"','id="gmhs-trang-' + i + '"')
		return html;
	}
	 */
	/*
	function getArrMachHuyetAp(page, dataGio, prop) {
		// page 0 ung voi DATA_GIO[0], DATA_GIO[1], DATA_GIO[2], DATA_GIO[3]
		// page 1 ung voi DATA_GIO[4], DATA_GIO[5], DATA_GIO[6], DATA_GIO[7]
		// ...
		var startI = (page + 1) * 4 - 4;
		var e1 = dataGio[startI];
		var e2 = dataGio[startI+1];
		var e3 = dataGio[startI+2];
		var e4 = dataGio[startI+3];

		var str = '';

		if (e1) {
			str += e1[prop];
		} else {
			str += "############"
		}

		if (e2) {
			str += e2[prop];
		} else {
			str += "############"
		}

		if (e3) {
			str += e3[prop];
		} else {
			str += "############"
		}

		if (e4) {
			str += e4[prop];
		} else {
			str += "############"
		}

		var arr = str.split("#");
		return arr;
	}
	 */
	/*
	function getStrGiaTriThuocDT (e, thuocDT, prop){
		var strGiaTri = '';
		if (e && e[prop]) {
			for (var j = 0; j < e[prop].length; j++) {
				if (thuocDT.IDTHUOC == e[prop][j].IDTHUOC ) {
					strGiaTri += e[prop][j].GIATRI;
					break;
				}
			}
		} else {
			strGiaTri += "############";
		}
		return strGiaTri;
	}
	 */
	/*
	function getTongHamLuongGio (e, thuocDT, prop){
		var strTongHamLuong = '';
		if (e && e[prop]) {
			for (var j = 0; j < e[prop].length; j++) {
				if (thuocDT.ID_THUOC == e[prop][j].ID_THUOC ) {
					strTongHamLuong += e[prop][j].TONGHAMLUONG;
					break;
				}
			}
		} else {
			strTongHamLuong += "############";
		}
		return strTongHamLuong;
	}
	 */
	/*
	trả về 1 mảng các object dạng
	[
	{
				IDTHUOC: "12313",
				TENTHUOC: "abc dè",
				GIATRI: "1#2#3#4#5#6#7#8#9#10#11#12#1#2#3#4#5#6#7#8#9#10#11#12#1#2#3#4#5#6#7#8#9#10#11#12#1#2#3#4#5#6#7#8#9#10#11#12#"
	},
	...
	]
	* */
	/*
	function getArrThuocDT2(page, dataGio, classname) {
		var arrThuocDT = [];
		var startI = (page+1) * 4 - 4;
		var e1 = dataGio[startI];
		var e2 = dataGio[startI+1];
		var e3 = dataGio[startI+2];
		var e4 = dataGio[startI+3];

		if (classname == 'thuoc') {
			for (var i = 0; i< ARR_DISTICT_THUOC.length; i++) {
				var thuocDT = Object.assign({}, ARR_DISTICT_THUOC[i]);
				var strTongGiaTri =
					getStrGiaTriThuocDT(e1, thuocDT,'THUOC')
					+ getStrGiaTriThuocDT(e2, thuocDT,'THUOC')
					+ getStrGiaTriThuocDT(e3, thuocDT, 'THUOC')
					+ getStrGiaTriThuocDT(e4, thuocDT, 'THUOC')
				;
				thuocDT.GIATRI = strTongGiaTri;
				arrThuocDT.push(thuocDT);
			}
		} else {
			for (var i = 0; i< ARR_DISTICT_DICHTRUYEN.length; i++) {
				var thuocDT = Object.assign({}, ARR_DISTICT_DICHTRUYEN[i]);
				var strTongGiaTri =
					getStrGiaTriThuocDT(e1, thuocDT,'DICHTRUYEN')
					+ getStrGiaTriThuocDT(e2, thuocDT,'DICHTRUYEN')
					+ getStrGiaTriThuocDT(e3, thuocDT, 'DICHTRUYEN')
					+ getStrGiaTriThuocDT(e4, thuocDT, 'DICHTRUYEN')
				;
				thuocDT.GIATRI = strTongGiaTri;
				arrThuocDT.push(thuocDT);
			}
		}

		return arrThuocDT;
	}
	 */
	/*
	trả về 1 mảng các object dạng
	[
	{
				ID_THUOC: "12313",
				TEN_THUOC: "abc dè",
				MA: "12313",
				TONGHAMLUONG: "##123###2####1233######1231#################################"
	},
	...
	]
	* */
	/*
	function getArrThuocDT(page, dataGio, classname) {
		var arrThuocDT = [];
		var startI = (page+1) * 4 - 4;
		var e1 = dataGio[startI];
		var e2 = dataGio[startI+1];
		var e3 = dataGio[startI+2];
		var e4 = dataGio[startI+3];

		if (classname == 'thuoc') {
			for (var i = 0; i< ARR_DISTICT_THUOC_NGOAI.length; i++) {
				var thuocDT = Object.assign({}, ARR_DISTICT_THUOC_NGOAI[i]);
				var strTongHamLuong =
					getTongHamLuongGio(e1, thuocDT,'THUOC')
					+ getTongHamLuongGio(e2, thuocDT,'THUOC')
					+ getTongHamLuongGio(e3, thuocDT, 'THUOC')
					+ getTongHamLuongGio(e4, thuocDT, 'THUOC')
				;
				thuocDT.TONGHAMLUONG = strTongHamLuong;
				arrThuocDT.push(thuocDT);
			}
		} else {
			for (var i = 0; i< ARR_DISTICT_DICHTRUYEN_NGOAI.length; i++) {
				var thuocDT = Object.assign({}, ARR_DISTICT_DICHTRUYEN_NGOAI[i]);
				var strTongHamLuong =
					getTongHamLuongGio(e1, thuocDT,'DICHTRUYEN')
					+ getTongHamLuongGio(e2, thuocDT,'DICHTRUYEN')
					+ getTongHamLuongGio(e3, thuocDT, 'DICHTRUYEN')
					+ getTongHamLuongGio(e4, thuocDT, 'DICHTRUYEN')
				;
				thuocDT.TONGHAMLUONG = strTongHamLuong;
				arrThuocDT.push(thuocDT);
			}
		}

		return arrThuocDT;

	};
	 */
	/*
	function fillHTMLThuocDichTruyen2(page, dataGio, classname){
		var lengthThuocVT;
		var textHeader;
		if (classname == 'thuoc') {
			textHeader = 'Thuốc';
		} else {
			textHeader = 'Dịch truyền';
		}

		var arrThuocDT = getArrThuocDT2(page, dataGio, classname);

		if (arrThuocDT.length > 0) {
			// hàng đầu tiên
			var firstThuocDT = arrThuocDT[0];
			var html1 = '' +
				'            <td rowspan="' + arrThuocDT.length + '">' + textHeader + '</td>\n' +
				'            <td colspan="3">' + firstThuocDT.TENTHUOC + '</td>' +
				'';
			var arrGiaTri = firstThuocDT.GIATRI.split("#");
			var tongHang = 0;
			for (var i = 0; i< 48; i++) {
				html1 += '' +
					'<td class="cot-chiso">' + arrGiaTri[i] + '</td>' +
					'';
				if (validator.isFloat(arrGiaTri[i])) {
					tongHang += parseFloat(arrGiaTri[i]);
				}
			}
			tongHang = Math.round(tongHang * 100) / 100;
			if (tongHang == 0) {
				tongHang = "";
			}
			html1 += '' +
				'<td class="cot-chiso">' + tongHang + '</td>' +
				'';


			$('#gmhs-trang-' + page + ' .' + classname).html(html1);

			// các hàng sau
			for (var i = 1; i< arrThuocDT.length; i++ ) {
				var thuocDT = arrThuocDT[i];
				var html = '' +
					'        <tr>\n' +
					'            <td colspan="3">' + thuocDT.TENTHUOC + '</td>' +
					'';
				var arrGiaTri = thuocDT.GIATRI.split("#");
				var tongHang = 0;
				for (var j = 0; j < 48; j++) {
					html += '' +
						'            <td class="cot-chiso">' + arrGiaTri[j] + '</td>' +
						'';
					if (validator.isFloat(arrGiaTri[j])) {
						tongHang += parseFloat(arrGiaTri[j]);
					}
				}
				tongHang = Math.round(tongHang * 100) / 100;
				if (tongHang == 0) {
					tongHang = "";
				}
				html += '' +
					'			<td class="cot-chiso">' + tongHang + '</td>' +
					'        </tr>';
				$('#gmhs-trang-' + page + ' .' + classname).after(html);
			}

		} else {
			var html = '' +
				'            <td rowspan="1">' + textHeader + '</td>\n' +
				'            <td colspan="3"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>' +
				'';
			$('#gmhs-trang-' + page + ' .' + classname).html(html);
		}
	}
	 */
	/*
	function fillHTMLThuocDichTruyen(page, dataGio, classname){
		var lengthThuocVT;
		var textHeader;
		if (classname == 'thuoc') {
			textHeader = 'Thuốc';
		} else {
			textHeader = 'Dịch truyền';
		}

		var arrThuocDT = getArrThuocDT(page, dataGio, classname);

		if (arrThuocDT.length > 0) {
			// hàng đầu tiên
			var firstThuocDT = arrThuocDT[0];
			var html1 = '' +
				'            <td rowspan="' + arrThuocDT.length + '">' + textHeader + '</td>\n' +
				'            <td colspan="3">' + firstThuocDT.TEN_THUOC + '</td>' +
				'';
			var arrTongHamLuongThuocDT1 = firstThuocDT.TONGHAMLUONG.split("#");
			var tongHamLuongHang = 0;
			for (var i = 0; i< 48; i++) {
				html1 += '' +
					'<td class="cot-chiso">' + arrTongHamLuongThuocDT1[i] + '</td>' +
					'';
				if (validator.isFloat(arrTongHamLuongThuocDT1[i])) {
					tongHamLuongHang += parseFloat(arrTongHamLuongThuocDT1[i]);
				}
			}
			tongHamLuongHang = Math.round(tongHamLuongHang * 100) / 100;
			if (tongHamLuongHang == 0) {
				tongHamLuongHang = "";
			}
			html1 += '' +
				'<td class="cot-chiso">' + tongHamLuongHang + '</td>' +
				'';

			$('#gmhs-trang-' + page + ' .' + classname).html(html1);

			// các hàng sau
			for (var i = 1; i< arrThuocDT.length; i++ ) {
				var thuocDT = arrThuocDT[i];
				var html = '' +
					'        <tr>\n' +
					'            <td colspan="3">' + thuocDT.TEN_THUOC + '</td>' +
					'';
				var arrTongHamLuongThuocDT = thuocDT.TONGHAMLUONG.split("#");
				var tongHamLuongHang = 0;
				for (var j = 0; j < 48; j++) {
					html += '' +
						'            <td class="cot-chiso">' + arrTongHamLuongThuocDT[j] + '</td>' +
						'';
					if (validator.isFloat(arrTongHamLuongThuocDT[j])) {
						tongHamLuongHang += parseFloat(arrTongHamLuongThuocDT[j]);
					}
				}
				tongHamLuongHang = Math.round(tongHamLuongHang * 100) / 100;
				if (tongHamLuongHang == 0) {
					tongHamLuongHang = "";
				}
				html += '' +
					'			<td class="cot-chiso">' + tongHamLuongHang + '</td>' +
					'        </tr>';
				$('#gmhs-trang-' + page + ' .' + classname).after(html);
			}

		} else {
			var html = '' +
				'            <td rowspan="1">' + textHeader + '</td>\n' +
				'            <td colspan="3"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>\n' +
				'            <td class="cot-chiso"></td>' +
				'';
			$('#gmhs-trang-' + page + ' .' + classname).html(html);

		}
	}
	*/
	// lấy ra dữ liệu chỉ số trong 4 giờ tương ứng với page, đưa vào 1 string (48 #) và fill vào html
	/*
	function fillHTML(page, dataGio, prop, classname, text){
		var startI = (page+1) * 4 - 4;
		var e1 = dataGio[startI];
		var e2 = dataGio[startI+1];
		var e3 = dataGio[startI+2];
		var e4 = dataGio[startI+3];

		var str = '';

		if (e1) {
			str += e1[prop];
		} else {
			str += "############"
		}

		if (e2) {
			str += e2[prop];
		} else {
			str += "############"
		}

		if (e3) {
			str += e3[prop];
		} else {
			str += "############"
		}

		if (e4) {
			str += e4[prop];
		} else {
			str += "############"
		}

		var arr = str.split("#");
		var _html = '';

		if (classname == 'cs_nhiptho') {
			_html += '<td rowspan="7">HÔ HẤP</td><td colspan="3">Nhịp thở</td>';
		}
		else if (classname == 'hh_ttlt') {
			_html += '' +
				'            <td rowspan="6">\n' +
				'                <p>Máy</p>\n' +
				'                <p>Mê</p>\n' +
				'            </td>\n' +
				'            <td colspan="2" rowspan="2" class="background crossOut">\n' +
				'                <div><span class="top">TTLT2</span>\n' +
				'                    <span class="bottom">FeCO2</span>\n' +
				'                </div>\n' +
				'            </td>' +
				'';
		}
		else if (classname == 'hh_feco2') {
			_html += '' +
				'' +
				'';
		}
		else if (classname == 'hh_apluc') {
			_html += '' +
				'            <td colspan="2" rowspan="2" class="background">\n' +
				'                <div><span class="top">Áp lực</span>\n' +
				'                    <span class="bottom">SpO2</span>\n' +
				'                </div>\n' +
				'            </td>' +
				'';
		}
		else if (classname == 'hh_spo2') {
			_html += '' +
				'' +
				'';
		}
		else if (classname == 'hh_fio2') {
			_html += '' +
				'            <td colspan="2" rowspan="2" class="background">\n' +
				'                <div><span class="top">FiO2</span>\n' +
				'                    <span class="bottom">%Halotan</span>\n' +
				'                </div>\n' +
				'            </td>' +
				'';
		}
		else if (classname == 'hh_hal') {
			_html += '' +
				'' +
				'';
		}
		else if (classname == 'quansat') {
			_html += '' +
				'            <td >Quan sát</td>\n' +
				'            <td colspan="3"></td>' +
				'';
		}

		else {
			_html += '<td colspan="4" class="' + classname + '">' + text + '</td>';
		}

		for (var i = 0; i < 48; i++) {
			_html += '<td class="cot-chiso">' + arr[i] + '</td>';
		}

		if (classname == 'hh_fio2') {
			_html += '' +
				'<td rowspan="2">TỔNG CỘNG</td>' +
				'';
		} else if (classname == 'quansat') {
			_html += '' +
				'<td>' +
				'TỔNG THỜI GIAN MÊ' +
				'<p class="tt_tongthoigianme"></p>' +
				'</td>'
				;
		}


		$('#gmhs-trang-' + page + ' .' + classname).html(_html);
	}
	 */
	/*
	function bindNhietDoMatMau(data, page){
		var _asa = '';
		for (var i = 1; i <= 5; i++) {
			_asa += (data.ASA == i) ? ' (' + i + ') ' : ' ' + i + ' ';
		}

		var _daday = data.DADAY == '1' ? '<u> Dạ dày </u> / ' : ' Dạ dày / ';
		var _capcuu = data.CAPCUU == '1' ? '<u> Cấp cứu </u>' : ' Cấp cứu ';

		var _malapati = '';
		for (var i = 1; i <= 5; i++) {
			_malapati += (data.MALLAMPATI == i) ? ' (' + i + ') ' : ' ' + i + ' ';
		}

		var thoiGianKetThucMe = data.TTTD_TGKETTHUCME;
		var thoiGianBatDauMe = data.TTTD_TGBATDAUME;
		var thoiGianKetThucMeMoment = moment(thoiGianKetThucMe, 'DD/MM/YYYY hh:mm:ss');
		var thoiGianBatDauMeMoment = moment(thoiGianBatDauMe, 'DD/MM/YYYY hh:mm:ss');
		var x = thoiGianKetThucMeMoment.diff(thoiGianBatDauMeMoment);
		var tempTime = moment.duration(x);
		var ngay = tempTime._data.days;
		var gio = tempTime._data.hours;
		var phut = tempTime._data.minutes;
		var giay = tempTime._data.seconds;
		var tongThoiGianMe = (ngay == 0 ? "" : ngay + " ngày") + gio + " giờ" + " " + phut + " phút";

		$('#gmhs-trang-' + page + ' .tm_asa').html(_asa);
		$('#gmhs-trang-' + page + ' .tm_daday').html(_daday);
		$('#gmhs-trang-' + page + ' .tm_capcuu').html(_capcuu);
		$('#gmhs-trang-' + page + ' .tm_mallampati').html(_malapati);
		$('#gmhs-trang-' + page + ' .tm_diung').html(data.TTCB_DIUNG);
		var tiensu = data.TTCB_TIENSU;
		var thuocdung = data.DGTM_THUOCDUNG;
		var tiensuthuocdung = tiensu + (thuocdung != "" ? "/" + thuocdung : "");
		$('#gmhs-trang-' + page + ' .tm_tiensuthuoc').html(tiensuthuocdung);
		$('#gmhs-trang-' + page + ' .tm_batthuonglamsang').html(data.DGTM_BATTHUONG);
		$('#gmhs-trang-' + page + ' .tt_nhanxet').html(data.KLDG_NHANXET);
		//$('#gmhs-trang-' + page + ' .tt_tongthoigianme').html(tongThoiGianMe);

		var dataGio = data.DATA_GIO;
		if (dataGio.length > 0) {
			// page 0 ung voi DATA_GIO[0], DATA_GIO[1], DATA_GIO[2], DATA_GIO[3]
			// page 1 ung voi DATA_GIO[4], DATA_GIO[5], DATA_GIO[6], DATA_GIO[7]
			// ...

			fillHTML(page, dataGio, 'CS_NHIETDO', 'cs_nhietdo', 'Nhiệt độ');
			fillHTML(page, dataGio, 'CS_MATMAU', 'cs_matmau', 'Mất máu');
			fillHTML(page, dataGio, 'CS_NUOCTIEU', 'cs_nuoctieu', 'Nước tiểu');
			fillHTML(page, dataGio, 'CS_ALDMP', 'cs_aldmp', 'ALĐMP/ALĐMPB/ALTMTU');
			fillHTML(page, dataGio, 'CS_NHIPTHO', 'cs_nhiptho', 'Nhịp thở');
			fillHTML(page, dataGio, 'HH_TTLT', 'hh_ttlt', 'TTLT2');
			fillHTML(page, dataGio, 'HH_FECO2', 'hh_feco2', 'FeCO2');
			fillHTML(page, dataGio, 'HH_APLUC', 'hh_apluc', 'Áp lực');
			fillHTML(page, dataGio, 'HH_SPO2', 'hh_spo2', 'SpO2');
			fillHTML(page, dataGio, 'HH_FIO2', 'hh_fio2', 'FiO2');
			fillHTML(page, dataGio, 'HH_HAL', 'hh_hal', '%Halotan');
			fillHTML(page, dataGio, 'QUANSAT', 'quansat', 'Quan sát');

			// lấy thuốc/ dịch truyền từ tab thuốc, dịch truyền
			//fillHTMLThuocDichTruyen(page, dataGio, 'thuoc');
			//fillHTMLThuocDichTruyen(page, dataGio, 'dichtruyen');

			// lấy thuốc/ dịch truyền từ tab thông tin theo dõi
			fillHTMLThuocDichTruyen2(page, dataGio, 'thuoc');
			fillHTMLThuocDichTruyen2(page, dataGio, 'dichtruyen');
		}
		$('#gmhs-trang-' + page + ' .tt_tongthoigianme').html(tongThoiGianMe);
	}
	 */
	/*
	function bindCanvasWithHeader (data, page){
		var pCanvas = '#gmhs-trang-' + page + ' .cvBieuDo';

		var _numLine = 6;
		var _numCol = 48;
		var _startX = 0;
		var _startY = 0;
		var _stopX = 1820;
		var _stopY = 380;
		var _lineHeight = 18.9;
		var _lineWidth = 22;
		var _numLine = 15;
		var _cvHeight = 301;
		var _cvwidth = 1058;

		$(pCanvas).attr('width', _cvwidth);
		$(pCanvas).attr('height', _cvHeight);

		for (var _i = 0; _i <= _numLine + 1; _i++) {
			if (((_i - 1) % 5) == 0) {
				$(pCanvas).drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
				$(pCanvas).drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
				$(pCanvas).drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
			} else {
				$(pCanvas).drawLine({ strokeDash: [2], strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
			}
		}

		// Fill cột
		for (var _i = 0; _i <= _numCol; _i++) {
			if (_i % 2 == 0) {
				$(pCanvas).drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY + _lineHeight, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
			}
		}


		// xử lý icon liên quan đến thời gian bắt đầu/ kết thúc mê/mổ

		var gioBatDauMe;
		var phutBatDauMe;
		var gioKetThucMe;
		var phutKetThucMe;
		var gioBatDauMo;
		var phutBatDauMo;
		var gioKetThucMo;
		var phutKetThucMo;
		if (data.TTTD_TGBATDAUME) {
			var thoiGianBatDauMe = data.TTTD_TGBATDAUME;
			var thoiGianKetThucMe = data.TTTD_TGKETTHUCME;
			var thoiGianBatDauMo = data.TTTD_TGBATDAUMO;
			var thoiGianKetThucMo = data.TTTD_TGKETTHUCMO;

			if (thoiGianBatDauMe.length == 19) {
				gioBatDauMe = thoiGianBatDauMe.substring(11, 13); // 14
				phutBatDauMe = thoiGianBatDauMe.substring(14, 16); // 28
			}

			if (thoiGianKetThucMe.length = 19) {
				gioKetThucMe = thoiGianKetThucMe.substring(11, 13);
				phutKetThucMe = thoiGianKetThucMe.substring(14, 16);
			}

			if (thoiGianBatDauMo.length = 19) {
				gioBatDauMo = thoiGianBatDauMo.substring(11, 13);
				phutBatDauMo = thoiGianBatDauMo.substring(14, 16);
			}

			if (thoiGianKetThucMo.length = 19) {
				gioKetThucMo = thoiGianKetThucMo.substring(11, 13);
				phutKetThucMo = thoiGianKetThucMo.substring(14, 16);
			}

		}




		var dataGio = data.DATA_GIO;

		// page 0 ung voi DATA_GIO[0], DATA_GIO[1], DATA_GIO[2], DATA_GIO[3]
		// page 1 ung voi DATA_GIO[4], DATA_GIO[5], DATA_GIO[6], DATA_GIO[7]
		// ...
		var startI = (page+1) * 4 - 4;
		var e1 = dataGio[startI];
		var e2 = dataGio[startI+1];
		var e3 = dataGio[startI+2];
		var e4 = dataGio[startI+3];

		// Cột tiêu đề
		for (var _i = 0; _i <= _numCol ; _i++) {
			if (_i % 2 == 0) {
				//  gio
				if (_i % 12 == 0) {
					var gio = "";
					if (_i == 0 && e1) {
						gio = e1.GIO ;
					} else if (_i == 12 && e2) {
						gio = e2.GIO ;
					} else if (_i == 24 && e3) {
						gio = e3.GIO ;
					} else if (_i == 36 && e4) {
						gio = e4.GIO ;
					} else {
						gio = '';
					}

					if (gio > 23) {
						gio = (gio - 24) + "";
					}


					$(pCanvas).drawText({
						fillStyle: '#000', strokeStyle: '#f50909', strokeWidth: 1, fontSize: 15, fontFamily: 'Verdana, sans-serif',
						x: _i * _lineWidth + 10, y: _startY + 10, text: gio == '' ? '' : gio + "h"
					});

					if (gio == gioBatDauMe) {
						var xPoint = _lineWidth * (_i + parseInt(phutBatDauMe) / 5);
						drawKyHieuThoiGian(pCanvas, 'o', 'v', xPoint);
					}

					if (gio == gioKetThucMe) {
						var xPoint = _lineWidth * (_i + parseInt(phutKetThucMe) / 5);
						drawKyHieuThoiGian(pCanvas, '^', 'o', xPoint);
					}

					if (gio == gioBatDauMo) {
						var xPoint = _lineWidth * (_i + parseInt(phutBatDauMo) / 5);
						drawKyHieuThoiGian(pCanvas, 'x', 'v', xPoint);
					}

					if (gio == gioKetThucMo) {
						var xPoint = _lineWidth * (_i + parseInt(phutKetThucMo) / 5);
						drawKyHieuThoiGian(pCanvas, '^', 'x', xPoint);
					}

				}
				// phut
				else {
					var phut = '';
					if (["2", "14",  "26", "38"].includes(_i + "")) {
						phut = 10;
					} else if (["4", "16",  "28", "40"].includes(_i + "")) {
						phut = 20;
					} else if (["6", "18",  "30", "42"].includes(_i + "")) {
						phut = 30;
					} else if (["8", "20",  "32", "44"].includes(_i + "")) {
						phut = 40;
					} else if (["10", "22",  "34", "46"].includes(_i + "")) {
						phut = 50;
					}
					$(pCanvas).drawText({
						fillStyle: '#000', strokeStyle: '#000', strokeWidth: 1, fontSize: 11, fontFamily: 'Verdana, sans-serif',
						x: _i * _lineWidth, y: _startY + 10, text: phut
					});
				}
			}
		}
	}
	 */
	/*
	function bindCanvasWithData (data, page) {

		var cvWidth = 1058;
		var _startNum = 0;

		var pcanvas = '#gmhs-trang-' + page +' .cvBieuDo';

		var dataGio = data.DATA_GIO;
		if (dataGio.length > 0) {
			var machArr = getArrMachHuyetAp(page, dataGio, 'BIEUDO_MACH');
			var huyetApArr = getArrMachHuyetAp(page, dataGio, 'BIEUDO_HUYETAP');

			// Vẽ mạch
			var _lastMachX_1 = 0;
			var _lastMachY_1 = 0;
			var _lastMachX_2 = 0;
			var _lastMachY_2 = 0;

			for (var _i = 0 ; _i < 48; _i++) {
				var mach = machArr[_i];
				if (mach != null && mach != '') {
					//var xPointMach = (cvWidth / 48) * (_i+1) - (cvWidth / (48 * 2));
					var xPointMach = (cvWidth / 48) * _i;
					var yPointMach = (150 - mach) * (191 / (150 - 50)) + 103;

					// Vẽ điểm mạch
					$(pcanvas).drawEllipse({ fillStyle: '#AA1233', x: xPointMach, y: yPointMach + 10, width: 9, height: 9 });

					// Điền mạch text
					//$(pCanvasTruoc).drawText({
					//    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
					//    x: _xPointMach - 10, y: _yPointMach, text: _machTruoc
					//});

					// Vẽ đường nối - mạch
					if (_i > 0 && _startNum > 0) {
						$(pcanvas).drawLine({ strokeDash: [2], strokeStyle: '#AA1233', strokeWidth: 1, x1: _lastMachX_1, y1: _lastMachY_1 + 10, x2: xPointMach, y2: yPointMach + 10 });
					}

					_lastMachX_1 = xPointMach;
					_lastMachY_1 = yPointMach;
					_startNum++;
				}
			}

			// Vẽ huyết áp
			for (var _i = 0 ; _i < 48; _i++) {
				// --------------- Mặt trước ---------------
				// Tách huyết áp
				var tmp = huyetApArr[_i].split('/');
				var haTruong = tmp[0] ? tmp[0] : '';
				var haThu =  tmp[1] ? tmp[1] : '';

				// Huyết áp trương
				//var xPointHaTruong = (cvWidth / 48) * (_i+1) - (cvWidth / (48 * 2));
				var xPointHaTruong = (cvWidth / 48) * _i;
				var yPointHaTruong = (150 - haTruong) * (191 / (150 - 50)) + 103;

				$(pcanvas).drawText({
					fillStyle: '#FF1B4D', strokeStyle: '#FF1B4D', strokeWidth: 1, fontSize: 11, fontFamily: 'Verdana, sans-serif',
					x: xPointHaTruong, y: yPointHaTruong + 10, text: 'v'
				});

				$(pcanvas).drawText({
					fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
					x: xPointHaTruong, y: yPointHaTruong , text: haTruong
				});


				// Huyết áp thu
				//var xPointHaThu = (cvWidth / 48) * (_i+1) - (cvWidth / (48 * 2));
				var xPointHaThu = (cvWidth / 48) * _i;
				var yPointHaThu = (150 - haThu) * (191 / (150 - 50)) + 103;

				$(pcanvas).drawText({
					fillStyle: '#FF1B4D', strokeStyle: '#FF1B4D', strokeWidth: 1, fontSize: 12, fontFamily: 'Verdana, sans-serif',
					x: xPointHaThu, y: yPointHaThu + 13, text: '^'

				});

				$(pcanvas).drawText({
					fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
					x: xPointHaThu, y: yPointHaThu + 20, text: haThu

				});

				// Vẽ dải huyết áp
				$(pcanvas).drawLine({ strokeStyle: '#095519', strokeWidth: 1, x1: xPointHaTruong, y1: yPointHaTruong + 10, x2: xPointHaThu, y2: yPointHaThu + 10});

			}


		}

	}
	 */
	/*
	function themThongTinDataGio(gmhsct){
		var newgmhsct = gmhsct;
		var obj = {
			gmhsctid: $('#sel_Trang').val() + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.14", param);
		if (result && result.length > 0) {
			newgmhsct.DATA_GIO = result;
		} else {
			newgmhsct.DATA_GIO = new Array();
		}
		return newgmhsct;
	}
	 */
	/*
	function themThongTinDataGio2(gmhsct){
		var newgmhsct = gmhsct;
		var obj = {
			gmhsctid: $('#sel_Trang').val() + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.14", param);
		if (result && result.length > 0) {
			newgmhsct.DATA_GIO = result;
		} else {
			newgmhsct.DATA_GIO = new Array();
		}
		return newgmhsct;
	}
	 */
	/*
	function getTTChiTiet(idgmhsct){
		var gmhsct = {
			// DGTM_ASA : "",
			// DGTM_MALL : "",
			ASA: "0",
			MALLAMPATI: "0",
			DADAY: "0",
			CAPCUU: "0",
			TTCB_DIUNG : "",
			TTCB_TIENSU : "",
			DGTM_THUOCDUNG : "",
			DGTM_BATTHUONG : "",
			TTTD_TGBATDAUME : "",
			TTTD_TGKETTHUCME : "",
			TTTD_TGBATDAUMO : "",
			TTTD_TGKETTHUCMO : "",
			THUOC : "",
			DICHTRUYEN: "",
			ARRTHUOC: [],
			ARRDICHTRUYEN: [],
			KLDG_NHANXET: ""
		}
		var obj = {
			idgmhsct: idgmhsct + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.21", param);
		if (result && result.length > 0) {
			gmhsct = result[0];
			gmhsct.ARRTHUOC = JSON.parse(gmhsct.THUOC);
			gmhsct.ARRDICHTRUYEN = JSON.parse(gmhsct.DICHTRUYEN);
		}
		return gmhsct;
	}
	 */
}