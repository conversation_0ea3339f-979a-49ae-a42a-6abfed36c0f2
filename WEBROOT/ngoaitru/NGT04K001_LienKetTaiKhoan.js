function NGT04K001_LienKetTaiKhoan(opt) {
	_gridId = "grdBenhNhan";
	_gridHeader = "BENHNHANID,BENHNHANID,1,0,t,l;" +
	"<PERSON><PERSON> <PERSON>ệnh Nhân,MABENHNHAN,100,0,f,l;" +
	"T<PERSON><PERSON>,TENBENHNHAN,150,0,f,l;" +
	"<PERSON><PERSON><PERSON><PERSON> t<PERSON>,GIOITINH_VIEW,70,0,f,l;" +
	"GIOITINH,GIOITINHID,0,0,t,l;" +
	"<PERSON><PERSON><PERSON>,NGAYSINH,70,0,f,l;" +
	"<PERSON><PERSON> điện thoại,SDTBENHNHAN,100,0,t,l;<PERSON><PERSON><PERSON> kho<PERSON> li<PERSON> kết,TK_LIENKET,150,0,f,l;" +
	"MANGHENGHIEP,MANGHENGHIEP,0,0,t,l;MADANTOC,MADANTOC,0,0,t,l;MAQT,MAQT,0,0,t,l;" +
	"MATINH,MATINH,0,0,t,l;MAHUYEN,MAHUYEN,0,0,t,l;MAXA,MAXA,0,0,t,l;DIACHI,DIACHI,150,0,f,l;" +
	"CMND,CMND,0,0,t,l;MA_BHYT,MA_BHYT,0,0,t,l";
	this.load = doLoad;
	var checkAction = "0";
	function doLoad() {
		_initControl();
		_bindEvent();
	}
	
	function _initControl(){
		var sql_par=[];
		GridUtil.init(_gridId,"100%","500px","",false,this._gridHeader,false, { rowNum: 100,rowList: [100, 200, 300]});
//		loadGrid();
	}
	
	function _bindEvent() {
				
		$("#btnTIMKIEM").on("click",function(e){					
			loadGrid();
		});		
		GridUtil.setGridParam(_gridId,{ 
	    	onSelectRow: function(index, selected) {
	    		GridUtil.unmarkAll(_gridId);
	    		GridUtil.markRow(_gridId,index);
				var selRowId = $('#' + _gridId).jqGrid('getGridParam','selrow');
				var rowData = $('#' + _gridId).jqGrid('getRowData',selRowId);
				clearForm();
				FormUtil.setObjectToForm("inputForm","",rowData);
				if(rowData["TK_LIENKET"] == ""){
					$("#btnLienket").css("display","");
					$("#btnSua").css("display","none");
					$("#btnHuyLK").css("display","none");
					$("#btnInphieu").css("display","none");
					$("#btnGui").css("display","none");
					$("#btnHuy").css("display","none");
				}
				else{
					$("#btnLienket").css("display","none");
					$("#btnSua").css("display","");
					$("#btnHuyLK").css("display","");
					$("#btnInphieu").css("display","none");
					$("#btnGui").css("display","none");
					$("#btnHuy").css("display","none");
				}
	    	}
		});
	}
	
	 function loadGrid(){
	 	var obj = new Object();		
	 	obj.mabenhnhan = nvl($('#txtMABENHNHANSEARCH').val(),'-1');
	 	obj.sdtbenhnhan = nvl($('#txtSDTBNSEARCH').val(),'-1');
		var sql_par=RSUtil.buildParam("",[JSON.stringify(obj)]);
		$("#grdBenhNhan").jqGrid("clearGridData", true);
		GridUtil.loadGridBySqlPage("grdBenhNhan","NGT04K001.01",sql_par);
	 }
	 
		$("#btnLienket").on("click",function(e){
			setEnabled(["txtTENBENHNHAN","txtNGAYSINH","calNGAYSINH","cboGIOITINHID","txtTK_LIENKET"],["btnGui","btnHuy"]);
			$("#btnLienket").css("display","none");
			$("#btnInphieu").css("display","");
			$("#btnGui").css("display","");
			$("#btnHuy").css("display","");
			checkAction = 1;
		});
		
		$("#btnHuyLK").on("click",function(e){
        	DlgUtil.showConfirm("Bạn có hủy liên kết tài khoản này ? ", function(flag){
        		if(flag){
        			checkAction = 3;
        			guidulieu();
        		}
        	}); 
		});
		
		
		$("#btnSua").on("click",function(e){
			setEnabled(["txtTENBENHNHAN","txtNGAYSINH","calNGAYSINH","cboGIOITINHID","txtTK_LIENKET"],["btnGui","btnHuy"]);
			$("#btnSua").css("display","none");
			$("#btnInphieu").css("display","");
			$("#btnGui").css("display","");
			$("#btnHuy").css("display","");
			checkAction = 2;
		});
		
		
		$("#btnInphieu").on("click",function(e){
			if(validate()){
			var par = [{
				      name : 'i_benhnhanid',
				      type : 'String',
				      value : $("#txtBENHNHANID").val()
				      }];
			CommonUtil.openReportGet('window', 'RPT_GIAYDENGHI_TRACUUTHONGTIN_LICHSUKHAM', 'pdf', par);
			setEnabled(["btnGui","btnHuy"],[]);
			}
		});
		
		$("#btnGui").on("click",function(e){
			if(validate()){
				guidulieu();
			}
		});
		
		function guidulieu(){
			var strReturn = "";
			if(checkAction == 1){
				var text = '{ "phone":"' + $("#txtTK_LIENKET").val()
				+ '", "maCSYT":"' + opt.hospital_code
				+ '","maBN":"' + $("#txtMABENHNHAN").val()
				+ '","tenBN":"' + $("#txtTENBENHNHAN").val()
				+ '","ngaySinh":"' + $("#txtNGAYSINH").val()
				+ '","gioiTinh":"' + ($("#cboGIOITINHID").val()==1?1:0)
				+ '","quanHe":"' 
				+ '","MANGHENGHIEP":"'+$("#hidMANGHENGHIEP").val()
				+ '","MADT":"'+$("#hidMADANTOC").val()
				+ '","MAQT":"'+$("#hidMAQT").val()
				+ '","MATINH":"'+$("#hidMATINH").val()
				+ '","MAHUYEN":"'+$("#hidMAHUYEN").val()
				+ '","MAXA":"'+$("#hidMAXA").val()
				+ '","DIACHI":"'+$("#hidDIACHI").val()
				+ '","DIENTHOAI":"'+$("#hidSDTBENHNHAN").val()
				+ '","CMT":"'+$("#hidCMND").val()
				+ '","SOTHE_BHYT":"'+$("#hidMA_BHYT").val()+'"}';
				strReturn = ajaxSvc.PortalWS.lienket_tk_vncare(text,1);
			}
			if(checkAction == 3){
				var text = '{ "phone":"' + $("#txtTK_LIENKET").val()
				+ '", "maCSYT":"' + opt.hospital_code
				+ '","maBN":"' + $("#txtMABENHNHAN").val()+ '"}';
				strReturn = ajaxSvc.PortalWS.lienket_tk_vncare(text,3);
			}
			if(strReturn == "-1" || strReturn == "-2" || strReturn == "-3"){
				DlgUtil.showMsg("Có lỗi xảy ra trong quá trình liên kết tài khoản!");
				return false;
			}
			else{
				ret = JSON.parse(strReturn);
				DlgUtil.showMsg(ret.errorMessage);
				if(ret.errorCode == '0'){
				if(checkAction == 1){
					var objData = new Object();
					objData["BENHNHANID"]= $("#txtBENHNHANID").val();			
					objData["TK_LIENKET"]= $("#txtTK_LIENKET").val();
					objData["ACTION"]= checkAction;
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT04K001.UPD", JSON.stringify(objData));
				}
				
				if(checkAction == 2){
					var objData = new Object();
					objData["BENHNHANID"]= $("#txtBENHNHANID").val();			
					objData["TK_LIENKET"]= $("#txtTK_LIENKET").val();
					objData["ACTION"]= checkAction;
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT04K001.UPD", JSON.stringify(objData));
				}
				
				if(checkAction == 3){
					var objData = new Object();
					objData["BENHNHANID"]= $("#txtBENHNHANID").val();			
					objData["TK_LIENKET"]= '';
					objData["ACTION"]= checkAction;
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT04K001.UPD", JSON.stringify(objData));
				}
				}
				loadGrid();
				clearForm();
				return;
			}
		}
		
		
		function validate(){
			if($("#txtTENBENHNHAN").val() == ''){
				DlgUtil.showMsg("Tên bệnh nhân không được để trống!");
				return false;
			}
			if($("#txtNGAYSINH").val() == ''){
				DlgUtil.showMsg("Ngày sinh không được để trống!");
				return false;
			}
			if($("#txtTK_LIENKET").val() == ''){
				DlgUtil.showMsg("Tài khoản liên kết không được để trông!");
				return false;
			}			
			return true;
		};
		
		function setEnabled(_ena, _dis) {
			for (var i = 0; i < _ena.length; i++) {
				$("#" + _ena[i]).attr('disabled', false);
			}
			for (var i = 0; i < _dis.length; i++) {
				$("#" + _dis[i]).attr('disabled', true);
			}
		}
		
		function clearForm(){
			setEnabled([],["txtTENBENHNHAN","txtNGAYSINH","calNGAYSINH","cboGIOITINHID","txtTK_LIENKET","btnGui","btnHuy"]);
			$("#btnLienket").css("display","none");
			$("#btnSua").css("display","none");
			$("#btnHuyLK").css("display","none");
			$("#btnInphieu").css("display","none");
			$("#btnGui").css("display","none");
			$("#btnHuy").css("display","none");
			$("#txtTENBENHNHAN").val("");
			$("#txtNGAYSINH").val("");
			$("#cboGIOITINHID").val("-1");
			$("#txtTK_LIENKET").val("");
		}
}









