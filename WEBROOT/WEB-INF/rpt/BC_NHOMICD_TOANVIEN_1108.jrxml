<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BC_NHOMICD_TUYENDUOI_1108" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="358401c8-f5fa-4312-b4ab-e79a3798e2a3">
	<property name="ireport.zoom" value="1.948717100000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="tungay" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="denngay" class="java.util.Date"/>
	<parameter name="VPARS_khoaid_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_khoaid_TEXT" class="java.lang.String"/>
	<parameter name="VPARS_phongid_TEXT" class="java.lang.String"/>
	<parameter name="VPARS_phongid_VALUE" class="java.lang.String"/>
	<parameter name="doituong" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{ call bc_nhomicd_toanvien_1108($P{[UID]}, $P{[HID]}, $P{[SCH]}, $P{[IP]},
$P{tungay},$P{denngay},$P{doituong},$P{VPARS_khoaid_VALUE},$P{VPARS_phongid_VALUE},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="ICDNAME" class="java.lang.String"/>
	<field name="ICD10CODE" class="java.lang.String"/>
	<field name="ICD10CODEMIN" class="java.lang.String"/>
	<field name="ICD10CODEMAX" class="java.lang.String"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="TONG" class="java.lang.String"/>
	<field name="TYLE" class="java.math.BigDecimal"/>
	<variable name="SOLUONG_1" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="TYLE_1" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{TYLE}]]></variableExpression>
	</variable>
	<variable name="SOLUONG_2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="TYLE_2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TYLE}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="98" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="40" width="555" height="20" uuid="b50321d4-5098-4702-ae4e-4f27aefbd331"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[DANH SÁCH NHÓM BỆNH KÈM MÃ ICD-10 ]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="60" width="555" height="19" uuid="47e82d67-3270-454b-a39d-f2d79df203c2"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + " Đến ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="381" height="20" uuid="b3070fdb-dcac-49fe-ad76-5201f8839b8f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="20" width="381" height="20" uuid="c7a4a44b-555d-4fbe-a3b3-b918ad20ef4b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="79" width="555" height="19" uuid="030a75ec-3655-4eb8-a4c7-270eaf326d70"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["DS Khoa: "+$P{VPARS_khoaid_TEXT}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="35">
			<staticText>
				<reportElement x="34" y="15" width="254" height="20" uuid="10df2e9a-0482-41c9-85e8-17b22d9f0b0a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[NHÓM BỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="15" width="34" height="20" uuid="bf2fc83f-94ae-4196-af97-f77574f35035"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="474" y="15" width="81" height="20" uuid="ff964bb3-b4d4-4fa8-9415-dc03826a5440"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tỷ lệ]]></text>
			</staticText>
			<staticText>
				<reportElement x="288" y="15" width="105" height="20" uuid="f2074c9a-ea15-4f9b-a5d0-4e90c186d1c7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[MÃ ICD-10]]></text>
			</staticText>
			<staticText>
				<reportElement x="393" y="15" width="81" height="20" uuid="ff3adf77-c97e-4d6b-86dc-dd6817dcc2e4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượt]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="34" height="20" uuid="a4ee6562-9565-4c5d-b044-606fa6922c2e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="34" y="0" width="254" height="20" uuid="2fda9d19-cc60-4dcf-914f-829ba79d37e6"/>
				<box leftPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ICDNAME}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="288" y="0" width="105" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="be0925ff-431f-4968-936a-1b4350c72e88"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ICD10CODEMIN}==null?$F{ICD10CODE}:
($F{ICD10CODE}+" ("+$F{ICD10CODEMIN}+"-"+$F{ICD10CODEMAX}+")")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="474" y="0" width="81" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="197ec729-8cbd-4f60-9ef6-01497706de94"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{TYLE} == null ? "" : $F{TYLE}) + " %"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="393" y="0" width="81" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="e7dfbee1-e3b3-4a0e-88e0-bc1e3d96da9e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="51">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="34" height="20" uuid="03069965-cf47-46b8-838c-4b5388903b59"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="34" y="0" width="254" height="20" uuid="45cea0a8-e8c2-4573-a1aa-0a58bc6c8517"/>
				<box leftPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng cộng"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="288" y="0" width="105" height="20" uuid="83e05ddb-5b00-4be3-8968-c3d9576db47d"/>
				<box leftPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="393" y="0" width="81" height="20" uuid="ed3e7041-c327-4e65-ba72-e65a1c429b31"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SOLUONG_2} == null ? "" : $V{SOLUONG_2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="474" y="0" width="81" height="20" uuid="605eb100-86b7-4c26-8100-5b7291ebbeb8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["100%"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
