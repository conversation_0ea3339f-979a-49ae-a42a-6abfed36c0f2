function dSCHList(_opts) {
	var _gridSangLoc = "grdDSSangLoc";
	var _gridSangLocHeader = " ,ICON,35,0,ns,l;FLAG_CA,FLAG_CA,50,0,t,l,1,2;TINHTRANGDINHDUONGID,TINHTRANGDINHDUONGID,50,0,t,l,1,2;<PERSON><PERSON><PERSON><PERSON>ian <PERSON>,THOIGIANTAOPHIEU,120,0,f,l,1,2;"
			+ "<PERSON><PERSON><PERSON><PERSON> tạo,NGUOITAO,160,,0,f,l,1,2;<PERSON><PERSON><PERSON> chỉ định,KHOACHIDINH,120,0,f,l,1,2;<PERSON><PERSON><PERSON> luận nguy cơ suy dinh dưỡng,KETLUAN,250,0,f,c,1,2";
	var _hosobenhanid = _opts._hosobenhanid;
	var _khambenhid = _opts._khambenhid;
	var _tinhtrangid = '';
	var checkRequired;
	var that = this;
	this.load = doLoad;
	var modeView = '0';
	var isKyCa = false;
	var cfObj = new Object();//L2PT-102956
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		_initControl();
		_bindEvent();
		var _kySo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", "HIS_SUDUNG_KYSO_KYDIENTU");
		if (_kySo == '1') {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnExportCa").show();
		}
		//L2PT-27784 start
		if (_opts._mode == '1') {
			$("#btnThem").prop("disabled", true);
			$("#btnLuu").prop("disabled", true);
			$("#btnXoa").prop("disabled", true);
			$("#btnKyCa").prop("disabled", true);
			$("#btnHuyCa").prop("disabled", true);
		}
		//L2PT-27784 end
	}
	function _initControl() {
		GridUtil.init(_gridSangLoc, "100%", "150", "Danh sách phiếu sàng lọc dinh dưỡng", false, _gridSangLocHeader, true);
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "SLDD_TT_MODEVIEW;SLDD_TT_LPKHA;SLDD_TT_REQ_MODE;SLDD_SHOW_CBO_SA");//L2PT-13486
		if (data_ar != null && data_ar.length > 0) {
			//Beg_HaNv_211221: Điều chỉnh form - L2PT-11875 L2PT-12779 L2PT-46333
			cfObj = data_ar[0];
			if (cfObj.SLDD_TT_MODEVIEW != '0') {
				modeView = cfObj.SLDD_TT_MODEVIEW;
				if (modeView == '1') {
					$("#divNLKN").show();
					$("#divTVDD").show();
					$("#divPhanLoai").hide();
					$("#tbl_dg_2").hide();
				} else if (modeView == '2') {
					$("#td_sl_sutcan").text("- Có giảm cân trong 6 tháng gần đây không?");
					$("#td_sl_luongan").text("- Ăn uống có giảm trong 3 tháng gần đây không?");
					$("#td_bmi2").text("Từ 16 đến ≤ 18,5 kg/m2");
					$("#td_bmi3").text("Dưới 16 kg/m2");
					$("#td_luongan3").text("Không tự ăn được đường miệng");
					//Beg_HaNv_130522: Điều chỉnh form DKHTH - L2PT-41195
					$("#td_sl_bmi").text("- BMI < 18.5 hoặc BMI > 23");
					$("#tr_sl_benhnang").hide();
					$("#tbl_dg_1").hide();
					$("#tbl_dg_2").show();
					$("#divPhanLoai").hide();
					$("#divKH_TAIDANHGIA7").text("Sau 7 ngày (Ở người bệnh không có nguy cơ suy dinh dưỡng)");
					$("#divKH_TAIDANHGIA3").text("Sau 3 ngày (Ở người bệnh có nguy cơ suy dinh dưỡng)");
					//End_HaNv_130423
				}
				//Beg_HaNv_230623: Tùy chỉnh giao diện BTN, set chỉ số BMI châu Á - L2PT-46333
				else if (modeView == '3') {
					$("#tdDOASDD").text("< 18,5");
					$("#tdSDD_BT").text("18,5 - 22,9");
					$("#tdSDD_TC").text("≥ 23");
					$("#tdSDD_TBP").text("23 - 24,9");
					$("#tdSDD_BP").text("≥ 25");
					$("#tdSDD_BP_DO1").text("25 - 29,9");
					$("#tdSDD_BP_DO2").text("30 - 39,9");
					$("#tdSDD_BP_DO3").text("≥ 40");
					$("#tbl_dg_2").hide();
				}
				//End_HaNv_230623
			} else {
				$("#tbl_dg_2").hide();
			}
			if (cfObj.SLDD_TT_LPKHA == '1') {
				$("#divSL_KETLUAN_KHONG").html("&rarr; Tái sàng lọc sau 2 tuần");
				$("#divKH_TAIDANHGIA7").text("Sau 2 tuần (Ở người bệnh không suy dinh dưỡng)");
				$("#divKH_TAIDANHGIA3").text("Sau 1 tuần (Ở người bệnh suy dinh dưỡng)");
			}
			if (cfObj.SLDD_TT_LPKHA == '2') {
				$("#divKH_TAIDANHGIA7").text("Sau 15 ngày (Ở người bệnh không suy dinh dưỡng)");
				$("#divKH_TAIDANHGIA3").text("Sau 5-7 ngày (Ở người bệnh suy dinh dưỡng)");
			}
			//L2PT-13486 end
			if (cfObj.SLDD_SHOW_CBO_SA == '1') {
				$("#divSUATANCBO").show();
				$("#divSUATANTXT").hide();
				ComboUtil.getComboTag("cboKH_CHIDINH", "NTU02.D161.CDA.01", [], "", {
					value : '-1',
					text : '---Chọn---'
				}, 'sql');
			}
		}
		// Lay thong tin benh nhan
		var _par = [ _khambenhid ];
		var benhNhanInfo = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D089.TTBN", _par.join('$'));
		if (benhNhanInfo != null && benhNhanInfo.length > 0) {
			FormUtil.setObjectToForm("inputForm", "", benhNhanInfo[0]);
		}
		$("#txtTHOIGIANTAOPHIEU").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')); //L2PT-13484
		//HungND - L2PT-63854
		var NTU_SYNC_CCCN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_SYNC_CCCN');
		if (NTU_SYNC_CCCN.includes('1')) {
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.05.V2", _khambenhid, []);
			if (data_ar != null && data_ar.length > 0) {
				var _rowKB = data_ar[0];
				var chieucao = _rowKB.KHAMBENH_CHIEUCAO;
				var cannang = _rowKB.KHAMBENH_CANNANG;
				$('#txtCANNANG_VAOVIEN').val(cannang);
				$('#txtCANNANG_RAVIEN').val(cannang);
				$('#txtCHIEUCAO').val(chieucao);
			}
		}
		//HungND - L2PT-63854 END
		//L2PT-102956
		if (cfObj.SLDD_TT_REQ_MODE == '1') {
			$('#lblChanDoan').addClass('required');
			$('#txtCHANDOAN').attr("valrule", "Chẩn đoán,required");
			$('#lblCanNangVV').addClass('required');
			$('#txtCANNANG_VAOVIEN').attr("valrule", "Cân nặng vào viện,required");
			$('#lblChieuCaoVV').addClass('required');
			$('#txtCHIEUCAO').attr("valrule", "Chiều cao,required");
			$('#lblBMIVV').addClass('required');
			$('#txtBMI_VAOVIEN').attr("valrule", "Cân nặng vào viện,required");
			$('[attrreq]').each(function(index, element) {
				$(this).addClass("required");
			});
		}
		loadGridData();
		loadComboGrid();
	};
	function _bindEvent() {
		GridUtil.setGridParam(_gridSangLoc, {
			onSelectRow : function(id) {
				GridUtil.unmarkAll(_gridSangLoc);
				GridUtil.markRow(_gridSangLoc, id);
				if (id) {
					var _row = $("#" + _gridSangLoc).jqGrid('getRowData', id);
					_tinhtrangid = _row.TINHTRANGDINHDUONGID;
					var objData = new Object();
					objData.TINHTRANGDINHDUONGID = _row.TINHTRANGDINHDUONGID;
					objData.KHAMBENHID = _khambenhid;
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D126.01", JSON.stringify(objData));
					if (data_ar != null && data_ar.length > 0) {
						var row = data_ar[0];
						FormUtil.clearForm("inputForm", "");
						FormUtil.setObjectToForm("inputForm", "", row);
						checkRadio(row);
						if (row["CHANDOAN"] != '') {
							$("#btnHuy").css("display", "block");
						}
						if (row["THOIGIANTAOPHIEU"] == '') {
							$("#txtTHOIGIANTAOPHIEU").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')); //L2PT-13486
						}
						//Beg_HaNv_211221: Điều chỉnh form - L2PT-11875
						if (modeView == '1') {
							if (row.TUVAN_DINHDUONG != null && row.TUVAN_DINHDUONG != '') {
								$('input:radio[name=rdoTUVAN_DINHDUONG]').filter('[value=' + row.TUVAN_DINHDUONG + ']').prop('checked', true);
							}
						}
						//End_HaNv_211221
					}
				}
			},
			gridComplete : function() {
				var ids = $("#" + _gridSangLoc).getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = $("#" + _gridSangLoc).jqGrid('getRowData', id);
					var _icon = '';
					if (row.FLAG_CA == '1') {
						_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
					}
					$("#" + _gridSangLoc).jqGrid('setCell', id, 'ICON', _icon);
				}
			}
		});
		$('input[type=radio][name=radSL_KETLUAN]').change(function() {
			if ($("input[name='radSL_KETLUAN']:checked").val() == '1') {
				$("#divDanhGia").show();
			} else {
				$("#divDanhGia").hide();
			}
		});
		$("#txtDG_BMI").change(function() {
			if (isNaN($('#txtDG_BMI').val().trim())) {
				DlgUtil.showMsg('Đánh giá tình trạng dinh dưỡng(BMI) không chính xác !', function() {
					$('#txtDG_BMI').focus();
				});
			} else {
				var _tong = tinhtong();
				$("#txtDG_TONGDIEM").val(_tong);
			}
		});
		$("#txtDG_SUTCAN").change(function() {
			if (isNaN($('#txtDG_SUTCAN').val().trim())) {
				DlgUtil.showMsg('Đánh giá tình trạng dinh dưỡng(Sụt Cân) không chính xác !', function() {
					$('#txtDG_SUTCAN').focus();
				});
			} else {
				var _tong = tinhtong();
				$("#txtDG_TONGDIEM").val(_tong);
			}
		});
		$("#txtDG_LUONGAN").change(function() {
			if (isNaN($('#txtDG_LUONGAN').val().trim())) {
				DlgUtil.showMsg('Đánh giá tình trạng dinh dưỡng(Lượng ăn) không chính xác !', function() {
					$('#txtDG_LUONGAN').focus();
				});
			} else {
				var _tong = tinhtong();
				$("#txtDG_TONGDIEM").val(_tong);
			}
		});
		$("#txtDG_BENHLY").change(function() {
			if (isNaN($('#txtDG_BENHLY').val().trim())) {
				DlgUtil.showMsg('Đánh giá tình trạng dinh dưỡng(Bệnh lý) không chính xác !', function() {
					$('#txtDG_BENHLY').focus();
				});
			} else {
				var _tong = tinhtong();
				$("#txtDG_TONGDIEM").val(_tong);
			}
		});
		$("#btnThem").click(function() {
			insert('0');
		});
		$("#btnLuu").click(function() {
			if (_tinhtrangid == '') {
				DlgUtil.showMsg("Chưa chọn phiếu đánh giá để chỉnh sửa!");
				return;
			}
			insert('1');
		});
		$("#btnIn").click(function() {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : _hosobenhanid
			}, {
				name : 'tinhtrangdinhduongid',
				type : 'String',
				value : _tinhtrangid
			}, {
				name : 'tinhtrangid',
				type : 'String',
				value : _tinhtrangid
			} ];
			openReport('window', "PHIEU_DANHGIA_TT_DINHDUONG_TRUONGTHANH", "pdf", par);
		});
		//L2PT-13074 start
		$("#btnRefresh").on("click", function() {
			_initControl();
		});
		//L2PT-13074 end
		// L2PT-13227 start
		$("#btnXoa").on("click", function() {
			DlgUtil.showConfirm("Bạn có muốn xóa phiếu này không?", function(flag) {
				if (flag) {
					var par = [ _tinhtrangid ];
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.03", par.join('$'));
					var data = $.parseJSON(result);
					if (data == '-1') {
						DlgUtil.showMsg("Đã có lỗi xảy ra!", undefined, 2000);
					} else {
						DlgUtil.showMsg('Xóa thành công phiếu!');
						loadGridData();
					}
				}
			});
		});
		// L2PT-13227 end
		$("#btnHuy").click(function() {
			$("#txtCHANDOAN_ID").val('');
			$("#txtCHANDOAN").val('');
			$("#btnHuy").css("display", "none");
		});
		$("#btnDong").on("click", function() {
			parent.DlgUtil.close("dlgTTDD");
		});
		$("#txtCANNANG_VAOVIEN").change(function() {
			_calBMI();
		});
		$("#txtCHIEUCAO").change(function() {
			_calBMI();
		});
		$("#txtCANNANG_RAVIEN").change(function() {
			_calBMI();
		});
		$("#btnKyCa").on("click", function() {
			isKyCa = true;
			insert(_tinhtrangid == '' ? '0' : '1');
		});
		$("#btnHuyCa").on("click", function() {
			if (_tinhtrangid == '') {
				DlgUtil.showMsg("Chưa chọn phiếu đánh giá để hủy ký!");
				return;
			}
			_caRpt('2');
		});
		$("#btnExportCa").on("click", function() {
			if (_tinhtrangid == '') {
				DlgUtil.showMsg("Chưa chọn phiếu đánh giá để in ký số!");
				return;
			}
			_caRpt('0');
		});
	}
	function loadComboGrid() {
		var _colICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		ComboUtil.initComboGrid("txtCHANDOAN_ID", "NT.008", [], "600px", _colICD, function(event, ui) {
			$('#txtCHANDOAN').val($("#txtCHANDOAN").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHANDOAN").val() + ";" + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			if ($("#txtCHANDOAN_ID").val() != '') {
				$("#btnHuy").css("display", "block");
			}
			return false;
		});
	}
	function insert(mode) {
		//L2PT-102956
		if (cfObj.SLDD_TT_REQ_MODE == '1') {
			var valid = new DataValidator("inputForm").validateForm();
			if (!valid) {
				return;
			}
			if (!$('#radSL_BMI_KHONG').is(':checked') && !$('#radSL_BMI_CO').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Sàng lọc nguy cơ - BMI < 20.5 kg/m2");
			}
			if (!$('#radSL_SUTCAN_KHONG').is(':checked') && !$('#radSL_SUTCAN_CO').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Sàng lọc nguy cơ - Sụt cân trong 1 tháng qua");
			}
			if (!$('#radSL_LUONGAN_KHONG').is(':checked') && !$('#radSL_LUONGAN_CO').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Sàng lọc nguy cơ - Lượng ăn giảm trong tuần qua");
			}
			if (!$('#radSL_BENHNANG_KHONG').is(':checked') && !$('#radSL_BENHNANG_CO').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Sàng lọc nguy cơ - Bệnh nặng đi lại hạn chế");
			}
			if (!$('#radSL_KETLUAN_KHONG').is(':checked') && !$('#radSL_KETLUAN_CO').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Sàng lọc nguy cơ - Kết luận nguy cơ suy dinh dưỡng");
			}
			if ($('#radSL_KETLUAN_CO').is(':checked')) {
				if (!$('#txtDG_BMI').val()) {
					return DlgUtil.showMsg("Bạn chưa nhập điểm tiêu chí đánh giá BMI", function() {
						$('#txtDG_BMI').focus();
					});
				}
				if (!$('#txtDG_SUTCAN').val()) {
					return DlgUtil.showMsg("Bạn chưa nhập điểm tiêu chí đánh giá Sụt Cân", function() {
						$('#txtDG_SUTCAN').focus();
					});
				}
				if (!$('#txtDG_LUONGAN').val()) {
					return DlgUtil.showMsg("Bạn chưa nhập điểm tiêu chí đánh giá Lượng ăn", function() {
						$('#txtDG_LUONGAN').focus();
					});
				}
				if (!$('#txtDG_BENHLY').val()) {
					return DlgUtil.showMsg("Bạn chưa nhập điểm tiêu chí đánh giá Bệnh lý", function() {
						$('#txtDG_BENHLY').focus();
					});
				}
				if (!$('#radDG_KETLUAN_KHONG').is(':checked') && !$('#radDG_KETLUAN_CO').is(':checked')) {
					return DlgUtil.showMsg("Bạn chưa nhập đánh giá kết luận");
				}
			}
			if ((cfObj.SLDD_SHOW_CBO_SA == '1' && $("#cboKH_CHIDINH").val() == '-1') || (cfObj.SLDD_SHOW_CBO_SA == '0' && !$('#txtKH_CHIDINH').val())) {//HaNv_180225: L2PT-127947
				DlgUtil.showMsg("Bạn chưa nhập Kế hoạch can thiệp - Chỉ định chế độ ăn");
				return;
			}
			if (!$('#radSL_BENHNANG_KHONG').is(':checked') && !$('#chkKH_ONGTHONG').is(':checked') && !$('#chkKH_TINHMACH').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Kế hoạch can thiệp - Đường nuôi ăn");
			}
			if (!$('#radKH_MOIHCDD_CO').is(':checked') && !$('#radKH_MOIHCDD_KHONG').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Kế hoạch can thiệp - Mời hội chẩn dinh dưỡng");
			}
			if (!$('#radKH_TAIDANHGIA7').is(':checked') && !$('#radKH_TAIDANHGIA3').is(':checked')) {
				return DlgUtil.showMsg("Bạn chưa nhập Kế hoạch can thiệp - Tái đánh giá");
			}
		}
		checkdata();
		if (checkRequired == 0) {
			var _check_sl = 0;
			var _check_dg = 0;
			var objData = new Object();
			var _sl_bmi = $("input[name='radSL_BMI']:checked").val();
			var _sl_sutcan = $("input[name='radSL_SUTCAN']:checked").val();
			var _sl_luongan = $("input[name='radSL_LUONGAN']:checked").val();
			var _sl_benhnang = $("input[name='radSL_BENHNANG']:checked").val();
			var _sl_ketluan = $("input[name='radSL_KETLUAN']:checked").val();
			var _dg_ketluan = $("input[name='radDG_KETLUAN']:checked").val();
			var _kh_moihcdd = $("input[name='radKH_MOIHCDD']:checked").val();
			var _kh_taidanhgia = $("input[name='radKH_TAIDANHGIA']:checked").val();
			var _dg_tongdiem = $("#txtDG_TONGDIEM").val();
			FormUtil.setFormToObject("inputForm", "", objData);
			objData["HOSOBENHANID"] = _hosobenhanid;
			objData["KHAMBENHID"] = _khambenhid;
			objData["SL_BMI"] = _sl_bmi;
			objData["SL_SUTCAN"] = _sl_sutcan;
			objData["SL_LUONGAN"] = _sl_luongan;
			objData["SL_BENHNANG"] = _sl_benhnang;
			objData["SL_KETLUAN"] = _sl_ketluan;
			objData["DG_KETLUAN"] = _dg_ketluan;
			objData["KH_MOIHCDD"] = _kh_moihcdd;
			objData["KH_TAIDANHGIA"] = _kh_taidanhgia;
			if (mode == '0') {
				objData.TYPE = 'Add'
			} else {
				objData.TYPE = 'Upd'
				objData.TINHTRANGDINHDUONGID = _tinhtrangid;
			}
			//Beg_HaNv_211221: Điều chỉnh form - L2PT-11875
			if (modeView == '1') {
				if ($.find("[name='rdoTUVAN_DINHDUONG']:checked").length > 0) {
					objData.TUVAN_DINHDUONG = $.find("[name='rdoTUVAN_DINHDUONG']:checked")[0].value;
				} else {
					objData.TUVAN_DINHDUONG = '';
				}
				objData.NANGLUONG_KHUYENNGHI = $("#txtNANGLUONG_KHUYENNGHI").val();
			} else {
				objData.TUVAN_DINHDUONG = '';
				objData.NANGLUONG_KHUYENNGHI = '';
			}
			//End_HaNv_211221
			if (cfObj.SLDD_SHOW_CBO_SA == '1') {//HaNv_180225: L2PT-127947
				objData.KH_CHIDINH = $("#cboKH_CHIDINH").val();
			} else {
				objData.KH_CHIDINH = $("#txtKH_CHIDINH").val();
			}
			if (typeof (_sl_ketluan) == 'undefined') {
				_check_sl = 1;
			} else if (_sl_ketluan == '0') {
				if (_sl_bmi == '1' || _sl_sutcan == '1' || _sl_luongan == '1' || _sl_benhnang == '1') {
					_check_sl = 2;
				}
			} else {
				if (_sl_bmi != '1' && _sl_sutcan != '1' && _sl_luongan != '1' && _sl_benhnang != '1') {
					_check_sl = 2;
				}
			}
			if (typeof (_dg_ketluan) == 'undefined') {
				_check_dg = 1;
			} else if (_dg_ketluan == '0') {
				if (_dg_tongdiem >= 2) {
					_check_dg = 2;
				}
			} else {
				if (_dg_tongdiem < 2) {
					_check_dg = 2;
				}
			}
			if (_check_sl == 1) {
				DlgUtil.showConfirm("Kết luận nguy cơ suy dinh dưỡng chưa được chọn.Bạn có muốn tiếp tục?", function(flag) {
					if (flag) {
						if (_check_dg == 1) {
							DlgUtil.showConfirm("Kết luận đánh giá tình trạng dinh dưỡng chưa được chọn.Bạn có muốn tiếp tục?", function(flag1) {
								if (flag1) {
									var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
									if (fl == '-1') {
										DlgUtil.showMsg("Không thành công !");
									} else {
										if (isKyCa) {
											_caRpt('1');
										} else {
											if (mode == '0') {
												DlgUtil.showMsg("Thêm mới thành công !");
											} else {
												DlgUtil.showMsg("Lưu thành công !");
											}
											loadGridData();
										}
									}
								}
							});
						} else if (_check_dg == 2) {
							DlgUtil.showConfirm("Kết luận đánh giá tình trạng dinh dưỡng không chính xác.Bạn có muốn tiếp tục?", function(flag1) {
								if (flag1) {
									var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
									if (fl == '-1') {
										DlgUtil.showMsg("Không thành công !");
									} else {
										if (isKyCa) {
											_caRpt('1');
										} else {
											if (mode == '0') {
												DlgUtil.showMsg("Thêm mới thành công !");
											} else {
												DlgUtil.showMsg("Lưu thành công !");
											}
											loadGridData();
										}
									}
								}
							});
						} else {
							var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
							if (fl == '-1') {
								DlgUtil.showMsg("Không thành công !");
							} else {
								if (isKyCa) {
									_caRpt('1');
								} else {
									if (mode == '0') {
										DlgUtil.showMsg("Thêm mới thành công !");
									} else {
										DlgUtil.showMsg("Lưu thành công !");
									}
									loadGridData();
								}
							}
						}
					}
				});
			} else if (_check_sl == 2) {
				DlgUtil.showConfirm("Kết luận nguy cơ suy dinh dưỡng không chính xác.Bạn có muốn tiếp tục?", function(flag) {
					if (flag) {
						if (_check_dg == 1) {
							DlgUtil.showConfirm("Kết luận đánh giá tình trạng dinh dưỡng chưa được chọn.Bạn có muốn tiếp tục?", function(flag1) {
								if (flag1) {
									var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
									if (fl == '-1') {
										DlgUtil.showMsg("Không thành công !");
									} else {
										if (isKyCa) {
											_caRpt('1');
										} else {
											if (mode == '0') {
												DlgUtil.showMsg("Thêm mới thành công !");
											} else {
												DlgUtil.showMsg("Lưu thành công !");
											}
											loadGridData();
										}
									}
								}
							});
						} else if (_check_dg == 2) {
							DlgUtil.showConfirm("Kết luận đánh giá tình trạng dinh dưỡng không chính xác.Bạn có muốn tiếp tục?", function(flag1) {
								if (flag1) {
									var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
									if (fl == '-1') {
										DlgUtil.showMsg("Không thành công !");
									} else {
										if (isKyCa) {
											_caRpt('1');
										} else {
											if (mode == '0') {
												DlgUtil.showMsg("Thêm mới thành công !");
											} else {
												DlgUtil.showMsg("Lưu thành công !");
											}
											loadGridData();
										}
									}
								}
							});
						} else {
							var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
							if (fl == '-1') {
								DlgUtil.showMsg("Không thành công !");
							} else {
								if (isKyCa) {
									_caRpt('1');
								} else {
									if (mode == '0') {
										DlgUtil.showMsg("Thêm mới thành công !");
									} else {
										DlgUtil.showMsg("Lưu thành công !");
									}
									loadGridData();
								}
							}
						}
					}
				});
			} else {
				if (_check_dg == 1) {
					DlgUtil.showConfirm("Kết luận đánh giá tình trạng dinh dưỡng chưa được chọn.Bạn có muốn tiếp tục?", function(flag1) {
						if (flag1) {
							var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
							if (fl == '-1') {
								DlgUtil.showMsg("Không thành công !");
							} else {
								if (isKyCa) {
									_caRpt('1');
								} else {
									if (mode == '0') {
										DlgUtil.showMsg("Thêm mới thành công !");
									} else {
										DlgUtil.showMsg("Lưu thành công !");
									}
									loadGridData();
								}
							}
						}
					});
				} else if (_check_dg == 2) {
					DlgUtil.showConfirm("Kết luận đánh giá tình trạng dinh dưỡng không chính xác.Bạn có muốn tiếp tục?", function(flag1) {
						if (flag1) {
							var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
							if (fl == '-1') {
								DlgUtil.showMsg("Không thành công !");
							} else {
								if (isKyCa) {
									_caRpt('1');
								} else {
									if (mode == '0') {
										DlgUtil.showMsg("Thêm mới thành công !");
									} else {
										DlgUtil.showMsg("Lưu thành công !");
									}
									loadGridData();
								}
							}
						}
					});
				} else {
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D126.02", JSON.stringify(objData));
					if (fl == '-1') {
						DlgUtil.showMsg("Không thành công !");
					} else {
						if (isKyCa) {
							_caRpt('1');
						} else {
							if (mode == '0') {
								DlgUtil.showMsg("Thêm mới thành công !");
							} else {
								DlgUtil.showMsg("Lưu thành công !");
							}
							loadGridData();
						}
					}
				}
			}
		}
	}
	function checkRadio(objData) {
		if (objData["SL_BMI"] == 1) {
			$("#radSL_BMI_CO").prop("checked", true);
		} else if (objData["SL_BMI"] == 0) {
			$("#radSL_BMI_KHONG").prop("checked", true);
		} else {
			$("#radSL_BMI_CO").prop("checked", false);
			$("#radSL_BMI_KHONG").prop("checked", false);
		}
		if (objData["SL_SUTCAN"] == 1) {
			$("#radSL_SUTCAN_CO").prop("checked", true);
		} else if (objData["SL_SUTCAN"] == 0) {
			$("#radSL_SUTCAN_KHONG").prop("checked", true);
		} else {
			$("#radSL_SUTCAN_CO").prop("checked", false);
			$("#radSL_SUTCAN_KHONG").prop("checked", false);
		}
		if (objData["SL_LUONGAN"] == 1) {
			$("#radSL_LUONGAN_CO").prop("checked", true);
		} else if (objData["SL_LUONGAN"] == 0) {
			$("#radSL_LUONGAN_KHONG").prop("checked", true);
		} else {
			$("#radSL_LUONGAN_CO").prop("checked", false);
			$("#radSL_LUONGAN_KHONG").prop("checked", false);
		}
		if (objData["SL_BENHNANG"] == 1) {
			$("#radSL_BENHNANG_CO").prop("checked", true);
		} else if (objData["SL_BENHNANG"] == 0) {
			$("#radSL_BENHNANG_KHONG").prop("checked", true);
		} else {
			$("#radSL_BENHNANG_CO").prop("checked", false);
			$("#radSL_BENHNANG_KHONG").prop("checked", false);
		}
		if (objData["SL_KETLUAN"] == 1) {
			$("#radSL_KETLUAN_CO").prop("checked", true);
			$("#divDanhGia").show();
		} else if (objData["SL_KETLUAN"] == 0) {
			$("#radSL_KETLUAN_KHONG").prop("checked", true);
			$("#divDanhGia").hide();
		} else {
			$("#radSL_KETLUAN_CO").prop("checked", false);
			$("#radSL_KETLUAN_KHONG").prop("checked", false);
			$("#divDanhGia").hide();
		}
		if (objData["DG_KETLUAN"] == 2) {
			$("#radDG_KETLUAN_NANG").prop("checked", true);
		} else if (objData["DG_KETLUAN"] == 1) {
			$("#radDG_KETLUAN_CO").prop("checked", true);
		} else if (objData["DG_KETLUAN"] == 0) {
			$("#radDG_KETLUAN_KHONG").prop("checked", true);
		} else {
			$("#radDG_KETLUAN_NANG").prop("checked", false);
			$("#radDG_KETLUAN_CO").prop("checked", false);
			$("#radDG_KETLUAN_KHONG").prop("checked", false);
		}
		if (objData["KH_MOIHCDD"] == 1) {
			$("#radKH_MOIHCDD_CO").prop("checked", true);
		} else if (objData["KH_MOIHCDD"] == 0) {
			$("#radKH_MOIHCDD_KHONG").prop("checked", true);
		} else {
			$("#radKH_MOIHCDD_CO").prop("checked", false);
			$("#radKH_MOIHCDD_KHONG").prop("checked", false);
		}
		if (objData["KH_TAIDANHGIA"] == 3) {
			$("#radKH_TAIDANHGIA3").prop("checked", true);
		} else if (objData["KH_TAIDANHGIA"] == 7) {
			$("#radKH_TAIDANHGIA7").prop("checked", true);
		} else {
			$("#radKH_TAIDANHGIA3").prop("checked", false);
			$("#radKH_TAIDANHGIA7").prop("checked", false);
		}
		if (objData["rdoTUVAN_DINHDUONG"] == 1) {
			$("#rdoTUVAN_DINHDUONG_CO").prop("checked", true);
		} else if (objData["rdoTUVAN_DINHDUONG"] == 0) {
			$("#rdoTUVAN_DINHDUONG_KHONG").prop("checked", true);
		} else {
			$("#rdoTUVAN_DINHDUONG_CO").prop("checked", false);
			$("#rdoTUVAN_DINHDUONG_KHONG").prop("checked", false);
		}
	}
	function tinhtong() {
		var _bmi = $("#txtDG_BMI").val() == '' ? 0 : parseInt($("#txtDG_BMI").val());
		var _sutcan = $("#txtDG_SUTCAN").val() == '' ? 0 : parseInt($("#txtDG_SUTCAN").val());
		var _luongan = $("#txtDG_LUONGAN").val() == '' ? 0 : parseInt($("#txtDG_LUONGAN").val());
		var _benhly = $("#txtDG_BENHLY").val() == '' ? 0 : parseInt($("#txtDG_BENHLY").val());
		var _number = _bmi + _sutcan + _luongan + _benhly;
		return _number;
	}
	function checkdata() {
		checkRequired = 0;
		var regex = /^\d+(.\d{1,2})?$/;
		if ($('#txtCANNANG_VAOVIEN').val().trim() != '') {
			if (!regex.test($('#txtCANNANG_VAOVIEN').val())) {
				checkRequired = 1;
				DlgUtil.showMsg('Cân nặng vào viện không chính xác!', function() {
					$('#txtCANNANG_VAOVIEN').focus();
				});
				return;
			}
		}
		if ($('#txtCHIEUCAO').val().trim() != '') {
			if (!regex.test($('#txtCHIEUCAO').val())) {
				checkRequired = 1;
				DlgUtil.showMsg('Chiều cao không chính xác!', function() {
					$('#txtCHIEUCAO').focus();
				});
				return;
			}
		}
		if ($('#txtBMI_VAOVIEN').val().trim() != '') {
			if (!regex.test($('#txtBMI_VAOVIEN').val())) {
				checkRequired = 1;
				DlgUtil.showMsg('BMI vào viện không chính xác!', function() {
					$('#txtBMI_VAOVIEN').focus();
				});
				return;
			}
		}
		if ($('#txtCANNANG_RAVIEN').val().trim() != '') {
			if (!regex.test($('#txtCANNANG_RAVIEN').val())) {
				checkRequired = 1;
				DlgUtil.showMsg('Cân nặng ra viện không chính xác!', function() {
					$('#txtCANNANG_RAVIEN').focus();
				});
				return;
			}
		}
		if ($('#txtBMI_RAVIEN').val().trim() != '') {
			if (!regex.test($('#txtBMI_RAVIEN').val())) {
				checkRequired = 1;
				DlgUtil.showMsg('BMI ra viện không chính xác!', function() {
					$('#txtBMI_RAVIEN').focus();
				});
				return;
			}
		}
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			value : _khambenhid
		});
		var tgvaovien = jsonrpc.AjaxJson.getOneValue('NTU02D036.TGVAOVIEN', _sql_par);
		if (!compareDate(tgvaovien, $('#txtTHOIGIANTAOPHIEU').val(), "DD/MM/YYYY HH:mm:ss")) {
			checkRequired = 1;
			$("#txtTHOIGIANTAOPHIEU").focus();
			DlgUtil.showMsg("Thời gian tạo phiếu không được trước ngày vào viện!");
			return;
		}
	}
	function _calBMI() {
		if ($("#txtCHIEUCAO").val() != '' && $("#txtCANNANG_VAOVIEN").val() != '') {
			var _cannang = parseFloat($("#txtCANNANG_VAOVIEN").val());
			var _chieucao = parseFloat($("#txtCHIEUCAO").val());
			var bmi = chiSoBMI(_cannang, _chieucao);
			$("#txtBMI_VAOVIEN").val(bmi);
		}
		if ($("#txtCHIEUCAO").val() != '' && $("#txtCANNANG_RAVIEN").val() != '') {
			var _cannang = parseFloat($("#txtCANNANG_RAVIEN").val());
			var _chieucao = parseFloat($("#txtCHIEUCAO").val());
			var bmi = chiSoBMI(_cannang, _chieucao);
			$("#txtBMI_RAVIEN").val(bmi);
		}
	}
	function chiSoBMI(trongluong, chieucao) {
		if (chieucao == '0') {
			return parseFloat(0).toFixed(1);
		}
		chieucao_m = chieucao / 100;
		return parseFloat(trongluong / (chieucao_m * chieucao_m)).toFixed(1);
	}
	function loadGridData() {
		var sql_par = [];
		var param = RSUtil.buildParam("", [ _hosobenhanid ]);
		GridUtil.loadGridBySqlPage(_gridSangLoc, "NTU02D126.10", param);
	}
	function _caRpt(signType) {
		isKyCa = false;
		var par_rpt_KySo = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : _hosobenhanid
		}, {
			name : 'tinhtrangdinhduongid',
			type : 'String',
			value : _tinhtrangid
		}, {
			name : 'RPT_CODE',
			type : 'String',
			value : 'PHIEU_DANHGIA_TT_DINHDUONG_TRUONGTHANH'
		} ];
		if (signType == '0') {
			CommonUtil.openReportGetCA2(par_rpt_KySo, false);
		} else {
			CommonUtil.kyCA(par_rpt_KySo, signType, true);
			EventUtil.setEvent("eventKyCA", function(e) {
				DlgUtil.showMsg(e.res);
				loadGridData();
			});
		}
	}
}
