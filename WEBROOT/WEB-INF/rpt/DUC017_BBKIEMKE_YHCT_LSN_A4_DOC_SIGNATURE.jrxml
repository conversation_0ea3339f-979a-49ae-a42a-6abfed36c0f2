<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_LSO_A4_SIGNATURE" language="groovy" pageWidth="555" pageHeight="274" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isIgnorePagination="true" uuid="76c4f380-909e-4a6c-a386-4cfcd93dde8f">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="nhapxuatid" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_CHUKYHDKN_YHCT_LSN($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{nhapxuatid},$P{ora_cursor})}]]>
	</queryString>
	<field name="STT" class="java.lang.String"/>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="CHUCDANH" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<summary>
		<band height="125">
			<crosstab runDirection="RTL">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="537" height="125" uuid="a7fb2849-f520-4af5-9d6d-bb343aabbe73"/>
				<rowGroup name="NHAPXUATID" width="0">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA["1"]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#F0F8FF" mode="Opaque"/>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="STT" height="0">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{STT}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#F0F8FF" mode="Opaque"/>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents/>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="HOTENMeasure" class="java.lang.String">
					<measureExpression><![CDATA[$F{HOTEN}]]></measureExpression>
				</measure>
				<measure name="CHUCDANHMeasure" class="java.lang.String">
					<measureExpression><![CDATA[$F{CHUCDANH}]]></measureExpression>
				</measure>
				<crosstabCell width="106" height="140">
					<cellContents>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="77" width="103" height="20" uuid="0a1290ff-cde4-4417-886c-383653c7914c"/>
							<box leftPadding="2" rightPadding="2"/>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="10" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{HOTENMeasure}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToTallestObject" x="0" y="2" width="103" height="20" uuid="999b566d-5f37-49ee-9399-a50c47bbcf73"/>
							<box leftPadding="2" rightPadding="2"/>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="10" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{CHUCDANHMeasure}]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="24" width="103" height="21" uuid="347ff170-c6c0-4694-b3f9-228821352b4c">
								<printWhenExpression><![CDATA[$V{CHUCDANHMeasure} != null]]></printWhenExpression>
							</reportElement>
							<textElement verticalAlignment="Top">
								<font fontName="Times New Roman" size="10"/>
							</textElement>
							<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
						</staticText>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="NHAPXUATID">
					<cellContents backcolor="#BFE1FF" mode="Opaque"/>
				</crosstabCell>
				<crosstabCell width="50" columnTotalGroup="STT">
					<cellContents backcolor="#BFE1FF" mode="Opaque"/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="NHAPXUATID" columnTotalGroup="STT">
					<cellContents backcolor="#BFE1FF" mode="Opaque"/>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>
