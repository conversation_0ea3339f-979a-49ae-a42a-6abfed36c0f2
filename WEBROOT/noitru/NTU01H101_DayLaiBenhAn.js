function dayBA(_opts) {

    var _gridId = "gridPhieu";
    var _gridHeader =
        "HOSOBENHANID,HOSOBENHANID,50,0,t,l,1,2;" +
        "TIEPNHANID,TIEPNHANID,50,0,t,l,1,2;" +
        "KHAMBENHID,KHAMBENHID,50,0,t,l,1,2;" +
        "MAUBENHPHAMID,MAUBENHPHAMID,50,0,t,l,1,2;" +
        "PARAM_HASHED,PARAM_HASHED,50,0,t,l,1,2;" +
        "KEY1,KEY1,50,0,t,l,1,2;" +
        "KEY2,KEY2,50,0,t,l,1,2;" +
        "KEY3,KEY3,50,0,t,l,1,2;" +
        "KEY4,KEY4,50,0,t,l,1,2;" +
        "KEY5,KEY5,50,0,t,l,1,2;" +
        "TYPE,TYPE,50,0,t,l,1,2;" +
        "RPT_CODE,RPT_CODE,50,0,t,l,1,2;" +
        "LOAIPHIEU,LOAIPHIEU,100,0,t,l,1,2;" +
        "FLAG_CA,FLAG_CA,50,0,t,l,1,2;" +
        "LOAINHOMMAUBENHPHAM,LOAINHOMMAUBENHPHAM,50,0,t,l,1,2;" +
        " ,ICON,15,0,ns,l;" +
        "Số phiếu,SOPHIEU,50,0,f,l,1,2;" +
        "Loại phiếu,TENPHIEU,100,0,f,l,1,2;" +
        "Ngày tạo,NGAYMAUBENHPHAM,70,0,f,l,1,2;" +
        "Người tạo,NGUOITAO,80,0,f,l,1,2";

    var _group = {
        groupField: ['TENPHIEU'],
        groupColumnShow: [false],
        groupText: ['<input type="checkbox" class="groupHeader"/> &nbsp;&nbsp;<b>{0} ({1})</b>'],
        groupText: ['<b>Age {0}</b> <button class="group-action-btn" data-age="{0}">Action</button>'],
        groupCollapse: true
    };

    this.load = doLoad;
    var object_ca = {};
    var flag_ca = -1;
    var type_mbp = -1;
    var rptcode = -1;
    var dtbnid = -1;
    var hopital = -1;
    var usingReportCode = -1;
    var loainhommbp = -1;
    var signtype = -1;
    var isPrint = false;
    var paramHashed = '';
    var tenphieu = '';
    var ris_dicom_id_type = '';
    var notPrint = false;
    var cf = new Object();
    var checkSSKXBA;
    const notification = new NotificationQueue({});

    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "OPT_HOSPITAL_ID;CDDV_USING_REPORT_CODE;NGT_SUABENHAN;RIS_DICOM_ID_TYPE" +
            ";HIS_DAYBENHAN_MULTISELECT;HIS_QLBA_NOTPRINT;HIS_QLBA_NOTKYCA;HIS_QLBA_HIDE_PHIEUCHUAKY");
        if (data_ar != null && data_ar.length > 0) {
            cf = data_ar[0];
            hopital = data_ar[0].OPT_HOSPITAL_ID;
            usingReportCode = data_ar[0].CDDV_USING_REPORT_CODE;
            NGT_SUABENHAN = data_ar[0].NGT_SUABENHAN;
            ris_dicom_id_type = data_ar[0].RIS_DICOM_ID_TYPE;
            if (cf.HIS_QLBA_NOTPRINT == '1') {
                notPrint = true;
            }
        }

        if (typeof _opts.hosobenhanid == 'undefined') {
            $("#div_mabenhan1").hide();
            $("#div_mabenhan2").show();
        }
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H101.01", _opts.hosobenhanid);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            $("#lblMABENHAN").html(row.MABENHAN);
            $("#lblTENBENHNHAN").html(row.TENBENHNHAN);
            $("#txtMABENHAN").val(row.MABENHAN);
            checkSSKXBA = jsonrpc.AjaxJson.ajaxCALL_SP_I("TRM.IBS.01", row.MABENHAN + "$");
            if (cf.HIS_QLBA_NOTKYCA == '1' && row.TRANGTHAITIEPNHAN != '0') {
                $("#btnKyCa").hide();
                $("#btnHuyCa").hide();
            }
        }

        if(cf.HIS_DAYBENHAN_MULTISELECT == '1') {
            GridUtil.initGroup(_gridId, "100%", "480", "Danh sách phiếu", true, _group, _gridHeader, true, {
                rowNum: 1000,
                rowList: [1000, 2000, 3000]
            });
        } else {
            GridUtil.initGroup(_gridId, "100%", "480", "Danh sách phiếu", false, _group, _gridHeader, true, {
                rowNum: 1000,
                rowList: [1000, 2000, 3000]
            });
        }


        loadGridData();
        addHidden(_opts.khambenhid);
        loadRISConfig();
        //view decom
        if (studyInstanceUID != "" && RIS_SERVICE_DOMAIN_NAME != "" && RIS_GET_DICOM_VIEWER != "") {
            $("#btnDicomViewer").show();
        } else {
            $("#btnDicomViewer").hide();
        }
    }

    function _bindEvent() {
        GridUtil.setGridParam(_gridId, {
            onSelectRow: function (id) {
                GridUtil.unmarkAll(_gridId);
                GridUtil.markRow(_gridId, id);
                if (id) {
                    var _row = $("#" + _gridId).jqGrid('getRowData', id);
                    object_ca["HOSOBENHANID"] = _row.HOSOBENHANID;
                    object_ca["RPT_CODE"] = _row.RPT_CODE;
                    object_ca["KEY1"] = _row.KEY1;
                    object_ca["KEY2"] = _row.KEY2;
                    object_ca["KEY3"] = _row.KEY3;
                    object_ca["KEY4"] = _row.KEY4;
                    object_ca["KEY5"] = _row.KEY5;
                    type_mbp = _row.TYPE;
                    flag_ca = _row.FLAG_CA;
                    rptcode = _row.RPT_CODE;
                    loainhommbp = _row.LOAINHOMMAUBENHPHAM;
                    paramHashed = _row.PARAM_HASHED;
                    tenphieu = _row.TENPHIEU;
                    let _sophieu = _row.SOPHIEU;

                    $("#hidMAUBENHPHAMID").val(_row.MAUBENHPHAMID);

                    // Phieu them moi
                    $("#fileContainer").html("");
                    $("#fileContainer").hide();
                    if (loainhommbp == 300) {
                        $("#ghichu").show();
                        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H101.03", object_ca["KEY1"]);
                        if (data_ar.length > 0) {
                            $("#fileContainer").show();
                            $("#lblGHICHU").val(data_ar[0]["GHICHU"]);
                        } else {
                            $("#fileContainer").hide();
                        }
                        var html = "";
                        data_ar.forEach(function (el) {
                            html = html + '<div class="col-md-5 low-padding text-center">' +
                                '<button onclick="viewFile(this)" type="button"' +
                                '   data-name="' + el.FILE_NAME + '"' +
                                '   data-key="' + object_ca["KEY1"] + '"' +
                                '   data-rptcode="' + rptcode + '"' +
                                '   data-flag-ca="' + flag_ca + '"' +
                                '   data-media-id="' + el.MEDIA_ID + '"' +
                                '   data-sophieu="' + el.SOPHIEU + '"' +
                                '   class="mgr5 viewFile btn btn-sm btn-primary">\n' +
                                '<span class="glyphicon glyphicon-eye-open"></span> Xem</button>';
                            html = html + '<button onclick="deleteFile(this)" type="button" data-key="' + object_ca["KEY1"] + '" data-media-id="' + el.MEDIA_ID + '" ' + '" data-sophieu="' + el.SOPHIEU + '" ' +
                                'class="mgr5 deleteFile btn btn-sm btn-danger">\n' +
                                '<span class="glyphicon glyphicon-remove-circle"></span> Xóa</button>';
                            // html = html + '<button onclick="downloadFile(this)" type="button" data-name="' + el.FILE_NAME + '" data-media-id="' + el.MEDIA_ID + '" ' +
                            //     'class="mgr5 downloadFile btn btn-sm btn-primary">\n' +
                            //     '<span class="glyphicon glyphicon-cloud-download"></span> Tải</button>';
                            html = html + `<button 
                                   type="button" 
                                   data-key="${object_ca["KEY1"]}" 
                                   data-rptcode="${rptcode}" 
                                   data-tenphieu="${tenphieu}"
                                   data-sophieu="${_sophieu}"
                                   data-signtype="${(flag_ca == "1") ? "0" : "1"}"
                                   data-media-id="${el.MEDIA_ID}" 
                                   class="mgr5 signFile btn btn-sm btn-${flag_ca == "1" ? "warning" : "primary"}" 
                                ><img style="height: 15px;" src="../common/image/signicon-white.png" /> ${flag_ca == "1" ? "Hủy ký PDF" : "Ký PDF"}</button></div>`;
                            html = html + '<div class="col-md-7 low-padding"><p style="font-size: 12px; color: red;">' + el.FILE_NAME + ' (Người đính kém: ' + el.FULL_NAME + ' - ' + el.NGAYCAPNHAT + ')' + '</p></div>';
                            html = html + '<hr>'
                        });
                        $("#fileContainer").html(html);
                        // $("#btnViewKySo").attr("disabled", "disabled");
                        $("#btnInKySo").attr("disabled", "disabled");
                        viewFile(data_ar[0], {
                            key: object_ca["KEY1"],
                            rptcode: rptcode,
                            flagCa: flag_ca
                        });
                        return;
                    } else if (loainhommbp == 500) {
                        $("#ghichu").show();
                        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H101.032", object_ca["KEY1"]);
                        if (data_ar.length > 0) {
                            $("#fileContainer").show();
                            $("#lblGHICHU").val(data_ar[0]["GHICHU"]);
                        } else {
                            $("#fileContainer").hide();
                        }
                        var html = "";
                        data_ar.forEach(function (el) {
                            html = html + '<div class="col-md-2 low-padding text-center">' +
                                '<button onclick="viewFile500(this)" type="button"' +
                                '   data-key="' + el.DUONGDANFILE + '"' +
                                '   data-rptcode="' + rptcode + '"' +
                                '   class="mgr5 viewFile btn btn-sm btn-primary">\n' +
                                '<span class="glyphicon glyphicon-eye-open"></span> Xem </button></div>';
                            html = html + '<div class="col-md-10 low-padding"><p style="font-size: 12px; color: red;">' + el.MOTA + ' (Người đính kém: ' + el.FULL_NAME + ' - ' + el.NGAYTAO + ')' + '</p></div>';
                            html = html + '<hr>'
                        });
                        $("#fileContainer").html(html);
                        viewFile500(data_ar[0], {
                            key: object_ca["KEY1"],
                            rptcode: rptcode
                        });
                        return;
                    } else {
                        // $("#btnViewKySo").attr("disabled", false);
                        $("#btnInKySo").attr("disabled", false);
                        $("#ghichu").hide();
                    }
                    kySo(2);

                    if (flag_ca == '1') {
                        $(".deleteFile").attr("disabled", "disabled");
                    }
                }
            },

            gridComplete: function () {
                var rowids = $("#gridPhieu").getDataIDs();
                if (rowids && rowids.length > 0) {
                    rowids.forEach(function (rowid) {
                        var row = $("#gridPhieu").jqGrid('getRowData', rowid);
                        var _icon = '';
                        if (row.FLAG_CA && row.FLAG_CA == '1') {
                            _icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
                        } else if (row.FLAG_CA && row.FLAG_CA == '99') {
                            _icon = '<center><img src="../common/image/ca-3.png" width="15px"></center>';
                        }
                        $("#gridPhieu").jqGrid('setCell', rowid, 'ICON', _icon);
                    })
                }
                // let tongSoPhieu = rowids.length;
                // let phieuDaKy = rowids.filter(function (rowid) {
                //     var row = $("#gridPhieu").jqGrid('getRowData', rowid);
                //     return row.FLAG_CA == '1';
                // }).length;
                // let tongNhomPhieu = new Set(rowids.map(rowId => $("#gridPhieu").jqGrid('getRowData', rowId).TENPHIEU)).size;
                //
                // $("#lblTongSoPhieu").text(tongSoPhieu + "/" + phieuDaKy + "/" + tongNhomPhieu);
                var fl_ca_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D021.EV002", _opts.hosobenhanid);
                if (fl_ca_ar != null && fl_ca_ar.length > 0) {
                    $("#lblTongSoPhieu").text(fl_ca_ar[0].TONGPHIEU + "/" + fl_ca_ar[0].DAKY + "/" + fl_ca_ar[0].LOAIPHIEU);
                }

                $(".jqgrow", "#" + _gridId).contextMenu('contextMenu', {
                    bindings: {
                        'addPhieu': function () {
                            _addPhieu('0');
                        }, 'updatePhieu': function () {
                            _updatePhieu();
                        }, 'deletePhieu': function () {
                            _deletePhieu();
                        }, 'printPhieuALL': function () {
                            _exportAllKhoa();
                        }
                        //Beg_HaNv_151122: thêm context tạo phiểu chỉ định DV
                        , 'chidinhDV': function () {
                            _chidinhDV();
                        }
                        //End_HaNv_151122
                        , 'kyCAKQ': function () {
                            _kyCAKQ();
                        }
                    },
                    onContextMenu: function (event) {
                        var rowId = $(event.target).parent("tr").attr("id");
                        var grid = $("#" + _gridId);
                        grid.setSelection(rowId);
                        GridUtil.unmarkAll(_gridId);
                        GridUtil.markRow(_gridId, rowId);
                        return true;
                    }
                });
            }
        });

        $("#btnKyCa").click(function () {
            isPrint = true;
            signtype = '1';
            kySo(0);
        });

        $("#btnHuyCa").click(function () {
            signtype = '2';
            kySo(0);
        });

        $("#btnInKySo").click(function () {
            signtype = '0';
            kySo(1);
        });

        $("#btnThemPhieu").click(function () {
            var myVar = {};
            myVar.hosobenhanid = _opts.hosobenhanid;
            dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../noitru/NTU01H102_ThemPhieuKySo", myVar, "Thông tin phiếu", 1000, 600);
            DlgUtil.open("divDlgThemPhieu");
        });

        EventUtil.setEvent("themPhieuThanhCong", function (e) {


            DlgUtil.close("divDlgThemPhieu");
            DlgUtil.showMsg("Thêm thành công !");
            $("#gridPhieu").trigger("reloadGrid");
        });

        $("#viewIframe").on("load", function () {
            $("#viewIframe").contents().find("img").css("width", "100%");
            $("#loading").addClass("hide");
            $("#loading").removeClass("show");
        });

        $("#btnHanhChinh").on("click", function () {
            if (NGT_SUABENHAN != '1') {
                if ($("#hidTRANGTHAITIEPNHAN").val() == '2') {
                    DlgUtil.showMsg('Vui lòng hủy duyệt bảo hiểm để cập nhật thông tin hành chính cho bệnh nhân');
                    return;
                }
            }
            var paramInput = {
                tiepnhanid: $("#hidTIEPNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgSuaBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân", 1100, 580);
            DlgUtil.open("divDlgSuaBenhNhan");
        });

        $("#btnThem").on("click", function () {
            _addPhieu('1');
        });

        $("#btnSua").on("click", function () {
            _updatePhieu();
        });

        $("#btnIn").on("click", function () {
            if (rptcode == '-1') {
                DlgUtil.showMsg("Chưa chọn phiếu để in!");
                return;
            }
            var _params = getParamFromRptCode(rptcode);
            var objInPhieuChuaKy = [];
            _params.forEach(function (el) {
                objInPhieuChuaKy.push(el);
                objInPhieuChuaKy.push({
                    name: el.name.toLowerCase(),
                    type: "String",
                    value: el.value
                })
            });
            var par_data = JSON.stringify(objInPhieuChuaKy);
            var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
            par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
            var uuid = jsonrpc.AjaxJson.getUUID();
            var _url = "../report/DirectReport?code=" + rptcode + "&filetype=pdf&reportParam=" + par_str + "&uuid=" + uuid;
            $("#viewIframe").attr("src", _url);
        });

        $("#btnInHtm").on("click", function () {
            if (rptcode == '-1') {
                DlgUtil.showMsg("Chưa chọn phiếu để in!");
                return;
            }
            var _params = getParamFromRptCode(rptcode);
            CommonUtil.openReportEmr(_params, false);
        });

        var _col_loaduser = "HOSOBENHANID,HOSOBENHANID,0,0,t,l;Mã bệnh án,MAHOSOBENHAN,20,0,f,l;Tên bệnh nhân,TENBENHNHAN,45,0,f,l;Trạng thái,TRANGTHAI,20,0,f,l;Ngày vào viện,NGAYTIEPNHAN,15,0,f,l";
        var sql_par = [];
        sql_par.push({
            "name": "[0]",
            "value": _opts._param[4]
        });
        ComboUtil.initComboGrid("txtMABENHAN", 'NTU01H101.05', sql_par, "650px", _col_loaduser, function (event, ui) {
            $("#txtMABENHAN").val(ui.item.MAHOSOBENHAN);
            _opts.hosobenhanid = ui.item.HOSOBENHANID;
            _opts.tiepnhanid = ui.item.TIEPNHANID;
            _opts.khambenhid = ui.item.KHAMBENHID;
            _opts.user_id = _opts._param[1];
            _initControl();
            return false;
        });

        $('#btnDicomViewer').on('click', function () {
            var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '?studyInstanceUID=' + studyInstanceUID;

            if (RIS_CONNECTION_TYPE == "1" || RIS_CONNECTION_TYPE == "2" && RIS_SERVICE_DOMAIN_NAME != "") {
                console.log("request_url=" + request_url);
                $.ajax({
                    type: "GET",
                    contentType: "application/json; charset=utf-8",
                    headers: {
                        'Ris-Access-Hash': getHashRIS(studyInstanceUID),
                        'Identify-Code': studyInstanceUID
                    },
                    data: "",
                    url: request_url,
                    success: function (data) {
                        window.open(data.data);
                        console.log(JSON.stringify(data));
                    },
                    error: function (xhr) {
                        console.log("get dicom viewer fail: " + JSON.stringify(xhr));
                    }
                });
            } else if (RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8" && RIS_SERVICE_DOMAIN_NAME != "") {
                if (RIS_PROVIDER == "INFINITT")
                    request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&LID=' + RIS_USERNAME + '&LPW=' + RIS_SECRET_KEY + '&AN=' + studyInstanceUID;
                else if (RIS_PROVIDER == "VBIT")
                    request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&acc=' + $("#hdfIDDichVuKB").val() + '&mrn=' + $("#hidMAHOSOBENHAN").val();
                else if (RIS_PROVIDER == "VIETTEL")
                    request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + (ris_dicom_id_type == "1" ? toHex($("#hdfIDDichVuKB").val()) : $("#hdfIDDichVuKB").val());
                console.log("request_url=" + request_url);
                window.open(request_url, "_blank");
            }
        });

        $('#cboTRANGTHAI').on('change', function () {
            if ($('#cboTRANGTHAI').val() == '-1') {
                $("#gridPhieu").jqGrid("setGridParam", {
                    groupingView: {
                        groupField: ['TENPHIEU'],
                        groupColumnShow: [false],
                        groupText: ['<b>{0} ({1})</b>'],
                        groupCollapse: true
                    }
                });
            } else {
                $("#gridPhieu").jqGrid("setGridParam", {
                    groupingView: {
                        groupField: ['TENPHIEU'],
                        groupColumnShow: [false],
                        groupText: ['<b>{0} ({1})</b>'],
                        groupCollapse: false
                    }
                });
            }
            loadGridData();
        });

        $("#btnKetXuat").click(function (e) {
            if (checkSSKXBA == 0) {
                let emrUtils = new EmrUtils();
                let rs = emrUtils.requestRenderingTreatmentInfo($("#hidMAHOSOBENHAN").val());
                if (rs.code == 200) {
                    DlgUtil.showMsg("Đã gửi yêu cầu kết xuất thông tin điều trị !");
                    checkSSKXBA = 1;
                } else {
                    DlgUtil.showMsg("Gửi yêu cầu kết xuất lỗi (" + rs.code + " : " + rs.message + ")");
                }
            } else if (checkSSKXBA == 1) {
                DlgUtil.showMsg("Hệ thống đang xử lý, xin hãy quay lại sau giây lát.");
            } else if (checkSSKXBA == 2) {
                $("#modalKXBA").modal("show");
            }
        });

        $("#btnKXBA_PRINT").click(function (){
            $("#modalKXBA").modal("hide");
            let emrUtils = new EmrUtils();
            let rs = emrUtils.getTreatmentInfo($("#hidMAHOSOBENHAN").val());
            if (rs.code == 200) {
                let urlTreatment = rs.results;
                window.open(urlTreatment);
            } else if (rs.code == 401) {
                DlgUtil.showMsg("Dữ liệu bệnh án chưa được kết xuất!");
            } else {
                DlgUtil.showMsg("Đã có lỗi xảy ra!");
            }
        });

        $("#btnmodalKXBA_RETRY").click(function (){
            $("#modalKXBA").modal("hide");
            let emrUtils = new EmrUtils();
            let rs = emrUtils.requestRenderingTreatmentInfo($("#hidMAHOSOBENHAN").val());
            if (rs.code == 200) {
                DlgUtil.showMsg("Đã gửi yêu cầu kết xuất thông tin điều trị !");
            } else {
                DlgUtil.showMsg("Gửi yêu cầu kết xuất lỗi (" + rs.code + " : " + rs.message + ")");
            }
        });
        $("#btnXuatXML").click(function () {
            if (flag_ca == '1' || flag_ca == '99') {
                var objxml = new Object();
                objxml.TYPE = type_mbp;
                objxml.PARAM_HASHED = paramHashed;
                objxml.MAUBENHPHAMID = $("#hidMAUBENHPHAMID").val();
                var data_xml = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H101.06", JSON.stringify(objxml));
                UploadUtil.getUrlDownloadFromIDGByKey('caid', data_xml[0].CAID,
                    function (result) {
                        var blob = new Blob([result], {type: 'text/xml'});
                        var urlxx = URL.createObjectURL(blob);
                        window.open(urlxx);
                        URL.revokeObjectURL(urlxx);
                    }, function (msg) {
                        DlgUtil.showMsg("Lỗi dowload file <br>" + msg)
                    });
            }
        });

        $("#printSelectedKyCA").click(function () {
            _inGopCa('0');
        });

        $("#printAllKyCA").click(function () {
            _inGopCa('1');
        });

        $("#fileContainer").on("click", ".signFile", function (e) {
            let element = e.currentTarget;
            let _mediaId = element.dataset.mediaId;
            let _phieuid = element.dataset.key;
            let _rptCodeSigning = element.dataset.rptcode;
            let _signType = element.dataset.signtype;
            let _tenphieu = element.dataset.tenphieu;
            let _sophieu = element.dataset.sophieu;
            let caUser = "";
            let caPwd = "";
            let smartcauser = "";
            let ttpKys = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCodeSigning);
            if (ttpKys != null && ttpKys.length > 0) {
                let pdfCaType = jsonrpc.AjaxJson.getFirstRowO('CA.EXGTYPE', [{
                    "name": "[0]",
                    value: type_mbp + ""
                }]);

                let catype = pdfCaType.length > 0 ? pdfCaType[0].CA_TYPE : ttpKys[0].CA_TYPE;
                let kieuKy = pdfCaType.length > 0 ? pdfCaType[0].CA_NAME : ttpKys[0].KIEUKY;
                let smartca_method = 0;
                EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                    showLoadingGlobal();
                    caUser = e.username;
                    caPwd = e.password;
                    smartcauser = e.smartcauser;
                    DlgUtil.close("divCALOGIN");
                    $.ajax({
                        url: "/vnpthis/CaApiPdf",
                        type: "POST",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify({
                            PHIEUID: _phieuid,
                            USERCA: caUser,
                            PWDCA: caPwd,
                            MEDIA_ID: _mediaId,
                            SIGN_TYPE: _signType,
                            CA_TYPE: catype,
                            SMARTCA_USER: smartcauser
                        }),
                        success: function (respObj) {
                            if (respObj.constructor.name === "String") {
                                respObj = JSON.parse(respObj);
                            }
                            let code = respObj.code;
                            let actionMsg = _signType == 1 ? "Ký" : "Hủy ký"
                            if (code > 0) {
                                notification.success({
                                    title: `${actionMsg} thành công.`,
                                    message: `${respObj.message} phiếu ${_tenphieu}: ${_sophieu}`
                                });
                                if (code > 10) {
                                    element.innerHTML = '<img style="height: 15px;" src="../common/image/signicon-white.png"> Ký PDF';
                                    element.classList.remove("btn-warning");
                                    element.setAttribute("data-signtype", "1");
                                    element.parentElement.firstChild.setAttribute("data-flag-ca", "0");
                                    element.classList.add("btn-primary");
                                    viewFile({
                                        MEDIA_ID: _mediaId
                                    }, {
                                        key: _phieuid,
                                        rptcode: _rptCodeSigning,
                                        flagCa: 0
                                    });
                                } else {
                                    element.innerHTML = '<img style="height: 15px;" src="../common/image/signicon-white.png"> Hủy ký PDF'
                                    element.classList.remove("btn-primary");
                                    element.setAttribute("data-signtype", "0");
                                    element.parentElement.firstChild.setAttribute("data-flag-ca", "1");
                                    element.classList.add("btn-warning");
                                    viewFile({
                                        MEDIA_ID: _mediaId
                                    }, {
                                        key: _phieuid,
                                        rptcode: _rptCodeSigning,
                                        flagCa: 1
                                    });
                                }
                                $("#gridPhieu").trigger("reloadGrid");
                            } else {
                                notification.error({
                                    title: `${actionMsg} thất bại!`,
                                    message: `${respObj.message} phiếu ${_tenphieu}: ${_sophieu}`
                                });
                            }
                            hideLoadingGlobal();
                        },
                        error: function (msg) {
                            hideLoadingGlobal();
                            alert("In phiếu thất bại!");
                        }
                    });
                });
                EventUtil.setEvent("dlgCaLogin_close", function (e) {
                    DlgUtil.close("divCALOGIN");
                    hideLoadingGlobal();
                });
                if (catype == '3' || catype == '6' || catype == '7') {
                    smartca_method = '1';
                }
                let popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg",
                    "manager.jsp?func=../danhmuc/CA_LOGIN_FORM",
                    {
                        ca_type: catype,
                        sign_type: _signType,
                        rpt_code: _rptCodeSigning,
                        smartca_method : smartca_method
                    },
                    kieuKy,
                    505, 268);
                popup.open("divCALOGIN");
            } else {
                alertify.error("Phiếu chưa cấu hình ký số!", 10000);
            }
        })
    }

    function loadGridData() {
        if($('#cboTRANGTHAI').val() == '3') {
            var objData = {};
            objData.HOSOBENHANID = _opts.hosobenhanid;
            objData.TIEPNHANID = _opts.tiepnhanid;
            objData.TRANGTHAI = $('#cboTRANGTHAI').val();
            var _sql_par = [{"name": "[0]", "value": JSON.stringify(objData)}];

            GridUtil.loadGridBySqlPage(_gridId, "NTU01H101.08", _sql_par);
        } else {
            var objData = {};
            objData.HOSOBENHANID = _opts.hosobenhanid;
            objData.TIEPNHANID = _opts.tiepnhanid;
            objData.TRANGTHAI = $('#cboTRANGTHAI').val();
            var _sql_par = [{"name": "[0]", "value": JSON.stringify(objData)}];
            GridUtil.loadGridBySqlPage(_gridId, "NTU01H101.02", _sql_par, function () {
            });
        }
    }

    function kySo(_mode) {
        var usingReportCode = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'CDDV_USING_REPORT_CODE');
        if (loainhommbp == 1 || loainhommbp == 2 || loainhommbp == 5) {
            if (type_mbp == 1) { // Phieu chi dinh
                if (hopital == 965) {
                    if (dtbnid == 1) {
                        var _loaidichvu = 0;
                        var _par_loai = [object_ca["KEY1"]];
                        var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loai.join('$'));
                        if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
                            for (var i = 0; i < arr_loaidichvu.length; i++) {
                                _loaidichvu = arr_loaidichvu[i].BHYT;
                                if (_loaidichvu == 1) {
                                    if (loainhommbp == 1) {
                                        object_ca["RPT_CODE"] = 'PHIEU_XETNGHIEM_A4_965';
                                    } else if (loainhommbp == 2) {
                                        object_ca["RPT_CODE"] = 'PHIEU_CDHA_A4_965';
                                    } else {
                                        object_ca["RPT_CODE"] = 'PHIEU_PTTT_A4_965';
                                    }
                                } else {
                                    if (loainhommbp == 1) {
                                        object_ca["RPT_CODE"] = 'PHIEU_XETNGHIEMDICHVU_A4_965';
                                    } else if (loainhommbp == 2) {
                                        object_ca["RPT_CODE"] = 'PHIEU_CDHADICHVU_A4_965';
                                    } else {
                                        object_ca["RPT_CODE"] = 'PHIEU_PTTTDICHVU_A4_965';
                                    }
                                }
                                var oparam = getParamFromRptCode(object_ca["RPT_CODE"]);
                                _kyCaRpt(oparam, _mode);
                            }
                        }
                    } else {
                        if (loainhommbp == 1) {
                            object_ca["RPT_CODE"] = 'PHIEU_XETNGHIEMDICHVU_A4_965';
                        } else if (loainhommbp == 2) {
                            object_ca["RPT_CODE"] = 'PHIEU_CDHADICHVU_A4_965';
                        } else {
                            object_ca["RPT_CODE"] = 'PHIEU_PTTTDICHVU_A4_965';
                        }
                        var oparam = getParamFromRptCode(object_ca["RPT_CODE"]);
                        _kyCaRpt(oparam, _mode);
                    }
                } else if (usingReportCode == 1) {
                    var _par_code = [object_ca["KEY1"]];
                    var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
                    if (i_report_code != null && i_report_code.length > 0) {
                        for (var i = 0; i < i_report_code.length; i++) {
                            var _report_code = i_report_code[i].REPORT_CODE;
                            object_ca["RPT_CODE"] = _report_code;
                            var oparam = getParamFromRptCode(object_ca["RPT_CODE"]);
                            if (loainhommbp == 2) {
                                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", _par_code.join('$'));
                                for (var j = 0; j < data_ar.length; j++) {
                                    var hinh_anh = data_ar[j]["DUONGDANFILE"];
                                    if (hinh_anh != "" && hinh_anh != null)
                                        oparam.push({
                                            name: 'hinh_anh' + j,
                                            type: 'Image',
                                            value: hinh_anh
                                        });
                                    var ten_file = data_ar[j]["TENFILE"];
                                    if (ten_file != "" && ten_file != null)
                                        oparam.push({
                                            name: 'ten_file' + j,
                                            type: 'String',
                                            value: ten_file
                                        });
                                }
                            }
                            _kyCaRpt(oparam, _mode);
                        }
                    } else {
                        if (loainhommbp == 1) {
                            object_ca["RPT_CODE"] = 'PHIEU_XETNGHIEM_CHUNG';
                        }
                        if (loainhommbp == 2) {
                            object_ca["RPT_CODE"] = 'PHIEU_CDHA_A4';
                        }
                        if (loainhommbp == 5) {
                            object_ca["RPT_CODE"] = 'PHIEU_PHAUTHUAT_A4';
                        }
                        var oparam = getParamFromRptCode(object_ca["RPT_CODE"]);
                        _kyCaRpt(oparam, _mode);
                    }
                } else {
                    var oparam = getParamFromRptCode(rptcode);
                    _kyCaRpt(oparam, _mode)
                }
            } else {
                // xu ly phieu ket qua
                if (_mode == 1) {
                    CommonUtil.openReportGetCA3(paramHashed, false);
                } else {
                    //chưa cky va hss
                    $.ajax({
                        url: "/vnpthis/carptrender?paramhashed=" + paramHashed + "&types=HTML;PDF",
                        type: "GET",
                        contentType: "application/json; charset=utf-8",
                        success: function (result) {
                            $("#viewIframe").attr("src", "");
                            setTimeout(function () {
                                var linkOHRenders = JSON.parse(result);
                                $("#viewIframe")[0].src = linkOHRenders[0];
                            }, 200);
                        },
                        error: function () {
                            alert("View phiếu thất bại");
                        }
                    });
                }
            }
        } else {
            var oparam = getParamFromRptCode(rptcode);
            if (oparam == '1' && (flag_ca == '1' || flag_ca == '99')) {
                //view theo paramHashed
                $.ajax({
                    url: "/vnpthis/carptrender?paramhashed=" + paramHashed + "&types=HTML;PDF",
                    type: "GET",
                    contentType: "application/json; charset=utf-8",
                    success: function (result) {
                        $("#viewIframe").attr("src", "");
                        setTimeout(function () {
                            var linkOHRenders = JSON.parse(result);
                            $("#viewIframe")[0].src = linkOHRenders[0];
                        }, 200);
                    },
                    error: function () {
                        alert("View phiếu thất bại");
                    }
                });
            } else {
                if ((rptcode == 'NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE' ||
                    rptcode == 'NTU028_PHIEUCHAMSOC_09BV01_QD4069_A4' ||
                    rptcode == 'NTU033_PHIEUTHEODOITRUYENDICH_17BV01_QD4069_A4') && _mode == '2') {
                    _inGopCa('2');
                } else {
                    _kyCaRpt(oparam, _mode);
                }
            }
        }
    }

    function _kyCaRpt(_params, _mode) {
        if (_mode == 1) {
            CommonUtil.openReportGetCA2(_params, false);
        } else if (_mode == 2) {
            var request = {
                params: _params
            };

            // CA.GET.LOAIPHIEU
            $("#loading").addClass("show");
            $("#loading").removeClass("hide");

            //phiếu đã ký or phiếu in EMR
            var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H101.07", rptcode);
            if (flag_ca == '1' || flag_ca == '99') {
                if (loainhommbp == 300) {
                    let paramHashed = CryptoJS.MD5(JSON.stringify(_params).toUpperCase()).toString().toUpperCase();
                    let _sql_par = [];
                    _sql_par.push({
                        "name": "[0]",
                        value: paramHashed
                    });
                    let objectName = jsonrpc.AjaxJson.getOneValue('EMR.GET.OBJECTNAME', _sql_par);
                    $("#viewIframe").attr("src", "../upload/getemrdata.jsp?objectname=" + objectName);
                } else {
                    var request = {
                        params: [_params],
                        types: "HTML;PDF"
                    };

                    $.ajax({
                        url: "/vnpthis/carptrender",
                        type: "POST",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(request),
                        success: function (respObj) {
                            try {
                                let linkOHRenders = JSON.parse(respObj);
                                $("#viewIframe").attr("src", "");
                                if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                    setTimeout(function () {
                                        $("#viewIframe")[0].src = linkOHRenders[0];
                                    }, 200);
                                }
                            } catch (e) {
                                try {
                                    if (respObj.code == "0") {
                                        let linkOHRenders = respObj.result;
                                        $("#viewIframe").attr("src", "");
                                        if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                            setTimeout(function () {
                                                $("#viewIframe")[0].src = linkOHRenders[0];
                                            }, 200);
                                        }
                                    } else {
                                        DlgUtil.showError(`${respObj.message} (${respObj.code})`, 10000);
                                    }
                                } catch (e2) {
                                    alert("Đã có lỗi xảy ra");
                                }
                            }
                        },
                        error: function (msg) {
                            alert("View phiếu thất bại");
                        }
                    });
                }
            } else if(check == '1') {
                request = {
                    params: _params,
                    merge: "0",
                    type: "3" // 1 = đã ký, 2 = gộp đã ký, 3 = chưa ký
                };
                $.ajax({
                    url: "/vnpthis/rptrender",
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(request),
                    success: function (respObj) {
                        if (respObj.code == "0") {
                            let linkOHRenders = respObj.result;
                            $("#viewIframe").attr("src", "");
                            if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                setTimeout(function () {
                                    $("#viewIframe")[0].src = linkOHRenders[0];
                                }, 200);
                            }
                        } else {
                            DlgUtil.showError(`${respObj.message} (${respObj.code})`, 10000);
                        }
                    },
                    error: function (msg) {
                        alert("View phiếu thất bại");
                    }
                });
            } else {
                if (loainhommbp == '300') {
                    $(".viewFile:first").click();
                } else {
                    var objInPhieuChuaKy = [];
                    _params.forEach(function (el) {
                        objInPhieuChuaKy.push(el);
                        objInPhieuChuaKy.push({
                            name: el.name.toLowerCase(),
                            type: "String",
                            value: el.value
                        })
                    });
                    var par_data = JSON.stringify(objInPhieuChuaKy);
                    var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
                    par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
                    var uuid = jsonrpc.AjaxJson.getUUID();
                    var _url = "../report/DirectReport?code=" + rptcode + "&filetype=pdf&reportParam=" + par_str + "&uuid=" + uuid;
                    $("#viewIframe").attr("src", _url);
                }
            }

        } else if (_mode == 4) {
            var msg = CommonUtil.sendEmr(_params);
            DlgUtil.showMsg(msg);
        } else {
            if (loainhommbp == 300 || notPrint) {
                isPrint = false;
            } else {
                isPrint = true;
            }
            CommonUtil.kyCA(_params, signtype, isPrint);
            EventUtil.setEvent("eventKyCA", function (e) {
                isPrint = false;
                signtype = '-1';
                DlgUtil.showMsg(e.res);
                $("#gridPhieu").trigger("reloadGrid");
            });
        }
    }

    function getParamFromRptCode(rpt_code) {
        var paramSelect = '';
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rpt_code);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            if (row.SQL_PARAM == '') {
                return 1;
            }
            paramSelect = JSON.parse(row.SQL_PARAM);
        }
        var oparam = [];
        paramSelect.forEach(function (el) {
            var name = el.name;
            var patt = /(?<=\[)(((?!\]).)*)/;
            var key = el.value.match(patt)[0];
            var value = el.value.replace("[" + key + "]", object_ca[key]);
            oparam.push({
                name: name,
                type: "String",
                value: value
            })
        });
        return oparam;
    }

    function getParamInput(rpt_code) {
        var paramSelect = '';
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rpt_code);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            paramSelect = JSON.parse(row.INPUT_PARAM);
        }
        var obj = new Object();
        paramSelect.forEach(function (el) {
            var name = el.name;
            var value = $('#' + el.value + '').val();
            obj[name] = value;
        });
        return obj;
    }

    function getUrlInput(rpt_code) {
        var paramSelect = '';
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rpt_code);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            paramSelect = JSON.parse(row.URL);
        }
        var obj = new Object();
        paramSelect.forEach(function (el) {
            var name = el.name;
            var value = el.value;
            obj[name] = value;
        });
        return obj;
    }

    function addHidden(khambenhid) {
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.055", khambenhid);
        if (data_ar != null && data_ar.length > 0) {
            var data = data_ar[0];
            //cap nhat thong tin cho cac input hidden
            $("#hidMAHOSOBENHAN").val(data.MAHOSOBENHAN);
            $("#hidKHAMBENHID").val(_opts.khambenhid);
            $("#hidBENHNHANID").val(data.BENHNHANID);
            $("#hidTIEPNHANID").val(_opts.tiepnhanid);
            $("#hidHOSOBENHANID").val(_opts.hosobenhanid);
            $("#hidDOITUONGBENHNHANID").val(data.DOITUONGBENHNHANID);
            $("#hidMABENHNHAN").val(data.MABENHNHAN);
            $("#hidHINHTHUCVAOVIENID").val(data.HINHTHUCVAOVIENID);
            $("#hidHINHTHUCRAVIENID").val(data.HINHTHUCRAVIENID);
            $("#hidLOAITIEPNHANID").val(data.LOAITIEPNHANID);
            $("#hidLOAIBENHANID").val(data.LOAIBENHANID);
            $("#hidTRANGTHAITIEPNHAN").val(data.TRANGTHAITIEPNHAN);
            $("#hidPHONGID").val(data.PHONGID);
            $("#hidUserID").val(_opts.user_id);
            $("#hidBACSIID").val(data.BACSYDIEUTRIID);
            $("#hidKETQUADIEUTRIID").val(data.KETQUADIEUTRIID);
            $("#hidTHOIGIANVAOVIEN").val(data.THOIGIANVAOVIEN);
            $("#hidTHOIGIANRAVIEN").val(data.THOIGIANRAVIEN);
            $("#hidNGAYTIEPNHAN").val(data.NGAYTIEPNHAN);
            $("#hidNAMSINH").val(data.NAMSINH);
            $("#hidKHOA").val(data.KHOA);

        }
    }

    function _addPhieu(type) {
        $("#hidMAUBENHPHAMID").val('');
        if (type == '0') {
            var paramInput = getParamInput(rptcode);
            var urlInput = getUrlInput(rptcode);
            dlgPopup = DlgUtil.buildPopupUrl(urlInput.divDlg, "divDlg", urlInput.url, paramInput, urlInput.title, urlInput.width, urlInput.height);
            DlgUtil.open(urlInput.divDlg);
        } else {
            var paramInput = {
                rptcode: rptcode,
                tenphieu: tenphieu
            };
            EventUtil.setEvent("ChangeCaRpt", function (e) {
                var paramInput = getParamInput(e.res);
                var urlInput = getUrlInput(e.res);
                dlgPopup = DlgUtil.buildPopupUrl(urlInput.divDlg, "divDlg", urlInput.url, paramInput, urlInput.title, urlInput.width, urlInput.height);
                DlgUtil.open(urlInput.divDlg);
            });
            dlgPopup = DlgUtil.buildPopupUrl("divChonPhieu", "divDlg", "manager.jsp?func=../noitru/NTU01H106_ChonPhieu", paramInput, "Chọn phiếu", 750, 250);
            DlgUtil.open("divChonPhieu");
        }

    }

    function _updatePhieu() {
        var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
        if (selRowId != null && selRowId != '') {
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            flag_ca = rowData.FLAG_CA;
            rptcode = rowData.RPT_CODE;
            loainhommbp = rowData.LOAINHOMMAUBENHPHAM;
            paramHashed = rowData.PARAM_HASHED;
            $("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
            if (flag_ca == '1') {
                DlgUtil.showMsg("Phiếu đã được ký số không được cập nhật!");
                return;
            }

            var paramInput = getParamInput(rptcode);
            var urlInput = getUrlInput(rptcode);
            dlgPopup = DlgUtil.buildPopupUrl(urlInput.divDlg, "divDlg", urlInput.url, paramInput, urlInput.title, urlInput.width, urlInput.height);
            DlgUtil.open(urlInput.divDlg);
        } else {
            DlgUtil.showMsg("Chưa chọn phiếu để sửa!");
            return;
        }
    }

    function _deletePhieu() {
        var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
        if (selRowId != null && selRowId != '') {
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            paramHashed = rowData.PARAM_HASHED;
            DlgUtil.showConfirm("Bạn chắc chắn muốn xóa phiếu này ?", function (flag) {
                if (flag) {
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H101.09", paramHashed);
                    if (fl == 1) {
                        DlgUtil.showMsg("Xóa thành công !");
                        loadGridData();
                    } else if (fl == 2) {
                        DlgUtil.showMsg("Bạn không có quyền xóa phiếu này !");
                    } else if (fl == 3) {
                        DlgUtil.showMsg("Phiếu đã được ký số không được xóa!");
                    } else if (fl == 4) {
                        DlgUtil.showMsg("Phiếu SCAN ko xóa tại chức năng này!");
                    } else {
                        DlgUtil.showMsg("Xảy ra lỗi !");
                    }
                }
            })
        } else {
            DlgUtil.showMsg("Chưa chọn phiếu!");
            return;
        }
    }
    //Beg_HaNv_151122: thêm context tạo phiểu chỉ định DV
    function _chidinhDV() {
    	var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
        if (selRowId != null && selRowId != '') {
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            flag_ca = rowData.FLAG_CA;
            loainhommbp = rowData.LOAINHOMMAUBENHPHAM;
            $("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
            if (flag_ca == '1') {
                DlgUtil.showMsg("Phiếu đã được ký số không được cập nhật!");
                return;
            }
            if (loainhommbp != '4') {
                DlgUtil.showMsg("Chỉ tạo phiếu chỉ định cho phiếu điều trị!");
                return;
            }
            var myVar = {
    			khambenhid : $("#hidKHAMBENHID").val(),
    			benhnhanid : $("#hidBENHNHANID").val(),
    			tiepnhanid : $("#hidTIEPNHANID").val(),
    			hosobenhanid : $("#hidHOSOBENHANID").val(),
    			doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
    			loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
    			phieudieutriId : $("#hidMAUBENHPHAMID").val(),
    			subDeptId : $("#hidPHONGID").val()
    		};
    		dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlgDV", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, myVar, "Chỉ định dịch vụ", $(document).width() - 50,
    					$(window).height() - 50);
    		DlgUtil.open("divDlgDichVu");
        } else {
            DlgUtil.showMsg("Chưa chọn phiếu điều trị để tạo phiếu chỉ định!");
            return;
        }
	}
    //End_HaNv_151122

    function _inGopCa(type) {
        if (rptcode != 'NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE' &&
            rptcode != 'NTU028_PHIEUCHAMSOC_09BV01_QD4069_A4' &&
            rptcode != 'NTU033_PHIEUTHEODOITRUYENDICH_17BV01_QD4069_A4') {
            DlgUtil.showMsg("Phiếu đang chọn không hỗ trợ in ký số nhiều phiếu!");
            return;
        }
        var lstmaubenhphamid = '';
        var obj = new Object();
        obj.hosobenhanid = $("#hidHOSOBENHANID").val();
        obj.loainhommaubenhpham = loainhommbp;
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("HIS.GETMBP", JSON.stringify(obj));
        if (data_ar != null && data_ar.length > 0) {
            for (var j = 0; j < data_ar.length; j++) {
                var rowData = data_ar[j];
                if (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99') {
                    lstmaubenhphamid = lstmaubenhphamid + "," + rowData.MAUBENHPHAMID;
                }
            }
        }

        if (lstmaubenhphamid.length > 1) {
            lstmaubenhphamid = lstmaubenhphamid.substring(1, lstmaubenhphamid.length);
            var par_rpt_KySo = [ {
                name : 'HOSOBENHANID',
                type : 'String',
                value : $("#hidHOSOBENHANID").val()
            }, {
                name : 'LST_MAUBENHPHAMID',
                type : 'String',
                value : lstmaubenhphamid
            }, {
                name : 'RPT_CODE',
                type : 'String',
                value : rptcode
            } ];
            if (type == '2') {
                var request = {};
                request.params = par_rpt_KySo;
                request.merge = 0;
                request.type = "2";
                let isRenderNew = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'RPT_RENDER_NEW_2');
                let _urlView = isRenderNew == "1" ? "/vnpthis/rptrender" : "/vnpthis/viewcareport";
                $.ajax({
                    url: _urlView,
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(request),
                    success: function (result) {
                        $("#viewIframe").attr("src", "");
                        setTimeout(function () {
                            var linkOHRenders = result.result;
                            $("#viewIframe")[0].src = linkOHRenders[0];
                        }, 200);
                    },
                    error: function (msg) {
                        alert("In phiếu thất bại");
                    }
                });
            } else {
                CommonUtil.openReportGetViewCa(par_rpt_KySo, false);
            }

        } else {
            DlgUtil.showMsg("Phiếu chưa ký số!");
            return;
        }
    }

    function _exportAllKhoa () {
        var par = [ {
            name : 'benhnhanid',
            type : 'String',
            value : $("#hidBENHNHANID").val()
        }, {
            name : 'hosobenhanid',
            type : 'String',
            value : $("#hidHOSOBENHANID").val()
        } ];
        openReport('window', "NTU020_PHIEUDIEUTRI_ALL_KHOA", "pdf", par);
    }
}

function viewFile(element, data) {
    let mediaId = "";
    let _phieuid = "";
    let _rptCodeSigning = "";
    let _flagCa = "";
    if (typeof $(element).data("media-id") != 'undefined') {
        mediaId = $(element).data("media-id");
        _rptCodeSigning = $(element).data("rptcode");
        _flagCa = $(element).data("flag-ca");
        _phieuid = $(element).data("key");
    } else {
        mediaId = element.MEDIA_ID;
        _phieuid = data.key;
        _rptCodeSigning = data.rptcode;
        _flagCa = data.flagCa;
    }
    $("#loading").addClass("show");
    $("#loading").removeClass("hide");
    if (_flagCa == 1) {
        let _paramHased = CryptoJS.MD5(_rptCodeSigning + "|" + _phieuid).toString().toUpperCase();
        let objectName = jsonrpc.AjaxJson.getOneValue('EMR.GET.OBJECTNAME', [{
            "name": "[0]",
            value: _paramHased
        }]);
        $("#viewIframe").attr("src", "../upload/getemrdata.jsp?objectname=" + objectName);
    } else {
        $("#viewIframe").attr("src", "../upload/getemrdata.jsp?id=" + mediaId);
    }
}

function viewFile500(element, data) {
    let duongdan = '';
    if (typeof $(element).data("key") != 'undefined') {
        duongdan = $(element).data("key");
    } else {
        duongdan = element.DUONGDANFILE;
    }
    $("#loading").addClass("show");
    $("#loading").removeClass("hide");
    $("#viewIframe").attr("src", duongdan);
}

function downloadFile(element) {
    var mediaId = $(element).data("media-id");
    var fileName = $(element).data("name");
    var el = document.createElement('a');
    el.setAttribute('href', "../upload/getemrdata.jsp?id=" + mediaId);
    el.setAttribute('download', fileName);
    document.body.appendChild(el);
    el.click();
}

function deleteFile(element) {
    DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko?", function (flag) {
        if (flag) {
            var mediaId = $(element).data("media-id");
            UploadUtil.deleteOnIDG(mediaId,
                function () {
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H101.XOAANH", mediaId);
                    if (fl == 1) {
                        $(element).parent(".text-center").next().remove();
                        $(element).parent(".text-center").remove();
                        if ($("#fileContainer button.deleteFile").length == 0) {
                            var key = $(element).data("key");
                            var fl2 = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H101.XOAPHIEU", key);
                            if (fl2 == 1) {
                                DlgUtil.showMsg("Xóa phiếu thành công !");
                                $("#gridPhieu").trigger("reloadGrid");
                                //xóa EMR
                                var oData = {
                                    HOSOBENHANID: _opts.hosobenhanid,
                                    ID_PHIEU : key + '',
                                    SO_PHIEU : $(element).data("sophieu"),
                                    RPT_CODE : 'PHIEU_SCAN'
                                };
                                var request = $.ajax({
                                    url: '/vnpthis/apiemrdelete',
                                    type: "POST",
                                    data: JSON.stringify(oData),
                                    contentType: 'application/json; charset=utf-8',
                                    dataType: "json",
                                    async: true
                                });
                            } else {
                                DlgUtil.showMsg("Đã có lỗi xảy ra, làm ơn tải lại trang!");
                            }
                        } else {
                            DlgUtil.showMsg("Xóa file thành công !");
                        }
                    } else {
                        DlgUtil.showMsg("Đã có lỗi xảy ra, làm ơn tải lại trang!");
                    }
                }, function (msg) {
                    DlgUtil.showMsg("Đã có lỗi xảy ra: " + msg);
                }, true);
        }
    });
}

function _kyCAKQ(element) {
    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", $("#hidMAUBENHPHAMID").val() + "$");
    if (data_ar.length == '0') {
        DlgUtil.showMsg("Phiếu kết quả chưa thực hiện ký số/điện tử!");
        return;
    }
    for (var i = 0; i < data_ar.length; i++) {
        var paramHashed = data_ar[i].PARAM_HASHED;
        if (data_ar2[i].KYCHOT != '1') {
            var signUtils = new SignUtils(rptcode);
            signUtils.requestSigningClosed(
                paramHashed,
                (successResponse) => {
                    //update trang thai ky chot
                    var obj = new Object();
                    obj.PARAM_HASHED = paramHashed;
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.CA.KYCHOT", JSON.stringify(obj));
                },
                (errorResponse) => {
                    DlgUtil.showMsg("Có lỗi xẩy ra!");
                    return;
                })
        }
    }
}

function showLoadingGlobal() {
    $("#global_loading").removeClass("hide");
    $("#global_loading").addClass("show");
}

function hideLoadingGlobal() {
    setTimeout(function () {
        $("#global_loading").addClass("hide");
        $("#global_loading").removeClass("show");
    }, 100);
}