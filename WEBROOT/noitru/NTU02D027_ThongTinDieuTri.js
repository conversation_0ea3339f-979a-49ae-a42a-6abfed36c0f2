(function($) {
	var causer = -1;
	var capassword = -1;
	var smartcauser = -1;
	var msgKyca = '';
	var kyChot = false;
	var isPrintCa = true;
	var htmlDlgThoiGian = ""; // fix lỗi chọn thời gian khi chuyển tab
	var showAllDVKT = false; //L2PT-20786
	var cfObj = new Object();
	var _rptCode = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE";
	$.widget("ui.ntu02d027_dt", {
		//Options to be used as defaults
		options : {
			_grdDieuTri : 'grdDieuTri',
			_khambenhid : "",
			_benhnhanid : "",
			_lnmbp : "",
			_modeView : "0", // =1 chi view; !=1 la update
			_hosobenhanid : "",
			checkLoad : false,
			_formCall : "" // Xác định mã màn hình cha gọi đến widget để tùy biến L2PT-20786
		},
		containerId : '',
		//Setup widget (eg. element creation, apply theming
		// , bind events etc.)
		_create : function() {
			// _create will automatically run the first time this widget is called. Put the initial widget  setup code here, then you can access the element
			// on which the widget was called via this.element. The options defined above can be accessed via this.options this.element.addStuff();
			console.log('_create');
			this.containerId = $(this.element).attr('id');
			this._initWidget();
		},
		_t : function(_id) {
			var newId = '';
			if (_id.indexOf("=") > 0) {
				newId = _id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi, _self.containerId + "$1");
			} else if (_id.indexOf("#") == 0) {
				newId = _id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi, "$1" + _self.containerId + "$2");
			} else {
				newId = _self.containerId + _id;
			}
			return newId;
		},
		_initWidget : function() {
			var _self = this;
			_self.options.checkLoad = false;
			//L2PT-20786 start
			var chkAllKhoa = false;
			if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
				chkAllKhoa = true;
			} else {
				chkAllKhoa = false;
			}
			// L2PT-20786 end
			$(_self.element).load('../noitru/NTU02D027_ThongTinDieuTri.tpl?v=250121', function() {
				$(_self.element).find("[id]").each(function() {
					if (this.id == ("pager_" + _self.options._grdDieuTri)) {
						this.id = "pager_" + _self.containerId + _self.options._grdDieuTri;
					} else {
						this.id = _self.containerId + this.id;
					}
				})
				//L2PT-20786 start
				if (chkAllKhoa) {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", true);
				} else {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", false);
				}
				// L2PT-20786 end
				//$("[data-i18n]").i18n();
				_self._loadData();
				_self._bindEvent();
				height_window = $(window).height(); // returns height of browser viewport
				height_suatan = $('#' + _self.element.attr('id')).height();
				height_divMain = $('#hidDocumentHeight').val();
				console.log('height_window1:' + height_window);
				console.log('height_suatan1:' + height_suatan);
				console.log('height_divMain1:' + height_divMain);
				if (height_suatan + 110 < height_window) {
					$('#divMain').css('height', height_window);
				} else if (height_window < height_suatan + 110) {
					$("#divMain").css('height', height_suatan + 110);
				} else if (height_suatan + 110 == height_window) {
					$('#divMain').css('height', height_suatan + 110);
				}
			});
		},
		_loadData : function() {
			var _self = this;
			var _look_sql = "NT.024.DSPHIEU";
			//khoi tao grid danh sach
			// L2PT-66455 thêm THU_TRONG_TUAN
			var _gridHeader = " ,ICON,35,0,ns,l; ,ICON2,35,0,ns,l;KHAMBENHID,KHAMBENHID,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;"
					+ "BENHNHANID,BENHNHANID,0,0,t,l;Số phiếu,SOPHIEU,150,0,f,l;Người tạo,NGUOITAO,225,0,f,l;Khoa,KHOADIEUTRI,280,0,f,l;"
					+ "Phòng,PHONGDIEUTRI,280,0,f,l;Thứ,THU_TRONG_TUAN,120,0,f,l;Thời gian chỉ định,NGAYMAUBENHPHAM,155,0,f,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;"
					+ "KHOAID,KHOAID,0,0,t,l;FLAG_CA,FLAG_CA,0,0,t,l;UNIQUE_KEY,UNIQUE_KEY,0,0,t,l;KHOADIEUTRI1,KHOADIEUTRI1,0,0,t,l;" 
					+ "TRANGTHAIPHIEUDIEUTRI,TRANGTHAIPHIEUDIEUTRI,0,0,t,l";
			//var pars = ['HIS_CHECK_ALL_DTNT'];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_CHECK_ALL_DTNT;NTU_PDT_TT_DKBTN;NTU_PDT_SHOW_DOCX;CA_WITHDRAW_ONLY_ADMIN;"
					+ "HIS_CHECK_DIEUTRI_KISO;HIS_DHYTB_INPDT_KHOAYHCT;HIS_DHYTB_INPDT_KHOAID;NTU_PDT_IN_RTF;NTU_PDT_IN_RTF_NGT;"
					+ "HIS_SUDUNG_KYSO_KYDIENTU;NTU_PDT_CHECKXOA_USER;NTU_INPDT_2KTG;HIS_CANHBAO_TTST;HIS_SHOWALL_DVKT;NTU_PDT_INTRUOCSAU;"
					+ "HIS_PHIEUDT_FORMLV;HIS_TRANGTHAIPHIEUDT_BDHN;KYSO_TUDONG_KYDIENTU;NTU_SCROLL_TABDIEUTRI;NT_PHIEUDTRI_UDP_DIENBIEN;WGPDT_HIGHT_GRID;"
					+ "NTU_PDT_DIENBIEN_CLOB;NTU_PDT_KYCA_NOTPRINT;HIS_PHIEUDT_CPY_BS;WGPDT_TRANGTHAIPDT;NTU_PDT_YLENH_LVVPC;NTU_PDT_YLENH_PHIEUIN;PHIEUDT_HAP_HIGHT_LOW;"
					+ "NTU_PDT_KBP_CT;" //L2PT-102278
					+ "NTU_PDT_CHECK_ADMINCA;PDT_INDOCX_RTF");
			if (data_ar != null && data_ar.length > 0) {
				cfObj = data_ar[0];
				_self.NT_PHIEUDTRI_UDP_DIENBIEN = data_ar[0].NT_PHIEUDTRI_UDP_DIENBIEN;
				_self.NTU_PDT_SHOW_DOCX = data_ar[0].NTU_PDT_SHOW_DOCX;
				_self.HIS_CHECK_DIEUTRI_KISO = data_ar[0].HIS_CHECK_DIEUTRI_KISO;
				_self.HIS_DHYTB_INPDT_KHOAYHCT = data_ar[0].HIS_DHYTB_INPDT_KHOAYHCT;
				_self.HIS_DHYTB_INPDT_KHOAID = data_ar[0].HIS_DHYTB_INPDT_KHOAID;
				_self.NTU_PDT_IN_RTF = data_ar[0].NTU_PDT_IN_RTF;
				_self.NTU_PDT_IN_RTF_NGT = data_ar[0].NTU_PDT_IN_RTF_NGT;
				_self.HIS_SUDUNG_KYSO_KYDIENTU = data_ar[0].HIS_SUDUNG_KYSO_KYDIENTU;
				_self.KYSO_TUDONG_KYDIENTU = data_ar[0].KYSO_TUDONG_KYDIENTU;
				_self.NTU_PDT_CHECKXOA_USER = data_ar[0].NTU_PDT_CHECKXOA_USER;
				_self.NTU_PDT_TT_DKBTN = data_ar[0].NTU_PDT_TT_DKBTN;
				_self.HIS_CHECK_ALL_DTNT = data_ar[0].HIS_CHECK_ALL_DTNT;
				_self.HIS_CANHBAO_TTST = data_ar[0].HIS_CANHBAO_TTST;
				_self.NTU_INPDT_2KTG = data_ar[0].NTU_INPDT_2KTG;
				// L2PT-20786 ttlinh start
				_self.HIS_SHOWALL_DVKT = data_ar[0].HIS_SHOWALL_DVKT;
				//L2PT-24725
				if (cfObj.NTU_PDT_KYCA_NOTPRINT == '1') {
					isPrintCa = false;
				}
				if (data_ar[0].NTU_PDT_INTRUOCSAU == 1) {
					$('#' + _self.containerId + 'btn_INTruoc').show();
					$('#' + _self.containerId + 'btn_INSau').show();
				}
				if (_self.options._formCall != '' && _self.HIS_SHOWALL_DVKT == '1' && "NTU_BDT,".includes(_self.options._formCall + ',')) {
					showAllDVKT = true;
					$('#' + _self.containerId + 'divAllKhoa').show();
				}
				//L2PT-41443
				var dtMenu = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', 'NTU02D027_HDGRID');
				if (dtMenu != undefined && dtMenu != 0 && dtMenu.length > 100) {
					_gridHeader = dtMenu;
				}
				// L2PT-20786 end
				var hight_grid = "150px";
				if (cfObj.WGPDT_HIGHT_GRID != '0') {
					hight_grid = cfObj.WGPDT_HIGHT_GRID + "px";
				}
				if (_self.HIS_CHECK_ALL_DTNT == '1') {
					//L2PT-20786 ttlinh start
					var _group_khoa = {
						groupField : [ 'KHOADIEUTRI1' ],
						groupColumnShow : [ false ],
						groupText : [ '<b>{0}</b>' ]
					};
					if (showAllDVKT) {
						GridUtil.initGroup(_self.containerId + _self.options._grdDieuTri, "100%", hight_grid, 'Danh sách phiếu điều trị', true, _group_khoa, _gridHeader, false, {
							rowNum : 500,
							rowList : [ 100, 200, 500 ]
						//HaNv_120921: L2PT-7213
						});
					} else {
						GridUtil.init(_self.containerId + _self.options._grdDieuTri, "100%", hight_grid, 'Danh sách phiếu điều trị', true, _gridHeader, false, {
							rowNum : 500,
							rowList : [ 100, 200, 500 ]
						});
					}
					// L2PT-20786 end
				} else {
					//L2PT-20786 ttlinh start
					var _group_khoa = {
						groupField : [ 'KHOADIEUTRI1' ],
						groupColumnShow : [ false ],
						groupText : [ '<b>{0}</b>' ]
					};
					if (showAllDVKT) {
						GridUtil.initGroup(_self.containerId + _self.options._grdDieuTri, "100%", hight_grid, 'Danh sách phiếu điều trị', true, _group_khoa, _gridHeader, false, {
							rowNum : 500,
							rowList : [ 100, 200, 500 ]
						//HaNv_120921: L2PT-7213
						});
					} else {
						GridUtil.init(_self.containerId + _self.options._grdDieuTri, "100%", hight_grid, 'Danh sách phiếu điều trị', true, _gridHeader, false, {
							rowNum : 500,
							rowList : [ 100, 200, 500 ]
						});
					}
					// L2PT-20786 end
				}
				//L2PT-23635
				if (cfObj.HIS_PHIEUDT_FORMLV == "1") {
					$('#' + _self.containerId + 'formLVVPC').show();
					$('#' + _self.containerId + 'formOri').hide();
					_self.YLENH_ID = 'txtYLENH_LVVPC';
					_self.DIENBIENBENH_ID = 'txtDIENBIENBENH_LVVPC';
				} else {
					$('#' + _self.containerId + 'formLVVPC').hide();
					$('#' + _self.containerId + 'formOri').show();
					_self.YLENH_ID = 'txtYLENH';
					_self.DIENBIENBENH_ID = 'txtDIENBIENBENH';
					if (cfObj.NTU_SCROLL_TABDIEUTRI == "1") {
						$('#' + _self.containerId + 'formOri').attr('style', 'height: 300px;overflow-y: scroll');
					}
				}
				
				//L2PT-102278
				if (cfObj.NTU_PDT_KBP_CT == "1") 
					$('#' + _self.containerId + 'divKhamBP1').show();
				else
					$('#' + _self.containerId + 'divKhamBP1').hide();
				
				if (_self.NTU_PDT_TT_DKBTN == '1') {
					$("#" + _self.containerId + "divYLENH").show();
					CKEDITOR.replace(_self.containerId + _self.YLENH_ID, {
						// Define the toolbar groups as it is a more accessible solution.
						toolbar : [
						//{ name: 'document', groups: [ 'mode', 'document', 'doctools' ], items: [ 'Source', '-', 'Save', 'NewPage', 'Preview', 'Print', '-', 'Templates' ] },
						'/', {
							name : 'basicstyles',
							groups : [ 'basicstyles', 'cleanup' ],
							items : [ 'Bold', 'Italic', 'Underline', 'Strike' ]
						}, {
							name : 'paragraph',
							groups : [ 'list', 'indent', 'blocks', 'align', 'bidi' ],
							items : [ 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', , 'Language' ]
						}, {
							name : 'insert',
							items : [ 'Image', 'Table', 'HorizontalRule' ]
						}, '/', {
							name : 'styles',
							items : [ 'Styles', 'Format', 'Font', 'FontSize' ]
						}, {
							name : 'colors',
							items : [ 'TextColor', 'BGColor' ]
						}, {
							name : 'tools',
							items : [ 'Maximize' ]
						} ],
						// Remove the redundant buttons from toolbar groups defined above.
						removeButtons : 'Strike,Subscript,Superscript,Anchor,Styles,Specialchar',
						height : '180px'
					});
				}
			}
			var _sql_par = [];
			// L2PT-20786 ttlinh start
			if (showAllDVKT) {
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					if(_self.options._hosobenhanid == '-2'){
						_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
					}
					else{
						_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, $('#hidHOSOBENHANID').val() ]);
					}
				} else {
					if(_self.options._hosobenhanid == '-2'){
						_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
					}
					else{
						_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, -1 ]);
					}
					
				}
			} else {
				_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
			}
			// L2PT-20786 end
			GridUtil.loadGridBySqlPage(_self.containerId + _self.options._grdDieuTri, _look_sql, _sql_par);
			if (cfObj.NTU_SCROLL_TABDIEUTRI == "2") {//HaNv_110823: L2PT-47988
				let
				elems = ".ui-jqgrid-bdiv, .ui-jqgrid-hdiv, .ui-jqgrid-pager, .ui-jqgrid-sdiv";
				let
				gboxId = "#gbox_" + "tcDieuTrigrdDieuTri";
				$(elems, gboxId).slideUp(1, function() {
					$(elems, gboxId).slideDown("fast")
				});
			}
			// Xử lý sự kiện liên quan ký CA => START
			//_kySoCH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
			if (_self.HIS_SUDUNG_KYSO_KYDIENTU != "1") {
				$('#' + _self.containerId + 'GROUP4').remove();
				$('#' + _self.containerId + 'kyCA').remove();
				$('#' + _self.containerId + 'kyCA2').remove();
				$('#' + _self.containerId + 'printKyCA').remove();
				$('#' + _self.containerId + 'huyKyCA').remove();
				$('#' + _self.containerId + 'printSelectedCASigned').remove();
				$('#' + _self.containerId + 'inGopCA').remove();
				$("#" + _self.containerId + _self.options._grdDieuTri).hideCol('ICON');
			}
			// Xử lý sự kiện liên quan ký CA => END
			//L2PT-27042
			if (cfObj.HIS_TRANGTHAIPHIEUDT_BDHN != '1') {
				$('#' + _self.containerId + 'hoanthanPDT').remove();
				$('#' + _self.containerId + 'gohoanthanhPDT').remove();
				$("#" + _self.containerId + _self.options._grdDieuTri).hideCol('ICON2');//L2PT-29128
			}
			// L2PT-10648 duonghn start
			if (_self.NTU_INPDT_2KTG == '1') {
				$('#' + _self.containerId + 'divTHOIGIAN2').show();
			}
			// L2PT-10648 end
			htmlDlgThoiGian = $('#' + _self.containerId + 'dlgWRAP_P').html(); // fix lỗi chọn thời gian khi chuyển tab
			// L2PT-124782 start
			configContextMenus([ {
				idContextMenu : 'tcDieuTricontextMenu',
				configCode : 'NTU_CONTEXT_MENU_PDT',
				
			} ], 'LI', true);
			// L2PT-124782 end
		},
		_bindEvent : function() {
			var _self = this;
			//L2PT-20786 start
			var modeView;
			if (showAllDVKT) {
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					modeView = "1";
				} else {
					modeView = _self.options._modeView;
				}
			} else {
				modeView = _self.options._modeView;
			}
			//L2PT-20786 end
			GridUtil.setGridParam(_self.containerId + _self.options._grdDieuTri, {
				onSelectRow : function(id, selected) {
					if (!selected) {
						return;
					}
					_self._viewDieuTriDetail(id);
					GridUtil.unmarkAll(_self.containerId + _self.options._grdDieuTri);
					GridUtil.markRow(_self.containerId + _self.options._grdDieuTri, id);
					var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', id);
					$("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
				},
				ondblClickRow : function(id) {
					if (modeView == "0") {
						_self._updateDieutriDetail(id)
					}
				},
				gridComplete : function(id) {
					var ids = $("#" + _self.containerId + _self.options._grdDieuTri).getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var row = $("#" + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', id);
						//set icon trang thai  benh nhan
						var _icon = '';
						// đã ký số
						//L2PT-26052 merge check ki so nhi quang nam 
						if ((row.UNIQUE_KEY && row.UNIQUE_KEY == row.MAUBENHPHAMID) || (row.FLAG_CA == '1' || row.FLAG_CA == '5' || row.FLAG_CA == '99')) {
							if (row.FLAG_CA == '1') {
								_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
							} else if (row.FLAG_CA == '99') {
								_icon = '<center><img src="../common/image/ca-3.png" width="15px"></center>';
							} else {
								_icon = '<center><img src="../common/image/ca2.PNG" width="15px"></center>';
							}
						}
						$("#" + _self.containerId + _self.options._grdDieuTri).jqGrid('setCell', id, 'ICON', _icon);//L2PT-20786
						//L2PT-29128
						if (cfObj.HIS_TRANGTHAIPHIEUDT_BDHN == '1') {
							var _icon = '';
							if (row.TRANGTHAIPHIEUDIEUTRI == '0' || row.TRANGTHAIPHIEUDIEUTRI == '1') {
								_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
							} else if (row.TRANGTHAIPHIEUDIEUTRI == '2') {
								_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
							}
							$("#" + _self.containerId + _self.options._grdDieuTri).jqGrid('setCell', id, 'ICON2', _icon);
						}
						//L2PT-15754
						if (_self.HIS_CANHBAO_TTST == '1') {
							var objData = new Object();
							objData.KHAMBENHID = row.KHAMBENHID;
							var sql_par = [ JSON.stringify(objData) ];
							var _dataResult = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D027.CBTTST", sql_par.join('$'));
							if (_dataResult != '0') {
								var objRS = JSON.parse(_dataResult);
								if (row.MAUBENHPHAMID == objRS.MAUBENHPHAMID) {
									/*$("#" + _self.containerId + _self.options._grdDieuTri).setCell(id, 'SOPHIEU', '', {
										'background-color' : objRS.MAUCB
									});*/
									$("#" + _self.containerId + _self.options._grdDieuTri).find("tr[id='" + id + "']").find('td').each(function(index, element) {
										$(element).css("background-color", objRS.MAUCB);
									});
								}
							}
						}
					}
					if (modeView == "0") {
						//build menu
						$(".jqgrow", '#' + _self.containerId + _self.options._grdDieuTri).contextMenu(_self.containerId + 'contextMenu', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._grdDieuTri);
								//grid.setSelection(rowId);
								if (_self.HIS_CHECK_ALL_DTNT == '1') {
									var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
									var check = true;
									if (rowDatas.length > 0) {
										for (var j = 0; j < rowDatas.length; j++) {
											if (rowDatas[j] == rowId) {
												check = false;
											}
										}
										if (check) {
											grid.setSelection(rowId);
										}
									} else {
										grid.setSelection(rowId);
									}
								} else {
									grid.setSelection(rowId);
								}
								return true;
							},
						});
						//START L2PT-21288
						if (_self.NTU_PDT_SHOW_DOCX == '0') {
							$('#' + _self.containerId + 'printDocx').remove();
						}
						if (!_self.options.checkLoad) {
							_self.options.checkLoad = true;
							//cập nhật phiếu điều trị
							$('#' + _self.containerId + 'updatePDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._updateDieutriDetail(rowKey)
							});
							$('#' + _self.containerId + 'copyPDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._copyPDT(rowKey)
							});
							//L2K74TW-612 -- Tach phieu dt
							$('#' + _self.containerId + 'TachPDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._TachPDT(rowKey)
							});
							//L2PT-31078
							$('#' + _self.containerId + 'CapnhatDVNGT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self.CapnhatDVNGT(rowKey)
							});
							$('#' + _self.containerId + 'hoanthanPDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._hoanthanhPDT_BDHN(rowKey, '2')
							});
							$('#' + _self.containerId + 'gohoanthanhPDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._hoanthanhPDT_BDHN(rowKey, '1')
							});
							//L2PT-31839
							$('#' + _self.containerId + 'xoaPDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._XoaPDT(rowKey)
							});
						}
						//xu ly su kien int
						$('#' + _self.containerId + 'print').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTodieutri(rowKey);
						});
						//START L2PT-21288 in docx
						$('#' + _self.containerId + 'printDocx').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTodieutriDocx(rowKey);
						});
						//Xu ly su kien in all
						$('#' + _self.containerId + 'printAll').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTodieutriAll(rowKey);
						});
						//xu ly su kien int
						$('#' + _self.containerId + 'printYLENH').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportToYLenh(rowKey);
						});
						$('#' + _self.containerId + 'printYLENHTD').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportToYLenhTD(rowKey);
						});
						if (_self.HIS_CHECK_ALL_DTNT == '1') {
							$('#' + _self.containerId + 'printSelected').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._exportSelected(rowKey);
							});
						} else {
							$('#' + _self.containerId + 'printAll').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._exportTodieutriAll(rowKey);
							});
						}
						$('#' + _self.containerId + 'printTHEOTHOIGIAN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTheoTG(rowKey);
						});
						$('#' + _self.containerId + 'printALlKhoa').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportAllKhoa(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => START
						$('#' + _self.containerId + 'kyCA').unbind("click").bind("click", function() {
							kyChot = false;
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._kyCA(rowKey);
						});
						$('#' + _self.containerId + 'kyCA2').unbind("click").bind("click", function() {
							kyChot = true;
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._kyCA(rowKey);
						});
						$('#' + _self.containerId + 'huyKyCA').unbind("click").bind("click", function() {
							kyChot = false;
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._huyKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printKyCA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printSelectedCASigned').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._printSelectedCASigned(rowKey);
						});
						$('#' + _self.containerId + 'inGopCA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._inGopCA(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => END
					} else if (modeView == "1") {
						//build menu
						$(".jqgrow", '#' + _self.containerId + _self.options._grdDieuTri).contextMenu(_self.containerId + 'contextMenuPrint', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._grdDieuTri);
								//grid.setSelection(rowId);
								if (_self.HIS_CHECK_ALL_DTNT == '1') {
									var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
									var check = true;
									if (rowDatas.length > 0) {
										for (var j = 0; j < rowDatas.length; j++) {
											if (rowDatas[j] == rowId) {
												check = false;
											}
										}
										if (check) {
											grid.setSelection(rowId);
										}
									} else {
										grid.setSelection(rowId);
									}
								} else {
									grid.setSelection(rowId);
								}
								return true;
							},
						});
						//START L2PT-21288
						if (_self.NTU_PDT_SHOW_DOCX == '0') {
							$('#' + _self.containerId + 'printViewDocx').remove();
						}
						//xu ly su kien int
						$('#' + _self.containerId + 'printView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTodieutri(rowKey);
						});
						//START L2PT-21288 in docx
						$('#' + _self.containerId + 'printViewDocx').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTodieutriDocx(rowKey);
						});
						//Xu ly su kien in all
						$('#' + _self.containerId + 'printAllView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTodieutriAll(rowKey);
						});
						//xu ly su kien int
						$('#' + _self.containerId + 'printYLENH').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportToYLenh(rowKey);
						});
						$('#' + _self.containerId + 'printYLENHTDView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportToYLenhTD(rowKey);
						});
						if (_self.HIS_CHECK_ALL_DTNT == '1') {
							$('#' + _self.containerId + 'printSelected').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._exportSelected(rowKey);
							});
							//xu ly su kien int
							$('#' + _self.containerId + 'printYLENHView').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._exportToYLenh(rowKey);
							});
							$('#' + _self.containerId + 'printSelectedView').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._exportSelected(rowKey);
							});
						} else {
							$('#' + _self.containerId + 'printView').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
								_self._exportTodieutri(rowKey);
							});
						}
						$('#' + _self.containerId + 'printTHEOTHOIGIANView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportTheoTG(rowKey);
						});
						$('#' + _self.containerId + 'printALlKhoaView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportAllKhoa(rowKey);
						});
						$('#' + _self.containerId + 'printKyCAView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdDieuTri).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
					}
					if (_self.HIS_CANHBAO_TTST != '1' && _self.NTU_PDT_TT_DKBTN != '1') {
						_setFocusMauBenhPham($("#hidMAUBENHPHAMID").val(), _self.containerId + _self.options._grdDieuTri);
					}
				}
			});
			//L2PT-20786 start
			$('#' + _self.containerId + 'chkAllKhoa').unbind("click").bind("click", function() {
				_self._initWidget();
			});
			//L2PT-20786 end
		},
		// Xử lý sự kiện liên quan ký CA => START
		_kyCA : function(rowId) {
			var _self = this;
			_self._caRpt(rowId, '1');
		},
		_huyKyCA : function(rowId) {
			if (cfObj.CA_WITHDRAW_ONLY_ADMIN == '1') {
				var isAdmin = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D027.AMID.CA", cfObj.CA_WITHDRAW_ONLY_ADMIN);
				if (isAdmin != '1') {
					DlgUtil.showMsg("Loi: Chi admin moi co the huy phieu da ky.");
					return;
				}
			}
			var _self = this;
			_self._caRpt(rowId, '2');
		},
		_caRpt : function(rowId, signType) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			var sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : rowData.KHOAID
			});
			var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
			var row2 = JSON.parse(data2)[0];
			var loaikhoa = row2.ORG_TYPE;
				if (_self.HIS_DHYTB_INPDT_KHOAYHCT == '1' && _self.HIS_DHYTB_INPDT_KHOAID == loaikhoa) {
				_rptCode = "NTU020_TODIEUTRI_39BV01_QD4069_YHCT_A4_ONE";
			}
			let	userCaConf = CaUtils.getCACachingConfig(_rptCode);
			var _par = [ {
				name : 'MAUBENHPHAMID',
				type : 'String',
				value : rowData.MAUBENHPHAMID
			}, {
				name : 'RPT_CODE',
				type : 'String',
				value : _rptCode
			} ];
			var _check = CommonUtil.checkKyCaByParam(_par, '2');
			if (kyChot || (_check == '2' && signType == '2')) {
				kyChot = true;
				_rptCode = 'NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE_KYCHOT';
			}
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				var catype = row.CA_TYPE;
				var kieuky = row.KIEUKY;
				if (catype == '3' || catype == '6') {
					var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					let
					_paramInput = {
						params : null,
						smartca_method : 0
					};
					EventUtil.setEvent("dlgCaLogin_confirm", function() {
						DlgUtil.close("divCALOGIN");
						let
						_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
						causer = _hisl2SmartCa.token.refresh_token;
						capassword = _hisl2SmartCa.token.access_token;
						smartcauser = _hisl2SmartCa.user.uid;
						_self._caRpt2(rowId, signType, catype, kieuky);
					});
					let
					hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
						_paramInput.smartca_method = 1;
						if (_self.KYSO_TUDONG_KYDIENTU == '1') {
							let
							_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
							causer = _hisl2SmartCa.token.refresh_token;
							capassword = _hisl2SmartCa.token.access_token;
							smartcauser = _hisl2SmartCa.user.uid;
							_self._caRpt2(rowId, signType, catype, kieuky);
						} else {
							let
							_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						}
					} else {
						EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
							if (e.data && e.data.token && e.data.token.access_token) {
								_paramInput.smartca_method = 1;
							}
							DlgUtil.close("dlgCA_SMARTCA_LOGIN");
							let
							_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						});
						DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {
							isSignPopup : true
						}, "Smart Ca Login", 500, 650);
						DlgUtil.open("dlgCA_SMARTCA_LOGIN");
						return;
					}
				} else if (catype == '5') {
					var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
					if (rowDatas.length > 1) {
						DlgUtil.showMsg("Không được phép ký nhiều phiếu vs kiểu " + kieuky);
						return;
					} else {
						_self._caRpt3(rowId, signType, catype, kieuky);
					}
				} else if (userCaConf && _self.KYSO_TUDONG_KYDIENTU == '1') {
					causer = userCaConf.USER_NAME;
					capassword = userCaConf.PASS_WORD;
					_self._caRpt2(rowId, signType, catype, kieuky);
				} else {
					EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
						causer = e.username;
						capassword = e.password;
						DlgUtil.close("divCALOGIN");
						_self._caRpt2(rowId, signType, catype, kieuky);
					});
					EventUtil.setEvent("dlgCaLogin_close", function(e) {
						DlgUtil.close("divCALOGIN");
					});
					var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
					popup.open("divCALOGIN");
				}
			}
		},
		_caRpt2 : function(rowId, signType, catype, kieuky) {
			var _self = this;
			msgKyca = '';
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas.length == 0 || rowDatas.length == 1) {
				_self._caRpt3(rowId, signType, catype, kieuky);
				DlgUtil.showMsg(msgKyca);
				_self._initWidget();
			} else {
				rowDatas.forEach(function(el) {
					_self._caRpt3(el, signType, catype, kieuky);
					_self._initWidget();
				});
				DlgUtil.showMsg(msgKyca);
			}
		},
		_caRpt3 : function(rowId, signType, catype, kieuky) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (kyChot) {
				var paramHashed = "";

				var sql_parHash = [rowData.MAUBENHPHAMID ];
				paramHashed = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D027.CHKPARHASH", sql_parHash.join('$'));

				var obj = new Object();
				obj.CAHASHED = paramHashed;
				var checkCa = jsonrpc.AjaxJson.ajaxCALL_SP_I('CHECK.KY.CA2', JSON.stringify(obj));
				if (checkCa == '0') {
					var _msg = rowData.SOPHIEU + ' - Phiếu chưa thực hiện Ký điện tử';
					msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
					return;
				} else if (checkCa == '2' && signType == '1') {
					var _msg = rowData.SOPHIEU + ' - Phiếu đã thực hiện Ký chốt';
					msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
					return;
				}
				_rptCode = 'NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE_KYCHOT';
			} else {
				if (signType == '1' && rowData.FLAG_CA == '1') {
					var _msg = rowData.SOPHIEU + ' - Phiếu đã thực hiện Ký điện tử';
					msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
					return;
				}
			}
			var par_rpt_KySo = [ {
				name : 'HOSOBENHANID',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			par_rpt_KySo.push({
				name : 'RPT_CODE',
				type : 'String',
				value : _rptCode
			});
			par_rpt_KySo.push({
				name : 'maubenhphamid',
				type : 'String',
				value : rowData.MAUBENHPHAMID
			});
			par_rpt_KySo.push({
				name : 'i_benhnhanid',
				type : 'String',
				value : rowData.BENHNHANID
			});
			par_rpt_KySo.push({
				name : 'i_maubenhphamid',
				type : 'String',
				value : rowData.MAUBENHPHAMID
			});
			par_rpt_KySo.push({
				name : 'i_khoaid',
				type : 'String',
				value : rowData.KHOAID
			});
			var oData = {
				sign_type : signType,
				causer : causer,
				capassword : capassword,
				smartcauser : smartcauser,
				params : par_rpt_KySo
			};
			if (catype == '5') {
				CommonUtil.kyCA(par_rpt_KySo, signType, isPrintCa);
				EventUtil.setEvent("eventKyCA", function(e) {
					DlgUtil.showMsg(e.res);
				});
			} else {
				var msg = CommonUtil.caRpt(oData, _rptCode, isPrintCa, '', true, kieuky, catype);
				var _code = msg.split("|")[0];
				var _msg = rowData.SOPHIEU + ' - ' + msg.split("|")[1];
				var _caid = msg.split("|")[2];
				msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
				//update ky chot
				if (kyChot && (_code == '0' || _code == '7' || _code == '8')) {
					var flag_kychot = signType == 1 ? '99' : '1';
					var _sql_par = [];
					_sql_par.push({
						"name" : "[0]",
						value : flag_kychot
					}, {
						"name" : "[1]",
						value : rowData.MAUBENHPHAMID
					});
					jsonrpc.AjaxJson.getOneValue("HIS.UPD.KYCHOT", _sql_par);
				}
				if (catype == '3') {
					if (_code == '0' || _code == '7' || _code == '8') {
						var intervalId = null;
						var smartCaLoaderFunction = function() {
							console.log("smartCaLoaderFunction is running!");
							var _sql_par = [];
							_sql_par.push({
								"name" : "[0]",
								value : _caid
							});
							var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
							if (fl == 1) {
								// bat phieu in
								CommonUtil.openReportGetCA2(_par, false);
								clearInterval(intervalId);
							}
						};
						intervalId = setInterval(smartCaLoaderFunction, 4000);
					}
				}
			}
		},
		_exportKyCA : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData.FLAG_CA == '5') {
				let
				parReport = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : rowData.BENHNHANID
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : rowData.KHOAID
				}, {
					name : 'i_maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				let
				data = CommonUtil.buildDataCA(101, RestInfo.uuid, {}, _rptCode, parReport);
				let
				notSign = CommonUtil.isSignCANotYet(data);
				if (notSign) {
					DlgUtil.showMsg('Phiếu chưa được ký CA');
					return false;
				}
				CommonUtil.openReportGetXml('window', _rptCode, "pdf", parReport);
			} else {
				var par_rpt_KySo = [ {
					name : 'HOSOBENHANID',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				} ];
				par_rpt_KySo.push({
					name : 'RPT_CODE',
					type : 'String',
					value : _rptCode
				});
				par_rpt_KySo.push({
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				});
				par_rpt_KySo.push({
					name : 'i_benhnhanid',
					type : 'String',
					value : rowData.BENHNHANID
				});
				par_rpt_KySo.push({
					name : 'i_maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				});
				par_rpt_KySo.push({
					name : 'i_khoaid',
					type : 'String',
					value : rowData.KHOAID
				});
				CommonUtil.openReportGetCA2(par_rpt_KySo, false);
			}
		},
		_exportTodieutri : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//L2PT-11856
				sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : rowData.KHOAID
				});
				var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
				var row2 = JSON.parse(data2)[0];
				var loaikhoa = row2.ORG_TYPE;
				if (_self.HIS_DHYTB_INPDT_KHOAYHCT == '1' && _self.HIS_DHYTB_INPDT_KHOAID == loaikhoa) {
					var par = [ {
						name : 'i_benhnhanid',
						type : 'String',
						value : rowData.BENHNHANID
					}, {
						name : 'i_maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'i_khoaid',
						type : 'String',
						value : rowData.KHOAID
					} ];
					openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_YHCT_A4_ONE", "pdf", par);
				} else {
					var par = [ {
						name : 'i_benhnhanid',
						type : 'String',
						value : rowData.BENHNHANID
					}, {
						name : 'i_maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'i_khoaid',
						type : 'String',
						value : rowData.KHOAID
					} ];
					if (_self.NTU_PDT_IN_RTF == '1') {
						//
						if (_self.NTU_PDT_IN_RTF_NGT == '1') {
							var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
							CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", 'rtf', par, rpName);
						} else {
							if ($("#hidLOAITIEPNHANID").val() == '0') {
								var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
								CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", 'rtf', par, rpName);
							} else {
								openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);
							}
						}
					} else {
						// kham benh ngt
						if ($("#hidLOAITIEPNHANID").val() == '1' && $("#company_id").val() == '10284') {
							openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_NGT", "pdf", par);
						} else {
							openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);
						}
					}
				}
			}
		},
		//START L2PT-21288
		_exportTodieutriDocx : function(rowId) {
			var _self = this;
			var _khambenhid = "-1";
			var _maubenhphamid = "-1";
			var _benhnhanid = "-1";
			var _khoaid = "-1";
			var lstmaubenhphamid = "";
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowDatas[j]);
					if (rowData != null) {
						_benhnhanid = rowData.BENHNHANID;
						_khambenhid = rowData.KHAMBENHID;
						_khoaid = rowData.KHOAID;
						lstmaubenhphamid = lstmaubenhphamid + "," + rowData.MAUBENHPHAMID
					}
				}
				lstmaubenhphamid = lstmaubenhphamid.substring(1, lstmaubenhphamid.length);
				var par = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : _benhnhanid
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : _khoaid
				}, {
					name : 'i_khambenhid',
					type : 'String',
					value : _khambenhid
				}, {
					name : 'i_maubenhphamids',
					type : 'String',
					value : lstmaubenhphamid
				} ];
				if (cfObj.PDT_INDOCX_RTF == '1') {//HaNv_151024: L2PT-104314
					var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI_18707" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
					CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI_18707", 'rtf', par, rpName);
				} else {
					var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI_18707" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
					CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI_18707", 'docx', par, rpName);
				}
			}
		},
		_exportTodieutriAll : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//L2PT-11856
				sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : rowData.KHOAID
				});
				var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
				var row2 = JSON.parse(data2)[0];
				var loaikhoa = row2.ORG_TYPE;
				//bv dhy tb va khoa yhct
				if (_self.HIS_DHYTB_INPDT_KHOAYHCT == '1' && _self.HIS_DHYTB_INPDT_KHOAID == loaikhoa) {
					//START L2PT-5287
					if (_self.options._mode_in_LSBA != null && _self.options._mode_in_LSBA == '1') {
						var par = [ {
							name : 'i_benhnhanid',
							type : 'String',
							value : rowData.BENHNHANID
						}, {
							name : 'i_khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						} ];
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_KHOA_ALL", "pdf", par);
					} else {
						var par = [ {
							name : 'i_benhnhanid',
							type : 'String',
							value : rowData.BENHNHANID
						}, {
							name : 'i_khoaid',
							type : 'String',
							value : rowData.KHOAID
						}, {
							name : 'i_khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						} ];
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_YHCT_A4_ALL", "pdf", par);
					}
					//END L2PT-5287
				} else {
					//START L2PT-5287
					if (_self.options._mode_in_LSBA != null && _self.options._mode_in_LSBA == '1') {
						var par = [ {
							name : 'i_benhnhanid',
							type : 'String',
							value : rowData.BENHNHANID
						}, {
							name : 'i_khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						} ];
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_KHOA_ALL", "pdf", par);
					} else {
						var par = [ {
							name : 'i_benhnhanid',
							type : 'String',
							value : rowData.BENHNHANID
						}, {
							name : 'i_khoaid',
							type : 'String',
							value : rowData.KHOAID
						}, {
							name : 'i_khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						} ];
						if (_self.NTU_PDT_IN_RTF == '1') {
							if (_self.NTU_PDT_IN_RTF_NGT == '1') {
								var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
								CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL", 'rtf', par, rpName);
							} else {
								if ($("#hidLOAITIEPNHANID").val() == '0') {
									var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
									CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL", 'rtf', par, rpName);
								} else {
									openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL", "pdf", par);
								}
							}
						} else {
							openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL", "pdf", par);
						}
					}
					//END L2PT-5287
				}
				rowData.KHAMBENHID
			}
		},
		_exportSelected : function(rowId) {
			var _self = this;
			var _khambenhid = "-1";
			var _maubenhphamid = "-1";
			var _benhnhanid = "-1";
			var _khoaid = "-1";
			var lstmaubenhphamid = "";
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowDatas[j]);
					if (rowData != null) {
						_benhnhanid = rowData.BENHNHANID;
						_khambenhid = rowData.KHAMBENHID;
						_khoaid = rowData.KHOAID;
						lstmaubenhphamid = lstmaubenhphamid + "," + rowData.MAUBENHPHAMID
					}
				}
				lstmaubenhphamid = lstmaubenhphamid.substring(1, lstmaubenhphamid.length);
				var par = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : _benhnhanid
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : _khoaid
				}, {
					name : 'i_khambenhid',
					type : 'String',
					value : _khambenhid
				}, {
					name : 'i_maubenhphamids',
					type : 'String',
					value : lstmaubenhphamid
				} ];
				//L2PT-11856
				sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : rowData.KHOAID
				});
				var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
				var row2 = JSON.parse(data2)[0];
				var loaikhoa = row2.ORG_TYPE;
				if (_self.HIS_DHYTB_INPDT_KHOAYHCT == '1' && _self.HIS_DHYTB_INPDT_KHOAID == loaikhoa) {
					//START L2PT-5287
					if (_self.options._mode_in_LSBA != null && _self.options._mode_in_LSBA == '1') {
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_KHOA_MULTI", "pdf", par);
					} else {
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_YHCT_A4_MULTI", "pdf", par);
					}
					//END L2PT-5287
				} else {
					//START L2PT-5287
					if (_self.options._mode_in_LSBA != null && _self.options._mode_in_LSBA == '1') {
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_KHOA_MULTI", "pdf", par);
					} else {
						if (_self.NTU_PDT_IN_RTF == '1') {
							if (_self.NTU_PDT_IN_RTF_NGT == '1') {
								var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
								CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", 'rtf', par, rpName);
							} else {
								if ($("#hidLOAITIEPNHANID").val() == '0') {
									var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
									CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", 'rtf', par, rpName);
								} else {
									openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", "pdf", par);
								}
							}
						} else {
							openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", "pdf", par);
						}
					}
					//END L2PT-5287
				}
				rowData.KHAMBENHID
			}
		},
		_printSelectedCASigned : function() {
			var par_rpt_KySo = [];
			var _rpt_code_kyso = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE";
			var _self = this;
			var lstmaubenhphamid = "";
			var lstmaubenhphamidOld = "";
			var lstmaubenhphamid2 = "";
			var _khambenhid = "-1";
			var _benhnhanid = "-1";
			var _khoaid = "-1";
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowDatas[j]);
					if (rowData != null) {
						_benhnhanid = rowData.BENHNHANID;
						_khambenhid = rowData.KHAMBENHID;
						_khoaid = rowData.KHOAID;
						if (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99') {
							lstmaubenhphamid = lstmaubenhphamid + "," + rowData.MAUBENHPHAMID;
						} else if (rowData.FLAG_CA == '5') {
							lstmaubenhphamidOld = lstmaubenhphamidOld + "," + rowData.MAUBENHPHAMID;
						} else {
							lstmaubenhphamid2 = lstmaubenhphamid2 + "," + rowData.SOPHIEU;
						}
					}
				}
				if (lstmaubenhphamid2.length > 1) {
					DlgUtil.showMsg("Tồn tại phiếu chưa thực hiện ký số :" + lstmaubenhphamid2.substring(1, lstmaubenhphamid2.length));
					return;
				}
				if (lstmaubenhphamid.length > 1) {
					lstmaubenhphamid = lstmaubenhphamid.substring(1, lstmaubenhphamid.length);
					par_rpt_KySo = [ {
						name : 'HOSOBENHANID',
						type : 'String',
						value : $("#hidHOSOBENHANID").val()
					}, {
						name : 'LST_MAUBENHPHAMID',
						type : 'String',
						value : lstmaubenhphamid
					}, {
						name : 'RPT_CODE',
						type : 'String',
						value : _rpt_code_kyso
					} ];
					CommonUtil.openReportGetViewCa(par_rpt_KySo, false);
				}
				if (lstmaubenhphamidOld.length > 1) {
					lstmaubenhphamidOld = lstmaubenhphamidOld.substring(1, lstmaubenhphamidOld.length);
					var par = [ {
						name : 'i_benhnhanid',
						type : 'String',
						value : _benhnhanid
					}, {
						name : 'i_khoaid',
						type : 'String',
						value : _khoaid
					}, {
						name : 'i_khambenhid',
						type : 'String',
						value : _khambenhid
					}, {
						name : 'i_maubenhphamids',
						type : 'String',
						value : lstmaubenhphamidOld
					} ];
					CommonUtil.openReportGetXml('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", "pdf", par);
					return false;
				}
			}
		},
		_inGopCA : function() {
			var _self = this;
			var uuid = RestInfo.uuid;
			var par_rpt_KySo = [];
			var _rpt_code_kyso = "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE";
			var lstmaubenhphamid = "";
			var lstmaubenhphamidOld = "";
			var lstmaubenhphamid2 = "";
			var _khambenhid = "-1";
			var _benhnhanid = "-1";
			var _khoaid = "-1";
			var obj = new Object();
			obj.hosobenhanid = $("#hidHOSOBENHANID").val();
			obj.loainhommaubenhpham = '4';
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("HIS.GETMBP", JSON.stringify(obj));
			if (data_ar != null && data_ar.length > 0) {
				for (var j = 0; j < data_ar.length; j++) {
					var rowData = data_ar[j];
					if (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99') {
						lstmaubenhphamid = lstmaubenhphamid + "," + rowData.MAUBENHPHAMID;
					} else if (rowData.FLAG_CA == '5') {
						lstmaubenhphamidOld = lstmaubenhphamidOld + "," + rowData.MAUBENHPHAMID;
					} else {
						lstmaubenhphamid2 = lstmaubenhphamid2 + "," + rowData.SOPHIEU;
					}
				}
			}
			if (lstmaubenhphamid2.length > 1) {
				DlgUtil.showMsg("Tồn tại phiếu chưa thực hiện ký số :" + lstmaubenhphamid2.substring(1, lstmaubenhphamid2.length));
				return;
			}
			if (lstmaubenhphamid.length > 1) {
				lstmaubenhphamid = lstmaubenhphamid.substring(1, lstmaubenhphamid.length);
				par_rpt_KySo = [ {
					name : 'HOSOBENHANID',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'LST_MAUBENHPHAMID',
					type : 'String',
					value : lstmaubenhphamid
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : _rpt_code_kyso
				} ];
				CommonUtil.openReportGetViewCa(par_rpt_KySo, false);
			}
			if (lstmaubenhphamidOld.length > 1) {
				// JWT expired
				var par = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : _benhnhanid
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : _khoaid
				}, {
					name : 'i_khambenhid',
					type : 'String',
					value : _khambenhid
				} ];
				CommonUtil.openReportGetXml('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL", "pdf", par);
				return false;
			}
		},
		// dannd_L2PT-4638
		_exportTheoTG : function(rowId) {
			var _self = this;
			var _benhnhanid = "-1";
			var _khambenhid = "-1";
			var _khoaid = "-1";
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			$('.popup_remove').remove();
			$('#' + _self.containerId + 'dlgWRAP_P').append($(htmlDlgThoiGian));
			var dlgINTHOIGIAN = DlgUtil.buildPopup("dlgINTHOIGIAN", _self.containerId + "dlgTHOIGIAN", "In tờ điều trị theo khoảng thời gian", 800, 110, {
				"zIndex" : 998
			}); // L2PT-10648 duonghn
			DlgUtil.open("dlgINTHOIGIAN");
			var btnIN = $('#' + _self.containerId + 'btn_IN');
			var btnClose = $('#' + _self.containerId + 'btn_CLOSE');
			var btn_INTruoc = $('#' + _self.containerId + 'btn_INTruoc');
			var btn_INSau = $('#' + _self.containerId + 'btn_INSau');
			btnIN.click(function() {
				_self._exportTheoTG_RPT(1, rowData);
			});
			btn_INTruoc.click(function() {
				_self._exportTheoTG_RPT(2, rowData);
			});
			btn_INSau.click(function() {
				_self._exportTheoTG_RPT(3, rowData);
			});
			btnClose.click(function() {
				dlgINTHOIGIAN.close();
			});
		},
		//L2PT-36310
		_exportAllKhoa : function(rowId) {
			var _self = this;
			var _benhnhanid = "-1";
			var _khambenhid = "-1";
			var _khoaid = "-1";
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			var par = [ {
				name : 'benhnhanid',
				type : 'String',
				value : rowData.BENHNHANID
			}, {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			openReport('window', "NTU020_PHIEUDIEUTRI_ALL_KHOA", "pdf", par);
		},
		//dannd_L2PT-4638
		_exportTheoTG_RPT : function(num, rowData) {
			var _self = this;
			var _self = this;
			var _benhnhanid = "-1";
			var _khambenhid = "-1";
			var _khoaid = "-1";
			if (rowData != null) {
				_benhnhanid = rowData.BENHNHANID;
				_khoaid = rowData.KHOAID;
				_khambenhid = rowData.KHAMBENHID;
			}
			// L2PT-10648 duonghn start
			var tungay = $('#' + _self.containerId + 'txtTUNGAY').val();
			var denngay = $('#' + _self.containerId + 'txtDENNGAY').val();
			if (tungay && denngay && compareDate(denngay, tungay, 'DD/MM/YYYY HH:mm:ss')) {
				setErrValidate('toolbarIdtxtFromDate');
				DlgUtil.showMsg('Từ ngày không thể lớn hơn đến ngày');
				return false;
			}
			var tungay2 = $('#' + _self.containerId + 'txtTUNGAY2').val();
			var denngay2 = $('#' + _self.containerId + 'txtDENNGAY2').val();
			if (tungay2 && denngay2 && compareDate(denngay2, tungay2, 'DD/MM/YYYY HH:mm:ss')) {
				setErrValidate('toolbarIdtxtFromDate');
				DlgUtil.showMsg('Từ ngày không thể lớn hơn đến ngày');
				return false;
			}
			var _data = "";
			if (tungay && denngay) {
				var sql_par = [ tungay, denngay, rowData.KHAMBENHID ];
				_data = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D027.LAYDSMBPID", sql_par.join('$'));
			}
			var _data2 = "";
			if (tungay2 && denngay2) {
				var sql_par2 = [ tungay2, denngay2, rowData.KHAMBENHID ];
				_data2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D027.LAYDSMBPID", sql_par2.join('$'));
			}
			if (_data2 == "" || _data2 == undefined || _data2 == null) {
				if (_data == "" || _data == undefined || _data == null) {
					DlgUtil.showMsg("Không có phiếu nào trong các khoảng thời gian này!");
					return;
				}
			}
			// L2PT-10648 duonghn end
			var par = [ {
				name : 'i_benhnhanid',
				type : 'String',
				value : _benhnhanid
			}, {
				name : 'i_khoaid',
				type : 'String',
				value : _khoaid
			}, {
				name : 'i_khambenhid',
				type : 'String',
				value : _khambenhid
			}, {
				name : 'i_maubenhphamids',
				type : 'String',
				value : _data
			} ];
			// L2PT-10648 duonghn start
			if (_self.NTU_INPDT_2KTG == '1') {
				par.push({
					name : 'i_maubenhphamids_2',
					type : 'String',
					value : _data2
				})
			}
			// L2PT-10648 duonghn end
			//var _data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", ';NTU_PDT_IN_RTF');
			sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : rowData.KHOAID
			});
			var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
			var row2 = JSON.parse(data2)[0];
			var loaikhoa = row2.ORG_TYPE;
			if (num == 1) {
				//START L2PT-5287
				if (_self.options._mode_in_LSBA != null && _self.options._mode_in_LSBA == '1') {
					openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_KHOA_MULTI", "pdf", par);
				} else {
					if (_self.NTU_PDT_IN_RTF == '1' && $("#hidLOAITIEPNHANID").val() == '0') {
						var rpName = "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
						CommonUtil.inPhieu('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", 'rtf', par, rpName);
						//openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "rtf", par);
					} else {
						openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI", "pdf", par);
					}
				}
			}
			//L2PT-24725
			else if (num == 2) {
				openReport('window', "TODIEUTRI_MATTRUOC", "pdf", par);
			} else if (num == 3) {
				openReport('window', "TODIEUTRI_MATSAU", "pdf", par);
			}
		},
		_exportToYLenh : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : rowData.BENHNHANID
				}, {
					name : 'i_maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : rowData.KHOAID
				} ];
				openReport('window', "NTU020_TOYLENH_39BV01_QD4069_A4_944", "pdf", par);
			}
		},
		_exportToYLenhTD : function(rowId) {
			var _self = this;
			var lstmaubenhphamid = "";
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowDatas[j]);
					if (rowData != null) {
						lstmaubenhphamid = lstmaubenhphamid + "," + rowData.MAUBENHPHAMID
					}
				}
				lstmaubenhphamid = lstmaubenhphamid.substring(1, lstmaubenhphamid.length);
				var par = [ {
					name : 'i_maubenhphamids',
					type : 'String',
					value : lstmaubenhphamid
				} ];
				openReport('window', "PHIEU_THUCHIENTHUOC", "pdf", par);
			}
		},
//        _checkRoles: function(_nguoitaoid){        	
//        	var _nguoidungid=$("#hidUserID").val();
//        	if(_nguoitaoid==null || _nguoitaoid==''){
//        		return false;
//        	}else{
//        		if(_nguoitaoid==_nguoidungid){
//        			return true;
//        		}else{
//        			return false;
//        		}
//        	}
//        },
		_updateDieutriDetail : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == '1' && (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99')) {
					DlgUtil.showMsg("Phiếu đã thực hiện ký số. Hủy ký số để thực hiện cập nhật phiếu!");
					return false;
				}
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _upd_dienbien = 0;
				//START L2PT-4775
				//var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_PDT_CHECKXOA_USER");
				if (_self.NTU_PDT_CHECKXOA_USER == '2') {
					//không xử lý gì
				} else {
					if (_self.NTU_PDT_CHECKXOA_USER == '1') {
						if (_nguoitaoid != $("#hidUserID").val()) {
							DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
							return false;
						}
					} else {
						if (_checkRoles(_nguoitaoid, $("#hidUserID").val()) == false) {
							//BVTM-7931 cho phep bs khac cap nhat dien bien
							if (_self.NT_PHIEUDTRI_UDP_DIENBIEN == '1') {
								_upd_dienbien = 1;
							} else {
								if (cfObj.NTU_PDT_CHECK_ADMINCA == '1') {
									var _sql_par = [];
									_sql_par.push({
										"name" : "[0]",
										value : $("#hidUserID").val()
									});
									var ret = jsonrpc.AjaxJson.getOneValue("HIS.ADMIN.HUYKY", _sql_par);
									if (ret == '0') {
										DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
										return false;
									}
								} else {
									DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
									return false;
								}
							}
						}
					}
				}
				//mo popup cap nhat phieu dieu tri khi dbclick on row
				var paramInput = {
					khambenhId : rowData.KHAMBENHID,
					maubenhphamId : rowData.MAUBENHPHAMID,
					benhnhanId : $("#hidBENHNHANID").val(),
					tiepnhanId : $("#hidTIEPNHANID").val(),
					hosobenhanId : $("#hidHOSOBENHANID").val(),
					doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
					loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),//L2PT-16629
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					subDeptId : $("#hidPHONGID").val(),
					mode_time : 1,
					upd_dienbien : _upd_dienbien
				// L2PT-7773
				};
				if (cfObj.HIS_PHIEUDT_FORMLV == "1") {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT_LV", paramInput, "Cập nhật phiếu điều trị", 1330, 700);
				} else {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT", paramInput, "Cập nhật phiếu điều trị", 1330, 700);
				}
				EventUtil.setEvent("divDlgPhieuDieuTri_onClose", function(name) {
					//_self._initWidget();
					_self._viewDieuTriDetail(rowId);//HaNv_240723: L2PT-49099
				});
				DlgUtil.open("divDlgPhieuDieuTri");
			}
		},
		_copyPDT : function(rowId) {
			var _self = this;
			//L2PT-71526
			if (cfObj.HIS_PHIEUDT_CPY_BS == "1") {
				var _sql_par = [];
				_sql_par.push({
					"name" : "[0]",
					value : $("#hidUserID").val()
				});
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.MALOAIBS", _sql_par);
				if (ret == '0') {
					DlgUtil.showMsg("User không phải loại bác sỹ hoặc chưa có mã bác sỹ không thể tạo bản sao!");
					return;
				}
			}
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var thoigianNhapvien = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D021.GET_TGVV", $("#hidBENHNHANID").val());
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					ngaymaubenhpham : rowData.NGAYMAUBENHPHAM, // L2PT-41047 duonghn
					type : '4',
					thoigianvaovien : thoigianNhapvien[0].THOIGIANVAOVIEN,//_self.options._thoigianvaovien,
					bacsidieutri : _self.options._bacsidieutri,
					benhnhanid : _self.options._benhnhanid,
					khambenhid : _self.options._khambenhid
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgCopyMbp", "divDlg", "manager.jsp?func=../noitru/NTU02D070_ThoiGianDonThuoc", paramInput, "Tạo bản sao", 800, 360);
				DlgUtil.open("divDlgCopyMbp");
				//mo popup cap nhat phieu dieu tri khi dbclick on row
//				  var paramInput={					
//						  khambenhId : rowData.KHAMBENHID,
//						  maubenhphamId : rowData.MAUBENHPHAMID,
//						  benhnhanId : $("#hidBENHNHANID").val(),
//						  tiepnhanId : $("#hidTIEPNHANID").val(),
//						  hosobenhanId : $("#hidHOSOBENHANID").val(),
//						  doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
//						  loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
//						  subDeptId : $("#hidKHOAID").val()
//					};					
//					
//					dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuDieuTri","divDlg","manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT",paramInput,"Cập nhật phiếu điều trị",1330,620);
//					EventUtil.setEvent("divDlgPhieuDieuTri_onClose",function(name){						
//						_self._initWidget();
//					 });
//					DlgUtil.open("divDlgPhieuDieuTri");	
			}
		},
		//L2K74TW-612 -- Tach phieudt
		_TachPDT : function(rowId) {
			var _self = this;
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas.length > 1) {
				return DlgUtil.showMsg("Chỉ được chọn một phiếu ĐT");
			}
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowDatas[0]);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có muốn Tách phiếu điều trị " + rowData.SOPHIEU + " không ?", function(flag) {
					if (flag) {
						sql_par = [];
						sql_par.push({
							"name" : "[0]",
							"value" : ''
						}, {
							"name" : "[1]",
							"value" : rowData.MAUBENHPHAMID
						});
						jsonrpc.AjaxJson.execute("NTU02D027.01", sql_par);
						DlgUtil.showMsg("Tách thành công!");
					}
				});
			}
		},
		//L2PT-31078
		CapnhatDVNGT : function(rowId) {
			var _self = this;
			var rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
			if (rowDatas.length > 1) {
				return DlgUtil.showMsg("Chỉ được chọn một phiếu ĐT");
			}
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowDatas[0]);
			if (rowData != null) {
				paramInput = {
					benhnhanid : _self.options._benhnhanid,
					khambenhid : _self.options._khambenhid,
					hosobenhanid : _self.options._hosobenhanid,
					lnmbp : _self.options._lnmbp,
					ingop : '0',
					phieudt_id : rowData.MAUBENHPHAMID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D165_Add_PhieuDT", paramInput, "Cập nhật DV ngoại trú", 1100, 600);
				DlgUtil.open("divDlgDeleteXN");
			}
		},
		//L2PT-27042
		_hoanthanhPDT_BDHN : function(rowId, type) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (type == null) {
				retrun;
			}
			if (rowData != null) {
				var mes = type == '2' ? 'Hoàn thành' : 'Gỡ hoàn thành';
				var objData = new Object();
				objData["MAUBENHPHAMID"] = rowData.MAUBENHPHAMID;
				objData["TRANGTHAIPHIEUDIEUTRI"] = type;
				var res = jsonrpc.AjaxJson.ajaxCALL_SP_I('NTU02D027.HT', JSON.stringify(objData));
				if (res > -1) {
					DlgUtil.showMsg(mes + " thành công");
				} else {
					DlgUtil.showMsg(mes + " KHÔNG thành công");
				}
			}
		},
		//L2PT-31839
		_XoaPDT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == '1' && (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99')) {
					DlgUtil.showMsg("Phiếu đã thực hiện ký số. Hủy ký số để thực hiện cập nhật phiếu!");
					return false;
				}
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				//START L2PT-4775
				//var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_PDT_CHECKXOA_USER");
				if (cfObj.NTU_PDT_CHECKXOA_USER == '2') {
					//không xử lý gì
				} else {
					if (_self.NTU_PDT_CHECKXOA_USER == '1') {
						if (_nguoitaoid != $("#hidUserID").val()) {
							DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
							return false;
						}
					} else {
						if (_checkRoles(_nguoitaoid, $("#hidUserID").val()) == false) {
							if (cfObj.NTU_PDT_CHECK_ADMINCA == '1') {
								var _sql_par = [];
								_sql_par.push({
									"name" : "[0]",
									value : $("#hidUserID").val()
								});
								var ret = jsonrpc.AjaxJson.getOneValue("HIS.ADMIN.HUYKY", _sql_par);
								if (ret == '0') {
									DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
									return false;
								}
							} else {
								DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
								return false;
							}
						}
					}
				}
				if (rowData.MAUBENHPHAMID > -1 && confirm("Bạn có thực sự muốn xóa")) {
					var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K015.XOA", rowData.MAUBENHPHAMID);
					if (ret == 1) {
						//L2PT-27828
						DlgUtil.showMsg("Xóa phiếu điều trị thành công", function() {
							//if (_close)
							EventUtil.raiseEvent("treatment_cancel");
						}, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO'));
					} else if (ret == -2) {
						var result_tg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K015.CHECKPDT", rowData.MAUBENHPHAMID);
						if (result_tg != '') {
							DlgUtil.showMsg("Không thể xóa phiếu điều trị đã được sử dụng<br>" + result_tg);
						}
					} else if (ret == -3) {
						DlgUtil.showMsg("Không thể xóa phiếu điều trị đã được ký số");
					} else {
						DlgUtil.showMsg("Xóa phiếu điều trị không thành công");
					}
				}
			}
		},
		_reloadYLenh : function(maubenhphamid) {
			var _self = this;
			var _par = [ maubenhphamid ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K015.YLENH", _par.join('$'));
			if (result != null) {
				CKEDITOR.instances[_self.containerId + _self.YLENH_ID].setData(FormUtil.unescape(result));
			} else {
				CKEDITOR.instances[_self.containerId + _self.YLENH_ID].setData(FormUtil.unescape(''));
			}
		},
		_viewDieuTriDetail : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//clear du lieu truoc khi set
				//L2PT-20786 start
				var chkAllKhoa = false;
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					chkAllKhoa = true;
				} else {
					chkAllKhoa = false;
				}
				FormUtil.clearForm(_self.containerId, _self.containerId);
				if (chkAllKhoa) {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", true);
				} else {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", false);
				}
				// L2PT-20786 end
				// get thong tin dieu tri
				var sql_par1 = [ rowData.MAUBENHPHAMID ];
				var data1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.024.2.DETAIL", sql_par1.join('$'));
				if (data1 != null && data1.length > 0) {
					for (var i = 0; i < data1.length; i++) {
						var row = data1[i];
						if (row["DICHVUTHUCHIENID"] == '1') {
							$("#" + _self.containerId + "txtTOANTHAN").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '2') {
							$("#" + _self.containerId + "txtKHAMBOPHAN").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '3') {
							$("#" + _self.containerId + "txtMACH" + ",#" + _self.containerId + "txtMACH_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '4') {
							$("#" + _self.containerId + "txtNHIETDO" + ",#" + _self.containerId + "txtNHIETDO_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}
						//Beg_HaNv_250324: Hiển thị huyết áp theo thứ tự Hight/low - L2PT-78063
						else if (row["DICHVUTHUCHIENID"] == '5') {
							if (cfObj.PHIEUDT_HAP_HIGHT_LOW == '1') {
								$("#" + _self.containerId + "txtHUYETAP2" + ",#" + _self.containerId + "txtHUYETAP2_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							} else {
								$("#" + _self.containerId + "txtHUYETAP1" + ",#" + _self.containerId + "txtHUYETAP1_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							}
						} else if (row["DICHVUTHUCHIENID"] == '6') {
							if (cfObj.PHIEUDT_HAP_HIGHT_LOW == '1') {
								$("#" + _self.containerId + "txtHUYETAP1" + ",#" + _self.containerId + "txtHUYETAP1_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							} else {
								$("#" + _self.containerId + "txtHUYETAP2" + ",#" + _self.containerId + "txtHUYETAP2_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							}
						}
						//End_HaNv_250324
						else if (row["DICHVUTHUCHIENID"] == '7') {
							$("#" + _self.containerId + "txtNHIPTHO" + ",#" + _self.containerId + "txtNHIPTHO_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '8') {
							$("#" + _self.containerId + "txtCANNANG" + ",#" + _self.containerId + "txtCANNANG_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '9') {
							$("#" + _self.containerId + "txtKETQUACLS").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '10') {
							$("#" + _self.containerId + "txtCHUANDOAN" + ",#" + _self.containerId + "txtCHUANDOAN_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '11') {
							$("#" + _self.containerId + "txtCHUANDOAN" + ",#" + _self.containerId + "txtCHUANDOAN_LVVPC").val(
									FormUtil.unescape(row["GIATRI_KETQUA"]) + '-' + $("#" + _self.containerId + "txtCHUANDOAN").val());
						} else if (row["DICHVUTHUCHIENID"] == '12') {
							$("#" + _self.containerId + "txtXULY" + ",#" + _self.containerId + "txtXULY_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '15') {
							//L2PT-70091
							if (cfObj.NTU_PDT_DIENBIEN_CLOB == 1 && row["KETQUACLS"] != null && row["KETQUACLS"] != '') {
								$("#" + _self.containerId + _self.DIENBIENBENH_ID).val(FormUtil.unescape(row["KETQUACLS"]));
							} else
								$("#" + _self.containerId + _self.DIENBIENBENH_ID).val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '16') {
							$("#" + _self.containerId + "txtBENHKEMTHEO" + ",#" + _self.containerId + "txtBENHKEMTHEO_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '24') {
							$("#" + _self.containerId + "txtSOTO_DT").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '45') {
							$("#" + _self.containerId + "txtCHIEUCAO" + ",#" + _self.containerId + "txtCHIEUCAO_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						} else if (row["DICHVUTHUCHIENID"] == '40') {
							$("#" + _self.containerId + "txtSPO2" + ",#" + _self.containerId + "txtSPO2_LVVPC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}
						//L2PT-102278
						else if (row["DICHVUTHUCHIENID"] == '63') {
							$("#" + _self.containerId + "txtTIENSUBENH_BANTHAN").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '64') {
							$("#" + _self.containerId + "txtSOTHANG_DIUNG").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							if(row["GIATRI_KETQUA"] != undefined && parseInt(row["GIATRI_KETQUA"]) > 0)
								$("#" + _self.containerId + "chkSOTHANG_DIUNG").prop('checked', true);
						}else if (row["DICHVUTHUCHIENID"] == '65') {
							$("#" + _self.containerId + "txtSOTHANG_THUOCLA").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							if(row["GIATRI_KETQUA"] != undefined && parseInt(row["GIATRI_KETQUA"]) > 0)
								$("#" + _self.containerId + "chkSOTHANG_THUOCLA").prop('checked', true);
						}else if (row["DICHVUTHUCHIENID"] == '66') {
							$("#" + _self.containerId + "txtSOTHANG_MATUY").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							if(row["GIATRI_KETQUA"] != undefined && parseInt(row["GIATRI_KETQUA"]) > 0)
								$("#" + _self.containerId + "chkSOTHANG_MATUY").prop('checked', true);
						}else if (row["DICHVUTHUCHIENID"] == '67') {
							$("#" + _self.containerId + "txtSOTHANG_THUOCLAO").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							if(row["GIATRI_KETQUA"] != undefined && parseInt(row["GIATRI_KETQUA"]) > 0)
								$("#" + _self.containerId + "chkSOTHANG_THUOCLAO").prop('checked', true);
						}else if (row["DICHVUTHUCHIENID"] == '68') {
							$("#" + _self.containerId + "txtSOTHANG_RUOUBIA").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							if(row["GIATRI_KETQUA"] != undefined && parseInt(row["GIATRI_KETQUA"]) > 0)
								$("#" + _self.containerId + "chkSOTHANG_RUOUBIA").prop('checked', true);
						}else if (row["DICHVUTHUCHIENID"] == '69') {
							$("#" + _self.containerId + "txtSOTHANG_KHAC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
							if(row["GIATRI_KETQUA"] != undefined && parseInt(row["GIATRI_KETQUA"]) > 0)
								$("#" + _self.containerId + "chkSOTHANG_KHAC").prop('checked', true);
						}else if (row["DICHVUTHUCHIENID"] == '70') {
							$("#" + _self.containerId + "txtTIENSUBENH_GIADINH").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '71') {
							$("#" + _self.containerId + "txtTUANHOAN").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '72') {
							$("#" + _self.containerId + "txtHOHAP").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '73') {
							$("#" + _self.containerId + "txtTIEUHOA").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '74') {
							$("#" + _self.containerId + "txtTHAN_TIETLIEU").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '75') {
							$("#" + _self.containerId + "txtTHANKINH").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '76') {
							$("#" + _self.containerId + "txtCO_XUONGKHOP").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '77') {
							$("#" + _self.containerId + "txtTAI_MUI_HONG").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '78') {
							$("#" + _self.containerId + "txtRANG_HAM_MAT").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '79') {
							$("#" + _self.containerId + "txtMAT").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '80') {
							$("#" + _self.containerId + "txtNOITIET_DD_BLK").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}
						//
						else if (row["DICHVUTHUCHIENID"] == '81') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radTINHTAO][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '82') {
							$("#" + _self.containerId + "txtGLASGOW").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '83') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radDAUBUNG][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '84') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radVITRIDAU][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '85') {
							$("#" + _self.containerId + "txtVITRIKHAC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '86') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radSOT][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '87') {
							$("#" + _self.containerId + "txtNHIETDO1").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '88') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radOI][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '89') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radNHUCDAU][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '90') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radCHONGMAT][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '91') {
							if(row["GIATRI_KETQUA"])
								$("input[name=radTIEUCHAY][value=" + FormUtil.unescape(row["GIATRI_KETQUA"]) + "]").attr('checked', 'checked');
						}else if (row["DICHVUTHUCHIENID"] == '92') {
							$("#" + _self.containerId + "txtTIEUCHAYLAN").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}else if (row["DICHVUTHUCHIENID"] == '93') {
							$("#" + _self.containerId + "txtTRIEUCHUNGKHAC").val(FormUtil.unescape(row["GIATRI_KETQUA"]));
						}
						
						
						//L2PT-27562
						if (row["DICHVUTHUCHIENID"] == '29') {
							if (cfObj.NTU_PDT_YLENH_PHIEUIN == '1' && _self.NTU_PDT_TT_DKBTN == '1') {//HaNv_191223: L2PT-67234
								_self._reloadYLenh(rowData.MAUBENHPHAMID);
							}
							//Beg_HaNv_031123: L2PT-59396
							if (cfObj.NTU_PDT_YLENH_LVVPC == 1 && row["KETQUACLS"] != null && row["KETQUACLS"] != '') {
								if (_self.NTU_PDT_TT_DKBTN == '1') {
									CKEDITOR.instances[_self.containerId + _self.YLENH_ID].setData(FormUtil.unescape(row["KETQUACLS"]));
								} else {
									$("#" + _self.containerId + _self.YLENH_ID).val(FormUtil.unescape(row["KETQUACLS"]));
								}
							}
							//End_HaNv_031123
							else if (row["GIATRI_KETQUA"] != null && row["GIATRI_KETQUA"] != '') {
								if (_self.NTU_PDT_TT_DKBTN == '1') {
									CKEDITOR.instances[_self.containerId + _self.YLENH_ID].setData(FormUtil.unescape(row["GIATRI_KETQUA"]));
								} else {
									$("#" + _self.containerId + _self.YLENH_ID).val(FormUtil.unescape(row["GIATRI_KETQUA"]));
								}
							} else {
								if (_self.NTU_PDT_TT_DKBTN == '1') {
									_self._reloadYLenh(rowData.MAUBENHPHAMID);
								}
							}
						}
					}
				}
			}
		},
		// Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
		destroy : function() {
			// this.element.removeStuff();
			// For UI 1.8, destroy must be invoked from the
			// base widget
			$.Widget.prototype.destroy.call(this);
			// For UI 1.9, define _destroy instead and don't
			// worry about
			// calling the base widget
		},
		methodB : function(event) {
			//_trigger dispatches callbacks the plugin user
			// can subscribe to
			// signature: _trigger( "callbackName" , [eventObject],
			// [uiObject] )
			// eg. this._trigger( "hover", e /*where e.type ==
			// "mouseenter"*/, { hovered: $(e.target)});
			console.log("methodB called");
		},
		methodA : function(event) {
			this._trigger("dataChanged", event, {
				key : "someValue"
			});
		},
		// Respond to any changes the user makes to the
		// option method
		_setOption : function(key, value) {
			switch (key) {
				case "someValue":
					//this.options.someValue = doSomethingWith( value );
				break;
				default:
					//this.options[ key ] = value;
				break;
			}
			// For UI 1.8, _setOption must be manually invoked
			// from the base widget
			$.Widget.prototype._setOption.apply(this, arguments);
			if (key == '_benhnhanid') {
				this._initWidget();
			}
			// For UI 1.9 the _super method can be used instead
			// this._super( "_setOption", key, value );
		}
	});
})(jQuery);
