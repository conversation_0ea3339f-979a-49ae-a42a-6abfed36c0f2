<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>

<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">       

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>                     
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>  
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>   
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>

<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../canlamsang/CLS02C011_DoLoangXuong.js?v=20210620"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=201810302" ></script>

<div class="" id="cls_doloangxuong">
	<input type="hidden" name="hidKHAMBENHID" id="hidKHAMBENHID" value="" />
	<input type="hidden" name="hidHOSOBENHANID" id="hidHOSOBENHANID" value="" />
	<input type="hidden" name="hidBENHNHANID" id="hidBENHNHANID" value="" />
	<input type="hidden" name="hidDICHVUKHAMBENHID" id="hidDICHVUKHAMBENHID" value="" />
	<input type="hidden" name="hidKETQUACLSID" id="hidKETQUACLSID" value="" />
	<div id="cls_form_input">
		<input type="hidden" name="hidDOLOANGXUONGID" id="hidDOLOANGXUONGID" value="" />
		<div id="dvSec1" style="background-color: white; padding: 5px; border: 1px solid #DCDBDA;">
			<div class="form-inline">
				<div class="col-xs-12" style="padding-top: 5px;">
					<div class="col-xs-2">
						<label>Ngày đăng ký</label>
					</div>
					<div class="col-xs-4">
						<div class="input-group" style="width: 100%">
							<input type="text" class="form-control input-sm" id="txtNGAYDANGKY" valrule="Giờ ngày,datetime"
								   name="txtNGAYDANGKY" data-mask="00/00/0000 00:00:00" minlength="19"
								   placeholder="dd/MM/yyyy HH24:mm:ss" placeholder="">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="imgFROM_DT" type="sCal"
								  onclick="NewCssCal('txtNGAYDANGKY','ddMMyyyy','dropdown',true,'24',true)"></span>

						</div>
					</div>
					<div class="col-xs-2">
						<label>Bác sĩ</label>
					</div>
					<div class="col-xs-4">
						<div class="input-group" style="width: 100%;">
							<input class="form-control input-sm" id="txtBACSI" style="width: 20%">
							<select class="form-control input-sm deletewhennull" id="cboBACSIID" style="width: 62%">
								<option value="-1">--Lựa chọn--</option>
							</select>
							<button type="button" class="btn btn-sm btn-primary" id="btnCLEARBACSI" modeDisXT="">
								<span class="glyphicon glyphicon-remove"></span>
							</button>
						</div>
					</div>
				</div>

				<div  class="col-xs-12" style="padding-top: 5px;">
					<div class="col-xs-2">
						<label>Chiều cao</label>
					</div>
					<div  class="col-xs-4" >
						<input class="form-control input-sm" id="txtCHIEUCAO" style="width: 100%" valrule="Chiều cao,numeric|max_length[6]">
					</div>
					<div class="col-xs-2">
						<label>Cân nặng</label>
					</div>
					<div  class="col-xs-4" >
						<input class="form-control input-sm" id="txtCANNANG" style="width: 100%" valrule="Cân nặng,numeric|max_length[6]"/>
					</div>
				</div>
				<div  class="col-xs-12" style="padding-top: 5px;">
					<div class="col-xs-2">
						<label>Dụng cụ hỗ trợ bàn chân</label>
					</div>
					<div  class="col-xs-4" >
						<input class="form-control input-sm" id="txtCAIHOTROBANCHAN" style="width: 100%" value="1">
					</div>
					<div class="col-xs-6">
					</div>
				</div>
			</div>
		</div>
		<div id="dvSec2" style="background:-webkit-linear-gradient(top, #EEE 0%,#DCDBDA 100%);padding:5px;border-radius:5px 5px 0px 0px;height:25px">
			Kết quả
		</div>

		<div id="dvSec4">
			<div class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
					<label>Thời gian chụp</label>
				</div>
				<div class="col-xs-4">
					<div class="input-group" style="width: 100%">
						<input type="text" class="form-control input-sm" id="txtTHOIGIANCHUP" valrule="Giờ ngày,datetime"
							   name="txtTHOIGIANCHUP" data-mask="00/00/0000 00:00:00" minlength="19"
							   placeholder="dd/MM/yyyy HH24:mm:ss" placeholder="">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="imgFROM_DT" type="sCal"
							  onclick="NewCssCal('txtTHOIGIANCHUP','ddMMyyyy','dropdown',true,'24',true)"></span>

					</div>
				</div>
				<div class="col-xs-2">
					<label>BQI</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtBQI" style="width: 100%">
				</div>
			</div>
			<div  class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
					<label>Vị trí</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtVITRI" style="width: 100%" value="Bàn chân phải">
				</div>
				<div class="col-xs-2">
					<label>T-radio</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtT_RADIO" style="width: 100%"/>
				</div>
			</div>
			<div  class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
					<label>T-score</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtT_SCORE" style="width: 100%">
				</div>
				<div class="col-xs-2">
					<label>Z-radio</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtZ_RADIO" style="width: 100%"/>
				</div>
			</div>
			<div  class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
					<label>Z-score</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtZ_SCORE" style="width: 100%">
				</div>
				<div class="col-xs-2">
					<label>BUA[dB/MHZ]</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtBJA" style="width: 100%"/>
				</div>
			</div>
			<div  class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
					<label>SOS[m/s]</label>
				</div>
				<div  class="col-xs-4" >
					<input class="form-control input-sm" id="txtSOS" style="width: 100%">
				</div>
				<div  class="col-xs-2" >
					<label class="mgl5">Tộc người</label>
				</div>
				<div  class="col-xs-4" >
					<select class="form-control input-sm" id="cboTOCNGUOIID" modeDis="" style="width: 100%; float: right;">
					</select>
				</div>
			</div>
			<div  class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
					<label>Chú thích</label>
				</div>
				<div  class="col-xs-10" >
					<textarea rows="6" id="txtCHUTHICH" style="width: 100%; border-color: #ccc;height: " maxlength="1000"></textarea>
				</div>
			</div>
		</div>
		<div id="dvSec5">
			<div class="col-xs-12" style="padding-top: 5px;">
				<div class="col-xs-2">
				</div>
				<div class="col-xs-2">
					<div class="radio">
						<label><input type="radio" name="radLOANGXUONG"
									  value="1" id="radLOANGXUONG"></label><label
							style="font-size: 0.9em;">Bình thường</label>
					</div>
				</div>
				<div class="col-xs-2">
					<div class="radio">
						<label><input type="radio" name="radLOANGXUONG"
									  value="2" id="radLOANGXUONG"></label><label
							style="font-size: 0.9em;">Tiền loãng xương</label>
					</div>
				</div>
				<div class="col-xs-2">
					<div class="radio">
						<label><input type="radio" name="radLOANGXUONG"
									  value="3" id="radLOANGXUONG"></label><label
							style="font-size: 0.9em;">Loãng xương</label>
					</div>
				</div>
				<div class="col-xs-2">
				</div>
			</div>
		</div>
	</div>
	
	<div  class="col-xs-12" style="text-align:center; padding-top: 20px;">
		<div class="form-inline">
			<button class="btn btn-sm btn-primary" type="submit" id="btnCapNhat" >
				<span class="glyphicon glyphicon-floppy-disk"></span> Lưu kết quả
			</button>
			<button class="btn btn-sm btn-primary" type="submit" id="btnCapNhatClose" >
				<span class="glyphicon glyphicon-floppy-disk"></span> Lưu & Đóng
			</button>
			<button class="btn btn-sm btn-primary" type="submit" id="btnXoa">
				<span class="glyphicon glyphicon-erase"></span> Xóa
			</button>
			<button class="btn btn-sm btn-primary" type="submit" id="btnInPhieu">
				<span class="glyphicon glyphicon-print"></span> In Phiếu
			</button>
			<button class="btn btn-sm btn-primary" type="submit" id="btnKySo" >
				<span class="glyphicon glyphicon-floppy-disk"></span>Ký số
			</button>
			<button class="btn btn-sm btn-primary" type="submit" id="btnHuyKy" >
				<span class="glyphicon glyphicon-floppy-disk"></span>Hủy ký
			</button>
			<button class="btn btn-sm btn-primary" type="submit" id="btnInKySo">
				<span class="glyphicon glyphicon-print"></span> In Phiếu ký số
			</button>
			<button class="btn btn-sm btn-primary" type="button" id="btnHuy">
				 <span class="glyphicon glyphicon-remove-circle"></span> Đóng
			</button>


		</div>
	</div>
</div>

<script type="text/javascript">
	var uuid = '{uuid}';
	var khoaid = '{dept_id}';
	var user_id = '{user_id}';
	var hospital_id = '{hospital_id}';
	var user_name = '{user_name}';
	var full_name = '{full_name}';
	initRest(uuid,"/vnpthis");
	
	var mode = '{showMode}';	
	var data;
	var subdept_name = '{subdept_name}';
	var _opts=new Object();
	if(mode=='dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		_opts.khambenhid = data.khambenhid;
		_opts.hosobenhanid = data.hosobenhanid;
		_opts.benhnhanid = data.benhnhanid;
		_opts.dichvukhambenhid = data.dichvukhambenhid;
		_opts.ngaychidinh = data.ngaychidinh;
		_opts.ketquaclsid = data.ketquaclsid;
		_opts.user_id = user_id;
		_opts.user_name = user_name;
		_opts.full_name = full_name;

	}
	var bn = new cls004_doloangxuong(_opts);
	bn.load();
</script>
