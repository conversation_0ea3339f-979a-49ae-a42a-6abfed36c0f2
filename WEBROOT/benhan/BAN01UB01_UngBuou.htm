<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>


<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jquery/jquery.confirm.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
			<!-- <link rel="stylesheet" href="../noitru/custom_nt.css"> -->
<link rel="stylesheet" href="../common/css/css_style.css">
<link rel="stylesheet" href="../common/css/custom_ban.css"></link>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../noitru/jquery.bootstrap-growl.min.js"></script>
<script type="text/javascript" src="../benhan/BAN_Common.js?v=17"></script>
<script type="text/javascript" src="../noitru/NTU02D023_Thongtinhanhchinh.js"></script>
<script type="text/javascript" src="../benhan/BAN01UB01_UngBuou.js?v=6"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>


<div width="100%" id="divMain-BA" style="border: 0px;">
	<ul id="tabs" class="nav nav-tabs" style="margin-top: 10px;" data-tabs="tabs">
		<li class="active"><a href="#tabBenhAnHoiKham" data-toggle="tab">A.Bệnh án hỏi khám bệnh</a></li>
		<li><a href="#tabBenhAnTongKet" data-toggle="tab">B.Tổng kết hồ sơ bệnh án</a></li>
		<li id="liBenhNhanThongTin"><a href="#tabBenhNhanThongTin" data-toggle="tab">C.Thông tin bệnh nhân</a></li>
	</ul>

	<div id="my-tab-content" class="tab-content">
		<div><a href="#" class="scrollToTop before"></a></div>   
		<div id="tabBenhAnHoiKham" class="tab-pane active">
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">I. Lý do vào viện:</div>
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group required">
							<label>Lý do vào viện:</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtLYDOVAOVIEN"
								maxlength="500" name="txtLYDOVAOVIEN" title="" valrule="Lý do vào viện,trim_required|max_length[500]"
								style="width: 100%;">
						</div>
						
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">II. Hỏi bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Quá trình bệnh
								lý:(khởi phát, diễn biến, chẩn đoán, điều trị của tuyến dưới
								v.v...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLY" valrule="Quá trình bệnh lý,max_length[500]"
								class="form-control input-sm i-col-3" id="txtQTBENHLY"
								style="height: 80px !important; width: 100%" maxlength="500"
								rows="4"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>2. Tiền sử bệnh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Bản thân: (phát
								triển thể lực từ nhỏ đến lớn, những bệnh đã mắc, phương pháp
								ĐTr, tiêm phòng, ăn uống, sinh hoạt vv...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtBANTHAN" valrule="Bản thân,max_length[500]"
								class="form-control input-sm i-col-3" id="txtBANTHAN"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>Đặc điểm liên quan
								bệnh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th style="font-size: x-small; text-align: center;">TT</th>
										<th style="font-size: x-small; text-align: center;">Kí
											hiệu</th>
										<th style="font-size: x-small; text-align: center;">Thời
											gian(tính theo tháng)</th>
										<th style="font-size: x-small; text-align: center;">TT</th>
										<th style="font-size: x-small; text-align: center;">Kí
											hiệu</th>
										<th style="font-size: x-small; text-align: center;">Thời
											gian(tính theo tháng)</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>01</td>
										<td style="width: 20%"><label
											class='l-col-1 control-label'>Dị ứng</label> <input
											class="form-control input-sm i-col-3" type="checkbox"
											id="chkDACDIEMLQB21" name="chkDACDIEMLQB21" title=""></td>
										<td><label class='l-col-1 control-label'><input
												class="form-control input-sm i-col-3" id="txtDACDIEMLQB31"
												maxlength="500" name="txtDACDIEMLQB31" title=""></label></td>
										<td>04</td>
										<td style="width: 20%"><label
											class='l-col-1 control-label'>Thuốc lá</label> <input
											class="form-control input-sm i-col-3" type="checkbox"
											id="chkDACDIEMLQB51" name="chkDACDIEMLQB51" title=""></td>
										<td><input class="form-control input-sm i-col-3" 
											valrule="Thời gian(tính theo tháng),is_natural|max_length[2]"
											id="txtDACDIEMLQB61" maxlength="2" name="txtDACDIEMLQB61"
											title=""></td>
									</tr>
									<tr>
										<td>02</td>
										<td><label class='l-col-1 control-label'>Ma túy</label>
											<input class="form-control input-sm i-col-3" type="checkbox"
											id="chkDACDIEMLQB22" name="chkDACDIEMLQB22" title=""></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Thời gian(tính theo tháng),is_natural|max_length[2]"
											id="txtDACDIEMLQB32" maxlength="2" name="txtDACDIEMLQB32"
											title=""></td>
										<td>05</td>
										<td><label class='l-col-1 control-label'>Thuốc
												lào</label> <input class="form-control input-sm i-col-3"
											type="checkbox" id="chkDACDIEMLQB52" name="chkDACDIEMLQB52"
											title=""></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Thời gian(tính theo tháng),is_natural|max_length[2]"
											id="txtDACDIEMLQB62" maxlength="2" name="txtDACDIEMLQB62"
											title=""></td>
									</tr>
									<tr>
										<td>03</td>
										<td><label class='l-col-1 control-label'>Rượu
												bia</label> <input class="form-control input-sm i-col-3"
											type="checkbox" id="chkDACDIEMLQB23" name="chkDACDIEMLQB23"
											title=""></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Thời gian(tính theo tháng),is_natural|max_length[2]"
											id="txtDACDIEMLQB33" maxlength="2" name="txtDACDIEMLQB33"
											title=""></td>
										<td>06</td>
										<td><label class='l-col-1 control-label'>Khác</label> <input
											class="form-control input-sm i-col-3" type="checkbox"
											id="chkDACDIEMLQB53" name="chkDACDIEMLQB53" title=""></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Thời gian(tính theo tháng),is_natural|max_length[2]"
											id="txtDACDIEMLQB63" maxlength="2" name="txtDACDIEMLQB63"
											title=""></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Gia đình: (Những
								người trong gia đình: bệnh đã mắc, đời sống, tinh thần, vật
								chất v.v...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtGIADINH" valrule="Gia đình,max_length[500]"
								class="form-control input-sm i-col-3" id="txtGIADINH"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div>
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">III. Khám bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label mgb-t5'>1. Toàn thân: (ý
								thức, da niêm mạc, hệ thống hạch, tuyến giáp, vị trí, kích
								thước, số lượng, di động v.v...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-9 low-padding">
							<textarea name="txtTOANTHAN" valrule="Toàn thân,max_length[1000]"
								class="form-control input-sm i-col-3" id="txtTOANTHAN"
								style="height: 130px !important; width: 100%" maxlength="1000"
								rows="3"></textarea>
						</div>
						
						<div class="col-xs-3 low-padding">
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class="mgl10">Mạch</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input class="col-xs-4 form-control input-sm " style="width: 100%;" id="txtMACH" name="txtMACH" valrule="Mạch,is_natural|max_length[3]" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhiệt độ:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhiệt độ,numeric|max_length[6]" maxlength="6"
												class="form-control input-sm " style="width: 100%;"
												id="txtNHIETDO" name="txtNHIETDO" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>C</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Huyết áp:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Huyết áp,is_natural|max_length[3]"
												class="form-control input-sm " style="width: 45%;"
												id="txtHUYETAP1" name="txtHUYETAP1" title=""> /
									 <input class="form-control input-sm " style="width: 45%; float: right;"
												valrule="Huyết áp,is_natural|max_length[3]"
												id="txtHUYETAP2" name="txtHUYETAP2" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>mmHg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhịp thở:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhịp thở,is_natural|max_length[3]"
									class="form-control input-sm " style="width: 100%;"
									id="txtNHIPTHO" name="txtNHIPTHO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Cân nặng:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Cân nặng,numeric|max_length[6]" maxlength="6"
									class="form-control input-sm " style="width: 100%;"
									id="txtCANNANG" name="txtCANNANG" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>kg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chiều cao:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Chiều cao,numeric|max_length[4]"
										   class="form-control input-sm " style="width: 100%;" maxlength="4"
										   id="txtCHIEUCAO" name="txtCHIEUCAO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>cm</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chỉ số BMI:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<label id="txtBMI" name="txtBMI" title=""></label>
								</div>
							</div>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>2. Bộ phận tổn thương:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtBOPHANTONTHUONG" valrule="Bộ phận tổn thương,max_length[500]"
								class="form-control input-sm i-col-3" id="txtBOPHANTONTHUONG"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Các cơ quan:</label>
						</div>
					</div>
				<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Thần kinh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTHANKINH" valrule="Thần kinh,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTHANKINH"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Tuần hoàn:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTUANHOAN" valrule="Tuần hoàn,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTUANHOAN"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Hô hấp:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHOHAP" class="form-control input-sm i-col-3"
								valrule="Hô hấp,max_length[500]"
								id="txtHOHAP" style="height: 50px !important; width: 100%"
								maxlength="500" rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Tiêu hóa:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTIEUHOA" valrule="Tiêu hóa,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTIEUHOA"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Cơ -Xương -Khớp:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtCOXUONGKHOP" valrule="Cơ -Xương -Khớp,max_length[500]"
								class="form-control input-sm i-col-3" id="txtCOXUONGKHOP"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Tiết niệu:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTIETNIEU" valrule="Tiết niệu,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTIETNIEU"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Sinh dục:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtSINHDUC" valrule="+ Sinh dục,max_length[500]"
								class="form-control input-sm i-col-3" id="txtSINHDUC"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Khác:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtKHAC" class="form-control input-sm i-col-3"
								valrule="Khác,max_length[500]"
								id="txtKHAC" style="height: 50px !important; width: 100%"
								maxlength="500" rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Các xét nghiệm cận lâm sàng
								cần làm:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtXETNGHIEMCANLAMSANG" 
								valrule="Các xét nghiệm cận lâm sàng cần làm,max_length[500]"
								class="form-control input-sm i-col-3"
								id="txtXETNGHIEMCANLAMSANG"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Tóm tắt bệnh án:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTOMTATBENHAN" valrule="Tóm tắt bệnh án,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTOMTATBENHAN"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div>

			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">IV. Chẩn đoán khi vào khoa điều trị:</div>
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>+ Bệnh chính:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHCHINH" name="txtMABENHCHINH" valrule="Mã bệnh chính,max_length[500]" title="" attrIcd="0" targetCtr="txtBENHCHINH">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHCHINH" name="txtBENHCHINH" valrule="Tên bệnh chính,max_length[500]" title="">
						</div>
					</div>
				</div>
				
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Bệnh kèm theo:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHKEMTHEO" name="txtMABENHKEMTHEO" valrule="Mã bệnh kèm theo,max_length[500]" title="" attrIcd="1" targetCtr="txtBENHKEMTHEO">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHKEMTHEO" name="txtBENHKEMTHEO" valrule="Tên bệnh kèm theo,max_length[500]" title="">
						</div>
					</div>
				</div>
			
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Phân biệt:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMAPHANBIET" name="txtMAPHANBIET" valrule="Mã phân biệt,max_length[500]" title="">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtPHANBIET" name="txtPHANBIET" valrule="Tên phân biệt,max_length[500]" title="">
						</div>
					</div>
				</div>
			</div>

			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">V. Tiên lượng:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTIENLUONG"
								valrule="Tiên lượng,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTIENLUONG"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div>

			<div class="form-inline panel panel-info" style="margin-bottom: 65px;">
				<div class="panel-heading">VI. Hướng dẫn điều trị:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHUONGDIEUTRI" valrule="Hướng dẫn điều trị,max_length[500]"
								class="form-control input-sm i-col-3" id="txtHUONGDIEUTRI"
								style="height: 50px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div>		
		</div>
		<div id="tabBenhAnTongKet" class="tab-pane">
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">TỔNG KẾT BỆNH ÁN:</div>
				<div class="panel-body">
					
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>1. Quá trình bệnh lý và diễn biến
								lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLYDIENBIENLS"
								valrule="Quá trình bệnh lý và diễn biến lâm sàng,max_length[500]"
								class="form-control input-sm i-col-3"
								id="txtQTBENHLYDIENBIENLS"
								style="height: 70px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>2. Tóm tắt kết quả xét nghiệm cận
								lâm sàng có giá trị chẩn đoán:</label>
						</div>
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>- XN máu: </label>	
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" id="txtXNMAU"
								maxlength="500" name="txtXNMAU" title="" valrule="- XN máu,max_length[500]"
								style="width: 100%;">
						</div>
						<div class="col-xs-1 low-padding" style="text-align: left;">
							<button id="btnKQCLS_XNMAU" class="btn btn-sm btn-primary" >Chọn KQCLS</button>
						</div>
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-6 low-padding"></div>
							<div class="col-xs-6 low-padding">	
								<label>- X quang: </label>
							</div>
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" id="txtXQUANG"
								maxlength="500" name="txtXQUANG" title="" valrule="- X quang,max_length[500]"
								style="width: 100%;">
						</div>
						<div class="col-xs-1 low-padding" style="text-align: left;">
							<button id="btnKQCLS_XQUANG" class="btn btn-sm btn-primary" >Chọn KQCLS</button>
						</div>
					</div>
						<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>- XN tế bào: </label>
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" id="txtXNTEBAO"
								maxlength="500" name="txtXNTEBAO" title="" valrule="- XN tế bào,max_length[500]"
								style="width: 100%;">
						</div>
							<div class="col-xs-1 low-padding" style="text-align: left;">
								<button id="btnKQCLS_XNTB" class="btn btn-sm btn-primary" >Chọn KQCLS</button>
							</div>
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-6 low-padding"></div>
							<div class="col-xs-6 low-padding">
								<label>- Siêu âm: </label>
							</div>
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" id="txtSIEUAM"
								maxlength="500" name="txtSIEUAM" title="" valrule="- Siêu âm,max_length[500]"
								style="width: 100%;">
						</div>
							<div class="col-xs-1 low-padding" style="text-align: left;">
								<button id="btnKQCLS_SIEUAM" class="btn btn-sm btn-primary" >Chọn KQCLS</button>
							</div>
					</div>
						<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>- XN BLGP: </label>
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" id="txtXNBLGP"
								maxlength="500" name="txtXNBLGP" title="" valrule="- XN BLGP,max_length[500]"
								style="width: 100%;">
						</div>
							<div class="col-xs-1 low-padding" style="text-align: left;">
								<button id="btnKQCLS_XNBLGP" class="btn btn-sm btn-primary" >Chọn KQCLS</button>
							</div>
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-6 low-padding"></div>
							<div class="col-xs-6 low-padding">	
								<label>- Các XN khác: </label>
							</div>
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" id="txtXNKHAC"
								maxlength="500" name="txtXNKHAC" title="" valrule="- Các XN khác,max_length[500]"
								style="width: 100%;">
						</div>
							<div class="col-xs-1 low-padding" style="text-align: left;">
								<button id="btnKQCLS_XNKHAC" class="btn btn-sm btn-primary" >Chọn KQCLS</button>
							</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Phương pháp điều trị:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-2 low-padding"></div>
						<div class="col-xs-2 low-padding">
							<input class="" type="checkbox"
								id="chkDIEUTRITRIETDE" name="chkDIEUTRITRIETDE" title="">
							<label class='control-label'>1. Điều trị triệt để:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="" type="checkbox"
								id="chkDIEUTRITRIEUCHUNG" name="chkDIEUTRITRIEUCHUNG" title="">
							<label class='control-label'>2. Điều trị triệu chứng:</label> 
						</div>
					</div>
					
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-8 low-padding">
								<label>- Tia xạ tiền phẫu:</label>
							</div>
							<div class="col-xs-4 low-padding">
								<label>tại u:  </label>
							</div>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtTIENPHAUTAIU"
								maxlength="100" name="txtTIENPHAUTAIU" title="" valrule="Tia xạ tiền phẫu tại u,max_length[100]"
								style="width: 100%;">
						</div>
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-6 low-padding">
								<label class="mgl5">Gy</label>
							</div>
							<div class="col-xs-6 low-padding">
								<label>Tại hạch: </label>
							</div>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtTIENPHAUTAIHACH"
								maxlength="100" name="txtTIENPHAUTAIHACH" title="" valrule="Tia xạ tiền phẫu tại hạch,max_length[100]"
								style="width: 100%;">
						</div>						
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-8 low-padding">
								<label>- Tia xạ đơn thuần:</label>
							</div>
							<div class="col-xs-4 low-padding">
								<label>tại u:  </label>
							</div>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtDONTHUANTAIU"
								maxlength="100" name="txtDONTHUANTAIU" title="" valrule="Tia xạ đơn thuần tại u,max_length[100]"
								style="width: 100%;">
						</div>
						<div class="col-xs-2 low-padding">
							<div class="col-xs-6 low-padding">
								<label class="mgl5">Gy</label>
							</div>
							<div class="col-xs-6 low-padding">
								<label>Tại hạch: </label>
							</div>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtDONTHUANTAIHACH"
								maxlength="100" name="txtDONTHUANTAIHACH" title="" valrule="Tia xạ đơn thuần tại hạch,max_length[100]"
								style="width: 100%;">
						</div>						
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding  ">
							<label>- Phẫu thuật (tên):</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtPHAUTHUATTEN"
								maxlength="500" name="txtPHAUTHUATTEN" title="" valrule="Phẫu thuật,max_length[500]"
								style="width: 100%;">
						</div>						
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-8 low-padding">
								<label>- Tia xạ hậu phẫu:</label>
							</div>
							<div class="col-xs-4 low-padding">
								<label>tại u:  </label>
							</div>	
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtHAUPHAUTAIU"
								maxlength="100" name="txtHAUPHAUTAIU" title="" valrule="Tia xạ hậu phẫu tại u,max_length[100]"
								style="width: 100%;">
						</div>
						<div class="col-xs-2 low-padding form-group ">
							<div class="col-xs-6 low-padding">
								<label class="mgl5">Gy</label>
							</div>
							<div class="col-xs-6 low-padding">
								<label>Tại hạch: </label>
							</div>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtHAUPHAUTAIHACH"
								maxlength="100" name="txtHAUPHAUTAIHACH" title="" valrule="Tia xạ hậu phẫu tại hạch,max_length[100]"
								style="width: 100%;">
						</div>						
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>- Hoá chất (phác đồ):    </label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtHOACHATPHACDO"
								maxlength="500" name="txtHOACHATPHACDO" title="" valrule="Hoá chất phác đồ,max_length[500]"
								style="width: 100%;">
						</div>						
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label class="mgl5">Số đợt:</label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtSODOT"
								maxlength="500" name="txtSODOT" title="" valrule="Số đợt,max_length[500]"
								style="width: 100%;">
						</div>						
					</div>
					<div class=" form-inline">
						<div class="col-xs-2 low-padding">
							<label class='mgl5'>Đáp ứng</label>
						</div>
						
						<div class="col-xs-2 low-padding">
							<input class="" type="checkbox"
								id="chkKHONGDAPUNG" name="chkKHONGDAPUNG" title="">
							<label class=' control-label'>1. Không đáp ứng</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="" type="checkbox"
								id="chkBANPHAN" name="chkBANPHAN" title="">
							<label class=' control-label'>2. Bán phần</label> 
						</div>
						<div class="col-xs-2 low-padding">
							<input class="" type="checkbox"
								id="chkHOANTOAN" name="chkHOANTOAN" title="">
							<label class=' control-label'>3. Hoàn toàn</label> 
						</div>
					</div>
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group ">
							<label>- Điều trị khác: </label>
						</div>
						<div class="col-xs-10 low-padding">
							<input class="form-control input-sm" id="txtDIEUTRIKHAC"
								maxlength="500" name="txtDIEUTRIKHAC" title="" valrule="Điều trị khác,max_length[500]"
								style="width: 100%;">
						</div>						
					</div>

					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Tình trạng người bệnh ra viện:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTTNGUOIBENHRAVIEN" 
								valrule="Tình trạng người bệnh ra viện,max_length[500]"
								class="form-control input-sm i-col-3" id="txtTTNGUOIBENHRAVIEN"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Hướng điều trị và các chế độ
								tiếp theo:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHUONGDIEUTRICHEDO"
								valrule="Hướng điều trị và các chế độ tiếp theo,max_length[500]"
								class="form-control input-sm i-col-3" id="txtHUONGDIEUTRICHEDO"
								style="height: 60px !important; width: 100%" maxlength="500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-1 low-padding" style="width: 2px"></div>
						<div class="col-xs-11 low-padding" style="width: 100%; margin-bottom: 35px;">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th colspan="2"
											style="font-size: x-small; text-align: center;">Hồ sơ,
											phim, ảnh</th>
									</tr>
									<tr>
										<th style="font-size: x-small; text-align: center;">Loại</th>
										<th style="font-size: x-small; text-align: center;">Số
											tờ</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td><label class='l-col-1 control-label'>- X -
												quang</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số X - Quang,is_natural|max_length[5]"
											id="txtSOXQUANG" name="txtSOXQUANG" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- CT
												Scanner</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số CT Scanner,is_natural|max_length[5]"
											id="txtSOSCANNER" name="txtSOSCANNER" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Siêu
												âm</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số Siêu âm,is_natural|max_length[5]"
											id="txtSOSIEUAM" name="txtSOSIEUAM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Xét
												nghiệm</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số Xét nghiệm,is_natural|max_length[5]"
											id="txtSOXETNGHIEM" name="txtSOXETNGHIEM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>-
												Khác……………</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số khác,is_natural|max_length[5]"
											id="txtSOKHAC" name="txtSOKHAC" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Toàn
												bộ hồ sơ</label></td>
										<td><input class="form-control input-sm i-col-3"
											id="txtSOTOANBOHOSO" name="txtSOTOANBOHOSO" title=""
											disabled="disabled"></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div id="tabBenhNhanThongTin" class="tab-pane"></div>
		
		<div class="form-inline btn-fixed">
			<div class="col-xs-12 low-padding mgt20 mgb10" style="text-align: center;" id="gridButton">
				<button class="btn btn-sm btn-primary" id="btnLuu">
					<span class="glyphicon glyphicon-pencil"></span> Lưu
				</button>
				<button class="btn btn-sm btn-primary" id="btnThemMoi">
					<span class="glyphicon glyphicon-floppy-remove"></span> Lưu & Đóng
				</button>
				<button class="btn btn-sm btn-primary" id="btnInBieuMau">
					<span class="fixDisplay glyphicon glyphicon-print"></span> In biểu mẫu
				</button>
				<button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
					<span class="glyphicon glyphicon-ok"></span> Ký & In
				</button>
				<button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
					<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
				</button>
				<button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
					<span class="glyphicon glyphicon-print"></span> In ký số
				</button>
				<!-- <select class="form-control input-sm i-col-3" id="sltInBieuMau" style="height: 29px !important;">
					<option value="pdf" selected="selected">file pdf</option>
					<option value="rtf">file doc</option>						
					 <option value="xlsx">xlsx</option>
					<option value="xls">doc</option>
				</select> -->
				<button class="btn btn-sm btn-primary" id="btn_Close">
					<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
			</div>
		</div>
		<script>
			var opt = [];
			var hospital_id = '{hospital_id}';
			var user_id = '{user_id}';
			var dept_id = '{dept_id}';
			var user_type = '{user_type}';
			var province_id = '{province_id}';
			var uuid = '{uuid}';
			var db_schema='{db_schema}';
			var lang = "vi";
			console.log('hospital_id=' + hospital_id + ' user_type='
					+ user_type);
			var session_par = [];
			session_par[0] = hospital_id;
			session_par[1] = user_id;
			session_par[2] = user_type;
			session_par[3] = province_id;
			session_par[4] = db_schema;
			var table_name = '{table}';
	
			var _opts = new Object();
			var mode = '{showMode}';				
			if(mode=='dlg') {		
				parent.DlgUtil.tunnel(DlgUtil.moveEvent);
				data=EventUtil.getVar("dlgVar");		
				_opts.khambenhid = data.khambenhid;
				_opts.hosobenhanid = data.hosobenhanid;		
				_opts.benhnhanid = data.benhnhanid;
				_opts.loaibenhanid = data.loaibenhanid;
				_opts.maloaibenhan = data.maloaibenhan;
				_opts.khoaid=dept_id;
			}
	
			_opts._param = session_par;
			_opts._uuid = uuid;
			initRest(_opts._uuid);
			var BAN01_UB01 = new BAN01UB01_UNGBUOU(_opts);
			BAN01_UB01.load();
		</script>
	</div>
</div>
<script>
	//initRest();
	if (_mode == 'view') {
		$("#divMain").removeClass("container").css("width", "96%").css(
				"margin", "0 auto");
	}
	var doctorMgr = new CRegDoctoringMgr();
	doctorMgr.load();
</script>