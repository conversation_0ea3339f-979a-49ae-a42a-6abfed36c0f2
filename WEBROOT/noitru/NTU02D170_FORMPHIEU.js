/*
CTL_SQL: NTU02D170
function: NTU02D170
 */
function NTU02D170_FORMPHIEU(opt) {

    var _CAUHINH = {};
    var hosobenhanid;
    var _kySo;
    this.load = doLoad;
    var rpt_code = '';

    function doLoad() {
        initControl();
        bindEvent();
        setInterface();
		
		//L2PT-39886
		try{
			if (opt.MODE == '1') { // trạng thái khám: Kết thúc
				$('#divMAU').hide();
				$('#divBUTTON').find('button').hide();
				$('#btnInPhieu').show();
			}
		} catch (ex) {}
		
    }

    function initControl() {
        _CAUHINH = getDSCauHinh('FORMPHIEU');
        var _sql_par = [];
        _sql_par.push({
            "name": "[0]",
            value: opt.KHAMBENHID
        });
        hosobenhanid = jsonrpc.AjaxJson.getOneValue('GET.HOSOBENHANID', _sql_par);
        $.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
        generateGrid();
        generateComboBox();
        generateComboGrid();
        generateKeyForDivTAB();

        switch (opt.CHUCNANG) {
            case "GCVSXH":
                rpt_code = "NGT003_GIAYCHUYENTUYEN_BNSXH";
                break;
            case "HDFONLINE":
                rpt_code = "PHIEU_CHAYTHAN_HDFONLINE";
                par = getParam1Hang();
                break;
            case "NTTBA":
                rpt_code = "TOMTAT_BENHLY_29040";
                break;
            case "PLGHDCNTG":
                rpt_code = "PHIEULUONGGIA_HDCN_A4";
                break;
            case "PKCDPHCN":
                rpt_code = "PHIEUKHAMVACHIDINH_PHCN_A4";
                break;
            case "PDGTTRV":
                rpt_code = "PHIEU_DANHGIA_NGUOIBENH_RAVIEN_A4";
                break;
            case "PDGNBNV":
                rpt_code = "PHIEU_DANHGIA_NGUOIBENH_NHAPVIEN_A4";
                break;
            case "CSSPTD":
                rpt_code = "PHIEU_CHAMSOC_SANPHU_TRUOCDE_A4_1007";
                break;
            case "CSSPSD":
                rpt_code = "PHIEU_CHAMSOC_SANPHU_SAUDE_A4";
                break;
            default:
                rpt_code = "";
                break;
        }

        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rpt_code);
        _kySo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", "HIS_SUDUNG_KYSO_KYDIENTU");
        if (data_ar != null && data_ar.length > 0 && _kySo == '1') {
            $("#btnKyCa").show();
            $("#btnHuyCa").show();
            $("#btnExportCa").show();
        }
    }

    function getDSCauHinh(tenManHinh) {
        var cauHinh = {};
        var obj = {
            manHinh: tenManHinh + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H002.L30", param);
        if (result && result.length > 0) {
            for (var i = 0; i < result.length; i++) {
                cauHinh[result[i].CAUHINH] = result[i].GIATRI;
            }
        }
        return cauHinh;
    }

    function generateComboBox() {
        loadDsMau();
    }

    function setInterface() {
        //$('#divMAU').hide();

        showHideByChucNang();

        disableTTBN();
        loadThongTinBN();

        disablAllDIV();
        $("#btnThem").prop('disabled', false);
        $("#btnDong").prop('disabled', false);

        // phieu danh gia nguoi benh nhap vien cua lac viet hien thi them nut lay thong tin co san
        if (opt.CHUCNANG == 'PDGNBNV') {
            $('#btnLayThongTinPDGNBNV').show();
        }

        loadGrid();
    }

    function showHideByChucNang() {
        // mac dinh de 1 tab thoi. chuc nang nao yeu cau nhieu tab thi tuy bien sau
        $('#tabB').hide();
        $('#tabC').hide();
        $('#tabD').hide();
        $('#tabE').hide();
        $('#nameTabA').html("Thông tin");
    }

    function generateGrid() {
        var gridHeader = ""
            + "IDDATA,IDDATA,0,0,t,l"
            + ";TIEPNHANID,TIEPNHANID,0,0,t,l"
            + ";KHAMBENHID,KHAMBENHID,0,0,t,l"
            + ";HOSOBENHANID,HOSOBENHANID,0,0,t,l"
            + ";STT,STT,1,0,f,c"
            + ";Số phiếu,SOPHIEU,1,0,f,c"
			//L2PT-37109
            //+ ";Ngày thực hiện,TIMECREATE,3,0,f,c"  
            //+ ";Người thực hiện,NGUOITHUCHIEN,3,0,f,c"
            + ";Ngày tạo phiếu,TIMECREATE,3,0,f,c"  
            + ";Người tạo phiếu,NGUOITHUCHIEN,3,0,f,c"
        ;
        GridUtil.init("grdDS", "100%", "150", "Danh sách phiếu: " + getTenPhieu(), true, gridHeader);

        $("#grdDS").jqGrid('setGridParam', {
            onSelectRow: function (id) {
                opt.rowid = id;
                selectRowGrid(id);
                if(_kySo == '1') {
                    checkKyCa(id);
                }
                $('#btnTaoBanSao').prop('disabled', false);
            },
            ondblClickRow: function (id) {
                $('#btnSua').click();
            }
        });
    }

    function generateComboGrid() {
    }

    // function generateComboGridChanDoan(){
    // 	var hearderICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
    // 	ComboUtil.initComboGrid("txtTKMACHANDOAN", "NT.008", [], "600px", hearderICD, function(event, ui) {
    // 		var item = ui.item;
    // 		$("#cboMACHANDOAN").empty();
    // 		$("#cboMACHANDOAN").append('<option value="' + item.ICD10CODE + '">' + item.ICD10NAME + '</option>');
    // 	});
    // }

    function generateKeyForDivTAB() {
        // lay ra danh sach key từ danh mục key
        var obj = {
            CHUCNANG: opt.CHUCNANG + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.L04", param);

        // luôn có ROOT
        if (result && result.length >= 2) {
            keyToHTMLDivTAB(result);
            suaGiaoDienTheoScreen(opt.CHUCNANG);
        } else {
            DlgUtil.showMsg("Chưa khai báo danh mục key", function () {
            });
            return;
        }
    }

    function get_elements_by_inner_strongtag(word) {
        var res = []
        var elems = [...document.getElementsByTagName('strong')];
        elems.forEach((elem) => {
            if (elem.outerHTML.includes(word)) {
                res.push(elem)
            }
        })
        return (res);
    }

    function suaGiaoDienTheoScreen(screen) {
        if (screen == 'GCVSXH') {
            customizeGCVSXH();
        } else if (screen == 'PKCDPHCN') {
            customizePKCDPHCN();
        } else if (screen == 'PLGHDCNTG') {
            customizePLGHDCNTG();
        } else if (screen == 'PDM') {
            customizePDM();
        } else if (screen == 'PDGNBNV') {
            customizePDGNBNV();
        }
    }

    function customizePDGNBNV() {

        pickerTime('txtTHOIGIAN', 'ddmmyyyy hhmiss');
    }

    function customizePDM() {
        pickerTime('txtNGAYTAOPHIEU', 'ddmmyyyy hhmiss');
        pickerChanDoan('txtCHANDOANSEARCH', 'txtMACHANDOAN', 'txtTENCHANDOAN', 'txtGHICHUCHANDOAN');
    }

    function customizePKCDPHCN() {
		//L2PT-34689  L2PT-36259
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU02D170_PKCDPHCN_BS') == '1'){
			pickerNhanVien('txtBACSISEARCH', 'txtBACSI');
			pickerTime('txtNGAYTHUCHIEN', 'ddmmyyyy hhmiss');
			pickerTime('txtNGAYTAO', 'ddmmyyyy hhmiss');//L2PT-55216
		        
			//L2PT-37109
			pickerChanDoan1Dong('txtCHANDOANKTSEARCH', 'txtTENCHANDOANKT'); //dành cho bệnh kèm theo 
			$('#txtTENCHANDOANKT').attr('rows','3');
			$('#txtTENCHANDOAN').attr('rows','1');
			$('#txtKHAMCHUYENKHOAPHCN').attr('rows','6');
			$("#txtNGAYTHUCHIEN").parent().removeClass("col-md-6").addClass("col-md-2");
			$("#txtNGAYTAO").parent().removeClass("col-md-6").addClass("col-md-2");//L2PT-55216
			$("#txtBACSISEARCH").parent().removeClass("col-md-6").addClass("col-md-2"); 
			
			pickerChanDoan('txtCHANDOANSEARCH', 'txtMACHANDOAN', 'txtTENCHANDOAN', 'txtGHICHUCHANDOAN'); //dành cho bệnh chính
			pickerDichVuPHCN('txtDICHVUSEARCH', 'txtMADICHVU', 'txtTENDICHVU');
			 
		}
		
        // yhct lang son, phcn lang son, tth, son tinh
        if (["28600", "30680", "947", "5926", "10284"].includes(opt.csytid + "")) {
            pickerChanDoan1Dong('txtCHANDOANSEARCH', 'txtTENCHANDOAN');
            pickerDichVu1Dong('txtDICHVUSEARCH', 'txtTENDICHVU');
            pickerTime('txtNGAYTHUCHIEN', 'ddmmyyyy hhmiss');
            if (opt.csytid == 30680) {
                changeFontSize();
                // L2PT-12299
                pickerNhanVien('txtBACSISEARCH', 'txtBACSI');
            }
            // son tinh khong can nhap bac si (da xoa trong dm key)
            return;
        }

		if (["30360"].includes(opt.csytid + "")) {
			pickerChanDoan1Dong('txtCHANDOANSEARCH', 'txtTENCHANDOAN');
			pickerDichVu1Dong('txtDICHVUSEARCH', 'txtTENDICHVU');
			pickerNhanVien('txtBACSISEARCH','txtBACSI');
			return;
		}
		
		//L2PT-25885 start ttlinh thêm chẩn đoán KT
		if (["27040","30300","1007", "1133"].includes(opt.csytid + "")) {
			pickerDichVu1Dong('txtDICHVUSEARCH', 'txtTENDICHVU');
			pickerChanDoan1Dong('txtCHANDOANKTSEARCH', 'txtTENCHANDOANKT');
            pickerChanDoan('txtCHANDOANSEARCH', 'txtMACHANDOAN', 'txtTENCHANDOAN', 'txtGHICHUCHANDOAN');
			return;
		}
		//L2PT-25885 end
		//L2PT-28028 start ttlinh
		if (["965"].includes(opt.csytid + "")) {
			$('#txtDICHVUSEARCH').prop('disabled','true');
			$('#txtCHANDOANKTSEARCH').prop('disabled','true');
			$('#txtCHANDOANSEARCH').prop('disabled','true');
			pickerDichVu1Dong('txtDICHVUSEARCH', 'txtTENDICHVU');
			pickerChanDoan1Dong('txtCHANDOANKTSEARCH', 'txtTENCHANDOANKT');
			pickerChanDoan('txtCHANDOANSEARCH', 'txtMACHANDOAN', 'txtTENCHANDOAN', 'txtGHICHUCHANDOAN');
			return;
		}
		//L2PT-28028 end
		//L2PT-98857 start
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU02D170_SHOW_PDT') == '1') {
			$('#divTAB_PHIEUDIEUTRI').show();
			sql_par = RSUtil.buildParam("", [ opt.KHAMBENHID, "4" ]);
			ComboUtil.getComboTag("cboPHIEUDIEUTRI", "COM.PHIEUDIEUTRI", sql_par, "", {
				value : '',
				text : '-- Chọn --'
			}, "sql", '', '');
		}
		//L2PT-98857 end
		pickerChanDoan('txtCHANDOANSEARCH', 'txtMACHANDOAN', 'txtTENCHANDOAN', 'txtGHICHUCHANDOAN');
		//pickerDichVuPHCN('txtDICHVUSEARCH', 'txtMADICHVU', 'txtTENDICHVU');
		pickerDichVu1Dong('txtDICHVUSEARCH', 'txtTENDICHVU');

	}
    function customizePLGHDCNTG() {
		//L2PT-34689  L2PT-36259
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU02D170_PLGHDCNTG_BS') == '1'){
			pickerNhanVien('txtBACSISEARCH', 'txtBACSI');
			pickerTime('txtNGAYTHUCHIEN', 'ddmmyyyy hhmiss');
			pickerTime('txtNGAYTAO', 'ddmmyyyy hhmiss');//L2PT-55216
			 
			//L2PT-37109 
			$("#txtNGAYTHUCHIEN").parent().removeClass("col-md-6").addClass("col-md-2");
			$("#txtBACSISEARCH").parent().removeClass("col-md-6").addClass("col-md-2");  
			$('#txtNGAYTHUCHIEN').attr('autocomplete', 'off');
			$("#txtNGAYTAO").attr('autocomplete', 'off');//L2PT-55216
		}
		
        // yhct lang son, phcn lang son, tth, son tinh
        if (["28600", "30680", "947", "5926"].includes(opt.csytid + "")) {
            pickerTime('txtNGAYTHUCHIEN', 'ddmmyyyy hhmiss');
            // L2PT-12298
            if (opt.csytid == 30680) {
                pickerNhanVien('txtBACSISEARCH', 'txtBACSI');
            }
            return;
        }

        if (!["28600", "30680", "947", "5926"].includes(opt.csytid + "")) { //L2PT-28030
            pickerNhanVien('txtBACSISEARCH', 'txtBACSI');
            return;
        }

        if (opt.csytid == 30680) {
            changeFontSize();
            return;
        }

    }

    function customizeGCVSXH() {
        var tongdichtruyen = get_elements_by_inner_strongtag("Tổng dịch truyền");
        tongdichtruyen[0].parentElement.outerHTML = "<h4><strong>Tổng dịch truyền</strong></h4>";

        // xu ly lai css cho gio, ngay
    }

    function keyToHTMLDivTAB(arr) {
        // mỗi tab phải generate 1 html riêng
        var htmlTabA = '';
        var htmlTabB = '';
        var htmlTabC = '';
        var htmlTabD = '';
        var htmlTabE = '';

        for (var i = 0; i < arr.length; i++) {
            var obj = arr[i];
            var html = '';

            if (obj.KEYNAME == 'ROOT' || obj.TYPE == '6') {
                continue;
            }
            // label - parent
            else if (obj.TYPE == '5') {
                html += '' +
                    '\t\t<div class="col-md-12 low-padding">\n' +
                    '\t\t\t<h3><strong>' + obj.PREFIX + '</strong></h3>\n' +
                    '\t\t</div>\n';
            } else {
                // cấp nhỏ nhất

                if (obj.REQUIRED == '1') {
                    html += '' +
                        '\t\t<div class="col-md-12 low-padding">\n' +
                        '\t\t\t<div class="col-md-2 low-padding">\n' +
                        '\t\t\t\t<div class="col-md-10 low-padding">\n' +
                        '\t\t\t\t\t<label><b>' + obj.PREFIX + '</b></label>\n' +
                        '\t\t\t\t</div>\n' +
                        '\t\t\t\t<div class="col-md-2 low-padding">\n' +
                        '\t\t\t\t\t<label style="color: red">(*)</label>\n' +
                        '\t\t\t\t</div>\n' +
                        '\t\t\t</div>\n';
                    ;

                } else {
                    html += '' +
                        '\t\t<div class="col-md-12 low-padding">\n' +
                        '\t\t\t<div class="col-md-2 low-padding">\n' +
                        '\t\t\t\t<label><b>' + obj.PREFIX + '</b></label>\n' +
                        '\t\t\t</div>\n';
                }


                // textbox
                if (obj.TYPE == '1') {
                    // chỉ textbox mới có suffix
                    if (obj.SUFFIX == '') {
                        html += '' +
                            '\t\t\t<div class="col-md-6 low-padding">\n' +
                            '\t\t\t\t<input class="form-control input-sm i-col-99 "' + getRule(obj) + ' type="text" id="txt' + obj.KEYNAME + '" placeholder="' + obj.TITLE + '" style="width: 100%">\n' +
                            '\t\t\t</div>\n';
                    } else {
                        html += '' +
                            '\t\t\t<div class="col-md-6 low-padding">\n' +
                            '\t\t\t\t<input class="form-control input-sm i-col-99 "' + getRule(obj) + 'type="text"  id="txt' + obj.KEYNAME + '" placeholder="' + obj.TITLE + '" style="width: 100%">\n' +
                            '\t\t\t</div>\n' +
                            '\t\t\t<div class="col-md-2 low-padding">\n' +
                            '\t\t\t\t<label>&nbsp;\n' + obj.SUFFIX + '</label>\n' +
                            '\t\t\t</div>\n';
                    }
                }
                // textarea
                else if (obj.TYPE == '2') {
                    html += '' +
                        '\t\t\t<div class="col-md-8 low-padding">\n' +
                        '\t\t\t\t<textarea rows="2" maxlength="2000"' + getRule(obj) + ' id="txt' + obj.KEYNAME + '" placeholder="' + obj.TITLE + '" style="width: 100%"></textarea>\n' +
                        '\t\t\t</div>\n';
                }
                // checkbox
                else if (obj.TYPE == '3') {
                    html += '' +
                        '\t\t\t<div class="col-md-8 low-padding">\n';

                    // danh sach con theo ngay sau
                    var arrChild = [];
                    for (var j = i + 1; j < arr.length; j++) {
                        var nextChild = arr[j];
                        if (nextChild.TYPE != '6') {
                            break;
                        } else {
                            arrChild.push(nextChild);
                        }
                    }

                    for (var j = 0; j < arrChild.length; j++) {
                        var objChild = arrChild[j];
                        html += '' +
                            '\t\t\t\t<div class="col-md-12 low-padding">\n' +
                            '\t\t\t\t\t<input type="checkbox" id="chk' + objChild.KEYNAME + '"> <label for="chk' + objChild.KEYNAME + '" class="label1">' + objChild.PREFIX + '</label>\n' +
                            '\t\t\t\t</div>\n';
                    }
                    html += '' +
                        '\t\t\t</div>\n';

                }
                // combobox: type = 4
                else if (obj.TYPE == '4') {
                    html += '' +
                        '\t\t\t<div class="col-md-8 low-padding">\n' +
                        '\t\t\t\t<select class="form-control input-sm" id="cbo' + obj.KEYNAME + '">\n';

                    // danh sach con theo ngay sau
                    var arrChild = [];
                    for (var j = i + 1; j < arr.length; j++) {
                        var nextChild = arr[j];
                        if (nextChild.TYPE != '6') {
                            break;
                        } else {
                            arrChild.push(nextChild);
                        }
                    }

                    for (var j = 0; j < arrChild.length; j++) {
                        var objChild = arrChild[j];
                        html += '' +
                            '\t\t\t\t\t<option value="' + objChild.KEYNAME + '">' + objChild.PREFIX + '</option>\n';
                    }
                    html += '' +
                        '\t\t\t\t</select>\n' +
                        '\t\t\t</div>\n';
                }

                html += '' +
                    '\t\t</div>\n';
            }

            if (obj.AREA == '1') {
                htmlTabA += html;
            } else if (obj.AREA == '2') {
                htmlTabB += html;
            } else if (obj.AREA == '3') {
                htmlTabC += html;
            } else if (obj.AREA == '4') {
                htmlTabD += html;
            } else if (obj.AREA == '5') {
                htmlTabE += html;
            }
        }

        $('#divTabA').append(htmlTabA);
        $('#divTabB').append(htmlTabB);
        $('#divTabC').append(htmlTabC);
        $('#divTabD').append(htmlTabD);
        $('#divTabE').append(htmlTabE);
    }

    function bindEvent() {

        $("#btnLayThongTinPDGNBNV").on("click", function (e) {
            var obj = {
                DIACHI: "",
                NGHENGHIEP: "",
                NGAYTHANGNAMNHAPVIEN: "",
                BACSINHAPVIEN: "",
                CHANDOANYKHOA: "",
                TTLIENHE: "",
                SODIENTHOAI: "",
                BANTHAN: "",
                GIADINH: "",
                MACH: "",
                NHIETDO: "",
                HUYETAP: "",
                NHIPTHO: "",
                // L2PT-22120 duonghn start
                SPO2: "",
                CHIEUCAO: "",
                CANNANG: ""
                // L2PT-22120 duonghn end
            }
            var param = {
                khambenhid: opt.KHAMBENHID + "",
            }
            var param = JSON.stringify(param);
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.L14", param);
            if (result && result.length > 0) {
                obj = result[0];
            }

            if ($('#txtDIACHI').val() == '') {
                $('#txtDIACHI').val(obj.DIACHI);
            }

            if (
                !$('#chkNGHEHUUTRI').is(":checked")
                && !$('#chkNGHECANBO').is(":checked")
                && !$('#chkNGHECONGNHAN').is(":checked")
                && !$('#chkNGHENOITRU').is(":checked")
                && !$('#chkNGHELAMRUONG').is(":checked")
                && !$('#chkNGHEHOCSINH').is(":checked")
                && !$('#chkNGHESINHVIEN').is(":checked")
                && !$('#chkNGHETHATNGHIEP').is(":checked")
                && !$('#chkNGHEKHAC').is(":checked")
                && !$('#chkNGHETREEM').is(":checked") // L2PT-22120 duonghn
            ) {
                var nghenghiep = obj.NGHENGHIEP;
                if (nghenghiep == '5') {
                    $("#chkNGHEHUUTRI").prop("checked", true);
                } else if (nghenghiep == '7') {
                    $("#chkNGHECANBO").prop("checked", true);
                } else if (nghenghiep == '6') {
                    $("#chkNGHECONGNHAN").prop("checked", true);
                } else if (nghenghiep == '485') {
                    $("#chkNGHENOITRU").prop("checked", true);
                } else if (nghenghiep == 'lamruong') {
                    $("#chkNGHELAMRUONG").prop("checked", true);
                } else if (nghenghiep == '4') {
                    $("#chkNGHEHOCSINH").prop("checked", true);
                    $("#chkNGHESINHVIEN").prop("checked", true);
                } else if (nghenghiep == 'thatnghiep') {
                    $("#chkNGHETHATNGHIEP").prop("checked", true);
                } else if (nghenghiep == '16') {
                    $("#chkNGHEKHAC").prop("checked", true);
                }
                // L2PT-22120 duonghn start
                else if (nghenghiep == '3') {
                    $("#chkNGHETREEM").prop("checked", true);
                }
                // L2PT-22120 duonhn end
            }

            if ($('#txtNGAYTHANGNAMNHAPVIEN').val() == '') {
                $('#txtNGAYTHANGNAMNHAPVIEN').val(obj.NGAYTHANGNAMNHAPVIEN);
            }

            if ($('#txtTENBACSYCHONHAPVIEN').val() == '') {
                $('#txtTENBACSYCHONHAPVIEN').val(obj.BACSINHAPVIEN);
            }

            if ($('#txtCHANDOANYKHOA').val() == '') {
                $('#txtCHANDOANYKHOA').val(obj.CHANDOANYKHOA);
            }

            if ($('#txtKHICANLIENHE').val() == '') {
                $('#txtKHICANLIENHE').val(obj.TTLIENHE);
            }

            if ($('#txtSODIENTHOAI').val() == '') {
                $('#txtSODIENTHOAI').val(obj.SODIENTHOAI);
            }

            if ($('#txtBANTHAN').val() == '') {
                $('#txtBANTHAN').val(obj.BANTHAN);
            }

            if ($('#txtGIADINH').val() == '') {
                $('#txtGIADINH').val(obj.GIADINH);
            }

            if ($('#txtMACH').val() == '') {
                $('#txtMACH').val(obj.MACH);
            }

            if ($('#txtTDO').val() == '') {
                $('#txtTDO').val(obj.NHIETDO);
            }

            if ($('#txtHUYETAP').val() == '') {
                $('#txtHUYETAP').val(obj.HUYETAP);
            }

            if ($('#txtNHIPTHO').val() == '') {
                $('#txtNHIPTHO').val(obj.NHIPTHO);

            }

            if ($('#txtHUYETAP').val() == '') {
                $('#txtHUYETAP').val(obj.HUYETAP);
            }

            if ($('#txtNHIPTHO').val() == '') {
                $('#txtNHIPTHO').val(obj.NHIPTHO);

            }
            // L2PT-22120 duonghn start
            if ($('#txtSPO2').val() == '') {
                $('#txtSPO2').val(obj.SPO2);

            }

            if ($('#txtCHIEUCAO').val() == '') {
                $('#txtCHIEUCAO').val(obj.CHIEUCAO);

            }

            if ($('#txtCANNANG').val() == '') {
                $('#txtCANNANG').val(obj.CANNANG);

            }
            // L2PT-22120 duonghn end
        });

        $("#btnThem").on("click", function (e) {
            GridUtil.unmarkAll('grdDS');

            opt.rowid = undefined;
            opt.IDDATA = undefined;

            enableAllDIV();
            disableTrue(["btnThem", "btnSua", "btnXoa", "btnInPhieu", "btnKyCa", "btnExportCa", "btnHuyCa"]);
            clearDivTTCT();

            focusTabA();

            $('#cboMAU').val('');
            $('#txtTENMAU').val('');
            disableFalse(["cboMAU", "txtTENMAU", "btnLuuThanhMau", "btnXoaMau"]);
            //L2PT-113830 L2PT-113831 start
            if ((opt.CHUCNANG == 'PKCDPHCN' || opt.CHUCNANG == 'PLGHDCNTG') && (_CAUHINH.NTU_FORMPHIEU_USER_MACDINH == '1')) {
            	var par_bs = []; 
    			var data_bs = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K028.BACSI", par_bs);
    			var row = JSON.parse(data_bs);					
    			if (row != null && row.length > 0 && row[0].OFFICER_NAME != '') {
    				$('#txtBACSI').val(row[0].OFFICER_NAME); 
    			}
            }
            //L2PT-113830 end
            // jira L2PT-9008, L2PT-9010: yhct lang son,phcn lang son
            if (
                opt.CHUCNANG == 'PKCDPHCN'
                &&
                (opt.csytid == '28600' || opt.csytid == '30680')) {
                var ttToDieuTri = getTTToDieuTri();
                $('#txtTENCHANDOAN').val(ttToDieuTri);
            }
            //L2PT-28028 start
            if (opt.CHUCNANG == 'PKCDPHCN' && (opt.csytid == '965')) {
                var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D199.GET_TTKB", opt.KHAMBENHID, []);
                if (result && result.length > 0) {
                    obj = result[0];
                }
                $('#txtKHAMCHUYENKHOAPHCN').val(obj.KHAMHOIBENH);
                $('#txtMACHANDOAN').val(obj.MACHANDOAN);
                $('#txtTENCHANDOAN').val(obj.CHANDOAN);
                $('#txtTENCHANDOANKT').val(obj.CHANDOAN_KT);
                $('#txtTENDICHVU').val(obj.DICHVU);
            }
            //L2PT-28028 end
            //L2PT-55216 start
            if ((opt.CHUCNANG == 'PKCDPHCN' || opt.CHUCNANG == 'PLGHDCNTG') && (opt.csytid == '30360' || opt.csytid == '32940' || opt.csytid == '1111')) {
            	$('#txtNGAYTHUCHIEN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')); 
            	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K015.LAYDLPDT", opt.KHAMBENHID);
        		if (data_ar != null && data_ar.length > 0) {
        			var row = data_ar[0];
        			FormUtil.setObjectToForm("", "", row);
        			if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_CHECK_K74_CDCHINH') == '1') {
        				$('#txtMACHANDOAN').val(row.MACHANDOANVAOKHOA);
        				$('#txtTENCHANDOAN').val(row.CHANDOANVAOKHOA);
        				$('#txtTENCHANDOANKT').val(row.CHANDOANVAOKHOA_KEMTHEO);
        			} else {
        				$('#txtMACHANDOAN').val(row.MACHANDOANCHINH);
        				$('#txtTENCHANDOAN').val(row.CHANDOANCHINH);
        				$('#txtTENCHANDOANKT').val(row.CHANDOANKT);
        			}
        		}
            }
            $('#txtNGAYTAO').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')); 
          //L2PT-55216 end
            //L2PT-62166 start
            if ((opt.CHUCNANG == 'PKCDPHCN') && (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PKCDPHCN_PDT1') == '1')) {
            	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.LAYDLPDT", opt.KHAMBENHID);
        		if (data_ar != null && data_ar.length > 0) {
        			var row = data_ar[0];
        			FormUtil.setObjectToForm("", "", row);
        			$('#txtKHAMCHUYENKHOAPHCN').val(row.KHAM_PHCN);
                    $('#txtMACHANDOAN').val(row.MACHANDOANCHINH);
                    $('#txtTENCHANDOAN').val(row.CHANDOANCHINH);
                    $('#txtTENCHANDOANKT').val(row.CHANDOAN_KT);
                    $('#txtTENDICHVU').val(row.DICHVU);
        		}
            }
            //L2PT-62166 end
        });

        $("#btnSua").on("click", function (e) {
            if (!opt.IDDATA) {
                DlgUtil.showMsg("Chưa chọn phiếu!");
                return;
            }

            enableAllDIV();
            disableTrue(["btnThem", "btnSua", "btnXoa", "btnInPhieu", "btnKyCa", "btnExportCa", "btnHuyCa"]);

            $('#cboMAU').val('');
            $('#txtTENMAU').val('');
            disableFalse(["cboMAU", "txtTENMAU", "btnLuuThanhMau", "btnXoaMau"]);
        });

        $("#btnXoa").on("click", function (e) {
            if (!opt.IDDATA) {
                DlgUtil.showMsg("Chưa chọn phiếu!");
                return;
            }
            DlgUtil.showConfirm("Bạn có muốn xóa phiếu này không?", function (flag) {
                if (flag) {
                    xoaPhieu();
                }
            });
        });

        $("#btnTaoBanSao").on("click", function (e) {
            var obj = new Object();
            obj.KHAMBENHID = opt.KHAMBENHID;
            obj.IDDATA = opt.IDDATA;
            obj.CHUCNANG = opt.CHUCNANG;
            obj.RPT_CODE = rpt_code;

            var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D170.L16", JSON.stringify(obj));
            var data = $.parseJSON(result);
            if (data == '-1') {
                DlgUtil.showMsg('Đã có lỗi xảy ra', function () {
                });
            } else {
                DlgUtil.showMsg('Tạo bản sao thành công!', function () {
                    loadGrid();
                    opt.rowid = undefined;
                    opt.IDDATA = undefined;
                    clearDivTTCT();
                    disablAllDIV();
                    $("#btnThem").prop('disabled', false);
                    $("#btnDong").prop('disabled', false);
                    $("#btnTaoBanSao").prop('disabled', true);
                });
            }
        });

        $("#btnLuu").on("click", function (e) {
            if (validateBeforeSave() == -1) {
                return;
            }
            savePhieu(false);
        });

        $("#btnInPhieu").on("click", function (e) {
            var idRow = $("#grdDS").jqGrid('getGridParam', 'selarrrow');

            if (idRow.length == 0) {
                DlgUtil.showMsg("Chưa chọn phiếu!");
                return;
            } else if (idRow.length > 5) {
                DlgUtil.showMsg("Chỉ được chọn tối đa 5 phiếu!");
                return;
            }

            var idPhieuArr = [];
            for (var i = 0; i < idRow.length; i++) {
                var idPhieu = $("#grdDS").jqGrid('getRowData', idRow[i]).IDDATA;
                idPhieuArr.push(idPhieu);
            }

            var ids = idPhieuArr.join();

            var par = [
                {name: 'IDPHIEU', type: 'String', value: ids}
            ];
            var reportName = "";
            switch (opt.CHUCNANG) {
                case "GCVSXH":
                    reportName = "NGT003_GIAYCHUYENTUYEN_BNSXH";
                    break;
                case "HDFONLINE":
                    reportName = "PHIEU_CHAYTHAN_HDFONLINE";
                    par = getParam1Hang();
                    break;
                case "NTTBA":
                    reportName = "TOMTAT_BENHLY_29040";
                    break;
                case "PLGHDCNTG":
                    reportName = "PHIEULUONGGIA_HDCN_A4";
                    break;
                case "PKCDPHCN":
                    reportName = "PHIEUKHAMVACHIDINH_PHCN_A4";
                    break;
                case "PDGTTRV":
                    reportName = "PHIEU_DANHGIA_NGUOIBENH_RAVIEN_A4";
                    break;
                case "PDGNBNV":
                    reportName = "PHIEU_DANHGIA_NGUOIBENH_NHAPVIEN_A4";
                    break;
                case "CSSPSD":
                    reportName = "PHIEU_CHAMSOC_SANPHU_SAUDE_A4";
                    break;
                default:
                    reportName = "";
                    break;
            }
            if (opt.csytid == '965') { //L2PT-28030
                var rpName = reportName + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', reportName, 'docx', par, rpName);
            } else {
                openReport('window', reportName, 'pdf', par);
            }
        });

        $("#btnHuy").on("click", function (e) {
            DlgUtil.showConfirm("Bạn có muốn huỷ không?", function (flag) {
                if (flag) {
                    cleanColor();
                    if (!opt.IDDATA) {
                        $('#btnThem').click();
                    } else {
                        selectRowGrid(opt.rowid);
                    }
                }
            });
        });

        $("#btnDong").on("click", function (e) {
            DlgUtil.showConfirm("Bạn có muốn đóng không?", function (flag) {
                if (flag) {
                    window.close();
                }
            });
        });

        $('#cboMAU').change(function () {
            $('#txtTENMAU').val('');
            clearDivTTCT();
            if ($('#cboMAU').val() != '') {
                var dataKeyValue = getDataKeyValue($('#cboMAU').val());
                var data = tuningData(dataKeyValue);
                fillDataToDivTTCT(data);
            }
        });

        $("#btnLuuThanhMau").on("click", function (e) {
            if ($('#txtTENMAU').val().trim() == '') {
                DlgUtil.showMsg('Tên mẫu không được để trống', function () {
                });
                return;
            }
            saveMau();
        });

        $("#btnXoaMau").on("click", function (e) {
            if ($('#cboMAU').val() == '') {
                DlgUtil.showMsg('Vui lòng chọn mẫu', function () {
                });
                return;
            }
            DlgUtil.showConfirm("Bạn có muốn xóa mẫu này không?", function (flag) {
                if (flag) {
                    var par = [
                        $('#cboMAU').val()
                    ];
                    var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D170.L09", par.join('$'));
                    var data = $.parseJSON(result);
                    if (data == '-1') {
                        DlgUtil.showMsg('Đã có lỗi xảy ra', function () {
                        });
                    } else {
                        DlgUtil.showMsg('Đã xóa mẫu', function () {
                            loadDsMau();
                        });
                    }
                }
            });
        });

        $("#btnKyCa").on("click", function (e) {
            _caRpt('1');
        });

        $("#btnHuyCa").on("click", function (e) {
            _caRpt('2');
        });

        $("#btnExportCa").on("click", function (e) {
            _caRpt('0');
        });
        //L2PT-98857 start
        $("#cboPHIEUDIEUTRI").on("change", function(e) {
			var tmp_id = $("#cboPHIEUDIEUTRI").val();
			changePDT(tmp_id);
		});
        //L2PT-98857 end
    }

    //
    // function getInfoMau(id){
    // 	var obj = {
    // 		idMau: id + ""
    // 	}
    // 	var param = JSON.stringify(obj);
    // 	var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.L08", param);
    // 	if (result && result.length > 0) {
    // 		return result[0];
    // 	} else {
    // 		return null;
    // 	}
    // }

    function loadGrid() {
        var obj = {
            khambenhid: opt.KHAMBENHID + "",
            chucnang: opt.CHUCNANG + ""
        }
        var param = [{
            name: "[0]",
            value: JSON.stringify(obj)
        }];
        GridUtil.loadGridBySqlPage("grdDS", "NTU02D170.L01", param);

        setTimeout(function () {
            if (
                (
                    opt.CHUCNANG == 'PLGHDCNTG'
                    || opt.CHUCNANG == 'PKCDPHCN'
                )
                && ["30680"].includes(opt.csytid + "")
            ) {
                changeFontSize();
            }
        }, 1000);

    }

    // fill data vao divTTCT
    function fillDataToDivTTCT(data) {
        //FormUtil.setObjectToForm('divTTCT', "", data);
        FILL_OBJ('divTTCT', "", data);
        change_obj(); 
    }

    function saveMau() {
        var objChung = {};
        objChung.TENMAU = $('#txtTENMAU').val();
        objChung.CHUCNANG = opt.CHUCNANG;

        var objKeyValue = {};
        //FormUtil.setFormToObject("divTAB", "", objKeyValue);
        SAVE_OBJ("divTTCT", "", objKeyValue);

        var arr = [];
        for (var prop in objKeyValue) {
            if (Object.prototype.hasOwnProperty.call(objKeyValue, prop)) {
                arr.push({
                    "KEYNAME": prop,
                    "VALUE": objKeyValue[prop]
                });
            }
        }
        var jsonKeyValue = JSON.stringify(arr);

        var par = [JSON.stringify(objChung), jsonKeyValue];
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D170.L06", par.join('$'));
        var data = $.parseJSON(result);
        switch (data) {
            case 1:
                DlgUtil.showMsg('Lưu thành công!', function () {
                    loadDsMau();
                    $('#txtTENMAU').val('');
                });
                break;
            default:
                DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
                });
                break;
        }
    }

    // enable tat ca tru vung grid và ttbn
    function enableAllDIV() {
        $('#divBUTTON select, #divBUTTON input, #divBUTTON button').attr('disabled', false);
        $('#divMAU select, #divMAU input, #divMAU button').attr('disabled', false);
        $('#divTTCT select, #divTTCT input, #divTTCT textarea').attr('disabled', false);
    }

    // disable tat ca tru vung grid và ttbn
    function disablAllDIV() {
        $('#divBUTTON select, #divBUTTON input, #divBUTTON button').attr('disabled', true);
        $('#divMAU select, #divMAU input, #divMAU button').attr('disabled', true);
        $('#divTTCT select, #divTTCT input, #divTTCT textarea').attr('disabled', true);
    }

    // disable thong tin benh nhan
    function disableTTBN() {
        $('#divTTBN select, #divTTBN input, #divTTBN button').attr('disabled', true);
    }

    // clear divTTCT
    function clearDivTTCT() {
        $('#divTTCT input[type=text]').val('');
        $('#divTTCT input[type=checkbox]').prop('checked', false);
        $('#divTTCT textarea').val('');
        $('#divTTCT select option:first-child').attr("selected", "selected");

		$('#txtTGCD').val('');//L2PT-37109

    }

    function loadDsMau() {
        var sqlMau = "NTU02D170.L07";
        if (_CAUHINH.NTU_FORMPHIEU_MAU_USER == '1') {
            sqlMau = "NTU02D170.L15";
        }
        ComboUtil.getComboTag("cboMAU", sqlMau, [{
            "name": "[0]",
            "value": opt.CHUCNANG + "" // hid
        }], "", {
            value: '',
            text: '-- Chọn mẫu --'
        }, "");

    }

    function savePhieu(close) {
        var objChung = {};
        objChung.KHAMBENHID = opt.KHAMBENHID;
        objChung.IDDATA = opt.IDDATA;
        objChung.CHUCNANG = opt.CHUCNANG;
        objChung.RPT_CODE = rpt_code;

        var objKeyValue = {};
        //FormUtil.setFormToObject("divTAB", "", objKeyValue);
        SAVE_OBJ("divTTCT", "", objKeyValue);
        
        var arr = [];
        for (var prop in objKeyValue) {
            if (Object.prototype.hasOwnProperty.call(objKeyValue, prop)) {
                arr.push({
                    "KEYNAME": prop,
                    "VALUE": objKeyValue[prop]
                });
            }
        }
        var jsonKeyValue = JSON.stringify(arr);

        var par = [JSON.stringify(objChung), jsonKeyValue];
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D170.L03", par.join('$'));
        var data = $.parseJSON(result);
        switch (data) {
            case 1:
            case 2:
                DlgUtil.showMsg('Lưu thành công!', function () {
                    if (close) {
                        window.close();
                    } else {
                        loadGrid();
                        opt.rowid = undefined;
                        opt.IDDATA = undefined;
                        clearDivTTCT();
                        disablAllDIV();
                        $("#btnThem").prop('disabled', false);
                        $("#btnDong").prop('disabled', false);
                    }
                });
                break;
            default:
                DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
                });
                break;
        }
    }

    function xoaPhieu() {
        var obj = new Object();
        obj.IDDATA = opt.IDDATA;
        obj.RPT_CODE = rpt_code;

        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D170.L02", JSON.stringify(obj));
        var data = $.parseJSON(result);
        if (data == '-1') {
            DlgUtil.showMsg('Đã có lỗi xảy ra', function () {
            });
        } else {
            DlgUtil.showMsg('Đã xóa phiếu', function () {
                loadGrid();
                opt.rowid = undefined;
                opt.IDDATA = undefined;
                clearDivTTCT();
                disablAllDIV();
                $("#btnThem").prop('disabled', false);
                $("#btnDong").prop('disabled', false);
            });
        }
    }

    function validateBeforeSave() {
        cleanColor();
        var validator = new DataValidator("divTTCT");
        var valid = validator.validateForm();
        if (!valid) {
            return -1;
        }
        cleanColor();
        return 'ok';
    }

    // chuyển về 1 object
    function tuningData(dataKeyValue) {
        var data = {};
        for (var i = 0; i < dataKeyValue.length; i++) {
            var keyvalue = dataKeyValue[i];
            data[keyvalue.KEYNAME] = keyvalue.VALUE;
        }
        return data;

    }

    function selectRowGrid(id) {
        GridUtil.unmarkAll('grdDS');
        GridUtil.markRow('grdDS', id);

        var obj = $("#grdDS").jqGrid('getRowData', id);
        opt.IDDATA = obj.IDDATA;
		
        disablAllDIV();
        disableFalse(["btnThem", "btnSua", "btnXoa", "btnInPhieu", "btnKyCa", "btnExportCa", "btnHuyCa", "btnDong", "txtTENMAU", "btnLuuThanhMau"]);

        clearDivTTCT();
        var dataKeyValue = getDataKeyValue(obj.IDDATA);
        var data = tuningData(dataKeyValue);
        fillDataToDivTTCT(data);

        $('#cboMAU').val('');
        $('#txtTENMAU').val('');

        cleanColor();

		$('#txtTGCD').val(data.NGAYTHUCHIEN);//L2PT-37109

    }

    // lay thong tin chi tiet cac key va value tu bang key_value
    function getDataKeyValue(id) {
        var obj = new Object();
        obj.IDDATA = id;
        var par = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.L05", par);
        if (result && result.length > 0) {
            return result;
        } else {
            return null;
        }

    }

    function dateRegEx(date) {
        var pattern = new RegExp("^(3[01]|[12][0-9]|0[1-9])/(1[0-2]|0[1-9])/[0-9]{4} (2[0-3]|[01]?[0-9]):([0-5]?[0-9]):([0-5]?[0-9])$");
        if (date.search(pattern) === 0) {
            return true;
        } else {
            return false;
        }
    }

    function loadThongTinBN() {
        var ttbn = getTTBN(opt.KHAMBENHID);
        FormUtil.setObjectToForm("divTTBN", "", ttbn);  
        if (ttbn.GIOITINHID == '1') {
            $('#txtGIOI').val("Nam");
        } else {
            $('#txtGIOI').val("Nữ");
        }
    }

    function getTTBN(khambenhid) {
        var obj = {
            khambenhid: khambenhid + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.L10", param);
        if (result && result.length > 0) {
            return result[0];
        } else {
            return null;
        }
    }

    // focus tab A
    function focusTabA() {
        $("#divTabB").removeClass("active");
        $("#divTabC").removeClass("active");
        $("#divTabD").removeClass("active");
        $("#divTabE").removeClass("active");
        $("#divTabA").addClass("active");

        $("#tabB").removeClass("active");
        $("#tabC").removeClass("active");
        $("#tabD").removeClass("active");
        $("#tabE").removeClass("active");
        $("#tabA").addClass("active");
    }


    function disableTrue(arr) {
        for (var i = 0; i < arr.length; i++) {
            $("#" + arr[i]).prop('disabled', true);
        }
    }

    function disableFalse(arr) {
        for (var i = 0; i < arr.length; i++) {
            $("#" + arr[i]).prop('disabled', false);
        }
    }

    function getParam1Hang() {
        var idx = $("#grdDS").jqGrid('getGridParam', 'selrow');
        var data = $("#grdDS").jqGrid('getRowData', idx);
        var par = [
            {
                name: 'khambenhid',
                type: 'String',
                value: data.KHAMBENHID
            },
            {
                name: 'formphieuid',
                type: 'String',
                value: data.IDDATA
            }
        ];
        return par;
    }

    function getTenPhieu() {
        var tenPhieu = "";
        switch (opt.CHUCNANG) {
            case "GCVSXH":
                tenPhieu = "Giấy chuyển viện cho bn sốt xuất huyết";
                break;
            case "PDGNBNV":
                tenPhieu = "Phiếu đánh giá người bệnh nhập viện";
                break;
            case "HDFONLINE":
                tenPhieu = "Phiếu điều trị, theo dõi chạy thận nhân HDF Online";
                break;
            case "PDGTTRV":
                tenPhieu = "Phiếu đánh giá tình trạng ra viện";
                break;
            case "PCKPT":
                tenPhieu = "Phiếu cam kết phẫu thuật";
                break;
            case "CSSPTD":
                tenPhieu = "Phiếu chăm sóc sản phụ trước đẻ";
                break;
            case "CSSPSD":
                tenPhieu = "Phiếu chăm sóc sản phụ sau đẻ";
                break;
            case "NTTBA":
                tenPhieu = "Nhập thông tin bệnh án";
                break;
            case "PDM":
                tenPhieu = "Tạo phiếu duyệt mổ";
                break;
            case "PLGHDCNTG":
                tenPhieu = "Phiếu lượng giá hoạt động chức năng và sự tham gia";
                break;
            case "PKCDPHCN":
                tenPhieu = "Phiếu khám và chỉ định phục hồi chức năng";
                break;
        }
        return tenPhieu;
    }

    function pickerChanDoan(txtSearch, txtMA, txtTEN, txtGHICHU) {
        // change css
        $("#" + txtSearch).parent().removeClass("col-md-6").addClass("col-md-2");
        $("#" + txtMA).parent().removeClass("col-md-6").addClass("col-md-2");


        $('#' + txtMA).attr('readonly', true);
        var hearderICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
        ComboUtil.initComboGrid(txtSearch, "NT.008", [], "600px", hearderICD, function (event, ui) {
            var item = ui.item;
            $('#' + txtMA).val(item.ICD10CODE);
            $('#' + txtTEN).val(item.ICD10NAME);
        });

        $('#' + txtSearch).change(function () {
            $('#' + txtSearch).val('');
        });
    }

    function pickerDichVuPHCN(txtSearch, txtMA, txtTEN) {
        // change css
        $("#" + txtSearch).parent().removeClass("col-md-6").addClass("col-md-2");
        $("#" + txtMA).parent().removeClass("col-md-6").addClass("col-md-2");


        $('#' + txtMA).attr('readonly', true);
        var header = "Mã dịch vụ,MADICHVU,30,0,f,l;Tên dịch vụ,TENDICHVU,70,0,f,l";
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _CAUHINH.NTU_MANHOMDICHVUPHCN + ""}); // ma nhom dichu vu phuc hoi chuc nang
        ComboUtil.initComboGrid(txtSearch, "NTU02D170.L11", sql_par, "600px", header, function (event, ui) {
            var item = ui.item;
            $('#' + txtMA).val(item.MADICHVU);
            $('#' + txtTEN).val(item.TENDICHVU);
        });

        $('#' + txtSearch).change(function () {
            $('#' + txtSearch).val('');
        });
    }

    function pickerChanDoan1Dong(txtSearch, txtTEN) {
        $("#" + txtSearch).parent().removeClass("col-md-6").addClass("col-md-2");
        // yhct lang son 30680 lay ma yhct
        if (opt.csytid == '30680') {
            var hearderICD = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
            ComboUtil.initComboGrid(txtSearch, "NT.008.YHCT", [], "1200px", hearderICD, function (event, ui) {
                var s = ui.item.YHCTNAME + ' [' + ui.item.ICD10NAME + '] ';
                var t = $('#' + txtTEN).val();
            });
        } else {
            var hearderICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
            ComboUtil.initComboGrid(txtSearch, "NT.008", [], "600px", hearderICD, function (event, ui) {
                var s = ui.item.ICD10CODE + '-' + ui.item.ICD10NAME;
                var t = $('#' + txtTEN).val();
                $('#' + txtTEN).val(t == '' ? s : t + ';' + s);
            });
        }
        $('#' + txtSearch).change(function () {
            $('#' + txtSearch).val('');
        });
    }

    function pickerDichVu1Dong(txtSearch, txtTEN) {
        $("#" + txtSearch).parent().removeClass("col-md-6").addClass("col-md-2");
        var header =
            "Mã dich vụ,MADICHVU,30,0,f,l"
            + ";Tên dịch vụ,TENDICHVU,70,0,f,l"
            + ";DICHVUID,DICHVUID,0,0,t,l"
        ;
        var pa1 = [];
        var sql = "";
        pa1.push({
            "name": "[0]",
            value: hosobenhanid
        });
        //L2PT-28028 start
        if (opt.csytid == '965') {
            sql = "NTU02D199.DV965.01";
        } else {
            sql = "NTU02D170.L12";
        }
        //L2PT-28028 end
        ComboUtil.initComboGrid(txtSearch, sql, pa1, "600px", header, function (event, ui) {
            var s = ui.item.MADICHVU + '-' + ui.item.TENDICHVU;
            //L2PT-28028 start
            if (opt.csytid == '965') {
                s = ui.item.TENDICHVU;
            }
            //L2PT-28028 end
            var t = $('#' + txtTEN).val();
			
			//L2PT-37230
			if (["1133"].includes(opt.csytid + "")) {
				if (txtSearch == 'txtDICHVUSEARCH') $('#txtTENDICHVU').attr('rows','6');
				$('#' + txtTEN).val(t == ''? s: t + '\n' + s);
			}
			else 
				$('#' + txtTEN).val(t == '' ? s : t + ';' + s);
        });
        $('#' + txtSearch).change(function () {
            $('#' + txtSearch).val('');
        });
    }
    //L2PT-98857 start
    function changePDT(tmp_id) {
    	if (tmp_id != '') {
    		var obj = {
    	            maubenhphamid: tmp_id + "",
    	        }
    	    var param = JSON.stringify(obj);
			data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.GETPDT", param);
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				$("#txtTENDICHVU").val(row.DICHVU);
				if (["47320"].includes(opt.csytid + "")) { //L2PT-116970
					$("#txtBACSI").val(row.TENBS);
					$("#txtNGAYTHUCHIEN").val(row.THOIGIANCHIDINH);
					$("#txtKHAMCHUYENKHOAPHCN").val(row.KHAMCHUYENKHOA);
					$("#txtTENCHANDOAN").val(row.CHANDOAN);
					$("#txtCHANDOANSEARCH").val(row.MACHANDOAN);
					$("#txtTENCHANDOANKT").val(row.CHANDOANPHU);
					$("#txtTENDICHVU").val(row.DICHVU_47320);
				}
			}
		}
    }
    //L2PT-98857 end
    function getRule(obj) {
        if (obj.REQUIRED == '1') {
            return 'valrule="' + (obj.PREFIX != "" ? obj.PREFIX : obj.TITLE).replace(',', '') + ',required"';
        } else {
            return '';
        }
    }

    function cleanColor() {
        var IDs = [];
        $("#divTAB").find("input,select,button,span,textarea").each(function () {
            IDs.push(this.id);
        });
        removeA(IDs, '');
        for (var i = 0; i < IDs.length; i++) {
            $("#" + IDs[i]).css({'background-color': ''});
        }
    }

    function removeA(arr) {
        var what, a = arguments, L = a.length, ax;
        while (L > 1 && arr.length) {
            what = a[--L];
            while ((ax = arr.indexOf(what)) !== -1) {
                arr.splice(ax, 1);
            }
        }
        return arr;
    }

    function getTTToDieuTri() {
        var obj = {
            khambenhid: opt.KHAMBENHID + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.L13", param);
        if (result && result.length > 0) {
            var r0 = result[0];

            // phcn lang son
            if (opt.csytid == '28600') {
                var s1 = r0.MACHANDOAN_YHCT;
                var s2 = r0.TENCHANDOAN_YHCT != '' ? '-' + r0.TENCHANDOAN_YHCT : '';
                var s3 = r0.CHANDOANPHU_YHCT != '' ? ';' + r0.CHANDOANPHU_YHCT : '';
                return s1 + s2 + s3;
            }

            // yhct lang son
            if (opt.csytid == '30680') {
                var s2 = r0.TENCHANDOAN_YHCT;
                var s3 = r0.CHANDOANPHU_YHCT != '' ? ';' + r0.CHANDOANPHU_YHCT : '';
                return s2 + s3;
            }
        }

        return "";
    }

    // L2PT-11034: to font chu cho nguoi gia yhct lang son
    function changeFontSize() {
        var IDs = [];
        var e = [];
        $(document.body).find("*").each(function () {
            IDs.push(this.id);
            e.push(this);
        });
        removeA(IDs, '');
        // for (var i = 0; i< IDs.length; i++) {
        // 	$("#" + IDs[i]).css({ 'font-size' : '2em'});
        // }

        for (var i = 0; i < e.length; i++) {
            $(e).css({'font-size': '18px'});
        }
    }

    function pickerNhanVien(txtSearch, txtTEN) {
        var hearder = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,33,0,f,l;Tên bác sỹ,FULLNAME,22,0,f,l;Chức danh/Khoa phòng,CHUCDANH,42,0,f,l";
        ComboUtil.initComboGrid(txtSearch, "PTTT.LOAD_USER_BDID", [
            {name: '[0]', value: 0}
            , {name: '[1]', value: '-1'}
        ], "600px", hearder, function (event, ui) {
            var s = ui.item.FULLNAME;
            var t = $('#' + txtTEN).val();
            $('#' + txtTEN).val(t == '' ? s : t + ';' + s);
        });

        $('#' + txtSearch).change(function () {
            $('#' + txtSearch).val('');
        });
    }

    function _caRpt(signType) {
        var _lstParamHashed = '';
        var idRow = $("#grdDS").jqGrid('getGridParam', 'selarrrow');

        if (idRow.length == 0) {
            DlgUtil.showMsg("Chưa chọn phiếu!");
            return;
        }

        idRow.forEach(function (el) {
            var idPhieu = $("#grdDS").jqGrid('getRowData', el).IDDATA;
            var sophieu = $("#grdDS").jqGrid('getRowData', el).SOPHIEU;
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: hosobenhanid
            }, {
                name: 'idphieu',
                type: 'String',
                value: idPhieu
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: rpt_code
            }];
            if (signType == '0') {
                if (idRow.length == 1) {
                    CommonUtil.openReportGetCA2(par, false);
                } else {
                    var paramHashed = CryptoJS.MD5(JSON.stringify(par).toUpperCase()).toString().toUpperCase();
                    _lstParamHashed = _lstParamHashed + "," + paramHashed;
                }
            } else {
                CommonUtil.kyCA(par, signType, true, true);
                EventUtil.setEvent("eventKyCA", function (e) {
                    var msg = e.res;
                    var _code = msg.split("|")[0];
                    var _msg = sophieu + ' - ' + msg.split("|")[1];
                    if (_code == '0' && signType == '1') {
                        $.bootstrapGrowl(_msg, {
                            type: 'success',
                            delay: 2000,
                        });
                    } else {
                        DlgUtil.showMsg(_msg);
                    }
                });
            }
        });

        //in gộp
        if (_lstParamHashed != '') {
            var par = [{
                name: 'HOSOBENHANID',
                type: 'String',
                value: hosobenhanid
            }, {
                name: 'LST_PARAM_HASHED',
                type: 'String',
                value: _lstParamHashed.substring(1, _lstParamHashed.length)
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: rpt_code
            }];
            CommonUtil.openReportGetViewCa(par, false);
        }
    }

    function checkKyCa(id) {
        var _row = $("#grdDS").jqGrid('getRowData', id);
        var par = [{
            name: 'hosobenhanid',
            type: 'String',
            value: hosobenhanid
        }, {
            name: 'idphieu',
            type: 'String',
            value: _row.IDDATA
        }, {
            name: 'RPT_CODE',
            type: 'String',
            value: rpt_code
        }];

        var _check = CommonUtil.checkKyCaByParam(par);
        if (_check > '0') {
            $("#btnSua").prop("disabled", true);
            $("#btnXoa").prop("disabled", true);
            $("#btnKyCa").prop("disabled", true);
            $("#btnExportCa").prop("disabled", false);
            $("#btnHuyCa").prop("disabled", false);
        } else {
            $("#btnSua").prop("disabled", false);
            $("#btnXoa").prop("disabled", false);
            $("#btnKyCa").prop("disabled", false);
            $("#btnExportCa").prop("disabled", true);
            $("#btnHuyCa").prop("disabled", true);
        }
    }
    
    function change_obj(){
		$("input[type=checkbox]").change();
		//$("div[id^='rad']").change(); 
	}
    
    function FILL_OBJ(_containerId,_prefix,objData) {
		console.log('FILL_OBJ ', objData);
		if (objData == null) objData = new Object();
	    var _container=null;
	    if ((_containerId == null) || (_containerId.length <= 0)) { 
	    	_container =  $(document);
	    }
	    else { 
	    	_container =  $('#' + _containerId);
	    }
	    if ((_container == null) || (_container.length <= 0)) return;
	    var ctl_type=["txt","cbo","hid","chk","lbl","mul","rad"];
	    for(var j=0;j<ctl_type.length;j++) {
	    	_type=ctl_type[j];
	    	var ctl_ar=[];
	    	
			if(_type=='rad') 
				ctl_ar=_container.find("div[id^='"+_prefix+_type+"']");		    
			else
				ctl_ar=_container.find("input[id^='"+_prefix+_type+"'],textarea[id^='"+_prefix+_type+"'],select[id^='"+_prefix+_type+"'],label[id^='"+_prefix+_type+"']");
	 
		    for(var i=0;i<ctl_ar.length;i++) {
		    	var ctl=ctl_ar[i];
		    	var fldName=ctl.id.substring((_prefix+_type).length);
		    	//console.log('fldName='+fldName);
		    	if(objData[fldName.toUpperCase()] != null)
		    	{
			    	if(_type=='chk') {
			    		if(objData[fldName.toUpperCase()]==1) {
			    			ctl.checked=true;
			    		}
			    		else {
			    			ctl.checked=false;
			    		}
			    	} 
			    	else if(_type=='rad'){	
						console.log('  ---- fill rad: rad'+fldName +' giá trị= '+ objData[fldName.toUpperCase()] );
						if (objData[fldName.toUpperCase()]){
							$("#rad"+fldName+" [value="+objData[fldName.toUpperCase()]+"]").prop('checked', true).change(); 
						}
						else{
							
						}
			    	}
			    	else if(_type=='lbl'){
			    		ctl.textContent = this.unescape(objData[fldName.toUpperCase()]);
			    	}
			    	else if(_type=='mul') {
			    		var _pid=objData[fldName.toUpperCase()];
						var val_ar=_pid.split(",");
						$(ctl).val(val_ar);
			    	}
			    	else
			    	{
			    		//var strVal=objData[fldName.toUpperCase()];
			    		//strVal=strVal.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
			    		//strVal=strVal.replace(/&LT;/g,'<').replace(/&GT;/g,'>').replace(/&AMP;/g,'&');
		    			//ctl.value = strVal;
			    		ctl.value = this.unescape(objData[fldName.toUpperCase()]);;
			    	}
			    	//console.log($(ctl).attr("id")+".exttype="+$(ctl).attr("exttype"));
			    	if($(ctl).attr("exttype")=='cbg') {
			    		//console.log($(ctl).attr("id")+".exttype="+$(ctl).attr("exttype")+" objData="+objData[fldName.toUpperCase()]);
			    		var _data=objData[fldName.toUpperCase()];
			    		if(_data!=undefined && _data!=null && _data!='')
			    			$(ctl).combogrid("setValue",objData[fldName.toUpperCase()]);
		    		}
		    	} 
		    }
	    }
		
		return true;
	}
	
	function SAVE_OBJ(_containerId,_prefix, objData) {
	    if (objData == null) objData = new Object();
	    var _container=null;
	    if ((_containerId == null) || (_containerId.length <= 0)) {
	    	_container =  $(document);
	    }
	    else {
	    	_container =  $('#' + _containerId);
	    }
	    
	   
	    if ((_container == null) || (_container.length <= 0)) return;
	    var ctl_type=["txt","cbo","hid","chk", "rad","mul"];
	    for(var j=0;j<ctl_type.length;j++) {
	    	_type=ctl_type[j];
		    var ctl_ar=[]; 
		    
			if(_type=='rad') 
				ctl_ar=_container.find("div[id^='"+_prefix+_type+"']");		    
			else
				ctl_ar=_container.find("input[id^='"+_prefix+_type+"'],textarea[id^='"+_prefix+_type+"'],select[id^='"+_prefix+_type+"'],label[id^='"+_prefix+_type+"']");
		    //console.log("input[id^='"+_prefix+_type+"'],textarea[id^='"+_prefix+_type+"'],select[id^='"+_prefix+_type+"'],label[id^='"+_prefix+_type+"']");
			 
			 
		    for(var i=0;i<ctl_ar.length;i++) {
		    	var ctl=ctl_ar[i];
		    	var fldName=ctl.id.substring((_prefix+_type).length);
		    	//console.log('fldName='+fldName);
		    	if(_type=='chk') {
		    		objData[fldName.toUpperCase()] = ($(ctl).is(":checked") ? '1' : '0');
		    	}
		    	else if(_type=='rad') {
		    		//var rad_ar=$.find("[name='" + ctl.name + "']:checked");
		    		//if(rad_ar.length>0)
		    		//	objData[fldName.toUpperCase()] = rad_ar[0].value; 	
					var _val = ctl_ar.eq(i).find('input:checked').val();
					
					
					if (_val) {
						objData[fldName.toUpperCase()] = _val;
						console.log(fldName.toUpperCase() + '  =====   ' + _val);
					}
					else{
						objData[fldName.toUpperCase()] = "";
							console.log(fldName.toUpperCase() + '  =====  null '  );
					}
		    	}
		    	else if(_type=='cbo') {
		    		var ctlVal = $(ctl).val();
		    		if($.type(ctlVal)==='array' && (typeof array == "undefined" || array == null || array.length == 0)) {
		    			ctlVal = '[{}]';
		    		}
		    		objData[fldName.toUpperCase()] = ctlVal;		    		
		    		if($(ctl).attr("reffld")) {
		    			var reffld=$(ctl).attr("reffld");
		    			objData[reffld.toUpperCase()] = $(ctl).find("option:selected").text().trim();
		    		}
		    	}
		    	else if(_type=='mul') {
		    		var _pid='';
					var val_ar=[];
		    		$('#'+ctl.id+' :selected').each(function(i, sel){ 
						val_ar.push($(sel).val());
					});
					_pid=val_ar.join(",");
		    		objData[fldName.toUpperCase()] = _pid;
		    	}
		    	else {
		    		var strVal=$(ctl).val().trim();
		    		//strVal=strVal.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
		    		//objData[fldName.toUpperCase()] = this.escape(strVal);
					objData[fldName.toUpperCase()] = strVal;
		    		if($(ctl).attr("reffld") && $(ctl).attr("val")) {
		    			var reffld=$(ctl).attr("reffld");
		    			objData[reffld.toUpperCase()] = $(ctl).attr("val").trim();
		    		}
		    	}
		    	 
		    }
	    }
	}

}