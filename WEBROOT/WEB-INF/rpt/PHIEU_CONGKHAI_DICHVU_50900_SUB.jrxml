<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEU_CONGKHAI_DICHVU_50900" language="groovy" pageWidth="828" pageHeight="583" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="828" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="27f1d17c-d8ed-4f78-826f-955ef3922c97">
	<property name="ireport.zoom" value="1.1269722013523695"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="tiepnhanid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="denngay" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="songay" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call rpt_congkhaidichvu_50900_sub($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{tiepnhanid},$P{tungay},$P{denngay},$P{songay},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.math.BigDecimal"/>
	<field name="SOVAOVIEN" class="java.lang.String"/>
	<field name="GIOITINH_NAM" class="java.lang.String"/>
	<field name="GIOITINH_NU" class="java.lang.String"/>
	<field name="NGAYVAOVIEN" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="PHONG" class="java.lang.String"/>
	<field name="MABUONG" class="java.lang.String"/>
	<field name="GIUONG" class="java.lang.String"/>
	<field name="STT_NHOM" class="java.math.BigDecimal"/>
	<field name="TEN_NHOM" class="java.lang.String"/>
	<field name="STT_DICHVU" class="java.math.BigDecimal"/>
	<field name="TEN_DICHVU" class="java.lang.String"/>
	<field name="DON_VI" class="java.lang.String"/>
	<field name="NGAY_CHIDINH" class="java.sql.Timestamp"/>
	<field name="SO_LUONG" class="java.math.BigDecimal"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="GROUP_DATE" class="java.lang.String"/>
	<title>
		<band height="71">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="48" width="273" height="16" uuid="8007687e-3052-4f70-8e91-09844ce91125"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Khoa: "+ $P{dept_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="676" y="16" width="23" height="16" uuid="b1b8d893-fee4-4d88-98f9-38a3a4e48485"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="481" y="0" width="347" height="16" uuid="133d47e3-2255-4704-ba6a-dadd0face8c1"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên người bệnh: <b>" + ($F{TENBENHNHAN} == null ? "" : $F{TENBENHNHAN}) + "</b>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="481" y="32" width="347" height="16" uuid="60764750-c917-4045-a780-1f9b9bcdf657"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Giường: " + (
    $F{GIUONG} == null ? "                      " : $F{GIUONG}
) +" Buồng: " + (
    $F{MABUONG} == null ? "                     " : $F{MABUONG}
) + " Ngày vào viện: " + (
    $F{NGAYVAOVIEN} == null ? "" : $F{NGAYVAOVIEN}
)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="481" y="16" width="109" height="16" uuid="dad1615f-1543-48db-bbb4-ff767494a024"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày sinh: "+($F{NGAYSINH}==null?"":$F{NGAYSINH})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="481" y="48" width="347" height="16" uuid="f34612cc-3f1e-42f4-becf-bec46113a008"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: " + ($F{CHANDOAN} == null ? "" : $F{CHANDOAN})]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="702" y="18" width="14" height="12" uuid="637842be-6d89-432c-89dd-6b2a0e8d2c27"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="273" y="0" width="208" height="48" uuid="0f596136-238c-4dd4-8edb-b56271b16c56"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[PHIẾU CÔNG KHAI DỊCH VỤ
KHÁM, CHỮA BỆNH NỘI TRÚ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="32" width="273" height="16" uuid="a64d7b58-6a76-4aef-a2ef-8bac3fb45f30"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Số vào viện: " + ($F{SOVAOVIEN} == null ? "" : $F{SOVAOVIEN})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="658" y="18" width="14" height="12" uuid="0da69f88-5fab-4267-90c8-42207c15b9b4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH_NAM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="590" y="16" width="65" height="16" uuid="22eb8726-b0d3-4652-b8ff-ed504e4fd183"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Giới tính: Nam]]></text>
			</staticText>
			<rectangle>
				<reportElement mode="Transparent" x="658" y="18" width="14" height="12" uuid="68a9c096-09a4-4bd8-974f-a3ccf2dacf1c"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="16" width="273" height="16" uuid="5aa901ea-fce8-4701-b1b1-fdbeb9ca5a0d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="273" height="16" uuid="ab57a326-634f-4e61-94ef-44c9f8d3735c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["SỞ Y TẾ TỈNH NGHỆ AN"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="702" y="18" width="14" height="12" uuid="beef6869-3a5a-4556-8f5a-500744f40812"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH_NU}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<summary>
		<band height="169">
			<crosstab>
				<reportElement x="0" y="0" width="828" height="169" uuid="f56ca4b7-23aa-4d1a-b1e8-01d1b5d29b11"/>
				<crosstabHeaderCell>
					<cellContents>
						<box>
							<pen lineWidth="0.5"/>
							<topPen lineWidth="0.5"/>
							<leftPen lineWidth="0.5"/>
							<bottomPen lineWidth="0.5"/>
							<rightPen lineWidth="0.5"/>
						</box>
						<staticText>
							<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="24" height="40" uuid="637dfab7-c33f-49eb-a35a-b70bd45634d0"/>
							<box>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="true"/>
							</textElement>
							<text><![CDATA[TT]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="24" y="0" width="205" height="40" uuid="2235aada-db39-44cc-bb1b-0e36cd4e06b2"/>
							<box>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="true"/>
							</textElement>
							<text><![CDATA[Tên dịch vụ khám bệnh, chữa bệnh]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="229" y="0" width="50" height="40" uuid="700aa505-4d09-47d2-9c8b-306672811e35"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="true"/>
							</textElement>
							<text><![CDATA[Đơn vị]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="STT_NHOM_0" width="0" totalPosition="End">
					<bucket class="java.math.BigDecimal">
						<bucketExpression><![CDATA[$F{STT_NHOM}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque"/>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="279" height="40" uuid="55a8381c-f97e-4d3f-8c69-691fbf3a5b45"/>
								<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<text><![CDATA[Người lập phiếu (ghi tên vào mỗi ô)]]></text>
							</staticText>
							<staticText>
								<reportElement style="Crosstab Data Text" x="0" y="40" width="279" height="40" uuid="0f95fba9-78a1-4a1c-b797-8b6902ef82c5"/>
								<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<text><![CDATA[Ký xác nhận của Người bệnh         Người nhà]]></text>
							</staticText>
							<rectangle>
								<reportElement style="Crosstab Data Text" x="124" y="52" width="16" height="16" uuid="92e7a73b-26dc-4a4a-9e15-9d0db4c444dc"/>
								<graphicElement>
									<pen lineWidth="0.5"/>
								</graphicElement>
							</rectangle>
							<rectangle>
								<reportElement style="Crosstab Data Text" x="192" y="52" width="16" height="16" uuid="2d97a936-52b7-45a1-825b-8c48fb663db5"/>
								<graphicElement>
									<pen lineWidth="0.5"/>
								</graphicElement>
							</rectangle>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="STT_NHOM_1" width="0" totalPosition="Start">
					<bucket class="java.math.BigDecimal">
						<bucketExpression><![CDATA[$F{STT_NHOM}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents/>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="24" height="20" uuid="e185da01-64a8-4ccf-9cf9-f1ba1349689d"/>
								<box leftPadding="0">
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{STTNHOMMeasure} == null ? "" : $V{STTNHOMMeasure}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="24" y="0" width="255" height="20" uuid="24933756-a401-4411-805c-51ba1add7d3d"/>
								<box topPadding="1" leftPadding="4" bottomPadding="1"/>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TENNHOMMeasure} == null ? "" : $V{TENNHOMMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="STT_DICHVU" width="24" totalPosition="End">
					<bucket order="Descending" class="java.math.BigDecimal">
						<bucketExpression><![CDATA[$F{TEN_DICHVU}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<topPen lineWidth="0.5" lineStyle="Solid"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textField isBlankWhenNull="false">
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="24" height="20" forecolor="#000000" uuid="4ced03bd-d0c1-4b50-99ac-fd3e43a50155"/>
								<box topPadding="2" leftPadding="0" bottomPadding="2" rightPadding="0"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[" "]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="TEN_DICHVU" width="205">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TEN_DICHVU}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5" lineStyle="Solid"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="205" height="20" uuid="c0bb4fb5-c36c-45e7-a4d1-94e550db6592"/>
								<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2"/>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[("ZZAA").equals($V{TEN_DICHVU})?"":$V{TEN_DICHVU}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="DON_VI" width="50">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{DON_VI}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5" lineStyle="Solid"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="50" height="20" uuid="92e2c3bd-1775-4072-9f4c-9e899eef3d89"/>
								<box topPadding="2" leftPadding="0" bottomPadding="2" rightPadding="0"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{DON_VI}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="GROUP_DATE" height="20" totalPosition="End" headerPosition="Stretch">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{GROUP_DATE}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" forecolor="#000000" uuid="d824d95c-eeb2-4ef0-97fb-4f18b8f847ed"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{GROUP_DATE} == null ? "Số lượng (theo ngày/tháng)" : "Số lượng (theo ngày/tháng)"]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="52" height="40" forecolor="#000000" uuid="becf4dae-9c2c-49b3-8faa-d1a08d1d0061"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<text><![CDATA[Ghi chú]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<columnGroup name="NGAY_CHIDINH" height="20" totalPosition="End">
					<bucket class="java.sql.Timestamp">
						<bucketExpression><![CDATA[$F{NGAY_CHIDINH}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" forecolor="#000000" uuid="510c46c1-d0c3-467d-93d5-aee92049f724"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="8" isBold="false" isItalic="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{NGAY_CHIDINH} == null ? "" : (
    new SimpleDateFormat("dd").format($V{NGAY_CHIDINH}) + "/" + new SimpleDateFormat("MM").format($V{NGAY_CHIDINH})
)]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="SO_LUONGMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{SO_LUONG}]]></measureExpression>
				</measure>
				<measure name="STTNHOMMeasure" class="java.lang.Integer">
					<measureExpression><![CDATA[$F{STT_NHOM}]]></measureExpression>
				</measure>
				<measure name="TENNHOMMeasure" class="java.lang.String">
					<measureExpression><![CDATA[$F{TEN_NHOM}]]></measureExpression>
				</measure>
				<crosstabCell width="30" height="20">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<topPen lineStyle="Solid"/>
							<bottomPen lineStyle="Solid"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" forecolor="#000000" uuid="491aef6a-73ef-47c1-9d21-0be72e349787"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{SO_LUONGMeasure}==0?"":$V{SO_LUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="0" height="20" columnTotalGroup="NGAY_CHIDINH">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="52" height="20" columnTotalGroup="GROUP_DATE">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							<topPen lineStyle="Solid"/>
							<bottomPen lineStyle="Solid"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="52" height="20" forecolor="#000000" uuid="983d96bc-2954-43e7-8832-3a56f2ef75fd"/>
							<box>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="false"/>
							</textElement>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="30" height="0" rowTotalGroup="STT_DICHVU">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="0" height="0" rowTotalGroup="STT_DICHVU" columnTotalGroup="NGAY_CHIDINH">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="52" height="0" rowTotalGroup="STT_DICHVU" columnTotalGroup="GROUP_DATE">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="30" height="20" rowTotalGroup="STT_NHOM_1">
					<cellContents>
						<box>
							<topPen lineWidth="0.5"/>
							<bottomPen lineWidth="0.5"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="0" height="20" rowTotalGroup="STT_NHOM_1" columnTotalGroup="NGAY_CHIDINH">
					<cellContents/>
				</crosstabCell>
				<crosstabCell width="52" height="20" rowTotalGroup="STT_NHOM_1" columnTotalGroup="GROUP_DATE">
					<cellContents>
						<box>
							<pen lineWidth="0.5"/>
							<topPen lineWidth="0.5"/>
							<leftPen lineWidth="0.0"/>
							<bottomPen lineWidth="0.5"/>
							<rightPen lineWidth="0.5"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="30" height="80" rowTotalGroup="STT_NHOM_0">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="30" height="40" uuid="23241625-173c-405f-8c71-9d9aa16ae579"/>
							<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<text><![CDATA[]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="40" width="30" height="40" uuid="e3e9dbd4-9188-4bd5-904d-78b939e4a00c"/>
							<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<text><![CDATA[]]></text>
						</staticText>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="0" height="80" rowTotalGroup="STT_NHOM_0" columnTotalGroup="NGAY_CHIDINH">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="52" height="80" rowTotalGroup="STT_NHOM_0" columnTotalGroup="GROUP_DATE">
					<cellContents backcolor="#FFFFFF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="52" height="40" uuid="4a7928c9-4950-434b-8012-61f08962c775"/>
							<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="40" width="52" height="40" uuid="ca166f92-7f94-493e-b25c-6879cf5e289b"/>
							<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[]]></text>
						</staticText>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>
