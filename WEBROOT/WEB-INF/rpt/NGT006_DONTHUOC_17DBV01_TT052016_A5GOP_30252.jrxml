<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT006_DONTHUOC_17DBV01_TT052016_A5GOP_30252" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="401" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" uuid="7aa9e6e8-b14b-4277-9a60-b444a656ea92">
	<property name="ireport.zoom" value="2.143588810000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="org_tel" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call report_donthuoct52gop_30252($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{maubenhphamid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="ROWNUM" class="java.math.BigDecimal"/>
	<field name="TENPKKT" class="java.lang.String"/>
	<field name="PHONG" class="java.math.BigDecimal"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="SOPHIEUTHUOC" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="DVTUOI" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.math.BigDecimal"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANKEMTHEO" class="java.lang.String"/>
	<field name="YKIENBACSY" class="java.lang.String"/>
	<field name="DOITUONG" class="java.math.BigDecimal"/>
	<field name="NGAYRAVIEN" class="java.sql.Timestamp"/>
	<field name="BACSYKEDON" class="java.lang.String"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="HOATCHAT" class="java.lang.String"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="THUTUINPHIEU" class="java.math.BigDecimal"/>
	<field name="NDOHLUONG" class="java.lang.String"/>
	<field name="HUONGDANCHUNG" class="java.lang.String"/>
	<field name="HINHTHUC_THANHTOAN" class="java.lang.String"/>
	<group name="BACSYKEDON">
		<groupExpression><![CDATA[$F{BACSYKEDON}]]></groupExpression>
		<groupHeader>
			<band height="14">
				<textField>
					<reportElement x="9" y="0" width="392" height="14" uuid="6cafb7ed-1ca7-4742-9dd9-5655728927cf"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{BACSYKEDON}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="83" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="248" height="16" uuid="cc1604bb-f6c9-45c3-86e6-9fd771036eaf"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="16" width="248" height="16" uuid="9b9ec047-32a0-4a78-ba4c-1c70ba10243e"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dept_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="282" y="0" width="119" height="16" uuid="d233afff-55e4-460e-bad1-f3442be5b833"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BN: " + ($F{MABENHNHAN} == null ? "" : $F{MABENHNHAN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="282" y="16" width="119" height="16" uuid="7c946982-9d29-4450-900e-4c9cf11e1e34"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã ĐT: " + ($F{MAHOSOBENHAN} == null ? "" : $F{MAHOSOBENHAN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="282" y="32" width="119" height="16" uuid="3ab34da5-1430-44e1-b05c-53fefc863c1c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã xuất: " + ($F{SOPHIEUTHUOC} == null ? "" : $F{SOPHIEUTHUOC})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="45" width="248" height="16" uuid="7f0f743b-380d-42c8-b6d5-7d4ee0fb1571"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Điện thoại: " + ($P{org_tel}==null?"":$P{org_tel})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="62" width="401" height="20" uuid="8da80d35-0f21-4507-9bd6-1ce029c9bfe5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["ĐƠN THUỐC " + ($F{DOITUONG} == null ? "" : (
    $F{DOITUONG}.equals("1") ? "BHYT" : (
        $F{DOITUONG}.equals("2") ? "VIỆN PHÍ" : (
            $F{DOITUONG}.equals("3") ? "DỊCH VỤ" : ""
        )
    )
))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="31" width="248" height="15" uuid="42a44587-78c3-422e-855b-7b3f10cb02e2"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng: " + ($F{TENPKKT}==null?"":$F{TENPKKT})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="105" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="30" width="401" height="15" uuid="e3603149-9b39-45ff-add7-f3b46b9ad3c8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: "+($F{DIACHI}==null?"":$F{DIACHI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="60" width="401" height="15" uuid="e620f73f-a1a9-4905-b3c5-35b5b49a3363"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: "+ ($F{CHANDOANVAOKHOA}==null?"":$F{CHANDOANVAOKHOA})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="75" width="401" height="15" uuid="9961d09d-4540-45ae-b128-92b14bffa030"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán kèm theo: "+($F{CHANDOANKEMTHEO} == null ? "" : $F{CHANDOANKEMTHEO})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="45" width="401" height="15" uuid="9515f83b-5f2a-4985-bbd3-c6b60a51f632"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số thẻ BHYT: <b>" +(
    $F{SOTHE} == null ? "" : (
        $F{SOTHE}.substring(0,2) + "-" +
        $F{SOTHE}.substring(2,3) + "-" +
        $F{SOTHE}.substring(3,5) + "-" +
        $F{SOTHE}.substring(5,7) + "-" +
        $F{SOTHE}.substring(7,10) + "-" +
        $F{SOTHE}.substring(10,$F{SOTHE}.length())
    )
) + "</b>"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="90" width="69" height="15" uuid="76a60cbd-085b-434a-98d6-feb6c9268c67"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[Thuốc điều trị:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="401" height="15" uuid="80aa70f1-c2e7-4679-9e48-75408d09274b"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên người bệnh: " + "<b>" + ($F{TENBENHNHAN} == null ? "" : $F{TENBENHNHAN}) + "</b>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="15" width="69" height="15" uuid="dc2bd3ff-b8f7-4c13-bb76-8d60dd785d92"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + ($F{TUOI} == null ? "" : $F{TUOI}) + " " + (
	$F{DVTUOI} == null ? "" : (
		$F{DVTUOI} == 1 ? " tuổi" : (
			$F{DVTUOI} == 2 ? " tháng" : (
				$F{DVTUOI} == 3 ? " ngày" : (
					$F{DVTUOI} == 4 ? " giờ" : ""
				)
			)
		)
	)
)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="69" y="15" width="332" height="15" uuid="e3d1d22f-031b-453a-8863-ca5a9d0c636f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính: " + ($F{GIOITINH} == null ? "" : (
    $F{GIOITINH} == 1 ? "Nam" : "Nữ"
))]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" x="9" y="0" width="392" height="14" uuid="cdf5d025-196f-4e52-bedc-45210cb10942"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="308" y="0" width="37" height="14" uuid="a1b9cdcd-fc4e-438d-abb9-4cb98282a5ad"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TEN_DVT}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="161" y="0" width="123" height="14" uuid="bdbb6239-9b1e-415a-b4d6-d837bd6fdd28"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HOATCHAT}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="284" y="0" width="24" height="14" uuid="6c768337-5490-4989-9cce-9db048e364f9"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{SOLUONG}.length() == 1 ? "0" + $F{SOLUONG} : $F{SOLUONG}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="0" width="18" height="14" uuid="1a90b645-4700-42e0-8654-815a0c40cc77"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="true"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ROWNUM}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="345" y="0" width="47" height="14" uuid="6e7ac95a-6a84-4d0b-8751-9201a6d4c204"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{HINHTHUC_THANHTOAN}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="18" y="0" width="143" height="14" uuid="9dcc9afd-4013-4421-92dc-8c28d482920f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
						<paragraph leftIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
				</textField>
			</frame>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="14" width="401" height="14" uuid="9a75d410-3a7f-49c5-89df-ea45f88e5415"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDANCHUNG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band height="192" splitType="Stretch">
			<textField isStretchWithOverflow="true" evaluationTime="Report" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="401" height="15" uuid="01825f19-e1cf-4a7a-94fd-169040dfff08"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đã nhận: <b>" + ($F{ROWNUM} == null ? "" : $F{ROWNUM}) + "</b> loại thuốc"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="231" y="40" width="170" height="15" uuid="4f7421d6-18ff-4eb6-b331-4b743b25c707"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[BÁC SỸ XÁC NHẬN]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="40" width="170" height="15" uuid="5e7ddfbd-b255-4511-8759-94a59b220f7e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CHỮ KÝ CỦA BỆNH NHÂN]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="231" y="25" width="170" height="15" uuid="57e11e98-3f0d-4ced-ab8a-c1d434bdac22"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + new SimpleDateFormat("dd").format($F{NGAYRAVIEN}) + " tháng " +
new SimpleDateFormat("MM").format($F{NGAYRAVIEN}) + " năm " +
new SimpleDateFormat("yyyy").format($F{NGAYRAVIEN})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="55" width="170" height="15" uuid="1df0785b-8d55-4d44-948f-ef659866102f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="231" y="55" width="170" height="15" uuid="a8aa9e3b-e417-4afa-9cc1-836672379a1d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ghi rõ họ tên)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="37" y="136" width="364" height="56" uuid="c78af8f9-1a1f-4de4-9534-4dd1079ef729"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{YKIENBACSY} == null ?
    "- Dùng thuốc theo đúng chỉ dẫn." + "\n" +
    "- Nếu có gì bất thường đến bệnh viện kiểm tra lại ngay." + "\n" +
    "- Khám lại khi người bệnh uống hết thuốc." + "\n" +
    "- Khám lại xin mang theo đơn này."
: ($F{YKIENBACSY} + "\n" +
    "- Dùng thuốc theo đúng chỉ dẫn." + "\n" +
    "- Nếu có gì bất thường đến bệnh viện kiểm tra lại ngay." + "\n" +
    "- Khám lại khi người bệnh uống hết thuốc." + "\n" +
    "- Khám lại xin mang theo đơn này."
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="136" width="37" height="14" uuid="621c9d19-5681-4f40-a80c-1bf02a069895"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[Lời dặn: ]]></text>
			</staticText>
			<textField>
				<reportElement x="231" y="117" width="170" height="14" uuid="5c727324-c3f6-4eac-826b-3b542fe18978"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONG} == 1 ? "" : (
    $F{BACSYKEDON} == null ? "" : $F{BACSYKEDON}
)]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
