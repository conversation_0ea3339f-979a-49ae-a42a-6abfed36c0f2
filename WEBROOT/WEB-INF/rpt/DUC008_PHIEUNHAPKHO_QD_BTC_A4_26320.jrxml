<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="duc_phieunhapkho" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="1fd2cc3b-a396-4fb5-9c4d-935a752054d9">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="nhapxuatid" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call duc008_phieunhapkho_26320(null,$P{[HID]},$P{[SCH]},null,$P{nhapxuatid},$P{ora_cursor})}]]>
	</queryString>
	<field name="NO" class="java.math.BigDecimal"/>
	<field name="CO" class="java.math.BigDecimal"/>
	<field name="GIO" class="java.lang.String"/>
	<field name="NGAY" class="java.lang.String"/>
	<field name="THANG" class="java.lang.String"/>
	<field name="NAM" class="java.lang.String"/>
	<field name="MAPHIEU" class="java.lang.String"/>
	<field name="TENNGUOIGIAO" class="java.lang.String"/>
	<field name="NGAYCT" class="java.lang.String"/>
	<field name="THANGCT" class="java.lang.String"/>
	<field name="NAMCT" class="java.lang.String"/>
	<field name="TENNCC" class="java.lang.String"/>
	<field name="KHONHAP" class="java.lang.String"/>
	<field name="TONGTIENCHU" class="java.lang.String"/>
	<field name="TEN_THUOC" class="java.lang.String"/>
	<field name="MA_THUOC" class="java.lang.String"/>
	<field name="DON_VI_TINH" class="java.lang.String"/>
	<field name="SO_LUONG" class="java.math.BigDecimal"/>
	<field name="DON_GIA" class="java.math.BigDecimal"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="HSD" class="java.lang.String"/>
	<field name="SO_LO" class="java.lang.String"/>
	<field name="SOLUONG_THUCNHAP" class="java.math.BigDecimal"/>
	<field name="SOCT" class="java.lang.String"/>
	<field name="TENDOITAC" class="java.lang.String"/>
	<field name="LOAITHUOC" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<variable name="v_thanhtien" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="v_soluong_ct" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SO_LUONG}]]></variableExpression>
	</variable>
	<variable name="v_soluong_tn" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG_THUCNHAP}]]></variableExpression>
	</variable>
	<variable name="v_dongia" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DON_GIA}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="184">
			<textField>
				<reportElement x="0" y="60" width="555" height="20" uuid="8a17067e-312a-4c76-a03e-d7648903b784"/>
				<textElement textAlignment="Center" markup="none">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHIẾU NHẬP KHO"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="284" y="0" width="271" height="60" uuid="604e8458-d220-4f38-bf5c-561d52cbd6cf"/>
				<textElement textAlignment="Center" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Mẫu số C30-HD
(Ban hành theo Thông tư số 107/2017/TT-
BTC ngày 10/10/2017 của Bộ Tài chính)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="160" width="555" height="20" uuid="c6098ed3-50de-41d1-9ee8-a40d52ba8d1e"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GHICHU}==null?"Diễn giải: ": "Diễn giải: " + $F{GHICHU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="120" width="284" height="20" uuid="877d0b69-a092-4da2-9c9b-94c506ff7cdb"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Theo số HĐ:           "+ ($F{SOCT} == null ? " ":$F{SOCT})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="284" height="20" isRemoveLineWhenBlank="true" uuid="38d688f3-30af-4645-af1f-67604ee3f5d3"/>
				<textElement markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="284" height="20" uuid="b848ee39-b09a-44fa-b130-d36d81c481ba"/>
				<textElement markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="140" width="555" height="20" uuid="f17a6ba7-d9b7-4d19-b5a7-8651858d75ab"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENNCC} == null ? "Đơn vị giao hàng: ": "Đơn vị giao hàng: "+ $F{TENNCC}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="126" y="80" width="86" height="20" uuid="b76c708f-391d-4a0b-a86f-cadc0472b206"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[Mã phiếu: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="212" y="80" width="343" height="20" uuid="8deb40a1-4283-47f4-9f82-044c4b3ebaed"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAPHIEU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="363" y="61" width="192" height="19" uuid="8668aac6-3c46-436d-9de9-0a4389773c82"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOCT}==null?"":"Số chứng từ: " + $F{SOCT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="100" width="284" height="20" uuid="89e549e0-88e6-413b-9f47-2a4aacc2f83c"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên người nhận: " + $F{TENNGUOIGIAO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="284" y="100" width="271" height="20" uuid="d7a4f161-fc25-4257-bf89-9d959b07b7a9"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhập tại kho: " + $F{KHONHAP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="284" y="120" width="271" height="20" uuid="664d2c23-139e-45c0-9121-258c13120848"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày hóa đơn: " + ($F{NGAYCT} == null ? " ":$F{NGAYCT}) + "/"+ ($F{THANGCT} == null ? " ":$F{THANGCT}) +"/" + ($F{NAMCT} == null ? " ":$F{NAMCT})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="40" width="284" height="20" uuid="8e719b73-3bcd-4d79-ba91-e8b46cb83a8d"/>
				<textElement markup="none">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dept_name}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="58">
			<staticText>
				<reportElement x="0" y="0" width="22" height="58" uuid="77348e56-132c-4a8a-9f84-a892c20c3c5d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="22" y="0" width="253" height="58" uuid="8d92fc62-3f46-406b-84ea-b8b839bed066"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên thuốc, nồng độ, hàm lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="415" y="0" width="60" height="58" uuid="bbc5abbc-6a9e-4a93-bfcd-01fb929aefd3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá (đồng)]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="0" width="90" height="29" uuid="52063489-ec24-476f-9471-f5f577305d10"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="0" width="50" height="58" uuid="1f73324c-75de-4ec7-8542-35335304e879"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn vị tính]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="29" width="45" height="29" uuid="1e4da004-753a-4acb-8b17-ab542a8db0f9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Theo chứng từ]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="29" width="45" height="29" uuid="36016dbd-9e38-44f0-95e0-3aad659334d9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Thực nhập]]></text>
			</staticText>
			<staticText>
				<reportElement x="475" y="0" width="80" height="58" uuid="dd64dede-84bc-42ff-8e9a-6f1b9d15694e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền (đồng)]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="22" height="20" uuid="a8dfdc01-1236-4bef-be7a-d9991f7a44f9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="22" y="0" width="253" height="20" uuid="e097d57d-139c-4f40-ba2b-7052647578e0"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_THUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="275" y="0" width="50" height="20" uuid="86f21ae2-f467-4483-8ee3-7b74608c9b95"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_VI_TINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="325" y="0" width="45" height="20" uuid="af089638-2e4e-4de9-9649-822539f5ebbd"/>
				<box rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SO_LUONG}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="415" y="0" width="60" height="20" uuid="3866a23c-7398-47de-8e9f-6fc098091aa4"/>
				<box rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{DON_GIA}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="475" y="0" width="80" height="20" uuid="2044ce55-e25f-4bc1-bb4b-ef2375e8bc2c"/>
				<box rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{THANH_TIEN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="370" y="0" width="45" height="20" uuid="c6b09c91-6003-4594-b4a8-c27a2be44073"/>
				<box rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SOLUONG_THUCNHAP}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="202">
			<textField isStretchWithOverflow="true" pattern=" #,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="72" width="555" height="25" uuid="0a1b72ec-3b63-4869-aaa6-295ca6f400c9"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng số tiền (Viết bằng chữ): " + ($F{TONGTIENCHU} == null ? " ":$F{TONGTIENCHU}.trim()) + " đồng."]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="275" y="0" width="140" height="18" uuid="c8f7512e-c552-4f4c-9bb2-68c8c4afd78a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tiền hàng:]]></text>
			</staticText>
			<textField pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="415" y="0" width="140" height="18" uuid="487e01b8-89df-44df-abe2-3fa84d16d3cb"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{v_thanhtien}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="284" y="97" width="271" height="20" uuid="527802fc-0e0e-4dd8-939d-45ef6adb32cc"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + $F{NGAY} + " Tháng "+ $F{THANG} + " Năm " +$F{NAM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="419" y="117" width="136" height="20" uuid="cdc207f0-a2be-4ff1-a45f-ad745935208b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Kế toán trưởng]]></text>
			</staticText>
			<staticText>
				<reportElement x="149" y="117" width="135" height="20" uuid="f873694d-a880-49a6-8592-c0207cd7c194"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Trưởng khoa]]></text>
			</staticText>
			<staticText>
				<reportElement x="149" y="137" width="135" height="20" uuid="0ff8bc89-c414-426a-9ce5-bff7b978b7cb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="284" y="117" width="135" height="20" uuid="0756b821-dade-4ff7-a715-85694373f94c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Thủ kho]]></text>
			</staticText>
			<staticText>
				<reportElement x="284" y="137" width="135" height="20" uuid="48fbba23-f66c-4a03-aaa7-18ec797c0899"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="137" width="149" height="20" uuid="68ab1e7d-cc6a-468e-b296-c5aba1a4fad2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="117" width="149" height="20" uuid="e43c9e3e-8cbf-4892-a1fa-e4cf21519f75"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Người lập]]></text>
			</staticText>
			<staticText>
				<reportElement x="419" y="137" width="136" height="20" uuid="0a3c3553-be16-42dd-a75c-4f839c0beedf"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, họ tên)]]></text>
			</staticText>
			<textField pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="415" y="18" width="140" height="18" uuid="030ee681-ae63-45f4-8327-6dde738817d7"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[0]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="275" y="18" width="140" height="18" uuid="9d941e62-e874-4fec-a564-9ddd8bc6f838"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thuế VAT: (0%)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="275" y="36" width="140" height="18" uuid="99af081d-46f6-40cf-896a-74acf0be7ee5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Chiết khấu:]]></text>
			</staticText>
			<textField pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="415" y="36" width="140" height="18" uuid="00e4e1d8-b458-4102-ab9f-a1ea3b75d2e9"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[0]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="415" y="54" width="140" height="18" uuid="5bdb02b6-5067-4024-a94a-935cc53bef51"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{v_thanhtien}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="275" y="54" width="140" height="18" uuid="9d80dbe3-9e16-4004-84f3-b17b768ce1f2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Giá trị thanh toán:]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
