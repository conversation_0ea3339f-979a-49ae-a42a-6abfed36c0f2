/*
 0. <PERSON><PERSON> xung các biến khoa phòng/chuyển phòng
 1. <PERSON><PERSON><PERSON> hàm trong jqGrid, hỗ trợ phân trang
 2. <PERSON><PERSON><PERSON> mầu/setIcon cho 1 row or cell
 3. Edit/Delete/Add/Custom Button on row
 4. Validate focus vào control bị lỗi-->danh sách container="divMain,divSub,..."
 5. Thông báo -->auto collapsed
 6. Custom Toolbar,
 7. lookup-->tự động set dữ liệu vào control
 8. Tăng code sql lên 20
 9. <PERSON><PERSON><PERSON> chức năng quản trị
 */

// Browser Window Size and Position
// copyright Stephen <PERSON>, 3rd Jan 2005, 8th Dec 2005
// you may copy these functions but please keep the copyright notice as well

var findClosest = function (el, fn) {
    //console.log('findClosest='+el.id+' help='+$(el).attr("help"));
    return el && (fn(el) ? el : findClosest(el.parentNode, fn));
};

function getURL(_hlink) {
    if (_hlink.indexOf('?') >= 0)
        _hlink = _hlink + '&ssid=' + ssid;
    else
        _hlink = _hlink + '?ssid=' + ssid;
    return _hlink;
}

function handler() {
    if (this.readyState === this.DONE) {
        if (this.status === 200) {
            // this.response is a Blob, because we set responseType above
            var data_url = URL.createObjectURL(this.response);
            window.open(data_url, '_blank');
        } else {
            console.error('no pdf :(');
        }
    }
}

function directNotUrlReport(result, rptCode, rptParam, rptJsonParam, idIframe,
                            isPrintDirect, isCountPrt) {
    if (result != undefined && result != null && result != '' && result != []) {
//		let dat = cvtDataBase64toBlob(result);		
        let _blobUrl = URL.createObjectURL(result);
        CommonUtil.fileURL = _blobUrl;
        if (isPrintDirect == undefined || isPrintDirect == null) {
            window.open(_blobUrl, '_blank');
            return;
        }
        let bu = new BrowserUtil();
        let dbrowser = bu.detectBrowser();

        if (dbrowser.isChrome) {
            if (isPrintDirect) {
                let iframe;
                if (idIframe != undefined && idIframe != null && idIframe != '') {
                    iframe = document.getElementById(idIframe);
                    if (!iframe) {
                        iframe = document.createElement('iframe');
                        iframe.setAttribute("id", idIframe);
                    }
                } else {
                    iframe = document.createElement('iframe');
                }

                document.body.appendChild(iframe);

                iframe.style.display = 'none';
                iframe.onload = function () {
                    setTimeout(function () {
                        iframe.focus();
                        iframe.contentWindow.print();
                    }, 1);
                };
                iframe.src = _blobUrl;
                if (result != null && result != undefined && isCountPrt) {
                    let param = rptCode + '$' + rptParam + '$' + rptJsonParam;
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("INS.RPT.CC.01", param);
                }

            } else {
                window.open(_blobUrl + "#toolbar=0&navpanes=0", '_blank');
            }
        } else {
            alert('Chưa hỗ trợ trình duyệt này!!!');
        }
    } else {
        alert('Không có dữ liệu trả về!');
    }
}

function directReport(result, rptCode, rptParam, rptJsonParam, idFrame, isPrintDirect, isCountPrt) {
    // if (CommonUtil.fileURL) {
    //     console.log('revokeObjectURL=' + CommonUtil.fileURL);
    //     URL.revokeObjectURL(CommonUtil.fileURL);
    // } else {
    //     console.log('NOT revokeObjectURL');
    // }
    directNotUrlReport(result, rptCode, rptParam, rptJsonParam, idFrame, isPrintDirect, isCountPrt);
}

CommonUtil = {
    setEnabled: function (_ena, _dis) {
        for (var i = 0; i < _ena.length; i++) {
            $("#" + _ena[i]).attr('disabled', false);
        }
        for (var i = 0; i < _dis.length; i++) {
            $("#" + _dis[i]).attr('disabled', true);
        }
    },
    disableDebug: function () {
        console.log('disableDebug');
        console.log('menubar=' + JSON.stringify(window.menubar));
        if (window.menubar.visiable == true || window.toolbar.visiable == true) {

            //window.location.href=="login/login.jsp";
            //return false;
        }
        $(document).bind("contextmenu", function (e) {
            //return false;
            return true;
        });
        $(document).keydown(function (event) {
            if (event.keyCode == 123) {
                return false;
            } else if (event.ctrlKey && event.shiftKey && event.keyCode == 73) {
                return false;  //Prevent from ctrl+shift+i
            }
        });
    },
    initHelp: function (def_hlp) {
        Mousetrap.bind(['f2'], function (e) {
            //console.log('mousetrap f2');
            var $focused = $(':focus');
            var $focused_id = $focused.attr("id");
            var _$help = findClosest($focused[0], function (el) {
                return ($(el).attr("help") != undefined);
            });

            //console.log('mousetrap f2 1$help='+_$help.help);
            var $help = $(_$help);
            if (_$help == undefined) {
                $help = $("div.tab.active");//.find("*[help]:first")
            }
            console.log('mousetrap f2 1$help=' + $help.attr("help") + ' of ' + $focused.attr("id"));
            var _help = $help.attr("help");
            if (!_help) {
                console.log('initHelp not found FOCUSED HELP=');
                $help = $("body *[help]:first");
                _help = $help.attr("help");
            }
            if (_help) {
                console.log('mousetrap f2 xxx $help=' + _help);
                window.open('../help/help.jsp?func=' + _help, '_blank');
            } else {
                window.open('../help/help.jsp?func=' + def_hlp, '_blank');
                console.log('mousetrap f2 not found $help');
            }
            return false;
        }, undefined, true);
    },

    decode: function (info) {
        var aesUtil = new AesUtil();
        var decrypt = aesUtil.decrypt(info);
        return JSON.parse(decrypt);
    },
    encode: function (info) {
        var aesUtil = new AesUtil();
        var encrypt = aesUtil.encrypt(info);
        return encrypt;
    },
    fromHex: function (hex) {
        var str;
        try {
            str = decodeURIComponent(hex.replace(/(..)/g, '%$1'));
        } catch (e) {
            str = hex;
            console.log('invalid hex input: ' + hex);
        }
        return str;
    },

    toHex: function (str) {
        var hex;
        try {
            hex = unescape(encodeURIComponent(str))
                .split('').map(function (v) {
                    return v.charCodeAt(0).toString(16);
                }).join('');
        } catch (e) {
            hex = str;
            console.log('invalid text input: ' + str);
        }
        return hex;
    },
    replaceAll: function (str, find, replace) {
        return str.replace(new RegExp(find, 'g'), replace);
    },
    /*
    openReport: function (_ctn,_code,_type,_par_ar) {
        var par_data=JSON.stringify(_par_ar);
        var par_str=window.btoa(unescape(encodeURIComponent(par_data)));
        //var _url="../report/parameter/ParamBuilder?code="+ _code+"&filetype="+_type+"&calltype=ajax&reporttype=JasperReport&reportParam="+ par_str;
        var _url="../report/directReportX.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str;
        //var url_report="http://localhost:8080/dreport/";
        //var _url = url_report+"report/directReport.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str+"&opt=1&pid=1&cid=3&uid=1&usrid=1&db_name=jdbc/HISL2DS&db_schema=HIS_DATA2&province_id=1";

        //code=NGT039_HOADONDCT_A4&filetype=pdf&calltype=ajax&reporttype=JasperReport&reportParam=W3sibmFtZSI6InBoaWV1dGh1aWQiLCJ0eXBlIjoiU3RyaW5nIiwidmFsdWUiOiI1NTIifV0=
        console.log('openReport._url='+_url);
        if(_ctn=='window') {
            window.open(_url);
        }
        else {
            document.getElementById(_ctn).src = _url;
        }
    },
    */
//printPdf : function (url, isPrintDirect) {
//		  var iframe = this._printIframe;
//		  if (!this._printIframe) {
//		    iframe = this._printIframe = document.createElement('iframe');
//		    document.body.appendChild(iframe);
//
//		    iframe.style.display = 'none';
//		    iframe.onload = function() {
//		      setTimeout(function() {
//		        iframe.focus();
//		        iframe.contentWindow.print();
//		      }, 1);
//		    };
//		  }
//
//		  iframe.src = url;
//		},

    openReportGet: function (_ctn,_code,_type,_par_ar, isPrintDirect, isCountPrt) {
        if (jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H101.07", _code) == 1) {
            let hosobenhanid;
            let parMBP = _par_ar.find(el2 => el2.name.toUpperCase() === "I_MAUBENHPHAMID");
            if (_par_ar.find(el => el.name.toUpperCase() === "HOSOBENHANID")) {
                let pars1 = _par_ar.find(el2 => el2.name.toUpperCase() === "HOSOBENHANID");
                hosobenhanid = pars1.value;
                _par_ar.push({
                    name : 'HOSOBENHANID',
                    type : 'String',
                    value : pars1.value
                });
                _par_ar.push({
                    name : 'RPT_CODE',
                    type : 'String',
                    value : _code
                });
                if (hosobenhanid && hosobenhanid != '0') {
                    // thực hiện gọi in phiếu bằng dữ liệu XML
                    CommonUtil.openReportEmr(_par_ar, isPrintDirect);
                    return;
                }
            } else {
                let objectValue = _par_ar.reduce(function (acc, obj) { acc[obj.name.toUpperCase()] = obj.value; return acc}, {});
                hosobenhanid = jsonrpc.AjaxJson.ajaxCALL_SP_S("GET.HSBAID", JSON.stringify(objectValue));
                _par_ar.push({
                    name : 'HOSOBENHANID',
                    type : 'String',
                    value : hosobenhanid
                });
            }
            if (parMBP && parMBP.value && !_par_ar.find(el2 => el2.name.toUpperCase() === "MAUBENHPHAMID")) {
                _par_ar.push({
                    name : 'MAUBENHPHAMID',
                    type : 'String',
                    value : parMBP.value
                });
            }
            _par_ar.push({
                name : 'RPT_CODE',
                type : 'String',
                value : _code
            });
            if (hosobenhanid && hosobenhanid != '0') {
                // thực hiện gọi in phiếu bằng dữ liệu XML
                CommonUtil.openReportEmr(_par_ar, isPrintDirect);
                return;
            }
        }
        var par_data=JSON.stringify(_par_ar);
        var par_str=window.btoa(unescape(encodeURIComponent(par_data)));
        par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
        var uuid = jsonrpc.AjaxJson.getUUID();
        var _sql_par = [];
        _sql_par.push({"name": "[0]", value: _code});
        var file_prefix = jsonrpc.AjaxJson.getOneValue("RPT.GET.PREFIXFN", _sql_par);
        if (file_prefix && file_prefix != null && file_prefix != "" && file_prefix != "null") {
            var _url = "../report/DirectReport?code=" + _code + "&filetype=" + _type + "&reportParam=" + par_str + "&uuid=" + uuid + "&file_prefix=" + file_prefix;
            window.open(_url);
        } else {
            var _url="../report/DirectReport?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str+"&uuid=" + uuid;
            //console.log('openReport._url='+_url);
            $.ajax({
                url: _url,
                type: "GET",
                dataType: 'binary',
                success: function(result) {
                    //called when successful
                    if(_ctn == 'window') {
                        directReport(result, _code, par_str, par_data, null, isPrintDirect, isCountPrt);
                    }
                }
            });
        }
    },

    openReportGetMultiple: function (params){
        if (Array.isArray(params)) {
            let uuid = jsonrpc.AjaxJson.getUUID();
            let promises = Array.from(params).map(pr => {
                let par_data = JSON.stringify(pr.par_str);
                let par_str = window.btoa(unescape(encodeURIComponent(par_data)));
                par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
                let _url = "../report/DirectReport?code=" + pr.code + "&filetype=pdf&reportParam=" + par_str + "&uuid=" + uuid;
                return fetch(_url).then(res => res.arrayBuffer()).then(doc => {
                    return {
                        document: doc,
                        rptCode: pr.code,
                        param: pr.par_str
                    }
                });
            })
            response = Promise.all(promises).then(PDFUtil.mergePDFDocuments);
            response.then(function (data) {
                var fileURL = URL.createObjectURL(new Blob([data], { type: "application/pdf" }));
                window.open(fileURL);
            }, function (err) {
                console.error(err);
                alert("Đã có lỗi xảy ra.");
            });
        } else {
            console.log(params);
            alert("Sai tham số.")
        }
    },

    openReportGetBlob: function (_code, _par_ar, success, error) {
        var par_data = JSON.stringify(_par_ar);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
        var uuid = jsonrpc.AjaxJson.getUUID();
        var _sql_par = [];
        _sql_par.push({"name": "[0]", value: _code});
        var _url = "../report/DirectReport?code=" + _code + "&filetype=pdf&reportParam=" + par_str + "&uuid=" + uuid;
        $.ajax({
            url: _url,
            type: "GET",
            dataType: 'binary',
            success: function (result) {
                if (typeof success === 'function') success(result);
            },
            error: function (err) {
                console.error(err);
                if (typeof error === 'function') error(err);
            }
        });
    },

    openReportGetCA2: function (params, isPrintDirect) {
        if (!Array.isArray(params)) {
            alert("In thất bại: [Tham số không hợp lệ]");
            return;
        }

        if (params.length < 1) {
            alert("In thất bại: [Tham số không được phép rỗng]");
            return;
        }

        if (!Array.isArray(params[0])) {
            params = [params];
        }

        var request = {
            params: params,
            types: "HTML;PDF"
        };

        $.ajax({
            url: "/vnpthis/carptrender",
            type: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(request),
            success: function (respObj) {
                try {
                    let linkOHRenders = JSON.parse(respObj);
                    if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                        linkOHRenders.forEach(el => window.open(el))
                    } else {
                        DlgUtil.showError(`Không có thông tin phiếu ký.`, 10000);
                    }
                } catch (e) {
                    try {
                        if (respObj.code == "0") {
                            let linkOHRenders = respObj.data;
                            if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                linkOHRenders.forEach(el => window.open(el))
                            } else {
                                DlgUtil.showError(`Không có thông tin phiếu ký.`, 10000);
                            }
                        } else {
                            DlgUtil.showError(`${respObj.message} (${respObj.code})`, 10000);
                        }
                    } catch (e2) {
                        alert("Đã có lỗi xảy ra!");
                    }
                }
            },
            error: function (msg) {
                alert("In phiếu thất bại!");
            }
        });
    },

    openReportGetCA3: function (params, isPrintDirect) {
        let _params = params;
        if (Array.isArray(params)) {
            _params = params.join();
        }
        let _types = "HTML;PDF"
        $.ajax({
            url: "/vnpthis/carptrender?paramhashed=" + _params + "&types=" + _types,
            type: "GET",
            contentType: "application/json; charset=utf-8",
            success: function (result) {
                try {
                    let linkOHRenders = JSON.parse(result);
                    linkOHRenders.forEach(el => window.open(el))
                } catch (e) {
                    try {
                        if (result.code == "0") {
                            let linkOHRenders = result.data;
                            if (linkOHRenders && Array.isArray(linkOHRenders) && linkOHRenders.length > 0) {
                                linkOHRenders.forEach(el => window.open(el))
                            } else {
                                DlgUtil.showError(`Không có thông tin phiếu ký.`, 10000);
                            }
                        } else {
                            DlgUtil.showError(`${result.message} (${result.code})`, 10000);
                        }
                    } catch (e2) {
                        alert("Đã có lỗi xảy ra!");
                    }
                }
            },
            error: function (msg) {
                alert("In phiếu thất bại");
            }
        });
    },

    openReportGetViewCa: function (params, isPrintDirect, optionalParams) {
        var request = optionalParams || {};
        request.params = params;
        request.merge = 0;
        request.type = "2";
        let isRenderNew = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'RPT_RENDER_NEW_2');
        let _urlView = isRenderNew == "1" ? "/vnpthis/rptrender" : "/vnpthis/viewcareport";

        $.ajax({
            url: _urlView,
            type: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(request),
            success: function (result) {
                if (isRenderNew == "1") {
                    try {
                        if (result.code == "0" || result.code == "00") {
                            result.result.forEach(el => window.open(el))
                        } else {
                            DlgUtil.showError(result.message, 5000);
                        }
                    } catch (e) {}
                    return;
                }
                if (isPrintDirect) {
                    iframe = document.createElement('iframe');
                    document.body.appendChild(iframe);
                    iframe.style.display = 'none';
                    iframe.onload = function () {
                        setTimeout(function () {
                            iframe.focus();
                            iframe.contentWindow.document.write(result);
                            iframe.contentWindow.print();
                        }, 1);
                    };
                } else {
                    var newWindow = window.open("");
                    newWindow.document.write(result);
                    newWindow.history.pushState({"html":"","pageTitle":""},"", "viewer4");
                }
            },
            error: function (msg) {
                alert("In phiếu thất bại");
            }
        });
    },

    openReportEmr: function (params, isPrintDirect) {
        var request = {
            params: params,
            merge: "0",
            type: "3" // 1 = đã ký, 2 = gộp đã ký, 3 = chưa ký
        };
        $.ajax({
            url: "/vnpthis/rptrender",
            type: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(request),
            success: function (result) {
                try {
                    if (result.code == "0" || result.code == "00") {
                        result.result.forEach(el => window.open(el))
                    } else {
                        DlgUtil.showError(result.message, 5000);
                    }
                } catch (e) {}
            },
            error: function () {
                alert("In phiếu thất bại");
            }
        });
    },

    openReportGetCA: function (_ctn, _code, _type, _par_ar, isPrintDirect, isCountPrt) {
        var par_data = JSON.stringify(_par_ar);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
        var uuid = jsonrpc.AjaxJson.getUUID();
        var _url = "../report/ReportCAView?code=" + _code + "&filetype=" + _type + "&reportParam=" + par_str + "&uuid=" + uuid;
        window.open(_url, '_blank');
    },
    openReportGetXml: function (_ctn, _code, _type, _par_ar, isPrintDirect,
                                isCountPrt) {
        var data = CommonUtil.buildDataCA("", RestInfo.uuid, {}, _code, _par_ar)
        var par_data = JSON.stringify(data);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        var uuid = jsonrpc.AjaxJson.getUUID();
        var _url = "../report/DirectReport/xml?code=" + _code + "&filetype="
            + _type + "&reportParam=" + par_str; // +"&uuid=" + uuid
        // console.log('openReport._url='+_url);
        var xhr = new XMLHttpRequest();
        xhr.open('GET', _url);
        xhr.onreadystatechange = handler;
        xhr.responseType = 'blob';
        xhr.setRequestHeader('Authorization', uuid);
        xhr.send();
    },

    checkKyCa: function (_rptCode, _idCheck) {
        var obj = new Object();
        obj.KEY = _idCheck;
        obj.RPT_CODE = _rptCode;
        var _checkKyCa = jsonrpc.AjaxJson.ajaxCALL_SP_I('CHECK.KY.CA', JSON.stringify(obj));
        return _checkKyCa;
    },

    checkKyCaByParam: function (_param, loaiphieu, levelky) {
        var paramHashed = "";
        if (loaiphieu == 2) {
            var _reportCode = _param.find(element => element.name.toUpperCase() == 'RPT_CODE')['value'];
            var _maubenhphamid = _param.find(element => element.name.toUpperCase() == 'MAUBENHPHAMID')['value'];
            paramHashed = CryptoJS.MD5(_reportCode + "|" + _maubenhphamid).toString().toUpperCase();
        } else {
            paramHashed = CryptoJS.MD5(JSON.stringify(_param).toUpperCase()).toString().toUpperCase();
        }
        var obj = new Object();
        obj.CAHASHED = paramHashed;
        if(typeof levelky != 'undefined' && levelky != '') {
            obj.LEVELKY = levelky;
        }
        var checkCa = jsonrpc.AjaxJson.ajaxCALL_SP_I('CHECK.KY.CA2', JSON.stringify(obj));
        return checkCa;
    },

    getXmlCa: function (_param) {
        var oData = {
            params: _param
        };

        var _rs = null;

        var request = $.ajax({
            url: '/vnpthis/emrgetxml',
            type: "POST",
            data: JSON.stringify(oData),
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false
        });

        request.done(function (_response) {
            _rs = _response;
            console.log('_response', _response);
        });

        // Response Null
        if (_rs.CODE == 0) {
            return _rs.DATA;
        }
        return "";
    },

    kyCA: function (_params, _signType, _isPrint, _returnCode, _tenPhieu, _typeKyCap, _options) {
        var _width = 505;
        var _height = 280;
        var _rptCode = _params.find(element => element.name.toUpperCase() == 'RPT_CODE')['value'];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            var loaiky = row.LOAIKY;
            var catype = row.CA_TYPE;
            var loaiphieu = row.LOAIPHIEU;
            var _title = row.KIEUKY;
            if (typeof _options == "undefined") {
                var _options = new Object();
                _options.bnky = row.BNKY;
                _options.loaiphieu = row.LOAIPHIEU;
                _options.causer = '';
                _options.loaiky = loaiky;
            }
            var paramHashed = "";
            if (loaiphieu == 2) {
                var _maubenhphamid = _params.find(element => element.name.toUpperCase() == 'MAUBENHPHAMID')['value'];
                paramHashed = CryptoJS.MD5(_rptCode + "|" + _maubenhphamid).toString().toUpperCase();
            } else {
                paramHashed = CryptoJS.MD5(JSON.stringify(_params).toUpperCase()).toString().toUpperCase();
            }
            _options.paramHashed = paramHashed;
            var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";

            if (loaiky == '1' || _typeKyCap == '1') {
                if (_signType == '1' || _typeKyCap == '1') {
                    // _isPrint = true;
                    _width = 650;
                    _height = 400;
                    _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM_KYCAP";
                    CommonUtil.openPopupCa(_params, _signType, _isPrint, _returnCode, _tenPhieu, _rptCode, _url, _title, catype, _width, _height, _typeKyCap, _options);
                } else {
                    var _par = [];
                    _par.push({"name": "[0]", "value": paramHashed});
                    var _check = jsonrpc.AjaxJson.getOneValue("CA.CHECK.KYTRINH", _par);
                    if (_check > 0) {
                        DlgUtil.showMsg("Phiếu đã được người trình ký. Không thể hủy!");
                    } else {
                        CommonUtil.openPopupCa(_params, _signType, _isPrint, _returnCode, _tenPhieu, _rptCode, _url, _title, catype, _width, _height, _typeKyCap, _options);
                    }
                }
            } else if (catype == '5') {
                var lstParams = [];
                lstParams.push(_params);
                //confrim khi hủy ký
                if (_signType == '2') {
                    DlgUtil.showConfirm('Bạn có muốn hủy ký số hay không?', function (flag) {
                        if (flag) {
                            CommonUtil._caRptTocken(lstParams, _signType, _isPrint, _returnCode, _title, _tenPhieu, _options);
                        } else {
                            return;
                        }
                    });
                } else {
                    CommonUtil._caRptTocken(lstParams, _signType, _isPrint, _returnCode, _title, _tenPhieu, _options);
                }
                return;
            } else if (typeof _signType === "undefined") {
                _isPrint = true;
                _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM2";
                CommonUtil.openPopupCa(_params, _signType, _isPrint, _returnCode, _tenPhieu, _rptCode, _url, _title, catype, _width, _height, _typeKyCap, _options);
            } else {
                let userCaConf = CaUtils.getCACachingConfig(_rptCode);
                let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                var KYSO_TUDONG_KYDIENTU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'KYSO_TUDONG_KYDIENTU');
                if (KYSO_TUDONG_KYDIENTU == '1' && (userCaConf
                    || (catype == '6' && hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token))) {
                    if (userCaConf) {
                        var oData = {
                            sign_type: _signType,
                            causer: userCaConf.USER_NAME,
                            capassword: userCaConf.PASS_WORD,
                            params: _params
                        };
                        var msg = CommonUtil.caRpt(oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, catype, _options);
                        var result = msg.split("|");
                        if (result.length == 2) {
                            msg = result[0];
                        }
                        EventUtil.raiseEvent("eventKyCA", {res: msg});
                        return;
                    } else {
                        var oData = {
                            sign_type: _signType,
                            causer: hisl2SmartCa.token.refresh_token,
                            capassword: hisl2SmartCa.token.access_token,
                            params: _params,
                            smartcauser: hisl2SmartCa.user.uid
                        };
                        var msg = CommonUtil.caRpt(oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, catype, _options);
                        var result = msg.split("|");
                        if (result.length == 2) {
                            msg = result[0];
                        } else if (catype == '3' || catype == '6') {
                            if (!_returnCode) {
                                msg = result[1];
                            }
                        }
                        EventUtil.raiseEvent("eventKyCA", {res: msg});

                        if (_signType == '1' && !_isPrint) {
                            CommonUtil.openReportGetCA2(_params, false);
                        }
                    }
                } else {
                    CommonUtil.openPopupCa(_params, _signType, _isPrint, _returnCode, _tenPhieu, _rptCode, _url, _title, catype, _width, _height, _typeKyCap, _options);
                }
            }
        } else {
            if (_returnCode) {
                var msg = '10|Phiếu chưa cấu hình ký số/điện tử!';
            } else {
                var msg = 'Phiếu chưa cấu hình ký số/điện tử!';
            }
            EventUtil.raiseEvent("eventKyCA", {res: msg});
        }
    },

    openPopupCa: function (_params, _signType, _isPrint, _returnCode, _tenPhieu, _rptCode, _url, _title, _catype, _width, _height, _typeKyCap, _options) {
        let _paramInput = {
            params: _params,
            typekycap : _typeKyCap,
            smartca_method: 0,
            ca_type: _catype,
            sign_type: _signType,
            rpt_code: _rptCode
        };
        let HIS_SIGN_SMART_CA_METHOD = 0;
        EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
            _catype = typeof e.catype == "undefined" ? _catype : e.catype;
            DlgUtil.close("divCALOGIN");
            if (typeof _signType === "undefined" || _signType == '') {
                _signType = e.signtype;
            }
            if (e.isSoanKy === 1) {
                if (_returnCode) {
                    var msg = '0|Trình ký thành công!';
                } else {
                    var msg = 'Trình ký thành công!';
                }
                EventUtil.raiseEvent("eventKyCA", {res: msg});
            } else if ((_options.bnky == '1' || _catype == '5') && _signType == '1') {
                var _check = CommonUtil.checkKyCaByParam(_params, _options.loaiphieu);
                if (_check == '0' || (_options.bnky == '0' && _options.loaiky == '1')) {
                    _options.username = e.username;
                    _options.password = e.password;
                    var lstParams = [];
                    lstParams.push(_params);
                    CommonUtil._caRptTocken(lstParams, _signType, _isPrint, _returnCode, _title, _tenPhieu, _options);
                    return;
                } else {
                    if (_returnCode) {
                        var msg = '0|Phiếu đã thực hiện ký số!';
                    } else {
                        var msg = 'Phiếu đã thực hiện ký số!';
                    }
                    EventUtil.raiseEvent("eventKyCA", {res: msg});
                }
            } else {
                if (_signType == '0') {
                    CommonUtil.openReportGetCA2(_params, false);
                } else {
                    let _usn = e.username;
                    let _pwd = e.password;
                    let _smartcauser = "";
                    if (_catype == '3' || _catype == '6') {
                        if (HIS_SIGN_SMART_CA_METHOD != undefined && HIS_SIGN_SMART_CA_METHOD != null && HIS_SIGN_SMART_CA_METHOD != '0') {
                            if (_paramInput.smartca_method == 1) {
                                let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                                _usn = _hisl2SmartCa.token.refresh_token;
                                _pwd = _hisl2SmartCa.token.access_token;
                                _smartcauser = _hisl2SmartCa.user.uid;
                            }
                        }
                    }

                    var oData = {
                        sign_type: _signType,
                        causer: _usn,
                        capassword: _pwd,
                        params: _params,
                        smartcauser: _smartcauser,
                        patientinfo: e.patientinfo
                    };
                    var msg = CommonUtil.caRpt(oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, _catype, _options);
                    var result = msg.split("|");
                    var caid = result[result.length -1];
                    if (result.length == 2) {
                        msg = result[0];
                    } else if(_catype == '3' || _catype == '6') {
                        if (!_returnCode) {
                            msg = result[1];
                        }
                    }
                    EventUtil.raiseEvent("eventKyCA", {res: msg});

                    // if (_catype == '3' || _catype == '6') {
                    //     if (HIS_SIGN_SMART_CA_METHOD != undefined && HIS_SIGN_SMART_CA_METHOD != null && HIS_SIGN_SMART_CA_METHOD != '0') {
                    //         if (_response.signInfo) {
                    //             try {
                    //                 let signInfo = JSON.parse(_response.signInfo);
                    //                 if (signInfo.access_token) {
                    //                     let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                    //                     if (_hisl2SmartCa && _hisl2SmartCa.token && _hisl2SmartCa.token.access_token) {
                    //                         _hisl2SmartCa.token = signInfo;
                    //                         sessionStorage.setItem("hisl2_smartca", JSON.stringify(_hisl2SmartCa));
                    //                     }
                    //                 }
                    //             } catch (e) {
                    //                 console.warn(e);
                    //                 sessionStorage.removeItem("hisl2_smartca");
                    //             }
                    //         }
                    //     }
                    // }

                    if(_catype == '3' && _signType == '1') {
                        var intervalId = null;
                        var smartCaLoaderFunction = function () {
                            console.log("smartCaLoaderFunction is running!")
                            var _sql_par = [];
                            _sql_par.push({"name": "[0]", value: caid});
                            var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
                            if (fl == 1) {
                                // bat phieu in
                                CommonUtil.openReportGetCA2(_params, false);
                                clearInterval(intervalId);
                            }
                        }
                        intervalId = setInterval(smartCaLoaderFunction, 4000);
                    }
                }
            }
        });
        EventUtil.setEvent("dlgCaLogin_close", function (e) {
            DlgUtil.close("divCALOGIN");
        });
        if (_catype == '3' || _catype == '6') {
            HIS_SIGN_SMART_CA_METHOD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SIGN_SMART_CA_METHOD');
            if (HIS_SIGN_SMART_CA_METHOD != undefined && HIS_SIGN_SMART_CA_METHOD != null && HIS_SIGN_SMART_CA_METHOD != '0') {
                let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
                    _paramInput.smartca_method = 1;
                } else {
                    EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
                        if (e.data && e.data.token && e.data.token.access_token) {
                            _paramInput.smartca_method = 1;
                        }
                        DlgUtil.close("dlgCA_SMARTCA_LOGIN");
                        let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, _title, _width, _height);
                        _popup.open("divCALOGIN");
                        return;
                    });
                    DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
                    DlgUtil.open("dlgCA_SMARTCA_LOGIN");
                    return;
                }
            }
        }

        if (_catype == '4') {
            DlgUtil.buildPopupUrl("dlgCA_FINGER_FORM", "divDlg", "manager.jsp?func=../danhmuc/CA_FINGER_FORM",
                {
                    khambenhid: _options.khambenhid,
                    rptcode: _rptCode,
                    signType: _signType
                },
                "Finger CA", 500, 220);
            DlgUtil.open("dlgCA_FINGER_FORM");
            return;
        }

        var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, _title, _width, _height);
        popup.open("divCALOGIN");
    },

    caRpt: function (_oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, _catype, _options) {
        var _msg = '';
        var _params = _oData.params;
        var hsbaObj = _params.find(element => element.name.toUpperCase() == 'HOSOBENHANID');
        var hosobenhanid = hsbaObj == undefined ? -1 : hsbaObj['value'];

        //check day tong
        EMR_ACCOUNT_LOGIN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'EMR_ACCOUNT_LOGIN');
        if (hosobenhanid != -1 && EMR_ACCOUNT_LOGIN != '0') {
            var _sql_par = [];
            _sql_par.push({"name": "[0]", value: hosobenhanid});
            var fl = jsonrpc.AjaxJson.getOneValue("EMR.CHECK.FLAGEMR", _sql_par);
            if (fl == '0') {
                var oData = {
                    HOSOBENHANID: hosobenhanid,
                    RPT_CODE: 'xmlTong'
                };

                var _rs = null;

                var request = $.ajax({
                    url: '/vnpthis/apiemrportal',
                    type: "POST",
                    data: JSON.stringify(oData),
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    async: false
                });

                request.done(function (_response) {
                    _rs = _response;
                    console.log('_response', _response);

                });
                request.fail(function (jqXHR, textStatus) {
                    alert("ERR: " + textStatus);
                    return false;
                });

                // Response Null
                if (!_rs) {
                    alert('ERR: Return NULL');
                } else if (_rs.CODE == 0) {
                    jsonrpc.AjaxJson.ajaxCALL_SP_I('EMR.UPD.FLAGEMR', hosobenhanid + "$");
                    _msg = CommonUtil._caRpt(_oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, _catype, _options);
                } else {
                    _msg = {
                        msg: 'Đẩy tổng thất bại!'
                    };
                }
            } else {
                _msg = CommonUtil._caRpt(_oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, _catype, _options);
            }
        } else {
            _msg = CommonUtil._caRpt(_oData, _rptCode, _isPrint, _tenPhieu, _returnCode, _title, _catype, _options);
        }

        return _msg;
    },

    _caRpt: function (_oData, _rptCode, _isPrint, _tenphieu, _returnCode, _title, _catype, _options) {
        var _params = _oData.params;
        var _signType = _oData.sign_type;
        var _rs = null;
        var _msg = '';

        if (typeof _tenphieu === "undefined") {
            _tenphieu = '';
        }
        if (typeof _title === "undefined") {
            _title = 'Ký số/điện tử';
        }
        //get thong tin update ky CA
        var table = '';
        var colum = '';
        var columData = '';
        var _sql_par = [];
        _sql_par.push({"name": "[0]", value: _rptCode});
        var _bdca = jsonrpc.AjaxJson.getOneValue("GET.DB.CA", _sql_par);
        if (_bdca != 'null') {
            var datadb = _bdca.split(';');
            table = datadb[0];
            colum = datadb[1];
            if (datadb.length == '3') {
                _params.forEach(function (element) {
                    if (element.name.toLowerCase().indexOf(datadb[2].toLowerCase()) != '-1') {
                        columData = element.value;
                    }
                });
            } else {
                _params.forEach(function (element) {
                    if (element.name.toLowerCase().indexOf(colum.toLowerCase()) != '-1') {
                        columData = element.value;
                    }
                });
            }
            var obj = new Object();
            obj.TABLENAME = table;
            obj.COLUMNAME = colum;
            obj.COLUMDATA = columData;
            obj.SINGTYPE = _signType;
            obj.RPT_CODE = _rptCode;
            if (_options) {
                obj.PARAM_HASHED =  _options.paramHashed;
            }
            if((_catype == '3' || _catype == '4') && _signType == '1') {
                obj.SINGTYPE = '9';
            }
        }

        var result = $.ajax({
            url: '/vnpthis/apicarpt',
            type: "POST",
            data: JSON.stringify(_oData),
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false
        }).done(function (_response) {
            _rs = _response;
        });

        // Response Null
        if (_rs == null) {
            _msg = 'Thất bại!';
            if (_returnCode) {
                return '99|' + _msg;
            } else {
                return _msg;
            }
        } else {
            if (_rs.CODE == 0 || _rs.CODE == 7) {
                if (_signType == '1') {
                    if(_catype == '3') {
                        _msg = 'Yêu cầu ký Smart CA thành công. Vui lòng xác nhận ký!';
                    } else if (_catype == '4') {
                        _msg = 'Yêu cầu ký vân tay thành công. Vui lòng lăn vân tay để ký!';
                        DlgUtil.close("dlgCA_FINGER_FORM");
                    } else {
                        _msg = _title + _tenphieu + ' thành công!';
                        if (_isPrint) {
                            CommonUtil.openReportGetCA2(_params, false);
                        }
                    }
                } else {
                    _msg = 'Hủy ' + _title + _tenphieu + ' thành công!';
                    if(_catype == '3') {
                        _msg = 'Hủy bản ký Smart CA thành công!';
                    } if (_catype == '4') {
                        _msg = 'Hủy bản ký sinh trắc vân tay thành công!';
                        DlgUtil.close("dlgCA_FINGER_FORM");
                    }
                }

                if(_rs.CODE == 7) {
                    _msg = _title + _tenphieu + ' gửi sang EMR thất bại!';
                }
                if (table != '') {
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(obj));
                }

                // Refresh token
                CaUtils.refreshSmartCAToken(_rs["SIGNINFO"], {
                    caType: _catype,
                    reportCode: _rptCode
                });
            } else {
                let userCaConf = CaUtils.getCACachingConfig(_rptCode);
                if (_rs.CODE == 1) {
                    if (_catype == '4') {
                        _msg = 'Nếu muốn ký lại cần hủy ký và yêu cầu ký lại!';
                    } else {
                        _msg = 'Phiếu' + _tenphieu + ' đã thực hiện ' + _title;
                    }
                } else if (_rs.CODE == 2) {
                    _msg = 'Phiếu' + _tenphieu + ' chưa thực hiện ' + _title;
                    if(_catype == '3') {
                        _msg = 'Phiếu chưa thực hiện ký Smart CA.';
                    } if (_catype == '4') {
                        _msg = 'Phiếu chưa thực hiện ký sinh trắc vân tay.';
                    }
                } else if (_rs.CODE == 3) {
                    _msg = 'Insert/Update ' + _title + _tenphieu + ' không thành công!';
                } else if (_rs.CODE == 4) {
                    _msg = 'Không tồn tại thông tin ký ' + _tenphieu + '!';
                } else if (_rs.CODE == 5) {
                    _msg = 'Sign_type không đúng định dạng!';
                } else if (_rs.CODE == 6) {
                    _msg = 'Tài khoản đăng nhập EMR không tồn tại!';
                } else if (_rs.CODE == 8) {
                    _msg = 'Hủy ' + _title + _tenphieu + ' thành công. Gửi sang EMR thất bại!';
                    if (table != '') {
                        jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(obj));
                    }
                } else if (_rs.CODE == 10) {
                    _msg = 'Phiếu' + _tenphieu + ' chưa cấu hình ký số/điện tử!';
                } else if (_rs.CODE == 11) {
                    _msg = _rs.MESSAGE;
                    if (_rs.MESSAGE == 'Error') {
                        _msg = 'Đăng nhập OneHealth-DigitalSign thất bại!';
                    }
                    if(userCaConf) {
                        jsonrpc.AjaxJson.ajaxCALL_SP_I("CA.CF.UPD", JSON.stringify({
                            CA_TYPE: userCaConf.CA_TYPE,
                            USER_NAME: '',
                            PASS_WORD: ''
                        }));
                    }
                } else if (_rs.CODE == 12) {
                    _msg = 'Vui lòng kiểm tra lại : Tài khoản/mật khẩu hoặc số lượt ký!';
                    if(userCaConf) {
                        jsonrpc.AjaxJson.ajaxCALL_SP_I("CA.CF.UPD", JSON.stringify({
                            CA_TYPE: userCaConf.CA_TYPE,
                            USER_NAME: '',
                            PASS_WORD: ''
                        }));
                    }
                } else if (_rs.CODE == 13) {
                    _msg = 'Tài khoản/mật khẩu không chính xác.<br> Tài khoản ký : ' + _rs.DATA;
                } else if (HisSignCA.hasCode(_rs.CODE)){
                    _msg = HisSignCA.message(_rs.CODE, {
                        MESSAGE: _rs.MESSAGE,
                        DATA: _rs.DATA,
                        SIGNTYPE: _signType == '1' ? 'ký' : 'hủy',
                        TITLE: _title,
                        NAME: _tenphieu
                    });
                } else {
                    if (_signType == '1') {
                        _msg = _rs.DATA == '' ? 'Ký ' + _title + _tenphieu + ' thất bại!' : _rs.DATA;
                    } else {
                        _msg = _rs.DATA == '' ? 'Hủy ' + _title + _tenphieu + ' thất bại!' : _rs.DATA;
                    }
                }
            }
        }
        // if (_returnCode || _catype == '3') {
        //     return _rs.CODE + '|' + _msg + '|' + _rs.DATA;
        // } else {
        //     return _msg + '|' + _rs.DATA;
        // }
        return _rs.CODE + '|' + _msg + '|' + _rs.DATA;
        // if (_returnCode || _catype == '3') {
        //     return {
        //         msg: _rs.CODE + '|' + _msg + '|' + _rs.DATA,
        //         signInfo: _rs.SIGNINFO
        //     }
        // } else {
        //     return {
        //         msg: _msg + '|' + _rs.DATA,
        //         signInfo: _rs.SIGNINFO
        //     };
        // }
    },

    _caRptTocken: function (_params, _signType, _isPrint, _returnCode, _title, _tenPhieu, _options) {
        if (typeof _options == "undefined") {
            var _options = new Object();
            _options.bnky = '';
        }
        var _rs = null;
        var _msg = '';
        var oData = {
            bn_ky: _options.bnky,
            sign_type: _signType,
            lstParams: _params,
            capassword: typeof _options.password == "undefined" ? '' : _options.password,
            levelky: typeof _options.levelky == "undefined" ? '1' : _options.levelky,
            thutuview: typeof _options.thutuview == "undefined" ? '' : _options.thutuview,
            catypelevel: typeof _options.catypelevel == "undefined" ? '' : _options.catypelevel,
            userca: _options['username'],
            pwdca: _options['password']
        };

        if (_signType != '1') { //hủy ký
            _params.forEach(function (el) {
                var _causer = '';
                if (typeof _options.username == "undefined") {
                    var _sql_par = [];
                    _sql_par.push({"name": "[0]", value: '1'});
                    _causer = jsonrpc.AjaxJson.getOneValue("HIS.GET.USERNAME", _sql_par);
                } else {
                    _causer = _options.username;
                }
                var oData = {
                    bn_ky: _options.bnky,
                    causer: _causer,
                    sign_type: _signType,
                    params: el
                };
                $.ajax({
                    url: '/vnpthis/apicarpt',
                    type: "POST",
                    data: JSON.stringify(oData),
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    async: false
                }).done(function (_response) {
                    _rs = _response;
                });
            });

            if (_rs.CODE == 0 || _rs.CODE == 7) {
                _msg = 'Hủy ' + _title + ' thành công!';
                if (_rs.CODE == 7) {
                    _msg = _title + ' gửi sang EMR thất bại!';
                }
            } else {
                if (_rs.CODE == 2) {
                    _msg = 'Phiếu chưa thực hiện ' + _title;
                } else if (_rs.CODE == 8) {
                    _msg = 'Hủy ' + _title + ' thành công. Gửi sang EMR thất bại!';
                } else {
                    _msg = 'Hủy ' + _title + ' thất bại!';
                }
            }
            if (_returnCode) {
                var msg = _rs.CODE + '|' + _msg + '|' + '0';
                EventUtil.raiseEvent("eventKyCA", {res: msg});
            } else {
                EventUtil.raiseEvent("eventKyCA", {res: _msg});
            }
        } else {
            $.ajax({
                url: '/vnpthis/apicatocken',
                type: "POST",
                data: JSON.stringify(oData),
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                async: false
            }).done(function (_response) {
                _rs = _response;
            });

            // Response Null
            if (_rs == null) {
                _msg = 'Thất bại!';
            } else {
                if (_rs.CODE == 0) {
                    var objFile = [];
                    _rs.OBJECT.myHashMap.files.myArrayList.forEach(function (el) {
                        objFile.push(el.myHashMap);
                    });
                    let agentPort = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'HIS_AGENT_PORT');
                    var objData = new Object();
                    objData.token = _rs.OBJECT.myHashMap.token;
                    objData.type = _rs.OBJECT.myHashMap.type;
                    objData.hoTen = _rs.OBJECT.myHashMap.hoTen;
                    objData.baseAgentUrl = "http://localhost:" + ((agentPort && agentPort != '0') ? agentPort : "18622");
                    objData.device = HisSignCA.patientSignMethod[_rs.OBJECT.myHashMap.patientSignMethod].device;
                    objData.ma = _rs.OBJECT.myHashMap.ma;
                    objData.OneHealthID = _rs.OBJECT.myHashMap.OneHealthID;
                    objData.files = objFile;

                    var _width = $(document).width() - 80;
                    var _height = $(window).height() - 80;
                    _msg = _title + _tenPhieu + ' thành công!';
                    var _html = '';
                    _html += '<div id="BCYYDID" style="width: 100%; display: none">';
                    _html += '<iframe  style="width:1300px;height:650px;" src="" id="BCYYDID_ifmView"	frameborder="0"></iframe>';
                    _html += '</div>';
                    $('#divHidden').html(_html)
                    let _urlSignDevice = _rs.DATA.replace("/app/onehealth/kyso/signpadv2", "/app/onehealth/kyso/sign-device");
                    document.getElementById('BCYYDID_ifmView').src = _urlSignDevice;

                    var __opts = {
                        title: "Khách hàng ký",
                        theme: 'ModalBorder',
                        closeOnEsc: false,
                        closeOnClick: false,
                        closeButton: 'title',
                        overlay: true,
                        zIndex: 10000,
                        content: $('#BCYYDID'),
                        draggable: 'title',
                        width: _width,
                        height: _height,
                    };

                    $("div[id^='jBoxID']:has(#divCALOGIN)").detach();
                    var dlgPopup = new jBox('Modal', __opts);
                    dlgPopup.open();

                    window.top.onmessage = (event) => {
                        if (event.data.message) {
                            // nhận thông báo sẵn sàng từ OneHealth, sau đó gửi data sang OH để ký
                            if (event.data.func == 'start' && event.data.message.start) {
                                var a = document.getElementById('BCYYDID_ifmView').contentWindow;
                                a.postMessage(
                                    {
                                        func: "send",
                                        obj: objData
                                    },
                                    '*'
                                );
                            }
                            // nhận dữ liệu trả về khi ký thành công
                            if (event.data.func == 'callBack') {
                                console.log(event.data.message);

                                if (event.data.message) {
                                    var oData = {
                                        data: event.data.message,
                                        bn_ky: _options.bnky,
                                        causer: typeof _options.username == "undefined" ? '' : _options.username,
                                        capassword: typeof _options.password == "undefined" ? '' : _options.password,
                                        smartcauser: typeof _options.smartcauser == "undefined" ? '' : _options.smartcauser,
                                        levelky: typeof _options.levelky == "undefined" ? '1' : _options.levelky,
                                        thutuview: typeof _options.thutuview == "undefined" ? '' : _options.thutuview,
                                        catypelevel: typeof _options.catypelevel == "undefined" ? '' : _options.catypelevel
                                    };
                                    $.ajax({
                                        url: '/vnpthis/apitockenconfirm',
                                        type: "POST",
                                        data: JSON.stringify(oData),
                                        contentType: 'application/json; charset=utf-8',
                                        dataType: "json",
                                        async: false
                                    }).done(function (_response) {
                                        _rs = _response;
                                    });

                                    try {
                                        CaUtils.refreshSmartCAToken(_rs["SIGNINFO"], {
                                            caType: null,
                                            reportCode: _params[0].find(el => el.name == "rpt_code").value
                                        });
                                    } catch (e) {}


                                    if (_rs.CODE == 0 || _rs.CODE == 7) {
                                        if (_signType == '1') {
                                            _msg = _title + ' thành công!';
                                            if (_isPrint) {
                                                _params.forEach(function (el) {
                                                    CommonUtil.openReportGetCA2(el, false);
                                                });
                                            }
                                        } else {
                                            _msg = 'Hủy ' + _title + ' thành công!';
                                        }

                                        if(_rs.CODE == 7) {
                                            _msg = _title + ' gửi sang EMR thất bại!';
                                        }
                                    } else {
                                        if (_rs.CODE == 1) {
                                            _msg = 'Phiếu' + ' đã thực hiện ' + _title;
                                        } else if (_rs.CODE == 2) {
                                            _msg = 'Phiếu' + ' chưa thực hiện ' + _title;
                                        } else if (_rs.CODE == 3) {
                                            _msg = 'Insert/Update ' + _title + ' không thành công!';
                                        } else {
                                            if (_signType == '1') {
                                                _msg = 'Ký ' + _title + ' thất bại!';
                                            } else {
                                                _msg = 'Hủy ' + _title + ' thất bại!';
                                            }
                                        }
                                    }
                                }
                                dlgPopup.close();
                                if (_returnCode) {
                                    var msg = _rs.CODE + '|' + _msg + '|' + '0';
                                    EventUtil.raiseEvent("eventKyCA", {res: msg});
                                } else {
                                    EventUtil.raiseEvent("eventKyCA", {res: _msg});
                                }
                            }
                        }
                    };
                } else {
                    if (_rs.CODE == 1) {
                        _msg = 'Phiếu đã thực hiện ký số/ký điện tử!';
                    } else if (_rs.CODE == 10) {
                        _msg = 'Phiếu chưa cấu hình ký số/ký điện tử!';
                    } else if (_rs.CODE == 4) {
                        _msg = 'Không tồn tại thông tin ký!';
                    } else if (_rs.CODE == 99) {
                        _msg = 'Upload IDG lỗi!';
                    } else if (HisSignCA.hasCode(_rs.CODE)){
                        _msg = HisSignCA.message(_rs.CODE, {
                            MESSAGE: _rs.MESSAGE,
                            DATA: _rs.DATA,
                            SIGNTYPE: _signType == '1' ? 'ký' : 'hủy',
                            TITLE: _title,
                            NAME: _tenPhieu
                        });
                    }
                    if (_returnCode) {
                        var msg = _rs.CODE + '|' + _msg + '|' + '0';
                        EventUtil.raiseEvent("eventKyCA", {res: msg});
                    } else {
                        EventUtil.raiseEvent("eventKyCA", {res: _msg});
                    }
                }
            }
        }
    },

    sendAllEmr: function (hosobenhanid, _uncheck) {
        var _msg = 'Đẩy tổng thành công!';
        //check day tong
        var _sql_par = [];
        _sql_par.push({"name": "[0]", value: hosobenhanid});
        var fl = jsonrpc.AjaxJson.getOneValue("EMR.CHECK.FLAGEMR", _sql_par);
        if (fl == '0' || _uncheck) {
            var oData = {
                HOSOBENHANID: hosobenhanid,
                RPT_CODE: 'xmlTong'
            };

            var _rs = null;

            var request = $.ajax({
                url: '/vnpthis/apiemrportal',
                type: "POST",
                data: JSON.stringify(oData),
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                async: true
            });

            request.done(function (_response) {
                _rs = _response;
                console.log('_response', _response);

            });
            request.fail(function (jqXHR, textStatus) {
                alert("ERR: " + textStatus);
                return false;
            });

            // Response Null
            if (!_rs) {
                // alert('ERR: Return NULL');
            } else if (_rs.CODE == 0) {
                _msg = 'Đẩy tổng thành công!';
            } else {
                _msg = 'Đẩy tổng thất bại!';
            }
            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('EMR.UPD.FLAGEMR', $("#hidHOSOBENHANID").val() + "$");
        } else {
            _msg = 'Phiếu đã đẩy tổng!';
        }
        return _msg;
    },

    sendAllEmr2: function (hosobenhanid, typeSender) {
        if (typeSender == '3') {
            var objData = new Object();
            var lsthosobenhanid = [];
            lsthosobenhanid.push(hosobenhanid)
            objData.LSTHOSOBENHANID = lsthosobenhanid;
            var oData = {
                FUNCTION: 'HIS_PATIENT_HANDOVER_EMR',
                DATA: objData
            };
            var result = $.ajax({
                url: '/vnpthis/api/v1/provider',
                type: "POST",
                data: JSON.stringify(oData),
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                async: false
            }).done(function (_response) {
                _rs = _response;
            });
            return;
        }
        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('EMR.INS.DETAIL', hosobenhanid + "$");
        if (fl == '1' && typeSender == '1') {
            jsonrpc.AjaxJson.ajaxCALL_SP_I('EMR.UPD.SENDER', hosobenhanid + "$");
            //đẩy tổng
            var oData = {
                HOSOBENHANID: hosobenhanid,
                RPT_CODE: 'xmlTong'
            };
            var _rs = null;

            var request = $.ajax({
                url: '/vnpthis/apijobemr',
                //apiemrportal
                type: "POST",
                data: JSON.stringify(oData),
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                async: true
            });
            //đẩy lẻ
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.DETAIL", hosobenhanid);
            for (var i = 0; i < data_ar.length; i++) {
                var oData = {
                    HOSOBENHANID: data_ar[i].HOSOBENHANID,
                    KEY1: data_ar[i].KEY1,
                    KEY2: data_ar[i].KEY2,
                    KEY3: data_ar[i].KEY3,
                    KEY4: data_ar[i].KEY4,
                    KEY5: data_ar[i].KEY5,
                    RPT_CODE: data_ar[i].RPT_CODE,
                    CSYTID: data_ar[i].CSYTID
                };
                var request = $.ajax({
                    url: '/vnpthis/apiemrrpt',
                    type: "POST",
                    data: JSON.stringify(oData),
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    async: true
                });
            }
        }
    },

    sendEmr: function (_params) {
        var _msg = '';
        //check day tong
        var hosobenhanid = _params.find(element => element.name.toUpperCase() == 'HOSOBENHANID')['value'];
        var _sql_par = [];
        _sql_par.push({"name": "[0]", value: hosobenhanid});
        var fl = jsonrpc.AjaxJson.getOneValue("EMR.CHECK.FLAGEMR", _sql_par);
        if (fl == '0') {
            _msg = 'Bệnh án chưa đẩy tổng!';
            return;
        }
        var oData = {
            sign_type: '',
            causer: '-1',
            capassword: '-1',
            params: _params
        };

        var request = $.ajax({
            url: '/vnpthis/apiemrrpt',
            type: "POST",
            data: JSON.stringify(oData),
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false
        });

        request.done(function (_response) {
            _rs = _response;
            console.log('_response', _response);

        });
        request.fail(function (jqXHR, textStatus) {
            alert("ERR: " + textStatus);
            return false;
        });

        // Response Null
        if (!_rs) {
            alert('ERR: Return NULL');
        } else if (_rs.CODE == 0) {
            _msg = 'Gửi EMR thành công!';
        } else {
            _msg = 'Gửi EMR thất bại!';
        }
        return _msg;
    },

    sendEmrDelete: function (_maubenhphamid, _rptCode) {
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.DEL.PHIEU", _maubenhphamid);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            var oData = {
                HOSOBENHANID: row.HOSOBENHANID,
                MAHOSOBENHAN : row.MAHOSOBENHAN,
                ID_PHIEU : row.IDPHIEU,
                SO_PHIEU : row.SOPHIEU,
                RPT_CODE : _rptCode
            };
            var _rs = null;

            var request = $.ajax({
                url: '/vnpthis/apiemrdelete',
                type: "POST",
                data: JSON.stringify(oData),
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                async: true
            });
        }
    },

    buildDataCA: function (type, uuid, cainfo, rptCode, parArrays) {
        return {
            type: type,
            uuid: uuid,
            cainfo: cainfo,
            content: {
                report_code: rptCode,
                params: parArrays
            },
        };
    },
    isSignCANotYet: function (data) {
        let result = "";
        $.ajax({
            url: '/vnpthis/apica',
            type: "POST",
            data: JSON.stringify(data),
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false
        }).done(function (_response) {
            result = _response;
            console.log('_response', _response);
        });

        // Response Null
        if (!result) {
            return false;
        }
        if (result.CODE != 0) {
            return false;
        }
        return true;
    },

    /**
     * Ham thuc hien in 2 file theo tu tu
     *
     * @param _codes:
     *            ma nao in truoc, thi them vao nhom truoc
     * @param _types
     * @param _par_ars
     * @param idFrames
     * @param isPrintDirect
     * @param isCountPrt
     * @returns
     */
    directReportV1: function (_codes, _types, _par_ars, isPrintDirect,
                              isCountPrt) {

        let _code = _codes[0];
        let _par_ar = _par_ars[0];
        let _type = _types[0];
        let par_data = JSON.stringify(_par_ar);
        let par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
        let uuid = jsonrpc.AjaxJson.getUUID();
        let _url = "../report/DirectReport?code=" + _code + "&filetype="
            + _type + "&reportParam=" + par_str + "&uuid=" + uuid;
        // console.log('openReport._url='+_url);
        $
            .ajax({
                url: _url,
                type: "GET",
                dataType: 'binary',
                success: function (result) {

                    // called when successful
                    if (result != []) {
//																
//								let blob = cvtDataBase64toBlob(result);
                        let _blobUrl = URL.createObjectURL(result);
                        if (isPrintDirect == undefined
                            || isPrintDirect == null) {
                            window.open(_blobUrl, '_blank');
                            return;
                        }
                        let bu = new BrowserUtil();
                        let dbrowser = bu.detectBrowser();
                        if (dbrowser.isChrome) {
                            if (isPrintDirect) {
                                var iframe = document
                                    .createElement('iframe');

                                document.body.appendChild(iframe);

                                iframe.style.display = 'none';
                                iframe.src = _blobUrl;
                                iframe.onload = function () {
                                    setTimeout(function () {
                                        iframe.focus();
                                        iframe.contentWindow.print();
                                    }, 1);

                                    let _code1 = _codes[1];
                                    let _par_ar1 = _par_ars[1];
                                    let _type1 = _types[0];
                                    if (_types.length == 2) {
                                        _type1 = _types[1];
                                    }
                                    let par_data1 = JSON
                                        .stringify(_par_ar1);
                                    let par_str1 = window
                                        .btoa(unescape(encodeURIComponent(par_data1)));

                                    let _url1 = "../report/DirectReport?code="
                                        + _code1
                                        + "&filetype="
                                        + _type1
                                        + "&reportParam="
                                        + par_str1
                                        + "&uuid="
                                        + uuid;
                                    // console.log('openReport._url='+_url);
                                    $
                                        .ajax({
                                            url: _url1,
                                            type: "GET",
                                            dataType: 'binary',
                                            success: function (
                                                result) {

                                                directNotUrlReport(
                                                    result,
                                                    _code1,
                                                    par_str1,
                                                    par_data1,
                                                    null,
                                                    isPrintDirect,
                                                    isCountPrt);

                                            }
                                        });

                                };
                                if (result != null
                                    && result != undefined
                                    && isCountPrt) {
                                    var param = _code + '$' + par_str
                                        + '$' + par_data;
                                    jsonrpc.AjaxJson.ajaxCALL_SP_I(
                                        "INS.RPT.CC.01", param);
                                }

                            } else {
                                window.open(_blobUrl
                                    + "#toolbar=0&navpanes=0",
                                    '_blank');
                            }
                        } else {
                            alert('Chưa hỗ trợ trình duyệt này!!!');
                        }
                    } else {
                        alert('Không có dữ liệu trả về!');
                    }

                }
            });
    },

    printReport: function (_ctn, _code, _type, _par_ar, _fileName, isPrintDirect, isCountPrt) {
        var isprint = 0;
        var par_data = JSON.stringify(_par_ar);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        par_str = CommonUtil.replaceAll(par_str, /\+/, '%2B');
        var uuid = jsonrpc.AjaxJson.getUUID();
        var _url = "../report/DirectReport?code=" + _code + "&filetype=" + _type + "&reportParam=" + par_str + "&uuid=" + uuid;
        //var _url="../report/directReport.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str+ "&uuid=" + uuid;
        //console.log('openReport._url='+_url);

//		if(CommonUtil.fileURL) {
//			console.log('revokeObjectURL='+CommonUtil.fileURL);
//  		    URL.revokeObjectURL(CommonUtil.fileURL);
//  	    }
//		else {
//			console.log('NOT revokeObjectURL');
//		}

        $.ajax({
            url: _url,
            type: "GET",
            dataType: 'binary',
            success: function (result) {
                //called when successful

//		    		  let blob = cvtDataBase64toBlob(result.result);
                var fileURL = URL.createObjectURL(result);
                CommonUtil.fileURL = fileURL;
                if (_fileName) {
                    var a = document.createElement('a');
                    a.href = fileURL;
                    a.download = _fileName;
                    a.click();
                } else {
                    directNotUrlReport(result, _code, par_str, par_data, null, isPrintDirect, isCountPrt);
                }


            }
        });

    },

//	inPhieuXXX: function (_ctn,_code,_type,_par_ar) {
//		var isprint = 0;
//		var par_data=JSON.stringify(_par_ar);
//		var par_str=window.btoa(unescape(encodeURIComponent(par_data))); 
//		var _url="../report/directReport.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str;
//		console.log('openReport._url='+_url);
//		
//		var iframe = this._printIframe;
//		  if (!this._printIframe) {
//		    iframe = this._printIframe = document.createElement('iframe');
//		document.body.appendChild(iframe);
//		
//		iframe.style.display = 'none';
//		    iframe.onload = function() {
//		      setTimeout(function() {
//		        iframe.focus();
//		        iframe.contentWindow.print();
//		      }, 1);
//		    };
//		  }
//		
//		iframe.src = _url;
//	},
    inNhieuPhieu: function (_ctn, _code, _type, _par_ar, _idFrm, isPrintDirect, isCountPrt) {
        /*
        var par_data=JSON.stringify(_par_ar);
        var par_str=window.btoa(unescape(encodeURIComponent(par_data)));
        var _url="../report/directReport.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str;
        console.log('openReport._url='+_url);

        var iframe = document.getElementById(_idFrm);
          if (!iframe) {
            iframe = document.createElement('iframe');
            iframe.setAttribute("id", _idFrm);

        document.body.appendChild(iframe );
        iframe.style.display = 'none';
            iframe.onload = function() {
              setTimeout(function() {
                iframe.focus();
                iframe.contentWindow.print();
              }, 1);
            };
          }

        iframe.src = _url;
        */
        var isprint = 0;
        var par_data = JSON.stringify(_par_ar);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        var uuid = jsonrpc.AjaxJson.getUUID();
//		var _url="../report/directReport.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str +"&uuid=" + uuid;
        var _url = "../report/DirectReport?code=" + _code + "&filetype=" + _type + "&reportParam=" + par_str + "&uuid=" + uuid;
//		console.log('openReport._url='+_url);

        $.ajax({
            url: _url,
            type: "GET",
//		      dataType: 'binary',
            dataType: 'binary',
            success: function (result) {
                //called when successful

                directReport(result, _code, par_str, par_data, _idFrm, isPrintDirect, isCountPrt);

                //directReport(result, _code, par_str, par_data, _idFrm, isPrintDirect, isCountPrt);
            }
        });
        return true;
    },

    openReportPost: function (_ctn, _code, _type, _par_ar) {
        var par_data = JSON.stringify(_par_ar);
        //console.log('openReport _code='+_code+' _par_ar='+par_data);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data)));
        CommonUtil.postForm("../report/directReport.jsp", {
            code: _code,
            filetype: _type,
            reportParam: par_str
        }, "POST", "_blank");
    },
    printImage: function (imagePath) {
        var width = $(window).width() * 0.9;
        var height = $(window).height() * 0.9;
        var content = '<!DOCTYPE html>' +
            '<html>' +
            '<head><title></title></head>' +
            '<body onload="window.focus(); window.print(); window.close();">' +
            '<img src="' + imagePath + '" style="width: 100%;" />' +
            '</body>' +
            '</html>';
        var options = "toolbar=no,location=no,directories=no,menubar=no,scrollbars=yes,width=" + width + ",height=" + height;
        var printWindow = window.open('', 'print', options);
        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();
        printWindow.focus();
    },
    postForm: function (path, params, method, target) {
        console.log('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxpostForm1');
        method = method || "post"; // Set method to post by default if not specified.

        // The rest of this code assumes you are not using a library.
        // It can be made less wordy if you use one.
        var form = document.createElement("form");
        form.setAttribute("method", method);
        form.setAttribute("action", path);
        //form.target="ifmViewPdf";
        form.target = target;
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", key);
                hiddenField.setAttribute("value", params[key]);

                form.appendChild(hiddenField);
            }
        }

        document.body.appendChild(form);
        form.submit();
        console.log('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxpostForm2');
    },
    loadAsync: function (_src, _cb) {
        var d1 = new $.Deferred();
        var sc = document.createElement('script');

        if (_cb !== null) {
            sc.async = true;
            sc.type = "text/javascript";
            sc.src = _src;
            if (sc.readyState) { // IE, incl. IE9
                sc.onreadystatechange = function () {
                    if (sc.readyState == "loaded" || sc.readyState == "complete") {
                        sc.onreadystatechange = null;
                        d1.resolve();
                    }
                };
            } else {
                sc.onload = function () { // Other browsers
                    d1.resolve();
                };
            }
        } else {
            sc.async = false;
            sc.type = "text/javascript";
            sc.src = _src;
            d1.resolve();
        }
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(sc, s);
        $.when(d1).then(function () {
            if (_cb !== null) _cb();
        });

    },
    toWord: function (s) {
        // American Numbering System
        var th = ['', 'nghìn', 'triệu', 'tỷ', 'nghìn tỷ'];
        // uncomment this line for English Number System
        // var th = ['','thousand','million','milliard','billion'];

        var dg = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
        var tn = ['mười', 'mười một', 'mười hai', 'mười ba', 'mười bốn', 'mười lăm', 'mười sáu', 'mười bảy', 'mười tám', 'mười chín'];
        var tw = ['hai mươi', 'ba mươi', 'bốn mươi', 'năm mươi', 'sáu mươi', 'bảy mươi', 'tám mươi', 'chín mươi'];
        s = s.replace(/[\, ]/g, '');
        if (s != parseFloat(s)) return 'not a number';
        var x = s.indexOf('.');
        if (x == -1) x = s.length;
        if (x > 15) return 'too big';
        var n = s.split('');
        var str = '';
        var sk = 0;
        for (var i = 0; i < x; i++) {
            if ((x - i) % 3 == 2) {
                if (n[i] == '1') {
                    str += tn[Number(n[i + 1])] + ' ';
                    i++;
                    sk = 1;
                } else if (n[i] == '0') {
                    if (n[i + 1] != '0') {
                        str += 'linh ' + dg[n[i + 1]] + ' ';
                    }
                    i++;
                    sk = 1;
                } else if (n[i] == 2 && n[i + 1] == 5) {
                    str += tw[n[i] - 2] + ' lăm ';
                    i++;
                    sk = 1;
                } else if (n[i] != 0) {
                    if (n[i + 1] == 1) {
                        str += tw[n[i] - 2] + ' mốt ';
                        i++;
                    } else {
                        str += tw[n[i] - 2] + ' ';
                    }
                    sk = 1;
                }
            } else {//if (n[i]!=0)
                if (n[i] == '0' && ((x - i) % 3 == 1))
                    str += '';
                else {
                    if ((x - i) % 3 == 0 && n[i] == '0' && n[i + 1] == '0' && n[i + 2] == '0') {
                        str += '';
                    } else {
                        str += dg[n[i]] + ' ';
                        if ((x - i) % 3 == 0) str += 'trăm ';
                    }
                }
                sk = 1;
            }
            if ((x - i) % 3 == 1) {
                if (sk) str += th[(x - i - 1) / 3] + ' ';
                sk = 0;
            }
        }
        if (x != s.length) {
            var y = s.length;
            str += 'phẩy ';
            for (var i = x + 1; i < y; i++) str += dg[n[i]] + ' ';
        }
        return str.replace(/\s+/g, ' ');
    }
};
CommonUtil.inPhieu = CommonUtil.printReport;
CommonUtil.openReport = CommonUtil.openReportGet;
var openReport = CommonUtil.openReport;
var loadAsync = CommonUtil.loadAsync;

function replaceAll(str, token, newToken) {
    var _token;
    var i = -1;
    var strData = str;
    if (strData == null || typeof strData == 'undefined') return "";
    if (typeof token === "string") {
        while ((i = strData.indexOf(token, i >= 0 ? i + newToken.length : 0)) !== -1) {
            strData = strData.substring(0, i) + newToken + strData.substring(i + token.length);
        }
    }
    return strData;
};

String.prototype.replaceAll = function (token, newToken, ignoreCase) {
    var _token;
    var str = this + "";
    var i = -1;

    if (typeof token === "string") {

        if (ignoreCase) {

            _token = token.toLowerCase();

            while ((
                i = str.indexOf(token, i >= 0 ? i + newToken.length : 0)) !== -1) {
                str = str.substring(0, i) + newToken + str.substring(i + token.length);
            }

        } else {
            return this.split(token).join(newToken);
        }

    }
    return str;
};

function decode(data, value, revalue) {
    if (data == value) return revalue;
    else return data;
}

function setSelectValue(ddlID, value, change) {
    var ddl = document.getElementById(ddlID);
    for (var i = 0; i < ddl.options.length; i++) {
        if (ddl.options[i].value == value) {
            if (ddl.selectedIndex != i) {
                ddl.selectedIndex = i;
                if (change) {
                    ddl.onchange();

                }
            }
            //ddl.options[i].selected=true;
            break;
        }
    }
}

function getValueById(id) {
    return document.getElementById(id).value;
}

function setValueById(id, value) {
    document.getElementById(id).value = value;
}

function setInnerById(id, value) {
    document.getElementById(id).innerHTML = value;
}

function getInnerById(id, value) {
    return document.getElementById(id).innerHTML;
}


function setFocusById(id) {
    document.getElementById(id).focus();
}

String.formatData = function (text, arData) {
    //usage: var s=String.format( "two tokens swapped, two args ({1},{0})<br />",["abc","123"]);
    //check if there are two arguments in the arguments list
    if (arData == null || arData.length == 0) {
        //if there are not 2 or more arguments there's nothing to replace
        //just return the original text
        return text;
    }
    //decrement to move to the second argument in the array
    var tokenCount = arData.length;
    for (var token = 0; token < tokenCount; token++) {
        //iterate through the tokens and replace their placeholders from the original text in order
        text = text.replace(new RegExp("\\{" + token + "\\}", "gi"), arData[token]);
    }
    return text;
};
String.format = function (text) {
    //usage: var s=String.format( "two tokens swapped, two args ({1},{0})<br />", "arg1", "arg2" );
    //check if there are two arguments in the arguments list
    if (arguments.length <= 1) {
        //if there are not 2 or more arguments there's nothing to replace
        //just return the original text
        return text;
    }
    //decrement to move to the second argument in the array
    var tokenCount = arguments.length - 2;
    for (var token = 0; token <= tokenCount; token++) {
        //iterate through the tokens and replace their placeholders from the original text in order
        text = text.replace(new RegExp("\\{" + token + "\\}", "gi"), arguments[token + 1]);
    }
    return text;
};

/**
 * Number.prototype.format(n, x, s, c)
 *
 * @param integer n: length of decimal
 * @param integer x: length of whole part
 * @param mixed   s: sections delimiter
 * @param mixed   c: decimal delimiter
 */
Number.prototype.format = function (n, x, s, c) {
    var re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\D' : '$') + ')',
        num = this.toFixed(Math.max(0, ~~n));

    return (c ? num.replace('.', c) : num).replace(new RegExp(re, 'g'), '$&' + (s || ','));
};

String.prototype.unmask = function () {
    return this.replace(/\D/g, '');
}

function currencyFormat(numbervalue) {
    try {
        return parseInt(numbervalue).format(0, 3, '.');
    } catch (e) {
        return numbervalue;
    }
}

function currencyUnFormat(strNumber) {
    return strNumber.unmask();
}

function formatNumber(number, decimals, dec_point, thousands_sep) {
    // Formats a number with grouped thousands  
    // 
    // version: 1109.2015
    // discuss at: http://phpjs.org/functions/number_format    // +   original by: Jonas Raoni Soares Silva (http://www.jsfromhell.com)
    // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
    // +     bugfix by: Michael White (http://getsprink.com)
    // +     bugfix by: Benjamin Lupton
    // +     bugfix by: Allan Jensen (http://www.winternet.no)    // +    revised by: Jonas Raoni Soares Silva (http://www.jsfromhell.com)
    // +     bugfix by: Howard Yeend
    // +    revised by: Luke Smith (http://lucassmith.name)
    // +     bugfix by: Diogo Resende
    // +     bugfix by: Rival    // +      input by: Kheang Hok Chin (http://www.distantia.ca/)
    // +   improved by: davook
    // +   improved by: Brett Zamir (http://brett-zamir.me)
    // +      input by: Jay Klehr
    // +   improved by: Brett Zamir (http://brett-zamir.me)    // +      input by: Amir Habibi (http://www.residence-mixte.com/)
    // +     bugfix by: Brett Zamir (http://brett-zamir.me)
    // +   improved by: Theriault
    // +      input by: Amirouche
    // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)    // *     example 1: number_format(1234.56);
    // *     returns 1: '1,235'
    // *     example 2: number_format(1234.56, 2, ',', ' ');
    // *     returns 2: '1 234,56'
    // *     example 3: number_format(1234.5678, 2, '.', '');    // *     returns 3: '1234.57'
    // *     example 4: number_format(67, 2, ',', '.');
    // *     returns 4: '67,00'
    // *     example 5: number_format(1000);
    // *     returns 5: '1,000'    // *     example 6: number_format(67.311, 2);
    // *     returns 6: '67.31'
    // *     example 7: number_format(1000.55, 1);
    // *     returns 7: '1,000.6'
    // *     example 8: number_format(67000, 5, ',', '.');    // *     returns 8: '67.000,00000'
    // *     example 9: number_format(0.9, 0);
    // *     returns 9: '1'
    // *    example 10: number_format('1.20', 2);
    // *    returns 10: '1.20'    // *    example 11: number_format('1.20', 4);
    // *    returns 11: '1.2000'
    // *    example 12: number_format('1.2000', 3);
    // *    returns 12: '1.200'
    // *    example 13: number_format('1 000,50', 2, '.', ' ');    // *    returns 13: '100 050.00'
    // Strip all characters but numerical ones.
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function (n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };
    // Fix for IE parseFloat(0.55).toFixed(0) = 0;
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);
}

var QueryString = function () {
    // This function is anonymous, is executed immediately and
    // the return value is assigned to QueryString!
    var query_string = {};

    var query = window.location.search.substring(1);

    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        // If first entry with this name
        if (typeof query_string[pair[0]] === "undefined") {
            query_string[pair[0]] = pair[1];
            // If second entry with this name
        } else if (typeof query_string[pair[0]] === "string") {
            var arr = [query_string[pair[0]], pair[1]];
            query_string[pair[0]] = arr;
            // If third or later entry with this name
        } else {
            query_string[pair[0]].push(pair[1]);
        }
    }
    return query_string;
}();

var subQueryString = function (urlid, divid) {
    // This function is anonymous, is executed immediately and
    // the return value is assigned to QueryString!
    var query_string = {};
    if (typeof urlid === "undefined")
        return '';

    var p = document.querySelector("#" + divid);//'contentdivctr_subs_group1_1');
    var query = null;
    if ((p == null) || (p.querySelector('#' + urlid) == null)) {
        query = document.querySelector('#' + urlid).value;
    } else
        query = p.querySelector('#' + urlid).value;

    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        // If first entry with this name
        if (typeof query_string[pair[0]] === "undefined") {
            query_string[pair[0]] = pair[1];
            // If second entry with this name
        } else if (typeof query_string[pair[0]] === "string") {
            var arr = [query_string[pair[0]], pair[1]];
            query_string[pair[0]] = arr;
            // If third or later entry with this name
        } else {
            query_string[pair[0]].push(pair[1]);
        }
    }
    return query_string;
};

//Lay gia tri bien query control cua 1 URL string: vidu: QueryString.id	
function getSubString(urlstr, control) {
    var query_string = {};
    var idx = urlstr.indexOf(control);
    if (idx >= 0) {
        var query = urlstr.substring(idx);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            // If first entry with this name
            if (typeof query_string[pair[0]] === "undefined") {
                query_string[pair[0]] = pair[1];
                // If second entry with this name
            } else if (typeof query_string[pair[0]] === "string") {
                var arr = [query_string[pair[0]], pair[1]];
                query_string[pair[0]] = arr;
                // If third or later entry with this name
            } else {
                query_string[pair[0]].push(pair[1]);
            }
        }
        return query_string;
    }
    return query_string;
}

function openForm(url, width, height, func) {
    form = window.open(url, '_blank', 'toolbar=no,location=no,scrollbars=yes,directories=0,status=yes,menubar=no,resizable=yes, copyhistory=no, width=' + width + ', height=' + height + ',left=50,top=50');
    form.callbackHandler = func;
    return form;
}

function getDateVNFormated(_pdate, _isFullDate) {
    var rdate;
    if (_pdate == null || _pdate === undefined || _pdate === '') {
        rdate = new Date();
    } else {
        rdate = _pdate;
    }
    var datetime = group00(rdate.getDate())
        + "/" + group00((rdate.getMonth() + 1))
        + "/" + rdate.getFullYear();
    if (_isFullDate != null && _isFullDate === undefined && _isFullDate) {
        datetime += " " + group00(rdate.getHours()) + ":"
            + group00(rdate.getMinutes()) + ":"
            + group00(rdate.getSeconds());
    }
    return datetime;
}

function group00(s) {
    s = s + '';
    if (s.length === 1) s = '0' + s;
    return s;
}

function bindTextBox_OnKeyPress(srcId, dstId, minLen, _sql, _par, attrs) {
    $("#" + srcId).on("keypress", function (e) {
        var keycode = (e.keyCode ? e.keyCode : e.which);
        if (keycode == '13') {
            var _code = $(this).val();
            if (_code.length >= minLen) {
                if ($("#" + dstId).is("select")) {
                    var sql_par = RSUtil_buildParam("", [_code]);
                    if (_par != undefined && _par.length > 0) {
                        sql_par = RSUtil_addParam(sql_par, "P", _par);
                    } else if (srcId == "txtMA_CHIPHI") {
                        var ma_khoa = $('#cboMA_KHOA_YL').val();
                        var loai_cp = $("#cboLOAI_CHIPHI").val();
                        sql_par = RSUtil_addParam(sql_par, "P", [ma_khoa]);
                        _sql = _sqlCP[loai_cp];
                    }
                    if (attrs) {
                        var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO(_sql, sql_par);
                        var rows = JSON.parse(data_ar);
                        var cbx = document.getElementById(dstId);
                        cbx.options.length = 0;
                        $.each(rows, function (idx, row) {
                            cbx.options[idx] = new Option();
                            cbx.options[idx].value = row[Object.keys(row)[0]];
                            cbx.options[idx].text = row[Object.keys(row)[1]];
                            $.each(attrs, function (i, attr) {
                                if (row[attr[1]] != null)
                                    cbx.options[idx].setAttribute(attr[0], row[attr[1]]);
                            });
                        });
                    } else {
                        getComboTag(dstId, _sql, sql_par, "", "");
                        //getComboTag(dstId,_sql,sql_par,""+_code,"","");
                    }
                } else {
                    var _name = jsonrpc.AjaxJson.getOneValue(_sql, RSUtil_buildParam("", [_code]));
                    //$("#"+dstId).val(_name);
                    if (_name != 'null') {
                        $("#" + dstId).val(_name);
                    } else {
                        $("#" + dstId).val();
                    }
                }
            }
        }
    });
}

function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}


function removeVietnameseTones(str) {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, " ");
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(/!|%|\^|\*|\+|\=|\<|\>|\?|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
    return str;
}

PDFUtil = {
    mergePDFDocuments: async function(responses) {
        const mergedPdf = await PDFLib.PDFDocument.create();
        for (let response of responses) {
            let document = response.document;
            let rptCode = response.rptCode;
            let param = response.param;
            if (document.byteLength > 0) {
                document = await PDFLib.PDFDocument.load(document);
                const copiedPages = await mergedPdf.copyPages(document, document.getPageIndices());
                copiedPages.forEach((page) => mergedPdf.addPage(page));
            } else {
                const blankPage = mergedPdf.addPage();
                const { width, height } = blankPage.getSize();
                blankPage.drawText('Create PDF Error!', {
                    x: width/2 - 120,
                    y: height - 50,
                    color: PDFLib.rgb(0.95, 0.1, 0.1)
                });
                blankPage.drawText("REPORT CODE = " + rptCode, {
                    x: 20,
                    y: height - 150,
                    color: PDFLib.rgb(0.95, 0.1, 0.1),
                    size: 10
                });
            }
        }
        return await mergedPdf.save();
    },
}

/*
yy = short year
yyyy = long year
M = month (1-12)
MM = month (01-12)
MMM = month abbreviation (Jan, Feb ... Dec)
MMMM = long month (January, February ... December)
d = day (1 - 31)
dd = day (01 - 31)
ddd = day of the week in words (Monday, Tuesday ... Sunday)
E = short day of the week in words (Mon, Tue ... Sun)
D - Ordinal day (1st, 2nd, 3rd, 21st, 22nd, 23rd, 31st, 4th...)
h = hour in am/pm (0-12)
hh = hour in am/pm (00-12)
H = hour in day (0-23)
HH = hour in day (00-23)
mm = minute
ss = second
SSS = milliseconds
a = AM/PM marker
p = a.m./p.m. marker

 var shortDateFormat = 'dd/MM/yyyy';
 var longDateFormat  = 'dd/MM/yyyy HH:mm:ss';
 */
var DateFormat = {};

(function ($) {
    var daysInWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    var shortDaysInWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    var shortMonthsInYear = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    var longMonthsInYear = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
    var shortMonthsToNumber = {
        'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
        'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    };

    var YYYYMMDD_MATCHER = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.?\d{0,3}[Z\-+]?(\d{2}:?\d{2})?/;

    $.format = (function () {
        function numberToLongDay(value) {
            // 0 to Sunday
            // 1 to Monday
            return daysInWeek[parseInt(value, 10)] || value;
        }

        function numberToShortDay(value) {
            // 0 to Sun
            // 1 to Mon
            return shortDaysInWeek[parseInt(value, 10)] || value;
        }

        function numberToShortMonth(value) {
            // 1 to Jan
            // 2 to Feb
            var monthArrayIndex = parseInt(value, 10) - 1;
            return shortMonthsInYear[monthArrayIndex] || value;
        }

        function numberToLongMonth(value) {
            // 1 to January
            // 2 to February
            var monthArrayIndex = parseInt(value, 10) - 1;
            return longMonthsInYear[monthArrayIndex] || value;
        }

        function shortMonthToNumber(value) {
            // Jan to 01
            // Feb to 02
            return shortMonthsToNumber[value] || value;
        }

        function parseTime(value) {
            // 10:54:50.546
            // => hour: 10, minute: 54, second: 50, millis: 546
            // 10:54:50
            // => hour: 10, minute: 54, second: 50, millis: ''
            var time = value,
                hour,
                minute,
                second,
                millis = '',
                delimited,
                timeArray;

            if (time.indexOf('.') !== -1) {
                delimited = time.split('.');
                // split time and milliseconds
                time = delimited[0];
                millis = delimited[delimited.length - 1];
            }

            timeArray = time.split(':');

            if (timeArray.length === 3) {
                hour = timeArray[0];
                minute = timeArray[1];
                // '20 GMT-0200 (BRST)'.replace(/\s.+/, '').replace(/[a-z]/gi, '');
                // => 20
                // '20Z'.replace(/\s.+/, '').replace(/[a-z]/gi, '');
                // => 20
                second = timeArray[2].replace(/\s.+/, '').replace(/[a-z]/gi, '');
                // '01:10:20 GMT-0200 (BRST)'.replace(/\s.+/, '').replace(/[a-z]/gi, '');
                // => 01:10:20
                // '01:10:20Z'.replace(/\s.+/, '').replace(/[a-z]/gi, '');
                // => 01:10:20
                time = time.replace(/\s.+/, '').replace(/[a-z]/gi, '');
                return {
                    time: time,
                    hour: hour,
                    minute: minute,
                    second: second,
                    millis: millis
                };
            }

            return {time: '', hour: '', minute: '', second: '', millis: ''};
        }


        function padding(value, length) {
            var paddingCount = length - String(value).length;
            for (var i = 0; i < paddingCount; i++) {
                value = '0' + value;
            }
            return value;
        }

        return {

            parseDate: function (value) {
                var values,
                    subValues;

                var parsedDate = {
                    date: null,
                    year: null,
                    month: null,
                    dayOfMonth: null,
                    dayOfWeek: null,
                    time: null
                };

                if (typeof value == 'number') {
                    return this.parseDate(new Date(value));
                } else if (typeof value.getFullYear == 'function') {
                    parsedDate.year = String(value.getFullYear());
                    // d = new Date(1900, 1, 1) // 1 for Feb instead of Jan.
                    // => Thu Feb 01 1900 00:00:00
                    parsedDate.month = String(value.getMonth() + 1);
                    parsedDate.dayOfMonth = String(value.getDate());
                    parsedDate.time = parseTime(value.toTimeString() + "." + value.getMilliseconds());
                } else if (value.search(YYYYMMDD_MATCHER) != -1) {
                    /* 2009-04-19T16:11:05+02:00 || 2009-04-19T16:11:05Z */
                    values = value.split(/[T\+-]/);
                    parsedDate.year = values[0];
                    parsedDate.month = values[1];
                    parsedDate.dayOfMonth = values[2];
                    parsedDate.time = parseTime(values[3].split('.')[0]);
                } else {
                    values = value.split(' ');
                    if (values.length === 6 && isNaN(values[5])) {
                        // values[5] == year
                        /*
                         * This change is necessary to make `Mon Apr 28 2014 05:30:00 GMT-0300` work
                         * like `case 7`
                         * otherwise it will be considered like `Wed Jan 13 10:43:41 CET 2010
                         * Fixes: https://github.com/phstc/jquery-dateFormat/issues/64
                         */
                        values[values.length] = '()';
                    }
                    switch (values.length) {
                        case 6:
                            /* Wed Jan 13 10:43:41 CET 2010 */
                            parsedDate.year = values[5];
                            parsedDate.month = shortMonthToNumber(values[1]);
                            parsedDate.dayOfMonth = values[2];
                            parsedDate.time = parseTime(values[3]);
                            break;
                        case 2:
                            /* 2009-12-18 10:54:50.546 */
                            subValues = values[0].split('-');
                            parsedDate.year = subValues[0];
                            parsedDate.month = subValues[1];
                            parsedDate.dayOfMonth = subValues[2];
                            parsedDate.time = parseTime(values[1]);
                            break;
                        case 7:
                        /* Tue Mar 01 2011 12:01:42 GMT-0800 (PST) */
                        case 9:
                        /* added by Larry, for Fri Apr 08 2011 00:00:00 GMT+0800 (China Standard Time) */
                        case 10:
                            /* added by Larry, for Fri Apr 08 2011 00:00:00 GMT+0200 (W. Europe Daylight Time) */
                            parsedDate.year = values[3];
                            parsedDate.month = shortMonthToNumber(values[1]);
                            parsedDate.dayOfMonth = values[2];
                            parsedDate.time = parseTime(values[4]);
                            break;
                        case 1:
                            /* added by Jonny, for 2012-02-07CET00:00:00 (Doctrine Entity -> Json Serializer) */
                            subValues = values[0].split('');
                            parsedDate.year = subValues[0] + subValues[1] + subValues[2] + subValues[3];
                            parsedDate.month = subValues[5] + subValues[6];
                            parsedDate.dayOfMonth = subValues[8] + subValues[9];
                            parsedDate.time = parseTime(subValues[13] + subValues[14] + subValues[15] + subValues[16] + subValues[17] + subValues[18] + subValues[19] + subValues[20]);
                            break;
                        default:
                            return null;
                    }
                }

                if (parsedDate.time) {
                    parsedDate.date = new Date(parsedDate.year, parsedDate.month - 1, parsedDate.dayOfMonth, parsedDate.time.hour, parsedDate.time.minute, parsedDate.time.second, parsedDate.time.millis);
                } else {
                    parsedDate.date = new Date(parsedDate.year, parsedDate.month - 1, parsedDate.dayOfMonth);
                }

                parsedDate.dayOfWeek = String(parsedDate.date.getDay());

                return parsedDate;
            },

            date: function (value, format) {
                try {
                    var parsedDate = this.parseDate(value);

                    if (parsedDate === null) {
                        return value;
                    }

                    var year = parsedDate.year,
                        month = parsedDate.month,
                        dayOfMonth = parsedDate.dayOfMonth,
                        dayOfWeek = parsedDate.dayOfWeek,
                        time = parsedDate.time;
                    var hour;

                    var pattern = '',
                        retValue = '',
                        unparsedRest = '',
                        inQuote = false;

                    /* Issue 1 - variable scope issue in format.date (Thanks jakemonO) */
                    for (var i = 0; i < format.length; i++) {
                        var currentPattern = format.charAt(i);
                        // Look-Ahead Right (LALR)
                        var nextRight = format.charAt(i + 1);

                        if (inQuote) {
                            if (currentPattern == "'") {
                                retValue += (pattern === '') ? "'" : pattern;
                                pattern = '';
                                inQuote = false;
                            } else {
                                pattern += currentPattern;
                            }
                            continue;
                        }
                        pattern += currentPattern;
                        unparsedRest = '';
                        switch (pattern) {
                            case 'ddd':
                                retValue += numberToLongDay(dayOfWeek);
                                pattern = '';
                                break;
                            case 'dd':
                                if (nextRight === 'd') {
                                    break;
                                }
                                retValue += padding(dayOfMonth, 2);
                                pattern = '';
                                break;
                            case 'd':
                                if (nextRight === 'd') {
                                    break;
                                }
                                retValue += parseInt(dayOfMonth, 10);
                                pattern = '';
                                break;
                            case 'D':
                                if (dayOfMonth == 1 || dayOfMonth == 21 || dayOfMonth == 31) {
                                    dayOfMonth = parseInt(dayOfMonth, 10) + 'st';
                                } else if (dayOfMonth == 2 || dayOfMonth == 22) {
                                    dayOfMonth = parseInt(dayOfMonth, 10) + 'nd';
                                } else if (dayOfMonth == 3 || dayOfMonth == 23) {
                                    dayOfMonth = parseInt(dayOfMonth, 10) + 'rd';
                                } else {
                                    dayOfMonth = parseInt(dayOfMonth, 10) + 'th';
                                }
                                retValue += dayOfMonth;
                                pattern = '';
                                break;
                            case 'MMMM':
                                retValue += numberToLongMonth(month);
                                pattern = '';
                                break;
                            case 'MMM':
                                if (nextRight === 'M') {
                                    break;
                                }
                                retValue += numberToShortMonth(month);
                                pattern = '';
                                break;
                            case 'MM':
                                if (nextRight === 'M') {
                                    break;
                                }
                                retValue += padding(month, 2);
                                pattern = '';
                                break;
                            case 'M':
                                if (nextRight === 'M') {
                                    break;
                                }
                                retValue += parseInt(month, 10);
                                pattern = '';
                                break;
                            case 'y':
                            case 'yyy':
                                if (nextRight === 'y') {
                                    break;
                                }
                                retValue += pattern;
                                pattern = '';
                                break;
                            case 'yy':
                                if (nextRight === 'y') {
                                    break;
                                }
                                retValue += String(year).slice(-2);
                                pattern = '';
                                break;
                            case 'yyyy':
                                retValue += year;
                                pattern = '';
                                break;
                            case 'HH':
                                retValue += padding(time.hour, 2);
                                pattern = '';
                                break;
                            case 'H':
                                if (nextRight === 'H') {
                                    break;
                                }
                                retValue += parseInt(time.hour, 10);
                                pattern = '';
                                break;
                            case 'hh':
                                /* time.hour is '00' as string == is used instead of === */
                                hour = (parseInt(time.hour, 10) === 0 ? 12 : time.hour < 13 ? time.hour
                                    : time.hour - 12);
                                retValue += padding(hour, 2);
                                pattern = '';
                                break;
                            case 'h':
                                if (nextRight === 'h') {
                                    break;
                                }
                                hour = (parseInt(time.hour, 10) === 0 ? 12 : time.hour < 13 ? time.hour
                                    : time.hour - 12);
                                retValue += parseInt(hour, 10);
                                // Fixing issue https://github.com/phstc/jquery-dateFormat/issues/21
                                // retValue = parseInt(retValue, 10);
                                pattern = '';
                                break;
                            case 'mm':
                                retValue += padding(time.minute, 2);
                                pattern = '';
                                break;
                            case 'm':
                                if (nextRight === 'm') {
                                    break;
                                }
                                retValue += time.minute;
                                pattern = '';
                                break;
                            case 'ss':
                                /* ensure only seconds are added to the return string */
                                retValue += padding(time.second.substring(0, 2), 2);
                                pattern = '';
                                break;
                            case 's':
                                if (nextRight === 's') {
                                    break;
                                }
                                retValue += time.second;
                                pattern = '';
                                break;
                            case 'S':
                            case 'SS':
                                if (nextRight === 'S') {
                                    break;
                                }
                                retValue += pattern;
                                pattern = '';
                                break;
                            case 'SSS':
                                var sss = '000' + time.millis.substring(0, 3);
                                retValue += sss.substring(sss.length - 3);
                                pattern = '';
                                break;
                            case 'a':
                                retValue += time.hour >= 12 ? 'PM' : 'AM';
                                pattern = '';
                                break;
                            case 'p':
                                retValue += time.hour >= 12 ? 'p.m.' : 'a.m.';
                                pattern = '';
                                break;
                            case 'E':
                                retValue += numberToShortDay(dayOfWeek);
                                pattern = '';
                                break;
                            case "'":
                                pattern = '';
                                inQuote = true;
                                break;
                            default:
                                retValue += currentPattern;
                                pattern = '';
                                break;
                        }
                    }
                    retValue += unparsedRest;
                    return retValue;
                } catch (e) {
                    if (console && console.log) {
                        console.log(e);
                    }
                    return value;
                }
            },
            /*
             * JavaScript Pretty Date
             * Copyright (c) 2011 John Resig (ejohn.org)
             * Licensed under the MIT and GPL licenses.
             *
             * Takes an ISO time and returns a string representing how long ago the date
             * represents
             *
             * ('2008-01-28T20:24:17Z') // => '2 hours ago'
             * ('2008-01-27T22:24:17Z') // => 'Yesterday'
             * ('2008-01-26T22:24:17Z') // => '2 days ago'
             * ('2008-01-14T22:24:17Z') // => '2 weeks ago'
             * ('2007-12-15T22:24:17Z') // => 'more than 5 weeks ago'
             *
             */
            prettyDate: function (time) {
                var date;
                var diff;
                var day_diff;

                if (typeof time === 'string' || typeof time === 'number') {
                    date = new Date(time);
                }

                if (typeof time === 'object') {
                    date = new Date(time.toString());
                }

                diff = (((new Date()).getTime() - date.getTime()) / 1000);

                day_diff = Math.floor(diff / 86400);

                if (isNaN(day_diff) || day_diff < 0) {
                    return;
                }

                if (diff < 60) {
                    return 'just now';
                } else if (diff < 120) {
                    return '1 minute ago';
                } else if (diff < 3600) {
                    return Math.floor(diff / 60) + ' minutes ago';
                } else if (diff < 7200) {
                    return '1 hour ago';
                } else if (diff < 86400) {
                    return Math.floor(diff / 3600) + ' hours ago';
                } else if (day_diff === 1) {
                    return 'Yesterday';
                } else if (day_diff < 7) {
                    return day_diff + ' days ago';
                } else if (day_diff < 31) {
                    return Math.ceil(day_diff / 7) + ' weeks ago';
                } else if (day_diff >= 31) {
                    return 'more than 5 weeks ago';
                }
            },
            toBrowserTimeZone: function (value, format) {
                return this.date(new Date(value), format || 'MM/dd/yyyy HH:mm:ss');
            }
        };
    }());
}(DateFormat));
Date.prototype.format = function (mask) {
    return DateFormat.format.date(this, mask);
};

HisSignCA = {
    msgTpl: {
        "14": "Phiếu cấu hình jasper không tồn tại",
        "15": "Lỗi: {DATA}",
        "16": "Có lỗi khi mã hóa dữ liệu.",
        "21": "(HIS)Thông tin người ký chưa được khai báo, vui lòng liên hệ IT bệnh viện.",
        "22": "(OneHealth)Thông tin người ký chưa được khai báo, vui lòng liên hệ IT bệnh viện.",
        "23": "Thông tin tài khoản ký không đúng, vui lòng liên hệ IT bệnh viện.",
        "30": "Phiếu đã được ký chốt, vui lòng liên hệ người ký: {DATA}"
    },
    hasCode: function (code) {
        return !!(HisSignCA.msgTpl[code] && HisSignCA.msgTpl[code] !== "");
    },
    message: function (code, params) {
        let msg = HisSignCA.msgTpl[code];
        if (params) {
            Object.keys(params).forEach(key => {
                msg = msg.replace("{" + key + "}", params[key]);
            });
        }
        return msg;
    },
    patientSignMethod: {
        "0": {
            "device": "Token"
        },
        "1": {
            "device": "SignPad"
        },
        "2": {
            "device": "Finger"
        }
    }
}

HisCacheUtil = {
    DRC: {
        clear: function (commandId) {
            $.ajax({
                url: '/vnpthis/api/caching-reloader',
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({
                    COMMAND_ID: commandId,
                    FUNC: "CLEAR",
                    BKKEY: "DRC"
                }),
                success: function (result) {
                    alertify.success("Reload cache thành công.", 10000);
                },
                error: function (msg) {
                    alertify.error("Xử lý cache thất bại.", 10000);
                }
            });
        },

        clearWithKey: function (commandId, key) {
            $.ajax({
                url: '/vnpthis/api/caching-reloader',
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({
                    COMMAND_ID: commandId,
                    KEY: key,
                    FUNC: "CLEAR",
                    BKKEY: "DRC"
                }),
                success: function (result) {
                    alertify.success("Reload cache thành công.", 10000);
                },
                error: function (msg) {
                    alertify.error("Xử lý cache thất bại.", 10000);
                }
            });
        }
    },
    DFC: {
        save: function (key, value, ttl, _success, _error) {
            $.ajax({
                url: '/vnpthis/api/caching-reloader',
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({
                    COMMAND_ID: "",
                    KEY: key,
                    VALUE: value,
                    TTL: ttl,
                    FUNC: "SAVE",
                    BKKEY: "DFC"
                }),
                success: function (result) {
                    if (typeof _success === "function") {
                        _success(result);
                    }
                },
                error: function () {
                    if (typeof _error === "function") {
                        _error("Thất bại");
                    }
                }
            });
        },

        get: function (key, _success) {
            var rs;
            let async = false;
            if (typeof _success === "function") {
                async = true;
            }
            $.ajax({
                url: '/vnpthis/api/caching-reloader',
                type: "POST",
                contentType: "application/json; charset=utf-8",
                async: async,
                data: JSON.stringify({
                    KEY: key,
                    FUNC: "FETCH",
                    BKKEY: "DFC"
                }),
                success: function (result) {
                    if (async) {
                        _success(result);
                    } else {
                        rs = result;
                    }
                },
                error: function () {
                    if (!async) {
                        rs = null;
                    }
                }
            });
            return rs;
        },

        clear: function (key, _success, _error) {
            $.ajax({
                url: '/vnpthis/api/caching-reloader',
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({
                    COMMAND_ID: "",
                    KEY: key,
                    FUNC: "CLEAR",
                    BKKEY: "DFC"
                }),
                success: function (result) {
                    if (typeof _success === "function") {
                        _success(result);
                    }
                },
                error: function () {
                    if (typeof _error === "function") {
                        _error("Thất bại");
                    }
                }
            });
        }
    }
}