/*
Một sự tập trung không hề nhẹ đã tạo ra chức năng thần thánh này
 */
function Phieu_KQ_Papanicolaou(_opts) {
	var _user_id;
	var _company_id = _opts.hospital_id;

	var _dichvukhambenhid = _opts.dichvukhambenhid;
	var mahosobenhan = _opts.mahosobenhan;
	var maubenhphamid = _opts.maubenhphamid;
	var _dept_id = _opts.dept_id;
	var ketquaclsid = _opts.ketquaclsid;
	
	var sql_par = [];
	var that = this;
	this.load = doLoad;
	this.luuAnh=luuAnh;
	this.xoaAnh=xoaAnh;
	this.lietKeAnh=lietKeAnh;
	
	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		this.validator = new DataValidator("divMain");
		bindEvent();
		loadData();
		lietKeAnh();
	}
	
	function bindEvent() {
		$("#btnHuy").on("click", function(e) {
			EventUtil.raiseEvent("assignPapanicolaou_cancel");
		});
		$("#btnLuu").on("click", function(e) {
			doIns();
		});
		$("#btnXoa").on("click", function(e) {
			deleteKQ();
			$('input[name=radVITRILAYMAU]').attr('checked', false);
			$('input[name=radNGAYKINHCHOT]').attr('checked', false);
			$('input[name=radDIEUTRINOITIET]').attr('checked', false);
			$('input[name=radMT_YEUCAU_CD]').attr('checked', false);
		});
		$("#btnInPhieu").on("click", function(e) {
			var par = [ 
				{name:'ID_KQ', type:'String', value:$("#txtID").val()}
			];
			var param = [maubenhphamid,_dichvukhambenhid];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.DSANHBN", param.join('$'));
			if(data_ar.length>0){			
				var hinh_anh = data_ar[0]["DUONGDANFILE"];
				if(hinh_anh!="" && hinh_anh!=null) 
					par.push({name:'hinh_anh', type:'Image', value:hinh_anh});
			}
			
			openReport('window', "XETNGHIEM_PAPSMEAR", 'pdf', par);	
		});
		$("#btnMoi").on("click", function(e) {
			FormUtil.clearForm("PTM", "");
			
			$('input[name=radVITRILAYMAU]').attr('checked', false);
			$('input[name=radNGAYKINHCHOT]').attr('checked', false);
			$('input[name=radDIEUTRINOITIET]').attr('checked', false);
			$('input[name=radMT_YEUCAU_CD]').attr('checked', false);
			
			$("#divLydo").hide();
		});
		$('#btnUpload').on("click", function (){
			var filename = $("#fileUpload").val();
			
	        // Use a regular expression to trim everything before final dot
	        var extension = filename.replace(/^.*\./, '');

	        // Iff there is no dot anywhere in filename, we would have extension == filename,
	        // so we account for this possibility now
	        if (extension == filename) {
	            extension = '';
	        } else {
	            // if there is an extension, we convert to lower case
	            // (N.B. this conversion will not effect the value of the extension
	            // on the file upload.)
	            extension = extension.toLowerCase();
	        }

	        switch (extension) {
	            case 'jpg':
	            case 'jpeg':
	            case 'png':
	            case 'bmp':
	    			UploadUtil.upload("formUpload","", luuAnh);
	    			break;
	            default:
	                alert("Chỉ cho phép upload ảnh định dạng jpeg, png, bmp");
	                submitEvent.preventDefault();
	        }
	    });
	}
	
	// lưu lại đường dẫn ảnh đã upload ứng với kết quả đang nhập
	function luuAnh(data){
		var myData = data.length;
		if(typeof(myData) != "undefined"){
			for(var i=0; i<data.length; i++){
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							''+'$'+	// id benh nhan
							''+'$'+ // ten benh nhan 
							maubenhphamid+'$'+ketquaclsid+'$'+
							data[i].id+'$'+data[i].url+'$'+data[i].name
						;
				
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.LUUANH2", param);
				
				if(rs==1){
					$("#fileUpload").val('');
					lietKeAnh();
				} else {
					DlgUtil.showMsg("Nhập ảnh thất bại: "+rs.error_msg,undefined,3000);
				}
			}
		} else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						''+'$'+	// id benh nhan
						''+'$'+ // ten benh nhan
						maubenhphamid+'$'+ketquaclsid+'$'+
						data.id+'$'+data.url+'$'+document.getElementById('fileUpload').files[0].name.replace(/\.[^/.]+$/, "")
					;
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.LUUANH2", param);
			
			if(rs==1){
				$("#fileUpload").val('');
				lietKeAnh();				
			} else {
				DlgUtil.showMsg("Nhập ảnh thất bại: "+rs.error_msg,undefined,3000);
			}
		}
	}
	
	// xóa ảnh đã upload
	function xoaAnh(idfile){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+maubenhphamid+'$'+ketquaclsid+'$'+idfile;
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.XOAANH2", param);
		
		if(rs == 1){
			$("#fileUpload").val('');
			var rt = UploadUtil.deleteMedia(idfile,'');
			if(rt == idfile){
				DlgUtil.showMsg("Kết quả xóa file: "+rt);
			}
			lietKeAnh();
		} else {
			DlgUtil.showMsg("Xóa ảnh "+idfile+" thất bại ",undefined,3000);
		}
	}
	
	// liệt kê ảnh đã lưu trong cơ sở dữ liệu
	function lietKeAnh(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+maubenhphamid+'$'+ketquaclsid;
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C003.LIETKEANH2", param);
		
		$("#list").html('');
		if(type=="read"){
			for(var i=0; i<data_ar.length; i++){
				var img = "<div class='col-xs-4' style='text-align:center;'>"+
								"<img src='"+data_ar[i]["DUONGDANFILE"]+"' width='150px' style='padding:5px' alt='"+data_ar[i]["MOTA"]+"' />"+
						  "</div>";
				
				$("#list").append(img);
			}
		} else if(type=="update"){
			for(var i=0; i<data_ar.length; i++){
				var img = "<div class='col-xs-4' style='text-align:center;'>"+
								"<img src='"+data_ar[i]["DUONGDANFILE"]+"' width='150px' style='padding:5px' alt='"+data_ar[i]["MOTA"]+"' />"+		
								"<input type='image' src='../common/icon/delete.png' width='18px' onclick='ptd.xoaAnh("+data_ar[i]["IDFILE"]+");' />"+								
						  "</div>";
					
				$("#list").append(img);
			}
		}
	}
	
	function loadData(){
		var sql_par = [ mahosobenhan, maubenhphamid ];
		var sql_par1 = [ _dichvukhambenhid, maubenhphamid ];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D146_TT", sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			FormUtil.clearForm("Info", "");
			FormUtil.setObjectToForm("Info", "", row);
		}
		if (_dichvukhambenhid != "") {
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X015.TTKQ2", sql_par1.join('$'));
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				FormUtil.clearForm("PTM", "");
				FormUtil.setObjectToForm("PTM", "", row);
				
				$("#txtID").val(row.ID);
				
//				if(row.VITRILAYMAU) $('input[name=radVITRILAYMAU][value=' + row.VITRILAYMAU + ']').attr('checked', true);
//				if(row.NGAYKINHCHOT) $('input[name=radNGAYKINHCHOT][value=' + row.NGAYKINHCHOT + ']').attr('checked', true);
//				if(row.DIEUTRINOITIET) $('input[name=radDIEUTRINOITIET][value=' + row.DIEUTRINOITIET + ']').attr('checked', true);
				if(row.DANHGIATIEUBAN) $('input[name=radDANHGIATIEUBAN][value=' + row.DANHGIATIEUBAN + ']').attr('checked', true);
				
				$("#btnXoa").show();
				$("#btnInPhieu").show();
				
				if (row.DANHGIATIEUBAN != '1') {
					$("#divLydo").show();
				} else {
					$("#divLydo").hide();
				}
			} else {
				FormUtil.clearForm("PTM", "");
				$("#txtID").val("-1");
				$("#btnXoa").hide();
				$("#btnInPhieu").hide();

//				$('input[name=radVITRILAYMAU]').attr('checked', false);
//				$('input[name=radNGAYKINHCHOT]').attr('checked', false);
//				$('input[name=radDIEUTRINOITIET]').attr('checked', false);
				$('input[name=radDANHGIATIEUBAN]').attr('checked', false);
//				
				$("#divLydo").hide();
			}
		}
	}
	
	$('input[type=radio][name=radDANHGIATIEUBAN]').change(function() {
	    if (this.value == '1') {
	    	$("#divLydo").hide();
	    }	    
	    if (this.value == '0') {
	    	$("#divLydo").show();
	    }
	    $("#txtMT_GHICHU_CD").val("");
	});

	function doIns() {
		var objData = new Object();
		FormUtil.setFormToObject("", "", objData);
		objData["DICHVUKHAMBENHID"] = _dichvukhambenhid;
		objData["MAUBENHPHAMID"] = maubenhphamid;
		objData["KETQUACLSID"] = ketquaclsid;
		objData["TYPE"] = "INS";
		if (validate(objData)) {
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X015.LKQ2", JSON.stringify(objData));
			if (ret == -1) {
				DlgUtil.showMsg("Có lỗi khi thực hiện!");
				return false;
			} else {
				DlgUtil.showMsg("Cập nhập thông tin thành công");
				loadData();
				return false;
			}
		}
	}
	
	function deleteKQ() {
		var objData = new Object();
		objData["ID"] = $("#txtID").val();
		objData["TYPE"] = "DELETE";
		ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X015.LKQ2", JSON.stringify(objData));
		if (ret == -1) {
			DlgUtil.showMsg("Có lỗi khi thực hiện!");
			return false;
		} else {
			DlgUtil.showMsg("Xóa kết quả thành công!");
			loadData();
			return false;
		}
	}
	

	function validate(objData) {
		if (objData.LYDO == '0') {
			if (objData.LYDO == '' || objData.MT_GHICHU_CD == null) {
				DlgUtil.showMsg("Bắt buộc nhập Lý do khi Không đạt yêu cầu chẩn đoán !");
				$("#txtLYDO").focus();
				return false;
			}
		}
		if (objData.KETLUAN == '' || objData.KETLUAN == null) {
			DlgUtil.showMsg("Bắt buộc nhập Kết luận!");
			$("#txtKETLUAN").focus();
			return false;
		}
		return true;
	}

	$('.input-sm').keydown(function(e) {
		if (e.which === 13) {
			var index = $('.input-sm').index(this) + 1;
			$('.input-sm').eq(index).focus();
		}
	});
}