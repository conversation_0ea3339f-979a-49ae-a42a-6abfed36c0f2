<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU020_TODIEUTRI_39BV01_QD4069_A4_MULTI_V13_30360" pageWidth="591" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="521" leftMargin="55" rightMargin="15" topMargin="15" bottomMargin="15" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="2.196150000000009"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="i_benhnhanid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="i_khoaid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_khambenhid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_maubenhphamids" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call NTU_TODIEUTRI_MULTIL_V13_30360($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{i_benhnhanid},$P{i_khoaid},$P{i_khambenhid},$P{i_maubenhphamids},$P{ora_cursor})}]]>
	</queryString>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="CMND" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="BUONG" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="TENBENHVIEN" class="java.lang.String"/>
	<field name="SOYTE" class="java.lang.String"/>
	<field name="SO" class="java.lang.String"/>
	<field name="MAGIUONG" class="java.lang.String"/>
	<field name="MAUBENHPHAMID" class="java.math.BigDecimal"/>
	<field name="HOSOBENHANID" class="java.math.BigDecimal"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="NGAYMAUBENHPHAM" class="java.lang.String"/>
	<field name="DIENBIENBENH" class="java.lang.String"/>
	<field name="YLENH" class="java.lang.String"/>
	<field name="CHANDOAN_KEMTHEO" class="java.lang.String"/>
	<field name="YLENHPLUS" class="java.lang.String"/>
	<field name="BSDIEUTRI" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="YLENHPLUS_CDAN" class="java.lang.String"/>
	<field name="GIUONG_PHIEU" class="java.lang.String"/>
	<field name="PDT_CDHA" class="java.lang.String"/>
	<field name="PDT_PTTT" class="java.lang.String"/>
	<field name="THUOCDONGY" class="java.lang.String"/>
	<field name="MACHNHIETDO" class="java.lang.String"/>
	<field name="VATTU" class="java.lang.String"/>
	<field name="PDT_XN" class="java.lang.String"/>
	<field name="ORG_TYPE" class="java.lang.String"/>
	<field name="PDT_DICHVU" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="92">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} >0]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="333" y="47" width="74" height="15" uuid="1010b7d6-b2a9-46e9-8fa9-15c13fe703ea"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="77" width="520" height="15" uuid="33651187-c8d1-4b1c-8a8a-b1ee998b873d"/>
				<box bottomPadding="3"/>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán: <b><i>" + ($F{CHANDOAN}
+($F{GHICHU_BENHCHINH}==null?"":(" ("+$F{GHICHU_BENHCHINH}+")"))
+($F{CHANDOAN_KEMTHEO}==null?" ":(";"+$F{CHANDOAN_KEMTHEO})))
+ "</b></i>"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="62" width="38" height="15" uuid="08078169-7efd-4579-b95d-4a27dfacf011"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[- Địa chỉ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="261" y="62" width="32" height="15" uuid="b07a1a99-4ade-4ad4-b4d2-c8d12de22d9e"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Buồng:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="293" y="62" width="91" height="15" uuid="4b1489c7-3be0-456a-88f4-ed4dbee0e7a1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BUONG}==null?"":$F{BUONG}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="459" y="47" width="61" height="15" uuid="5416a162-49fb-4bca-b3f0-0c48949b3e99"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="15" width="70" height="15" uuid="a1934dd6-6927-4fb9-a746-dc53d5c720be"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="15" width="180" height="15" uuid="20f36072-**************-833104d29de9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHVIEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="407" y="47" width="52" height="15" uuid="c5e79ab8-feb5-4d44-9ee3-ab96ca020d98"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Giới tính: ]]></text>
			</staticText>
			<textField>
				<reportElement x="180" y="15" width="217" height="15" uuid="1dba9cc2-**************-389331e80d8d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["(Số: ......................)"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="397" y="15" width="53" height="15" uuid="0e90f9f2-3e4f-488c-8805-008f8b16670a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[Mã BN       :]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="304" y="47" width="30" height="15" uuid="2e627466-bd87-4ef9-902b-36546e706740"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Tuổi: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="38" y="62" width="220" height="15" uuid="41cade86-1702-49d5-b9a6-8842897ffc5c"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIACHI}==null?"":$F{DIACHI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="397" y="0" width="53" height="15" uuid="f0ca8a1b-d74a-430c-8948-edb82f7108a4"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[Mã bệnh án:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="47" width="93" height="15" uuid="a9a9fa76-73e3-4cda-9fb2-d6dc7fce212b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[- Họ tên người bệnh:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="93" y="47" width="211" height="15" uuid="e9bfa033-609e-4410-b64f-250e1f5a61cd"/>
				<box leftPadding="10"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="180" height="15" uuid="58a8a38b-cb84-4583-a6bc-903d247e38b5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOYTE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="420" y="62" width="100" height="15" uuid="c6236693-6684-4d95-bd97-64d30a4178a3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAGIUONG}==null?"":$F{MAGIUONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="180" y="0" width="217" height="15" uuid="e62a91e3-e57c-42ea-a3f6-a16e13410945"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["TỜ ĐIỀU TRỊ"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="450" y="0" width="70" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="4a41b28d-cf7b-42ca-80b8-1fb095f2ce67"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="30" width="180" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="779243fb-1167-4a64-8d56-23ed571b1466"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ORG_TYPE}.equals("2") ? ("Khoa Khám bệnh") : $F{KHOA}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="386" y="62" width="34" height="15" uuid="7af9f480-5296-4d4d-ac08-7e78fb621ccd"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1"/>
				</textElement>
				<text><![CDATA[Giường:]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="17" splitType="Stretch">
			<staticText>
				<reportElement x="64" y="0" width="198" height="17" uuid="acacf906-707d-4667-b1b0-599eecd32f31"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[DIỄN BIẾN BỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement x="262" y="0" width="259" height="17" uuid="64ab4677-9b79-4d7e-80c8-8b2cfac43cde"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Y LỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="64" height="17" uuid="6a62e29a-0b72-4787-8eb4-830345be4293"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[NGÀY GIỜ]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="21">
			<frame>
				<reportElement x="0" y="0" width="521" height="21" uuid="7ea62254-086d-4770-88df-5e8a1237bd8b"/>
				<box>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="64" height="21" uuid="fd3782db-6781-43a2-94e7-7507400a0bad"/>
					<box topPadding="2" leftPadding="5" bottomPadding="2">
						<topPen lineWidth="0.5" lineStyle="Solid"/>
						<leftPen lineWidth="0.0" lineStyle="Solid"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
						<font fontName="Times New Roman" size="10"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYMAUBENHPHAM} +
($F{GIUONG_PHIEU} == null?"": ("<br>" + $F{GIUONG_PHIEU}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="64" y="0" width="198" height="21" uuid="2665e641-d898-4d7a-86d3-3e2d7c51fd70"/>
					<box topPadding="2" leftPadding="5" bottomPadding="2">
						<topPen lineWidth="0.5" lineStyle="Solid"/>
						<leftPen lineWidth="0.5" lineStyle="Solid"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Top" markup="html">
						<font fontName="Times New Roman" size="10"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{DIENBIENBENH}==null? "" : ($F{DIENBIENBENH} + "<br>") )
+ ("Chẩn đoán: " + $F{CHANDOAN} + "<br>")
+ ($F{CHANDOAN_KEMTHEO}==null? "" : ("Chẩn đoán kèm theo: " + $F{CHANDOAN_KEMTHEO}) + "<br>")]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="64" y="0" width="457" height="21" uuid="8c41d9cc-4bce-4df3-afdd-1e1a4b055463"/>
					<box>
						<leftPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="198" y="0" width="258" height="21" uuid="2d4eddd3-fc5f-42ba-8d5e-c520bd844da5"/>
						<box topPadding="2" leftPadding="5" bottomPadding="2">
							<topPen lineWidth="0.5" lineStyle="Solid"/>
							<leftPen lineWidth="0.5"/>
							<bottomPen lineWidth="0.0"/>
							<rightPen lineWidth="0.0" lineStyle="Solid"/>
						</box>
						<textElement verticalAlignment="Top" markup="html">
							<font fontName="Times New Roman" size="10"/>
							<paragraph leftIndent="2" rightIndent="2"/>
						</textElement>
						<textFieldExpression><![CDATA[($F{YLENHPLUS}==null?"":($F{YLENHPLUS}.replace("Chọn", "")  + "<br>"))
+ ($F{YLENH}==null?"":($F{YLENH} + ($F{THUOCDONGY} == null?"<br>":"")))
+ ($F{THUOCDONGY} == null?"":($F{THUOCDONGY} + "<br>"))
+ ($F{PDT_DICHVU}==null?"":($F{PDT_DICHVU} + "<br>"))
+ ($F{YLENHPLUS_CDAN}==null?"":($F{YLENHPLUS_CDAN}.replace("Chọn", "") + "<br>"))]]></textFieldExpression>
					</textField>
				</frame>
			</frame>
		</band>
		<band height="50" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="262" y="0" width="259" height="50" uuid="9ad90074-e348-4721-8457-b31f5c28e542"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["<br><br><br>"+($F{BSDIEUTRI} == null ? "" : $F{BSDIEUTRI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="64" height="50" uuid="0fe1b60d-ff55-4cad-bfb0-fadc34ac59fd"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="64" y="0" width="198" height="50" uuid="fd8ea35c-ffe4-418c-b9de-f10db0e9066e"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["<br><br><br>"+($F{BSDIEUTRI} == null ? "" : $F{BSDIEUTRI})]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band/>
	</summary>
</jasperReport>
