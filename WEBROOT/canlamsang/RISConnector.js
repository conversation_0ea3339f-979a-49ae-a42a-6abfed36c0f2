/**
 * Viet Anh
 * 17/11/2016
 * Kết nối RIS Service
 */

var hashidsjs = document.createElement('script');
hashidsjs.src = '../canlamsang/hashids.min.js';
document.head.appendChild(hashidsjs);

var RIS_CONNECTION_TYPE = "";
var RIS_USERNAME = "";
var RIS_SECRET_KEY = "";
var RIS_SERVICE_DOMAIN_NAME = "";
var RIS_PROVIDER = "";
var RIS_PROVIDER_AUTH = "";

var RIS_CANCEL_REQUEST = "/api/public/request/cancel";
var RIS_GET_DICOM_VIEWER = "/api/public/dicomViewer";
var RIS_DELETE_REQUEST = "/api/public/request/delete";
var RIS_DELETE_ORDER = "/api/public/request/order/delete";
var RIS_UPDATE_REQUEST = "/api/public/request/update-request";
var RIS_GET_REPORT = "api/public/result/obs";
var studyInstanceUID = "";


var RIS_CONNECTION_TYPE_V1 = "";
var RIS_USERNAME_V1 = "";
var RIS_SECRET_KEY_V1 = "";
var RIS_SERVICE_DOMAIN_NAME_V1 = "";
var RIS_PROVIDER_V1 = "";
var RIS_PROVIDER_AUTH_V1 = "";

var RIS_CANCEL_REQUEST_V1 = "/api/public/request/cancel";
var RIS_GET_DICOM_VIEWER_V1 = "/api/public/dicomViewer";
var RIS_DELETE_REQUEST_V1 = "/api/public/request/delete";
var RIS_DELETE_ORDER_V1 = "/api/public/request/order/delete";
var RIS_UPDATE_REQUEST_V1 = "/api/public/request/update-request";
var RIS_GET_REPORT_V1 = "api/public/result/obs";
var studyInstanceUID_v1 = "";


function getHashRIS(code) {
    var input = code + RIS_SECRET_KEY;	    
    var hashids = new Hashids(input);
    var date = parseInt(jsonrpc.AjaxJson.getSystemDate('yyyyMMdd'));	    
    return hashids.encode(date);
}

function loadRISConfig(){
	var key = "['RIS_CONNECTION_TYPE','RIS_USERNAME','RIS_SECRET_KEY','RIS_SERVICE_DOMAIN_NAME'," +
			"'RIS_CANCEL_REQUEST','RIS_GET_DICOM_VIEWER','RIS_DELETE_REQUEST','RIS_UPDATE_REQUEST'," +
			"'RIS_PROVIDER','RIS_PROVIDER_AUTH','RIS_GET_REPORT','RIS_DELETE_ORDER'," +
			"'RIS_CONNECTION_TYPE_V1','RIS_USERNAME_V1','RIS_SECRET_KEY_V1','RIS_SERVICE_DOMAIN_NAME_V1'," +
			"'RIS_CANCEL_REQUEST_V1','RIS_GET_DICOM_VIEWER_V1','RIS_DELETE_REQUEST_V1','RIS_UPDATE_REQUEST_V1'," +
			"'RIS_PROVIDER_V1','RIS_PROVIDER_AUTH_V1','RIS_GET_REPORT_V1','RIS_DELETE_ORDER_V1']";
	
	var rsjson = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02C001.RISC", key);
	var rs = JSON.parse(rsjson);
	
	RIS_CONNECTION_TYPE = rs.RIS_CONNECTION_TYPE;
	RIS_USERNAME = rs.RIS_USERNAME;
	RIS_SECRET_KEY = rs.RIS_SECRET_KEY;
	RIS_SERVICE_DOMAIN_NAME = rs.RIS_SERVICE_DOMAIN_NAME;
	RIS_PROVIDER = rs.RIS_PROVIDER;
	RIS_RPOVIDER_AUTH = rs.RIS_PROVIDER_AUTH;
	
	if (rs.RIS_CANCEL_REQUEST != "") RIS_CANCEL_REQUEST = rs.RIS_CANCEL_REQUEST;
	if (rs.RIS_GET_DICOM_VIEWER != "") RIS_GET_DICOM_VIEWER = rs.RIS_GET_DICOM_VIEWER;
	if (rs.RIS_DELETE_REQUEST != "") RIS_DELETE_REQUEST = rs.RIS_DELETE_REQUEST;
	if (rs.RIS_UPDATE_REQUEST != "") RIS_UPDATE_REQUEST = rs.RIS_UPDATE_REQUEST;
	if (rs.RIS_GET_REPORT != "") RIS_GET_REPORT = rs.RIS_GET_REPORT;
	if (rs.RIS_DELETE_ORDER != "") RIS_DELETE_ORDER = rs.RIS_DELETE_ORDER;
	
	
	RIS_CONNECTION_TYPE_V1 = rs.RIS_CONNECTION_TYPE_V1;
	RIS_USERNAME_V1 = rs.RIS_USERNAME_V1;
	RIS_SECRET_KEY_V1 = rs.RIS_SECRET_KEY_V1;
	RIS_SERVICE_DOMAIN_NAME_V1 = rs.RIS_SERVICE_DOMAIN_NAME_V1;
	RIS_PROVIDER_V1 = rs.RIS_PROVIDER_V1;
	RIS_RPOVIDER_AUTH_V1 = rs.RIS_PROVIDER_AUTH_V1;
	
	if (rs.RIS_CANCEL_REQUEST_V1 != "") RIS_CANCEL_REQUEST_V1 = rs.RIS_CANCEL_REQUEST_V1;
	if (rs.RIS_GET_DICOM_VIEWER_V1 != "") RIS_GET_DICOM_VIEWER_V1 = rs.RIS_GET_DICOM_VIEWER_V1;
	if (rs.RIS_DELETE_REQUEST_V1 != "") RIS_DELETE_REQUEST_V1 = rs.RIS_DELETE_REQUEST_V1;
	if (rs.RIS_UPDATE_REQUEST_V1 != "") RIS_UPDATE_REQUEST_V1 = rs.RIS_UPDATE_REQUEST_V1;
	if (rs.RIS_GET_REPORT_V1 != "") RIS_GET_REPORT_V1 = rs.RIS_GET_REPORT_V1;
	if (rs.RIS_DELETE_ORDER_V1 != "") RIS_DELETE_ORDER_V1 = rs.RIS_DELETE_ORDER_V1;
	
}
