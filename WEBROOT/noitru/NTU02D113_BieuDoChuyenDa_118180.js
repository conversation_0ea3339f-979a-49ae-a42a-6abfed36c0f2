function DataBDCD() {
    /*------ Thong tin chung -----*/
    this.soyte = '';
    this.ten_benhvien = '';
    this.khoa = '';
    /*------ Thong tin ca nhan benh nhan -----*/
    this.hoten = '';
    this.tuoi = '';
    this.sovaovien = '';
    this.lan_thai = '';
    this.lan_de = '';
    this.ngay_vao = '';
    this.gio_vao = '';
    this.oi_vo = '';
    this.gio_oi_vo = '';

    /*------ Thong tin phieu bieu do chuyen da -----*/
    this.id_bv_bieudochuyenda = '';
    this.id_benhan = '';
    this.khoang_theodoi = 4;

    this.giodo = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.mach = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.huyetap = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.nhietdo = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.protein = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.timthai = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.tinhtrang_oi = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.chongkhop = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '','');
    this.domo_ctc = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.ngoithai = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.conco_ctc = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.gio = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.thoigian = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.oxitocin = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.giotphut = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.aceton = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.thetich = Array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
    this.dienbienkhac = '';

    var that = this;
}
function BieuDoChuyenDa(_data, opt) {

    this.data = _data;
    this.ajxUrl = '../Ajax.aspx/';
    var that = this;
    var cach = 5;

    // In
    this.in_trang = function () {
            var panel = document.getElementById("print-page");
            var printWindow = window.open('', '', 'left=50000,top=50000,width=0,height=0');
            printWindow.document.write('<html moznomarginboxes mozdisallowselectionprint>');
            printWindow.document.write('<head><style type="text/css" media="print">@page {size: auto;margin: 5px 5px 5px 5px;}</style></head>');
            printWindow.document.write('<body >');
            printWindow.document.write(panel.innerHTML);
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            setTimeout(function () {
                printWindow.print();
            }, 500);
            return false;
    }

    // Hiển thị thông báo
    this.showThongBao = function (loai, thongbao) {
        var _type = 'info';
        switch (loai) {
            case 1: _type = 'success'; break;
            case 2: _type = 'danger'; break;
            case 3: _type = 'warning'; break;
            default: _type = 'info'; break;
        }
        
        $("#notThongBao").attr('class', 'alert alert-' + _type + ' no-alert-styled-left');
        $('#notThongBao .text-semibold').html(thongbao);
        $('#notThongBao').show();
    }


    /* -------------------------------------- BIỂU ĐỒ CHUYỂN DẠ ----------------------------------- */
    // Fill thông tin chung
    this.fillThongTinChung = function () {
        $('#soyte').html(that.data.soyte);
        $('#ten_benhvien').html(that.data.ten_benhvien);
        $('#khoa').html(that.data.khoa);
    }

    // Fill thông tin bệnh nhân
    this.fillThongTinBenhNhan = function () {
        $('#hoten').html(that.data.hoten);
        $('#tuoi').html(that.data.tuoi);
        $('#sovaovien').html(that.data.sovaovien);
        $('#ngay_vao').html(that.data.ngay_vao);
        $('#gio_oi_vo').html(that.data.gio_oi_vo);
        $('#ngaycapnhat').html(that.data.ngaycapnhat_chuoi);
        $('#para').html(that.data.para);
    }

    // Fill giờ trong ngày
    this.fill_cv_giotrongngay = function () {
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 30;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * 2;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cv_giotrongngay").attr('width', _cvwidth);
        $("#cv_giotrongngay").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i < 3; _i++) {
            $('#cv_giotrongngay').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= 24; _i++) {
            $('#cv_giotrongngay').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
            $('#cv_giotrongngay').drawText({
                fillStyle: '#000', strokeStyle: '#000', strokeWidth: 1, fontSize: 11, fontFamily: 'Verdana, sans-serif',
                x: _i * _lineWidth + 14, y: _startY + 44, text: _i + 1
            });
        }

        // Điền giờ trong ngày
        var _giodo = that.data.giodo;
        //console.log(_giodo);
        _startCell=1;
        for (var _i = 0; _i <= 24; _i++) {
            $('#cv_giotrongngay').drawText({
                fillStyle: '#000', strokeStyle: '#000', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
                x: (_startCell + _i - 1) * _lineWidth + 12, y: _startY + 20, text: !_giodo[_i] ? '--' : _giodo[_i]
            });
            //console.log(_giodo[_i]);
        }
        


    }



    // Fill theo dõi người mẹ
    this.fill_cv_theodoime = function () {
        var _numLine = 12;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cv_theodoime").attr('width', _cvwidth);
        $("#cv_theodoime").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cv_theodoime').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cv_theodoime').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }
    }

    // Fill dữ liệu mạch
    this.fillMach = function () {
        var _mach = that.data.mach; // mạch
        var _huyetap = that.data.huyetap; // Huyết áp
        var _haThu = new Array();
        var _haTruong = new Array();


        //console.log(_huyetap);
        var _numLine = 12;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvWidth = _lineWidth * 24 + 2;
        var _lastMachX = 0;
        var _lastMachY = 0;
        var _startNum = 0;
        $("#cv_theodoime").attr('width', _cvWidth);
        $("#cv_theodoime").attr('height', _cvHeight);
        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cv_theodoime').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cv_theodoime').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }
        
        for (var _i = 0; _i < 24; _i++) {
            // Vẽ biểu đồ mạch
            if (_mach[_i] != null && _mach[_i] != '') {
                //var _xPointMach = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25;
                var _xPointMach = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 12.5;
                var _yPointMach = (180 - _mach[_i]) * (_cvHeight / (180 - 60));
                // Vẽ điểm mạch
                $('#cv_theodoime').drawEllipse({ fillStyle: '#AA1233', x: _xPointMach, y: _yPointMach, width: 6, height: 6 });

                // Điền mạch text
                $('#cv_theodoime').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 7, fontFamily: 'Verdana, sans-serif',
                    x: _xPointMach, y: _yPointMach + 8, text: _mach[_i]
                });

                // Vẽ đường nối - mạch
                if (_i > 1 && _startNum>0) {
                    $('#cv_theodoime').drawLine({ strokeStyle: '#AA1233', strokeWidth: 2, x1: _lastMachX, y1: _lastMachY, x2: _xPointMach, y2: _yPointMach });
                }

                _lastMachX = _xPointMach;
                _lastMachY = _yPointMach;
                _startNum++;
            }

            // Tách huyết áp
            var _tmp = _huyetap[_i];
            _tmp = _tmp.split('/');
            _haTruong[_i] = _tmp[0] ? _tmp[0] : '';
            _haThu[_i] = _tmp[1] ? _tmp[1] : '';
        }

        // Vẽ huyết áp
        for (var _i = 0; _i < 24; _i++) {

            // Huyết áp trương
            //var _xPointHaTruong = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25;
            var _xPointHaTruong = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 12.5;
            var _yPointHaTruong = (180 - _haTruong[_i]) * (_cvHeight / (180 - 60));
            $('#cv_theodoime').drawText({
                fillStyle: '#FF1B4D', strokeStyle: '#FF1B4D', strokeWidth: 1, fontSize: 11, fontFamily: 'Verdana, sans-serif',
                x: _xPointHaTruong, y: _yPointHaTruong, text: '^'
            });
            $('#cv_theodoime').drawText({
                fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 7, fontFamily: 'Verdana, sans-serif',
                x: _xPointHaTruong + 15 , y: _yPointHaTruong, text: _haTruong[_i]
            });


            // Huyết áp thu
            //var _xPointHaThu = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25;
            var _xPointHaThu = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 12.5;
            var _yPointHaThu = (180 - _haThu[_i]) * (_cvHeight / (180 - 60));
            $('#cv_theodoime').drawText({
                fillStyle: '#FF1B4D', strokeStyle: '#FF1B4D', strokeWidth: 1, fontSize: 11, fontFamily: 'Verdana, sans-serif',
                x: _xPointHaThu, y: _yPointHaThu, text: 'v'
            });
            $('#cv_theodoime').drawText({
                fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 7, fontFamily: 'Verdana, sans-serif',
                x: _xPointHaThu + 15, y: _yPointHaThu, text: _haThu[_i]
            });


            // Vẽ dải huyết áp
            $('#cv_theodoime').drawLine({ strokeStyle: '#095519', strokeWidth: 2, x1: _xPointHaTruong, y1: _yPointHaTruong, x2: _xPointHaThu, y2: _yPointHaThu });

        }
    }

    // Fill dữ liệu nhiệt độ
    this.fillNhietDo = function () {
        var _nhietdo = that.data.nhietdo; // dữ liệu nhiệt độ
        //console.log(_huyetap);
        var _numLine = 6;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvWidth = _lineWidth * 24 + 2;
        var _lastMachX = 0;
        var _lastMachY = 0;
        
        //console.log(_nhietdo);
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (_nhietdo[_i] != null && _nhietdo[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cv_nhietdo').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: _nhietdo[_i] 
                });
            } 
        }
    }
    
    // Fill nhiệt độ
    this.fill_cv_nhietdo12 = function () {
    	var _nhietdo = that.data.nhietdo;
        var _numLine = 1;
        var _numCol = 12;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23 * 2;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 12 + 2;

        $("#cv_nhietdo").attr('width', _cvwidth);
        $("#cv_nhietdo").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cv_nhietdo').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cv_nhietdo').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }
        var _lineWidth = 23;
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (_nhietdo[_i] != null && _nhietdo[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cv_nhietdo').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: _nhietdo[_i] 
                });
            } 
        }
    }

    // Fill protein nước tiểu
    this.fill_cv_nuoctieu_3con = function () {
        var _protein = that.data.protein; // dữ liệu protein
        var _numLine = 3;
        var _numCol = 12;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23 * 2;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 12 + 2;

        $("#cvProteinNuocTieu").attr('width', _cvwidth);
        $("#cvProteinNuocTieu").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvProteinNuocTieu').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvProteinNuocTieu').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

        // Fill dữ liệu
        var _lineWidth = 23;
        //console.log(_nhietdo);
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (_protein[_i] != null && _protein[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cvProteinNuocTieu').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: _protein[_i]
                });
            }
        }

    }

    // Fill nhịp tim thai
    this.fillCvNhipTimThai = function () {
       
        var _timthai = that.data.timthai; // dữ liệu timthai
        var _numLine = 12;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 21;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvWidth = _lineWidth * 24 + 2;

        $("#cvNhipTimThai").attr('width', _cvWidth);
        $("#cvNhipTimThai").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvNhipTimThai').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvNhipTimThai').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

        // Fill dữ liệu
        var _lastX = 0;
        var _lastY = 0;
        var _startNum = 0;
        for (var _i = 0; _i < 24; _i++) {
            if (_timthai[_i] != null && _timthai[_i] != '') {
                //var _xPoint = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 25;
                var _xPoint = (_cvWidth / 24) * _i - (_cvWidth / (24 * 2)) + 12.5;
                var _yPoint = (200 - _timthai[_i]) * (_cvHeight / (200 - 80));
                // Vẽ điểm mạch
                //$('#cvNhipTimThai').drawEllipse({ fillStyle: '#5A8300', x: _xPoint + 10, y: _yPoint + 10, width: 7, height: 7});
                $('#cvNhipTimThai').drawEllipse({ fillStyle: '#5A8300', x: _xPoint , y: _yPoint , width: 7, height: 7});

                // Điền mạch text

                $('#cvNhipTimThai').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint -10, y: _yPoint - 15, text: _timthai[_i],
                    fromCenter:false
                });

                // Vẽ đường nối - mạch
                if (_i > 0 &&  _startNum>0) {
                    //$('#cvNhipTimThai').drawLine({ strokeStyle: '#AA1233', strokeWidth: 2, x1: _lastX+10 , y1: _lastY+ 10, x2: _xPoint+10, y2: _yPoint + 10 });
                    $('#cvNhipTimThai').drawLine({ strokeStyle: '#AA1233', strokeWidth: 2, x1: _lastX , y1: _lastY, x2: _xPoint, y2: _yPoint });
                }

                _lastX = _xPoint;
                _lastY = _yPoint;
                _startNum++;
            }
        }

    }

    // Fill tình trạng ối, chống khớp
    this.fillCvOiChongKhop = function () {
        var _tinhtrang_oi = that.data.tinhtrang_oi;
        var _chongkhop = that.data.chongkhop;
        var _numLine = 2;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cvOiChongKhop").attr('width', _cvwidth);
        $("#cvOiChongKhop").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvOiChongKhop').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvOiChongKhop').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

        // Fill dữ liệu tình trạng ối
        var _lineWidth = 23;
        // Fill tình trạng ối
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (_tinhtrang_oi[_i] != null && _tinhtrang_oi[_i] != '' && _chongkhop[_i] != null && _chongkhop[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cvOiChongKhop').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: _tinhtrang_oi[_i]
                });

                $('#cvOiChongKhop').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint + 20, text: _chongkhop[_i]
                });
            }
        }
    }

    // fill oxitocin, giọt
    this.fillCvOxitocinGiot = function () {
        var oxitocin = that.data.oxitocin;
        var giotphut = that.data.giotphut;
        var _numLine = 2;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cvOxitocin").attr('width', _cvwidth);
        $("#cvOxitocin").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvOxitocin').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvOxitocin').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

        // Fill dữ liệu
        var _lineWidth = 23;
        // Fill tình trạng ối
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (oxitocin[_i] != null && oxitocin[_i] != '' && giotphut[_i] != null && giotphut[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cvOxitocin').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: oxitocin[_i]
                });

                $('#cvOxitocin').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint + 20, text: giotphut[_i]
                });
            }
        }
    }
    
    this.fillCvNuocTieu = function () {
    	var _data ='';
    	var protein=[];
    	var aceton=[];
    	var thetich=[];
    	if (that.data.json_data) {
    		_data = JSON.parse(that.data.json_data);
            protein = chuyenMang(_data['Protein'], '');
            aceton = chuyenMang(_data['Axeton'], '');
            thetich = chuyenMang(_data['Thetich'], '');
    	}
        var _numLine = 3;
        var _numCol = 12;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23*2;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 12 + 2;

        $("#cvProteinNuocTieu").attr('width', _cvwidth);
        $("#cvProteinNuocTieu").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvProteinNuocTieu').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvProteinNuocTieu').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

        // Fill dữ liệu
        var _lineWidth = 23;
        // Fill tình trạng ối
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (protein[_i] != null && protein[_i] != '' && aceton[_i] != null && aceton[_i] != '' && thetich[_i] != null && thetich[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cvProteinNuocTieu').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: protein[_i]
                });

                $('#cvProteinNuocTieu').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint + 20, text: aceton[_i]
                });
                
                $('#cvProteinNuocTieu').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint + 40, text: thetich[_i]
                });
            }
        }
    }
    
    this.fill_cv_thuocdadung = function () {
    	var _data ='';
    	var thuoc=[];
    	if (that.data.json_data) {
    		_data = JSON.parse(that.data.json_data)
    		thuoc = chuyenMang(_data['Thuoc'], '');
    	}
        var _numLine = 1;
        var _numCol = 12;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 100;
        var _lineWidth = 23*2;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 12 + 2;

        $("#cv_thuocdadung").attr('width', _cvwidth);
        $("#cv_thuocdadung").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cv_thuocdadung').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cv_thuocdadung').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

     // Fill dữ liệu
        var _lineWidth = 23;
        // Fill tình trạng ối
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (thuoc[_i] != null && thuoc[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 40;
                $('#cv_thuocdadung').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif', rotate: -270,
                    x: _xPoint, y: _yPoint, text: thuoc[_i]
                });
            }
        }
    }

    // Fill giờ trong ngày 2
    this.fillCvGioTrongNgay2 = function () {

        var _numLine = 1;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cvGioTrongNgay2").attr('width', _cvwidth);
        $("#cvGioTrongNgay2").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvGioTrongNgay2').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvGioTrongNgay2').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });

            if (_i == 3 || _i == 7 || _i == 11 || _i == 15 || _i == 19 || _i == 23) {
                $('#cvGioTrongNgay2').drawText({
                    fillStyle: '#000', strokeStyle: '#000', strokeWidth: 1, fontSize: 11, fontFamily: 'Verdana, sans-serif',
                    x: _i * _lineWidth + 14, y: _startY + 12, text: _i + 1
                });
            }
        }
    }

    // fill giờ thời gian
    this.fillGioThoiGian = function () {
        var _gio = that.data.gio;
        var _thoigian = that.data.thoigian;
        var _numLine = 2;
        var _numCol = 12;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23 * 2;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 12 + 2;

        $("#cvGioThoiGian").attr('width', _cvwidth);
        $("#cvGioThoiGian").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvGioThoiGian').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvGioThoiGian').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });

        }

        // Fill dữ liệu
        var _lineWidth = 23;
        for (var _i = 0; _i < 24; _i++) {
            // Điền nhiệt độ
            if (_gio[_i] != null && _gio[_i] != '' && _thoigian[_i] != null && _thoigian[_i] != '') {
                var _xPoint = _i * _lineWidth + 1 + 12;
                var _yPoint = _startY + 10;
                $('#cvGioThoiGian').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: _gio[_i]
                });

                $('#cvGioThoiGian').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint + 20, text: _thoigian[_i]
                });
            }
        }
    }

    // Fill độ mở CTC
    this.fillDoMoCTC = function () {
        var _domo_ctc = that.data.domo_ctc;
        var _ngoithai = that.data.ngoithai;
        var _numLine = 10;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 20;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cvDoMoCTC").attr('width', _cvwidth);
        $("#cvDoMoCTC").attr('height', _cvHeight);

        // Validate
        if (_domo_ctc.length == 0) {
            console.log('#WARNING: Data empty!');
            return;
        }

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvDoMoCTC').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvDoMoCTC').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }


        // Fill pha tiềm tàng - pha tích cực

        // Pha tiềm tàng
        //$('#cvDoMoCTC').drawRect({fillStyle: '#FFF',x: _startX + 25 , y: _lineHeight -19,width: 113,height: 18,fromCenter: false});
        // $('#cvDoMoCTC').drawText({
        //     fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
        //     x: _startX + 70, y: _lineHeight - 10, text: 'PHA TIỀM TÀNG'
        // });

        // Pha tích cực
        //$('#cvDoMoCTC').drawRect({fillStyle: '#FFF',x: _startX + 215, y: _lineHeight - 19,width: 113,height: 18,fromCenter: false});
        // $('#cvDoMoCTC').drawText({
        //     fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
        //     x: _startX + 275, y: _lineHeight - 10, text: 'PHA TÍCH CỰC '
        // });

        // Đường báo động
        $('#cvDoMoCTC').drawLine({
            strokeStyle: '#25a', strokeWidth: 2,
            x1: 276, y1: 0,
            x2: 0, y2: 120,
        });
        $('#cvDoMoCTC').drawText({
            fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
            x: 110, y: 120 -50, text: 'ĐƯỜNG BÁO ĐỘNG',
            fromCenter: false,
            rotate: -22
        });

        // Đường hành động
        $('#cvDoMoCTC').drawLine({
            strokeStyle: '#25a', strokeWidth: 2,
            x1: 460, y1: 0,
            x2: 186, y2: 120
        });
        $('#cvDoMoCTC').drawText({
            fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 9, fontFamily: 'Verdana, sans-serif',
            x: 290, y: 120 - 50, text: 'ĐƯỜNG HÀNH ĐỘNG',
            fromCenter: false,
            rotate: -22
        });


        // Fill dữ liệu độ mở ctc
        var dem = 0;
        var _lastX = 0;
        var _lastY = 0;
        for (var _i = 0; _i < 24; _i++) {
            if (_domo_ctc[_i] != null && _domo_ctc[_i] != '') {
                //var _xPoint = (_cvwidth / 24) * _i - (_cvwidth / (24 * 2)) + 25;
                var _xPoint = (_cvwidth / 24) * _i - (_cvwidth / (24 * 2)) + 12.5;
                var _yPoint = (10 - _domo_ctc[_i]) * (_cvHeight / (10 - 0));
                //var _lastX = _xPoint;
                //var _lastY = _yPoint;
  
                // Vẽ điểm mạch
                //$('#cvDoMoCTC').drawEllipse({ fillStyle: '#005515', x: _xPoint, y: _yPoint, width: 7, height: 7 }); // Diem tron
                // Ky tu x
                $('#cvDoMoCTC').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 10, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: 'X'
                });

                // Điền chỉ số text
                $('#cvDoMoCTC').drawText({
                    fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint -20, y: _yPoint + 5, text: _domo_ctc[_i] + ' cm'
                });

                // Vẽ đường nối
                if (_i > 0 && dem>0) {
                    //alert(_lastX); alert(_xPoint);
                    $('#cvDoMoCTC').drawLine({ strokeStyle: '#25a', strokeWidth: 1, x1: _lastX, y1: _lastY, x2: _xPoint, y2: _yPoint });
                    //$('#cvDoMoCTC').drawLine({ strokeStyle: '#005515', strokeWidth: 1, x1: _lastX, y1: _lastY, x2: _xPoint, y2: _yPoint });
                }
                dem++;
                _lastX = _xPoint;
                _lastY = _yPoint;
            }
        }
        dem = 0;
        // Fill dữ liệu ngôi thai
        for (var _i = 0; _i < 24; _i++) {
            if (_ngoithai[_i] != null && _ngoithai[_i] != '') {
                //var _xPoint = (_cvwidth / 24) * _i - (_cvwidth / (24 * 2)) + 25;
                var _xPoint = (_cvwidth / 24) * _i - (_cvwidth / (24 * 2)) + 12.5;
                var _yPoint = (10 - _ngoithai[_i]) * (_cvHeight / (10 - 0));
                
                // Vẽ điểm 
                

                //$('#cvDoMoCTC').drawEllipse({ fillStyle: '#AA1233', x: _xPoint, y: _yPoint, width: 7, height: 7 }); // Diem tron

                $('#cvDoMoCTC').drawText({
                    fillStyle: '#AA1233', strokeStyle: '#AA1233', strokeWidth: 1, fontSize: 10, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint, y: _yPoint, text: 'O'
                });

                // Điền chỉ số text
                $('#cvDoMoCTC').drawText({
                    fillStyle: '#AA1233', strokeStyle: '#AA1233', strokeWidth: 1, fontSize: 8, fontFamily: 'Verdana, sans-serif',
                    x: _xPoint - 20, y: _yPoint -5, text: _ngoithai[_i] + ' cm'
                });

                // Vẽ đường nối
                if (_i > 0 && dem>0) {
                    $('#cvDoMoCTC').drawLine({ strokeStyle: '#AA1233', strokeWidth: 1, x1: _lastX, y1: _lastY, x2: _xPoint, y2: _yPoint });
                }
                dem++;
                _lastX = _xPoint;
                _lastY = _yPoint;
            }
        }

    }

    // Fill cơn co CTC
    this.fillConCoCTC = function () {
        var _conco_ctc = that.data.conco_ctc;
        var _numLine = 5;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 100;
        var _lineHeight = 20; 
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cvConCoCTC").attr('width', _cvwidth);
        $("#cvConCoCTC").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvConCoCTC').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvConCoCTC').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }

        // Fill dữ liệu
        for (var _i = 0; _i < 24; _i++) {
            if (_conco_ctc[_i] != null && _conco_ctc[_i] != '') {
                var _xPoint = (_cvwidth / 24) * _i - (_cvwidth / (24 * 2)) + 25;
                var _yPoint = 0;
                // Tách chỉ số
                var _tmp = _conco_ctc[_i];
                _tmp = _tmp.split('-');
                _conco = _tmp[0] ? _tmp[0] : '';
                _loaico= _tmp[1] ? _tmp[1] : '';
                //console.log('Thời gian: ' + _i + 'h; Số con co: ' + _conco + '; Loại co: ' + _loaico);

                if (_loaico == '') {
                    _loaico = 1;
                }

                for (var j = 0; j < _conco; j++) {
                    $('#cvConCoCTC').drawImage({
                        source: '../common/image/NTU02D113_BieuDoChuyenDa/conco_ctc_' + _loaico + '.png', fromCenter:false,
                        x: _xPoint - 13, y: 100 - (_yPoint + j * _lineHeight) - 20
                    });
                }
            }
        }
    }

    // Fill diễn biến khác & xử trí
    this.fillCvDienBienXuTri = function () {
        var _dienbienkhac = that.data.dienbienkhac;
        var _numLine = 1;
        var _numCol = 1;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 50;
        var _lineWidth = 23*24;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth  + 2;

        $("#cvDienBienXuTri").attr('width', _cvwidth);
        $("#cvDienBienXuTri").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvDienBienXuTri').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvDienBienXuTri').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }
        

        // Fill dữ liệu
        $('#cvDienBienXuTri').drawText({
            fillStyle: '#25a', strokeStyle: '#25a', strokeWidth: 1, fontSize: 10, fontFamily: 'Verdana, sans-serif',
            x: 10, y: 10, text: _dienbienkhac,
            fromCenter: false
        });
    }

    // Fill người theo dõi
    this.fillCvNguoiTheoDoi = function () {

        var _numLine = 1;
        var _numCol = 24;
        var _startX = 0;
        var _startY = 0;
        var _stopX = 644;
        var _stopY = 380;
        var _lineHeight = 40;
        var _lineWidth = 23;
        var _cvHeight = _lineHeight * _numLine;
        var _cvwidth = _lineWidth * 24 + 2;

        $("#cvNguoiTheoDoi").attr('width', _cvwidth);
        $("#cvNguoiTheoDoi").attr('height', _cvHeight);

        // Fill dòng
        for (var _i = 0; _i <= _numLine; _i++) {
            $('#cvNguoiTheoDoi').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1.5, x1: _startX + 2, y1: _i * _lineHeight, x2: _stopX, y2: _i * _lineHeight });
        }

        // Fill cột 
        for (var _i = 0; _i <= _numCol; _i++) {
            $('#cvNguoiTheoDoi').drawLine({ strokeStyle: '#7F7F83', strokeWidth: 1, x1: _i * _lineWidth + 1, y1: _startY, x2: _i * _lineWidth + 1, y2: _stopY - 2 });
        }
    }

    // Fill data
    this.fillData = function () {
        that.fillThongTinChung();
        that.fillThongTinBenhNhan();
        //that.fill_cv_giotrongngay();
        //that.fill_cv_theodoime();
        that.fill_cv_nhietdo12();
        //that.fillNhietDo();

        //that.fillCvProteinNuocTieu();
        that.fillCvNhipTimThai();
        that.fillCvOiChongKhop();
        //that.fillCvGioTrongNgay2();
        that.fillDoMoCTC();
        that.fillGioThoiGian();
        that.fillConCoCTC();
        that.fillCvOxitocinGiot();
        that.fill_cv_thuocdadung();
        //that.fillNhietDo();
        //that.fill_cv_nuoctieu_3con();
        that.fillMach();
        that.fillCvNuocTieu();
        draw_json_data(that.data.json_data, that.data.domo_ctc);

        //that.fillCvDienBienXuTri();
        //that.fillCvNguoiTheoDoi();


        // Fill biểu đồ
        // that.fillMach();
    }

    this.fillFormInPut = function () {
    	$('#txtNGAYCAPNHAT').val(that.data.ngaycapnhat_chuoi);
        $('#txtLanThai').val(that.data.lan_thai);
        $('#txtLanDe').val(that.data.lan_de);
        $('#sel_giochuyenda').val(that.data.gio_vao);
        $('#sel_tinhtrangoi').val(that.data.oi_vo);
        $('#sel_giooivo').val(that.data.gio_oi_vo);
        $('#txtDienBienXuTri').val(that.data.dienbienkhac);
        $('#txtPara').val(that.data.para);

        for (var i = 1; i <= 24; i++) {
            $('#txt_giodo_' + i).val(that.data.giodo[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_mach_' + i).val(that.data.mach[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_huyetap_' + i).val(that.data.huyetap[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_nhietdo_' + i).val(that.data.nhietdo[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#sel_protein_' + i).val(that.data.protein[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_timthai_' + i).val(that.data.timthai[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#sel_tinhtrangoi_' + i).val(that.data.tinhtrang_oi[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#sel_chongkhop_' + i).val(that.data.chongkhop[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_domoctc_' + i).val(that.data.domo_ctc[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_ngoithai_' + i).val(that.data.ngoithai[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_concoctc_' + i).val(that.data.conco_ctc[i - 1]);
        }

        for (var i = 1; i <= 24; i++) {
            $('#txt_gio_' + i).val(that.data.gio[i - 1]);
        }
        for (var i = 1; i <= 24; i++) {
            $('#txt_thoigian_' + i).val(that.data.thoigian[i - 1]);
        }
        for (var i = 1; i <= 24; i++) {
            $('#txt_oxitocin_' + i).val(that.data.oxitocin[i - 1]);
        }
        for (var i = 1; i <= 24; i++) {
            $('#txt_giotphut_' + i).val(that.data.giotphut[i - 1]);
        }
        set_json_data(that.data.json_data);

    }

    /*----------------- Gen html ----------------*/

    this.genInputForm = function () {
        that.genGioTrongNgay();
        that.genGioDo();
        that.genMach();
        that.genHuyetAp();
        that.genNhietDo();
        that.genProtein();
        that.genTimThai();
        that.genTinhTrangOi();
        that.genChongKhop();
        that.genDoMoCTC();
        that.genNgoiThai();
        that.genConCoCTC();
        that.genGio();
        that.genThoiGian();
        that.genOxitocin();
        that.genGiotPhut();
        genHTML_json_data(opt);

        
        // Mask
        $('.txtGioDo').mask('00:00');
        $('.txtMach').mask('000');
        $('.txtHuyetAp').mask('000/000');
        $('.txtNhietDo').mask('00.0');
        // $('.txtConCoCTC').mask('0-Z', {
        //     translation: {
        //       'Z': {
        //         pattern: /[1-3]/
        //       }
        //     }
        //   });
        $('.txtConCoCTC').mask('Y-Z', {
            translation: {
                'Y': {
                    pattern: /[1-5]/,
                },
                'Z': {
                    pattern: /[1-3]/,
                }
            }
        });



        $('.txtNgoiThai').mask('00');
        $('.txtTimThai').mask('000');
        $('.txtDoMoCTC').mask('00');
        



    }

    this.genGioTrongNgay = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += "<td align='center'>"+i+"<\/td>";
        }
        
        $('.inGioTrongNgay').html(_html);

    }

    this.genGioDo = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_giodo_' + i + '" class="form-control txtGioDo"  value="" \/><\/td>';
        }
        $('.inGioDo').html(_html);
    }

    this.genMach = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_mach_'+i+'" class="form-control txtMach"  value="" \/><\/td>';
        }
        $('.inMeMach').html(_html);
    }

    this.genHuyetAp = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_huyetap_' + i + '" class="form-control txtHuyetAp" value="" \/><\/td>';
        }
        $('.inMeHuyetAp').html(_html);
    }

    this.genNhietDo = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_nhietdo_' + i + '" class="form-control txtNhietDo" value="" \/><\/td>';
        }
        $('.inMeNhietDo').html(_html);
    }

    this.genProtein = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><select class="form-control" id="sel_protein_' + i + '">';
            _html += '	<option value=""><\/option>';
            _html += '	<option value="(-)">(-)<\/option>';
            _html += '	<option value="(+)">(+)<\/option>';
            _html += '	<option value="(++)">(++)<\/option>';
            _html += '	<option value="(+++)">(+++)<\/option>';
            _html += '<\/select></td>';
        }
        $('.inProtein').html(_html);
    }

    this.genTimThai = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_timthai_' + i + '" class="form-control txtTimThai" value="" \/><\/td>';
        }
        $('.inTimThai').html(_html);
    }

    this.genGio = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_gio_' + i + '" class="form-control " value="" \/><\/td>';
        }
        $('.inGio').html(_html);
    }

    this.genThoiGian = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_thoigian_' + i + '" class="form-control " value="" \/><\/td>';
        }
        $('.inThoiGian').html(_html);
    }

    this.genOxitocin = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_oxitocin_' + i + '" class="form-control " value="" \/><\/td>';
        }
        $('.inOxitocin').html(_html);
    }

    this.genGiotPhut = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_giotphut_' + i + '" class="form-control " value="" \/><\/td>';
        }
        $('.inGiotPhut').html(_html);
    }

    this.genTinhTrangOi = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><select class="form-control" id="sel_tinhtrangoi_' + i + '">';
            _html += '	<option value=""><\/option>';
            _html += '	<option value="C">C<\/option>';
            _html += '	<option value="T">T<\/option>';
            _html += '	<option value="X">X<\/option>';
            _html += '	<option value="VO">Vỡ<\/option>';
            _html += '	<option value="BAM">Bấm ối<\/option>';
            _html += '<\/select></td>';
        }
        $('.inTinhTrangOi').html(_html);
    }

    this.genTinhTrangOi = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><select class="form-control" id="sel_tinhtrangoi_' + i + '">';
            _html += '	<option value=""><\/option>';
            _html += '	<option value="C">C<\/option>';
            _html += '	<option value="T">T<\/option>';
            _html += '	<option value="X">X<\/option>';
            _html += '	<option value="VO">Vỡ<\/option>';
            _html += '	<option value="BAM">Bấm ối<\/option>';
            _html += '<\/select></td>';
        }
        $('.inTinhTrangOi').html(_html);
    }

    this.genChongKhop = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><select class="form-control" id="sel_chongkhop_' + i + '">';
            _html += '	<option value=""><\/option>';
            _html += '	<option value="0">0<\/option>';
            _html += '	<option value="+">+<\/option>';
            _html += '	<option value="++">++<\/option>';
            _html += '	<option value="+++">+++<\/option>';
            _html += '<\/select></td>';
        }
        $('.inChongKhop').html(_html);
    }

    this.genDoMoCTC = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_domoctc_' + i + '" class="form-control txtDoMoCTC" value="" \/><\/td>';
        }
        $('.inDoMoCTC').html(_html);
    }

    this.genNgoiThai = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_ngoithai_' + i + '" class="form-control txtNgoiThai" value="" \/><\/td>';
        }
        $('.inNgoiThai').html(_html);
    }

    this.genConCoCTC = function () {
        var _num = 24;
        var _html = '';
        for (var i = 1; i <= _num; i++) {
            _html += '<td><input type="text" id="txt_concoctc_' + i + '" class="form-control txtConCoCTC" value="" \/><\/td>';
        }
        $('.inConCoCTC').html(_html);
    }

    /*----------------- Data ----------------*/

    this.getDateFromString = function (pStr) {
        if (pStr == '') return '';
        var _nam = pStr.substring(0, 4);
        var _thang = pStr.substring(4, 6);
        var _ngay = pStr.substring(6, 8);
        return _ngay + '/' + _thang + '/' + _nam ;
    }

    this.getGioDo = function () {
        var _aGioDo = new Array();
        for (var i = 1; i <= 24; i++) {
            _aGioDo.push($('#txt_giodo_' + i).val());
        }
        return _aGioDo;
    }

    this.getMach = function () {
        var _aMach = new Array();
        for (var i = 1; i <= 24; i++) {
            _aMach.push($('#txt_mach_' + i).val());
        }
        return _aMach;
    }

    this.getHuyetAp = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#txt_huyetap_' + i).val());
        }
        return _rs;
    }

    this.getNhietDo = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#txt_nhietdo_' + i).val());
        }
        return _rs;
    }

    this.getProtein = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#sel_protein_' + i).val());
        }
        return _rs;
    }

    this.getTimThai = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#txt_timthai_' + i).val());
        }
        return _rs;
    }

    this.getTinhTrangOi = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#sel_tinhtrangoi_' + i).val());
        }
        return _rs;
    }

    this.getChongKhop = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#sel_chongkhop_' + i).val());
        }
        return _rs;
    }

    this.getDoMoCTC = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#txt_domoctc_' + i).val());
        }
        return _rs;
    }

    this.getNgoiThai = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#txt_ngoithai_' + i).val());
        }
        return _rs;
    }

    this.getConCoCTC = function () {
        var _rs = new Array();
        for (var i = 1; i <= 24; i++) {
            _rs.push($('#txt_concoctc_' + i).val());
        }
        return _rs;
    }

    this.convertArray2String = function (_array, _split) {
        var _rs = '';

        if (_array == null || _array.length==0) {
            return '';
        }
        _rs = _array.join('#');
    }

    this.getUrlParrams = function () {
        var vars = [], hash;
        var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            vars.push(hash[0]);
            vars[hash[0]] = hash[1];
        }
        return vars;
    }

    this.save = function () {
    	that.data.ngaycapnhat_chuoi = $('#txtNGAYCAPNHAT').val();
        that.data.lan_thai = $('#txtLanThai').val()!=''?$('#txtLanThai').val():'0';
        that.data.lan_de = $('#txtLanDe').val() != '' ? $('#txtLanDe').val() : '0';
        that.data.gio_vao = $('#sel_giochuyenda').val();
        that.data.oi_vo = $('#sel_tinhtrangoi').val();
        that.data.gio_oi_vo = $('#sel_giooivo').val();
        that.data.dienbienkhac = $('#txtDienBienXuTri').val();
        that.data.para = $('#txtPara').val();
        

        for (var i = 1; i <= 24; i++) {
            that.data.giodo[i - 1] = $('#txt_giodo_' + i).val();
        }

        // Mạch
        for (var i = 1; i <= 24; i++) {
            that.data.mach[i - 1] = $('#txt_mach_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.huyetap[i - 1] = $('#txt_huyetap_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.nhietdo[i - 1] = $('#txt_nhietdo_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.protein[i - 1] = $('#sel_protein_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.timthai[i - 1] = $('#txt_timthai_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.tinhtrang_oi[i - 1] = $('#sel_tinhtrangoi_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.chongkhop[i - 1] = $('#sel_chongkhop_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.domo_ctc[i - 1] = $('#txt_domoctc_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.ngoithai[i - 1] = $('#txt_ngoithai_' + i).val();
        }


        for (var i = 1; i <= 24; i++) {
            that.data.conco_ctc[i - 1] = $('#txt_concoctc_' + i).val();
        }

        for (var i = 1; i <= 24; i++) {
            that.data.gio[i - 1] = $('#txt_gio_' + i).val();
        }
        for (var i = 1; i <= 24; i++) {
            that.data.thoigian[i - 1] = $('#txt_thoigian_' + i).val();
        }
        for (var i = 1; i <= 24; i++) {
            that.data.oxitocin[i - 1] = $('#txt_oxitocin_' + i).val();
        }
        for (var i = 1; i <= 24; i++) {
            that.data.giotphut[i - 1] = $('#txt_giotphut_' + i).val();
        }
        that.data.json_data = get_json_data();
        that.Array2String();

        var bdcd = JSON.stringify({ 'bdcd': that.data });

//        console.log(bdcd);
//        var _result = that.requestAjax('saveBDCD', bdcd);
//        that.String2Array();
//
//        if (_result.Code='SUC') {
//            that.showThongBao(1, 'Lưu thành công!');
//            that.fillData();
//        } else {
//            that.showThongBao(2, 'Lưu thất bại!');
//            console.log(_result.Messeage);
//
//        }

        // start laphm custom
        var _par = JSON.stringify(that.data);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D113.02.118180", _par);
		that.String2Array();
		var data = $.parseJSON(result);
		
		if (data != '-1') {
			//that.showThongBao(1, 'Lưu thành công!');
			DlgUtil.showMsg('Lưu thành công!', function() {				
			});
			that.fillData();
		} else {
			//that.showThongBao(2, 'Lưu thất bại!');
			DlgUtil.showMsg('Lưu thất bại!', function() {				
			});
		}	
        // end laphm custom
               
        
    }

    // Convert data biểu đồ từ mảng  sang string
    this.Array2String = function () {
        that.data.giodo = that.data.giodo.join('#');
        that.data.mach = that.data.mach.join('#');
        that.data.huyetap = that.data.huyetap.join('#');
        that.data.nhietdo = that.data.nhietdo.join('#');
        that.data.protein = that.data.protein.join('#');
        that.data.timthai = that.data.timthai.join('#');
        that.data.tinhtrang_oi = that.data.tinhtrang_oi.join('#');
        that.data.chongkhop = that.data.chongkhop.join('#');
        that.data.domo_ctc = that.data.domo_ctc.join('#');
        that.data.ngoithai = that.data.ngoithai.join('#');
        that.data.conco_ctc = that.data.conco_ctc.join('#');
        that.data.gio = that.data.gio.join('#');
        that.data.thoigian = that.data.thoigian.join('#');
        that.data.oxitocin = that.data.oxitocin.join('#');
        that.data.giotphut = that.data.giotphut.join('#');

        //var xxx = that.data.mach.join('#');
        //console.log(that.data);
        //console.log(that.data.mach);
    }

    // Convert data biểu đồ từ string sang Array
    this.String2Array = function () {
        that.data.giodo = that.data.giodo.split('#');
        that.data.mach = that.data.mach.split('#');
        that.data.huyetap = that.data.huyetap.split('#');
        that.data.nhietdo = that.data.nhietdo.split('#');
        that.data.protein = that.data.protein.split('#');
        that.data.timthai = that.data.timthai.split('#');
        that.data.tinhtrang_oi = that.data.tinhtrang_oi.split('#');
        that.data.chongkhop = that.data.chongkhop.split('#');
        that.data.domo_ctc = that.data.domo_ctc.split('#');
        that.data.ngoithai = that.data.ngoithai.split('#');
        that.data.conco_ctc = that.data.conco_ctc.split('#');
        that.data.gio = that.data.gio.split('#');
        that.data.thoigian = that.data.thoigian.split('#');
        that.data.oxitocin = that.data.oxitocin.split('#');
        that.data.giotphut = that.data.giotphut.split('#');
        //console.log(that.data);

    }




    /*------------------------  Load thong tin  ---------------------------*/

    this.requestAjax = function (pTask, pData) {
        var _result = null;
        var _url = that.ajxUrl + pTask;
        try {
            $.ajax({
                type: "POST",
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                url: _url,
                data: pData,
                async: false,
                //if received a response from the server
                success: function (data, textStatus, jqXHR) {
                    _result = $.parseJSON(data.d);
                },

                //If there was no resonse from the server
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log('[' + jqXHR.status + '] - ' + jqXHR.statusText);
                },

                beforeSend: function (jqXHR, settings) { },

                complete: function (jqXHR, textStatus) { }
            });
        } catch (e) {
            console.log('#ERR from Client: ' + e.stack);
        }

        return _result;
    }

    this.loadBDCD = function () {
        var _url = '../Ajax.aspx/loadBDCD';
        var _idBenhAn = $('#hdfIdBenhAn').val();
        if (_idBenhAn == null || _idBenhAn == '') {
            console.log('#ERR: hdfIdBenhAn null');
            return;
        }
        var _data = { pIdBenhAn: _idBenhAn }
        _data = JSON.stringify(_data);

        // --- Thông tin chung ----
        //var _bdcd = that.requestAjax('getBDCDByIdBenhAn', _data);
		
		
		// start laphm custom
		var obj = new Object();
		obj.hosobenhanid = _idBenhAn;
		var _par = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D113.01", _par);
		if (result.length > 0) {
			var _bdcd = result[0];

			// Thông tin chung 
	        that.data.id_bv_bieudochuyenda = _bdcd.ID_BV_BIEUDOCHUYENDA;
	        that.data.id_benhan = _bdcd.ID_BENHAN;

	        that.data.soyte = _bdcd.SOYTE;
	        that.data.ten_benhvien = _bdcd.TEN_BENHVIEN;
	        that.data.khoa = _bdcd.KHOA;

	        // Thông tin bệnh nhân
	        that.data.hoten = _bdcd.HOTEN;
	        that.data.sovaovien = _bdcd.SOVAOVIEN;
	        that.data.tuoi = _bdcd.TUOI;
	        that.data.ngay_vao = _bdcd.NGAY_VAO;
	        that.data.lan_thai = _bdcd.LAN_THAI;
	        that.data.lan_de = _bdcd.LAN_DE;
	        that.data.gio_vao = _bdcd.GIO_VAO;
	        that.data.gio_oi_vo = _bdcd.GIO_OI_VO;
	        that.data.oi_vo = _bdcd.OI_VO;
	        that.data.ngaycapnhat_chuoi = _bdcd.NGAYCAPNHAT_CHUOI;
	        that.data.para = _bdcd.PARA;

	        that.data.giodo = _bdcd.GIODO != '' ? _bdcd.GIODO.split('#') : that.data.giodo;
	        that.data.mach = _bdcd.MACH != '' ? _bdcd.MACH.split('#') : that.data.mach;
	        that.data.huyetap = _bdcd.HUYETAP != '' ? _bdcd.HUYETAP.split('#') : that.data.huyetap;
	        that.data.nhietdo = _bdcd.NHIETDO != '' ? _bdcd.NHIETDO.split('#') : that.data.nhietdo;
	        that.data.protein = _bdcd.PROTEIN != '' ? _bdcd.PROTEIN.split('#') : that.data.protein;
	        that.data.timthai = _bdcd.TIMTHAI != '' ? _bdcd.TIMTHAI.split('#') : that.data.timthai;
	        that.data.tinhtrang_oi = _bdcd.TINHTRANG_OI != '' ? _bdcd.TINHTRANG_OI.split('#') : that.data.tinhtrang_oi;
	        that.data.chongkhop = _bdcd.CHONGKHOP != '' ? _bdcd.CHONGKHOP.split('#') : that.data.chongkhop;
	        that.data.domo_ctc = _bdcd.DOMO_CTC != '' ? _bdcd.DOMO_CTC.split('#') : that.data.domo_ctc;
	        that.data.ngoithai = _bdcd.NGOITHAI != '' ? _bdcd.NGOITHAI.split('#') : that.data.ngoithai;
	        that.data.conco_ctc = _bdcd.CONCO_CTC != '' ? _bdcd.CONCO_CTC.split('#') : that.data.conco_ctc;
            that.data.gio = _bdcd.GIO != '' ? _bdcd.GIO.split('#') : that.data.gio;
            that.data.thoigian = _bdcd.THOIGIAN != '' ? _bdcd.THOIGIAN.split('#') : that.data.thoigian;
            that.data.oxitocin = _bdcd.OXITOCIN != '' ? _bdcd.OXITOCIN.split('#') : that.data.oxitocin;
            that.data.giotphut = _bdcd.GIOTPHUT != '' ? _bdcd.GIOTPHUT.split('#') : that.data.giotphut;
	        that.data.dienbienkhac = _bdcd.DIENBIENKHAC != '' ? _bdcd.DIENBIENKHAC : that.data.dienbienkhac;
	        that.data.json_data = _bdcd.JSON_DATA;
	        // Fill Data
	        that.fillData();
	        that.fillFormInPut();	       
	        $('#notThongBao').hide();

		}
		// end laphm custom
    }
}
//L2PT-34645 CÁC HÀM THÊM (thêm biến json_data)
var objName = ['Thuoc', 'Protein', 'Axeton', 'Thetich']; // các dl điền dạng text trong 24h    //người theo dõi thêm 'Nguoi'
var cach = 5;
function get_json_data() { // form sửa: lấy dl từ form -> obj
	var _obj = new Object();
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		//if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			var _val = [];
			var _valId = [];
			for (var i = 1; i <= 24; i++) {
				_val[i - 1] = $('#txt_' + _name + '_' + i).val();
			}
			_obj[_name] = _val;
		//}
	}
	console.log('=====Lấy dl từ form sửa:', _obj);
	return JSON.stringify(_obj);
}
function set_json_data(data) { // form sửa: set dl
	if (!data)
		return;
	var _data = JSON.parse(data);
	console.log('=====Điền dl vào form sửa:', data);
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		//if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			console.log('  --- set ' + _name);
			if (_data[_name]) {
				for (var i = 1; i <= 24; i++) {
					$('#txt_' + _name + '_' + i).val(_data[_name][i - 1]);
				}
			}
		//}
	}
}
function genHTML_json_data(opt) { // form sửa: tạo html
	var _num = 24;
	console.log('=====genHTML vào form sửa.');
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		var _html = '';
		//if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			console.log('  --- genHTML ' + _name);
			for (var i = 1; i <= _num; i++) {
				//HaNv_261023: L2PT-56736
				if (_name == 'Thuoc') {
					_html += '<td><input type="text" maxlength=15 id="txt_' + _name + '_' + i + '" class="form-control txt' + _name + '" value="" \/><\/td>';
				} else {
					_html += '<td><input type="text" maxlength=5 id="txt_' + _name + '_' + i + '" class="form-control txt' + _name + '" value="" \/><\/td>';
				}
			}
			$('#divFormSua_' + _name).html(_html);
		//}
	}
}
//người theo dõi
function pickerNhanVien(txtSearch, txtTEN, idNguoi) {
	var hearder = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,33,0,f,l;Tên bác sỹ,FULLNAME,22,0,f,l;Chức danh/Khoa phòng,CHUCDANH,42,0,f,l";
	ComboUtil.initComboGrid(txtSearch, "PTTT.LOAD_USER_BDID", [ {
		name : '[0]',
		value : 0
	}, {
		name : '[1]',
		value : '-1'
	} ], "600px", hearder, function(event, ui) {
		var s = ui.item.FULLNAME;
		//var t = $('#' + txtTEN).val();
		//$('#' + txtTEN).val(t == '' ? s : t + ';' + s); cộng thêm tên
		$('#' + txtTEN).val(s);
		$('#' + idNguoi).val(ui.item.USER_ID);//HaNv_110324: L2PT-75635
	});
	$('#' + txtSearch).change(function() {
		$('#' + txtSearch).val('');
	});
}
function draw_Data_Nguoi(divID, data, dataId) {
	try {
		if ($('#hidHospitalId').val() == 28880) {//HaNv_100424: L2PT-81693
			var data1 = [];
			var dataId1 = [];
			var moveX = $('#hidMoveX').val();
			for (var i = 0; i < 23; i++) {
				if (i < moveX) {
					data1[i] = '';
					dataId1[i] = '';
				} else if (i >= moveX) {
					data1[i] = data[i - moveX];
					dataId1[i] = dataId[i - moveX];
				}
			}
			data = data1;
			dataId = dataId1;
		}
		$('#tblBS').find("td").each(function(index) {
			var _html = data[index];
			if ($('#hidHospitalId').val() == 28880 && dataId && dataId.length > 0 && dataId[index]) {//HaNv_110324: L2PT-75635
				var src = jsonrpc.AjaxJson.ajaxCALL_SP_C("DMC12.GET_ANH", dataId[index] + "$");
				if (src) {
					_html = _html + '<span><img class = "imgKy" src="' + src + '"></span>';
				}
			}
			$(this).html(_html);
		});
	} catch (ex) {}
	return;
}
function draw_json_data(data, _domo_ctc) { // form in: hiển thị/vẽ biểu đồ
	if (!data)
		return;
	var _data = JSON.parse(data);
	console.log('=====draw_json_data vào form IN.');
	// Oxytoci GiotPhut Thuoc Protein Axeton Duong
	for (var k = 0; k < objName.length; k++) {
		var _name = objName[k];
		// L2PT-69382 duonghn start
		var _data_to_draw = chuyenMang(_data[_name], _domo_ctc);
		// L2PT-69382 duonghn end
		//if ($('#divFormIn_' + _name).is(":visible")) { //nếu form này được hiển thị
			console.log('  --- draw ' + _name);
			draw_Data('divFormIn_' + _name, _data_to_draw);
		//}
	}
}
function draw_Data(divID, data) {
	var _conco_ctc = data;
	var _numLine = 1;
	var _numCol = 12;
	var _startX = 0;
	var _startY = 0;
	var _stopX = 644;
	var _stopY = 100;
	var _lineHeight = 100;
	var _lineWidth = 23*2;
	var _cvHeight = _lineHeight * _numLine;
	var _cvwidth = _lineWidth * 12 + 2;
	var _canvas = $("#" + divID).find('canvas').eq(0);
	_canvas.attr('width', _cvwidth + cach);
	_canvas.attr('height', _cvHeight);
	// Fill dòng
	for (var _i = 0; _i <= _numLine; _i++) {
		_canvas.drawLine({
			strokeStyle : '#7F7F83',
			strokeWidth : 1.5,
			x1 : _startX + 2 + cach,
			y1 : _i * _lineHeight,
			x2 : _stopX + cach,
			y2 : _i * _lineHeight
		});
	}
	// Fill cột
	for (var _i = 0; _i <= _numCol; _i++) {
		_canvas.drawLine({
			strokeStyle : '#7F7F83',
			strokeWidth : 1,
			x1 : _i * _lineWidth + 1 + cach,
			y1 : _startY,
			x2 : _i * _lineWidth + 1 + cach,
			y2 : _stopY - 2
		});
	}
	//điền dữ liệu
	if (data) {
		_startCell = 1;
		for (var _i = 0; _i <= 24; _i++) {
			_canvas.drawText({
				fillStyle : '#000',
				strokeStyle : '#000',
				strokeWidth : 1,
				fontSize : 9,
				fontFamily : 'Verdana, sans-serif',
				x : (_startCell + _i - 1) * _lineWidth + 12 + cach,
				y : _startY + 10,
				text : data[_i] ? data[_i] : ''
			});
		}
	}
}
function NTU02D113_BieuDoChuyenDa(opt) {

	this.load = doLoad;
	
	var _data = new DataBDCD();
	
    var BDCD = new BieuDoChuyenDa(_data);
	
	function doLoad() {
		initControl(); 
		bindEvent();  
		setInterface();
	}
	
	function initControl() {
		loadBieuDoChuyenDa();
	}
	
	function setInterface(){}

	function bindEvent() {
		
		 $("#print_task").on("click", "#btnNhapPhieu", function () {
			 $('#modCapNhat').modal('show');
         });
		
		$("#print_task").on("click", "#btn_in_trang", function () {
            window.print();
        });
		
		$("#modCapNhat").on("click", "#btnLuu", function () {
            BDCD.save();
        });

        $('#modCapNhat').on('hidden.bs.modal', function () {
            window.location.reload();
        })

		// phim tat
		 $(document).unbind('keydown').keydown(function (e) {
			 var f7 = 118; var f8 = 119;
	            if (e.keyCode == f7) {
	                $('#modCapNhat').modal('show');
	            }
	            if (e.keyCode == f8) {
	                window.print();
	            }
	      });
		
	}
	
	function loadBieuDoChuyenDa(){
    	BDCD.genInputForm();
        BDCD.loadBDCD();
	}
	
}
function chuyenMang(arr, _arr_domo_ctc) {
	var arrOut = $.extend([], arr);
	if (!_arr_domo_ctc || _arr_domo_ctc.length == 0)
		return arrOut;
	var objCP = chuyenPha(_arr_domo_ctc);
	var arrOffset = [];
	if (objCP.CHUYEN) {
		for (var indx = 1; indx < objCP.OFFSET; indx++) {
			arrOut.splice(objCP.INDX_DIEMDAU_CHUYEN + indx, 0, "");
		}
		arrOut.splice(objCP.INDX_DIEMCUOI_CHUYEN, 0, arrOut[objCP.INDX_DIEMDAU_CHUYEN]);
	}
	return arrOut;
}
function chuyenPha(_domo_ctc) {
	var Co_Chuyen = false;
	var bat_dau_chuyen = -1;
	for (var _i = 23; _i >= 0; _i--)
		if (_domo_ctc[_i] != null && _domo_ctc[_i].trim() != '') {
			if (parseInt(_domo_ctc[_i]) > 3)
				bat_dau_chuyen = _i;
			else if (parseInt(_domo_ctc[_i]) == 3) {
				bat_dau_chuyen = _i;
				break;
			}
		}
	var diem_cuoi_chuyen = 0;
	if ($('#hidHospitalId').val() != 28880) {//HaNv_280324: L2PT-79631
		if (bat_dau_chuyen > -1) {
			diem_cuoi_chuyen = 8 + (parseInt(_domo_ctc[bat_dau_chuyen]) - 3);
			Co_Chuyen = true;
		} else {
			bat_dau_chuyen = 0;
		}
	} else {//HaNv_100424: L2PT-81693
		if (bat_dau_chuyen > -1) {
			diem_cuoi_chuyen = parseInt(_domo_ctc[bat_dau_chuyen]) - 3;
			moveX = diem_cuoi_chuyen;
			$('#hidMoveX').val(moveX);
			Co_Chuyen = false;
		} else {
			bat_dau_chuyen = 0;
		}
	}
	return {
		INDX_DIEMDAU_CHUYEN : bat_dau_chuyen,
		INDX_DIEMCUOI_CHUYEN : diem_cuoi_chuyen,
		OFFSET : diem_cuoi_chuyen - bat_dau_chuyen,
		CHUYEN : Co_Chuyen
	};
}