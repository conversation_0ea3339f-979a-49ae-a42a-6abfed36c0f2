/*
 * Trả kết quả chẩn đoán hình ảnh
 * 
 * VietDA	04/08/2016		tạo mới 
 * VietDA	05/09/2016		thêm phần upload ảnh và chụp ảnh cls
 */

function TraKetQua(opt){	
	this.load=doLoad;
	this.loadAnhKetQua=loadAnhKetQua;
	
	var MAPHONG;
	var tab1=0;
	var tab2=0;

	var userRights = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'RIS_MODE_CONTROL');

	function doLoad() {
		$("li[id^='tab']").on("click",function(e){
			var tabName = $(this).attr("id").substr(3);
			$("li[class='active']").removeClass("active");
			$(this).addClass("active");
			$("div[class='tab active']").removeClass("active");
			$("#div"+ tabName).addClass("active");
		});
		$("li[id=tabHinhAnh]").on("click",function(){
			if(tab1==0){
				pic.load();
			}
			tab1=tab1+1;
			setTimeout(function(){$(".mce-tinymce-inline").hide();},10); setTimeout(function(){$(".mce-tinymce-inline").hide();},50);
		});
		
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().locale = lang;
		
		$("#hdfIDMauBenhPham").val(opt._idmaubenhpham);
		$("#hdfIDKetQuaCLS").val(opt._idketquacls);

		initControl();
		bindEvent();

		// load dữ liệu của ca chụp lên form
		loadAnhKetQua();
		loadDuLieu();
		
        if(userRights=="1"){
 			$("#btnLuu").hide();
 			$("#btnLuuDong").hide();
 			$("#btnluuVaTraKQ").hide();
 			$("#btnDSMauKQ").hide();
 			$("#cboMauKetQua").hide();
 			$("#btnSuDungMauKQ").hide();
 			$("#btnUpload").hide();
 			$("#btnSend").hide();
 		}
	}
	
	function initControl() {	
		console.log("type="+type);
		if(type=="read"){
			$("#btnLuu").attr("disabled","disabled");
			$("#btnLuuDong").attr("disabled","disabled");			
			$('#btnUpload').attr("disabled","disabled");
			$('#btnSave').attr("disabled","disabled");
			$('#btnSend').attr("disabled","disabled");
			
		} else if(type=="update"){
			$("#btnLuu").removeAttr("disabled");
			$("#btnLuuDong").removeAttr("disabled");
			$('#btnUpload').removeAttr("disabled");
			$('#btnSave').removeAttr("disabled");
			$('#btnSend').removeAttr("disabled");
		}
		//nghiant 28082017
		if(opt._rolePTH != "true"){
			$("#btnLuu").attr("disabled","disabled");
			$("#btnLuuDong").attr("disabled","disabled");
			$('#btnUpload').attr("disabled","disabled");
			$('#btnSave').attr("disabled","disabled");
			$('#btnSend').attr("disabled","disabled");
		}
		//end nghiant 28082017 
		//Begin_HaNv_02072018: In phieu giai phau benh sinh thiet
		if(opt._printGPB != "true"){
			$("#btnInGiaiPhauBenh").hide();
		}else{
			$("#btnInGiaiPhauBenh").show();
		}
		//End_HaNv_02072018
	}
	
	function bindEvent() { 
		$.jMaskGlobals = {
			maskElements: 'input,td,span,div',
			dataMaskAttr: '*[data-mask]',
			dataMask: true,
			watchInterval: 300,
			watchInputs: true,
			watchDataMask: true,
			byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
			translation: {
				'0': {pattern: /\d/},
				'9': {pattern: /\d/, optional: true},
				'#': {pattern: /\d/, recursive: true},
				'A': {pattern: /[a-zA-Z0-9]/},
				'S': {pattern: /[a-zA-Z]/}
			}
		};
		
		$('#btnLuu').on('click', function (){			
			luuKetQua();
		});
		
		$('#btnLuuDong').on('click', function (){			
			luuKetQua_Dong();
		});
		
		$('#btnluuVaTraKQ').on('click', function (){			
			if(luuTraKetQua())
				EventUtil.raiseEvent("CLS02C003_LUU_DONG",{msg:''});
		});
		
		$('#btnHuy').on('click', function (){			
			EventUtil.raiseEvent("CLS02C003_HUY",{msg:''});
		});
		
		$('#btnDSMauKQ').on('click', function (){			
			var url = "manager.jsp?func=../canlamsang/CLS02C005_ChonMauKetQua&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type="+type;			
			
			EventUtil.setEvent("CLS02C005_CHON",function(e){
				loadMauKQ(e.msg);
				DlgUtil.close("dlgChonMauKetQua");
			});
			
			EventUtil.setEvent("CLS02C005_THOAT",function(e){
				DlgUtil.close("dlgChonMauKetQua");				
			});				
			
			var dlgPopup=DlgUtil.buildPopupUrl("dlgChonMauKetQua","divChonMauKetQua",url,{},"Chọn mẫu kết quả",1014,538);
			dlgPopup.open("dlgChonMauKetQua");
		});

		$('#btnSuDungMauKQ').on('click', function (){			
			var param = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#cboMauKetQua").val();
			
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C005.CTMAU", param);
			
			if(data_ar.length>0){
				var row = data_ar[0];
				tinyMCE.get('txtKetQua').setContent(row["GIATRI"]);
				$("#txtKetLuan").val(row["KETQUA"]);
				$("#txtGhiChu").val("");				
			}
		});
		
		//Begin_HaNv_02072018: In phieu giai phau benh sinh thiet
		$('#btnInGiaiPhauBenh').on('click', function (){			
			if($("#hdfIDMauBenhPham").val()==""){
				DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
			}
			else {
				var par = [{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}];
				openReport('window', 'NTU043_PHIEUXETNGHIEMGIAIPHAUBENHSINHTHIET_36BV01_QD4069_A4', 'pdf', par);
			}
		});
		//End_HaNv_02072018
	}
	
	// load dữ liệu lên form
	function loadDuLieu(){
		// load tên bệnh nhân lên tiêu đề
		var paramTTBN = [$("#hdfIDMauBenhPham").val()];
		var dataTTBN = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", paramTTBN.join('$'));
		if(dataTTBN.length>0){
			var row = dataTTBN[0];
			document.getElementById("tabTTBN").innerHTML = "Mã bệnh nhân: <span style='color:red;font-size:18px;'>" + row.MABENHNHAN + 
				"</span> - Tên bệnh nhân: <span style='color:blue;font-size:18px;'>" + row.TENBENHNHAN +  
				"</span> - Tuổi: <span style='color:green;font-size:18px;'>" + row.TUOI + " " + row.DVTUOI +
				"</span>";
		}

		// load mẫu kết quả soạn sẵn
		var paramMKQ = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+$("#hdfIDKetQuaCLS").val();
		ComboUtil.getComboTag("cboMauKetQua", "CLS02C003.DSMKQ", paramMKQ, "", "", "sp");
		
		// load danh sách máy cận lâm sàng
		var paramDSMay = $("#hdfIDKetQuaCLS").val();
		var dataDSMay = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS.GETDSMAY", paramDSMay);
		var dataBody = "";
		for(t = 0;t<dataDSMay.length;t++){
			if(dataDSMay[t].IS_DEFAULT=="1"){
				dataBody = '<option value="'+dataDSMay[t].NAME+'" selected>'+dataDSMay[t].TENMAY+" - "+dataDSMay[t].NAME+'</option>';
			} else {
				dataBody = '<option value="'+dataDSMay[t].NAME+'">'+dataDSMay[t].TENMAY+" - "+dataDSMay[t].NAME+'</option>';
			}
			$('#cboMayCLS').append(dataBody);
		}
		dataBody = '<option value="">--Chọn máy--</option>';
		$('#cboMayCLS').prepend(dataBody);

		var param = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+
					$("#hdfIDKetQuaCLS").val();
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C003.TTKQ", param);
		
		if(data_ar.length>0){
			var row = data_ar[0];
			$("#txtTenDichVu").text(row["TENDICHVU"]);
			$("#txtKetQua").val(row["KETQUA"]);
			$("#txtKetLuan").val(row["KETLUAN"]);
			$("#txtGhiChu").val(row["GHICHU"]);
			if(row["MAMAY"].trim()!="") $("#cboMayCLS").val(row["MAMAY"]);
			$("#txtTGThucHien").val(row["NGAYKETQUA"]);

			var chinhSuaNSTH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_CHOPHEPCHINHSUA_NHANSUTHUCHIEN');
			if(chinhSuaNSTH=="1"){
				$("#NhanSuThucHien").show();

				ComboUtil.getComboTag("cboBSThucHien","FCLS02C003_DSBS",khoaId,"",{text:"--- Chọn ---",value:""},"sp",null,function(){
					if(row["IDBACSITHUCHIEN"]==""||row["IDBACSITHUCHIEN"]==null)
						$('#cboBSThucHien').val(user_id);
					else
						$('#cboBSThucHien').val(row["IDBACSITHUCHIEN"]);
				});

				ComboUtil.getComboTag("cboKTVThucHien","FCLS02C003_DSKTV",khoaId,"",{text:"--- Chọn ---",value:""},"sp",null,function(){
					$("#cboKTVThucHien").val(row["IDKTVTHUCHIEN"]);
				});
			} else {
				$("#NhanSuThucHien").hide();
			}
			
			var slPhimXQuang = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_SLPHIM_XQUANG');
			var slPhim_arr = slPhimXQuang.split(",");
			if($.inArray(row["MANHOM"], slPhim_arr) > -1){
				$("#SLXQUANG").show();
				
				$("#txtSLPhim").val(row["SOLUONGPHIM"]);
				$("#cboLoaiPhim").val(row["LOAIPHIM"]);			
			}
			
			// vietda L2PT-2199
			var showPPThucHien = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_XN_NOISOI');
			var config = showPPThucHien.split(",");
			if($.inArray(row["MANHOM"], config) > -1){
				$("#PPTHUCHIEN").show();
				
				$('#txtKetQua').attr('rows', 12);
				$("#txt_TRUOCTHUCHIEN").val(row["TTBN_TRUOCSOI"]);
				$("#txt_SAUTHUCHIEN").val(row["TTBN_SAUSOI"]);
				$("#txt_THUOCSUDUNG").val(row["THUOCSUDUNG"]);
				$("#txt_PPTHUCHIEN").val(row["PHUONGPHAPTHUCHIEN"]);
			}
			// end of L2PT-2199
			
			//nghiant 12112018 
			var showGPB = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_XN_GPB');
			if(showGPB=="1"){
				$("#PPNHUOM").show();
				
				$("#txtPP_NHUOM").val(row["PP_NHUOM"]);
				$("#txtSOLUONG_GPB").val(row["SOLUONG_GPB"]);
			}

			if(opt._hospital_id=="957"){
				$("#btnluuVaTraKQ").show();
			}
		}
	}
	
	function loadAnhKetQua(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+$("#hdfIDMauBenhPham").val()+'$'+$("#hdfIDKetQuaCLS").val();
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C003.LIETKEANH2", param);
		
		var only3Picture = data_ar.length;
		if(only3Picture>3) { only3Picture = 3; }
		
		$("#pictureToPrint").html('');
		for(var i=0; i<only3Picture; i++){
			var img = "<img src='" + data_ar[i]["DUONGDANFILE"] + "' width='100%' height='auto' style='padding:5px' alt='#" +
						data_ar[i]["MOTA"] + "' />" + "<br/><i>" + data_ar[i]["MOTA"] + "</i></div>";
			$("#pictureToPrint").append(img);
		}		
	}
	
	// lưu kết quả chẩn đoán hình ảnh
	function luuKetQua(){
		if($("#txtSLPhim").val()!=""){	
			if(isNaN($("#txtSLPhim").val())) {
				DlgUtil.showMsg('Số lượng phim phải nhập kiểu số');			
				return;
			} 
			else if ($("#txtSLPhim").val()<1){
				DlgUtil.showMsg('Phải nhập số lượng lớn hơn 0');			
				return;
			}
		}
		
		var par = {
			"IDMAUBENHPHAM": $("#hdfIDMauBenhPham").val(),
			"IDKETQUACLS": $("#hdfIDKetQuaCLS").val(),
			"KETQUAHTML": tinyMCE.get('txtKetQua').getContent({format : 'html'}),
			"KETQUA": tinyMCE.get('txtKetQua').getContent({format : 'text'}),
			"KETLUAN": $("#txtKetLuan").val(),
			"GHICHU": $("#txtGhiChu").val(),
			"MAMAY": $("#cboMayCLS").val(),
			"TGTHUCHIEN": $("#txtTGThucHien").val(),
			"BSTHUCHIEN": $("#cboBSThucHien").val(),
			"KTVTHUCHIEN": $("#cboKTVThucHien").val(),
			"PPNHUOM": $("#txtPP_NHUOM").val(),
			"SOLUONGGPB": $("#txtSOLUONG_GPB").val(),
			"TRUOCTHUCHIEN": $("#txt_TRUOCTHUCHIEN").val(),
			"SAUTHUCHIEN": $("#txt_SAUTHUCHIEN").val(),
			"THUOCSUDUNG": $("#txt_THUOCSUDUNG").val(),
			"PPTHUCHIEN": $("#txt_PPTHUCHIEN").val(),
			"SOLUONGPHIM": $("#txtSLPhim").val(),
			"LOAIPHIM": $("#cboLoaiPhim").val()
		};
		
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+ JSON.stringify(par);

		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02C003.LKQ5", param);

		if(rs == '1'){
			DlgUtil.showMsg('Lưu kết quả thành công',undefined,1000);
			EventUtil.raiseEvent("CLS02C003_LUU",{msg:''});
		} 
		else {
			DlgUtil.showMsg('Có lỗi xảy ra, không thể lưu kết quả',undefined,2000);
		}
	}
	
	// lưu kết quả chẩn đoán hình ảnh
	function luuKetQua_Dong(){
		if($("#txtSLPhim").val()!=""){	
			if(isNaN($("#txtSLPhim").val())) {
				DlgUtil.showMsg('Số lượng phim phải nhập kiểu số');			
				return;
			} 
			else if ($("#txtSLPhim").val()<1){
				DlgUtil.showMsg('Phải nhập số lượng lớn hơn 0');			
				return;
			}
		}
		
		var par = {
			"IDMAUBENHPHAM": $("#hdfIDMauBenhPham").val(),
			"IDKETQUACLS": $("#hdfIDKetQuaCLS").val(),
			"KETQUAHTML": tinyMCE.get('txtKetQua').getContent({format : 'html'}),
			"KETQUA": tinyMCE.get('txtKetQua').getContent({format : 'text'}),
			"KETLUAN": $("#txtKetLuan").val(),
			"GHICHU": $("#txtGhiChu").val(),
			"MAMAY": $("#cboMayCLS").val(),
			"TGTHUCHIEN": $("#txtTGThucHien").val(),
			"BSTHUCHIEN": $("#cboBSThucHien").val(),
			"KTVTHUCHIEN": $("#cboKTVThucHien").val(),
			"PPNHUOM": $("#txtPP_NHUOM").val(),
			"SOLUONGGPB": $("#txtSOLUONG_GPB").val(),
			"TRUOCTHUCHIEN": $("#txt_TRUOCTHUCHIEN").val(),
			"SAUTHUCHIEN": $("#txt_SAUTHUCHIEN").val(),
			"THUOCSUDUNG": $("#txt_THUOCSUDUNG").val(),
			"PPTHUCHIEN": $("#txt_PPTHUCHIEN").val(),
			"SOLUONGPHIM": $("#txtSLPhim").val(),
			"LOAIPHIM": $("#cboLoaiPhim").val()
		};
		
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+ JSON.stringify(par);

		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02C003.LKQ5", param);

		if(rs == '1'){
			EventUtil.raiseEvent("CLS02C003_LUU_DONG",{msg:''});
		} else {
			DlgUtil.showMsg('Có lỗi xảy ra, không thể lưu kết quả',undefined,2000);
		}
	}
	
	function luuTraKetQua(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+
					$("#hdfIDKetQuaCLS").val()+'$'+	
					tinyMCE.get('txtKetQua').getContent({format : 'html'})+'$'+
					tinyMCE.get('txtKetQua').getContent({format : 'text'})+'$'+
					$("#txtKetLuan").val()+'$'+
					$("#txtGhiChu").val()+'$'+
					$("#cboMayCLS").val();
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.LTKQ2", param);
		
		if(rs == '1'){
			return true;
		}
		else if(rs=="2"){
			DlgUtil.showMsg("Bạn vừa Trả kết quả sau thời điểm Kết thúc bệnh án. Bạn cần điều chỉnh lại thời gian Trả kết quả phiếu "+sophieuBN+"!");
		}
		else if(rs=="3"){
			DlgUtil.showMsg("Bạn chưa chọn Kĩ thuật viên thực hiện!",undefined,1500);
		}
		else if(rs=="5"){
			DlgUtil.showMsg("Chưa nhập số lượng phim và loại phim xquang!",undefined,1500);
		}
		else if(rs=="6"){
			var b_min_time = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_MIN_EXEC_TIME');
			DlgUtil.showMsg("Khoảng thời gian thực hiện CĐHA dưới "+b_min_time+" phút, vui lòng kiểm tra lại!",undefined,4000);
		}
		else if(rs == "9"){
			DlgUtil.showMsg("Hệ thống từ chối Trả kết quả sau thời điểm Kết thúc bệnh án! Liên hệ quản trị viên để được hỗ trợ");
		}
		else {
			DlgUtil.showMsg('Có lỗi xảy ra, không thể lưu kết quả',undefined,1500);
		}
		return false;
	}
	
	// load mẫu kết quả
	function loadMauKQ(idmaukq){ 
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+idmaukq;
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C005.CTMAU", param);
		
		if(data_ar.length>0){
			var row = data_ar[0];
			tinyMCE.get('txtKetQua').setContent(row["GIATRI"]);
			$("#txtKetLuan").val(row["KETQUA"]);
		}
	}
}

/* Upload ảnh từ PC lên hệ thống HIS */
function HinhAnhCLS(){
	this.load=doLoad;
	this.luuAnh=luuAnh;
	this.xoaAnh=xoaAnh;
	this.lietKeAnh=lietKeAnh;
	this.doiTenFile=doiTenFile;
	
	function doLoad(){
		initControl();
		bindEvent();
	}
	
	function initControl(){
		lietKeAnh();
	}
	
	function bindEvent(){
		// upload ảnh lên server, sau khi upload thành công thì update vào database
		$('#btnUpload').on("click", function (){
			var filename = $("#fileUpload").val();
			
	        // Use a regular expression to trim everything before final dot
	        var extension = filename.replace(/^.*\./, '');

	        // Iff there is no dot anywhere in filename, we would have extension == filename,
	        // so we account for this possibility now
	        if (extension == filename) {
	            extension = '';
	        } else {
	            // if there is an extension, we convert to lower case
	            // (N.B. this conversion will not effect the value of the extension
	            // on the file upload.)
	            extension = extension.toLowerCase();
	        }

	        switch (extension) {
	            case 'jpg':
	            case 'jpeg':
	            case 'png':
	            case 'bmp':
	    			UploadUtil.upload("formUpload","", luuAnh);
	    			break;
	            default:
	                alert("Chỉ cho phép upload ảnh định dạng jpeg, png, bmp");
	                submitEvent.preventDefault();
	        }
	    });
	}
	
	// lưu lại đường dẫn ảnh đã upload ứng với kết quả đang nhập
	function luuAnh(data){
		var myData = data.length;
		if(typeof(myData) != "undefined"){
			for(var i=0; i<data.length; i++){
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							''+'$'+	// id benh nhan
							''+'$'+ // ten benh nhan
							$("#hdfIDMauBenhPham").val()+'$'+
							$("#hdfIDKetQuaCLS").val()+'$'+
							data[i].id+'$'+data[i].url+'$'+data[i].name//document.getElementById('fileUpload').files[0].name.replace(/\.[^/.]+$/, "")
						;
				
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.LUUANH2", param);
				
				if(rs==1){
					$("#fileUpload").val('');
					lietKeAnh();
					tt.loadAnhKetQua();
				} else {
					DlgUtil.showMsg("Nhập ảnh thất bại: "+rs.error_msg,undefined,3000);
				}
			}
		} else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						''+'$'+	// id benh nhan
						''+'$'+ // ten benh nhan
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDKetQuaCLS").val()+'$'+
						data.id+'$'+data.url+'$'+document.getElementById('fileUpload').files[0].name.replace(/\.[^/.]+$/, "")
					;
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.LUUANH2", param);
			
			if(rs==1){
				$("#fileUpload").val('');
				lietKeAnh();
				tt.loadAnhKetQua();
			} else {
				DlgUtil.showMsg("Nhập ảnh thất bại: "+rs.error_msg,undefined,3000);
			}
		}
	}
	
	// xóa ảnh đã upload
	function xoaAnh(idfile){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+
					$("#hdfIDKetQuaCLS").val()+'$'+
					idfile;
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.XOAANH2", param);
		
		if(rs == 1){
			$("#fileUpload").val('');
			var rt = UploadUtil.deleteMedia(idfile,'');
			if(rt == idfile){
				DlgUtil.showMsg("Kết quả xóa file: "+rt);
			}
			lietKeAnh();
			tt.loadAnhKetQua();
		} else {
			DlgUtil.showMsg("Xóa ảnh "+idfile+" thất bại ",undefined,3000);
		}
	}
	
	// liệt kê ảnh đã lưu trong cơ sở dữ liệu
	function lietKeAnh(){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+
					$("#hdfIDKetQuaCLS").val();
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C003.LIETKEANH2", param);
		
		$("#list").html('');
		if(type=="read"){
			for(var i=0; i<data_ar.length; i++){
				var img = "<div class='col-md-3' style='text-align:center;'>"+
								"<img src='"+data_ar[i]["DUONGDANFILE"]+"' width='200px' alt='"+data_ar[i]["MOTA"]+"' />"+																
								"</br><i>"+data_ar[i]["MOTA"]+"</i>"+
						  "</div>";
				
				$("#list").append(img);
			}
		} else if(type=="update"){
			for(var i=0; i<data_ar.length; i++){
				var img = "<div class='col-md-3' style='text-align:center;'>"+
								"<img src='"+data_ar[i]["DUONGDANFILE"]+"' width='200px' alt='"+data_ar[i]["MOTA"]+"' />"+		
								"<input type='image' src='../common/icon/delete.png' width='18px' onclick='pic.xoaAnh("+data_ar[i]["IDFILE"]+");' /></br>"+
								"<input id='file"+data_ar[i]["IDFILE"]+"' type='text' value='"+data_ar[i]["MOTA"]+"' onchange='pic.doiTenFile("+data_ar[i]["IDFILE"]+");' maxlength='25' />"+
						  "</div>";
				
				$("#list").append(img);
			}
		}
	}
	
	function doiTenFile(idfile){
		var tenMoi = $("#file"+idfile).val();
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val()+'$'+
					idfile+'$'+tenMoi
		;

		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.DOITENFILE", param);
		if(rs == 1){
			console.log("Đổi tên file "+idfile+" thành công");
			tt.loadAnhKetQua();
		} else {
			console.log("Đổi tên file "+idfile+" thất bại: "+ rs);
		}
	}
}

/* Chụp ảnh từ thiết bị chẩn đoán được kết nối */
function ChupAnhCLS(){
	this.load = doLoad;
	this.luuAnhChup=luuAnhChup;

	var _checkchon = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_CHECKCHON_ANHCHUP');
	
	var imgIndex = 0;
	
	function doLoad(){	    
		initControl();
		bindEvent();
		
		var videocont = document.getElementById("videocont");
		videocont.addEventListener('contextmenu', function(ev) {
		    ev.preventDefault();
		    imgIndex++;
            if (imgIndex > 8) { imgIndex = 1; }

            var imgTag = document.getElementById('imgTag' + imgIndex);

            Webcam.snap( function(uri) {
            	imgTag.src = uri;
            });
            
            var cb = document.getElementById("cb" + imgIndex);
            if(_checkchon == "1"){
            	cb.checked = false;
            } else {
            	cb.checked = true;
            }
            cb.disabled = false;
            
            var tb = document.getElementById("mota" + imgIndex);            
            tb.readOnly = false;
            tb.value = "Ảnh "+ imgIndex;
		    return false;
		}, false);
		
	}
    
	function initControl(){
		try {
			Webcam.set({
				width: 400,
				height: 300,
				image_format: 'png',
				jpeg_quality: 90
			});
			Webcam.attach('#videocont');  
		}
		catch(err){
			console.log(err);
			setTimeout(function(){$(".mce-tinymce-inline").hide();},10); setTimeout(function(){$(".mce-tinymce-inline").hide();},50);
		}
	}

    function bindEvent(){	
    	$('#btnSave').on('click', function (e) {
            imgIndex++;
            if (imgIndex > 8) { imgIndex = 1; }

            var imgTag = document.getElementById('imgTag' + imgIndex);

            Webcam.snap( function(uri) {
            	imgTag.src = uri;
            });
            
            var cb = document.getElementById("cb" + imgIndex);
            if(_checkchon == "1"){
            	cb.checked = false;
            } else {
            	cb.checked = true;
            }
            cb.disabled = false;
            
            var tb = document.getElementById("mota" + imgIndex);            
            tb.readOnly = false;
            tb.value = "Ảnh "+ imgIndex;
        });	

    	$('#btnSend').on('click', function (){
    		var count = 0;
            for (var i = 1; i <= 8; i++) {
                var cb = document.getElementById("cb" + i);
                if (cb.checked == true) {                	
                    var kq = SendImageData(i);
                    if(kq == 1) count++;
                }
            }
            if (count > 0) {
            	DlgUtil.showMsg("Lưu ảnh thành công!",undefined,1000);
            } else {
            	DlgUtil.showMsg("Bạn chưa chọn ảnh để lưu!",undefined,1500);
            }
    	});
    }
    
    function SendImageData(index) {
        var imgTag = document.getElementById("imgTag" + index);
        var base64Img = $(imgTag).attr('src');
        var motaTag = document.getElementById("mota" + index);
        var motaText = motaTag.value;
        var rs = UploadUtil.uploadBase64(base64Img,'png');
        
        var kq = luuAnhChup(rs,motaText);
        return kq;
    }

    function luuAnhChup(data,mota){
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					''+'$'+	// id benh nhan
					''+'$'+ // ten benh nhan
					$("#hdfIDMauBenhPham").val()+'$'+
					$("#hdfIDKetQuaCLS").val()+'$'+
					data.id+'$'+
					data.url+'$'+mota;
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C003.LUUANH2", param);
		
		if(rs == 1){
			$("#fileUpload").val('');
			pic.lietKeAnh();
			tt.loadAnhKetQua();
			return 1;
		} else {
			console.log("Lưu ảnh thất bại, mã lỗi: " + rs);
			return 0;
		}
	}
}
