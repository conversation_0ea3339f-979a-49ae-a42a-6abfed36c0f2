<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT006_DONTHUOC_17DBV01_TT052016_A5_915_subreport1" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="406" leftMargin="0" rightMargin="15" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="7aa9e6e8-b14b-4277-9a60-b444a656ea92">
	<property name="ireport.zoom" value="1.6500000000000066"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="173"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call REPORT_PHIEU_DONTHUOC915($P{[UID]},$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="NAMSINH" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA_KEMTHEO" class="java.lang.String"/>
	<field name="ROWNUM" class="java.math.BigDecimal"/>
	<field name="DICHVUID" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.lang.Number"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="GIATRI_KETQUA" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="SOPHIEUTHUOC" class="java.lang.String"/>
	<field name="ORG_ADDRESS" class="java.lang.String"/>
	<field name="CHECKVP" class="java.lang.String"/>
	<field name="NDOHLUONG" class="java.lang.String"/>
	<field name="YKIENBACSY" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="DUNGTUYEN" class="java.lang.String"/>
	<field name="ICD10" class="java.lang.String"/>
	<field name="CHANDOANRAVIENKT" class="java.lang.String"/>
	<field name="PHIEUHEN" class="java.lang.Number"/>
	<field name="NGAYHEN" class="java.lang.String"/>
	<field name="NGUOITAO" class="java.lang.String"/>
	<field name="TENDICHVU_NT" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="HUONGDANCHUNGDKHN" class="java.lang.String"/>
	<field name="GHICHU_KHAMBENH" class="java.lang.String"/>
	<field name="NGAYMAUBENHPHAM" class="java.lang.String"/>
	<field name="LOAITIEPNHANID" class="java.lang.String"/>
	<field name="KHAMBENH_HUYETAP_HIGH" class="java.lang.String"/>
	<field name="KHAMBENH_HUYETAP_LOW" class="java.lang.String"/>
	<field name="KHAMBENH_NHIPTHO" class="java.lang.String"/>
	<field name="khambenh_cannang" class="java.lang.String"/>
	<field name="khambenh_nhietdo" class="java.lang.String"/>
	<field name="khambenh_mach" class="java.lang.String"/>
	<field name="KHAMBENH_TOANTHAN" class="java.lang.String"/>
	<field name="khambenh_bophan" class="java.lang.String"/>
	<field name="khambenh_chieucao" class="java.lang.String"/>
	<field name="DONTHUOC_CLS" class="java.lang.String"/>
	<field name="TENNGUOITHAN" class="java.lang.String"/>
	<field name="CANNANG" class="java.lang.String"/>
	<variable name="TOTAL_PAGES" class="java.lang.Integer" resetType="None" calculation="Highest">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="87" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="16" width="183" height="16" uuid="e0d1ce53-fe5e-406f-bd0b-daa25cdb02a8"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="2" y="-1" width="184" height="17" uuid="b9e1d37b-69cf-46f9-90eb-f77c027d2afd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="324" y="57" width="93" height="16" uuid="d6cd14ea-fdb4-4a42-a773-8488e7dc5ced"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOPHIEUTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="37" y="59" width="96" height="14" uuid="fd950365-907a-4818-8d0a-50cf43016e21"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="133" y="33" width="142" height="17" uuid="2750d24f-3ab8-4b26-b9ea-0b5e47a2b99a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐƠN THUỐC]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="2" y="59" width="35" height="14" uuid="23b22148-cd22-4274-af4e-1fcd76736c3c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Số BA: ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="282" y="57" width="42" height="16" uuid="58c97986-d8c5-4a5a-bfc3-1f61657a9c68"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Số phiếu:]]></text>
			</staticText>
			<componentElement>
				<reportElement x="2" y="37" width="131" height="22" uuid="d7bb0313-7903-449a-a0ee-05282a484eed"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<componentElement>
				<reportElement x="277" y="35" width="129" height="22" uuid="943b107a-f3df-4553-9125-b8ba206b84a8"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$F{SOPHIEUTHUOC}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="37" y="73" width="96" height="14" uuid="8c5f3bc0-6851-4f28-a807-afff1708a2d9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="2" y="73" width="35" height="14" uuid="54c9440a-0d85-463b-bb4e-bcea3fd9e367"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Mã BN: ]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="7" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="143" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="1" y="128" width="20" height="15" uuid="24c68e82-ad87-47cc-948f-3391bfb67932"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="90" width="406" height="14" uuid="85bead5d-889c-4a98-96c4-4da066b3a3c3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán bệnh: " + $F{CHANDOANVAOKHOA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="69" y="75" width="167" height="15" uuid="f0cd8716-f9e8-4f5a-80bb-602fd4aa8059"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="75" width="69" height="15" uuid="8b0e08e4-0c43-4b29-92b4-43a2e96aaa79"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Số thẻ BHYT:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="15" width="406" height="15" uuid="3f876d3e-0cfb-4bc5-9aea-f875c118af3c"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: " + $F{DIACHI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="104" width="406" height="20" uuid="b337af5c-1428-4adf-a947-bc5e7139a320"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Bệnh kèm theo: " + ($F{CHANDOANRAVIENKT}==null?" ":$F{CHANDOANRAVIENKT})
+ ($F{GHICHU_KHAMBENH}==null?"": (($F{CHANDOANRAVIENKT}==null?" " : " - ") + $F{GHICHU_KHAMBENH}))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="21" y="128" width="280" height="15" uuid="d07129cd-3d9c-4d4b-b57f-2605b7ac8faa"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên thuốc/hàm lượng]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="352" y="128" width="54" height="15" uuid="8cac30cb-7b8e-4a6b-b62e-7efb1d5a80f1"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="301" y="128" width="51" height="15" uuid="49e71eda-9e80-4397-9abb-f84a5cfecf2f"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐVT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="236" y="75" width="61" height="15" uuid="74e508ac-3a87-491e-bda5-56a807fb89ec"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Nơi ĐKKCB:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="296" y="75" width="110" height="15" uuid="02a34a97-8799-41b8-b0dd-3315ac6e2db5"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAKCBBD}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="30" width="133" height="15" isRemoveLineWhenBlank="true" uuid="13f64a6d-2ca4-4447-b129-c534dc656f57">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Mạch: " + ($F{khambenh_mach}==null?" ":$F{khambenh_mach}+" lần/p")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="133" y="30" width="142" height="15" isRemoveLineWhenBlank="true" uuid="d73174ec-5c7a-4967-865f-98f8eff7fcec">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhiệt độ: " + ($F{khambenh_nhietdo}==null?" ":$F{khambenh_nhietdo}+" C")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="275" y="30" width="131" height="15" isRemoveLineWhenBlank="true" uuid="f63c74da-d9ac-4a4a-9ae7-271e47530652">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Cân nặng: " + ($F{khambenh_cannang}==null?" ":$F{khambenh_cannang}+" kg")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="45" width="133" height="15" isRemoveLineWhenBlank="true" uuid="dba11f9c-9dd6-4bc7-ae8b-3833445688bf">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Huyết áp: " + ($F{KHAMBENH_HUYETAP_HIGH}==null?" ":$F{KHAMBENH_HUYETAP_HIGH}+"/")
+($F{KHAMBENH_HUYETAP_LOW}==null?" ":$F{KHAMBENH_HUYETAP_LOW}+" mmHg")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="133" y="45" width="142" height="15" isRemoveLineWhenBlank="true" uuid="729584a6-3cd6-48fb-83e7-f9f4b90d1b91">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhịp thở: " + ($F{KHAMBENH_NHIPTHO}==null?" ":$F{KHAMBENH_NHIPTHO}+" lần/p")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="60" width="406" height="15" isRemoveLineWhenBlank="true" uuid="d826dc98-e11c-437c-a7e0-5438af1d0d7f">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Diễn biến bệnh: " + ($F{KHAMBENH_TOANTHAN}==null?" ":$F{KHAMBENH_TOANTHAN}+"; ")
+($F{khambenh_bophan}==null?" ":$F{khambenh_bophan})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="275" y="45" width="130" height="15" isRemoveLineWhenBlank="true" uuid="b33e2977-faa3-4e02-90f7-56e8e0124274">
					<printWhenExpression><![CDATA[$F{LOAITIEPNHANID}.equals("1")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Chiều cao: " + ($F{khambenh_chieucao}==null?" ":$F{khambenh_chieucao}+" cm")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="236" height="15" uuid="19deeede-2366-40f3-a937-fe305d181995"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên:  " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="318" y="0" width="88" height="15" uuid="f511e32b-f960-4f25-8ea7-d4956f5c6cb0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính: " + $F{GIOITINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="236" y="0" width="82" height="15" uuid="f7758810-4c4a-4d9d-8e09-11881fe4d9ba"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + $F{TUOI} + " " + $F{DVTUOI}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="1" y="0" width="20" height="15" uuid="3f558b53-d4ba-48d4-8b41-4aee6c43994b"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ROWNUM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="352" y="0" width="54" height="15" uuid="b3c3fda5-0426-47b8-96a7-7f4edff4636a"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG} < 10 ? "0" + $F{SOLUONG} : $F{SOLUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="21" y="0" width="280" height="15" uuid="bf273ea9-b506-44d6-9e28-9ee965c9087d"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENDICHVU_NT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="301" y="0" width="51" height="15" uuid="55e3d809-8bf4-492c-843c-9ddeb7e95766"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DVT}]]></textFieldExpression>
			</textField>
		</band>
		<band height="14">
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="1" y="0" width="405" height="14" uuid="9a75d410-3a7f-49c5-89df-ea45f88e5415"/>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.2"/>
					<leftPen lineWidth="0.2"/>
					<bottomPen lineWidth="0.2"/>
					<rightPen lineWidth="0.2"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDANCHUNGDKHN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="15">
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="15" y="0" width="195" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1f1a114c-c316-4c24-966b-60c2d1cb411d"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}+"/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement positionType="Float" mode="Transparent" x="210" y="0" width="196" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="30b9a7ae-9394-40e0-a5b6-670dd627cc03"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="140">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="1" y="19" width="405" height="14" isRemoveLineWhenBlank="true" uuid="30f1cf2f-5cb1-4cc8-acbb-0766b6d5dd6e">
					<printWhenExpression><![CDATA[$F{DONTHUOC_CLS}!=null]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.2"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONTHUOC_CLS}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="1" y="3" width="405" height="16" isRemoveLineWhenBlank="true" uuid="d81fc30b-f3f1-4cd5-951a-4454dc1a81a1">
					<printWhenExpression><![CDATA[$F{DONTHUOC_CLS}!=null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾT QUẢ CẬN LÂM SÀNG]]></text>
			</staticText>
			<frame>
				<reportElement positionType="Float" x="0" y="34" width="406" height="105" uuid="ea94466a-bd69-400c-909b-6ef3d24add35"/>
				<textField isBlankWhenNull="true">
					<reportElement positionType="Float" x="250" y="90" width="156" height="15" uuid="1afa2887-6043-407f-b5bc-455542d8a53e"/>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Times New Roman" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITAO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="90" width="39" height="15" uuid="1fe3666e-27e6-4abf-94ed-51e9f9743b02"/>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[Lời dặn:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="250" y="16" width="155" height="15" uuid="f9b88a19-7bf7-479e-99dc-8d2b8baa92ed"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYMAUBENHPHAM}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="250" y="30" width="155" height="15" uuid="b1ca6e81-aec0-4447-94f6-1a96a7ebf1b4"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[Bác sĩ]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="40" y="90" width="210" height="15" uuid="b8afc82e-30e9-4414-9e13-cfb5a0e89a43"/>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{YKIENBACSY}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="2" y="1" width="248" height="64" uuid="68edf9f1-3818-415f-bbf6-16fb3feacb5f"/>
					<textElement verticalAlignment="Middle" markup="html">
						<font fontName="Times New Roman" size="10" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA["- Đọc kỹ hướng dẫn sử dụng trước khi dùng.<br>"+
"- Khi dùng thuốc, nếu có bất thường gì cần hỏi lại Bác sỹ.<br>"+
"- Bệnh nhân đi khám lần sau xin mang theo đơn này!<br>"+
"- Tên bố hoặc mẹ của trẻ hoặc người đưa trẻ đến khám bệnh, chữa bệnh: "+($F{TENNGUOITHAN}==null?"":$F{TENNGUOITHAN})]]></textFieldExpression>
				</textField>
			</frame>
			<staticText>
				<reportElement positionType="Float" x="2" y="100" width="248" height="24" uuid="97f3f5aa-7dd9-481f-ac51-6030d2585614"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Chú ý: Sau 5 ngày kể từ ngày in đơn thuốc, nếu bệnh nhân
không lấy thuốc thì đơn thuốc này không có giá trị sử dụng]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
