<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT02K028_DanhSachKhamSmartAds.js?v=2109083"></script>
<div class="overlay">
<div id="divMain" style="min-height: 200px !important;"> 
		<div class=" form-inline">
			<div class="col-xs-3 low-padding required"
				style="margin-left: 20px;" align="left">
				<label style="margin-top: 5px;margin-right:5px;">Chọn bác sĩ</label>

			</div>
			<div class="col-xs-3 low-padding" style="margin-top: 2px;">
				<input class="form-control input-sm" id="txtBS_NAME_SEARCH"
					name="txtBS_NAME_SEARCH" title="">
			</div>
			<div class="col-xs-5 low-padding"
				style="padding-top: 2px !important; padding-bottom: 2px !important;">
				<select class="form-control input-sm isnotnull" id="cboBSYID"
					filterLike="txtBS_NAME_SEARCH"></select>
			</div>

		</div>
		<div class=" form-inline">
			<div class="col-xs-3 low-padding required"
				style="margin-left: 20px;" align="left">
				<label style="margin-top: 5px;margin-right:5px;">Chọn điều dưỡng</label>

			</div>
			<div class="col-xs-3 low-padding" style="margin-top: 2px;">
				<input class="form-control input-sm" id="txtDD_NAME_SEARCH"
					name="txtDD_NAME_SEARCH" title="">
			</div>
			<div class="col-xs-5 low-padding"
				style="padding-top: 2px !important; padding-bottom: 2px !important;">
				<select class="form-control input-sm isnotnull" id="cboDIEUDUONGID"
					filterLike="txtDD_NAME_SEARCH"></select>
			</div>

		</div>
		
		
		<div class=" form-inline">
	     	<div class="col-xs-6 low-padding" style="text-align: right;">
	     		<button id="btnSend" class="btn btn-sm btn-primary">
	     			<span class="glyphicon glyphicon-remove-circle"></span>Gọi SmartAds</button>
	     	</div> 
	     	<div class="col-xs-5 low-padding" style="text-align: left;margin-left: 2px;">
	     		<button id="btnClose" class="btn btn-sm btn-primary">
	     			<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
	     	</div>     	
     	</div>
	</div>
</div>
	
<script type="text/javascript">
	var uuid = '{uuid}';
	initRest(uuid,"/vnpthis");
	var mode = '{showMode}';	 
	var data;
	var ngaybd = '{ngaybd}';
	var ngaykt = '{ngaykt}';
	var phongid = '{subdept_id}';
	var subdept_name = '{subdept_name}';
	var fname = '{fname}';
	var hid = '{hospital_id}';
	var _opts = new Object();
	_opts.ngaybd = ngaybd;
	_opts.ngaykt = ngaykt;
	_opts.phongid = phongid;
	_opts.subdept_name = subdept_name;
	_opts.fname = fname;
	_opts.hid = hid;

	var tt = new ThongBaoBenhNhan(_opts);
	tt.load();
</script>