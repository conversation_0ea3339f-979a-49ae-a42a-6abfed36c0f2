function NGT02K046_THEM_CHUYENPHONGKHAM(opt) {
    this.load = doLoad;
    var _opt = opt;
    var _sql = ["NGTPK.CP", "NGTPK.TP"];
    var _luu = 0;
    var _badaingay = "0";
    var _tinhthukhac = '0';
    var dtbnid = opt.doituongbenhnhanid;
    var maubenhphamidd = null;
    var _subdtbnid = opt.sub_dtbnid;
    var _dtkhaibao = null;
    var _bhytdv = null;
    var _doivitrickpk = "0"; 				// giu nguyen vi tri;
    var LOAD_YEUCAUKHAM_THEO_DT = "0";
    var checkSLKham = 0;
    var ht_cbo_loaidv = 0;
    var HIS_KHOA_YC = 0;
    var causer = '';
    var capassword = '';
    var isKyCa = false;
    var LOAI_CHUYEN_PHONGKHAM = "";
    var _ktinhcongkham ;
    var cfObj = new Object();
    var qrCode = '';
    function doLoad() {
        var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NGT_TINHTHUKHAC_PKGIAOSU;HIS_SUDUNG_DOITUONG_KHAIBAO;NGT_AN_CHECKBOX_BHYT_DV;KBH_CHUYENPK_KCHECKTIEN;" +
            "HIS_SUDUNG_KYSO_KYDIENTU;NGT_AN_CHECKBOX_BHYT_DV;LOAD_YEUCAUKHAM_THEO_DT;NGT_INPHIEUKHAM_CHUYENKHOA;NGT_INPHIEUKHAM_CHUYENPHONG;LOAI_CHUYEN_PHONGKHAM;NGT_DOIVITRI_CKPK;NGT_HIENTHI_DV_THEM_CHUYEN_PK;" +
            "NGT_KHONGTINHCONGKHAM_SHOW;VPI_ID_DVTT;NGT_CHUYENPK_QRCODEBIDV;HIS_CHECKMALOAIKCB_7;NGT_CHUYENPHONG_SUBDTBN;NGT_CHECK65BHYT_CHUYENPK;NGT_LOADPKCS;NGT_TUDONGIN_CHUYENPK;CHECKTN65BHYT;CHUYENPHONG_KETTHUCKHAM;HIS_CANHBAO_KHONG_TTDT;HIS_KHOA_YC");
        if (config_ar != null && config_ar.length > 0) {
            cfObj = config_ar[0];
        } else {
            DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
            return;
        }
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        $('#hidKHAMBENHID').val(_opt.khambenhid);
        $('#hidTIEPNHANID').val(_opt.tiepnhanid);

        _tinhthukhac = cfObj.NGT_TINHTHUKHAC_PKGIAOSU;
        _dtkhaibao = cfObj.HIS_SUDUNG_DOITUONG_KHAIBAO;
        _bhytdv = cfObj.NGT_AN_CHECKBOX_BHYT_DV;
        _ktinhcongkham = cfObj.KBH_CHUYENPK_KCHECKTIEN;
        _doivitrickpk = cfObj.NGT_DOIVITRI_CKPK;
        ht_cbo_loaidv = cfObj.NGT_HIENTHI_DV_THEM_CHUYEN_PK;
        HIS_KHOA_YC = cfObj.HIS_KHOA_YC;

        if (ht_cbo_loaidv == 1 ) {
            $("#dvLOAIDICHVU").show();
            $("#dvKHONGTINHCONGKHAM").show();
            if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'NGT_CHUYENPHONG_MACDINHKHONGTINHCK') != 0) {
                $('#chkKHONGTINHCONGKHAM').attr('checked', true);
                if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'NGT_CHUYENPHONG_MACDINHKHONGTINHCK') == 2) {
                    $('#chkKHONGTINHCONGKHAM').prop('disabled', true);
                }  
            }
        }

        // BDHCM: THUC TE LA DOI PHONG KHAM GIU NGUYEN MOI DICH VU TRONG DO;
        if (_tinhthukhac == '1') {
            $("#dvTHUKHAC").show();
            $("#dvBOCONGKHAM").show();

            var _sql_par = [{"name": "[0]", "value": 1}, {"name": "[1]", "value": -1}, {"name": "[2]", "value": -1}];
            ComboUtil.getComboTag("cboTHUKHACID", "DMC01.GDV",
                _sql_par, "", {value: -1, text: '-- Chọn thu khác --'}, "sql", "", false);
        } else {
            $("#dvTHUKHAC").hide();
            $("#dvBOCONGKHAM").hide();
        }
        LOAI_CHUYEN_PHONGKHAM =cfObj.LOAI_CHUYEN_PHONGKHAM;
        if (cfObj.NGT_INPHIEUKHAM_CHUYENPHONG != 1) {
            $("#btnInPhieuKham").hide();
        }

        //L2PT-27977
        if (cfObj.NGT_INPHIEUKHAM_CHUYENKHOA != 1) {
            $("#btnInPhieuKhamCK").hide();
        }

        sql_par = [];
        LOAD_YEUCAUKHAM_THEO_DT = cfObj.LOAD_YEUCAUKHAM_THEO_DT;
        $("#cboLOAIDICHVU").val(LOAD_YEUCAUKHAM_THEO_DT);
        if (_doivitrickpk == "0") {
            $("#dvDOIVITRI1").remove();

            if (LOAD_YEUCAUKHAM_THEO_DT != 0) {
                sql_par.push({"name": "[0]", "value": opt.doituongbenhnhanid});
            } else {
                sql_par.push({"name": "[0]", "value": "0"});
            }

            ComboUtil.getComboTag("cboDICHVUID", "NGTDV.002", sql_par, _opt.dichvuid, {
                value: '',
                text: 'Chọn yêu cầu khám'
            }, 'sql', '', function () {
                if ($('#cboDICHVUID').val() != 0) {
                    _loadPhongKham($('#cboDICHVUID').val());
                }
            });
        } else {
            $("#dvDOIVITRI").remove();

            var sql_par1 = [];
            sql_par1.push({"name": "[0]", "value": 2});				// 2: phong kham benh;
            ComboUtil.getComboTag("cboPHONGID", "NGTPK.DV.02", sql_par1, _opt.phongcuid, {extval: true}, "sql", "", function () {
                if ($("#cboPHONGID").val() != 0) {
                    _loadYCKham($("#cboPHONGID").val());
                }
            });
        }
        // if(_opt.i_hid == "902"){
        if (opt.doituongbenhnhanid == "2") {
            $("#dvUUTIEN").hide();
        } else {
            if (cfObj.NGT_AN_CHECKBOX_BHYT_DV != "1") {
                $("#dvUUTIEN").show();
            } else {
                $("#dvUUTIEN").hide();
            }
        }
        // }
        if (cfObj.NGT_KHONGTINHCONGKHAM_SHOW == 1){
            $("#dvKHONGTINHCONGKHAM").show();
            $("#dvBOCONGKHAM").hide();
        }

    }

    function _bindEvent() {
        $('document').ready(function () {
            if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1") {
                $("#btnKyCa").show();
                $("#btnHuyCa").show();
                $("#btnInKySo").show();
                $("#btnLuuKyCa").show();
                var _rptCode = '';
                var _par = [{
                    name: 'hosobenhanid',
                    type: 'String',
                    value: _opt.hosobenhanid
                }, {
                    name: 'i_khambenhid',
                    type: 'String',
                    value: _opt.khambenhid
                }, {
                    name: 'i_phongids',
                    type: 'String',
                    value: _opt.phongcuid
                }, {
                    name: 'i_pkdkids',
                    type: 'String',
                    value: _opts.phongkhamdangkyid
                }, {
                    name: 'RPT_CODE',
                    type: 'String',
                    value: 'RPT_PHIEUKHAMCHUYENKHOA_A4'
                }
                ];
                var _check = CommonUtil.checkKyCaByParam(_par);
                if (_check > 0) {
                    $("#btnKyCa").prop('disabled', true);
                    $("#btnLuuKyCa").prop('disabled', true);
                } else {
                    $("#btnHuyCa").prop('disabled', true);
                }
            }
        });
        $("#btnHuy").on("click", function (e) {
            //tuyennx_add_20171114_start
            EventUtil.raiseEvent("close_chuyenphongkham", {type: _luu, badaingay: _badaingay});
            //tuyennx_add_20171114_end
        });

        $("#btnInPhieuKham").on("click", function (e) {
            _inPhieuKham();
        });


        //L2PT-27977
        $("#btnInPhieuKhamCK").on("click", function (e) {
            var objData = new Object();
            FormUtil.setFormToObject("tabTiepNhan", "", objData);
            var par = [{
                name: 'i_khambenhid',
                type: 'String',
                value: _opt.khambenhid
            }, {
                name: 'i_phongids',
                type: 'String',
                value: $('#cboPHONGID').val()
            }, {
                name: 'i_pkdkids',
                type: 'String',
                value: _opts.phongkhamdangkyid
            }];

            if (_opt.khambenhid != null && _opt.khambenhid > 0) {
                CommonUtil.inPhieu('window', 'RPT_PHIEUKHAMCHUYENKHOA_A4', 'pdf', par);
            } else {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
            }
        });

        $("#btnThuKhac").on("click", function (e) {
            EventUtil.raiseEvent("chidinhthukhac", {phongchidinh: $('#cboPHONGID').val()});
        });

        $("#btnLuu").on("click", function (e) {
            var bhyt_dv = 0;
            var bocongkham = 0;
            var subdtbnid = 0;
            var khongtinhcongkham = 0;

            if ($("#cboDICHVUID").val() == "" || $("#cboDICHVUID").val() == "0" || $("#cboDICHVUID").val() == null) {
                setErrValidate('cboDICHVUID');
                DlgUtil.showMsg('Chưa chọn yêu cầu khám. ');
                return false;
            }

            if ($("#cboPHONGID").val() == "" || $("#cboPHONGID").val() == "0" || $("#cboPHONGID").val() == null) {
                setErrValidate('cboPHONGID');
                DlgUtil.showMsg('Chưa chọn phòng khám. ');
                return false;
            }

            if (_tinhthukhac == '1') {
                var _phonggiaosu = $('#cboPHONGID' + " option:selected").attr('extval0');
                if ($("#cboTHUKHACID").val() == -1 && _phonggiaosu == '1' && (dtbnid == "1" || dtbnid == "6")) {
                    DlgUtil.showMsg('Hãy chọn thông tin thu khác');
                    return;
                }
            }
            if (opt.doituongbenhnhanid == '1' && cfObj.HIS_CHECKMALOAIKCB_7 != '0') {
                var maloaikcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.GETMALOAIKCB", $("#hidTIEPNHANID").val());
                if (maloaikcb == '07') {
                    DlgUtil.showMsg("Mã loại khám chữa bệnh của bệnh nhân không được chuyển phòng khám!");
                    return false;
                }
            }
            var checkTt = cfObj.HIS_CANHBAO_KHONG_TTDT;
            //=========== check dich vu thanh toan dong thoi;
            var msgCheckTt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV018", $('#hidTIEPNHANID').val());
            if (msgCheckTt && msgCheckTt != '') {
                DlgUtil.showMsg('Các dịch vụ ' + msgCheckTt + ' miễn giảm thanh toán đồng thời');
                if (checkTt == '1') {
                    return;
                }
            }
            //===========
            //if (_bhytdv == "1"){
            //	bhyt_dv = "0";
            //}else{
            //	bhyt_dv = $("#cboDOITUONGKHAM").val() == "-1" ? "0": "1";
            //}

            if ($('#chkUUTIENKHAMID').is(":checked")) {
                bhyt_dv = 1;
            }

            bocongkham = $('#chkBOCONGKHAM').is(":checked") == true ? 1 : 0;
            kchecktien = $('#chkKHONGCHECKTIEN').is(":checked") == true ? 1 : 0;
            subdtbnid =
                // _dtkhaibao == "1" && opt.i_hid == "902" ? $('#cboDOITUONGKHAM'+ " option:selected").attr('extval0') : "0";
                khongtinhcongkham = $('#chkKHONGTINHCONGKHAM').is(":checked") == true ? 1 : 0;
            if (cfObj.NGT_CHUYENPHONG_SUBDTBN == 1) {
                subdtbnid = _subdtbnid;
            }
            _objData = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongid: $('#cboPHONGID').val(),
                dichvuid: $('#cboDICHVUID').val(),
                kieu: _opt.kieu,
                phongcuid: _opt.phongcuid,
                phongkhamdangkyid: _opts.phongkhamdangkyid,
                bhyt_dv: bhyt_dv,
                thukhacid: $("#cboTHUKHACID").val(),
                dtbnid: opt.doituongbenhnhanid,
                subdtbnid: subdtbnid,
                bocongkham: bocongkham,
                kchecktien: kchecktien,
                khongtinhcongkham: ht_cbo_loaidv == 0 ? '0' : khongtinhcongkham
            };
            //tuyennx_add_start_L2PT-27398
            if (cfObj.CHUYENPHONG_KETTHUCKHAM == '1') {
                DlgUtil.showConfirm("Bạn có muốn kết thúc khám BN sau khi chuyển phòng khám?", function (flag) {
                    if (flag) {
                        _objData.KETTHUCKHAM = '1';
                        _luudata(_objData);
                    } else {
                        _objData.KETTHUCKHAM = '0';
                        _luudata(_objData);
                    }
                });
            } else {
                _luudata(_objData);
            }
            //tuyennx_add_end_L2PT-27398
        });

        $('#cboDICHVUID').change(function () {
            if (_doivitrickpk == "0") {
                _loadPhongKham($(this).val());
            }
        });

        $('#cboPHONGID').change(function () {
            if (_doivitrickpk == "0") {
                if (_tinhthukhac == '1') {
                    var _thukhacid = $('#cboPHONGID' + " option:selected").attr('extval1');
                    if (_thukhacid == '0') {
                        _thukhacid = '-1';
                    }
                    $("#cboTHUKHACID").val(_thukhacid);
                }
                if (ht_cbo_loaidv == "1") {
                    if (HIS_KHOA_YC != "0" && HIS_KHOA_YC != "") {
                        if (HIS_KHOA_YC == "ALL") {
                            $('#chkKHONGTINHCONGKHAM').attr('checked', true);
                        } else {
                            var makhoa = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", [{
                                "name": "[0]",
                                "value": $("#cboPHONGID").val()
                            }]);
                            if (HIS_KHOA_YC.indexOf(makhoa) != -1) {
                                $('#chkKHONGTINHCONGKHAM').attr('checked', true);
                            } else {
                                $('#chkKHONGTINHCONGKHAM').attr('checked', false);
                            }
                        }
                    }
                }
            } else {
                _loadYCKham($(this).val());
            }
        });

        $('#cboLOAIDICHVU').change(function () {
            var sql_value = [];
            if ($("#cboLOAIDICHVU").val() != 0) {
                sql_value.push({"name": "[0]", "value": opt.doituongbenhnhanid});
            } else {
                sql_value.push({"name": "[0]", "value": "0"});
            }

            ComboUtil.getComboTag("cboDICHVUID", "NGTDV.002", sql_value, _opt.dichvuid, {
                value: '',
                text: 'Chọn yêu cầu khám'
            }, 'sql', '', function () {
                if ($('#cboDICHVUID').val() != 0) {
                    _loadPhongKham($('#cboDICHVUID').val());
                }
            });
        });
        $('#btnKyCa').bindOnce("click", function () {
            isKyCa = true;
            _caRpt('1', '');
        });
        $("#btnHuyCa").on("click", function (e) {
            isKyCa = true;
            _caRpt('2');
        });
        $("#btnInKySo").on("click", function (e) {
            isKyCa = true;
            _caRpt('0');
        });
        $("#btnLuuKyCa").on("click", function (e) {
            isKyCa = true
            $('#btnLuu').click();
        });
    }

    function _caRpt(signType, _msgCheck) {
        var _par = [{
            name: 'hosobenhanid',
            type: 'String',
            value: _opt.hosobenhanid
        }, {
            name: 'i_khambenhid',
            type: 'String',
            value: _opt.khambenhid
        }, {
            name: 'i_phongids',
            type: 'String',
            value: $('#cboPHONGID').val()
        }, {
            name: 'i_pkdkids',
            type: 'String',
            value: _opts.phongkhamdangkyid
        }, {
            name: 'RPT_CODE',
            type: 'String',
            value: 'RPT_PHIEUKHAMCHUYENKHOA_A4'
        }];
        if (signType == '0') {
            CommonUtil.openReportGetCA2(_par, false);
        } else {
            CommonUtil.kyCA(_par, signType, true, true);
            EventUtil.setEvent("eventKyCA", function (e) {
                var _code = e.res.split("|")[0];
                var _msg = e.res.split("|")[1];
                if (_code == '0') {
                    if (signType == '1') {
                        var evFunc = EventUtil.getEvent("assignSevice_closephieukham");
                        if (typeof evFunc === 'function') {
                            evFunc({
                                msg: 'Xử trí thành công' + '</br>' + _msgCheck + '</br>' + _msg,
                                type: 1,
                                badaingay: _badaingay
                            });
                        } else {
                            console.log('evFunc not a function');
                        }
                    } else {
                        DlgUtil.showMsg(_msg);
                        $("#btnKyCa").prop('disabled', false);
                        $("#btnLuuKyCa").prop('disabled', false);

                    }
                } else {
                    DlgUtil.showMsg(_msg);
                }
            });
        }
    }

    function _inPhieuKham() {
        var objData = new Object();
        FormUtil.setFormToObject("tabTiepNhan", "", objData);
        var par = [{
            name: 'khambenhid',
            type: 'String',
            value: _opt.khambenhid
        }, {
            name: 'phongid',
            type: 'String',
            value: $('#cboPHONGID').val()
        }];
        if (qrCode) {
            par.push({
                name : 'qrcode',
                type : 'String',
                value : qrCode
            });
        }
//		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT01T001.01", sql_par);
        if (_opt.khambenhid != null && _opt.khambenhid > 0) {
            CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par);
        } else {
            DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
        }
    }

    function _luudata(_objData) {
        if (cfObj.NGT_CHECK65BHYT_CHUYENPK != '0') {
            var _check65 = cfObj.NGT_CHECK65BHYT_CHUYENPK.split(';');
            if ( (_check65[1] == 0 || (_check65[1] == 1 && dtbnid == 1)) && checkSLKham == 0) {
                var _obj = new Object();
                _obj.PHONGID = $("#cboPHONGID").val();
                var so_bn_bhyt = 0;
                var ret =  jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK65BNBHYT.CHUYENPK", JSON.stringify(_obj));
                if (ret != 0) {
                    so_bn_bhyt = ret;
                }
                if (parseInt(so_bn_bhyt) >= parseInt(_check65[2])) {
                    if (_check65[0] == 1 ) {
                        DlgUtil.showConfirm("Phòng khám vượt quá " + _check65[2] + " bệnh nhân trong 1 ngày. Có muốn tiếp tục tiếp nhận hay không?", function (flag) {
                            if (flag) {
                                checkSLKham = 1;
                                _luudata(_objData);
                            } else {
                                return false;
                            }
                        });
                        return false;
                    }else {
                        DlgUtil.showMsg("Phòng khám vượt quá " + _check65[2] + " bệnh nhân trong 1 ngày, không thể thao tác!");
                    }
                }
            }
            checkSLKham = 0;
        }
        var rets = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K046.CNDATA", JSON.stringify(_objData));
        ret = rets.split(',');
        _badaingay = ret[2];
        maubenhphamidd = ret[3];
        var strCapCuu = ret[4] != "" ? ". Số cấp cứu " + ret[4] : "";

        if (ret[0] == 1) {
            $('#lblSTTMOI').text('Bệnh nhân có số thứ tự mới: ' + ret[1] + strCapCuu);
            //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
            DlgUtil.showMsg('Cập nhật thông tin thành công', undefined, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO'));
            //tuyennx_edit_end_20190425 L1PT-661
            _luu = 1;
            callQRCode_BIDV();
            if (_tinhthukhac == '1') {
                if (Number(maubenhphamidd) > 0) {
                    if (cfObj.NGT_TUDONGIN_CHUYENPK == "1") {
                        if (opt.hospital_id == '32620') {
                            var par = [{
                                name: 'i_khambenhid',
                                type: 'String',
                                value: _opt.khambenhid
                            }, {
                                name: 'i_phongids',
                                type: 'String',
                                value: $('#cboPHONGID').val()
                            }, {
                                name: 'i_pkdkids',
                                type: 'String',
                                value: _opts.phongkhamdangkyid
                            }];
                            if (qrCode) {
                                par.push({
                                    name : 'qrcode',
                                    type : 'String',
                                    value : qrCode
                                });
                            }
                            CommonUtil.inPhieu('window', 'RPT_PHIEUKHAMCHUYENKHOA_A4', 'pdf', par);
                        } else {
                            var par = [{
                                name: 'maubenhphamid',
                                type: 'String',
                                value: maubenhphamidd
                            }, {
                                name: 'i_hid',
                                type: 'String',
                                value: opt.i_hid
                            }, {
                                name: 'i_sch',
                                type: 'String',
                                value: opt.i_sch
                            }
                            ];
                            if (qrCode) {
                                par.push({
                                    name : 'qrcode',
                                    type : 'String',
                                    value : qrCode
                                });
                                openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5_QRCODE", "pdf", par);
                            }else{
                            openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5", "pdf", par);
                            }
                        }
                    }
                }
            }
            if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1" && isKyCa) {
                isKyCa = false;
                _caRpt('1', '');
            }
        } else if (ret == "tontaipk") {
            DlgUtil.showMsg('Bệnh nhân đã đăng ký phòng khám ' + $('#cboPHONGID option:selected').text());
        } else if (ret == "dachuyenphong") {
            DlgUtil.showMsg('Bệnh nhân đã chuyển phòng khám');
        } else if (ret == "dongbenhan") {
            DlgUtil.showMsg('Đã đóng bệnh án hoặc BN đã nhập viện. ');
        } else if (ret == "duyetketoan") {
            DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán, không được chuyển.');
        } else if (ret == "khongtaothukhac") {
            DlgUtil.showMsg('Không tạo được thu khác cho bệnh nhân này');
        } else if (ret == "tontaixn") {
            DlgUtil.showMsg('Tồn tại phiếu dịch vụ Xét nghiệm đang sửa ở phòng khám cũ, không được chuyển. ');
        } else if (ret == "tontaicdha") {
            DlgUtil.showMsg('Tồn tại phiếu dịch vụ CĐHA đang sửa ở phòng khám cũ, không được chuyển. ');
        } else if (ret == "tontaipttt") {
            DlgUtil.showMsg('Tồn tại phiếu dịch vụ PTTT đang sửa ở phòng khám cũ, không được chuyển. ');
        } else if (ret == "tontaithuoc") {
            DlgUtil.showMsg('Tồn tại phiếu Thuốc đang sửa ở phòng khám cũ, không được chuyển. ');
        } else if (ret == "tontaivt") {
            DlgUtil.showMsg('Tồn tại phiếu Vật tư đang sửa ở phòng khám cũ, không được chuyển. ');
        } else if (ret == "dvcls") {
            DlgUtil.showMsg('Chưa hoàn thành dịch vụ CLS, không được chuyển. ');
        } else if (ret == "codonthuoc") {
            DlgUtil.showMsg('Có đơn thuốc, không cho phép chuyển phòng khám. ');
        } else if (ret == "chancungyc") {
            DlgUtil.showMsg('Trùng yêu cầu khám cũ, không được phép chuyển. ');
        } else if (ret == "bckgiakhacnhau") {
            DlgUtil.showMsg('Giá công khám cũ và công khám mới khác nhau. Không chuyển được. ');
        } else if (ret == "bcktontaidvbhyt") {
            DlgUtil.showMsg('Y/c khám cũ tồn tại dịch vụ BHYT. Không chuyển được');
            //L2PT-27398
        } else if (ret == "dachuyenpk") {
            DlgUtil.showMsg('Đã chuyển phòng khám yêu cầu hủy chuyển khám rồi chuyển khám lại');
        } else if (ret == "bckkhongxacdinh") {
            DlgUtil.showMsg('Lỗi xảy ra trong khi bỏ công khám. ');
        } else if (ret == "bckkhongthaydv" || ret == "bckkhongthaymbp" || ret == "bckkhongthaydvkb") {
            DlgUtil.showMsg('Lỗi xảy ra trong khi bỏ công khám .');
        } else if (ret == "conphongchokham") {
            DlgUtil.showMsg('Còn phòng chuyển khám đang ở trạng thái chờ khám không thể chuyển !');
        } else if (ret == "chuadutime") {//HaNv_300123: L2PT-33118
            DlgUtil.showMsg('Thời gian khám tại phòng đầu tiên chưa đủ số phút quy định !');
        } else if (ret == "chokham") {
            DlgUtil.showMsg('Phòng khám đang ở trạng thái chờ khám không thể chuyển phòng!');
        } else if (ret == "chuacochandoan") {
            DlgUtil.showMsg('Phòng khám chưa có chẩn đoán, không thể chuyển phòng!');
        } else if (ret == "chuacochandoan") {
            DlgUtil.showMsg('Phòng khám đã mở bệnh án dài ngày nhưng icd chính không phải icd dai ngày vui lòng kiểm tra lại!');
        } else {
            DlgUtil.showMsg('Cập nhật thông tin không thành công');
        }
    }

    function _loadYCKham(phongkhamid) {
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": phongkhamid});
        ComboUtil.getComboTag("cboDICHVUID", "NGTDV.009.01", sql_par, '', {
            value: '',
            text: 'Chọn yêu cầu khám'
        }, 'sql', '', function () {
            var _yckham = $('#cboPHONGID' + " option:selected").attr('extval3');
            if (_yckham != "0") {
                $("#cboDICHVUID").val(_yckham);
            }
        });
    }

    function _loadPhongKham(dichvuid) {
        var sql_par1 = [];
        sql_par1.push({"name": "[0]", "value": dichvuid});
        sql_par1.push({"name": "[1]", "value": khoaid});
        if (cfObj.NGT_LOADPKCS == "1") {
            if (LOAI_CHUYEN_PHONGKHAM == 3 && $("#cboLOAICK").val() == 1) {
                ComboUtil.getComboTag("cboPHONGID", "NGTPK.DV", sql_par1, _opt.phongcuid, {extval: true}, "sql", "", function () {
                    $("#cboPHONGID").change();
                });
            } else {
                ComboUtil.getComboTag("cboPHONGID", "NGTPK.DVCS", sql_par1, _opt.phongcuid, {extval: true}, "sql", "", function () {
                    $("#cboPHONGID").change();
                });
            }
        } else {
            ComboUtil.getComboTag("cboPHONGID", "NGTPK.DV", sql_par1, _opt.phongcuid, {extval: true}, "sql", "", function () {
                $("#cboPHONGID").change();
            });
        }
    }
    function callQRCode_BIDV() {
        if (cfObj.NGT_CHUYENPK_QRCODEBIDV == '1') {
            var opts = new Object();
            opts.TIEPNHANID = $('#hidTIEPNHANID').val();
            opts.KHOAID_DN = _opt._depId; // khoa đăng nhập
            opts.PHONGID_DN = _opt._subdeptId; // phòng đăng nhập
            opts.LOAIPHIEUTHUID = "6"; // tạm thời fix cứng
            opts.HINHTHUCTHANHTOAN = "12"; // tạm thời fix cứng
            opts.TEN_HTTT = "Quét QRCODE"; // tạm thời fix cứng
            opts.DOITUONGDV = "0"; // tạm thời fix cứng
            opts.THUKHAC = "0"; // tạm thời fix cứng
            opts.CHILAPHD = "0"; // tạm thời fix cứng
            opts._loaihd = "0"; // tạm thời fix cứng
            opts._nhomtt_pt = "0"; // tạm thời fix cứng
            opts._id_dvtt = cfObj.VPI_ID_DVTT; // có thể thêm cấu hình
            opts._sotaikhoan_bv = ""; // để trống
            opts.CashierID = _opt.user_name; // người thu id
            opts.CashierName = _opt.full_name; // tên người thu
            qrCode = vienphi_tinhtien.getQRCode(opts);  
            return qrCode;
        }
    }
}