/*
 * DEV		DATE		NOTE
 * HaNv		300123		<PERSON><PERSON><PERSON> chuẩn bị và bàn giao người bệnh trước phẫu thuật - L2PT-32050
*/
function NTU02D226_BanGiaoTruocPT(_opt) {
	this.load = doLoad;
	var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4} (\d{2}):(\d{2}):(\d{2})$/;
	var _gridHeaderDs = "PHIEUID,PHIEUID,0,0,t,l;<PERSON><PERSON><PERSON> th<PERSON> hiện,NGAYTH,80,0,f,l;<PERSON><PERSON><PERSON> đ<PERSON>,CHANDOAN,120,0,f,l;Dị ứng thuốc,DIUNGTHUOC,120,0,f,l;" + "Người tạo,NGUOITAO,80,0,f,l";
	var arrData = [];
	var arrDis = [ 'txtMABN', 'txtHOTEN', 'txtNGAYSINH', 'txtGIOITINH' ];
	var isEdit = false;
	var phieuid = '';
	var mapDt = new Object();
	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		var timeStart = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		$('#txtNGAYTH').val(timeStart);
		GridUtil.init("grdDsPhieu", "100%", "100", "", false, _gridHeaderDs);
		$(document).find('input,textarea,select').each(function(index, element) {
			$(this).attr('disabled', 'disabled');
			var ctl_type = [ "txt", "cbo", "chk", "rad" ];
			var name = $(element).attr('name');
			var id = $(element).attr('id');
			var fldName = '';
			if (name) {
				var fldName = name.substring(0, 3);
				if (ctl_type.includes(fldName) && !arrData.includes(name)) {
					arrData.push(name);
				}
			} else if (id) {
				var fldName = id.substring(0, 3);
				if (ctl_type.includes(fldName) && !arrData.includes(id)) {
					arrData.push(id);
				}
			}
		});
		arrData = arrData.filter(function(val) {
			return !arrDis.includes(val);
		});
		reloadForm();
	};
	function _bindEvent() {
		GridUtil.setGridParam("grdDsPhieu", {
			onSelectRow : function(id) {
				GridUtil.unmarkAll("grdDsPhieu");
				GridUtil.markRow("grdDsPhieu", id);
				if (id) {
					setEnabled([ 'btnThem', 'btnSua', 'btnXoa' ], [ 'btnLuu', 'btnHuy' ].concat(arrData));
					var _row = $("#grdDsPhieu").jqGrid('getRowData', id);
					phieuid = _row.PHIEUID;
					loadDataForm(mapDt[phieuid]);
				}
			}
		});
		$("#btnThem").click(function() {
			loadDataForm();
			setEnabled([ 'btnLuu', 'btnHuy' ].concat(arrData), [ 'btnThem', 'btnSua', 'btnXoa' ]);
		});
		$("#btnSua").click(function() {
			isEdit = true;
			setEnabled([ 'btnLuu', 'btnHuy' ].concat(arrData), [ 'btnThem', 'btnSua', 'btnXoa' ]);
		});
		$("#btnXoa").click(function() {
			DlgUtil.showConfirm("Bạn có chắc chắn xóa bản ghi này?", function(flag) {
				if (flag) {
					saveData('1', 'DEL');
				}
			});
		});
		$("#btnHuy").click(function() {
			loadDataForm();
			if (isEdit) {
				setEnabled([ 'btnThem' ], [ 'btnLuu', 'btnHuy' ].concat(arrData));
				isEdit = false;
			} else {
				setEnabled([ 'btnThem' ], [ 'btnSua', 'btnXoa', 'btnLuu', 'btnHuy' ].concat(arrData));
			}
		});
		$("#btnLuu").click(function() {
			var mode = isEdit ? 'UPD' : 'INS';
			saveData('1', mode);
		});
		$("#btnDong").on("click", function() {
			parent.DlgUtil.close("divDlgCbiTruocmo");
		});
		
		//L2PT-36087
		$("#btnIn").on("click", function() {
			if (phieuid == ''){
				DlgUtil.showMsg("Chưa chọn phiếu để In!");
				return;
			}
			var par = [ 
				{
					name : 'phieuid',
					type : 'String',
					value : ''+phieuid
				} 
				, {
					name : 'dichvukhambenhid',
					type : 'String',
					value : ''+_opt.dichvukhambenhid
				} 
				, {
					name : 'khambenhid',
					type : 'String',
					value : ''+_opt.khambenhid
				} 
			];
			console.log(par);
			openReport('window', "RPT_BANGIAO_BENHNHAN_TRUOCMO_1007", "pdf", par); 
		});
		
	};
	function loadDataForm(objData) {
		if (!objData) {
			objData = arrData.reduce((obj, item) => Object.assign(obj, { [item]: '' }), {});
		}
		for (var i = 0; i < arrData.length; i++) {
			var fldName = arrData[i].substring(0, 3);
			if (fldName == 'rad') {
				var rad = $('input:radio[name=' + arrData[i] + ']');
				if (!objData || objData[arrData[i]] == '') {
					rad.prop( "checked", false );
				} else {
					rad.filter('[value=' + objData[arrData[i]] + ']').prop('checked', true);
				}
			} else if (fldName == 'chk') {
				if (objData[arrData[i]] == '1') {
					$('#' + arrData[i]).attr('checked', true);
				}
			} else {
				$("#" + arrData[i]).val(objData[arrData[i]]);
			}
		}
	}
	function reloadForm() {
		isEdit = false;
		var obj = new Object();
		obj.KHAMBENHID = _opt.khambenhid;
		obj.LOAIPHIEU = '2';
		obj.DICHVUKHAMBENHID = _opt.dichvukhambenhid;
		var param = [ JSON.stringify(obj) ];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTUD123.S002", JSON.stringify(obj));
		if (data_ar.length > 0) {
			$("#txtMABN").val(data_ar[0].MABENHNHAN);
			$("#txtHOTEN").val(data_ar[0].TENBENHNHAN);
			$("#txtNGAYSINH").val(data_ar[0].NGAYSINH);
			$("#txtGIOITINH").val(data_ar[0].GIOITINH);
			var arrGrid = [];
			var list = [];
			data_ar.forEach(function(el) {
				if (el.GIATRI_DL) {
					var data = el.GIATRI_DL.split("|@|");
					dataRow = {
						PHIEUID : el.PHIEUID,
						NGAYTH : data[2],
						CHANDOAN : data[0],
						DIUNGTHUOC : data[1],
						NGUOITAO : el.NGUOITAO
					};
					arrGrid.push(dataRow);
					var objGr = arrData.reduce((obj, item) => Object.assign(obj, { [item]: '' }), {});
					arrData.forEach(function(item, index) {
						objGr[item] = data[index];
					});
					mapDt[el.PHIEUID] = objGr;
				}
			});
			loadDataForm();
			loadGridFromObj("grdDsPhieu", arrGrid);
		}
	}
	function loadGridFromObj(gridid, data_ar) {
		var buildHeader = "";
		$("#" + gridid).jqGrid('clearGridData');
		if (data_ar != null && data_ar.length > 0) {
			$("#" + gridid).jqGrid('setGridParam', {
				datatype : 'local',
				data : data_ar
			}).trigger("reloadGrid");
		}
	}
	function saveData(type, mode) {
		var obj = new Object();
		obj.TIEPNHANID = _opt.tiepnhanid;
		obj.KHAMBENHID = _opt.khambenhid;
		obj.DICHVUKHAMBENHID = _opt.dichvukhambenhid;
		obj.LOAI_DL = type + '';
		obj.MODE = mode;
		obj.LOAI_PHIEU = '2';
		obj.GIATRI_DL = '';
		if (type == '1') {//Hanh chinh
			obj.GIATRI_DL = joinText();
			obj.PHIEUID = phieuid;
		}
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('NTUD123.S001', JSON.stringify(obj));
		if (fl == 1) {
			DlgUtil.showMsg('Thêm mới thành công !');
			reloadForm();
		} else if (fl == 2) {
			DlgUtil.showMsg('Cập nhật thành công !');
			reloadForm();
		} else if (fl == 3) {
			DlgUtil.showMsg('Xóa thành công!');
			reloadForm();
		} else {
			DlgUtil.showMsg('Có lỗi xảy ra khi thực hiện cập nhật !');
		}
	}
	function joinText() {
		var data = '-1';
		for (var i = 0; i < arrData.length; i++) {
			var fldName = arrData[i].substring(0, 3);
			var el = $("#" + arrData[i]).val();
			if (fldName == 'rad') {
				el = $('input[name=' + arrData[i] + ']:checked').val() ? $('input[name=' + arrData[i] + ']:checked').val() : '';
			} else if (fldName == 'chk') {
				el = $('#' + arrData[i]).is(':checked') ? '1' : '0';
			}
			if (data == '-1') {
				data = el;
			} else {
				data = data + '|@|' + el;
			}
		}
		return data;
	}
	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
			$('input[name=' + _ena[i] + ']').attr("disabled", false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
			$('input[name=' + _dis[i] + ']').attr("disabled", true);
		}
	}
}
