/*
 	MUC DICH: HIEN THI CHI TIET THONG TIN LICH SU KHAM CHUA BENH BAO HIEM; 
 	
 	DU LIEU TRUYEN LA 1 JSON DUOI DANG SAU: 
 		- USERBH; 
 		- PASSBH; 
 		- MAHOSO CAN HIEN THI; 
 	
 	NEU KHONG LAY RA DU LIEU THI HIEN THI THONG BAO VA DE DU LIEU TRANG CHO FORM; 
 	NEU CO DU LIEU THI FILL VAO TEXTBOX; 
 	
 	NGUOI TAO		NGAY TAO		NOI DUNG SUA DOI; 
 	SONDN			10/05/2017			TAO MOI; 
 	
*/
function lichsukcbct(_opts) {
	var _company_id = _opts._hospital_id;
	var _action = "";
	var _user_id = _opts._uuid;
	var _data = [];
	var _arr;	
	var _sql_par = [];
	
	var _row = _opts.ROW; 
	
	var that=this;
	this.load=doLoad;

	function doLoad() {
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		this.validator = new DataValidator("dvLichSuKCB");
		
		loadDataField(_row);
		
		bindEvent(); 
		setEnable(false);
	}
	
	// THAY DOI DINH DANG NGAY THANG: ************ => 23/12/2017 (KHONG SU DUNG TIME )
	function XuLyNgayThang(dt){
		if(dt.length < 8){
			return null;
		}else{
			return dt.substring(6,8) + "/" + dt.substring(4,6) + "/" + dt.substring(0,4);  
		}
	}
	
	function loadDataField(xml1){
		$("#txtCanNang").val(xml1.CAN_NANG); 
		$("#txtHoTen").val(xml1.HOTEN); 
//		$("#txtTenChame").val("k"); 					//
//		$("#txtNgaySinh").val(xml1.NgaySinh); 			// "19840000"
		
		$("#txtMaBenh").val(xml1.MA_BENH); 			// "K29"
		$("#txtMaBenhkhac").val(xml1.MA_BENH_KHAC); 		// null
		$("#txtTenBenh").val(xml1.TEN_BENH); 
		$("#txtMaBn").val(xml1.MA_BN); 				
		$("#txtMaKhoa").val(xml1.MA_KHOA); 			// "K01"
		$("#cboTinhTrangRv").val(xml1.TINH_TRANG_RV); 					// 1

		$("#txtGtTheDen").val(xml1.GT_THE_DEN); 			// = "20171231"
		$("#txtGtTheTu").val(xml1.GT_THE_TU); 
		$("#txtMaPtttQt").val(xml1.MA_PTTT_QT); 			// null
		$("#txtMaTaiNan").val(xml1.MA_TAI_NAN); 			// null
		$("#txtMaThe").val(xml1.MATHE); 				// "HN2370701700246"
//		$("#txtMieuta").val(xml1.Mieuta); 			// null
//		$("#txtMucHuong").val(xml1.MucHuong == "" ? "0%" : xml1.MucHuong + "%"); 			// 100
		
		$("#txtSoNgayDtri").val(xml1.SO_NGAY_DTRI); 		// 0 
		
		$("#txtTBhtt").val(xml1.T_BHTT == "" ? "0 VNĐ" : xml1.T_BHTT + " VNĐ"); 				// 168960
		$("#txtTBntt").val( xml1.T_BNTT == "" ? "0 VNĐ" : xml1.T_BNTT + " VNĐ"); 					// 0
		$("#txtTNgoaids").val(xml1.T_NGOAIDS == "" ? "0 VNĐ" : xml1.T_NGOAIDS + " VNĐ"); 					//0
		$("#txtTNguonkhac").val(xml1.T_NGUONKHAC == "" ? "0 VNĐ" : xml1.T_NGUONKHAC + " VNĐ"); 					// 0
		$("#txtTThuoc").val(xml1.T_THUOC == "" ? "0 VNĐ" : xml1.T_THUOC + " VNĐ"); 					//137960
		$("#txtTTongchi").val(xml1.T_TONGCHI == "" ? "0 VNĐ" : xml1.T_TONGCHI + " VNĐ"); 					//168960
		$("#txtTVtyt").val(xml1.T_VTYT == "" ? "0 VNĐ" : xml1.T_VTYT + " VNĐ"); 					// 0

//		$("#txtTiengiuong").val(xml1.Tiengiuong == "" ? "0 VNĐ" : xml1.Tiengiuong + " VNĐ"); 					// 0
//		$("#txtTienkham").val(xml1.Tienkham == "" ? "0 VNĐ" : xml1.Tienkham + " VNĐ"); 					//31000
//		$("#txtTienvanchuyen").val(xml1.Tienvanchuyen == "" ? "0 VNĐ" : xml1.Tienvanchuyen + " VNĐ"); 					// 0

		$("#txtNgayRa").val(xml1.NGAY_RA); 			// "201704201630"
//		$("#txtNgaynhap").val(xml1.Ngaynhap == "" ? "" : XuLyNgayThang(xml1.Ngaynhap)); 			// "201704201632"
//		$("#txtNgaythanhtoan").val(xml1.Ngaythanhtoan == "" ? "" : XuLyNgayThang(xml1.Ngaythanhtoan)); 		// "201704201630"
		$("#txtNgayVao").val(xml1.NGAY_VAO);
	}
	
	function setEnable(val){
		$("#txtCanNang").prop("disabled", !val ); 
//		$("#txtDiaChi").prop("disabled", !val ); 
//		$("#txtGioiTinh").prop("disabled", !val ); 			
		$("#txtHoTen").prop("disabled", !val ); 
//		$("#txtTenChame").prop("disabled", !val ); 					
//		$("#txtNgaySinh").prop("disabled", !val ); 			
		
		$("#txtMaBenh").prop("disabled", !val ); 			
		$("#txtMaBenhkhac").prop("disabled", !val ); 		
		$("#txtTenBenh").prop("disabled", !val ); 
		$("#txtMaBn").prop("disabled", !val ); 				
		$("#txtMaKhoa").prop("disabled", !val ); 			
		$("#cboTinhTrangRv").prop("disabled", !val ); 					

		$("#txtGtTheDen").prop("disabled", !val ); 			
		$("#txtGtTheTu").prop("disabled", !val ); 
		$("#txtMaPtttQt").prop("disabled", !val ); 			
		$("#txtMaTaiNan").prop("disabled", !val ); 			
		$("#txtMaThe").prop("disabled", !val ); 				
//		$("#txtMieuta").prop("disabled", !val ); 			
//		$("#txtMucHuong").prop("disabled", !val ); 			
		
		$("#txtSoNgayDtri").prop("disabled", !val ); 		
		
		$("#txtTBhtt").prop("disabled", !val ); 				
		$("#txtTBntt").prop( "disabled", !val); 					
		$("#txtTNgoaids").prop("disabled", !val ); 					
		$("#txtTNguonkhac").prop("disabled", !val ); 					
		$("#txtTThuoc").prop("disabled", !val ); 					
		$("#txtTTongchi").prop("disabled", !val ); 					
		$("#txtTVtyt").prop("disabled", !val ); 					

//		$("#txtTiengiuong").prop("disabled", !val ); 					
//		$("#txtTienkham").prop("disabled", !val ); 					
//		$("#txtTienvanchuyen").prop("disabled", !val ); 					

		$("#txtNgayRa").prop("disabled", !val ); 			
//		$("#txtNgaynhap").prop("disabled", !val ); 			
//		$("#txtNgaythanhtoan").prop("disabled", !val ); 		
		$("#txtNgayVao").prop("disabled", !val );
	}
	
	function bindEvent(){
		$("#btnClose").on("click", function(){
			parent.DlgUtil.close("divDlgDDT");
		});
	}
	
	$('.input-sm').keydown(function (e) {
	     if (e.which === 13) {
	         var index = $('.input-sm').index(this) + 1;
	         $('.input-sm').eq(index).focus();
	     }
	 });
}