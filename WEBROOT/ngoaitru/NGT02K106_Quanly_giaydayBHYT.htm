<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>

<script src="../common/script/jqgrid/js/context-menu.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/xml2json.js"></script>
<script type="text/javascript"
	src="../ngoaitru/NGT02K106_Quanly_giaydayBHYT.js?v=22092600"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css"
	type="text/css" />

<div width="100%" id="divMain" class="container">
	<div class="form-inpfo mgt3" style="text-align: right">Quản lý
		giấy đẩy BHYT</div>

	<div id="divSearch" class="col-xs-12 low-padding border-group-1 mgt5">
		<div class="col-xs-12 mgt5 low-padding ">
			<div class="col-xs-8">

				<div class="col-xs-6 low-padding">
					<div class="col-xs-5 low-padding required">
						<label class="control-label">Loại giấy</label>
					</div>
					<div class="col-xs-5 low-padding">
						<div class="input-group mgt3">
							<select class="form-control input-sm" id="cboLoaiGiay">
								<option value=-1>Chọn</option>
								<option value=0>Giấy chứng sinh</option>
								<option value=1>Giấy ra viện</option>
								<option value=2>Giấy nghỉ ốm</option>
								<!-- BVTM-5090 start -->
								<option value=3>Giấy chứng nhận nghỉ thai sản</option>
								<option value=4>Tóm tắt hồ sơ bệnh án</option>
								<!-- BVTM-5090 end -->
							</select>
						</div>
					</div>
				</div>

				<div class="col-xs-6 low-padding">
					<div class="col-xs-4 low-padding">
						<label>Khoa</label>
					</div>
					<div class="col-md-8 low-padding">
						<div class="col-md-8 low-padding"
							style="width: 100%; margin-left: -1.9%">
							<select class="selectpicker SumoUnder" id="cboKHOA_ID_SEARCH"
								multiple data-actions-box="true"
								data-selected-text-format="count > 2" data-width="auto"
								style="width: 100%">
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-4">
				<div class="col-xs-12 low-padding">
					<div class="col-md-4 low-padding">
						<label class="">Đối tượng</label>
					</div>
					<div class="col-md-8 low-padding">
						<select class="form-control input-sm" style="width: 100%"
							id="cboDOITUONGBENHNHANID" modeDisBH="">
						</select>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xs-12 mgt5 low-padding ">
			<div class="col-xs-8">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-5 low-padding">
						<label class="control-label">Ngày chứng từ: Từ Ngày</label> <!-- L2PT-26486 -->
					</div>
					<div class="col-xs-7 low-padding">
						<div class="input-group mgt3">
							<input class="form-control input-sm" style="width: 70%"
								id="txtTuNgay" name="txtTuNgay" title="" data-mask="00/00/0000"
								placeholder="dd/MM/yyyy"> <span
								class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar ngay-den"
								type="sCal"
								onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"
								style="float: left; width: 24px;"></span>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="control-label">Đến ngày</label>
					</div>
					<div class="col-xs-8"
						style="padding-left: 0; padding-right: 7px; margin-left: -1%">
						<div class="input-group mgt3">
							<input class="form-control input-sm" style="width: 70%"
								id="txtDenNgay" name="txtDenNgay" title=""
								data-mask="00/00/0000" placeholder="dd/MM/yyyy"> <span
								class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
								type="sCal"
								onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"
								style="float: left; width: 24px;"></span>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-4">
				<div class="col-xs-12 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="control-label">Thông tin bảo hiểm</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group">
							<select class="form-control input-sm" id="cboTTBH">
								<option value=1>Đủ</option>
								<option value=0>Tất cả</option>
								<option value=2>Thiếu</option>
							</select>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div class="col-xs-12 mgt5 low-padding ">
			<div class="col-xs-8">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-5 low-padding">
						<label class="control-label">Loại bệnh án</label>
					</div>
					<div class="col-xs-5 low-padding">
						<div class="input-group mgt3">
							<select class="form-control input-sm" id="cboLOAITIEPNHAN"
								style="width: 123%;">
								<option value=-1>--Tất cả--</option>
								<option value=0>Nội trú</option>
								<option value=1>Ngoại trú</option>
								<option value=3>Điều trị ngoại trú</option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-xs-6 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="control-label">Trạng thái bệnh nhân</label>
					</div>
					<div class="col-xs-8"
						style="padding-left: 0; padding-right: 7px; margin-left: -1%">
						<div class="input-group mgt3">
							<select class="form-control input-sm" id="cboTRANGTHAI_TT"
								style="width: 135%;">
								<option value=-1>--Tất cả--</option>
								<option value=0>Chưa kết thúc</option>
								<option value=1>Đã kết thúc</option>
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xs-12 mgt5 low-padding ">
			<div class="col-xs-8">
				<div class="col-xs-6 low-padding">
					<div class="col-xs-5 low-padding">
						<label class="control-label">Gửi Bảo Hiểm</label>
					</div>
					<div class="col-xs-7 low-padding">
						<div class="input-group mgt3">
							<select class="form-control input-sm" id="cboTrangThai"
								style="width: 143%;">
								<option value=-1>Chọn</option>
								<option value=1>Chưa gửi BH</option>
								<option value=2>Đã gửi BH</option>
								<option value=3>Lỗi</option>
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xs-2 mgt5 low-padding ">
		</div>
		<div class="col-xs-10 mgt5 low-padding ">
			<div class="col-xs-10">
				<div class="col-xs-2" style="padding-left: 0;">
					<button type="button" class="btn btn-sm btn-primary"
						id="btnTimKiem">
						<span class="glyphicon glyphicon-search"></span> Tìm kiếm
					</button>
				</div>
				<div class="col-xs-2" style="padding-left: 0;">
					<button type="button" class="btn btn-sm btn-primary" id="btnXuat"
						>
						<span></span> Xuất Excel
					</button>
				</div>
				<div class="col-xs-2" style="padding-left: 0;margin-left: -5px">
					<button type="button" class="btn btn-sm btn-primary"
						id="btnHuyxuat" >
						<span></span> Hủy xuất
					</button>
				</div>
				<div class="col-xs-2" style="padding-left: 0;margin-left: -15px">
					<button type="button" class="btn btn-sm btn-primary"
						id="btnBaoLoi">
						<span></span> Báo lỗi
					</button>
				</div>
				<div class="col-xs-2" style="padding-left: 0;margin-left: -25px">
					<button type="button" class="btn btn-sm btn-primary"
						id="btnXemLog" >
						<span></span> Xem Log
					</button>
				</div>
				<!-- <div class="col-xs-2">
					<div class="col-xs-3">
						<button type="button" class="btn btn-sm btn-primary"
							id="btnBaoLoi">
							<span></span>Báo lỗi
						</button>
					</div>
				</div>
				<div class="col-xs-2">
					<div class="col-xs-3">
						<button type="button" class="btn btn-sm btn-primary"
							id="btnXemLog">
							<span></span>Xem Log
						</button>
					</div>
				</div> -->
			</div>
		</div>
	</div>
	<div class="col-md-12">
		<div class="col-xs-12 low-padding">
			<table id="grdDSBN"></table>
			<div id="pager_grdDSBN"></div>
		</div>
	</div>

	<div id="popupId"></div>
	<div class="col-xs-2 low-padding mgt-10">
		<img src="../common/image/Circle_Yellow.png" width="15px"
			style="margin-top: 10px;">
		<div style="margin-top: -17px; margin-left: 25px;">
			<label class="control-label">Chưa gửi bảo hiểm</label>
		</div>
	</div>

	<div class="col-xs-2 low-padding mgt-10">
		<img src="../common/image/Circle_Green.png" width="15px"
			style="margin-top: 10px;">
		<div style="margin-top: -17px; margin-left: 25px;">
			<label class="control-label">Đã gửi bảo hiểm</label>
		</div>
	</div>

	<div class="col-xs-2 low-padding mgt-10">
		<img src="../common/image/Circle_Red.png" width="15px"
			style="margin-top: 10px;">
		<div style="margin-top: -17px; margin-left: 25px;">
			<label class="control-label">Gửi lỗi</label>
		</div>
	</div>
</div>

<script>
	var userInfo = CommonUtil.decode('{userData}');
	var opt = [];
	var schema = '{db_schema}';
	var hospital_id = '{hospital_id}';
	var company_id = '{company_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var dept_id = '{dept_id}';
	initAjax("/vnpthis");
	//ajaxSvc.register("CongBTKDWS");
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var paramInfo = CommonUtil.decode('{paramData}');
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';

	initRest(uuid, "/vnpthis");

	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;
	_opts._deptId = dept_id;
	_opts.lk = paramInfo.lk;
	_opts.company_id = company_id;
	_opts.hospital_code = paramInfo.HOSPITAL_CODE;
	_opts.backColor = [ "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF",
			"#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF",
			"#FFFFFF", "#FFFFFF", "#FFFFFF" ];
	_opts.imgPath = [ "Circle_Yellow.png", "Circle_Green.png" ];
	_opts.foreColor = [ "#000000", "#000000", "#000000", "#808080", "#000000",
			"#000000", "#000000", "#000000", "#000000", "#000000", "#000000",
			"#FF0000", "#800000", "#000000" ];
	var DS = new dSCHList(_opts);
	DS.load(hospital_id);
</script>