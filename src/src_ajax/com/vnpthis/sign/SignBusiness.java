package com.vnpthis.sign;

import AjaxUtil.AesUtil;
import AjaxUtil.AjaxJson;
import com.google.gson.Gson;
import com.vnpt.util.HospitalUtils;
import com.vnpt.util.consts.Constants;
import com.vnpthis.VnptCaKySo.Utils;
import com.vnptit.util.CheckObjectUtils;
import com.vnptit.util.DBActUtil;
import com.vnptit.util.PropertiesUtility;
import com.vnptit.util.SysConstants;
import exception.HisSigningException;
import intellsoft.db.DBUtil;
import intellsoft.db.DBUtility;
import jsonutil.JSONArray;
import jsonutil.JSONObject;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Base64;


/**
 * <AUTHOR>
 */
public class SignBusiness {
    private static final Logger logger = LoggerFactory.getLogger(SignBusiness.class);

    public static final String bucketName = PropertiesUtility.getConfBundle("com.media.emr.storage.bucketname");
    public static final String serviceUrl = PropertiesUtility.getConfBundle("idg.storage.emr.service.url");
    public static final String accessKey = PropertiesUtility.getConfBundle("idg.storage.emr.access.key");
    public static final String secretKey = PropertiesUtility.getConfBundle("idg.storage.emr.secret.key");

    public static final String signUrl = Utils.getConfig("conf_ca_vn.properties", "sign.endpoint_gateway");
    private static final String signUser = Utils.getConfig("conf_ca_vn.properties", "sign.ca.user");
    private static final String signPassword = Utils.getConfig("conf_ca_vn.properties", "sign.ca.password");
    private static final String smartCaRefreshToken = Utils.getConfig("conf_ca_vn.properties", "smart.ca.refresh.token.url");
    private static final String DB_INFO = "jdbc/HISL2DS";
    private static final String SP_EMR_CA_DETAIL_HTR_INS = "{?=call EMR_CA_DETAIL_HTR_INS('[UID]','[HID]','[SCH]','[IP]',?2S,?3S)}";

    public static final String SP_EMR_CA_DETAIL_ADD = "{?=call EMR_DETAIL_ADD('[UID]','[HID]','[SCH]','[IP]',?2S,?3S)}";
    private static final String AU_BAC = "Basic ";
    private static final boolean ALLOWED = true;

    private String url;
    private String username;
    private String password;
    private String sourceTypeValue;
    private String docTypeValue;
    private String outputTypeValue;
    private String signMedhodTypeValue;
    private InfoData infoData;
    private Gson gson = new Gson();

    SignBusiness(String url, String username, String password, String sourceTypeValue, String docTypeValue, String outputTypeValue, String signMedhodTypeValue, InfoData infoData) {
        this.url = url;
        this.username = username;
        this.password = password;
        this.sourceTypeValue = sourceTypeValue;
        this.docTypeValue = docTypeValue;
        this.outputTypeValue = outputTypeValue;
        this.signMedhodTypeValue = signMedhodTypeValue;
        this.infoData = infoData;
    }

    public static SignBuilder builder() {
        return new SignBuilder();
    }

    public SignObjectResponse sign(String document, JSONObject patientInfo) throws Exception {
        MetaData metaData = buildMetaData(patientInfo);
        JSONObject confForCA = getLstConfForCA();
        SignObjectRequest signObjectRequest = new SignObjectRequest();
        signObjectRequest.setDocType(this.docTypeValue);
        signObjectRequest.setOutputType(this.outputTypeValue);
        signObjectRequest.setSignMethod(this.signMedhodTypeValue);
        signObjectRequest.setSource(this.sourceTypeValue);
        signObjectRequest.setEnableSignatureImageV2("1".equals(confForCA.getString(Const.ENABLE_SIGNATURE_IMAGE_V2)));
        boolean isKyBaoHiemXaHoi =
                CheckObjectUtils.isNotNull(patientInfo)
                    && CheckObjectUtils.isNotNull(patientInfo.get("isKyBaoHiemXaHoi"))
                    && "1".equals(patientInfo.getString("isKyBaoHiemXaHoi"));
        signObjectRequest.setKyBaoHiemXaHoi(isKyBaoHiemXaHoi);
        if (!isKyBaoHiemXaHoi) {
            JSONObject signatureMetaData = new JSONObject();
            signatureMetaData.put("DS_CHUKY", CheckObjectUtils.isNullOrEmpty(this.infoData.getDsChuky()) ? "1" : this.infoData.getDsChuky());
            signObjectRequest.setSignatureMetadata(signatureMetaData.toString());
        } else {
            if (CheckObjectUtils.isNotNull(patientInfo.get("signFragment"))) {
                signObjectRequest.setTagSaveSignature(patientInfo.getString("signFragment"));
            }
        }
        if (
                (
                        this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.SMARTCA.getKEY())
                        || this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.E_SEAL.getKEY())
                )
                    && "1".equals(confForCA.getString(Const.HIS_SIGN_SMART_CA_METHOD))) {
            signObjectRequest.setUsername("TOKEN");
            metaData.setRefreshToken(this.username);
        } else {
            signObjectRequest.setUsername(this.username);
        }
        signObjectRequest.setPassword(this.password);
        if (this.sourceTypeValue.equals(SignBuilder.SourceType.FILE.getKEY())) {
            String base64XMLIn = Base64.getEncoder().encodeToString(document.getBytes(StandardCharsets.UTF_8));
            signObjectRequest.setDocument(base64XMLIn);
        } else {
            signObjectRequest.setDocument(document);
        }
        signObjectRequest.setMetaData(metaData);
        String jsonData = gson.toJson(signObjectRequest);
        logger.info("SignBusiness >> 1th >> " + jsonData);
        //không commit
//        TrustAllCertificates.install();
        SignObjectResponse signObjectResponse = signInvoke(jsonData);
        if (signObjectResponse.getCode().equals("19")
                && (
                        this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.SMARTCA.getKEY())
                        || this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.E_SEAL.getKEY())
                    )
                && "1".equals(confForCA.getString(Const.HIS_SIGN_SMART_CA_METHOD))) {
            JSONObject refreshTokenObj = refreshTokenSmartCA(this.username);
            if (refreshTokenObj.getString("code").equals("00")) {
                JSONObject data = refreshTokenObj.getJSONObject("data");
                String access_token = data.getString("access_token");
                String refresh_token = data.getString("refresh_token");
                metaData.setRefreshToken(refresh_token);
                signObjectRequest.setMetaData(metaData);
                signObjectRequest.setPassword(access_token);
                String jsonData2 = gson.toJson(signObjectRequest);
                logger.info("SignBusiness >> 2th >> " + jsonData2);
                signObjectResponse = signInvoke(jsonData2);
                signObjectResponse.setSignInfo(data.toString());
                return signObjectResponse;
            } else {
                return signObjectResponse;
            }
        } else {
            return signObjectResponse;
        }
    }

    private JSONObject getLstConfForCA() throws HisSigningException {
        JSONObject userSysInfo = new JSONObject()
                .put(SysConstants.DB_NAME, DB_INFO)
                .put(SysConstants.USER_ID, infoData.getUserId())
                .put(SysConstants.COMPANY_ID, infoData.getCompanyId())
                .put(SysConstants.DB_SCHEMA, infoData.getDbschema());

        JSONArray dsCh = new HospitalUtils(userSysInfo).getLstConf(new String[] {
                Const.HIS_SIGN_SMART_CA_METHOD,
                Const.ENABLE_SIGNATURE_IMAGE_V2
        });

        if (dsCh == null || dsCh.length() < 1) {
            throw new HisSigningException("Không thể tìm thấy thông tin cấu hình cho ký số");
        }

        return dsCh.getJSONObject(0);
    }

    private SignObjectResponse signInvoke(String jsonData) {
        try {
            URL reqUrl = new URL(this.url);
            URLConnection urlCon = reqUrl.openConnection();
            urlCon.setDoOutput(true);
            urlCon.setDoInput(true);
            HttpURLConnection httpConnection = (HttpURLConnection) urlCon;
            httpConnection.setRequestMethod("POST");
            httpConnection.setRequestProperty("Content-Type", "application/json");
            httpConnection.setRequestProperty("Content-Length", Integer.toString(jsonData.getBytes().length));
            String authorization = Base64.getEncoder().encodeToString((signUser + ":" + signPassword).getBytes(StandardCharsets.UTF_8));
            httpConnection.setRequestProperty("Authorization", AU_BAC + authorization);
            PrintStream ps = new PrintStream(httpConnection.getOutputStream(), true, "UTF-8");
            ps.write(jsonData.getBytes());
            ps.flush();

            int code = httpConnection.getResponseCode();

            if (code != 200) {
                SignObjectResponse signObjectResponse = new SignObjectResponse();
                signObjectResponse.setCode(String.valueOf(code));
                signObjectResponse.setMessage(IOUtils.toString(httpConnection.getErrorStream(), StandardCharsets.UTF_8));
                return signObjectResponse;
            }

            try (BufferedReader br = new BufferedReader(
            		new InputStreamReader(httpConnection.getInputStream(), 
            				StandardCharsets.UTF_8))) {
            	String str;
                StringBuffer sb = new StringBuffer();
                while ((str = br.readLine()) != null) {
                    sb.append(str);
                }
//                is.close();
                String respData = new String(sb.toString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                if (respData.isEmpty()) {
                    SignObjectResponse signObjectResponse = new SignObjectResponse();
                    signObjectResponse.setCode("99");
                    signObjectResponse.setMessage("ERR: Response null");
                    return signObjectResponse;
                }
                return gson.fromJson(respData, SignObjectResponse.class);         	
            }            
        } catch (Exception e) {
            logger.error("SignBusiness >> signInvoke >>" + e.getMessage());
            SignObjectResponse signObjectResponse = new SignObjectResponse();
            signObjectResponse.setCode("99");
            signObjectResponse.setMessage(e.getMessage());
            return signObjectResponse;
        }
    }

    private JSONObject refreshTokenSmartCA(String token) {
        URL reqUrl;
        String jsonData = new JSONObject().put("token", token).toString();
        try {
            reqUrl = new URL(smartCaRefreshToken);
            URLConnection urlCon = reqUrl.openConnection();
            urlCon.setDoOutput(true);
            urlCon.setDoInput(true);
            HttpURLConnection httpConnection = (HttpURLConnection) urlCon;
            httpConnection.setRequestMethod("POST");
            httpConnection.setRequestProperty("Content-Type", "application/json");
            httpConnection.setRequestProperty("Content-Length", Integer.toString(jsonData.getBytes().length));
            String authorization = Base64.getEncoder().encodeToString((signUser + ":" + signPassword).getBytes(StandardCharsets.UTF_8));
            httpConnection.setRequestProperty("Authorization", AU_BAC + authorization);
            PrintStream ps = new PrintStream(httpConnection.getOutputStream(), true, "UTF-8");
            ps.write(jsonData.getBytes());
            ps.flush();

            int code = httpConnection.getResponseCode();
            if (code != 200) {
                return new JSONObject().put("code", String.valueOf(code));
            }
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(httpConnection.getInputStream(),
                            StandardCharsets.UTF_8))) {
                String str;
                StringBuffer sb = new StringBuffer();
                while ((str = br.readLine()) != null) {
                    sb.append(str);
                }

                String respData = new String(sb.toString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                if (respData.isEmpty()) {
                    return new JSONObject().put("code", "99").put("message", "ERR: Response null");
                }
                return new JSONObject(respData);
            }
        } catch (IOException | ParseException e) {
            e.printStackTrace();
            return new JSONObject().put("code", "99").put("message", e.getMessage());
        }
    }

    private static String getCauHinh(String companyId, String cauhinh) {
        AjaxJson ajaxJson = new AjaxJson(DB_INFO);
        return ajaxJson.ajaxCALL_SP_S("{?=call com_lay_cauhinh('" + companyId + "',?2S)}", cauhinh + "$", new Object[]{});
    }

    private MetaData buildMetaData(JSONObject patientInfo) {
        MetaData metaData = new MetaData();
        metaData.setVisibleSignatureRectangle("105,786,205,876");
        metaData.setSignatureTextSize(4);
        if (this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.SMARTCA.getKEY()) || this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.E_SEAL.getKEY())) {
            String urlRest = DBUtil.getOneValueString(DB_INFO, "SELECT URL_REST FROM ORG_ORGANIZATION WHERE ORG_ID = ?1L", new Object[]{this.infoData.getCompanyId()});
            String smartCaCallbackUrl = urlRest.replace("RestService", "smart-ca/callback");
            //tach luong đơn thuốc điện tử
            if (patientInfo != null) {
                if("RPT_DONTHUOC_DIENTU".equals(patientInfo.getString("RPT_CODE"))){
                    smartCaCallbackUrl = urlRest.replace("RestService", "smart-ca/callback-dtdt");
                }
                if("XML130".equals(patientInfo.getString("RPT_CODE"))){
                    smartCaCallbackUrl = urlRest.replace("RestService", "smart-ca/callback-xml130");
                }
            }
            String so = AesUtil.toHex(this.infoData.toString());
            logger.info("SignBusiness >> smartCaCallbackUrl >> " + smartCaCallbackUrl + "?so=" + so);
            metaData.setCallbackUrl(smartCaCallbackUrl + "?so=" + so);
        }

        if(this.signMedhodTypeValue.equals(SignBuilder.SignMethodType.KYVANTAY.getKEY())) {
            String urlRest = DBUtil.getOneValueString(DB_INFO, "SELECT URL_REST FROM ORG_ORGANIZATION WHERE ORG_ID = ?1L", new Object[]{this.infoData.getCompanyId()});
            String fingerCaCallbackUrl = urlRest.replace("RestService", "finger-ca/callback");
            String so = AesUtil.toHex(this.infoData.toString());
            logger.info("SignBusiness >> fingerCaCallbackUrl >> " + fingerCaCallbackUrl + "?so=" + so);
            metaData.setCallbackUrl(fingerCaCallbackUrl + "?so=" + so);
            metaData.setMaBenhNhan(patientInfo.getString("maBenhNhan"));
            metaData.setHoVaTen(patientInfo.getString("hoVaTen"));
            metaData.setNgaySinh(patientInfo.getString("ngaySinh"));
            metaData.setSoDienThoai(patientInfo.getString("soDienThoai"));
            metaData.setMaThietBi(patientInfo.getString("maThietBi"));
            metaData.setMaDonVi(this.infoData.getHospitalcode());
            metaData.setTemplateCode(patientInfo.getString("templateCode"));
        }
        return metaData;
    }

    public static String addLogHistory(JSONObject userInfo, String dbInfo, String paramHashed, String isClosed) {
        String msgLogHTR = "";
        if (!Arrays.asList("1", "0").contains(isClosed)) {
            logger.error("SignBusiness addLogHistory -> logHTR error : isClosed invalid: " + paramHashed);
            msgLogHTR = "(Log issue - 1)";
            return msgLogHTR;
        }
        String spCaHistory = DBActUtil.setDefaultInfo(
                SP_EMR_CA_DETAIL_HTR_INS,
                userInfo.getString(SysConstants.USER_ID),
                userInfo.getString(SysConstants.HOSPITAL_ID),
                SysConstants.EMPTY_STRING,
                userInfo.getString(SysConstants.DB_SCHEMA));
        final StringBuilder spParam = new StringBuilder();
        spParam.append(paramHashed).append(Constants.DOLAR_CHARACTERS);
        spParam.append(isClosed).append(Constants.DOLAR_CHARACTERS);
        String strParam = spParam.toString();
        Object[] sp_out = new Object[]{};
        int logId = new AjaxJson(dbInfo).ajaxCALL_SP_I(spCaHistory, strParam, sp_out);
        if (logId < 0) {
            logger.error("SignBusiness addLogHistory -> logHTR error : logId: " + logId + ", paramHashed: " + paramHashed);
            msgLogHTR = "(Log issue)";
        }
        return msgLogHTR;
    }

    private static class Const {
        static String ENABLE_SIGNATURE_IMAGE_V2 = "ENABLE_SIGNATURE_IMAGE_V2";
        static String HIS_SIGN_SMART_CA_METHOD = "HIS_SIGN_SMART_CA_METHOD";
    }
}