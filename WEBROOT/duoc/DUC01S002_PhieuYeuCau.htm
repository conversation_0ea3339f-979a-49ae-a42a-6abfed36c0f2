<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script type="text/javascript"
	src="../common/script/alertify/alertify.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />

<script type="text/javascript" src="../common/script/moment.min.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=201809203"></script>

<script type="text/javascript" src="..//noitru/cominf.js?v=2018092102" ></script>
<script type="text/javascript" src="../common/script/xml2json.js?v=2018092101" ></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/responsive-voice.js?v=2610" ></script>
<script type="text/javascript" src="../common/script/GoogleCloudService.js?v=20200317"></script>
<script type="text/javascript" src="../common/vienphi/vienphi_tinhtien.js"></script>
<script type="text/javascript"	src="../duoc/DUC01S002_PhieuYeuCau.js?v=202404070"></script>

<script type="text/javascript" src="../noitru/cominf.js?v=20181101002" ></script>
<div class="container" id="divMain">
	<div id="toolbarId"></div>
<!-- 	<!-- SONDN -->  
<!-- 	<div id="dvCall5" class="panel-body mgt5"> -->
<!-- 		<div class="col-xs-12 low-padding"> -->
<!-- 			<div class="col-xs-4"> -->
<!-- 				<div class="col-xs-4"> -->
<!-- 					<input type="text" class="form-control input-sm" style="font-weight:bold;color:red;font-size:15px;width:100%" value="" id="txtSTT5" placeholder="STT" disabled> -->
<!-- 				</div>	 -->
<!-- 				<div class="col-xs-8"> -->
<!-- 					<input type="text" class="form-control input-sm" style="font-weight:bold;color:green;font-size:12px;width:100%" value="" id="txtHOTEN5" placeholder="Họ tên" disabled> -->
<!-- 				</div>	 -->
<!-- 				<div class="col-xs-8" style="display:none"> -->
<!-- 					<input type="text" value = "" id="txtID5" disabled> -->
<!-- 					<input type="text" value = "" id="txtLANGOI5" disabled> -->
<!-- 					<input type="text" value = "" id="txtMABHYT5" disabled> -->
<!-- 					<input type="text" value = "" id="txtUUTIEN5" disabled> -->
<!-- 					<input type="text" value = "" id="txtMABENHNHAN5" disabled> -->
<!-- 					<input type="text" value = "" id="txtSOQUAY5" disabled> -->
<!-- 				</div>	 -->
<!-- 				<div style="display:none;"> -->
<!-- 				      <label>Choose voice</label> -->
<!-- 				      <select id="voices"></select> -->
<!-- 			    </div> -->
<!-- 			</div>					 -->
<!-- 			<div class="col-xs-5"> -->
<!-- 				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnGOITIEP5"> -->
<!-- 					 <span class="glyphicon glyphicon-volume-up"></span> Gọi tiếp -->
<!-- 				</button> -->
<!-- 				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnGOILAI5"> -->
<!-- 					 <span class="glyphicon glyphicon-refresh"></span> Gọi BN -->
<!-- 				</button> -->
<!-- 				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnLCDNHO5"> -->
<!-- 					 <span class="glyphicon glyphicon-picture"></span> Xem LCD -->
<!-- 				</button> -->
<!-- 				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnDSGOILAI5" style="display:none"> -->
<!-- 					 <span class="glyphicon glyphicon-list"></span> DS gọi lại -->
<!-- 				</button> -->
<!-- 			</div>	 -->
<!-- 			<div class="col-xs-3" style="font-size:14px;font-weight:bold;color:red;"> -->
<!-- 	<!-- 							<label id="txtMSGSTT"></label> -->  
<!-- 				<input type="text" class="form-control input-sm" style="font-weight:bold;color:red;font-size:14px;width:100%" value="" id="txtMSGSTT" placeholder="Gọi bệnh nhân" disabled> -->
<!-- 			</div>									   -->
<!-- 		</div> -->
<!-- 	</div> -->
<!-- 	<!-- END SONDN -->  
	<div id="divSearch" class="panel-body border-group-1 mgt5">
		<div class="col-xs-11 row">
			<div class="col-xs-12 row">
				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 ">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Tên kho</label>
							</div>
							<div class="col-xs-8 low-padding mgt3">
								 <div class="col-xs-3 low-padding mgt3" style="display: none" id = 'divTimKho'>
									<input class="form-control input-sm i-col-m_fl text-left"
										style="width: 100% ; " id="txtTimKho" name="txtTimKho" title=""  >
							 	</div>
							 	<div class="col-xs-9 low-padding mgt3">
									<select class="form-control input-sm" id="cboKho" style="width: 146%" ></select>
								</div>
								
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-4 low-padding">
								<label class="control-label mgl20">Từ ngày</label>
							</div>
							<div class="col-xs-8" id ="div_old_TN">
								<div class="input-group mgt3">
									<input class="form-control input-sm" id="txtNgayBD"
										name="txtNgayBD" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal"
										onclick="NewCssCal('txtNgayBD','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
							<div class="col-xs-8" id ="div_new_TN" style = "display:none;">
								<div class="input-group mgt3">
									<input class="form-control input-sm" id="txtNgayBDNew"
										name="txtNgayBDNew" title="" data-mask="00/00/0000 00:00:00"
										placeholder="dd/MM/yyyy hh:mm:ss"> <span
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal"
										onclick="NewCssCal('txtNgayBDNew','ddMMyyyy','dropdown',true,'24',true)"></span>
								</div>
							</div>
						</div>
					</div>
				</div>


				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Đến ngày</label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%" id ="div_old_DN">
									<input class="form-control input-sm" id="txtNgayKT"
										name="txtNgayKT" title="" data-mask="00/00/0000"
										placeholder="dd/MM/yyyy"> <span
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal"
										onclick="NewCssCal('txtNgayKT','ddMMyyyy','dropdown',false,'24',true)"></span>
								</div>
							</div>
							<div class="col-xs-8" id ="div_new_DN" style = "display:none;">
								<div class="input-group mgt3">
									<input class="form-control input-sm" id="txtNgayKTNew"
										name="txtNgayKTNew" title="" data-mask="00/00/0000 00:00:00"
										placeholder="dd/MM/yyyy hh:mm:ss"> <span
										class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
										type="sCal"
										onclick="NewCssCal('txtNgayKTNew','ddMMyyyy','dropdown',true,'24',true)"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-xs-12 row">
				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 mgt3">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Loại phiếu</label>
							</div>
							<div class="col-xs-8 low-padding">
								<select class="form-control input-sm" id="cboLoaiPhieu"
									style="width: 110%">
									<!-- <option value="0,1,2,3" selected>-- Tất cả --</option>
									<option value="0">Phiếu Nhập</option>
									<option value="1">Phiếu Xuất</option>
									<option value="2">YC Nhập</option>
									<option value="3">YC Xuất</option> -->
								</select>
							</div>
						</div>
					</div>
				</div>

				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-4 low-padding">
								<label class="control-label mgl20">Hình thức</label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%">
									<select class="form-control input-sm" id="cboHinhThuc"></select>
								</div>
							</div>
						</div>
					</div>
				</div>


				<div class="col-xs-4">
					<div class="row">
						
						<div class="col-xs-12 low-padding" id="ttDuyet">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Trạng thái</label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%">
									<select class="form-control input-sm" id="cboTrangThaiDuyet"></select>
								</div>
							</div>
						</div>
						<div class="col-xs-12 low-padding" id="ttDuyetLS" style="display:none;">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Trạng thái DLS</label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%">
									<select class="form-control input-sm" id="cboTrangThaiDuyetLS">
										  <option value="0,1,2">-- Toàn bộ --</option>
										  <option value="0">Đã duyệt</option>
										  <option value="1">Chưa duyệt</option>
										   <option value="2">Từ chối</option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-xs-12 low-padding" id="ttDuyetPT" style="display:none;">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Trạng thái </label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%">
									<select class="form-control input-sm" id="cboTrangThaiDuyetPT">
										  <option value="1,4">-- Toàn bộ --</option>
										  <option value="4">Đã phát</option>
										  <option value="1">Chưa phát</option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-xs-12 low-padding" id="ttDuyetVP" style="display:none;">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Duyệt VP </label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%">
									<select class="form-control input-sm" id="cboDuyetVP">
										  <option value="0" >-- Toàn bộ --</option>
										  <option value="1" selected >Đã duyệt</option>
										  <option value="2">Chưa duyệt</option>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
				
				
				
				
			</div>
			
			<div class="col-xs-12 row" id='divKhoa' style="display: none">
				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 mgt3">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Khoa</label>
							</div>
							<div class="col-xs-8 low-padding">
								<select class="form-control input-sm" id="cboKhoa"
									style="width: 110%">
									 
								</select>
							</div>
						</div>
					</div>
				</div>

				<div class="col-xs-4">
					<div class="row">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-4 low-padding">
								<label class="control-label mgl20">Phòng</label>
							</div>
							<div class="col-xs-8">
								<div class="input-group mgt3" style="width: 100%">
									<select class="form-control input-sm" id="cboPhong"></select>
								</div>
							</div>
						</div>
					</div>
				</div>
				
		 	</div>
			<div class="col-xs-12 row">
				<div class="col-xs-4" id = "divLYDOXUAT" style="display:none;">
					<div class="row">
						<div class="col-xs-12 mgt3">
							<div class="col-xs-4 low-padding">
								<label class="control-label">Lý do xuất</label>
							</div>
							<div class="col-xs-8 low-padding">
								<select class="form-control input-sm" id="cboLYDOXUAT"
									style="width: 110%">
								</select>
							</div>
						</div>
					</div>
				</div>
				
			</div>
		</div>

		<div class="col-xs-1 low-padding">
			<button type="button" class="btn btn-sm btn-primary" id="btnTimKiem"
				style="margin-top: 15px;">
				<span class="glyphicon glyphicon-search"></span>&nbsp;Tìm kiếm
			</button>
		</div>
	</div>

<div class="panel-body mgt5" id="dvCall5" >
		<div class="col-xs-12 low-padding">
			<div class="col-xs-4">
				<div class="col-xs-4">
					<input type="text" class="form-control input-sm" style="font-weight:bold;color:red;font-size:15px;width:100%" value="" id="txtSTT5" placeholder="STT" disabled>
				</div>	
				<div class="col-xs-8">
					<input type="text" class="form-control input-sm" style="font-weight:bold;color:green;font-size:12px;width:100%" value="" id="txtHOTEN5" placeholder="Họ tên" disabled>
				</div>	
				<div class="col-xs-8" style="display:none">
					<input type="text" value = "" id="txtID5" disabled>
					<input type="text" value = "" id="txtLANGOI5" disabled>
					<input type="text" value = "" id="txtMABHYT5" disabled>
					<input type="text" value = "" id="txtUUTIEN5" disabled>
					<input type="text" value = "" id="txtMABENHNHAN5" disabled>
					<input type="text" value = "" id="txtSOQUAY5" disabled>
				</div>	
				<div style="display:none;">
				      <label>Choose voice</label>
				      <select id="voices"></select>
			    </div>
			</div>					
			<div class="col-xs-5">
				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnGOITIEP5">
					 <span class="glyphicon glyphicon-volume-up"></span> Gọi tiếp
				</button>
				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnGOILAI5">
					 <span class="glyphicon glyphicon-refresh"></span> Gọi BN
				</button>
				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnLCDNHO5">
					 <span class="glyphicon glyphicon-picture"></span> Xem LCD
				</button>
				<button type="button" class="btn btn-sm btn-primary heightbtn" id="btnDSGOILAI5" style="display:none">
					 <span class="glyphicon glyphicon-list"></span> DS gọi lại
				</button>
			</div>	
			<div class="col-xs-3" style="font-size:14px;font-weight:bold;color:red;">
	<!-- 							<label id="txtMSGSTT"></label> -->
				<input type="text" class="form-control input-sm" style="font-weight:bold;color:red;font-size:14px;width:100%" value="" id="txtMSGSTT" placeholder="Gọi bệnh nhân" disabled>
			</div>									  
		</div>
	</div>

	<div class="col-md-12 low-padding mgt-5">
		<div class="col-md-5 low-padding">
			<table id="grdPhieu"></table>
			<div id="pager_grdPhieu"></div>
		</div>
		<div class="col-md-7 low-padding mgt10" id="divCT">
			<div class="col-md-12 low-padding" id="divCT_NGT">
				<div class="col-md-6 low-padding">
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15">Nơi lập</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtNOILAP"
								name="txtNOILAP" style="width: 100%" disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15">Ngày lập</label>
						</div>
						<div class="col-md-8 low-padding">
							<!-- <input class="form-control input-sm" id="txtNGAYNX" name="txtNGAYNX" style="width:100%" disabled> -->
							<input class="form-control input-sm" id="txtNGAYNX"
								name="txtNGAYNX" style="width: 100%"
								data-mask="00/00/0000 00:00:00" disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding" id = "divCDChinh">
						<div class="col-md-4 low-padding">
							<label class="mgl15 mgt10" id='lbCDChinh'>CĐ chính</label>
						</div>
						<!-- <div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtGHICHU" name="txtGHICHU" style="width:100%" disabled>
						</div> -->
						<div class="col-md-8 low-padding mgb3">
							<textarea class="form-control input-sm" rows="2" id="txtCDCHINH"
								name="txtCDCHINH" style="width: 100%; height: 51px !important;"
								disabled></textarea>
						</div>
					</div>
					<span id='spThongtin'>
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15" id ='lbSCT'>Số chứng từ</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtSOCHUNGTU"
								name="txtSOCHUNGTU" style="width: 100%" disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15 mgt15">Nguồn nhập/xuất</label>
						</div>
						<div class="col-md-8 low-padding mgb3">
							<textarea class="form-control input-sm" rows="2"
								id="txtNHACUNGCAP" name="txtNHACUNGCAP"
								style="width: 100%; height: 51px !important;" disabled></textarea>
						</div>
					</div>
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15">Phiếu yêu cầu</label>
						</div>
						<div class="col-md-3 low-padding">
							<input class="form-control input-sm" id="txtPHIEUYC"
								name="txtPHIEUYC" style="width: 100%" disabled>
						</div>
						<div class="col-md-4 low-padding">
							<label class="mgl5">Chiết khấu(%)</label>
						</div>
						<div class="col-md-1 low-padding">
							<input class="form-control input-sm" id="txtCHIETKHAU"
								name="txtCHIETKHAU" style="float: right; width: 150%" disabled>
						</div>
					</div>

					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15 mgt10" id='lbGhiChu'>Ghi chú</label>
						</div>
						<!-- <div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtGHICHU" name="txtGHICHU" style="width:100%" disabled>
						</div> -->
						<div class="col-md-8 low-padding mgb3">
							<textarea class="form-control input-sm" rows="2" id="txtGHICHU"
								name="txtGHICHU" style="width: 100%; height: 51px !important;"
								disabled></textarea>
						</div>
					</div>
					<!-- Begin: DucTT20181024 -->
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl15" id= 'lbBenhNhan'>Bệnh nhân</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtTENBENHNHAN"
								name="txtTENBENHNHAN" style="width: 100%" disabled>
						</div>
					
					</div>
					
					</span>
					<!-- End: DucTT20181024 -->
				</div>
				<div class="col-md-6 low-padding " >
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Người lập</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtNGUOINX"
								name="txtNGUOINX" style="width: 100%" disabled>
						</div>
					</div>
					<div>
					
					</div>
					
					<div class="col-md-12 low-padding">
						
						<div class="col-md-4 low-padding">
							<label class="mgl5">Mã phiếu</label>
						</div>
						<div class="col-md-6 low-padding">
							<input class="form-control input-sm" id="txtMA" name="txtMA"
								style="width: 100%" disabled>
						</div>
						
						<div class="col-md-1 low-padding " style="margin-left: 14px; height: 24px;">
							<button id="btnShowHide" type="button" class="btn btn-primary" aria-label="Center Align"> 
							<img src="../common/image/up.png" width="12px">
							</button>
							
						</div>
					 
					</div>
					<div class="col-md-12 low-padding" id = "divCDKemTheo">
						
						<div class="col-md-4 low-padding">
							<label class="mgl5">CĐ kèm theo</label>
						</div>
						<div class="col-md-8 low-padding">
							<textarea class="form-control input-sm" rows="2" id="txtKemTheo"
								name="txtKemTheo" style="width: 100%; height: 51px !important;"
								disabled></textarea>
						</div>
					 
					</div>
					<span id="spThongTin2">
					<div class="col-md-12 low-padding">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Ngày</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtNGAYCHUNGTU"
								name="txtNGAYCHUNGTU" style="width: 100%" disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding">
					
						<div class="col-md-4 low-padding">
							<label class="mgl5">Lần in</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtLANIN"
								name="txtLANIN" style="width: 100%" disabled>
						</div>
						
					</div>
					<div class="col-md-12 low-padding" name="divGia">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Tiền đơn (đ)</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtTIENDON"
								name="txtTIENDON" style="width: 100%; text-align: right;"
								disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding" name="divGia">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Tiền chiết khấu (đ)</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtTIENCHIETKHAU"
								name="txtTIENCHIETKHAU" style="width: 100%; text-align: right;"
								disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding" name="divGia">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Tổng cộng (đ)</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtTONGCONG"
								name="txtTONGCONG" style="width: 100%; text-align: right;"
								disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding" name="divGia">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Đã thanh toán (đ)</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtTONGTIENDATRA"
								name="txtTONGTIENDATRA" style="width: 100%; text-align: right;"
								disabled>
						</div>
					</div>
					<div class="col-md-12 low-padding" name="divBanThuoc">
						<div class="col-md-4 low-padding">
							<label class="mgl5" >Năm sinh</label>
							
						</div>
						<div class="col-md-3 low-padding">
							<input class="form-control input-sm" id="txtNamSinh"
								name="txtNamSinh" style="width: 100%" disabled>
						</div>
						<div class="col-md-2 low-padding">
							<label  >Giới tính</label>
						</div>
						<div class="col-md-3 low-padding">
							<input class="form-control input-sm" id="txtGioiTinh"
								name="txtGioiTinh" style="width: 100%" disabled>
						</div>
					</div>
					
					<div class="col-md-12 low-padding" name="divSoPhieu" style="display:none;">
						<div class="col-md-4 low-padding">
							<label class="mgl5">Số phiếu</label>
						</div>
						<div class="col-md-8 low-padding">
							<input class="form-control input-sm" id="txtSoPhieuIn"
								name="txtSoPhieuIn" style="width: 100%; text-align: right;"
								disabled>
						</div>
					</div>
					
					</span>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt5"
				style="padding-left: 15px !important; text-align: center;">
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnDuyetNhieuDon" >
					<span class="glyphicon glyphicon-ok"></span> Duyệt nhiều đơn
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnNhapKho" disabled>
					<span class="glyphicon glyphicon-ok"></span> Nhập kho
				</button>
				<!-- 				<button type="button" class="btn btn-sm btn-primary " style="" data-toggle="confirmation-singleton" data-placement="top" data-original-title title id="btnNhapKhoNCC" > -->
				<!-- 					<span class="glyphicon glyphicon-ok"></span> Nhập kho -->
				<!-- 				</button> -->
				<!-- 				<button type="button" class="btn btn-sm btn-primary " style="" data-toggle="confirmation-singleton" data-placement="top" data-original-title title id="btnXuatTraNCC" disabled> -->
				<!-- 					<span class="glyphicon glyphicon-ok"></span> Xuất trả -->
				<!-- 				</button> -->
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnXuatTra" disabled>
					<span class="glyphicon glyphicon-ok"></span> Xuất trả
				</button>

				<!-- 				<button type="button" class="btn btn-sm btn-primary " style="" data-toggle="confirmation-singleton" data-placement="top" data-original-title title id="btnDaThanhToan" disabled> -->
				<!-- 					<span class="glyphicon glyphicon-ok"></span> Đã thanh toán -->
				<!-- 				</button> -->
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnGoDuyet"
					data-toggle="modal" disabled>
					<span class="glyphicon glyphicon-remove"></span> Gỡ duyệt
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnGoTuChoi"
					data-toggle="modal" disabled>
					<span class="glyphicon glyphicon-remove"></span> Gỡ từ chối
				</button>
				
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnBoKhoa"
					data-toggle="modal" disabled>
					<span class="glyphicon glyphicon-remove"></span> Bỏ khóa
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnKySo" data-toggle="modal"
					>
					<span class="glyphicon glyphicon-add"></span> Ký số
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnHuyKySo" data-toggle="modal"
					>
					<span class="glyphicon glyphicon-add"></span> Hủy ký số
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnGoNhapkho" data-toggle="modal"
					disabled>
					<span class="glyphicon glyphicon-remove"></span> Hủy phiếu
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnHuyGuiPhieu" data-toggle="modal"
					disabled>
					<span class="glyphicon glyphicon-remove"></span> Hủy gửi phiếu
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnGuiDuyetHMIS" data-toggle="modal">
					<span class="glyphicon glyphicon-ok"></span> Gửi duyệt HMIS
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					style="display: none;" id="btnGuiFast" data-toggle="modal">
					<span class="glyphicon glyphicon-ok"></span> Gửi tích hợp
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnInPhieu" disabled>
					<span class="glyphicon glyphicon-print"></span> In phiếu
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnXemPhieu" disabled>
					<span class="glyphicon glyphicon-search"></span> Xem phiếu
					nhập/xuất
				</button>
				
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnInPhieuBBKiemKe" style="display: none;">
					<span class="glyphicon glyphicon-print"></span> In bb kiểm kê
				</button>

				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnDonThuoc" disabled
					style="display: none;">
					<span class="fa fa-file-text"></span> Đơn thuốc/VT
				</button>
				<!-- DucTT20181105 -->
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnHuyGuiDuyet"
					style="display: none;">
					<span class="fa fa-file-text"></span> Huỷ gửi duyệt
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnHuyGiuAo"
					style="display: none;">
					<span class="fa fa-file-text"></span> Huỷ giữ ảo
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnInDonThuoc"
					style="display: none;">
					<span class="fa fa-file-text"></span> In đơn thuốc
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnInBangKe"
					style="display: none;">
					<span class="fa fa-file-text"></span> In bảng kê
				</button>
				<!--  <button type="button" class="btn btn-sm btn-primary " style="display: none;" id="btnXoaDonMuaNgoai" data-toggle="modal" disabled >
					<span class="glyphicon glyphicon-remove"></span> Hủy phiếu thuốc
				</button> -->
				
				<!-- ChuanNT hoa hop -->
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnMauHoaHop"
					style="display: none;">
					<span class="fa fa-file-text"></span> Truyền máu hòa hợp
				</button>
				<button type="button" class="btn btn-sm btn-primary "
					data-toggle="confirmation-singleton" data-placement="top"
					data-original-title title id="btnDuyetDT"
					style="display: none;" 
					<span class="fa fa-file-text"></span> Duyệt đơn thuốc
				</button>
			</div>

			<div class="col-md-12 low-padding"
				style="padding-left: 15px !important;">
				<table id="grdThuoc"></table>
				<div id="pager_grdThuoc"></div>


				<div class="contextMenu" id="contextMenu"
					style="display: none; width: 250px;">
					<ul style="width: 250px; font-size: 65%;">
						<li id="selCopyPhieu"><span class="ui-icon ui-icon-pencil"
							style="float: left"></span> <span
							style="font-size: 130%; font-family: Verdana">Sao chép
								Phiếu</span></li>
					</ul>
				</div>

				<input type="hidden" id="copy" value=""> <input
					type="hidden" id="hidEdit" value="0"> <input type="hidden"
					id="hid_NHAPXUATID" value="0"> <input type="hidden"
					id="hid_KHOID" value="0">
				<div class="col-md-12 low-padding" style="width: 647px;">
					<iframe id="ifmXLS" name="ifmXLS"
						style="width: 100%; height: 100%; min-height: 500px; margin-left: -2px !important; display: none">
					</iframe>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	var userInfo = CommonUtil.decode('{userData}');
	var paramInfo = CommonUtil.decode('{paramData}');

	//var subdept_id = paramInfo.subdept_id;
	//var dept_id = paramInfo.dept_id;
	initRest(userInfo.UUID, "/vnpthis");
	initAjax("/vnpthis");
	
	//var _par_Check= ['PHARMA_CHECK_LIENTHONG_HMIS'];
	//var PHARMA_CHECK_LIENTHONG_HMIS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
	//		_par_Check.join('$'));
	//if(PHARMA_CHECK_LIENTHONG_HMIS == '1'){
		ajaxSvc.register("HMIS_PHAMAR");	
	//}
	//ajaxSvc.register("CongDLYTWS");
	var db_schema = '{db_schema}';
	var user_gr_id = '{user_group_id}';
	var user_name = '{user_name}';

	var _opt = new Object();
	_opt.lk = paramInfo.lk;
	_opt.lp = paramInfo.lp;
	_opt.ht = paramInfo.ht;
	_opt.tt = paramInfo.tt;
	_opt.cs = paramInfo.cs;
	_opt.type = paramInfo.type;
	_opt.gd = paramInfo.gd;
	_opt.cs = paramInfo.cs;
	_opt.td = paramInfo.td;
	_opt.lt = paramInfo.lt;
	_opt.subdept = paramInfo.subdept_id;
	_opt.dept = paramInfo.dept_id;
	_opt.LDXUAT = paramInfo.LDXUAT;
	_opt.backColor = [ "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF",
			"#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF",
			"#FFFFFF", "#FFFFFF", "#FFFFFF" ];
	_opt.imgPath = [ "Edit.png", "", "", "", "Circle_Yellow.png",
			"Circle_Green.png", "Circle_Red.png", "", "", "", "",
			"Shopping_cart.png", "Shopping_cart.png", "","","","","","","",  "Flag_Red_New.png"    ];
	
	_opt.foreColor = [ "#000000", "#000000", "#000000", "#808080", "#000000",
			"#000000", "#000000", "#000000", "#000000", "#000000", "#000000",
			"#FF0000", "#800000", "#000000" ];

	var mode = '{showMode}';
	var objVar;
	if (mode == 'dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		objVar = EventUtil.getVar("dlgVar");
		//_opt._chidinhdichvu = objVar.chidinhdichvu; 
		//_opt._loaidichvu = objVar.loaidichvu;
		//_opt._loaiphieumbp = objVar.loaiphieumbp; 
		//_opt._benhnhanId = objVar.benhnhanid;
		//_opt._khambenhId = objVar.khambenhid;
		//_opt._tiepnhanId = objVar.tiepnhanid;
		//_opt._hosobenhanId = objVar.hosobenhanid;
		//_opt._doituongbenhnhanId = objVar.doituongbenhnhanid;
		//_opt._loaitiepnhanId = objVar.loaitiepnhanid;
		//_opt._maubenhphamId = objVar.maubenhphamid; 
		//_opt._maubenhphamId = '1869';
		//_opt._maubenhphamId = '1878';
		//_opt._maubenhphamId = '1880';
		$("#hid_NHAPXUATID").val(objVar.nhapxuatid);
		$("#hid_KHOID").val(objVar.khoid);
		$("#copy").val(objVar.copy);
		$("#hidEdit").val(objVar.edit);
	}
	_opt.hospitalId = userInfo.HOSPITAL_ID;
	_opt.user_gr_id = user_gr_id;
	_opt.user_name = user_name;
	_opt.i_sch = db_schema;
	var p = new DUC01S002_PhieuYeuCau(_opt);
	p.load();
</script>