/*
 * Nhập kết quả chẩn đoán hình ảnh
 * 
 * VietDA	04/08/2016		tạo mới 
 * 
 */

function KetQuaCDHA(opt){
	
	var timeMoment = '';
	this.load = doLoad;	
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		initControl();
		bindEvent();

		/* dữ liệu nhận từ form cha Danh sách CDHA */
		$("#hdfIDMauBenhPham").val(opt.maubenhphamid);
		
		console.log("id mau benh pham="+opt.maubenhphamid);
		
		loadDanhSachKetQua();
		
	}

	function initControl(){
		$.extend($.jgrid.inlineEdit, {
			keys: true
		});
		
		$("#grdKetQuaCDHA").jqGrid({
		  datatype: 'local',
		  colNames: ['<PERSON><PERSON><PERSON> thực hiện','<PERSON><PERSON><PERSON> vị','<PERSON><PERSON><PERSON> đoán', '<PERSON>ên dịch vụ', 'Kết luận', 'Kết quả', 'Bác sỹ thực hiện',
		             'MAUBENHPHAMID','KETQUACLSID','DICHVUTHUCHIENID','DICHVUKHAMBENHID'],
		  colModel: [
			  {name: 'NGAYMAUBENHPHAM_HOANTHANH', width: 120, formatter:GridUtil.fm.formatStrEncode },
			  {name: 'TENKHOA', width: 120, formatter:GridUtil.fm.formatStrEncode },
			  {name: 'CHANDOAN', width: 120, editable: true},
			  {name: 'TENDICHVU', width: 250, editable: true},
			  {name: 'KETLUAN', width: 200 },
			  {name: 'KETQUACLS', width: 350 },
			  {name: 'FULL_NAME', width: 120, formatter:GridUtil.fm.formatStrEncode },
		      {name: 'MAUBENHPHAMID', hidden:true},
			  {name: 'KETQUACLSID', hidden:true},
			  {name: 'DICHVUTHUCHIENID', hidden:true},
			  {name: 'DICHVUKHAMBENHID', hidden:true}
		  ],
		  rowNum: 20,
		  rowList: [20, 50, 100],
		  pager: '#pager_grdKetQuaCDHA',
		  gridview: true,
		  ignoreCase: true,
		  rownumbers: true,
		  sortname: 'invdate',
		  viewrecords: true,
		  sortorder: 'desc',
		  width:"100%",
		  autowidth: true,
		  editurl: 'clientArray',
		  caption: 'Danh sách chỉ định'
		});
          
		$("#grdKetQuaCDHA").jqGrid('navGrid', '#pager_grdKetQuaCDHA', {edit: false, add: false, del: false});	    
		$("#grdKetQuaCDHA").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		$('#gview_grdKetQuaCDHA').find('.ui-jqgrid-bdiv').attr("style","height: 520px !important");
		GridUtil.setWidthPercent("grdKetQuaCDHA","100%");
		
		$(".ui-jqgrid-titlebar").hide();
	}
	
	function bindEvent(){
		$("#grdKetQuaCDHA").jqGrid("setGridParam",{	
			onSelectRow: function (rowid){
				GridUtil.unmarkAll("grdKetQuaXetNghiem");
				GridUtil.markRow("grdKetQuaXetNghiem", rowid);				  
			},		
			ondblClickRow: function(rowid) {						
				console.log("rowid: "+ rowid);				
			}
		});		
	}
	
	function loadDanhSachKetQua(){	
		var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val();
		
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C019.LSTH", param);
		
		$("#grdKetQuaCDHA").jqGrid("clearGridData");
		$("#grdKetQuaCDHA")[0].grid.beginReq();
		$("#grdKetQuaCDHA").jqGrid("setGridParam", { data: data_ar });
		$("#grdKetQuaCDHA")[0].grid.endReq();
		$("#grdKetQuaCDHA").trigger("reloadGrid");		
	}	
}
