<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC017_BBKIEMKETHUOC_11DBV01_TT22_A4" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="9307372b-f4de-490d-ac99-d0d1f62e7218">
	<property name="ireport.zoom" value="1.2100000000000108"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="99"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="nhapxuatid" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="DUC017_BBKIEMKENHAPTVT_SUB" class="net.sf.jasperreports.engine.JasperReport"/>
	<queryString language="plsql">
		<![CDATA[{call duc017_bbkiemnhap_tvt($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{nhapxuatid},$P{ora_cursor})}]]>
	</queryString>
	<field name="TENKHO" class="java.lang.String"/>
	<field name="SOCHUNGTU" class="java.lang.String"/>
	<field name="TENNCC" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="SOLO" class="java.lang.String"/>
	<field name="DONVI" class="java.lang.String"/>
	<field name="SOKIEMSOAT" class="java.lang.String"/>
	<field name="NUOCSX" class="java.lang.String"/>
	<field name="HANDUNG" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="THANHTIEN" class="java.math.BigDecimal"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="DONGIAVAT" class="java.math.BigDecimal"/>
	<field name="TONGTIENCHU" class="java.lang.String"/>
	<field name="NGAYNX" class="java.lang.String"/>
	<field name="QUYETDINH" class="java.lang.String"/>
	<field name="NGAYCHUNGTU" class="java.lang.String"/>
	<field name="CHUCDANHCHUKY" class="java.lang.String"/>
	<field name="NGAYQD" class="java.lang.String"/>
	<variable name="tongtien" class="java.math.BigDecimal" resetType="Group" resetGroup="TENKHO" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<variable name="tong" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<group name="TENKHO">
		<groupExpression><![CDATA[$F{TENKHO}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="555" height="20"  uuid="50fe161f-2d24-4886-97a2-0f56be5b5492"/>
					<box leftPadding="5">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["1.  " + ($F{SOCHUNGTU} == null ? " ":$F{SOCHUNGTU}) + " - " + ($F{NGAYCHUNGTU} == null ? " ":$F{NGAYCHUNGTU})]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="281" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="50" width="555" height="23" uuid="01807dfa-1e32-435e-a7f3-0339d0b4b173"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[BIÊN BẢN KIỂM NHẬP HÀNG HÓA]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="20" width="220" height="20" uuid="10acfa4d-66f8-40ac-800a-5839dbfc2455"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="1" width="220" height="19" isRemoveLineWhenBlank="true" uuid="9c5f0e3f-2d21-4a97-bffa-83940ac9ac6d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="236" width="555" height="20" uuid="3a22d3a4-e4eb-4b0a-971d-a3b8a9d613d6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chúng tôi đã tiến hành kiểm nhập lô hàng do: " + "<b>" + ($F{TENNCC} == null ? " ":$F{TENNCC}) + "</b>" + " cung ứng."]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="322" y="0" width="233" height="19" uuid="618f1ade-6280-4919-9b1d-de3c52df3092"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="322" y="20" width="233" height="20" uuid="70d8bb2f-487e-4c98-9387-3c32cb17881e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐỘC LẬP - TỰ DO - HẠNH PHÚC]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="73" width="555" height="23" uuid="13e5e1c9-50ad-48a3-b257-7e4a4bcadaae"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{QUYETDINH} == null ? " " : ("Căn cứ theo QĐ số: " + $F{QUYETDINH}))
+ ($F{NGAYQD} == null ? " " : (" ngày " + (new SimpleDateFormat("dd")).format(new SimpleDateFormat("dd/MM/yyyy").parse($F{NGAYQD}))+ " tháng " + (new SimpleDateFormat("MM")).format(new SimpleDateFormat("dd/MM/yyyy").parse($F{NGAYQD})) + " năm " + (new SimpleDateFormat("yyyy")).format(new SimpleDateFormat("dd/MM/yyyy").parse($F{NGAYQD}))))
+ ($F{QUYETDINH} == null ? " " : " của Giám đốc " + $P{org_name} )]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="139" width="555" height="20" uuid="513d434e-4d87-49c4-9d6e-12e61f51fea0"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Tại: " + "<b>"  + $P{org_name} + "</b>" + ", Chúng tôi gồm :"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="256" width="555" height="20" uuid="ea6970b4-6e49-468c-bd27-65f85b35413c"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["- Nhập vào kho: " + "<b>" + ($F{TENKHO} == null ? " ":$F{TENKHO}) + "</b>" + ". Với kết quả như sau"]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="159" width="555" height="72" uuid="14bf88b3-6900-4e50-bcf8-1807bc68b09d"/>
				<subreportParameter name="org_name">
					<subreportParameterExpression><![CDATA[$P{org_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[HID]">
					<subreportParameterExpression><![CDATA[$P{[HID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[UID]">
					<subreportParameterExpression><![CDATA[$P{[UID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="parent_name">
					<subreportParameterExpression><![CDATA[$P{parent_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="nhapxuatid">
					<subreportParameterExpression><![CDATA[$P{nhapxuatid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[SCH]">
					<subreportParameterExpression><![CDATA[$P{[SCH]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dept_name">
					<subreportParameterExpression><![CDATA[$P{dept_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ora_cursor">
					<subreportParameterExpression><![CDATA[$P{ora_cursor}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[IP]">
					<subreportParameterExpression><![CDATA[$P{[IP]}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{DUC017_BBKIEMKENHAPTVT_SUB}]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="0" y="119" width="555" height="20" uuid="8cd105d3-d351-4409-bae7-ef260b106cb8"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Hôm nay, vào hồi "+ (new SimpleDateFormat("HH")).format(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse($F{NGAYNX}))+" giờ "+ (new SimpleDateFormat("mm")).format(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse($F{NGAYNX}))+" phút ngày " + (new SimpleDateFormat("dd")).format(new SimpleDateFormat("dd/MM/yyyy").parse($F{NGAYNX}))+" tháng " + (new SimpleDateFormat("MM")).format(new SimpleDateFormat("dd/MM/yyyy").parse($F{NGAYNX})) + " năm "+ (new SimpleDateFormat("yyyy")).format(new SimpleDateFormat("dd/MM/yyyy").parse($F{NGAYNX}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="96" width="555" height="20" uuid="cb6269a1-cd6c-4d3f-ae8e-781ef443a8cc"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["V/v: Thành lập hội đồng kiểm nhập thuốc, vật tư tiêu hao, hóa chất xét nghiệm của " + $P{org_name}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="391" y="43" width="100" height="1" uuid="12cbc6ed-979f-40d5-b9a0-2403c82f1048"/>
			</line>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="56" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="23" height="56" uuid="376918c7-43c2-4fcf-a5ad-c3bcb1eb2446"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="142" y="0" width="40" height="56" uuid="b51a71d7-8ed5-49fb-b814-b41b30875c2e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số đăng ký ]]></text>
			</staticText>
			<staticText>
				<reportElement x="483" y="0" width="72" height="56" uuid="3fa1630d-734a-4264-bcc4-ca15592c4138"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Hãng, nước SX]]></text>
			</staticText>
			<staticText>
				<reportElement x="23" y="0" width="85" height="56" uuid="e36d2dad-2a3b-48c1-9413-573d7edc97e8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tên (hàng hóa), nồng độ, hàm lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="182" y="0" width="38" height="56" uuid="39a0ed45-74c0-4ef4-a240-68184a59d723"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="0" width="52" height="56" uuid="48180a3e-866f-4929-b6f0-be2d79d03fb5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="272" y="0" width="50" height="56" uuid="da27ff70-1d7b-4f1f-94eb-8c36e589bf34"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn giá VAT]]></text>
			</staticText>
			<staticText>
				<reportElement x="431" y="0" width="52" height="56" uuid="52be9556-48bb-41d6-8506-0bb11d37ddb6"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Hạn dùng]]></text>
			</staticText>
			<staticText>
				<reportElement x="391" y="0" width="40" height="56" uuid="ac28233f-d03f-445f-b078-b812b1055d2d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lô]]></text>
			</staticText>
			<staticText>
				<reportElement x="108" y="0" width="34" height="56" uuid="29f8326d-96fe-47b9-8e7b-8a6072367585"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn vị tính ]]></text>
			</staticText>
			<staticText>
				<reportElement x="322" y="0" width="69" height="56" uuid="a5cb27dc-c9e0-41cd-b786-aa5bf25b16ec"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="23" height="20"  uuid="c5bc6414-d531-4f88-a833-cfb202dbfab3"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="23" y="0" width="85" height="20"  uuid="e765cb04-7c11-432e-adec-e5d499e3b0ab"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="431" y="0" width="52" height="20"  uuid="500be96b-8307-4d85-a841-e8587c5270af"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HANDUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="220" y="0" width="52" height="20"  uuid="dfd9e0e3-cc2a-40a0-acee-0ef5ecc32b8e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{DONGIA} == null ? 0 : $F{DONGIA}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="182" y="0" width="38" height="20"  uuid="2ad25267-8f9b-487e-ae54-10ec8380747c"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{SOLUONG} == null ? 0 : $F{SOLUONG}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="322" y="0" width="69" height="20"  uuid="fc161ac6-02c1-40df-b0fe-1ca76d2a20cb"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{THANHTIEN} == null ? 0 : $F{THANHTIEN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="483" y="0" width="72" height="20"  uuid="9334205a-c9e0-46cf-a426-49b862628000"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUOCSX}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="108" y="0" width="34" height="20"  uuid="63c29b48-fe28-4c20-9e4c-8b9c982f3fd6"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONVI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="142" y="0" width="40" height="20"  uuid="3e1505db-a866-48c1-b841-16855af97890"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOKIEMSOAT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="391" y="0" width="40" height="20"  uuid="9db7d4fa-5680-4953-8f71-fe11479ad5e4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="272" y="0" width="50" height="20"  uuid="c3f76973-0de0-4da6-93ff-5cc288035e25"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{DONGIAVAT} == null ? 0 : $F{DONGIAVAT}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="180" splitType="Stretch">
			<staticText>
				<reportElement x="92" y="96" width="90" height="30" uuid="c51dc992-8a22-4e81-ba3b-7ce32d9c506f"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI NHẬN]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="96" width="92" height="30" uuid="936ae4fa-76f5-414f-b171-d7640bad47b5"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI GIAO]]></text>
			</staticText>
			<staticText>
				<reportElement x="182" y="96" width="90" height="30" uuid="12e8fa2f-1f47-4b59-bd8f-6c0a0bf441df"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[KẾ TOÁN THEO DÕI]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="322" y="0" width="69" height="20"  uuid="652d58fb-3c6e-4926-b8f3-53528f89b5e8"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tong} == null ? 0 : $V{tong}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="322" height="20"  uuid="120360ae-8aa9-44cd-a994-4a46b3555230"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="391" y="0" width="164" height="20"  uuid="4168d54d-410c-4ad1-80fb-91170e43b4e6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="31" width="555" height="20" uuid="afcdadcb-6aa1-4905-aaba-108f9d1ca3bf"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng cộng: <b>1</b> Hóa đơn và <b>" + $V{REPORT_COUNT} + "</b> mặt hàng"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="51" width="555" height="20" uuid="ac3f9990-e553-42c0-af08-84a2fe97711b"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Thành tiền (VAT): " + ($F{TONGTIENCHU} == null ? " ":$F{TONGTIENCHU})+ " đồng chẵn"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="272" y="96" width="92" height="30" uuid="2b9f1bf6-f9ea-45b6-b90c-24ac28eb486d"/>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[THỐNG KÊ]]></text>
			</staticText>
			<staticText>
				<reportElement x="364" y="96" width="92" height="30" uuid="df469790-d809-46fe-baa1-63672a9a7094"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG PHÒNG TC_KT]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="456" y="96" width="99" height="30" uuid="c82a19e7-1e77-425c-bb60-11e92679594d"/>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="html">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-V" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHUCDANHCHUKY}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
