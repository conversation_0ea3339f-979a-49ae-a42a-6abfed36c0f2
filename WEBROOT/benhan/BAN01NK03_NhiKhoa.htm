<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js?v=211118"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>


<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />

<link rel="stylesheet" href="../common/css/custom_ban.css"></link>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jquery/jquery.confirm.js"></script>
<link href="../common/css/custom.css"/>
<link rel="stylesheet" href="../common/css/custom.css">
			<!-- <link rel="stylesheet" href="../noitru/custom_nt.css"> -->
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../noitru/jquery.bootstrap-growl.min.js"></script>
<script type="text/javascript" src="../benhan/BAN_Common.js?v=17"></script>
<script type="text/javascript" src="../benhan/BAN01NK03_NhiKhoa.js?v=20200117"></script>
<script type="text/javascript" src="../noitru/NTU02D023_Thongtinhanhchinh.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>


<script type="text/javascript">	
	var opt = [];
	var lang = '{lang}';
	var hospital_id = '{hospital_id}';
	var province_id = '{province_id}';
	var ma_tinh_id = '{ma_tinh}';
	var company_id = '{company_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_id);
	var sys_par = [];
	var ma_lk = '{ma_lk}';
	var _mode = '{mode}';
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var _param = session_par;
</script>

<style>
	.form-inline {
		overflow: auto ;
	}
</style>
<div width="100%" id="divMain-BA" style="border: 0px;">
	<ul id="tabs" class="nav nav-tabs" style="margin-top: 10px;" data-tabs="tabs">
		<li class="active" id="liBenhAnHoiKham"><a href="#tabBenhAnHoiKham" data-toggle="tab">A.Bệnh án hỏi khám bệnh</a></li>
		<li id="liBenhAnTongKet"><a href="#tabBenhAnTongKet" data-toggle="tab">B.Tổng kết hồ sơ bệnh án</a></li>
		<li id="liBenhNhanThongTin"><a href="#tabBenhNhanThongTin" data-toggle="tab">C.Thông tin bệnh nhân</a></li>
	</ul>	

	<div id="my-tab-content" class="tab-content">
	    <div><a href="#" class="scrollToTop before"></a></div>    
		<div id="tabBenhAnHoiKham" class="tab-pane active mgb10" style="padding-bottom: 10px;">
			
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
					<div class="panel-heading">I. Lý do vào viện:</div>
					<div class="panel-body">
						<div class="form-inline">
							<div class="col-xs-2 low-padding form-group required">
								<label>Lý do vào viện:</label>
							</div>
							<div class="col-xs-7 low-padding">
								<input class="form-control input-sm" id="txtLYDOVAOVIEN"
									maxlength="500" name="txtLYDOVAOVIEN" title="" valrule="Lý do vào viện,trim_required|max_length[1500]"
									style="width: 100%;">
							</div>
							<div class="col-xs-3 low-padding">
								<label class='mgl10'>Vào ngày thứ:</label> <input valrule="Vào ngày thứ,is_natural|max_length[3]"
									class="form-control input-sm " style="width: 45%;"
									id="txtVAONGAYTHU" name="txtVAONGAYTHU" title=""> <span>
								</span> <label class='l-col-3' style="float: right;">của bệnh</label>
							</div>
						</div>
					</div>
			</div> <!-- END FORM INLINE -->
				
				
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">II. Hỏi bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Quá trình bệnh
								lý:(khởi phát, diễn biến, chẩn đoán, điều trị của tuyến dưới
								v.v...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtQTBENHLY" valrule="Quá trình bệnh lý,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtQTBENHLY"
								style="height: 80px !important; width: 100%" maxlength="1500"
								rows="4"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>2. Tiền sử bệnh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Bản thân: (phát
								triển thể lực từ nhỏ đến lớn, những bệnh đã mắc, phương pháp
								ĐTr, tiêm phòng, ăn uống, sinh hoạt vv...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtBANTHAN" valrule="Bản thân,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtBANTHAN"
								style="height: 70px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>				
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>+ Gia đình: (Những
								người trong gia đình: bệnh đã mắc, đời sống, tinh thần, vật
								chất v.v...)</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtGIADINH" valrule="Gia đình,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtGIADINH"
								style="height: 70px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>3. Quá trình sinh trưởng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
						    <div class="col-xs-2 low-padding"> 
							 	<label class='control-label'>Con thứ mấy</label>
							</div>
							<div class="col-xs-10 low-padding"> 
								<div class="col-xs-1 low-padding">
								  <input class="form-control input-sm" id="txtCONTHUMAY"
										maxlength="10" name="txtCONTHUMAY" title="" style="width: 100%;">
								</div>
								<div class="col-xs-1 low-padding">
								</div>
								<div class="col-xs-2 low-padding">
								    <label class='control-label'>- Tiền thai (Para)</label>							    
								</div>
								<div class="col-xs-8  low-padding" id="dvPARACHECK"  >
									<input class="form-control input-sm i-col-3" id="chkSINHDUTHANG"  multiple="multiple"
										   maxlength="1" name="chkSINHDUTHANG" title="" type="checkbox">
									<span></span>
									<input class="form-control input-sm i-col-3" id="chkSOMDENON"  multiple="multiple"
										   maxlength="1" name="chkSOMDENON" title="" type="checkbox">
									<span></span>
									<input class="form-control input-sm i-col-3" id="chkSAYNAOHUT"  multiple="multiple"
										   maxlength="1" name="chkSAYNAOHUT" title="" type="checkbox">
									<span></span>
									<input class="form-control input-sm i-col-3" id="chkSONG" multiple="multiple"
										   maxlength="1" name="chkSONG" title="" type="checkbox">

									<label class='control-label'>( Sinh (đủ tháng), Sớm (đẻ non), Sẩy (nạo, hút), Sống )</label>
								</div>
								<div class="col-xs-8 low-padding" id="dvPARANHAP" style="display: none" >
									<div class="col-xs-4 low-padding">
										<div class="col-xs-3 low-padding">
											<input class="form-control input-sm" id="txtPARASINH" style="width: 50%"
												   name="txtPARASINH" title="" type="text">
										</div>
										<div class="col-xs-3 low-padding">
											<input class="form-control input-sm"
												   id="txtPARASOM" name="txtPARASOM" title="" style="width: 50%"
												   type="text">
										</div>
										<div class="col-xs-3 low-padding">
											<input class="form-control input-sm"
												   id="txtPARASAY" name="txtPARASAY" title="" style="width: 50%"
												   type="text">
										</div>
										<div class="col-xs-3 low-padding">
											<input class="form-control input-sm"
												   id="txtPARASONG" name="txtPARASONG" title="" style="width: 50%"
												   type="text">
										</div>
									</div>
									<div class="col-xs-8 low-padding">
										<label class='mgl5'>(Sinh (đủ tháng), Sớm (đẻ non), Sẩy (nạo, hút), Sống) </label>
									</div>
								</div>
							</div>
						</div>
					</div>
			         <div class=" form-inline">
						<div class="col-xs-12 low-padding">
						   <div class="col-xs-2 low-padding">
						     <label class='control-label'>- Tình trạng khi sinh:</label>
						   </div>
						   	<div class="col-xs-10 low-padding">								
							   <div class="col-xs-2 low-padding">
							      <input class="" id="chkDETHUONG"
														maxlength="1" name="chkDETHUONG" title="" type="checkbox"> 1.Đẻ thường
							   </div>
							   <div class="col-xs-2 low-padding">
							      <input class="" id="chkFORCEPS"
														maxlength="1" name="chkFORCEPS" title="" type="checkbox"> 2.Forceps
							   </div>
							   <div class="col-xs-2 low-padding">
							      <input class="" id="chkGIACHUT"
														maxlength="1" name="chkGIACHUT" title="" type="checkbox"> 3.Giác hút          
							   </div>
							    <div class="col-xs-2 low-padding">
							      <input class="" id="chkDEPHAUTHUAT"
														maxlength="1" name="chkDEPHAUTHUAT" title="" type="checkbox"> 4.Đẻ phẫu thuật          
							   </div>
							   <div class="col-xs-2 low-padding">
							      <input class="" id="chkDECHIHUY"
														maxlength="1" name="chkDECHIHUY" title="" type="checkbox"> 5.Đẻ chỉ huy          
							   </div>
							   <div class="col-xs-2 low-padding">
							      <input class="" id="chkKHAC"
														maxlength="1" name="chkKHAC" title="" type="checkbox"> 6.Khác        
							   </div>
						    </div>
						</div>
					</div>
					
						
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-2 low-padding">
						    	<label class='control-label'>- Cân nặng lúc sinh:</label>
						   	</div>
						   	<div class="col-xs-10 low-padding">
								<div class="col-xs-1 low-padding">
								  	<input class="form-control input-sm" id="txtCANNANGLUCSINH"
								  	valrule="Cân nặng lúc sinh,numeric|max_length[10]"								  								  	
									maxlength="10" name="txtCANNANGLUCSINH" title="" style="width: 100%;">
								</div>
								
								<div class="col-xs-1 low-padding"><label class="mgl5">kg</label></div>
								
							   	<div class="col-xs-2 low-padding">
							    	<input class="" id="chkDITATLUCSINH"
														maxlength="1" name="chkDITATLUCSINH" title="" type="checkbox"> Dị tật bẩm sinh          
							   	</div>
							   
							   <div class="col-xs-2 low-padding">
							     	<label class='control-label;'>Cụ thể tật bẩm sinh:</label>
							   </div>
							   <div class="col-xs-6 low-padding">
								  <input class="form-control input-sm" id="txtCUTHETATBAMSINH"
										maxlength="200" name="txtCUTHETATBAMSINH" title="" style="width: 100%;">									
								</div>
							</div>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
						   <div class="col-xs-2 low-padding">
						     <label class='control-label'>- Phát triển về tinh thần:</label>
						   </div>
						   <div class="col-xs-10 low-padding">
						     <input class="form-control input-sm" id="txtPHATTRIENTINHTHAN"
									maxlength="200" name="txtPHATTRIENTINHTHAN" title="" style="width: 100%;">
						   </div>
						</div>
					</div>	
			         
					<div class=" form-inline" style="height: 3px">
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
						   <div class="col-xs-2 low-padding">
						     <label class='control-label'>- Phát triển về vận động:</label>
						   </div>
						   <div class="col-xs-10 low-padding">
						     <input class="form-control input-sm" id="txtPHATTRIENVANDONG"
									maxlength="200" name="txtPHATTRIENVANDONG" title="" style="width: 100%;">
						   </div>
						</div>
					</div>	
					<div class=" form-inline" style="height: 3px">
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
						   <div class="col-xs-2 low-padding">
						     <label class='control-label'>- Các bệnh lý khác: </label>
						   </div>
						   <div class="col-xs-10 low-padding">
						     <input class="form-control input-sm" id="txtBENHLYKHAC"
									maxlength="200" name="txtBENHLYKHAC" title="" style="width: 100%;">
						   </div>
						</div>
					</div>	
					<div class=" form-inline" style="height: 3px">
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<div class="col-xs-2 low-padding">
						     	<label class='control-label'>- Nuôi dưỡng: </label>
						   	</div>
						   	<div class="col-xs-10 low-padding">
							   	<div class="col-xs-2 low-padding">
							        <input class="" id="chkNUOISUAME"
														maxlength="1" name="chkNUOISUAME" title="" type="checkbox">
									<label class='control-label'>1. Sữa mẹ</label>     
							   	</div>
							   	<div class="col-xs-2 low-padding">
							        <input class="" id="chkNUOINHANTAO"
														maxlength="1" name="chkNUOINHANTAO" title="" type="checkbox">        
							   		<label class='control-label'>2. Nuôi nhân tạo</label>
							   	</div>
							   	<div class="col-xs-2 low-padding">
							        <input class="" id="chkNUOIHONHOP"
														maxlength="1" name="chkNUOIHONHOP" title="" type="checkbox">        
							   		<label class='control-label'>3. Hỗn hợp</label>
							   	</div>
							   	<div class="col-xs-2 low-padding">
							   		<div class="col-xs-9 low-padding">
							     		<label class='control-label'>- Cai sữa tháng thứ</label>
							     	</div>
							     	<div class="col-xs-3 low-padding">
							     		<input class="form-control input-sm" id="txtTHANGCAISUA"
							     		valrule="Cai sữa tháng thứ,is_natural|max_length[2]"
										maxlength="2" name="txtTHANGCAISUA" title="" style="width: 100%;">
							     	</div>
							   	</div>
							</div>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
						  <div class="col-xs-2 low-padding">
						     <label class='control-label'>- Chăm sóc: </label>
						   </div>
						   <div class="col-xs-10 low-padding">
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkCSTAIVUONGTRE"
														maxlength="1" name="chkCSTAIVUONGTRE" title="" type="checkbox">        
							   		<label class='control-label'>1. Tại vườn trẻ</label>
							   </div>
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkCSTAINHA"
														maxlength="1" name="chkCSTAINHA" title="" type="checkbox">        
							   		<label class='control-label' >2. Tại nhà</label>
							   </div>
							</div>
						</div>
					</div>	
				    	<div class=" form-inline">
					  <div class="col-xs-12 low-padding">
						   <div class="col-xs-2 low-padding">
						     	<label class='control-label'>- Đã tiêm chủng: </label>
						   </div>
						   <div class="col-xs-10 low-padding">
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkTIEMLAO"
														maxlength="1" name="chkTIEMLAO" title="" type="checkbox">        
							   		<label class='control-label' >1. Lao</label>
							   </div>
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkTIEMBAILIET"
														maxlength="1" name="chkTIEMBAILIET" title="" type="checkbox">        
							   		<label class='control-label' >2. Bại liệt</label>
							   </div>
							   <div class="col-xs-1 low-padding">
							        <input class="" id="chkTIEMSOI"
														maxlength="1" name="chkTIEMSOI" title="" type="checkbox">        
							   		<label class='control-label' >3. Sởi</label>
							   </div>
							   <div class="col-xs-1 low-padding">
							        <input class="" id="chkTIEMHOGA"
														maxlength="1" name="chkTIEMHOGA" title="" type="checkbox">        
							   		<label class='control-label' >4. Ho gà</label>
							   </div>
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkTIEMUONVAN"
														maxlength="1" name="chkTIEMUONVAN" title="" type="checkbox">        
							   		<label class='control-label' >5. Uốn ván</label>
							   </div>
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkTIEMBACHHAU"
														maxlength="1" name="chkTIEMBACHHAU" title="" type="checkbox">        
							   		<label class='control-label' >6. Bạch hầu</label>
							   </div>
							   <div class="col-xs-2 low-padding">
							        <input class="" id="chkTIEMCHUNGKHAC"
														maxlength="1" name="chkTIEMCHUNGKHAC" title="" type="checkbox">        
							   		<label class='control-label' >7. Tiêm chủng khác</label>
							   </div>
						</div>
					</div>   		
				</div>
				<div class=" form-inline">
					  <div class="col-xs-12 low-padding">
					     <div class="col-xs-2 low-padding">
						     <label class='control-label'>- Những bệnh khác được tiêm chủng: </label>
						  </div>
						  <div class="col-xs-10 low-padding">
						     <input class="form-control input-sm" id="txtCUTHETIEMCHUNGKHAC"
									maxlength="200" name="txtCUTHETIEMCHUNGKHAC" title="" style="width: 100%;"> 
						  </div>
					  </div>
				</div>  
				</div>	
			</div>	
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">III. Khám bệnh:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3 control-label'>1. Toàn thân: (ý
								thức, da niêm mạc, hệ thống hạch, tuyến giáp, vị trí, kích
								thước, số lượng, di động v.v...)</label>
						</div>
					</div>
					<div class=" form-inline">					    		
						<div class="col-xs-9 low-padding">
						    <div class="col-xs-12 low-padding">
						        <div class="col-xs-6 low-padding">
						        	<div class="col-xs-3 low-padding"></div>
						        	<div class="col-xs-2 low-padding">
						        		<label class='control-label'>- Chiều cao</label>
						        	</div>
						        	<div class="col-xs-3">
						        		<div class="col-xs-8 low-padding">
							        		<input class="form-control input-sm" id="txtCHIEUCAOLUCSINH"
							        		valrule="Chiều cao,is_natural|max_length[10]"
											maxlength="10" name="txtCHIEUCAOLUCSINH" title="" style="width: 100%;">
										</div>
										<div class="col-xs-3 low-padding">
											<label class="mgl5">cm</label>
										</div>
									</div>
									<div class="col-xs-1"></div>
									<div class="col-xs-3">
										<label class='control-label'>- Vòng ngực</label>
									</div>
						     	</div>
						     <div class="col-xs-1 low-padding">
						        <div class="col-xs-9 low-padding">	
						        	<input class="form-control input-sm" id="txtVONGNGUCLUCSINH"
						        	valrule="Vòng ngực,is_natural|max_length[10]"
									maxlength="10" name="txtVONGNGUCLUCSINH" title="" style="width: 100%;">
								</div>
								<div class="col-xs-3 low-padding">
									<label class="mgl5">cm</label>
								</div>
						     </div>
						     <div class="col-xs-1"></div>
						     <div class="col-xs-1 low-padding">
						        <label class='control-label'>- Vòng đầu</label>
						     </div>
						     <div class="col-xs-1 low-padding">
						     	<div class="col-xs-9 low-padding">	
						        	<input class="form-control input-sm" id="txtVONGDAULUCSINH"
						        	valrule="Vòng đầu,is_natural|max_length[10]"
									maxlength="10" name="txtVONGDAULUCSINH" title="" style="width: 100%;">
								</div>
								<div class="col-xs-3 low-padding">
									<label class="mgl5">cm</label>
								</div>
						     </div>
						     <div class="col-xs-1"></div>
						    </div>
						    <div class="col-xs-12 low-padding" style="height: 3px">
						    </div>
						    <div class="col-xs-12 low-padding">
						      <textarea name="txtTOANTHAN" valrule="Toàn thân,max_length[1000]"
								class="form-control input-sm i-col-3" id="txtTOANTHAN"
								style="height: 103px !important; width: 100%" maxlength="1000"
								rows="3"></textarea>
						    </div>							
						</div>
						<div class="col-xs-3 low-padding">
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class="mgl10">Mạch</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input class="col-xs-4 form-control input-sm " style="width: 100%;" id="txtMACH" name="txtMACH" valrule="Mạch,is_natural|max_length[3]" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhiệt độ:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhiệt độ,numeric|max_length[6]" maxlength="6"
												class="form-control input-sm " style="width: 100%;"
												id="txtNHIETDO" name="txtNHIETDO" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>C</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Huyết áp:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Huyết áp,is_natural|max_length[3]"
												class="form-control input-sm " style="width: 45%;"
												id="txtHUYETAP1" name="txtHUYETAP1" title=""> /
									 <input class="form-control input-sm " style="width: 45%; float: right;"
												valrule="Huyết áp,is_natural|max_length[3]"
												id="txtHUYETAP2" name="txtHUYETAP2" title="">	
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>mmHg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Nhịp thở:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Nhịp thở,is_natural|max_length[3]"
									class="form-control input-sm " style="width: 100%;"
									id="txtNHIPTHO" name="txtNHIPTHO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>lần/ph</label>
								</div>
							</div>
							
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Cân nặng:</label> 
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Cân nặng,numeric|max_length[6]" maxlength="6"
									class="form-control input-sm " style="width: 100%;"
									id="txtCANNANG" name="txtCANNANG" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>kg</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chiều cao:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<input valrule="Chiều cao,numeric|max_length[4]"
										   class="form-control input-sm " style="width: 100%;" maxlength="4"
										   id="txtCHIEUCAO" name="txtCHIEUCAO" title="">
								</div>
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>cm</label>
								</div>
							</div>
							<div class="col-xs-12 low-padding">
								<div class="col-xs-4 low-padding">
									<label class='mgl10'>Chỉ số BMI:</label>
								</div>
								<div class="col-xs-4 low-padding">
									<label id="txtBMI" name="txtBMI" title=""></label>
								</div>
							</div>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>2. Các cơ quan:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Tuần hoàn:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTUANHOAN"
								class="form-control input-sm i-col-3" id="txtTUANHOAN"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Hô hấp:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHOHAP" class="form-control input-sm i-col-3"
								id="txtHOHAP" style="height: 50px !important; width: 100%"
								maxlength="1500" rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Tiêu hóa:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTIEUHOA"
								class="form-control input-sm i-col-3" id="txtTIEUHOA"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>					
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Thận - Tiết niệu- Sinh dục:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTHANTIETNIEUSINHDUC"
								class="form-control input-sm i-col-3"
								id="txtTHANTIETNIEUSINHDUC"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Thần kinh:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTHANKINH"
								class="form-control input-sm i-col-3" id="txtTHANKINH"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Cơ -Xương -Khớp:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtCOSUONGKHOP"
								class="form-control input-sm i-col-3" id="txtCOSUONGKHOP"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Tai - Mũi - Họng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTAIMUIHONG"
								class="form-control input-sm i-col-3" id="txtTAIMUIHONG"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Răng - Hàm - Mặt:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtRANGHAMMAT"
								class="form-control input-sm i-col-3" id="txtRANGHAMMAT"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Mắt:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtMAT" class="form-control input-sm i-col-3"
								id="txtMAT" style="height: 50px !important; width: 100%"
								maxlength="1500" rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>+ Nội tiết, dinh dưỡng và các
								bệnh lý khác:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtNTDDKHAC"
								class="form-control input-sm i-col-3"
								id="txtNTDDKHAC"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Các xét nghiệm cận lâm sàng
								cần làm:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class=" form-inline">
							<div class="col-xs-11 low-padding">
							<textarea name="txtXETNGHIEMCLS"
								class="form-control input-sm i-col-3"
								id="txtXETNGHIEMCLS"
									style="height: 60px !important; width: 100%" maxlength="2000"
								rows="3"></textarea>
							</div>
							<div class="col-xs-1 low-padding" style="text-align: left;">
								<button id="btnTONGHOPCLS" class="btn btn-sm btn-primary"
										style="width: 100% !important;">Tổng hợp KQCLS</button>
							</div>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Tóm tắt bệnh án:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTTBENHAN"
								class="form-control input-sm i-col-3" id="txtTTBENHAN"
								style="height: 60px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">IV. Chẩn đoán khi vào khoa điều trị:</div>
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Bệnh chính:</label>
						</div>
						<div class="col-xs-1 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHCHINH" name="txtMABENHCHINH" valrule="Mã bệnh chính,max_length[500]" title="" attrIcd="0" targetCtr="txtBENHCHINH">
						</div>
						<div class="col-xs-6 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHCHINH" name="txtBENHCHINH" valrule="Tên bệnh chính,max_length[500]" title="">
						</div>
						<div class="col-xs-3 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtGHICHU_BENHCHINH" name="txtGHICHU_BENHCHINH"
								   placeholder="Ghi chú bệnh chính"
								   valrule="Tên bệnh chính,max_length[1000]" title="">
						</div>
					</div>
				</div>
				
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Bệnh kèm theo:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMABENHKEMTHEO" name="txtMABENHKEMTHEO" valrule="Mã bệnh kèm theo,max_length[500]" title="" attrIcd="1" targetCtr="txtBENHKEMTHEO">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtBENHKEMTHEO" name="txtBENHKEMTHEO" valrule="Tên bệnh kèm theo,max_length[500]" title="">
						</div>
					</div>
				</div>
				
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-2 low-padding form-group">
							<label>+ Phân biệt:</label>
						</div>
						<div class="col-xs-2 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtMAPHANBIET" name="txtMAPHANBIET" valrule="Mã phân biệt,max_length[500]" title="">
						</div>
						<div class="col-xs-8 low-padding">
							<input class="form-control input-sm" style="width: 100%;" id="txtPHANBIET" name="txtPHANBIET" valrule="Tên phân biệt,max_length[500]" title="">
						</div>
					</div>
				</div>
				<div class="panel-body">
					<div class="form-inline">
						<div class="col-xs-6 low-padding form-group">
							<div class="col-xs-2 low-padding form-group">
								<label>+ Tai biến</label>
							</div>
							<div class="col-xs-2 low-padding">
								<input type="checkbox" title="" id ="chkTAIBIEN" name ="chkTAIBIEN">
							</div>
							<div class="col-xs-2 low-padding form-group">
								<label>+ Biến chứng</label>
							</div>
							<div class="col-xs-2 low-padding">
								<input type="checkbox" title="" id ="chkBIENCHUNG" name ="chkBIENCHUNG">
							</div>
						</div>
					</div>
				</div>
				<div class="form-inline">
					<div class="col-xs-2 low-padding">
						<label>- Phẫu thuật</label>
						<input type="checkbox" id="chkPHAUTHUAT" name="chkPHAUTHUAT" title="">
					</div>
					<div class="col-xs-2 low-padding">
						<label>- Thủ thuật</label>
						<input type="checkbox" id="chkTHUTHUAT" name="chkTHUTHUAT" title="">
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			

			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">V. Tiên lượng:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTIENLUONG"
								valrule="Tiên lượng,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtTIENLUONG"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div> <!-- END FORM INLINE -->

			<div class="form-inline panel panel-info" style="margin-bottom: 65px;">
				<div class="panel-heading">VI. Hướng dẫn điều trị:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHDDIEUTRI" valrule="Hướng dẫn điều trị,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtHDDIEUTRI"
								style="height: 50px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
			
		</div>		
		
		<div id="tabBenhAnTongKet" class="tab-pane mgb10" style="padding-bottom: 10px;">
	
			<div class="form-inline panel panel-info" style="margin-bottom: 5px;">
				<div class="panel-heading">TỔNG KẾT BỆNH ÁN:</div>
				<div class="panel-body">
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>1. Quá trình bệnh lý và diễn biến
								lâm sàng:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtBENHLYDBLS"
								valrule="Quá trình bệnh lý và diễn biến lâm sàng,max_length[2000]"
								class="form-control input-sm i-col-3"
								id="txtBENHLYDBLS"
								style="height: 70px !important; width: 100%" maxlength="2000"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>2. Tóm tắt kết quả xét nghiệm cận
								lâm sàng có giá trị chẩn đoán:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-11 low-padding">
							<textarea name="txtKQXNCLS"
								valrule="Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán,max_length[2000]"
								class="form-control input-sm i-col-3" id="txtKQXNCLS"
								style="height: 60px !important; width: 100%" maxlength="2000"
								rows="3"></textarea>
						</div>
						<div class="col-xs-1 low-padding" style="text-align: left;">
							<button id="btnKQCLS" class="btn btn-sm btn-primary"
									style="width: 100% !important;">Chọn KQCLS</button>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>3. Phương pháp điều trị:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtPPDIEUTRI"
								valrule="Phương pháp điều trị,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtPPDIEUTRI"
								style="height: 60px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>

					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>4. Tình trạng người bệnh ra viện:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtTTNBRAVIEN" 
								valrule="Tình trạng người bệnh ra viện,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtTTNBRAVIEN"
								style="height: 60px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<label class='l-col-3'>5. Hướng điều trị và các chế độ
								tiếp theo:</label>
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-12 low-padding">
							<textarea name="txtHDTVACDT"
								valrule="Hướng điều trị và các chế độ tiếp theo,max_length[1500]"
								class="form-control input-sm i-col-3" id="txtHDTVACDT"
								style="height: 60px !important; width: 100%" maxlength="1500"
								rows="3"></textarea>
						</div>
					</div>
					<div class="form-inline" >
						<div class="col-xs-2 low-padding">
							<label class='l-col-3'>6. Giải phẫu bệnh(khi có sinh thiết)</label>
						</div>
						<div class="col-xs-2 low-padding">
							<label>1. Lành tính</label>
							<input type="checkbox" id="chkLANHTINH" name="chkLANHTINH" title="">
						</div>
						<div class="col-xs-2 low-padding">
							<label>2. Nghi ngờ</label>
							<input type="checkbox" id="chkNGHINGHO" name="chkNGHINGHO" title="">
						</div>
						<div class="col-xs-2 low-padding">
							<label>3. Ác tính</label>
							<input type="checkbox" id="chkACTINH" name="chkACTINH" title="">
						</div>
					</div>
					<div class="form-inline" >
						<div class="col-xs-2 low-padding">
							<label class='l-col-3'>7. Tình hình tử vong</label>
						</div>
						<div class="col-xs-1 low-padding">
							<label class='l-col-3 mgl5'>thời gian tử vong: </label>
						</div>
						<div class=" col-xs-2 input-group" >
							<input type="text" class="form-control input-sm" id="cldNGAYGIOTUVONG" valrule="Giờ ngày,datetime"
								   name="cldNGAYGIOTUVONG" data-mask="00/00/0000 00:00:00" minlength="19" placeholder="dd/MM/yyyy HH24:mm:ss" placeholder="">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="imgFROM_DT" type="sCal" onclick="NewCssCal('cldNGAYGIOTUVONG','ddMMyyyy','dropdown',true,'24',true)"></span>
						</div>
					</div>
					<div class="form-inline" >
						<div class="col-xs-2 low-padding">
						</div>
						<div class="col-xs-2 low-padding">
							<label>1. Do bệnh</label>
							<input type="checkbox" id="chkDOBENH" name="chkDOBENH" title="">
						</div>
						<div class="col-xs-2 low-padding">
							<label>2. Do tai biến điều trị</label>
							<input type="checkbox" id="chkDOTAIBIEN" name="chkDOTAIBIEN" title="">
						</div>
						<div class="col-xs-2 low-padding">
							<label>3. Khác</label>
							<input type="checkbox" id="chkTUVONGKHAC" name="chkTUVONGKHAC" title="">
						</div>
					</div>
					<div class="form-inline" >
						<div class="col-xs-2 low-padding">
						</div>
						<div class="col-xs-2 low-padding">
							<label>1. Trong 24h vào viện</label>
							<input type="checkbox" id="chkTRONG24H" name="chkTRONG24H" title="">
						</div>
						<div class="col-xs-2 low-padding">
							<label>2. Sau 24h vào viện</label>
							<input type="checkbox" id="chkSAU24H" name="chkSAU24H" title="">
						</div>
					</div>
					<div class="form-inline" >
						<div class="col-xs-2 low-padding">
							<label>8. Khám nghiệm tử thi</label>
							<input type="checkbox" id="chkKHAMNGHIEMTUTHI" name="chkKHAMNGHIEMTUTHI" title="">
						</div>
					</div>

					<div class=" form-inline mgt5">
						<div class="col-xs-2 low-padding">
							<label class='l-col-3'> Người giao hồ sơ:</label>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtHOTEN_GIAOHS"
								   maxlength="500" name="txtHOTEN_GIAOHS" title=""
								   valrule="Người giao hồ sơn,max_length[500]"
								   style="width: 100%;">
						</div>
						<div class="col-xs-2 low-padding">
							<label class='l-col-3 mgl2'> Người nhận hồ sơ:</label>
						</div>
						<div class="col-xs-4 low-padding">
							<input class="form-control input-sm" id="txtHOTEN_NHANHS"
								   maxlength="500" name="txtHOTEN_NHANHS" title=""
								   valrule="Người nhận hồ sơn,max_length[500]"
								   style="width: 100%;">
						</div>
					</div>
					<div class=" form-inline">
						<div class="col-xs-1 low-padding" style="width: 2px"></div>
						<div class="col-xs-11 low-padding" style="width: 100%;margin-bottom: 55px">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th colspan="2"
											style="font-size: x-small; text-align: center;">Hồ sơ,
											phim, ảnh</th>
									</tr>
									<tr>
										<th style="font-size: x-small; text-align: center;">Loại</th>
										<th style="font-size: x-small; text-align: center;">Số
											tờ</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td><label class='l-col-1 control-label'>- X -
												quang</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số X - Quang,is_natural|max_length[5]"
											id="txtSOXQUANG" name="txtSOXQUANG" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- CT
												Scanner</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số CT Scanner,is_natural|max_length[5]"
											id="txtSOSCANNER" name="txtSOSCANNER" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Siêu
												âm</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số Siêu âm,is_natural|max_length[5]"
											id="txtSOSIEUAM" name="txtSOSIEUAM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Xét
												nghiệm</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số Xét nghiệm,is_natural|max_length[5]"
											id="txtSOXETNGHIEM" name="txtSOXETNGHIEM" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>-
												Khác……………</label></td>
										<td><input class="form-control input-sm i-col-3"
											valrule="Số khác,is_natural|max_length[5]"
											id="txtSOKHAC" name="txtSOKHAC" title=""></td>
									</tr>
									<tr>
										<td><label class='l-col-1 control-label'>- Toàn
												bộ hồ sơ</label></td>
										<td><input class="form-control input-sm i-col-3"
											id="txtSOTOANBOHOSO" name="txtSOTOANBOHOSO" title=""
											disabled="disabled"></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div> <!-- END FORM INLINE -->
		</div>
		
		<div id="tabBenhNhanThongTin" class="tab-pane"></div>
		
		<div class="form-inline btn-fixed">
			<div class="col-xs-12 low-padding mgt10" style="text-align: right;" id="gridButton">
				<div class="col-xs-7 low-padding">
					<button class="btn btn-sm btn-primary" id="btnLuu">
						<span class="glyphicon glyphicon-pencil"></span> Lưu
					</button>
					<button class="btn btn-sm btn-primary" id="btnThemMoi">
						<span class="glyphicon glyphicon-floppy-remove"></span> Lưu & Đóng
					</button>
					<button class="btn btn-sm btn-primary" id="btnInBieuMau">
						<span class="fixDisplay glyphicon glyphicon-print"></span> In biểu mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btn_Close">
						<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
					<button class="btn btn-sm btn-primary" id="btnLuuBAMau">
						<span class="glyphicon glyphicon-pencil"></span> Lưu BA mẫu
					</button>
					<button class="btn btn-sm btn-primary" id="btnXoaBAMau">
						<span class="glyphicon glyphicon-pencil"></span> Xóa BA mẫu
					</button>					
				</div>
				<div class="col-xs-5 low-padding mgt3">
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Tên BA mẫu:</label>
					</div>
					<div class="col-xs-3 low-padding">
						<input class="form-control input-sm" id="txtTENBENHANMAU"
							   maxlength="500" name="txtTENBENHANMAU" title="" valrule="Tên bệnh án mẫu,max_length[500]"
							   style="width: 100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Bệnh án mẫu:</label>
					</div>
					<div class="col-xs-1 low-padding">
						<input class="form-control input-sm" id="txtTKTENBENHANMAU"
							   name="txtTKTENBENHANMAU" title="" 
							   style="width: 100%;">
					</div>
					<div class="col-xs-4 low-padding">
						<div class="input-group" style="width:100%">
							<select class="form-control input-sm" id="cboMAUBAID" filterlike="txtTKTENBENHANMAU" style="width: 100%;" reffld="TENBAMAU" >
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding mgt5 mgb5" style="text-align: center;">
				<div class="col-xs-7 low-padding" style="text-align: center;">
					<button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
						<span class="glyphicon glyphicon-ok"></span> Ký & In
					</button>
					<button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
						<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
					</button>
					<button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
						<span class="glyphicon glyphicon-print"></span> In ký số
					</button>
				</div>
			</div>
		</div>
	</div>
</div>
		<script>
			var opt = [];
			var hospital_id = '{hospital_id}';
			var user_id = '{user_id}';
			var dept_id = '{dept_id}';
			var user_type = '{user_type}';
			var province_id = '{province_id}';
			var uuid = '{uuid}';
			var db_schema='{db_schema}';
			var phongid = '{subdept_id}';	
			var lang = "vi";
			console.log('hospital_id=' + hospital_id + ' user_type='
					+ user_type);
			var session_par = [];
			session_par[0] = hospital_id;
			session_par[1] = user_id;
			session_par[2] = user_type;
			session_par[3] = province_id;
			session_par[4] = db_schema;
			var table_name = '{table}';

			var _opts = new Object();
			var mode = '{showMode}';				
			if(mode=='dlg') {		
				parent.DlgUtil.tunnel(DlgUtil.moveEvent);
				data=EventUtil.getVar("dlgVar");		
				_opts.khambenhid = data.khambenhid;
				_opts.hosobenhanid = data.hosobenhanid;		
				_opts.benhnhanid = data.benhnhanid;
				_opts.loaibenhanid = data.loaibenhanid;
				_opts.maloaibenhan = data.maloaibenhan;
				_opts.khoaid=dept_id;
				_opts.phongid=phongid;
			}
		    initRest(_opts._uuid);

		    _opts._param = session_par;
			_opts._uuid = uuid;
			initRest(_opts._uuid);
			var BAN01_NK03 = new BAN01NK03_NhiKhoa(_opts);
			BAN01_NK03.load();
		</script>