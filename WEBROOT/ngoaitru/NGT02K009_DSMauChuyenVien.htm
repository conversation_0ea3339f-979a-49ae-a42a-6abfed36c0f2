<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
       value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
        src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../plugin/jquery.textboxLookup.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.storageapi.js"></script>


<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
      rel="stylesheet" />

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>

<script type="text/javascript"
        src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript"
        src="../ngoaitru/NGT02K009_DSMauChuyenVien.js?v=20210428"></script>

<div id="" class="container">
    <div class="col-md-12 low-padding">
        <div class="col-md-4 low-padding mgt-10">
            <table id="grdMau"></table>
        </div>
    </div>
    <div class="col-md-12 low-padding mgb10" style="text-align: center;">
        <button id="btnOK" class="btn btn-sm btn-primary" >
            <span class="glyphicon glyphicon-ok"></span> Chọn
        </button>
        <button id="btnClose" class="btn btn-sm btn-primary">
            <span class="glyphicon glyphicon-remove-circle"></span> Đóng
        </button>
    </div>
</div>
<script>
    //var uuid = '{uuid}';
    var userInfo = CommonUtil.decode('{userData}');
    initRest(userInfo.UUID, "/vnpthis");
    var _opt = new Object();
    //truyen cac tham so tu ngoai vao
    _opt.khoaId = '{dept_id}';
    var objVar;
    var mode = '{showMode}';
    if (mode == 'dlg') {
        parent.DlgUtil.tunnel(DlgUtil.moveEvent);
        objVar=EventUtil.getVar("dlgVar");
        _opt.csytid = objVar.csytid;
    }
    var cvm = new NGT02K009_DSMauChuyenVien(_opt);
    cvm.load();
</script>