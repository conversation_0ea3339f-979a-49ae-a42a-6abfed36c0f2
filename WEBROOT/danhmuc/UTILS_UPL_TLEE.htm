<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	   value="{company_id}" />
<style>
	::placeholder {
		color: #bfbfbf !important;
	}
</style>
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
		src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
		src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
		src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
		src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	  href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	  rel="stylesheet" />
<script type="text/javascript"
		src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
		src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>


<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
		src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript"
		src="../danhmuc/UTILS_UPL_TLEE.js"></script>
<div id="loading" class="hide">
    <span class="spinner"></span>
</div>
<div width="100%" id="divMain" class="container">
	<div class="col-md-12 low-padding mgt5 mgb5">
		<h3>Upload File</h3>
	</div>
	<div class="col-md-12 low-padding mgt5 mgb5" style="margin-bottom: 15px !important;">
		<div class="col-md-3 low-padding"></div>
		<div class="col-md-6 low-padding">
			<div class="col-md-2 low-padding">
				<label class="mgl5" style="width: 105%;">Type: </label>
			</div>
			<div class="col-md-7 low-padding">
				<select class="form-control input-sm kb-i-col-m "
						id="cboPATH_CHOOSE"
						style="width: 100%;height: 30px !important;">
					<option value="" selected="selected">-- Chọn --</option>
					<option value="JASPER">Jasper báo cáo</option>
					<option value="CANLAMSANG">Cận lâm sàng</option>
					<option value="NGOAITRU">Ngoại trú</option>
					<option value="NOITRU">Nội trú</option>
					<option value="DUOC">Dược</option>
					<option value="DANHMUC">Danh mục</option>
					<option value="VIENPHI">Viện phí</option>
					<option value="BENHAN">Bệnh án</option>
					<option value="CHIDAOTUYEN">Chỉ đạo tuyến</option>
					<option value="CLBV">Chất lượng bệnh viện</option>
					<option value="KSK">Khám sức khỏe</option>
					<option value="NGHIENCUUKHOAHOC">Nghiên cứu khoa học</option>
					<option value="EMRXML">XML EMR</option>
					<option value="SIGNCA">Ký số</option>
<!--					<option value="COMMON">Jasper/jrxml</option>-->
				</select>
			</div>
			<div class="col-md-3 low-padding">
				<button style="width: 80%;" class="btn btn-info" id="browserFolderBtn" disabled="disabled">
					<span class="glyphicon glyphicon-folder-open"></span>
					&nbsp
					Browser
				</button>
			</div>
		</div>
		<div class="col-md-3 low-padding"></div>
	</div>
	<div class="col-md-12 low-padding mgt-5 mgb20" id="downloadArea" style="display: none;">
		<div class="col-md-3 low-padding"></div>
		<div class="col-md-6 low-padding">
			<div class="col-md-2 low-padding">
				<label class="mgl5" style="width: 105%;">File name: </label>
			</div>
			<div class="col-md-7 low-padding">
				<input type="text" style="width: 100%" id="fileDownload"/>
			</div>
			<div class="col-md-3 low-padding">
				<button style="width: 80%;" class="btn btn-info" id="downloadBtn" disabled="disabled">
					<span class="glyphicon glyphicon-download-alt"></span>
					&nbspDownload
				</button>
			</div>
		</div>
		<div class="col-md-3 low-padding"></div>
	</div>
	<div class="col-md-12 low-padding mgt-5" id="inputFilesArea">
		<div class="col-md-3 low-padding"></div>
		<div class="col-md-6 low-padding">
			<input type="file" id="fileUpload" multiple/>
			<pre id="fileUploadName" class="raw">Browser...</pre>
			<button class="btn btn-success" id="uploadBtn" disabled="disabled">Upload</button>
		</div>
		<div class="col-md-3 low-padding"></div>
	</div>
</div>
<style>
	.ui-menu .ui-menu-item {
		border-bottom: 1px solid #d4d4d4;
	}

	.ui-menu.ui-autocomplete > li:nth-child(even) {
		background-color: #e5e4ff;
	}

	h3 {
		text-align: center;
		color: red;
	}
	hr {
		border-top: 1px solid #bfbfbf;
	}
	#results {
		height: 200px;
		overflow-y: scroll;
	}
	#results p {
		margin: 0;
		font-size: 12px;
		font-family: serif;
	}

	#results p.success {
		color: blue;
	}

	#results p.error {
		color: red;
	}

	#fileUpload {
		display: none;
	}
	#fileUploadName {
		height: 300px;
		font-family: serif;
		background-color: white;
		border: 1px solid #6a6a6a;
	}
	#inputFilesArea {
		margin-top: 40px !important;
	}
	#fileUploadName.raw{
		text-align: center;
		padding-top: 80px;
		font-size: 26px;
		color: blue;
	}
	#fileUploadName.data{
		text-align: left;
		padding-top: 0px;
		font-size: 14px;
		color: #000000bf;
	}

    @keyframes spinner {
        to {transform: rotate(360deg);}
    }

    #loading {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #0000007a;
    }

    #loading.show{
        display: block;
        z-index: 999;
    }

    #loading.hide{
        display: none;
        z-index: -1;
    }

    .spinner:before {
        content: '';
        box-sizing: border-box;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 40px;
        height: 40px;
        margin-top: -20px;
        margin-left: -20px;
        border-radius: 50%;
        border: 4px solid #000;
        border-top-color: #fff;
        animation: spinner 1s linear infinite;
    }
</style>
<script>
	var paramInfo=CommonUtil.decode('{paramData}');
	var opt = [];
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var lang = "vi";
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	session_par[4] = paramInfo.rpt_schema;
	var table_name = '{table}';

	initRest(uuid, "/vnpthis");

	var _opts = new Object();
	_opts.lang = lang;
	_opts._param = session_par;
	_opts._uuid = uuid;
	var UtilUploadReport = new UtilUploadReport(_opts);
	UtilUploadReport.load(hospital_id);
</script>