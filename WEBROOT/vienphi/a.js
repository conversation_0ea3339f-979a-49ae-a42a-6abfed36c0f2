$("#toolbarIdbtnYCChiHo").on("click", function() {
	var VPI_CHIHO_QRCODE = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CHIHO_QRCODE');
	if (VPI_CHIHO_QRCODE != '1') {
		DlgUtil.showMsg("Không hỗ trợ hoàn trả thanh toán QRCODE");
		return;
	}
	var opt = new Object;
	opt.ACTION = "TAO_YC"
	opt.TIEPNHANID = $("#hidTIEPNHANID").val();
	opt.objData = new Object();
	var dlgName = "divDlgdivRefundQR";
	opt.dlgName = dlgName;
	var dlgPopup = DlgUtil.buildPopupUrl(dlgName, "divRefundQR", "manager.jsp?func=../vienphi/VPI01T034_hoantra_qrocde", opt, "Hoàn trả thanh toán <PERSON>RCOD<PERSON>", 1000, 365);
	DlgUtil.open(dlgName);
});