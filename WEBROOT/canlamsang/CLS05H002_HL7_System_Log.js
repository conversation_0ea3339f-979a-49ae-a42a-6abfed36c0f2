/*
 * Log HL7 Inbound, Outbound
 * 
 * VietDA 	05/12/2020		Tạo mới
 * 
 */

var ctl_ar=[
	{type:'button',id:'btnConfig',icon:'glyphicon glyphicon-cog',text:'HL7 Config'},
	{type:'button',id:'btnAuto',icon:'glyphicon glyphicon-tasks',text:'Automator'},
	{type:'button',id:'btnMaker',icon:'glyphicon glyphicon-envelope',text:'Msg Maker'},
	{type:'button',id:'btnLog',icon:'glyphicon glyphicon-list-alt',text:'Msg Log'},
	{type:'label',id:'lblInfo',icon:'',text:'HL7 System Log'}
];

function HL7Log(opt) {
	this.load = doLoad;
	
	function doLoad(){		
		initControl();
		bindEvent();
		
		$("#txtTuNgay").val(moment().format('DD/MM/YYYY'));
		$("#txtDenNgay").val(moment().format('DD/MM/YYYY'));
		
		var callback = function () {
            if ($.active !== 0) {
                setTimeout(callback, '50');
                return;
            }
            loadData();
        };
        callback();
	}

	function initControl(){		
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		ToolbarUtil.build('toolbarId', ctl_ar);
		var _toolbar=ToolbarUtil.getToolbar('toolbarId');	
		
		$("#grdThongKeLog").jqGrid({
			datatype: 'local',
			colNames: ['ID','SID','MESSAGE','RESPONSE','STATUS','TIMEPOST','REMOTE_HOST', 'PORT','CSYTID'],
			colModel: [
			    {name:'ID', width: 30, hidden: true },
				{name:'SID', width: 90, formatter:GridUtil.fm.formatStrEncode },
			    {name:'MESSAGE', width: 630, formatter:GridUtil.fm.formatStrEncode },
			    {name:'RESPONSE', width: 200, formatter:GridUtil.fm.formatStrEncode },
				{name:'STATUS', width: 60, formatter:GridUtil.fm.formatStrEncode },
			    {name:'TIMEPOST', width: 120, formatter:GridUtil.fm.formatStrEncode },
			    {name:'REMOTE_HOST', width: 100, formatter:GridUtil.fm.formatStrEncode },
			    {name:'REMOTE_PORT', width: 80, formatter:GridUtil.fm.formatStrEncode },
			    {name:'CSYTID', hidden: true }
			  ],
			  rowNum: 10,
			  rowList: [10, 50, 100],
			  pager: '#pager_grdThongKeLog',
			  gridview: true,
			  ignoreCase: true,
			  rownumbers: true,
			  viewrecords: true,
			  sortorder: 'desc',
			  width : 1136,
			  shrinkToFit: false,
			  editurl: 'clientArray',
			  caption: ''
		});		      
		$("#grdThongKeLog").jqGrid('navGrid', '#pager_grdThongKeLog', {edit: false, add: false, del: false});
		$("#grdThongKeLog").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		
		$('#gview_grdThongKeLog').find('.ui-jqgrid-bdiv').attr("style","height: 420px !important");
		GridUtil.setWidthPercent("grdThongKeLog","100%");
	}
	
	function bindEvent(){
		$('#btnXem').on('click', function () {
			loadData();			
		});	
		$('input[name=Log]').change(function() {
			loadData();		        
	    });

		$('#toolbarIdbtnAuto').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H003_HL7_Automator");	
		});

		$('#toolbarIdbtnMaker').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H004_HL7_Msg_Maker");	
		});
		
		$('#toolbarIdbtnConfig').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H001_HL7_System_Config");	
		});

		$('#toolbarIdbtnLog').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS05H002_HL7_System_Log");	
		});
		
		$("#grdThongKeLog").jqGrid("setGridParam", {
			ondblClickRow : function (rowid,iRow,iCol,e) {
				var row = $("#grdThongKeLog").jqGrid('getRowData', rowid);
				var idlog = row["ID"];
				var logtype = $('input[name=Log]:checked').val();
				var param = [idlog,logtype];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05H002.CT", param.join('$'));
				if(iCol==2){
					var res = data_ar[0]["SID"];
					copyToClipboard(res);
				} else if(iCol==3){
					var res = data_ar[0]["MESSAGE"];
					copyToClipboard(res);
				} else if(iCol==4){
					var res = data_ar[0]["RESPONSE"];
					copyToClipboard(res);
				} else if(iCol==5){
					var res = data_ar[0]["STATUS"];
					copyToClipboard(res);
				}
			}
		});
	}
	
	const copyToClipboard = str => {
		  const el = document.createElement('textarea');
		  el.value = str;
		  el.setAttribute('readonly', '');
		  el.style.position = 'absolute';
		  el.style.left = '-9999px';
		  document.body.appendChild(el);
		  const selected =
		    document.getSelection().rangeCount > 0
		      ? document.getSelection().getRangeAt(0)
		      : false;
		  el.select();
		  document.execCommand('copy');
		  document.body.removeChild(el);
		  if (selected) {
		    document.getSelection().removeAllRanges();
		    document.getSelection().addRange(selected);
		  }
		};
		
	function loadData(){
		var param = RSUtil.buildParam("",[user_id,schema,province_id,hospital_id,
			$("#txtTuNgay").val(),
			$("#txtDenNgay").val(),
			$('input[name=Log]:checked').val()]);

		GridUtil.loadGridBySqlPage("grdThongKeLog","CLS05H002.DS",param);
	}
	
}
