
/**
Script: <PERSON><PERSON> lý gọi bệnh nhân khám bệnh, script sử dụng thư viện <PERSON>quer<PERSON> 1.9
Author: HieuBD
Date created: 03-08-2016
Change Log:
  Date          Version  Modifier	Descriptions
  03-08-2016    1.0      HieuBD		Tạo mới file
  18-08-2016    2.0      HieuBD		Chuyể<PERSON> sang kỹ thuật viết theo class
  13-09-2016    2.1      HieuBD		Thêm function xử lý CLS
  19-09-2016	2.1.1	 VietDA		Chỉnh sửa base_dir và func format_so_cls cho phù hợp với HIS-L2
*/

function JSF_GoiBenhNhan() {

  /*-------- Properties ----------*/
  this.base_dir = '../common/sound';
  /*-------- END Properties ----------*/

  /*-------- Private method ----------*/

  // Kiể<PERSON> tra file có tồn tại không
  this.UrlExists = function (url) {
      var http = new XMLHttpRequest();
      http.open('HEAD', url, false);
      http.send();
      return http.status != 404;
  }

  this.play_sound = function(files) {
      var current = 0;
      var playlistPlayer = document.querySelector("#playlist audio");
      function next() {
          if (current === files.length - 1) {
              current = 0;
              playlistPlayer.src = '';
          } else {
              current++;

              playlistPlayer.src = files[current]!=null?files[current]:'';
          }
      }

      if (playlistPlayer === null) {
          throw "Playlist Player does not exists ...";
      } else {
          playlistPlayer.src = files[current];
          playlistPlayer.addEventListener('ended', next, false);
      }
  }

  this.format_so_benhnhan = function (so_bn) {
      console.log('');
      so_bn = parseInt(so_bn);
      if (so_bn < 10) {
          return new Array('0', '0', so_bn);
      } else if (so_bn < 100) {
    	  var a = so_bn % 10;
          var b = parseInt(so_bn / 10);
          return new Array('0', b, a);
      } else {
    	  var a = so_bn % 10;
          var b = parseInt(so_bn / 10) % 10;
          var c = parseInt(so_bn / 100);
          return new Array(c, b, a);
      }
  }

  // MỜI BÊNH NHÂN SỐ <> VÀO PHÒNG SỐ <> BỆNH NHÂN SỐ <> CHUẨN BỊ.
  this.goi_vaophong_chuanbi = function (so_bn, ma_phong, so_chuanbi) {
      console.log('MỜI BỆNH NHÂN SỐ ' + so_bn + ' VÀO ' + ma_phong + ' BỆNH NHÂN SỐ ' + so_chuanbi + ' CHUẨN BỊ');
      var playlist = new Array();
      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_MOI_BENH_NHAN_SO.mp3');
      var i = 0;
      var arr_so = this.format_so_cls(so_bn);
      for (var j = 0; j < arr_so.length; j++) {
          i++;
          playlist.push(this.base_dir + '/sound_so/' + arr_so[j] + '.mp3');
      }
      var sPhong = this.base_dir + '/sound_phong/' + ma_phong + '.mp3';
      if (this.UrlExists(sPhong)) {
          playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_VAO.mp3');
          playlist.push(this.base_dir + '/sound_phong/' + ma_phong + '.mp3');
      }else{
          playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_VAO_KHAM.mp3');
      }

      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_BENH_NHAN_SO.mp3');
      var arr_so_chuanbi = this.format_so_cls(so_chuanbi);
      for (var j = 0; j < arr_so_chuanbi.length; j++) {
          playlist.push(this.base_dir + '/sound_so/' + arr_so_chuanbi[j] + '.mp3');
      }
      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_CHUAN_BI.mp3');

      this.play_sound(playlist);
  }

  // MỜI BỆNH NHÂN SỐ <> VÀO <>
  this.goi_vaophong = function (so_bn, ma_phong) {
      console.log('MỜI BỆNH NHÂN SỐ ' + so_bn + ' VÀO ' + ma_phong);
      var playlist = new Array();
      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_MOI_BENH_NHAN_SO.mp3');
      var i = 0;
      var arr_so = this.format_so_cls(so_bn);
      for (var j = 0; j < arr_so.length; j++) {
          i++;
          playlist.push(this.base_dir + '/sound_so/' + arr_so[j] + '.mp3');
      }

      let sPhong = this.base_dir + '/sound_phong/' + ma_phong + '.mp3';
      if (this.UrlExists(sPhong)) {
          playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_VAO.mp3');
          playlist.push(this.base_dir + '/sound_phong/' + ma_phong + '.mp3');
      } else {
          playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_VAO_KHAM.mp3');
      }
      this.play_sound(playlist);
  }

  // MỜI BỆNH NHÂN SỐ <> VÀO KHÁM
  this.goi_vaokham = function (so_bn) {
      console.log('MỜI BỆNH NHÂN SỐ ' + so_bn + ' VÀO KHÁM');
      var playlist = new Array();
      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_MOI_BENH_NHAN_SO.mp3');
      var arr_so = this.format_so_cls(so_bn);
      for (var j = 0; j < arr_so.length; j++) {
          playlist.push(this.base_dir + '/sound_so/' + arr_so[j] + '.mp3');
      }

      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_VAO_KHAM.mp3');

      this.play_sound(playlist);
  }

  // MỜI BỆNH NHÂN SỐ <> CHUẨN BỊ.
  this.goi_chuanbi = function (so_bn) {
      console.log('MỜI BỆNH NHÂN SỐ ' + so_bn + ' CHUẨN BỊ');
      var playlist = new Array();
      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_MOI_BENH_NHAN_SO.mp3');
      var arr_so = this.format_so_cls(so_bn);
      for (var j = 0; j < arr_so.length; j++) {
          playlist.push(this.base_dir + '/sound_so/' + arr_so[j] + '.mp3');
      }

      playlist.push(this.base_dir + '/sound_goibenhnhan/SOUND_CHUAN_BI.mp3');

      this.play_sound(playlist);
  }

  /* Chuyển số bệnh nhân về format 4 số VD: 0012 */
  this.format_so_cls = function (so_bn) {
      var a_sophieu = ['0','0','0','0'];

      if(so_bn.length < 4) {
    	  return so_bn;
      }
      else {
    	  var s_sophieu = so_bn.substring(so_bn.length - 4, so_bn.length);
    	  a_sophieu = s_sophieu.split('');

    	  return a_sophieu;
      }
  }

  // Gọi API tăng lần gọi khám của phiếu khám
  this.tang_langoi_kham = function(so_phieu) {

      var js_request = 'js_request={"Task":"tang_lan_goi","Params":"' + so_phieu + '"}';
      var url_1 = this.base_dir + '/API/GoiBenhNhan/tang_lan_goi';
      console.log(url_1);
      $.ajax({
          type: "GET",
          dataType: "json",
          url: url_1,
          data: js_request,
          success: function (data) {
              console.log("LOG:  SUCCESS" + data);
          },
          error: function (data, msg, detail) {
              console.log("LOG:  ERROR" + msg + detail + data);
          }
      });
  }
  /*-------- END Private method ----------*/

  /*-------- Public method ----------*/

  this.GOI_BENHNHAN_VAOPHONG_CHUANBI = function (so_phieu, ma_phong, so_phieu_next) {
      if (so_phieu != '0') {
          if (ma_phong != '') {
              if (so_phieu_next != '0') {
                  this.goi_vaophong_chuanbi(so_phieu, ma_phong, so_phieu_next);
              } else {
                  this.goi_vaophong(so_phieu, ma_phong);
              }

          } else {
              this.goi_vaokham(so_phieu);
          }
      }

      if (so_phieu != '' && so_phieu != null && so_phieu != '0') {
          this.tang_langoi_kham(so_phieu);
      }
  }

  this.GOI_BENHNHAN_CHUANBI = function (so_phieu_next) {
      if (so_phieu_next != '0') {
          this.goi_chuanbi(so_phieu_next);
      }
  }

  /*-------- End Public method ----------*/
}
