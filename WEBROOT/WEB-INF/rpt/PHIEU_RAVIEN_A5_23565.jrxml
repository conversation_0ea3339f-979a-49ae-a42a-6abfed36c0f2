<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEU_RAVIEN_A5_23565" language="groovy" pageWidth="595" pageHeight="421" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="565" leftMargin="25" rightMargin="5" topMargin="14" bottomMargin="20" uuid="18c5e549-94fd-415a-8af9-02c896054c66">
	<property name="ireport.zoom" value="1.771561000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="khambenhid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<queryString language="plsql">
		<![CDATA[{call REPORT_PHIEU_RAVIEN_BTN(null,$P{[HID]},$P{[SCH]},null,$P{khambenhid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="NAMSINH" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="NGAYRAVIEN" class="java.lang.String"/>
	<field name="CHANDOANBANDAU" class="java.lang.String"/>
	<field name="PHUONGPHAPDIEUTRI" class="java.lang.String"/>
	<field name="TINHTRANGNGUOIBENHRAVIEN" class="java.lang.String"/>
	<field name="HUONGDIEUTRITIEPTHEO" class="java.lang.String"/>
	<field name="LOIDANBACSI" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="SOLUUTRU" class="java.lang.String"/>
	<field name="SOVAOVIEN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="HO_TEN_CHA" class="java.lang.String"/>
	<field name="HO_TEN_ME" class="java.lang.String"/>
	<field name="CHUANDOANPHU" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH" class="java.lang.String"/>
	<field name="GHICHU_BENHKEMTHEO" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="331" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="222" y="36" width="226" height="43" uuid="bfd04981-66e9-4eef-8536-08bf73859d44"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[GIẤY RA VIỆN]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="52" y="4" width="170" height="16" uuid="e4c0dd15-9c09-412a-aa3f-65ecb6653e53"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="52" y="20" width="170" height="16" uuid="3871d33d-d408-4473-ac31-f398b597398f"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="448" y="20" width="117" height="16" uuid="77ac90a8-20a6-41d5-a31f-08c71d6b7e66"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BN: "+$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="5" y="267" width="228" height="16" uuid="d78f4c4b-b584-4c40-b45f-3ac67bcf45a2"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[THỦ TRƯỞNG ĐƠN VỊ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="346" y="267" width="219" height="14" uuid="a97be892-6d70-4027-a409-e43bafa63a56"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG KHOA]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="222" y="4" width="226" height="16" uuid="4cd0e8ca-47ec-4ea7-9754-add4a7541e2a"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="222" y="20" width="226" height="16" uuid="1d710d54-8dfe-4d61-8e4c-484b674642fd"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="true" isUnderline="true"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="254" y="129" width="71" height="14" uuid="f5d848d9-651b-40a3-8faf-27c67733116d"/>
				<box>
					<pen lineWidth="0.5" lineColor="#666666"/>
					<topPen lineWidth="0.5" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 5, 15 )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="161" y="129" width="30" height="14" uuid="bd040011-445d-4d72-9a28-00d06e4c3dce"/>
				<box>
					<pen lineWidth="0.5" lineColor="#666666"/>
					<topPen lineWidth="0.5" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 0, 2 )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="221" y="129" width="33" height="14" uuid="99a6003e-329b-4041-add9-293fb64988f1"/>
				<box>
					<pen lineWidth="0.5" lineColor="#666666"/>
					<topPen lineWidth="0.5" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 3, 5 )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="191" y="129" width="30" height="14" uuid="77964672-3dc0-468b-af15-1bfde610d462"/>
				<box>
					<pen lineWidth="0.5" lineColor="#666666"/>
					<topPen lineWidth="0.5" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}.substring( 2, 3 )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="448" y="65" width="117" height="14" uuid="cb9b06a1-ccae-4047-9490-028d4765e07e"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số lưu trữ: "+($F{SOLUUTRU}==null?"":$F{SOLUUTRU})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="448" y="51" width="117" height="14" uuid="32ddbca6-98fd-49ea-9388-1086e8963cda"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã y tế: "+($F{SOVAOVIEN}==null?"":$F{SOVAOVIEN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="129" width="152" height="14" uuid="1a447f58-da4c-4df5-968b-e01b4fcea5a9"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Mã số BHXH/Thẻ BHYT số: "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="67" y="159" width="498" height="14" uuid="ad8ac3fe-a973-4766-86f8-3270b769d9c7"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{NGAYTIEPNHAN}==null?"":$F{NGAYTIEPNHAN}.substring( 0, 2 )+" giờ "
+ $F{NGAYTIEPNHAN}.substring( 3, 5 ) +" phút, ngày " + $F{NGAYTIEPNHAN}.substring( 6, 8 )
+" tháng " + $F{NGAYTIEPNHAN}.substring( 9, 11 ) +" năm "+ $F{NGAYTIEPNHAN}.substring( 12, 16 ))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="67" y="174" width="498" height="14" uuid="c53dd2dc-3508-4d2a-9e5e-10b6513f4a89"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{NGAYRAVIEN}==null?"":$F{NGAYRAVIEN}.substring( 0, 2 )+" giờ "
+ $F{NGAYRAVIEN}.substring( 3, 5 ) +" phút, ngày " + $F{NGAYRAVIEN}.substring( 6, 8 )
+" tháng " + $F{NGAYRAVIEN}.substring( 9, 11 ) +" năm "+ $F{NGAYRAVIEN}.substring( 12, 16 ))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="144" width="560" height="14" uuid="ee4c241f-9edc-43c7-820f-cb2244a44293"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ: " + ($F{DIACHI}==null?"":$F{DIACHI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="220" width="560" height="14" uuid="5efd2e9b-9313-48a8-b2ae-90d294667c72"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phương pháp điều trị: " + ($F{PHUONGPHAPDIEUTRI}==null?"":$F{PHUONGPHAPDIEUTRI})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="346" y="252" width="219" height="14" uuid="31b41908-3773-42b6-8973-1969dcad9021"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Bình Thuận, ngày " + $F{NGAYRAVIEN}.substring( 6, 8 ) + " tháng " + $F{NGAYRAVIEN}.substring( 9, 11 ) + " năm " + $F{NGAYRAVIEN}.substring( 12, 16 )]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="52" y="36" width="170" height="15" uuid="24ea04f8-84de-4041-ad34-c7eef600ef6e"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dept_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="448" y="36" width="117" height="15" uuid="1c38b475-11d9-4bfa-81f5-f6835fe1a272"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã ĐT: "+$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="448" y="4" width="117" height="16" uuid="4ddf48f8-2b80-4fd8-ba03-04ed8a5e5221"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["MS: 01/BV-01"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="84" width="560" height="14" uuid="59734d35-008d-4c1e-9686-3370da334fd3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên bệnh nhân: " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="296" y="98" width="269" height="14" uuid="fe3effee-0d45-4efa-b8af-c02b00e33e3b"/>
				<textElement>
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="5" y="112" width="217" height="14" uuid="f0de19ed-2c15-4537-9f68-701246e4d359"/>
				<textElement>
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["- Dân tộc: " + $F{TEN_DANTOC}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="315" y="112" width="250" height="14" uuid="df2dc31e-197d-44f5-8d8d-4ad88fe496b1"/>
				<textElement>
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENNGHENGHIEP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="189" width="560" height="14" uuid="3053cf47-**************-0c2a67954eeb"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán: " + ($F{CHANDOANBANDAU} == null ? "" : $F{CHANDOANBANDAU}.replace("Hội chứng nghiện","Hội chứng nghiện")) + ($F{GHICHU_BENHCHINH} == null?"":"("+$F{GHICHU_BENHCHINH}+")")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="46" y="236" width="519" height="14" uuid="77ca7576-76f1-47cd-9448-626afe404948"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{LOIDANBACSI}==null?"":$F{LOIDANBACSI} + " - ") + ($F{HO_TEN_ME}==null?"":"MẸ: "+$F{HO_TEN_ME}.toUpperCase()) + ($F{HO_TEN_CHA}==null?"":",CHA: " + $F{HO_TEN_CHA}.toUpperCase())]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="252" width="228" height="14" uuid="93d89a5d-7282-4a92-8ca2-702192a38b68"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+
(new SimpleDateFormat("dd")).format(new Date())
+ " tháng " + (new SimpleDateFormat("MM")).format(new Date())+ " năm " + (new SimpleDateFormat("YYYY")).format(new Date())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="5" y="205" width="560" height="14" uuid="fb185e6c-f3fb-4c19-bf27-e655c166f035"/>
				<textElement>
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán kèm theo: " + ($F{CHUANDOANPHU}==null?"":$F{CHUANDOANPHU}) + ($F{GHICHU_BENHKEMTHEO} == null ? "":"("+$F{GHICHU_BENHKEMTHEO}+")")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="5" y="283" width="228" height="13" uuid="fd77149b-b16d-4950-82f5-10cd6ea2a4bd"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký tên, đóng dấu)]]></text>
			</staticText>
			<image scaleImage="FillFrame">
				<reportElement positionType="Float" x="0" y="5" width="50" height="50" uuid="2f5705cc-b5ee-4563-99e4-dcc14384b6d5"/>
				<box>
					<pen lineWidth="0.5" lineColor="#333333"/>
					<topPen lineWidth="0.0" lineColor="#333333"/>
					<leftPen lineWidth="0.0" lineColor="#333333"/>
					<bottomPen lineWidth="0.0" lineColor="#333333"/>
					<rightPen lineWidth="0.0" lineColor="#333333"/>
				</box>
				<imageExpression><![CDATA[$P{DPAR_ORG_LOGO}]]></imageExpression>
			</image>
			<staticText>
				<reportElement positionType="Float" x="5" y="159" width="62" height="14" uuid="4a267925-a3da-41ab-98ed-af91d63bc9cc"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[- Vào viện  lúc:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="5" y="174" width="62" height="14" uuid="a84eca21-beec-47f0-a148-72221cf7231a"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[- Ra viện  lúc:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="253" y="112" width="62" height="14" uuid="0e7178b8-8f2f-49a1-afa0-f78f5d3c7f76"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Nghề nghiệp:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="5" y="236" width="41" height="14" uuid="7098ae6f-d04f-419b-803f-ec3db330f815"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[- Ghi chú:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="254" y="98" width="42" height="14" uuid="f244a669-35e9-491c-b3f6-1ed8744efbc9"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Nam/Nữ:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="5" y="98" width="249" height="14" uuid="7866245a-db2c-414f-a98b-22e2a1540868"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Ngày/tháng/năm sinh: "
+($F{NGAYSINH}==null?($F{NAMSINH}==null?"............":$F{NAMSINH}):$F{NGAYSINH})
+" (Tuổi: " + ($F{TUOI} == null ? " " : ($F{TUOI}+($F{DVTUOI}==null?"":" " + $F{DVTUOI})))
+")"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
