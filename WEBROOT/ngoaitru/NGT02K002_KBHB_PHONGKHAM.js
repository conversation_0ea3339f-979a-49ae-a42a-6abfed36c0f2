(function($) {
	$.widget( "ui.ngt02k002_kbhb_phongkham" , {

        //Options to be used as defaults
        options: {    
        	_data: null,
        },
        containerId: '',
        //Setup widget (eg. element creation, apply theming
        // , bind events etc.)
        _create: function () {
            // _create will automatically run the first time this widget is called. Put the initial widget  setup code here, then you can access the element
            // on which the widget was called via this.element. The options defined above can be accessed via this.options this.element.addStuff();
        	console.log('_create');
        	this.containerId=$(this.element).attr('id');        	
        	this._initWidget();  	
        },
        _t : function(_id) {
        	var newId='';
        	if(_id.indexOf("=")>0) {
        		newId=_id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi,_self.containerId+"$1");
        	}
        	else if(_id.indexOf("#")==0) {
        		newId=_id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi,"$1"+_self.containerId+"$2");
        	}
        	else {
        		newId=_self.containerId+_id;
        	}
        	return newId;
        },
        _initWidget: function() {
        	var _self=this;
        	$(_self.element).load('../ngoaitru/NGT02K002_KBHB_PHONGKHAM.tpl', function() {
        		$(_self.element).find("[id]").each(function() {
        	        this.id = _self.containerId+this.id;
        	    });
    			_self._loadData();
    		});
        },
        _loadData: function() { 
        	var _self=this;   
        	obj = _self.options._data;
        	FormUtil.setObjectToForm(_self.containerId,_self.containerId,_self.options._data);
        },
        // Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
        destroy: function () {

            // this.element.removeStuff();
            // For UI 1.8, destroy must be invoked from the
            // base widget
            $.Widget.prototype.destroy.call(this);
            // For UI 1.9, define _destroy instead and don't
            // worry about
            // calling the base widget
        },

        methodB: function ( event ) {
            //_trigger dispatches callbacks the plugin user
            // can subscribe to
            // signature: _trigger( "callbackName" , [eventObject],
            // [uiObject] )
            // eg. this._trigger( "hover", e /*where e.type ==
            // "mouseenter"*/, { hovered: $(e.target)});
            console.log("methodB called");
        },

        methodA: function ( event ) {
            this._trigger("dataChanged", event, {
                key: "someValue"
            });
        },

        // Respond to any changes the user makes to the
        // option method
        _setOption: function ( key, value ) {
            switch (key) {
            case "someValue":
                //this.options.someValue = doSomethingWith( value );
                break;
            default:
                //this.options[ key ] = value;
                break;
            }

            // For UI 1.8, _setOption must be manually invoked
            // from the base widget
            $.Widget.prototype._setOption.apply( this, arguments );
            // For UI 1.9 the _super method can be used instead
            // this._super( "_setOption", key, value );
        }
    });
})(jQuery);
