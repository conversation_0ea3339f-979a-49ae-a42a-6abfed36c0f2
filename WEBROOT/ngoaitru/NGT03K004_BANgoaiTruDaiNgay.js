function BANgoaiTruDaiNgay(_opts) {
    var grdDSBADaiNgay = 'grdDSBADaiNgay';
    var grdDSPKAMHeader =
        "Mã bệnh nhân,MABENHNHAN,100,0,f,l,1,2;" +
        "<PERSON><PERSON> bệnh án,MAHOSOBENHAN,100,0,f,l,1,2;" +
        "Họ và tên,TENBENHNHAN,100,0,f,l,1,2;" +
        "Số BHYT,MA_BHYT,100,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON><PERSON> tính,GIOITINH,100,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON> sinh,NGAYSINH,100,0,f,l,1,2;" +
        "Địa chỉ,DIACHI,100,0,f,l,1,2;" +
        "Trạng thái BA,TRANGTHAIBA,100,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON> sỹ mở BA,BACSYMO,100,0,f,l,1,2;" +
        "Ngày mở BA,NGAYMOBA,100,0,f,l,1,2;" +
        "<PERSON><PERSON>ng khám mở BA,PHONGMOBA,150,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON>T BA,NGAYKTBA,100,0,f,l,1,2;" +
        "Bác sỹ KT BA,BACSYKTBA,100,0,f,l,1,2;" +
        "KHAMBENHID,KHAMBENHID,100,0,t,l,1,2;" +
        "HOSOBENHANID,HOSOBENHANID,100,0,t,l,1,2;" +
        "BENHNHANID,BENHNHANID,100,0,t,l,1,2;" +
        "TRANG_THAI,TRANG_THAI,100,0,t,l,1,2;" +
        "PHONGID,PHONGID,100,0,t,l,1,2";
    var _param;
    var _user_id;
    var _hospital_id;
    var _khoaId;
    var _phongId;
    var validatorFormSearch = null;
    var benhAnInfo = {};
    this.load = doLoad;

    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        //$.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        var _options = $.extend({}, _opts);
        console.log(_options);
        _param = _options._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        _khoaId = _options.dept_id;
        _phongId = _options.subdept_id;
        validatorFormSearch = new DataValidator("formSearch");
        initControl();
        bindEvent();
    }

    function initControl() {
        $('#txtFROMDATE').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
        $('#txtTODATE').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
        ComboUtil.getComboTag("cboPHONGID", "NGT03K004.DS_PHONG", [{
            "name": "[0]",
            "value": _khoaId
        }], _phongId, {value: '', text: 'Vui lòng chọn'}, "sql");
        GridUtil.init(grdDSBADaiNgay, "100%", "380", "DANH SÁCH PHÒNG KHÁM", false,
            grdDSPKAMHeader, false, {rowNum: 50, rowList: [50, 100, 150]});
    }

    function bindEvent() {
        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {
                    pattern: /\d/
                },
                '9': {
                    pattern: /\d/,
                    optional: true
                },
                '#': {
                    pattern: /\d/,
                    recursive: true
                },
                'A': {
                    pattern: /[a-zA-Z0-9]/
                },
                'S': {
                    pattern: /[a-zA-Z]/
                }
            }
        };

        $('#btnTimKiem').click(function () {
            var valid = validatorFormSearch.validateForm();
            if (valid) {
                loadGridBADaiNgay();
            }
        });

        GridUtil.setGridParam(grdDSBADaiNgay, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(grdDSBADaiNgay);
                GridUtil.markRow(grdDSBADaiNgay, id);
                if (id) {
                    setEnabled(['btnXoaBenhAn'], []);
                    benhAnInfo = $("#" + grdDSBADaiNgay).jqGrid('getRowData', id);
                }
            },
            ondblClickRow: function (id) {
                var selRowId = $('#' + grdDSBADaiNgay).jqGrid('getGridParam', 'selrow');
                var rowData = $('#' + grdDSBADaiNgay).jqGrid('getRowData', selRowId);
                var _sql_par1 = RSUtil.buildParam("", [36]);
                var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
                var _rows1 = JSON.parse(_data1);
                var _sreenName = _rows1[0].URL;
                var _tenloaibenhan = _rows1[0].TENLOAIBENHAN;
                var _maloaibenhan = _rows1[0].MALOAIBENHAN;

                paramInput = {
                    khambenhid: rowData.KHAMBENHID,
                    hosobenhanid: rowData.HOSOBENHANID,
                    benhnhanid: rowData.BENHNHANID,
                    loaibenhanid: 36,
                    maloaibenhan: _maloaibenhan,
                    trang_thai: rowData.TRANG_THAI
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
                DlgUtil.open("divDlgBenhAnDetail");//
            }
        });

        $('#btnXoaBenhAn').click(function () {
            DlgUtil.showConfirm("Bạn có chắc chắn muốn xóa bệnh án dài ngày không? ", function (flag) {
                if (flag) {
                    console.log(benhAnInfo);
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K101.XOABADN", JSON.stringify(benhAnInfo));
                    switch (Number(fl)) {
                        case -2:
                            DlgUtil.showMsg("Bệnh án đang tồn tại BADN con, không được xóa!");
                            break;
                        case -1:
                            DlgUtil.showMsg("Xóa không thành công!");
                            break;
                        case 1:
                            DlgUtil.showMsg("Xóa bệnh án dài ngày thành công!");
                            loadGridBADaiNgay();
                            setEnabled([], ['btnXoaBenhAn']);
                            break;
                        default:
                            DlgUtil.showMsg("Đã có lỗi xảy ra!");
                            break;
                    }
                }
            });
        });
    }

    function loadGridBADaiNgay() {
        var object = {};
        FormUtil.setFormToObject("formSearch", "", object);
        console.log(object);
        var _sql_par = [{"name": "[0]", "value": object}];
        GridUtil.loadGridBySqlPage(grdDSBADaiNgay, "NGT03K004.BADAINGAY", _sql_par);
    }

    function setEnabled(_ena, _dis) {
        for (var i = 0; i < _ena.length; i++) {
            $("#" + _ena[i]).attr('disabled', false);
        }
        for (var i = 0; i < _dis.length; i++) {
            $("#" + _dis[i]).attr('disabled', true);
        }
    }
}