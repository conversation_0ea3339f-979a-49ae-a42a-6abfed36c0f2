<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BC042_PHUCAPPTTT_TONGHOP" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="20" isIgnorePagination="true" uuid="02afe4c9-87bb-4263-9fdc-5ca4328b421b">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="dt_tungay" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dt_denngay" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoaid" class="java.lang.Long">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="doituongbenhnhanid" class="java.lang.Long"/>
	<parameter name="loai" class="java.lang.Long"/>
	<parameter name="khoaid_TEXT" class="java.lang.String"/>
	<parameter name="nguoithuchienid" class="java.lang.Long"/>
	<parameter name="VPARS_nguoithuchienid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_nguoithuchienid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call BC042_PHUCAPPTTT_TV($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{dt_tungay},$P{dt_denngay},$P{khoaid},$P{loai},$P{VPARS_nguoithuchienid_VALUE},$P{ora_cursor})}]]>
	</queryString>
	<field name="LOAINHOM" class="java.lang.String"/>
	<field name="PHAUTHUATVIENCHINH" class="java.math.BigDecimal"/>
	<field name="PHAUTHUATVIENPHU1" class="java.math.BigDecimal"/>
	<field name="PHAUTHUATVIENPHU2" class="java.math.BigDecimal"/>
	<field name="GAYMECHINH" class="java.math.BigDecimal"/>
	<field name="PHUME1" class="java.math.BigDecimal"/>
	<field name="DUNGCUVIEN" class="java.math.BigDecimal"/>
	<field name="PHUME2" class="java.math.BigDecimal"/>
	<field name="GIUPVIEC" class="java.math.BigDecimal"/>
	<field name="TONG" class="java.math.BigDecimal"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="TENHANG" class="java.lang.String"/>
	<field name="PHAUTHUATVIENPHU3" class="java.math.BigDecimal"/>
	<field name="SOCA" class="java.math.BigDecimal"/>
	<variable name="tong_tienphucap" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG}]]></variableExpression>
	</variable>
	<variable name="variable9" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SOCA}]]></variableExpression>
	</variable>
	<variable name="SOLUONG_1" class="java.lang.Integer" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{SOCA}]]></variableExpression>
	</variable>
	<variable name="SOLUONG_2" class="java.lang.Integer" resetType="Group" resetGroup="loai" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG}]]></variableExpression>
	</variable>
	<group name="loai">
		<groupExpression><![CDATA[$F{LOAINHOM}]]></groupExpression>
		<groupHeader>
			<band height="21">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="555" height="21" uuid="60dbfe44-5392-4d93-b94f-7750d9aadd8d"/>
					<box leftPadding="2">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{LOAINHOM}.equals("PT")) ? "PHẪU THUẬT" : "THỦ THUẬT"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="19">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="278" height="19" uuid="a868222b-43b9-48c2-bf4d-78a53c20f09e"/>
					<box leftPadding="0" rightPadding="10">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{LOAINHOM}.equals("PT")) ? "Tổng phẫu thuật" : "Tổng thủ thuật"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="278" y="0" width="76" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="60bc66b8-83d2-44de-8d5a-6d05306d4f1a"/>
					<box rightPadding="2">
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SOLUONG_1}.doubleValue()==0?"":$V{SOLUONG_1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="354" y="0" width="136" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="28cdf804-2ec2-4331-b8df-03a17d09f969"/>
					<box rightPadding="2">
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{SOLUONG_2}.doubleValue()==0?"":$V{SOLUONG_2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="490" y="0" width="65" height="19" uuid="7899375c-193a-454b-a7cb-f6b26434212e"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="152" splitType="Stretch">
			<textField>
				<reportElement x="-1" y="0" width="556" height="18" uuid="6fc6008c-fdab-4078-99f8-ef256a99b58f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="18" width="555" height="18" uuid="fe9609a1-f2f2-498f-9c2c-cf69497e2d47"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="46" width="555" height="25" uuid="c2914e67-8ae5-477d-94f6-0771cbfc059e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BẢNG TIỀN KỸ THUẬT TOÀN VIỆN]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="105" width="24" height="47" uuid="ad24f7ab-b4f4-4cc7-8022-b46af62622e2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="24" y="105" width="254" height="47" uuid="8f7b3567-575a-4064-af8b-a26cd86904de"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TÊN HẠNG]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="71" width="556" height="20" uuid="5e4837d6-21f9-4489-87eb-32cae203ca75"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng hợp: Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format( $P{dt_tungay})  + " đến ngày " +  new SimpleDateFormat("dd/MM/yyyy").format( $P{dt_denngay})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="490" y="105" width="65" height="47" uuid="64f9b163-585f-4c70-8d50-be2ba9d7b9f8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[GHI CHÚ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="278" y="105" width="76" height="47" uuid="250da218-85d6-48cb-acce-9e8766559c72"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[SỐ CA]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="354" y="105" width="136" height="47" uuid="8793d2c9-b37b-4cb5-8613-11d7c95a12b6"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TỔNG TIỀN]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="18" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="24" height="18" uuid="79bfbda9-80a1-400a-b628-2871c6fd1268"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="24" y="0" width="254" height="18" uuid="ae1a58b5-6be4-4fce-a074-d0cb4982bdb3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENHANG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="278" y="0" width="76" height="18" uuid="8c3ddb5e-f47b-4de6-9099-2000eb91604a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOCA}.doubleValue()==0?"":$F{SOCA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="354" y="0" width="136" height="18" uuid="a5ab77ea-cd8e-44bd-b996-7a86fad5ae8c"/>
				<box rightPadding="2">
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG}.doubleValue()==0?"":$F{TONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="490" y="0" width="65" height="18" uuid="ff948f00-38fa-43cc-8ffb-29029cd7bc29"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="111">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="278" height="20" uuid="24e2e4c2-d41c-4e22-9d9e-4e6d5e3c7e25"/>
				<box rightPadding="10">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng tiền]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="354" y="0" width="136" height="20" uuid="54ad38ee-9ca9-4b6f-bd51-0be7e50f297a"/>
				<box rightPadding="2">
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tong_tienphucap}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="278" y="0" width="76" height="20" uuid="0c3e1d2e-7779-451a-bd88-e253503df9c9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{variable9}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="490" y="0" width="65" height="20" uuid="b6b6179c-6353-42e6-a952-69324362e240"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="451" y="49" width="104" height="18" uuid="fb22bc0f-f899-46e4-9c6e-e5a5c6713492"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[PHÒNG KHTH]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="330" y="31" width="225" height="18" uuid="bee7a347-b8d7-49f7-b094-82410f96c568"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + new SimpleDateFormat("dd").format(new Date()) + " tháng " + new SimpleDateFormat("MM").format(new Date()) + " năm " + new SimpleDateFormat("yyyy").format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="330" y="49" width="121" height="18" uuid="92ddce2a-d761-4cf5-9320-66e2239c30fd"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[PHÒNG CNTT]]></text>
			</staticText>
			<staticText>
				<reportElement x="229" y="49" width="101" height="18" uuid="f52d86fe-e8f0-48d2-b4f1-87e243d2e8ab"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TC - KT]]></text>
			</staticText>
			<staticText>
				<reportElement x="107" y="49" width="122" height="18" uuid="e018bb8d-011a-429e-b60b-79b8d2f3dc27"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾ TOÁN TRƯỞNG]]></text>
			</staticText>
			<staticText>
				<reportElement x="-1" y="49" width="108" height="18" uuid="38722b0a-6dda-4018-9e0e-b995ba92573b"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[GIÁM ĐỐC]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
