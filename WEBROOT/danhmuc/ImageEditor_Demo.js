function dSCHList(_opts) {
    this.load = doLoad;

    var chart;

    function doLoad(_hosp_id) {
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        CalMultiDatesPicker.init('txtNGAYTHEODOI');
        chart = new Chart($("#cv_test"), {
            x: new AxisX({
                range: 600, numof: 10,
                // style: new Chart.Style({}),
                odd: new Chart.OddLine({
                    enable: true,
                    strokeStyle: "black",
                    strokeDash: [3],
                    strokeDashOffset: 3,
                })
            }),
            y: new AxisY({
                range: 400, numof: 12,
                padding: [5, 5],
                // style: new Chart.Style({}),
                odd: new Chart.OddLine({
                    enable: true,
                    strokeStyle: "blue",
                    strokeDash: [4],
                    strokeDashOffset: 1,
                }),
                miliLine: new Chart.OddLine({
                    enable: true,
                    strokeStyle: "green",
                    strokeWidth: 1,
                    strokeDash: [4],
                    strokeDashOffset: 1,
                }),
            }),
            backgroundColor: '#ffe8c7',
            barX: {
                numof: 1,
                position: CHART_BARX_POSITION_ENUM.TOP,
                lines: new Chart.Lines({
                    enable: true,
                    lotNumber: [2, 3]
                }),
                text: [new Chart.Text(
                    {
                        value: "12",
                        backgroundColor: "#de7bff",
                        fontSize: 10,
                        lot: 2
                    }
                ), new Chart.Text(
                    {
                        value: "567",
                        fillStyle: "blue",
                        strokeStyle: "blue",
                        fontSize: 10,
                        backgroundColor: "#7bffdf",
                        lot: 3
                    }
                ), new Chart.Text(
                    {
                        value: "xxxx",
                        fontSize: 10,
                        backgroundColor: "#de7bff",
                        lot: 2
                    }
                ), new Chart.Text(
                    {
                        value: "Anh em",
                        fillStyle: "red",
                        fontSize: 10,
                        strokeStyle: "red",
                        backgroundColor: "#7bffdf",
                        lot: 3
                    }
                )],
                fullBorders: true,
                vuText: new Chart.Text({
                    value: "Bảo Anh",
                    fillStyle: "red",
                    fontSize: 10,
                    strokeStyle: "red",
                    strokeWidth: 1,
                    backgroundColor: "#7bffdf"
                }),
                backgroundColor: '#d5d5d5',
                values: [...Array(12).keys()].map(el => (el + 1) + "h")
            },
            valueUnit: [{
                id: "xxx",
                numof: 1,
                style: new Chart.Style({
                    fontSize: 5
                }),
                border: true,
                values: [120, 10],
                showLast: false
            },{
                id: "yyy",
                numof: 1,
                style: new Chart.Style({
                    fontSize: 5
                }),
                border: true,
                values: [2, 1, "2độ", "3độ", "4độ", "5độ", "6độ", "7độ", "8độ", "9độ", "10độ"],
                showLast: false
            }],
            borderValueUnit: new Chart.Style({
                fillStyle: "red",
                strokeWidth: 1
            }),
        }, {})
        chart
            .drawGrid()
            .drawLineByGridUnit(
                [
                    new Chart.Coordinate({x: 0, y: 5}),
                    new Chart.Coordinate({x: 1, y: 6}),
                    new Chart.Coordinate({x: 2, y: 7}),
                    new Chart.Coordinate({x: 3, y: 8}),
                    new Chart.Coordinate({x: 4, y: 9}),
                    new Chart.Coordinate({x: 4, y: 10}),
                    new Chart.Coordinate({x: 4, y: 11}),
                    new Chart.Coordinate({x: 4, y: 12}),

                ],
                new Chart.Style({
                    strokeWidth: 5
                })
            )
            .drawLineChart(new Chart.DataChart({
                id: "xxx",
                data: [130, 120, 190, 200],
                config: { unit: .5 , startX: 1 },
                style: new Chart.Style({
                    strokeStyle: 'blue',
                    strokeWidth: 2
                }),
                pointStyle: {
                    type: CHART_POINT_STYLE_ENUM.TRIANGLE,
                    size: [5, 5],
                    style: new Chart.Style({
                        fillStyle: 'red',
                        strokeWidth: 1,
                        strokeStyle: 'red',
                    }),
                    width: 30,
                    zooming: true,
                    showValue: {
                        enable: true,
                        style: new Chart.Style({
                            fillStyle: 'red',
                            // strokeWidth: 1,
                            strokeStyle: 'red',
                        }),
                    }
                }
            }))
            .drawLineChart(new Chart.DataChart({
                id: "yyy",
                data: [4, 6, 10, 5, 2],
                config: {
                    unit: 1, startX: 5, zooming: true
                },
                style: new Chart.Style({
                    strokeStyle: 'red',
                    strokeWidth: 2
                }),
                pointStyle: {
                    type: CHART_POINT_STYLE_ENUM.CIRCLE,
                    style: new Chart.Style({
                        fillStyle: 'yellow',
                        strokeWidth: 3,
                        strokeStyle: 'yellow',
                    }),
                    size: [10, 10],
                    showValue: {
                        enable: true,
                        style: new Chart.Style({
                            fillStyle: 'red',
                            strokeWidth: 0
                        })
                    }
                }
            }))
            .drawRangeChart(new Chart.DataChart({
                id: "yyy",
                data: [
                    new Chart.RangeValue({
                        start: 3.5,
                        end: 6
                    }),
                    new Chart.RangeValue({
                        start: 4.5,
                        end: 6.2
                    })
                ],
                config: {
                    unit: 1, startX: .5, zooming: true
                },
                style: new Chart.Style({
                    strokeStyle: 'green',
                    strokeWidth: 3,
                    fillStyle: 'green',
                    arrowRadius: 10,
                    arrowAngle: 60,
                    // strokeDash: [4],
                    // strokeDashOffset: 4
                }),
            }))

        setTimeout(function () {
            // chart.clearAll();
            // chart.clearLineChart();
        }, 5000)
        $("#btnImageEditor").click(function () {
            var myModal = DlgUtil.buildImageEditorPopup(
                $(this).data("url"),
                function (e) {
                    myModal.close();
                    console.log(e);
                    $("img#10420")[0].src = e.imageBase64;
                });
            myModal.open();
        })
    }

    function _bindEvent() {
        $("#btnImageDemo").click(function () {
            // let thirdPartyCaller = new ThirdPartyCaller("DUC_DTQG_NEW_BSY");
            // let rs = thirdPartyCaller.post([], { MAUBENHPHAMID: "20009848"});
            // console.log(rs);

            let pdfSign = new SignPdf("PHIEU_CDHA_A4",
                [
                    {"name":"maubenhphamid","type":"String","value":"20020681"},
                    {"name":"report_code","type":"String","value":"PHIEU_CDHA_A4"},
                    {"name":"hosobenhanid","type":"String","value":"20005757"}
                ]);
            // pdfSign.openReportGetBlob();
        })
    }
}
