<!--  
<PERSON><PERSON> màn hình  : NTU02D040
File mã nguồn : NTU02D040_DanhSachPhieuPhuThu.html
Mục đích  : <PERSON><PERSON><PERSON> cuu khoa hoc
Tham số vào :
<PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON>t  <PERSON> chú
TUYENNX	- 08022017 - Comment
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />


<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js"></script>

<link href="../common/script/tree/skin-win8/ui.fancytree.css"
	rel="stylesheet" />
<script src="../common/script/tree/jquery.fancytrees.js"></script>
<script src="../common/script/tree/jquery.fancytree.filter.js"></script>
<script type="text/javascript" src="../chidaotuyen/CDT_Danhsachnhanvien.js" charset="utf-8"></script>
<style>
.ui-jqgrid-htable,
.ui-jqgrid-btable,
.ui-jqgrid-pager,
.ui-jqgrid-view,
.ui-jqgrid-bdiv,
.ui-jqgrid-hdiv,
.ui-jqgrid-hbox,
.ui-jqgrid { max-width: 100% !important; width:100% !important; }
</style>

<div class="col-xs-12 low-padding mgt5">
	<div class="col-xs-12">
		<table id="grdDanhSachNV"></table>
		<div id="pager_grdDanhSachNV"></div>
	</div>
	<div class="col-xs-12 mgt10 mgb15" style="text-align: center;">
		<button type="button" class="btn btn-sm btn-primary" id="btnLuu">
			<span class="glyphicon glyphicon-floppy-disk"></span> Lưu
		</button>
		<button type="button" class="btn btn-sm btn-primary" id="btnClose">
			<span class="glyphicon glyphicon-remove-circle"></span> Đóng
		</button>
	</div>	
</div>

<script>
	var opt = [];
	var schema = '{db_schema}';
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';

	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';

	var mode = '{showMode}';
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';

	var _opts = new Object();

	_opts._param = session_par;
	_opts._uuid = user_id;

	if (mode == 'dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		objVar = EventUtil.getVar("dlgVar");
		_opts._detaiid = objVar.detaiid;

	}

	initRest(uuid, "/vnpthis");
	var list = new dSCHList(_opts);
	list.load();
</script>