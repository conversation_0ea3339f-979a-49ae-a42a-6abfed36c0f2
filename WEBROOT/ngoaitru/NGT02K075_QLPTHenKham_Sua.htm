<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>   
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>

<link rel="stylesheet" href="../common/script/alertify/themes/alertify.core.css" />
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.default.css" id="toggleCSS" />
<script type="text/javascript" src="../common/script/alertify/alertify.js" ></script>

<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../canlamsang/ParaclinicalUtility.js?v=20170525"></script>
<script type="text/javascript" src="../canlamsang/RISConnector.js"></script>
<script type="text/javascript" src="../canlamsang/LISConnector.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT02K075_QLPTHenKham_Sua.js?v=20171012"></script>

<div class="container">
	<div id="dlgSuaNhieu">
	<div class="col-xs-12 low-padding">
				
				<div class="col-xs-12 mgt5">
							<div class="col-xs-5 low-padding">
									<div class="col-xs-5 low-padding required">
										<label>Ngày hẹn</label>
									</div>
									<div class="col-xs-7 low-padding">
										<div class="input-group" style="width: 340%;">
									<input class="form-control input-sm" id="txtNGAYHENSUA" disabled
										valrule="Ngày hẹn,required|datetime|max_length[19]" title=""
										data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss"  maxlength="19">
									<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNGAYHENSUA','ddMMyyyy','dropdown',true,'24',true)"></span>
								</div>
									</div>
							</div>
							
				</div>
				<div class="col-xs-12 mgt5">
							<div class="col-xs-5 low-padding">
									<div class="col-xs-5 low-padding required">
										<label>Bác sĩ khám</label>
									</div>
									<div class="col-xs-7 low-padding">
										<input class="form-control input-sm" id="txtUSER_NAME_SUA"
										name="txtUSER_NAME_SUA" title="" >
									</div>
							</div>
							<div class="col-xs-7 low-padding">
									
									<div class="col-md-8 low-padding">
										<select class="form-control input-sm isnotnull"
											id="cboUSER_ID_SUA" msgErr="---Tất cả---,"
											filterLike="txtUSER_NAME_SUA" "
											>
										</select>
									</div>
							</div>
				</div>
				<div class="col-xs-12 low-padding mgt10" style="text-align: center;">
					<button type="button" class="btn btn-sm btn-primary"
						id="btn_Luu">
						<span class="glyphicon glyphicon-floppy-disk"></span> Lưu
					</button>
					<button type="button" class="btn btn-sm btn-primary"
						id="btn_Close">
						<span class="glyphicon glyphicon-remove-circle"></span> Đóng
					</button>
				</div>
				</div>
	</div>
</div>
<script>

	var opt=[];
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var dept_id = '{dept_id}';
	var subdept_id = '{subdept_id}';
	var dept_name = '{dept_name}';
	var subdept_name = '{subdept_name}';
	var lang= "vn";
	var mode = '{showMode}';
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=user_type;
	session_par[3]=province_id;
	var table_name='{table}';
	
	var _opts=new Object();
	
	_opts._param=session_par;
	_opts._uuid=uuid;
	_opts._deptId = dept_id;
	initRest(_opts._uuid);
	
	parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	data=EventUtil.getVar("dlgVar");	
	
 	if(mode=='dlg') {		
 		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
 		objVar=EventUtil.getVar("dlgVar");
 		_opts.objxoa = objVar.objxoa;
 	}
	
	var dsbn = new danhsachtkbenhnhanList(_opts);
	dsbn.load();
	
</script>
