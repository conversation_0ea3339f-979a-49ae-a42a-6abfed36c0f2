<script type="text/javascript" src="../common/script/RestService.js"></script>
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>

<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/validator.min.js"></script>
<script type="text/javascript" src="../common/script/datetimepicker/i18n/jquery.ui.datepicker-vi-VN.js"></script>

<script type="text/javascript" src="../noitru/NTU01H002_Util.js?v=003"></script>
<script type="text/javascript" src="../noitru/jquery.bootstrap-growl.min.js"></script>
<script type="text/javascript" src="../noitru/NTU02D170_FORMPHIEU.js?v=2411304"></script>

<style>


    label {
        font-size: 0.85em !important;
    }

    .label1 {
        font-size: 0.85em !important;
        display: inline !important;
        margin-left: 5px !important;
    }


    /* Default CSS Values */
    h3 {
        display: block;
        font-size: 1.17em;
        margin-top: 1em;
        margin-bottom: 1em;
        margin-left: 0;
        margin-right: 0;
        font-weight: bold;
    }

</style>

<div id="divMain" class="container" style=" width:95%;"><!--L2PT-37109  95%-->
	<!--L2PT-37109-->
	<br>
	<table id="divTTBN" style="padding-top: 10px; width:100%;">
		<tr>
			<td><label style="margin-right: -10px;">Mã BN</label>
			</td>
			<td style="width:110px;"><input class="form-control input-sm" id="txtMABENHNHAN" disabled>
			</td>
			<td><label class="mgl10" style="margin-right: -15px;">Tên BN</label>
			</td>
			<td><input class="form-control input-sm" id="txtTENBENHNHAN" disabled>
			</td>
			<td><label class="mgl10" style="margin-right: -20px;">Mã BA</label>
			</td>
			<td style="width:120px;"><input class="form-control input-sm" id="txtMAHOSOBENHAN" disabled>
			</td>
			<td><label class="mgl10" style="margin-right: -25px;">Năm sinh</label>
			</td>
			<td style="width:50px;"><input class="form-control input-sm" id="txtNAMSINH" disabled>
			</td>
			<td><label class="mgl10" style="margin-right: -30px;">Giới tính</label>
			</td>
			<td style="width:50px;"><input class="form-control input-sm" id="txtGIOI" disabled>
			</td>
			<td><label class="mgl10" style="margin-right: -35px;">T/g chỉ định</label>
			</td>
			<td style="width:140px;"><input class="form-control input-sm" id="txtTGCD" disabled>
			</td>
		</tr>
	</table>
	
     
    <div class="col-md-12 low-padding mgt10" id="divGRID">
        <table id="grdDS"></table>
        <div id="pager_grdDS"></div>
    </div>

    <div class="col-md-12 low-padding mgt10" id="divBUTTON" style="text-align: center">
            <button type="button" class="btn btn-sm btn-primary" id="btnThem">
                <span class="glyphicon glyphicon-pencil"></span> Thêm
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnSua">
                <span class="glyphicon glyphicon-edit"></span> Sửa
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnXoa">
                <span class="glyphicon glyphicon-remove"></span> Xóa
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnInPhieu">
                <span class="glyphicon glyphicon-print"></span> In phiếu
            </button>
            <button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
                <span class="glyphicon glyphicon-ok"></span> Ký & In
            </button>
            <button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
                <span class="glyphicon glyphicon-print"></span> In ký số
            </button>
            <button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
                <span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnLayThongTinPDGNBNV" style="display: none">
                <span class="glyphicon glyphicon-arrow-down"></span> Lấy thông tin
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnLuu">
                <span class="glyphicon glyphicon-floppy-disk"></span> Lưu
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnLuuVaDong" style="display: none">
                <span class="glyphicon glyphicon-floppy-disk"></span> Lưu và đóng
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnHuy">
                <span class="glyphicon glyphicon-remove-circle"></span> Huỷ
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnTaoBanSao" disabled>
                <span class="glyphicon glyphicon-floppy-disk"></span> Tạo bản sao
            </button>
            <button type="button" class="btn btn-sm btn-primary" id="btnDong" style="display: none">
                <span class="glyphicon glyphicon-folder-close"></span> Đóng
            </button>
    </div>

    <div class="col-md-12 low-padding mgt35" id="divMAU">
        <div class="col-md-2 low-padding"></div>
        <div class="col-md-2 low-padding">
            <select class="form-control input-sm i-col-m_fl text-left" id="cboMAU">
                <option value="">-- Chọn mẫu --</option>
            </select>
        </div>
        <div class="col-md-2 low-padding" style="text-align: center">
            <label>Nhập tên mẫu</label>
        </div>
        <div class="col-md-2 low-padding">
            <input class="form-control input-sm i-col-99 mgb5" type="text" id="txtTENMAU" style="width: 100%">
        </div>
        <div class="col-md-4 low-padding">
            <div class="col-md-1 low-padding"></div>
            <div class="col-md-5 low-padding">
                <button type="button" class="btn btn-sm btn-primary" id="btnLuuThanhMau">
                    <span class="glyphicon glyphicon-floppy-disk"></span> Lưu thành mẫu
                </button>
            </div>
            <div class="col-md-6 low-padding">
                <button type="button" class="btn btn-sm btn-primary" id="btnXoaMau">
                    <span class="glyphicon glyphicon-remove"></span> Xoá mẫu
                </button>
            </div>
        </div>
    </div>

    <div class="col-md-12 low-padding mgt35" id="divTTCT"> <!-- L2PT-98857 -->
    	<div class="col-md-12 low-padding mgt5" id="divTAB_PHIEUDIEUTRI" style="display: none"> <!-- đặt tên bắt đầu divTAB để lấy dl khi lưu  -->
			<div class="col-md-2 low-padding"> 
				<label style="font-size: 15px !important;font-weight: bold !important;">Chọn phiếu điều trị: </label>
			</div> 
			<div class="col-md-6 low-padding">
				<select class="form-control input-sm i-col-m_fl text-left" id="cboPHIEUDIEUTRI">
					<option value="">-- Chọn phiếu --</option>
				</select>
			</div> 
		</div>
        <div class="col-md-12 low-padding">
            <ul class="nav nav-tabs">
                <li role="presentation" id="tabA" class="active">
                    <a href="#divTabA" data-toggle="tab"><span id="nameTabA"></span></a>
                </li>
                <li id="tabB">
                    <a href="#divTabB" data-toggle="tab"><span id="nameTabB"></span></a>
                </li>
                <li id="tabC">
                    <a href="#divTabC" data-toggle="tab"><span id="nameTabC"></span></a>
                </li>
                <li id="tabD">
                    <a href="#divTabD" data-toggle="tab"><span id="nameTabD"></span></a>
                </li>
                <li id="tabE">
                    <a href="#divTabE" data-toggle="tab"><span id="nameTabE"></span></a>
                </li>
            </ul>
        </div>
        <div class="col-md-12 low-padding" id="divTAB">
            <div id="divTabA" class="tab active"></div>
            <div id="divTabB" class="tab"></div>
            <div id="divTabC" class="tab"></div>
            <div id="divTabD" class="tab"></div>
            <div id="divTabE" class="tab"></div>
        </div>
        <div style="margin-bottom: 20px">&nbsp;</div>
    </div>

</div>

<script>
    var name = this.name;
    var param = name.split('@');
    var opt = new Object();
    opt.csytid = '{hospital_id}';
    opt.CHUCNANG = param[0];
    opt.KHAMBENHID = param[1];
    opt.MODE = param[2];
	opt.PARAM = param;  //L2PT-39886
    var uuid = '{uuid}';
    var user_id = '{user_id}'; //L2PT-113831
    initRest(uuid, "/vnpthis"); 
    var obj = new NTU02D170_FORMPHIEU(opt);
    obj.load();
</script>
