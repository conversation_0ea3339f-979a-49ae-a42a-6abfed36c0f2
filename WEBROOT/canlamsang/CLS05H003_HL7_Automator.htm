<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>           
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20170105" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script src="../common/script/tinymce/tinymce.min.js"></script>
<script type="text/javascript" src="../canlamsang/CLS05H003_HL7_Automator.js?v=20201224"></script>

<div id="divMain" class="container">
	<div id='toolbarId'></div>
	<input type="hidden" id="hdfSYSTEM_ID" value="">
	<div class="mgt5" id="divThongTinThongKe">		
		<div class="col-md-12 low-padding">
			<div class="col-xs-2 low-padding">
				Mr Robot's status
			</div>
			<div class="col-xs-2 low-padding">
				<label id="lblStatus"></label> 
			</div>		
			<div class="col-md-3 low-padding" style="text-align: center;">				
				<button class="btn btn-sm btn-primary" id="btnStart" >
					<span class="glyphicon glyphicon-play"></span> Run 1
				</button>
				<button class="btn btn-sm btn-primary" id="btnStop" >
					<span class="glyphicon glyphicon-stop"></span> Stop
				</button>		
				<button class="btn btn-sm btn-primary" id="btnStartAll" >
					<span class="glyphicon glyphicon-play"></span> Don't Stop
				</button>
			</div>
			<div class="col-xs-1 low-padding">
			</div>
			<div class="col-xs-2 low-padding">
				<div class="col-xs-4 low-padding">
					<label class="mgl5">Ngày</label>	
				</div>
				<div class="col-xs-8 low-padding">
					<div class='input-group'>
						<input class="form-control input-sm" id="txtTuNgay" valrule="Từ ngày,datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div> 	
				</div>
			</div>
			<div class="col-xs-2 low-padding">
				<div class="col-md-3 low-padding">
				</div>
				<div class="col-md-3 low-padding">
					<button class="btn btn-sm btn-primary" id="btnXem" >
						<span class="glyphicon glyphicon-play"></span> Xem
					</button>
				</div>
				<div class="col-md-6 low-padding">
				</div>
			</div>
		</div>
		<div class="col-md-12 low-padding">
		</div>
		
		<div id="divOutboundQueue">
			<div class="col-md-12 low-padding">
				<table id="grdMsgQueue"></table>
				<div id="pager_grdMsgQueue"></div>
			</div>
		</div>
	</div>
</div>

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang= "vn";
	console.log('user_id='+user_id+', schema='+schema+', province_id='+province_id+', hospital_id='+hospital_id);
	
	var session_par=[];
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	session_par[0]=hospital_id;
	var table_name='{table}';
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	initRest(_opts._uuid);
	
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("HL7_Gateway");
	
	var log = new LogCLS(_opts);		
	log.load();
</script>