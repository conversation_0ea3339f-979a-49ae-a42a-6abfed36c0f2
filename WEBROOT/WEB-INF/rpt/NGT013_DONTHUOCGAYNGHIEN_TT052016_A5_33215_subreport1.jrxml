<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT013_DONTHUOCGAYNGHIEN_TT052016_A5_33215_SUB1" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="381" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="4" uuid="7aa9e6e8-b14b-4277-9a60-b444a656ea92">
	<property name="ireport.zoom" value="2.8531167061100104"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="343"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="org_tel" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_DONTHUOCN_33215($P{[UID]},$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="BENHVIEN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA_KEMTHEO" class="java.lang.String"/>
	<field name="STT" class="java.math.BigDecimal"/>
	<field name="TEN_THUOC" class="java.lang.String"/>
	<field name="DICHVUID" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="HUONGDANSUDUNG" class="java.lang.String"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="SODIENTHOAI" class="java.lang.String"/>
	<field name="SOPHIEU_BARCODE" class="java.lang.String"/>
	<field name="NGUOIKEDON" class="java.lang.String"/>
	<field name="DOITUONG_VIENPHI" class="java.lang.String"/>
	<field name="YHCT_TUNGAY" class="java.lang.String"/>
	<field name="YHCT_DENNGAY" class="java.lang.String"/>
	<field name="BHYT_1" class="java.lang.String"/>
	<field name="BHYT_2" class="java.lang.String"/>
	<field name="BHYT_3" class="java.lang.String"/>
	<field name="BHYT_5" class="java.lang.String"/>
	<field name="BHYT_6" class="java.lang.String"/>
	<field name="BHYT_4" class="java.lang.String"/>
	<field name="YKIENBACSY" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="ICD10" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="DUNGTUYEN" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="NGAYHEN" class="java.lang.String"/>
	<field name="PHIEUHEN" class="java.lang.Number"/>
	<field name="NGUOITAO" class="java.lang.String"/>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="TUNGAY" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="khoaid" class="java.lang.String"/>
	<field name="KHOAKHAM" class="java.lang.String"/>
	<field name="khokeid" class="java.lang.String"/>
	<field name="PHONGID" class="java.lang.String"/>
	<field name="diachi_bhyt" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="76" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="42" width="219" height="17" uuid="4c9ae545-c22d-462c-9ab7-41d6bf2237b3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Điện thoại: "+($P{org_tel}==null?"":$P{org_tel})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="261" y="42" width="121" height="17" uuid="e9e4af13-c891-498d-92ca-bb0cbb7e0f89"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Số: " +$F{SOPHIEU}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="8" width="219" height="17" uuid="830562f1-ee45-4c84-80e6-9c2dd47aa66e"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="261" y="25" width="121" height="17" uuid="1f49c924-470b-4f97-a487-f208d3c66c73"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BA: " +$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="25" width="219" height="17" uuid="a1128956-ae28-48b6-bc6a-6270cb284646"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOAKHAM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="261" y="59" width="121" height="17" uuid="91e08ce1-9bc1-4677-9a30-712722b95312"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BN: " +$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="261" y="8" width="121" height="17" uuid="1e5daeca-3e5b-4930-b9d7-48df1fd2e4a8"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code39 (Extended)" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
		</band>
	</title>
	<pageHeader>
		<band height="38" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="381" height="20" uuid="2750d24f-3ab8-4b26-b9ea-0b5e47a2b99a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐƠN THUỐC "N"]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="20" width="381" height="18" uuid="ebbb8986-a6ae-43ae-8a1e-519b0106d501"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[(Bản lưu tại cơ sở khám bệnh, chữa bệnh)]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="117" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="1" y="47" width="152" height="15" uuid="b7626f81-2fb4-413d-97f5-ac3347a575ea"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Số thẻ bảo hiểm y tế (nếu có) :]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="1" y="102" width="260" height="15" uuid="43565ab2-0c35-4597-90d6-63ef3b3a5f92"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thuốc điều trị:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="153" y="47" width="228" height="15" uuid="d39f5d6c-a345-4c78-bbdf-de638a29534b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="63" width="381" height="14" uuid="204db296-4047-4bf1-a793-22cfa8215916"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: " + " (" + $F{ICD10} + ") " + $F{CHANDOANVAOKHOA} +
($F{CHANDOANVAOKHOA_KEMTHEO}==null?"":"-"+$F{CHANDOANVAOKHOA_KEMTHEO})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="16" width="220" height="15" uuid="ba55e8a4-d046-4fd0-84fd-ad56c33aca4c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + $F{TUOI} + " " + $F{DVTUOI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="381" height="15" uuid="4631e551-b9e9-431c-9658-fd69e18975ea"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên:  " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="220" y="16" width="161" height="15" uuid="bf5421a1-a8d9-4746-865c-1488ee3f7830"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Nam/Nữ: " + $F{GIOITINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="31" width="381" height="15" uuid="8ab07120-0c91-4e2a-af9f-8bbb031698f6"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: " + ($F{diachi_bhyt}==null?$F{DIACHI}:$F{diachi_bhyt})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" mode="Transparent" x="272" y="102" width="109" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="201bfe6a-75d5-47d5-85b3-7cdc5411496d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="78" width="379" height="14" uuid="9385792b-9527-4d17-b7e9-381c5b2b0df0"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đợt  (Từ ngày " + $F{TUNGAY} +" Đến ngày " +  $F{NGAYHEN} + ")"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="32" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="1" y="0" width="22" height="18" uuid="102ecaf8-be6e-471b-bea8-6663df96f929"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="23" y="0" width="238" height="18" uuid="f10bcfff-5156-4f97-9252-d33d89a55660"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_THUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="272" y="0" width="75" height="18" uuid="e6c4f8a5-5ca9-4931-b57a-b8e8d84a41e9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG}.length()==1?"0"+$F{SOLUONG}:$F{SOLUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="23" y="18" width="356" height="14" uuid="a5a907d6-d8f5-4102-8e16-19342813286d"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDANSUDUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="347" y="0" width="34" height="18" uuid="00d603b9-78db-4929-aa3b-1c2f85a6c629"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  " + $F{TEN_DVT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="127">
			<staticText>
				<reportElement positionType="Float" x="190" y="33" width="191" height="15" uuid="2059e3aa-9a45-4907-93de-4464b35befd2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Bác sỹ khám bệnh]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="190" y="18" width="191" height="15" uuid="1d19143e-08ba-4722-837b-6997acce6e10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + (new SimpleDateFormat("dd")).format(new Date())
+ " tháng " + (new SimpleDateFormat("MM")).format(new Date())
+ " năm " + (new SimpleDateFormat("yyyy")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="190" y="48" width="192" height="14" uuid="5206a019-f692-4ec3-9648-f81808353f79"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="47" y="0" width="335" height="16" uuid="05368627-ccc3-45b0-be5c-940d91d3e96c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{YKIENBACSY}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="47" height="16" uuid="e91a9e6d-e283-4022-80ed-f6817e44706d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[Lời dặn:  ]]></text>
			</staticText>
			<textField>
				<reportElement x="190" y="107" width="189" height="20" uuid="9b2fcd8c-3398-4faa-aa39-9e91be0594de"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOITAO}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
