// manhnv - 8-8-2016

function TC01K003_LichSuTiemChung(opt) {
    this.load = doLoad;
    var _opt = opt;
    var that = this;
    var _gridDSCHId = "grdDanhSachLichSu";
    var _gridDSCHHeader = "Mã tiêm chủng,MADOITUONG_TIEMCHUNG,70,0,f,l,1,2;"
        + "<PERSON><PERSON><PERSON> b<PERSON>nh nh<PERSON>,ten<PERSON><PERSON><PERSON>,100,0,f,c,1,2;"
        + "Tên thuố<PERSON>,ten_vacxin,100,0,f,c,1,2;"
        + "L<PERSON>,LO_VACXIN,100,0,f,c,1,2;"
        + "Địa chỉ,diachi,100,0,f,l,1,2;"
        + "<PERSON><PERSON> điện thoại,sdt<PERSON><PERSON><PERSON>,80,0,f,l,1,2;"
        + "G<PERSON><PERSON> tiêm,ngay_tiem,60,0,f,l,1,2;"
        + "<PERSON><PERSON><PERSON> tự mũi tiêm,thu_tu_mui_tiem,100,0,f,l,1,2;"
        + "<PERSON><PERSON> sở tiêm chủng,co_so_tiem_chung,100,0,f,l,1,2;"
        + "<PERSON><PERSON><PERSON> chỉ,diachi,100,0,f,l,1,2"
    ;
    [{
        "lich_su_tiem_id": 3.08599266e8,
        "doi_tuong_id": 6.5592785e7,
        "vacxin_id": 1,
        "ten_vacxin": "Viêm gan B sơ sinh",
        "khang_nguyen": [{"khang_nguyen_id": 3, "ten_khang_nguyen": "Viêm gan B"}],
        "trang_thai": 2,
        "ngay_tiem": "14:34 20/03/2024",
        "thu_tu_mui_tiem": 1,
        "so_mui_uv": null,
        "lich_su_mang_thai_id": null,
        "co_so_id": 17109,
        "co_so_tiem_chung": "Khoa Sản Nam Đàn",
        "loai_co_so": 2,
        "dia_diem_tiem_khac": null,
        "seo": null,
        "truoc_24h": 0,
        "can_nang": null,
        "don_vi_can_nang": null,
        "chieu_cao": null,
        "don_vi_chieu_cao": null,
        "hinh_thuc_tiem_chung": 1,
        "lo_vacxin": "GB-040522E",
        "phan_ung_sau_tiem": {
            "loai_phan_ung": 1,
            "phan_ung_thong_thuong": null,
            "phan_ung_nang": null,
            "phan_ung_khac": null
        }
    }]

    function doLoad() {
        $.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
        _initControl();
        _bindEvent();
        loadGridData();
    }

    function _initControl() {
        GridUtil.init(_gridDSCHId, "100%", "320px", "Lịch sử tiêm chủng", false, _gridDSCHHeader, true);
    }

    function _bindEvent() {
        $("#btnHuy").on("click", function (e) {
            parent.DlgUtil.close("dlgHenTiem");
        });
    }

    function loadGridData() {
        var data = ajaxSvc.TIEMCHUNGWS.GetLichSuTiem("403353520240024", "" + _opt.token);
        // var data  = ajaxSvc.TIEMCHUNGWS.GetLichSuTiem(_opt.madoituong_tiemchung, _opt.token);

        console.log(data);
        var datagrid = JSON.parse(data);

        datagrid[0].tenbenhnhan = _opt.tenbenhnhan;
        datagrid[0].MADOITUONG_TIEMCHUNG = _opt.madoituong_tiemchung;
        $("#" + _gridDSCHId).jqGrid("clearGridData", true);
        GridUtil.fetchGridData(_gridDSCHId, datagrid);
    }

}

