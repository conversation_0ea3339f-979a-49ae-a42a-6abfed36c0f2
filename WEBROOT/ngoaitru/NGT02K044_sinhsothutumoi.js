function NGT02K044_SINHSOTHUTUMOI(opt) {
	this.load=doLoad;
	var _opt = opt;

	function doLoad() {
		_initControl();
		_bindEvent();	
	}
	
	function _initControl(){
		var sql_par=[];
		sql_par.push({"name":"[0]","value": _opt.khambenhid});
		ComboUtil.getComboTag("cboPHONGID", "DS.PKLAYSOUUTIEN", sql_par, "", {extval: true}, "sql", "","");
		if(_opt.kieu == 1){
			$("#divSinhSo").show();
			$("#btnPrint").prop('disabled', true);
		}
		else{
			$("#divSinhSo").hide();
			$("#btnPrint").prop('disabled', false);
		}
	}
	
	function _bindEvent() {			
		$("#btnSinhso").on("click",function(e){	
			
			if(_checksoluongmaxphongkham($('#cboPHONGID').val()) == '-1'){
				DlgUtil.showMsg('Phòng khám hết số');
				return;
			}
			
			var myVar={
				khambenhid : _opt.khambenhid,
				phongkhamdangkyid : $('#cboPHONGID'+ " option:selected").attr('extval0'),
				phongid : $("#cboPHONGID").val()
			}; 

			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC.SINHSO.UUTIEN", JSON.stringify(myVar));				
			if(ret == 1){
				$("#btnPrint").prop('disabled', false);
				_inphieu();
			}else if (ret == -100 ){
				DlgUtil.showMsg('Đã kết thúc khám trong phòng khám');
			}
		});
		
		$("#btnPrint").on("click",function(e){					
			_inphieu();
		});
		
		$("#btnDong").on("click",function(e){					
			parent.DlgUtil.close("dlgsinhstt");
		});
	}
	
	function _inphieu(){
		var par = [ {
			name : 'khambenhid',
			type : 'String',
			value : _opt.khambenhid
		}, {
			name : 'phongid',
			type : 'String',
			value : $("#cboPHONGID").val()
		}];
		
		CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par);
	}
	
	function _checksoluongmaxphongkham(_phongid){				
		var rets = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.MAXPHONGKHAM",_phongid + '$');
		return rets;
	}
}

