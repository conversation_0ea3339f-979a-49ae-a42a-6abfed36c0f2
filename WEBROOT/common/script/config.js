ConfigUtil = {
    castString: function(v) {
        let type = typeof v;
        if (type === "object" || type === "array") {
            return JSON.stringify(v);
        } else {
            return v + "";
        }
    },
    castNumber: function (v) {
        if (isNaN(v)) {
            return v
        } else {
            return parseInt(v);
        }
    },
    parseConfigBasingType: function (obj, listConf) {
        if (typeof obj !== "object") {
            return {}
        } else {
            let rs = {};
            Object.keys(obj).forEach(el => {
                let funcType = listConf[el];
                if (typeof funcType === "function") {
                    rs[el] = funcType(obj[el]);
                } else {
                    rs[el] = obj[el];
                }
            })
            return rs;
        }
    },
    fetch: function () {
        window.GLOBAL_CONFIG = {};
        let rsConf = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH.NEW", Object.keys(ConfigUtil.listGlobalConfig).join(";"));
        if (rsConf && rsConf.length > 0) {
            window.GLOBAL_CONFIG = ConfigUtil.parseConfigBasingType(rsConf[0], ConfigUtil.listGlobalConfig);
            sessionStorage.setItem(user_name + ":GLOBAL_CONFIG", JSON.stringify(window.GLOBAL_CONFIG));
        }
    },
    listGlobalConfig: {
        "DIALOG_LINE_CLAMP": this.castNumber,
        "SIGNHUB_TYPE": this.castNumber // 0 - Off, 1 - pdf, 2 - xml
    }
}
if (typeof user_name != 'undefined' && user_name && user_name.length > 0) {
    window.GLOBAL_CONFIG = {};
    let GLOBAL_CONFIG_SESSION = sessionStorage.getItem(user_name + ":GLOBAL_CONFIG");
    if (GLOBAL_CONFIG_SESSION && GLOBAL_CONFIG_SESSION.length > 0) {
        try {
            let GLOBAL_CONFIG_SESSION_OBJ = JSON.parse(GLOBAL_CONFIG_SESSION);
            if (GLOBAL_CONFIG_SESSION_OBJ.constructor.name === 'Object') {
                window.GLOBAL_CONFIG = GLOBAL_CONFIG_SESSION_OBJ;
            } else {
                ConfigUtil.fetch();
            }
        } catch (e) {
            ConfigUtil.fetch();
        }
    } else {
        ConfigUtil.fetch();
    }
}