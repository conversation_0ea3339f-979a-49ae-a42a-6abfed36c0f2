/*
 * DEV			Date			Note
 * giangpt.dng	06/04/2021		BVTM-738
*/
function TC01K002_TheoDoiSauTiemChiTiet(_opts) {
	var _user_id = -1;
	var _hospital_id = _opts.hospital_id;
	var hosobenhanid = _opts.hosobenhanid;
	flagLoading = false;
	_param = [];
	_cauhinhId = -1;
	var checkRequired;
	var that = this;
	var chedoanid = '';  
	this.load = doLoad;
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		
		var _sql_par_phong = [];
		_sql_par_phong.push({"name":"[0]","value":_hospital_id});
		ComboUtil.getComboTag("cboNGUOITHUCHIEN","TC01K002.03",_sql_par_phong,"",{value:'-1',text:'--Chọn người thực hiện--'},"sql"); 
		
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		
		$("#txtTHOIGIAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		
		var objData = new Object();
		objData.hosobenhanid = hosobenhanid;
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("TC01K002.01", hosobenhanid); 
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			FormUtil.clearForm("inputForm", "");
			FormUtil.setObjectToForm("inputForm", "", row);	
			setTimeout(function(){
				$("#cboNGUOITHUCHIEN").val(row.NGUOITHUCHIEN);
			}, 300);
			
		}
	};
	function _bindEvent() {
			
		$("#btnLuu").on("click", function() {
			saveData('0');
		});
		$("#btnLuuIn").on("click", function() {
			saveData('1');
		});
		$("#btnRefresh").on("click", function() {
			_initControl();
		});
		$("#btnDong").on("click", function() {
			parent.DlgUtil.close("divDlgTheoDoiSauTiemChiTiet");
		});
	}
	function saveData(mode) {
		objData = new Object();
		FormUtil.setFormToObject("inputForm", "", objData);
		objData.hosobenhanid = hosobenhanid;		
		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("TC01K002.02", JSON.stringify(objData));
		if (ret == '1') {
			DlgUtil.showMsg("Lưu thành công !");
			if (mode == '1') {
				var par = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : hosobenhanid
				} ];
				openReport('window', "TIMCHUNG_THEODOI_SAUTIEM_A4", "pdf", par);
			}
		} else {
			DlgUtil.showMsg("Không thành công !");
		}
	}	
}
