/*
 * Log LIS
 * 
 * VietDA 	05/08/2016		Tạo mới
 * 
 */

var ctl_ar=[
	{type:'button',id:'btnQuayLai',icon:'chevron-left',text:'Quay lại'},
	{type:'label',id:'lblInfo',icon:'',text:'Log trả kết quả XN'}
];

function LogCLS(opt) {
	this.load = doLoad;
	
	function doLoad(){
		
		initControl();
		bindEvent();
        
		$("#txtTuNgay").val(moment().format('DD/MM/YYYY'));
		$("#txtDenNgay").val(moment().format('DD/MM/YYYY'));
		
		var callback = function () {
            if ($.active !== 0) {
                setTimeout(callback, '50');
                return;
            }
            timKiemThongKe();
        };
        callback();
	}
	
	function initControl(){		
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		ToolbarUtil.build('toolbarId', ctl_ar);
		var _toolbar=ToolbarUtil.getToolbar('toolbarId');
		
		_gridId = "grdLogLIS";
		var _gridHeader="ID,ID,10,0,t,l;" +
				"Thời điểm thao tác,TIMEPOST,70,0,f,l;" +
				"Tài khoản,USERNAME,65,0,f,l;" +
				"Thao tác,FUNC,55,0,f,l;" +
				"SID,SID,50,0,f,l;" +
				"Ngày MBP,CREATED,40,0,f,c;" +
				"Yêu cầu,REQUEST,130,0,f,l;" +
				"Mã lỗi,CODE,35,0,f,c;" +
				"Thông báo,MESSAGE,120,0,f,l;" +
				"Kết quả,RESULT,90,0,f,l";
		GridUtil.init(_gridId,"100%","560px","",false,_gridHeader);	
		GridUtil.addExcelButton(_gridId,"Export all",false);		
	}
	
	function bindEvent(){
		$('#toolbarIdbtnQuayLai').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS01X002_BenhPhamDangLam");
		});
		
		$("#btnTimKiem").click(function(){
			timKiemThongKe();
		});
	}
	
	function validateDate(ctrId, key, checkNull){
		var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
		
		if(checkNull && ($('#' + ctrId).val() == null || $('#' + ctrId).val() == '')){
			DlgUtil.showMsg(key + $.i18n("require"),undefined,3000);	
			$('#' + ctrId).focus();
			return false;
		}
		
		if($('#' + ctrId).val().trim().length > 0 && !datetimeRegex.test($('#' + ctrId).val()) && !checkDate($('#' + ctrId).val())){
			DlgUtil.showMsg(key + $.i18n("date_type_invalid"),undefined,3000);	
			$('#' + ctrId).focus();
			return false;		
		}
		return true;
	}
	
	function kiemTra(){
		if(validateDate("txtTuNgay","Từ ngày ", false) && validateDate("txtDenNgay", "Đến ngày ", false)){
			var txtTuNgay = $("#txtTuNgay").val();
			var txtDenNgay = $("#txtDenNgay").val();
			
			var d1 = stringToDateFormat(txtTuNgay);
			var d2 = stringToDateFormat(txtDenNgay);
			
			if(d1 <= d2){
				return true;
			} 
			else {
				DlgUtil.showMsg("Từ ngày không được lớn hơn Đến ngày",undefined,3000);	
				$("#txtTuNgay").focus();
				return false;
			}
		}		
		return false;
	}
	
	// load log lis
	function timKiemThongKe(){
		if(kiemTra()){
			var param = RSUtil.buildParam("",[uuid, schema, province_id, hospital_id, $("#txtTuNgay").val(), $("#txtDenNgay").val()]);

			GridUtil.loadGridBySqlPage("grdLogLIS", "CLS01X009.LOG", param);			
		}
	}
}
