function NTU02D132_DM(_opts) {
    var _gridLSM = "grdLSDuyetMo";
    var _gridLSMHeader = "LICHMOID,LICHMOID,50,0,t,l,1,2;" +
        "<PERSON>h<PERSON><PERSON> gian mổ,THOIGIAN_MO,120,0,f,l,1,2;" +
        "Tr<PERSON>ng thái,TRANGTHAI,100,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON> thực hiện,PHONG_MO_FULLNAME,200,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON> <PERSON><PERSON>,BS_CHINH,120,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON> <PERSON>ĩ yêu cầu,NGUOITAO,140,0,f,l,1,2;" +
        "<PERSON><PERSON><PERSON><PERSON>,NGUOITAO,140,0,f,l,1,2";

    var _gridDuyetMo = "grdDSDuyetMo";
    var _gridDuyetMoHeader = "LICHMOID,LICHMOID,50,0,t,l,1,2;" +
        "BENHNHANID,BENHNHANID,50,0,t,l,1,2;" +
        "KHAMBENHID,KHAMBENHID,50,0,t,l,1,2;" +
        "HOSOBENHANID,HOSOBENHANID,50,0,t,l,1,2;" +
        "FLAG_CA,FLAG_CA,50,0,t,l,1,2;" +
        " ,ICON,35,0,ns,l;" +
        "Mã BA,MAHOSOBENHAN,120,0,f,l,1,2;" +
        "Tên bệnh nhân,TENBENHNHAN,120,0,f,l,1,2;" +
        "Trạng thái,TRANGTHAI,100,0,f,l,1,2;" +
        "Thời gian mổ dự kiến,THOIGIAN_MO,120,0,f,l,1,2;" +
        "Ngày yêu cầu,NGAYTAO,80,0,f,l,1,2;" +
        "Khoa yêu cầu,KHOA,200,0,f,l,1,2;" +
        "Phòng thực hiện,PHONG_MO_FULLNAME,200,0,f,l,1,2;" +
        "Bác sĩ chính,BS_CHINH,120,0,f,l,1,2;" +
        "Bác sĩ yêu cầu,NGUOITAO,140,0,f,l,1,2;" +
        "Người duyệt,NGUOITAO,140,0,f,l,1,2";

    var _lichmoid = '';
    var _hosobenhanid = '';
    var _benhnhanid = '';
    var _khambenhid = '';
    this.load = doLoad;
    var checkRequired;
    var _trangthai = '';
    var isPtvShowAll = false;
    var _showlm = '';
    var _rpt_code_kyso = "BC_DUYET_MO";

    var isModeDAKHOAQUANGTRI = false;
	var chkNGAYDUYETMO = 0; // L2PT-8237
    var lstLichmoId = [];
    var cf = new Object();

    /*
        cac noi dung isModeDAKHOAQUANGTRI:
        - màn mặc định (cấu hình NTU_LICHMO_LCI = 0; PTTT_PTV_SHOWALL = 0)
		- chỉ cảnh báo Thời gian mổ và Thời gian khám mê trước ngày hiện tại! (không cần chặn)
		- Hiển thị nội dung Thời gian kết thúc dự kiến
		- Thay thế nội dung tìm kiếm theo ngày bằng Từ ngày, đến ngày
		- Hiển thị nút Xuất excel danh sách duyệt mổ
    */


    if (_opts.hospital_id == 30360) {
        isModeDAKHOAQUANGTRI = true;
    }

    function doLoad(_hosp_id) {
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        _initControl();
        _bindEvent();

        var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
        if (kyso_kydientu != "1") {
            $('#' + 'btnKySo').remove();
            $('#' + 'btnHuyKy').remove();
            $('#' + 'btnInKySo').remove();
        }

        if (isModeDAKHOAQUANGTRI) {
            $('#divTHOIGIANKETTHUC').show();
            $('#divTIMTHEONGAYCUTHE').hide();
            $('#divTIMTHEOTUNGAYDENNGAY').show();
            $("#txtTUNGAY").val(moment().format('DD/MM/YYYY'));
            $("#txtDENNGAY").val(moment().format('DD/MM/YYYY'));
        }

        // $('#divTHOIGIANKETTHUC').show();
        // $('#div_khamme').show();
        // $('#divKhamMeMini').show();
        // $('#btnKhamDuyet').show();
        // $('#btnIn').show();
        // $('#divTIMTHEONGAYCUTHE').show();
        // $('#divTIMTHEOTUNGAYDENNGAY').show();
        // $('#btnEXCEL').show();

        // xu ly cbokhoa
        // neu khoa hien tai la khoa cua cau hinh NTU_GET_MAKHOA_PTGM hoac tai khoan hien tai nam trong danh sach cau hinh NTU_DMKM_TAIKHOAN
        // thi duoc phép thay all khoa
        if (isModeDAKHOAQUANGTRI && !isViewAllKhoaVaDuyet()) {
            var option = $('<option value="' + _opts.dept_id + '">' + _opts.dept_name + '</option>');
            $("#cboKHOA").empty();
            $("#cboKHOA").append(option);
        } else {
            loadCboKhoa();
        }

        // nut duyet va huy duyet
        // neu khoa hien tai la khoa cua cau hinh NTU_GET_MAKHOA_PTGM hoac tai khoan hien tai nam trong danh sach cau hinh NTU_DMKM_TAIKHOAN
        // thi duoc thay nut duyet va huy duyet
        if (isModeDAKHOAQUANGTRI && !isViewAllKhoaVaDuyet()) {
            $('#btnDuyet').hide();
            $('#btnHuyDuyet').hide();
            $('#btnKySo').hide();
            $('#btnHuyKy').hide();
            $('#btnInKySo').hide();
        }

        setTimeout(loadGridData, 1000);

    }

    function loadCboKhoa(){
        ComboUtil.getComboTag("cboKHOA", 'NTU02D132.11', [], "", {
            text: "--- Tất cả ---",
            value: -1
        }, 'sql', '', function () {
        });
    }

    function _initControl() {
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "PTTT_PTV_SHOWALL;HIS_QUYTRINH_LENLICHMO;HIS_LICHMO_TGLENLICH");
        if (data_ar != null && data_ar.length > 0) {
            cf = data_ar[0];
        }

        GridUtil.init(_gridLSM, "100%", "440", "Lịch sử duyệt mổ", false, _gridLSMHeader, false);
        GridUtil.init(_gridDuyetMo, "100%", "200", "Danh sách duyệt mổ", true, _gridDuyetMoHeader, false);

        _showlm = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_LICHMO_LCI');
        if (_showlm == '3') {
            $("#div_khamme").show();
            $("#divKhamMeMini").show();
            $("#btnKhamDuyet").show();
            $("#btnIn").show();
            GridUtil.init(_gridLSM, "100%", "590", "Lịch sử mổ", false, _gridLSMHeader, false);
        }

        var _colICD = "ORG_ID,ORG_ID,0,0,t,l;Phòng,PHONG,50,0,f,l;Khoa,KHOA,50,0,f,l";
        var _makhoa = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_GET_MAKHOA_PTGM');
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": _makhoa});
        var sqlPhongMo = "NTU02D132.12";
        // da khoa quang tri chi lay cac phong co loai la phong mo
        if (_opts.hospital_id == 30360) {
            sqlPhongMo = "NTU02D132.L01";
        }
        ComboUtil.initComboGrid("txtPHONG_MO_ID", sqlPhongMo, sql_par, "500px", _colICD, function (event, ui) {
            $("#txtPHONG_MO_ID").val("");
            var option = $('<option value="' + ui.item.ORG_ID + '">' + ui.item.PHONG + '</option>');
            $("#cboPHONG_MO").empty();
            $("#cboPHONG_MO").append(option);
            return false;
        });

        if (cf.PTTT_PTV_SHOWALL == '1') {
            isPtvShowAll = true;
        }
        if (cf.HIS_QUYTRINH_LENLICHMO == '1') {
            $("#cboTRANGTHAIPHIEU").val('4');
        }
        loadComboGrid();
    };

    function _bindEvent() {

        GridUtil.setGridParam(_gridDuyetMo, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(_gridDuyetMo);
                GridUtil.markRow(_gridDuyetMo, id);
                if (id) {
                    var _row = $("#" + _gridDuyetMo).jqGrid('getRowData', id);
                    _lichmoid = _row.LICHMOID;
                    _hosobenhanid = _row.HOSOBENHANID;
                    _benhnhanid = _row.BENHNHANID;
                    _khambenhid = _row.KHAMBENHID;
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D132.02", _lichmoid);
                    if (data_ar != null && data_ar.length > 0) {
                        var row = data_ar[0];
                        FormUtil.clearForm("inputForm", "");
                        FormUtil.setObjectToForm("inputForm", "", row);
                        viewTT(row);
                        loadGridLichSu();
                    }
                    // luu vao danh sach lịch mổ
                    if (status && lstLichmoId.indexOf(_row.LICHMOID) == -1 && _row.LICHMOID != null) {
                        lstLichmoId.push(_row.LICHMOID);
                    }
                    if (!status) {
                        var j = lstLichmoId.indexOf(_row.LICHMOID);
                        if (j != -1) {
                            lstLichmoId.splice(j, 1);
                        }
                    }
                }
            },
            onSelectAll: function (id, status) {
                var rowIds = $("#" + _gridDuyetMo).jqGrid('getDataIDs');
                for (var i = 0; i < rowIds.length; i++) {
                    var rowData = $("#" + _gridDuyetMo).jqGrid('getRowData', rowIds[i]);
                    if (status && _gridDuyetMo.indexOf(rowData["LICHMOID"]) == -1 && rowData["LICHMOID"] != null) {
                        lstLichmoId.push(rowData.LICHMOID);
                    }
                    if (!status) {
                        var j = _gridDuyetMo.indexOf(rowData["LICHMOID"]);
                        if (j != -1) {
                            lstLichmoId.splice(j, 1);
                        }
                    }
                }
            },
            gridComplete: function (id) {
                var ids = $("#" + _gridDuyetMo).getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#" + _gridDuyetMo).jqGrid('getRowData',id);
                    var _icon = '';
                    if(row.FLAG_CA == '1'){
                        _icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
                    }
                    $("#" + _gridDuyetMo).jqGrid ('setCell', id, 'ICON', _icon);
                }
            }
        });

        GridUtil.setGridParam(_gridLSM, {
            onSelectRow: function (id) {
                GridUtil.unmarkAll(_gridLSM);
                GridUtil.markRow(_gridLSM, id);
                if (id) {
                    var _row = $("#" + _gridLSM).jqGrid('getRowData', id);
                    _lichmoid = _row.LICHMOID;
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D132.02", _lichmoid);
                    if (data_ar != null && data_ar.length > 0) {
                        var row = data_ar[0];
                        FormUtil.clearForm("inputForm", "");
                        FormUtil.setObjectToForm("inputForm", "", row);
                        viewTT(row);
                    }
                }
            }
        });

        $("#btnTIMKIEM").click(function () {
            loadGridData();
        });

        $("#btnKhamDuyet").click(function () {
            if (_lichmoid == '') {
                DlgUtil.showMsg("Chưa chọn lịch mổ !");
            } else {
                var paramInput = {
                    lichmoid: _lichmoid,
                    hosobenhanid: _hosobenhanid,
                    khambenhid: _khambenhid
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgKDM", "divDlg", "manager.jsp?func=../noitru/NTU02D180_KhamDuyetMo", paramInput, "Khám duyệt mổ", 1100, 650);
                DlgUtil.open("dlgKDM");
            }
        });

        $("#btnDuyet").click(function () {
            _trangthai = '1';
            duyetMo();
        });

        $("#btnIn").click(function () {
            if (_lichmoid == '') {
                DlgUtil.showMsg("Chưa chọn lịch mổ !");
                return;
            } else {
                var par = [{
                    name: 'i_lichmoid',
                    type: 'String',
                    value: _lichmoid
                }];
                openReport('window', "BC_DUYET_MO", "pdf", par);
            }
        });

        $("#btnHuyDuyet").click(function () {
            $("#cboPHONG_MO").removeAttr("valrule");
            $("#txtTHOIGIAN_KHAMME").removeAttr("valrule");
            _trangthai = '2';
            duyetMo();
        });

        $("#btnVIEW_HOICHAN").click(function () {
            $('#dsLSDuyetMo').hide();
            $('#search').hide();
            $('#dsDuyetMo').hide();
            $('#closeHoiChuan').show();
            $('#tcHoiChuan').show();
            $('#tcHoiChuan').ntu02d032_phc({
                _grdHoiChan: 'grdPhieuHoiChuan',
                _khambenhid: _khambenhid,
                _benhnhanid: _benhnhanid,
                _lnmbp: "15",
                _modeView: "1", // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        $("#btnCloseHoiChuan").click(function () {
            $('#dsLSDuyetMo').show();
            $('#search').show();
            $('#dsDuyetMo').show();
            $('#closeHoiChuan').hide();
            $('#tcHoiChuan').hide();
            $('#txtMACHANDOAN').focus();
        });

        $('#btnCLEARBS_CHINH').on("click", function () {
            $("#txtBS_CHINH_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_CHINH").empty();
            $("#cboBS_CHINH").append(option);
        });

        $('#btnCLEARBS_PHU1').on("click", function () {
            $("#txtBS_PHU1_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_PHU1").empty();
            $("#cboBS_PHU1").append(option);
        });

        $('#btnCLEARBS_PHU2').on("click", function () {
            $("#txtBS_PHU2_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_PHU2").empty();
            $("#cboBS_PHU2").append(option);
        });

        $('#btnCLEARBS_PHU3').on("click", function () {
            $("#txtBS_PHU3_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_PHU3").empty();
            $("#cboBS_PHU3").append(option);
        });

        $('#btnCLEARVC_CHINH').on("click", function () {
            $("#txtVC_CHINH_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVC_CHINH").empty();
            $("#cboVC_CHINH").append(option);
        });

        $('#btnCLEARVC_PHU').on("click", function () {
            $("#txtVC_PHU_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVC_PHU").empty();
            $("#cboVC_PHU").append(option);
        });

        $('#btnCLEARXEP_LICH').on("click", function () {
            $("#txtXEP_LICH_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboXEP_LICH").empty();
            $("#cboXEP_LICH").append(option);
        });

        $('#btnCLEARHUU_TRUNG').on("click", function () {
            $("#txtHUU_TRUNG_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboHUU_TRUNG").empty();
            $("#cboHUU_TRUNG").append(option);
        });

        $('#btnCLEARBS_GAYMECHINH').on("click", function () {
            $("#txtGAYMECHINH_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboGAYMECHINH").empty();
            $("#cboGAYMECHINH").append(option);
        });

        $('#btnCLEARBS_PHUME').on("click", function () {
            $("#txtPHUME_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboPHUME").empty();
            $("#cboPHUME").append(option);
        });

        $('#btnCLEARBS_DCVONGTRONG').on("click", function () {
            $("#txtDCVONGTRONG_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboDCVONGTRONG").empty();
            $("#cboDCVONGTRONG").append(option);
        });

        $('#btnCLEARBS_DCVONGNGOAI').on("click", function () {
            $("#txtDCVONGNGOAI_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboDCVONGNGOAI").empty();
            $("#cboDCVONGNGOAI").append(option);
        });

        $('#btnCLEARBS_BSHUONGDANGAYME').on("click", function () {
            $("#txtBSHUONGDANGAYME_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBSHDGAYME").empty();
            $("#cboBSHDGAYME").append(option);
        });

        $("#btnKySo").on("click", function (e) {
            _kySo();
        });
        $("#btnHuyKy").on("click", function (e) {
            _huyKySo();
        });
        $("#btnInKySo").on("click", function (e) {
            _inPhieuKySo();
        });

        $('#cboTRANGTHAIPHIEU').change(function () {
            $('#btnTIMKIEM').click();
        });

        $('#cboKHOA').change(function () {
            $('#btnTIMKIEM').click();
        });
		
		// L2PT-8237 start
		$('#chkNGAYDUYETMO').click(function() {
			if ($("#chkNGAYDUYETMO").is(':checked')) {
				chkNGAYDUYETMO = 1;
			}else{
				chkNGAYDUYETMO = 0;
			}
		});
		// L2PT-8237 end
		
        $("#btnEXCEL").click(function () {
            var par = [
                {
                    name: 'i_tungay',
                    type: 'String',
                    value: $('#txtTUNGAY').val() + ''
                },
                {
                    name: 'i_denngay',
                    type: 'String',
                    value: $('#txtDENNGAY').val() + ''
                },
                {
                    name: 'i_khoaid',
                    type: 'String',
                    value: $('#cboKHOA').val() + ""
                },
                {
                    name: 'i_trangthai',
                    type: 'String',
                    value: $('#cboTRANGTHAIPHIEU').val() + ""
                },
                { 
                    name: 'i_type',
                    type: 'String',
                    value: chkNGAYDUYETMO + "" // L2PT-8237
                },
                {
                    name: 'i_lst_lichmoid',
                    type: 'String',
                    value: lstLichmoId + "" // L2PT-18563
                }
            ];
            var rpName = "RPT_DANHSACH_BENHNHAN_DUYETMO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "RPT_DANHSACH_BENHNHAN_DUYETMO", 'xlsx', par, rpName);
        });
    }

    function _kySo() {
        if (_lichmoid == '') {
            DlgUtil.showMsg("Chưa chọn lịch mổ !");
            return;
        }
        _caRpt('1');
    }

    function _huyKySo() {
        if (_lichmoid == '') {
            DlgUtil.showMsg("Chưa chọn lịch mổ !");
            return;
        }
        _caRpt('2');
    }

    function _inPhieuKySo() {
        if (_lichmoid == '') {
            DlgUtil.showMsg("Chưa chọn lịch mổ !");
            return;
        }

        var par_rpt_KySo = [ {
            name : 'HOSOBENHANID',
            type : 'String',
            value : _hosobenhanid
        },{
            name : 'I_LICHMOID',
            type : 'String',
            value : _lichmoid
        }
        ];

        par_rpt_KySo.push({
            name : 'RPT_CODE',
            type : 'String',
            value : _rpt_code_kyso
        });

        CommonUtil.openReportGetCA2(par_rpt_KySo, false);
    }

    function _caRpt(signType) {
        var par_rpt_KySo = [];
        par_rpt_KySo = [ {
            name : 'HOSOBENHANID',
            type : 'String',
            value : _hosobenhanid
        },{
            name : 'I_LICHMOID',
            type : 'String',
            value : _lichmoid
        }];
        par_rpt_KySo.push({
            name : 'RPT_CODE',
            type : 'String',
            value : _rpt_code_kyso
        });
        //ky
        CommonUtil.kyCA(par_rpt_KySo, signType, true, true);
		EventUtil.setEvent("eventKyCA",function(e){
			var _code = e.res.split("|")[0];
			var _msg = e.res.split("|")[1];
			if(_code == '0') {
				$("#btnKySo").prop('disabled', signType=="1");
				$("#btnHuyKy").prop('disabled', signType=="2");
			}
			DlgUtil.showMsg(_msg);
		});
		loadGridData();
    }

    function duyetMo() {
        var validator = new DataValidator("inputForm");
        var valid = validator.validateForm();
        if (valid) {
            checkdata();
            if (checkRequired == 0) {
                var objData = new Object();
                FormUtil.setFormToObject("inputForm", "", objData);
                objData["LICHMOID"] = _lichmoid;
                objData["TRANGTHAI"] = _trangthai;
                var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D132.03", JSON.stringify(objData));
                if (fl == 1) {
                    if (_trangthai == 1) {
                        DlgUtil.showMsg("Duyệt mổ thành công !");
                    } else {
                        DlgUtil.showMsg("Hủy mổ thành công !");
                    }
                    loadGridData();
                    loadGridLichSu();
                } else if (fl == 2) {
                    DlgUtil.showMsg("Đã duyệt khám mê, không thể duyệt/hủy duyệt !");
                } else {
                    DlgUtil.showMsg("Không thành công !");
                }
            }
        }
    }

    function loadGridData() {
        // var _sql_par =
        //     [
        //         {"name": "[0]", "value": $("#txtNGAYTAO").val()},
        //         {"name": "[1]", "value": $("#cboTRANGTHAIPHIEU").val()},
        //         {"name": "[2]", "value": $("#cboKHOA").val()}
        //     ];
        // GridUtil.loadGridBySqlPage(_gridDuyetMo, "NTU02D132.01", _sql_par, function () {
        //     $(".jqgrow", '#' + _gridDuyetMo).contextMenu(
        //         'contextMenu_BA',
        //         {
        //             bindings: {
        //                 // Hủy phiếu thu trên grid
        //                 'rLichSuBenhAn': function () {
        //                     paramInput = {
        //                         benhnhanId: _benhnhanid,
        //                         khambenhid: _khambenhid
        //                     };
        //                     dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn", paramInput, "Lịch sử bệnh án", 1320, 610);
        //                     DlgUtil.open("dlgLichSuBenhAn");
        //                 }
        //             },
        //             onContextMenu: function (event, menu) {
        //                 var rowId = $(event.target).parent("tr").attr("id");
        //                 var grid = $('#' + _gridDuyetMo);
        //                 grid.setSelection(rowId);
        //                 return true;
        //             }
        //         }
        //     );
        // });
        var obj = {
            ngayTao: $("#txtNGAYTAO").val() + "",
            tuNgay: $("#txtTUNGAY").val() + "",
            denNgay: $("#txtDENNGAY").val() + "",
            trangThaiPhieu: $("#cboTRANGTHAIPHIEU").val() + "",
            khoa: $("#cboKHOA").val() + "",
        }
        var param = [{name: "[0]", value: JSON.stringify(obj)}];
        GridUtil.loadGridBySqlPage("grdDSDuyetMo", "NTU02D132.01.2", param, function () {
            $(".jqgrow", '#grdDSDuyetMo').contextMenu(
                'contextMenu_BA',
                {
                    bindings: {
                        // Hủy phiếu thu trên grid
                        'rLichSuBenhAn': function () {
                            paramInput = {
                                benhnhanId: _benhnhanid,
                                khambenhid: _khambenhid
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn", paramInput, "Lịch sử bệnh án", 1320, 610);
                            DlgUtil.open("dlgLichSuBenhAn");
                        }
                    },
                    onContextMenu: function (event, menu) {
                        var rowId = $(event.target).parent("tr").attr("id");
                        var grid = $('#' + _gridDuyetMo);
                        grid.setSelection(rowId);
                        return true;
                    }
                }
            );
        }); // thay đổi page size mặc định chạy vào đây
    }

    function loadGridLichSu() {
        var _sql_par =
            [
                {"name": "[0]", "value": _hosobenhanid}
            ];
        GridUtil.loadGridBySqlPage(_gridLSM, "NTU02D132.04", _sql_par);
    }

    function loadComboGrid() {
        var sql_par = [];
        if (isPtvShowAll) {
            sql_par.push({"name": "[0]", "value": 0}, {"name": "[1]", "value": _opts.dept_id});
        } else {
            sql_par.push({"name": "[0]", "value": 1}, {"name": "[1]", "value": _opts.dept_id});
        }

        // buu dien ho chi minh
        if (_opts.hospital_id == 965) {
            sql_par = [];
            sql_par.push({"name": "[0]", "value": _opts.hospital_id}, {"name": "[1]", "value": 0});
        }
        var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
        ComboUtil.initComboGrid("txtBS_CHINH_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBS_CHINH_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboBS_CHINH").empty();
            $("#cboBS_CHINH").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtBS_PHU1_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBS_PHU1_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboBS_PHU1").empty();
            $("#cboBS_PHU1").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtBS_PHU2_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBS_PHU2_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboBS_PHU2").empty();
            $("#cboBS_PHU2").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtBS_PHU3_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBS_PHU3_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboBS_PHU3").empty();
            $("#cboBS_PHU3").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtVC_CHINH_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtVC_CHINH_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboVC_CHINH").empty();
            $("#cboVC_CHINH").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtVC_PHU_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtVC_PHU_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboVC_PHU").empty();
            $("#cboVC_PHU").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtXEP_LICH_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtXEP_LICH_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboXEP_LICH").empty();
            $("#cboXEP_LICH").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtHUU_TRUNG_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtHUU_TRUNG_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboHUU_TRUNG").empty();
            $("#cboHUU_TRUNG").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtGAYMECHINH_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtGAYMECHINH_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboGAYMECHINH").empty();
            $("#cboGAYMECHINH").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtPHUME_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtPHUME_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboPHUME").empty();
            $("#cboPHUME").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtDCVONGTRONG_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtDCVONGTRONG_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboDCVONGTRONG").empty();
            $("#cboDCVONGTRONG").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtDCVONGNGOAI_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtDCVONGNGOAI_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboDCVONGNGOAI").empty();
            $("#cboDCVONGNGOAI").append(option);
            return false;
        });
        ComboUtil.initComboGrid("txtBSHUONGDANGAYME_ID", "PTTT.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
            $("#txtBSHUONGDANGAYME_ID").val("");
            var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
            $("#cboBSHDGAYME").empty();
            $("#cboBSHDGAYME").append(option);
            return false;
        });
    }

    function checkdata() {
        checkRequired = 0;
        var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');

		if (cf.HIS_LICHMO_TGLENLICH != 1) {
			if (!compareDate(_ngayhientai, $("#txtTHOIGIAN_MO").val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
	            if (!isModeDAKHOAQUANGTRI) {
	                checkRequired = 1;
	                DlgUtil.showMsg("Thời gian mổ không được phép trước ngày hiện tại!");
	                $('#txtTHOIGIAN_MO').focus();
	                return;
	            } else {
	                DlgUtil.showMsg("Thời gian mổ trước ngày hiện tại!");
	            }
	        }
	
	        if (!compareDate(_ngayhientai, $("#txtTHOIGIAN_KHAMME").val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
	            if (!isModeDAKHOAQUANGTRI) {
	                checkRequired = 1;
	                DlgUtil.showMsg("Thời gian khám mê dự kiến không được phép trước ngày hiện tại!");
	                $('#txtTHOIGIAN_KHAMME').focus();
	                return;
	            } else {
	                DlgUtil.showMsg("Thời gian khám mê dự kiến trước ngày hiện tại!");
	            }
	
	        }
		}


        if ($("#cboPHONG_MO").val() == '-1' && _trangthai == 1) {
            checkRequired = 1;
            DlgUtil.showMsg("Phòng mổ chưa được chọn !");
            return;
        }
    }

    function viewTT(data) {
        if (data.PHIEU_HOI_CHAN == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboPHIEU_HOI_CHAN").empty();
            $("#cboPHIEU_HOI_CHAN").append(option);
        } else {
            var option = $('<option value="' + data.PHIEU_HOI_CHAN + '">' + data.PHIEU_HOI_CHAN + '</option>');
            $("#cboPHIEU_HOI_CHAN").empty();
            $("#cboPHIEU_HOI_CHAN").append(option);
        }
        if (data.PHONG_MO == '-1' || data.PHONG_MO == '') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboPHONG_MO").empty();
            $("#cboPHONG_MO").append(option);
        } else {
            var option = $('<option value="' + data.PHONG_MO + '">' + data.PHONG_MO_FULLNAME + '</option>');
            $("#cboPHONG_MO").empty();
            $("#cboPHONG_MO").append(option);
        }
        if (data.BS_CHINH != '-1') {
            var option = $('<option value="' + data.BS_CHINH + '">' + data.BS_CHINH_FULLNAME + '</option>');
            $("#cboBS_CHINH").empty();
            $("#cboBS_CHINH").append(option);
        }
        if (data.BS_PHU1 == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_PHU1").empty();
            $("#cboBS_PHU1").append(option);
        } else {
            var option = $('<option value="' + data.BS_PHU1 + '">' + data.BS_PHU1_FULLNAME + '</option>');
            $("#cboBS_PHU1").empty();
            $("#cboBS_PHU1").append(option);
        }

        if (data.BS_PHU2 == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_PHU2").empty();
            $("#cboBS_PHU2").append(option);
        } else {
            var option = $('<option value="' + data.BS_PHU2 + '">' + data.BS_PHU2_FULLNAME + '</option>');
            $("#cboBS_PHU2").empty();
            $("#cboBS_PHU2").append(option);
        }
        if (data.BS_PHU3 == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_PHU3").empty();
            $("#cboBS_PHU3").append(option);
        } else {
            var option = $('<option value="' + data.BS_PHU3 + '">' + data.BS_PHU3_FULLNAME + '</option>');
            $("#cboBS_PHU3").empty();
            $("#cboBS_PHU3").append(option);
        }
        if (data.VC_CHINH == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVC_CHINH").empty();
            $("#cboVC_CHINH").append(option);
        } else {
            var option = $('<option value="' + data.VC_CHINH + '">' + data.VC_CHINH_FULLNAME + '</option>');
            $("#cboVC_CHINH").empty();
            $("#cboVC_CHINH").append(option);
        }
        if (data.VC_PHU == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVC_PHU").empty();
            $("#cboVC_PHU").append(option);
        } else {
            var option = $('<option value="' + data.VC_PHU + '">' + data.VC_PHU_FULLNAME + '</option>');
            $("#cboVC_PHU").empty();
            $("#cboVC_PHU").append(option);
        }
        if (data.XEP_LICH == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboXEP_LICH").empty();
            $("#cboXEP_LICH").append(option);
        } else {
            var option = $('<option value="' + data.XEP_LICH + '">' + data.XEP_LICH_FULLNAME + '</option>');
            $("#cboXEP_LICH").empty();
            $("#cboXEP_LICH").append(option);
        }
        if (data.HUU_TRUNG == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboHUU_TRUNG").empty();
            $("#cboHUU_TRUNG").append(option);
        } else {
            var option = $('<option value="' + data.HUU_TRUNG + '">' + data.HUU_TRUNG_FULLNAME + '</option>');
            $("#cboHUU_TRUNG").empty();
            $("#cboHUU_TRUNG").append(option);
        }
        if (data.BS_HOICHAN == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_HOICHAN").empty();
            $("#cboBS_HOICHAN").append(option);
        } else {
            var option = $('<option value="' + data.BS_HOICHAN + '">' + data.BS_HOICHAN_FULLNAME + '</option>');
            $("#cboBS_HOICHAN").empty();
            $("#cboBS_HOICHAN").append(option);
        }
        if (data.BS_TANGCUONG == '-1') {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBS_TANGCUONG").empty();
            $("#cboBS_TANGCUONG").append(option);
        } else {
            var option = $('<option value="' + data.BS_TANGCUONG + '">' + data.BS_TANGCUONG_FULLNAME + '</option>');
            $("#cboBS_TANGCUONG").empty();
            $("#cboBS_TANGCUONG").append(option);
        }
        if (data.GAYMECHINH != '-1' && typeof data.GAYMECHINH != 'undefined') {
            var option = $('<option value="' + data.GAYMECHINH + '">' + data.GAYMECHINH_FULLNAME + '</option>');
            $("#cboGAYMECHINH").empty();
            $("#cboGAYMECHINH").append(option);
        } else {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboGAYMECHINH").empty();
            $("#cboGAYMECHINH").append(option);
        }
        if (data.PHUME != '-1' && typeof data.PHUME != 'undefined') {
            var option = $('<option value="' + data.PHUME + '">' + data.PHUME_FULLNAME + '</option>');
            $("#cboPHUME").empty();
            $("#cboPHUME").append(option);
        } else {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboPHUME").empty();
            $("#cboPHUME").append(option);
        }
        if (data.DCVONGTRONG != '-1' && typeof data.DCVONGTRONG != 'undefined') {
            var option = $('<option value="' + data.DCVONGTRONG + '">' + data.DCVONGTRONG_FULLNAME + '</option>');
            $("#cboDCVONGTRONG").empty();
            $("#cboDCVONGTRONG").append(option);
        } else {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboDCVONGTRONG").empty();
            $("#cboDCVONGTRONG").append(option);
        }
        if (data.DCVONGNGOAI != '-1' && typeof data.DCVONGNGOAI != 'undefined') {
            var option = $('<option value="' + data.DCVONGNGOAI + '">' + data.DCVONGNGOAI_FULLNAME + '</option>');
            $("#cboDCVONGNGOAI").empty();
            $("#cboDCVONGNGOAI").append(option);
        } else {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboDCVONGNGOAI").empty();
            $("#cboDCVONGNGOAI").append(option);
        }
        if (data.BSHDGAYME != '-1' && typeof data.BSHDGAYME != 'undefined') {
            var option = $('<option value="' + data.BSHDGAYME + '">' + data.BSHDGAYME_FULLNAME + '</option>');
            $("#cboBSHDGAYME").empty();
            $("#cboBSHDGAYME").append(option);
        } else {
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboBSHDGAYME").empty();
            $("#cboBSHDGAYME").append(option);
        }
    }

    // dựa vào khoa hiện tại và tài khoản hiện tại, kiểm tra xem có được phép thấy all khoa và nút duyệt, hủy duyệt hay không
    function isViewAllKhoaVaDuyet(){
        var obj = {
            khoa: _opts.dept_id + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D132.L02", param);
        if (result == '1') {
            return true;
        } else {
            return false;
        }
        debugger;
    }
}
