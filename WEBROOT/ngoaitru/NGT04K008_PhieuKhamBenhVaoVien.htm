<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
       value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
        src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
        src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
      href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
      rel="stylesheet"/>
<script type="text/javascript"
        src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>

<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script src="../common/script/jqgrid/js/context-menu.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT04K008_PhieuKhamBenhVaoVien.js?v=220922"></script>
<div class="container">
    <div class="col-md-12 low-padding mgt5">
        <div id="inputFormPTCK" class="panel panel-default">
            <div class="panel-body">
                <div class="col-xs-12 mgt5" style="" >
                    <div class="col-xs-4 low-padding required">
                        <label class="mgl10" id="lblloaithuoc">Bác sỹ kê đơn</label>
                    </div>
                    <div class="col-xs-3 form-inline low-padding">
                        <input class="form-control input-sm" id="txtTKBACSIID" name="txtTKBACSIID" title="" style="width: 100%;">
                    </div>
                    <div class="col-xs-5 form-inline low-padding">
                        <select class="form-control input-sm" id="cboBACSIID" filterlike="txtTKBACSIID" style="width:100%;"></select>
                    </div>
                </div>
                <div class="col-xs-12 low-padding mgt10 mgb10" style="text-align: center;">

                    <button type="button" class="btn btn-sm btn-primary" id="btnIn">
                        <span class="glyphicon glyphicon-edit"></span> In phiếu
                    </button>
                    <button class="btn btn-default btn-primary" id="btnKyCa" style="display: none">
                        <span class="glyphicon glyphicon-ok"></span> Ký & In
                    </button>
                    <button class="btn btn-default btn-primary" id="btnHuyCa" style="display: none">
                        <span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
                    </button>
                    <button class="btn btn-default btn-primary" id="btnExportCa" style="display: none">
                        <span class="glyphicon glyphicon-print"></span> In ký số
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" id="btnClose">
                        <span class="glyphicon glyphicon-remove-circle"></span> Đóng
                    </button>
                </div>
            </div>

        </div>
    </div>
    <div id="popupId"></div>
</div>
<script>
    var opt = [];
    var schema = '{db_schema}';
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var uuid = '{uuid}';
    var phongid = '{subdept_id}';
    var khoaid = '{dept_id}';
    var db_schema = '{db_schema}';

    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");
    parent.DlgUtil.tunnel(DlgUtil.moveEvent);
    objVar = EventUtil.getVar("dlgVar");
    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid;
    _opts._khambenhid = objVar.khambenhid;
    _opts._hosobenhanid = objVar.hosobenhanid;
    _opts._khoaid = khoaid;
    _opts._phongid = phongid;
    _opts.db_schema = db_schema;
    var DS = new dSCHList2(_opts);
    DS.load(hospital_id);
</script>