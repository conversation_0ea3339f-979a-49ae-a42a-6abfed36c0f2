function NTU02D037_PTTT(opt) {
	var _col = "Mã ICD,ICD10CODE,10,0,f,l;Tên VN,ICD10NAME,40,0,f,l;Tên <PERSON>,ICD10NAME_EN,50,0,f,l";
	var _sql = "CG.ICD10";
	var _colPTTT = "Mã PTTT,MAPTTT,25,0,f,l;Tên PTTT,TENPTTT,50,0,f,l";
	var _sqlPTTT = "PTTT.PHUONGPHAP";
	var _colCTPTTT = "Mã Cách thức PTTT,MADICHVU,25,0,f,l;T<PERSON><PERSON> cách thức PTTT,TENDICHVU,50,0,f,l";
	var _sqlCTPTTT = "PTTT_DICHVU";
	var _sqlPTTT_hang = "PTTT.HANG";
	var _sqlPTTT_vocam = "PTTT.VOCAM";
	var _sqlPTTT_taibien = "PTTT.TAIBIEN";
	var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;<PERSON><PERSON><PERSON>,USERNAME,33,0,f,l;<PERSON><PERSON><PERSON><PERSON>,FULLNAME,22,0,f,l;Ch<PERSON>c danh/Khoa phòng,CHUCDANH,42,0,f,l";
	var sql_par = [];
	var _phauthuatthuthuatid = "";
	var khoacdid = opt.khoaid;
	var _checkUpdate = 0;
	var tyle_pttt = '';
	var istextPttt = false;
	var that = this;
	this.load = doLoad;
	var isShowInfoTuongTrinh = false;
	var isPtvShowAll = false;
	var tinhhinhPTTTmacdinh = false;//START L2PT-962
	var isNhapChandoanchinh = false;//L1PT-1562
	var machuandoanvaokhoa, tenchuandoanvaokhoa = '';
	var machuandoantruocpt, tenchuandoantruocpt, machuandoansaupt, tenchuandoansaupt = '';//L2PT-9014
	var isNhapPTTT = false;
	var isGetICD_Bandau = false;//L2PT-7739
	var changeTextEkipNhiHDG = false;//L2PT-8920
	var machuandoanyhct = '', tenchuandoanyhtc = '';//L2PT-101148
	//START L2PT-4898
	this.luuAnh = luuAnh;
	this.xoaAnh = xoaAnh;
	this.lietKeAnh = lietKeAnh;
	//END L2PT-4898
	var checkktBA;//L2PT-16223
	var check_songuoi_thuchien, sl_nguoi, sl_giupviec = '', sl_nhanlucchinh = '', sl_nhanlucphu = '', sl_nhanlucphume = '', sl_gaymechinh = '';//L2PT-19388
	var hienthi_ekipduyetmo;
	//L2PT-20993
	var mau_pttt_theophongcd;
	var mdv_in_caychi_ydgli, madichvu_caychi = '';//L2PT-22996
	var form_pttt_bvnt, ppvc_default;//L2PT-25760
	var pp_pttt_dklsn;//L2PT-26618
	var kt_pttt_dklci;//L2PT-27254
	var get_nth_cdha_snvpc;//L2PT-28421
	var sql_load_user = '';//L2PT-31604
	var show_ekip_pttt;//START L2PT-32306
	var check_trungvitri_pttt;//L2PT-320600
	var ch_macdinhkhoa_cd;//L2PT-34204
	var ptv_chinh;//L2PT-34023
	var show_them_pmome; //L2PT-1404
	var isShift = false;//BVTM-3345
	var iskhoa_gmhs, isKethem;//BVTM-3412
	var loadEkip_checkbox = false;//BVTM-3454 mac dinh lay ekip theo khoa thiet lap
	var layhinhanh_mau = false;//L2PT-6207
	var req_mota = false; //L2PT-6951
	var tg_timeout_ready = 200;
	var luudong_print = false;//L2PT-9316
	var show_cbo_tgkt = false;//L2PT-9899
	var check_tg_tvt_trung = false;//BVTM-7086
	var mau_pttt_theop_ngtao = false;//L2PT-9826
	var load_nhommau_rh = false;//BVTM-7586
	var macdinh_nhap = false; // L2PT-123233
	var detrong_giothuocme = false;//L2PT-12898
	var ngaypttmacdinh_tgchidinh = false;//L2PT-14065
	var ngaypttmacdinh_add_sophut = '0';//L2PT-14065
	var luudongin = false;
	var gioihan_ekip = 0;//L2PT-14424
	var chonnhieu_ppvc = '0';//L2PT-14930
	var iskyso = false;
	var cfObj = new Object();
	var dichvuid;
	var _madichvu = '';//L2PT-44313
	var _ketquaclsid = ""; //L2PT-80602
	var files = []; //L2PT-60804
	let
	dlgPopup = "";
	var checkppvc = '0'; //L2PT-93277
	var checkbsgm = '0'; //L2PT-106738
	var _mamay_req = false; //L2PT-96184
	var check_tinhhinhpttt = '0', check_taibien = '0', check_tuvongtrongpttt = '0'; //L2PT-95386
	var check_nhommau = '0'; // L2PT-123233
	var _msgCA = '';
	function doLoad() {
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		// khoi tao man hinh
		initControl();
		//validate
		this.validator = new DataValidator("divMain");
		//xu ly nghiep vu
		bindEvent();
	}
	// ham khoi tao man hinh
	function initControl() {
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_CAUHINH_NHAPHANG_PTTT;HIS_PTTT_SHOW_INFO_TUONGTRINH;"
				+ "NTU_PTTT_NHIHDG;PTTT_PTV_SHOWALL;PTTT_BO_REQ_CHANDOAN_TRUOCSAU;TINHHINH_PTTT_MACDINH;SHOW_HINHANH_PTTT;"
				+ "NTU_LDTT_PTTT_BND;NTU_NHAP_CDC;NTU_NHAP_PTTT;GET_ICD_BANDAU;HIS_TENEKIP_NHIHDG;NTU_CHECK_KTBA;"
				+ "NTU_PTTT_CHECK_SONGUOI;NTU_PTTT_SHOWEKIP_DUYETMO;NTU_MAU_PTTT_PHONGCD;NTU_IN_CAYCHI_YDGLI;NTU_PTTT_BVNT;"
				+ "NTU_PTTT_PPVC_DEFAULT;NTU_PTTT_PPPT_DKLSN;NTU_PTTT_KTPT_DKLCI;NTU_PTTT_BTN_TT_BS;NTU_PTTT_DKLSN_PHUMO;"
				+ "NTU_PTTT_NTH_CDHA;NTU_PTTT_SHOW_ICD9CM;NTU_PTTT_SQL_LOADUSER;NTU_SHOW_EKIP_PTTT;NTU_CHECK_TRUNG_VITRI;"
				+ "NTU_PTTT_KCD_DEFAULT;NTU_PTTT_LOAD_MOME;NTU_PTTT_BDHN_KGMS;PTTT_HINHANH_MAU;NTU_PTTT_MOTA_REQ;NTU_PTTT_TGVV_QTI;"
				+ "NTU_PTTT_TIMOUT_READY;NTU_PTTT_DISABLE_PPPT;NTU_PTTT_CLOSE_PRINT;NTU_PTTT_CBO_TGKT;VPI_NGAYYL_PTTT;MABACSI_CAPNHAT_PTTT;"
				+ "NTU_PTTT_REQUIRE;NTU_BAC_LOADNHOMMAU_RH;NTU_PTTT_THUOCME_NULL;NTU_PTTT_MOTA_LENGTH;NTU_PTTT_NGAYPTTT_TGCD;" + "MAU_PTTT_TBH;" + //L2PT-47167
				"NTU_PTTT_CHECK_USER;" + //L2PT-47744
				"NTU_PTTT_ANNUT_KTBA;" + //L2PT-47936
				"NTU_PTTT_TGCD_GMHS;" + //L2PT-45297
				"NTU_PTTT_TT_QLP;" + //L2PT-51382
				"NTU_PTTT_CDHA_XN;" + //L2PT-56061
				"NTU_PTTT_PHONG_HAUPHAU;" + //L2PT-60506
				"NTU_PTTT_SLANH_MAX;" + //L2PT-61087
				"NTU_PTTT_BOTRONG_TIME;" + //L2PT-66580
				"NTU_PTTT_DONGBO_TIME_CDHA;" + //L2PT-69541
				"NTU_PTTT_NOT_KQ_CU;" + //L2PT-80709
				"NTU_PTTT_DONGBOMOTA_CDHA;" + //L2PT-80601
				"NTU_PTTT_DONGBOANH_CDHA;" + //L2PT-80602
				"NTU_PTTT_UPD_USER_LOCK;" + //L2PT-89368
				"NTU_PTTT_CHECK_KYTU;" + //L2PT-87493
				"NTU_PTTT_LOAD_KHOA_USER;" + //L2PT-97544
				"NTU_PTTT_MAMAY_REQ;" + //L2PT-96184
				"QD_4750;" + //L2PT-93277
				"NTU_PTTT_ENABLE;" + //L2PT-94152
				"NTU_PTTT_BDBD_REQ;" + //L2PT-99948
				"NTU_PTTT_MAMAY_RED;" + //L2PT-100076
				"NTU_PTTT_BSGM_REQUIRED;" + //L2PT-106738
				"HIS_SHOW_ICDYHCT;HIS_SHOW_ICDYHCT_PTTT;" + //L2PT-94682" 
				"NTU_PTTT_NHOMMAU_REQUIRED;NTU_PTTT_MACDINH_NHAPTT;" + // L2PT-123233
				"NTU_PTTT_ADD_SOPHUT;NTU_PTTT_SHOW_LUUDONGIN;NTU_PTTT_SHOW_GIUPVIEC2;NTU_PTTT_GIOIHAN_EKIP;NTU_PTTT_PPVC_MULTI;NTU_PTTT_YK_TK;"
				+ "NTU_PTTT_SHOW_ITEMS;NTU_PTTT_SOPHUT_THUOCME;MAU_PTTT_THEO_NGUOITAO;HIS_CHOPHEP_UPD_PTTT_HT;NTU_PTTT_MACDINH_PTVCHINH;NTU_PTTT_UPD_TGBANGNHAU;"
				+ "HIS_CHOPHEP_UPD_PTTT_KTBA;NTU_PTTT_MAMAY2;NTU_PTTT_REQ_COL;NTU_PTTT_CHIXEM;NTU_PTTT_KHOA_EKIP_UPD;NTU_PTTT_CDPTTT_MACDINH;"
				+ "NTU_PTTT_TGCD_ADD_SOPHUT;NTU_PTTT_KHOANGTG_PTTT;NTU_PTTT_THOIGIAN_PTTT_CBO;NTU_PTTT_TRUNGGIO_HUE;NTU_PTTT_DKLCI_TRUNGGIO;NTU_PTTT_LABEL_NAME;"
				+ "NTU_PTTT_HIDE_ITEMS;NTU_PTTT_CDP_DF_ICD;NTU_PTTT_CHECKSL_NHIHD;NTU_PTTT_KHONG_TRUNG_NGAY;HIS_TIMEOUT_THONGBAO;NTU_PTTT_CHECK_TRUNGTIME;NTU_PTTT_TRUNGGIO_HUE_EX;" + //START L2PT-962
				"NTU_PTTT_TINHHINH_REQUIRED;NTU_PTTT_TAIBIEN_REQUIRED;NTU_PTTT_TUVONG_REQUIRED;NTU_PTTT_SHOW_ICD9");////L2PT-95386
		if (data_ar != null && data_ar.length > 0) {
			cfObj = data_ar[0];
			//L2PT-35614
			if (cfObj.NTU_PTTT_KHOA_EKIP_UPD != '0' && cfObj.NTU_PTTT_KHOA_EKIP_UPD.indexOf(opt.dept_id) != '-1') {
				var sql_par_off = [];
				sql_par_off.push({
					"name" : "[0]",
					"value" : opt.user_id
				});
				var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTUD095.OFFICE2", sql_par_off);
				if (data2 != '[]') {
					var rows = JSON.parse(data2);
					var khoanhanvien = rows[0]["DEPT_ID"];
					if (khoanhanvien != opt.dept_id) {
						$("#divEkipChoose :input").attr("disabled", true);
					}
				}
			}
			//STAR L2PT-31284
			if (cfObj.HIS_CHOPHEP_UPD_PTTT_KTBA == '0' && opt.hospital_id == '10284') {
				// chi check khi call tu form ds cdha
				if (opt.callfrom != null & opt.callfrom == '1') {
					//check benh nhan da ra vien - ket thuc benh an
					sql_par = [ {
						"name" : "[0]",
						"value" : opt.tiepnhanid
					} ];
					var thangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("PTTT.CHECK_KTBA", sql_par);
					if (thangthaitiepnhan != '0' && cfObj.NTU_CHECK_KTBA == 1) {
						// dtbn là bhyt thi ko cho thao tac
						if (opt.doituongbenhnhanid != null && opt.doituongbenhnhanid == '1') {
							$('#divAction').hide();
						} else {
							// dtbn khac bhyt cho phep cap nhat ptt nhung khong cho chuyen hau phau
							$('#chkCHUYENHAUPHAU').prop('checked', false);
							$('#chkCHUYENHAUPHAU').prop('disabled', true);
						}
					}
				}
			}
			//L2PT-18361
			if (data_ar[0].NTU_PTTT_YK_TK == '1') {
				$('#divyktk').show();
			}
			//L2PT-14930
			chonnhieu_ppvc = data_ar[0].NTU_PTTT_PPVC_MULTI;
			//L2PT-14424
			if (data_ar[0].NTU_PTTT_SHOW_GIUPVIEC2 == '1') {
				$('#divGiupviec2').show();
				$("#lblgv").text("Giúp việc 1");
			}
			gioihan_ekip = data_ar[0].NTU_PTTT_GIOIHAN_EKIP;
			//L2PT-14065
			if (data_ar[0].NTU_PTTT_NGAYPTTT_TGCD == '1') {
				ngaypttmacdinh_tgchidinh = true;
			}
			if (data_ar[0].NTU_PTTT_SHOW_LUUDONGIN == '1') {
				$('#btnLuuDongIn').show();
			}
			ngaypttmacdinh_add_sophut = data_ar[0].NTU_PTTT_ADD_SOPHUT;
			// SET GIA TRI MAXLENGTH CHO txtMOTA
			if (data_ar[0].NTU_PTTT_MOTA_LENGTH > 0) {
				$("#txtMOTA").attr('maxlength', data_ar[0].NTU_PTTT_MOTA_LENGTH);
			}
			//tam thoi dat cau hinh L2PT-10996
			if (data_ar[0].NTU_PTTT_REQUIRE == '0') {
				$('#lblPTVChinh').removeClass('required');
				$("#cboPHAUTHUATVIEN").removeAttr("valrule");
			} else {
				$('#cboPHAUTHUATVIEN').find('option').remove();
			}
			//L2PT-12898
			if (data_ar[0].NTU_PTTT_THUOCME_NULL == '1') {
				detrong_giothuocme = true;
			}
			//BVTM-7586
			if (data_ar[0].NTU_BAC_LOADNHOMMAU_RH == '1') {
				load_nhommau_rh = true;
			}
			// L2PT-123233 start
			if (data_ar[0].NTU_PTTT_MACDINH_NHAPTT == '1') {
				macdinh_nhap = true;
			}
			// L2PT-123233 end
			//BVTM-7086
			check_tg_tvt_trung = data_ar[0].VPI_NGAYYL_PTTT;
			//L2PT-9899
			if (data_ar[0].NTU_PTTT_CBO_TGKT == '1') {
				show_cbo_tgkt = true;
				$('#divTG_PTTT_CBO').show();
			}
			//L2PT-9316
			if (data_ar[0].NTU_PTTT_CLOSE_PRINT == '1') {
				luudong_print = true;
			}
			if (data_ar[0].HIS_CAUHINH_NHAPHANG_PTTT == '1') {
				istextPttt = true;
				$('#txtPTTT_Hang').show();
				$('#cboPTTT_HangID').hide();
			}
			if (data_ar[0].HIS_PTTT_SHOW_INFO_TUONGTRINH == '1') {
				isShowInfoTuongTrinh = true;
				$('#divInfoNew').show();
			} else {
				$('#divInfoNew').hide();
			}
			//L1PT-1562
			if (data_ar[0].NTU_NHAP_CDC == '1') {
				isNhapChandoanchinh = true;
			}
			//START -- HISL2TK-691 --hongdq
			if (data_ar[0].NTU_PTTT_NHIHDG == '1') {
				$('#divLuocdoPTTT').show();
				$('#divTrintuPTTT').show();
			} else {
				$('#divLuocdoPTTT').hide();
				$('#divTrintuPTTT').hide();
			}
			//END -- HISL2TK-691 --hongdq
			//START L2PT-6521
			if (data_ar[0].NTU_LDTT_PTTT_BND == '1') {
				$('#btnLuuIn_BND').show();
				$('#divTrinhtuPTTT_BND').show();
				$('#divMota').hide();
			} else {
				$('#btnLuuIn_BND').hide();
				$('#divTrinhtuPTTT_BND').hide();
				$('#divMota').show();
			}
			//END L2PT-6521
			//Begin_HaNv_23102018: Hiển thị tất cả nhân viên ở ô Phẫu thuật viên 1 - L2HOTRO-11688
			if (data_ar[0].PTTT_PTV_SHOWALL == '1') {
				isPtvShowAll = true;
			}
			//End_HaNv_23102018
			//Begin_HaNv_05122018: Cấu hình bỏ required ở phần chẩn đoán trước pttt , sau pttt
			if (data_ar[0].PTTT_BO_REQ_CHANDOAN_TRUOCSAU == '1') {
				$('#divCDTruocPttt').removeClass('required');
				$('#divCDSauPttt').removeClass('required');
				$("#txtCHANDOANTRUOCPHAUTHUAT").removeAttr("valrule");
				$("#txtCHANDOANSAUPHAUTHUAT").removeAttr("valrule");
			}
			//End_HaNv_05122018
			//START L2PT-962
			if (data_ar[0].TINHHINH_PTTT_MACDINH == '1') {
				tinhhinhPTTTmacdinh = true;
			}
			//END L2PT-962
			//START L2PT-4898
			if (data_ar[0].SHOW_HINHANH_PTTT != null && data_ar[0].SHOW_HINHANH_PTTT == '1') {
				lietKeAnh();
				$("#divHinhanhPTTT").show();
			} else {
				$("#divHinhanhPTTT").hide();
			}
			//END L2PT-4898
			//L2PT-94152
			if (cfObj.NTU_PTTT_ENABLE != '0') {
				setEnabled(cfObj.NTU_PTTT_ENABLE.split(','), []);
			}
			//START L2PT-8042
			if (data_ar[0].NTU_NHAP_PTTT != null && data_ar[0].NTU_NHAP_PTTT == '1') {
				isNhapPTTT = true;
			}
			//L2PT-7739
			if (data_ar[0].GET_ICD_BANDAU != null && data_ar[0].GET_ICD_BANDAU == '1') {
				isGetICD_Bandau = true;
			}
			//L2PT-8920
			if (data_ar[0].HIS_TENEKIP_NHIHDG != null && data_ar[0].HIS_TENEKIP_NHIHDG == '1') {
				changeTextEkipNhiHDG = true;
			}
			//START L2PT-16223
			if (data_ar[0].NTU_CHECK_KTBA != null && data_ar[0].NTU_CHECK_KTBA == '1') {
				checkktBA = true;
			}
			//L2PT-19388
			if (data_ar[0].NTU_PTTT_CHECK_SONGUOI != null && data_ar[0].NTU_PTTT_CHECK_SONGUOI == '1') {
				check_songuoi_thuchien = true;
			}
			if (data_ar[0].NTU_PTTT_SHOWEKIP_DUYETMO != null && data_ar[0].NTU_PTTT_SHOWEKIP_DUYETMO == '1') {
				hienthi_ekipduyetmo = true;
			}
			// L2PT-20993
			if (data_ar[0].NTU_MAU_PTTT_PHONGCD != null && data_ar[0].NTU_MAU_PTTT_PHONGCD == '1') {
				mau_pttt_theophongcd = true;
			}
			//L2PT-22996
			if (data_ar[0].NTU_IN_CAYCHI_YDGLI != null) {
				mdv_in_caychi_ydgli = data_ar[0].NTU_IN_CAYCHI_YDGLI;
			}
			//L2PT-25760
			if (data_ar[0].NTU_PTTT_BVNT != null && data_ar[0].NTU_PTTT_BVNT == '1') {
				form_pttt_bvnt = data_ar[0].NTU_PTTT_BVNT;
			}
			if (!ppvc_default && data_ar[0].NTU_PTTT_PPVC_DEFAULT != null) {
				ppvc_default = data_ar[0].NTU_PTTT_PPVC_DEFAULT;
			}
			//L2PT-26618
			if (data_ar[0].NTU_PTTT_PPPT_DKLSN != null && data_ar[0].NTU_PTTT_PPPT_DKLSN == '1') {
				pp_pttt_dklsn = true;;
			}
			//L2PT-27254
			if (data_ar[0].NTU_PTTT_KTPT_DKLCI != null && data_ar[0].NTU_PTTT_KTPT_DKLCI == '1' && isShowInfoTuongTrinh) {
				kt_pttt_dklci = true;
				$("#div_KTPT_LCI").show();
				$("#div_KTPT").hide();
				$("#div_KTPT_LBL").show();
			}
			//L2PT-28073
			if (data_ar[0].NTU_PTTT_BTN_TT_BS != null && data_ar[0].NTU_PTTT_BTN_TT_BS == '1') {
				$("#divTT_BS").show();
			}
			//L2PT-28383
			if (data_ar[0].NTU_PTTT_DKLSN_PHUMO != null && data_ar[0].NTU_PTTT_DKLSN_PHUMO == '1') {
				$('#cboPHUMO1').attr('style', 'width: 43% !important');
				$("#cboPHUMO2").attr('style', 'width: 43% !important');
				$("#cboTILE_PHUMO1").show();
				$("#cboTILE_PHUMO2").show();
			}
			//L2PT-28421
			if (data_ar[0].NTU_PTTT_NTH_CDHA != null && data_ar[0].NTU_PTTT_NTH_CDHA == '1') {
				get_nth_cdha_snvpc = true;
			}
			//L2PT-29766
			if (data_ar[0].NTU_PTTT_SHOW_ICD9CM != null && data_ar[0].NTU_PTTT_SHOW_ICD9CM == '1') {
				$("#divICM9CM").show();
			}
			//L2PT-31604
			//L2PT-115494
			if (data_ar[0].NTU_PTTT_SHOW_ICD9 != null && data_ar[0].NTU_PTTT_SHOW_ICD9 == '1') {
				$("#divICD9_BVNT").show();
			}
			if (data_ar[0].NTU_PTTT_SQL_LOADUSER != null && data_ar[0].NTU_PTTT_SQL_LOADUSER != '0') {
				//JSON.parse('{ "name":"John", "age":30, "city":"New York"}');
				sql_load_user = JSON.parse(data_ar[0].NTU_PTTT_SQL_LOADUSER);
			}
			//L2PT-32306
			if (data_ar[0].NTU_SHOW_EKIP_PTTT != null && data_ar[0].NTU_SHOW_EKIP_PTTT == '1') {
				//JSON.parse('{ "name":"John", "age":30, "city":"New York"}');
				show_ekip_pttt = true;
			}
			//L2PT-320600
			if (data_ar[0].NTU_CHECK_TRUNG_VITRI == '1') {
				check_trungvitri_pttt = true;
			}
			//L2PT-34204
			if (data_ar[0].NTU_PTTT_KCD_DEFAULT == '1') {
				ch_macdinhkhoa_cd = true;
			}
			//L2PT-1404
			if (data_ar[0].NTU_PTTT_LOAD_MOME == '1') {
				$("#divPhumo_Me_Them").show();
			}
			//BVTM-3412
			if (data_ar[0].NTU_PTTT_BDHN_KGMS.indexOf(opt.dept_id) != -1) {
				iskhoa_gmhs = true;
			}
			//L2PT-6207
			if (data_ar[0].PTTT_HINHANH_MAU == '1') {
				$("#divhinhanhMau").show();
				layhinhanh_mau = true;
			}
			//L2PT-6951
			if (data_ar[0].NTU_PTTT_MOTA_REQ == '1') {
				$('#lblMota').addClass('required');
				req_mota = true;
			}
			//L2PT-99948
			if (cfObj.NTU_PTTT_BDBD_REQ == '1') {
				$('#lblTinhHinhPT').addClass('required');
				$('#lblTaiBien').addClass('required');
				$('#lblTuVong').addClass('required');
			}
			//L2PT-8046 --chi check voi loaitiepnhan khambenh , noitru da lay tgvaovien tu form duoi
			if (data_ar[0].NTU_PTTT_TGVV_QTI == '1' && opt.loaitiepnhan == '1') {
				//tgvv_qti = true;
				var _sql_par = [ opt.khambenhid, opt.phongid ];
				var mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.PKDK", _sql_par.join('$'));
				var dataPKDK = typeof mya != "object" ? JSON.parse(mya) : mya;
				opt.thoigianvaovien = dataPKDK.length > 0 ? dataPKDK[0].NGAY_BD : '';
			}
			tg_timeout_ready = Number(data_ar[0].NTU_PTTT_TIMOUT_READY);
			//L2PT-9410
			if (data_ar[0].NTU_PTTT_DISABLE_PPPT == '1') {
				$("#txtPTTTID").prop('disabled', true);
				$("#txtPHUONGPHAPPTTT").prop('disabled', true);
			}
			//BVTM-7549
			if (data_ar[0].MABACSI_CAPNHAT_PTTT != null && data_ar[0].MABACSI_CAPNHAT_PTTT != '0') {
				if (data_ar[0].MABACSI_CAPNHAT_PTTT == '1') {
					sql_load_user.TKPHAUTHUATVIEN = 'PTTT.LOAD_USER_BDID1';
				} else if (data_ar[0].MABACSI_CAPNHAT_PTTT == '2') {
					sql_load_user.TKPHAUTHUATVIEN = 'PTTT.LOAD_USER_BDID1';
					sql_load_user.VC_CHINH_ID = 'PTTT.LOAD_USER_BDID1';
				}
			}
			if (data_ar[0].NTU_PTTT_SHOW_ITEMS != '0') {
				var ch = data_ar[0].NTU_PTTT_SHOW_ITEMS;
				var ctrids = ch.split(',');
				for (var h = 0; h < ctrids.length; h++) {
					var ctrid = ctrids[h];
					$('div').children().each(function(i, obj) {
						if ($(obj).attr('id') != null && $(obj).attr('id').indexOf(ctrid) > -1) {
							$(obj).show();
						}
					});
				}
			}
			if (data_ar[0].NTU_PTTT_HIDE_ITEMS != '0') {
				var ch = data_ar[0].NTU_PTTT_HIDE_ITEMS;
				var ctrids = ch.split(',');
				for (var h = 0; h < ctrids.length; h++) {
					var ctrid = ctrids[h];
					$('div').children().each(function(i, obj) {
						if ($(obj).attr('id') != null && $(obj).attr('id').indexOf(ctrid) > -1) {
							$(obj).hide();
						}
					});
				}
			}
			//L2PT-34004
			if (data_ar[0].NTU_PTTT_REQ_COL != '0') {
				var ch = data_ar[0].NTU_PTTT_REQ_COL;
				var ctrids = ch.split(',');
				for (var h = 0; h < ctrids.length; h++) {
					var ctrid = ctrids[h];
					$('[confreq]').each(function(index, element) {
						if (ctrid == 'ALL' || $(element).attr('id').indexOf(ctrid) > -1) {
							if ($(this)[0].tagName == 'DIV') {
								$(this).addClass("required");
							} else {
								var newrules = 'Trường thiết lập,required';
								var _tmp = $(this).attr("valrule");
								if (typeof _tmp != "undefined") {
									var _tmp_ar = _tmp.split(",");
									var display = _tmp_ar[0];
									var rules = 'required|' + _tmp_ar[1];
									newrules = display + ',' + rules;
								}
								$(this).attr("valrule", newrules);
							}
						}
					});
				}
			}
			//L2PT-40429
			if (data_ar[0].NTU_PTTT_THOIGIAN_PTTT_CBO != '0') {
				var array = data_ar[0].NTU_PTTT_THOIGIAN_PTTT_CBO.split(",");
				$("#cboTHOIGIAN_PTTT_CBO").empty();
				var option = $('<option value="-1">--Lựa chọn--</option>');
				$("#cboTHOIGIAN_PTTT_CBO").append(option);
				for (var i = 0; i < array.length; ++i) {
					var option = $('<option value="' + array[i] + '">' + array[i] + ' phút</option>');
					$("#cboTHOIGIAN_PTTT_CBO").append(option);
				}
			}
			//L2PT-40775
			if (data_ar[0].NTU_PTTT_LABEL_NAME != '0') {
				var ch = JSON.parse(data_ar[0].NTU_PTTT_LABEL_NAME);
				$.each($.parseJSON('[' + data_ar[0].NTU_PTTT_LABEL_NAME + ']'), function() {
					$.each(this, function(key, value) {
						$("#" + key + "").text(value);
					});
				});
			}
		}
		//L2PT-17519 - set ngay chi dinh  pttt
		if (opt.ngaymaubenhpham) {
			$("#txtNGAYMAUBENHPHAM").val(opt.ngaymaubenhpham);
		}
		//L2PT-15248
		if (opt.hospital_id == '32620') {
			$("#ptttluc").text("Thời gian bắt đầu PTTT");
		}
		//L2PT-25760
		if (form_pttt_bvnt) {
			$("#divPPPT,#divCDCHINH,#divCDPHU,#divBSTHUCHIEN").hide();
			$("#divPPPT_BVNT,#div_CDP_TRCPT,#div_CDP_SAUPT").show();
			$("#cboPHUONGPHAPPTTT_BVNT").attr("valrule", "P.Pháp PTTT,required");
			$("#txtCHANDOANVAOKHOA").removeAttr("valrule");
			//ComboUtil.initComboGrid("txtTKMACHANDOANPHU_SAU",_sql,[],"600px",_col,"txtTKMACHANDOANPHU_SAU=ICD10CODE,txtCHANDOANPHU_SAU=ICD10NAME");
			//L2PT-6037
			$("#divICD9_BVNT").show();
		}
		ComboUtil.init("txtTKMACHANDOANPHU_TRC", _sql, [], "600px", _col, function(event, ui) {
			var str = $("#txtCHANDOANPHU_TRC").val();
			if (str != '')
				str += ";";
			$("#txtCHANDOANPHU_TRC").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			return false;
		});
		ComboUtil.init("txtTKMACHANDOANPHU_SAU", _sql, [], "600px", _col, function(event, ui) {
			var str = $("#txtCHANDOANPHU_SAU").val();
			if (str != '')
				str += ";";
			$("#txtCHANDOANPHU_SAU").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			return false;
		});
		if (opt.hospital_id == '924') {
			$("#divketluan").show();
			$("#labelMota").html("Kết quả");
		}
		//START L2PT-19030
		if (opt.hospital_id == '957' && opt.callfrom != null && opt.callfrom == '1') {
			$("#divLbl_PPPTTT").addClass("required");
			$("#divCDTruocPttt").addClass("required");
		}
		//LAY DICH VU KHAM BENH
		var dtSys = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		sql_par = [ opt.dichvukhambenhid ];
		var data1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.DVKB.FUN", sql_par.join('$')); //L2PT-42499
		var row1 = data1[0];
		// L2PT-123233 start
		if(row1.PTTTHANGID == '5' || row1.PTTTHANGID == '6' || row1.PTTTHANGID == '7' || row1.PTTTHANGID == '8') {
			console.log('PTTT loai 1,2,3 va dac biet');
		} else {
			macdinh_nhap = false;
		}
		// L2PT-123233 end
		//L2PT-80601
		if (row1.KETQUA && cfObj.NTU_PTTT_DONGBOMOTA_CDHA == '1')
			$("#txtMOTA").val(row1.KETQUA);
		//L2PT-108829
		if (row1.GHICHU)
			$("#txtGHICHU").val(row1.GHICHU);
		//L2PT-80602
		if (row1.KETQUACLSID) {
			_ketquaclsid = row1.KETQUACLSID;
			lietKeAnh();
		}
		_madichvu = row1.MADICHVU;
		madichvu_caychi = row1.MADICHVU;
		var _tendichvu = row1.TENDICHVU;
		dichvuid = row1.DICHVUID;
		cfObj.TG_THUCHIEN_PTTT = row1.TG_THUCHIEN_PTTT;
		//L2PT-6037
		//L2PT-35097
		$("#txtMA_ICD9").val(row1.MA_ICD9);
		$("#txtTEN_ICD9").val(row1.TEN_ICD9);
		var col_icd9 = "Mã ICD 9,MA_ICD9,40,0,f,l;Tên ICD9,TEN_ICD9,60,0,f,l";
		ComboUtil.init("txtMA_ICD9", 'PTTT.DICHVUKB.04', [ {
			"name" : "[0]",
			"value" : opt.dichvukhambenhid
		} ], "600px", col_icd9, function(event, ui) {
			var str = $("#txtMA_ICD9").val();
			$("#txtMA_ICD9").val(ui.item.MA_ICD9);
			$("#txtTEN_ICD9").val(ui.item.TEN_ICD9);
			return false;
		});
		//L2PT-96184
		if (cfObj.NTU_PTTT_MAMAY_REQ != '0') {
			var ds_madvs = cfObj.NTU_PTTT_MAMAY_REQ.split(',')
			if ($.inArray(_madichvu, ds_madvs) >= 0) {
				_mamay_req = true;
				$('#lblMAMAY').addClass('required');
				$("#cboMAMAY").attr("valrule", "Mã máy,required");
			}
		}
		//L2PT-100076
		if (cfObj.NTU_PTTT_MAMAY_RED != '0') {
			$('#lblMAMAY').css({
				"color" : "red",
				"font-weight" : "bold"
			});
		}
		
		initPopupCanhBao(); //L2PT-123034
		
		//L2PT-93277
		if (cfObj.QD_4750 == '1' && row1.NHOM_MABHYT_ID == '6' && chonnhieu_ppvc == '0') {
			$('#lblPPVC').addClass('required');
			checkppvc = '1';
			$("#cboPTTT_PHUONGPHAPVOCAMID").attr("valrule", "P.Pháp vô cảm,required");
		}
		//L2PT-106738
		if (row1.NHOM_MABHYT_ID == '6' && cfObj.NTU_PTTT_BSGM_REQUIRED == '1') {
			$('#bsgayme').addClass('required');
			checkbsgm = '1';
			
		}
		//L2PT-95386 :NTU_PTTT_TINHHINH_REQUIRED;NTU_PTTT_TAIBIEN_REQUIRED;NTU_PTTT_TUVONG_REQUIRED
		if (cfObj.NTU_PTTT_TINHHINH_REQUIRED == '1' || macdinh_nhap) {
			$('#lblTinhHinhPT').addClass('required'); // L2PT-123233
			check_tinhhinhpttt = '1';
			$("#cboPTTT_TINHHINHID").attr("valrule", "Tình hình PTTT,required");
		}
		if (cfObj.NTU_PTTT_TAIBIEN_REQUIRED == '1' || macdinh_nhap) {
			$('#lblTaiBien').addClass('required'); // L2PT-123233
			check_taibien = '1';
			$("#cboPTTT_TAIBIENID").attr("valrule", "Tai biến,required");
		}
		if (cfObj.NTU_PTTT_TUVONG_REQUIRED == '1' || macdinh_nhap) {
			$('#lblTuVong').addClass('required'); // L2PT-123233
			check_tuvongtrongpttt = '1';
			$("#cboPTTT_TUVONGID").attr("valrule", "Tử vong trong PTTT,required");
		}
		// L2PT-123233 start
		if (cfObj.NTU_PTTT_NHOMMAU_REQUIRED == '1') { // L2PT-123233_1
			$('#divNhommau').addClass('required');
			$('#divRH').addClass('required');
			check_nhommau = '1';
			$("#cboPTTT_NhomMauID").attr("valrule", "Nhóm máu,required");
			$("#cboPTTT_NHOMMAURHID").attr("valrule", "RH,required");
		}
		// L2PT-123233 end
		//START L2PT-19388
		if (check_songuoi_thuchien) {
			var sql_par_checksl = [];
			sql_par_checksl.push({
				"name" : "[0]",
				"value" : opt.dichvukhambenhid
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D037.SONGUOI.1", sql_par_checksl);
			if (data != null) {
				var rows = JSON.parse(data);
				if (rows != null && rows.length > 0) {
					var row = rows[0];
					$('#txtSONGUOI').prop('disabled', true);
					sl_nguoi = row.SONGUOI;
					sl_giupviec = row.SL_GIUPVIEC;
					sl_nhanlucchinh = row.SL_NHANLUCCHINH;
					sl_nhanlucphu = row.SL_NHANLUCPHU;
					sl_nhanlucphume = row.SL_PHUME;
					sl_gaymechinh = row.SL_GAYMECHINH;
				}
			}
		} else {
			$('#txtSONGUOI').val(row1.SONGUOI);
		}
		$('#txtTYLEPTTT').val(row1.TILE_PTTT);
		if (changeTextEkipNhiHDG) {
			if (row1.PTTTHANGID == 0 || row1.PTTTHANGID == 1 || row1.PTTTHANGID == 2 || row1.PTTTHANGID == 3 || row1.PTTTHANGID == 4) {
				$("#ptvchinh").text("Thủ thuật viên chính");
				$("#bsgayme").text("Bác sỹ gây mê/tê");
				$("#pmo1").text("Phụ");
				$("#pme1").text("Phụ mê, tê");
				$("#pmo2").text("Phụ");
				$("#lbldcvien").text("Giúp việc");
				$("#lblgv").text("Giúp việc");
			}
			if (row1.PTTTHANGID == 5 || row1.PTTTHANGID == 6 || row1.PTTTHANGID == 7 || row1.PTTTHANGID == 8) {
				$("#ptvchinh").text("Phẫu thuật viên chính");
				$("#bsgayme").text("Bác sỹ gây mê/tê");
				$("#pmo1").text("Phụ mổ");
				$("#pme1").text("Phụ mê, tê");
				$("#pmo2").text("Phụ mổ");
				$("#lbldcvien").text("Giúp việc");
				$("#lblgv").text("Giúp việc");
			}
		}
		//START L1PT-962
		if ((opt.hospital_id == '1014' || opt.hospital_id == '1077') && row1.EKIP_PTTT != null && row1.EKIP_PTTT == '1') {
			setEnabled([], [ 'txtTKPHAUTHUATVIEN', 'cboPHAUTHUATVIEN', 'txtTKDUNGCUVIEN', 'cboDUNGCUVIEN', 'txtTKBACSIGAYME', 'cboBACSIGAYME', 'txtTKPHUME', 'cboPHUME', 'txtTKPHUME2', 'cboPHUME2',
					'txtTKPHUMO1', 'cboPHUMO1', 'txtTKPHUMO2', 'cboPHUMO2', 'txtTKPHUMO3', 'cboPHUMO3', 'txtTKBACSITHUCHIEN', 'cboBACSITHUCHIEN', 'txtTKDIEUDUONG', 'cboDIEUDUONG',
					'txtTKCHAYMAYCHINH', 'cboCHAYMAYCHINH', 'txtTKCHAYMAYPHU', 'cboCHAYMAYPHU', 'btnCLEARPHAUTHUATVIEN', 'btnCLEARDUNGCUVIEN', 'btnCLEARBACSIGAYME', 'btnCLEARPHUME', 'btnCLEARPHUMO1',
					'btnCLEARPHUMO2', 'btnCLEARDIEUDUONG', 'btnCLEARCHAYMAYCHINH', 'btnCLEARCHAYMAYPHU', 'btnCLEARPHUMO3', 'btnCLEARPHUME2', 'txtTK_EKIPPTTT', 'cboEKIPPTTT', 'btnEDIT_EKIPPTTT',
					'txtTENMAU_EKIP', 'btnLuuMauEKIP' ]);//L1PT-1139
		}
		//END L1PT-962
		//START L2PT-4894
		/*if(opt.hospital_id == '939'){
			$('#divMaMay').show();
		}else{
			$('#divMaMay').hide();
		}*/
		//END L2PT-4894
		sql_par = [];
		//Begin_HaNv_28062018: Lay thong tin chan doan va chan doan kem theo tu phieu chi dinh - HISL2TK-775
		sql_par.push({
			"name" : "[0]",
			"value" : opt.maubenhphamid
		});
		var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.MACHUANDOAN", sql_par);
		var row2 = JSON.parse(data2)[0];
		var machandoanravien = row2.MACHANDOAN;
		var chandoanravien = row2.CHANDOAN;
		var chandoankemtheo = row2.CHANDOAN_KEMTHEO;
		//L2PT-94682
		var machandoanyhctravien = row2.MACHANDOANICDYHCT;
		var chandoanyhctravien = row2.TENCHANDOANICDYHCT;
		var chandoanyhctkemtheo = row2.TENCHANDOANICDYHCT_KT;
		//End_HaNv_28062018
		//BVTM-3412
		if (opt.hospital_id == '10284') {
			var sql_kethem = [];
			sql_kethem.push({
				"name" : "[0]",
				"value" : opt.dichvukhambenhid
			});
			var datakethem = jsonrpc.AjaxJson.ajaxExecuteQueryO("D049.DV_KETTHEM", sql_kethem);
			var data_ar_kethem = JSON.parse(datakethem);
			if (data_ar_kethem != null && data_ar_kethem.length > 0) {
				//khong phai khoa gay me hoi suc thi bo require
				if (!iskhoa_gmhs) {
					$('#divppvc').removeClass('required');
					$('#divnvcc').removeClass('required');
					$('#divnvcp').removeClass('required');
				}
				if (data_ar_kethem[0]["ISKETHEM"] == 1) {
					isKethem = true;
					opt.loaiptttid = 1;
					//bỏ require với dv kê thêm
					$('#divppvc').removeClass('required');
					$('#divnvcc').removeClass('required');
					$('#divnvcp').removeClass('required');
				}
			}
		}
		//END BVTM-3412
		//L2PT-19597
		/*if(opt.hospital_id == '40260'){
			$('#divthuoc_tm').show();
		}*/
		//check xem da co ban ghi phau thuat thu thuat  chua
		var rowDetail;
		sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : opt.dichvukhambenhid
		}, {
			"name" : "[1]",
			"value" : opt.khambenhid
		});
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.CHECK", sql_par);
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			//L2PT-34023
			if (opt.hospital_id == '10284') {
				$('#btnThemDV').show();
			}
			//L2PT-34204
			ch_macdinhkhoa_cd = false;
			_phauthuatthuthuatid = rows[0].PHAUTHUATTHUTHUATID;
			_checkUpdate = 1;
			var sql_par1 = [ _phauthuatthuthuatid ];
			var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GET.INFO", sql_par1.join('$'));
			rowDetail = dataDetail[0];
			$('#txtSONGUOI').val(rowDetail.SONGUOI);
			$('#txtTYLEPTTT').val(rowDetail.TILE_PTTT);
			khoacdid = rowDetail.KHOACHIDINH != '-1' ? rowDetail.KHOACHIDINH : opt.khoaid;
			//L2PT-34023
			ptv_chinh = rowDetail.PHAUTHUATVIENID ? rowDetail.PHAUTHUATVIENID : null;
			//START L2PT-17277
			if (rowDetail.HENTRASAU == 1) {
				$("#calTG_HENTRASAU").show();
			} else {
				$("#calTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").val('01/01/2000 00:00:00');
			}
			//START L2PT-19388
			if (check_songuoi_thuchien) {
				sl_nguoi = rowDetail.SONGUOI;
				sl_giupviec = rowDetail.SL_GIUPVIEC;
				sl_nhanlucchinh = rowDetail.SL_NHANLUCCHINH;
				sl_nhanlucphu = rowDetail.SL_NHANLUCPHU;
			}
			//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
			if (rowDetail.PTTT_TINHHINHID == '' || rowDetail.PTTT_TINHHINHID == null) {
				sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : opt.dichvukhambenhid
				});
				var data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.TINHTRANG_PTTT", sql_par);
				var row1 = JSON.parse(data1)[0];
				$("#cboPTTT_TINHHINHID").val(row1.TINHTRANG_PTTT);
			}
			//End_HaNv_21062018
			//L2PT-27254
			if (kt_pttt_dklci) {
				$("#txtKETTHUCPTTT_DKLCI").val(rowDetail.KETTHUCPTTT);
			}
		} else {
			if (opt.hospital_id == '1014' || opt.hospital_id == '1077') {
				sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : opt.maubenhphamid
				});
				var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D037.KHOACD", sql_par);
				var rows = JSON.parse(data);
				if (rows != null && rows.length > 0) {
					khoacdid = rows[0].KHOAID;
				}
			}
			//L2PT-8404
			if (hienthi_ekipduyetmo) {
				var sql_par1 = [ opt.maubenhphamid ];
				var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("LICHMO.INFO", sql_par1.join('$'));
				if (dataDetail != null && dataDetail.length > 0) {
					var row = dataDetail[0];
					row.BS_CHINH_ID != null ? setValueToCbo('txtTKPHAUTHUATVIEN', 'cboPHAUTHUATVIEN', row.BS_CHINH_ID, row.BS_CHINH) : 1 == 1;
					row.BS_PHU1_ID != null ? setValueToCbo('txtTKPHUMO1', 'cboPHUMO1', row.BS_PHU1_ID, row.BS_PHU1) : 1 == 1;
					row.BS_PHU2_ID != null ? setValueToCbo('txtTKPHUMO2', 'cboPHUMO2', row.BS_PHU2_ID, row.BS_PHU2) : 1 == 1;
					row.BS_PHU3_ID != null ? setValueToCbo('txtTKPHUMO3', 'cboPHUMO3', row.BS_PHU3_ID, row.BS_PHU3) : 1 == 1;
					row.GAYMECHINH_ID != null ? setValueToCbo('txtTKBACSIGAYME', 'cboBACSIGAYME', row.GAYMECHINH_ID, row.GAYMECHINH) : 1 == 1;
					row.PHUME_ID != null ? setValueToCbo('txtTKPHUME', 'cboPHUME', row.PHUME_ID, row.PHUME) : 1 == 1;
					//L2PT-32060
					row.VC_CHINH_ID != null ? setValueToCbo('txtVC_CHINH_ID', 'cboVC_CHINH', row.VC_CHINH_ID, row.VC_CHINH) : 1 == 1;
					row.VC_PHU_ID != null ? setValueToCbo('txtVC_PHU_ID', 'cboVC_PHU', row.VC_PHU_ID, row.VC_PHU) : 1 == 1;
					row.XEP_LICH_ID != null ? setValueToCbo('txtXEP_LICH_ID', 'cboXEP_LICH', row.XEP_LICH_ID, row.XEP_LICH) : 1 == 1;
					row.HUU_TRUNG_ID != null ? setValueToCbo('txtHUU_TRUNG_ID', 'cboHUU_TRUNG', row.HUU_TRUNG_ID, row.HUU_TRUNG) : 1 == 1;
					row.VO_TRUNG_ID != null ? setValueToCbo('txtVO_TRUNG_ID', 'cboVO_TRUNG', row.VO_TRUNG_ID, row.VO_TRUNG) : 1 == 1;
					//phuong phap vo cam
					rowDetail = new Object();
					rowDetail.PTTT_PHUONGPHAPVOCAMID = row.PPVCAM;
				}
			}
			if (tinhhinhPTTTmacdinh) {
				$("#cboPTTT_TINHHINHID").val('2');
			}
			//L2PT-28421
			if (get_nth_cdha_snvpc) {
				var sql_par1 = [ opt.dichvukhambenhid ];
				var dataNTH = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.GETNTH", sql_par1.join('$'));
				if (dataNTH.length > 0) {
					var nguoiTH = dataNTH[0];
					rowDetail = new Object();
					if (nguoiTH.TKPHAUTHUATVIEN && nguoiTH.TKPHAUTHUATVIEN != '') {
						rowDetail.TKPHAUTHUATVIEN = nguoiTH.TKPHAUTHUATVIEN;
					}
					if (nguoiTH.TKPHUMO1 && nguoiTH.TKPHUMO1 != '') {
						rowDetail.TKPHUMO1 = nguoiTH.TKPHUMO1;
					}
				}
			}
			//BVTM-7586
			// L2PT-123233 start: dong bo nhom mau
			if (load_nhommau_rh || macdinh_nhap) {
				var type_nm = '2';
				if(macdinh_nhap) {
					type_nm = '3';
				}
				var data_ar_nhommau = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.LOADRH", opt.khambenhid + '$' + type_nm);
				if (data_ar_nhommau != null && data_ar_nhommau.length > 0) {
					var row_nhommau = data_ar_nhommau[0];
					if(type_nm == '3' && row_nhommau.HINHTHUCVAOVIENID == '2') {
						$("#cboPTTT_TINHHINHID").val(1);
					} else {
						$("#cboPTTT_TINHHINHID").val(2);
					}
					if (row_nhommau.NHOMMAU) {
						switch (row_nhommau.NHOMMAU) {
							case 'A':
								$('#cboPTTT_NhomMauID').val('1');
							break;
							case 'B':
								$('#cboPTTT_NhomMauID').val('2');
							break;
							case 'O':
								$('#cboPTTT_NhomMauID').val('3');
							break;
							case 'AB':
								$('#cboPTTT_NhomMauID').val('4');
							break;
						}
					}
					if (row_nhommau.NHOMMAU_RH) {
						$('#cboPTTT_NHOMMAURHID').val(row_nhommau.NHOMMAU_RH);
					}
				}
			}
			// L2PT-123233 end
		
		}
		if (rowDetail == null) {
			rowDetail = new Object();
		}
		//set gia tri len form phau thuat thu thuat
		sql_par = [];
		sql_par = [ opt.hosobenhanid, opt.benhnhanid ];
		var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU036.GTS.TTBN", sql_par.join('$'));
		if (data != null && data.length > 0) {
			FormUtil.setObjectToForm("divMain", "", data[0]);
		}
		//L2PT-23949
		var kq = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H024.EV036", opt.maubenhphamid);
		if (kq != '0') {
			$('#txtTHOIGIANTHUCHIEN').val(kq);
		}
		//L2PT-34204
		var hang_pttt_id = typeof (rowDetail.PTTT_HANGID) == 'undefined' || rowDetail.PTTT_HANGID == '-1' || rowDetail.PTTT_HANGID == '' || rowDetail.PTTT_HANGID == '0' ? (typeof (row1.PTTTHANGID) == 'undefined' ||
				row1.PTTTHANGID == null || row1.PTTTHANGID == '' ? '0' : row1.PTTTHANGID)
				: rowDetail.PTTT_HANGID;
		if (ch_macdinhkhoa_cd && (hang_pttt_id == '5' || hang_pttt_id == '6' || hang_pttt_id == '7' || hang_pttt_id == '8')) {
			khoacdid = '-1';
		}
		ComboUtil.getComboTag("cboKHOACHIDINH", 'NTU02D037.EV001', [], khoacdid, {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql');
		//L2PT-28435 //L2PT-97544
		var khoaid_user = opt.dept_id;
		if (data && data[0].KHOAID_USER && cfObj.NTU_PTTT_LOAD_KHOA_USER == '1') {
			khoaid_user = data[0].KHOAID_USER;
		}
		ComboUtil.getComboTag("cboKHOAHIENTHI", 'NTU02D037.EV001', [], khoaid_user, {
			text : "--- Tất cả ---",
			value : -1
		}, 'sql', '', function() {
			$("#cboKHOAHIENTHI").change();
		});
		$("#cboKHOAHIENTHI").change();
		let
		mamaySQL = 'NTU02D037.MAMAY';
		//L2PT-45078
		if (cfObj.NTU_PTTT_MAMAY2 == '1') {
			mamaySQL = 'NTU02D037.MAMAY2';
		}
		if (cfObj.NTU_PTTT_MAMAY2 == '2') {
			mamaySQL = 'NTU02D037.MAMAY3';
		}
		var sql_par_mayid = [ {
			"name" : "[0]",
			"value" : dichvuid
		} ];
		sql_par_mayid.push({
			"name" : "[1]",
			"value" : opt.maubenhphamid
		}); //L2PT-91393
		ComboUtil.getComboTag("cboMAMAY", mamaySQL, sql_par_mayid, rowDetail.MAMAY, {
			text : "---Không chọn---",
			value : -1
		}, 'sql', '', function() {
			var values = $.map($('#cboMAMAY option'), function(e) {
				return e.value;
			});
			_ds_mm = values.join(',');
			if (values && values.length == 2 && cfObj.NTU_PTTT_MAMAY2 == '2')
				$('#cboMAMAY').val(values[1]);
		});
		//L2PT-94682
		var sql_par_k = [];
		sql_par_k.push({
			"name" : "[0]",
			value : opt.khoaid
		});
		var check_k = jsonrpc.AjaxJson.getOneValue('CHECK.KHOAYHCT', sql_par_k);
		var sql_par_p = [];
		sql_par_p.push({
			"name" : "[0]",
			value : opt.phongid
		});
		var check_p = jsonrpc.AjaxJson.getOneValue('CHECK.PHONGYHCT', sql_par_p);
		if(cfObj.HIS_SHOW_ICDYHCT=='1' && cfObj.HIS_SHOW_ICDYHCT_PTTT == '0' 
			&& form_pttt_bvnt != '1' //check bnvt ko hien thi
			&& (check_k != '0' || check_p != '0')){
			$("#divICDYHTC").css("display", "");
			$("#divCDT_YHCTPttt").css("display", "");
			$("#divCDS_YHCTPttt").css("display", "");
			var sql_icd_yhct = 'NT.008.YHCTV4';
			var _col_yhct = 'Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l';
			ComboUtil.initComboGrid("txtMA_CHANDOANVAOKHOA", sql_icd_yhct, [], "900px", _col_yhct, function(event, ui) {
				var _ui = ui.item;
				if (_ui.YHCTCODE) {
					$("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
					$("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
				}
				$("#txtMA_CHANDOANVAOKHOA").val(_ui.ICD10CODE);
				$("#txtCHANDOANVAOKHOA").val(_ui.ICD10NAME);
				return false;
			});
			ComboUtil.initComboGrid("txtMACHANDOANYHCT", sql_icd_yhct, [], "900px", _col_yhct, function(event, ui) {
				var _ui = ui.item;
				if (_ui.YHCTCODE) {
					$("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
					$("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
				}
				$("#txtMA_CHANDOANVAOKHOA").val(_ui.ICD10CODE);
				$("#txtCHANDOANVAOKHOA").val(_ui.ICD10NAME);
				return false;
			});
			ComboUtil.init("txtMA_CHANDOANVAOKHOAKEMTHEO", sql_icd_yhct, [], "900px", _col_yhct, function(event, ui) {
				var stryhct = $("#txtCHANDOANYHCTPHU").val();
				if (ui.item.YHCTCODE) {
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				var str = $("#txtCHANDOANVAOKHOAKEMTHEO").val();
				if (str != '')
					str += ";";
				$("#txtCHANDOANVAOKHOAKEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				return false;
			});
			ComboUtil.init("txtTKMACHANDOANYHCTPHU", sql_icd_yhct, [], "900px", _col_yhct, function(event, ui) {
				var stryhct = $("#txtCHANDOANYHCTPHU").val();
				if (ui.item.YHCTCODE) {
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				var str = $("#txtCHANDOANVAOKHOAKEMTHEO").val();
				if (str != '')
					str += ";";
				$("#txtCHANDOANVAOKHOAKEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				return false;
			});
			ComboUtil.initComboGrid("txtMA_CDT_YHCT_PHAUTHUAT", sql_icd_yhct, [], "900px", _col_yhct, function(event, ui) {
				var _ui = ui.item;
				if (_ui.YHCTCODE) {
					$("#txtMA_CDT_YHCT_PHAUTHUAT").val(_ui.YHCTCODE);
					$("#txtCDYHCTTRUOCPHAUTHUAT").val(_ui.YHCTNAME);
				}
				return false;
			});
			ComboUtil.initComboGrid("txtMA_CDS_YHCT_PHAUTHUAT", sql_icd_yhct, [], "900px", _col_yhct, function(event, ui) {
				var _ui = ui.item;
				if (_ui.YHCTCODE) {
					$("#txtMA_CDS_YHCT_PHAUTHUAT").val(_ui.YHCTCODE);
					$("#txtCDYHCTSAUPHAUTHUAT").val(_ui.YHCTNAME);
				}
				return false;
			});
		} else {
			//khoi tao cho chuan doan chinh
			ComboUtil.initComboGrid("txtMA_CHANDOANVAOKHOA", _sql, [], "600px", _col, "txtMA_CHANDOANVAOKHOA=ICD10CODE,txtCHANDOANVAOKHOA=ICD10NAME");
			//khoi tao chuan doan phu
			ComboUtil.initComboGrid("txtMA_CHANDOANVAOKHOAKEMTHEO", _sql, [], "600px", _col, function(event, ui) {
				$('#txtCHANDOANVAOKHOAKEMTHEO').val(
						$("#txtCHANDOANVAOKHOAKEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHANDOANVAOKHOAKEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
								ui.item.ICD10NAME);
				//Begin_HaNv_28062018: Lay thong tin chan doan va chan doan kem theo tu phieu chi dinh - HISL2TK-775
//				var FOMAT_MA_BENHPHU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','FOMAT_MA_BENHPHU');
//				if(FOMAT_MA_BENHPHU == 1){
//					$('#txtCHANDOANVAOKHOAKEMTHEO').val($("#txtCHANDOANVAOKHOAKEMTHEO").val() == '' ? ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME : $("#txtCHANDOANVAOKHOAKEMTHEO").val() + ";" + ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME);
//				}
//				else{
//					$('#txtCHANDOANVAOKHOAKEMTHEO').val($("#txtCHANDOANVAOKHOAKEMTHEO").val() == '' ? ui.item.ICD10NAME+ "( "+ui.item.ICD10CODE +")" : $("#txtCHANDOANVAOKHOAKEMTHEO").val() + ";" + ui.item.ICD10NAME+ "( "+ui.item.ICD10CODE +")");
//				}
				//End_HaNv_28062018
			});
		}
		//khoi tao chuan doan TRUOC PTTTT
		ComboUtil.initComboGrid("txtMA_CHANDOANTRUOCPHAUTHUAT", _sql, [], "600px", _col, "txtMA_CHANDOANTRUOCPHAUTHUAT=ICD10CODE,txtCHANDOANTRUOCPHAUTHUAT=ICD10NAME");
		//khoi tao chuan doan SAU PTTTT
		ComboUtil.initComboGrid("txtMA_CHANDOANSAUPHAUTHUAT", _sql, [], "600px", _col, "txtMA_CHANDOANSAUPHAUTHUAT=ICD10CODE,txtCHANDOANSAUPHAUTHUAT=ICD10NAME");
		//Phuong phap PTTT
		ComboUtil.initComboGrid("txtPTTTID", _sqlPTTT, [], "600px", _colPTTT, "txtPTTTID=MAPTTT,txtPHUONGPHAPPTTT=TENPTTT");
		//loai PTTT
		sql_par = [];
		ComboUtil.getComboTag("cboPTTT_HangID", _sqlPTTT_hang, sql_par, hang_pttt_id, {
			value : '-1',
			text : '--Lựa chọn--'
		}, "sql");
		//START L2PT-8042
		if (isNhapPTTT) {
			$('#cboPTTT_HangID').attr("disabled", false);
		} else {
			$('#cboPTTT_HangID').attr("disabled", true);
		}
		//phuong phap vo cam
		//L2PT-25760
		var ckhoa = rowDetail.CHUYENKHOA
		ComboUtil.getComboTag("cboCHUYENKHOA", "COM.TRANGTHAI", [ {
			"name" : "[0]",
			"value" : 137
		} ], ckhoa, "", "sql");
		//L2PT-92372
		var ppvc_md = jsonrpc.AjaxJson.getOneValue('GET.PPVCMD', sql_par_mayid);
		if (ppvc_md && ppvc_md != 'null') {
			var array = ppvc_md.split(';');
			ppvc_default = array[0];
		}
		if (rowDetail.PTTT_PHUONGPHAPVOCAMID) {
			ppvc_default = rowDetail.PTTT_PHUONGPHAPVOCAMID;
		}
		if (chonnhieu_ppvc == '1') {
			$('#divPPVC').attr('style', 'overflow:visible');
			$('#cboPTTT_PHUONGPHAPVOCAMID_SUMO').show();
			$('#cboPTTT_PHUONGPHAPVOCAMID').hide();
			ComboUtil.getComboTag("cboPTTT_PHUONGPHAPVOCAMID_SUMO", _sqlPTTT_vocam, [], '', "", 'sql', '', function() {
				$('#cboPTTT_PHUONGPHAPVOCAMID_SUMO').SumoSelect({
					up : true,
					search : true,
					searchText : 'Tìm kiếm',
					okCancelInMulti : true,
					selectAll : true
				});
				$('#cboPTTT_PHUONGPHAPVOCAMID_SUMO')[0].sumo.reload();
				var str = rowDetail.PTTT_PHUONGPHAPVOCAMID.split(',');
				$('#cboPTTT_PHUONGPHAPVOCAMID_SUMO option').each(function(i) {
					for (var j = 0; j < str.length; j++) {
						if ($(this).val() == str[j]) {
							$('#cboPTTT_PHUONGPHAPVOCAMID_SUMO')[0].sumo.selectItem(i);
						}
					}
				});
			});
			//ComboUtil.getComboTag("cboPTTT_PHUONGPHAPVOCAMID_SUMO",_sqlPTTT_vocam, sql_par,ppvc_default,{value:'-1',text:'--Lựa chọn--'},"sql");
		} else {
			ComboUtil.getComboTag("cboPTTT_PHUONGPHAPVOCAMID", _sqlPTTT_vocam, sql_par, ppvc_default, {
				value : '-1',
				text : '--Lựa chọn--'
			}, "sql");
		}
		//tai bien
		ComboUtil.getComboTag("cboPTTT_TAIBIENID", _sqlPTTT_taibien, sql_par, rowDetail.PTTT_TAIBIENID, {
			value : '-1',
			text : '--Lựa chọn--'
		}, "sql","",""); // L2PT-123233: them tham so de goi dong bo
		// L2PT-123233 start: mac dinh gia tri
		if (macdinh_nhap && (rows == null || rows.length == 0)) {
			$("#cboPTTT_TAIBIENID").val(1);
			$("#cboPTTT_TUVONGID").val(1);
		}
		// L2PT-123233 end
		// ChienDV START L2PT-71163
		let
		checkCauHinh = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'SHOW_OR_HIDDEN_TTPTTT_TT32');
		if (checkCauHinh == 1) {
			$("#divThemPhieu").removeClass("hidden");
			var sql_param = [];
			sql_param.push({
				"name" : "[0]",
				"value" : 'NTU_TTPTTT'
			});
			ComboUtil.getComboTag("cboThemPhieu", 'NTU02D037.TTPTTT', sql_param, rowDetail.THEMPHIEU, {
				text : "--Chọn--",
				value : -1
			}, 'sql');
		}
		// END START L2PT-71163
		//cach thuc pttt
		$("#txtTENCACHTHUCPTTT").val(_tendichvu);
		$("#txtMACACHTHUCPTTT").val(_madichvu);
		//L2PT-26618
		if (pp_pttt_dklsn) {
			$("#txtPHUONGPHAPPTTT").val(_tendichvu);
			$("#txtPTTTID").val(_madichvu);
		}
		if (_checkUpdate == 0) {
			$("#txtCHANDOANVAOKHOA").val(chandoanravien);
			$("#txtMA_CHANDOANVAOKHOA").val(machandoanravien);
			$("#txtCHANDOANVAOKHOAKEMTHEO").val(chandoankemtheo);
			//L2PT-94682
			$("#txtCHANDOANYHCT").val(chandoanyhctravien);
			$("#txtMACHANDOANYHCT").val(machandoanyhctravien);
			$("#txtCHANDOANYHCTPHU").val(chandoanyhctkemtheo);
		}
		//Begin_HaNv_19062018: Hien thi ekip thuc hien theo khoa thiet lap - L2HOTRO-2797
		//HaNv_20170731: load doi ngu y, bac si, phau thuat vien bang initComboGrid (cho phep tim kiem)
		//BVTM-3454
		loadComboVitriThuchien();
		FormUtil.setObjectToForm("divMain", "", rowDetail);
		//L2PT-89368
		if (cfObj.NTU_PTTT_UPD_USER_LOCK == '1') {
			rowDetail.PHAUTHUATVIENID != null ? setValueToCbo('txtTKPHAUTHUATVIEN', 'cboPHAUTHUATVIEN', rowDetail.PHAUTHUATVIENID, rowDetail.TENPHAUTHUATVIEN) : 1 == 1;
			rowDetail.TKPHUMO1 != null ? setValueToCbo('txtTKPHUMO1', 'cboPHUMO1', rowDetail.TKPHUMO1, rowDetail.TENPHUMO1) : 1 == 1;
			rowDetail.TKPHUMO2 != null ? setValueToCbo('txtTKPHUMO2', 'cboPHUMO2', rowDetail.TKPHUMO2, rowDetail.TENPHUMO2) : 1 == 1;
			rowDetail.TKPHUMO3 != null ? setValueToCbo('txtTKPHUMO3', 'cboPHUMO3', rowDetail.TKPHUMO3, rowDetail.TENPHUMO3) : 1 == 1;
			rowDetail.TKPHUMO4 != null ? setValueToCbo('txtTKPHUMO4', 'cboPHUMO4', rowDetail.TKPHUMO4, rowDetail.TENPHUMO4) : 1 == 1;
			rowDetail.TKPHUMO5 != null ? setValueToCbo('txtTKPHUMO5', 'cboPHUMO5', rowDetail.TKPHUMO5, rowDetail.TENPHUMO5) : 1 == 1;
			rowDetail.TKPHUME != null ? setValueToCbo('txtTKPHUME', 'cboPHUME', rowDetail.TKPHUME, rowDetail.TENPHUME) : 1 == 1;
			rowDetail.TKPHUME2 != null ? setValueToCbo('txtTKPHUME2', 'cboPHUME2', rowDetail.TKPHUME2, rowDetail.TENPHUME2) : 1 == 1;
			rowDetail.TKBACSIGAYME != null ? setValueToCbo('txtTKBACSIGAYME', 'cboBACSIGAYME', rowDetail.TKBACSIGAYME, rowDetail.TENBACSIGAYME) : 1 == 1;
			//rowDetail.PHUME_ID != null ? setValueToCbo('txtTKPHUME','cboPHUME',rowDetail.PHUME_ID,rowDetail.PHUME) : 1 == 1;
			rowDetail.TKDUNGCUVIEN != null ? setValueToCbo('txtTKDUNGCUVIEN', 'cboDUNGCUVIEN', rowDetail.TKDUNGCUVIEN, rowDetail.TENDUNGCUVIEN) : 1 == 1;
		}
		//L2PT-7739
		//if(!isGetICD_Bandau){
		//START fix loi ma icd cd vao khoa co trong ten icd => hien thi sai icd, lay icd dau tien co ma icd
		if (rowDetail.MA_CHANDOANVAOKHOA != null && rowDetail.MA_CHANDOANVAOKHOA != '') {
			machuandoanvaokhoa = rowDetail.MA_CHANDOANVAOKHOA;
		} else {
			machuandoanvaokhoa = machandoanravien;
		}
		if (rowDetail.CHANDOANVAOKHOA != null && rowDetail.CHANDOANVAOKHOA != '') {
			tenchuandoanvaokhoa = rowDetail.CHANDOANVAOKHOA;
		} else {
			tenchuandoanvaokhoa = chandoanravien;
		}
		//START L2PT-9014
		if (rowDetail.MA_CHANDOANTRUOCPHAUTHUAT != null && rowDetail.MA_CHANDOANTRUOCPHAUTHUAT != '') {
			machuandoantruocpt = rowDetail.MA_CHANDOANTRUOCPHAUTHUAT;
		} else {
			machuandoantruocpt = machandoanravien;
			if (form_pttt_bvnt) {
				machuandoantruocpt = machuandoanvaokhoa;
			}
		}
		if (rowDetail.CHANDOANTRUOCPHAUTHUAT != null && rowDetail.CHANDOANTRUOCPHAUTHUAT != '') {
			tenchuandoantruocpt = rowDetail.CHANDOANTRUOCPHAUTHUAT;
		} else {
			tenchuandoantruocpt = chandoanravien;
			if (form_pttt_bvnt) {
				tenchuandoantruocpt = tenchuandoanvaokhoa;
			}
		}
		if (rowDetail.MA_CHANDOANSAUPHAUTHUAT != null && rowDetail.MA_CHANDOANSAUPHAUTHUAT != '') {
			machuandoansaupt = rowDetail.MA_CHANDOANSAUPHAUTHUAT;
		} else {
			machuandoansaupt = machandoanravien;
			if (form_pttt_bvnt) {
				machuandoansaupt = machuandoanvaokhoa;
			}
		}
		if (rowDetail.CHANDOANSAUPHAUTHUAT != null && rowDetail.CHANDOANSAUPHAUTHUAT != '') {
			tenchuandoansaupt = rowDetail.CHANDOANSAUPHAUTHUAT;
		} else {
			tenchuandoansaupt = chandoanravien;
			if (form_pttt_bvnt) {
				tenchuandoansaupt = tenchuandoanvaokhoa;
			}
		}
		//L2PT-101148
		if (rowDetail.MACHANDOANYHCT != null && rowDetail.MACHANDOANYHCT != '') {
			machuandoanyhct = rowDetail.MACHANDOANYHCT;
			tenchuandoanyhtc = rowDetail.CHANDOANYHCT;
		}
		//END L2PT-9014
		$(document).ready(function() {
			setTimeout(function() {
				$("#txtMA_CHANDOANVAOKHOA").val(machuandoanvaokhoa);
				$("#txtCHANDOANVAOKHOA").val(tenchuandoanvaokhoa);
				//L2PT-25760
				//L2PT-101148
				if(machuandoanyhct){
					$("#txtMACHANDOANYHCT").val(machuandoanyhct);
					$("#txtCHANDOANYHCT").val(tenchuandoanyhtc);
				}
				$("#txtMA_CHANDOANTRUOCPHAUTHUAT").val(machuandoantruocpt);
				$("#txtMA_CHANDOANSAUPHAUTHUAT").val(machuandoansaupt);
				//L2PT-18622
				if (opt.hospital_id == '30680') {
					$("#txtCHANDOANTRUOCPHAUTHUAT").val(tenchuandoantruocpt + ';' + chandoankemtheo);
					$("#txtCHANDOANSAUPHAUTHUAT").val(tenchuandoansaupt + ';' + chandoankemtheo);
				}
				if (cfObj.NTU_PTTT_CDPTTT_MACDINH == '1') {//L2PT-34687
					if (_phauthuatthuthuatid == '') {
						$("#txtCHANDOANTRUOCPHAUTHUAT,#txtMA_CHANDOANTRUOCPHAUTHUAT,#txtCHANDOANSAUPHAUTHUAT,#txtMA_CHANDOANSAUPHAUTHUAT").val('');
					}
				} else {
					$("#txtCHANDOANTRUOCPHAUTHUAT").val(tenchuandoantruocpt);
					$("#txtCHANDOANSAUPHAUTHUAT").val(tenchuandoansaupt);
				}
				//L2PT-41542
				if (cfObj.NTU_PTTT_CDP_DF_ICD == '1') {
					if ($("#txtCHANDOANPHU_TRC").val() == '') {
						$("#txtCHANDOANPHU_TRC").val(chandoankemtheo);
					}
					if ($("#txtCHANDOANPHU_SAU").val() == '') {
						$("#txtCHANDOANPHU_SAU").val(chandoankemtheo);
					}
				}
				//START L2PT-19388
				if (check_songuoi_thuchien) {
					$('#txtSONGUOI').val(sl_nguoi);
				}
				//BVTM-5609
				if (opt.hospital_id == '10284') {
					$('#txtNGAYPHAUTHUATTHUTHUAT').focus();
				}
				//START L2PT-29317, //L2PT-34022
				if (opt.trangthaiketqua != null && opt.trangthai_mbp != null && opt.trangthaiketqua == '7' && opt.trangthai_mbp == '3') {
					if (cfObj.NTU_PTTT_CHIXEM == '1') {
						$('#btnLuu,#btnLuuDong,#btnKyCa,#btnLuuDongIn').hide();
					}
				}
				if (data_ar[0].NTU_PTTT_MACDINH_PTVCHINH == '1' && _phauthuatthuthuatid == '') {
					$("#txtTKPHAUTHUATVIEN").val("");
					var sql_par_off = [];
					sql_par_off.push({
						"name" : "[0]",
						"value" : opt.user_id
					});
					var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTUD0037.OFFICE", sql_par_off);
					if (data2 != null) {
						var rows = JSON.parse(data2);
						var option = $('<option value="' + rows[0]["OFFICER_ID"] + '">' + rows[0]["OFFICER_NAME"] + '</option>');
						$("#cboPHAUTHUATVIEN").empty();
						$("#cboPHAUTHUATVIEN").append(option);
					}
				}
				//L2PT-105624
				var diffMinute = diffDate($('#txtKETTHUCPTTT_DKLCI').val(), $('#txtNGAYPHAUTHUATTHUTHUAT').val(), 'DD/MM/YYYY HH:mm', 'minutes');
				if(diffMinute || diffMinute ==0){
					$('#txtTHOIGIAN_PTTT').val(diffMinute);
				}
			}, tg_timeout_ready);
		});
		//END fix loi ma icd cd vao khoa
		//}
		$("#txtTENPHONG").val(opt.tenphong);
		$("#txtVAOVIENLUC").val(opt.thoigianvaovien);
		$("#txtNGHENGHIEP").val(opt.nghenghiep);
		if ($("#txtNGAYPHAUTHUATTHUTHUAT").val() == '') {
			//L2PT-34076
			if (ngaypttmacdinh_tgchidinh && opt.ngaymaubenhpham) {
				var ngayPttt = moment(opt.ngaymaubenhpham, "DD/MM/YYYY HH:mm:ss");
				//if(opt.hospital_id == '30360'){
				//L2PT-37943 -- them so phut cfObj.NTU_PTTT_TGCD_ADD_SOPHUT vao ngay opt.ngaymaubenhpham
				if (cfObj.NTU_PTTT_TGCD_ADD_SOPHUT != '0') {
					ngayPttt.add(cfObj.NTU_PTTT_TGCD_ADD_SOPHUT, 'minutes');
				}
				$("#txtNGAYPHAUTHUATTHUTHUAT").val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
				// them so phut mac dinh vao ngay pttt luc de set vao ket thuc pttt
				if (ngaypttmacdinh_add_sophut != '0') {
					ngayPttt.add(ngaypttmacdinh_add_sophut, 'minutes');
				} else if (cfObj.TG_THUCHIEN_PTTT != '' && cfObj.TG_THUCHIEN_PTTT != null) {//L2PT-42499
					ngayPttt.add(cfObj.TG_THUCHIEN_PTTT, 'minutes');
				}
				$("#txtKETTHUCPTTT").val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
				$("#txtKETTHUCPTTT_DKLCI").val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
			} else {
				//L2PT-17519 - set t/g ket thuc pttt = pttt luc + ngaypttmacdinh_add_sophut.
				//ngaypttmacdinh_add_sophut = 0  mac dinh  ket thuc pttt = pttt luc
				var ngayPttt = moment(dtSys, "DD/MM/YYYY HH:mm:ss");
				if (ngaypttmacdinh_add_sophut != '0') {
					ngayPttt.add(ngaypttmacdinh_add_sophut, 'minutes');
					$("#txtKETTHUCPTTT").val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
					$("#txtKETTHUCPTTT_DKLCI").val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
				} else if (cfObj.TG_THUCHIEN_PTTT != '' && cfObj.TG_THUCHIEN_PTTT != null) {//L2PT-42499
					ngayPttt.add(cfObj.TG_THUCHIEN_PTTT, 'minutes');
					$('#txtKETTHUCPTTT').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
					$('#txtKETTHUCPTTT_DKLCI').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
				}
				if (cfObj.NTU_PTTT_BOTRONG_TIME == '0')
					$("#txtNGAYPHAUTHUATTHUTHUAT").val(dtSys);
				//L2PT-69541
				if (cfObj.NTU_PTTT_DONGBO_TIME_CDHA == '1' && data1[0] && data1[0].LOAINHOMMAUBENHPHAM == 2) {
					$("#txtNGAYPHAUTHUATTHUTHUAT").val(data1[0].NGAYMAUBENHPHAM_LAYMAU);
					//L2PT-80601
					if (cfObj.NTU_PTTT_DONGBOMOTA_CDHA == '1' && data1[0].NGAYKETQUA)
						$("#txtNGAYPHAUTHUATTHUTHUAT").val(data1[0].NGAYKETQUA);
					$("#txtKETTHUCPTTT").val(data1[0].NGAYMAUBENHPHAM_HOANTHANH);
					$("#txtKETTHUCPTTT_DKLCI").val(data1[0].NGAYMAUBENHPHAM_HOANTHANH);
				}
			}
		}
		//L2PT-45297
		if (cfObj.NTU_PTTT_TGCD_GMHS == '1' && !_phauthuatthuthuatid) {
			var objData = new Object();
			objData.HOSOBENHANID = opt.hosobenhanid;
			objData.DICHVUKHAMBENHID = opt.dichvukhambenhid;
			var _par = [ JSON.stringify(objData) ];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('NTU37.GETGMHS', _par.join('$'));
			if (data_ar != null && data_ar.length > 0) {
				$("#txtNGAYPHAUTHUATTHUTHUAT").val(data_ar[0].TTTD_TGBATDAUMO);
				$("#txtKETTHUCPTTT_DKLCI").val(data_ar[0].TTTD_TGKETTHUCMO);
			}
		}
		//Begin_HaNv_19062018: Bổ sung thêm thông tin - L2DKBD-1275
		if (isShowInfoTuongTrinh) {
			if ($("#txtBATDAUTHUOCME").val() == '' && !detrong_giothuocme) {
				$("#txtBATDAUTHUOCME").val(dtSys);
			}
			if ($("#txtDUTTHUOCME").val() == '' && !detrong_giothuocme) {
				if (cfObj.NTU_PTTT_SOPHUT_THUOCME != '0') {
					var giodutthuocme = moment($("#txtBATDAUTHUOCME").val(), "DD/MM/YYYY HH:mm:ss");
					giodutthuocme.add(cfObj.NTU_PTTT_SOPHUT_THUOCME, 'minutes');
					$('#txtDUTTHUOCME').val(giodutthuocme.format('DD/MM/YYYY HH:mm:ss'));
				} else {
					$("#txtDUTTHUOCME").val(dtSys);
				}
			}
			//L2PT-27254
			if (kt_pttt_dklci) {
				if ($("#txtKETTHUCPTTT_DKLCI").val() == '') {
					$("#txtKETTHUCPTTT_DKLCI").val(dtSys);
				}
			} else {
				//L2PT-66580
				if ($("#txtKETTHUCPTTT").val() == '' && cfObj.NTU_PTTT_BOTRONG_TIME == '0') {
					$("#txtKETTHUCPTTT").val(dtSys);
				}
			}
		}
		//End_HaNv_19062018
		$("#txtMA_CHANDOANVAOKHOA").focus();
		//check ket thuc benh an
		sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : opt.tiepnhanid
		});
		var thangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("PTTT.CHECK_KTBA", sql_par);
		if (thangthaitiepnhan != '0' && checkktBA) {
			$("#calNGAYPHAUTHUATTHUTHUAT").hide();
			$("#txtNGAYPHAUTHUATTHUTHUAT").prop('disabled', true);
		} else {
			$("#calNGAYPHAUTHUATTHUTHUAT").show();
		}
		//START L2PT-469
		loadCombomau();
		//END L2PT-469
		//START L1PT-680
		if (opt.hospital_id == '1014' || opt.hospital_id == '1077') {
			$("#lbldcvien").text("Dụng cụ vòng trong");
			$("#lblgv").text("Dụng cụ vòng ngoài");
			$("#ptttluc").text("Bắt đầu PTTT");
			$("#ptvchinh").text("Phẫu thuật viên chính");
			$("#divChaymayBM").show();
			$("#divEkipPTTT").show();
		} else {
			$("#divChaymayBM").hide();
			$("#divEkipPTTT").hide();
		}
		//END L1PT-680
		//STARTL2PT-32306--show ekip pttt
		if (show_ekip_pttt) {
			$("#divEkipPTTT").show();
		}
		//L2PT-32306
		if (opt.hospital_id == '902') {
			$("#lblgv").text("Điều dưỡng chạy ngoài");
		}
		//START L1PT-1139
		_colTEN_MAUEKIP = "Tên mẫu,TENMAU_EKIP,30,0,f,l";
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			"value" : opt.khoaid
		});
		ComboUtil.initComboGrid("txtTK_EKIPPTTT", "TENMAU_EKIP", _sql_par, "300px", _colTEN_MAUEKIP, function(event, ui) {
			var idMau = ui.item.ID;
			var option = $('<option value="' + ui.item.ID + '">' + ui.item.TENMAU_EKIP + '</option>');
			$("#cboEKIPPTTT").empty();
			$("#cboEKIPPTTT").append(option);
			$("#txtTENMAU_EKIP").val(ui.item.TENMAU_EKIP);
			var sql_par1 = [ idMau ];
			var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("TT_MAUEKIP_PTTT", sql_par1.join('$'));
			if (dataDetail != null && dataDetail.length > 0) {
				rowDetail = dataDetail[0];
				FormUtil.setObjectToForm("divMain", "", rowDetail);
			}
		});
		//END L1PT-1139
		//L2PT-34023
		if (opt.loaiptttid) {
			//BVTM-2458 check loai pttt la thu thuat thi khong load cac loai pttt
			if ('1,2,3,4'.indexOf(hang_pttt_id) != -1) {
				//bv bưu điện tỉ lệ thủ thuật = tỉ lệ PT chính => add value = id của PT chính
				$('#cboLOAIPTTTID').append('<option value="0">Thủ thuật</option>')
			} else {
				ComboUtil.getComboTag("cboLOAIPTTTID", 'NTU02D037.LOAIPTTT', [], opt.loaiptttid, {
					text : "--- chọn ---",
					value : -1
				}, 'sql');
			}
		}
		//DoanPV_20210511 check chuyển BN về hậu phẫu BDHNI
		var _chuyenBN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PTTT_CHUYEN_BN');
		if (_chuyenBN == 1) {
			var objCheck = new Object();
			objCheck.KHAMBENHID = opt.khambenhid;
			objCheck.MAUBENHPHAMID = opt.maubenhphamid;
			objCheck.KHOAID = opt.dept_id;
			var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D049.CHECK.CBN", JSON.stringify(objCheck));
			if (_check == '1') {
				$("#divChuyenBN").show();
				$('#chkCHUYENHAUPHAU').prop('checked', true);
			} else if (_check == '2') {
				DlgUtil.showMsg("Bệnh nhân đã chuyển về hậu phẫu");
			} else if (_check == '3') {
				$("#divChuyenBN").show();
			}
			//L2PT-60506
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : opt.phongid
			});
			var maphong = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", _sql_par);
			if ($.inArray(maphong, cfObj.NTU_PTTT_PHONG_HAUPHAU.split(',')) >= 0)
				$("#divChuyenBN").show();
		}
		// Xử lý sự kiện liên quan ký CA => START
		var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (_cauhinh == '1') {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnInCa").show();
			_checkBtnKySo(); //L2PT-29044
		}
		// Xử lý sự kiện liên quan ký CA => END
		//L2PT-47936
		if (thangthaitiepnhan != '0' && cfObj.NTU_PTTT_ANNUT_KTBA == '1') {
			$("#btnLuu").hide();
			$("#btnLuuDong").hide();
			$("#btnLuuIn_BND").hide();
			$("#btnLuuDongIn").hide();
			$("#btnLuuMau").hide();
			$("#btnXoaMau").hide();
			$("#btnXoaCapnhat").hide();
			$("#btnKyCa").hide();
			$("#btnHuyCa").hide();
			//L2PT-82509
			//$("#btnInCa").hide(); 
			$("#btnThemDV").hide();
		}
	}
	//in phieu
	function inPhieu() {
		$("#btnExport").on("click", function(e) {
			print();
		})
	};
	//L2PT-6521
	function print() {
		if (opt.hospital_id == '965') {
			var par = [ {
				name : 'ptttid',
				type : 'String',
				value : _phauthuatthuthuatid
			} ];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LIETKEANH", opt.maubenhphamid + "$");
			var max = (data_ar.length > 6 ? 6 : data_ar.length);
			for (var i = 0; i < max; i++) {
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if (hinh_anh != "" && hinh_anh != null) {
					par.push({
						name : 'hinh_anh' + i,
						type : 'Image',
						value : hinh_anh
					});
				}
			}
			par.push({
				name : 'soluong',
				type : 'String',
				value : max
			});
			openReport('window', "PHIEU_TUONGTRINHPHAUTHUAT_A4", "pdf", par);
			//BVTM-5887 E17092021
		} else {
			var par = [ {
				name : 'dichvukhambenhid',
				type : 'String',
				value : opt.dichvukhambenhid
			}, {
				name : 'benhnhanid',
				type : 'String',
				value : opt.benhnhanid
			}, { // ChienDV : L2PT-101010
				name : 'hosobenhanid',
				type : 'String',
				value : opt.hosobenhanid
			} ];
			//L2PT-22996
			if (opt.hospital_id == '913' && (madichvu_caychi == '08.0007.0227' || mdv_in_caychi_ydgli.indexOf(madichvu_caychi) != -1)) {
				openReport('window', "PHIEU_KETQUA_CAYCHI_913", "pdf", par);
			} else {
				//start L2PT-5577(L2PT-5654)
				var param = [ opt.dichvukhambenhid ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GETIMG", param.join('$'));
				if (data_ar != null) {
					for (var i = 0; i < data_ar.length; i++) {
						var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
						if (hinh_anh1 != null && hinh_anh1 != "")
							par.push({
								name : 'hinh_anh' + i,
								type : 'Image',
								value : hinh_anh1
							});
					}
				}
				//end L2PT-5577(L2PT-5654)
				//L2PT-5239
				if (opt.hospital_id == '923') {
					openReport('window', "NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4_14866", "pdf", par);
				} else {
					openReport('window', "NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4", "pdf", par);
				}
			}
		}
	};
	function bindEvent() {
		saveAndClose();
		// luu them
		saveOrUpdate();
		//dong trang
		closePage();
		//in phieu
		inPhieu();
		//L2PT-6521
		saveAndPrint();
		//ký số
		saveAndCa();
		huyCa();
		exportKyCA();
		// ChienDV START L2PT-71163
		$("#cboThemPhieu").on("change", function(e) {
			onClickThemPhieu(1);
		});
		$("#editThemPhieu").on("click", function() {
			onClickThemPhieu(2);
		})
		// ChienDV END L2PT-71163
		var f2 = 113;
		$(document).unbind('keydown').keydown(function(e) {
			if (e.keyCode == f2) {
				getIcd(e.target);
			}
		});
		EventUtil.setEvent("assignSevice_resultTK", function(e) {
			if (e.mode == '0') {
				$('#' + e.ctrId).combogrid("setValue", e.text);
			} else if (e.mode == '1') {
				$('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
			}
			DlgUtil.close(e.popupId);
		});
		//L2PT-34023
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			DlgUtil.showMsg(e.msg);
			//loadGrid();
			DlgUtil.close("divDlgDichVu");
			if (e.result) {
				var result = e.result.split('@')[1];
				opt.dichvukhambenhid = result;
				//BVTM-1295
				ppvc_default = $("#cboPTTT_PHUONGPHAPVOCAMID").val();
				initControl();
			}
		});
		$('#btnCLEARPHAUTHUATVIEN').on("click", function() {
			$("#txtTKPHAUTHUATVIEN").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHAUTHUATVIEN").empty();
			$("#cboPHAUTHUATVIEN").append(option);
		});
		$('#btnCLEARPHAUTHUATVIEN2').on("click", function() {
			$("#txtTKPHAUTHUATVIEN2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHAUTHUATVIEN2").empty();
			$("#cboPHAUTHUATVIEN2").append(option);
		});
		//START L1PT-680
		$('#btnCLEARCHAYMAYCHINH').on("click", function() {
			$("#txtTKCHAYMAYCHINH").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboCHAYMAYCHINH").empty();
			$("#cboCHAYMAYCHINH").append(option);
		});
		$('#btnCLEARCHAYMAYPHU').on("click", function() {
			$("#txtTKCHAYMAYPHU").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboCHAYMAYPHU").empty();
			$("#cboCHAYMAYPHU").append(option);
		});
		//END L1PT-680
		$('#btnCLEARDUNGCUVIEN').on("click", function() {
			$("#txtTKDUNGCUVIEN").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboDUNGCUVIEN").empty();
			$("#cboDUNGCUVIEN").append(option);
		});
		$('#btnCLEARBACSIGAYME').on("click", function() {
			$("#txtTKBACSIGAYME").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBACSIGAYME").empty();
			$("#cboBACSIGAYME").append(option);
		});
		$('#btnCLEARPHUME').on("click", function() {
			$("#txtTKPHUME").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME").empty();
			$("#cboPHUME").append(option);
		});
		$('#btnCLEARPHUME2').on("click", function() {
			$("#txtTKPHUME2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME2").empty();
			$("#cboPHUME2").append(option);
		});
		$('#btnCLEARDIEUDUONG').on("click", function() {
			$("#txtTKDIEUDUONG").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboDIEUDUONG").empty();
			$("#cboDIEUDUONG").append(option);
		});
		$('#btnCLEARDIEUDUONG2').on("click", function() {
			$("#txtTKDIEUDUONG2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboDIEUDUONG2").empty();
			$("#cboDIEUDUONG2").append(option);
		});
		$('#btnCLEARPHUMO1').on("click", function() {
			$("#txtTKPHUMO1").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUMO1").empty();
			$("#cboPHUMO1").append(option);
		});
		$('#btnCLEARPHUMO2').on("click", function() {
			$("#txtTKPHUMO2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUMO2").empty();
			$("#cboPHUMO2").append(option);
		});
		$('#btnCLEARPHUMO3').on("click", function() {
			$("#txtTKPHUMO3").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUMO3").empty();
			$("#cboPHUMO3").append(option);
		});
		$('#btnCLEARBACSITHUCHIEN').on("click", function() {
			$("#txtTKBACSITHUCHIEN").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBACSITHUCHIEN").empty();
			$("#cboBACSITHUCHIEN").append(option);
		});
		$('#btnCLEARPHUMO4').on("click", function() {
			$("#txtTKPHUMO4").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUMO4").empty();
			$("#cboPHUMO4").append(option);
		});
		$('#btnCLEARPHUMO5').on("click", function() {
			$("#txtTKPHUMO5").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUMO5").empty();
			$("#cboPHUMO5").append(option);
		});
		//L2PT-32060
		$('#btnCLEARVC_CHINH').on("click", function() {
			$("#txtVC_CHINH_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboVC_CHINH").empty();
			$("#cboVC_CHINH").append(option);
		});
		$('#btnCLEARVC_PHU').on("click", function() {
			$("#txtVC_PHU_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboVC_PHU").empty();
			$("#cboVC_PHU").append(option);
		});
		$('#btnCLEARXEP_LICH').on("click", function() {
			$("#txtXEP_LICH_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboXEP_LICH").empty();
			$("#cboXEP_LICH").append(option);
		});
		$('#btnCLEARHUU_TRUNG').on("click", function() {
			$("#txtHUU_TRUNG_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboHUU_TRUNG").empty();
			$("#cboHUU_TRUNG").append(option);
		});
		$('#btnCLEARVO_TRUNG').on("click", function() {
			$("#txtVO_TRUNG_ID").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboVO_TRUNG").empty();
			$("#cboVO_TRUNG").append(option);
		});
		//END L2PT-32060
		//START 1404
		$('#btnCLEARPHUME3').on("click", function() {
			$("#txtTKPHUME3").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME3").empty();
			$("#cboPHUME3").append(option);
		});
		$('#btnCLEARPHUME4').on("click", function() {
			$("#txtTKPHUME4").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME4").empty();
			$("#cboPHUME4").append(option);
		});
		$('#btnCLEARPHUME5').on("click", function() {
			$("#txtTKPHUME5").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME5").empty();
			$("#cboPHUME5").append(option);
		});
		$('#btnCLEARPHUME6').on("click", function() {
			$("#txtTKPHUME6").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboPHUME6").empty();
			$("#cboPHUME6").append(option);
		});
		//END 1404
		//START -- HISL2TK-691 --hongdq
		loadCboMau();
		$("#cboMAULUOCDOPTTT").on("change", function(e) {
			setEnabled([ 'btnXoaMauLUOCDO' ], []);
			var tmp_id = $("#cboMAULUOCDOPTTT").val();
			if (tmp_id != '') {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.MAUCT", tmp_id);
				if (data_ar != null && data_ar.length > 0) {
					var row = data_ar[0];
					$("#txtLUOCDOPTTT").val(row.NOIDUNG);
				}
			} else {
				$("#txtLUOCDOPTTT").val("");
			}
		});
		$("#cboMAUTRINHTUPTTT").on("change", function(e) {
			setEnabled([ 'btnXoaMauTRINHTU' ], []);
			var tmp_id = $("#cboMAUTRINHTUPTTT").val();
			if (tmp_id != '') {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.MAUCT", tmp_id);
				if (data_ar != null && data_ar.length > 0) {
					var row = data_ar[0];
					$("#txtTRINHTUPTTT").val(row.NOIDUNG);
				}
			} else {
				$("#txtLUOCDOPTTT").val("");
			}
		});
		$("#btnLuuMauLUOCDO").on("click", function(e) {
			if ($("#txtTENMAULUOCDOPTTT").val().trim() == "") {
				$("#txtTENMAULUOCDOPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập tên mẫu.");
			}
			if ($("#txtLUOCDOPTTT").val().trim() == "") {
				$("#txtLUOCDOPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập nội dung.");
			}
			var objData = new Object();
			var _par;
			objData["LOAI"] = '1';
			objData["TENMAU"] = $("#txtTENMAULUOCDOPTTT").val().trim();
			objData["NOIDUNG"] = $("#txtLUOCDOPTTT").val().trim();
			objData["KHOAID"] = opt.khoaid;
			_par = [ JSON.stringify(objData) ];
			var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUMAU", _par.join('$'));
			if (_result == 1) {
				DlgUtil.showMsg("Lưu mẫu thành công");
				$("#txtTENMAULUOCDOPTTT").val("");
				$("#txtLUOCDOPTTT").val("");
				loadCboMau();
			} else if (_result == 2) {
				DlgUtil.showMsg("Tên mẫu bị trùng");
				loadCboMau();
			} else {
				DlgUtil.showMsg("Lưu mẫu không thành công");
			}
		})
		$("#btnXoaMauLUOCDO").on("click", function(e) {
			if ($("#cboMAULUOCDOPTTT").val() == "") {
				return DlgUtil.showMsg("Chưa chọn mẫu.");
			}
			var sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : $("#cboMAULUOCDOPTTT").val()
			});
			jsonrpc.AjaxJson.execute("NTU02D037.XOAMAU", sql_par);
			DlgUtil.showMsg("Xóa thành công !");
			$("#txtLUOCDOPTTT").val("");
			loadCboMau();
		})
		$("#btnLuuMauTRINHTU").on("click", function(e) {
			if ($("#txtTENMAUTRINHTUPTTT").val().trim() == "") {
				$("#txtTENMAUTRINHTUPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập tên mẫu.");
			}
			if ($("#txtTRINHTUPTTT").val().trim() == "") {
				$("#txtTRINHTUPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập nội dung.");
			}
			var objData = new Object();
			var _par;
			objData["LOAI"] = '2';
			objData["TENMAU"] = $("#txtTENMAUTRINHTUPTTT").val().trim();
			objData["NOIDUNG"] = $("#txtTRINHTUPTTT").val().trim();
			objData["KHOAID"] = opt.khoaid;
			_par = [ JSON.stringify(objData) ];
			var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUMAU", _par.join('$'));
			if (_result == 1) {
				DlgUtil.showMsg("Lưu mẫu thành công");
				$("#txtTENMAUTRINHTUPTTT").val("");
				$("#txtTRINHTUPTTT").val("");
				loadCboMau();
			} else if (_result == 2) {
				DlgUtil.showMsg("Tên mẫu bị trùng");
				loadCboMau();
			} else {
				DlgUtil.showMsg("Lưu mẫu không thành công");
			}
		})
		$("#btnXoaMauTRINHTU").on("click", function(e) {
			if ($("#cboMAUTRINHTUPTTT").val() == "") {
				return DlgUtil.showMsg("Chưa chọn mẫu.");
			}
			var sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : $("#cboMAUTRINHTUPTTT").val()
			});
			jsonrpc.AjaxJson.execute("NTU02D037.XOAMAU", sql_par);
			DlgUtil.showMsg("Xóa thành công !");
			$("#txtTRINHTUPTTT").val("");
			loadCboMau();
		})
		//END -- HISL2TK-691 --hongdq
		//START L2PT-469
		$("#btnLuuMau").on("click", function(e) {
			if ($("#txtTEN_MAU").val() == '') {
				return DlgUtil.showMsg("Chưa nhập tên mẫu.");
			}
			var _sql_par = [ {
				"name" : "[0]",
				"value" : $("#txtTEN_MAU").val()
			} ];
			var checkP = jsonrpc.AjaxJson.getOneValue("NTU02D037.CHECK_TEN", _sql_par);
			if (checkP == '0') {
				var objData = new Object();
				var _par;
				FormUtil.setFormToObject("", "", objData);
				//START L2PT-20993
				objData["KHOAID"] = opt.khoaid;//khoa chi dinh
				objData["PHONGID"] = opt.phongid;//phong chi dinh
				objData["MAUBENHPHAMID"] = opt.maubenhphamid;//L2PT-6207
				//L2PT-16204
				var data_arr_Cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_LDTT_PTTT_BND;NTU_NHAP_CDC");
				if (data_arr_Cauhinh != null && data_arr_Cauhinh.length > 0) {
					if (data_arr_Cauhinh[0].NTU_LDTT_PTTT_BND == '1') {
						objData.TRINHTUPTTT = $("#txtTRINHTUPTTT_BND").val();
					}
				}
				_par = [ JSON.stringify(objData) ];
				var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.MAU", _par.join('$'));
				if (_result == '1') {
					DlgUtil.showMsg("Lưu mẫu thành công!");
					loadCombomau();
				} else {
					DlgUtil.showMsg("Lưu mẫu không thành công!");
				}
			} else {
				return DlgUtil.showMsg("Tên mẫu đã có.");
			}
		});
		//START L1PT-1139
		$("#btnLuuMauEKIP").on(
				"click",
				function(e) {
					if ($("#txtTENMAU_EKIP").val() == '') {
						return DlgUtil.showMsg("Chưa nhập tên mẫu ekip.");
					}
					if ($("#cboPHAUTHUATVIEN").val() == '-1' && $("#cboBACSIGAYME").val() == '-1' && $("#cboDUNGCUVIEN").val() == '-1' && $("#cboPHUMO1").val() == '-1' &&
							$("#cboPHUME").val() == '-1' && $("#cboDIEUDUONG").val() == '-1' && $("#cboPHUMO2").val() == '-1' && $("#cboCHAYMAYCHINH").val() == '-1' &&
							$("#cboCHAYMAYCHINH").val() == '-1') {
						return DlgUtil.showMsg("Chưa nhập thông tin vị trí thực hiện.");
					}
					var objData = new Object();
					var _par;
					objData.KHOAID = opt.khoaid;
					FormUtil.setFormToObject("", "", objData);
					_par = [ JSON.stringify(objData) ];
					var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.MAUEKIP", _par.join('$'));
					if (_result == '1') {
						DlgUtil.showMsg("Lưu mẫu thành công!");
						sql_par = [];
					} else if (_result == '3') {
						DlgUtil.showMsg("Tên mẫu đã có!");
					} else {
						DlgUtil.showMsg("Lưu mẫu không thành công!");
					}
				});
		//END L1PT-1139
		$("#btnXoaMau").on("click", function(e) {
			var tmp_id_xoa = $("#cboMAUPTTT").val();
			var ten_mau_xoa = $("#cboMAUPTTT option:selected").text();
			if (tmp_id_xoa == "-1") {
				DlgUtil.showMsg("Chưa chọn mẫu để xóa");
				$('#cboMAUPTTT').focus();
				return false;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa mẫu " + ten_mau_xoa + " không?", function(flag) {
				if (flag) {
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						"value" : tmp_id_xoa
					});
					jsonrpc.AjaxJson.execute("NTU02D037.XOAPT", sql_par);
					DlgUtil.showMsg("Xóa mẫu " + ten_mau_xoa + " thành công");
					loadCombomau();
				}
			});
		});
		//L2PT-42494
		$("#cboKETQUA_OLD").on("change", function(e) {
			var sql_par1 = [ $("#cboKETQUA_OLD").val() ];
			var dataDetailOld = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GET.INFO", sql_par1.join('$'));
			//L2PT-80709
			if (cfObj.NTU_PTTT_NOT_KQ_CU != '0') {
				var lstItem = cfObj.NTU_PTTT_NOT_KQ_CU.split(',');
				if (lstItem.length > 0) {
					for (var i = 0; i < lstItem.length; i++) {
						delete dataDetailOld[0][lstItem[i]];
					}
				}
			}
			FormUtil.setObjectToForm("divMain", "", dataDetailOld[0]);
		});
		$("#cboMAUPTTT").on("change", function(e) {
			var mauid = $("#cboMAUPTTT").val();
			loadMauPttt(mauid);
		});
		//END L2PT-469
		$('#cboKHOAHIENTHI').change(function() {
			loadComboVitriThuchien();
		});
		//L2PT-6207
		/*$('#chkHINHANH_MAU').change(function() {
			if($(this).is(":checked")) {
				$('#lblChkHA_MAU').val('Lấy hình ảnh theo mẫu '+$("#cboMAUPTTT option:selected").text());
			}

		});*/
		//START L2PT-4898
		// upload ảnh lên server, sau khi upload thành công thì update vào database
		$('#btnUpload').on("click", function() {
			//L2PT-61087
			if (cfObj.NTU_PTTT_SLANH_MAX != '0') {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LIETKEANH", opt.maubenhphamid + "$");
				if (data_ar.length >= Number(cfObj.NTU_PTTT_SLANH_MAX)) {
					return DlgUtil.showMsg("Số lượng ảnh upload vượt quá số lượng cho phép!");
				}
			}
			var filename = $("#fileUpload").val();
			// Use a regular expression to trim everything before final dot
			var extension = filename.replace(/^.*\./, '');
			// Iff there is no dot anywhere in filename, we would have extension == filename,
			// so we account for this possibility now
			if (extension == filename) {
				extension = '';
			} else {
				// if there is an extension, we convert to lower case
				// (N.B. this conversion will not effect the value of the extension
				// on the file upload.)
				extension = extension.toLowerCase();
			}
			switch (extension) {
				case 'jpg':
				case 'jpeg':
				case 'png':
				case 'bmp':
					UploadUtil.upload("formUpload", "", luuAnh);
				break;
				default:
					alert("Chỉ cho phép upload ảnh định dạng jpeg, png, bmp");
					submitEvent.preventDefault();
			}
		});
		//L2PT-39088
		$("#btnVehinh").click(function() {
			//L2PT-61087
			if (cfObj.NTU_PTTT_SLANH_MAX != '0') {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LIETKEANH", opt.maubenhphamid + "$");
				if (data_ar.length >= Number(cfObj.NTU_PTTT_SLANH_MAX)) {
					return DlgUtil.showMsg("Số lượng ảnh upload vượt quá số lượng cho phép!");
				}
			}
			$("#modalNote").modal('hide');
			var myModal = DlgUtil.buildSignPadPopup("HÌNH ẢNH PTTT", 628, 400, {}, function(e) {
				myModal.close();
				var signpadImageBase64 = e.imageBase64;
				//console.log(signpadImageBase64);
				var dt = new Date();
				var pictureName = "HINH_PTTT" + dt.getFullYear() + "" + (dt.getMonth() + 1) + "" + dt.getDate() + "" + dt.getHours() + "" + dt.getMinutes() + "" + dt.getSeconds();
				if (signpadImageBase64) {
					var rs = UploadUtil.uploadBase64(signpadImageBase64, 'png');
					var param = opt.maubenhphamid + '$' + rs.id + '$' + rs.url + '$' + pictureName;
					var kqUpAnh = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUANH", param);
					lietKeAnh();
				}
			});
			myModal.open();
		});
		//L2PT-60804
		$("#fileUpload").change(function() {
			files = this.files;
		})
		$("#btnSuahinh").click(function() {
			if (files.length == 0)
				return DlgUtil.showMsg("Chưa chọn ảnh để chỉnh sửa");
			let
			data = files[0];
			let
			urlImage = URL.createObjectURL(data);
			var myModal = DlgUtil.buildImageEditorPopup(urlImage, function(e) {
				myModal.close();
				console.log(e);
				var dt = new Date();
				var pictureName = "HINH_PTTT" + dt.getFullYear() + "" + (dt.getMonth() + 1) + "" + dt.getDate() + "" + dt.getHours() + "" + dt.getMinutes() + "" + dt.getSeconds();
				if (e.imageBase64) {
					var rs = UploadUtil.uploadBase64(e.imageBase64, 'png');
					var param = opt.maubenhphamid + '$' + rs.id + '$' + rs.url + '$' + pictureName;
					var kqUpAnh = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUANH", param);
					lietKeAnh();
				}
			});
			myModal.open();
		})
		//L2PT-74418
		$("#btnChonhinh").click(function() {
			let
			myModal = DlgUtil.buildFileManagementPopup("Chọn ảnh", "90%", "90%", {
				code : 'HIS_IMG_SERVER',
				multiselect : false
			}, function(e) {
				myModal.close();
				let
				selected = e.selected;
				//var base64 = getBase64Image(window.location.protocol + '//' + window.location.hostname + '/' + selected[0]);
				toDataURL(window.location.protocol + '//' + window.location.hostname + '/' + selected[0], function(base64) {
					var dt = new Date();
					var pictureName = "HINH_PTTT" + dt.getFullYear() + "" + (dt.getMonth() + 1) + "" + dt.getDate() + "" + dt.getHours() + "" + dt.getMinutes() + "" + dt.getSeconds();
					if (base64) {
						var rs = UploadUtil.uploadBase64(base64, 'png');
						var param = opt.maubenhphamid + '$' + rs.id + '$' + rs.url + '$' + pictureName;
						var kqUpAnh = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUANH", param);
						lietKeAnh();
					}
				})
				console.log(selected)
			});
			myModal.open();
		})
		function toDataURL(url, callback) {
			var xhr = new XMLHttpRequest();
			xhr.onload = function() {
				var reader = new FileReader();
				reader.onloadend = function() {
					callback(reader.result);
				}
				reader.readAsDataURL(xhr.response);
			};
			xhr.open('GET', url);
			xhr.responseType = 'blob';
			xhr.send();
		}
		$('#btnEDIT_EKIPPTTT').on("click", function() {
			var par = {
				thamSo1 : 'hihi',
			};
			dlgPopup = DlgUtil.buildPopupUrl("NTU02D117_DanhMucKipPTTT", "divDsKipPTTT", "manager.jsp?func=../noitru/NTU02D117_DanhMucKipPTTT", par, "Danh sách ekip PTTT", 1000, 520);
			dlgPopup.open();
		});
		//START L2PT-17277
		$("#chkHENTRASAU").change(function() {
			if (this.checked) {
				$("#calTG_HENTRASAU").show();
				$("#txtTG_HENTRASAU").show();
			} else {
				$("#calTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").val("");
			}
		});
		//L2PT-25760
		$("#btnCLEARCHANDOANRAVIEN_TRC").on("click", function(e) {
			$('#txtCHANDOANPHU_TRC').val('');
		});
		$("#btnCLEARCHANDOANRAVIEN_SAU").on("click", function(e) {
			$('#txtCHANDOANPHU_SAU').val('');
		});
		//L2PT-34023
		$('#btnThemDV').on("click", function() {
			//Chỉ cho phép phẫu thuật viên chính chỉ định thêm dịch vụ phẫu thuật phụ đi kèm
			/*var sql_par_off = [];
			sql_par_off.push({
				"name" : "[0]",
				"value" : opt.user_id
			});
			var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO(
						"NTUD095.OFFICE", sql_par_off);
			var rows = JSON.parse(data2);
			var officer_id = rows[0]["OFFICER_ID"];
			if(ptv_chinh != officer_id){
				return DlgUtil.showMsg("Chỉ phẫu thuật viên chính được thêm dịch vụ phụ đi kèm");
			}*/
			boSungDichVu();
		});
		//BVTM-2853
		$('#txtNGAYPHAUTHUATTHUTHUAT').on('keypress', function(e) {
			if (e.which === 13) {
				$('#txtKETTHUCPTTT_DKLCI').focus();
			}
		});
		//BVTM-3345
		if (opt.hospital_id == '10284') {
			//$("#txtNGAYPHAUTHUATTHUTHUAT").attr('maxlength','17');
			//$("#txtKETTHUCPTTT_DKLCI").attr('maxlength','17');
			$("#txtNGAYPHAUTHUATTHUTHUAT").on('keydown', function(e) {
				return IsNumeric(this, e.keyCode);
			});
			$("#txtKETTHUCPTTT_DKLCI").on('keydown', function(e) {
				return IsNumeric(this, e.keyCode);
			});
			$("#txtTHOIGIAN_MUI_1").on('keydown', function(e) {
				return IsNumeric(this, e.keyCode);
			});
			$("#txtNGAYPHAUTHUATTHUTHUAT").on('keyup', function(e) {
				ValidateDateFormat(this, e.keyCode);
			});
			$("#txtKETTHUCPTTT_DKLCI").on('keydown', function(e) {
				ValidateDateFormat(this, e.keyCode);
			});
			$("#txtTHOIGIAN_MUI_1").on('keydown', function(e) {
				ValidateDateFormat(this, e.keyCode);
			});
			// BVTM-3454
			$("#chkNHANVENTRONGKHOA").change(function() {
				if (this.checked) {
					loadEkip_checkbox = true;
					loadComboVitriThuchien();
				} else {
					loadEkip_checkbox = false;
					loadComboVitriThuchien();
				}
			});
			//BVTM-5609
			/*$("#txtNGAYPHAUTHUATTHUTHUAT").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if ( code == 13) {
					$("#txtKETTHUCPTTT_DKLCI").focus();
					return false;
				}
			});*/
			$("#txtKETTHUCPTTT_DKLCI").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtTKPPVOCAM").focus();
					return false;
				}
			});
			$("#txtTKPPVOCAM").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtTKPHAUTHUATVIEN").focus();
					return false;
				}
			});
			//ductx bvtm-7283
			$('#cboPTTT_PHUONGPHAPVOCAMID').click(function() {
				$("#txtTKPPVOCAM").val("");
			});
			//end bvtm-7283
			$("#txtTKPHAUTHUATVIEN").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtTKPHUMO1").focus();
					return false;
				}
			});
			$("#txtTKPHUMO1").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtTKPHUMO2").focus();
					return false;
				}
			});
			$("#txtTKPHUMO2").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtTKPHUMO3").focus();
					return false;
				}
			});
			$("#txtTKPHUMO3").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtVC_CHINH_ID").focus();
					return false;
				}
			});
			$("#txtVC_CHINH_ID").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtVC_PHU_ID").focus();
					return false;
				}
			});
			$("#txtVC_PHU_ID").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#txtVO_TRUNG_ID").focus();
					return false;
				}
			});
			$("#txtVO_TRUNG_ID").on("keydown", function(e) {
				var code = e.keyCode || e.which;
				if (code == 13) {
					$("#btnLuuDong").focus();
					return false;
				}
			});
			//END BVTM-5609
			$('input').focus(function() {
				$(this).css('border-color', '#ff0000');
				$(this).css('outline', '0');
			});
			$('input').focusout(function() {
				$(this).css('border-color', '#a9a9a9');
			});
			//END BVTM-5609
		}
		//L2PT-9899
		$('#cboTHOIGIAN_PTTT_CBO').on('change', function(e) {
			changeTGKT_PTTT();
		});
		$("#txtNGAYPHAUTHUATTHUTHUAT").change(function() {
			changeTGKT_PTTT();
			if (cfObj.NTU_PTTT_CHECK_TRUNGTIME == '1') {
				kiemtra_trunggio_pttt($('#cboPHAUTHUATVIEN').val(), "cboPHAUTHUATVIEN", '1');
			}
			//L2PT-105624
			var diffMinute = diffDate($('#txtKETTHUCPTTT_DKLCI').val(), $('#txtNGAYPHAUTHUATTHUTHUAT').val(), 'DD/MM/YYYY HH:mm', 'minutes');
			if(diffMinute || diffMinute ==0){
				$('#txtTHOIGIAN_PTTT').val(diffMinute);
			}
		});
		$("#txtTHOIGIAN_PTTT").change(function() {
			changeTGKT_PTTT();
		});
		$("#txtKETTHUCPTTT_DKLCI").change(function() {
			//L2PT-110219
			if ((cfObj.NTU_PTTT_TRUNGGIO_HUE == '1' || cfObj.NTU_PTTT_TRUNGGIO_HUE == '2' || cfObj.NTU_PTTT_TRUNGGIO_HUE == '3') 
					|| cfObj.NTU_PTTT_TRUNGGIO_HUE == '4' || cfObj.NTU_PTTT_CHECK_TRUNGTIME == '1') {
				kiemtra_trunggio_pttt($('#cboPHAUTHUATVIEN').val(), "cboPHAUTHUATVIEN", '1');
			}
			//L2PT-42494
			if (cfObj.NTU_PTTT_KHONG_TRUNG_NGAY == '1') {
				if ($('#txtNGAYPHAUTHUATTHUTHUAT').val().substr(0, 2) != $('#txtKETTHUCPTTT_DKLCI').val().substr(0, 2)) {
					return DlgUtil.showMsg("PTTT lúc không trùng ngày với Kết thúc PTTT", function() {}, cfObj.HIS_TIMEOUT_THONGBAO);
				}
			}
			//L2PT-105624
			var diffMinute = diffDate($('#txtKETTHUCPTTT_DKLCI').val(), $('#txtNGAYPHAUTHUATTHUTHUAT').val(), 'DD/MM/YYYY HH:mm', 'minutes');
			if(diffMinute || diffMinute ==0){
				$('#txtTHOIGIAN_PTTT').val(diffMinute);
			}
		});
	}
	// lưu lại đường dẫn ảnh đã upload ứng với kết quả đang nhập
	function luuAnh(data) {
		var param = opt.maubenhphamid + '$' + data.id + '$' + data.url + '$' + document.getElementById('fileUpload').files[0].name.replace(/\.[^/.]+$/, "");
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUANH", param);
		if (rs == 1) {
			$("#fileUpload").val('');
			lietKeAnh();
		} else {
			DlgUtil.showMsg("Nhập ảnh thất bại: " + rs.error_msg, undefined, 3000);
		}
	}
	// liệt kê ảnh đã lưu trong cơ sở dữ liệu
	function lietKeAnh() {
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LIETKEANH", opt.maubenhphamid + "$");
		$("#list").html('');
		for (var i = 0; i < data_ar.length; i++) {
			var img = "<div class='col-md-3' style='text-align:center;'>" + "<img src='" + data_ar[i]["DUONGDANFILE"] + "' width='200px' alt='" + data_ar[i]["MOTA"] + "' />" +
					"<input type='image' src='../common/icon/delete.png' width='18px' onclick='NTU02D037.xoaAnh(" + data_ar[i]["FILEID"] + ");' />" + "</br><i>" + data_ar[i]["MOTA"] + "</i>" +
					"</div>";
			$("#list").append(img);
		}
		//L2PT-80602
		if (cfObj.NTU_PTTT_DONGBOANH_CDHA == '1' && _ketquaclsid) {
			var data_ar_cdha = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LKCDHA", opt.maubenhphamid + "$" + _ketquaclsid);
			$("#listCDHA").html('');
			for (var i = 0; i < data_ar_cdha.length; i++) {
				var img = "<div class='col-md-3' style='text-align:center;'>" + "<img src='" + data_ar_cdha[i]["DUONGDANFILE"] + "' width='200px' alt='" + data_ar_cdha[i]["MOTA"] + "' />" +
						"</br><i>" + data_ar_cdha[i]["MOTA"] + "</i>" + "</div>";
				$("#listCDHA").append(img);
			}
		}
	}
	// xóa ảnh đã upload
	function xoaAnh(idfile) {
		var param = opt.maubenhphamid + '$' + idfile;
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.XOAANH", param);
		if (rs == 1) {
			$("#fileUpload").val('');
			//L2PT-6207
			if (layhinhanh_mau == false) {
				var rt = UploadUtil.deleteMedia(idfile, '');
				if (rt == idfile) {
					DlgUtil.showMsg("Kết quả xóa file: " + rt);
				}
			}
			lietKeAnh();
		} else {
			DlgUtil.showMsg("Xóa ảnh " + idfile + " thất bại ", undefined, 3000);
		}
	}
	//END L2PT-4898
	function validForm() {
		//L2PT-14424
		if (gioihan_ekip != '0') {
			var count_ekip = 0;
			$('[ekippttt]').each(function(index, element) {
				if ($(element).val() != '-1') {
					count_ekip = count_ekip + 1;
				}
			});
			if (count_ekip > gioihan_ekip) {
				return DlgUtil.showMsg("Đã nhập quá vị trí trong kíp PTTT(Tối đa " + gioihan_ekip + ")");
			}
		}
		//L1PT-1562
		if (isNhapChandoanchinh) {
			if ($("#txtCHANDOANVAOKHOA").val() == '') {
				DlgUtil.showMsg("Bạn chưa nhập Chẩn đoán chính.");
				$("#txtCHANDOANVAOKHOA").focus();
				return false;
			}
		}
		//L2PT-87493
		if (cfObj.NTU_PTTT_CHECK_KYTU == '1') {
			if (!kiemtra_kytunhap($("#txtMOTA").val())) {
				DlgUtil.showMsg("Trường mô tả không được nhập ký tự đặc biệt.");
				$("#txtMOTA").focus();
				return false;
			}
			if (containsControlCharacter($("#txtMOTA").val())) {
				DlgUtil.showMsg("Trường mô tả không được nhập ký tự đặc biệt.");
				$("#txtMOTA").focus();
				return false;
			}
		}
		if (!istextPttt) {
			if (isNhapChandoanchinh) {//L1PT-1562
				if ($("#cboPTTT_HangID").val() == '-1') {
					DlgUtil.showMsg("Bạn chưa chọn loại PTTT");
					$("#cboPTTT_HangID").focus();
					return false;
				}
			}
		} else {
			if (isNhapChandoanchinh) {//L1PT-1562
				if ($("#txtPTTT_Hang").val().trim() == '') {
					DlgUtil.showMsg("Bạn chưa nhập loại PTTT");
					$("#txtPTTT_Hang").focus();
					return false;
				}
			}
		}
		//L2PT-92372
		if (checkppvc == '1' && $("#cboPTTT_PHUONGPHAPVOCAMID").val() == '-1') {
			DlgUtil.showMsg("Bạn chưa nhập phương pháp vô cảm");
			$("#cboPTTT_PHUONGPHAPVOCAMID").focus();
			return false;
		}
		
		//L2PT-106738
		if (checkbsgm == '1' && $("#cboBACSIGAYME").val() == '-1') {
			DlgUtil.showMsg("Bạn chưa nhập bác sỹ gây mê/tê");
			$("#cboBACSIGAYME").focus();
			return false;
		}
		
		//L2PT-96184
		if (_mamay_req && $("#cboMAMAY").val() == '-1') {
			DlgUtil.showMsg("Bạn chưa nhập mã máy");
			$("#cboMAMAY").focus();
			return false;
		}
		//L2PT-95386
		// L2PT-123233 start: neu pt loai 1,2,3, dac biet va cau hinh macdinh thi bat buoc nhap
		if ((check_tinhhinhpttt == '1' || macdinh_nhap) && $("#cboPTTT_TINHHINHID").val() == '-1') {
			DlgUtil.showMsg("Bạn chưa nhập Tình hình PTTT");
			$("#cboPTTT_TINHHINHID").focus();
			return false;
		}
		if ((check_taibien == '1' || macdinh_nhap) && $("#cboPTTT_TAIBIENID").val() == '-1') {
			DlgUtil.showMsg("Bạn chưa nhập Tai biến");
			$("#cboPTTT_TAIBIENID").focus();
			return false;
		}
		if ((check_tuvongtrongpttt == '1' || macdinh_nhap) && $("#cboPTTT_TUVONGID").val() == '-1') {
			DlgUtil.showMsg("Bạn chưa nhập Tử vong trong PTTT");
			$("#cboPTTT_TUVONGID").focus();
			return false;
		}
		if (check_nhommau == '1' && $("#cboPTTT_NhomMauID").val() == '-1') { // L2PT-123233_1
			DlgUtil.showMsg("Bạn chưa nhập Nhóm máu");
			$("#cboPTTT_NhomMauID").focus();
			return false;
		}
		if (check_nhommau == '1' && $("#cboPTTT_NHOMMAURHID").val() == '-1') { // L2PT-123233_1
			DlgUtil.showMsg("Bạn chưa nhập RH");
			$("#cboPTTT_NHOMMAURHID").focus();
			return false;
		}
		// L2PT-123233 end
		var txtTuNgay = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
		//L2PT-27254
		if (kt_pttt_dklci) {
			var txtDenNgay = $("#txtKETTHUCPTTT_DKLCI").val();
		} else {
			var txtDenNgay = $("#txtKETTHUCPTTT").val();
		}
		//L2PT-31595 //L2PT-78486
		if (cfObj.NTU_PTTT_UPD_TGBANGNHAU == '1' && txtTuNgay.substr(0, 16) == txtDenNgay.substr(0, 16)) {
			DlgUtil.showMsg("PTTT lúc không được bằng Giờ phẫu thuật xong.");
			return false;
		}
		if (!compareDate(txtTuNgay, txtDenNgay, "DD/MM/YYYY HH:mm:ss")) {
			$("#txtKETTHUCPTTT").focus();
			DlgUtil.showMsg("PTTT lúc không được lớn hơn Giờ phẫu thuật xong.");
			return false;
		}
		//L2PT-39681
		if (cfObj.NTU_PTTT_KHOANGTG_PTTT != '0') {
			var khoangtg = (moment($('#txtKETTHUCPTTT_DKLCI').val(), "DD/MM/YYYY HH:mm:ss") - moment($('#txtNGAYPHAUTHUATTHUTHUAT').val(), "DD/MM/YYYY HH:mm:ss")) / 3600000;
			if (khoangtg > Number(cfObj.NTU_PTTT_KHOANGTG_PTTT)) {
				DlgUtil.showMsg("Khoảng thời gian từ PTTT lúc đến Kết thúc PTTT quá số giờ quy định: " + cfObj.NTU_PTTT_KHOANGTG_PTTT);
				return false;
			}
		}
		if (!compareDate(opt.thoigianvaovien, $("#txtNGAYPHAUTHUATTHUTHUAT").val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
			DlgUtil.showMsg("Thời gian phẫu thuật phải lớn hơn hoặc bằng thời gian vào viện!");
			$('#txtNGAYPHAUTHUATTHUTHUAT').focus();
			return false;
		}
		if ($('#txtSONGUOI').val() != '') {
			if (!/^[1-9][0-9]*$/i.test($('#txtSONGUOI').val())) {
				DlgUtil.showMsg('Trường số lượng người không hợp lệ', function() {
					$('#txtSONGUOI').focus();
				});
				return false;
			}
		}
		if ($('#cboKHOACHIDINH').val() == null || $('#cboKHOACHIDINH').val() == '' || $('#cboKHOACHIDINH').val() == '-1') {
			DlgUtil.showMsg("Vui lòng chọn khoa chỉ định");
			return false;
		}
		//START L2PT-19388
		if (check_songuoi_thuchien) {
			var sl_giupviec_tmp = 0, sl_nhanlucchinh_tmp = 0, sl_nhanlucphu_tmp = 0;
			sl_nhanlucphume_tmp = 0;
			sl_gaymechinh_tmp = 0;
			if (cfObj.NTU_PTTT_CHECKSL_NHIHD == '1') {
				var substr = [ 'cboPHAUTHUATVIEN', 'cboBACSITHUCHIEN', 'cboPHAUTHUATVIEN2', 'cboBACSIGAYME' ]
			} else {
				var substr = [ 'cboPHAUTHUATVIEN', 'cboBACSITHUCHIEN', 'cboPHAUTHUATVIEN2' ]
			}
			$.each(substr, function(index, val) {
				if ($('#' + this).val() != '-1')
					sl_nhanlucchinh_tmp = sl_nhanlucchinh_tmp + 1;
			});
			var substr = [ 'cboPHUMO1', 'cboPHUMO2', 'cboPHUMO3', 'cboPHUMO4', 'cboPHUMO5' ]
			$.each(substr, function(index, val) {
				if ($('#' + this).val() != '-1')
					sl_nhanlucphu_tmp = sl_nhanlucphu_tmp + 1;
			});
			var substr = [ 'cboPHUME', 'cboPHUME2', 'cboPHUME3', 'cboPHUME4', 'cboPHUME5', 'cboPHUME6' ]
			$.each(substr, function(index, val) {
				if ($('#' + this).val() != '-1')
					sl_nhanlucphume_tmp = sl_nhanlucphume_tmp + 1;
			});
			var substr = [ 'cboDUNGCUVIEN', 'cboDIEUDUONG', 'cboDIEUDUONG2' ]
			$.each(substr, function(index, val) {
				if ($('#' + this).val() != '-1')
					sl_giupviec_tmp = sl_giupviec_tmp + 1;
			});
			if (cfObj.NTU_PTTT_CHECKSL_NHIHD == '0') {
				if ($('#cboBACSIGAYME').val() != '-1')
					sl_gaymechinh_tmp = sl_gaymechinh_tmp + 1;
				if (sl_gaymechinh != null && sl_gaymechinh_tmp > sl_gaymechinh) {
					DlgUtil.showMsg("Đã nhập số lượng nhân lực gây mê chính lớn hơn quy định");
					return false;
				}
			}
			if (sl_nhanlucchinh != null && sl_nhanlucchinh_tmp > sl_nhanlucchinh) {
				DlgUtil.showMsg("Đã nhập số lượng nhân lực chính lớn hơn quy định");
				return false;
			}
			if (sl_nhanlucphu != null && sl_nhanlucphu_tmp > sl_nhanlucphu) {
				DlgUtil.showMsg("Đã nhập số lượng nhân lực phụ lớn hơn quy định");
				return false;
			}
			if (sl_giupviec != null && sl_giupviec_tmp > sl_giupviec) {
				DlgUtil.showMsg("Đã nhập số lượng giúp việc lớn hơn quy định");
				return false;
			}
			if (sl_nhanlucphume != null && sl_nhanlucphume_tmp > sl_nhanlucphume) {
				DlgUtil.showMsg("Đã nhập số lượng nhân lực phụ mê lớn hơn quy định");
				return false;
			}
		}
		//L2PT-32060
		if (check_trungvitri_pttt) {
			var arrVitri = [];
			if ($("#cboPHAUTHUATVIEN").val() != null && $("#cboPHAUTHUATVIEN").val() != '-1')
				arrVitri.push($("#cboPHAUTHUATVIEN").val());
			if ($("#cboPHUMO1").val() != null && $("#cboPHUMO1").val() != '-1')
				arrVitri.push($("#cboPHUMO1").val());
			if ($("#cboPHUMO2").val() != null && $("#cboPHUMO2").val() != '-1')
				arrVitri.push($("#cboPHUMO2").val());
			if ($("#cboPHUMO3").val() != null && $("#cboPHUMO3").val() != '-1')
				arrVitri.push($("#cboPHUMO3").val());
			if ($("#cboDUNGCUVIEN").val() != null && $("#cboDUNGCUVIEN").val() != '-1')
				arrVitri.push($("#cboDUNGCUVIEN").val());
			if ($("#cboVC_CHINH").val() != null && $("#cboVC_CHINH").val() != '-1')
				arrVitri.push($("#cboVC_CHINH").val());
			if ($("#cboVC_PHU").val() != null && $("#cboVC_PHU").val() != '-1')
				arrVitri.push($("#cboVC_PHU").val());
			if ($("#cboXEP_LICH").val() != null && $("#cboXEP_LICH").val() != '-1')
				arrVitri.push($("#cboXEP_LICH").val());
			if ($("#cboHUU_TRUNG").val() != null && $("#cboHUU_TRUNG").val() != '-1')
				arrVitri.push($("#cboHUU_TRUNG").val());
			if ($("#cboVO_TRUNG").val() != null && $("#cboVO_TRUNG").val() != '-1')
				arrVitri.push($("#cboVO_TRUNG").val());
			if ($("#cboDIEUDUONG").val() != null && $("#cboDIEUDUONG").val() != '-1')
				arrVitri.push($("#cboDIEUDUONG").val());
			var recipientsArray = arrVitri.sort();
			var reportRecipientsDuplicate = [];
			for (var i = 0; i < recipientsArray.length - 1; i++) {
				if (recipientsArray[i + 1] == recipientsArray[i]) {
					reportRecipientsDuplicate.push(recipientsArray[i]);
				}
			}
			if (reportRecipientsDuplicate.length > 0) {
				DlgUtil.showMsg("Đã có vị trí bị trùng trong ekip mổ!", undefined, 3000);
				return false;
			}
		}
		//BVTM-949
		if (opt.hospital_id == '10284') {
			//BVTM-7207 bo bat buoc PTV chinh
			/*if($('#cboPHAUTHUATVIEN').val() == '-1'
				//|| $('#cboVC_CHINH').val() == '-1'  || $('#cboVC_PHU').val() == '-1' || $('#cboPTTT_PHUONGPHAPVOCAMID').val() == '-1' || $('#cboVO_TRUNG').val() == '-1'
					){
					DlgUtil.showMsg("Nhập tất cả các trường bắt buộc có dấu (*)",undefined,3000);
				  return false;
			}*/
			//END BVTM-7207 bo bat buoc PTV chinh
			//BVTM-3412
			//khoa gay me hoi suc va ko phai dv ke them
			if (iskhoa_gmhs && !isKethem) {
				if ($('#cboVC_CHINH').val() == '-1' || $('#cboVC_PHU').val() == '-1' || $('#cboPTTT_PHUONGPHAPVOCAMID').val() == '-1') {
					DlgUtil.showMsg("Nhập tất cả các trường bắt buộc có dấu (*)", undefined, 3000);
					return false;
				}
			}
			//BVTM-7086
			//check voi doi tuong bhyt
			/*if(opt.doituongbenhnhanid == '1'){
				if(check_tg_tvt_trung == '0'){
					var pars = [opt.dichvukhambenhid,opt.ngaymaubenhpham.substr(0,length + 15)];
				}else if(check_tg_tvt_trung == '1'){
					var pars = [opt.dichvukhambenhid,$('#txtNGAYPHAUTHUATTHUTHUAT').val().trim().substr(0,length + 15)]
				}
				var result_tg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D037.CHECK_TVT", pars.join('$'));
				if(result_tg != '' && result_tg != null ){
					if(check_tg_tvt_trung == '0'){
						DlgUtil.showMsg("Thời gian kê phiếu thuốc, vật tư<br> "+result_tg+" <br>khác với thời gian chỉ định PTTT. " +
								"Đề nghị cập nhật lại thời gian kê thuốc trùng với thời gian chỉ định PTTT là "+opt.ngaymaubenhpham.substr(0,length + 15));
						return false;
					}else if(check_tg_tvt_trung == '1'){
						DlgUtil.showMsg("Thời gian kê phiếu thuốc, vật tư<br> "+result_tg+" <br>khác với thời gian chỉ định PTTT. " +
								"Đề nghị cập nhật lại thời gian kê thuốc trùng với thời gian thực hiện PTTT là "+$('#txtNGAYPHAUTHUATTHUTHUAT').val().trim().substr(0,length + 15));
						return false;
					}
				}
			}*/
		}
		//BVTM-2458
		if (opt.loaiptttid) {
			if ('1,2,3,4'.indexOf($('#cboPTTT_HangID').val()) != -1 && $('#cboLOAIPTTTID').val() != '0') {
				DlgUtil.showMsg("Chọn sai các loại PTTT khi dịch vụ là thủ thuật", undefined, 3000);
				return false;
			}
		}
		//BVTM-2625
		if (opt.hospital_id == '10284') {
			//validate 'DD/MM/YYY HH:MM:SS'
			var regEx = /([0-3][0-9])\/([0-1][0-9])\/([0-2][0-9]{3}) ([0-5][0-9]):([0-5][0-9]):([0-5][0-9])(([\-\+]([0-1][0-9])\:00))?/;
			if ($("#txtNGAYPHAUTHUATTHUTHUAT").val().match(regEx) == null) {
				DlgUtil.showMsg("Sai định dạng giờ PTTT lúc", undefined, 3000);
				return false;
			}
			if ($("#txtKETTHUCPTTT_DKLCI").val().match(regEx) == null) {
				DlgUtil.showMsg("Sai định dạng giờ Kết thúc PTTT", undefined, 3000);
				return false;
			}
		}
		//L2PT-6951
		if (req_mota && $("#txtMOTA").val() == "") {
			DlgUtil.showMsg("Bạn chưa nhập Mô tả", undefined, 3000);
			$("#txtMOTA").focus();
			return false;
		}
		//L2PT-26378
		if (isShowInfoTuongTrinh && opt.hospital_id == '10284' && $("#txtBATDAUTHUOCME").val() != null &&
				!compareDate($("#txtBATDAUTHUOCME").val().trim(), $("#txtDUTTHUOCME").val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
			DlgUtil.showMsg("Giờ dứt thuốc mê phải lớn hơn hoặc bằng Giờ bắt đầu thuốc mê");
			$('#txtDUTTHUOCME').focus();
			return false;
		}
		if (isShowInfoTuongTrinh && opt.hospital_id == '10284' && $("#txtBATDAUTHUOCME").val() != null &&
				!compareDate($("#txtBATDAUTHUOCME").val().trim(), $("#txtNGAYPHAUTHUATTHUTHUAT").val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
			DlgUtil.showMsg("PTTT lúc phải lớn hơn hoặc bằng Giờ bắt đầu thuốc mê");
			$('#txtBATDAUTHUOCME').focus();
			return false;
		}
		return true;
	}
	// luu thong tin duyet mo
	function saveOrUpdate() {
		$("#btnXoaCapnhat").bindOnce(
				"click",
				function() {
					if (_phauthuatthuthuatid == '') {
						return DlgUtil.showMsg("Chưa có Cập nhật PTTT", undefined, 3000);
					}
					DlgUtil.showConfirm("Bạn có muốn xóa cập nhật PTTT không?", function(flag) {
						if (flag) {
							var objData = new Object();
							FormUtil.setFormToObject("", "", objData);
							objData.TYPE_ACTION = 'DEL_PTTT';
							var _par;
							_par = [ JSON.stringify(objData), _phauthuatthuthuatid, opt.khambenhid, opt.benhnhanid, opt.hosobenhanid, opt.khoaid, opt.phongid, opt.maubenhphamid, opt.tiepnhanid,
									opt.dichvukhambenhid, _checkUpdate ];
							var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("PTTT.INS", _par.join('$'));
							if (_result == '1') {
								//xu ly su kien callback
								var evFunc = EventUtil.getEvent("assignSevice_savePTTT");
								if (typeof evFunc === 'function') {
									evFunc({
										msg : "Xóa Cập nhật PTTT thành công"
									});
								} else {
									console.log('evFunc not a function');
								}
							} else {
								DlgUtil.showMsg("Xóa Cập nhật PTTT Không thành công!", undefined, 3000);
							}
						}
					});
				}, 500)
		$("#btnLuu").bindOnce("click", function() {
			var valid = that.validator.validateForm();
			if (valid) {
				//validate rieng tung input
				var _check = validForm();
				if (!_check)
					return false;
				//START L2PT-17277
				if ($('#chkHENTRASAU').is(":checked") && $('#txtTG_HENTRASAU').val() == '') {
					return DlgUtil.showMsg("Chưa đặt thời gian hẹn trả sau!", undefined, 3000);
				}
				//START L2PT-19030
				if (opt.hospital_id == '957' && opt.callfrom != null && opt.callfrom == '1') {
					if ($('#txtPHUONGPHAPPTTT').val() == '') {
						return DlgUtil.showMsg("Chưa nhập Phương pháp PTTT!", undefined, 3000);
					}
					if ($('#txtCHANDOANTRUOCPHAUTHUAT').val() == '') {
						return DlgUtil.showMsg("Chưa nhập Chẩn đoán trước PTTT!", undefined, 3000);
					}
				}
				//DongNV BVTM-8009 Check đã thanh toán dịch vụ hoặc còn tạm ứng mới cho cập nhật
				var checkThanhToan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_CHANCAPNHAT_PTTT');
				if (checkThanhToan == '1' && opt.loaitiepnhan == '1' && opt.doituongbenhnhanid == '2') {
					//Với BN khám bệnh loại viện phí, check đủ tiền tạm ứng hoặc đã thanh toán dịch vụ
					var _par = [ opt.tiepnhanid, opt.dichvukhambenhid ];
					var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D026.NGOAITRU", _par.join('$'));
					if (_result == '1') {
						return DlgUtil.showMsg("Người bệnh không đủ tiền tạm ứng hoặc dịch vụ chưa được thanh toán. Đề nghị người bệnh đến quầy thu ngân để đóng tiền!");
					}
				}
				//DoanPV_20211209 BVTM-7680 check duyệt mổ mới cho cập nhật PTTT
				var _checkDuyetmo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_CHECK_DUYETMO');
				if (_checkDuyetmo != '0') {
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						value : opt.maubenhphamid
					});
					var _check = jsonrpc.AjaxJson.getOneValue('NTU.CHECK.DM', sql_par);
					if (_check == '0') {
						if (_checkDuyetmo == '1') {
							return DlgUtil.showMsg("Chưa duyệt mổ không thể cập nhật PTTT!", undefined, 3000);
						} else {
							result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D037.EV002", opt.khoaid);
							if (result_ct == '1') {
								return DlgUtil.showMsg("Chưa duyệt mổ không thể cập nhật PTTT!", undefined, 3000);
							}
						}
					}
				}
				//L2PT-47744
				//DoanPV L2PT-100974 check chưa hoàn thành ký
				var _checkKyDuyetmo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_CHECK_HOANTHANHKY_DUYETMO');
				if (_checkKyDuyetmo == '1') {
					var obj = new Object();
					obj.HOSOBENHANID = opt.hosobenhanid;
					obj.MAUBENHPHAMID = opt.maubenhphamid;
					var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I('NTU02D037.EV003', JSON.stringify(obj));
					if (_check == '1') {
						_msgCA = 'Phiếu duyệt mổ gắn với dịch vụ PTTT chưa thực hiện ký!';
					} else if (_check == '2') {
						_msgCA = 'Phiếu duyệt mổ gắn với dịch vụ PTTT chưa hoàn thành ký!';
					}
				}
				//L2PT-100974
				if (cfObj.NTU_PTTT_CHECK_USER == '1' || cfObj.NTU_PTTT_CHECK_USER == '2') {
					var objData = new Object();
					objData.NGAYPHAUTHUATTHUTHUAT = $('#txtNGAYPHAUTHUATTHUTHUAT').val();
					objData.KETTHUCPTTT = $('#txtKETTHUCPTTT').val();
					objData.KETTHUCPTTT_DKLCI = $('#txtKETTHUCPTTT_DKLCI').val();
					var list_nv = '(0';
					if ($('#cboPHAUTHUATVIEN').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHAUTHUATVIEN').val();
					if ($('#cboPHUMO1').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUMO1').val();
					if ($('#cboPHUMO2').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUMO2').val();
					if ($('#cboPHUMO3').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUMO3').val();
					if ($('#cboDUNGCUVIEN').val() != '-1')
						list_nv = list_nv + ',' + $('#cboDUNGCUVIEN').val();
					if ($('#cboBACSIGAYME').val() != '-1')
						list_nv = list_nv + ',' + $('#cboBACSIGAYME').val();
					if ($('#cboPHUME').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUME').val();
					if ($('#cboPHUME2').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUME2').val();
					if ($('#cboBACSITHUCHIEN').val() != '-1')
						list_nv = list_nv + ',' + $('#cboBACSITHUCHIEN').val();
					if ($('#cboDIEUDUONG').val() != '-1')
						list_nv = list_nv + ',' + $('#cboDIEUDUONG').val();
					list_nv = list_nv + ')';
					objData.LIST_NV = list_nv;
					objData.BENHNHANID = opt.benhnhanid;
					objData.PTTTID = _phauthuatthuthuatid;//L2PT-39083
					objData.MAUBENHPHAMID = opt.maubenhphamid; //L2PT-53246
					objData.PHAUTHUATVIEN = $('#cboPHAUTHUATVIEN').val();//L2PT-50933
					objData.MAMAY = $('#cboMAMAY').val();//L2PT-96539
					objData.DICHVUID = dichvuid; //L2PT-73660
					objData.DICHVUKHAMBENHID =opt.dichvukhambenhid //L2PT-105897
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("PTTT.CHECK.USER", JSON.stringify(objData));
					if (result && result != '1' && result != '-1') {
						// L2PT-128929 start
						$("#lblCHUY").html(FormUtil.unescape(result));
						DlgUtil.open("dlgChuY");
						$("#btn_OK").hide();
						if (cfObj.NTU_PTTT_CHECK_USER == '2') {
							return;
						}
						opt.dongForm = false;
						$("#btn_OK").show();
						//return DlgUtil.showMsg(result);
						/*DlgUtil.showConfirm('Bạn có muốn tiếp tục thực hiện?', function(flag) {
							if (flag) {
								luupttt();
							} else {
								return;
							}
						});*/
						// L2PT-128929 end
					} else
						luupttt();
				} else
					luupttt();
			}
		}, 500)
	}
	//L2PT-47744
	function luupttt() {
		var _result = save();
		if (_result == '1') {
			//L2PT-34023
			if (opt.hospital_id == '10284') {
				$('#btnThemDV').show();
			}
			opt.loaiptttid = $('#cboLOAIPTTTID').val();
			if (iskyso) {
				_caRpt('1');
			} else {
				if (_msgCA != '') {
					_msgCA = _msgCA + '</br>' + 'Cập nhật thông tin phẫu thuật thủ thuật thành công!';
					DlgUtil.showMsg(_msgCA, undefined, 3000);
				} else {
					DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật thành công!", undefined, 3000);
				}
			}
			initControl();
			//L2PT-12601
			var ch_taophieuvattu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_NOITRU_MOKETHUOC');
			if (opt.loaitiepnhan != null && opt.loaitiepnhan == '1' && ch_taophieuvattu == '1') {
				var _msg = "Tạo phiếu vật tư đi kèm hao phí";
				paramInput = {
					khambenhid : opt.khambenhid,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : opt.dichvukhambenhid,
					opt : '02D015', // tao phieu thuoc
					loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAD_KHO_KETHUOC_PTTT'),
					macdinh_hao_phi : 9
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1080, 550);
				DlgUtil.open("divDlgTaoPhieuThuoc02D015");
				EventUtil.setEvent("assignDrug_cancel", function(e) {
					DlgUtil.close("divDlgTaoPhieuThuoc02D015");
				});
			}
			// L2PT-129655 start
			var NTU_RELOAD_DSPHIEU_PTTT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_RELOAD_DSPHIEU_PTTT');
			if (NTU_RELOAD_DSPHIEU_PTTT == '1') {
				var evFunc = EventUtil.getEvent("assignSevice_reloadGridPTTT");
				if (typeof evFunc === 'function') {
					evFunc({
						msg : "Cập nhật phẫu thuật thủ thuật thành công",
						MAUBENHPHAMID : opt.maubenhphamid
					});
				} else {
					console.log('evFunc not a function');
				}
			}
			// L2PT-129655 end
		} else if (_result == '2' || _result == '3') { //L2PT-1434
			DlgUtil.showMsg("Thời gian PTTT phải lớn hơn thời gian chỉ định và nhỏ hơn hoặc bằng ngày hiên tại");
			initControl();
		}//L2PT-26328
		else if (_result == '4') {
			DlgUtil.showMsg("Thời gian kết thúc PTTT phải lớn hơn thời gian quy định");
		}//L2PT-34023
		else if (_result == '5') {
			DlgUtil.showMsg("Không thể cập nhật, Phòng hậu phẫu đã kết thúc điều trị kết hợp");
		} else if (_result == '6') { //L2PT-15233
			DlgUtil.showMsg("Thời gian Kết thúc PTTT phải nhỏ hơn thời gian ra viện");
			//initControl();
		} else if (_result == '7') {
			DlgUtil.showMsg("Chưa nhập PTV/TTV chính");
		} else if (_result == '8') { //L2PT-79328
			DlgUtil.showMsg("Loại PTTT khác với loại PTTT trong danh mục");
		} else {
			DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật không thành công!");
		}
		EventUtil.raiseEvent("refresh_gridCKDetail", {});
	}
	function saveAndClose() {
		$("#btnLuuDong").bindOnce("click", function() {
			var valid = that.validator.validateForm();
			if (valid) {
				//validate rieng tung input
				var _check = validForm();
				if (!_check)
					return false;
				//START L2PT-17277
				if ($('#chkHENTRASAU').is(":checked") && $('#txtTG_HENTRASAU').val() == '') {
					return DlgUtil.showMsg("Chưa đặt thời gian hẹn trả sau!", undefined, 3000);
				}
				//START L2PT-19030
				if (opt.hospital_id == '957' && opt.callfrom != null && opt.callfrom == '1') {
					if ($('#txtPHUONGPHAPPTTT').val() == '') {
						return DlgUtil.showMsg("Chưa nhập Phương pháp PTTT!", undefined, 3000);
					}
					if ($('#txtCHANDOANTRUOCPHAUTHUAT').val() == '') {
						return DlgUtil.showMsg("Chưa nhập Chẩn đoán trước PTTT!", undefined, 3000);
					}
				}
				//DongNV BVTM-8009 Check đã thanh toán dịch vụ hoặc còn tạm ứng mới cho cập nhật
				var checkThanhToan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_CHANCAPNHAT_PTTT');
				if (checkThanhToan == '1' && opt.loaitiepnhan == '1' && opt.doituongbenhnhanid == '2') {
					//Với BN khám bệnh loại viện phí, check đủ tiền tạm ứng hoặc đã thanh toán dịch vụ
					var _par = [ opt.tiepnhanid, opt.dichvukhambenhid ];
					var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D026.NGOAITRU", _par.join('$'));
					if (_result == '1') {
						return DlgUtil.showMsg("Người bệnh không đủ tiền tạm ứng hoặc dịch vụ chưa được thanh toán. Đề nghị người bệnh đến quầy thu ngân để đóng tiền!");
					}
				}
				//DoanPV_20211209 BVTM-7680 check duyệt mổ mới cho cập nhật PTTT
				var _checkDuyetmo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_CHECK_DUYETMO');
				if (_checkDuyetmo != '0') {
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						value : opt.maubenhphamid
					});
					var _check = jsonrpc.AjaxJson.getOneValue('NTU.CHECK.DM', sql_par);
					if (_check == '0') {
						if (_checkDuyetmo == '1') {
							return DlgUtil.showMsg("Chưa duyệt mổ không thể cập nhật PTTT!", undefined, 3000);
						} else {
							result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D037.EV002", opt.khoaid);
							if (result_ct == '1') {
								return DlgUtil.showMsg("Chưa duyệt mổ không thể cập nhật PTTT!", undefined, 3000);
							}
						}
					}
				}
				//DoanPV L2PT-100974 check chưa hoàn thành ký
				var _checkKyDuyetmo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_CHECK_HOANTHANHKY_DUYETMO');
				if (_checkKyDuyetmo == '1') {
					var obj = new Object();
					obj.HOSOBENHANID = opt.hosobenhanid;
					obj.MAUBENHPHAMID = opt.maubenhphamid;
					var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I('NTU02D037.EV003', JSON.stringify(obj));
					if (_check == '1') {
						_msgCA = 'Phiếu duyệt mổ gắn với dịch vụ PTTT chưa thực hiện ký!';
					} else if (_check == '2') {
						_msgCA = 'Phiếu duyệt mổ gắn với dịch vụ PTTT chưa hoàn thành ký!';
					}
				}
				//L2PT-100974
				//L2PT-47744
				if (cfObj.NTU_PTTT_CHECK_USER == '1' || cfObj.NTU_PTTT_CHECK_USER == '2') {
					var objData = new Object();
					objData.NGAYPHAUTHUATTHUTHUAT = $('#txtNGAYPHAUTHUATTHUTHUAT').val();
					objData.KETTHUCPTTT = $('#txtKETTHUCPTTT').val();
					objData.KETTHUCPTTT_DKLCI = $('#txtKETTHUCPTTT_DKLCI').val();//L2PT-74375
					var list_nv = '(0';
					if ($('#cboPHAUTHUATVIEN').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHAUTHUATVIEN').val();
					if ($('#cboPHUMO1').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUMO1').val();
					if ($('#cboPHUMO2').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUMO2').val();
					if ($('#cboPHUMO3').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUMO3').val();
					if ($('#cboDUNGCUVIEN').val() != '-1')
						list_nv = list_nv + ',' + $('#cboDUNGCUVIEN').val();
					if ($('#cboBACSIGAYME').val() != '-1')
						list_nv = list_nv + ',' + $('#cboBACSIGAYME').val();
					if ($('#cboPHUME').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUME').val();
					if ($('#cboPHUME2').val() != '-1')
						list_nv = list_nv + ',' + $('#cboPHUME2').val();
					if ($('#cboBACSITHUCHIEN').val() != '-1')
						list_nv = list_nv + ',' + $('#cboBACSITHUCHIEN').val();
					if ($('#cboDIEUDUONG').val() != '-1')
						list_nv = list_nv + ',' + $('#cboDIEUDUONG').val();
					list_nv = list_nv + ')';
					objData.LIST_NV = list_nv;
					objData.BENHNHANID = opt.benhnhanid;
					objData.PTTTID = _phauthuatthuthuatid;//L2PT-39083
					objData.PHAUTHUATVIEN = $('#cboPHAUTHUATVIEN').val();//L2PT-50933
					objData.MAMAY = $('#cboMAMAY').val();//L2PT-96539
					objData.MAUBENHPHAMID = opt.maubenhphamid; //L2PT-53246
					objData.DICHVUID = dichvuid; //L2PT-73660
					objData.DICHVUKHAMBENHID =opt.dichvukhambenhid //L2PT-105897
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("PTTT.CHECK.USER", JSON.stringify(objData));
					if (result && result != '1' && result != '-1') {
						// L2PT-128929 start
						$("#lblCHUY").html(FormUtil.unescape(result));
						DlgUtil.open("dlgChuY");
						$("#btn_OK").hide();
						if (cfObj.NTU_PTTT_CHECK_USER == '2') {
							return;
						}
						opt.dongForm = true;
						$("#btn_OK").show();
						//return DlgUtil.showMsg(result);
						/*DlgUtil.showConfirm('Bạn có muốn tiếp tục thực hiện?', function(flag) {
							if (flag) {
								luupttt_close();
							} else {
								return;
							}
						});*/
						// L2PT-128929 end
					} else
						luupttt_close();
				} else
					luupttt_close();
			}
		}, 500)
		$("#btnLuuDongIn").click(function() {
			luudongin = true;
			$("#btnLuuDong").click();
		});
	}
	//L2PT-47744
	function luupttt_close() {
		var _result = save();
		if (_result == '1') {
			//L2PT-12601
			var ch_taophieuvattu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_NOITRU_MOKETHUOC');
			paramInput = {
				khambenhid : opt.khambenhid,
				maubenhphamid : "",
				loaikedon : 1,
				dichvuchaid : opt.dichvukhambenhid,
				opt : '02D015', // tao phieu thuoc
				loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAD_KHO_KETHUOC_PTTT'),
				macdinh_hao_phi : 9,
				msg : "Cập nhật phẫu thuật thủ thuật thành công"
			};
			//L2PT-9316
			if (luudong_print) {
				print();
			}
			//L2PT-9316
			if (luudongin) {
				print();
			}
			//xu ly su kien callback
			var evFunc = EventUtil.getEvent("assignSevice_savePTTT");
			if (typeof evFunc === 'function') {
				if (opt.loaitiepnhan != null && opt.loaitiepnhan == '1' && ch_taophieuvattu == '1') {
					evFunc({
						msg : paramInput
					});
				} else {
					// L2PT-129655 start
					evFunc({
						msg : "Cập nhật phẫu thuật thủ thuật thành công",
						MAUBENHPHAMID : opt.maubenhphamid
					});
					// L2PT-129655 end
				}
			} else {
				console.log('evFunc not a function');
			}
		} else if (_result == '2' || _result == '3') { //L2PT-1434
			DlgUtil.showMsg("Thời gian PTTT phải lớn hơn thời gian chỉ định và nhỏ hơn hoặc bằng ngày hiên tại");
		}
		//L2PT-26328
		else if (_result == '4') {
			DlgUtil.showMsg("Thời gian kết thúc PTTT phải lớn hơn thời gian quy định");
		}
		//L2PT-34023
		else if (_result == '5') {
			DlgUtil.showMsg("Không thể cập nhật, Phòng hậu phẫu đã kết thúc điều trị kết hợp");
		} else if (_result == '7') {
			DlgUtil.showMsg("Chưa nhập PTV/TTV chính");
		} else if (_result == '8') { //L2PT-79328
			DlgUtil.showMsg("Loại PTTT khác với loại PTTT trong danh mục");
		}
		EventUtil.raiseEvent("refresh_gridCKDetail", {});
	}
	//START L2PT-6521
	function save() {
		var txtTuNgay = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
		//L2PT-27254
		if (kt_pttt_dklci) {
			var txtDenNgay = $("#txtKETTHUCPTTT_DKLCI").val();
		} else {
			var txtDenNgay = $("#txtKETTHUCPTTT").val();
		}
		//L1PT-1562
		var data_arr_Cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_LDTT_PTTT_BND;NTU_NHAP_CDC");
		var objData = new Object();
		var _par;
		FormUtil.setFormToObject("", "", objData);
		if (data_arr_Cauhinh != null && data_arr_Cauhinh.length > 0) {
			if (data_arr_Cauhinh[0].NTU_LDTT_PTTT_BND == '1') {
				objData.TRINHTUPTTT = $("#txtTRINHTUPTTT_BND").val();
			}
		}
		//START L2PT-19388
		objData.SL_NHANLUCCHINH = sl_nhanlucchinh == null ? '' : sl_nhanlucchinh;
		objData.SL_NHANLUCPHU = sl_nhanlucphu == null ? '' : sl_nhanlucphu;
		objData.SL_GIUPVIEC = sl_giupviec == null ? '' : sl_giupviec;
		//L2PT-27254
		if (kt_pttt_dklci) {
			objData.KETTHUCPTTT = $("#txtKETTHUCPTTT_DKLCI").val();
		}
		//L2PT-6207
		if (layhinhanh_mau) {
			objData.PTTT_TMP_ID = $("#cboMAUPTTT").val();
		}
		//L2PT-51382
		if (cfObj.NTU_PTTT_TT_QLP == '1' && opt.fromtt == '1')
			objData.FROMTT = '1';
		else
			objData.FROMTT = '0';
		//L2PT-56061
		if (cfObj.NTU_PTTT_CDHA_XN == '1' && opt.callfrom == '1')
			objData.FROMTT = '1';
		if (opt.fromtt == '1') {//HaNv_291123: L2PT-60600
			objData.FORM_CNTT = '1';
		} else {
			objData.FORM_CNTT = '0';
		}
		//L2PT-14930
		if (chonnhieu_ppvc == '1') {
			var str = '';
			var str1 = '';
			$('#cboPTTT_PHUONGPHAPVOCAMID_SUMO option:selected').each(function(i) {
				str = str + ',' + $(this).val();
			});
			objData["PTTT_PHUONGPHAPVOCAMID"] = str;
		}
		objData.MOTA = objData.MOTA.replaceAll('$', ',,,,,'); //L2PT-92872
		_par = [ JSON.stringify(objData), _phauthuatthuthuatid, opt.khambenhid, opt.benhnhanid, opt.hosobenhanid, opt.khoaid, opt.phongid, opt.maubenhphamid, opt.tiepnhanid, opt.dichvukhambenhid,
				_checkUpdate ];
		var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("PTTT.INS", _par.join('$'));
		return _result;
	}
	//START L2PT-6521
	function saveAndPrint() {
		$("#btnLuuIn_BND").on("click", function(e) {
			var valid = that.validator.validateForm();
			if (valid) {
				//validate rieng tung input
				var _check = validForm();
				if (!_check)
					return false;
				//START L2PT-17277
				if ($('#chkHENTRASAU').is(":checked") && $('#txtTG_HENTRASAU').val() == '') {
					return DlgUtil.showMsg("Chưa đặt thời gian hẹn trả sau!", undefined, 3000);
				}
				//START L2PT-19030
				if (opt.hospital_id == '957' && opt.callfrom != null && opt.callfrom == '1') {
					if ($('#txtPHUONGPHAPPTTT').val() == '') {
						return DlgUtil.showMsg("Chưa nhập Phương pháp PTTT!", undefined, 3000);
					}
					if ($('#txtCHANDOANTRUOCPHAUTHUAT').val() == '') {
						return DlgUtil.showMsg("Chưa nhập Chẩn đoán trước PTTT!", undefined, 3000);
					}
				}
				var _result = save();
				if (_result == '1') {
					DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật thành công!", undefined, 3000);
					initControl();
					print();
					//L2PT-12601
					var ch_taophieuvattu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_NOITRU_MOKETHUOC');
					if (opt.loaitiepnhan != null && opt.loaitiepnhan == '1' && ch_taophieuvattu == '1') {
						var _msg = "Tạo phiếu vật tư đi kèm hao phí";
						paramInput = {
							khambenhid : opt.khambenhid,
							maubenhphamid : "",
							loaikedon : 1,
							dichvuchaid : opt.dichvukhambenhid,
							opt : '02D015', // tao phieu thuoc
							loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAD_KHO_KETHUOC_PTTT'),
							macdinh_hao_phi : 9
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1080, 550);
						DlgUtil.open("divDlgTaoPhieuThuoc02D015");
						EventUtil.setEvent("assignDrug_cancel", function(e) {
							DlgUtil.close("divDlgTaoPhieuThuoc02D015");
						});
					}
				} else if (_result == '2' || _result == '3') { //L2PT-1434			
					DlgUtil.showMsg("Thời gian PTTT phải lớn hơn thời gian chỉ định và nhỏ hơn hoặc bằng ngày hiên tại");
					initControl();
				} else if (_result == '6') { //L2PT-15233		
					DlgUtil.showMsg("Thời gian Kết thúc PTTT phải nhỏ hơn thời gian ra viện");
					//initControl();
				} else if (_result == '8') { //L2PT-79328
					DlgUtil.showMsg("Loại PTTT khác với loại PTTT trong danh mục");
					//initControl();
				} else {
					DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật không thành công!");
				}
			}
		})
	}
	//END L2PT-6521
	// ham xu ly close trang
	function closePage() {
		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close("dlgPTTT");
		})
	}
	//START -- HISL2TK-691 --hongdq
	function loadCboMau() {
		var sql_par = []
		sql_par.push({
			"name" : "[0]",
			"value" : 1
		}, {
			"name" : "[1]",
			"value" : opt.user_id
		}, {
			"name" : "[2]",
			"value" : opt.khoaid
		});
		ComboUtil.getComboTag("cboMAULUOCDOPTTT", "NTU02D037.DSMAU", sql_par, "", {
			extval : true,
			value : '',
			text : '--Chọn mẫu--'
		}, "sql");
		var sql_par = []
		sql_par.push({
			"name" : "[0]",
			"value" : 2
		}, {
			"name" : "[1]",
			"value" : opt.user_id
		}, {
			"name" : "[2]",
			"value" : opt.khoaid
		});
		ComboUtil.getComboTag("cboMAUTRINHTUPTTT", "NTU02D037.DSMAU", sql_par, "", {
			extval : true,
			value : '',
			text : '--Chọn mẫu--'
		}, "sql");
	}
	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
		}
	}
	//END -- HISL2TK-691 --hongdq
	function setValueToCbo(element1, element2, id, name) {
		$("#" + element1 + "").val("");
		var option = $('<option value="' + id + '">' + name + '</option>');
		$("#" + element2 + "").empty();
		$("#" + element2 + "").append(option);
	}
	function loadCombomau() {
		//START L2PT-20993
		if (mau_pttt_theophongcd) {
			sql_par = [ {
				"name" : "[0]",
				"value" : opt.phongid
			} ];
			//L2PT-47167
			if (cfObj.MAU_PTTT_TBH == '1') {
				sql_par = [ {
					"name" : "[0]",
					"value" : opt.khoaid
				} ];
				var _colTEN_MAU = "Tên mẫu,TEN_MAU,30,0,f,l";
				ComboUtil.initComboGrid("txtTKTEN_MAU", "NTU02D037.MAUTBH", sql_par, "300px", _colTEN_MAU, function(event, ui) {
					var idMau = ui.item.ID;
					var option = $('<option value="' + ui.item.PHAUTHUATTHUTHUAT_TMP_ID + '">' + ui.item.TEN_MAU + '</option>');
					$("#cboMAUPTTT").empty();
					$("#cboMAUPTTT").append(option);
					$("#txtTKTEN_MAU").val(ui.item.TEN_MAU);
					loadMauPttt(ui.item.PHAUTHUATTHUTHUAT_TMP_ID);
				});
			} else
				ComboUtil.getComboTag("cboMAUPTTT", "NTU02D037.DSMAU_PCD", sql_par, "", {
					extval : true,
					value : '-1',
					text : '--Chọn mẫu pttt--'
				}, "sql");
		} else {
			if (cfObj.MAU_PTTT_THEO_NGUOITAO == '1') {
				sql_par = [ {
					"name" : "[0]",
					"value" : opt.user_id
				} ];
				ComboUtil.getComboTag("cboMAUPTTT", "NTU02D037.DSMAUUSER", sql_par, "", {
					extval : true,
					value : '-1',
					text : '--Chọn mẫu pttt--'
				}, "sql");
			} else {
				sql_par = [];
				ComboUtil.getComboTag("cboMAUPTTT", "NTU02D037.DSMAU_PT", sql_par, "", {
					extval : true,
					value : '-1',
					text : '--Chọn mẫu pttt--'
				}, "sql");
			}
		}
		var sql_par_mau = [ {
			"name" : "[0]",
			"value" : opt.tiepnhanid
		}, {
			"name" : "[1]",
			"value" : dichvuid
		} ];
		ComboUtil.getComboTag("cboKETQUA_OLD", "NTU02D037.PTTTOLD", sql_par_mau, "", {
			extval : true,
			value : '-1',
			text : '--Kết quả cũ--'
		}, "sql");
	}
	//L2PT-47167
	function loadMauPttt(mauid) {
		var mau_dhytb;//L2PT-1437
		var mau_nhihdg;//L2PT-7690
		sql_par = [];
		sql_par = [ mauid ];
		var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.MAUCT", sql_par.join('$'));
		if (data != null && data.length > 0) {
			//L2PT-16204
			var data_arr_Cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_LDTT_PTTT_BND;NTU_NHAP_CDC;" + "NTU_PTTT_BND_MAUCT;NTU_PTTT_MAU_DHYTB;NTU_PTTT_MAU_NHIHDG");
			if (data_arr_Cauhinh != null && data_arr_Cauhinh.length > 0) {
				if (data_arr_Cauhinh[0].NTU_LDTT_PTTT_BND == '1') {
					$("#txtTRINHTUPTTT_BND").val(data[0].TRINHTUPTTT);
				}
				//L2PT-21108
				if (data_arr_Cauhinh[0].NTU_PTTT_BND_MAUCT != '0') {
					var lstItem = data_arr_Cauhinh[0].NTU_PTTT_BND_MAUCT.split(',');
					if (lstItem.length > 0) {
						for (var i = 0; i < lstItem.length; i++) {
							delete data[0][lstItem[i]];
						}
					}
					//delete data[0].CHANDOANVAOKHOA;
					//delete data[0].MA_CHANDOANVAOKHOA;
				}
				//L2PT-1437
				if (data_arr_Cauhinh[0].NTU_PTTT_MAU_DHYTB == '1') {
					mau_dhytb = true
				}
				// L2PT-7690 start
				if (data_arr_Cauhinh[0].NTU_PTTT_MAU_NHIHDG == '1') {
					mau_nhihdg = true
				}
				// L2PT-7690 end
			}
			//L2PT-1437
			if (mau_dhytb) {
				$("#txtMOTA").val(data[0].MOTA);
			} else if (mau_nhihdg) { //L2PT-7690
				$("#txtMOTA").val(data[0].MOTA);
				$("#txtTRINHTUPTTT").val(data[0].TRINHTUPTTT);
			} else {
				FormUtil.setObjectToForm("divMain", "", data[0]);
				//START L2PT-7767
				var dtSys = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
				$("#txtNGAYPHAUTHUATTHUTHUAT").val(dtSys);
			}
		}
		//L2PT-6207
		$('#chkHINHANH_MAU').prop('checked', false);
	}
	//L2PT-30152
	function kiemtra_trunggio_pttt(user_id, element, type) {
		//L2PT-39499
		//L2PT-44313 check neu cau hinh = 1 va check madichvu ngoai le
		//L2PT-110219
		if ((cfObj.NTU_PTTT_TRUNGGIO_HUE == '1' || cfObj.NTU_PTTT_TRUNGGIO_HUE == '2' || cfObj.NTU_PTTT_TRUNGGIO_HUE == '3'
			|| cfObj.NTU_PTTT_TRUNGGIO_HUE == '4') && (cfObj.NTU_PTTT_TRUNGGIO_HUE_EX == '0' || cfObj.NTU_PTTT_TRUNGGIO_HUE_EX.indexOf(_madichvu) == '-1')) {
			var objData = new Object();
			//L2PT-79365
			if (!compareDate($("#txtNGAYPHAUTHUATTHUTHUAT").val(), $("#txtKETTHUCPTTT_DKLCI").val(), "DD/MM/YYYY HH:mm:ss")) {
				return false;
			}
			var _par;
			objData.USERID = user_id;
			objData.NGAYPHAUTHUATTHUTHUAT = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
			objData.KETTHUCPTTT = $("#txtKETTHUCPTTT_DKLCI").val();
			objData.TYPE = type;
			objData.PHAUTHUATTHUTHUATID = _phauthuatthuthuatid;
			objData.KHAMBENHID = opt.khambenhid;
			_par = [ JSON.stringify(objData) ];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('D037.CHECK_TGTH', _par.join('$'));
			if (data_ar != null && data_ar.length > 0) {
				
				var msg = 'NVYT: ' + $("#" + element + " option:selected").text()+ ', Đã trùng giờ làm PTTT, dịch vụ ' + data_ar[0].TENDICHVU + ',<br>Số phiếu ' + data_ar[0].SOPHIEU + ', mã bệnh án ' + data_ar[0].MAHOSOBENHAN
				+ ', tên bệnh nhân ' + data_ar[0].TENBENHNHAN + ', <br> PTTT lúc ' +
						data_ar[0].NGAYPHAUTHUATTHUTHUAT + ', Kết thúc PTTT ' + data_ar[0].KETTHUCPTTT + '';
				//L2PT-110219 L2PT-123034
				if(cfObj.NTU_PTTT_TRUNGGIO_HUE == '3' || cfObj.NTU_PTTT_TRUNGGIO_HUE == '4'){
					msg = '<font color=\"red\">NVYT: ' + $("#" + element + " option:selected").text()+ ' </font>'  + ', Đã trùng giờ làm PTTT, dịch vụ ' + data_ar[0].TENDICHVU + ',<br>Số phiếu ' + data_ar[0].SOPHIEU + ', mã bệnh án ' + data_ar[0].MAHOSOBENHAN
					+ ', tên bệnh nhân ' + data_ar[0].TENBENHNHAN + ', <br> PTTT lúc ' +
							data_ar[0].NGAYPHAUTHUATTHUTHUAT + ', Kết thúc PTTT ' + data_ar[0].KETTHUCPTTT + '';
					var data_ar_ds = jsonrpc.AjaxJson.ajaxCALL_SP_O('D037.CHECK.TG', _par.join('$'));
					if (data_ar_ds != null && data_ar_ds.length > 0) {
						msg = msg +'<br>Danh sách PTTT PTV đã thực hiện trong ngày ' + data_ar_ds[0].NGAYPHAUTHUATTHUTHUAT.substr(0, 10);
						for(var i = 0; i < data_ar_ds.length; i ++){
							 msg = msg +'<br>' + data_ar_ds[i].SOPHIEU + ' - ' + data_ar_ds[i].TENBENHNHAN + ' - Lúc ' +
							 '<font color=\"red\">' + data_ar_ds[i].NGAYPHAUTHUATTHUTHUAT.substr(11, 8) + ' - ' + data_ar_ds[i].KETTHUCPTTT.substr(11, 8) + ' </font>';
						}
					}
					$("#lblCHUY").html(msg);
					DlgUtil.open("dlgChuY");
					$("#btn_OK").hide();// L2PT-128929
				}else
					DlgUtil.showMsg(msg);
				//L2PT-69324 L2PT-110219
				if (cfObj.NTU_PTTT_TRUNGGIO_HUE == '1' || cfObj.NTU_PTTT_TRUNGGIO_HUE == '3') {
					var option = $('<option value="-1">--Lựa chọn--</option>');
					$("#" + element + "").empty();
					$("#" + element + "").append(option);
				}
				return false;
			} else {
				return true;
			}
		} else if (cfObj.NTU_PTTT_DKLCI_TRUNGGIO == '1') {
			if (user_id) {
				var objData = new Object();
				var _par;
				objData.USERID = user_id;
				objData.NGAYPHAUTHUATTHUTHUAT = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
				objData.KETTHUCPTTT = $("#txtKETTHUCPTTT_DKLCI").val();
				objData.TYPE = type;
				objData.PHAUTHUATTHUTHUATID = _phauthuatthuthuatid;
				_par = [ JSON.stringify(objData) ];
				//L2PT-66641
//				var checkTrunggio=jsonrpc.AjaxJson.ajaxCALL_SP_I("D037.CHECK_TIME",_par.join('$'));
//				//var checkTrunggio = jsonrpc.AjaxJson.getOneValue("D037.CHECK_TIME", par);
//				if(checkTrunggio >= 1){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('D037.CHECKTIME.LC', _par.join('$'));
				if (data_ar != null && data_ar.length > 0) {
					var msg = 'Đã trùng giờ làm PTTT, dịch vụ ' + data_ar[0].TENDICHVU + ',<br>Số phiếu ' + data_ar[0].SOPHIEU + ', mã bệnh án ' + data_ar[0].MAHOSOBENHAN + ',tên BN ' +
							data_ar[0].TENBENHNHAN + ',người thực hiện ' + $("#" + element + " option:selected").text();
					DlgUtil.showConfirm(msg + ", Bạn có muốn thực hiện tiếp không?", function(flag) {
						if (flag) {
							return true;
						} else {
							$("#" + element + "").empty();
							//DlgUtil.showMsg("Đã trùng giờ làm PTTT",undefined,3000);	
							$("#" + element + "").focus();
							return false;
						}
					});
				} else {
					return true;
				}
			}
		}
		//Beg_HaNv_120623: Check trùng TG cho NAN - L2PT-43982
		else if (cfObj.NTU_PTTT_CHECK_TRUNGTIME == '1') {
			if (user_id) {
				var objData = new Object();
				var _par;
				objData.USERID = user_id;
				objData.NGAYPHAUTHUATTHUTHUAT = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
				objData.KETTHUCPTTT = $("#txtKETTHUCPTTT_DKLCI").val();
				objData.TYPE = type;
				objData.PHAUTHUATTHUTHUATID = _phauthuatthuthuatid;
				objData.KHAMBENHID = opt.khambenhid;
				_par = [ JSON.stringify(objData) ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("D037.CHK_TIME", _par.join('$'));
				if (data_ar != null && data_ar.length > 0) { //L2PT-63913
					var msg = 'Đã trùng giờ làm PTTT, dịch vụ ' + data_ar[0].TENDICHVU + ',<br>Số phiếu ' + data_ar[0].SOPHIEU + ', mã bệnh án ' + data_ar[0].MAHOSOBENHAN + ', tên BN ' +
							data_ar[0].TENBENHNHAN + ',<br>Khoa ' + data_ar[0].KHOA + ', phòng ' + data_ar[0].PHONG + ', BS thực hiện ' + data_ar[0].BSTH + ', <br> PTTT lúc ' +
							data_ar[0].NGAYPHAUTHUATTHUTHUAT + ', Kết thúc PTTT ' + data_ar[0].KETTHUCPTTT + '';
					DlgUtil.showMsg(msg);
					var option = $('<option value="-1">--Lựa chọn--</option>');
					$("#" + element + "").empty();
					$("#" + element + "").append(option);
					return false;
				} else {
					return true;
				}
			}
		}
		//End_HaNv_120623
	}
	//L2PT-34023
	function boSungDichVu() {
		var paramInput = {
			benhnhanid : opt.benhnhanid,
			mabenhnhan : opt.mabenhnhan,
			khambenhid : opt.khambenhid,
			tiepnhanid : opt.tiepnhanid,
			hosobenhanid : opt.hosobenhanid,
			doituongbenhnhanid : opt.doituongbenhnhanid,
			loaitiepnhanid : opt.loaitiepnhan,
			subDeptId : opt.phongid,
			deptId : opt.dept_id,
			mbpid_bosungdv : opt.maubenhphamid,
			loaiPhieu : 5
		};
		dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, paramInput, "Tạo phiếu chỉ định dịch vụ", 1300, 600);
		DlgUtil.open("divDlgDichVu");
	}
	// Xử lý sự kiện liên quan ký CA => START
	function saveAndCa() {
		$("#btnKyCa").on("click", function(e) {
			iskyso = true;
			$("#btnLuu").click();
		})
	};
	function huyCa() {
		$("#btnHuyCa").on("click", function(e) {
			_caRpt('2');
		})
	};
	function exportKyCA() {
		$("#btnInCa").on("click", function(e) {
			_caRpt('0');
		})
	};
	function _caRpt(_signType) {
		var _reportCode = '';
		if (opt.hospital_id == '965') {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : opt.hosobenhanid
			}, {
				name : 'ptttid',
				type : 'String',
				value : _phauthuatthuthuatid
			} ];
			_reportCode = 'PHIEU_TUONGTRINHPHAUTHUAT_A4';
		} else {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : opt.hosobenhanid
			}, {
				name : 'dichvukhambenhid',
				type : 'String',
				value : opt.dichvukhambenhid
			}, {
				name : 'benhnhanid',
				type : 'String',
				value : opt.benhnhanid
			} ];
			//L2PT-22996
			if (opt.hospital_id == '913' && (madichvu_caychi == '08.0007.0227' || mdv_in_caychi_ydgli.indexOf(madichvu_caychi) != -1)) {
				_reportCode = 'PHIEU_KETQUA_CAYCHI_913';
			} else {
				_reportCode = 'NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4';
			}
		}
		par.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _reportCode
		});
		if (_signType != '0') {
			//L2PT-59286
			CommonUtil.kyCA(par, _signType, true, true);
			EventUtil.setEvent("eventKyCA", function(e) {
				//DlgUtil.showMsg(e.res);
				_checkBtnKySo();//L2PT-29044
				var _code = e.res.split("|")[0];
				var _msg = e.res.split("|")[1];
				DlgUtil.showMsg(_msg);
				if (_code == '0') {
					var obj = new Object();
					obj.TABLENAME = 'kbh_phauthuatthuthuat';
					obj.COLUMNAME = 'phauthuatthuthuatid';
					obj.COLUMDATA = _phauthuatthuthuatid;
					obj.SINGTYPE = _signType;
					jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(obj));
				}
			});
		} else {
			CommonUtil.openReportGetCA2(par, false);
		}
	}
	// Xử lý sự kiện liên quan ký CA => END
	//BVTM-3345
	function IsNumeric(input, keyCode) {
		if (keyCode == 16) {
			isShift = true;
		}
		//Allow only Numeric Keys.
		if (((keyCode >= 48 && keyCode <= 57) || keyCode == 8 || keyCode <= 37 || keyCode <= 39 || (keyCode >= 96 && keyCode <= 105)) && isShift == false) {
			if (input.value.length == 10 && keyCode != 8) {
				input.value += ' ';
			}
			if ((input.value.length == 2 || input.value.length == 5) && keyCode != 8) {
				input.value += '/';
			}
			if ((input.value.length == 13 || input.value.length == 16) && keyCode != 8) {
				input.value += ':';
			}
			if ((input.value.length == 15) && keyCode != 8) {
				if (keyCode >= 96 && keyCode <= 105) {
					input.value += String.fromCharCode(keyCode - 48) + ':00';
				} else {
					input.value += String.fromCharCode(keyCode) + ':00';
				}
			}
			return true;
		} else {
			return false;
		}
	};
	function ValidateDateFormat(input, keyCode) {
		var dateString = input.value;
		if (keyCode == 16) {
			isShift = false;
		}
		/*var regex = /([0-3][0-9])\/([0-1][0-9])\/([0-2][0-9]{3}) ([0-5][0-9]):([0-5][0-9]):([0-5][0-9])(([\-\+]([0-1][0-9])\:00))?/;
		
		//Check whether valid dd/MM/yyyy Date Format.
		if (regex.test(dateString) || dateString.length == 0) {
			$(input).css("color", "black");
		    //ShowHideError(input, "none");
		} else {
			$(input).css("color", "Red");
		    //ShowHideError(input, "block");
		}*/
	};
	function ShowHideError(textbox, display) {
		var row = textbox.parentNode.parentNode;
		var errorMsg = row.getElementsByTagName("span")[0];
		if (errorMsg != null) {
			errorMsg.style.display = display;
		}
	};
	//BVTM-3454
	function loadComboVitriThuchien() {
		//load doi ngu y, bac sy phau thuat vien
		var sql_par = [];
		//phau thuat vien
		//Begin_HaNv_23102018: Hiển thị tất cả nhân viên ở ô Phẫu thuật viên 1 - L2HOTRO-11688
		if (isPtvShowAll) {
			//BVTM-3434
			if (opt.hospital_id == '10284') {
				sql_par.push({
					"name" : "[0]",
					"value" : 0
				}, {
					"name" : "[1]",
					"value" : loadEkip_checkbox == true ? opt.dept_id : '-1'
				});
			} else {
				sql_par.push({
					"name" : "[0]",
					"value" : 0
				}, {
					"name" : "[1]",
					"value" : $("#cboKHOAHIENTHI").val() != null ? $("#cboKHOAHIENTHI").val() : '-1'
				});
			}
		} else {
			//BVTM-3434
			if (opt.hospital_id == '10284') {
				sql_par.push({
					"name" : "[0]",
					"value" : 1
				}, {
					"name" : "[1]",
					"value" : loadEkip_checkbox == true ? opt.dept_id : '-1'
				});
			} else {
				sql_par.push({
					"name" : "[0]",
					"value" : 1
				}, {
					"name" : "[1]",
					"value" : $("#cboKHOAHIENTHI").val() != null ? $("#cboKHOAHIENTHI").val() : opt.dept_id
				});
			}
		}
		//End_HaNv_23102018
		ComboUtil.initComboGrid("txtTKPHAUTHUATVIEN", sql_load_user.TKPHAUTHUATVIEN, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHAUTHUATVIEN").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHAUTHUATVIEN").empty();
			$("#cboPHAUTHUATVIEN").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHAUTHUATVIEN", '1');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHAUTHUATVIEN2", sql_load_user.TKPHAUTHUATVIEN2, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHAUTHUATVIEN2").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHAUTHUATVIEN2").empty();
			$("#cboPHAUTHUATVIEN2").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHAUTHUATVIEN2", '2');
			return false;
		});
		//dung cu vien
		sql_par = [];
		if (opt.hospital_id == '10284') {
			sql_par.push({
				"name" : "[0]",
				"value" : 0
			}, {
				"name" : "[1]",
				"value" : loadEkip_checkbox == true ? opt.dept_id : '-1'
			});
		} else {
			if (isPtvShowAll) {
				sql_par.push({
					"name" : "[0]",
					"value" : 0
				}, {
					"name" : "[1]",
					"value" : $("#cboKHOAHIENTHI").val() != null ? $("#cboKHOAHIENTHI").val() : '-1'
				});
			} else {
				sql_par.push({
					"name" : "[0]",
					"value" : 0
				}, {
					"name" : "[1]",
					"value" : $("#cboKHOAHIENTHI").val() != null ? $("#cboKHOAHIENTHI").val() : opt.dept_id
				});
			}
		}
		ComboUtil.initComboGrid("txtTKDUNGCUVIEN", sql_load_user.TKDUNGCUVIEN, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKDUNGCUVIEN").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboDUNGCUVIEN").empty();
			$("#cboDUNGCUVIEN").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboDUNGCUVIEN", '3');
			return false;
		});
		//bac sy gay me		
		ComboUtil.initComboGrid("txtTKBACSIGAYME", sql_load_user.TKBACSIGAYME, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKBACSIGAYME").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboBACSIGAYME").empty();
			$("#cboBACSIGAYME").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboBACSIGAYME", '4');
			return false;
		});
		//bac sy phu me
		ComboUtil.initComboGrid("txtTKPHUME", sql_load_user.TKPHUME, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUME").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME").empty();
			$("#cboPHUME").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUME", '5');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME2", sql_load_user.TKPHUME2, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUME2").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME2").empty();
			$("#cboPHUME2").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUME2", '6');
			return false;
		});
		//dieu duong vien
		ComboUtil.initComboGrid("txtTKDIEUDUONG", sql_load_user.TKDIEUDUONG, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKDIEUDUONG").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboDIEUDUONG").empty();
			$("#cboDIEUDUONG").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboDIEUDUONG", '7');
			return false;
		});
		//L2PT-14424
		//giup viec 2
		ComboUtil.initComboGrid("txtTKDIEUDUONG2", sql_load_user.TKDIEUDUONG, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKDIEUDUONG2").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboDIEUDUONG2").empty();
			$("#cboDIEUDUONG2").append(option);
			//L2PT-30152
			//kiemtra_trunggio_pttt(ui.item.USER_ID,"cboDIEUDUONG2",'7');
			return false;
		});
		//PHỤ Mổ
		ComboUtil.initComboGrid("txtTKPHUMO1", sql_load_user.TKPHUMO1, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUMO1").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUMO1").empty();
			$("#cboPHUMO1").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUMO1", '8');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO2", sql_load_user.TKPHUMO2, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUMO2").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUMO2").empty();
			$("#cboPHUMO2").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUMO2", '9');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO3", sql_load_user.TKPHUMO3, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUMO3").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUMO3").empty();
			$("#cboPHUMO3").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUMO3", '10');
			return false;
		});
		ComboUtil.initComboGrid("txtTKBACSITHUCHIEN", sql_load_user.TKBACSITHUCHIEN, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKBACSITHUCHIEN").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboBACSITHUCHIEN").empty();
			$("#cboBACSITHUCHIEN").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboBACSITHUCHIEN", '11');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO4", sql_load_user.TKPHUMO4, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUMO4").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUMO4").empty();
			$("#cboPHUMO4").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUMO4", '12');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO5", sql_load_user.TKPHUMO5, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUMO5").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUMO5").empty();
			$("#cboPHUMO5").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUMO5", '13');
			return false;
		});
		//End HaNv_20170731
		//End_HaNv_19062018
		//START L1PT-680
		ComboUtil.initComboGrid("txtTKCHAYMAYCHINH", sql_load_user.TKCHAYMAYCHINH, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKCHAYMAYCHINH").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboCHAYMAYCHINH").empty();
			$("#cboCHAYMAYCHINH").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboCHAYMAYCHINH", '14');
			return false;
		});
		ComboUtil.initComboGrid("txtTKCHAYMAYPHU", sql_load_user.TKCHAYMAYPHU, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKCHAYMAYPHU").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboCHAYMAYPHU").empty();
			$("#cboCHAYMAYPHU").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboCHAYMAYPHU", '15');
			return false;
		});
		//END L1PT-680
		//L2PT-32060
		ComboUtil.initComboGrid("txtVC_CHINH_ID", sql_load_user.VC_CHINH_ID, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtVC_CHINH_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboVC_CHINH").empty();
			$("#cboVC_CHINH").append(option);
			//L2PT-30152
			//kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
			return false;
		});
		ComboUtil.initComboGrid("txtVC_PHU_ID", sql_load_user.VC_PHU_ID, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtVC_PHU_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboVC_PHU").empty();
			$("#cboVC_PHU").append(option);
			//L2PT-30152
			//kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
			return false;
		});
		ComboUtil.initComboGrid("txtXEP_LICH_ID", sql_load_user.XEP_LICH_ID, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtXEP_LICH_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboXEP_LICH").empty();
			$("#cboXEP_LICH").append(option);
			//L2PT-30152
			//kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
			return false;
		});
		ComboUtil.initComboGrid("txtHUU_TRUNG_ID", sql_load_user.HUU_TRUNG_ID, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtHUU_TRUNG_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboHUU_TRUNG").empty();
			$("#cboHUU_TRUNG").append(option);
			//L2PT-30152
			//kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
			return false;
		});
		ComboUtil.initComboGrid("txtVO_TRUNG_ID", sql_load_user.VO_TRUNG_ID, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtVO_TRUNG_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboVO_TRUNG").empty();
			$("#cboVO_TRUNG").append(option);
			//L2PT-30152
			//kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
			return false;
		});
		//END L2PT-32060
		//START L2PT-1404
		ComboUtil.initComboGrid("txtTKPHUME3", sql_load_user.TKPHUME, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUME3").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME3").empty();
			$("#cboPHUME3").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUME", '5');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME4", sql_load_user.TKPHUME, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUME4").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME4").empty();
			$("#cboPHUME4").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUME", '5');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME5", sql_load_user.TKPHUME, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUME5").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME5").empty();
			$("#cboPHUME5").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUME", '5');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME6", sql_load_user.TKPHUME, sql_par, "600px", _col_loaduser, function(event, ui) {
			$("#txtTKPHUME6").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboPHUME6").empty();
			$("#cboPHUME6").append(option);
			//L2PT-30152
			kiemtra_trunggio_pttt(ui.item.USER_ID, "cboPHUME", '5');
			return false;
		});
		//END L2PT-1404
	}
	//L2PT-123034
	function initPopupCanhBao(){
		var dlgPOPUP_Chuy = DlgUtil.buildPopup("dlgChuY", "dlgPOPUP_CBAO_HUE", "Cảnh báo trùng thời gian thực hiện PTTT", 1000, 550, {"zIndex":998});
		var btnOK = $('#btn_OK');
		// L2PT-128929 start
		var btnClose = $('#btn_Close');
		btnClose.click(function() {	
			dlgPOPUP_Chuy.close();
		});
		btnOK.click(function() {
			if (opt.dongForm) {
				luupttt_close();
			} else {
				luupttt();
			}
			opt.dongForm = false;
			dlgPOPUP_Chuy.close();
		});
		// L2PT-128929 start
	}
	function changeTGKT_PTTT() {
		var ngayPttt = moment($('#txtNGAYPHAUTHUATTHUTHUAT').val(), "DD/MM/YYYY HH:mm:ss");
		if (show_cbo_tgkt && $('#cboTHOIGIAN_PTTT_CBO').val() != '-1' && $('#txtNGAYPHAUTHUATTHUTHUAT').val()) {
			//var ngayPttt = moment($('#txtNGAYPHAUTHUATTHUTHUAT').val(), "DD/MM/YYYY HH:mm:ss");
			ngayPttt.add($('#cboTHOIGIAN_PTTT_CBO').val(), 'minutes');
			$('#txtKETTHUCPTTT').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
			$('#txtKETTHUCPTTT_DKLCI').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
		} else if ($('#txtTHOIGIAN_PTTT').val() != '') {
			ngayPttt.add($('#txtTHOIGIAN_PTTT').val(), 'minutes');
			$('#txtKETTHUCPTTT').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
			$('#txtKETTHUCPTTT_DKLCI').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
		} else {
			//L2PT-17519 - set t/g ket thuc pttt = pttt luc + ngaypttmacdinh_add_sophut.
			//ngaypttmacdinh_add_sophut = 0  mac dinh  ket thuc pttt = pttt luc 
			if (ngaypttmacdinh_add_sophut != '0') {
				ngayPttt.add(ngaypttmacdinh_add_sophut, 'minutes');
				$('#txtKETTHUCPTTT').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
				$('#txtKETTHUCPTTT_DKLCI').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
			} else if (cfObj.TG_THUCHIEN_PTTT != '' && cfObj.TG_THUCHIEN_PTTT != null) {//L2PT-42499
				ngayPttt.add(cfObj.TG_THUCHIEN_PTTT, 'minutes');
				$('#txtKETTHUCPTTT').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
				$('#txtKETTHUCPTTT_DKLCI').val(ngayPttt.format('DD/MM/YYYY HH:mm:ss'));
			}
		}
	};
	// Xử lý sự kiện an hien nut ky so L2PT-29044
	function _checkBtnKySo() {
		var _reportCode = '';
		var par = [];
		if (opt.hospital_id == '965') {
			par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : opt.hosobenhanid
			}, {
				name : 'ptttid',
				type : 'String',
				value : _phauthuatthuthuatid
			} ];
			_reportCode = 'PHIEU_TUONGTRINHPHAUTHUAT_A4';
		} else {
			par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : opt.hosobenhanid
			}, {
				name : 'dichvukhambenhid',
				type : 'String',
				value : opt.dichvukhambenhid
			}, {
				name : 'benhnhanid',
				type : 'String',
				value : opt.benhnhanid
			} ];
			//L2PT-22996
			if (opt.hospital_id == '913' && (madichvu_caychi == '08.0007.0227' || mdv_in_caychi_ydgli.indexOf(madichvu_caychi) != -1)) {
				_reportCode = 'PHIEU_KETQUA_CAYCHI_913';
			} else {
				_reportCode = 'NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4';
			}
		}
		par.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _reportCode
		});
		var _check = CommonUtil.checkKyCaByParam(par);
		if (_check >= '1') {
			$("#btnLuu").prop("disabled", true);
			$("#btnLuuDong").prop("disabled", true);
			$("#btnKyCa").prop("disabled", true);
			$("#btnHuyCa").prop("disabled", false);
			$("#btnInCa").prop("disabled", false);
		} else {
			$("#btnLuu").prop("disabled", false);
			$("#btnLuuDong").prop("disabled", false);
			$("#btnKyCa").prop("disabled", false);
			$("#btnHuyCa").prop("disabled", true);
			$("#btnInCa").prop("disabled", true);
		}
	}
	//L2PT-87493
	function kiemtra_kytunhap(textInput) {
		if (textInput != '') {
			var str = ' aAáàạãảaAáàạãảâÂấầậẫẩbBcCdDđĐeEéèẹẽẻêÊếềệễểgGhHiIkKlLmMnNoOóòọõỏôÔốồộỗổơƠớờợỡởpPqQrRsStTuUúùụũủưƯứừựựửvVxXyY1234567890-=!@#%^&*(){}[]\\|;:\"\'<>,.?/'
			for (var i = 0; i < textInput.length; i++) {
				if (str.indexOf(textInput[i]) == -1) {
					return false;
				}
			}
		}
		return true;
	}
	// ChienDV START L2PT-71163
	function onClickThemPhieu(loaiClick) {
		let
		url = "manager.jsp?func=../noitru/NTU02D204_ThemPhieu";
		let
		width = window.innerWidth * 0.95;
		let
		heigth = window.innerHeight * 0.95;
		let
		key = "NTU_TTPTTT";
		let
		formid = $("#cboThemPhieu").val();
		let
		_formid = "";
		let
		_phieuid = "";
		let
		sql_par = new Object();
		sql_par.FORMID = formid;
		sql_par.DICHVUKHAMBENHID = opt.dichvukhambenhid;
		let
		dataArray = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.GETFORM", sql_par);
		if (dataArray != null && dataArray.length > 0) {
			_formid = dataArray[0].FORMID;
			_phieuid = dataArray[0].PHIEUID;
		}
		if (formid != _formid && loaiClick == 2) {
			DlgUtil.showMsg("Loại trình tự PTTT chưa được lưu không thể sửa.");
		} else {
			if (formid != -1) {
				if ($("#cboThemPhieu").val() != '-1') {
					let
					objParam = new Object();
					objParam.HOSOBENHANID = opt.hosobenhanid;
					objParam.TIEPNHANID = opt.tiepnhanid;
					objParam.KHAMBENHID = opt.khambenhid;
					objParam.BENHNHANID = opt.benhnhanid;
					objParam.MAUBENHPHAMID = opt.maubenhphamid;
					objParam.DICHVUKHAMBENHID = opt.dichvukhambenhid;
					objParam.KEY_VALUE = 1;
					objParam.FORM_ID = formid.length > 0 ? formid : _formid;
					objParam.PHIEU_ID = _phieuid;
					objParam.LOAI_PHIEU = key;
					dlgPopup = DlgUtil.buildPopupUrl("dlgThemPhieu", "divThemPhieu", url, objParam, "Trình tự phẫu thuật, thủ thuật", width, heigth);
					DlgUtil.open("dlgThemPhieu")
				} else {
					console.log("Chưa chọn trình tự phẫu thuật, thủ thuật.");
				}
			}
		}
	}
	// ChienDV END L2PT-71163
}
