/*
Mã màn hình  : DUC034_BAOCAOXUATNHAPTON
File mã nguồn : DUC034_BAOCAOXUATNHAPTON.js
<PERSON><PERSON><PERSON> đích  : màn hình in báo cáo xuất nhập tồn
<PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON>t   <PERSON> chú
<PERSON>anNT         13/03/2017      Tạo mới
ChienDV         17/11/2023      L2PT-58094
 */

var ctl_ar = [{
    type: 'buttongroup', id: 'btnPrint', icon: 'print', text: 'Báo cáo'
    , children: [
        {id: 'tbInXemBC', icon: 'print', text: 'Xem báo cáo ', hlink: '#'},
        {id: 'tbInXLS', icon: 'print', text: 'Kết xuất XLS', hlink: '#'},
        {id: 'tbRTF', icon: 'print', text: 'Kết xuất RTF', hlink: '#'},
        {id: 'tbInXemBC2', icon: 'print', text: 'Xem báo cáo 2', hlink: '#'},
        {id: 'tbInXLS2', icon: 'print', text: 'Kết xuất XLS 2', hlink: '#'},
    ]
}
    , {type: 'label', id: 'lblInfo', icon: '', text: 'Báo cáo Xuất nhập tồn'}];


function QLKhoList(i_mode) {

    var _mode = i_mode;
    if (_mode == '')
        _mode = "edit";
    var _gridHeader = "";
    var _param = session_par;

    var _gridId = "grid_DSThuocVatTu";

    var checkChiTiet = "0";
    var chkChiTietGia = "0";

    var gridHeaderHienTai = "";
    var loaitracuu = "duc034_bcxnt";
    var PHARMA_TCXNT_CANHBAOSOLUONG = '';
    var PHARMA_DUC_CHOTSOLIEU_ALL = '';
	var _dskho = '-1';

    this.load = doLoad;

    function setSysParam(_par_ar) {
        var v_par = _par_ar;
        for (var i1 = 0; i1 < _param.length; i1++) {
            console.log('_param[' + i1 + ']=' + _param[i1]);
            v_par.push({
                "name": "[S" + i1 + "]",
                "value": _param[i1]
            });
        }
        return v_par;
    }

    // Cau hinh load col Phu Tho
    var checkColPhuTho = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
        "LOAD_GRIDHEADER_PHUTHO");

    // Cau hinh hien thi loai tvt trong tra cuu xuat nhap ton
    var checkloaitvt = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
        "DUC_XNT_LOAITVT");

    function doLoad() {
        var toolbar = ToolbarUtil.build('toolbarId', ctl_ar);
        if ($("#cboKhoLap").val().toString() != '') { 
			_dskho = $("#cboKhoLap").val().toString() 
		} else { 
			_dskho = '-1'; 
		}		
			
        ComboUtil.getComboTag("cboKhoLap", "DUC01S002.DSKHO", [{ "name": "[0]", "value": "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16" }],"", "","sql", "",false);
		$('#cboKhoLap').SumoSelect({ search: true, searchText: 'Tìm kiếm', okCancelInMulti: true, selectAll: true });
		
        // Them loc theo gia nhan dan Phuc Lam Hung Yen
        if ((_param[0] == '55900')) {
            ComboUtil.getComboTag("cboDonGia", "DUC034_XNT_3", [""], "",
                {
                    value: '-1',
                    text: '-- Chọn đơn giá --'
                }, "");
        } else {
            ComboUtil.getComboTag("cboDonGia", "DUC034_XNT_2", [""], "",
                {
                    value: '-1',
                    text: '-- Chọn đơn giá --'
                }, "");
        }
        // Them combobox loai thuoc, vat tu Nghia Dan
        if (checkloaitvt == '1') {
            $('#divLoaiTVT').show();
            ComboUtil.getComboTag("cboLoaiTVT", "DUC034_XNT_LOAITVT", [""], "",
                {
                    value: '-1',
                    text: '-- Chọn loại TVT --'
                }, "");
        }

        loadDSCotHienTai();
        PHARMA_TCXNT_CANHBAOSOLUONG = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH',
            'PHARMA_TCXNT_CANHBAOSOLUONG');
        // Kiem tra cau hinh tra cuu xuat nhap ton theo chot so lieu
        PHARMA_DUC_CHOTSOLIEU_ALL = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH',
            'DUC_CHOTSOLIEU_ALL');
        var today = new Date();
        var dd = today.getDate();
        var mm = today.getMonth() + 1; // January is 0!
        var yyyy = today.getFullYear();
        var hour = today.getHours();
        var minute = today.getMinutes();
        var second = today.getSeconds();

        if (dd < 10) {
            dd = '0' + dd;
        }

        if (mm < 10) {
            mm = '0' + mm;
        }
        if (hour.toString().length == 1) {
            hour = '0' + hour;
        }
        if (minute.toString().length == 1) {
            minute = '0' + minute;
        }
        if (second.toString().length == 1) {
            second = '0' + second;
        }

        today = dd + '/' + mm + '/' + yyyy + ' ' + '23:59:59';
        var tuNgay = '01' + '/' + mm + '/' + yyyy + ' ' + '00:00:00';
        $("#txtNgayBD").val(tuNgay);
        $("#txtNgayKT").val(today);

        $('#cboKhoLap').focus();
        var _gridHeader = "";
        var _par = ['PHARMA_COL_TRACUUXNT'];
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par.join('$'));
        // Cau hinh loa col Phu Tho
        if (result != '0' && result != null)
            _gridHeader = result;
        else if (checkColPhuTho == '1') {
            // Load col Phu Tho
            _gridHeader =
                "Mã mặt hàng,MA_THUOC,70,0,f,l;" +
                "Tên mặt hàng,TENTHUOC,120,0,f,l;" +
				"Tên hoạt chất,HOATCHAT,90,0,f,l;" +
                "Số đăng ký,SODANGKY,90,0,f,l;" +
                "Số lô,SOLO,90,0,f,l;" +
                "Hạn dùng,HANDUNG,90,0,f,l;" +
                "Nước sản xuất,NUOCSANXUAT,90,0,f,l;" +
                "ĐVT,DONVI,50,0,f,l;" +
                "Đơn giá,DONGIA,80,0,f,r;" +
                "SL tồn đầu kỳ,SLTONDAUKY,70,0,f,r;" +
                "Tiền đầu kỳ,TTTONDAUKY,100,0,f,r;" +
                "SL nhập trong kỳ,SLNHAPTRONGKY,70,0,f,r;" +
                "Tiền nhập trong kỳ,TTNHAPTRONGKY,100,0,f,r;" +
                "SL hoàn trả,SLHOANTRATRONGKY,70,0,f,r;" +
                "Tiền hoàn trả trong kỳ,TTHOANTRATRONGKY,70,0,f,r;" +
                "SL xuất trong kỳ,SLXUATTRONGKY,70,0,f,r;" +
                "Tiền xuất trong kỳ,TTXUATTRONGKY,100,0,f,r;" +
                "SL tồn cuối kỳ,SLTONCUOIKY,70,0,f,r;" +
                "Cảnh báo số lượng,CANHBAOSOLUONG,70,0,f,r;" +
                "Tiền cuối kỳ,TTTONCUOIKY,100,0,f,r";
        } else {
            _gridHeader =
                "Mã mặt hàng,MA_THUOC,70,0,f,l;" +
                "Tên mặt hàng,TENTHUOC,120,0,f,l;" +
				"Tên hoạt chất,HOATCHAT,90,0,f,l;" +
                "ĐVT,DONVI,50,0,f,l;" +
                "Đơn giá,DONGIA,80,0,f,r;" +
                "SL tồn đầu kỳ,SLTONDAUKY,70,0,f,r;" +
                "Tiền đầu kỳ,TTTONDAUKY,100,0,f,r;" +
                "SL nhập trong kỳ,SLNHAPTRONGKY,70,0,f,r;" +
                "Tiền nhập trong kỳ,TTNHAPTRONGKY,100,0,f,r;" +
                "SL xuất trong kỳ,SLXUATTRONGKY,70,0,f,r;" +
                "Tiền xuất trong kỳ,TTXUATTRONGKY,100,0,f,r;" +
                "SL tồn cuối kỳ,SLTONCUOIKY,70,0,f,r;" +
                "Cảnh báo số lượng,CANHBAOSOLUONG,70,0,f,r;" +
                "Tiền cuối kỳ,TTTONCUOIKY,100,0,f,r";
        }

        GridUtil.init(_gridId, "100%", "360px", "", false, _gridHeader);
        GridUtil.addExcelButton("grvChiTiet", 'Xuất excel', true);
        GridUtil.addExcelButton(_gridId, 'Xuất excel', true);

        bindEvent();
        $('#toolbarIdtbInXemBC2').hide();
        $('#toolbarIdtbInXLS2').hide();
        if ((_param[0] == '1111')) {
            $('#divTonCuoi').show();
            $('#toolbarIdtbInXemBC2').show();
            $('#toolbarIdtbInXLS2').show();
        }
    }

    function bindEvent() {
        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {
                    pattern: /\d/
                },
                '9': {
                    pattern: /\d/,
                    optional: true
                },
                '#': {
                    pattern: /\d/,
                    recursive: true
                },
                'A': {
                    pattern: /[a-zA-Z0-9]/
                },
                'S': {
                    pattern: /[a-zA-Z]/
                }
            }
        };

        GridUtil.setGridParam(_gridId, {
            gridComplete: function (id) {
                if (PHARMA_TCXNT_CANHBAOSOLUONG == '1') {
                    CanhBaoSoLuong();
                }
            }
        });

        function CanhBaoSoLuong() {
            var rowIds = $('#grid_DSThuocVatTu').jqGrid('getDataIDs');
            for (var i = 0; i < rowIds.length; i++) {
                rowData = $('#grid_DSThuocVatTu').jqGrid('getRowData', rowIds[i]);
                var _color = '#FF9900';

                if (Number(rowData['SLTONCUOIKY']) < Number(rowData['CANHBAOSOLUONG'])) {
                    $('#grid_DSThuocVatTu').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', 'Số lượng cuối kỳ < cảnh báo số lượng');
                    $("#grid_DSThuocVatTu").jqGrid('setRowData', rowIds[i], "", {color: 'red'});
                }

            }
        }

        function loadKho() {

            var sql_par = $("#txtTimKho").val().trim() + '$';

            ComboUtil.getComboTag("cboKhoLap", "DUC005.DSKHOTK",
                sql_par, "", "", "sp", '', function () {
                });

        };
        $('#txtTimKho').on('change', function (e) {
            loadKho();
        });

        $("#toolbarIdtbInXemBC").on("click", function (e) {
            _print('pdf');
        });
        $("#toolbarIdtbInXLS").on("click", function (e) {
            _print('xls');
        });
        $("#toolbarIdtbInXemBC2").on("click", function (e) {
            _print2('pdf');
        });
        $("#toolbarIdtbInXLS2").on("click", function (e) {
            _print2('xls');
        });
        $("#toolbarIdtbRTF").on("click", function (e) {
            _print('rtf');
        });
        $("#toolbarIdtbXLSX").on("click", function (e) {
            _print('xlsx');
        });
        $("#toolbarIdtbExcel").on("click", function (e) {

            var rowIds = $('#' + _gridId).jqGrid('getDataIDs');

            if (rowIds != null && rowIds.length > 0) {
                _exportexcel(_gridId, '');
            } else {
                DlgUtil.showMsg("Không có dữ liệu trên danh sách !");
            }
        });

        $("#chkChiTiet").change(function () {
            if (this.checked) {
                checkChiTiet = "1";
            } else {
                checkChiTiet = "0";
            }
        });
        $("#chkChiTietGia").change(function () {
            if (this.checked) {
                chkChiTietGia = "1";
            } else {
                chkChiTietGia = "0";
            }
        });

        // Thuc hien tim kiem
        $('#btnTimKiem').on('click', function () {

            if ($('#cboKhoLap').val() == '') {
                DlgUtil.showMsg("Chưa chọn kho thuốc vật tư !");
                $('#cboKhoLap').focus();
            } else {
                initControl();
            }

        });
        $("#cmdChinhCot").on("click", function () {

            EventUtil.setEvent("dlg_thekho", function (e) {
                gridHeaderHienTai = "";
                gridHeaderHienTai = e.DSCOT;

                $("#grid_DSThuocVatTu").jqGrid("clearGridData", true);
                delete $("#grid_DSThuocVatTu");
                $('#grid_DSThuocVatTu').GridUnload('#grid_DSThuocVatTu');
                _gridHeader =
                    "Ngày tháng,NGAYTHANG,80,0,f,l" +
                    gridHeaderHienTai +
                    "SL tồn đầu kỳ,SLTDAUKY,70,number!3,f,r;" +
                    "SL nhập,SLNHAP,70,number!3,f,r;" +
                    "SL xuất,SLXUAT,70,number!3,f,r;" +
                    "SL tồn cuối,SLTONCK,70,number!3,f,r;" +
                    "Ghi chú,GHICHU,60,0,f,l";

                GridUtil.init(_gridId, "100%", "395px", "", false, _gridHeader);
                DlgUtil.close("dlgAppr");
            });

            EventUtil.setEvent("appr_close", function (e) {
                DlgUtil.close("dlgAppr");
            });

            var myVar = {loaitracuu: loaitracuu};
            dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC005_THEKHO_TuyChinhCot", myVar, "Tùy chỉnh cột hiển thị", 1100, 550);
            DlgUtil.open("dlgAppr");
        });
    }

    function _print(_file) {

        var tungay = $("#txtNgayBD").val().trim();
        var denngay = $("#txtNgayKT").val().trim();
        var par = [
            {
                name: 'tungay',
                type: 'date',
                value: tungay
            }
            ,
            {
                name: 'denngay',
                type: 'date',
                value: denngay
            },
            {
                name: 'khoid',
                type: 'String',
                value: $('#cboKhoLap').val()
            },
            {
                name: 'loaidongia',
                type: 'String',
                value: $('#cboDonGia').val()
            },
            {
                name: 'xemchitiet',
                type: 'String',
                value: checkChiTiet
            }
        ];
        if (chkChiTietGia == "0") {


            if (_file == "pdf") {
                openReport('window', 'DUC034_BAOCAOXUATNHAPTON_A4', _file, par);
            } else {
                var rpName = "DUC034_BAOCAOXUATNHAPTON_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _file;
                CommonUtil.inPhieu('window', "DUC034_BAOCAOXUATNHAPTON_A4", _file, par, rpName);
            }
        } else {
            if (_file == 'pdf') {
                openReport('window', 'DUC034_BAOCAOXUATNHAPTON_NHIEUGIA_A4', _file, par);
            } else {
                var rpName = 'DUC034_BAOCAOXUATNHAPTON_NHIEUGIA_A4' + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + '.' + _file;
                CommonUtil.inPhieu('window', 'DUC034_BAOCAOXUATNHAPTON_NHIEUGIA_A4', _file, par, rpName);
            }
        }
    }

    function _print2(_file) {

        var tungay = $("#txtNgayBD").val().trim();
        var denngay = $("#txtNgayKT").val().trim();
        var par = [
            {
                name: 'tungay',
                type: 'date',
                value: tungay,
            },
            {
                name: 'denngay',
                type: 'date',
                value: denngay,
            },
            {
                name: 'khoid',
                type: 'String',
                value: $('#cboKhoLap').val(),
            },
            {
                name: 'loaidongia',
                type: 'String',
                value: $('#cboDonGia').val(),
            },
            {
                name: 'xemchitiet',
                type: 'String',
                value: checkChiTiet,
            },
        ];
        if (_file == 'pdf') {
            openReport('window', 'DUC034_BAOCAOXUATNHAPTON2_A4', _file, par);
        } else {
            var rpName = 'DUC034_BAOCAOXUATNHAPTON2_A4' + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + '.' + _file;
            CommonUtil.inPhieu('window', 'DUC034_BAOCAOXUATNHAPTON2_A4', _file, par, rpName);
        }
    }

    function _exportexcel(gridId, _client) {
        var _self = this;
        var _colModel = $("#" + gridId).jqGrid('getGridParam', 'colModel');
        var theads = "";
        var colNames = new Array();
        var ii = 0;
        for (var cc = 0; cc < _colModel.length; cc++) {
            if (!_colModel[cc].hidden && _colModel[cc].label) {
                theads = theads + "<th>" + _colModel[cc].label + "</th>";
                var col = new Object();
                col.name = _colModel[cc].name;
                var _wd = "100";
                var _ta = "left";
                if (_colModel[cc].width != null) {
                    _wd = _colModel[cc].width;
                }
                if (_colModel[cc].align != null) {
                    _ta = _colModel[cc].align;
                }
                col.style = " style='width: " + _wd + ";text-align: " + _ta + "'";
                col.name = _colModel[cc].name.toUpperCase();
                colNames[ii++] = col;
            } else {
                console.log("_colModel[cc].hidden=" + _colModel[cc].hidden + " _colModel[cc].label=" + _colModel[cc].label);
            }
        }
        theads = "<tr>" + theads + "</tr>";

        var html = "<table border='1' class='tableList_1 t_space' cellspacing='10' cellpadding='0'>" + theads;
        var tungay = $("#txtNgayBD").val().trim();
        var denngay = $("#txtNgayKT").val().trim();
        var _sql_par = [$('#cboKhoLap').val(), tungay, denngay];
        mya = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC034_XNT_EXCEL", _sql_par.join('$'));
        console.log('mya=' + JSON.stringify(mya));
        for (i = 0; i < mya.length; i++) {
            html = html + "<tr>";
            data = mya[i]; // get each row
            for (j = 0; j < colNames.length; j++) {
                html = html + "<td " + colNames[j].style + ">" + data[colNames[j].name] + "</td>";
            }
            html = html + "</tr>";
        }
        html = html + "</table>";
        var excelFile = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";
        excelFile += "<head>";
        excelFile += '<meta http-equiv="Content-type" content="text/html;charset=utf-8" />';
        excelFile += "<!--[if gte mso 9]>";
        excelFile += "<xml>";
        excelFile += "<x:ExcelWorkbook>";
        excelFile += "<x:ExcelWorksheets>";
        excelFile += "<x:ExcelWorksheet>";
        excelFile += "<x:Name>";
        excelFile += "Sheet1";
        excelFile += "</x:Name>";
        excelFile += "<x:WorksheetOptions>";
        excelFile += "<x:DisplayGridlines/>";
        excelFile += "</x:WorksheetOptions>";
        excelFile += "</x:ExcelWorksheet>";
        excelFile += "</x:ExcelWorksheets>";
        excelFile += "</x:ExcelWorkbook>";
        excelFile += "</xml>";
        excelFile += "<![endif]-->";
        excelFile += "</head>";
        excelFile += "<body>";
        excelFile += html.replace(/"/g, '\'');
        excelFile += "</body>";
        excelFile += "</html>";

        var uri = "data:application/vnd.ms-excel;base64,";
        var base64_excelFile = window.btoa(unescape(encodeURIComponent(excelFile)));
        var _excelData = (uri + base64_excelFile);
        window.open(_excelData);
    }

    function initControl(ch) {
        var _t = $("#txtNgayBD").val().trim().substring(0, 10);
        var _d = $("#txtNgayKT").val().trim().substring(0, 10);
        if (_t != '' && !moment(_t, 'DD/MM/YYYY', true).isValid()) {
            DlgUtil.showMsg("Giá trị từ ngày nhập không hợp lệ");
            $("#txtNgayBD").focus();
            return;
        }
        if (_d != '' && !moment(_d, 'DD/MM/YYYY', true).isValid()) {
            DlgUtil.showMsg("Giá trị đến ngày nhập không hợp lệ");
            $("#txtNgayKT").focus();
            return;
        }

        if (_t != '' && _d != '') {
            var tungay = moment(_t, 'DD/MM/YYYY');
            var denngay = moment(_d, 'DD/MM/YYYY');
            if (tungay > denngay) {
                DlgUtil.showMsg("Giá trị từ ngày không được vượt quá giá trị đến ngày");
                $("#txtNgayBD").focus();
                return;
            }
        }

        if ($('#cboKhoLap').val() != '') {
			if ($("#cboKhoLap").val().toString() != '') { 
				_dskho = $("#cboKhoLap").val().toString() 
			} else { 
				_dskho = '-1'; 
			}
            // Load grid ds kho
            var tungay = $("#txtNgayBD").val().trim();
            var denngay = $("#txtNgayKT").val().trim();
            if (chkChiTietGia == 0) {
                if (checkloaitvt == '1') {
                    // Them load grid du lieu theo thuoc vat tu Nghia Dan
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), $('#cboLoaiTVT').val(), tungay, denngay]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_50860", sql_par);
                } else if (checkColPhuTho == '1') {
                    // Them load grid du lieu theo thuoc vat tu Phu Tho
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), tungay, denngay]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_PHUTHO", sql_par);
                } else if (_param[0] == '1111') {
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), tungay, denngay, $('#cboTonCuoi').val()]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_1111", sql_par);
                } else if (PHARMA_DUC_CHOTSOLIEU_ALL == '1') {
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), tungay, denngay]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_ALL", sql_par);
                } else {
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), tungay, denngay]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT", sql_par);
                }
            } else {
                if (checkloaitvt == '1') {
                    // Them load grid du lieu theo thuoc vat tu Nghia Dan
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), $('#cboLoaiTVT').val(), tungay, denngay]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_1_50860", sql_par);
                }
                if (_param[0] == '1111') {
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), tungay, denngay, $('#cboTonCuoi').val()]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_5", sql_par);
                } else {
                    var sql_par = RSUtil.buildParam("", [_dskho, checkChiTiet, $('#cboDonGia').val(), tungay, denngay]);
                    GridUtil.loadGridBySqlPage(_gridId, "DUC034_XNT_1", sql_par);
                }
            }
        }
    }

    function loadDSCotHienTai() {
        var param_ar_kho = loaitracuu + '$';
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC005.08",
            param_ar_kho);
        if (result != '-1' && result != '') {
            gridHeaderHienTai = result;
        }
    }
}