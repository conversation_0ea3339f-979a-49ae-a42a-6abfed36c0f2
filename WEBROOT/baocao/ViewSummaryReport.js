/*
BAOCAO.DSTH : <PERSON><PERSON><PERSON> danh sách báo cáo tổng hợp.
DMC.TIEUCHI.01 : <PERSON><PERSON><PERSON> danh sách tiêu chí.
DMC.TIEUCHI.02 : <PERSON><PERSON><PERSON> danh sách các phiên tổng hợp.
DMC.TIEUCHI.03 : <PERSON><PERSON><PERSON> danh sách tiêu chi tổng hợp.
CTL.REPORT.01 : <PERSON><PERSON> lý thêm từng phiên báo cáo.
CTL.REPORT.02 : L<PERSON>y những tiêu chí tổng hợp thành công.
CTL.REPORT.03 : Xử lý update param phiên báo cáo.
*/
function ViewSummaryReport(_opts) {
    this.load = doLoad;
    let DR_DB_NAME = "";
    let DATA_DB_NAME = _opts.DATA_DB_NAME;
    let CST_ID = 0;
    let CST_DESCRIPTION = 1;
    let CST_PARAM_SQL = 2;
    let CST_PARENT_CHILD = 3;
    let CST_TABLE_NAME = 4;
    let CST_FIELD_TYPE = 5;
    let CST_LOOKUP = 6;
    let CST_OPERATION = 7;
    let CST_TEXT_VALUE = 8;
    let CST_FIELD_NAME = 9;
    let CST_REPORT_ID = 10;
    let CST_PARAM_NAME = 11;
    let CST_FIELD_VIEW = 12;
    let _grdTieuChi = "grdDSTieuChi";
    let _grdPhienTongHop = "grdDSPhienTongHop";
    let _grdChiTietTieuChi = "grdDSChiTietTieuChi";
    let _paramData = [];
    let _transactionid = "";
    let _par_report = "";
    let _report_name = "";


    function doLoad() {
        _initControl();
        _bindEvent();
    }

    function _loadComboboxBaoCao() {
        let _cbo_dsth = "BAOCAO.DSTH";
        let _sql_dsth = "" + "$";
        let _def_opt = {value: '-1', text: '-- Chọn báo báo --'};
        ComboUtil.getComboTag("cboLoaiBaoCao", _cbo_dsth, _sql_dsth, "", _def_opt, "sp", "", "");
    }

    function _initControl() {
        $("#cboLoaiBaoCao").select2();
        _loadComboboxBaoCao();
        // Hiển thị tiêu đề danh sách tiêu chí
        _loadHeaderDSTieuChi();
        // Hiển thị tiêu đề danh sách phiên tổng hợp
        _loadHeaderPhienTongHop();
        // Hiển thị tiêu đề danh sách chi tiết tiêu chí tổng hợp
        _loadHeaderChiTietTieuChi();
    }


    function _bindEvent() {
        $("#cboLoaiBaoCao").change(function (event) {
            let select = document.getElementById("cboLoaiBaoCao");
            _report_name = select.options[select.selectedIndex].text;
            _setClassElement([], ['btnTongHop'], 'hidden');
            let _report_id = $("#cboLoaiBaoCao").val();
            if (_report_id != "-1") {
                _setClassElement([], ['tabPhienTongHop'], 'hidden');
            } else {
                _setClassElement(['tabPhienTongHop'], [], 'hidden');
            }
            // Hiển thị các field báo cáo
            _selReport(_report_id);
            // Hiển thị dữ liệu danh sách tiêu chí
            _loadGridDataTieuChi(_report_id);
            // Hiển thị dữ liệu danh sách phiên tổng hợp
            _loadGridDataPhienTongHop(_report_id);
        });

        // Xử lý Grid danh sách tiêu chí
        GridUtil.setGridParam(_grdTieuChi, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(_grdTieuChi);
                GridUtil.markRow(_grdTieuChi, id);
            }
        });

        // Xử lý Grid danh sách phiên tổng hợp
        GridUtil.setGridParam(_grdPhienTongHop, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(_grdPhienTongHop);
                GridUtil.markRow(_grdPhienTongHop, id);
                if (id) {
                    let rowData = $("#" + _grdPhienTongHop).jqGrid('getRowData', id);
                    _transactionid = rowData.TRANSACTIONID;
                    _par_report = rowData.PARAMS;
                    _loadGridDataChiTietTieuChi(_transactionid);
                }
            }
        });

        // Xử lý Grid danh sách chi tiết tổng hợp tiêu chí
        GridUtil.setGridParam(_grdChiTietTieuChi, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(_grdChiTietTieuChi);
                GridUtil.markRow(_grdChiTietTieuChi, id);
            }, gridComplete: function (id) {
                let _rowids = $("#" + _grdChiTietTieuChi).getDataIDs();
                if (_rowids && _rowids.length > 0) {
                    _rowids.forEach(function (rowid) {
                        let _row = $("#" + _grdChiTietTieuChi).jqGrid('getRowData', rowid);
                        if (_row.TRANGTHAI == '1') {
                            let _icon = '<div style="text-align: center"><img src="../common/image/Circle_Green.png" width="15px"></div>';
                            $("#" + _grdChiTietTieuChi).jqGrid('setCell', rowid, 'ICON', _icon);
                        } else if (_row.TRANGTHAI == '2') {
                            let _icon = '<div style="text-align: center"><img src="../common/image/Circle_Red.png" width="15px"></div>';
                            $("#" + _grdChiTietTieuChi).jqGrid('setCell', rowid, 'ICON', _icon);
                        } else if (_row.TRANGTHAI == '0') {
                            let _icon = '<div style="text-align: center"><img src="../common/image/Circle_Yellow.png" width="15px"></div>';
                            $("#" + _grdChiTietTieuChi).jqGrid('setCell', rowid, 'ICON', _icon);
                        }
                    });
                }
            },
        });

        // Xử lý khi nhấn nút Tổng hợp báo cáo
        $("#btnTongHop").click(function (e) {
            let _type = $(e.currentTarget).data("type") + "";
            let _listParamData = getParamToServer($("#cboLoaiBaoCao").val());
            let _listParamObject = new Object();
            let _arrayTieuChi = $("#" + _grdTieuChi).getGridParam('selarrrow').map(el => $('#' + _grdTieuChi).getRowData(el).TIEUCHIID);
            if (_arrayTieuChi.length > 0) {
                for (j = 0; j < _listParamData.length; j++) {
                    _listParamObject[_replaceParam(_listParamData[j].name)] = _listParamData[j].value;
                }
                _listParamObject.report_id = $("#cboLoaiBaoCao").val();
                _listParamObject.tieuchi_id = _orderByTieuChiId(_arrayTieuChi.join(","));
                console.log(JSON.stringify(_listParamObject));
                // Gọi hàm thêm dữ liệu từng phiên báo cáo
                _insertReportingSession(JSON.stringify(_listParamObject), _type);
            } else {
                DlgUtil.showMsg("Vui lòng chọn tiêu chí báo cáo để tổng hợp.", function () {
                }, 1000);
            }
        });

        // Xử lý nút Tổng hợp lại báo cáo
        $("#btnTongHopChiTietTieuChi").click(function (e) {
            let _type = $(e.currentTarget).data("type") + "";
            let _report_id = $("#cboLoaiBaoCao").val();
            let _jsonParam = JSON.parse(_par_report);
            let _arrayTieuChi = $("#" + _grdChiTietTieuChi).getGridParam('selarrrow').map(el => $('#' + _grdChiTietTieuChi).getRowData(el).TIEUCHIID).map(Number);
            if (_arrayTieuChi.length > 0) {
                // Lấy nhưng tiêu chí được tổng hợp thành công
                let _sql_par = [_report_id.toString(), _transactionid.toString()];
                let _arTieuChiSuccess = jsonrpc.AjaxJson.ajaxCALL_SP_S("CTL.REPORT.02", _sql_par.join('$'));
                _arTieuChiSuccess = _arTieuChiSuccess ? _arTieuChiSuccess.split(",").map(Number) : [0];
                // Kiểm tra xem có tồn tại những tiêu chí đã tổng hợp hay không.
                let _arrayExist = _arTieuChiSuccess.filter(item => _arrayTieuChi.includes(item));
                if (_arrayExist.length > 0) {
                    DlgUtil.showConfirm("" +
                        `<div style='text-align: center'><b>(Đồng ý) => Tổng hợp tiêu chí đã tổng hợp và chưa tổng hợp.</b>` +
                        `<b>(Hủy bỏ) => Tổng hợp tiêu chí chưa được tổng hợp.</b></div>`, function (flag) {
                        if (flag) {
                            // Tổng hợp những tiêu chí đã được tổng hợp và chưa tổng hợp.
                            let _arrayUpdate = [...new Set([..._arrayTieuChi, ...(_jsonParam.tieuchi_id).split(",").map(Number)])].sort((a, b) => a - b);
                            let _arrayApi = _arrayTieuChi ? _arrayTieuChi.map(Number).sort((a, b) => a - b) : [];
                            _updateReportingSession(1, _arrayUpdate.join(","), _arrayApi.join(","), _type);
                        } else {
                            // Xử lý loại bỏ những TIEUCHIID đã tổng hợp thành công.
                            let _uniqueArr1 = _arTieuChiSuccess.filter(item => !new Set(_arrayTieuChi).has(item));
                            let _uniqueArr2 = _arrayTieuChi.filter(item => !new Set(_arTieuChiSuccess).has(item));
                            let _array = [..._uniqueArr1, ..._uniqueArr2];
                            let _arrayResult = _arrayTieuChi.filter(item => _array.includes(item));
                            let _arrayUpdate = [...new Set([..._arrayResult, ...(_jsonParam.tieuchi_id).split(",").map(Number)])].sort((a, b) => a - b);
                            let _arrayApi = _arrayResult ? _arrayResult.map(Number).sort((a, b) => a - b) : [];
                            // Tổng hợp những tiêu chí chưa được tổng hợp.
                            if (_arrayApi.length > 0) {
                                _updateReportingSession(2, _arrayUpdate.join(","), _arrayApi.join(","), _type);
                            } else {
                                DlgUtil.showMsg("Không có tiêu chí nào được chọn (Tiêu chí chưa được tổng hợp).");
                            }
                        }
                    });
                } else {
                    DlgUtil.showConfirm("" +
                        `<div style='text-align: center'><b>Tổng hợp tiêu chí đã được chọn hay không ?</b></div>`, function (flag) {
                        if (flag) {
                            // Tổng hợp những tiêu chí đã được chọn.
                            let _arrayUpdate = [...new Set([..._arrayTieuChi, ...(_jsonParam.tieuchi_id).split(",").map(Number)])].sort((a, b) => a - b);
                            let _arrayApi = _arrayTieuChi ? _arrayTieuChi.map(Number).sort((a, b) => a - b) : [];
                            _updateReportingSession(2, _arrayUpdate.join(","), _arrayApi.join(","), _type);
                        }
                    });
                }
            } else {
                DlgUtil.showMsg("Vui lòng chọn tiêu chí báo cáo để tổng hợp.", function () {
                }, 1000);
            }
        });

        // Xem log Tổng hợp báo cáo
        $("#btnLogReport").click(function (e) {
            let _type = $(e.currentTarget).data("type") + "";
            let paramInput = {
                PAR_REPORTID: $("#cboLoaiBaoCao").val().toString(),
                PAR_TRANSACTIONID: _transactionid.toString(),
                PAR_TYPE: _type.toString()
            };
            let url = "manager.jsp?func=../baocao/ViewSummaryReportLog";
            let popup = DlgUtil.buildPopupUrl("divDlgViewSummaryReportLog", "divDlg", url, paramInput, "Danh sách Log phiên tổng hợp", 1200, 600);
            popup.open("divDlgViewSummaryReportLog");
        });
    }

    // Xử lý thêm dữ liệu từng phiên báo cáo
    function _insertReportingSession(_listObject, _type) {
        console.log("Array Insert SQL: " + _listObject);
        let _json = JSON.parse(_listObject);
        let _report_id = $("#cboLoaiBaoCao").val();
        let _sql_par = [_listObject.toString(), _report_id.toString(), "", "", "INSERT"];
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("CTL.REPORT.01", _sql_par.join('$'));
        if (fl != '' || fl != null || fl != undefined) {
            if (fl.split("$")[0] == '2') {
                DlgUtil.showMsg(`Đã tồn tại phiên báo với điều kiện này (Phiên tổng hợp: ` + fl.split("$")[1] + `)`, function () {
                }, 2000);
            } else {
                DlgUtil.showMsg(`Thêm phiên tổng hợp báo cáo thành công (Phiên tổng hợp: ` + fl.split("$")[1] + `). Chờ tổng hợp các tiêu chí báo cáo...`, function () {
                    $("#liTongHop").tab("show");
                }, 4000);
                _json["transaction_id"] = fl.split("$")[1];
                _apiDoPostReport(JSON.stringify(_json), _type);
                _loadGridDataPhienTongHop(_report_id);
            }
        } else {
            DlgUtil.showMsg("Lưu thông tin Phiên tổng hợp báo cáo lỗi.", function () {
            }, 1000);
        }
    }

    // Xử lý cập nhập lại dữ liệu tổng hợp tiêu chí
    function _updateReportingSession(key, array_update, array_api, _type) {
        console.log("Array Update SQL: " + array_update);
        console.log("Array Update API : " + array_api);
        let _jsonParamUpdate = JSON.parse(_par_report);
        _jsonParamUpdate.tieuchi_id = array_update.toString();
        let _jsonParamApi = JSON.parse(_par_report);
        _jsonParamApi.tieuchi_id = array_api.toString();
        _jsonParamApi.transaction_id = _transactionid.toString();
        let _report_id = $("#cboLoaiBaoCao").val();
        let _sql_par = [JSON.stringify(_jsonParamUpdate).toString(), _report_id.toString(), _transactionid, "", "UPDATE"];
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("CTL.REPORT.01", _sql_par.join('$'));
        if (fl != '' || fl != null || fl != undefined) {
            if (fl.split("$")[0] == '1') {
                console.log("Update Param Success" + JSON.stringify(_jsonParamUpdate));
                _apiDoPostReport(JSON.stringify(_jsonParamApi), _type);
            } else {
                console.log("Update Param Error" + JSON.stringify(_jsonParamUpdate));
            }
        }
    }

    // Api xử lý tổng hợp dữ liệu báo cáo
    function _apiDoPostReport(json, type) {
        let _url = "/vnpthis/reportcollector";
        $.ajax({
            url: _url.toString(),
            type: "POST",
            contentType: "application/json; charset=UTF-8",
            data: json.toString(),
            success: function (request) {
                if (request.code == '200') {
                    _aptDoGetReport(json, type);
                } else {
                    alert("Xảy ra lỗi khi tổng hợp dữ liệu các tiêu chí.");
                }
            },
            error: function (msg) {
                alert("Xảy ra lỗi khi tổng hợp dữ liệu các tiêu chí.");
            }
        });
    }

    // Api lấy kết quả tổng hợp báo cáo
    function _aptDoGetReport(json, type) {
        let report_id = $("#cboLoaiBaoCao").val();
        let _url = `/vnpthis/reportcollector?report_id=${report_id}`;
        $.ajax({
            url: _url.toString(),
            type: "GET",
            contentType: "application/json; charset=UTF-8",
            success: function (request) {
                if (request.code == 200) {
                    if (type == '1') {
                        _loadGridDataChiTietTieuChi(_transactionid);
                        alertify.success(`<b style="color: black">Tổng hợp tiến trình báo cáo [<span style="color: blue">` + _report_name + `</span>] thành công. </b>`, 4000);
                    } else if (type == '2') {
                        _loadGridDataChiTietTieuChi(_transactionid);
                        alertify.success(`<b style="color: black">Tổng hợp tiến trình các tiêu chí báo cáo [<span style="color: blue">` + _report_name + `</span>] thành công. </b>`, 4000);
                    }
                }
            },
            error: function (msg) {
                alert("Xảy ra lỗi khi tổng hợp dữ liệu các tiêu chí.");
            }
        });
    }

    // Sắp xếp TIEUCHIID theo chiều tăng dần
    function _orderByTieuChiId(_lstTieuChiId) {
        let _order = "";
        try {
            let array = [...new Set(_lstTieuChiId.split(",").map(Number))];
            _order = array.sort((a, b) => a - b).map(String).join(",");
        } catch (e) {
            _order = "";
        }
        return _order;
    }

    // Xử lý xóa các VPAR ở đầu từng PARAM
    function _replaceParam(_param) {
        let _strParam = "";
        if (_param.indexOf("VPARD_") >= 0) {
            _strParam = _param.replace("VPARD_", "");
        } else if (_param.indexOf("VPARS_") >= 0) {
            _strParam = _param.replace("VPARS_", "");
        } else if (_param.indexOf("VPARN_") >= 0) {
            _strParam = _param.replace("VPARN_", "");
        }
        return _strParam;
    }

    // Hiển thị tiêu đề danh sách tiêu chí
    function _loadHeaderDSTieuChi() {
        let _group = {
            groupField: ['TIEUCHICHA'], groupColumnShow: [false], groupText: ['<b>{0}</b>']
        };
        let _gridDSHeader = "" +
            "TIEUCHIID,TIEUCHIID,0,0,t,l,1,2;" +
            "Tiêu chí cha,TIEUCHICHA,300,0,f,l,1,2;" +
            "STT,STT,70,0,f,l,1,2;" +
            "Tên tiêu chí,TENTIEUCHI,340,0,f,l,1,2;" +
            "Đơn vị,DONVI,80,0,f,l,1,2;" +
            "Thủ tục,FUNCNAME,220,0,f,l,1,2;" +
            "Ngày tạo,NGAYTAO,150,0,f,l,1,2;" +
            "Người tạo,NGUOITAO,220,0,f,l,1,2";

        GridUtil.initGroup(_grdTieuChi, "100%", "420", "Danh sách các tiêu chí theo báo cáo", true, _group, _gridDSHeader, false, {
            rowNum: 40, rowList: [80, 120, 160]
        });
    }

    // Hiển thị dữ liệu danh sách tiêu chí
    function _loadGridDataTieuChi(_report_id) {
        let _sql_par = [];
        let _objData = new Object();
        _objData["REPORT_ID"] = _report_id.toString();
        _sql_par.push({"name": "[0]", "value": _objData});
        GridUtil.loadGridBySqlPage(_grdTieuChi, "DMC.TIEUCHI.01", _sql_par);
    }

    // Hiển thị tiêu đề danh sách phiên tổng hợp báo cáo
    function _loadHeaderPhienTongHop() {
        let _gridDSHeader = "" +
            "PARAMS,PARAMS,0,0,t,l,1,2;" +
            "REPORT_CODE,REPORT_CODE,0,0,t,l,1,2;" +
            "Phiên tổng hợp,TRANSACTIONID,140,0,f,l,1,2;" +
            "Tên báo cáo,TENBAOCAO,360,0,f,l,1,2;" +
            "Ngày tạo,NGAYTAO,160,0,f,l,1,2;" +
            "Ngày cập nhật,NGAYCAPNHAT,160,0,f,l,1,2;" +
            "Người tạo,NGUOITAO,220,0,f,l,1,2";

        GridUtil.init(_grdPhienTongHop, "100%", "200", "Danh sách các lần tổng hợp báo cáo", false, _gridDSHeader, false, {
            rowNum: 20, rowList: [40, 60, 80]
        });
    }

    // Hiển thị dữ liệu danh sách phiên tổng hợp
    function _loadGridDataPhienTongHop(_report_id) {
        let _sql_par = [];
        let _objData = new Object();
        _objData["REPORT_ID"] = _report_id.toString();
        _sql_par.push({"name": "[0]", "value": _objData});
        GridUtil.loadGridBySqlPage(_grdPhienTongHop, "DMC.TIEUCHI.02", _sql_par);
    }

    // Hiển thị tiêu đề danh sách chi tiết tiêu chí tổng hợp
    function _loadHeaderChiTietTieuChi() {
        let _group = {
            groupField: ['TIEUCHICHA'], groupColumnShow: [false], groupText: ['<b>{0}</b>']
        };
        let _gridDSHeader = "" +
            "ID,ID,0,0,t,l,1,2;" +
            "TRANSACTIONID,TRANSACTIONID,0,0,t,l,1,2;" +
            "TIEUCHIID,TIEUCHIID,0,0,t,l,1,2;" +
            "REPORTID,REPORTID,0,0,t,l,1,2;" +
            "TRANGTHAI,TRANGTHAI,0,0,t,l,1,2;" +
            " ,ICON,25,0,ns,l;" +
            "Tiêu chí cha,TIEUCHICHA,300,0,f,l,1,2;" +
            "Tên tiêu chí,TENTIEUCHI,240,0,f,l,1,2;" +
            "Ngày tạo,NGAYTAO,140,0,f,l,1,2;" +
            "Ngày cập nhật,NGAYCAPNHAT,140,0,f,l,1,2;" +
            "Người tạo,NGUOITAO,160,0,f,l,1,2;" +
            "Dữ liệu,DULIEU,400,0,f,l,1,2";

        GridUtil.initGroup(_grdChiTietTieuChi, "100%", "280", "Danh sách các tiêu chí theo báo cáo", true, _group, _gridDSHeader, false, {
            rowNum: 40, rowList: [80, 120, 160]
        });
    }

    // Hiển thị danh sách các tiêu chí được tổng hợp
    function _loadGridDataChiTietTieuChi(_transactionid) {
        let _sql_par = [];
        let _objData = new Object();
        _objData["REPORT_ID"] = _transactionid.toString() + "|" + $("#cboLoaiBaoCao").val();
        _sql_par.push({"name": "[0]", "value": _objData});
        GridUtil.loadGridBySqlPage(_grdChiTietTieuChi, "DMC.TIEUCHI.03", _sql_par);
    }

    // Hiển thị các field báo cáo
    function _selReport(_report_id) {
        _paramData = jsonrpc.AjaxJson.dbExecuteQuery(DR_DB_NAME, "DRPT.S03", [{name: '[0]', value: _report_id.toString()}]);
        console.log('_paramData=' + JSON.stringify(_paramData));
        htParam = new HashMap();
        htObjType = new HashMap();
        $('#divParam').html("");
        $("#divTongHopDuLieu").removeClass("hidden");
        _shListParamRp('', '');
    }

    // Thiết lập class vào element
    function _setClassElement(arrMore, arrCancel, keyValue) {
        if (arrMore.length > 0) {
            for (let i = 0; i < arrMore.length; i++) {
                $("#" + arrMore[i]).addClass(keyValue);
            }
        }

        if (arrCancel.length > 0) {
            for (let i = 0; i < arrCancel.length; i++) {
                $("#" + arrCancel[i]).removeClass(keyValue);
            }
        }
    }

    function _shListParamRp(param_id, reSel) {
        var sTable = "";
        var tbl;
        if (chkExistsTable("tbListParamRp") == 0) {
            sTable = "<table id='tbListParamRp' width='100%' cellpadding='0' cellspacing='1'></table>";
            $("#divParam").html(sTable);
        }
        tbl = document.getElementById('tbListParamRp');
        var i = 1;
        var rDel = 1;
        var opt = 0;
        var rowNew;
        for (j = 0; j < _paramData.length; j++) {
            var param_desc = _paramData[j][CST_DESCRIPTION];
            var param_id = _paramData[j][CST_ID];
            var fieldfsql = _paramData[j][CST_PARAM_SQL];
            var parent_child = _paramData[j][CST_PARENT_CHILD];
            var r_chkObjExists = chkExists(param_id);
            var lastRow = tbl.rows.length;
            if (r_chkObjExists == 1) {
                var cellRight;
                if (j % 2 == 0) {
                    rowNew = doCreateRow(tbl, lastRow, param_desc, param_id);
                    cellRight = doCreateCell(rowNew, param_desc, param_id, 0);
                } else {
                    cellRight = doCreateCell(rowNew, param_desc, param_id, 2);
                }
                objReportParamField(param_id, DATA_DB_NAME, 0, cellRight, opt);
            }
        }
    }

    function doCreateRow(tbl, lastRow, param_desc, param_id) {
        var row = tbl.insertRow(lastRow);
        row.height = "27px";
        row.styles = "font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold;";
        return row;
    }

    function doCreateCell(row, param_desc, param_id, idx) {
        htParam.put(param_id, "");
        var cellLeft = row.insertCell(idx);
        cellLeft.width = "15%";
        var textNode = document.createTextNode(param_desc);
        cellLeft.appendChild(textNode);
        var cellRight = row.insertCell(idx + 1);
        cellRight.width = "30%";
        cellRight.style.paddingRight = "20px";
        return cellRight;
    }

    function chkExistsTable(tbName) {
        try {
            var strTable = document.getElementById(tbName).innerHTML;
            if (strTable.length > 0) return 1; else return 0;
        } catch (e) {
            return 0;
        }
    }

    function chkExists(tagName) {
        try {
            if (htParam.get(tagName) == "undefined") {
                return 0;
            } else if (htParam.get(tagName) == null) {
                return 1;
            } else {
                return 2;
            }
        } catch (e) {
            return 0;
        }
    }

    function getSelectedList(cboId, fldType) {
        var selectedValues = [];
        $("#parCtl" + cboId + " :selected").each(function () {
            var val1 = $(this).val();
            if (fldType == 'adVarChar') val1 = "'" + val1 + "'";
            selectedValues.push($(this).val());
        });
        return selectedValues;
    }

    function objReportParamField(v_param_id, db_data_name, objExists, cellRight, opt) {
        var strObj;
        var _field_type, _operations, _lookup, _defaulValue, _fieldView;
        var ControlType, ParamType;
        var parentParamId;

        var vPar = getParamById(v_param_id);

        if (vPar != undefined && vPar != null) {
            console.log('vPar=' + JSON.stringify(vPar));
            _field_type = vPar[CST_FIELD_TYPE];
            _lookup = vPar[CST_LOOKUP];
            _operations = vPar[CST_OPERATION];
            _fieldView = vPar[CST_FIELD_VIEW];
            _param_sql = vPar[CST_PARAM_SQL];
            strParentChild = vPar[CST_PARENT_CHILD];
            ParamType = getParamType(_field_type);
            ControlType = getControlType(_operations, _field_type, _lookup);
            _defaulValue = vPar[CST_TEXT_VALUE];
            console.log('1_defaulValue=' + _defaulValue);
            if (_defaulValue == undefined || _defaulValue == null || _defaulValue == 'null') _defaulValue = '';
            console.log('2_defaulValue=' + _defaulValue);

            var strBuffer = "";
            default_value = "";
            var strLookup;
            htObjType.put(v_param_id, ControlType);
            console.log('_operations=' + _operations + ' _field_type=' + _field_type + ' ControlType=' + ControlType);
            switch (ControlType) {
                case CONTROL_TYPE_MULTI_SELECT:
                case CONTROL_TYPE_SINGLE_SELECT:
                    strLookup = _param_sql;
                    let jPar = [];
                    if (strParentChild != null && strParentChild != ',') {
                        let idPar = {
                            "id": v_param_id
                        };
                        jPar.push(idPar);
                        fields = new Array();
                        fields = strParentChild.split(",");
                        var fieldsLength = fields.length;
                        var strField;
                        var strParentField;
                        if (fieldsLength == 2) {
                            strField = fields[0];
                            strParentField = fields[1];
                            var ParentParam = null;
                            var parentValues = "";
                            var pPar = getParamByFieldName(strParentField);

                            if (pPar != undefined && pPar != null) {
                                parentParamId = pPar[CST_ID];
                                var parentFieldType = pPar[CST_FIELD_TYPE];
                                var parentValues = getSelectedList(parentParamId, parentFieldType);
                                if (parentValues) {
                                    let objValue = {};
                                    objValue[strField] = parentValues.toString();
                                    jPar.push(objValue);
                                }
                            }
                        } else if (fieldsLength > 2) {
                            var condStr = "";
                            var isNoParentValue = false;
                            for (var i = 0; i < fieldsLength; i += 2) {
                                strField = fields[i];
                                strParentField = fields[i + 1];
                                var ParentParam = null;
                                var parentValues = "";
                                var pPar = getParamByFieldName(strParentField);
                                if (pPar != undefined && pPar != null) {
                                    parentParamId = pPar[CST_ID];
                                    var parentFieldType = pPar[CST_FIELD_TYPE];
                                    var parentValues = getSelectedList(parentParamId, parentFieldType);
                                    if (parentValues) {
                                        let objValue = {};
                                        objValue[strField] = parentValues.toString();
                                        jPar.push(objValue);
                                    }
                                }
                            }
                        }
                    } else {
                        strBuffer += "1=1";
                    }
                    strLookup = strLookup.replace(/BPAR/g, strBuffer);
                    strLookup = strLookup.replace(/bpar/g, strBuffer);
                    try {
                        for (var i = 0; i < usrData.length; i++) {
                            console.log('usrData:' + usrData[i][0] + '=' + usrData[i][1]);
                            strLookup = strLookup.replace(new RegExp(usrData[i][0], "g"), usrData[i][1]);
                        }
                    } catch (eee) {
                        alert('eee=' + eee);
                    }
                    console.log('strLookup=' + strLookup);
                    var strMultiple = "";
                    if (ControlType == CONTROL_TYPE_MULTI_SELECT) {
                        strMultiple = "multiple";
                    }
                    creComboBox(db_data_name, strLookup, v_param_id, "", _defaulValue, strMultiple, objExists, cellRight, opt, parentParamId, jPar, _fieldView);
                    break;
                case CONTROLTYPE_MULTI_TEXT:
                case CONTROLTYPE_SINGLE_TEXT:
                case CONTROLTYPE_MULTI_NUMERIC:
                case CONTROLTYPE_SINGLE_NUMERIC:

                    if (objExists == 0) {
                        var strButton = creTextBox(v_param_id, _defaulValue, "60", "form-control input-sm", "");
                        cellRight.innerHTML = strButton;

                    } else document.getElementById("parCtl" + v_param_id).value = _defaulValue;
                    break;
                case CONTROLTYPE_DOUBLE_NUMERIC:
                case CONTROLTYPE_DOUBLE_TEXT:
                    if (objExists == 0) {
                        var strButton = creTextBox("nho" + v_param_id, _defaulValue, "60", "form-control input-sm", "") + " ";
                        strButton = strButton + creTextBox("lon" + v_param_id, _defaulValue, "60", "form-control input-sm", "");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_SINGLE_DATEPICKER:
                    if (objExists == 0) {
                        console.log('_defaulValue2=' + _defaulValue);
                        var strButton = createDateBox("", v_param_id, _defaulValue, false, "T");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_DOUBLE_DATEPICKER:
                    if (objExists == 0) {
                        var strButton = createDateBox("from", v_param_id, _defaulValue, false, "T");
                        strButton += createDateBox("to", v_param_id, _defaulValue, false, "T");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_SINGLE_DATE_D:
                    if (objExists == 0) {
                        var strButton = createDateBox("", v_param_id, _defaulValue, false, "D");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_DOUBLE_DATE_D:
                    if (objExists == 0) {
                        var strButton = createDateBox("from", v_param_id, _defaulValue, false, "D");
                        strButton += createDateBox("to", v_param_id, _defaulValue, false, "D");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_SINGLE_DATE_M:
                    if (objExists == 0) {
                        var strButton = createDateBox("", v_param_id, _defaulValue, false, "M");
                        cellRight.innerHTML = strButton;
                    }

                    break;
                case CONTROLTYPE_DOUBLE_DATE_M:
                    if (objExists == 0) {
                        var strButton = createDateBox("from", v_param_id, _defaulValue, false, "M");
                        strButton += createDateBox("to", v_param_id, _defaulValue, false, "M");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_SINGLE_DATE_Y:
                    if (objExists == 0) {
                        var strButton = createDateBox("", v_param_id, _defaulValue, false, "Y");
                        cellRight.innerHTML = strButton;
                    }
                    break;
                case CONTROLTYPE_DOUBLE_DATE_Y:
                    if (objExists == 0) {
                        var strButton = createDateBox("from", v_param_id, _defaulValue, false, "Y");
                        strButton += createDateBox("to", v_param_id, _defaulValue, false, "Y");
                        cellRight.innerHTML = strButton;
                    }
                    break;
            }
        }
    }

    function removeTab(liElem) {
        $('ul#tabsReport > li#tab_' + liElem).fadeOut(1000, function () {
            $(this).remove();
        });
        $('div.tab-content div#tabContent_' + liElem).remove();
        return false;
    };

    function getParamById(_id) {
        var _par;
        for (j = 0; j < _paramData.length; j++) {
            if (_paramData[j][CST_ID] == _id) {
                _par = _paramData[j];
                break;
            }
        }
        return _par;
    }

    function getParamByName(_par_name) {
        var _par;
        for (j = 0; j < _paramData.length; j++) {
            console.log('_paramData[' + j + '].CST_PARAM_NAME=' + _paramData[j][CST_PARAM_NAME]);
            if (_paramData[j][CST_PARAM_NAME] == _par_name) {
                console.log('getParamByFieldName ' + _par_name + ' ' + j);
                _par = _paramData[j];
                break;
            }
        }
        return _par;
    }

    function getParamByFieldName(_field_name) {
        var _par;
        for (j = 0; j < _paramData.length; j++) {
            console.log('_paramData[' + j + '].CST_FIELD_NAME=' + _paramData[j][CST_FIELD_NAME]);
            if (_paramData[j][CST_FIELD_NAME] == _field_name) {
                console.log('getParamByFieldName ' + _field_name + ' ' + j);
                _par = _paramData[j];
                break;
            }
        }
        return _par;
    }

    function validateParam() {
        for (j1 = 0; j1 < _paramData.length; j1++) {
            var param_desc = _paramData[j1][CST_DESCRIPTION];
            var param_id = _paramData[j1][CST_ID];
            var field_type = _paramData[j1][CST_FIELD_TYPE];
            var field_view = _paramData[j1][CST_FIELD_VIEW];
            if (field_type == 'adDBTimeStamp' || field_type == 'adDate') {
                if (field_view != '' && field_view != null) {
                    var rule = field_view.split('+');
                    var prevParName = rule[0];
                    var consVal = parseFloat(rule[1]);
                    var prevPar = getParamByName(prevParName);
                    var prevParId = prevPar[CST_ID];
                    var prevVal = $('#parCtl' + prevParId).val();
                    var currVal = $('#parCtl' + param_id).val();
                    if (prevVal.length <= 10) prevVal = prevVal + ' 00:00:00';
                    if (currVal.length <= 10) currVal = currVal + ' 00:00:00';
                    var prevMS = getDateFromFormat(prevVal, 'dd/MM/yyyy HH:mm:ss');
                    var currMS = getDateFromFormat(currVal, 'dd/MM/yyyy HH:mm:ss');
                    if (currMS - prevMS >= consVal * (1000 * 60 * 60 * 24)) {
                        alert(param_desc + ' không hợp lệ');
                        return false;
                    }
                }
            }
        }
        return true;
    }

    function getParamToServer(report_id) {
        htSession = new HashMap();
        var i = 0;
        var par_ar = new Array();
        for (i = 0; i < _paramData.length; i++) {
            var paramId = _paramData[i][CST_ID];
            var paramName = _paramData[i][CST_PARAM_NAME];
            var obj = document.getElementById("parCtl" + paramId);
            var ctl = htObjType.get(paramId);
            console.log('getParamToServer obj=' + obj.value + ' ctl=' + ctl);
            var par_jo = new Object();
            par_jo.name = paramName;
            if (ctl == CONTROLTYPE_DOUBLE_DATEPICKER) {
                par_jo.value = document.getElementById("parCtl" + "from" + paramId).value + "@" + document.getElementById("parCtl" + "to" + paramId).value;
            } else if (ctl == CONTROLTYPE_SINGLE_DATEPICKER) {
                par_jo.value = obj.value;
            } else if (ctl == CONTROLTYPE_SINGLE_DATE_D) {
                par_jo.value = obj.value;
            } else if (ctl == CONTROLTYPE_SINGLE_DATE_M) {
                par_jo.value = obj.value;
            } else if (ctl == CONTROLTYPE_SINGLE_DATE_Y) {
                par_jo.value = obj.value;
            } else if (ctl == CONTROLTYPE_SINGLE_TEXT) {
                par_jo.value = obj.value;
            } else if (ctl == CONTROLTYPE_SINGLE_NUMERIC) {
                par_jo.value = obj.value;
            } else if (ctl == CONTROL_TYPE_SINGLE_SELECT) {
                par_jo.value = obj.value
            } else if (ctl == CONTROL_TYPE_MULTI_SELECT) {
                var arrValue = "x";
                if (obj.options.length > 0) {
                    for (var j = 0; j < obj.options.length; j++) {
                        if (obj.options[j].selected) {
                            var _value = obj.options[j].value;
                            if (arrValue == 'x') {
                                arrValue = _value;
                            } else {
                                arrValue = arrValue + "," + _value;
                            }
                        }
                    }
                    if (arrValue.startsWith(",")) {
                        arrValue = arrValue.substring(1);
                    }
                }
                if (arrValue == 'x') arrValue = "";
                par_jo.value = arrValue;
            }
            par_ar.push(par_jo);
        }
        return par_ar;
    }

    window.objReportParamField = objReportParamField;
}