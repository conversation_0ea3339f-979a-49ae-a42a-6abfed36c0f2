<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
	src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>


<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../chidaotuyen/CDT_Vattuhotro.js"></script>
<style>
.ui-jqgrid-htable,
.ui-jqgrid-btable,
.ui-jqgrid-pager,
.ui-jqgrid-view,
.ui-jqgrid-bdiv,
.ui-jqgrid-hdiv,
.ui-jqgrid-hbox,
.ui-jqgrid { max-width: 100% !important; width:100% !important; }
</style>

<div width="100%" id="divMain" style="border: 0px !important;">

	<div class="col-md-12 low-padding mgt-10">
		<table id="grdVattuhotro"></table>
		<div id="pager_grdVattuhotro"></div>
	</div>
	
	<div class="col-md-12 low-padding mgt5" style="text-align: center;">
		<button type="button" class="btn btn-sm btn-primary" id="btnLuu">
				<span class="glyphicon glyphicon-floppy-disk"></span> Lưu
			</button>
		<button type="button" class="btn btn-sm btn-primary" id="btnClose">
			<span class="glyphicon glyphicon-remove-circle"></span> Đóng
		</button>
	</div>
	<div id="popupId"></div>
</div>
<script>
	var opt = [];
	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	//var lang= "vi";
	console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
	var session_par = [];
	session_par[0] = hospital_id;
	session_par[1] = user_id;
	session_par[2] = user_type;
	session_par[3] = province_id;
	var table_name = '{table}';
	
	var mode = '{showMode}';
	if(mode=='dlg') {		
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	}

	initRest(uuid, "/vnpthis");

	var _opts = new Object();

	_opts._param = session_par;
	_opts._uuid = uuid;
	var DS = new Vattuhotro(_opts);
	DS.load(hospital_id);
</script>