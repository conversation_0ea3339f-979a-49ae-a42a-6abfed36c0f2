// L2PT-16575 start
// L2PT-30891 : thêm tham số isCheckedHDDTChitiet
function downloadXML(ds<PERSON><PERSON><PERSON>, dsCH, _config_hddt, mode, fileType, isCheckedHDDTChitiet) {
	var xmls = guiHDDTTheoDSPhieu(ds<PERSON><PERSON><PERSON>, dsCH, _config_hddt, mode, isCheckedHDDTChitiet);
	if (xmls.length > 0) {
		var ext = ".xml";
		if (dsCH.VPI_HDDT_VT == 2) {
			ext = ".json";
		}
		// L2PT-13782 start: file zip
		if (fileType == 0) {
			_date_ext = moment().format('YYYYMMDD');
			var zip = new JSZip();
			for (var k = 0; k < xmls.length; k++) {
				var _fileName = _date_ext + "_" + xmls[k].MAHOSOBENHAN + "_" + xmls[k].MANHOMPHIEUTHU + "_" + xmls[k].MAPHIEUTHU + ext;
				zip.file(_fileName, xmls[k].BODY);
			}
			zip.generateAsync({
				type : "blob"
			}).then(function(blob) {
				saveAs(blob, "XML_HDDT" + _date_ext + ".zip");
			});
		}
		// L2PT-13782 start: xuất file 
		else if (fileType == 1) {
			_date_ext = moment().format('YYYYMMDD');
			for (var k = 0; k < xmls.length; k++) {
				var content = xmls[k].BODY;
				if (content && content.length > 0) {
					var _fileName = _date_ext + "_" + xmls[k].MAHOSOBENHAN + "_" + xmls[k].MANHOMPHIEUTHU + "_" + xmls[k].MAPHIEUTHU + ext;
					download_async(xmls[k].BODY, _fileName, "application/xml", "btnXuatHDDTFILE_LINK");
				} else {
					DlgUtil.showMsg("Không kết xuất được XML hóa đơn");
					return;
				}
			}
		}
		// L2PT-13782 end
	} else {
		DlgUtil.showMsg("Không tìm thấy hóa đơn này!");
	}
}
download_async = function(content, filename, contentType, _rptExport) {
	if (!contentType)
		contentType = 'application/octet-stream';
	var a = document.getElementById(_rptExport);
	var blob = new Blob([ content ], {
		'type' : contentType
	});
	var url = window.URL.createObjectURL(blob);
	a.href = url;
	a.download = filename;
	a.click();
	window.URL.revokeObjectURL(a.href);
};
download = function(content, filename, contentType, _rptExport) {
	if (!contentType)
		contentType = 'application/octet-stream';
	var a = document.getElementById(_rptExport);
	var blob = new Blob([ content ], {
		'type' : contentType
	});
	a.href = window.URL.createObjectURL(blob);
	a.download = filename;
};
// L2PT-16575 end
function guiHDDTTheoDSPhieu(arrPhieu, dsCH, _config_hddt, mode, isCheckedHDDTChitiet) {
	if (!arrPhieu && arrPhieu.length == 0) {
		return "Chưa chọn hóa đơn";
	}
	var msgSendInv = "";
	var countSuc = 0;
	var countFai = 0;
	var arrErr = [];
	var arrMsgErr = [];
	var countErrMsg = 0;
	var arrXML = []; // L2PT-12841
	for (var i = 0; i < arrPhieu.length; i++) {
		// L2PT-24178 them gia tri cấu hình
		var guiTheoLo = 0;
		if (arrPhieu.length > 1) {
			guiTheoLo = 1;
		}
		var retSendInv = guiHDDTTheoPhieu(arrPhieu[i].PHIEUTHUID, dsCH, _config_hddt, arrPhieu[i].BENHNHANID, mode, isCheckedHDDTChitiet, guiTheoLo);
		// L2PT-24178 them gia tri cấu hình
		// L2PT-12841 start
		// L2PT-13782: XML bán thuốc
		if (mode == 1 || mode == 2) {
			var xmlHDDT = new Object();
			xmlHDDT.MAHOSOBENHAN = arrPhieu[i].MAHOSOBENHAN;
			xmlHDDT.MANHOMPHIEUTHU = arrPhieu[i].MANHOMPHIEUTHU;
			xmlHDDT.MAPHIEUTHU = arrPhieu[i].MAPHIEUTHU;
			xmlHDDT.BODY = retSendInv;
			arrXML.push(xmlHDDT);
		}
		// L2PT-12841 end
		if (retSendInv != "1") {
			var index = arrErr.indexOf(retSendInv);
			var billInfo = arrPhieu[i].MAHOSOBENHAN + ":" + arrPhieu[i].MANHOMPHIEUTHU + ":" + arrPhieu[i].MAPHIEUTHU;
			if (index == -1) {
				arrErr.push(retSendInv);
				arrMsgErr[countErrMsg] = retSendInv + ": " + billInfo;
				countErrMsg += 1;
			} else {
				arrMsgErr[index] += ";" + billInfo;
			}
			countFai += 1;
			//msgSendInv += "[" + arrPhieu[i].MAHOSOBENHAN + ":" + arrPhieu[i].MANHOMPHIEUTHU + ":" + arrPhieu[i].MAPHIEUTHU + "]: " + retSendInv + "<br>";
		} else {
			countSuc += 1;
		}
	}
	// L2PT-12841 start
	// L2PT-13782: XML bán thuốc
	if (mode == 1 || mode == 2) {
		return arrXML;
	}
	// L2PT-12841 end
	msgSendInv = arrMsgErr.join('<br>');
	msgSendInv = "Gửi thành công " + countSuc + " hóa đơn" + (countFai > 0 ? ("<br>Gửi lỗi " + countFai + " hóa đơn: <br>" + msgSendInv) : "");
	return msgSendInv;
}
// L2PT-28880 start: tach ham gui hoa don
function guiHDDTTheoPhieu(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode, isCheckedHDDTChitiet, guiTheoLo) { // L2PT-1436 thêm tham số mode
	try { // BVTM-4343
		// L2PT-1648 start
		//var VPI_KHOAPT_GUIHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_KHOAPT_GUIHDDT');
		if (mode == 1 || mode == 2) {
			console.log("xuat xml hddt");
		} else if (dsCH.VPI_KHOAPT_GUIHDDT == '1') {
			// L2PT-18357 start
			var objTPhieuThu = {
				"PHIEUTHUID" : phieuthuid_hddt,
				"KIEU" : "1"
			};
			var tPhieuThu = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.TT.LT', JSON.stringify(objTPhieuThu));
			// L2PT-18357 end
			if (tPhieuThu && tPhieuThu.length > 0) {
				var rPhieuThu = tPhieuThu[0];
				if (rPhieuThu.DADUYET == '1') {
					return "Đã khóa không thể gửi HDDT";
				}
			} else {
				return "Không tìm thấy hóa đơn này";
			}
		}
		// L2PT-1648 end
		// L2PT-912 start	
		if (dsCH.VPI_HDDT_VT == 1) {
			return guiHDDTTheoPhieuVT(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
		}
		// L2PT-912 end
		// L2PT-14600 start
		if (dsCH.VPI_HDDT_VT == 2) {
			return guiHDDTTheoPhieuMISA(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
		}
		// L2PT-14600 end
		// L2PT-28287 start
		var kiemtraTK = layTKHDDT(phieuthuid_hddt, dsCH, _config_hddt);
		if (kiemtraTK != "1") {
			return kiemtraTK;
		}
		// L2PT-28287 end
		// L2PT-12841 start
		// L2PT-13782: XML bán thuốc
		if (mode == 1 || mode == 2) {
			console.log("xuat xml hddt");
		} else {
			// L2PT-24425 start: gui hddt bán thuốc
			var kq_ttbn = "";
			if (mode == 3) {
				kq_ttbn = daythongtinthuoc(phieuthuid_hddt, dsCH, _benhnhanid);
			} else {
				kq_ttbn = daythongtinbn(phieuthuid_hddt, dsCH, _benhnhanid);
			}
			if (kq_ttbn != "1") {
				return kq_ttbn;
			}
			// L2PT-24425 end
		}
		// L2PT-12841 end
		var sql_par = [];
		// L2PT-4262 start
		if (dsCH.VPI_HDDT_CHITIET == 1) {
			sql_par = [ phieuthuid_hddt, isCheckedHDDTChitiet ];
		}
		// L2PT-4262 end
		else {
			sql_par = [ phieuthuid_hddt ];
		}
		var fl = "";
		// L2PT-13782 start : xuất xml bán thuốc
		if (mode == 2 || mode == 3) { // L2PT-24425 start: gui hddt bán thuốc
			// L2PT-43777 start
			if (dsCH.VPI_HDDT_MTT == '1') {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.INVOICES.MTT", sql_par.join('$'));
			} else {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.INVOICES.BT", sql_par.join('$'));
			}
			// L2PT-43777 end
		}
		// L2PT-13782 end
		// L2PT-119019 start
		else if (dsCH.VPI_FN_IMPORT_HDDT == 5) {
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.INVOICES.MTT.VP", sql_par.join('$'));
		}
		// L2PT-119019 end
		else if (dsCH.INVOICES_CONTENT_TYPE == 1) {
			// lay XML noi dung gom: tien dong chi tra BHYT va Tien tung nhom dich vu BN tra
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_IP_INVOITYPE1", sql_par.join('$'));
		} else if (dsCH.INVOICES_CONTENT_TYPE == 2) {
			// lay XML noi dung gom 2 dong tien BH va tien ngoai BH chi tra hoac cau hinh theo tien tung nhom ke toan BN tra
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_IP_INVOITYPE2", sql_par.join('$'));
		} else if (dsCH.INVOICES_CONTENT_TYPE == 3) {
			// gộp tien chi tra thanh 1 dong: thanh toan chi phi KCB --DKLSN
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_IP_INVOITYPE3", sql_par.join('$'));
		} else {
			// L2PT-4262 start
			if (dsCH.VPI_HDDT_CHITIET == 1) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES", sql_par.join('$'));
			}
			// L2PT-4262 end
			// L2PT-18916 start
			else if (dsCH.INVOICE_XML_TYPE == '1') { // 220422
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IMPORT.INVOICES", sql_par.join('$'));
			}
			// L2PT-18916 end
			else if (dsCH._hospital_id == 957) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOINHI957", sql_par.join('$'));
			} else if (dsCH._hospital_id == 965) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES965", sql_par.join('$'));
			} else if (dsCH._hospital_id == 902) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES902", sql_par.join('$'));
			} else if (dsCH._hospital_id == 1007) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1007", sql_par.join('$'));
			} else if (dsCH._hospital_id == 919) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES919", sql_par.join('$'));
			} else if (dsCH._hospital_id == 944) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES944", sql_par.join('$'));
			} else if (dsCH._hospital_id == 951) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES951", sql_par.join('$'));
			} else if (dsCH._hospital_id == 1059) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1059", sql_par.join('$'));
			} else if (dsCH._hospital_id == 1001) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1001", sql_par.join('$'));
			} else if (dsCH._hospital_id == 1108) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1108", sql_par.join('$'));
			} else if (dsCH._hospital_id == 982) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES982", sql_par.join('$'));
			} else if (dsCH._hospital_id == 1091) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1091", sql_par.join('$'));
			} else if (dsCH._hospital_id == 9364) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES9364", sql_par.join('$'));
			} else if (dsCH._hospital_id == 993) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES993", sql_par.join('$'));
			} else if (dsCH.INVOICE_XML_IMPORT == '-1' && _hospital_id == 1111) { // L2PT-16803
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1111", sql_par.join('$'));
			} else if (dsCH._hospital_id == 915) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES915", sql_par.join('$'));
				var ap = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
				dsCH.INVOICES_WS_USER_ACC = ap[0].INVOICES_USER;
				dsCH.INVOICES_WS_PWD_ACC = ap[0].INVOICES_PASS;
				if (ap[0].INVOICES_PASS == "" || ap[0].INVOICES_USER == "") {
					return "chưa có acc/pass hóa đơn điện tử!";
				}
			} else {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IMPORT.INVOICES", sql_par.join('$'));
			}
		}
		// L2PT-12841 start
		// L2PT-13782: XML bán thuốc
		if (mode == 1 || mode == 2) {
			return fl;
		}
		// L2PT-13782 end
		// L2PT-12841 end
		//var dsCH.INVOICES_HAI_QUYENHD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_HAI_QUYENHD');
		// them ghi logs day HDDT: bo sung trang thai 2 la da gui tu HIS
		var _par_flag_sendhd = [ "2", "", "", "", phieuthuid_hddt ];
		var _return_flag_sendhd = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par_flag_sendhd.join('$'));
		var ret = "";
		// L2PT-44489 start
		if (dsCH.VPI_SERIAL_PATTERN_HDDT == '1') { //duonghn 230619
			var objTtPt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', phieuthuid_hddt);
			if (objTtPt && objTtPt.length > 0) {
				var mauSo = objTtPt[0].MAU_SO;
				var kyHieu = objTtPt[0].KY_HIEU;
				if (typeof mauSo == 'undefined' || typeof kyHieu == 'undefined' || mauSo == "" || kyHieu == "") {
					DlgUtil.showMsg("Sổ hóa đơn chưa có mẫu số hoặc ký hiệu");
					return;
				}
				dsCH.INVOICES_WS_PATTERN = mauSo; //duonghn 230619
				dsCH.INVOICES_WS_SERIAL = kyHieu; //duonghn 230619
			} else {
				DlgUtil.showMsg("Lỗi lấy thông tin hóa đơn");
				return;
			}
		}
		// L2PT-44489 end
		// L2PT-24350 start
		//var dsCH.VPI_FN_IMPORT_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_FN_IMPORT_HDDT');
		// L2PT-24425 start: gui hddt bán thuốc
		if (mode == 3) {
			// L2PT-43777 start
			var ret = "";
			if (dsCH.VPI_HDDT_MTT == '1') {
				if (dsCH.INVOICES_WS_PATTERN_BT && dsCH.INVOICES_WS_PATTERN_BT != 0 && dsCH.INVOICES_WS_SERIAL_BT && dsCH.INVOICES_WS_SERIAL_BT != 0) {
					ret = ajaxSvc.InvoicesWS.importHoaDonMTT(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
							dsCH.INVOICES_WS_PATTERN_BT, dsCH.INVOICES_WS_SERIAL_BT);
				} else {
					ret = ajaxSvc.InvoicesWS.importHoaDonMTT(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
							dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
				}
			} else {
				if (dsCH.INVOICES_WS_PATTERN_BT && dsCH.INVOICES_WS_PATTERN_BT != 0 && dsCH.INVOICES_WS_SERIAL_BT && dsCH.INVOICES_WS_SERIAL_BT != 0) {
					//L2PT-96165
					if (dsCH.VPI_FN_IMPORT_HDDT == 2) {
						ret = ajaxSvc.InvoicesWS.importInvByPattern(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
								dsCH.INVOICES_WS_PATTERN_BT, dsCH.INVOICES_WS_SERIAL_BT);
					} else
						ret = ajaxSvc.InvoicesWS.importHoaDon(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
								dsCH.INVOICES_WS_PATTERN_BT, dsCH.INVOICES_WS_SERIAL_BT);
				} else {
					//L2PT-96165
					if (dsCH.VPI_FN_IMPORT_HDDT == 2) {
						ret = ajaxSvc.InvoicesWS.importInvByPattern(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
								dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
					} else
						ret = ajaxSvc.InvoicesWS.importHoaDon(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
								dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
				}
			}
			// L2PT-43777 end
		} else
		// L2PT-24425 end
		if (dsCH.VPI_FN_IMPORT_HDDT == 1) {
			ret = ajaxSvc.InvoicesWS.importInvWithPattern(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD, dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
		} else
		// L2PT-24350 end
		// L2PT-17809 start
		if (dsCH.VPI_FN_IMPORT_HDDT == 2) {
			ret = ajaxSvc.InvoicesWS.importInvByPattern(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
					dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
		} else
		// L2PT-17809 end
		// L2PT-33584 : thêm cấu hình gửi HDDT không phát hành
		if (dsCH._hospital_id == 951 || dsCH._hospital_id == 9364 || dsCH.VPI_FN_IMPORT_HDDT == 3) {
			ret = ajaxSvc.InvoicesWS.importHoaDon_notPH(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
					dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
		} else
		// L2PT-32146 : thêm cấu hình gửi HDDT token
		if (dsCH._hospital_id == 1059 || dsCH._hospital_id == 1001 || dsCH.VPI_FN_IMPORT_HDDT == 4) { // BV su dung token
			ret = ajaxSvc.InvoicesWS.importHoaDon_token(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
		}
		// L2PT-119019 start
		else if (dsCH.VPI_FN_IMPORT_HDDT == 5) {
			ret = ajaxSvc.InvoicesWS.importHoaDonMTT(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
		}
		// L2PT-119019 end
		else if (dsCH.INVOICES_HAI_QUYENHD == 1) {
			// case cho BV SNPYN
			//var dsCH.INVOICES_BH_PATTERN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_BH_PATTERN');
			//var dsCH.INVOICES_BH_SERIAL = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_BH_SERIAL');
			//var dsCH.INVOICES_DV_PATTERN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_DV_PATTERN');
			//var dsCH.INVOICES_DV_SERIAL = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_DV_SERIAL');
			var _flag_phieuhd = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.INVOI.FLAGHD", sql_par.join('$')); // check phieu co phai la hoa don hay ko: 1 la hoa don, khac 1 ko phai hoa don
			if (_flag_phieuhd != 1) {
				return "Phiếu không phải là hóa đơn!";
			}
			var _flag_phieubh_invoi = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.INVOI.FLAGBH1", sql_par.join('$')); // check phieu la hoa don thu khac: 4 la thu khac, khac 4 ko phai thu khac
			// L2PT-28880 start: sua serial va pattern voi truong hop dung 2 quyen hddt
			if (_flag_phieubh_invoi == 4) {
				dsCH.INVOICES_WS_PATTERN = dsCH.INVOICES_DV_PATTERN;
				dsCH.INVOICES_WS_SERIAL = dsCH.INVOICES_DV_SERIAL;
			} else {
				dsCH.INVOICES_WS_PATTERN = dsCH.INVOICES_BH_PATTERN;
				dsCH.INVOICES_WS_SERIAL = dsCH.INVOICES_BH_SERIAL;
			}
			ret = ajaxSvc.InvoicesWS.importHoaDon(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
					dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
			// L2PT-28880 end: sua serial va pattern voi truong hop dung 2 quyen hddt
		} else {
			ret = ajaxSvc.InvoicesWS.importHoaDon(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
					dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL);
		}
		if (ret == "ERR:13") {
			var ret1 = ajaxSvc.InvoicesWS.listInvByCusFkey(phieuthuid_hddt, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
			if (ret1 == "" || ret1.toUpperCase().includes("ERR")) {
				return "Hóa đơn đã phát hành cập nhật số hóa đơn nhưng không lấy được thông tin để cập nhật";
			}
			var obj = convertXml2JSon(ret1);
			obj = JSON.parse(obj);
			var invoi_nb = obj.Data.Item.invNum;
			var _par = [ "1", dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL, parseInt(invoi_nb), phieuthuid_hddt ];
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
			return "Hóa đơn đã phát hành cập nhật số hóa đơn thành công";
		} else if (ret.substring(0, 3) != "OK:") {
			// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
			var err_sendInv = thongbao_loi('HDDT_PHAT_HANH', ret);
			// L2PT-37815 end
			return err_sendInv;
		} else {
			var _par = [ "1", dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL, ret.split("_")[1], phieuthuid_hddt ];
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
			// L2PT-14008 start duyệt phiếu
			if (dsCH.VPI_KHOA_HDDT == 1 && _return == 1) {
				var _result_duyet = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI03.KHOA.PHIEUTHU', phieuthuid_hddt + '$' + 1);
				console.log(_result_duyet);
			}
			// L2PT-14008 end
			//var dsCH.VPI_VIEW_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_VIEW_HDDT');
			// L2PT-24178 them gia tri cấu hình
			if (!guiTheoLo) {
				guiTheoLo = 0;
			}
			// L2PT-24178 them gia tri cấu hình
			if (dsCH.VPI_VIEW_HDDT == 2 || (dsCH.VPI_VIEW_HDDT == 1 && guiTheoLo == 0)) { // L2PT-24178 them gia tri cấu hình
				// L2PT-19351 start
				// 220422 start
				setTimeout(function() {
					_inHDDTTheoPhieu(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
				}, dsCH.VPI_VIEW_HDDT_DELAY * 1000);
				// 220422 end
				// L2PT-19351 end
			}
			return "1";
		}
	} catch (err) { // BVTM-4343 start
		return "Có lỗi xảy ra, chưa gửi được HDDT: " + err;
	}
}
// BVTM-4343 end
// L2PT-28880 end
// L2PT-28955 start
function arHDDT(opt) { // L2PT-35782 : bỏ tham số dsCH
	var dsCH = opt.dsCH; // L2PT-35782
	var _phieuthuid_hddt = opt._phieuthuid_hddt;
	var _nguoilapphieu = opt._nguoilapphieu;
	var _datra = opt._datra;
	var _user_id = opt._user_id;
	var modeAR = opt.MODEAR; // L2PT-35782
	_datra = parseFloat(_datra); //L2PT-1878
	if (_datra != 0) {
		if (!_user_id || _user_id == 0) {
			DlgUtil.showMsg("Chưa đăng nhập");
			return false;
		}
		if (dsCH.VPI_QUYEN_GUIHDDT == 1 && _user_id != _nguoilapphieu) {
			DlgUtil.showMsg("Chỉ người lập phiếu mới có thể gửi HĐĐT");
			return false;
		}
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
			"name" : "[0]",
			"value" : _phieuthuid_hddt
		} ]);
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			var check = rows[0]["SYNC_FLAG"];
			if (check == 1) {
				DlgUtil.showMsg("Hóa đơn đã được gửi, không thể dùng để thay thế/ điều chỉnh hóa đơn khác");
			} else {
				if (_hospital_id == 915) {
					var ap = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
					dsCH.INVOICES_WS_USER_ACC = ap[0].INVOICES_USER;
					dsCH.INVOICES_WS_PWD_ACC = ap[0].INVOICES_PASS;
					if (ap[0].INVOICES_PASS == "" || ap[0].INVOICES_USER == "") {
						DlgUtil.showMsg("chưa có acc/pass hóa đơn điện tử!");
						return false;
					}
				} else if (dsCH.INVOICES_HAI_QUYENHD == 1) {
					// case cho BV SNPYN
					var _flag_phieuhd = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.INVOI.FLAGHD", _phieuthuid_hddt); // check phieu co phai la hoa don hay ko: 1 la hoa don, khac 1 ko phai hoa don
					if (_flag_phieuhd != 1) {
						DlgUtil.showMsg("Phiếu không phải là hóa đơn!");
						return false;
					}
					var _flag_phieubh_invoi = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.INVOI.FLAGBH1", _phieuthuid_hddt); // check phieu la hoa don thu khac: 4 la thu khac, khac 4 ko phai thu khac
					// L2PT-28880 start: sua serial va pattern voi truong hop dung 2 quyen hddt
					if (_flag_phieubh_invoi == 4) {
						dsCH.INVOICES_WS_PATTERN = dsCH.INVOICES_DV_PATTERN;
						dsCH.INVOICES_WS_SERIAL = dsCH.INVOICES_DV_SERIAL;
					} else {
						dsCH.INVOICES_WS_PATTERN = dsCH.INVOICES_BH_PATTERN;
						dsCH.INVOICES_WS_SERIAL = dsCH.INVOICES_BH_SERIAL;
					}
				}
				// L2PT-84525 start
				var ma_chuc_nang = '';
				if (modeAR == 1) { // L2PT-28955 fix 221115
					var fl = opt.xmlDieuChinh;
					var ret = ajaxSvc.InvoicesWS.AdjustInvoiceAction(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
							dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL, opt.PHIEUTHUID_CU); //duonghn fix 230421
					ma_chuc_nang = 'HDDT_DIEU_CHINH';
				} else if (modeAR == 2) { // L2PT-28955 fix 221115
					// L2PT-30618 start
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.AR.INVOICES.1", _phieuthuid_hddt + "$" + JSON.stringify({
						LOAI : modeAR + "",
						LOAIDIEUCHINH : opt.LOAIDIEUCHINH + "" // L2PT-35782
					}));
					// L2PT-30618 end
					var ret = ajaxSvc.InvoicesWS.ReplaceInvoiceAction(fl, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD,
							dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL, opt.PHIEUTHUID_CU); //duonghn fix 230421
					ma_chuc_nang = 'HDDT_THAY_THE';
				}
				if (ret.substring(0, 3) != "OK:") {
					// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
					DlgUtil.showMsg(thongbao_loi(ma_chuc_nang, ret));
					// L2PT-37815 end
					return false;
				} else {
					// L2PT-22748 start
					//var _par = [ "1", dsCH.INVOICES_WS_PATTERN, dsCH.INVOICES_WS_SERIAL, ret.split("_")[1], _phieuthuid_hddt ]; // duonghn fix 230201
					//var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
					var objUpdHDDT = new Object();
					objUpdHDDT.SYNC_FLAG = "1";
					objUpdHDDT.PHIEUTHUID = _phieuthuid_hddt;
					objUpdHDDT.PATTERN = dsCH.INVOICES_WS_PATTERN;
					objUpdHDDT.SERIAL = dsCH.INVOICES_WS_SERIAL;
					objUpdHDDT.NUMBER = ret.split("_")[1];
					objUpdHDDT.TRANSACTIONID_VT = "";
					objUpdHDDT.RESERVATIONCODE_VT = "";
					objUpdHDDT.PHIEUTHUID_CU = opt.PHIEUTHUID_CU; // L2PT-84525: update theo phieuthuid cu
					var retUdpHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.HDDT.UPDATE.UGR", JSON.stringify(objUpdHDDT));
					// L2PT-22748  end
					if (dsCH.VPI_KHOA_HDDT == 1 && _return == 1) {
						var _result_duyet = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI03.KHOA.PHIEUTHU', _phieuthuid_hddt + '$' + 1); // duonghn fix 230201
						console.log(_result_duyet);
					}
					if (dsCH.VPI_VIEW_HDDT == 2 || (dsCH.VPI_VIEW_HDDT == 1 && guiTheoLo == 0)) {
						setTimeout(function() {
							_inHDDTTheoPhieu(_phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, modeAR); // duonghn fix 230201
						}, dsCH.VPI_VIEW_HDDT_DELAY * 1000);
					}
					return true;
				}
				// L2PT-84525 end
			}
		}
	}
}
// L2PT-28955 end
// L2PT-912 start
function guiHDDTTheoPhieuVT(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode, type) { // 220422
	var xmlInv = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_DAY_HDDT_29180", phieuthuid_hddt);
	if (xmlInv && xmlInv.length > 0) {
		// 220422
		if (mode == 1 || mode == 2) {
			return xmlInv;
		}
		// 220422
		var x2js = new X2JS({
			arrayAccessFormPaths : [ "root.itemInfo", "root.metadata", "root.payments", "root.taxBreakdowns" ]
		});
		var objInv = x2js.xml_str2json(xmlInv);
		// L2PT-108921 L2PT-25725 start
		if (dsCH.VPI_HDDT_VIETTEL_V2 == '1') {
			formatObj(objInv, dsCH.INVOICES_VT_FMTNBR);
		}
		var objSenInv = objInv.root;
		var jsonSendInv = JSON.stringify(objSenInv);
		// L2PT-108921 L2PT-25725 end
		// them ghi logs day HDDT: bo sung trang thai 2 la da gui tu HIS
		var _par_flag_sendhd_vt = [ "2", "", "", "", phieuthuid_hddt ];
		var _return_flag_sendhd_vt = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par_flag_sendhd_vt.join('$'));
		// L2PT-108921 L2PT-22748 start
		var retHDDTviettel = "";
		if (type == 2 || dsCH.VPI_DAY_HDDT_PH == '1') {
			retHDDTviettel = ajaxSvc.InvoicesWS.createOrUpdateInvoiceDraft(jsonSendInv);
		} else {
			// L2PT-129800 start
			if (mode == '3' && dsCH.INVOICES_WS_TAX_CODE_NT && dsCH.INVOICES_WS_TAX_CODE_NT != '0') {
				retHDDTviettel = ajaxSvc.InvoicesWS.createInvoice(jsonSendInv, 'INVOICES_WS_TAX_CODE_NT');
			} else {
				retHDDTviettel = ajaxSvc.InvoicesWS.createInvoice(jsonSendInv);
			}
			// L2PT-129800 end
		}
		// L2PT-108921 L2PT-22748 end
		if (retHDDTviettel.includes("ERR_CREATTE_REQ")) {
			return 'Lỗi gửi hóa đơn điện tử';
		} else {
			var objRetHDDTviettel = JSON.parse(retHDDTviettel);
			if (objRetHDDTviettel.errorCode == null) {
				var resultHDDTviettel = objRetHDDTviettel.result;
				// L2PT-108921 start
				if (type == 2 || dsCH.VPI_DAY_HDDT_PH == '1') {
					updateHDDT("1", "", "", "", phieuthuid_hddt, "", "");
				} else {
					updateHDDT("1", dsCH.INVOICES_WS_PATTERN, resultHDDTviettel.invoiceNo.substr(0, 6), resultHDDTviettel.invoiceNo.substr(6), phieuthuid_hddt, resultHDDTviettel.transactionID,
							resultHDDTviettel.reservationCode);
				}
				// L2PT-108921 end
				// L2PT-5786 start
				//var VPI_VIEW_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_VIEW_HDDT');
				if (dsCH.VPI_VIEW_HDDT == 1) {
					setTimeout(function() {
						_inHDDTTheoPhieuVT(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
					}, dsCH.VPI_VIEW_HDDT_DELAY * 1000);
				}
				// L2PT-5786 end
				return '1';
			} else {
				return objRetHDDTviettel.description + " ( " + objRetHDDTviettel.errorCode + " ) ";
			}
		}
	} else {
		return 'Không tìm thấy dữ liệu hóa đơn này';
	}
}
// L2PT-912 end
// L2PT-108921 L2PT-25725 start
function formatObj(obj, arrAtt) {
	if (typeof obj === 'object') {
		if (Array.isArray(obj)) {
			for (var i = 0; i < obj.length; i++) {
				formatObj(obj[i], arrAtt);
			}
		} else {
			formatObjNaA(obj, arrAtt);
		}
	}
}
function formatObjNaA(obj, arrAtt) {
	var keys = Object.keys(obj);
	for (var j = 0; j < keys.length; j++) {
		var key = keys[j];
		if (obj[key] == null || obj[key] == "") {
			continue;
		} else if (typeof obj[key] === 'object') {
			formatObj(obj[key], arrAtt);
		} else if (arrAtt.indexOf(key) != -1) {
			var newVal = obj[key];
			if (isNaN(obj[key])) {
				newVal = (/true/i).test(obj[key]);
			} else {
				newVal = parseFloat(obj[key]); // L2PT-33849
			}
			obj[key] = newVal;
		}
	}
}
// L2PT-108921 L2PT-25725 end
// L2PT-14600 start
function guiHDDTTheoPhieuMISA(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode) {
	var arrInv = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.MISA.HDDT", phieuthuid_hddt);
	if (arrInv && arrInv.length > 0) {
		var objInv = arrInv[0];
		// L2PT-17896 start
		if (dsCH.INVOICE_XML_TYPE == '1') {
			objInv.TaxRateInfo = JSON.parse(objInv.TaxRateInfo);
		}
		// L2PT-17896 end
		var arrInvDet = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.MISA.ITEM", phieuthuid_hddt);
		if (arrInvDet && arrInvDet.length > 0) {
			objInv.OriginalInvoiceDetail = arrInvDet;
			// L2PT-119965 start
			// L2PT-14600 start
			if (dsCH.INVOICES_MISA_FMTNBR !== null && typeof dsCH.INVOICES_MISA_FMTNBR === 'object' && Array.isArray(dsCH.INVOICES_MISA_FMTNBR)) {
				console.log(dsCH.INVOICES_MISA_FMTNBR);
			} else {
				return 'Kiểm tra cấu hình INVOICES_MISA_FMTNBR';
			}
			// L2PT-14600 end
			// L2PT-119965 end
			if (dsCH.INVOICES_MISA_OPTIONUSER !== null && typeof dsCH.INVOICES_MISA_OPTIONUSER === 'object' && !Array.isArray(dsCH.INVOICES_MISA_OPTIONUSER)) {
				objInv.OptionUserDefined = dsCH.INVOICES_MISA_OPTIONUSER;
				var data = [ objInv ];
				parseObj(data, dsCH.INVOICES_MISA_FMTNBR);
				// L2PT-105772 L2PT-104997 start
				if (dsCH.INVOICES_TYPE == "MISA_HSM") {
					param = {
						"type" : "0",
						"data" : [ {
							RefID : data[0].RefID,
							OriginalInvoiceData : data[0]
						} ]
					};
				} else {
					param = {
						"type" : "0",
						"data" : data
					};
				}
				// L2PT-105772 L2PT-104997 end
				// 220422
				if (mode == 1 || mode == 2) {
					return JSON.stringify(param);
				}
				// 220422
				// them ghi logs day HDDT: bo sung trang thai 2 la da gui tu HIS
				var _par_flag_sendhd_ms = [ "2", "", "", "", phieuthuid_hddt ];
				var _return_flag_sendhd_ms = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par_flag_sendhd_ms.join('$'));
				var retInvMISA = ajaxSvc.InvoicesWS.issueInvoices(param);
				try {
					var retObj = JSON.parse(retInvMISA);
					console.log(retObj);
					if (retObj.errorCode == "0") {
						var arrInvObj = JSON.parse(retObj.data);
						console.log(arrInvObj);
						// L2PT-17896 start
						if (dsCH.INVOICE_XML_TYPE == '1') {
							return '1';
						} else
						// L2PT-17896 end	
						if (arrInvObj && arrInvObj.length > 0) {
							var dataInvObj = arrInvObj[0];
							updateHDDT("1", dataInvObj.TemplateCode, dataInvObj.InvoiceSeries, dataInvObj.InvoiceNumber, phieuthuid_hddt, dataInvObj.TransactionID, dataInvObj.RefID);
							// L2PT-5786 start
							//var VPI_VIEW_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_VIEW_HDDT');
							if (dsCH.VPI_VIEW_HDDT == 1) {
								setTimeout(function() {
									_inHDDTTheoPhieuMISA(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
								}, dsCH.VPI_VIEW_HDDT_DELAY * 1000);
							} else {
								return 'Phát hành hóa đơn điện tử MISA thành công';
							}
							// L2PT-5786 end
							return '1';
						} else {
							return 'Không lấy được dữ liệu hddt để cập nhật';
						}
					} else {
						DlgUtil.showMsg("Mã lỗi " + retObj.errorCode + ": " + retObj.message);
					}
				} catch (e) {
					console.log(e);
				}
			} else {
				// L2PT-119965 start
				return 'Kiểm tra cấu hình INVOICES_MISA_OPTIONUSER';
				// L2PT-119965 end
			}
		} else {
			return 'Không lấy được dữ liệu chi tiết hóa đơn này';
		}
	} else {
		return 'Không tìm thấy dữ liệu hóa đơn này';
	}
}
// L2PT-14600 end
function parseObj(arrObj, arrAtt) {
	for (var i = 0; i < arrObj.length; i++) {
		var keys = Object.keys(arrObj[i]);
		for (var j = 0; j < keys.length; j++) {
			var key = keys[j];
			if (arrObj[i][key] == null || arrObj[i][key] == "") {
				continue;
			} else if (typeof arrObj[i][key] === 'object' && Array.isArray(arrObj[i][key])) {
				parseObj(arrObj[i][key], arrAtt);
			} else if (arrAtt.indexOf(key) != -1) {
				var newVal = parseFloat(arrObj[i][key]);
				if (isNaN(arrObj[i][key])) {
					newVal = (/true/i).test(arrObj[i][key]);
				}
				arrObj[i][key] = newVal;
			}
		}
	}
}
function updateHDDT(status, invPattern, invSerial, invNumber, phieuthuid_hddt, transactionID, refID) {
	var objUdpHDDT = new Object();
	objUdpHDDT.SYNC_FLAG = "1";
	objUdpHDDT.PATTERN = invPattern;
	objUdpHDDT.SERIAL = invSerial;
	objUdpHDDT.NUMBER = invNumber;
	objUdpHDDT.PHIEUTHUID = phieuthuid_hddt;
	objUdpHDDT.TRANSACTIONID_VT = transactionID;
	objUdpHDDT.RESERVATIONCODE_VT = refID;
	var retUdpHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.HDDT.UPDATE.UGR", JSON.stringify(objUdpHDDT));
	if (retUdpHDDT != 1) {
		return 'Cập nhật trạng thái gửi không thành công'; // L2PT-108921
	}
	return '1'; // L2PT-108921
}
// L2PT-14600 end
// L2PT-28287 start
function daythongtinbn(_phieuthu_cushd_id, dsCH, _benhnhanid) {
	var data_bn_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.TTBN.HDDT", _benhnhanid);
	if (data_bn_ar && data_bn_ar.length > 0) {
		_benhnhan = data_bn_ar[0];
		var objCusData = new Object();
		var objCustomers = new Object();
		var objCustomer = new Object();
		if (dsCH.VPI_TENKH_HDDT == '1') {
			objCustomer.Buyer = _benhnhan.TENBENHNHAN;
			objCustomer.Name = _benhnhan.TENCONGTYBN;
		} else {
			objCustomer.Name = _benhnhan.TENBENHNHAN;
		}
		if (dsCH.VPI_HDDT_MA_KH == '0') {
			objCustomer.Code = _benhnhan.MABENHNHAN;
		} else if (dsCH.VPI_HDDT_MA_KH == '1') {
			objCustomer.Code = _benhnhan.MAHOSOBENHAN;
		} else {
			var _cuscode = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.CUSCODE.HD', _phieuthu_cushd_id);
			objCustomer.Code = _cuscode;
		}
		objCustomer.TaxCode = _benhnhan.MASOTHUE_CTYBN;
		objCustomer.Address = _benhnhan.DIACHI;
		objCustomer.BankName = _benhnhan.TEN_NGANHANG;
		objCustomer.BankAccountName = "";
		objCustomer.BankNumber = _benhnhan.SOTAIKHOAN;
		objCustomer.Email = _benhnhan.EMAIL_CTYBN;
		objCustomer.Fax = "";
		objCustomer.Phone = _benhnhan.SDTBENHNHAN;
		objCustomer.ContactPerson = _benhnhan.TEN_NGUOITHAN;
		objCustomer.RepresentPerson = "";
		objCustomer.CusType = '0';
		objCustomers.Customer = objCustomer;
		objCusData.Customers = objCustomers;
		var str = JSON.stringify(objCusData);
		str = convertJSon2XML(str);
		var result = ajaxSvc.InvoicesWS.updateCus(str, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
		if (result == "1") {
			return "1";
		} else if (result == "-1") {
			return "Tài khoản đăng nhập sai hoặc không có quyền";
		} else if (result == "-2") {
			return "Không cập nhật được dữ liệu khách hàng";
		} else if (result == "-3") {
			return "Dữ liệu khách hàng không đúng định dạng";
		} else if (result == "-5") {
			return "Có khách hàng đã tồn tại";
		} else {
			return "Lỗi đẩy thông tin BN lên hệ thống HĐĐT";
			console.log(result);
		}
	} else {
		return "Không lấy được thông tin BN";
	}
}
// L2PT-24425 start: day thong tin hoa don thuoc len he thong hdddt
function daythongtinthuoc(_phieuthu_cushd_id, dsCH, _benhnhanid) {
	var objCusData = new Object();
	var objCustomers = new Object();
	var objCustomer = new Object();
	var data_pt_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T012.TT.PTT", _phieuthu_cushd_id);
	if (data_pt_ar && data_pt_ar.length > 0) {
		_donthuoc = data_pt_ar[0]
		objCustomer.Name = _donthuoc.TENBENHNHAN;;
		objCustomer.Code = _donthuoc.SOPHIEU;;
		objCustomer.TaxCode = "";
		objCustomer.Address = _donthuoc.DIACHI;
		objCustomer.BankAccountName = "";
		objCustomer.BankNumber = "";
		objCustomer.Email = _donthuoc.EMAIL_CTYBN;
	} else {
		return "Không lấy được thông tin đơn thuốc";
	}
	objCustomer.Fax = "";
	objCustomer.Phone = "";
	objCustomer.ContactPerson = "";
	objCustomer.RepresentPerson = "";
	objCustomer.CusType = '0';
	objCustomers.Customer = objCustomer;
	objCusData.Customers = objCustomers;
	var str = JSON.stringify(objCusData);
	str = convertJSon2XML(str);
	var result = ajaxSvc.InvoicesWS.updateCus(str, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	if (result == "1") {
		return "1";
	} else if (result == "-1") {
		return "Tài khoản đăng nhập sai hoặc không có quyền";
	} else if (result == "-2") {
		return "Không cập nhật được dữ liệu khách hàng";
	} else if (result == "-3") {
		return "Dữ liệu khách hàng không đúng định dạng";
	} else if (result == "-5") {
		return "Có khách hàng đã tồn tại";
	} else {
		return "Lỗi đẩy thông tin BN lên hệ thống HĐĐT";
		console.log(result);
	}
}
// L2PT-24425 end
function _inHDDTTheoPhieu(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode) {
	// BVTM-7213 start
	if (dsCH.VPI_XN_GUILAI_HDDT == '1') {
		var checkSyncInv = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
			"name" : "[0]",
			"value" : phieuthuid_hddt
		} ]);
		var rows = [];
		try {
			rows = JSON.parse(checkSyncInv);
		} catch (err) {
			console.log(err);
		}
		if (rows && rows.length > 0) {
			var check = rows[0]["SYNC_FLAG"];
			if (check == 2) {
				DlgUtil.showConfirm("Trạng thái HĐĐT chưa được cập nhật. Xác nhận phát hành lại HĐĐT ?", function(flag) {
					if (flag) {
						guiHDDTTheoPhieu(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
					}
				});
				return;
			}
		}
	}
	// BVTM-7213 end
	// L2PT-912 start
	if (dsCH.VPI_HDDT_VT == 1) {
		_inHDDTTheoPhieuVT(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
		return;
	}
	// L2PT-912 end
	// L2PT-14600 start
	if (dsCH.VPI_HDDT_VT == 2) {
		_inHDDTTheoPhieuMISA(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode);
		return;
	}
	// L2PT-14600 end
	// L2PT-28287 start
	var kiemtraTK = layTKHDDT(phieuthuid_hddt, dsCH, _config_hddt);
	if (!kiemtraTK) {
		return -4;
	}
	// L2PT-28287 end
	var result = '';
	// L2PT-33584 : thêm cấu hình gửi HDDT không phát hành
	if (dsCH._hospital_id == 951 || dsCH._hospital_id == 9364 || dsCH.VPI_FN_IMPORT_HDDT == 3) {
		result = ajaxSvc.InvoicesWS.viewHoaDon_notPH(phieuthuid_hddt, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	} else {
		result = ajaxSvc.InvoicesWS.viewHoaDon(phieuthuid_hddt, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	}
	if (result == "" || result.includes("ERR:")) {
		// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
		DlgUtil.showMsg(thongbao_loi('HDDT_TAI_VE', ret));
		// L2PT-37815 end
	} else {
		var randomnumber = Math.floor((Math.random() * 100) + 1);
		var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
		win.document.write(result);
	}
}
// L2PT-912 start
function _inHDDTTheoPhieuVT(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode) {
	var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', phieuthuid_hddt);
	if (arr_pt && arr_pt.length > 0) {
		// L2PT-115115 start
		var rowPT = arr_pt[0];
		/*
		var supplierTaxCode = INVOICES_WS_USER;
		var invoiceNo = rowPT.INVOICENO;
		var strIssueDate = rowPT.STRISSUEDATE;
		var additionalReferenceDesc = "huy";
		var additionalReferenceDate = rowPT.ADDITIONALREFERENCEDATE;
		*/
		var objGetFile = new Object;
		// L2PT-129800 start
		if (rowPT.BANTHUOC == '1' && dsCH.INVOICES_WS_TAX_CODE_NT && dsCH.INVOICES_WS_TAX_CODE_NT != '0') {
			objGetFile.supplierTaxCode = dsCH.INVOICES_WS_TAX_CODE_NT;
		} else {
			objGetFile.supplierTaxCode = dsCH.INVOICES_WS_TAX_CODE;
		}
		// L2PT-129800 end
		objGetFile.invoiceNo = rowPT.INVOICENO;
		objGetFile.templateCode = dsCH.VPI_HDDTVT_PATTERN == '0' ? dsCH.INVOICES_WS_PATTERN : rowPT.MAU_SO; // L2PT-109124
		// L2PT-25725 start
		if (dsCH.VPI_HDDT_VIETTEL_V2 == '1') {
			objGetFile.transactionID = rowPT.TRANSACTIONID_VT;
		} else {
			objGetFile.transactionUuid = rowPT.TRANSACTIONID_VT;
		}
		// L2PT-25725 end
		objGetFile.fileType = "PDF";
		// L2PT-115115 end
		var retRFHDDTviettel = ajaxSvc.InvoicesWS.getInvoiceRepresentationFile(JSON.stringify(objGetFile));
		if (retRFHDDTviettel.includes("ERR_GETINVOICE_REQ")) {
			DlgUtil.showMsg('Lỗi xem hóa đơn điện tử');
			return -1;
		} else {
			var objRFHDDTviettel = JSON.parse(retRFHDDTviettel);
			if (objRFHDDTviettel.errorCode == null || objRFHDDTviettel.errorCode == '200') { // L2PT-25725 errorCode = 200
				var arrrayBuffer = base64ToArrayBuffer(objRFHDDTviettel.fileToBytes); //data is the base64 encoded string
				var blob = new Blob([ arrrayBuffer ], {
					type : "application/pdf"
				});
				var link = window.URL.createObjectURL(blob);
				window.open(link, '', 'height=500,width=850');
			} else {
				DlgUtil.showMsg(objRFHDDTviettel.description + " ( " + objRFHDDTviettel.errorCode + " )");
			}
		}
	} else {
		DlgUtil.showMsg("Không tìm thấy hóa đơn này");
	}
}
// L2PT-912 end
// L2PT-14600 start
function _inHDDTTheoPhieuMISA(phieuthuid_hddt, dsCH, _config_hddt, _benhnhanid, mode) {
	var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', phieuthuid_hddt);
	if (arr_pt && arr_pt.length > 0) {
		var retViewHDDTMISA = ajaxSvc.InvoicesWS.getIssuedInvoice(arr_pt[0].TRANSACTIONID_VT);
		console.log(retViewHDDTMISA);
		try {
			var arrViewHDDTMISA = JSON.parse(retViewHDDTMISA);
			if (arrViewHDDTMISA && arrViewHDDTMISA.length > 0) {
				window.open(arrViewHDDTMISA[0]);
			}
		} catch (err) {
			DlgUtil.showMsg("có lỗi xảy ra:" + err);
		}
	} else {
		DlgUtil.showMsg("Không tìm thấy hóa đơn này");
	}
}
// L2PT-14600 end
// L2PT-37815: tách các hàm chuyển đổi
function convertForStore(_phieuthuId, dsCH, invToken) {
	// L2PT-28287 start
	var kiemtraTK = layTKHDDT(_phieuthuId, dsCH);
	if (!kiemtraTK) {
		return -4;
	}
	// L2PT-28287 end
	var result = ajaxSvc.InvoicesWS.convertForStore(invToken, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	if (result == "" || result.includes("ERR:")) {
		// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
		DlgUtil.showMsg(thongbao_loi('HDDT_CDLUUTRU_TOKEN', result));
		// L2PT-37815 end
	} else {
		var randomnumber = Math.floor((Math.random() * 100) + 1);
		var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
		win.document.write(result);
		//L2PT-76341 start
		if (dsCH.VPI_HDDT_HIEN_GDIN == '1') {
			win.document.close();
			win.print();
		}
		//L2PT-76341 end
	}
}
// L2PT-25887 start: tách hàm in hóa đơn điện tử chuyển đổi
function convertForStoreFkey(_phieuthuId, dsCH) {
	// L2PT-28287 start
	var kiemtraTK = layTKHDDT(_phieuthuId, dsCH);
	if (!kiemtraTK) {
		return -4;
	}
	// L2PT-28287 end
	// L2PT-83683 start: chuyen doi hd viettel
	if (dsCH.VPI_HDDT_VT == 1) {
		var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuId);
		if (arr_pt && arr_pt.length > 0) {
			var rowPT = arr_pt[0];
			var supplierTaxCode = dsCH.INVOICES_WS_TAX_CODE; // L2PT-5786
			var invoiceNo = rowPT.INVOICENO;
			var templateCode = rowPT.INVOICES_PATTERN;
			var strIssueDate = rowPT.STRISSUEDATE;
			var exchangeUser = encodeURIComponent(rowPT.TEN_ND_DN);
			var additionalReferenceDate = rowPT.ADDITIONALREFERENCEDATE;
			// L2PT-25725 start
			if (dsCH.VPI_HDDT_VIETTEL_V2 == '1') {
				strIssueDate = new Date(strIssueDate.substring(0, 4), strIssueDate.substring(4, 6) - 1, strIssueDate.substring(6, 8), strIssueDate.substring(8, 10), strIssueDate.substring(10, 12),
						strIssueDate.substring(12, 14)).getTime();
				additionalReferenceDate = new Date(additionalReferenceDate.substring(0, 4), additionalReferenceDate.substring(4, 6) - 1, additionalReferenceDate.substring(6, 8),
						additionalReferenceDate.substring(8, 10), additionalReferenceDate.substring(10, 12), additionalReferenceDate.substring(12, 14)).getTime();
			}
			// L2PT-43054 start: Lấy strIssueDate từ site hóa đơn
			// L2PT-46267 start: thêm cấu hình
			if (dsCH.VPI_HDDT_VIETTEL_V3 == '1') {
				//var transactionUuid = rowPT.TRANSACTIONID_VT;
				var transactionUuid = dsCH._hospital_code + _phieuthuId.padStart(36 - dsCH._hospital_code.length, '0');
				var requestParameter = 'supplierTaxCode=' + supplierTaxCode + '&transactionUuid=' + transactionUuid;
				var retSearchInvoiceByTrans = ajaxSvc.InvoicesWS.searchInvoiceByTransactionUuid(requestParameter);
				try {
					var objSearchInvoiceByTrans = JSON.parse(retSearchInvoiceByTrans);
					if (objSearchInvoiceByTrans.errorCode == null) {
						strIssueDate = objSearchInvoiceByTrans.result[0].issueDate;
					} else {
						DlgUtil.showMsg('Hủy HDDT - không tìm thấy hóa đơn này trên site HDDT');
						return false;
					}
				} catch (err) {
					DlgUtil.showMsg('Hủy HDDT - có lỗi xảy ra: ' + err);
					return false;
				}
			}
			// L2PT-46267 end
			// L2PT-43054 end
			// L2PT-25725 end
			var paramTrans = "supplierTaxCode=" + supplierTaxCode + "&invoiceNo=" + invoiceNo + "&templateCode=" + templateCode + "&strIssueDate=" + strIssueDate + "&exchangeUser=" + exchangeUser;
			var retConvertHDDTviettel = ajaxSvc.InvoicesWS.createExchangeInvoiceFile(paramTrans);
			var objConvertHDDTviettel = JSON.parse(retConvertHDDTviettel);
			if (objConvertHDDTviettel && objConvertHDDTviettel.errorCode && objConvertHDDTviettel.errorCode.length > 0) {
				DlgUtil.showMsg(objConvertHDDTviettel.description + " ( " + objConvertHDDTviettel.errorCode + " )");
			} else {
				var _par = [ "3", "", "", "", _phieuthuId ];
				var _returnConvertHDDTviettel = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
				var arrrayBuffer = base64ToArrayBuffer(objConvertHDDTviettel.fileToBytes); //data is the base64 encoded string
				var blob = new Blob([ arrrayBuffer ], {
					type : "application/pdf"
				});
				var link = window.URL.createObjectURL(blob);
				window.open(link, '', 'height=500,width=850');
			}
			//supplierTaxCode=0800207726&invoiceNo=K24TVP5313&templateCode=2/001&strIssueDate=1713325452000&exchangeUser=test123
		} else {
			DlgUtil.showMsg("Không lấy được dữ liệu hóa đơn");
		}
	}
	// L2PT-83683 end
	else {
		var result = ajaxSvc.InvoicesWS.convertForStoreFkey(_phieuthuId, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
		if (result == "" || result.includes("ERR:")) {
			// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
			DlgUtil.showMsg(thongbao_loi('HDDT_CDLUUTRU_FKEY', result));
			// L2PT-37815 end
		} else {
			var randomnumber = Math.floor((Math.random() * 100) + 1);
			var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
			win.document.write(result);
		}
	}
}
// L2PT-25887 end
function convertForVerifyFkey(_phieuthuId, dsCH) {
	var kiemtraTK = layTKHDDT(_phieuthuId, dsCH);
	if (kiemtraTK != "1") {
		return kiemtraTK;
	}
	var result = ajaxSvc.InvoicesWS.convertForVerifyFkey(_phieuthuId, dsCH.INVOICES_URL_VIEW, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	if (result == "" || result.includes("ERR:")) {
		// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
		var msgLoi = thongbao_loi('HDDT_CHUYEN_DOI', result);
		// L2PT-37815 end
		return msgLoi;
	} else {
		var _par = [ "3", "", "", "", _phieuthuId ];
		jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
		var randomnumber = Math.floor((Math.random() * 100) + 1);
		var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
		win.document.write(result);
		return "OK:";
	}
}
// L2PT-37815 end
//L2PT-83683 start
function base64ToArrayBuffer(base64) {
	var binaryString = window.atob(base64);
	var binaryLen = binaryString.length;
	var bytes = new Uint8Array(binaryLen);
	for (var i = 0; i < binaryLen; i++) {
		var ascii = binaryString.charCodeAt(i);
		bytes[i] = ascii;
	}
	return bytes;
}
//L2PT-83683 end
// L2PT-28287 start
function layTKHDDT(phieuthuid_hddt, dsCH) {
	if (!phieuthuid_hddt) {
		return "Chưa chọn hóa đơn";
	}
	if (dsCH.VPI_N_TKHD == '1') {
		var tk_id = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.LOAI.HDDT", phieuthuid_hddt);
		for (var i = 0; i < _config_hddt.length; i++) {
			if (_config_hddt[i].TKID == tk_id) {
				dsCH.INVOICES_CONTENT_TYPE = _config_hddt[i].INVOICES_CONTENT_TYPE;
				dsCH.INVOICES_URL_IMPORT = _config_hddt[i].TKID
				dsCH.INVOICES_URL_CANCEL = _config_hddt[i].TKID
				dsCH.INVOICES_URL_VIEW = _config_hddt[i].TKID
				dsCH.INVOICES_WS_PATTERN = _config_hddt[i].INVOICES_WS_PATTERN;
				dsCH.INVOICES_WS_SERIAL = _config_hddt[i].INVOICES_WS_SERIAL;
				dsCH.INVOICES_WS_USER = _config_hddt[i].INVOICES_WS_USER;
				dsCH.INVOICES_WS_PWD = _config_hddt[i].INVOICES_WS_PWD;
				dsCH.INVOICES_WS_USER_ACC = _config_hddt[i].INVOICES_WS_USER_ACC;
				dsCH.INVOICES_WS_PWD_ACC = _config_hddt[i].INVOICES_WS_PWD_ACC;
				return "1";
			}
		}
		if (!co_tk) {
			return "Không có tài khoản HDDT tương ứng với hóa đơn này";
		}
	}
	return "1";
}
// L2PT-28287 end
//L2PT-95812 start
function cancelInv(_phieuthuid_hddt, dsCH) {
	if (dsCH.VPI_HDDT_VT == 1) {
		cancelInvVT(_phieuthuid_hddt, dsCH);
		return;
	}
	// L2PT-14600 start
	if (dsCH.VPI_HDDT_VT == 2) {
		cancelInvMISA(_phieuthuid_hddt, dsCH)
		return;
	}
	// L2PT-14600 end
	// L2PT-15577 start
	var VPI_GUI_TBL_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_GUI_TBL_HDDT');
	if (VPI_GUI_TBL_HDDT == 1) {
		// L2PT-18357 start
		var objTPhieuThu = {
			"PHIEUTHUID" : _phieuthuid_hddt,
			"KIEU" : "1"
		};
		var tPhieuThu = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.TT.LT', JSON.stringify(objTPhieuThu));
		// L2PT-18357 end
		var retMCCQT = "";
		if (tPhieuThu && tPhieuThu.length > 0) {
			var VPI_HDDT_LAYMACQT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HDDT_LAYMACQT');
			if (VPI_HDDT_LAYMACQT == 1) {
				retMCCQT = ajaxSvc.InvoicesWS.getMCCQThueByFkeys(_phieuthuid_hddt, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER,
						dsCH.INVOICES_WS_PWD, tPhieuThu[0].INVOICES_PATTERN);
				if (retMCCQT == "ERR:1") {
					DlgUtil.showMsg("Lấy XML HĐ có mã CQT: Tài khoản đăng nhập sai hoặc không có quyền thêm mới hóa đơn");
					return;
				} else if (retMCCQT == "ERR:2") {
					DlgUtil.showMsg("Lấy XML HĐ có mã CQT: Không tìm thấy hóa đơn tương ứng");
					return;
				} else if (retMCCQT == "ERR:10") {
					DlgUtil.showMsg("Lấy XML HĐ có mã CQT: Vượt quá số lượng 100 hóa đơn cần lấy");
					return;
				} else if (retMCCQT == "ERR:20") {
					DlgUtil.showMsg("Lấy XML HĐ có mã CQT: Không lấy được thông tin người dùng");
					return;
				} else if (retMCCQT == "ERR:5") {
					DlgUtil.showMsg("Lấy XML HĐ có mã CQT: Lỗi không xác định, không lấy được dữ liệu hóa đơn cấp mã theo dữ liệu truyền vào");
					return;
				}
			}
			var xmlDataTBL = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.HDDT.TBL", _phieuthuid_hddt + "$" + $('#txtLYDOHUYPHIEU').val() + "$" + retMCCQT);
			if (xmlDataTBL && xmlDataTBL.length > 0) {
				var retSendInvNoticeErrors = ajaxSvc.InvoicesWS.sendInvNoticeErrors(xmlDataTBL, dsCH.INVOICES_URL_IMPORT, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER,
						dsCH.INVOICES_WS_PWD, tPhieuThu[0].INVOICES_PATTERN);
				if (retSendInvNoticeErrors == "ERR:1") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Tài khoản đăng nhập sai hoặc không có quyền");
				} else if (retSendInvNoticeErrors == "ERR:21") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Không tìm thấy công ty hoặc tài khoản khôngtồn tại");
				} else if (retSendInvNoticeErrors == "ERR:22") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Công ty chưa đăng ký chứng thư số");
				} else if (retSendInvNoticeErrors == "ERR:28") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Chưa có thông tin chứng thư trong hệ thống");
				} else if (retSendInvNoticeErrors == "ERR:24") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Chứng thư truyền lên không đúng với chứng thư đăng ký trong hệ thống");
				} else if (retSendInvNoticeErrors == "ERR:27") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Chứng thư chưa đến thời điểm sử dụng");
				} else if (retSendInvNoticeErrors == "ERR:26") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Chứng thư số hết hạn ");
				} else if (retSendInvNoticeErrors == "ERR:3") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Dữ liệu xml đầu vào không đúng quy định");
				} else if (retSendInvNoticeErrors == "ERR:10") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Lô có số hóa đơn vượt quá số lượng tối đa cho phép");
				} else if (retSendInvNoticeErrors == "ERR:5") {
					DlgUtil.showMsg("Gửi thông báo lỗi: Có lỗi xảy ra");
				}
			} else {
				DlgUtil.showMsg("Lỗi tạo XML gửi thông báo lỗi");
				return;
			}
		} else {
			DlgUtil.showMsg("Không tìm thấy phiếu thu này");
			return;
		}
	}
	// L2PT-15577 end
	// L2PT-95812 start
	if (dsCH.VPI_HUYHDDT_SINHBIENBAN == '1') {
		ret = ajaxSvc.InvoicesWS.cancelInvSignFileNoPay(_phieuthuid_hddt, dsCH.INVOICES_URL_CANCEL, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	} else {
		ret = ajaxSvc.InvoicesWS.cancelHoaDon(_phieuthuid_hddt, dsCH.INVOICES_URL_CANCEL, dsCH.INVOICES_WS_USER_ACC, dsCH.INVOICES_WS_PWD_ACC, dsCH.INVOICES_WS_USER, dsCH.INVOICES_WS_PWD);
	}
	// L2PT-95812 end
	// L2PT-18932 start
	if (ret == "OK:") {
		var _par = [ "0", "", "", "", _phieuthuid_hddt ];
		var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
		if (_return == '-1') {
			DlgUtil.showMsg("Cập nhật trạng thái hủy HDDT không thành công");
			return false;
		}
		return true;
	} else {
		// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
		DlgUtil.showMsg(thongbao_loi('HDDT_HUY_PH', ret));
		// L2PT-37815 end
		return false;
	}
	// L2PT-18932 end
}
//L2PT-95812 end
// L2PT-129800 start
function cancelInvVT(_phieuthuid_hddt, dsCH, reasonDelete) { // L2PT-128915
	var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuid_hddt);
	if (arr_pt && arr_pt.length > 0) {
		var rowPT = arr_pt[0];
		// L2PT-129800 start
		var supplierTaxCode = "";
		if (rowPT.BANTHUOC == '1' && dsCH.INVOICES_WS_TAX_CODE_NT && dsCH.INVOICES_WS_TAX_CODE_NT != '0') {
			supplierTaxCode = dsCH.INVOICES_WS_TAX_CODE_NT;
		} else {
			supplierTaxCode = dsCH.INVOICES_WS_TAX_CODE; // L2PT-5786
		}
		// L2PT-129800 end
		var invoiceNo = rowPT.INVOICENO;
		var strIssueDate = rowPT.STRISSUEDATE;
		var additionalReferenceDesc = "huy";
		var additionalReferenceDate = rowPT.ADDITIONALREFERENCEDATE;
		// L2PT-25725 start
		if (dsCH.VPI_HDDT_VIETTEL_V2 == '1') {
			strIssueDate = new Date(strIssueDate.substring(0, 4), strIssueDate.substring(4, 6) - 1, strIssueDate.substring(6, 8), strIssueDate.substring(8, 10), strIssueDate.substring(10, 12),
					strIssueDate.substring(12, 14)).getTime();
			additionalReferenceDate = new Date(additionalReferenceDate.substring(0, 4), additionalReferenceDate.substring(4, 6) - 1, additionalReferenceDate.substring(6, 8), additionalReferenceDate
					.substring(8, 10), additionalReferenceDate.substring(10, 12), additionalReferenceDate.substring(12, 14)).getTime();
		}
		// L2PT-43054 start: Lấy strIssueDate từ site hóa đơn
		// L2PT-46267 start: thêm cấu hình
		if (dsCH.VPI_HDDT_VIETTEL_V3 == '1') {
			//var transactionUuid = rowPT.TRANSACTIONID_VT;
			var transactionUuid = _hospital_code + _phieuthuid_hddt.padStart(36 - _hospital_code.length, '0');
			var requestParameter = 'supplierTaxCode=' + supplierTaxCode + '&transactionUuid=' + transactionUuid;
			var retSearchInvoiceByTrans = ajaxSvc.InvoicesWS.searchInvoiceByTransactionUuid(requestParameter);
			try {
				var objSearchInvoiceByTrans = JSON.parse(retSearchInvoiceByTrans);
				if (objSearchInvoiceByTrans.errorCode == null) {
					strIssueDate = objSearchInvoiceByTrans.result[0].issueDate;
				} else {
					DlgUtil.showMsg('Hủy HDDT - không tìm thấy hóa đơn này trên site HDDT');
					return false;
				}
			} catch (err) {
				DlgUtil.showMsg('Hủy HDDT - có lỗi xảy ra: ' + err);
				return false;
			}
		}
		// L2PT-46267 end
		// L2PT-43054 end
		// L2PT-25725 end
		var paramTrans = "supplierTaxCode=" + supplierTaxCode + "&invoiceNo=" + invoiceNo + "&strIssueDate=" + strIssueDate + "&additionalReferenceDesc=" + additionalReferenceDesc +
				"&additionalReferenceDate=" + additionalReferenceDate;
		// L2PT-128915 start
		if (reasonDelete) {
			paramTrans += "&reasonDelete=" + reasonDelete;
		}
		// L2PT-128915 end
		var retCancelHDDTviettel = ajaxSvc.InvoicesWS.cancelTransactionInvoice(paramTrans);
		if (retCancelHDDTviettel.includes("ERR_CANCEL_REQ")) {
			DlgUtil.showMsg('Lỗi hủy hóa đơn điện tử');
		} else {
			var objRetCancelHDDTviettel = JSON.parse(retCancelHDDTviettel);
			if (objRetCancelHDDTviettel.errorCode == null) {
				var _par = [ "0", "", "", "", _phieuthuid_hddt ];
				var _returnCancelHDDTviettel = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
				DlgUtil.showMsg("Hủy hóa đơn điện tử thành công");
				return true;
			} else {
				DlgUtil.showMsg(objRetCancelHDDTviettel.description + " ( " + objRetCancelHDDTviettel.errorCode + " )");
			}
		}
	} else {
		DlgUtil.showMsg("Không tìm thấy hóa đơn này");
	}
	return false;
}
function cancelInvMISA(_phieuthuid_hddt, dsCH) {
	var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuid_hddt);
	if (arr_pt && arr_pt.length > 0) {
		var param = new Object();
		// L2PT-17896 start
		if (dsCH.INVOICE_XML_TYPE == '1') {
			param = [ {
				"TransactionID" : arr_pt[0].TRANSACTIONID_VT,
				"RefDate" : arr_pt[0].ISSUED_DATE,
				"CancelReason" : "HUY HD"
			} ];
		} else {
			param = {
				"TransactionID" : arr_pt[0].TRANSACTIONID_VT,
				"RefDate" : arr_pt[0].ISSUED_DATE,
				"RefNo" : arr_pt[0].REFNO,
				"DeletedReason" : "HUY HD"
			};
		}
		// L2PT-17896 end
		var retCancelMISA = ajaxSvc.InvoicesWS.deleteInvoices(param);
		try {
			var retCancelMISAObj = JSON.parse(retCancelMISA);
			console.log(retCancelMISAObj);
			if (retCancelMISAObj.errorCode == "0") {
				var _par = [ "0", "", "", "", _phieuthuid_hddt ];
				var _retUdpCancelHDDTMISA = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
				DlgUtil.showMsg("Hủy hóa đơn điện tử thành công");
			} else {
				DlgUtil.showMsg("Mã lỗi " + retCancelMISAObj.errorCode + ": " + retCancelMISAObj.message);
			}
		} catch (e) {
			console.log(e);
		}
	} else {
		DlgUtil.showMsg("Không tìm thấy hóa đơn này");
	}
}
// L2PT-129800 end
//L2PT-95857 start
function guiDuLieuLenFastApi(phieuthuid, _hospital_code, huyPhieu) {
	var VPI_CALL_FASTAPI = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CALL_FASTAPI');
	if (VPI_CALL_FASTAPI != '1') {
		return null;
	}
	var arrPTGuiFast05 = [];
	var arrPTGuiFast07 = [];
	var arrPTGuiFast08 = [];
	var arrPTGuiFast09 = [];
	var dataPTGuiFast = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.LAYTT.FAST", phieuthuid);
	var strKey05 = "YourId,VcDate,VcNo,SeriNo,CusID,SaleID,Buyer,Note,Status,TaxCode,TaxRate,CompanyName,Address,TaxNo,ItemId,Uom,SiteID,Bonnus,Quality,Price";
	var strKey07 = "YourId,VcDate,VcNo,CusID,Note,Status,Buyer,BuyerAddress,Type,DebitAccount,FcCode,ExRate,CreditAccount,CusInvoiceId,Amount,Memo";
	var strKey08 = "YourId,VcDate,VcNo,CusID,Note,Status,Buyer,BuyerAddress,Type,DebitAccount,FcCode,ExRate,CreditAccount,CusInvoiceId,Amount,Memo";
	var strKey09 = "YourId,VcDate,VcNo,CusID,Note,Status,Receiver,ReceiverAddress,Type,DebitAccount,FcCode,ExRate,CreditAccount,CusInvoiceId,Amount,Memo";
	arrKey05 = strKey05.split(",");
	arrKey07 = strKey07.split(",");
	arrKey08 = strKey08.split(",");
	arrKey09 = strKey09.split(",");
	parseObj(dataPTGuiFast, "TAXRATE,BONNUS,QUALITY,PRICE,EXRATE,AMOUNT".split(","));
	dataPTGuiFast.forEach(function(phieuThu) {
		if (huyPhieu) {
			if (phieuThu.LOAIPHIEUTHUID == 1 || phieuThu.LOAIPHIEUTHUID == 3) {
				var objPTGuiFast09 = new Object();
				arrKey09.forEach(function(key) {
					objPTGuiFast09[key] = phieuThu[key.toUpperCase()];
				});
				arrPTGuiFast09.push(objPTGuiFast09);
			}
		} else {
			if ((phieuThu.LOAIPHIEUTHUID == 1 || phieuThu.LOAIPHIEUTHUID == 3) && phieuThu.BANTHUOC == 1) {
				var objPTGuiFast05 = new Object();
				arrKey05.forEach(function(key) {
					objPTGuiFast05[key] = phieuThu[key.toUpperCase()];
				});
				arrPTGuiFast05.push(objPTGuiFast05);
			} else if (phieuThu.LOAIPHIEUTHUID == 2) {
				var objPTGuiFast09 = new Object();
				arrKey09.forEach(function(key) {
					objPTGuiFast09[key] = phieuThu[key.toUpperCase()];
				});
				arrPTGuiFast09.push(objPTGuiFast09);
			} else if (phieuThu.LOAIPHIEUTHUID == 1 || phieuThu.LOAIPHIEUTHUID == 3) {
				if (phieuThu.HINHTHUCTHANHTOAN == "1") {
					var objPTGuiFast07 = new Object();
					arrKey07.forEach(function(key) {
						objPTGuiFast07[key] = phieuThu[key.toUpperCase()];
					});
					// L2PT-126797 start
					objPTGuiFast07.CreditAccount = "131";
					objPTGuiFast07.CusID = "KHACHLE";
					// L2PT-126797 end
					arrPTGuiFast07.push(objPTGuiFast07);
				} else {
					var objPTGuiFast08 = new Object();
					arrKey08.forEach(function(key) {
						objPTGuiFast08[key] = phieuThu[key.toUpperCase()];
					});
					arrPTGuiFast08.push(objPTGuiFast08);
				}
			}
		}
	});
	var objRet = new Object();
	objRet.S05 = callFastApi("S05", arrPTGuiFast05, _hospital_code);
	objRet.S07 = callFastApi("S07", arrPTGuiFast07, _hospital_code);
	objRet.S08 = callFastApi("S08", arrPTGuiFast08, _hospital_code);
	objRet.S09 = callFastApi("S09", arrPTGuiFast09, _hospital_code);
	return objRet;
}
function callFastApi(code, data, hospital_code) {
	if (!data || data.length == 0) {
		return;
	}
	var apikey = "";//"eyJ4NXQiOiJOVGRtWmpNNFpEazNOalkwWXpjNU1tWm1PRGd3TVRFM01XWXdOREU1TVdSbFpEZzROemM0WkE9PSIsImtpZCI6ImdhdGV3YXlfY2VydGlmaWNhdGVfYWxpYXMiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vQXRUJQuT7SLpJnJYrQfBnyJG2xlFN7LuNETlaka7oyUwHBYr81oOHUJEEBAsF3hGv-6rV9kG8PKnyYg3fM2LsonaPZhycc8DYpcI8vnO4O6CoF3rGufXYPG_2QI9wes53j5dXlRp_0hj2jOlQDDJB2zIqmg3IZPaYOexBU9nAFL8GJiSeX9yocdb0bl4y6bwpJQVOLInxL_DxNA4ox8gIO5b3dBo1wa0NCXaneVjem4irBKDCC61reJztsmgsST6FMyFERQuyVCcLKIm6EWkNL95SqwrE-B8Yg63xNcACkcneYmUveq06wESLFSdvQ68KViP3oAc4LDXmW9MFX-CA==";
	var bearer = "";//"1c9700873a5ae7d77563c4358459c923de7ea2e9f9df7a46ac1329d867e96aef";
	var url = "";//"https://hub-test-gw.vncare.vn/gw/api/acc-hub/1.1.0/process";
	var jsonFast = {
		"serviceCode" : code,
		"data" : data,
		"hospitalCode" : hospital_code,
		"checksum" : "<CHECKSUM>"
	}
	try {
		var strRetFastApi = ajaxSvc.PortalWS4750.callFastApi(code, jsonFast, url, 60, apikey, bearer, "");
		console.log(strRetFastApi);
		objRetFastApi = JSON.parse(strRetFastApi);
		console.log(objRetFastApi);
		var msg = code + ": " + objRetFastApi.message;
		if (objRetFastApi.code != '00' && objRetFastApi.result && objRetFastApi.result.length > 0) {
			var result = objRetFastApi.result[0];
			if (result.message) {
				msg += ". Lỗi: " + result.message;
			}
			if (result.messages) {
				msg += ". Lỗi: " + result.messages;
			}
			var dataErr = result.data;
			if (dataErr && dataErr.length > 0) {
				dataErr.forEach(function(rowErr) {
					msg += ". Dòng " + rowErr.payloadIndex + ": Trường " + rowErr.key + " " + rowErr.message;
				});
			}
			var resultErr = result.result;
			if (resultErr && resultErr.message) {
				msg += ". Chi tiết lỗi: " + resultErr.message;
			}
			if (resultErr && resultErr.messages) {
				msg += ". Chi tiết lỗi: " + resultErr.messages;
			}
		}
		DlgUtil.showMsg(msg);
		return strRetFastApi;
	} catch (err) {
		console(err);
		DlgUtil.showMsg(code + ": " + err);
		return err;
	}
}
// L2PT-95857 end
// L2PT-18357 start
// L2PT-16575 start
function layDsCH(_hospital_id) {
	var objCfg = null;
	// L2PT-1852 start
	// L2PT-17249 thêm cấu hình: VPI_MAKHOA_TCKT;VPI_MAKHOA_DUOC
	var str_ch = "VPI_KIEUIN_HOADON;HIS_IN_HOADONCHITIET";
	str_ch += ";VPI_DAY_HOADONDT;VPI_HDDT_VT;VPI_N_TKHD;INVOICES_HAI_QUYENHD;VPI_FN_IMPORT_HDDT;INVOICES_BH_PATTERN;INVOICES_BH_SERIAL;INVOICES_DV_PATTERN;INVOICES_DV_SERIAL;VPI_VIEW_HDDT;VPI_TENKH_HDDT;VPI_HDDT_MA_KH;VPI_DS_MSKH;VPI_KHOA_HDDT";
	str_ch += ";INVOICES_CONTENT_TYPE;INVOICES_URL_IMPORT;INVOICES_URL_CANCEL;INVOICES_URL_VIEW;INVOICES_WS_PATTERN;INVOICES_WS_SERIAL;INVOICES_WS_USER;INVOICES_WS_PWD;INVOICES_WS_USER_ACC;INVOICES_WS_PWD_ACC;VPI_MAKHOA_TCKT;VPI_MAKHOA_DUOC";
	str_ch += ";VPI_HDDT_CHITIET;VPI_KHOAPT_GUIHDDT;INVOICE_XML_TYPE;VPI_VIEW_HDDT_DELAY;INVOICES_WS_TAX_CODE;INVOICES_MISA_OPTIONUSER;INVOICES_MISA_FMTNBR;VPI_XN_GUILAI_HDDT;INVOICE_XML_IMPORT";// L2PT-18980 // L2PT-16803
	str_ch += ";INVOICES_WS_PATTERN_BT;INVOICES_WS_SERIAL_BT"; // L2PT-26947 : thêm serial, partern nhà thuốc
	str_ch += ";VPI_QUYEN_GUIHDDT"; // L2PT-28955: thêm cấu hình quyền gửi hddt
	str_ch += ';VPI_SERIAL_PATTERN_HDDT'; // L2PT-44489
	str_ch += ';VPI_HDDT_MTT'; // L2PT-43777
	str_ch += ';VPI_HDDT_VIETTEL_V2;VPI_HDDT_VIETTEL_V3' // L2PT-83683
	str_ch += ';INVOICES_VT_FMTNBR;VPI_DAY_HDDT_PH'; // L2PT-108921
	str_ch += ';VPI_HUYHDDT_SINHBIENBAN'; // L2PT-95812
	str_ch += ';MISA_HSM'; // L2PT-105772
	str_ch += ';VPI_HDDTVT_PATTERN'; // L2PT-115115
	str_ch += ';INVOICES_WS_TAX_CODE_NT'; // L2PT-129800
	var arr_dsCH = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
	if (arr_dsCH.length > 0) {
		objCfg = arr_dsCH[0];
		// L2PT-119965 start
		// L2PT-14600 start
		if (objCfg.INVOICES_MISA_OPTIONUSER && objCfg.INVOICES_MISA_OPTIONUSER != 0) {
			try {
				objCfg.INVOICES_MISA_OPTIONUSER = JSON.parse(objCfg.INVOICES_MISA_OPTIONUSER);
			} catch (err) {
				console.log(err);
			}
		}
		if (objCfg.INVOICES_MISA_FMTNBR && objCfg.INVOICES_MISA_FMTNBR != 0) {
			try {
				objCfg.INVOICES_MISA_FMTNBR = JSON.parse('[' + FormUtil.unescape(objCfg.INVOICES_MISA_FMTNBR) + ']');
			} catch (err) {
				console.log(err);
			}
		}
		// L2PT-14600 end
		// L2PT-119965 end
		if (objCfg.VPI_DAY_HOADONDT == 1) {
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
			if (rs[0].INVOICES_PASS != "" && rs[0].INVOICES_USER != "") {
				objCfg.INVOICES_WS_USER_ACC = rs[0].INVOICES_USER;
				objCfg.INVOICES_WS_PWD_ACC = rs[0].INVOICES_PASS;
			}
		}
		objCfg._hospital_id = _hospital_id;
		return objCfg;
	} else {
		DlgUtil.showMsg("Không lấy được tham số cấu hình");
		return null;
	}
	return null;
	// L2PT-1852 end
}
// L2PT-18357 end
// L2PT-16575 end
function setEnabled(_ena, _dis) {
	for (var i = 0; i < _ena.length; i++) {
		$("#" + _ena[i]).attr('disabled', false);
	}
	for (var i = 0; i < _dis.length; i++) {
		$("#" + _dis[i]).attr('disabled', true);
	}
}