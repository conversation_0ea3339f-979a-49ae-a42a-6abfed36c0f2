<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEU_XETNGHIEM_A4" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="406" leftMargin="10" rightMargin="5" topMargin="5" bottomMargin="5" uuid="18c5e549-94fd-415a-8af9-02c896054c66">
	<property name="ireport.zoom" value="1.8150000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<queryString language="plsql">
		<![CDATA[{call RPT_PHIEU_XETNGHIEM_26720(null,$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="SOTHUTU_LAYMAU" class="java.math.BigDecimal"/>
	<field name="SOTHUTU" class="java.math.BigDecimal"/>
	<field name="BARCODE" class="java.lang.String"/>
	<field name="PHONGLAYMAU" class="java.lang.String"/>
	<field name="PHONGTHUCHIEN" class="java.lang.String"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="TENXETNGHIEM" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="NGAYDICHVU" class="java.sql.Timestamp"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="ORG_ADDRESS" class="java.lang.String"/>
	<field name="DOCTOR_NAME" class="java.lang.String"/>
	<field name="CHUANDOANPHU" class="java.lang.String"/>
	<field name="KHOATHUCHIEN" class="java.lang.String"/>
	<field name="CHANDOANBD" class="java.lang.String"/>
	<field name="NOTE" class="java.lang.String"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="TENGIUONG" class="java.lang.String"/>
	<field name="DONGIA" class="java.lang.Integer"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="TINHTRANG" class="java.lang.Integer"/>
	<field name="THANHTIEN" class="java.lang.Integer"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="GHICHUBENHCHINH" class="java.lang.String"/>
	<field name="GHICHUBENHPHU" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="TEN_NHOM" class="java.lang.String"/>
	<field name="MSPHIEU" class="java.lang.String"/>
	<field name="GHICHU_TITLE" class="java.lang.String"/>
	<field name="KHOACHIDINH" class="java.lang.String"/>
	<field name="LOAITHANHTOAN" class="java.lang.String"/>
	<variable name="TONGTIEN" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<variable name="vchandoan" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{CHANDOAN} == null ? "" : $F{CHANDOAN}]]></initialValueExpression>
	</variable>
	<variable name="vghichu1" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{GHICHUBENHCHINH} == null ? "" : "; " + $F{GHICHUBENHCHINH}]]></initialValueExpression>
	</variable>
	<variable name="vchandoanphu" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{CHUANDOANPHU} == null ? "" : "; " + $F{CHUANDOANPHU}]]></initialValueExpression>
	</variable>
	<variable name="vghichu2" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
		<initialValueExpression><![CDATA[$F{GHICHUBENHPHU} == null ? "" : "; " + $F{GHICHUBENHPHU}]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="212">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="89" width="268" height="12" uuid="59734d35-008d-4c1e-9686-3370da334fd3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Họ tên: " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="101" width="406" height="12" uuid="96a0210f-b609-4959-8a4b-150b87807b45"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Địa chỉ: " + ($F{DIACHI}==null?"":$F{DIACHI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="137" width="406" height="12" uuid="2597b26a-b985-4274-9486-ed3398c61659"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Chẩn đoán: " + $V{vchandoan} + $V{vghichu1} + $V{vchandoanphu} + $V{vghichu2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="26" y="185" width="242" height="27" uuid="9e5e0607-d4ed-4fd6-9324-731f7fb09670"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Yêu cầu cận lâm sàng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="135" height="18" uuid="1edea119-13db-454b-9cdd-e92945eafb2a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="18" width="135" height="18" uuid="9482c0f3-328b-4b89-a7d7-91e2cd3aee8f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="185" width="26" height="27" uuid="db7ffd8b-7cfd-4196-bb09-50662faf3701"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="327" y="185" width="79" height="27" uuid="e3b82826-6bdc-4140-acdc-5a597ecef844"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="173" width="117" height="12" uuid="f24736e3-9761-40ed-bdfe-091dcd2575fc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[-Tình trạng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="63" y="173" width="343" height="12" uuid="56fbaeb6-a774-4235-a5d2-02f8c4c138ff"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TINHTRANG} == 2 ? ": Khẩn" : ": Thường"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="14" width="110" height="14" uuid="d9043d7a-b65e-49b5-b4d4-8ac10058c7c8"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã BN: " + $F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="28" width="110" height="14" uuid="4ae6f83d-5b25-462b-a4d0-1851aad94e4b"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đối tượng: " + $F{TEN_DTBN}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="49" y="36" width="60" height="1" uuid="616edd04-87ad-4ac1-9a51-ab6c1d517e50"/>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="135" y="18" width="163" height="18" uuid="41609797-6a72-4884-abb2-92f41dcca1b4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONGKHAM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="125" width="55" height="12" uuid="0df5992d-6d34-4870-be69-90a091942015"/>
				<textElement>
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[-Số thẻ BH:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="135" y="0" width="163" height="18" uuid="696d9987-0535-4f5c-8681-0d00b3bb6f56"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MSPHIEU}.equals("MS: 30/BV-01")? "PHIẾU CHỈ ĐỊNH XÉT NGHIỆM CHẨN ĐOÁN " : "PHIẾU CHỈ ĐỊNH XÉT NGHIỆM ")+ $F{TEN_NHOM}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="55" y="125" width="213" height="12" uuid="0807648a-7cfb-4198-a430-0f2265943df8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTHE}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement positionType="Float" x="6" y="40" width="127" height="40" uuid="839e2919-7550-4674-89a9-850410f92872"/>
				<imageExpression><![CDATA[$P{DPAR_ORG_LOGO}]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="42" width="110" height="14" uuid="cff2dc05-eaad-4f83-a378-63d4a10eb121"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số phiếu:" + $F{SOPHIEU}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement positionType="Float" x="298" y="57" width="110" height="20" uuid="5e633ddc-bdb1-4ba0-b34e-7a59f611c552"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code39 (Extended)" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$F{BARCODE}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="77" width="110" height="12" uuid="5619e3d8-5f4b-412e-bbdb-b9c8972e0ab0"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BARCODE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="298" y="0" width="110" height="14" isRemoveLineWhenBlank="true" uuid="1f96e05e-7a09-412e-9f5a-db7d538311ca"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MSPHIEU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="113" width="406" height="12" uuid="4b0cf3e0-7dc1-46fa-b506-949533503e81"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Khoa: "+( $F{KHOACHIDINH}==null?"":$F{KHOACHIDINH})
+"    Buồng: "+($F{PHONGKHAM}==null?"":$F{PHONGKHAM})
+"    Giường: "+($F{TENGIUONG}==null?"":$F{TENGIUONG})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="149" width="406" height="12" isRemoveLineWhenBlank="true" uuid="8b4a7acd-3afa-4ca5-8fa0-6e1c8bf0890a"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Vị trí lấy bệnh phẩm: "+($F{GHICHU_TITLE}==null?"":$F{GHICHU_TITLE})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="161" width="406" height="12" uuid="0ce6d89b-ec36-44d8-8696-347cddaa15fd"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["-Khoa thực hiện: "+($F{KHOATHUCHIEN}==null?"":$F{KHOATHUCHIEN})+"<b>     Phòng thực hiện: "+($F{PHONGTHUCHIEN}==null?"":$F{PHONGTHUCHIEN})+"</b>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="268" y="89" width="138" height="12" uuid="528c80b2-deca-4f2f-bf81-c5f7254911b3"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: "+$F{TUOI} + " " + ($F{DVTUOI}==null?"":$F{DVTUOI})
+"  "+"Giới tính: "+($F{GIOITINH}==null?"":$F{GIOITINH})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="268" y="185" width="59" height="27" uuid="5857f786-9e31-4f51-91da-6e69a328a747"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Loại hình thanh toán]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="26" y="0" width="242" height="14" uuid="cd9d34f4-2ee1-44d0-82db-c1619be712f0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENDICHVU}+($F{GHICHU}==null?"":"\n         "+$F{GHICHU})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="327" y="0" width="79" height="14" uuid="6bfe9b5e-ae19-4d91-a448-35b410c095a4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONGIA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="26" height="14" uuid="50e99b3b-d734-45ae-9f96-b5a222b014e7"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="268" y="0" width="59" height="14" uuid="48f6ad01-2cac-4e39-a58e-d31ab462f1ec"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAITHANHTOAN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="115">
			<staticText>
				<reportElement positionType="Float" x="162" y="18" width="244" height="15" uuid="4988b46a-eee3-4cdd-951f-7444cfc8bf07"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[BÁC SĨ]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="162" y="3" width="244" height="15" uuid="1cf89fe1-73b9-4773-ba43-95b348282cd4"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("'Bắc Sơn, Lúc 'HH:mm', Ngày 'dd' Tháng 'MM' Năm 'yyyy")).format($F{NGAYDICHVU})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="162" y="61" width="244" height="15" uuid="5edf20b1-3f26-4e44-84c8-cac4dc43095b"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DOCTOR_NAME}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
