/*
 * Thống kê tìm kiếm báo cáo thực hiện chụp chiếu tại khoa phòng
 * 
 * VietDA 	08/08/2016		Tạo mới
 * 
 */

var ctl_ar=[
	{type:'button',id:'btnQuayLai',icon:'chevron-left',text:'Quay lại'},
	{type:'label',id:'lblInfo',icon:'',text:'Thống Kê CĐHA'}
];

function ThongKeCDHA(opt) {
	this.load = doLoad;
	var CLS_TKCDHA_DATETYPE = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_TKCDHA_DATETYPE') == 1);
	
	function doLoad(){
		// khởi tạo các control
		initControl();
		bindEvent();
        
        $('#txtSoPhieu').focus();
		
		var sql_par = [];
		ComboUtil.getComboTag("cboDoiTuong","CLS02C004.DT",sql_par,"",{value:'', text:'Tất cả đối tượng'},"sql");

		var sql_par2 = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+'';
		ComboUtil.getComboTag("cboKhoa","CLS02C004.KP",sql_par2,"","","sp","",function () {
			$('#cboKhoa' ).SumoSelect({search: true, searchText: 'Tìm kiếm', okCancelInMulti: true, selectAll: true });
			$('#cboKhoa')[0].sumo.reload();
		});
		
		$("#txtTuNgay").val(moment().format('01/MM/YYYY') + (CLS_TKCDHA_DATETYPE ? " 00:00:00" : ""));
		$("#txtDenNgay").val(moment().format('DD/MM/YYYY') + (CLS_TKCDHA_DATETYPE ? " 23:59:59" : ""));
		
		var callback = function () {
            if ($.active !== 0) {
                setTimeout(callback, '50');
                return;
            }
            timKiemThongKe();
        };
        callback();
	}
	
	function initControl(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;

		ToolbarUtil.build('toolbarId', ctl_ar);
		var _toolbar=ToolbarUtil.getToolbar('toolbarId');


		_gridId = "grdThongKeCDHA";
		var _gridHeader="MAUBENHPHAMID,MAUBENHPHAMID,10,0,t,l;DICHVUKHAMBENHID,DICHVUKHAMBENHID,10,0,t,l;" +
				"STT,SOTHUTU,45,0,f,l;" +
				"Số phiếu,SOPHIEU,85,0,f,l;" +
				"Khẩn,LOAIMAUBENHPHAM,75,0,f,l;" +
				"Mã BN,MABENHNHAN,90,0,f,l;" +
				"Mã BA,MAHOSOBENHAN,90,0,f,l;" +
				"Tên BN,TENBENHNHAN,155,0,f,l;" +
				"Tuổi,TUOI,50,0,f,l;" +
				"Giới tính,GIOITINH,80,0,f,l;" +
				"Địa chỉ,DIACHI,200,0,f,l;" +
				"Tên dịch vụ,TENDICHVU,200,0,f,l;" +
				"Khoa,TENKHOA,160,0,f,l;" +
				"Phòng,TENPHONG,160,0,f,l;" +
				"Bác sĩ chỉ định,BACSY,120,0,f,l;" +
				"TG tiếp nhận,THOIGIANTIEPNHAN,100,0,f,l;" +
				"TG chỉ định,NGAYDICHVU,100,0,f,l;" +
				"TG trả KQ,NGAYMAUBENHPHAM_HOANTHANH,100,0,f,l";
		GridUtil.init(_gridId,"1024px","280px","",false,_gridHeader,false,{rowNum:500,rowList: [500,1000,2000,5000]});	
		GridUtil.addExcelButton(_gridId,"Xuất excel",true);		
		GridUtil.addExcelFullButton(_gridId,"Xuất excel full",false);
	}
	
	function bindEvent(){
		var dateFm = 'dd/MM/yyyy' + (CLS_TKCDHA_DATETYPE ? " HH:mm:SS" : "");
		$("#lblTuNgay").click(function () {
			NewCssCal('txtTuNgay',dateFm,'dropdown',CLS_TKCDHA_DATETYPE,'24',true)
		});
		$("#lblDenNgay").click(function(){
			NewCssCal('txtDenNgay',dateFm,'dropdown',CLS_TKCDHA_DATETYPE,'24',true);
		});
		$('#toolbarIdbtnQuayLai').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS02C001_DanhSachCDHA");
		});
	
		$("#txtTuoi").keydown(
				function(e) {
					-1 !== $.inArray(e.keyCode, [ 46, 8, 9, 27, 13 ])
							|| /65|67|86|88/.test(e.keyCode)
							&& (!0 === e.ctrlKey || !0 === e.metaKey)
							|| 35 <= e.keyCode && 40 >= e.keyCode
							|| (e.shiftKey || 48 > e.keyCode || 57 < e.keyCode)
							&& (96 > e.keyCode || 105 < e.keyCode)
							&& e.preventDefault()
				});
		
		$("#btnTimKiem").click(function(){
			timKiemThongKe();
		});
		
		$('#btnXuatExcel').on('click', function () {	
			var typeIn = 'XLS';
			if(kiemTra()){
				var _param = [{
						name : 'sophieu',
						type : 'String',
						value : $("#txtSoPhieu").val()
	       			},
	       			{
					    name : 'mabn',
					    type : 'String',
					    value : $("#txtMaBN").val()
	       			},
	       			{
					    name : 'tenbn',
					    type : 'String',
					    value : $("#txtTenBN").val()
	       			},
	       			{
					    name : 'doituong',
					    type : 'String',
					    value : $("#cboDoiTuong").val()
	       			},
	       			{
				        name : 'tuoi',
				        type : 'String',
				        value : $("#txtTuoi").val()
	       			},
	       			{
				        name : 'gioitinh',
				        type : 'String',
				        value : $("#cboGioiTinh").val()
	       			},
	       			{
				        name : 'diachi',
				        type : 'String',
				        value : $("#txtDiaChi").val()
	       			},
	       			{
				        name : 'khoa',
				        type : 'String',
				        value : $("#cboKhoa").val().toString()
	       			},
	       			{
				        name : 'chandoan',
				        type : 'String',
				        value : $("#txtChanDoan").val()
	       			},
	       			{
				        name : 'bacsy',
				        type : 'String',
				        value : $("#txtBacSi").val()
	       			},
	       			{
				        name : 'tungay',
						type : 'String',
						value : CLS_TKCDHA_DATETYPE == false ? $("#txtTuNgay").val() + " 00:00:00" : $("#txtTuNgay").val()
	       			},
	       			{
	       				name : 'denngay',
						type : 'String',
						value : CLS_TKCDHA_DATETYPE == false ? $("#txtDenNgay").val() + " 23:59:59" : $("#txtDenNgay").val()
	       			},
	       			{
	       				name : 'timtheo',
						type : 'String',
						value : $("input:radio[name='ngaytimkiem']:checked").val()
	       			}
				];
				
				CommonUtil.inPhieu('window', 'BC_DSBN_THUCHIENCLS', typeIn, _param, 'baocaothongke.xls');
			}					
		});
	}
	
	function validateDate(ctrId, key, checkNull){
		var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
		
		if(checkNull && ($('#' + ctrId).val() == null || $('#' + ctrId).val() == '')){
			DlgUtil.showMsg(key + $.i18n("require"),undefined,3000);
			$('#' + ctrId).focus();
			return false;
		}
		
		if($('#' + ctrId).val().trim().length > 0 && !datetimeRegex.test($('#' + ctrId).val()) && !checkDate($('#' + ctrId).val())){
			DlgUtil.showMsg(key + $.i18n("date_type_invalid"),undefined,3000);
			$('#' + ctrId).focus();
			return false;		
		}
		return true;
	}

	function kiemTra(){
		if(validateDate("txtTuNgay","Từ ngày ", false) && validateDate("txtDenNgay", "Đến ngày ", false)){
			var txtTuNgay = $("#txtTuNgay").val();
			var txtDenNgay = $("#txtDenNgay").val();
			
			var d1 = stringToDateFormat(txtTuNgay);
			var d2 = stringToDateFormat(txtDenNgay);
			
			if(!compareDate(txtTuNgay,txtDenNgay,"DD/MM/YYYY")){
				DlgUtil.showMsg("Từ ngày không được lớn hơn Đến ngày");	
				$("#txtTuNgay").focus();
				return false;
			}
		}		
		return true;
	}
	
	// load kết quả thống kê
	function timKiemThongKe(){
		if(kiemTra()){
			var param = RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,
										$("#txtSoPhieu").val(),
										$("#txtMaBN").val(),
										$("#txtTenBN").val(),
										$("#cboDoiTuong").val(),
										$("#txtTuoi").val(),
										$("#cboGioiTinh").val(),
										$("#txtDiaChi").val(),
										$("#cboKhoa").val().toString(),
										$("#txtChanDoan").val(),
										$("#txtBacSi").val(),
										CLS_TKCDHA_DATETYPE == false ? $("#txtTuNgay").val() + " 00:00:00" : $("#txtTuNgay").val(),
										CLS_TKCDHA_DATETYPE == false ? $("#txtDenNgay").val() + " 23:59:59" : $("#txtDenNgay").val(),
										$("input:radio[name='ngaytimkiem']:checked").val()]);
			
			GridUtil.loadGridBySqlPage("grdThongKeCDHA","CLS02C004.TKPG",param);
		}
	}
	
	function doViewReport(fileType, reportCode , _param) {
		var winReport = window.open(window.location.protocol + "//" + window.location.host + "/vnpthis/report/viewReport.jsp", '_blank');
		setTimeout(function (){
			winReport.close();
			
			var sql_par=[];
			sql_par.push({"name":"[0]","value":reportCode});	
			var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D044.19",sql_par);
			var data_ar_af = JSON.parse(data_ar);
			var arrayLength = data_ar_af.length;
			for (var i = 0; i < arrayLength; i++) {
				var iframe = document.getElementById("ifmXLS");
				var report_url= window.location.protocol + "//" + window.location.host + "/" + "dreport/";
				if(report_url=='') report_url="../";
				if(fileType=='grid') {
					document.getElementById("ifmXLS").src = report_url+"report/parameter/ParamBuilder?report_id="+ data_ar_af[0].REPORT_ID+"&filetype="+fileType+"&reportParam="+ getParamToServer(_param)+"&db_name="+DATA_DB_NAME+"&db_schema="+DB_SCHEMA_NAME;
				}
				else {
					document.getElementById("ifmXLS").src = report_url+"report/parameter/ParamBuilder?report_id="+ data_ar_af[0].REPORT_ID+"&filetype="+fileType+"&reportParam="+ getParamToServer(_param);					
				}
				if (navigator.userAgent.indexOf("MSIE") > -1 && !window.opera){
				    iframe.onreadystatechange = function(){
				        if (iframe.readyState == "complete"){
				        	$("#dLoading").addClass("hidden");
				        }
				    };
				} else {
				    iframe.onload = function(){		    	
				    	$("#dLoading").addClass("hidden");
				    };
				}
			}
		},1000);
	}
	
	function getParamToServer(_param){
		var par_data=JSON.stringify(_param);
		var par_str=window.btoa(unescape(encodeURIComponent(par_data))); //base64.encode(par_data);
		console.log("par_str11="+par_str);
		return par_str;
	}
}
