/*
 Mã màn hình  : DUC02N001
File mã nguồn : DUC02N001_NhapThuocNCC_PhuPhi.js
<PERSON><PERSON><PERSON>  : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> c<PERSON> , <PERSON><PERSON><PERSON><PERSON> xu<PERSON>t c<PERSON><PERSON> và thêm phụ phí
<PERSON> lập tr<PERSON><PERSON> cập nhật  <PERSON> chú
ChuanNT	- 02/08/2017 - tạo mới
 */

function _NhapKhoNCC(_opt) {
	
	this.load=doLoad;
	this.opt = $.extend({},_opt);
	var that = this;

	var _keyField = "NHAPXUATID";
	var _gridId = "gridList_GCT";
	var _gridId1 = "gridList_THUOC";
	var _gridId2 = "gridList_PhuPhi";
	var _gridId3 = "gridList_HDKN";
	var _param = session_par;
	var title_thuocvatu = '';
	var _loaikho = '0';
	var _loaiphieunhap ='NKNCC';
	var _loai_thuocvattu = '';

	var _kieu = "";
	var _trangthai = "";
	var _hinhthuc = "";
	var _title_xuatnhap = "";
	var _daclick_luuphieu = '0';
	var _nguoidangnhap = "";
	var _luuvadong = '0';
	var _succes;
	var _thuocid;
	var _nhapkhoduyet ='0';
	var _loaihuyphieu = '1';
	var _nguonchuongtrinh = '1';
	var _nxid_bosung ='0';
	var hi_thuocid = '0';
	var PHARMA_TVT_NHIEU_MUC_GIA = '0';
	var PHARMA_SOSANH_GIATHAU = '0';
	var PHARMA_CHECK_NGUONCT = '0';
	var PHARMA_TINH_TYLEHUHAO = '0';
	var PHARMA_DIACHI_NGUOIHIENMAU = '0';
	var PHARMA_TUDONG_INPHIEUNHAP = '0';
	var PHARMA_LAYGIA_2SOLE = '0';
	var PHARMA_LOADGRID_NHAPNCC = '0';
	var PHARMA_NHOMTVT_THIEU_SOLO_HSD = '0';
	var PHARMA_DISABLED_NGAYLAP = '0';
	var PHARMA_DISPLAY_IMPORTTVT = '0';
	var PHARMA_BO_CHECK_SOCHUNGTU = '0';
	var PHARMA_FIX_DONGIAVAT_THEO_DM = '0';
	var PHARMA_TONGTIEN_SOLE_NCC = '0';
	var PHARMA_LOADDONGIA = '0';
	var PHARMA_CHECKGIABHYT = '0';
	var PHARMA_LAYTHEONGUONCT = '0';
	var PHARMA_CHECK_SOHOADON_THEO_NGAYCT ='0';
	var PHARMA_NHAPKHO_CONGTHUC_TYLEHUHAO ='0';
	var _sole = 3;
	var _lamtron = 2;
	var MA_NHOM = '';
	var changeSoLuong =0;
	var that = this;
	this.load = doLoad;

	function doLoad() {
		$("#txtMA_PHIEU").focus();
		$("#lblNguoiGiao").text('Người giao');
	
		/*if ($("#hidEdit").val() == '1') {
			$('#btnLuu').removeAttr("disabled");
			$('#btnLuuDong').removeAttr("disabled");
			$('#btnXoaThuoc').removeAttr("disabled");
			
			 * if(Number($('#hidThuocVatTuID').val()) > 0){
			 * $('#btnChonKho').removeAttr("disabled"); }
			 
		}*/
		 
			$("#divThanhToan").show();
			$("#divConLai").show();
		
		if(_param[0] == '43263'){ //43263
			$("#btnThemthuoc2").css("display", "");
		}
		 
		var _par = ['PHARMA_TVT_NHIEU_MUC_GIA'];
		 PHARMA_TVT_NHIEU_MUC_GIA = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
				_par.join('$'));
		 
			var _par_giathau = ['PHARMA_SOSANH_GIATHAU'];
			PHARMA_SOSANH_GIATHAU = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
					_par_giathau.join('$'));
			
			var _par_ngct = ['PHARMA_CHECK_NGUONCT'];
			PHARMA_CHECK_NGUONCT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
					_par_ngct.join('$'));
			PHARMA_TINH_TYLEHUHAO = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_TINH_TYLEHUHAO');
			PHARMA_DIACHI_NGUOIHIENMAU = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_DIACHI_NGUOIHIENMAU');
			PHARMA_TUDONG_INPHIEUNHAP = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_TUDONG_INPHIEUNHAP');
			PHARMA_LAYGIA_2SOLE = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_LAYGIA_2SOLE');
			PHARMA_LOADGRID_NHAPNCC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_LOADGRID_NHAPNCC');
			PHARMA_DISABLED_NGAYLAP = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_DISABLED_NGAYLAP');
			PHARMA_DISPLAY_IMPORTTVT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_DISPLAY_IMPORTTVT');
			PHARMA_NHOMTVT_THIEU_SOLO_HSD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_NHOMTVT_THIEU_SOLO_HSD');
			PHARMA_BO_CHECK_SOCHUNGTU = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_BO_CHECK_SOCHUNGTU');
			PHARMA_FIX_DONGIAVAT_THEO_DM = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_FIX_DONGIAVAT_THEO_DM');
			PHARMA_TONGTIEN_SOLE_NCC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_TONGTIEN_SOLE_NCC');
			PHARMA_GIA_CHENH_BHYT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_GIA_CHENH_BHYT');
			PHARMA_LOADDONGIA= jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_LOADDONGIA');
			PHARMA_CHECKGIABHYT= jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_CHECKGIABHYT');
			var PHARMA_TONGTIEN_LAMTRON_NCC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_TONGTIEN_LAMTRON_NCC');  
		    PHARMA_LAYTHEONGUONCT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_LAYTHEONGUONCT');  
		    PHARMA_CHECK_SOHOADON_THEO_NGAYCT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_CHECK_SOHOADON_THEO_NGAYCT');  
		    PHARMA_NHAPKHO_CONGTHUC_TYLEHUHAO = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','PHARMA_NHAPKHO_CONGTHUC_TYLEHUHAO'); 
			 
			 if(PHARMA_TONGTIEN_SOLE_NCC == '1'
				 || PHARMA_TONGTIEN_SOLE_NCC == '2' 
					 || PHARMA_TONGTIEN_SOLE_NCC == '3' 
						 || PHARMA_TONGTIEN_SOLE_NCC == '4'
							 || PHARMA_TONGTIEN_SOLE_NCC == '5'
								 || PHARMA_TONGTIEN_SOLE_NCC == '6'){
				 _sole = replaceStrtoNum(PHARMA_TONGTIEN_SOLE_NCC);
			 }else{			
				 _sole = 3;
			 }
			 
			 if(PHARMA_TONGTIEN_LAMTRON_NCC == '1'){
				 _lamtron = 0;
			 }
			 
		switch ($("#hidEdit").val()) {
		  			
		   case '1':
			   $('#cmdLuuPhieu').attr('disabled', 'disabled');
			   $('#cmdLuuDong').attr('disabled', 'disabled');
				break;
				
		   case '2':
				
				break;
		   default:
			   $('#cmdNhapKho').attr('disabled', 'disabled');
			
		}
		var _parMaPhieu = ['PHARMA_CAP_NHAT_MA_PHIEU_NX'];
		var resultMP = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
				_parMaPhieu.join('$'));
		if(resultMP==0) {
			$('#txtMA_PHIEU').attr('disabled', 'disabled');
		}
		var _par = ['PHARMA_KHONG_CAN_KETOANDUOC_DUYET'];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA",
				_par.join('$'));
		
		if(result == '1'){
			_nhapkhoduyet = '1';
		}
		$("#txtbar").css("display", "none");
		//$("#txtmathvt").css("display", "");
	//	_nhapkhoduyet = '1';
		$("#span_nguonct").css("display", "none");
		
	   if(PHARMA_DIACHI_NGUOIHIENMAU == '1'){
		   $("#span_dc_nhm").css("display", "");		
			$("#txt_dc_nhm").css("display", "");
	    }
		if(PHARMA_DISABLED_NGAYLAP == '1'){
			$('#txtNGAY_LAP').attr('disabled', 'disabled');
			$('#ngaylap').attr('disabled', 'disabled');
		}
		 if(PHARMA_DISPLAY_IMPORTTVT == '1'){
			   $("#div_importtvt").css("display", "none");	
		    }
		 if(PHARMA_FIX_DONGIAVAT_THEO_DM == '1'){
				$('#txtDON_GIA').attr('disabled', 'disabled');
				
			}
		if(PHARMA_BO_CHECK_SOCHUNGTU == 1){
			$("#span_sct").css("display", "none");
		}
		// phan quyen theo user
		checkRole('cmdNhapKho');
		
		
		switch ($("#hid_kieu").val()) {
		case 'THUOC':
			if(PHARMA_CHECK_NGUONCT == '1'){$("#span_nguonct").css("display", "");}
			title_thuocvatu = 'thuốc';
			_loaikho = 1;
			_hinhthuc = "1";
			
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			if(_nhapkhoduyet == '1'){
				$("#cmdNhapKho").css("display", "none");
				_kieu = "2";
			}
			else{
				$("#cmdYCNhapBu").css("display", "none");
				_kieu = "0";
			}
			
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			
			$('#divMain').attr("help","help_X_2_1_1_NhapxuatthuoctuNCC");
			if($("#hidTRANGTHAIID").val() == "4"){
				_loadEditNhapKho();
				
			}
			else if($("#hidTRANGTHAIID").val() == "2"){
				_loadcontrolgrid();
				$("#gridList_THUOC").jqGrid("clearGridData", true);
				  $('#txtSO_CHUNG_TU').attr('disabled', 'disabled');
				  $('#txtNGAY_LAP_SCT').attr('disabled', 'disabled');
				  $('#ngayct').attr('disabled', 'disabled');
				  $('#txtNhaCungCap').attr('disabled', 'disabled');
				  $('#cmdAddNhaCungCap').attr('disabled', 'disabled');
				  $('#txtCHIET_KHAU').attr('disabled', 'disabled');
				  $('#txtGHI_CHU').attr('disabled', 'disabled');
				  $('#txtNGUOI_GIAO').attr('disabled', 'disabled');
				
			}
			else{
				_loadcontrolgrid();
			}
			break;
		case 'VATTU':
			if(PHARMA_CHECK_NGUONCT == '1'){$("#span_nguonct").css("display", "");}
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_hinhthuc = "1";
			
			_loai_thuocvattu = ' (tvt.loai in (1,4))';
			if(_nhapkhoduyet == '1'){
				$("#cmdNhapKho").css("display", "none");
				_kieu = "2";
			}
			else{
				$("#cmdYCNhapBu").css("display", "none");
				_kieu = "0";
			}
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			$('#divMain').attr("help","help_X_2_1_1_NhapxuatthuoctuNCC");
			if($("#hidTRANGTHAIID").val() == "4"){
				_loadEditNhapKho();
				
			}else if($("#hidTRANGTHAIID").val() == "2"){
				_loadcontrolgrid();
				$("#gridList_THUOC").jqGrid("clearGridData", true);
				
			
			}
			else{
				_loadcontrolgrid();
			}
			break;

		case 'NHAPBUTHUOC':
			title_thuocvatu = 'thuốc';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			
			$('#divMain').attr("help","help_X_2_3_1_Nhapbuthuoc");
			_loadbuttonNhapBu();
			break;

		case 'NHAPBUVATTU':
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_3_3_Nhapbuvattu");

			_loadbuttonNhapBu();
			break;
		case 'NHAPKHACTHUOC':
			title_thuocvatu = 'thuốc';
			_loaikho = 7;
			_loaihuyphieu = '4';
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$("#span_dc").css("display", "none");
			$("#cbo_dc").css("display", "none");
			$("#span_lydohp").css("display", "");
			$("#cbo_lydohp").css("display", "");
			$('#divMain').attr("help","help_X_2_3_1_Nhapbuthuoc");
			$("#lbLyDo").text("Lý do nhập:");
 			
			_loadbuttonNhapBu();
			break;

		case 'NHAPKHACVATTU':
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_loaihuyphieu = '4';
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_3_3_Nhapbuvattu");
			$("#span_dc").css("display", "none");
			$("#cbo_dc").css("display", "none");
			$("#span_lydohp").css("display", "");
			$("#cbo_lydohp").css("display", "");
			$("#lbLyDo").text("Lý do nhập:");
			_loadbuttonNhapBu();
			break;

		case 'XUATTHIEUTHUOC':
			title_thuocvatu = 'thuốc';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_3_2_Xuatthuathuoc");
			$("#span_dongia").css("display", "none");
			_loadbuttonNhapBu();
			_loaiphieunhap ='XKNCC';
			break;

		case 'XUATTHIEUVATTU':
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_3_4_Xuatthuavattu");
			$("#span_dongia").css("display", "none");
			_loadbuttonNhapBu();
			_loaiphieunhap ='XKNCC';
			break;

		case 'XUATHUYTHUOC':
			title_thuocvatu = 'thuốc';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_1_5_Yeucauxuathuythuoc");
			_loadbuttonNhapBu();
			$("#span_dongia").css("display", "none");
			$("#span_dc").css("display", "none");
			$("#cbo_dc").css("display", "none");
			$("#span_lydohp").css("display", "");
			$("#cbo_lydohp").css("display", "");
			_loaiphieunhap ='XKNCC';
			$("#lydohuyxuat").text('Lý do hủy');
			break;

		case 'XUATHUYVATTU':
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_1_5_Yeucauxuathuythuoc");
			_loadbuttonNhapBu();
			$("#span_dongia").css("display", "none");
			$("#span_dc").css("display", "none");
			$("#cbo_dc").css("display", "none");
			$("#span_lydohp").css("display", "");
			$("#cbo_lydohp").css("display", "");
			$("#lydohuyxuat").text('Lý do hủy');
			_loaiphieunhap ='XKNCC';
			
			break;
		case 'XUATKHACTHUOC':
			title_thuocvatu = 'thuốc';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			_loaihuyphieu = '2';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_1_5_Yeucauxuathuythuoc");
			_loadbuttonNhapBu();
			$("#span_dongia").css("display", "none");
			$("#span_dc").css("display", "none");
			$("#cbo_dc").css("display", "none");
			$("#span_lydohp").css("display", "");
			$("#cbo_lydohp").css("display", "");
			$("#lydohuyxuat").text('Lý do xuất');
			_loaiphieunhap ='XKNCC';
			
			break;

		case 'XUATKHACVATTU':
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			_loaihuyphieu = '2';
			// $("#cmdLuuPhieu").css("display","none");
			$("#cmdNhapKho").css("display", "none");
			$('#divMain').attr("help","help_X_2_1_5_Yeucauxuathuythuoc");
			_loadbuttonNhapBu();
			$("#span_dongia").css("display", "none");
			$("#span_dc").css("display", "none");
			$("#cbo_dc").css("display", "none");
			$("#span_lydohp").css("display", "");
			$("#cbo_lydohp").css("display", "");
			$("#lydohuyxuat").text('Lý do xuất');
			_loaiphieunhap ='XKNCC';
			break;
		case 'DUTRU':
			_hinhthuc = "1";
			_kieu = "0";
			// $("#cmdNhapKho").css("display","none");
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#cmdYCNhapBu").css("display", "none");
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			$("#txtNhaCungCap").val($("#hidTenNCC").val());
			_loadcontrolgrid();
//			reloadCash();

			break;
		case 'XUATDTTHKP':
			title_thuocvatu = 'vật tư';
			_loaikho = 7;
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			// $("#cmdLuuPhieu").css("display","none");
			$("#span_dongia").css("display", "none");
			$("#span_sct").css("display", "none");
			$("#cmdNhapKho").css("display", "none");
			$('#ckbSua').attr('disabled', 'disabled');
			$('#txtTHANH_TOAN_TONG').attr('disabled', 'disabled');
			$('#divMain').attr("help","help_I_1_Huongdanchung");
			_loaiphieunhap ='XKNCC';
			_loadbuttonNhapBu();
			$("#lblNguoiGiao").text('Người tạo');
			$("#txtNGUOI_GIAO").val(_param[4].toString());
			break;
		case 'VPP':
			title_thuocvatu = 'VPP';
			_loaikho = 1;
			_hinhthuc = "1";
			
			_loai_thuocvattu = ' (tvt.loai in (0,3))';
			
			$("#span_solo").css("display", "none");
			$("#span_hsd").css("display", "none");
			
			if(_nhapkhoduyet == '1'){
				$("#cmdNhapKho").css("display", "none");
				_kieu = "2";
			}
			else{
				$("#cmdYCNhapBu").css("display", "none");
				_kieu = "0";
			}
			
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			
			$('#divMain').attr("help","help_X_2_1_1_NhapxuatthuoctuNCC");
			if($("#hidTRANGTHAIID").val() == "4"){
				_loadEditNhapKho();
				
			}
			else if($("#hidTRANGTHAIID").val() == "2"){
				_loadcontrolgrid();
				$("#gridList_THUOC").jqGrid("clearGridData", true);
				  $('#txtSO_CHUNG_TU').attr('disabled', 'disabled');
				  $('#txtNGAY_LAP_SCT').attr('disabled', 'disabled');
				  $('#ngayct').attr('disabled', 'disabled');
				  $('#txtNhaCungCap').attr('disabled', 'disabled');
				  $('#cmdAddNhaCungCap').attr('disabled', 'disabled');
				  $('#txtCHIET_KHAU').attr('disabled', 'disabled');
				  $('#txtGHI_CHU').attr('disabled', 'disabled');
				  $('#txtNGUOI_GIAO').attr('disabled', 'disabled');
				
			}
			else{
				_loadcontrolgrid();
			}
			break;
		default:
			'';
		}
		$("#mathuocvattu").text('Mã ' + title_thuocvatu);
		
		Number.prototype.format = function(n, x) {
		    var re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\.' : '$') + ')';
		  
		    var valueStr = this.toFixed(Math.max(0, ~~n)).replace(new RegExp(re, 'g'), '$&,'); 
		    var last2 = valueStr.slice(-3);
		    if(last2 =='.00')
		    	valueStr = valueStr.substring(0,valueStr.length -3);
		    
		    
		    return valueStr;
		};
		
		 if($('#hid_KHOID').val() == -1){
				$("#txtKHO_LAP").css("display", "none");
				$("#cboKhoLap").css("display", "");
				$("#cmdLuuPhieu").css("display", "none");
				$("#cmdLuuDong").css("display", "none");
				ComboUtil.getComboTag("cboKhoLap", "DUC40K001.01", [""], "", 
						{
							value : '',
							text : '-- Chọn kho --'
						}, "");
			}else{
				 var _par_kho = $("#hid_KHOID").val() + '$';
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC53M001.05", _par_kho);
					MA_KHO = result[0].MAKHO;
					$("#hid_KhoaId").val(result[0].KHOAID);
					$("#txtMA_PHIEU").val(layMaPhieu());
					loadComboGrid_new();
					loadComboGridNCC();
					loadComboGrid_PhuPhi();	
					loadComboGridHDKN();
					//lay hdkn id theo khoa id cua kho
					 var _par_hdkn = $("#hid_KhoaId").val() + '$';
					var data_r = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC_HDKN_KHOA", _par_hdkn);
					if (data_r != null && data_r.length > 0) {
						$("#hid_HDKN_Id").val(data_r[0].HOIDONG_KIEMNHAP_ID);
					}
					
					
					
			}
		 
		
		/*
		 * if($("#hid_NHAPXUATID").val() =="0"){ $("#title").text("NHẬP "+
		 * title_thuocvatu.toUpperCase() +" TỪ NHÀ CUNG CẤP"); } else{
		 * $("#title").text("SỬA PHIẾU ĐÃ TẠO"); }
		 */

		// var userid=_param[2];
		// lay ten nguoi dang nhap
		_nguoidangnhap = _param[4];
		// var _khoid = $("#hid_KHOID").val();		
		GridUtil.init(_gridId1, "100%", "140px",
				"Danh sách " + title_thuocvatu, false,
				"THUOCVATTUID,THUOCVATTUID,0,0,t,l;" + "Mã " + title_thuocvatu
						+ ",MA_THUOC,100,0,f,l;Tên " + title_thuocvatu
						+ ",TEN_THUOC,200,0,f,l;"
						+ "Biệt dược,BIET_DUOC,60,0,f,l;"
						+ "Đơn vị,DON_VI_TINH,70,0,f,l;"
						+ "Số lượng ,SL_YC,80,number,f,r;"
						+ "SL Duyệt,SL_DUYET,80,number,t,r;"						
						+ "Đơn giá,DON_GIA,90,decimal!"+_sole+",f,r;"
						+ "VAT%,VAT,60,number,f,r;"
						+ "Đơn giá VAT,DON_GIA_VAT,90,decimal!3,f,r;"
						+ "Tiền trước VAT,TIEN_TRUOC_VAT,120,decimal!3,f,r;"
						+ "Thuế GTGT,THUEGTGT,80,decimal!3,f,r;"
						+ "Thành tiền,THANH_TIEN,130,decimal!3,f,r;"
						+ "Số lô,SO_LO,60,0,f,l;"
						+ "Hàm lượng,LIEULUONG,70,0,f,l;"
						+ "Nguồn CT,NGUOC_CT,75,0,t,l;"
						+ "NUOC_SAN_XUAT,NUOC_SAN_XUAT,0,0,t,l;"
						+ "HANG_SAN_XUAT,HANG_SAN_XUAT,0,0,t,l;"	
						+ "GIA_BAN,GIA_BAN,0,number,t,l;"
						+ "SDK,SDK,0,0,t,l;" 
						+ "GOI_THAU,GOI_THAU,0,0,t,l;"
						+ "HSD,HSD,0,0,t,l;"
						+ "DONVITINHID,DONVITINHID,0,0,t,l;"
						+ "DVTNHANID,DVTNHANID,0,0,t,l;"
						+ "SL_DVT_CU,SL_DVT_CU,0,0,t,l;"
						+ "GIANHANDAN,GIANHANDAN,0,number,t,l;"
						+ "GIADICHVU,GIADICHVU,0,number,t,l;"
						+ "GIABHYT,GIABHYT,0,number,t,l;"
						+ "GIATRANBHYT,GIATRANBHYT,0,number,t,l;"
						+ "STTTHAU,STTTHAU,30,0,t,l;"
						+ "TYLEHUHAO,TYLEHUHAO,0,0,t,l;"
						+ "MANHOM,MANHOM,0,0,t,l;"
						+ "ACT,ACT,40,d,f,l", false,
						{ 
							rowNum : 3000,
							rowList : [ 3000, 5000 ]
							,loadonce : true
							,sqltype: 'sp'	
						}
		);
		
		
		// danh sach phu phi
		GridUtil.init(_gridId2, "100%", "200px",
				"Danh sách phụ phí", false,
				"THUOCVATTUID,THUOCVATTUID,0,0,t,l;"
						+ "Mã phụ phí,MA_THUOC,80,0,f,l;" 
						+ "Mã BYT ,MA_BYT,80,0,f,l;"
						+ "Tên BYT,TEN_BYT,110,0,f,l;"
						+ "Mã hoạt chất,MAHOATCHAT,80,0,f,l;"						
						+ "Hoạt chất,HOATCHAT,110,0,f,l;"
						+ "Giá nhập,DON_GIA,90,number,f,r;"	
						+ "Giá bán,DON_GIA_VAT,90,number,f,r;"						
						+ "Giá nhân dân,GIANHANDAN,90,number,f,r;"
						+ "Giá dịch vụ,GIADICHVU,90,number,f,r;"
						+ "Giá BHYT,GIABHYT,90,number,f,r;"
						+ "Giá trần BHYT,GIATRANBHYT,90,number,f,r;"						
						+ "ACT,ACT,30,d,f,l", false,
						{ 
							rowNum : 3000,
							rowList : [ 3000, 5000 ]
							,loadonce : true
							,sqltype: 'sp'	
						}
		);
		
		// ds hội đồng kiểm nhập
		
		GridUtil.init(_gridId3, "100%", "200px",
				"Danh sách hội đồng kiểm nhập", false,
				"HOIDONG_KIEMNHAP_ID,HOIDONG_KIEMNHAP_ID,0,0,t,l;"
				+ "NHAPXUATID,NHAPXUATID,0,0,t,l;"
						+ "STT,STT,50,0,f,l;" 
						+ "Chức danh,CHUCDANH,200,0,f,l;"
						+ "Họ tên,HOTEN,300,0,f,l"	,										
					//	+ "ACT,ACT,30,d,f,l", 
						false,
						{ 
							rowNum : 3000,
							rowList : [ 3000, 5000 ]
							,loadonce : true
							,sqltype: 'sp'	
						}
		);
		
		// $('.ui-search-toolbar').hide();
		//$("#txtMA_PHIEU").val(layMaPhieu());
		ComboUtil.getComboTag("cboNguonCT", "DUC67T001.28", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "", {
			value : "",
			text : 'Chọn'
		}, "");
		if ($("#hid_NHAPXUATID").val() != "0") {
			// DlgUtil.showMsg($("#hid_NHAPXUATID").val());
			loadEditorPhieu($("#hid_NHAPXUATID").val(), 1);			
			var param = RSUtil.buildParam("", [ $("#hid_NHAPXUATID").val() ]);
			GridUtil.loadGridBySqlPage(_gridId2, "DUC02N001.05", param);
			
			if($("#hidTRANGTHAIID").val() == "2"){
			
				$("#gridList_THUOC").jqGrid("clearGridData", true).trigger("reloadGrid");
				
			}
			if(PHARMA_CHECKGIABHYT=='1'){
				SetGiaBHYT();
			}
			
			
		}
		$("#txtNGAY_LAP").val(_creat_ngaylap($("#txtNGAY_LAP").val()));
		$("#txtNGAY_LAP_SCT").val(_creat_ngaylap($("#txtNGAY_LAP_SCT").val()));
		if ($("#hidDuTruId").val() != "") {
			_loai_thuocvattu = ' (tvt.loai in (0,1,2))';
			title_thuocvatu = "thuốc vật tư";

			if ($("#hid_TENKHO").val() == "") {
				DlgUtil.showMsg("Không có dư liệu du trù!");
				return;
			}
//			loadEditorPhieu($("#hid_NHAPXUATID").val(), 1);
			var sql_par = [];		
			var param = RSUtil.buildParam("", [ $("#hidDuTruId").val() ]);			
			GridUtil.loadGridBySqlPage("gridList_THUOC", "DUC04D001.15", param);			
		
			
//			loadEditorPhieu($("#hid_NHAPXUATID").val(), 1);
		}
		
		

		// 
		/*ComboUtil.getComboTag("txtNhaCungCap", "DUC04D001.01", [ {
			"name" : "[0]",
			"value" : "1"
		} ], $("#hid_NHACUNGCAPID").val(), "", "sql");*/
		
		/*ComboUtil.getComboTag("cboLyDoHuyPhieu", "DUC04D001.18", [""], "", 
				{
					value : '0',
					text : '-- Chọn Lý do hủy phiếu --'
				}, "");*/
		//ComboUtil.getComboTag("cboLoaiThuocVT","DUC54T001.05",[{"name":"[0]", "value":par}], "", "","sql");
		
		sql_par = RSUtil.buildParam("",[_loaihuyphieu]);
		
			ComboUtil.getComboTag("cboLyDoHuyPhieu","DUC04D001.18",sql_par,$("#hid_LYDOHUYPHIEU").val(),{value:0,text:'------Chọn-----'},"sql","",false);
		/*ComboUtil.getComboTag("cboLyDoHuyPhieu", "DUC04D001.18", [ {
			"name" : "[0]",
			"value" : _loaihuyphieu
		} ], $("#hid_LYDOHUYPHIEU").val(), "", "sql");
		*/
			
			
		 
			
			
		bindEvent();
		loadData_HDKN();
		// $("#txtGT_THE_TU").val(getDateVNFormated(new Date(new
		// Date().getFullYear(), 0, 1)));
		// $("#txtGT_THE_DEN").val(getDateVNFormated(new Date(new
		// Date().getFullYear(), 11, 31)));
		$("#txtKHO_LAP").val($("#hid_TENKHO").val());
		// $("#txtNhaCungCap").append('<option value=0
		// selected>--------Chọn---------</option>');
		

	}
	
		
			
	
	function setSysParam(_par_ar) {
		var v_par = _par_ar;
		for (var i1 = 0; i1 < _param.length; i1++) {
			console.log('_param[' + i1 + ']=' + _param[i1]);
			v_par.push({
				"name" : "[S" + i1 + "]",
				"value" : _param[i1]
			});
		}
		return v_par;
	}

	function _loadbuttonNhapBu() {
		switch ($("#hidLoaiNhapBu").val()) {
		case 'YC_NHAPBU':
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			_kieu = "2";
			_trangthai = "1";
			_hinhthuc = "5";
			_title_xuatnhap = "nhập bù";
			$("#span_ncc").css("display", "none");
			
			break;
		case 'NHAPBU':
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#cmdYCNhapBu").css("display", "none");
			$("#txtMA_THUOC").attr('readonly', true);
			$("#cmdLuuThuocKho").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_kieu = "0";
			_trangthai = "4";
			_hinhthuc = "5";
			_title_xuatnhap = "nhập bù";
			_loadNhapBu();
			$("#span_ncc").css("display", "none");
			break;
		case 'YC_NHAPKHAC':
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			_kieu = "2";
			_trangthai = "1";
			_hinhthuc = "8";
			_title_xuatnhap = "nhập bù";
			$("#span_ncc").css("display", "none");
			
			break;
		case 'YC_XUATTHIEU':
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			_kieu = "3";
			_trangthai = "1";
			_hinhthuc = "6";
			_title_xuatnhap = "xuất thiếu";
			$("#span_ncc").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_dongia").css("display", "none");
			$("#span_solo").css("display", "none");
			$('#txtNhaCungCap').attr('disabled', 'disabled');
			$('#cmdAddNhaCungCap').attr('disabled', 'disabled');
			break;
		case 'XUATTHIEU':
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#cmdYCNhapBu").css("display", "none");
			$("#txtMA_THUOC").attr('readonly', true);
			$("#cmdLuuThuocKho").css("display", "none");
			$("#cmdNhapBu").css("display", "none");
			_kieu = "1";
			_trangthai = "4";
			_hinhthuc = "6";
			_title_xuatnhap = "xuất thiếu";
			_loadNhapBu();
			$("#span_ncc").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_dongia").css("display", "none");
			break;

		case 'YC_XUATHUY':
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			_kieu = "3";
			_trangthai = "1";
			_hinhthuc = "7";
			_title_xuatnhap = "xuất hủy";
			$("#span_ncc").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_dongia").css("display", "none");
			$('#txtNhaCungCap').attr('disabled', 'disabled');
			$('#cmdAddNhaCungCap').attr('disabled', 'disabled');
			break;
		case 'XUATHUY':
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#cmdYCNhapBu").css("display", "none");
			$("#txtMA_THUOC").attr('readonly', true);
			$("#cmdLuuThuocKho").css("display", "none");
			$("#cmdNhapBu").css("display", "none");
			_kieu = "1";
			_trangthai = "4";
			_hinhthuc = "7";
			_title_xuatnhap = "xuất hủy";
			_loadNhapBu();
			$("#span_ncc").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_dongia").css("display", "none");
			break;
			
		case 'YC_XUATKHAC':
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			_kieu = "3";
			_trangthai = "1";
			_hinhthuc = "8";
			_title_xuatnhap = "xuất khác";
			$("#span_ncc").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_dongia").css("display", "none");
			$('#txtNhaCungCap').attr('disabled', 'disabled');
			$('#cmdAddNhaCungCap').attr('disabled', 'disabled');
			break;
		case 'XUATKHAC':
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#cmdYCNhapBu").css("display", "none");
			$("#txtMA_THUOC").attr('readonly', true);
			$("#cmdLuuThuocKho").css("display", "none");
			$("#cmdNhapBu").css("display", "none");
			_kieu = "1";
			_trangthai = "4";
			_hinhthuc = "8";
			_title_xuatnhap = "xuất khác";
			_loadNhapBu();
			$("#span_ncc").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_dongia").css("display", "none");
			break;
			
		case 'YC_XUATDTTHKP':
			$("#cmdNhapBu").css("display", "none");
			$("#cmdXuatHuy").css("display", "none");
			_loadcontrolgrid();
			_kieu = "3";
			_trangthai = "1";
			_hinhthuc = "4";
			_title_xuatnhap = "dự trù tiêu hao khoa phòng";
			$("#span_ncc").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_dongia").css("display", "none");
			$('#txtNhaCungCap').attr('disabled', 'disabled');
			$('#cmdAddNhaCungCap').attr('disabled', 'disabled');
			break;
		case 'XUATDTTHKP':
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#cmdYCNhapBu").css("display", "none");
			$("#txtMA_THUOC").attr('readonly', true);
			$("#cmdLuuThuocKho").css("display", "none");
			$("#cmdNhapBu").css("display", "none");
			_kieu = "1";
			_trangthai = "4";
			_hinhthuc = "4";
			_title_xuatnhap = "dự trù tiêu hao khoa phòng";
			_loadNhapBu();
			$("#span_ncc").css("display", "none");
			$("#span_solo").css("display", "none");
			$("#span_hsd").css("display", "none");
			$("#span_dongia").css("display", "none");
			break;

		default:
			'';
		}
	}
	function _loadcontrolgrid() {
	
			$("#" + _gridId1).bind(
					"CustomAction",
					function(e, act, rid) {
					/*	DlgUtil
						.showConfirm("Bạn có muốn xóa bản ghi này ko ?",
								function(flag) {
									if (flag) {
										$('#' + _gridId1).jqGrid(
												'delRowData', rid);											
										       reloadCash();
										    
										return true;
									}

								});*/
						// console.log("CustomAction act="+act+" rid="+rid);
						// return true;
					
						
						$('#' + _gridId1).jqGrid(
								'delRowData', rid);											
						       reloadCash();
					});
			
			
			$("#" + _gridId2).bind(
					"CustomAction",
					function(e, act, rid) {
				
						
						$('#' + _gridId2).jqGrid(
								'delRowData', rid);											
						       //reloadCash();
					});
			
			
		
		
		
	}
	function _loadNhapBu() {
		$("#txtMA_PHIEU").attr('readonly', true);
		$("#txtSO_CHUNG_TU").attr('readonly', true);
		$("#txtCHIET_KHAU").attr('readonly', true);
		$('#txtNGAY_LAP').attr('disabled', 'disabled');
		$("#txtNGUOI_GIAO").attr('readonly', true);
		$("#txtGHI_CHU").attr('readonly', true);
		$("#txtTHANH_TOAN_TONG").attr('readonly', true);

		$('#txtNhaCungCap').attr('disabled', 'disabled');
		$('#cmdAddNhaCungCap').attr('disabled', 'disabled');
		$('#cboLyDoHuyPhieu').attr('disabled', 'disabled');
		$('#txtNGAY_LAP_SCT').attr('disabled', 'disabled');
		$('#ngayct').attr('disabled', 'disabled');
		$('#txtHSD').attr('disabled', 'disabled');
		$('#hsd').attr('disabled', 'disabled');
		$('#ngaylap').attr('disabled', 'disabled');

	}
	function _loadEditNhapKho() {
		
		$("#cmdLuuDong").css("display", "none");
		$("#cmdYCNhapBu").css("display", "none");
		$('#cmdNhapKho').css("display", "none");
	
		$("#cmdLuuThuocKho").css("display", "none");
		$("#cmdNhapBu").css("display", "none");
		
		$("#txtMA_THUOC").attr('readonly', true);
	
		$('#cmdGiaBan').removeAttr("disabled");	
		$('#cmdLuuThuocKho').removeAttr("disabled");
		$('#cboLyDoHuyPhieu').attr('disabled', 'disabled');
		$('#txtHSD').attr('disabled', 'disabled');
		$('#hsd').attr('disabled', 'disabled');


	}
	function fillGia()
	{
		var thanhtien = 0;
		var donGiaVAT = 0;
		if(PHARMA_FIX_DONGIAVAT_THEO_DM != '1'){
			 donGiaVAT =(
					((Number($('#txtVAT').val())/100)
							* replaceStrtoNum($('#txtDON_GIA').val()))
							+ replaceStrtoNum($('#txtDON_GIA').val())) ;
			$("#txtDON_GIA_VAT").val(Number(donGiaVAT).format(3,3));
			 thanhtien = (
					replaceStrtoNum($('#txtSO_LUONG').val())
					* donGiaVAT);
		}else{
			 thanhtien = (
					replaceStrtoNum($('#txtSO_LUONG').val())
					* replaceStrtoNum($('#txtDON_GIA_VAT').val()));
			 donGiaVAT = replaceStrtoNum($('#txtDON_GIA_VAT').val());			
			 
			 var donGia =(
						replaceStrtoNum($('#txtDON_GIA_VAT').val())*100/(100 + Number($('#txtVAT').val()))
						).toFixed(6);
				if(_sole != 3){
					$('#txtDON_GIA').val(Number(donGia).format(_sole,3));
				}else{
					$('#txtDON_GIA').val(Number(donGia).format(4,3));
				}
				
			
		}
		
		
		
		var thanhtientruocvat =replaceStrtoNum($('#txtSO_LUONG').val())* replaceStrtoNum($('#txtDON_GIA').val());
		
		$("#txtTHANH_TIEN").val(Number(thanhtien).format(2, 3));
		$("#hid_thanhtientruocvat").val(Number(thanhtientruocvat).format(2,3));
		if(PHARMA_SOSANH_GIATHAU == '1'){
			if(parseInt($('#txtDON_GIA_VAT').val().replaceAll(',',"")) > parseInt($('#txtGIABAN').val().replaceAll(',',""))){
				DlgUtil.showMsg("Đơn giá VAT đã lớn hơn Giá Thầu!",undefined,2000);
			}
		}else if(PHARMA_SOSANH_GIATHAU == '2'){
			if(parseInt($('#txtDON_GIA_VAT').val().replaceAll(',',"")) != parseInt($('#txtGIABAN').val().replaceAll(',',""))){
				DlgUtil.showMsg("Đơn giá VAT khác Giá Thầu!",undefined,2000);
			}	
		}
		
		if(PHARMA_TVT_NHIEU_MUC_GIA == '1' && PHARMA_TINH_TYLEHUHAO == '1'){
			var taisd_huhao = '0';
			 if(Number($("#hid_TYLEHUHAO").val()) > 0){
					
				 taisd_huhao = replaceStrtoNum($('#txtDON_GIA_VAT').val()) + replaceStrtoNum($('#txtDON_GIA_VAT').val()) * (replaceStrtoNum($("#hid_TYLEHUHAO").val()) / 100 );
			 }else{taisd_huhao = donGiaVAT;}
			if(changeSoLuong == 1 && PHARMA_LOADDONGIA  =='0'){
				$("#txtGIA_NHAN_DAN").val(Number(taisd_huhao).format(3,3));
				$("#txtGIA_DICH_VU").val(Number(taisd_huhao).format(3,3));
				$("#txtGIA_BHYT").val(Number(taisd_huhao).format(3,3));
				$("#txtGIA_TRAN_BHYT").val(Number(taisd_huhao).format(3,3));
			}
		}
		
		if(PHARMA_NHAPKHO_CONGTHUC_TYLEHUHAO == '1'){
			var taisd_huhao = replaceStrtoNum($('#txtDON_GIA').val());
			if(Number($("#hid_TYLEHUHAO").val()) > 0){
				taisd_huhao = (100 * taisd_huhao)/(100-replaceStrtoNum($("#hid_TYLEHUHAO").val()));
			}
			$("#txtDON_GIA_VAT").val(Number(taisd_huhao).format(3,3));
			$("#txtGIA_NHAN_DAN").val(Number(taisd_huhao).format(3,3));
			$("#txtGIA_DICH_VU").val(Number(taisd_huhao).format(3,3));
			$("#txtGIA_BHYT").val(Number(taisd_huhao).format(3,3));
			$("#txtGIA_TRAN_BHYT").val(Number(taisd_huhao).format(3,3));
			$("#txtGIABAN").val(Number(taisd_huhao).format(3,3));
		}
	}
	$('input[name=radio]').change(
			function() {
				if($('input[name=radio]:checked').val() == '1')
				{
					$("#lblbar").css("display", "");
					$("#txtbar").css("display", "");					
					$("#lblmathvt").css("display", "none");
					$("#txtmathvt").css("display", "none");
				}
				if($('input[name=radio]:checked').val() == '0')
				{
					$("#lblbar").css("display", "none");
					$("#txtbar").css("display", "none");					
					$("#lblmathvt").css("display", "");
					$("#txtmathvt").css("display", "");
				}
				
				
			});
	$('#cboMa_Barcode').change(
			function(){
				if($('#cboMa_Barcode').val()=='0'){				
					$("#txtbar").css("display", "none");
					$("#txtmathvt").css("display", "");
				}
				else {
					$("#txtbar").css("display", "");
					$("#txtmathvt").css("display", "none");
				}
			}
			);
	
	$('#txtSO_LUONG').change(
			function() {
				if(!isNumeric(replaceStrtoNum($('#txtSO_LUONG').val())))
				{
					DlgUtil.showMsg("Số lượng không đúng!",undefined,2000);
					$('#txtSO_LUONG').focus();
					return;
				}
				$('#hid_SLDVT_CU').val($('#txtSO_LUONG').val());
				if(Number($('#hidSLQuyĐoiVT').val()) > 1){
					var soluongnhan = '0';
					soluongnhan = replaceStrtoNum($('#txtSO_LUONG').val()) * Number($('#hidSLQuyĐoiVT').val());
					$('#txtSO_LUONG').val(soluongnhan);					
				}
				$('#txtSO_LUONG').val(Number(replaceStrtoNum($('#txtSO_LUONG').val())).format(3,3));
				 changeSoLuong = 1;
				 fillGia();	 
			});
	
	$('#txtNGAY_LAP_SCT').change(
			function() {
				if(!isDate($('#txtNGAY_LAP_SCT').val()))
					{
						DlgUtil.showMsg("Ngày lập không đúng!",undefined,2000);
						$('#txtNGAY_LAP_SCT').focus();
					}
			});
	$('#txtHSD').change(
			function() {
				if(!isDate($('#txtHSD').val()))
					{
						DlgUtil.showMsg("Hạn sử dụng không đúng!",undefined,2000);
						$('#txtHSD').focus();
					}
			});

	$('#txtDON_GIA').change(
			function() {
				if(!isNumeric(replaceStrtoNum($('#txtDON_GIA').val())))
					{
					DlgUtil.showMsg("Đơn giá không đúng!",undefined,2000);
					$('#txtDON_GIA').focus();
					return;
				}
				if(replaceStrtoNum($("#txtDON_GIA").val()) == '0'){
					DlgUtil.showMsg("Lưu ý. Đơn giá = 0!",undefined,2000);
					$('#txtDON_GIA').focus();
				}
					
//				replaceStrtoNum
				$('#txtDON_GIA').val(Number(replaceStrtoNum($('#txtDON_GIA').val())).format(_sole,3));
				fillGia();
//				var x = formatNumber($('#txtDON_GIA').val());
//				//x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
//				$('#txtDON_GIA').val(x);
				
				
				if(PHARMA_TVT_NHIEU_MUC_GIA == '1' && PHARMA_TINH_TYLEHUHAO == '1'){
					var taisd_huhao = '0';
					 if(Number($("#hid_TYLEHUHAO").val()) > 0){
							
						 taisd_huhao = replaceStrtoNum($('#txtDON_GIA_VAT').val()) + replaceStrtoNum($('#txtDON_GIA_VAT').val()) * (replaceStrtoNum($("#hid_TYLEHUHAO").val()) / 100 );
					 }else{taisd_huhao = $('#txtDON_GIA_VAT').val();}
					
					$("#txtGIA_NHAN_DAN").val(taisd_huhao);
					$("#txtGIA_DICH_VU").val(taisd_huhao);
					$("#txtGIA_BHYT").val(taisd_huhao);
					$("#txtGIA_TRAN_BHYT").val(taisd_huhao);
				}
				if(PHARMA_NHAPKHO_CONGTHUC_TYLEHUHAO == '1'){
					var taisd_huhao = replaceStrtoNum($('#txtDON_GIA').val());
					if(Number($("#hid_TYLEHUHAO").val()) > 0){
						taisd_huhao = (100 * taisd_huhao)/(100-replaceStrtoNum($("#hid_TYLEHUHAO").val()));
					}
					$("#txtDON_GIA_VAT").val(Number(taisd_huhao).format(3,3));
					$("#txtGIA_NHAN_DAN").val(Number(taisd_huhao).format(3,3));
					$("#txtGIA_DICH_VU").val(Number(taisd_huhao).format(3,3));
					$("#txtGIA_BHYT").val(Number(taisd_huhao).format(3,3));
					$("#txtGIA_TRAN_BHYT").val(Number(taisd_huhao).format(3,3));
					$("#txtGIABAN").val(Number(taisd_huhao).format(3,3));
				}
			});
	
	$('#txtGIA_NHAN_DAN').change(
			function() {
				
				$('#txtGIA_NHAN_DAN').val(Number(replaceStrtoNum($('#txtGIA_NHAN_DAN').val())).format(3,3));
			
			});
 
	
	$('#txtGIABAN').change(
			function() {
				
				$('#txtGIABAN').val(Number(replaceStrtoNum($('#txtGIABAN').val())).format(3,3));
			
			});
	
	$('#txtGIA_DICH_VU').change(
			function() {
				
				$('#txtGIA_DICH_VU').val(Number(replaceStrtoNum($('#txtGIA_DICH_VU').val())).format(3,3));
			
			});
	
	$('#txtGIA_BHYT').change(
			function() {
				
				$('#txtGIA_BHYT').val(Number(replaceStrtoNum($('#txtGIA_BHYT').val())).format(3,3));
			
			});
	
	$('#txtGIA_TRAN_BHYT').change(
			function() {
				
				$('#txtGIA_TRAN_BHYT').val(Number(replaceStrtoNum($('#txtGIA_TRAN_BHYT').val())).format(3,3));
			
			});
	$("#txtMA_THUOC").change(
			function() {
				//$("#hidTHUOCVATTU").val("0");
			}
			);
	
	$("#txtMA_BARCODE").change(
			function() {
				//$("#hidTHUOCVATTU").val("0");
				
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC_GETBY_BARCODE", $("#txtMA_BARCODE").val());
				if(fl == "0"){
					DlgUtil.showMsg("Mã barcode chưa có trong hệ thống,hãy cập nhật vào thuốc tương ứng!");
				}
				else{loadEditorData(fl,"1");}
				
				
			}
			);
	
	

	$('#txtVAT').change(
			function() {
				if(!isNumeric(replaceStrtoNum($('#txtVAT').val())))
				{
					DlgUtil.showMsg("Vat không đúng!",undefined,2000);
					$('#txtVAT').focus();
					return;
				}
				fillGia();
//				var x = formatNumber($('#txtVAT').val());
//				//x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
//				$('#txtVAT').val(x);
			});
	
	$('#cboKhoLap').change(
			function() {
				$('#hid_KHOID').val($('#cboKhoLap').val());
				//doLoad();
				 var _par_kho = $("#hid_KHOID").val() + '$';
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC53M001.05", _par_kho);
					MA_KHO = result[0].MAKHO;
					$("#txtMA_PHIEU").val(layMaPhieu());
					loadComboGrid_new();
					loadComboGridNCC();
					loadComboGrid_PhuPhi();
					loadComboGridHDKN();
					 
			});
	$('#cboNguonCT').change(
			function() {
				if(PHARMA_LAYTHEONGUONCT=='1')
					loadComboGrid_new();				
			});
	
//	$('#txtTHANH_TIEN').change(
//			function() {
//				var x = formatNumber($('#txtTHANH_TIEN').val());
//				//x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
//				$('#txtTHANH_TIEN').val(x);
//			});
	
	/*$('#txtMA_THUOC').change(function() {
		loadComboGrid_new();
		// DlgUtil.showMsg($("#hidTHUOCVATTU").val());
		if ($("#hidTHUOCVATTU").val() == '') {
			// $("#txtMA_THUOC").val(row.MA_THUOC);
			$("#txtTEN_THUOC").val('');
			$("#txtNUOC_SAN_XUAT").val('');
			$("#txtHANG_SAN_XUAT").val('');
			$("#txtDON_VI_TINH").val('');
			$("#txtVAT").val('');

			$("#h_donvitinhid").val('');

			$("#txtSO_LUONG").attr('readonly', true);
			$("#txtDON_GIA").attr('readonly', true);
			$("#txtVAT").attr('readonly', true);
			$("#txtSDK").attr('readonly', true);
			$("#txtGOI-THAU").attr('readonly', true);
			$("#txtSO_LO").attr('readonly', true);

			$("#txtGIA_NHAN_DAN").attr('readonly', true);
			$("#txtGIA_DICH_VU").attr('readonly', true);
			$("#txtGIA_BHYT").attr('readonly', true);
			$("#txtGIA_TRAN_BHYT").attr('readonly', true);
			// $("#txtHSD1").attr('readonly', true);
			// $("#txtHSD2").attr('readonly', true);
			// $("#txtHSD3").attr('readonly', true);
			$('#cmdGiaBan').attr('disabled', 'disabled');
			$('#cmdLuuThuocKho').attr('disabled', 'disabled');
			$('#cmdXoaThuocKho').attr('disabled', 'disabled');
			_Reset();
		}
		
	});*/

	/*$('select').on(
			'change',
			'',
			function(e) {

				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : $("#txtNhaCungCap").val()
				});

				sql_par = setSysParam(sql_par);
				var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO(
						"DUC04D001.06", sql_par);

				var rows = JSON.parse(data_ar);

				if (rows != null && rows.length > 0) {
					var row = rows[0];
					$("#txtDIA_CHI").val(row.DIACHI);

				} else {
					$("#txtDIA_CHI").val('');
				}
			});*/

	function bindEvent() {
	/*	$("#" + _gridId3).bind(
				"CustomAction",
				function(e, act, rid) {
			
					
					$('#' + _gridId3).jqGrid(
							'delRowData', rid);											
					       //reloadCash();
				});*/
		$(':input').keydown(function(e) {
			if (e.which === 13) {
				var index = $(':input').index(this) + 1;
				$(':input').eq(index).focus();
			}
		});
		
		$("#txtNhaCungCap").keydown(function(e) {
			if (e.which === 13) {
				$("#txtNGUOI_GIAO").focus();
				$("#txtNGUOI_GIAO").select();
			}
		});
		$("#txtMA_THUOC").keydown(function(e) {
			if (e.which === 13) {
				$("#txtSO_LUONG").focus();
				$("#txtSO_LUONG").select();
			}
		});
		$("#txtDIA_CHI").keydown(function(e) {
			if (e.which === 13) {
				$("#txtCHIET_KHAU").focus();
				$("#txtCHIET_KHAU").select();
			}
		});
	

		/*$("#txtGOI_THAU").keydown(function(e) {
			if (e.which === 13) {
				$("#txtSO_LUONG").focus();
				$("#txtSO_LUONG").select();
			}
		});
*/
		if ($("#hid_kieu").val().toString().indexOf("XUAT") < 0) {
			$("#txtSO_LUONG").keydown(function(e) {
				if (e.which === 13) {
					$("#txtDON_VI_TINH").focus();
					$("#txtDON_VI_TINH").select();
				}
			});
			
			$("#txtDON_VI_TINH").keydown(function(e) {
				if (e.which === 13) {
					$("#txtDON_GIA").focus();
					$("#txtDON_GIA").select();
				}
			});

			$("#txtDON_GIA").keydown(function(e) {
				if (e.which === 13) {
					$("#txtVAT").focus();
					$("#txtVAT").select();
				}
			});

			$("#txtVAT").keydown(function(e) {
				if (e.which === 13) {
					$("#txtSO_LO").focus();
					$("#txtSO_LO").select();
				}
			});
			$("#txtSO_LO").keydown(function(e) {
				if (e.which === 13) {
					//$("#txtSDK").focus();
					$("#txtHSD").focus();
					$("#txtHSD").select();
				}
			});
			$("#txtHSD").keydown(function(e) {
				if (e.which === 13) {
					$("#cmdLuuThuocKho").focus();
//					$("#txtGIA_DICH_VU").select();
				}
			});
		}
		else{
			$("#txtSO_LUONG").keydown(function(e) {
				if (e.which === 13) {
					$("#cmdLuuThuocKho").focus();
				}
			});
		}
		
		/*
		 * $( "#txtGIA_TRAN_BHYT" ).keydown(function(e) { if (e.which === 13) {
		 * $("#txtHSD").focus(); } });
		 */
		$('#txtVAT').on('click', function() {
			$("#txtVAT").select();

		});

		$('#txtSO_LUONG').on('click', function() {
			$("#txtSO_LUONG").select();

		});

		$('#txtDON_GIA').on('click', function() {
			$("#txtDON_GIA").select();

		});
		$('#txtGOI_THAU').on('click', function() {
			$("#txtGOI_THAU").select();

		});
		
		$('#txtTHANH_TIEN').change(
				function() {
					if(!isNumeric(replaceStrtoNum($('#txtTHANH_TIEN').val())))
					{
						DlgUtil.showMsg("Thành tiền không đúng!",undefined,2000);
						$('#txtTHANH_TIEN').focus();
						return;
					}
					$('#txtTHANH_TIEN').val(replaceStrtoNum($('#txtTHANH_TIEN').val()).toFixed(2));
					//them phan nhap thanh tien tinh lai
					if(PHARMA_FIX_DONGIAVAT_THEO_DM != '1'){
						var donGiaVAT;
						if(replaceStrtoNum($('#txtSO_LUONG').val()) > 0){
							donGiaVAT = (replaceStrtoNum($('#txtTHANH_TIEN').val())/ replaceStrtoNum($('#txtSO_LUONG').val())).toFixed(3);
						}
						else {
							donGiaVAT ='0';
						}
						
						$('#txtDON_GIA_VAT').val(donGiaVAT);
					}
								
					
					var donGia =(
							replaceStrtoNum($('#txtDON_GIA_VAT').val())*100/(100 + Number($('#txtVAT').val()))
							).toFixed(6);
					
					$('#txtDON_GIA').val(donGia);
					var thanhtientruocvat =replaceStrtoNum($('#txtSO_LUONG').val())* replaceStrtoNum($('#txtDON_GIA').val());
					
					//$('#txtTHANH_TIEN').val(Number(thanhtientruocvat).format(2, 3));
					if(PHARMA_FIX_DONGIAVAT_THEO_DM != '1'){
						$("#txtDON_GIA_VAT").val(Number(donGiaVAT).format(3, 3));
					}
					
					
					$("#hid_thanhtientruocvat").val(replaceStrtoNum(thanhtientruocvat));
					
				
					if(PHARMA_TVT_NHIEU_MUC_GIA == '1' && replaceStrtoNum($('#txtDON_GIA_VAT').val()) > 0 && PHARMA_TINH_TYLEHUHAO == '1'){	
						
						  var taisd_huhao = '0';
							 if(Number($("#hid_TYLEHUHAO").val()) > 0){
									
								 taisd_huhao = replaceStrtoNum($('#txtDON_GIA_VAT').val()) + replaceStrtoNum($('#txtDON_GIA_VAT').val()) * (replaceStrtoNum($("#hid_TYLEHUHAO").val()) / 100 );
							 }else{taisd_huhao = $('#txtDON_GIA_VAT').val();}
							
							  if(replaceStrtoNum($("#txtGIA_NHAN_DAN").val()) > 0) {			  			  
								  $("#txtGIA_NHAN_DAN").val(taisd_huhao);
							  }
							  if(replaceStrtoNum($("#txtGIA_DICH_VU").val()) > 0) {			 
								  $("#txtGIA_DICH_VU").val(taisd_huhao);			  
							  }
							 // if(replaceStrtoNum($("#txtGIA_BHYT").val()) > 0) {$("#txtGIA_BHYT").val($('#txtDON_GIA_VAT').val());}
							  if(replaceStrtoNum($("#txtGIA_BHYT").val()) > 0) {
								  $("#txtGIA_BHYT").val(taisd_huhao);
							  }
					  //if(replaceStrtoNum($("#txtGIA_TRAN_BHYT").val()) > 0) {$("#txtGIA_TRAN_BHYT").val($('#txtDON_GIA_VAT').val());}
				
					}
					
					
		 });
		//$('#tiendon').mask('#.###');
		$('#txtNhaCungCap').change(
				function() {
                 // $('#hid_NHACUNGCAPID').val('0');
                  loadComboGridNCC();
				});
		$("#txtTHANH_TIEN")
				.keydown(
						function(e) {
							// Allow: backspace, delete, tab, escape, enter and
							// .
							if ($.inArray(e.keyCode, [ 46, 8, 9, 27, 13, 110,
									190 ]) !== -1
									||
									// Allow: Ctrl+A
									(e.keyCode == 65 && e.ctrlKey === true) ||
									// Allow: Ctrl+C
									(e.keyCode == 67 && e.ctrlKey === true) ||
									// Allow: Ctrl+X
									(e.keyCode == 88 && e.ctrlKey === true) ||
									// Allow: home, end, left, right
									(e.keyCode >= 35 && e.keyCode <= 39)) {
								// let it happen, don't do anything
								return;
							}
							// Ensure that it is a number and stop the keypress
							if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57))
									&& (e.keyCode < 96 || e.keyCode > 105)) {
								e.preventDefault();
							}
						});
		$('#txtDON_GIA_VAT').change(
				function() {
					if(!isNumeric(replaceStrtoNum($('#txtDON_GIA_VAT').val())))
					{
						DlgUtil.showMsg("Đơn giá Vat không đúng!",undefined,2000);
						$('#txtDON_GIA_VAT').focus();
						return;
					}					
					$('#txtDON_GIA_VAT').val(Number(replaceStrtoNum($('#txtDON_GIA_VAT').val())).format(3,3));
					var thanhtien = replaceStrtoNum($('#txtDON_GIA_VAT').val())*replaceStrtoNum($('#txtSO_LUONG').val());
					$("#txtTHANH_TIEN").val(Number(thanhtien).format(2, 3));
					
					
				});
		$("#txtDON_GIA_VAT")
				.keydown(
						function(e) {
							// Allow: backspace, delete, tab, escape, enter and
							// .
							if ($.inArray(e.keyCode, [ 46, 8, 9, 27, 13, 110,
									190 ]) !== -1
									||
									(e.keyCode == 65 && e.ctrlKey === true) ||
									(e.keyCode == 67 && e.ctrlKey === true) ||
									(e.keyCode == 88 && e.ctrlKey === true) ||
									(e.keyCode >= 35 && e.keyCode <= 39)) {
								return;
							}
							if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57))
									&& (e.keyCode < 96 || e.keyCode > 105)) {
								e.preventDefault();
							}
						});
		$("#txtSO_LUONG")
		.keydown(
				function(e) {
					if ($.inArray(e.keyCode, [ 46, 8, 9, 27, 13, 110,
							190 ]) !== -1
							||
							(e.keyCode == 65 && e.ctrlKey === true) ||
							(e.keyCode == 67 && e.ctrlKey === true) ||
							(e.keyCode == 88 && e.ctrlKey === true) ||
							(e.keyCode >= 35 && e.keyCode <= 39)) {
						return;
					}
					if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57))
							&& (e.keyCode < 96 || e.keyCode > 105)) {
						e.preventDefault();
					}
				});
		$("#txtTHANH_TOAN_TONG")
		.keydown(
				function(e) {
					if ($.inArray(e.keyCode, [ 46, 8, 9, 27, 13, 110,
							190 ]) !== -1
							||
							(e.keyCode == 65 && e.ctrlKey === true) ||
							(e.keyCode == 67 && e.ctrlKey === true) ||
							(e.keyCode == 88 && e.ctrlKey === true) ||
							(e.keyCode >= 35 && e.keyCode <= 39)) {
						return;
					}
					if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57))
							&& (e.keyCode < 96 || e.keyCode > 105)) {
						e.preventDefault();
					}
				});
		$("#txtDON_GIA")
				.keydown(
						function(e) {
							if ($.inArray(e.keyCode, [ 46, 8, 9, 27, 13, 110,
									190 ]) !== -1
									||
									(e.keyCode == 65 && e.ctrlKey === true) ||
									(e.keyCode == 67 && e.ctrlKey === true) ||
									(e.keyCode == 88 && e.ctrlKey === true) ||
									(e.keyCode >= 35 && e.keyCode <= 39)) {
								return;
							}
							if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57))
									&& (e.keyCode < 96 || e.keyCode > 105)) {
								e.preventDefault();
							}
						});
		$.jMaskGlobals = {  
			maskElements : 'input,td,span,div',
			dataMaskAttr : '*[data-mask]',
			dataMask : true,
			watchInterval : 300,
			watchInputs : true,
			watchDataMask : true,
			byPassKeys : [ 9, 16, 17, 18, 36, 37, 38, 39, 40, 91 ],
			translation : {
				'0' : {
					pattern : /\d/
				},
				'9' : {
					pattern : /\d/,
					optional : true
				},
				'#' : {
					pattern : /\d/,
					recursive : true
				},
				'A' : {
					pattern : /[a-zA-Z0-9]/
				},
				'S' : {
					pattern : /[a-zA-Z]/
				}
			}
		};
		// lưu phiếu nhập thuốc từ nhà cung câp
		$('#cmdLuuPhieu').on('click', function() {
			
			switch ($("#hid_kieu").val()) {
			case 'THUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					if ($("#hidTRANGTHAIID").val() == "4"){
						EditPhieuNK();
					}else{
						LuuPhieu();						
					}
					
				}
				break;
			case 'VATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					if ($("#hidTRANGTHAIID").val() == "4"){
						EditPhieuNK();
					}else{
						LuuPhieu();						
					}
				}
				break;

			case 'NHAPBUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;

			case 'NHAPBUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'NHAPKHACTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'NHAPKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;

			case 'XUATTHIEUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;

			case 'XUATTHIEUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATHUYTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATHUYVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATKHACTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATDTTHKP':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			default:
				'';
			}

		});

		// lưu phiếu và đóng nhập thuốc từ nhà cung câp
		$('#cmdLuuDong').on('click', function() {
			_luuvadong = '1';
			switch ($("#hid_kieu").val()) {
			case 'THUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieu();
				}
				break;
			case 'VATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieu();
				}
				break;

			case 'NHAPBUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;

			case 'NHAPBUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'NHAPKHACTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'NHAPKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;

			case 'XUATTHIEUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;

			case 'XUATTHIEUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATHUYTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATHUYVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATKHACTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			case 'XUATKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
				
			case 'XUATDTTHKP':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					LuuPhieuYCNhapBu();
				}
				break;
			default:
				'';
			}

		});
		// nhập kho từ nhà cung cấp _trangthai ="1";
		$('#cmdLuuThuocKho').on('click', function() {

			var _isvalid = _isValidateThuoc();
			if (_isvalid == '1') {
				AddOrUpdateRowData();
				$("#txtMA_THUOC").focus();
				$("#txtMA_THUOC").select();
				_Reset();
				$("#txtMA_THUOC").val('');
			}
			
			// else{DlgUtil.showMsg('sai')}

		});
		
		$('#btnThemthuoc2').on('click', function() {

			var _isvalid = _isValidateThuoc();
			if (_isvalid == '1') {
				AddOrUpdateRowData();
				$("#txtMA_THUOC").focus();
				$("#txtMA_THUOC").select();
				_Reset();
				$("#txtMA_THUOC").val('');
			}
			
			// else{DlgUtil.showMsg('sai')}

		});
		
		// nhập kho từ nhà cung cấp _trangthai ="1";
		$('#cmdLuuHDKN').on('click', function() {
			var _isvalid = _isValidateHDKN();
			if (_isvalid == '1') {
				AddHDKN();
				$("#txtSTT").val('');
				$("#txtSTT").focus();
				$("#txtSTT").select();				
				$("#txtCHUC_DANH").val('');
				$("#txtHoTen_HDKN").val('');
			}

		});
		// gửi phiếu -> gửi phiếu nhập bù thuốc
		$('#cmdYCNhapBu').on('click', function() {

			_trangthai = "5";
			switch ($("#hid_kieu").val()) {
			
			case 'THUOC' :
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuNhapBu();
				}
				break;
			case 'VATTU' :
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuNhapBu();
				}
				break;

			case 'NHAPBUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuNhapBu();
				}
				break;

			case 'NHAPBUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuNhapBu();
				}
				break;
			case 'NHAPKHACTHUOC' :
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuNhapBu();
				}
				break;

			case 'NHAPKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuNhapBu();
				}
				break;
			case 'XUATTHIEUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;

			case 'XUATTHIEUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;
			case 'XUATHUYTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;
			case 'XUATHUYVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;
				
			case 'XUATKHACTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;
			case 'XUATKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;
				
			case 'XUATDTTHKP':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					GuiPhieuXuatThieuHuy();
				}
				break;
			default:
				'';
			}

		});

		// Lưu phiếu nhập bù thuốc
		$('#cmdNhapBu').on('click', function() {

			_trangthai = "4";
			switch ($("#hid_kieu").val()) {

			case 'NHAPBUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;

			case 'NHAPBUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;

			default:
				'';
			}

		});

		// Lưu phiếu xuât , hủy thuốc
		$('#cmdXuatHuy').on('click', function() {

			_trangthai = "4";
			switch ($("#hid_kieu").val()) {

			case 'XUATTHIEUTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
			case 'XUATTHIEUVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
			case 'XUATHUYTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
			case 'XUATHUYVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
			case 'XUATKHACTHUOC':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
			case 'XUATKHACVATTU':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
				
			case 'XUATDTTHKP':
				var _isValid = _isValidateNhapKho();
				if (_isValid == '1') {
					NhapBu();
				}
				break;
			default:
				'';
			}

		});

		$('#cmdXoaThuocKho').on('click', function() {
			if ($("#hidTHUOCVATTU").val() == "") {
				DlgUtil.showMsg('Bạn phải chọn ' + title_thuocvatu);
			} else {
				/*
				 * var _isvalid = _isValidateThuoc(); if(_isvalid ==
				 * '1'){DeleteRowThuoc();}
				 */
				DeleteRowThuoc();
			}

		});

		$('#cmdNhapKho').on('click', function() {
			var _isValid = _isValidateNhapKho();
			if (_isValid == '1') {
				NhapKho();
			}

		});

		$('#cmdDong').on('click', function(e) {
			EventUtil.raiseEvent("nhapkho_cancel", {
				ncc : 'NHAP_KHO',
				id : "0"
			});

		});
		
		$('#cmdXoaPhieu').on('click', function(e) {
			
			DlgUtil
			.showConfirm("Bạn có chắc chắn muốn hủy phiếu không ?",
					function(flag) {
						if (flag) {
							
							var _par = [ parseFloat($("#hid_NHAPXUATID").val()) ];
							var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.XOAPHIEU", _par
									.join('$'));
							if (result == '0') {
								DlgUtil.showMsg('Hủy phiếu lỗi');
							} else {
								// DlgUtil.showMsg('Nhập kho thành công');
								// EventUtil.raiseEvent("nhapkho_success",{ncc:'NHAP_KHO',id:$("#hid_NHAPXUATID").val()});

								DlgUtil.showMsg('Hủy phiếu thành công !', function() {
									EventUtil.raiseEvent("nhapkho_success", {
										ncc : 'NHAP_KHO',
										id : $("#hid_NHAPXUATID").val()
									});
								});
							}
							
						}

					});
			

		});
		
      $('#cmdInPhieu').on('click', function(e) {
			
    	  var par = [ {
				name : 'nhapxuatid',
				type : 'String',
				value : $("#hid_NHAPXUATID").val()
			}];
    	  var rpName= "DUC008_PHIEUNHAPKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
	       CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_QD_BTC_A4", 'xlsx', par, rpName);
	       openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4', 'pdf', par);
	       
	       var rpName= "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xls';
			CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4", 'xls', par, rpName);	
			openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4', 'pdf', par);

		});
		// click tab danh sach phu phi
		$("#tabPPTab").on("click", function() {
			
			_LoadDSPhuPhi();
		});
		//load danh sách phụ phí 
		function _LoadDSPhuPhi(){
			// danh sach phu phi
		
			
		}
		
		$('#cmdAddNhaCungCap').on('click', function(e) {
		
			
			dlgPopup = DlgUtil
			.buildPopupUrl(
					"dlgTuongTac",
					"divDlg",
					"manager.jsp?func=../duoc/DUC_NHACUNGCAP",
					"", "Thêm mới nhà cung cấp", 600, 300);

	       dlgPopup.open("dlgTuongTac");

		});
		
		$('#btnFileTemp').on('click', function () {
		    window.location.href = '../upload/Import_NhapKho_NCC.xls';
		});
		
		$('#btnImport').on('click', function () {
//			var attrLink = '/upload/fileSelectXLS_thuocvattu.jsp?action=uploadExcel&useFilter=false';
//		    window.open(window.location.pathname.substring(0, window.location.pathname.indexOf("/",2)) + attrLink + ".htm");
			var paramInput={							
			};
			var url2 = '/upload/fileSelectXLS_nhapkho_ncc.jsp?action=uploadExcel&useFilter=false';
			url2 = window.location.pathname.substring(0, window.location.pathname.indexOf("/",2)) + url2
			console.log("open url2: "+ url2);
			var popup = DlgUtil.buildPopupUrl("dlgthuocvattu","divthuocvattu",url2,paramInput,"Tải file tài liệu",700,150);
			popup.open("dlgthuocvattu");
		});
		
		$("#btnFresh").click(function(){
			$("#gridList_THUOC").jqGrid("clearGridData");
			$("#gridList_THUOC").trigger("reloadGrid");	
			loadGridData()
		});
		
		EventUtil.setEvent("nhacungcap_cancel",function(e){
			DlgUtil.close("dlgTuongTac");
			loadComboGridNCC();
			
		});
		
		EventUtil.setEvent("nhacungcap_success",function(e){
			DlgUtil.close("dlgTuongTac");
			loadComboGridNCC();
			
			$("#txtNhaCungCap").val(e.ten);
			$("#hid_NHACUNGCAPID").val(e.nccid);
			$("#txtDIA_CHI").val(e.diachi);
			$("#txtNGUOI_GIAO").focus();
			
			
		});
		
      $('#cmdAddMa_Barcode').on('click', function(e) {
    	  
    	  if($("#txtMA_BARCODE").val() == ""){
    		  DlgUtil.showMsg("Chưa có mã Barcode!");
    	  }
    	  else{
    		  var myVar = {khoid: $('#hid_KHOID').val(),ma_barcode:$("#txtMA_BARCODE").val()};
  			dlgPopup = DlgUtil
  			.buildPopupUrl(
  					"dlgBarcode",
  					"divDlg",
  					"manager.jsp?func=../duoc/DUC_UPDATE_BARCODE",
  					myVar, "Update Barcode tương ứng thuốc/ vật tư", 600, 300);
  	       dlgPopup.open("dlgBarcode");
    	  }
		
			

		});
      
      EventUtil.setEvent("barcode_cancel",function(e){
			DlgUtil.close("dlgBarcode");
		
			
		});
		
		EventUtil.setEvent("barcode_success",function(e){
			DlgUtil.close("dlgBarcode");
			loadEditorData(e.thuocvattuid,"1");
			$("#hidTHUOCVATTU").val(e.thuocvattuid);
		});

		GridUtil.setGridParam(_gridId, {
			onSelectRow : function(id) {
				if (id) {
					$("#txtSO_LUONG").removeAttr("readonly");
					$("#txtDON_GIA").removeAttr("readonly");
					$("#txtVAT").removeAttr("readonly");
					//$("#txtSDK").removeAttr("readonly");
					//$("#txtGOI_THAU").removeAttr("readonly");
					$("#txtSO_LO").removeAttr("readonly");
					// $("#txtHSD1").removeAttr("readonly");
					// $("#txtHSD2").removeAttr("readonly");
					// $("#txtHSD3").removeAttr("readonly");
				
					if(PHARMA_TVT_NHIEU_MUC_GIA == '1'){
						$("#txtGIA_NHAN_DAN").removeAttr("readonly");
						$("#txtGIA_DICH_VU").removeAttr("readonly");
						$("#txtGIA_BHYT").removeAttr("readonly");
						$("#txtGIA_TRAN_BHYT").removeAttr("readonly");
					}
//					$("#txtGIA_NHAN_DAN").removeAttr("readonly");
//					$("#txtGIA_DICH_VU").removeAttr("readonly");
//					$("#txtGIA_BHYT").removeAttr("readonly");
//					$("#txtGIA_TRAN_BHYT").removeAttr("readonly");
						
					$('#cmdLuuThuocKho').removeAttr("disabled");
					$('#cmdXoaThuocKho').removeAttr("disabled");
					var _ret = $("#" + _gridId).jqGrid('getRowData', id);
					_Reset();
					loadEditorData(_ret.THUOCVATTUID, 1);
					_thuocid = 	_ret.THUOCVATTUID;
					$("#hidAddOrUpdate").val('0');
					GridUtil.unmarkAll(_gridId);
					GridUtil.markRow(_gridId, id);
					// loadRowSelect(_ret.THUOCVATTUID);
				}
			} 
		});

		GridUtil.setGridParam(_gridId1, {
			onSelectRow : function(id) {
				if (id) {
					
					if ($("#hid_kieu").val().toString().indexOf("XUAT") >= 0
							&& ($("#hidLoaiNhapBu").val() == 'YC_XUATTHIEU'
								|| $("#hidLoaiNhapBu").val() == 'YC_XUATHUY'
									|| $("#hidLoaiNhapBu").val() == 'YC_XUATDTTHKP')) {
						$("#txtSO_LUONG").removeAttr("readonly");
						
					}
					else {						
						
						if ($("#hidLoaiNhapBu").val() != 'NHAPBU'
							&& $("#hidLoaiNhapBu").val() != 'XUATTHIEU'
							&& $("#hidLoaiNhapBu").val() != 'XUATHUY'
								&& $("#hidLoaiNhapBu").val() != 'XUATDTTHKP'
									&& $("#hidTRANGTHAIID").val() != "4") {
						$("#txtSO_LUONG").removeAttr("readonly");
						$("#txtDON_GIA").removeAttr("readonly");
						$("#txtVAT").removeAttr("readonly");
						$("#txtGIABAN").removeAttr("readonly");
						$("#txtSDK").removeAttr("readonly");
						$("#txtGOI_THAU").removeAttr("readonly");
						$("#txtDON_VI_TINH").removeAttr("readonly");
						$("#txtSO_LO").removeAttr("readonly");
//						$("#txtGIA_NHAN_DAN").removeAttr("readonly");
//						$("#txtGIA_DICH_VU").removeAttr("readonly");
//						$("#txtGIA_BHYT").removeAttr("readonly");
//						$("#txtGIA_TRAN_BHYT").removeAttr("readonly");
						// $("#txtHSD1").removeAttr("readonly");
						// $("#txtHSD2").removeAttr("readonly");
						// $("#txtHSD3").removeAttr("readonly");
						$('#cmdGiaBan').removeAttr("disabled");
						$('#cmdLuuThuocKho').removeAttr("disabled");
						$('#cmdXoaThuocKho').removeAttr("disabled");
					}
					}
					
					if(PHARMA_TVT_NHIEU_MUC_GIA == '1'){
						$("#txtGIA_NHAN_DAN").removeAttr("readonly");
						$("#txtGIA_DICH_VU").removeAttr("readonly");
						$("#txtGIA_BHYT").removeAttr("readonly");
						$("#txtGIA_TRAN_BHYT").removeAttr("readonly");
					}

				

					// loadEditorData(_ret.THUOCVATTUID,1);
					$("#hidAddOrUpdate").val(id);
					$("#hid_ChonThuoc").val(id);
					loadRowSelectThuoc(id);
					GridUtil.unmarkAll(_gridId1);
					GridUtil.markRow(_gridId1, id);
					// loadRowSelect(_ret.THUOCVATTUID);
				}
			},
		gridComplete : function(id) {
			if($("#hidDuTruId").val()!='')			
			TinhTienDuTru();
			if(PHARMA_CHECKGIABHYT=='1'){
				SetGiaBHYT();
			}
		},
		});

	}
	function loadGridData() {
		var sql_par = $("#hid_KHOID").val() + '$';
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC_DS_IMPORT_NK", sql_par);
		
		var rowCount = $("#gridList_THUOC").getGridParam("reccount");
		for (var k = 0; k < data_ar.length; k++) {
			var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
			if(data_ar[k].THUOCVATTUID == '' || data_ar[k].THUOCVATTUID == null){
				DlgUtil.showMsg('Mã thuốc ' + data_ar[k].MA_THUOC
						+ '. Không tồn tại trong Hệ Thống !');
				return;
			}
			if(data_ar[k].TONKHOID == '' || data_ar[k].TONKHOID == null){
				DlgUtil.showMsg('Mã thuốc ' + data_ar[k].MA_THUOC
						+ '. Chưa được gán vào Kho !');
				return;
			}
			if(data_ar[k].DONVITINHID == '' || data_ar[k].DONVITINHID == null){
				DlgUtil.showMsg('Đơn vị tính ' + data_ar[k].DON_VI_TINH
						+ ' của Mã thuốc ' + data_ar[k].MA_THUOC + '. Không tồn tại trong Hệ Thống !');
				return;
			}
			if(data_ar[k].SO_LO == '' || data_ar[k].SO_LO == null){
				DlgUtil.showMsg('Mã thuốc ' + data_ar[k].MA_THUOC
						+ '. Chưa có Số Lô!');
				return;
			}
			if(data_ar[k].HAN_SU_DUNG == '' || data_ar[k].HAN_SU_DUNG == null){
				DlgUtil.showMsg('Mã thuốc ' + data_ar[k].MA_THUOC
						+ '. Chưa có Hạn Sử Dụng!');
				return;
			}
			if (data_ar[k].HAN_SU_DUNG == '0' 
				|| data_ar[k].HAN_SU_DUNG == ''
				||	!isDate(data_ar[k].HAN_SU_DUNG)) {				
				
				DlgUtil.showMsg('Hạn sử dụng của Mã thuốc ' + data_ar[k].MA_THUOC
						+ ' không đúng !');
				
				return;
			}

			var hsd = data_ar[k].HAN_SU_DUNG;

			var hsd1 = hsd.substring(0, 2);
			var hsd2 = hsd.substring(3, 5);
			var hsd3 = hsd.substring(6, 10);

			var d = new Date();

			var _month = (d.getMonth() + 1);
			var _date = d.getDate();
			if (_month < 10) {
				_month = '0' + _month;
			}
			if (_date < 10) {
				_date = '0' + _date;
			}
			var strDate = d.getFullYear() + "" + _month + "" + _date;
			var hsd_check = hsd2 + '-' + hsd1 + '-' + hsd3;
			var pattern = /^(?:(0[1-9]|1[012])[\- \/.](0[1-9]|[12][0-9]|3[01])[\- \/.](19|20)[0-9]{2})$/;
			if ((parseFloat(hsd3 + hsd2 + hsd1) <= parseFloat(strDate))
					|| !hsd_check.match(pattern)) {
				DlgUtil.showMsg('Hạn sử dụng của Mã thuốc ' + data_ar[k].MA_THUOC
						+ ' không đúng !');				
				return;
			}
			
			var datarow = {
					THUOCVATTUID : data_ar[k].THUOCVATTUID,
					MA_THUOC : data_ar[k].MA_THUOC,
					TEN_THUOC : data_ar[k].TEN_THUOC,
					BIET_DUOC : '',
					DON_VI_TINH : data_ar[k].DON_VI_TINH,
					SL_YC : replaceStrtoNum(data_ar[k].SO_LUONG),
					SL_DUYET : replaceStrtoNum(data_ar[k].SO_LUONG),
					DON_GIA : replaceStrtoNum(data_ar[k].DON_GIA),
					VAT : data_ar[k].VAT,
					THANH_TIEN : replaceStrtoNum(data_ar[k].THANH_TIEN_VAT),
					TIEN_TRUOC_VAT : replaceStrtoNum(data_ar[k].THANH_TIEN),
					SO_LO : FormUtil.escape(data_ar[k].SO_LO),
					NGUOC_CT : '',
					LIEULUONG : '',
					NUOC_SAN_XUAT : '',
					HANG_SAN_XUAT : '',
					DON_GIA_VAT : replaceStrtoNum(data_ar[k].DON_GIA_VAT),
					SDK : FormUtil.escape(data_ar[k].SO_DANG_KY),
					//GOI_THAU : FormUtil.escape($("#txtGOI_THAU").val()),
					GOI_THAU : data_ar[k].QD_THAU,
					HSD : data_ar[k].HAN_SU_DUNG,
					DONVITINHID : data_ar[k].DONVITINHID,
					GIANHANDAN : replaceStrtoNum(data_ar[k].DON_GIA_VAT),
					GIADICHVU : replaceStrtoNum(data_ar[k].DON_GIA_VAT),
					GIABHYT : replaceStrtoNum(data_ar[k].DON_GIA_VAT),
					GIATRANBHYT : replaceStrtoNum(data_ar[k].DON_GIA_VAT),
					STTTHAU : '',
					DAUTHAUID : data_ar[k].DAUTHAUID,
					DAUTHAUCTID : data_ar[k].DAUTHAUCTID
				};
				if (rowIds == null || rowIds.length <= 0) {
					$('#gridList_THUOC').jqGrid('addRowData', 1, datarow);
				} else {
					$('#gridList_THUOC').jqGrid('addRowData',
							Math.max.apply(null, rowIds) + 1, datarow);
				}
				rowCount = rowCount + 1;
		}
		$("#hidAddOrUpdate").val(rowCount);	
		reloadCash();
		
	}
	function _Reset() {
		$("#txtSO_LUONG").val("0");
		$("#txtDON_GIA").val("0");
		$("#txtVAT").val("0");
		$("#txtDON_GIA_VAT").val("0");
		$("#txtTHANH_TIEN").val("0");
		$("#txtGIABAN").val("0");
		$("#txtVAT").val("0");
		$("#txtSDK").val("");
		$("#txtGOI_THAU").val("");
		$("#txtSO_LO").val("");
		$("#txtHSD").val("");
		$("#txtGIA_NHAN_DAN").val("0");
		$("#txtGIA_DICH_VU").val("0");
		$("#txtGIA_BHYT").val("0");
		$("#txtGIA_TRAN_BHYT").val("0");
		$("#txtSTTTHAU").val("");
		// $("#txtHSD2").val("");
		// $("#txtHSD3").val("");
	}
	 function loadComboGrid_new(){
		 var _col = "";
			var _sql = "";
			if ($("#hid_kieu").val() == 'XUATTHIEUTHUOC'
					|| $("#hid_kieu").val() == 'XUATHUYTHUOC'
					|| $("#hid_kieu").val() == 'XUATTHIEUVATTU'
					|| $("#hid_kieu").val() == 'XUATHUYVATTU'
				    || $("#hid_kieu").val() == 'XUATDTTHKP'
				    || $("#hid_kieu").val() == 'XUATKHACTHUOC'
				    || $("#hid_kieu").val() == 'XUATKHACVATTU') {
				_col = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;Mã " + title_thuocvatu
				+ ",MA_THUOC,10,0,f,l;Tên " + title_thuocvatu
				+ ",TEN_THUOC,18,0,f,l;"
				+ "Hoạt chất,HOATCHAT,20,0,f,l;"
				+ "ĐVT,DON_VI_TINH,5,0,f,l;"
				+ "Đóng gói,DONGGOI,11,0,t,l;"
				+ "Hàm lượng,LIEULUONG,11,0,f,l;"
				+ "Khả dụng,KHA_DUNG,8,0,f,l;"
				+ "Tồn Kho,TON_KHO,8,0,f,l;"
				+ "Nước sx,NUOCSANXUAT,8,0,f,l";
				 _sql = "DUC02N001.03";
			} else {
				if(PHARMA_LOADGRID_NHAPNCC == '0' || PHARMA_LOADGRID_NHAPNCC == null){
					_col = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;Mã " + title_thuocvatu
					+ ",MA_THUOC,10,0,f,l;Tên " + title_thuocvatu
					+ ",TEN_THUOC,16,0,f,l;"
					+ "Hoạt chất,HOATCHAT,15,0,f,l;"
					+ "ĐVT,DON_VI_TINH,5,0,f,l;"
					+ "Đóng gói,DONGGOI,11,0,t,l;"
					+ "Hàm lượng,LIEULUONG,10,0,f,l;"
					//+ "Khả dụng,KHA_DUNG,10,0,f,l;"
					//+ "Tồn Kho,TON_KHO,10,0,f,l";
					+ "Số ĐK,SDK,5,0,f,l;"
					+ "Gói thầu,GOITHAU,8,0,f,l;"
					+ "Nhóm thầu,MANHOMTHAU,8,0,f,l;"
					+ "QĐ thầu,MATHAU,10,0,f,l;"
					+ "Đơn giá VAT,DONGIAVAT,8,0,f,l;"
					+ "Nước sx,NUOCSANXUAT,8,0,f,l";
					 _sql = "DUC02N001.01";
				}else if(PHARMA_LOADGRID_NHAPNCC.includes("DUONG_DUNG")){
					_col = PHARMA_LOADGRID_NHAPNCC;			  
			    	  _sql = "DUC02N001.DD.01";
				}
				else{
					 _col = PHARMA_LOADGRID_NHAPNCC;			  
			    	  _sql = "DUC02N001.01";
				}
				
			}
			var _sql_par = [];
			// _sql_par.push({"name":"[0]","value":$('#hid_KHOID').val()},{"name":"[1]","value":_loai_thuocvattu});
			_sql_par.push({
				"name" : "[0]",
				"value" : $('#hid_KHOID').val()
			});
			if(PHARMA_LAYTHEONGUONCT=='1' && ($('#cboNguonCT').val() != null && $('#cboNguonCT').val() != "")){
				_sql_par.push({
					"name" : "[1]",
					"value" : $('#cboNguonCT').val()
				});
				_sql = "DUC02N001.NGUONCT";
			}
			$("#hidTHUOCVATTU").val("");
			ComboUtil.initComboGrid(FormUtil.escape("txtMA_THUOC"), _sql, _sql_par, "1150px", _col, function(event, ui){
				
				$("#hidTHUOCVATTU").val(ui.item.THUOCVATTUID);
				$("#txtTEN_THUOC").val(ui.item.TEN_THUOC);		
				$("#txtMA_THUOC").val(ui.item.MA_THUOC);
				loadEditorData(ui.item.THUOCVATTUID, "1");
				$('#hidSLQuyĐoiVT').val('1');
				loadComboGridDVT();
				return false;
			});
	 }
	// nhap ma phu phi
	 function loadComboGrid_PhuPhi(){
		 var _col = "";
			var _sql = "";			
				_col = "THUOCVATTUID,THUOCVATTUID,0,0,t,l;" 
				+ "Mã phụ phí ,MA_THUOC,15,0,f,l;" 
				+ "Tên phụ phí,TEN_THUOC,30,0,f,l;"
				//+ "Hoạt chất,HOATCHAT,20,0,f,l;"
				+ "ĐVT,DON_VI_TINH,8,0,f,l;"
				+ "Đóng gói,DONGGOI,15,0,t,l;"
				//+ "Hàm lượng,LIEULUONG,13,0,f,l;"
				//+ "Khả dụng,KHA_DUNG,10,0,f,l;"
				//+ "Tồn Kho,TON_KHO,10,0,f,l";
				+ "Số ĐK,SDK,10,0,f,l;"
				+ "Gói thầu,GOITHAU,10,0,f,l;"
				+ "QĐ thầu,MATHAU,10,0,f,l;"
				+ "Đơn giá VAT,DONGIAVAT,10,0,f,l";
				//+ "Nước sx,NUOCSANXUAT,8,0,f,l";
				 _sql = "DUC02N001.01";
			
			var _sql_par = [];
			// _sql_par.push({"name":"[0]","value":$('#hid_KHOID').val()},{"name":"[1]","value":_loai_thuocvattu});
			_sql_par.push({
				"name" : "[0]",
				"value" : $('#hid_KHOID').val()
			});
			$("#hidTHUOCVATTU").val("");
			ComboUtil.initComboGrid(FormUtil.escape("txtMA_PHUPHI"), _sql, _sql_par, "980px", _col, function(event, ui){
				
				$("#hidTHUOCVATTU_PP").val(ui.item.THUOCVATTUID);
				//$("#txtTEN_THUOC").val(ui.item.TEN_THUOC);		
				$("#txtMA_PHUPHI").val(ui.item.MA_THUOC);
				$("#txtMA_PHUPHI").select();
				AddGridPhuPhi(ui.item.THUOCVATTUID, "1");
				return false;
			});
	 }
	 
	// nhập nhà cung cấp
	function loadComboGridNCC() {
		var _col = "NHACUNGCAPID,NHACUNGCAPID,0,0,t,l;"
				+ "Nhà cung cấp,TEN,40,0,f,l;"
				
				+ "Địa chỉ,DIACHI,50,0,f,l";
		
			var _sql = "DUC04D001.05";
	
		//$("#hid_NHACUNGCAPID").val("0");
		/*var _selfnc = function(event, ui) {
			$("#txtNhaCungCap").val(ui.item.TEN);
			$("#hid_NHACUNGCAPID").val(ui.item.NHACUNGCAPID);
			$("#txtDIA_CHI").val(ui.item.DIACHI);
		
			return false;
		};
	
		ComboUtil.initComboGrid(FormUtil.escape("txtNhaCungCap"), _sql, [""], "850px", _col,
				_selfnc);*/
		
		ComboUtil.initComboGrid(FormUtil.escape("txtNhaCungCap"), _sql, [""], "850px", _col, function(event, ui){
			
			$("#txtNhaCungCap").val(ui.item.TEN);
			$("#hid_NHACUNGCAPID").val(ui.item.NHACUNGCAPID);
			$("#txtDIA_CHI").val(ui.item.DIACHI);
		
			return false;
		});
	}
	// load hội đồng kiểm nhập mặc định
	
	function loadComboGridHDKN() {
		var _col = "HOIDONG_KIEMNHAP_ID,HOIDONG_KIEMNHAP_ID,0,0,t,l;"
				+ "Mã,MA,10,0,f,l;"
				+ "Ngày lập,NGAYLAP,30,0,f,l;"
				+ "Khoa,KHOA,50,0,f,l";
		
			var _sql = "DUC02N001.14";	
			 var _sql_par = [];
				// _sql_par.push({"name":"[0]","value":$('#hid_KHOID').val()},{"name":"[1]","value":_loai_thuocvattu});
				_sql_par.push({
					"name" : "[0]",
					"value" : _hinhthuc
				});
		ComboUtil.initComboGrid(FormUtil.escape("txtHDKN"), _sql, _sql_par, "850px", _col, function(event, ui){			
			$("#txtHDKN").val(ui.item.MA)	
			$("#txtNGAYLAP_HDKN").val(ui.item.NGAYLAP)
			$("#hid_HDKN_Id").val(ui.item.HOIDONG_KIEMNHAP_ID);			
			//loadComboGridHDKN_CT();
			loadData_HDKN();
			return false;
		});
	}
	
	function loadComboGridHDKN_CT() {
		var _col = "STT,STT,10,0,f,l;"
				+ "Chức danh,CHUCDANH,30,0,f,l;"
				+ "Họ tên,HOTEN,50,0,f,l";
		
			var _sql = "DUC02N001.15";	
			 var _sql_par = [];
				// _sql_par.push({"name":"[0]","value":$('#hid_KHOID').val()},{"name":"[1]","value":_loai_thuocvattu});
				_sql_par.push({
					"name" : "[0]",
					"value" : $("#hid_HDKN_Id").val()
				});
		ComboUtil.initComboGrid(FormUtil.escape("txtSTT"), _sql, _sql_par, "850px", _col, function(event, ui){			
			$("#txtSTT").val(ui.item.STT);
			$("#txtCHUC_DANH").val(ui.item.CHUCDANH);
			$("#txtHoTen_HDKN").val(ui.item.HOTEN);		
			return false;
		});
	}
	function loadData_HDKN(){
		$("#gridList_HDKN").jqGrid("clearGridData", true);
		var sql_par = $("#hid_HDKN_Id").val() + '$';
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC_HDKNCT_BYID", sql_par);		
		var rowCount = $("#gridList_HDKN").getGridParam("reccount");
		if (data_ar != null && data_ar.length > 0) {
			for (var k = 0; k < data_ar.length; k++) {
				var rowIds = $('#gridList_HDKN').jqGrid('getDataIDs');
				var datarow = {
						STT : data_ar[k].STT,
						CHUCDANH : data_ar[k].CHUCDANH,
						HOTEN : data_ar[k].HOTEN
					};
					if (rowIds == null || rowIds.length <= 0) {
						$('#gridList_HDKN').jqGrid('addRowData', 1, datarow);
					} else {
						$('#gridList_HDKN').jqGrid('addRowData',
								Math.max.apply(null, rowIds) + 1, datarow);
					}
					rowCount = rowCount + 1;
			}
		}
		
	}
	
	
	// quy đổi đvt
	function loadComboGridDVT() {
		var _col = "DONVITINHID,DONVITINHID,0,0,t,l;"
				+ "ĐVT,TEN,60,0,f,l;"				
				+ "Số lượng,SOLUONG,40,0,f,l";
		
			var _sql = "DUC.QD_DVT";	
			var _sql_par = [];
			// _sql_par.push({"name":"[0]","value":$('#hid_KHOID').val()},{"name":"[1]","value":_loai_thuocvattu});
			_sql_par.push({
				"name" : "[0]",
				"value" : $("#hidTHUOCVATTU").val()
			});
	
		ComboUtil.initComboGrid(FormUtil.escape("txtDON_VI_TINH"), _sql, _sql_par, "150px", _col, function(event, ui){			
			$("#txtDON_VI_TINH").val(ui.item.TEN);
			$("#hid_DVTQD").val(ui.item.DONVITINHID);
			$("#hidSLQuyĐoiVT").val(ui.item.SOLUONG);		
			if(Number($('#txtSO_LUONG').val()) >0){
				$('#hid_SLDVT_CU').val($('#txtSO_LUONG').val());
				var soluongnhan = '0';
				soluongnhan = replaceStrtoNum($('#txtSO_LUONG').val()) * Number($('#hidSLQuyĐoiVT').val());
				$('#txtSO_LUONG').val(soluongnhan);
				$('#txtSO_LUONG').val(Number(replaceStrtoNum($('#txtSO_LUONG').val())).format(2,3));
				fillGia();
			}
			
			return false;
		});
	}
	

	function loadEditorPhieu(_keyField, isAddNew) {

		var sql_par = _keyField;
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC04D001.04', sql_par);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			$("#txtKHO_LAP").val(row.NOI_LAP);
			$("#txtNGUOI_LAP").val(row.NGUOI_LAP);
			$("#txtNGAY_LAP").val(row.NGAY_LAP);
			$("#txtLAN_IN").val('0');
			$("#cmdInPhieu").css("display", "");
			if ($("#copy").val() == 'true'){
				$("#hid_NHAPXUATID").val('0');
			}else{
				$("#txtMA_PHIEU").val(FormUtil.unescape(row.MA_PHIEU));
				$("#hid_NHAPXUATID").val(_keyField);
				$("#txtSO_CHUNG_TU").val(FormUtil.unescape(row.SO_CT));
				$("#txtNGAY_LAP_SCT").val(row.NGAY_LAP_SCT);
				
				if(row.TRANGTHAIID == '1'){
					$("#cmdXoaPhieu").css("display", "");					
					$('#cmdLuuThuocKho').removeAttr("disabled");
				}
			}
			
			if(row.TRANGTHAIID == '4'){
				//$('#txtNGAY_LAP').attr('disabled', 'disabled');
				//$('#ngaylap').attr('disabled', 'disabled');
				if($("#hidTRANGTHAIID").val() == '4'){
					$("#txtMA_PHUPHI").attr('readonly', true);
				}
				
			}
			
		
			$("#txtNhaCungCap").val(row.NHA_CUNG_CAP);
			$("#cboLyDoHuyPhieu").val(row.LYDOHUYPHIEU);
			$(document).ready(function() {
				var callback = function() {
					if ($.active !== 0) {
						setTimeout(callback, '200');
						return;
					}
					$("#cboNguonCT").val(row.NGUONCHUONGTRINHID);
				};
				callback();
				});
			
			//loadNguonCT(row.NGUONCHUONGTRINHID);
			$("#hid_LYDOHUYPHIEU").val(row.LYDOHUYPHIEU);
			$("#txtNGUOI_GIAO").val(row.NGUOIGIAO);
			$("#txtDIA_CHI").val(row.DIACHI);
			$("#txtCHIET_KHAU").val(row.CHIET_KHAU);
			$("#txtDIA_CHI_NHM").val(row.DIACHI_NHM);
			$("#txtSOHOPDONG").val(row.SOHOPDONG);
			$("#txtNGAY_HD").val(row.NGAY_HD);
			$("#txtGHI_CHU").val(FormUtil.unescape(row.GHI_CHU));
			$("#txtTHANH_TOAN_TONG").val(row.THANH_TOAN_TONG);
			
			$("#txtHDKN").val(FormUtil.unescape(row.MA_HDKN));
			$("#txtNGAYLAP_HDKN").val(row.NGAYLAP_HDKN);
			$("#hid_NHACUNGCAPID").val(row.NHACUNGCAPID);
			
			$("#hid_HDKN_Id").val(row.HOIDONG_KIEMNHAPID);

			$("#hid_NHAPID").val(row.NHAPID);
			$("#hid_XUATID").val(row.XUATID);
			
			if($("#hidTRANGTHAIID").val() != "2"){
				var sumAll = parseFloat(row.TIENDON);
				var tien_chiet_khau = (sumAll * parseFloat($("#txtCHIET_KHAU").val())) / 100;
				$("#h_thanhtoantong").val(sumAll);
				//$("#tiendon").text(sumAll.format(2, 3, ',') + 'đ');
				$("#thuegtgt").val(row.TIENTHUEVAT);
				$("#tiendon").text((Number(sumAll) - Number(row.TIENTHUEVAT)).format(_lamtron, 3, ',') + 'đ');
				$("#thuegtgt").text(parseFloat(row.TIENTHUEVAT).format(_lamtron, 3, ',') + 'đ');
				$("#tienchietkhau").text(tien_chiet_khau.format(_lamtron, 3, ',') + 'đ');
				$("#tongcong").text(
						(sumAll - parseFloat(tien_chiet_khau)).format(_lamtron, 3, ',')
								+ 'đ');
				$("#conlai")
						.text(
								(sumAll - parseFloat($("#txtTHANH_TOAN_TONG").val()) - parseFloat(tien_chiet_khau))
										.format(_lamtron, 3, ',')
										+ 'đ');

				$("#tongcong").val((sumAll - parseFloat(tien_chiet_khau)));
				$("#tienchietkhau").val(tien_chiet_khau);
				$("#tiendon").val(sumAll);
				$("#conlai").val(
						sumAll - parseFloat($("#txtTHANH_TOAN_TONG").val())
								- parseFloat(tien_chiet_khau));

				$("#hid_NHAPXUATID_CHA").val(row.NHAPXUATID_CHA);
			}

			var param = RSUtil.buildParam("", [ $("#hid_HDKN_Id").val() ]);
			GridUtil.loadGridBySqlPage(_gridId3, "DUC02N001.13", param);

		} else {
			if (isAddNew != null && isAddNew != '0') {
				DlgUtil.showMsg("Không có dữ liệu");
			}
			return;
		}
		/*
		 * var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC02N001.02",sql_par); //
		 * console.log(JSON.stringify(data_ar));
		 * $("#gridList_THUOC").jqGrid("clearGridData");
		 * $("#gridList_THUOC")[0].grid.beginReq();
		 * $("#gridList_THUOC").jqGrid("setGridParam", { data : data_ar });
		 * $("#gridList_THUOC")[0].grid.endReq();
		 * $("#gridList_THUOC").trigger("reloadGrid");
		 */
		// GridUtil.loadGridBySqlPage(_gridId1,"DUC02N001.02",sql_par);
		if($("#hidTRANGTHAIID").val() == "2"){
			$("#cmdLuuPhieu").css("display", "none");
			$("#cmdLuuDong").css("display", "none");
			$("#gridList_THUOC").jqGrid("clearGridData", true).trigger("reloadGrid");
			_nxid_bosung = $("#hid_NHAPXUATID").val();
			$("#hid_NHAPXUATID").val('0');
			
		}else {
			var param = RSUtil.buildParam("", [ _keyField ]);
			GridUtil.loadGridBySqlPage("gridList_THUOC", "DUC02N001.02", param);
			$('#cmdXoaThuocKho').removeAttr("disabled");
		}
		

	}
		
	function SetGiaBHYT() {
		 
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		for (i = 0; i < rowIds.length; i++) {
			rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);
			var _color='#9ACD32';	
			if (rowData['GIABHYT'] != rowData['GIATRANBHYT']) {			
				$('#gridList_THUOC').find("tr[id='" + rowIds[i] + "']").find('td').each(function(index, element) {			        
					$(element).css('background-color', _color);
					 
			    });

			}
			 
		}
	}
	function TinhTienDuTru(){
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		var sumAll = 0;
		for (i = 1; i <= rowIds.length; i++) {
			rowData = $('#gridList_THUOC').jqGrid('getRowData', i);
			sumAll =sumAll +parseFloat(rowData['THANH_TIEN'])	;
		}
		
		var tien_chiet_khau = (sumAll * parseFloat($("#txtCHIET_KHAU").val())) / 100;
		$("#h_thanhtoantong").val(sumAll);
		$("#tiendon").text(sumAll.format(0, 3, ',') + 'đ');
		$("#thuegtgt").text('0');
		$("#tienchietkhau").text(tien_chiet_khau.format(0, 3, ',') + 'đ');
		$("#tongcong").text(
				(sumAll - parseFloat(tien_chiet_khau)).format(0, 3, ',')
						+ 'đ');
		$("#conlai")
				.text(
						(sumAll - parseFloat($("#txtTHANH_TOAN_TONG").val()) - parseFloat(tien_chiet_khau))
								.format(0, 3, ',')
								+ 'đ');

		$("#tongcong").val((sumAll - parseFloat(tien_chiet_khau)));
		$("#tienchietkhau").val(tien_chiet_khau);
		$("#tiendon").val(sumAll);
		$("#conlai").val(
				sumAll - parseFloat($("#txtTHANH_TOAN_TONG").val())
						- parseFloat(tien_chiet_khau));
		
	}
	// tao ngay lap neu trong de ngay hien tai
	function _creat_ngaylap(_ngaylap) {
		var ngay_lap = _ngaylap.substring(0, 10);
		if (ngay_lap == null || ngay_lap == "") {
			var d = new Date();
			var _month = (d.getMonth() + 1);
			var _date = d.getDate();
			if (_month < 10) {
				_month = '0' + _month;
			}
			if (_date < 10) {
				_date = '0' + _date;
			}
			ngay_lap = _date + "/" + _month + "/" + d.getFullYear();
		}
		return ngay_lap;
	}
	// sửa phiếu đã nhập kho chỉ lưu phiếu ko sửa thuốc
	function EditPhieuNK() {
		
		var ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
		var param_ar_kho = {
			"KHOID" : $("#hid_KHOID").val(),
			"MA_PHIEU" : FormUtil.escape($.trim($("#txtMA_PHIEU").val())),
			"NGAY_LAP" : ngay_lap,
			"SO_CHUNG_TU" : FormUtil.escape($.trim($("#txtSO_CHUNG_TU").val())),
			"NGAY_LAP_SCT" : $("#txtNGAY_LAP_SCT").val(),
			"NHACUNGCAPID" : $("#hid_NHACUNGCAPID").val(),
			"NGUOI_GIAO" : FormUtil.escape($("#txtNGUOI_GIAO").val()),
			"DIA_CHI" : $("#txtDIA_CHI").val(),
			"CHIET_KHAU" : $("#txtCHIET_KHAU").val(),
			"TONG_CONG" : $("#tongcong").val(),
			"TIEN_CHIETKHAU" : $("#tienchietkhau").val(),
			"TIEN_DONGIA" : $("#tiendon").val(),
			"TIEN_CONLAI" : $("#conlai").val(),
			"TIENTHUEVAT" : $("#thuegtgt").val(),
			"CSYTID" : "1",
			"HINHTHUCID" : "1",
			"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
			"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
			"NHAPXUATID_CHA" : $("#hid_NHAPXUATID_CHA").val(),
			"TONGTIENDATRA" : replaceStrtoNum($("#txtTHANH_TOAN_TONG").val()),
			"DIACHI_NHM" : $("#txtDIA_CHI_NHM").val(),
			"SOHOPDONG" : $("#txtSOHOPDONG").val(),
			"NGAY_HD" : $("#txtNGAY_HD").val(),
			"KHOAID" : that.opt._khoaid,
			"PHONGID" : that.opt._phongid,
			"LYDOHUYPHIEU" : $("#cboLyDoHuyPhieu").val(),
			"HOIDONG_KIEMNHAPID":$("#hid_HDKN_Id").val()
		};
		// var param_ar_kho = {"":161};
		var param_str_kho = JSON.stringify(param_ar_kho);

		var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
				"}");	

		var _par = [ str_kho_json ];		
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC02N001.07", _par
				.join('$'));

		var data = $.parseJSON(result);

		 _succes = data.SUCCESS;
	
		if (_succes == '0') {
			DlgUtil.showMsg('Ngày lập không được lớn hơn ngày lập của phiếu xuất gần nhất hoặc ngày hiện tại là :  ' + data.MESSAGE);
			// Tuan tự động lấy mã mới trong trường hợp trùng mã
			//$("#txtMA_PHIEU").val(layMaPhieu());
			
		} else {
		
			$("#hid_NHAPXUATID").val(data.SUCCESS);
			$('#cmdLuuPhieu').attr('disabled', 'disabled');
			$('#cmdLuuDong').attr('disabled', 'disabled');
			// EventUtil.raiseEvent("assignSevice_cancel",{ncc:'LUU_PHIEU',id:$("#hid_NHAPXUATID").val()});
			if (_luuvadong == '1') {

				DlgUtil.showMsg('Đã lưu phiếu', function() {
					EventUtil.raiseEvent("nhapkho_success", {
						ncc : 'NHAP_KHO',
						id : $("#hid_NHAPXUATID").val()
					});
					//LuuHDKN($("#hid_NHAPXUATID").val());
				});
			} else {
				DlgUtil.showMsg('Đã lưu phiếu');
				//LuuHDKN($("#hid_NHAPXUATID").val());
			}

		}
	}
	// lưu phiếu nhập thuốc nhà cung cấp
	function LuuPhieu() {
		$('input[id*="gs_"]').val("");
		$('select[id*="gs_"]').val("ALL");
		$("#gridList_THUOC").jqGrid('setGridParam', {
			search : false,
			postData : {
				"filters" : ""
			}
		}).trigger("reloadGrid");
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
			if($("#hidTRANGTHAIID").val() == '2'){
				$("#txtMA_PHIEU").val('PHIEUBS');
			}
			else{
				var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
						_par.join('$'));
				if(result == '0'){	
				    	var maphieu = layMaPhieu();
				    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
						$("#txtMA_PHIEU").val(layMaPhieu());
						
				}
			}
			
			
			var param_ar_kho = {
				"KHOID" : $("#hid_KHOID").val(),
				"MA_PHIEU" : FormUtil.escape($.trim($("#txtMA_PHIEU").val())),
				"NGAY_LAP" : ngay_lap,
				"SO_CHUNG_TU" : FormUtil.escape($.trim($("#txtSO_CHUNG_TU").val())),
				"NGAY_LAP_SCT" : $("#txtNGAY_LAP_SCT").val(),
				"NHACUNGCAPID" : $("#hid_NHACUNGCAPID").val(),
				"NGUOI_GIAO" : FormUtil.escape($("#txtNGUOI_GIAO").val()),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"CHIET_KHAU" : $("#txtCHIET_KHAU").val(),
				"TONG_CONG" : replaceStrtoNum($("#tongcong").val()),
				"TIEN_CHIETKHAU" : replaceStrtoNum($("#tienchietkhau").val()),
				"TIEN_DONGIA" : replaceStrtoNum($("#tiendon").val()),
				"TIEN_CONLAI" : replaceStrtoNum($("#conlai").val()),
				"TIENTHUEVAT" : replaceStrtoNum($("#thuegtgt").val()),
				"CSYTID" : "1",
				"HINHTHUCID" : "1",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : $("#hid_NHAPXUATID_CHA").val(),
				"TONGTIENDATRA" : replaceStrtoNum($("#txtTHANH_TOAN_TONG").val()),
				"DIACHI_NHM" : $("#txtDIA_CHI_NHM").val(),
				"SOHOPDONG" : $("#txtSOHOPDONG").val(),
				"NGAY_HD" : $("#txtNGAY_HD").val(),
				"KHOAID" : that.opt._khoaid,
				"PHONGID" : that.opt._phongid,
				"LYDOHUYPHIEU" : $("#cboLyDoHuyPhieu").val(),
				"NGUONCT" : $("#cboNguonCT").val(),
				"NGAYCAPNHAT":_creat_ngaylap(""),
				"DOT_NHAPXUATID":$("#hidDOT_NHAPXUATID").val(),
				"HOIDONG_KIEMNHAPID":$("#hid_HDKN_Id").val()
			};
			// var param_ar_kho = {"":161};
			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			// xml nhap kho thuoc chi tiet
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			// var param_str = createXML(param_arr);
			for (var i = 0; i < param_arr.length; i++) {
				param_arr[i].SO_LO = FormUtil.escape(param_arr[i].SO_LO);
				param_arr[i].SDK = FormUtil.escape(param_arr[i].SDK);
				param_arr[i].GOI_THAU = FormUtil.escape(param_arr[i].GOI_THAU);
			}
			var param_str = JSON.stringify(param_arr);

			
			var param_arr_pp = $("#gridList_PhuPhi").jqGrid('getRowData');
			var param_str_pp = JSON.stringify(param_arr_pp);
			/*
			 * var _par=['1','HIS_DATA','10.145.37.191','1', 161,
			 * $("#txtMA_PHIEU").val(), ngay_lap, $("#txtSO_CHUNG_TU").val(),
			 * $("#txtNGAY_LAP_SCT").val(), $("#txtNhaCungCap").val(),
			 * $("#txtNGUOI_GIAO").val(), $("#txtDIA_CHI").val(),
			 * $("#txtCHIET_KHAU").val(), $("#tongcong").val(),
			 * $("#tienchietkhau").val(), $("#tiendon").val(),
			 * $("#conlai").val(), 1, 1, $("#txtGHI_CHU").val(),param_str];
			 */

			// var _par=['1','HIS_DATA','10.145.37.191','1',
			// str_kho_json,param_str];
			var _par = [ str_kho_json, param_str,param_str_pp ];
			// var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("{?=call
			// duc02n001_luu_phieu_nhap_ncc(?2S,?3S,?4S,?5S,?6L,?7S,?8T,?9S,?10T,?11L,?12S,?13S,?14S,?15L,?16L,?17L,?18L,?19L,?20L,?21S,?22S)}",_par.join('$'));
			//var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC04D001.09", _par
			//		.join('$'));
			
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC04D001.19", _par
					.join('$'));

			var data = $.parseJSON(result);

			 _succes = data.SUCCESS;
			param = $("#hidDuTruId").val() + '$';

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);
				// Tuan tự động lấy mã mới trong trường hợp trùng mã
				$("#txtMA_PHIEU").val(layMaPhieu());
				
			} else {
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC04D001.16",
						param);

				$("#hid_NHAPXUATID").val(data.SUCCESS);
				$('#cmdLuuPhieu').attr('disabled', 'disabled');
				$('#cmdLuuDong').attr('disabled', 'disabled');
				$("#cmdXoaPhieu").css("display", "");
				$("#cmdInPhieu").css("display", "");
				// EventUtil.raiseEvent("assignSevice_cancel",{ncc:'LUU_PHIEU',id:$("#hid_NHAPXUATID").val()});
				if (_luuvadong == '1') {
					DlgUtil.showMsg('Đã lưu phiếu', function() {
						EventUtil.raiseEvent("nhapkho_success", {
							ncc : 'NHAP_KHO',
							id : $("#hid_NHAPXUATID").val()
						});
					});
					//LuuHDKN($("#hid_NHAPXUATID").val());
				} else {
					DlgUtil.showMsg('Đã lưu phiếu');
					_daclick_luuphieu = '1';
					//LuuHDKN($("#hid_NHAPXUATID").val());
				}

			}
		} else {
			DlgUtil.showMsg('Danh sách ' + title_thuocvatu
					+ ' không được rỗng !');
			_succes = '0';
		}
	}
	// lưu hội đồng kiểm nhập
	function LuuHDKN(_nhapxuatid){		
		$('input[id*="gs_"]').val("");
		$('select[id*="gs_"]').val("ALL");
		$("#gridList_HDKN").jqGrid('setGridParam', {
			search : false,
			postData : {
				"filters" : ""
			}
		}).trigger("reloadGrid");
		var param_arr = $("#gridList_HDKN").jqGrid('getRowData');		
		for (var i = 0; i < param_arr.length; i++) {		
			param_arr[i].CHUCDANH = FormUtil.escape(param_arr[i].CHUCDANH);
			param_arr[i].HOTEN = FormUtil.escape(param_arr[i].HOTEN);
		}
		var ngay_lap = ($("#txtNGAYLAP_HDKN").val()).substring(0, 10);	
		objData = new Object();
		objData["KHOAID"] = that.opt._khoaid;
		objData["MA"] = FormUtil.escape($.trim($("#txtHDKN").val()));
		objData["NGAYLAP"] = ngay_lap;
		objData["NHAPXUATID"] = _nhapxuatid;
		
		var param_str = JSON.stringify(param_arr);		
		var _par = [ JSON.stringify(objData), param_str];	
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.12", _par
				.join('$'));
		if(result == 0){
			DlgUtil.showMsg('Lưu hội đồng kiểm nhập có lỗi');
		}
	}	
	// nhập kho từ nhà cung cấp
	function NhapKho() {
		/*
		 * if($("#hid_NHAPXUATID").val() == '0'){ LuuPhieu();
		 * if($("#hid_NHAPXUATID").val() != '0'){ _ActNhapKho(); } } else{
		 * _ActNhapKho(); }
		 */
		
		if(_daclick_luuphieu  == '0'){
			var _isValid = _isValidateNhapKho();
			if (_isValid == '1') {
				LuuPhieu();	
				_ActNhapKho();				
			}
		
		}
		else{
				_ActNhapKho();				
		}
		

	}
	function _ActNhapKho() {
		var _par ='';
		var result = '';
		var _nhapxuatid_in = '0';
		if($("#hidTRANGTHAIID").val() == "2"){
			_nhapxuatid_in = _nxid_bosung;
			 _par = [ parseFloat($("#hid_NHAPXUATID").val()),_nxid_bosung ];
			 result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC04D001.21", _par
						.join('$'));
		}else{
			_nhapxuatid_in = $("#hid_NHAPXUATID").val();
			 _par = [ parseFloat($("#hid_NHAPXUATID").val()) ];
			 result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC04D001.08", _par
					.join('$'));
		}
		
		if (result == '0') {
			DlgUtil.showMsg('Nhập kho lỗi');
		} else {
			// DlgUtil.showMsg('Nhập kho thành công');
			// EventUtil.raiseEvent("nhapkho_success",{ncc:'NHAP_KHO',id:$("#hid_NHAPXUATID").val()});

			DlgUtil.showMsg('Nhập kho thành công !', function() {
				EventUtil.raiseEvent("nhapkho_success", {
					ncc : 'NHAP_KHO',
					id : $("#hid_NHAPXUATID").val()
				});	
			});
			
			if(PHARMA_TUDONG_INPHIEUNHAP == '1'){
				var par = [ {
					name : 'nhapxuatid',
					type : 'String',
					value : _nhapxuatid_in
				}];
				//'PHARMA_INTACHMA_PHIEUNHAP'
			   
			   var rpName= "DUC008_PHIEUNHAPKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
		       CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_QD_BTC_A4", 'xlsx', par, rpName);
		       openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4', 'pdf', par);
			}
			
		
						
							
					
					   
				
			
		}
	}

	// Lưu phiếu yêu cầu nhập bù

	function LuuPhieuYCNhapBu() {
		$('input[id*="gs_"]').val("");
		$('select[id*="gs_"]').val("ALL");
		$("#gridList_THUOC").jqGrid('setGridParam', {
			search : false,
			postData : {
				"filters" : ""
			}
		}).trigger("reloadGrid");
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
			var nhacungcap = $("#hid_NHACUNGCAPID").val();
			if ($("#hid_NHACUNGCAPID").val() == "0") {
				nhacungcap = "";
			}
			var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
					_par.join('$'));
			if(result == '0'){	
			    	var maphieu = layMaPhieu();
			    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
					$("#txtMA_PHIEU").val(layMaPhieu());
					
			}
			var param_ar_kho = {
				"KHOID" : $("#hid_KHOID").val(),
				"MA_PHIEU" : FormUtil.escape($.trim($("#txtMA_PHIEU").val())),
				"NGAY_LAP" : ngay_lap,
				"SO_CHUNG_TU" : FormUtil.escape($.trim($("#txtSO_CHUNG_TU").val())),
				"NGAY_LAP_SCT" : $("#txtNGAY_LAP_SCT").val(),
				"NHACUNGCAPID" : nhacungcap,
				"NGUOI_GIAO" : FormUtil.escape($("#txtNGUOI_GIAO").val()),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"CHIET_KHAU" : $("#txtCHIET_KHAU").val(),
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_CHIETKHAU" : $("#tienchietkhau").val(),
				"TIEN_DONGIA" : $("#tiendon").val(),
				"TIEN_CONLAI" : $("#conlai").val(),
				"CSYTID" : "1",
				"HINHTHUCID" : "5",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : $("#hid_NHAPXUATID_CHA").val(),
				"TRANGTHAI" : _trangthai,
				"KIEU" : _kieu,
				"HINHTHUC" : _hinhthuc,
				"TONGTIENDATRA" : $("#txtTHANH_TOAN_TONG").val(),
				"DIACHI_NHM" : $("#txtDIA_CHI_NHM").val(),
				"SOHOPDONG" : $("#txtSOHOPDONG").val(),
				"NGAY_HD" : $("#txtNGAY_HD").val(),
				"KHOAID" : that.opt._khoaid,
				"PHONGID" : that.opt._phongid,
				"LYDOHUYPHIEU" : $("#cboLyDoHuyPhieu").val(),
				"NGUONCT" : $("#cboNguonCT").val(),
				"NGAYCAPNHAT":_creat_ngaylap("")
			};

			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");

			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');

			var param_str = JSON.stringify(param_arr);

			var _par = [ str_kho_json, param_str ];

			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC21N001.01", _par
					.join('$'));

			var data = $.parseJSON(result);

			 _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);
			} else {

				$("#hid_NHAPXUATID").val(data.SUCCESS);
				$('#cmdLuuPhieu').attr('disabled', 'disabled');
				$('#cmdLuuDong').attr('disabled', 'disabled');
				$("#cmdXoaPhieu").css("display", "");
				$("#cmdInPhieu").css("display", "");
				// EventUtil.raiseEvent("assignSevice_cancel",{ncc:'YC_NHAP_BU',id:$("#hid_NHAPXUATID").val()});

				if (_luuvadong == '1') {

					DlgUtil.showMsg('Đã lưu phiếu yêu cầu ' + _title_xuatnhap,
							function() {
								EventUtil.raiseEvent("nhapkho_success", {
									ncc : 'YC_NHAP_BU',
									id : $("#hid_NHAPXUATID").val()
								});
							});
				} else {
					
					DlgUtil.showMsg('Đã lưu phiếu yêu cầu ' + _title_xuatnhap);
				}

			}
		} else {
			DlgUtil.showMsg('Danh sách ' + title_thuocvatu
					+ ' không được rỗng !');
		}

	}

	// gửi phiếu yêu cầu nhập bù
	function GuiPhieuNhapBu() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
			var nhacungcap = $("#hid_NHACUNGCAPID").val();
			if ($("#hid_NHACUNGCAPID").val() == "0") {
				nhacungcap = "";
			}

			var param_ar_kho = {
				"KHOID" : $("#hid_KHOID").val(),
				"MA_PHIEU" : FormUtil.escape($.trim($("#txtMA_PHIEU").val())),
				"NGAY_LAP" : ngay_lap,
				"SO_CHUNG_TU" : $.trim($("#txtSO_CHUNG_TU").val()),
				"NGAY_LAP_SCT" : $("#txtNGAY_LAP_SCT").val(),
				"NHACUNGCAPID" : nhacungcap,
				"NGUOI_GIAO" : FormUtil.escape($("#txtNGUOI_GIAO").val()),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"CHIET_KHAU" : $("#txtCHIET_KHAU").val(),
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_CHIETKHAU" : $("#tienchietkhau").val(),
				"TIEN_DONGIA" : $("#tiendon").val(),
				"TIEN_CONLAI" : $("#conlai").val(),
				"CSYTID" : "1",
				"HINHTHUCID" : "5",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : $("#hid_NHAPXUATID_CHA").val(),
				"DIACHI_NHM" : $("#txtDIA_CHI_NHM").val(),
				"SOHOPDONG" : $("#txtSOHOPDONG").val(),
				"NGAY_HD" : $("#txtNGAY_HD").val(),
				"TRANGTHAI" : _trangthai,
				"KIEU" : _kieu,
				"HINHTHUC" : _hinhthuc,
				"TONGTIENDATRA" : $("#txtTHANH_TOAN_TONG").val(),
				"KHOAID" : that.opt._khoaid,
				"PHONGID" : that.opt._phongid,
				"LYDOHUYPHIEU" : $("#cboLyDoHuyPhieu").val(),
				"NGUONCT" : $("#cboNguonCT").val(),
				"NGAYCAPNHAT":_creat_ngaylap("")
			};
			var param_str_kho = JSON.stringify(param_ar_kho);
			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			var param_str = JSON.stringify(param_arr);
			var _par = [ str_kho_json, param_str ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC21N001.01", _par
					.join('$'));
			var data = $.parseJSON(result);
			 _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);
			} else {

				$("#hid_NHAPXUATID").val(data.SUCCESS);

				DlgUtil.showMsg('Đã gửi phiếu yêu cầu ' + _title_xuatnhap,
						function() {
							EventUtil.raiseEvent("nhapkho_success", {
								ncc : 'YC_NHAP_BU',
								id : $("#hid_NHAPXUATID").val()
							});
						});

			}
		} else {
			DlgUtil.showMsg('Danh sách ' + title_thuocvatu
					+ ' không được rỗng !');
		}
	}

	// Gửi phiếu yêu cầu xuất thiếu , xuất hủy

	function GuiPhieuXuatThieuHuy() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
			var nhacungcap = $("#hid_NHACUNGCAPID").val();
			if ($("#hid_NHACUNGCAPID").val() == "0") {
				nhacungcap = "";
			}

			var param_ar_kho = {
				"KHOID" : $("#hid_KHOID").val(),
				"MA_PHIEU" : FormUtil.escape($.trim($("#txtMA_PHIEU").val())),
				"NGAY_LAP" : ngay_lap,
				"SO_CHUNG_TU" : FormUtil.escape($.trim($("#txtSO_CHUNG_TU").val())),
				"NGAY_LAP_SCT" : $("#txtNGAY_LAP_SCT").val(),
				"NHACUNGCAPID" : nhacungcap,
				"NGUOI_GIAO" : FormUtil.escape($("#txtNGUOI_GIAO").val()),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"CHIET_KHAU" : $("#txtCHIET_KHAU").val(),
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_CHIETKHAU" : $("#tienchietkhau").val(),
				"TIEN_DONGIA" : $("#tiendon").val(),
				"TIEN_CONLAI" : $("#conlai").val(),
				"CSYTID" : "1",
				"HINHTHUCID" : "5",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : $("#hid_NHAPXUATID_CHA").val(),
				"TRANGTHAI" : _trangthai,
				"KIEU" : _kieu,
				"HINHTHUC" : _hinhthuc,
				"TONGTIENDATRA" : $("#txtTHANH_TOAN_TONG").val(),
				"DIACHI_NHM" : $("#txtDIA_CHI_NHM").val(),
				"SOHOPDONG" : $("#txtSOHOPDONG").val(),
				"NGAY_HD" : $("#txtNGAY_HD").val(),
				"KHOAID" : that.opt._khoaid,
				"PHONGID" : that.opt._phongid,
				"LYDOHUYPHIEU" : $("#cboLyDoHuyPhieu").val(),
				"NGUONCT" : $("#cboNguonCT").val(),
				"NGAYCAPNHAT":_creat_ngaylap("")
			};
			var param_str_kho = JSON.stringify(param_ar_kho);
			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");
			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');
			var param_str = JSON.stringify(param_arr);
			var _par = [ str_kho_json, param_str ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC21N001.03", _par
					.join('$'));
			var data = $.parseJSON(result);
			var _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);
			} else {

				$("#hid_NHAPXUATID").val(data.SUCCESS);

				DlgUtil.showMsg('Đã gửi phiếu yêu cầu ' + _title_xuatnhap,
						function() {
							EventUtil.raiseEvent("nhapkho_success", {
								ncc : 'YC_NHAP_BU',
								id : $("#hid_NHAPXUATID").val()
							});
						});

			}
		} else {
			DlgUtil.showMsg('Danh sách ' + title_thuocvatu
					+ ' không được rỗng !');
		}
	}
	// Nhập bù or Xuất thiếu - hủy
	$("#ckbSua").change(function() {
		if( $("#ckbSua").prop('checked') == true){
			$('#txtTHANH_TIEN').prop('disabled',false);
			$('#txtDON_GIA_VAT').prop('disabled',false);
			//$('#spanGia *').css("background", "#f5f5f5");
			}
			else{
				$('#txtTHANH_TIEN').prop('disabled',true);
				$('#txtDON_GIA_VAT').prop('disabled',true);
			}
		if(PHARMA_FIX_DONGIAVAT_THEO_DM == '1'){
			$('#txtDON_GIA_VAT').prop('disabled',true);
		}
	});
	

	function NhapBu() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		if (rowIds != null && rowIds.length > 0) {
			var ngay_lap = ($("#txtNGAY_LAP").val()).substring(0, 10);
			var nhacungcap = $("#hid_NHACUNGCAPID").val();
			if ($("#hid_NHACUNGCAPID").val() == "0") {
				nhacungcap = "";
			}
			var _par = [ $("#txtMA_PHIEU").val(),$("#hid_NHAPXUATID").val() ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC02N001.08",
					_par.join('$'));
			if(result == '0'){	
			    	var maphieu = layMaPhieu();
			    	DlgUtil.showMsg('Mã phiếu '+ $("#txtMA_PHIEU").val() + ' đã tồn tại và thay Mã phiếu mới ' + maphieu);
					$("#txtMA_PHIEU").val(layMaPhieu());
					
			}
			var param_ar_kho = {
				"KHOID" : $("#hid_KHOID").val(),
				"MA_PHIEU" : FormUtil.escape($.trim($("#txtMA_PHIEU").val())),
				"NGAY_LAP" : ngay_lap,
				"SO_CHUNG_TU" : FormUtil.escape($.trim($("#txtSO_CHUNG_TU").val())),
				"NGAY_LAP_SCT" : $("#txtNGAY_LAP_SCT").val(),
				"NHACUNGCAPID" : nhacungcap,
				"NGUOI_GIAO" : FormUtil.escape($("#txtNGUOI_GIAO").val()),
				"DIA_CHI" : $("#txtDIA_CHI").val(),
				"CHIET_KHAU" : $("#txtCHIET_KHAU").val(),
				"TONG_CONG" : $("#tongcong").val(),
				"TIEN_CHIETKHAU" : $("#tienchietkhau").val(),
				"TIEN_DONGIA" : $("#tiendon").val(),
				"TIEN_CONLAI" : $("#conlai").val(),
				"CSYTID" : "1",
				"HINHTHUCID" : "5",
				"GHI_CHU" : FormUtil.escape($("#txtGHI_CHU").val()),
				"NHAPXUATID" : $("#hid_NHAPXUATID").val(),
				"NHAPXUATID_CHA" : $("#hid_NHAPXUATID").val(),
				"TRANGTHAI" : _trangthai,
				"KIEU" : _kieu,
				"HINHTHUC" : _hinhthuc,
				"TONGTIENDATRA" : $("#txtTHANH_TOAN_TONG").val(),
				"DIACHI_NHM" : $("#txtDIA_CHI_NHM").val(),
				"SOHOPDONG" : $("#txtSOHOPDONG").val(),
				"NGAY_HD" : $("#txtNGAY_HD").val(),
				"KHOAID" : that.opt._khoaid,
				"PHONGID" : that.opt._phongid,
				"LYDOHUYPHIEU" : $("#cboLyDoHuyPhieu").val(),
				"NGUONCT" : $("#cboNguonCT").val(),
				"NGAYCAPNHAT":_creat_ngaylap("")
			};

			var param_str_kho = JSON.stringify(param_ar_kho);

			var str_kho_json = (param_str_kho.replace("[", "{")).replace("]",
					"}");

			var param_arr = $("#gridList_THUOC").jqGrid('getRowData');

			var param_str = JSON.stringify(param_arr);

			var _par;
			var result;
			if (_kieu == "0") {
				// _par=[str_kho_json,param_str];
				_par = [ $("#hid_NHAPXUATID").val() ];
				result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC21N001.02", _par
						.join('$'));
			} else {
				_par = [ $("#hid_NHAPXUATID").val() ];
				result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC21N001.04", _par
						.join('$'));
			}

			var data = $.parseJSON(result);

			 _succes = data.SUCCESS;

			if (_succes == '0') {
				DlgUtil.showMsg(data.MESSAGE);
			} else {

				$("#hid_NHAPXUATID").val(data.SUCCESS);

				DlgUtil.showMsg('Đã lưu phiếu ' + _title_xuatnhap, function() {
					EventUtil.raiseEvent("nhapkho_success", {
						ncc : 'NHAP_BU',
						id : $("#hid_NHAPXUATID").val()
					});
				});

			}
		} else {
			DlgUtil.showMsg('Danh sách ' + title_thuocvatu
					+ ' không được rỗng !');
		}
	}

	function newEditorData() {
		// DlgUtil.showMsg("user_id="+user_id);
		_object_id = null;
		for (var i = 0; i < _dataCtl.length; i++) {
			var ctlName = _dataCtl[i][CTL_CONTROL_TYPE]
					+ _dataCtl[i][CTL_COLUMN_NAME];
			$('#' + ctlName).val("");
		}
		$('#' + _gridId).jqGrid('clearGridData');
		return true;
	}
	function loadEditorData(_keyField, isAddNew) {
		var sql_par = _keyField + '$';
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC04D001.03", sql_par);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			$("#txtMA_THUOC").val(row.MA_THUOC);
			$("#txtTEN_THUOC").val(row.TEN_THUOC);
			$("#txtNUOC_SAN_XUAT").val(row.NUOC_SAN_XUAT);
			$("#txtHANG_SAN_XUAT").val(row.HANG_SAN_XUAT);
			$("#txtDON_VI_TINH").val(row.DON_VI_TINH);
			$("#txtVAT").val(row.VAT);
			$("#hidTHUOCVATTU").val(row.THUOCVATTUID);
			$("#h_donvitinhid").val(row.DONVITINHID);
			$("#hid_DVTQD").val(row.DVTNHANID);
			 MA_NHOM = row.MA_NHOM;

			$("#txtGIABAN").val(Number(row.GIABAN).format(3,3));
			$("#txtSO_LO").val(row.SOLO);
			// $("#txtSO_LUONG").val(row.SOLUONGNGAY);
			$("#txtSO_LUONG").val("0");
			$("#txtDON_GIA").val(Number(row.GIANHAP).format(_sole,3));
			$("#txtGIA_DICH_VU").val(Number(row.GIAVIENPHI).format(3,3));// can confirm
			$("#txtGIA_BHYT").val(Number(row.GIABHYT).format(3,3));
			$("#txtGIA_TRAN_BHYT").val(Number(row.GIATRANBHYT).format(3,3));
			$("#txtGIA_NHAN_DAN").val(Number(row.GIANHANDAN).format(3,3));// can confirm
			$("#txtSDK").val(row.SODANGKY);
			$("#txtGOI_THAU").val(row.MATHAU);
			$("#txtSTTTHAU").val(row.STTTHAU);
			$("#hid_TYLEHUHAO").val(row.TYLEHUHAO);
			$("#hidLieuLuong").val(row.LIEULUONG);
			
			if ($("#hid_kieu").val().toString().indexOf("XUAT") < 0) {
				$("#txtSO_LUONG").removeAttr("readonly");
				$("#txtGIABAN").removeAttr("readonly");
				$("#txtDON_GIA").removeAttr("readonly");
				$("#txtVAT").removeAttr("readonly");
				$("#txtDON_VI_TINH").removeAttr("readonly");
				//$("#txtSDK").removeAttr("readonly");
				//$("#txtGOI_THAU").removeAttr("readonly");
				$("#txtSO_LO").removeAttr("readonly");
			
				if(PHARMA_TVT_NHIEU_MUC_GIA == '1'){
					$("#txtGIA_NHAN_DAN").removeAttr("readonly");
					$("#txtGIA_DICH_VU").removeAttr("readonly");
					$("#txtGIA_BHYT").removeAttr("readonly");
					$("#txtGIA_TRAN_BHYT").removeAttr("readonly");
				}
				if(PHARMA_FIX_DONGIAVAT_THEO_DM == '1'){
					$("#txtDON_GIA_VAT").val(Number(row.GIABAN).format(3,3));
					$('#txtDON_GIA_VAT').attr('disabled', 'disabled');
					$('#txtDON_GIA').attr('disabled', 'disabled');
				}

//				$("#txtGIA_NHAN_DAN").removeAttr("readonly");
//				$("#txtGIA_DICH_VU").removeAttr("readonly");
//				$("#txtGIA_BHYT").removeAttr("readonly");
//				$("#txtGIA_TRAN_BHYT").removeAttr("readonly");
			} else {
				$("#txtSO_LUONG").removeAttr("readonly");
				$('#txtHSD').attr('disabled', 'disabled');
				$('#hsd').attr('disabled', 'disabled');
			}

			//$("#txtGIA_NHAN_DAN").val(row.GIANHANDAN);
			//$("#txtGIA_DICH_VU").val(row.GIADICHVU);
			//$("#txtGIA_BHYT").val(row.GIABHYT);
			//$("#txtGIA_TRAN_BHYT").val(row.GIATRANBHYT);
			// $("#txtHSD1").removeAttr("readonly");
			// $("#txtHSD2").removeAttr("readonly");
			// $("#txtHSD3").removeAttr("readonly");
			$('#cmdGiaBan').removeAttr("disabled");
			$('#cmdLuuThuocKho').removeAttr("disabled");
			$('#cmdXoaThuocKho').removeAttr("disabled");

		} else {
			if (isAddNew != null && isAddNew != '0') {
				DlgUtil.showMsg("Không có dữ liệu");
			}
			return;
		}
	}
	
	//add lên grid phụ phí
	function AddGridPhuPhi(_keyField, isAddNew) {
		var sql_par = _keyField + '$';
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC04D001.03", sql_par);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			
			
			//----
			var rowIds = $('#gridList_PhuPhi').jqGrid('getDataIDs');
			var _checkdaco = '0';
			
			for (var k = 0; k < rowIds.length; k++) {
				var rowData = $('#gridList_PhuPhi').jqGrid('getRowData', rowIds[k]);
				 var hi_thuocid = rowData.THUOCVATTUID;
				if (hi_thuocid == $("#hidTHUOCVATTU_PP").val()) {
	                 _checkdaco = '1';
	                 DlgUtil.showMsg("Đã có mã phụ phí" + rowData.MA_THUOC + " trên danh sách!");
					break;
				} else {
					hi_thuocid = '0';
				}

		    }
			if(_checkdaco == '0'){
				var datarow = {
						THUOCVATTUID : $("#hidTHUOCVATTU_PP").val(),
						MA_THUOC : row.MA_THUOC,
						MA_BYT : row.MABYT,
						TEN_BYT : row.TENBYT,
						MAHOATCHAT : row.MAHOATCHAT,
						HOATCHAT : row.HOATCHAT,						
						DON_GIA : replaceStrtoNum(row.GIABAN),						
						DON_GIA_VAT : replaceStrtoNum(row.GIANHAP),						
						GIANHANDAN : replaceStrtoNum(row.GIANHANDAN),
						GIADICHVU : replaceStrtoNum(row.GIAVIENPHI),
						GIABHYT : replaceStrtoNum(row.GIABHYT),
						GIATRANBHYT : replaceStrtoNum(row.GIATRANBHYT)
					};
				
				if (rowIds == null || rowIds.length <= 0) {
					$('#gridList_PhuPhi').jqGrid('addRowData', 1, datarow);
				} else {
					$('#gridList_PhuPhi').jqGrid('addRowData',
							Math.max.apply(null, rowIds) + 1, datarow);
				}
			}
			
			
			
		

		} else {
				DlgUtil.showMsg("Không có dữ liệu");
				return;
		}
	}

	function loadRowSelectThuoc(item_id) {
		//var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		var rowData = $('#gridList_THUOC').jqGrid('getRowData', item_id);
		 hi_thuocid = rowData['THUOCVATTUID'];
		$("#hidTHUOCVATTU").val(rowData['THUOCVATTUID']);
		$('#txtMA_THUOC').val(rowData['MA_THUOC']);
		$('#txtTEN_THUOC').val(rowData['TEN_THUOC']);
		$('#txtDON_VI_TINH').val(rowData['DON_VI_TINH']);
		$('#txtSO_LUONG').val(Number(rowData['SL_YC']).format(2, 3));
		$('#hid_SLDVT_CU').val(Number(rowData['SL_DVT_CU']).format(2, 3));
		$('#txtDON_GIA').val(Number(rowData['DON_GIA']).format(_sole, 3));
		$('#txtVAT').val(rowData['VAT']);
		$('#txtTHANH_TIEN').val(Number(rowData['THANH_TIEN']).format(2,3));		
		$('#hid_thanhtientruocvat').val( (Number(rowData['DON_GIA']) * Number(rowData['SL_YC']) ).format(2,3));
		$('#txtSO_LO').val(rowData['SO_LO']);
		$('#txtNUOC_SAN_XUAT').val(rowData['NUOC_SAN_XUAT']);
		$('#txtHANG_SAN_XUAT').val(rowData['HANG_SAN_XUAT']);
		$('#txtDON_GIA_VAT').val(Number(rowData['DON_GIA_VAT']).format(3, 3));
		$('#txtGIABAN').val(Number(rowData['GIA_BAN']).format(3,3));
		$('#txtSDK').val(rowData['SDK']);
		$('#txtGOI_THAU').val(rowData['GOI_THAU']);
		$("#hid_TYLEHUHAO").val(rowData['TYLEHUHAO']);
		$('#txtHSD').val(rowData['HSD']);
		$('#h_donvitinhid').val(rowData['DONVITINHID']);
		$('#hid_DVTQD').val(rowData['DVTNHANID']);
		$('#txtGIA_NHAN_DAN').val(Number(rowData['GIANHANDAN']).format(3,3));
		$('#txtGIA_DICH_VU').val(Number(rowData['GIADICHVU']).format(3,3));
		$('#txtGIA_BHYT').val(Number(rowData['GIABHYT']).format(3,3));
		$('#txtGIA_TRAN_BHYT').val(Number(rowData['GIATRANBHYT']).format(3, 3));
		$('#txtSTTTHAU').val(rowData['STTTHAU']);
		MA_NHOM = rowData['MANHOM'];
		if(PHARMA_CHECKGIABHYT=='1' &&  (rowData['GIABHYT']).toString() != (rowData['GIATRANBHYT']).toString() ){
				 
				document.getElementById("txtGIA_TRAN_BHYT").style.color = "green";
				document.getElementById("lbGiaTranBHYT").style.color = "green";
			}
			else {
				document.getElementById("txtGIA_TRAN_BHYT").style.color = "#080808e0";
				document.getElementById("lbGiaTranBHYT").style.color = "#080808e0";
			}
		loadComboGridDVT();
		

	}
	function _isValidateNhapKho() {
		var kq = '1';

		var d = new Date();

		var _month = (d.getMonth() + 1);
		var _date = d.getDate();
		if (_month < 10) {
			_month = '0' + _month;
		}
		if (_date < 10) {
			_date = '0' + _date;
		}	
		
		var strDate = _date + "/" + _month + "/" + d.getFullYear();
		
		if ($("#txtNGAY_LAP").val() == '0' 
			|| $("#txtNGAY_LAP").val() == ''
			|| !isDate($("#txtNGAY_LAP").val())) {
			DlgUtil.showMsg('Ngày lập không đúng !', function() {
				$("#txtNGAY_LAP").focus();
			});
			kq = '0';
			return kq;
		}
		else{
			var ngaylap = moment($("#txtNGAY_LAP").val(),'DD/MM/YYYY');
			var ngayht = moment(strDate,'DD/MM/YYYY');
			if(ngaylap>ngayht){
				DlgUtil.showMsg("Ngày lập không được lớn hơn ngày hiện tại !");
				$("#txtNGAY_LAP").focus();
				kq = '0';
				return kq;
			}
		}
		
		if($("#hidTRANGTHAIID").val() != "2" ){	
			if(PHARMA_BO_CHECK_SOCHUNGTU != '1'){
				var d_nl = $("#txtNGAY_LAP").val().substring(6, 10);
				if(PHARMA_CHECK_SOHOADON_THEO_NGAYCT == '3'){
					var check_shd = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.SHD.NGAYCT",
							$("#hid_NHACUNGCAPID").val() + 
							'$' + $.trim($("#txtSO_CHUNG_TU").val())+
							'$' + '-1' +
							'$'+ $("#hid_NHAPXUATID").val() + '$');
				}else {
				var check_shd = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.SHD.NGAYCT",'-1' + 
						'$' + $.trim($("#txtSO_CHUNG_TU").val())+
						'$' + '-1' +
						'$'+ $("#hid_NHAPXUATID").val() + '$');
				}
				if(Number(check_shd) > 0){	
					
					if(confirm('Số chứng từ "' + $("#txtSO_CHUNG_TU").val() + '" đã tồn tại . Bạn có muốn chỉnh lại không ?')){
						kq = '0';
						return kq;
						}					
					
					/*DlgUtil.showMsg('Số chứng từ "' + $("#txtSO_CHUNG_TU").val() + '" đã tồn tại. Trong năm ' + d_nl +' !');
					return 0;*/
					
				}
			}
		
		}
		
		

		if ($("#txtNGAY_LAP_SCT").val().trim() != ''
				&& !isDate($("#txtNGAY_LAP_SCT").val())) {
			DlgUtil.showMsg('Ngày chứng từ không hợp lệ !', function() {
				$("#txtNGAY_LAP_SCT").focus();
			});
			kq = '0';
			return kq;
		}

		if ($.trim($("#txtMA_PHIEU").val()) == ""
				|| $("#txtMA_PHIEU").val() == null) {
			// DlgUtil.showMsg('Bạn phải nhập Mã Phiếu !');
			// $("#txtMA_PHIEU").focus();
			DlgUtil.showMsg('Mã phiếu không đúng !', function() {
				$("#txtMA_PHIEU").focus();
			});
			kq = '0';
			return kq;
		}
		
			if (FormUtil.escape($.trim($("#txtMA_PHIEU").val())).length > 30) {
			// DlgUtil.showMsg('Bạn phải nhập Mã Phiếu !');
			// $("#txtMA_PHIEU").focus();
			DlgUtil.showMsg('Mã phiếu nhập dạng script ko đc vượt quá 15 ký tự !', function() {
				$("#txtMA_PHIEU").focus();
			});
			kq = '0';
			return kq;
		}
		if($("#hid_kieu").val() != 'XUATDTTHKP' && PHARMA_BO_CHECK_SOCHUNGTU != 1){
			if ($.trim($("#txtSO_CHUNG_TU").val()) == ""
				|| $("#txtSO_CHUNG_TU").val() == null) {
			DlgUtil.showMsg('Bạn phải nhập Số chứng từ !', function() {
				$("#txtSO_CHUNG_TU").focus();
			});
			kq = '0';
			return kq;
		 }
		}
		if($("#hid_kieu").val() == 'XUATDTTHKP' && that.opt._khoaid == 0){
			
			DlgUtil.showMsg('Bạn phải chọn thiết lập khoa phòng !');
			kq = '0';
			return kq;
		}
		
		if ($("#hid_kieu").val() == 'THUOC' || $("#hid_kieu").val() == 'VATTU') {
			if ($("#hid_NHACUNGCAPID").val() == '0' || $("#hid_NHACUNGCAPID").val() == '') {
				DlgUtil.showMsg('Bạn phải chọn đúng nhà cung cấp !', function() {
					$("#txtNhaCungCap").focus();
				});
				kq = '0';
				return kq;
			}
			if(PHARMA_CHECK_NGUONCT == '1'){
				if ($.trim($("#cboNguonCT").val()) == ""
					|| $("#cboNguonCT").val() == null) {			
				DlgUtil.showMsg('Bạn phải chọn Nguồn Chương Trình !', function() {
					$("#cboNguonCT").focus();
				});
				kq = '0';
				return kq;
			   }
			}
			
	    }

			
		if ($.trim($("#txtCHIET_KHAU").val()) == ''
				|| parseFloat($("#txtCHIET_KHAU").val()) > 100) {
			DlgUtil.showMsg('Chiết khấu không đúng !', function() {
				$("#txtCHIET_KHAU").focus();
			});
			kq = '0';
			return kq;
		}
		if ($("#hid_kieu").val() == 'DUTRU') {
			var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
			var check = 0;
			for (i = 0; i < rowIds.length; i++) {
				rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);

				if ( rowData['HSD'] == '') {
					check = check + 1;
					$("#gridList_THUOC").jqGrid('setRowData', rowIds[i], "", {
						color : 'red'
					});
				}
			}
			if (check > 0) {
				DlgUtil.showMsg('Chọn hạn sử dụng của thuốc ở các dòng màu đỏ!');
				return 0;
			}
		}
		if($("#hid_kieu").val() == 'XUATHUYTHUOC' || $("#hid_kieu").val() == 'XUATHUYVATTU'
			|| $("#hid_kieu").val() == 'XUATKHACTHUOC' || $("#hid_kieu").val() == 'XUATKHACVATTU'){
			if ($("#cboLyDoHuyPhieu").val() == '0') {
				DlgUtil.showMsg('Bạn phải chọn lý do xuất hoặc hủy !', function() {
					$("#cboLyDoHuyPhieu").focus();
				});
				kq = '0';
				return kq;
			}
			
		}

		return kq;
	}
	function _isValidateThuoc() {
		var kq = '1';

		if ($("#txtSO_LUONG").val() == '0'
			|| $.trim($("#txtSO_LUONG").val()) == ''
			|| !isNumeric(replaceStrtoNum($('#txtSO_LUONG').val()))) 
		{
			DlgUtil.showMsg('Số lượng không đúng !', function() {
				$("#txtSO_LUONG").focus();
			});
			kq = '0';
			return kq;
		}
		
		

		if ($("#hidTHUOCVATTU").val() == "0" || $("#hidTHUOCVATTU").val() == "" || $("#txtMA_THUOC").val() == "") {
			DlgUtil.showMsg('Bạn phải chọn ' + title_thuocvatu + ' trước!',
					function() {
						$("#txtMA_THUOC").focus();
					});
			kq = '0';
			return kq;
		}
		
		// check sl thau trong dm
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC.CHECKSLTHAU_DM", $("#hidTHUOCVATTU").val().trim()+'$'+replaceStrtoNum($("#txtSO_LUONG").val()));
		if (Number(result) > 0 ){
			DlgUtil.showMsg("Số lượng vượt quá số lượng thầu trong danh mục! Vượt " +result+" đơn vị"  , function() {
				$('#txtSO_LUONG').focus();
				$('#txtSO_LUONG').select();
			});
			kq = '0';
			return kq;
		}	
		// trường hợp xuất thuốc thì chỉ bắt số lượng còn lại ko bắt lỗi
		if ($("#hid_kieu").val().toString().indexOf("XUAT") < 0) {
			if (  $.trim($("#txtDON_GIA").val()) == ''
					|| !isNumeric(replaceStrtoNum($('#txtDON_GIA').val()))) {
				DlgUtil.showMsg('Đơn giá không đúng !', function() {
					$("#txtDON_GIA").focus();
				});
				kq = '0';
				return kq;
			}
			if ($.trim($("#txtVAT").val()) == ''
					|| parseFloat($("#txtVAT").val()) > 100
					||!isNumeric(replaceStrtoNum($('#txtVAT').val()))) {
				DlgUtil.showMsg('VAT không đúng !', function() {

					$("#txtVAT").focus();
				});
				kq = '0';
				return kq;
			}

			if ($("#txtTHANH_TIEN").val().length > 20) {
				DlgUtil.showMsg(
						'Thành tiền quá lớn!',
						function() {
							$("#txtSO_LUONG").focus();
						});
				kq = '0';
				return kq;
			}
			if(!isNumeric(replaceStrtoNum($('#txtTHANH_TIEN').val()))){
				DlgUtil.showMsg('Thành tiền không đúng!');
				kq ='0';
				return kq;
			}
			
			// 1 số thuốc vật tư ko cần số lô hạn dùng theo mã nhóm
			var str_solohandung = $.trim(PHARMA_NHOMTVT_THIEU_SOLO_HSD);
			var arraykho = str_solohandung.split(',');
			if(arraykho.indexOf(MA_NHOM) == -1){
				if ($("#hid_kieu").val() == 'THUOC' 
					|| $("#hid_kieu").val() == 'VATTU'
						|| $("#hid_kieu").val() == 'NHAPBUTHUOC'
							|| $("#hid_kieu").val() == 'NHAPBUVATTU') {
								if ($("#txtSO_LO").val().trim() == '')
								{
								DlgUtil.showMsg('Chưa nhập số lô !', function() {
									$("#txtSO_LO").focus();
								});
								kq = '0';
								return kq;
							  }
				}
				
				if ($("#txtHSD").val() == '0' 
					|| $("#txtHSD").val() == ''
					||	!isDate($("#txtHSD").val())) {
					DlgUtil.showMsg('Hạn sử dụng ' + title_thuocvatu
							+ ' không đúng !', function() {
						$("#txtHSD").focus();
					});
					kq = '0';
					return kq;
				}

				var hsd = $("#txtHSD").val();

				var hsd1 = hsd.substring(0, 2);
				var hsd2 = hsd.substring(3, 5);
				var hsd3 = hsd.substring(6, 10);

				var d = new Date();

				var _month = (d.getMonth() + 1);
				var _date = d.getDate();
				if (_month < 10) {
					_month = '0' + _month;
				}
				if (_date < 10) {
					_date = '0' + _date;
				}
				var strDate = d.getFullYear() + "" + _month + "" + _date;
				var hsd_check = hsd2 + '-' + hsd1 + '-' + hsd3;
				var pattern = /^(?:(0[1-9]|1[012])[\- \/.](0[1-9]|[12][0-9]|3[01])[\- \/.](19|20)[0-9]{2})$/;
				if ((parseFloat(hsd3 + hsd2 + hsd1) <= parseFloat(strDate))
						|| !hsd_check.match(pattern)) {

					DlgUtil.showMsg('Hạn sử dụng ' + title_thuocvatu
							+ ' không đúng !', function() {

						$("#txtHSD").focus();
					});

					kq = '0';
					return kq;
				}
			}
		

		}

		return kq;
	}
	
	function _isValidateHDKN() {
		if ($("#txtSTT").val() == '') 
		{
			DlgUtil.showMsg('STT không được để trống !', function() {
				$("#txtSTT").focus();
			});			
			return 0;
		}
		if ($("#txtCHUC_DANH").val() == '') 
		{
			DlgUtil.showMsg('Chức danh không được để trống !', function() {
				$("#txtCHUC_DANH").focus();
			});			
			return 0;
		}
		if ($("#txtHoTen_HDKN").val() == '') 
		{
			DlgUtil.showMsg('Họ tên không được để trống !', function() {
				$("#txtHoTen_HDKN").focus();
			});			
			return 0;
		}
		return 1;
	}

	function DeleteRowThuoc() {

		if ($("#hid_ChonThuoc").val() == "0") {

			DlgUtil
					.showMsg('Bạn phải chọn danh sách ' + title_thuocvatu
							+ ' !');
		} else {
			var result = confirm("Bạn có chắc chắn muốn xóa " + title_thuocvatu
					+ " ?");
			if (result) {
				$('#gridList_THUOC').delRowData($("#hidAddOrUpdate").val());
				$("#hid_ChonThuoc").val("0");
				_Reset();
				$("#txtMA_THUOC").val("");
				$("#txtTEN_THUOC").val("");
				$("#hidTHUOCVATTU").val("");

			}

		}

	}

	function SetColor() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		for (i = 0; i < rowIds.length; i++) {
			rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[i]);

			if (parseFloat(rowData['SL_DUYET']) <= 0 || rowData['HSD'] == '') {
				$("#gridList_THUOC").jqGrid('setRowData', rowIds[i], "", {
					color : 'red'
				});

			} else
				$("#gridList_THUOC").jqGrid('setRowData', rowIds[i], "", {
					color : 'black'
				});
		}
	}

	function AddOrUpdateRowData() {		
		
	
		
		
		var row =$("#gridList_THUOC").jqGrid('getGridParam','selrow');  
		//var rowData = jQuery("#gridList_THUOC").getRowData(row);
	  //  hi_thuocid = rowData['THUOCVATTUID']; 
		if (hi_thuocid == $("#hidTHUOCVATTU").val()){
			$("#hidAddOrUpdate").val(row);
		}
		else {
			hi_thuocid = '0';
		}
	/*	var hi_thuocid = '0';
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		for (var k = 0; k < rowIds.length; k++) {
			var rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[k]);
			hi_thuocid = rowData.THUOCVATTUID;
			if (hi_thuocid == $("#hidTHUOCVATTU").val()) {

				$("#hidAddOrUpdate").val(rowIds[k]);
				break;
			} else {
				hi_thuocid = '0';
			}
		}*/
		
		
//		for (var k = 0; k < rowIds.length; k++) {
//			var rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[k]);
//			hi_thuocid = rowData.THUOCVATTUID;
//			if (hi_thuocid == $("#hidTHUOCVATTU").val()) {
//
//				$("#hidAddOrUpdate").val(rowIds[k]);
//				break;
//			} else {
//				hi_thuocid = '0';
//			}
//
//		}
		/*
		 * var hsd1 = $("#txtHSD1").val(); var hsd2 = $("#txtHSD2").val(); var
		 * hsd3 = $("#txtHSD3").val(); var hsd; if (hsd1.length < 2){ hsd1 = '0' +
		 * hsd1; } if (hsd2.length < 2){ hsd2 = '0' + hsd2; }
		 */		
	    var _gianhandan = '0';	
		var _giadichvu = '0';	
		var _giabhyt = '0';		
		var _giatranbhyt = '0';	
		if(PHARMA_LAYGIA_2SOLE == '1'){
			_gianhandan = $("#txtGIA_NHAN_DAN").val().substring(0,$("#txtGIA_NHAN_DAN").val().length - 1);
			_giadichvu = $("#txtGIA_DICH_VU").val().substring(0,$("#txtGIA_DICH_VU").val().length - 1);
			_giabhyt = $("#txtGIA_BHYT").val().substring(0,$("#txtGIA_BHYT").val().length - 1);
			_giatranbhyt = $("#txtGIA_TRAN_BHYT").val().substring(0,$("#txtGIA_TRAN_BHYT").val().length - 1);
			
		}else{
			_gianhandan = $("#txtGIA_NHAN_DAN").val();
			_giadichvu = $("#txtGIA_DICH_VU").val();
			_giabhyt = $("#txtGIA_BHYT").val();
			_giatranbhyt = $("#txtGIA_TRAN_BHYT").val();
		}
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');
		
		var hsd = ($("#txtHSD").val()).substring(0, 10);

		if (hi_thuocid == '0') {

			var rowCount = $("#gridList_THUOC").getGridParam("reccount");

			var datarow = {
				THUOCVATTUID : $("#hidTHUOCVATTU").val(),
				MA_THUOC : $("#txtMA_THUOC").val(),
				TEN_THUOC : $("#txtTEN_THUOC").val(),
				BIET_DUOC : '',
				DON_VI_TINH : $("#txtDON_VI_TINH").val(),
				SL_YC : replaceStrtoNum($("#txtSO_LUONG").val()),				
				SL_DVT_CU : replaceStrtoNum($("#hid_SLDVT_CU").val()),
				SL_DUYET : replaceStrtoNum($("#txtSO_LUONG").val()),
				DON_GIA : replaceStrtoNum($("#txtDON_GIA").val()),
				VAT : $("#txtVAT").val(),
				THANH_TIEN : replaceStrtoNum($("#txtTHANH_TIEN").val()),
				TIEN_TRUOC_VAT : replaceStrtoNum($("#hid_thanhtientruocvat").val()),
				//THUEGTGT : replaceStrtoNum(Number(replaceStrtoNum($("#txtTHANH_TIEN").val()) - replaceStrtoNum($("#hid_thanhtientruocvat").val())).format(2,3)),
				THUEGTGT :replaceStrtoNum(Number(replaceStrtoNum($("#txtDON_GIA").val()) * replaceStrtoNum($("#txtVAT").val())/100 *replaceStrtoNum($("#txtSO_LUONG").val())).format(2,3)),
				SO_LO : FormUtil.escape($("#txtSO_LO").val()),
				NGUOC_CT : '',
				LIEULUONG : $("#hidLieuLuong").val(),
				NUOC_SAN_XUAT : $("#txtNUOC_SAN_XUAT").val(),
				HANG_SAN_XUAT : $("#txtHANG_SAN_XUAT").val(),
				DON_GIA_VAT : replaceStrtoNum($("#txtDON_GIA_VAT").val()),
				TYLEHUHAO :  $("#hid_TYLEHUHAO").val() ,
				SDK : FormUtil.escape($("#txtSDK").val()),
				GOI_THAU : FormUtil.escape($("#txtGOI_THAU").val()),
				HSD : hsd,
				DONVITINHID : $("#h_donvitinhid").val(),
				DVTNHANID : $("#hid_DVTQD").val(),
				GIA_BAN : replaceStrtoNum($("#txtGIABAN").val()),
				GIANHANDAN : replaceStrtoNum(_gianhandan),
				GIADICHVU : replaceStrtoNum(_giadichvu),
				GIABHYT : replaceStrtoNum(_giabhyt),
				GIATRANBHYT : replaceStrtoNum(_giatranbhyt),
				STTTHAU : $("#txtSTTTHAU").val()
			};
			// $('#gridList_THUOC').jqGrid('addRowData',rowCount+1,datarow);
			// $("#list").trigger("reloadGrid");
			// $("#list").setGridParam({page:1}).trigger("reloadGrid");
			// sumCoin();
			if (rowIds == null || rowIds.length <= 0) {
				$('#gridList_THUOC').jqGrid('addRowData', 1, datarow);
			} else {
				$('#gridList_THUOC').jqGrid('addRowData',
						Math.max.apply(null, rowIds) + 1, datarow);
			}
			reloadCash();
			$("#hidAddOrUpdate").val(rowCount + 1);
		} else {
			var item_id = $("#hidAddOrUpdate").val();
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_YC',
					replaceStrtoNum($("#txtSO_LUONG").val()));			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_DVT_CU',
					replaceStrtoNum($("#hid_SLDVT_CU").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SL_DUYET',
					replaceStrtoNum($("#txtSO_LUONG").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA',
					replaceStrtoNum($("#txtDON_GIA").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'VAT',
					$("#txtVAT").val());
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THANH_TIEN',
					replaceStrtoNum($("#txtTHANH_TIEN").val()));
//			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THUEGTGT',
//					replaceStrtoNum(Number(replaceStrtoNum($("#txtTHANH_TIEN").val()) -  replaceStrtoNum($("#hid_thanhtientruocvat").val())).format(2,3)));			
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'THUEGTGT',
					replaceStrtoNum(Number(replaceStrtoNum($("#txtDON_GIA").val()) * replaceStrtoNum($("#txtVAT").val())/100 *replaceStrtoNum($("#txtSO_LUONG").val())).format(2,3)));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'TIEN_TRUOC_VAT',
					replaceStrtoNum($("#hid_thanhtientruocvat").val()));
//			TIEN_TRUOC_VAT : replaceStrtoNum($("#hid_thanhtientruocvat").val()),
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SO_LO',
					FormUtil.escape($("#txtSO_LO").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'TYLEHUHAO',
					FormUtil.escape($("#hid_TYLEHUHAO").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'LIEULUONG',
					FormUtil.escape($("#hidLieuLuong").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'SDK',
					FormUtil.escape($("#txtSDK").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GOI_THAU',
					FormUtil.escape($("#txtGOI_THAU").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'HSD', hsd);
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'DON_GIA_VAT',
					replaceStrtoNum($("#txtDON_GIA_VAT").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIA_BAN',
					replaceStrtoNum($("#txtGIABAN").val()));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIANHANDAN',
					replaceStrtoNum(_gianhandan));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIADICHVU',
					replaceStrtoNum(_giadichvu));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIABHYT',
					replaceStrtoNum(_giabhyt));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'GIATRANBHYT',
					replaceStrtoNum(_giatranbhyt));
			$('#gridList_THUOC').jqGrid('setCell', item_id, 'STTTHAU',
					$("#txtSTTTHAU").val());
			// $('#gridList_THUOC').jqGrid('setCell', item_id, 'HSD2', hsd2);
			// $('#gridList_THUOC').jqGrid('setCell', item_id, 'HSD3', hsd3);
			reloadCash();

			$("#txtMA_THUOC").focus();
		}
		if ($("#hid_kieu").val() == 'DUTRU') 
		SetColor();
		
		$('input[id*="gs_"]').val("");
		$('select[id*="gs_"]').val("ALL");
		$("#gridList_THUOC").jqGrid('setGridParam', {
			search : false,
			postData : {
				"filters" : ""
			}
		}).trigger("reloadGrid");
	 
	}
	function AddHDKN(){	
         	var rowIds = $('#gridList_HDKN').jqGrid('getDataIDs');	   
			var rowCount = $("#gridList_HDKN").getGridParam("reccount");
			var datarow = {				
				STT : $("#txtSTT").val(),
				CHUCDANH : $("#txtCHUC_DANH").val(),				
				HOTEN : $("#txtHoTen_HDKN").val()
			};
			if (rowIds == null || rowIds.length <= 0) {
				$('#gridList_HDKN').jqGrid('addRowData', 1, datarow);
			} else {
				$('#gridList_HDKN').jqGrid('addRowData',
						Math.max.apply(null, rowIds) + 1, datarow);
			}
	}

	function reloadCash() {
		var rowIds = $('#gridList_THUOC').jqGrid('getDataIDs');

		var sumAll = 0;
		var thuegtgt = 0;
		for (var k = 0; k < rowIds.length; k++) {
			var rowData = $('#gridList_THUOC').jqGrid('getRowData', rowIds[k]);
			sumAll = parseFloat(sumAll) + parseFloat(rowData.TIEN_TRUOC_VAT);
			//thuegtgt = thuegtgt + (parseFloat(rowData.DON_GIA)* parseFloat(rowData.VAT)* parseFloat(rowData.SL_DUYET)/100);
			thuegtgt = parseFloat(thuegtgt) + parseFloat(rowData.THUEGTGT);
			
			
			//thuegtgt = thuegtgt + (parseFloat(rowData.THANH_TIEN) - parseFloat(rowData.TIEN_TRUOC_VAT));
			//thuegtgt = thuegtgt + (parseFloat(rowData.DON_GIA_VAT) - parseFloat(rowData.DON_GIA))*parseFloat(rowData.SL_DUYET);
			
		}
		var thuegtgt2 = parseFloat(thuegtgt.toFixed(0));
		var tien_chiet_khau = (sumAll * parseFloat($("#txtCHIET_KHAU").val())) / 100;
		$("#h_thanhtoantong").val(sumAll);
		$("#tiendon").text(sumAll.format(_lamtron, 3, ',') + 'đ');
		$("#thuegtgt").text(thuegtgt2.format(_lamtron, 3, ',') + 'đ');
		$("#tienchietkhau").text(tien_chiet_khau.format(_lamtron, 3, ',') + 'đ');
		$("#tongcong").text(
				(sumAll - parseFloat(tien_chiet_khau) + thuegtgt2).format(_lamtron, 3, ',') + 'đ');
		$("#conlai")
				.text(
						(sumAll - parseFloat(replaceStrtoNum($("#txtTHANH_TOAN_TONG").val())) - parseFloat(tien_chiet_khau) + thuegtgt2)
								.format(_lamtron, 3, ',')
								+ 'đ');

		$("#tongcong").val(sumAll - parseFloat(tien_chiet_khau) + thuegtgt2);
		$("#tienchietkhau").val(tien_chiet_khau);
		$("#thuegtgt").val(thuegtgt2);
		$("#tiendon").val(sumAll);
		$("#conlai").val(
				sumAll - parseFloat(replaceStrtoNum($("#txtTHANH_TOAN_TONG").val()))
						- parseFloat(tien_chiet_khau) + thuegtgt2);

		// $("#conlai").text(sumPay);
	}
	$('#txtTHANH_TOAN_TONG').change(function () {
		   
		//$("#conlai").text((parseInt($("#h_thanhtoantong").val()) - parseInt($("#txtTHANH_TOAN_TONG").val())).format(0,3,',') + 'đ');
		$('#txtTHANH_TOAN_TONG').val(Number(replaceStrtoNum($('#txtTHANH_TOAN_TONG').val())).format(_lamtron,3));
		if($('#txtTHANH_TOAN_TONG').val()==''|| parseFloat($('#txtTHANH_TOAN_TONG').val()) <0 )
			$('#txtTHANH_TOAN_TONG').val('0');
		reloadCash();
	});

	$('#txtCHIET_KHAU').change(function() {

		// $("#conlai").text((parseFloat($("#h_thanhtoantong").val()) -
		// parseFloat($("#txtTHANH_TOAN_TONG").val())).format(0,3,',') + 'đ');
		if($('#txtCHIET_KHAU').val()==''|| parseFloat($('#txtCHIET_KHAU').val()) <0 || parseFloat($('#txtCHIET_KHAU').val())>100)
			$('#txtCHIET_KHAU').val('0');
		reloadCash();
	});
	function checkRole(control){	
		// that.opt.ht
				var _parPQ = 'DUC01S002_PhieuYeuCau' +'%ht=1&'+ '$';
				  var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ	); 
				  if(result.length==0) $('#'+control).hide();
				  for(var i =0; i< result.length ;i++){
				    	if(result[i].ELEMENT_ID == control){
				    	if (result[i].ROLES  =='1') 
				    		$('#'+result[i].ELEMENT_ID).show();
				    	if (result[i].ROLES  =='0'||result[i].ROLES  =='') 
				    		$('#'+result[i].ELEMENT_ID).hide();
				    	}	    
				    }
			};

	function createXML(param_arr) {
		var xml = "<xmlParent>";
		for ( var paramObj in param_arr) {
			xml = xml.concat("<xmlChild>");
			var objTmp = param_arr[paramObj];
			for ( var p in objTmp) {
				if (objTmp.hasOwnProperty(p)) {
					xml = xml
							.concat("<" + p + ">" + objTmp[p] + "</" + p + ">");
				}
			}
			xml = xml.concat("</xmlChild>");
		}
		xml = xml.concat("</xmlParent>");
		return xml;
	}
	// lấy mã phiếu tự động
	function layMaPhieu(){
		/*var _par = [ _loaiphieunhap ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC02N001.04",
				_par.join('$'));
		if (result == '0') {
			DlgUtil.showMsg('Lấy stt lỗi');
			return ;
		} else {
			return result;
		}*/
		/*var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC04D001.09", _par
				.join('$'));

		var data = $.parseJSON(result);

		 _succes = data.SUCCESS;*/
		var _par = [ _hinhthuc,_kieu,$("#hid_KHOID").val() ];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_GET_SOPHIEU",
				_par.join('$'));
		var data = $.parseJSON(result);

		 _succes = data.SUCCESS;
		if (_succes == '0') {
			DlgUtil.showMsg('Lấy stt lỗi');
			return ;
		} else {
			//var _val = data.MESSAGE.replace("MA1", _loaiphieunhap);
			//_val = data.MESSAGE.replace("MA", _loaiphieunhap);
			return data.MESSAGE;
		}
	}
	function replaceStrtoNum(strSoluong){
		return Number(strSoluong.replace(/[^0-9\.]+/g,""));
	};
	function isDate(txtDate) {
		var currVal = txtDate;
		if (currVal == '')
			return false;

		//Declare Regex  
		var rxDatePattern = /^(\d{1,2})(\/|-)(\d{1,2})(\/|-)(\d{4})$/;
		var dtArray = currVal.match(rxDatePattern); // is format OK?

		if (dtArray == null)
			return false;

		//Checks for mm/dd/yyyy format.
		dtDay = dtArray[1];
		dtMonth = dtArray[3];
		dtYear = dtArray[5];

		if (dtMonth < 1 || dtMonth > 12)
			return false;
		else if (dtDay < 1 || dtDay > 31)
			return false;
		else if ((dtMonth == 4 || dtMonth == 6 || dtMonth == 9 || dtMonth == 11)
				&& dtDay == 31)
			return false;
		else if (dtMonth == 2) {
			var isleap = (dtYear % 4 == 0 && (dtYear % 100 != 0 || dtYear % 400 == 0));
			if (dtDay > 29 || (dtDay == 29 && !isleap))
				return false;
		}
		return true;
	};
	function isNumeric(n) {
		  return !isNaN(parseFloat(n)) && isFinite(n);
		}

}
