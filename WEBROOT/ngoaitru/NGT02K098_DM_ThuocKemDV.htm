<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">        
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>   
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
 
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js "></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.src.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/xml2json.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT02K098_DM_ThuocKemDV.js?v=20180325"></script>

<div  width="100%"  id="divMain" class="container">
     <!--  <legend>QUẢN LÝ DM Cau Hinh</legend>-->
     
     <div class="col-md-12 low-padding">
     	<div class="col-xs-12 low-padding mgt3">
						<div class="col-xs-1">
							<label class="control-label">Loại</label>
						</div>
						<div class="col-xs-3 low-padding">
							<select class="form-control input-sm" style="width: 100%" id="cboLOAIDV">
								<option value="3">Xét nghiệm</option>
								<option value="4">CĐHA và TDCN</option>
								<option value="5">Phẫu thuật, thủ thuật</option>
								<option value="13">Dịch vụ Giường</option>
								<option value="1">Thu khác</option>
							</select>
						</div>
						<div class="col-xs-8 low-padding">
					<div class="mgl15 capthuoc">
			<div class="col-xs-12 low-padding searchthuoc">
				<div class="col-xs-4 low-padding required" id="dvSearchName" style="text-align: center;">
					<label class="mgt5" id="lbSearchName">Tên thuốc/Vật tư</label>
				</div>
				<div class="col-xs-2 low-padding required" style="text-align: center;display:none;" id="dvTenThuoc">
					<label class="mgt5" id="lbTenThuoc"></label>
				</div>
				<div class="col-xs-2 low-padding required" style="text-align: center;" id="lblDUONGDUNG">
					<label class="mgl5 mgt5">Đường dùng</label>
				</div>
				<div class="col-xs-1 low-padding" id="dvKE_TONG_LB">
					<div class="col-xs-12 low-padding" style="text-align: center;">
						<label class="mgt5">Số lượng<span class="required1">(*)</span></label>
					</div>
				</div>
				<div class="col-xs-4 low-padding" style="text-align: center;" id="dvlGhiChu">
					<label class="mgt5" id="lblcachdung">Cách dùng<span class="required1">(*)</span></label>
				</div>
				<div class="col-xs-1 low-padding">
					<div class="col-xs-12 low-padding" style="text-align: center;">
						<label class="mgt5">Sử dụng<span class="required1">(*)</span></label>
					</div>
				</div>
			</div>
		
		<div class="col-xs-12 low-padding mgt5 mgb3">
			<!-- <div class="col-xs-2 low-padding before"> -->
			<div class="col-xs-4 low-padding" id="dvDS_THUOC">
				<input class="form-control input-sm mgl5" style="width: 95%;" id="txtDS_THUOC"/>
			</div>
			
			<div class="col-xs-2 low-padding" id="dvDUONG_DUNG">
				<select class="form-control input-sm" style="width: 98%;" id="cboDUONG_DUNG" valrule="Đường dùng,trim_required"></select>
			</div>
			<div class="col-xs-1 low-padding required" id="dvKE_TONG_TXT">
				<div class="col-xs-12 low-padding">
					<input class="form-control input-sm clsfloat" style="width: 95%;" id="txtSOLUONG_TONG" name="txtSOLUONG_TONG" valrule="Số lượng,trim_required|decimal|max_length[3]" title="">
				</div>
			</div>

			<div class="col-xs-4 low-padding" id="dvGhiChu">
				<input class="form-control input-sm" msgErr="Cách dùng," style="width: 99%;" id="txtGHICHU" name="txtGHICHU" valrule="Ghi chú,max_length[200]" title="">
			</div>
			<div class="col-xs-1 low-padding"  style="text-align: center;">
				<input type="checkbox" id="chkSUDUNG" value="">
			</div>
			
		</div>
	</div>
						</div>
						
					</div>
     	<div class="col-md-4 low-padding mgt-10">
			<table id="grdDSThuoc"></table><div id="pager_grdDSThuoc"></div>	
     	</div>
     	<div class="col-md-8 low-padding mgt-10" style="padding-left: 15px !important;">
     		<table id="grdDSThuocCT"></table><div id="pager_grdDSThuocCT"></div>	
     	</div>
     	<div class="col-md-12 low-padding mgt10 mgb5" style="text-align: center;">
			    			<button type="button" class="btn btn-sm btn-primary" id="btnLuu" >
							      <span class="glyphicon glyphicon-floppy-disk"></span> Lưu
							</button>
							
			    		
		</div>
     </div>    


</div>
<script>
		var opt=[];
		var hospital_id = '{hospital_id}';
		var user_id = '{user_id}';
		var user_type = '{user_type}';
		var province_id = '{province_id}';
		var uuid = '{uuid}';
		var lang= "vi";
		console.log('hospital_id='+hospital_id+' user_type='+user_type);
		var session_par=[];
		session_par[0]=hospital_id;
		session_par[1]=user_id;
		session_par[2]=user_type;
		session_par[3]=province_id;
		var table_name='{table}';
		
		initRest(uuid,"/vnpthis");
		
		var _opts=new Object();
		_opts.lang=lang;
		_opts._param=session_par;
		
		var mode = '{showMode}';	
		var data;
		if(mode=='dlg') {
			parent.DlgUtil.tunnel(DlgUtil.moveEvent);
			data=EventUtil.getVar("dlgVar");
			_opts._khambenhid = data._khambenhid;
		}
		
		_opts._uuid=uuid;
		//_opts._khambenhid="11840";
		_opts._benhnhanid="";
		_opts._hosobenhanid="";
		var DS = new dSCHList(_opts);
		DS.load(hospital_id);
</script>