<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
       value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
        src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript"
        src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
      href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
      rel="stylesheet" />
<script type="text/javascript"
        src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>

<script src="../common/script/jqgrid/js/context-menu.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript"
        src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/xml2json.js"></script>
<script type="text/javascript"
        src="../ngoaitru/NGT07K002_QUANLYHOSOBENHANDAINGAY.js?v=240701821"></script>
<script type="text/javascript"
        src="../noitru/cominf.js?v=221115"></script>
<script type="text/javascript"
        src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css"
      type="text/css" />

<div id="divMain" class="container">
    <div class="mgt3" style="text-align: right">Quản lý Hồ sơ bệnh án dài ngày</div>
    <div id="divSearch" class="col-xs-12 low-padding border-group-1 mgt5">
        <div class="col-xs-12 mgt5 low-padding">
            <div class="col-xs-3 low-padding">
                <div class="col-xs-4 low-padding">
                    <label class="control-label">Từ ngày</label>
                </div>
                <div class="col-xs-7 low-padding">
                    <div class="input-group mgt3">
                        <input class="form-control input-sm"
                               id="txtTuNgay" name="txtTuNgay" title="" data-mask="00/00/0000"
                               placeholder="dd/MM/yyyy"> <span
                            class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar ngay-den"
                            type="sCal"
                            onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)">
                            </span>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 low-padding">
                <div class="col-xs-4 low-padding">
                    <label class="control-label">Đến ngày</label>
                </div>
                <div class="col-xs-7 low-padding">
                    <div class="input-group mgt3">
                        <input class="form-control input-sm"
                               id="txtDenNgay" name="txtDenNgay" title=""
                               data-mask="00/00/0000" placeholder="dd/MM/yyyy"> <span
                            class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                            type="sCal"
                            onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)">
                            </span>
                    </div>
                </div>
            </div>

            <div class="col-xs-3 low-padding">
                <div class="col-xs-6 low-padding">
                    <button class="btn btn-sm btn-primary" id="btnTimKiem" style="width:70%;">
                        <span class="glyphicon glyphicon-search"></span> Tìm kiếm
                    </button>
                </div>
                <div class="col-xs-6 low-padding">
                    <button id="btnDong" class="btn btn-sm btn-primary" style="width:45%;">
                        <span style="margin-left: -3px;"></span> Đóng
                    </button>
                    <button id="btnMo" class="btn btn-sm btn-primary" style="width:45%;">
                        <span></span> Mở
                    </button>
                </div>
            </div>
        </div>

    </div>

    <div class="col-md-13">
        <div class="col-xs-12 low-padding">
            <table id="grdDSBN"></table>
            <div id="pager_grdDSBN"></div>
        </div>
    </div>

</div>

<script>
    var userInfo = CommonUtil.decode('{userData}');
    var opt = [];
    var schema = '{db_schema}';
    var hospital_id = '{hospital_id}';
    var company_id = '{company_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var uuid = '{uuid}';
    var dept_id = '{dept_id}';

    initAjax("/vnpthis");
    ajaxSvc.register("InsrWS");
    ajaxSvc.register("InsrWS4210");

    var paramInfo = CommonUtil.decode('{paramData}');
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");

    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid;
    _opts._deptId = dept_id;
    _opts.company_id = company_id;
    _opts.hospital_code = paramInfo.HOSPITAL_CODE;
    var DS = new dSCHList(_opts);
    DS.load(hospital_id);
</script>