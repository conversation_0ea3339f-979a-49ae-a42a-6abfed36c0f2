/*

*/
function ViewSummaryReportLog(_opts) {
    this.load = doLoad;
    let _grdLogReport = "grdDSLogReport";

    function doLoad() {
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        _loadHeaderDSLogReport();
    }


    function _bindEvent() {

    }

    function _loadHeaderDSLogReport() {
        let _gridDSHeader = "" +
            "ID,ID,0,0,t,l,1,2;" +
            "Tên báo cáo,REPORT_NAME,220,0,f,l,1,2;" +
            "Tên tiêu chí,CRITERIA_NAME,250,0,f,l,1,2;" +
            "<PERSON><PERSON><PERSON> tổng hợp,TRANSACTIONID,250,0,f,l,1,2;" +
            "Ngư<PERSON>i tạo,USER_NAME,220,0,f,l,1,2;" +
            "<PERSON><PERSON><PERSON> tạo,CREATE_DATE,150,0,f,l,1,2;" +
            "ERROR LOG,ERROR_MSG,600,0,f,l,1,2"
        ;
        GridUtil.init(_grdLogReport, "100%", "280", "Danh sách form nhập chung", false, _gridDSHeader, false);
    }
}