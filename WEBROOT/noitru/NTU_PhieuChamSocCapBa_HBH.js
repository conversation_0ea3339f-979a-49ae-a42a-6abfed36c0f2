function ChamSocCapBa_HBH(_opt) {
    this.load = doLoad;
    this.opt = $.extend({}, _opt);

    var numColPerTable = 3;

    var param_x;

    var listNhanDinh = ['CHISOSTST', 'TOANTHAN', 'HOHAP', 'TUANHOAN', 'TIEUH<PERSON>', 'DINHDUONG',
        'SANKHOA', 'PHUKHOA', 'GIACNGU', 'VESINH', 'TINHTHAN', 'VANDONG', 'BAITIET',
        'NHUCAUTVGDSK', 'THEODOIKHAC', 'GHICHU']

    function doLoad() {
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        param_x = $('tr[id*="PARAM"]').map(function () { return this.id; }).get();
        var dsphieuid = _opt._dsphieuid;
        var objParam = {
            "phieuid": dsphieuid,
            "form_id": "-1"
        }
        var param = JSON.stringify(objParam);
        var objData = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU.CHAMSOCCAP3VPC", param)[0];
        console.log(objData);

        setTTHanhChinh(objData);

        if (objData.JSON_DATA_CT) {
            var dsPhieuCon = JSON.parse(objData.JSON_DATA_CT);

            console.log(JSON.parse(objData.JSON_DATA_CT));
            var dsPhieuCon = formatData(dsPhieuCon);
            console.log(dsPhieuCon);

            var mainDiv = $('.phieuchitiet table');

            param_x.forEach(key => {
                if (checkEmptyData(dsPhieuCon, key)) {
                    mainDiv.find('#' + key).remove();
                }
            })

            for (let i = 0; i < listNhanDinh.length; i++) {
                var count = mainDiv.find('.' + listNhanDinh[i]).length;
                mainDiv.find('.' + listNhanDinh[i]).first().attr("rowspan", count);
                mainDiv.find('.' + listNhanDinh[i]).first().css("display", "table-cell");
            }

            var numOfTable = Math.ceil(dsPhieuCon.length / numColPerTable);
            for (let i = 0; i < numOfTable; i++) {
                let modelDiv = mainDiv.clone();
                let modelid = 'table' + i;
                $('.phieuchitiet').append(modelDiv);
                let start = i * numColPerTable, end = (dsPhieuCon.length < (i + 1) * numColPerTable ? dsPhieuCon.length : (i + 1) * numColPerTable);
                for (let j = start; j < end; j++) {
                    for (const [key, value] of Object.entries(dsPhieuCon[j])) {
                        let cssChild = ' #' + key + ' .txtDATA';
                        modelDiv.find(cssChild).eq(j % numColPerTable).html(value);
                    }
                }
                modelDiv.attr({ id: modelid });
            }
            mainDiv.remove();

            setTTChanDoanMucTieu(objData);
            setTTQuyuoc(JSON.parse(objData.JSON_DATA_CT));
        }
        else {
            //$('.phieuchitiet table').remove();
        }

    }

    function _bindEvent() {
        $('#btn_in_trang').click(function () {
            window.print();
        })
    }

    function setTTHanhChinh(objData) {
        var ttHanhChinhData = {
            'TENBENHNHAN': objData.TENBENHNHAN,
            'TUOI': objData.TUOI + ' ' + objData.DVTUOI,
            'GTNAM': objData.GTNAM,
            'GTNU': objData.GTNU,
            'TSDIUNG0': objData.TSDIUNG0,
            'TSDIUNG1': objData.TSDIUNG1,
            'TSDIUNG2': objData.TSDIUNG2,
            'TENPHONG': objData.TENBUONG,
            'TENGIUONG': objData.TENGIUONG,
            'CHANDOAN': objData.CHANDOAN
        }

        $('#txtPARENT_NAME').text(objData.PARENT_NAME);
        $('#txtORG_NAME').text(objData.ORG_NAME);
        $('#lblSOVAOVIEN').text(objData.SOVAOVIEN);
        $('#lblMABENHNHAN').text(objData.MABENHNHAN);

        FormUtil.clearForm("tthanhchinh", "");
        FormUtil.setObjectToForm("tthanhchinh", "", ttHanhChinhData);
    }

    function setTTChanDoanMucTieu(objData) {
        var dskey = Object.keys(objData);
        console.log(dskey);
        var numOfCD = dskey.filter(item => item.startsWith("TTCHANDOAN")).length;
        var count = 1;
        for (let i = 1; i <= numOfCD; i++) {
            let keyOfCD = 'TTCHANDOAN' + i;
            let objCD = JSON.parse(objData[keyOfCD]);
            let muctieuchon = objCD.MUCTIEUCHON.split("\|");
            let htmlCHANDOAN = '';
            if (objCD && !objCD.CHANDOANID.startsWith("-1")) {
                htmlCHANDOAN = '<div><div><span class="fw-600">Chẩn đoán ' + (count++) + ': </span>'
                    + objCD.TENCHANDOAN + '</div>';
            }
            let dsMT = objCD.DSMUCTIEU;
            let htmlMUCTIEU = '';
            for (let j = 0; j < dsMT.length; j++) {
                let check = muctieuchon.includes(dsMT[j].MUCTIEUID) ? 'checked' : '';
                if (dsMT[j]) {
                    htmlMUCTIEU = htmlMUCTIEU + '<div><input type="checkbox" disabled="" label="" ' + check + '>Mục tiêu ' + ': '
                        + dsMT[j].TENMUCTIEU + '</div>'
                }
            }
            $('#CHANDOAN').append('<div style="margin-top: 6px;">' + htmlCHANDOAN + htmlMUCTIEU + '</div>');
        }
    }

    function setTTQuyuoc(objData) {
        var dskyhieu = new Set();
        var keyName = ['PARAM_117', 'PARAM_118', 'PARAM_119', 'PARAM_120', 'PARAM_121', 'PARAM_122'];
        for (let i = 0; i < objData.length; i++) {
            for (let j = 0; j < keyName.length; j++) {
                if (objData[i][keyName[j]]) {
                    let kyhieu = objData[i][keyName[j]].split(";");
                    kyhieu.forEach(x => dskyhieu.add(x));
                }
            }
        }
        dskyhieu = Array.from(dskyhieu);
        dskyhieu.sort();
        var htmlQuyUoc = '';
        dskyhieu.forEach(function (value) {
            htmlQuyUoc += '<p>' + value + '</p>';
        })
        $('#QUYUOC').append(htmlQuyUoc);
    }

    function checkEmptyData(objData, key) {
        for (let i = 0; i < objData.length; i++) {
            if (objData[i][key])
                return false;
        }
        console.log(key);
        return true;
    }

    function splitChamSocDieuDuong(chamsoc) {
        return chamsoc ? chamsoc.split(";").map(x => x.split(":")[0]).join('; ') : "";
    }

    function formatData(objectData) {
        let end = objectData.length;
        for (let i = 0; i < end; i++) {
            objectData[i]['PARAM_49'] = objectData[i]['PARAM_49'].startsWith("-1") ? "" : objectData[i]['PARAM_49'].split("\|")[1];
            objectData[i]['DIEUDUONG'] = objectData[i]['DIEUDUONG'].startsWith("-1") ? "" : objectData[i]['DIEUDUONG'].split("\/")[1];
            objectData[i]['PARAM_117'] = splitChamSocDieuDuong(objectData[i]['PARAM_117']);
            objectData[i]['PARAM_118'] = splitChamSocDieuDuong(objectData[i]['PARAM_118']);
            objectData[i]['PARAM_119'] = splitChamSocDieuDuong(objectData[i]['PARAM_119']);
            objectData[i]['PARAM_120'] = splitChamSocDieuDuong(objectData[i]['PARAM_120']);
            objectData[i]['PARAM_121'] = splitChamSocDieuDuong(objectData[i]['PARAM_121']);
            objectData[i]['PARAM_122'] = splitChamSocDieuDuong(objectData[i]['PARAM_122']);
            let thoOxy = objectData[i]['PARAM_25'].split("\|");
            objectData[i]['PARAM_25'] = (thoOxy[0] ? 'C1 canula: ' + thoOxy[0] + ' (l/p); ' : "") + (thoOxy[1] ? 'M Mask: ' + thoOxy[1] + ' (l/p)' : "");
            let tsTimThai = objectData[i]['PARAM_58'].split("\|");
            objectData[i]['PARAM_58'] = (tsTimThai[0] ? 'ổ 1: ' + tsTimThai[0] + ' (l/p); ' : "") + (tsTimThai[1] ? 'ổ 2: ' + tsTimThai[1] + ' (l/p); ' : "") + (tsTimThai[2] ? 'ổ 3: ' + tsTimThai[2] + ' (l/p);' : "");
            let vetThuong = objectData[i]['PARAM_111'].split("\|");
            objectData[i]['PARAM_111'] = (vetThuong[0] ? 'Độ: ' + vetThuong[0] + ' ; ' : "") + (vetThuong[1] ? 'Vị trí: ' + vetThuong[1] : "");
            objectData[i]['PARAM_17'] = (objectData[i]['PARAM_17'] == 'Khác' || objectData[i]['PARAM_17'] == '') ? objectData[i]['PARAM_18'] : objectData[i]['PARAM_17'];
            objectData[i]['PARAM_19'] = (objectData[i]['PARAM_19'] == 'Khác' || objectData[i]['PARAM_19'] == '') ? objectData[i]['PARAM_20'] : objectData[i]['PARAM_19'];
            objectData[i]['PARAM_26'] = (objectData[i]['PARAM_26'] == 'Khác' || objectData[i]['PARAM_26'] == '') ? objectData[i]['PARAM_27'] : objectData[i]['PARAM_26'];
            objectData[i]['PARAM_61'] = (objectData[i]['PARAM_61'] == 'Khác' || objectData[i]['PARAM_61'] == '') ? objectData[i]['PARAM_62'] : objectData[i]['PARAM_61'];
            objectData[i]['PARAM_63'] = (objectData[i]['PARAM_63'] == 'Khác' || objectData[i]['PARAM_63'] == '') ? objectData[i]['PARAM_64'] : objectData[i]['PARAM_63'];
            objectData[i]['PARAM_124'] = (objectData[i]['PARAM_124']) ? objectData[i]['PARAM_124'].substring(0, 16) : '';
        }

        return objectData;
    }

}