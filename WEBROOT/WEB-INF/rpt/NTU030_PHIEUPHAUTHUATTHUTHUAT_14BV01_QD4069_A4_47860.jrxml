<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4_47860" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="519" leftMargin="56" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6ccec38e-3237-42f1-8ab6-cb46b46adcbe">
	<property name="ireport.zoom" value="1.3310000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="hinh_anh0" class="java.awt.Image"/>
	<parameter name="hinh_anh1" class="java.awt.Image"/>
	<parameter name="hinh_anh2" class="java.awt.Image"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="dichvukhambenhid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="benhnhanid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call ntu_phieuphauthuatthuthuat_960($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{dichvukhambenhid},$P{benhnhanid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="PHAUTHUATTHUTHUATID" class="java.math.BigDecimal"/>
	<field name="NGAYPHAUTHUATTHUTHUAT" class="java.lang.String"/>
	<field name="MA_CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="MA_CHANDOANVAOKHOAKEMTHEO" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOAKEMTHEO" class="java.lang.String"/>
	<field name="MA_CHANDOANTRUOCPHAUTHUAT" class="java.lang.String"/>
	<field name="CHANDOANTRUOCPHAUTHUAT" class="java.lang.String"/>
	<field name="MA_CHANDOANSAUPHAUTHUAT" class="java.lang.String"/>
	<field name="CHANDOANSAUPHAUTHUAT" class="java.lang.String"/>
	<field name="DICHVUKHAMBENHID" class="java.math.BigDecimal"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="PHUONGPHAPPTTT" class="java.lang.String"/>
	<field name="PTTTID" class="java.lang.String"/>
	<field name="NGAYGIUONGPTTT" class="java.lang.String"/>
	<field name="PHAUTHUATVIEN1" class="java.math.BigDecimal"/>
	<field name="TENPHAUTHUATVIEN" class="java.lang.String"/>
	<field name="TENPHAUTHUATVIENPLUS" class="java.lang.String"/>
	<field name="PHAUTHUATVIEN2" class="java.math.BigDecimal"/>
	<field name="TENPHAUTHUATVIEN2" class="java.lang.String"/>
	<field name="DUNGCUVIEN" class="java.math.BigDecimal"/>
	<field name="TENDUNGCUVIEN" class="java.lang.String"/>
	<field name="BACSIGAYME" class="java.math.BigDecimal"/>
	<field name="TENBACSIGAYME" class="java.lang.String"/>
	<field name="PHUME" class="java.math.BigDecimal"/>
	<field name="TENPHUME" class="java.lang.String"/>
	<field name="PHUME2" class="java.math.BigDecimal"/>
	<field name="TENPHUME2" class="java.lang.String"/>
	<field name="DIEUDUONG" class="java.math.BigDecimal"/>
	<field name="TENDIEUDUONG" class="java.lang.String"/>
	<field name="PHUMO1" class="java.math.BigDecimal"/>
	<field name="TENPHUMO1" class="java.lang.String"/>
	<field name="PHUMO2" class="java.math.BigDecimal"/>
	<field name="TENPHUMO2" class="java.lang.String"/>
	<field name="PHUMO3" class="java.math.BigDecimal"/>
	<field name="TENPHUMO3" class="java.lang.String"/>
	<field name="PHUMO4" class="java.math.BigDecimal"/>
	<field name="TENPHUMO4" class="java.lang.String"/>
	<field name="PHUMO5" class="java.math.BigDecimal"/>
	<field name="TENPHUMO5" class="java.lang.String"/>
	<field name="PTTT_NHOMMAUID" class="java.math.BigDecimal"/>
	<field name="TENNHOMMAU" class="java.lang.String"/>
	<field name="PTTT_NHOMMAURHID" class="java.math.BigDecimal"/>
	<field name="TENPTTT_NHOMMAURH" class="java.lang.String"/>
	<field name="PTTT_TINHHINHID" class="java.math.BigDecimal"/>
	<field name="TENPHONG" class="java.lang.String"/>
	<field name="PTTT_HANGID" class="java.math.BigDecimal"/>
	<field name="PTTT_HANG_TEN" class="java.lang.String"/>
	<field name="PTTT_PHUONGPHAPVOCAMID" class="java.math.BigDecimal"/>
	<field name="PTTT_PHUONGPHAPVOCAM_TEN" class="java.lang.String"/>
	<field name="THOIGIANVAOVIEN" class="java.sql.Timestamp"/>
	<field name="NAMVV" class="java.math.BigDecimal"/>
	<field name="THANGVV" class="java.math.BigDecimal"/>
	<field name="NGAYVV" class="java.math.BigDecimal"/>
	<field name="GIOVV" class="java.math.BigDecimal"/>
	<field name="PHUTVV" class="java.math.BigDecimal"/>
	<field name="NAMPT" class="java.math.BigDecimal"/>
	<field name="THANGPT" class="java.math.BigDecimal"/>
	<field name="NGAYPT" class="java.math.BigDecimal"/>
	<field name="GIOPT" class="java.math.BigDecimal"/>
	<field name="PHUTPT" class="java.math.BigDecimal"/>
	<field name="SO" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="BUONG" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="MOTA" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="CMND" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="TENBENHVIEN" class="java.lang.String"/>
	<field name="SOYTE" class="java.lang.String"/>
	<field name="MAGIUONG" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="621" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="147" y="0" width="225" height="18" uuid="198d78d0-5478-4f54-a4f6-f350f3fcdd0e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[PHIẾU PHẪU THUẬT, THỦ THUẬT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="372" y="0" width="147" height="18" uuid="da3b80d0-a3f0-4143-8715-e6283be709f5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[MS: 14/BV-01]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="372" y="18" width="64" height="18" uuid="8d6fa238-366d-4dad-9550-1a5c4fbe24eb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Số vào viện: ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="216" width="236" height="20" uuid="1130c6e9-aad5-4cb4-a9ba-4a5b59c8e799"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[LƯỢC ĐỒ PHẪU THUẬT/ THỦ THUẬT]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="436" y="18" width="83" height="18" uuid="7192811e-c807-4987-a353-cdf84f11bac1"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="147" height="18" uuid="61df887a-de2c-44cb-9b6d-b4b4cc4207b5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="18" width="372" height="18" uuid="e26e36bf-c280-4ea2-a7e9-e8a5e9522ff9"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="112" y="44" width="230" height="14" uuid="7ae314e5-918c-41f5-a384-2288340ef78b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="378" y="44" width="82" height="14" uuid="8f640d68-9d61-4301-9e62-3595c3405a45"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI}+ " "+$F{DVTUOI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="58" width="519" height="14" uuid="cb25e870-156a-46df-bf42-d56ed3e107f5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Khoa:"+($F{KHOA}==null?"":$F{KHOA})
+ "        - Buồng: "+ ($F{BUONG}==null?"":$F{BUONG})
+ "        - Giường: "+ ($F{MAGIUONG}==null?"":$F{MAGIUONG})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="133" y="72" width="386" height="14" uuid="d57fa712-ad1c-4fa4-881c-32219b2516e2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOVV} + "  giờ  " +
$F{PHUTVV} + "  phút   ngày  " +
$F{NGAYVV} + "  tháng  " +
$F{THANGVV} + "  năm  " +
$F{NAMVV}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="133" y="86" width="386" height="14" uuid="e6137300-f397-4568-acc4-40a58fee227c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOPT} + "  giờ  " +
$F{PHUTPT} + "  phút   ngày  " +
$F{NGAYPT} + "  tháng  " +
$F{THANGPT} + "  năm  " +
$F{NAMPT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="114" width="519" height="14" uuid="fb42cb49-3d56-44b7-8829-a19869276c16"/>
				<box leftPadding="15"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Trước phẫu thuật/ thủ thuật: "+($F{CHANDOANTRUOCPHAUTHUAT}==null?"":$F{CHANDOANTRUOCPHAUTHUAT})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="128" width="519" height="14" uuid="13e8b0dc-08fa-42b2-ba11-f873ca6de3eb"/>
				<box leftPadding="15"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Sau phẫu thuật/ thủ thuật: "+($F{CHANDOANSAUPHAUTHUAT}==null?"":$F{CHANDOANSAUPHAUTHUAT})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="142" width="519" height="14" uuid="0ee0cb42-af18-4d0c-8efd-5c58a78b74d5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Phương pháp phẫu thuật, thủ thuật:"+
($F{PHUONGPHAPPTTT}==null?"":$F{PHUONGPHAPPTTT})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="44" width="112" height="14" uuid="58f8d2e3-b6c7-48fa-bdbc-cb35c3917e0a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Họ và tên người bệnh:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="342" y="44" width="36" height="14" uuid="24fdf2ec-80b6-4233-83a9-47220aee6522"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Tuổi:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="72" width="133" height="14" uuid="8ccba0f6-410a-4312-ab9a-b59c4d115bac"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Vào viện lúc:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="86" width="133" height="14" uuid="78d97f31-4178-4f67-8aa9-d52b04c59195"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Phẫu thuật/ thủ thuật lúc:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="100" width="519" height="14" uuid="0d981120-aa0c-497f-8c27-bdf908a3056d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Chẩn đoán:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="156" width="165" height="14" uuid="739537b7-c38f-4f6a-a5dc-f8edb3221129"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Phân loại phẫu thuật, thủ thuật:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="170" width="125" height="14" uuid="00eee883-876f-4870-8efc-9d86a035a243"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Phương pháp vô cảm:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="15" y="542" width="174" height="20" uuid="40450ce5-6570-44db-a340-7dbcdec8d2b7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Dẫn lưu:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="15" y="562" width="174" height="20" uuid="f927e797-843f-45d1-925b-a391ebcc8e36"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Ngày rút:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="16" y="580" width="174" height="20" uuid="474906bb-190d-407f-87be-74f3f8c0d83b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Ngày cắt chỉ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="16" y="600" width="174" height="20" uuid="689d083a-dfe2-46b5-a73c-6a8012d71e9e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[- Khác:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="184" width="519" height="14" uuid="73818862-a940-4e2d-8baa-5d9417d1fb9e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Phẫu/thủ thuật viên: "+ $F{TENPHAUTHUATVIEN}
+($F{TENPHUMO1}==null?"":" - "+$F{TENPHUMO1})
+($F{TENPHUMO2}==null?"":" - "+$F{TENPHUMO2})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="198" width="519" height="14" uuid="cea54372-76d7-4c48-ba84-ee37bcdb591e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["BS gây mê: "+$F{TENBACSIGAYME}+($F{TENDUNGCUVIEN}==null?"":";"+$F{TENDUNGCUVIEN})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="165" y="156" width="354" height="14" uuid="25fb3bff-56c2-47d0-a04f-cefa60d92046"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PTTT_HANG_TEN}==null?"":$F{PTTT_HANG_TEN}.replaceAll( "Loại khác", "" )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="125" y="170" width="394" height="14" uuid="cec2bf83-e42c-4f73-92ea-fda99c3e8bd0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PTTT_PHUONGPHAPVOCAM_TEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="460" y="44" width="59" height="14" uuid="e8076c23-b959-41bf-bc40-356ff95b6e19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="236" y="216" width="283" height="20" uuid="3fc271b4-783d-43a7-9dad-4d277a487c2e"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TRÌNH TỰ PHẪU THUẬT, THỦ THUẬT]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="236" width="236" height="385" uuid="aaf4df44-356e-42d9-ac75-749b781c8880"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="236" y="236" width="283" height="385" uuid="3aac6400-ee20-4893-9d4c-81a31a3a6280"/>
				<box leftPadding="2">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MOTA}]]></textFieldExpression>
			</textField>
			<image hAlign="Center">
				<reportElement positionType="Float" x="14" y="239" width="204" height="150" isRemoveLineWhenBlank="true" uuid="b67732b1-a49e-4b66-ad7f-8c3c7476d830"/>
				<imageExpression><![CDATA[$P{hinh_anh0}]]></imageExpression>
			</image>
			<image hAlign="Center">
				<reportElement positionType="Float" x="13" y="392" width="204" height="150" isRemoveLineWhenBlank="true" uuid="2a22babd-69c0-4531-85b7-c5f028ffa5a2"/>
				<imageExpression><![CDATA[$P{hinh_anh1}]]></imageExpression>
			</image>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="116">
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="304" y="20" width="215" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="c520e50f-2164-4b8e-a0dc-e7cda972718b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Phẫu thuật / thủ thuật viên]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="304" y="98" width="215" height="18" uuid="26c78c96-abc7-43d8-a771-501d88aeebfb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENPHAUTHUATVIEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="304" y="2" width="215" height="18" uuid="1262d3ab-f8d4-4f43-93b0-d233277a89b5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + $F{NGAYPT} + " tháng " + $F{THANGPT} + " năm " + $F{NAMPT}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
