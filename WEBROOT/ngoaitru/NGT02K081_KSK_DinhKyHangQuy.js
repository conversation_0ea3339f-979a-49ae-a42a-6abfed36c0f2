
function Ksk_DinhKyNLD(opt) {
	this.load = doLoad;
	var _opts = opt;

	function doLoad() {
		load_ThongtinBN();
		_initControl();
	}
	function _initControl() {
		loadComboGrid();
	}
	function load_ThongtinBN() {}
	function loadComboGrid() {
		var sql_par = [];
		// BĐHCM
		if (_opts.hospital_id == 965) {
			sql_par = [];
			sql_par.push({"name": "[0]", "value": _opts.hospital_id}, {"name": "[1]", "value": 0});
		}
		var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;" +
			"Tài k<PERSON>n,USERNAME,20,0,f,l;" +
			"Tên PTV,FULLNAME,30,0,f,l;" +
			"Ch<PERSON>c danh,CHUCDANH,50,0,f,l";

		ComboUtil.initComboGrid("txtBSKHAM_THELUC_ID", "GCDD.LOAD_USER", sql_par, "600px", _col_loaduser, function (event, ui) {
			$("#txtBSKHAM_THELUC_ID").val("");
			var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.FULLNAME + '</option>');
			$("#cboBSKHAM_THELUC").empty();
			$("#cboBSKHAM_THELUC").append(option);
			return false;
		});
	}
	$("#btnDong").on("click", function (e) {
		parent.DlgUtil.close("dlgKSK_DinhKyHangQuy");
	});
}

