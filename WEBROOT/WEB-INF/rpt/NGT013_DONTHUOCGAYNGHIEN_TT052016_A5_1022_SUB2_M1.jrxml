<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT013_DONTHUOCGAYNGHIEN_TT052016_A5_902_subreport1" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="381" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" isIgnorePagination="true" uuid="7aa9e6e8-b14b-4277-9a60-b444a656ea92">
	<property name="ireport.zoom" value="1.5000000000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="193"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<parameter name="org_tel" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_DONTHUOC_N_1022($P{[UID]},$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="BENHVIEN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA_KEMTHEO" class="java.lang.String"/>
	<field name="STT" class="java.math.BigDecimal"/>
	<field name="TEN_THUOC" class="java.lang.String"/>
	<field name="DICHVUID" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="HUONGDANSUDUNG" class="java.lang.String"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="SODIENTHOAI" class="java.lang.String"/>
	<field name="SOPHIEU_BARCODE" class="java.lang.String"/>
	<field name="NGUOIKEDON" class="java.lang.String"/>
	<field name="DOITUONG_VIENPHI" class="java.lang.String"/>
	<field name="YHCT_TUNGAY" class="java.lang.String"/>
	<field name="YHCT_DENNGAY" class="java.lang.String"/>
	<field name="BHYT_1" class="java.lang.String"/>
	<field name="BHYT_2" class="java.lang.String"/>
	<field name="BHYT_3" class="java.lang.String"/>
	<field name="BHYT_5" class="java.lang.String"/>
	<field name="BHYT_6" class="java.lang.String"/>
	<field name="BHYT_4" class="java.lang.String"/>
	<field name="YKIENBACSY" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="ICD10" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="DUNGTUYEN" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="NGAYHEN" class="java.lang.String"/>
	<field name="PHIEUHEN" class="java.lang.Number"/>
	<field name="NGUOITAO" class="java.lang.String"/>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="TENDICHVU_NT" class="java.lang.String"/>
	<field name="TENDICHVU_LAN" class="java.lang.String"/>
	<field name="NHIEUHOATCHAT" class="java.lang.Integer"/>
	<field name="CHKHOATCHAT" class="java.lang.Integer"/>
	<field name="NGAYDUNG" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="89" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="44" y="13" width="336" height="14" uuid="339dd1f8-32a3-4a12-8cbc-00a1c0965e25"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Điện thoại: "+($P{org_tel}==null?"":$P{org_tel})]]></textFieldExpression>
			</textField>
			<image>
				<reportElement positionType="Float" x="1" y="0" width="43" height="40" uuid="ab1635b4-a0ee-48cd-98f6-e385ef63cf7b"/>
				<imageExpression><![CDATA[$P{DPAR_ORG_LOGO}]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="44" y="0" width="336" height="14" uuid="11d8a11a-5b0b-450b-9c8d-91e7ef346515"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="40" width="265" height="11" uuid="8654470d-0910-4b98-920e-846cb2bf306d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Phòng khám:  " + ($F{PHONGKHAM}==null?"":$F{PHONGKHAM})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="51" width="381" height="20" uuid="2750d24f-3ab8-4b26-b9ea-0b5e47a2b99a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐƠN THUỐC "N"]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="71" width="381" height="18" uuid="ebbb8986-a6ae-43ae-8a1e-519b0106d501"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[(Bản lưu tại cơ sở cấp, bán thuốc)]]></text>
			</staticText>
			<componentElement>
				<reportElement x="265" y="40" width="116" height="23" uuid="22ebc623-b63b-4bc6-ad4a-611c54be7b10"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none" displayChecksum="false" displayStartStop="false" extendedCharSetEnabled="false">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement positionType="Float" x="265" y="63" width="40" height="13" uuid="01ac37f3-77f2-4b93-ab06-1d62dca1d4f9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<text><![CDATA[Mã BA: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="305" y="63" width="76" height="13" uuid="a394a33d-d8fe-4ba4-9c57-b46416b78791"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="265" y="27" width="40" height="13" uuid="03fade6e-fef6-4f9d-945e-8d025a1611ed"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<text><![CDATA[Mã BN: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="305" y="27" width="76" height="13" uuid="ab79f4ca-5203-407d-91b7-d851b6a74e9b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="84" splitType="Stretch">
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="70" width="111" height="14" uuid="43565ab2-0c35-4597-90d6-63ef3b3a5f92"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thuốc điều trị:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="381" height="14" uuid="4631e551-b9e9-431c-9658-fd69e18975ea"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên:  " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="28" width="381" height="14" uuid="8ab07120-0c91-4e2a-af9f-8bbb031698f6"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: " + $F{DIACHI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="42" width="380" height="14" uuid="b3af09ee-eac3-4b54-9b86-9e1648b7ce84"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Số thẻ bảo hiểm y tế (nếu có): "+($F{BHYT_1}.equals(" ")?" ":($F{BHYT_1}+$F{BHYT_2}+$F{BHYT_3}+$F{BHYT_4}+$F{BHYT_5}+$F{BHYT_6}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="208" y="14" width="173" height="14" uuid="fbc5e02c-5588-4255-921f-0b0833b4eacf"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[" Nam/Nữ: " + $F{GIOITINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="14" width="208" height="14" uuid="0fcb8848-d593-4b95-836e-cb740bca5731"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + $F{TUOI} + " " + $F{DVTUOI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="56" width="381" height="14" uuid="7a31ea26-ab3e-4d19-8bd5-3676792666b9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<textFieldExpression><![CDATA[("Chẩn đoán: " + $F{CHANDOANVAOKHOA} + " (" + $F{ICD10} + ")"+"\n")
+
($F{CHANDOANVAOKHOA_KEMTHEO}==null?"":"Chẩn đoán phụ: "+$F{CHANDOANVAOKHOA_KEMTHEO}
+"\n")+
("Đợt "+"............."+" (Từ ngày ......./......./..........   đến hết ngày ......./......./..........)")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="317" y="0" width="65" height="15" uuid="57e62657-6c6c-431e-b709-4bfe4f0af586"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["Số ngày: "+$F{NGAYDUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="25" y="0" width="215" height="15" uuid="7ce0c83e-5f3a-4277-ba18-a81a67355635"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHKHOATCHAT}==1?$F{TENDICHVU_NT}:($F{CHKHOATCHAT}==2?$F{TENDICHVU}:($F{NHIEUHOATCHAT}==1?$F{TENDICHVU_LAN}:$F{TENDICHVU}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="25" height="15" uuid="297df01d-c283-4c76-bf7c-690b202d953a"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="240" y="0" width="77" height="15" uuid="2575aebb-2d7f-4b3a-ba8e-b7326151817d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["SL: "+($F{SOLUONG}.substring(0,1).toUpperCase()+ $F{SOLUONG}.substring(1,$F{SOLUONG}.length()) + " "+$F{TEN_DVT})]]></textFieldExpression>
			</textField>
		</band>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="24" y="0" width="357" height="15" uuid="0586f97f-de31-430f-8191-1434daa3521f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDANSUDUNG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="14">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="1" y="0" width="381" height="14" uuid="1b79b179-0099-4915-9212-ca4fb51584c8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{NGAYHEN} == null ? "" : "- Tái khám ngày: " + $F{NGAYHEN} +"\n")
+
($F{YKIENBACSY} == null ? "" : "- " + $F{YKIENBACSY})]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="12">
			<textField evaluationTime="Report">
				<reportElement positionType="Float" mode="Transparent" x="195" y="0" width="186" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="5201d7fd-c747-401b-950c-6c1ac1eda42c"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="195" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="0c2d13a8-**************-ceb5d043e64c"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}+"/"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="100">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="175" height="13" uuid="f2079cbc-49d6-47d7-939c-35c0dc3949dd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Người nhận thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="13" width="175" height="28" uuid="7b537ae4-aaa0-4263-8233-4cfeddc40948"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên và số chứng minh nhân dân/ căn cước công dân)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="179" y="26" width="202" height="13" uuid="04456ca6-3cfd-464d-b4ea-1abeb849ba22"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="179" y="0" width="202" height="13" uuid="56709ed2-af71-4283-bdb9-87b4ade8324c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + (new SimpleDateFormat("dd")).format(new Date())
+ " tháng " + (new SimpleDateFormat("MM")).format(new Date())
+ " năm " + (new SimpleDateFormat("yyyy")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="179" y="13" width="202" height="13" uuid="40090218-0f5d-4f1d-972e-68c70f29ebb5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Bác sỹ khám bệnh]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="0" y="76" width="380" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="976d761c-dbd8-44d4-9b74-749d7335acbf"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["NVYT vui lòng xác định chính xác người bệnh:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="230" y="88" width="20" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="5a9a238f-e62e-43dc-b752-ae3755bfca0d"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="130" y="88" width="120" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="59420450-da7f-4df1-808b-e98cb5108833"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày sinh/Tuổi"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="100" y="88" width="20" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1b2ce089-fa31-4f0c-b084-d8f61304026b"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="317" y="88" width="20" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="585b2c82-bab3-492c-ad32-b2044350fd62"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="265" y="88" width="105" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d645b7e8-2f89-42e5-ac86-7f599c671913"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="0" y="88" width="120" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1d5baf54-fd3e-486d-bcbd-b942be4f16d2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ và tên"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
