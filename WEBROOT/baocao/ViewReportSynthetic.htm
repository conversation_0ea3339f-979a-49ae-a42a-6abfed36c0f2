<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/RestService.js?v=181218"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js?v=181218"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.core.css"/>
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.default.css" id="toggleCSS"/>
<script type="text/javascript" src="../common/script/alertify/alertify.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js?v=1"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script type="text/javascript" src="../common/script/jszip/jszip.js"></script>
<script type="text/javascript" src="../common/script/jszip/FileSaver.js"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link href="../common/script/select2/dist/css/select2.min.css" rel="stylesheet"/>
<script src="../common/script/select2/dist/js/select2.min.js"></script>
<script type="text/javascript" src="../baocao/ViewHashMap.js"></script>
<script type="text/javascript" src="../baocao/ViewReportUtil.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css" type="text/css"/>
<link rel="stylesheet" type="text/css" href="../common/script/datetimepicker/MonthPicker.css" media="all"/>
<script type="text/javascript" src="../common/script/datetimepicker/MonthPicker.js"></script>

<script type="text/javascript" src="../baocao/ViewReportSynthetic.js?v=20250311_01"></script>

<style>
    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
        background-color: #b0b0b0 !important;
    }

    .select2-container--default .select2-selection--single {
        border-radius: 2px;
    }

    .table tr th, table tr td {
        background: white;
        padding: 2px;
    }

    .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
        background: white;
    }

    .ui-search-clear {
        background: white;
    }

    .tab-pane label {
        margin-top: 5px;
        min-width: 1px;
    }

    .dropdown-menu > li > a {
        text-align: left;
    }

    #divParam select, #divParam input {
        padding: 3px 5px;
        height: 27px !important;
        font-size: .9em;
        border-radius: 0;
    }

    div.ui-jqgrid {
        margin-top: 2px !important;
        border: 0px solid #a6c9e2;
        box-sizing: initial;
    }

    td {
        word-break: break-word;
    }

</style>
<div width="100%" id="divMain" class="container">
    <div class="col-md-12 low-padding">
        <ul class="nav nav-tabs">
            <li role="presentation" class="active" id="tabTongHop">
                <a href="#divTongHop" aria-controls="home" role="tab" data-toggle="pill">
                    <span class="glyphicon glyphicon-list"></span> Tổng hợp báo cáo</a>
                </a>
            </li>
            <li role="presentation" id="tabPhienTongHop">
                <a href="#divPhienTongHop" aria-controls="profile" role="tab" data-toggle="pill">
                    <span class="glyphicon glyphicon-list"></span> Phiên tổng hợp</a>
                </a>
            </li>
        </ul>

        <div id="divTongHop" class="tab active">
            <div class="col-md-12 low-padding mgt3">
                <select class="form-control input-sm" id="cboLoaiBaoCao" style="width: 100%;color:blue;font-weight: bold"></select>
                <div class="mgt3" id="divParam"></div>
                <div class="col-md-12 mgt3 mgb3" id="divTongHopDuLieu" style="text-align: center;">
                    <button type="button" class="btn btn-sm " id="btnTongHop" data-catype="1" style="color: white;background-color: #1164b4;border-radius: 2px">
                        <span class="glyphicon glyphicon-repeat"></span> Tổng hợp dữ liệu
                    </button>
                </div>
            </div>
            <div class="col-md-12 low-padding">
                <div class="col-md-12 low-padding">
                    <table id="grdDSTieuChi"></table>
                    <div id="pager_grdDSTieuChi"></div>
                </div>
            </div>
        </div>

        <div id="divPhienTongHop" class="tab">
            <table id="grdDSTieuChiTongHop"></table>
            <div id="pager_grdDSTieuChiTongHop"></div>
            <div class="col-md-12 low-padding mgt3 mgb3" style="text-align: center;float: none">
                <button type="button" class="btn btn-sm " id="btnTongHopLaiTieuChi" data-catype="2" style="color: white;background-color: #1164b4;height: 28.2px;">
                    <span class="glyphicon glyphicon-repeat"></span> Tổng hợp lại các tiêu chí
                </button>
                <div class="btn-group" role="group" aria-label="Button group with nested dropdown">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-default" id="btnInKQK" style="color: white;background-color: #1164b4;height: 30px;">
                            <span class="glyphicon glyphicon-save-file"></span> Xem báo cáo
                        </button>
                        <button type="button" class="btn btn-default dropdown-toggle" style="height: 28.5px;width: 30px;background-color: #1164b4;color: white;height: 30px;"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" disabledMode="">
                            <span class="caret"></span><span class="sr-only"></span>
                        </button>
                        <div class="dropdown-menu">
                            <li id='printPDF' data-catype="1"><a><span class="glyphicon glyphicon-print"></span> In PDF</a></li>
                            <li id='printWord' data-catype="2"><a><span class="glyphicon glyphicon-print"></span> In Word</a></li>
                            <li id='printExcel' data-catype="3"><a><span class="glyphicon glyphicon-print"></span> In Excel</a></li>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-sm " id="btnLogReport" data-catype="2" style="color: white;background-color: #1164b4;height: 28.2px;">
                    <span class="glyphicon glyphicon-repeat"></span> Log tổng hợp
                </button>
            </div>
            <table id="grdDSTieuChiTongHopChiTiet"></table>
            <div id="pager_grdDSTieuChiTongHopChiTiet"></div>
            <div class="mgt3" style="text-align: center;">
                <img src="../common/image/Circle_Green.png" width="15px"><span> Đã tổng hợp</span>
                <img src="../common/image/Circle_Red.png" width="15px"><span> Lỗi tổng hợp</span>
                <img src="../common/image/Circle_Yellow.png" width="15px"><span> Chờ tổng hợp</span>
            </div>
        </div>
    </div>
</div>
<script>
    var opt = [];
    var hospital_id = '{hospital_id}';
    var company_id = '{company_id}';
    var province_id = '{province_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var dept_id = '{dept_id}';
    var subdept_id = '{subdept_id}'
    var uuid = '{uuid}';
    var DATA_DB_NAME = "";
    var DB_SCHEMA_NAME = "{db_schema}";
    var usrData = [];
    var paramInfo = '{paramData}';
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';
    initRest(uuid, "/vnpthis");
    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid
    _opts._dept_id = dept_id;
    _opts._subdept_id = subdept_id
    _opts._user_id = user_id;
    _opts._hospital_id = hospital_id;
    _opts.DATA_DB_NAME = DATA_DB_NAME;
    _opts.DB_SCHEMA_NAME = DB_SCHEMA_NAME;
    usrData[0] = ['XPAR_province_id', province_id];
    usrData[1] = ['XPAR_company_id', company_id];
    usrData[2] = ['XPAR_user_type', user_type];
    var DS = new ViewReportSynthetic(_opts);
    DS.load(hospital_id);
</script>