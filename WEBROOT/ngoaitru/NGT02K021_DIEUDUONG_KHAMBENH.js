var ctl_ar=[
		{type:'button',id:'btnINLAISTT',icon:'print',text:'In lại STT'}
		,{type:'button',id:'btnSINHTON',icon:'thuoc',text:'<PERSON>h tồn'}
		,{type:'button',id:'btnDOICONGKHAM',icon:'goikham',text:'Đổi PK/CK'} 
		,{type:'button',id:'btnTRABN',icon:'dieutri',text:'Tr<PERSON> BN'} 
		,{type:'button',id:'btnCAPUUTIEN',icon:'dichvu',text:'Cấp STT ƯT mới'} 
		,{type:'button',id:'btnDSCHOKHAM',icon:'dsben<PERSON><PERSON>',text:'DS chờ khám'} 
	];

function CAPSO_DIEUDUONG(_opts) {
	var _company_id = _opts._hospital_id;
	var _action = "";
	var _user_id = _opts._uuid;
	var _khoaid = _opts.khoaid;
	var _data = [];
	var _arr;	
	var _sql_par = [];
    
	var _colHeader=" ,ICON,20,0,ns,l; ,ICONCLS,20,0,ns,l;" +
			"Mã bệnh nhân,MABENHNHAN,50,0,f,l;" +
			"BENHNHANID,BENHNHANID,60,0,t,l;" +
			"Mã bệnh án,MAHOSOBENHAN,50,0,f,l;" + 
			"KQCLS,HOSOBENHANID,60,0,t,l;" + 
			"Họ tên,TENBENHNHAN,100,0,f,l;" + 
			"STT,STTKHAM,30,0,f,l;" + 
			"Ngày sinh,NGAYSINH,40,0,f,l;" + 
			"KQCLS,GIOITINHID,30,0,t,l;" + 
			"Giới tính,GIOITINH,30,0,f,l;" + 
			"KQCLS,BHYTID,60,0,t,l;" + 
			"Mã BHYT,MA_BHYT,60,0,f,l;" + 
			"Ngày tiếp nhận,NGAYTIEPNHAN,60,0,f,l;" + 
			"KQCLS,PHONGID,60,0,t,l;" + 
			"Phòng khám,TENPHONG,100,0,f,l;" + 
			"KQCLS,TIEPNHANID,60,0,t,l;" + 
			"KQCLS,KHAMBENHID,60,0,t,l;" + 
			"KQCLS,PHONGKHAMDANGKYID,60,0,t,l;" + 
			"KQCLS,MAUBENHPHAMID,60,0,t,l;" + 
			"KQCLS,DICHVUKHAMBENHID,60,0,t,l;" + 
			"KQCLS,DICHVUID,60,0,t,l;" + 
			"KQCLS,TRANGTHAI_STT,60,0,t,l;" + 
			"KQCLS,TUOI,60,0,t,l;" + 
			"KQCLS,DONVI_TUOI,60,0,t,l;" + 
			"KQCLS,UUTIENKHAMID,60,0,t,l;" + 
			"KQCLS,KQCLS,60,0,t,l;" + 
			"KQCLS,TRANGTHAIKHAMBENH,60,0,t,l;" + 
			"KQCLS,DOITUONGBENHNHANID,60,0,t,l;" + 
			"KQCLS,SUB_DTBNID,60,0,t,l";
	
	var _grdDSBENHNHAN = "grdDSBENHNHAN";
	
	var that=this;
	this.load=doLoad;
	
	function doLoad() {
		//tuyennx_add_start_20190424 L1PT-664
		var hopital=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
		if(hopital!= null && hopital!=0) {
			_opts.hospital_id = hopital;
		} 
		//tuyennx_add_end_20190424 L1PT-664
		GridUtil.init(_grdDSBENHNHAN,"100%","330px","Cập nhật STT Khám",false, _colHeader,true);
		$("#txtTUNGAY").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		$("#txtDENNGAY").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));		
		ComboUtil.getComboTag("cboTRANGTHAIID", "NGT02K009.RV005", [], "", {value:0, text:'Tất cả'}, "sql", "","");
		var _par_kho = _khoaid;
		ComboUtil.getComboTag("cboPHONGKHAMID", "LOADPK_DIEUDUONG" ,_par_kho, "", {value:0, text:'Tất cả'},"sp","", false);
    	$('#txtMABENHNHAN').focus();
		bindEvent();
		doLoadGrid();
	}
	
	function doLoadGrid(){
        if($("#txtTUNGAY").val() == "" || $("#txtTUNGAY").val() == ""){
        	DlgUtil.showMsg("Yêu cầu nhập từ ngày / đến ngày ");
        	return; 
        }
        
		var _sql_par=[];
		_sql_par.push({"name":"[0]","value":$("#txtTUNGAY").val() + " 00:00:00"});
		_sql_par.push({"name":"[1]","value":$("#txtDENNGAY").val() + " 23:59:59"});
		_sql_par.push({"name":"[2]","value": $("#cboTRANGTHAIID").val()});
		_sql_par.push({"name":"[3]","value": $("#cboPHONGKHAMID").val()});
		_sql_par.push({"name":"[4]","value": $("#cboSTTKHAMID").val()});
		_sql_par.push({"name":"[5]","value": _khoaid});
		_sql_par.push({"name":"[6]","value": $("#txtMABENHNHAN").val()});
		GridUtil.loadGridBySqlPage(_grdDSBENHNHAN,"NGT02K021.LAYDL",_sql_par);
	}
	
	function setTrangThai(mode){
		var mode1 = mode == "" ? "1" : mode; 
		//$("#btnCAPSTT").prop('disabled',$("#hidSTTKHAM").val() != "" ? true : false );
		$("#btnSINHTON").prop("disabled", mode1 == "9" ? true : false);
		$("#btnINLAISTT").prop("disabled", mode1 == "9" ? true : false);
		$("#btnDOICONGKHAM").prop("disabled", mode1 == "9" ? true : false);
		$("#btnTRABN").prop("disabled", mode1 == "4" ? false : true);
		$("#dbtnCAPUUTIEN").prop("disabled", mode1 == "9" ? true : false); 
	}
	
	function fillData(ret){
		$("#hidTIEPNHANID").val(ret != null ? ret.TIEPNHANID : "");
		$("#hidKHAMBENHID").val(ret != null ? ret.KHAMBENHID : "");
		$("#hidHOSOBENHANID").val(ret != null ? ret.HOSOBENHANID : "");
		$("#hidTRANGTHAI_STT").val(ret != null ? ret.TRANGTHAI_STT : "");
		$("#hidTUOI").val(ret != null ? ret.TUOI : "");
		$("#hidDONVI_TUOI").val(ret != null ? ret.DONVI_TUOI : "");
		$('#hidDOITUONGBENHNHANID').val(ret != null ? ret.DOITUONGBENHNHANID : "");
		$('#hidPHONGKHAMDANGKYID').val(ret != null ? ret.PHONGKHAMDANGKYID : "");
		$('#hidSTTKHAM').val(ret != null ? ret.STTKHAM : "");
		$('#hidUUTIENKHAMID').val(ret != null ? ret.UUTIENKHAMID : "");
		$('#hidPHONGID').val(ret != null ? ret.PHONGID : "");
		$('#hidNGAYTIEPNHAN').val(ret != null ? ret.NGAYTIEPNHAN : "");
		$("#hidBENHNHANID").val(ret != null ? ret.BENHNHANID : "");
	}
	
	function bindEvent(){
		//ToolbarUtil.build('toolbarId',ctl_ar);
		
		$("#cboPHONGKHAMID").on('change', function (e) {
			doLoadGrid();
		});
		$("#cboTRANGTHAIID").on('change', function (e) {
			doLoadGrid();
		});
		$("#cboSTTKHAMID").on('change', function (e) {
			doLoadGrid();
		});
		$("#txtTUNGAY").on('change', function (e) {
			doLoadGrid();
		});
		$("#txtDENNGAY").on('change', function (e) {
			doLoadGrid();
		});
		
		$("#txtMABENHNHAN").focusout(function(){
			doLoadGrid();
		}); 
		
		$("#txtMABENHNHAN").focusout(function(){
			doLoadGrid();
		}); 
		
		$('#txtMABENHNHAN').keydown(function (e) {
			if (e.which === 13 || e.which === 9) {	
				doLoadGrid();
			}
		});
		
		GridUtil.setGridParam(_grdDSBENHNHAN, {
			onSelectRow : function(id) {
				GridUtil.unmarkAll(_grdDSBENHNHAN);
	    		GridUtil.markRow(_grdDSBENHNHAN,id);
				var ret = $("#"+_grdDSBENHNHAN).jqGrid('getRowData',id);
				fillData(ret); 
				setTrangThai(ret.TRANGTHAI_STT); 
			}
			,gridComplete: function(id){
				var ids = $("#" + _grdDSBENHNHAN).getDataIDs(); 
		        for(var i=0;i<ids.length;i++){
		        	var id = ids[i];
		        	var row = $("#" + _grdDSBENHNHAN).jqGrid('getRowData',id);
		        	var _icon = '';
		        	var _iconcls = '';
		        	
		        	if(row.TRANGTHAIKHAMBENH == 1){
		        		_icon = '<center><img src="'+ _opts.imgPath[0] +'" width="15px"></center>';
		        	}else if(row.TRANGTHAIKHAMBENH == 4){
		        		_icon = '<center><img src="'+ _opts.imgPath[1] +'" width="15px"></center>';
		        	}else if(row.TRANGTHAIKHAMBENH == 9){
		        		_icon = '<center><img src="'+ _opts.imgPath[2] +'" width="15px"></center>';
		        	}		       
		        	
		        	if(row.KQCLS == "1"){
		        		_iconcls = '<center><img src="'+ _opts.imgPath[3] +'" width="15px"></center>';
		        	}else if(row.KQCLS == "2"){
		        		_iconcls = '<center><img src="'+ _opts.imgPath[4] +'" width="15px"></center>';
		        	}
					$("#" + _grdDSBENHNHAN).jqGrid ('setCell', id, 1, _icon);
					$("#" + _grdDSBENHNHAN).jqGrid ('setCell', id, 2, _iconcls);
		        } 
			}
		});
		
		$("#btnGOIKHAM").on("click", function(){
			if ($("#hidTIEPNHANID").val() == ""){
				DlgUtil.showMsg("Chưa chọn bệnh nhân để gọi khám");
				return; 
			}
			var myVar={
				KIEU : "2", 
				ID : $("#hidTIEPNHANID").val(), 
				PHONGGOI : _opts.khoaid
			};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K021.GOIVAOKHAM", JSON.stringify(myVar));
			if (Number(ret) > 0){
				var text = "Mời bệnh nhân nguyễn ngọc linh số thứ tự " + ret + " vào đóng tiền viện phí " ; 
		        var msg = new SpeechSynthesisUtterance();
		        msg.rate = 0.75; 
		        msg.pitch = -1; 
		        var voices = window.speechSynthesis.getVoices();
		         $('#voices').find('option:contains("GoogleTranslate Vietnamese")').attr("selected",true);
//		        $('#voices').find('option:contains("OpenFPT Vietnamese")').attr("selected",true);
		        msg.voice = voices[$('#voices').val()];
		        //msg.rate = $('#rate').val() / 10;
		        //msg.pitch = $('#pitch').val();
		        msg.text = text;
		        msg.onend = function(e) {
		          console.log('Finished in ' + event.elapsedTime + ' seconds.');
		        };
		        speechSynthesis.speak(msg);
			} else{
				DlgUtil.showMsg("Lỗi gọi khám bệnh nhân. ");
			}
			
		});
		
		$("#btnMANVPI55").on("click", function(){
			var param = "";
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_VPI_LCDBM55&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		
		}); 
		$("#btnMANXN55").on("click", function(){
			var param = "";
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_VPI_LCDBM55&type=6&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		
		}); 
		$("#btnMANTHUOC55").on("click", function(){
			var param = "";
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_VPI_LCDBM55&type=5&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		
		}); 
		$("#btnMANCDHA55").on("click", function(){
			var param = "";
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_CDHA_LCDBM2&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		
		}); 
		
		$("#btnINLAISTT").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return; 
			}
			if($('#hidSTTKHAM').val() == ""){
				DlgUtil.showMsg("Bệnh nhân chưa được cấp STT, không cho phép thao tác tiếp. ");
				return; 
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}, {
				name : 'phongid',
				type : 'String',
				value : $('#hidPHONGID').val()
			}, {
				name : 'i_sch',
				type : 'String',
				value : _opts.db_schema
			} ];

			var par1= [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			} ];
			
			CommonUtil.inPhieu('window', 'NGT_STT', 'pdf', par);	
			if($('#hidDOITUONGBENHNHANID').val() == "1"){
				openReport('window', "GIAYXACNHAN_GIUTHEBHYT_A4_1014", "pdf", par1);		
			}		
			
		});
		
		$("#btnSINHTON").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return; 
			}
			var param = {
				tuoi : $('#hidTUOI').val(),	
				dvtuoi : $('#hidDONVI_TUOI').val(),		
				khambenhid : $('#hidKHAMBENHID').val(),
				trangthaikhambenh : $('#hidTRANGTHAI_STT').val()
			};			
			
			EventUtil.setEvent("assignSevice_savetv",function(e){
				if(e.msg != ""){
					DlgUtil.showMsg(e.msg); 
				}
//				objSinhTon = e.msg;
			});
			
			dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT01T001_sinhton",param,'THÔNG TIN DẤU HIỆU SINH TỒN',510,250, close);
			DlgUtil.open("dlgCV");
		});
		$("#btnDOTHILUC").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return;
			}
			var param = {
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val()
			};

			EventUtil.setEvent("assignSevice_savetv",function(e){
				if(e.msg != ""){
					DlgUtil.showMsg(e.msg);
				}
			});

			dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT06K002_DonKinh",param,'ĐO THỊ LỰC',1300,650, close);
			DlgUtil.open("dlgCV");
		});
		$("#btnDOICONGKHAM").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return; 
			}
			var myVar={
					khambenhid : $('#hidKHAMBENHID').val(), 
					tiepnhanid : $("#hidTIEPNHANID").val(), 
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
				};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCongKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K054_CHUYENYCKHAM",myVar,"Cập nhật công khám/phòng khám", 900,300);
			DlgUtil.open("dlgCongKham");
		});
		
		EventUtil.setEvent("ngt02k054_chuyenyckham_close",function(e){
			doLoadGrid();
			DlgUtil.close("dlgCongKham");
		});
		
		function _ketthuckham(vkieu){
			var myVar={
				kieu : vkieu,
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val()
			};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.KTKHAM", JSON.stringify(myVar));
			var rets = ret.split(',');
			if(rets[0] == '1'){
				DlgUtil.showMsg('Cập nhật thông tin thành công');
				doLoadGrid();
			}else if(ret == 'kocoxutri'){
				DlgUtil.showMsg('Bệnh nhân hiện chưa có xử trí.');
			}else if(ret == 'coxutri'){
				DlgUtil.showMsg('Bệnh nhân có xử trí không trả bệnh nhân.');
			}else if(ret == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ CLS hoặc đã kê đơn thuốc');
			} else if(ret == 'connotien'){
				DlgUtil.showMsg('Bệnh nhân còn nợ tiền, phải thanh toán mới kết thúc khám.');
			} else if(ret == 'cophieudangsua'){
				DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
			}else if(ret == 'chuacochandoan'){
				DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán.');
			}else if(ret == 'kococannang'){
				DlgUtil.showMsg('Bệnh nhân chưa có thông tin cân nặng');
			}else if(ret == 'thoigianxtri'){ 
				DlgUtil.showMsg('Bệnh nhân chưa có thời gian ra viện hoạc thời gian ra viện nhỏ hơn ngày hiện tại, cập nhật lại ngày ra viện trước.');
			} else if(ret == 'coloibhyt'){ 
				dlgPopup=DlgUtil.buildPopupUrl("divErrInfo","divDlg","manager.jsp?func=../ngoaitru/NGT02K067_ErrInfo" ,[],"Danh sách chi tiết lỗi",1100,500);
				DlgUtil.open("divErrInfo");
			}else{
				DlgUtil.showMsg('Cập nhật thông tin không thành công');
			} 
		}
		
		$("#btnTRABN").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return; 
			}
			var vkieu = 1; 					// tra bn khong kham; 
			var myVar = {khambenhid : $("#hidKHAMBENHID").val()};
			var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KTKHAM", JSON.stringify(myVar));	
			if(check == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ trạng thái đang chờ tiếp nhận, có thể hủy phiếu để kết thúc khám');
				return false;
			}
			//tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
			else if(check == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện không thể kết thúc khám');
				return false;
			}
			//tuyennx_add_end_20170727
			else if(check == 'pasdvcls'){
				DlgUtil.showConfirm("Bệnh nhân có dịch vụ đang thực hiện, bạn có muốn kết thúc khám không.",function(flag) {
	           		if (flag) {
	           			_ketthuckham(vkieu);
	           		} 
           		});       
			}else if(check == '1'){
				_ketthuckham(vkieu);
			}
		});
		
		$("#btnCAPUUTIEN").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return; 
			}
			var myVar={
				khambenhid : $('#hidKHAMBENHID').val(),
				kieu : 1
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgsinhstt","divDlg","manager.jsp?func=../ngoaitru/NGT02K044_sinhsothutumoi",myVar,"Sinh số thứ tự ưu tiên mới",400,200);
			DlgUtil.open("dlgsinhstt");
		});
		
		$("#btnDSCHOKHAM"	).on("click", function(){
			var param = "";
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_KB_LCDBM2&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		});
		$("#btnTIMKIEM").on("click", function(){
			doLoadGrid(); 
		});
		$("#btnCAPSTT").on("click", function(){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
				return; 
			}
			
			var myVar={ 
				PHONGKHAMDANGKYID : $("#hidPHONGKHAMDANGKYID").val(), 
				UUTIENKHAMID : $("#hidUUTIENKHAMID").val(), 
				PHONGID : $("#hidPHONGID").val(), 
				HOSOBENHANID : $("#hidHOSOBENHANID").val(), 
				NGAYTIEPNHAN : $("#hidNGAYTIEPNHAN").val(), 
				KIEU : "1"
			};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K021.CAPSO", JSON.stringify(myVar));
			if(ret == "1"){
				DlgUtil.showMsg("Cập nhật STT mới cho bệnh nhân thành công");
				doLoadGrid(); 
				fillData(null); 		// reset thong tin; 
			}else{
				DlgUtil.showMsg("Lỗi cập nhật STT khám mới cho bệnh nhân này. ");
			}
		});
	}

}