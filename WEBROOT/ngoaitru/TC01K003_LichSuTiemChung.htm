<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>

<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">

<script src="../common/script/jqgrid/js/context-menu.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>

<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/TC01K003_LichSuTiemChung.js?v=240332"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>

<div class="" id="">
    <div class="col-xs-12 low-padding">
        <table id="grdDanhSachLichSu"></table>
        <div id="pager_grdDanhSachLichSu"></div>
    </div>
</div>
    <script type="text/javascript">
        var uuid = '{uuid}';
        var khoaid = '{dept_id}';
        initRest(uuid, "/vnpthis");
        initAjax("/vnpthis");
        ajaxSvc.register("TIEMCHUNGWS");

        var mode = '{showMode}';
        var data;
        //tuonglt them hien thi phong mac dinh la phong dang nhap 8/5/2017
        var phongid = '{subdept_id}';
        var subdept_name = '{subdept_name}';
        var _opts = new Object();
        if (mode == 'dlg') {
            parent.DlgUtil.tunnel(DlgUtil.moveEvent);
            data = EventUtil.getVar("dlgVar");
            _opts.tenbenhnhan = data.tenbenhnhan;
            _opts.madoituong_tiemchung = data.madoituong_tiemchung;
            _opts.token = data.token;
        }

        var bn = new TC01K003_LichSuTiemChung(_opts);
        bn.load();
    </script>
