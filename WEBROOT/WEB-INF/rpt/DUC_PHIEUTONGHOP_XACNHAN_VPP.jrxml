<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU008_SOTONGHOPTHUOCHANGNGAY_14DBV01_TT23_A2_1111" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="816" leftMargin="8" rightMargin="18" topMargin="8" bottomMargin="8" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="9215922f-64ed-4844-82b3-8fb959f97ad7">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="nhapxuatid" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_PHIEUTH_XN_VPP($P{[UID]},$P{[HID]},$P{[SCH]},null,$P{nhapxuatid},$P{ora_cursor})}]]>
	</queryString>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="TENKHOA" class="java.lang.String"/>
	<variable name="variable1" class="java.lang.Integer" incrementType="Group" incrementGroup="TENTHUOC" calculation="Count">
		<variableExpression><![CDATA[Boolean.TRUE]]></variableExpression>
	</variable>
	<group name="TENTHUOC">
		<groupExpression><![CDATA[$F{TENTHUOC}]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="69">
			<staticText>
				<reportElement positionType="Float" x="0" y="38" width="816" height="24" uuid="1f3b1fde-d42a-40da-b54f-3ec9c70db6cd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[BẢNG DỰ TRÙ VĂN PHÒNG PHẨM]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="18" width="274" height="18" uuid="fba64185-384a-47fe-9b94-8a2ad398ba8b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="274" height="18" uuid="07018de3-465f-40ef-bbda-c648b9cab146"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageFooter>
		<band height="15" splitType="Stretch">
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="700" y="0" width="95" height="15" isPrintInFirstWholeBand="true" forecolor="#000000" backcolor="#FFFFFF" uuid="97c5332a-2881-4697-a51a-d366c6b9089d"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}+"/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement positionType="Float" mode="Transparent" x="795" y="0" width="21" height="15" isPrintInFirstWholeBand="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a02bc573-e793-453a-81c8-6e7b751d4fa0"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="337">
			<staticText>
				<reportElement positionType="Float" x="546" y="231" width="271" height="28" isPrintInFirstWholeBand="true" uuid="b8b5cc90-f156-4719-9a82-cc4f8ce9acac"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI LẬP PHIẾU]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="546" y="259" width="271" height="18" isPrintInFirstWholeBand="true" uuid="92f7b0ce-1426-4042-aea1-3f350e7c666d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(ký, họ tên)]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="546" y="211" width="270" height="20" uuid="e56d2d5c-3200-4024-ad61-8509f67326da"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + (new SimpleDateFormat("dd")).format(new Date())
+ " tháng " + (new SimpleDateFormat("MM")).format(new Date())
+ " năm " + (new SimpleDateFormat("yyyy")).format(new Date())]]></textFieldExpression>
			</textField>
			<crosstab>
				<reportElement positionType="Float" x="1" y="0" width="815" height="200" uuid="168602d6-f6a3-4b3a-8d84-298b5cf5f8eb"/>
				<crosstabHeaderCell>
					<cellContents>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="38" height="119" uuid="75495263-c447-4a14-8d41-22eb8063c7f2"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[TT]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="38" y="0" width="139" height="119" uuid="217ec6ab-80a9-4eec-ad62-5ef2ae0549de"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Tên khoa,phòng]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="119" width="38" height="27" uuid="aef968e4-db71-471d-bf1b-2cddd92d0546"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[STT]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="STT" width="38">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TENKHOA}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="38" height="26" uuid="05ba5160-60dc-4866-8ce0-04b7a579ab52"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{ROW_COUNT}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="TENKHOA" width="139" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TENKHOA}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="139" height="26" uuid="5a240796-cc78-428b-ba9e-202dd9ad9fa1"/>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TENKHOA}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque"/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="TENTHUOC" height="146">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TENTHUOC}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque">
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="119" uuid="0caaf6ce-6f93-4b52-8e1c-4a687eb1f36c"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TENTHUOC}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="119" width="50" height="27" uuid="e7cc9dbe-17ec-40b2-a80c-9dd4a883ec09"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{COLUMN_COUNT}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#FFFFFF" mode="Opaque"/>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="SOLUONGMeasure" class="java.math.BigDecimal">
					<measureExpression><![CDATA[$F{SOLUONG}]]></measureExpression>
				</measure>
				<crosstabCell width="50" height="26">
					<cellContents>
						<textField isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="26" uuid="acc19312-2605-4ad5-9c5f-0fd36bba647a"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{SOLUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="0" rowTotalGroup="TENKHOA">
					<cellContents backcolor="#FFFFFF" mode="Opaque"/>
				</crosstabCell>
				<crosstabCell width="50" height="26" columnTotalGroup="TENTHUOC">
					<cellContents backcolor="#FFFFFF" mode="Opaque"/>
				</crosstabCell>
				<crosstabCell height="0" rowTotalGroup="TENKHOA" columnTotalGroup="TENTHUOC">
					<cellContents backcolor="#FFFFFF" mode="Opaque"/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="STT">
					<cellContents/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="STT" columnTotalGroup="TENTHUOC">
					<cellContents/>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>
