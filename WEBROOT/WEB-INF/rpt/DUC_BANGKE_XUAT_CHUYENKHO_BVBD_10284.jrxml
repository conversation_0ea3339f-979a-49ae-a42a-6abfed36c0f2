<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU007_PHIEUTRALAITHUOCHOACHATVTYTTIEUHAO_05DBV01_TT23_A4_DHTNN" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="e9432a68-162d-495b-9257-7d9122b9c3cf">
	<property name="ireport.zoom" value="2.853116706110056"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="VPARS_loaitvt_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_coso_TEXT" class="java.lang.String"/>
	<parameter name="khoid" class="java.lang.String"/>
	<parameter name="VPARS_khonhanid_VALUE" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_BANGKE_XUATCHUYENKHO_BVBD($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{tungay},
$P{denngay},
$P{VPARS_loaitvt_VALUE},
$P{VPARS_coso_TEXT},
$P{khoid},
$P{VPARS_khonhanid_VALUE},
$P{ora_cursor})}]]>
	</queryString>
	<field name="LOAI_VAT_TU" class="java.lang.String"/>
	<field name="KHOBC" class="java.lang.String"/>
	<field name="DS_KHOXUAT" class="java.lang.String"/>
	<field name="MA" class="java.lang.String"/>
	<field name="MAKHO" class="java.lang.String"/>
	<field name="NGAY_DUYET" class="java.lang.String"/>
	<field name="DS_KHONHAN" class="java.lang.String"/>
	<field name="NOIXUAT" class="java.lang.String"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="TIEN_BANG_CHU" class="java.lang.String"/>
	<field name="NOINHAN" class="java.lang.String"/>
	<variable name="TONG_TIEN" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="163" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="80" width="555" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9e3235a6-4af6-4735-a5a2-77e3a67f203b"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + "   đến ngày "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="120" width="555" height="20" uuid="f762b8af-0d2b-4186-bc77-04cb747aefa3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ kho: " + ($F{DS_KHOXUAT} == null ? "" : $F{DS_KHOXUAT})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="40" width="555" height="40" uuid="2415022d-9f1b-4940-ba72-51cdab2e18ee"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAI_VAT_TU} == null ? "BẢNG KÊ XUẤT CHUYỂN KHO" : "BẢNG KÊ XUẤT " + $F{LOAI_VAT_TU} + " CHUYỂN KHO"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="100" width="555" height="20" uuid="4bcc13bf-bd89-451c-94c1-635485ee6504"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Cơ sở: " + ($P{VPARS_coso_TEXT}.indexOf(",") > 0 ? "Tất cả" : $P{VPARS_coso_TEXT})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="140" width="555" height="23" uuid="8b0d8329-946b-4fc3-a8fc-407f488867a8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đến kho: " + ($F{DS_KHONHAN} == null ? "" : $F{DS_KHONHAN})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="231" height="20" uuid="70d29f59-1306-41fe-8fa8-492cff959514"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="-1" y="20" width="232" height="20" uuid="ea3e0df8-285c-4485-8ca8-f98e8feb450d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="45" splitType="Stretch">
			<staticText>
				<reportElement x="30" y="0" width="111" height="45" uuid="7173e713-0985-4451-b855-8d9fe2842840"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số CT]]></text>
			</staticText>
			<staticText>
				<reportElement x="141" y="0" width="90" height="45" uuid="77f23329-c58f-4cab-b002-266c934808c2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Ngày]]></text>
			</staticText>
			<staticText>
				<reportElement x="455" y="0" width="100" height="45" uuid="dde0fd47-**************-4e1da254a767"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="231" y="0" width="80" height="45" uuid="80c3b374-144a-497a-9426-0d510dc4dbff"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Mã KH]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="30" height="45" uuid="82feb1f4-c680-454f-aaef-9cc495fdc760"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="311" y="0" width="144" height="45" uuid="2d21012a-e585-4c53-a620-b9543c577f17"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Nơi nhận]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="141" y="0" width="90" height="20" uuid="36d360ad-6bbb-41e5-9768-17f4c498c3a4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_DUYET}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="231" y="0" width="80" height="20" uuid="5542a6f3-df4c-4b10-b40d-1429669e4c3a"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAKHO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="30" y="0" width="111" height="20" uuid="2adca175-22aa-4c41-97af-4ed306e9e83e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isPdfEmbedded="true"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="455" y="0" width="100" height="20" uuid="11699aff-3011-4f50-b45d-2025fad117da"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(new DecimalFormat("#,##0.00").format($F{THANH_TIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" uuid="2c007739-7161-4234-a3de-aa4e14006980"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="311" y="0" width="144" height="20" uuid="a8cad2e2-30d9-454f-92aa-479ab3932b59"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOINHAN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="196" y="3" width="177" height="14" uuid="f257f8c5-4da5-4ae7-b754-f5fe94ec57c2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang : " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="412" y="3" width="143" height="14" uuid="bc563b68-9827-463a-8cd9-ecedbf1f863d"/>
				<box>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss")).format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="146" splitType="Stretch">
			<staticText>
				<reportElement x="311" y="76" width="244" height="26" uuid="59465689-fd1c-4c76-9b3d-6f7e58cdd3ef"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG KHOA]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="455" height="20" uuid="7f024149-7bd0-4b1d-8e48-f12472049a37"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement x="455" y="0" width="100" height="20" uuid="f74f1977-bdac-427a-a730-1c111255642b"/>
				<box rightPadding="5">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new DecimalFormat("#,##0.00").format($V{TONG_TIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="76" width="141" height="26" uuid="f7f21638-5f50-454b-9d66-13d55b33c00b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[LẬP BIỂU]]></text>
			</staticText>
			<staticText>
				<reportElement x="141" y="76" width="170" height="26" uuid="30db223e-d8d8-4d10-b596-25b6b3466910"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[PHỤ TRÁCH KẾ TOÁN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="20" width="555" height="36" uuid="db9232e9-2b7e-4ae6-8c8e-d637155f90da"/>
				<box topPadding="5"/>
				<textElement>
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEN_BANG_CHU} == null ? "Tiền bằng chữ:" : "Tiền bằng chữ: " + $F{TIEN_BANG_CHU} + " đồng"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="311" y="56" width="244" height="20" uuid="0e65f089-4304-4646-a8bc-626369d404da"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Hà Nội, ngày "+ new SimpleDateFormat("dd").format(new java.util.Date())+" tháng "+ new SimpleDateFormat("MM").format(new java.util.Date())+" năm "+ new SimpleDateFormat("yyyy").format(new java.util.Date())]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
