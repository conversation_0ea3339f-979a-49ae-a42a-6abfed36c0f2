<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT03K010_ImportBenhNhan.js?v=20210513955211112111"></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>

<script src="../common/script/js-xlsx/xlsx.js"></script>
<script src="../common/script/js-xlsx/jszip.js"></script>

<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/xlsx.full.min.js"></script>
<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/ods.js"></script>
<style>
    .ui-jqgrid-htable,
    .ui-jqgrid-btable,
    .ui-jqgrid-pager,
    .ui-jqgrid-view,
    .ui-jqgrid-bdiv,
    .ui-jqgrid-hdiv,
    .ui-jqgrid-hbox,
    .ui-jqgrid {
        max-width: 100% !important;
        width: 100% !important;
    }
</style>
<div class="container" id="divMain">
    <div class="col-xs-12 low-padding mgt5" id="dvIMPORTBENHNHAN">
        <div class="col-xs-12 low-padding mgt5" id="dvNGT_EXCEL">
            <div class="panel panel-primary">
                <div class="panel-body">
                    <div class="col-xs-12 low-padding mgt5" style="text-align: center">
                        <div class="col-xs-2 low-padding required">
                            <label class="mgl5">Excel file</label>
                        </div>
                        <div class="col-xs-5 low-padding">
                            <input type="file" accept=".xls,.xlsx,.ods" id="my_file_input" fileread
                                   opts="vm.gridOptions" multiple="false"/>
                        </div>
                        <div class="col-xs-5 low-padding mgb5">
                            <button type="submit" id="btnGetFile" class="btn btn-default btn-primary">
                                <span class="glyphicon glyphicon-floppy-disk"></span> Tải file mẫu
                            </button>
                        </div>
                    </div>
                    <div class="col-xs-12 ">
                        <table id='my_file_output'></table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xs-12 low-padding text-center mgt5" style="" id="hdkhambenh">
            <div class="col-xs-1 low-padding required">
                <label>Loại khám</label>
            </div>
            <div class="col-xs-3 low-padding">
                <select class="form-control input-sm" id="cboLOAIKHAM" style="width: 100%;">
                    <option value="0" selected="">Chọn loại khám</option>
                    <option value="1">Gửi thực hiện CLS</option>
                    <option value="2">Chăm sóc sức khỏe cán bộ</option>
                </select>
            </div>
            <div class="col-xs-1 low-padding required">
                <label> Hợp Đồng </label>
            </div>
            <div class="col-xs-4 low-padding">
                <select class="form-control input-sm" id="cboHOPDONGID" style="width:100%;">
                </select>
            </div>
        </div>
        <div class="col-xs-12 low-padding" id="divDSIMPORT">
            <table id="grdDSBN_IMPORT" class="table table-striped jambo_table bulk_action"></table>
            <div id="pager_grdDSBN_IMPORT"></div>
        </div>
        <div id="dLoading" class="hidden"
             style="width: 90% !important; height: 100% !important;min-height:100% !important;  position:absolute;top:0; left:0; text-align: center; vertical-align: middle;">
            <div style=" top: 50%; left:50%; position:absolute;"><i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
            </div>
        </div>
        <div class="col-xs-12 low-padding" style="text-align: center;">
            <button type="submit" id="btnSave" class="btn btn-default btn-primary">
                <span class="glyphicon glyphicon-floppy-disk"></span> Thêm Bệnh Nhân
            </button>
            <button type="submit" id="btnDelBN" class="btn btn-default btn-primary">
                <span class="glyphicon glyphicon-floppy-disk"></span> Xóa Grid
            </button>
            <button type="submit" id="btnClose" class="btn btn-default btn-primary">
                <span class="glyphicon glyphicon-remove-circle"></span> Đóng
            </button>
        </div>
    </div>
    <!-- use for download file feature -->
    <iframe id="my_iframe" style="display:none;"></iframe>
</div>


<script>

    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var phongid = '{subdept_id}';
    var khoaid = '{dept_id}';
    var uuid = '{uuid}';
    var url = '{url}';
    var session_par = [];
    initRest(uuid);
    var _opts = {
        lang: lang,
        _param: session_par,
        _uuid: uuid,
        _hospital_id: hospital_id,

    }
    var mode = '{showMode}';
    var objVar;
    if (mode == 'dlg') {
        parent.DlgUtil.tunnel(DlgUtil.moveEvent);
        objVar = EventUtil.getVar("dlgVar");
        _opts.type = objVar.type;

    }
    _opts._phongid = phongid;
    _opts._khoaid = khoaid;

    var dsbn = new importDSBenhnhan(_opts);
    dsbn.load();
</script>