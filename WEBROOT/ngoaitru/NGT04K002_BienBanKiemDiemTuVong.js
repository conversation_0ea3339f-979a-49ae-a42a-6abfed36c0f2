// hainm - 03/05/2021

function NGT04K002_BienBanKiemDiemTuVong(opt) {
	this.load=doLoad;
	var _opt = opt;
	var that=this;
	var sql_par = [];
	var _rpt_code_kyso =  "NTU02_PHIEUKIEMDIEMTUVONG_41BV01_965";
	
	function doLoad() {
		_initControl();
		_bindEvent();		
	}
	
	function _initControl(){
		$('#txtNGAY_LAPKD').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		$('#hidKHAMBENHID').val(_opt.khambenhid);
		
		sql_par = [];
		sql_par.push({"name" : "[0]","value" : _opt.khambenhid});
		data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT04K002.TTKDTV02", sql_par);
		
		//get thoi gian vao vien
		var data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT04K002.VV001", sql_par);
		var row = JSON.parse(data1)[0];
		if(row!=null) {
			$("#txtNGAY_VAOVIEN").val(row.NGAY_VAOVIEN);
		}
		//get thoi gian tu vong 
		var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT04K002.VV002", sql_par);
		var row1 = JSON.parse(data2)[0];
		if(row1!=null && row1!='') {
			$("#txtNGAY_TUVONG").val(row1.NGAY_TUVONG);
		}
		var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";

		ComboUtil.initComboGrid("txtBACSITRUCID","TTRV.LOAD_USER",sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtBACSITRUCID").val("");
			var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
			$("#cboBACSITRUCID").empty();
			$("#cboBACSITRUCID").append(option);
			return false;
	  	});
		ComboUtil.initComboGrid("txtNVTIEPNHANID","TTRV.LOAD_USER",sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtNVTIEPNHANID").val("");
			var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
			$("#cboNVTIEPNHANID").empty();
			$("#cboNVTIEPNHANID").append(option);
			return false;
	  	});
		var rows=$.parseJSON(data);
		if (rows != null && rows.length > 0) {
			FormUtil.setObjectToForm("kiemdiemtuvong","",rows[0]);
			rows[0].BACSITRUCID != null ? setValueToCbo('txtBACSITRUCID','cboBACSITRUCID',rows[0].BACSITRUCID,rows[0].TEN_BSTRUC):1==1;
			rows[0].NVTIEPNHANID != null ? setValueToCbo('txtNVTIEPNHANID','cboNVTIEPNHANID',rows[0].NVTIEPNHANID,rows[0].TEN_NVTIEPNHAN):1==1;
		}else{
			FormUtil.clearForm('kiemdiemtuvong');
		}
		
		$('#txtNGUOI_NHA').focus();
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		this.validator = new DataValidator("kiemdiemtuvong");
		var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (kyso_kydientu != "1") {
			$('#' + 'btnKySo').remove();
			$('#' + 'btnHuyKy').remove();
			$('#' + 'btnInKySo').remove();
		}
	}
	
	function _bindEvent() {	
		$("#btnLuu").on("click",function(e){
			var valid= validator.validateForm();			
			if(!valid) return false;
			
			if(!_validate()) return false;
			
			var objData = new Object();
			FormUtil.setFormToObject("kiemdiemtuvong","",objData);
			objData['KHAMBENHID'] =  _opt.khambenhid;
			
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT04K002.BBKDTV",JSON.stringify(objData));
			if($.isNumeric(ret) && ret != -1 && ret != 0){
				DlgUtil.showMsg('Cập nhật thông tin kiểm điểm tử vong thành công');
			}else{
				DlgUtil.showMsg('Cập nhật thông tin kiểm điểm tử vong không thành công');
			}
		});
		
		$("#btnHuy").on("click",function(e){
			parent.DlgUtil.close("dlgBienBanKiemDiemTuVong");
		});
		
		$("#btnPrint").on("click",function(e){
			_printPhieu(_opt.khambenhid, "NTU02_PHIEUKIEMDIEMTUVONG_41BV01_965");
			//_printPhieu(_opt.khambenhid, "NGT004_TRICHBBKIEMTHAOTUVONG_41BV01_QD406_A4");
		});
		
		//BEGIN 2255 CA ladinhtuan ********
		$("#btnKySo").on("click", function(e) {
			_kySo();
		});
		$("#btnHuyKy").on("click", function(e) {
			_huyKySo();
		});
		$("#btnInKySo").on("click", function(e) {
			_inPhieuKySo();
		});
		//END 2255 CA ladinhtuan ********

		$.jMaskGlobals = {
		   maskElements: 'input,td,span,div',
		   dataMaskAttr: '*[data-mask]',
		   dataMask: true,
		   watchInterval: 300,
		   watchInputs: true,
		   watchDataMask: true,
		   byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
		   translation: {
		       '0': {pattern: /\d/},
		   '9': {pattern: /\d/, optional: true},
		   '#': {pattern: /\d/, recursive: true},
		   'A': {pattern: /[a-zA-Z0-9]/},
		   'S': {pattern: /[a-zA-Z]/}
		   }
		};
	}
	
	function _validate(){		
		var current = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHH24MI');	
		kdtv = $('#txtNGAY_LAPKD').val().substr(6,4) + $('#txtNGAY_LAPKD').val().substr(3,2) + $('#txtNGAY_LAPKD').val().substr(0,2)
		+ $('#txtNGAY_LAPKD').val().substr(11,2) + $('#txtNGAY_LAPKD').val().toString().substr(14,2);
		if(current < kdtv){
			setErrValidate('txtNGAY_LAPKD');
			DlgUtil.showMsg('Ngày kiểm điểm tử vong không thể lơn hơn ngày hiện tại');
			return false;
		}
		
		var ngaytn = _opt.ngaytn;	
		if(kdtv < ngaytn){
			setErrValidate('txtNGAY_LAPKD');
			DlgUtil.showMsg('Ngày kiểm điểm tử vong không thể nhỏ hơn ngày khám bệnh');
			return false;
		}
		
		return true;
	}
	
	function _printPhieu(khambenhid, code){
		var par = [ {
			name : 'khambenhid', 
			type : 'String',
			value : khambenhid
		}];
		
		openReport('window', code, 'pdf', par);
	}

	function setValueToCbo(element1,element2, id, name) {
		$("#"+element1+"").val("");
		  var option = $('<option value="'+id+'">'+name+'</option>');
		  $("#"+element2+"").empty();
		  $("#"+element2+"").append(option);
	}
	
	//BEGIN 2255 CA ladinhtuan ********
	function _kySo() {_caRpt('1');}

	function _huyKySo() {_caRpt('2');}

	function _inPhieuKySo() {
		var par_rpt_KySo = [ {
			name : 'hosobenhanid',
			type : 'String',
			value : _opt.hosobenhanid
		},
			{
				name : 'khambenhid',
				type : 'String',
				value : _opt.khambenhid
			}
		];
		par_rpt_KySo.push({
			name : 'rpt_code',
			type : 'String',
			value : _rpt_code_kyso
		});
		CommonUtil.openReportGetCA2(par_rpt_KySo, false);
	}

	function _caRpt(signType) {
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : _opt.hosobenhanid
		}, {
			name : 'khambenhid',
			type : 'String',
			value : _opt.khambenhid
		} ];
		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : _rpt_code_kyso
		});
		CommonUtil.kyCA(par_rpt_KySo, signType, true, true);
		EventUtil.setEvent("eventKyCA",function(e){
			var _code = e.res.split("|")[0];
			var _msg = e.res.split("|")[1];
			if(_code == '0') {
				$("#btnKySo").prop('disabled', signType=="1");
				$("#btnHuyKy").prop('disabled', signType=="2");
			}
			DlgUtil.showMsg(_msg);
		});
	}
	//END 2255 CA ladinhtuan ********
}

