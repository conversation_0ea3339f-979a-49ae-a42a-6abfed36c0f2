
function BAN01TN01_TRUYENNHIEM(_opts) {
	this.load = doLoad;
	var benhnhanid = _opts.benhnhanid;
	var loaibenhanid = _opts.loaibenhanid;
	var khambenhid = _opts.khambenhid;
	var hosobenhanid = _opts.hosobenhanid;
	var loaihsba_code = _opts.maloaibenhan;
	var checkInsert = 1;
	var arrMaHoiKham = [];
	var arrMaHoiKhamTable = [];
	var i_hid = hospital_id;	
	var checkInput = $(':input');
	
	// mang duyet list cac loai ho so phim anh
	var arrSoPhimAnh = [ "txtSOXQUANG", "txtSOSCANNER", "txtSOSIEUAM",
			"txtSOXETNGHIEM", "txtSOKHAC", "txtSOTOANBOHOSO" ];

	//ham thuc hien load trang ungdung
	function doLoad() {
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";		
		// khoi tao trang ho so benh an
		_initControl();
		// xu ly su kien click tren nut them moi
		_bindEvent();
		// export bieu mau
		if (hosobenhanid != null && hosobenhanid != '0') {
			exportBan(hosobenhanid, loaibenhanid, benhnhanid,_opts._param[4], 'BAN005_TRUYENNHIEM_QD4069_A4');
			exportKyCa(hosobenhanid, loaibenhanid, benhnhanid, 'BAN005_TRUYENNHIEM_QD4069_A4');
		}
		_calBMI();
		// close trang ung dung
		closePage();
		if ($('#txtKETQUAXNCLS').val().trim() != "") {
			$('#btnKQCLS').text("x");
		}else {
			$('#btnKQCLS').text("Chọn KQCLS");
		}
	}
	
	function _initControl() {
		// xu ly su kien load trang khi moi vao
		doLoadCombo("txtMABENHCHINH","txtBENHCHINH");
		doLoadComboKemTheo("txtMABENHKEMTHEO","txtBENHKEMTHEO");
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BAN_LOADPHANBIET_KEMTHEO') == 1){
			doLoadComboKemTheo("txtMAPHANBIET","txtPHANBIET");
		}else{
		doLoadCombo("txtMAPHANBIET","txtPHANBIET");
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BAN_LOADPDT_DAUTIEN') == 1) {
			var par=[];
			par.push({"name" : "[0]", "value" : hosobenhanid});
			var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.GETPDT.DAUTIEN", par);
			if (data_ar != null && data_ar.length > 0) {
				var datakq = JSON.parse(data_ar);
				$('#txtMACH').val(datakq[2].GIATRI_KETQUA);
				$('#txtNHIETDO').val(datakq[3].GIATRI_KETQUA);
				$('#txtHUYETAP1').val(datakq[5].GIATRI_KETQUA);
				$('#txtHUYETAP2').val(datakq[4].GIATRI_KETQUA);
				$('#txtNHIPTHO').val(datakq[6].GIATRI_KETQUA);
				$('#txtCANNANG').val(datakq[7].GIATRI_KETQUA);
				$('#txtCHIEUCAO').val(datakq[44].GIATRI_KETQUA);
				$('#txtTTBENHAN').val(datakq[14].GIATRI_KETQUA);
				$('#txtTOANTHAN').val(datakq[0].GIATRI_KETQUA);
				$('#txtBANTHAN').val(datakq[62].GIATRI_KETQUA);
				$('#txtGIADINH').val(datakq[69].GIATRI_KETQUA);
				$('#txtMABENHCHINH').val(datakq[10].GIATRI_KETQUA);
				$('#txtBENHCHINH').val(datakq[9].GIATRI_KETQUA);
				$('#txtBENHKEMTHEO').val(datakq[15].GIATRI_KETQUA);
			}
		}
		// Check xem da co du lieu ho so benh an khong
		loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid);
		if(hosobenhanid != undefined){
			checkInsert = 0;
		}
		// lấy danh sách mã hỏi khám bệnh của khoa
		arrMaHoiKham = getMaHoiKham(loaibenhanid);
		
		// Lấy danh sách mã hỏi khám bệnh dang table của khoa
		arrMaHoiKhamTable = getMaHoiKhamMaTran(loaibenhanid);
		
		// reset gia tri tong so ho so phim anh khi thay doi cac so
		// lieu(x-quang, scanner, xieu am,...)
		changePhimAnh(arrSoPhimAnh);
		//xu ly khi kich checkbox dac diem lien quan
		changeDataCheck();
		$("#txtLYDOVAOVIEN").focus();
		
		var f2 = 113;
		$(document).unbind('keydown').keydown(function (e) {                             
		 if(e.keyCode == f2){
		  getIcd(e.target);
		 }
		});
		
		EventUtil.setEvent("assignSevice_resultTK", function(e) {
		   if(e.mode == '0'){
		    $('#' + e.ctrId).combogrid("setValue",e.text);
		   } else if(e.mode == '1'){
		    $('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
		   }
		   DlgUtil.close(e.popupId);
		  });
				
		//L2PT-42752
		ComboUtil.getComboTag("cboMAUBAID", "HSBA.MAU1", [{"name" : "[0]", "value" : loaibenhanid}], [ {"name" : "[0]","value" : "1"} ], {value : '', text : 'Chọn'}, "", ""); 
 
	}

	//ham xu ly luu va cap nhat thong tin
	function _bindEvent() {
		$("#txtCANNANG").on("blur",function(e){
			_calBMI();
		});
		$("#txtCHIEUCAO").on("blur",function(e){
			_calBMI();
		});
		$("#btnThemMoi").on(
				"click",
				function(e) {
					_save(true);
				});
		$("#btnLuu").on(
				"click",
				function(e) {
					_save(false);
				});
		
		$("#btnLuuBAMau").on(
				"click",
				function(e) {
					_saveBAMAU(false);
				});
		//Xoa mau benh an
		$("#btnXoaBAMau").on(
				"click",
				function(e) {
					_delBAMAU(false);
				});
		$("#btnKQCLS").on("click", function(e) {
			if ($('#txtKETQUAXNCLS').val().trim() != "") {
				$('#btnKQCLS').text("Chọn KQCLS");
				$('#txtKETQUAXNCLS').val("");
				return;
			}
			var myVar = {
				khambenhid : khambenhid,
				benhnhanid : benhnhanid,
				hosobenhanid :hosobenhanid,
				mode : '1'
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKQCLS", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K031_ChonKQCLS", myVar, "CHỌN KẾT QUẢ CẬN LÂM SÀNG", 1050, 410);
			DlgUtil.open("dlgKQCLS");
		});
		EventUtil.setEvent("assignSevice_luu", function(e) {
			DlgUtil.close("dlgKQCLS");
				$('#txtKETQUAXNCLS').val(e.msg);
				$('#btnKQCLS').text("x");
		});
	}
	
	function _saveBAMAU(checkClose){
		var tenmau=$("#txtTENBENHANMAU").val();
		var validate = new DataValidator("divMain-BA");
		if(validate.validateForm() && _validateFormBAMAU()){			
			// buoc 1:insert thong tin ho so benh an				
			var mauid = insHSBenhAnTemp(_opts.khoaid, _opts.phongid, tenmau, loaibenhanid);			
			// buoc 2: insert thong tin cho ho so phim anh
			var r1=insHSPhimAnhTemp(arrSoPhimAnh, mauid, checkInsert);

			// buoc 3 insert thong tin hoi kham benh va tong ket benh an
			var r2 = insHoiKhamTemp(checkInput, loaihsba_code, arrMaHoiKham, mauid, checkInsert);	
			// insert hoi kham ma tran
			var r3=insHoiKhamMaTranTemp(checkInput, loaihsba_code, arrMaHoiKhamTable, mauid, checkInsert);
				
			if(mauid==-1){
				DlgUtil.showMsg("Tên mẫu bệnh án đã tồn tại trên hệ thống");
				return false;
			}
			//xu ly su kien callback	
			if(r1==1 && r2==1 && r3==1){
				//xu ly su kien callback
				//var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");			
				//if(typeof evFunc==='function') {	
					//if(checkClose){
					//	evFunc({msg:"Cập nhật hồ sơ bệnh án mẫu thành công"});
					//}
					//else{
						DlgUtil.showMsg("Cập nhật hồ sơ bệnh án mẫu thành công");
					//}
				
		//L2PT-42752
		ComboUtil.getComboTag("cboMAUBAID", "HSBA.MAU1", [{"name" : "[0]", "value" : loaibenhanid}], [ {"name" : "[0]","value" : "1"} ], {value : '', text : 'Chọn'}, "", ""); 
 
				//}
				//else {
				//	console.log('evFunc not a function');
				//} 
			}else{
				callBack(r1, checkInsert);				
			}			
		}
	}
	//xoa mau benh an
	function _delBAMAU(checkClose){
		// Xoa mau benh an		
		 var mauid=$("#cboMAUBAID").val();
		
			var r1=deleteBenhAnTemp(mauid);
			
			//xu ly su kien callback	
			if(r1==1){
				//xu ly su kien callback
			//	var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");			
				//if(typeof evFunc==='function') {	
				//	if(checkClose){
					//	evFunc({msg:"Cập nhật hồ sơ bệnh án mẫu thành công"});
					//}
					//else{
						DlgUtil.showMsg("Xóa hồ sơ bệnh án mẫu thành công");
					//}
				
		//L2PT-42752
		ComboUtil.getComboTag("cboMAUBAID", "HSBA.MAU1", [{"name" : "[0]", "value" : loaibenhanid}], [ {"name" : "[0]","value" : "1"} ], {value : '', text : 'Chọn'}, "", ""); 
 
				//}
				//else {
				//	console.log('evFunc not a function');
				//} 
			}else{
				callBack(r1, "Xóa bệnh án mẫu không thành công");
			}		
	}
	$("#cboMAUBAID").change(function() {
		 var mauid=$("#cboMAUBAID").val();		 
		 if(mauid!=''){	
			 var _row = new Object(); 
			 for (i=1;i<=14;i++){
			 	for(j=1;j<=6;j++){
			 		_row["TIENSUSANKHOA" + i + j] = ""; 
			 	}
			 }
			 FormUtil.setObjectToForm("HOIKHAMMATRAN","",_row);
			 loadDataTemp(benhnhanid, loaibenhanid, i_hid, loaihsba_code, mauid, khambenhid,hosobenhanid);	
		 }else{
			 loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid);
		 }
		 
		  });
	function _validateFormBAMAU() {	
		var check = true;
		var SELECTOR_ERRORS = '';
		var tenmau1=$("#txtTENBENHANMAU").val();
		var tenmau2 =$("#cboMAUBAID").text();
		if (tenmau1==tenmau2)
		{
			SELECTOR_ERRORS += 'Tên bệnh án mẫu đã có' + '<br>'; 
			onfocus('#txtTENBENHANMAU');
			check =  false;
		}
		if($('#txtTENBENHANMAU').val()==''){
			SELECTOR_ERRORS += 'Tên bệnh án mẫu không được để trống' + '<br>'; 
			onfocus('#txtTENBENHANMAU');
			check =  false;
		}
		
		if(!check){
			DlgUtil.showMsg(SELECTOR_ERRORS);
		}
		return check;
	}
	
	function _save(checkClose){
		// validate thong tin khi thuc hien cap nhat
		// them moi
		
		var validate = new DataValidator("divMain-BA");
		if(validate.validateForm() && _validateForm()){
			if (checkInsert == 1) {
				// buoc 1:insert thong tin ho so benh an
				hosobenhanid = insHSBenhAn(benhnhanid, loaibenhanid, khambenhid, i_hid, checkInsert);
			}
			
			// buoc 2: insert thong tin cho ho so phim anh
			var r1=insHSPhimAnh(arrSoPhimAnh, hosobenhanid, checkInsert);

			// buoc 3 insert thong tin hoi kham benh va tong ket benh an
			var r2=insHoiKham(checkInput, loaihsba_code, arrMaHoiKham, hosobenhanid, checkInsert);
			
			// insert thong tin hoi kham benh va tong ket benh an dang ma tran
			var r3=insHoiKhamMaTran(checkInput, loaihsba_code, arrMaHoiKhamTable, hosobenhanid, checkInsert);
			//dong bo du lieu
			var _par = [hosobenhanid, khambenhid];	
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.RIGHT",_par.join('$'));
			
			if($("#txtMABENHCHINH").val()!=''){
				var _par_td = [$("#txtMABENHCHINH").val(), _opts.khoaid];
				var _r_td=jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.ICD.TD",_par_td.join('$'));	
			}				
			
			if(_return==1 && r1==1 && r2==1 && r3==1){
				//xu ly su kien callback
				var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");			
				if(typeof evFunc==='function') {	
					if(checkClose){
						evFunc({msg:"Cập nhật hồ sơ bệnh án thành công"});
					}
					else{
						DlgUtil.showMsg("Cập nhật hồ sơ bệnh án thành công");
					}
				}
				else {
					console.log('evFunc not a function');
				} 
			}else{
				callBack(_return, checkInsert);
			}
									
		}
	}
	function _calBMI(){
		if($("#txtCHIEUCAO").val() != '' && $("#txtCANNANG").val() != ''){
			if(isNaN(parseFloat($("#txtCANNANG").val())) || isNaN(parseFloat($("#txtCHIEUCAO").val())))
				return;
			var _cannang = parseFloat($("#txtCANNANG").val());
			var _chieucao = parseFloat($("#txtCHIEUCAO").val());

			var bmi = chisobmi(_cannang, _chieucao);

			$("#txtBMI").text(bmi);
		}
	}
	function _validateForm() {
		var SELECTOR_ERRORS = '';
		var check = true;
		if($('#txtCANNANG').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtCANNANG');
			check =  false;
		}
		if($('#txtCANNANG').val().split('.')[1] != null && $('#txtCANNANG').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtCANNANG');
			check =  false;
		}
		if($('#txtNHIETDO').val()<0){
			SELECTOR_ERRORS += 'Nhiệt độ phải lớn hơn 0' + '<br>';
			onfocus('#txtNHIETDO');
			check =  false;
		}
		if($('#txtNHIETDO').val().split('.')[1] != null && $('#txtNHIETDO').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Nhiệt độ chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtNHIETDO');
			check =  false;
		}
		if ($("#chkDACDIEMLQB21").is(":checked")
				&& ($("#txtDACDIEMLQB31").val() == null || $("#txtDACDIEMLQB31")
						.val() == "")) {
			SELECTOR_ERRORS += 'Chưa nhập giá trị thời gian dị ứng' + '<br>';
			onfocus('#txtDACDIEMLQB31');
			check = false;
		}
		if ($("#chkDACDIEMLQB22").is(":checked")
				&& ($("#txtDACDIEMLQB32").val() == null || $("#txtDACDIEMLQB32")
						.val() == "")) {
			SELECTOR_ERRORS += 'Chưa nhập giá trị thời gian ma túy' + '<br>';
			onfocus('#txtDACDIEMLQB32');
			check = false;
		}
		if ($("#chkDACDIEMLQB23").is(":checked")
				&& ($("#txtDACDIEMLQB33").val() == null || $("#txtDACDIEMLQB33")
						.val() == "")) {
			SELECTOR_ERRORS += 'Chưa nhập giá trị thời gian rượu bia' + '<br>';
			onfocus('#txtDACDIEMLQB33');
			check = false;
		}
		if ($("#chkDACDIEMLQB51").is(":checked")
				&& ($("#txtDACDIEMLQB61").val() == null || $("#txtDACDIEMLQB61")
						.val() == "")) {
			SELECTOR_ERRORS += 'Chưa nhập giá trị thời gian thuốc lá' + '<br>';
			onfocus('#txtDACDIEMLQB61');
			check = false;
		}
		if ($("#chkDACDIEMLQB52").is(":checked")
				&& ($("#txtDACDIEMLQB62").val() == null || $("#txtDACDIEMLQB62")
						.val() == "")) {
			SELECTOR_ERRORS += 'Chưa nhập giá trị thời gian thuốc lào' + '<br>';
			onfocus('#txtDACDIEMLQB62');
			check = false;
		}
		if ($("#chkDACDIEMLQB53").is(":checked")
				&& ($("#txtDACDIEMLQB63").val() == null || $("#txtDACDIEMLQB63")
						.val() == "")) {
			SELECTOR_ERRORS += 'Chưa nhập giá trị thời gian khác' + '<br>';
			onfocus('#txtDACDIEMLQB63');
			check = false;
		}
		if($('#txtMABENHCHINH').val() != null && $('#txtMABENHCHINH').val() != '' 
			&& !checkIcdCode($('#txtMABENHCHINH').val().trim())){
			SELECTOR_ERRORS += 'Mã bệnh chính ko đúng' + '<br>';
			onfocus('#txtMABENHCHINH');
			check =  false;
		}
		if($('#txtBENHCHINH').val() != null && $('#txtBENHCHINH').val() != '' 
			&& !checkIcdName($('#txtBENHCHINH').val().trim())){
			SELECTOR_ERRORS += 'Tên bệnh chính ko đúng' + '<br>';
			onfocus('#txtBENHCHINH');
			check =  false;
		}
//		if($('#txtMABENHKEMTHEO').val() != null && $('#txtMABENHKEMTHEO').val() != '' 
//			&& !checkIcdCode($('#txtMABENHKEMTHEO').val().trim())){
//			SELECTOR_ERRORS += 'Mã bệnh kèm theo ko đúng' + '<br>';
//			onfocus('#txtMABENHKEMTHEO');
//			check =  false;
//		}
//		if($('#txtBENHKEMTHEO').val() != null && $('#txtBENHKEMTHEO').val() != '' 
//			&& !checkIcdName($('#txtBENHKEMTHEO').val().trim())){
//			SELECTOR_ERRORS += 'Tên bệnh kèm theo ko đúng' + '<br>';
//			onfocus('#txtBENHKEMTHEO');
//			check =  false;
//		}
		if($('#txtMAPHANBIET').val() != null && $('#txtMAPHANBIET').val() != '' 
			&& !checkIcdCode($('#txtMAPHANBIET').val().trim())){
			SELECTOR_ERRORS += 'Mã phân biệt ko đúng' + '<br>';
			onfocus('#txtMAPHANBIET');
			check =  false;
		}
		if($('#txtPHANBIET').val() != null && $('#txtPHANBIET').val() != '' 
			&& !checkIcdName($('#txtPHANBIET').val().trim())){
			SELECTOR_ERRORS += 'Tên phân biệt ko đúng' + '<br>';
			onfocus('#txtPHANBIET');
			check =  false;
		}
		
		if(!check){
			DlgUtil.showMsg(SELECTOR_ERRORS);
		}
		return check;
	}
}