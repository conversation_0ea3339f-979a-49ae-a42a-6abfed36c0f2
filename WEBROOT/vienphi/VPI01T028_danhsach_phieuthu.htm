<!--  
<PERSON><PERSON> màn hình  : NTU01H021
File mã nguồn : NTU01H021_PhieuTamUngBenhNhan.htm
M<PERSON><PERSON> đích  : <PERSON>h sach phieu tam ung
Tham số vào :
<PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON>t  <PERSON> chú
HungNt	- 03122016 - Comment
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js" ></script>
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.combogrid/jquery.ui.combogrid-1.6.3.js"></script>
<link rel="stylesheet" href="../common/script/jquery.combogrid/jquery.ui.combogrid.css">
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css"> 
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.storageapi.js" ></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>            
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>

<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../vienphi/VPI01T028_danhsach_phieuthu.js?v=230426_2"></script>
<script type="text/javascript" src="../vienphi/inv.js?v=230306_1"></script>
<script type="text/javascript" src="../common/script/xml2json.js?v=2"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=221208_6"></script>
<style type="text/css">
.money-sm {
	font-weight: bolder;
	font-size: 12px;
	color: red;
}
.money-sm:focus {
	font-weight: bolder;
	font-size: 12px;
	color: red;
	dir: rtl;
}
</style>
<div id="divDlg" style="width: 100%; display: none">
	<iframe src="" id="ifmView"
		style="width: 1200px; height: 800px; border: dotted 1px red"
		frameborder="0"></iframe>
</div>
<div width="100%" id="divPHIEUTHU" class="container" style=" border: 0px !important;"> 
	<div class="mgt-5">	  
		<table id="grdPhieuThu"></table>	
		<div id="pager_grdPhieuThu"></div>
	</div>
	<!-- L2PT-30618 start -->
	<div class="col-xs-12 low-padding" id="divLOAIDIEUCHINH">
		<div class="col-xs-1 low-padding">
			<label class="control-label">Loại điều chỉnh</label>
		</div>
		<div class="col-xs-2 low-padding">
			<select class="form-control input-sm" id="cboLoaiDieuChinh"
				style="float: right; width: 100%;">
				<option value="">Chọn</option>
				<option value="2">Điều chỉnh tăng</option>
				<option value="3">Điều chỉnh giảm</option>
				<option value="4">Điều chỉnh thông tin</option>
			</select>
		</div>
		<div class="col-xs-1 low-padding">
			<label class="control-label">Chọn HĐ</label>
		</div>
		<div class="col-xs-2 low-padding">
			<select class="form-control input-sm" id="cboChonHD"
				style="float: right; width: 100%;">
				<option value="1">Hóa đơn cũ</option>
				<option value="2">Hóa đơn mới</option>
			</select>
		</div>
	</div>
	<!-- L2PT-30618 end -->
	<div class="col-xs-12 low-padding">
		<div class="col-xs-5 low-padding border-group-1 mgt10" id ='divHoaDonDT'>
			
		</div>
		<div class="col-xs-7 low-padding">
			<table id="grdProducts"></table>	
			<div id="pager_grdProducts"></div>
		</div>
	</div>
	<div class="row form-inline">
		<div class="col-xs-12 low-padding mgt10 mgb5" style="text-align: center;" id="gridButton">
			<button class="btn btn-sm btn-primary" id="btnChon" disabled>
				<span class="glyphicon glyphicon-pencil"></span><span id="spaDCTT">Chọn</span></button>		
			<button class="btn btn-sm btn-primary" id="btnDong">
				<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
		</div>
	</div>
</div>
<script>
	var uuid = '{uuid}';	
	initRest(uuid,"/vnpthis");
	var _opts = new Object();	
	_opts._uuid = uuid;	
	_opts.user_id = '{user_id}';
	var khoaid = '{dept_id}';
	var mode = '{showMode}';
	var hospital_id='{hospital_id}';	
	if(mode=='dlg') {		
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		_opts.data = EventUtil.getVar("dlgVar");
	}
    initRest(_opts._uuid);	
    // BVTM-23 ttlinh start
    var path = "../common/image/";
	_opts.imgPath = [ path + "Circle_Red.png", path + "Circle_Yellow.png", path + "Circle_Green.png", path + "Flag_Red.png", path + "True.png", path + "DeleteRed.png", path + "process.png" ];
	// BVTM-23 ttlinh end
	var ptubn = new VPI01T028_danhsach_phieuthu(_opts);
	ptubn.load();
</script>