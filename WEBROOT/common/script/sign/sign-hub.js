/**
 * <AUTHOR>
 *
 *
 * Create date: 2025/03/20
 * VNPT HIS - EHealth
 *
 */

class SignHub extends Utility {
    static OH_SIGNHUB_RETURN = "oh_signhub_return";
    static OH_SIGNHUB_READY = "oh_signhub_ready";
    static OH_SIGNHUB_CONFIG = "oh_signhub_config";
    static OH_SIGNHUB_RELOAD = "oh_signhub_reload";
    static OH_SIGNHUB_HTML_TEMPLATE =
        `<div class="modal fade"
            id="staticBackdrop"
            data-bs-backdrop="static"
            data-bs-keyboard="false"
            tabindex="-1"
            aria-labelledby="staticBackdropLabel"
            style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-fullscreen" style="">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
              <div class="modal-content">
                <div class="modal-body">
                  <iframe id="sign_hub_iframe" src="" frameborder="0" style="width: 100%; height: 100%;"></iframe>
                </div>
              </div>
            </div>
          </div>`;

    static ID = "SignHandlerDialog_Box";

    static onCloseModal = function () {
        document.getElementById("sign_hub_iframe").src = '';
    }
    jsonData;

    constructor(rptCode, urlSh, _params) {
        super();
        this._loadScript(SignHub + "_NQ", '/vnpthis/common/utility/NotificationQueue.js', () => {
            this._notification = new NotificationQueue({
                zIndex: 9999
            });
            if (this.constructor === SignHub) {
                this._notification.error({
                    title: "Lỗi hệ thống",
                    message: "Đã xảy ra lỗi hệ thống, vui lòng liên hệ quản trị.",
                })
                throw new Error("Class is of abstract type and can't be instantiated");
            }
            let objectPhieus = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rptCode);
            if (this.constructor.name === "SignXml") {
                if ((!objectPhieus || objectPhieus.length < 1)) {
                    this._notification.error({
                        title: "Không thể lấy thông tin dữ liệu ký",
                        message: "Thông tin dữ liệu ký không tồn tại hoặc có lỗi khi lấy thông tin/",
                    })
                    throw new Error("Cannot find config for rptCode: " + rptCode);
                } else {
                    this._objectPhieu = objectPhieus[0];
                }
            } else {
                this._objectPhieu = {};
            }
            this._rptCode = rptCode;
            this._urlSh = urlSh;
            this._params = _params;
            let hsbaObj = _params.find(x => x.name.toUpperCase() === "HOSOBENHANID");
            if (!hsbaObj || hsbaObj.length < 1) {
                this._notification.error({
                    title: "Thông tin dữ liệu không đầy đủ",
                    message: "Thông tin bệnh án không có",
                })
                throw new Error("HOSOBENHANID is required in params!");
            }
            this._hosobenhanid = hsbaObj.value;
            this._initEventMessage();
            if (this._objectPhieu["CA_TYPE"] == 3 || this._objectPhieu["CA_TYPE"] == 6) {
                this._setupSmartCaUser();
            }
            this._setupOneHealthToken();
            this._setUserInfo();
            this.open();
        });
    }

    get rptCode() {
        return this._rptCode;
    }

    set rptCode(value) {
        this._rptCode = value;
    }

    _initEventMessage() {
        window.addEventListener("message", (event) => {
            if (event.data.code === SignHub.OH_SIGNHUB_READY) {
                this._postMessage(SignHub.OH_SIGNHUB_CONFIG, this._jsonData);
            } else if (event.data.code === SignHub.OH_SIGNHUB_RETURN) {
                console.log("Kết quả trả về:", event.data.data);
                alert("Kết quả trả về")
                // new JsonViewer({ value: event.data.data }).render("#result");
            }
        });

        if (!document.getElementById(SignHub.ID)) {
            let sdElement = document.createElement('div');
            sdElement.id = SignHub.ID;
            document.body.appendChild(sdElement);
        }
        this._loadCss(SignHub.ID, '/vnpthis/common/script/sign/sign-style.css');
        this._container = document.getElementById(SignHub.ID);
        this._container.innerHTML = SignHub.OH_SIGNHUB_HTML_TEMPLATE;
        this._box = this._container.getElementsByTagName("iframe")[0];
        // this._loadCss(SelectionDialog.ID, '/vnpthis/common/utility/SelectionDialog.css');
        // this._loadScript(SelectionDialog.ID, '/vnpthis/common/utility/NotificationQueue.js', () => {
        //     this._notification = new NotificationQueue({
        //         zIndex: 9999
        //     });
        // });
    }

    _postMessage(code, data) {
        const message = {
            code: code,
            data: data,
        };
        this._box.contentWindow.postMessage(message, "*");
    }

    open() {
        this._box.src = this._urlSh;
        $("#staticBackdrop").modal("show");
    }

    _buildDocuments(){
        let documents = [
            {
                "docid": this._rptCode,
                "doc_type": this._typeDoc,
                "doc_data": this._docData,
                "template_code": this._objectPhieu["TEMPLATECODE"],
                "minio_bucket": "",
                "his_transid": ""
            }
        ];

        let config = {
            "signer_name": this._userInfo['FULL_NAME'],
            "sign_identify": this._userInfo['USER_ID'],
            "sign_csyt": this._userInfo['HOSPITAL_CODE'],
            "token_smartca": this._smartCAAccessToken,
            "refreshtoken_smartca": this._smartCARefreshToken,
            "token_onehealth": this._accessTokenOneHealth,
            "refreshtoken_onehealth": this._refreshTokenOneHealth,
            "form_editable": 1,
            "emr_send": 1,
            "single": 0,
            "show_list": 1,
            "show_step": 1,
            "show_xml": 1,
            "show_download": 1,
            "show_patient": 1,
            "show_sign_image": 1,
            "sign_methods": [
                {
                    "label": "Ký người dân",
                    "value": "KYNGUOIDAN",
                    "default": 1
                },
                {
                    "label": "Ký VNPT-SmartCA",
                    "value": "SMARTCA",
                    "default": 0
                },
                {
                    "label": "Ký VNPT-SmartCA theo thông tư 769",
                    "value": "SMARTCA769",
                    "default": 0
                },
                {
                    "label": "Ký eSeal – VNPT-SmartCA nâng cao",
                    "value": "ESEAL",
                    "default": 0
                },
                {
                    "label": "Ký eSeal – VNPT-SmartCA nâng cao theo thông tư 769",
                    "value": "ESEAL769",
                    "default": 0
                },
                {
                    "label": "Ký Điện tử",
                    "value": "DIGITALSIGN",
                    "default": 0
                },
                {
                    "label": "Ký bảng ký",
                    "value": "SIGNPAD",
                    "default": 0
                },
                {
                    "label": "Thiết bị di động",
                    "value": "MOBILE",
                    "default": 0
                },
                {
                    "label": "Vân tay",
                    "value": "FINGER",
                    "default": 0
                },
                {
                    "label": "USB Token",
                    "value": "USBTOKEN",
                    "default": 0
                }
            ]
        }

        this._jsonData = { documents, config }
    }

    _setupSmartCaUser() {
        let hisl2_smartca = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
        this._smartCAAccessToken = hisl2_smartca.token["access_token"];
        this._smartCARefreshToken = hisl2_smartca.token["refresh_token"];
    }

    _setupOneHealthToken() {
        // let thirdPartyCaller = new ThirdPartyCaller("OH_GETTOKEN");
        // let rs = thirdPartyCaller.post([], {});
        // if (rs.code === 0) {
        //     let result = rs.result;
        //     if (result.code === "0" || result.code === 0) {
        //         this._accessTokenOneHealth = result.data["accessToken"];
        //         this._refreshTokenOneHealth = result.data["refreshToken"];
        //     } else {
        //         this._notification.error({
        //             title: "Không thể lấy dữ liệu xác thực",
        //             message: "Không thể lấy dữ liệu xác thực hệ thống OneHealth",
        //         })
        //         throw new Error("Cannot get token OneHealth(oh): " + JSON.stringify(result));
        //     }
        // } else {
        //     this._notification.error({
        //         title: "Không thể lấy dữ liệu xác thực",
        //         message: "Không thể lấy dữ liệu xác thực hệ thống OneHealth",
        //     })
        //     throw new Error("Cannot get token OneHealth(his): " + JSON.stringify(rs));
        // }
        this._accessTokenOneHealth = "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fthna5Bq87OZ3or3EZzc7TWwtEA-77fGQ8ufPwpEtSCUkZ0OQocavWl1Nj5wnPsOAALcTj5kG8mrGBFkUYDAiA";
    }

    _setUserInfo() {
        if (!userInfo || !userInfo['FULL_NAME'] || !userInfo['USER_ID'] || !userInfo['HOSPITAL_CODE']) {
            let usInfo = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.UINFO", user_id);
            if (!usInfo || usInfo.length < 1) {
                this._notification.error({
                    title: "Không thể lấy dữ liệu chính",
                    message: "Không thể lấy dữ liệu người dùng phiên đăng nhập",
                })
                throw new Error("Cannot get userinfo for user_id: " + user_id);
            }
            this._userInfo = usInfo[0];
        } else {
            this._userInfo = {
                FULL_NAME: userInfo['FULL_NAME'],
                USER_ID: userInfo['USER_ID'],
                HOSPITAL_CODE: userInfo['HOSPITAL_CODE']
            }
        }
    }
}