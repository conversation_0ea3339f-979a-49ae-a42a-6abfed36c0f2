function dSCHList(opt) {
    var _gridDSCHId = "grdDSBN";
    this.opt = $.extend({}, opt);
    this.load = doLoad;
    var benhanid = _opts.benhanid;
    function doLoad(_hosp_id) {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
        var _tu_ngay = _sys_date;
        var _den_ngay = _sys_date;

        $("#txtTuNgay").val(_tu_ngay);
        $("#txtDenNgay").val(_den_ngay);
        var _options = $.extend({}, _opts);
        var _param = _options._param;
        _hospital_id = _param[0];
        _user_id = _param[1];
        _initControl();
        _bindEvent();
        //loadGrid(); // L2PT-29915
    }
    function _initControl() {

    };
    function _bindEvent() {
        $.jMaskGlobals = {
            maskElements : 'input,td,span,div',
            dataMaskAttr : '*[data-mask]',
            dataMask : true,
            watchInterval : 300,
            watchInputs : true,
            watchDataMask : true,
            byPassKeys : [ 9, 16, 17, 18, 36, 37, 38, 39, 40, 91 ],
            translation : {
                '0' : {
                    pattern : /\d/
                },
                '9' : {
                    pattern : /\d/,
                    optional : true
                },
                '#' : {
                    pattern : /\d/,
                    recursive : true
                },
                'A' : {
                    pattern : /[a-zA-Z0-9]/
                },
                'S' : {
                    pattern : /[a-zA-Z]/
                }
            }
        };
        // L2PT-31151 duonghn: thêm mã thẻ

        var _gridHeader =
            "HOSOBENHANID,HOSOBENHANID,80,0,t,l;Số lưu trữ,SOLUUTRU,80,0,f,l;Mã bệnh án,MABENHAN,80,0,f,l;Tên bệnh án,TENBENHAN,80,0,f,l;"
            + "Ngày sinh,NGAYSINH,100,0,f,l;Giới tính,GIOITINH,80,0,f,l;"
            + "Ngày mở hồ sơ,NGAYMOHS,100,0,f,l;Ngày đóng hồ sơ,NGAYDONGHS,100,0,f,l;Người đóng,NGUOIDONG,80,0,f,l;Trạng thái,TRANGTHAI,80,0,f,l";
        GridUtil.init(_gridDSCHId, "100%", "350px", "Danh sách bệnh nhân", true, _gridHeader, true, {
            rowNum : 100,
            rowList : [ 100, 500, 1000, 5000 ]
        });
        $('#btnTimKiem').on('click', function() {
            loadGrid();
        });

        // L2PT-4753 start
        $("#btnDong").on("click", function() {
            updateStatus("9");
        });

        $("#btnMo").on("click", function() {
            updateStatus("1");
        });
    }


    // L2PT-4753 start
    function loadGrid() {
        var objTimKiem = new Object();
        FormUtil.setFormToObject('divSearch', '', objTimKiem);

        var sql_par = [ {
            "name" : "[0]",
            "value" : JSON.stringify(objTimKiem)
        } ];
        GridUtil.loadGridBySqlPage(_gridDSCHId, "NGT.LOADDSBADN", sql_par);
    }



    function updateStatus(actionType) {
        var selRows = $("#" + _gridDSCHId).jqGrid('getGridParam', 'selarrrow');

        if (selRows.length == 0) {
            DlgUtil.showMsg("Vui lòng chọn ít nhất 1 hồ sơ!");
            return;
        }

        var successCount = 0, failCount = 0;

        for (var i = 0; i < selRows.length; i++) {
            var rowData = $("#" + _gridDSCHId).jqGrid('getRowData', selRows[i]);
            //console.log(`Cập nhật hồ sơ: MABENHAN=${rowData.MABENHAN}, action=${actionType}`);
            console.log(`rowData`, rowData);
            if (!rowData.HOSOBENHANID || rowData.HOSOBENHANID <= 0) {
                console.error("ID hồ sơ không hợp lệ:", rowData);
                continue;
            }

            var ojb = new Object();
            //ojb.MABENHAN = benhanid;
            ojb.HOSOBENHANID = rowData.HOSOBENHANID;
            ojb.TRANGTHAI = actionType;
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.UPDATEDSBADN", JSON.stringify(ojb));

            console.log("Kết quả cập nhật:", ret);

            if (ret == 1) {
                successCount++;
            } else {
                failCount++;
            }
        }

        if (successCount > 0) {
            DlgUtil.showMsg(`Cập nhật thành công ${successCount} hồ sơ!`);
            loadGrid();
        } else {
            DlgUtil.showMsg(`Cập nhật thất bại! ${failCount} hồ sơ không cập nhật được.`);
        }
    }
}
