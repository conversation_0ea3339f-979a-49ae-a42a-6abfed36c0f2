<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id"
	value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript"
	src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript"
	src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet"
	href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css"
	rel="stylesheet" />


<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script type="text/javascript" src="..//noitru/cominf.js"></script>
<script type="text/javascript" src="../common/script/jszip/jszip.js"></script>
<script type="text/javascript" src="../common/script/jszip/FileSaver.js"></script>
<script type="text/javascript"
	src="../common/script/jquery.priceformat.2.0.min.js"></script>
	<script type="text/javascript" src="../common/script/xlsx/xlsx.full.min.js"></script>
	
	<script type="text/javascript" src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css" type="text/css"/>

<script type="text/javascript"	src="../duoc/DUC61S001_DSThuocTrongKho.js?v=20211215025"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<style type="text/css">
table.ui-jqgrid-btable {
	height: 1px;
}
</style>
<div id="divMain" class="container">
	<div id="toolbarId"></div>
	<div id="divSearch" class="panel-body border-group-1 mgt5">
		<div class="col-xs-10 low-padding">
			<div class="col-xs-12 low-padding">

				<div class="col-xs-4 low-padding">
					<div class="col-xs-12 mgt3">
						<div class="col-xs-6 low-padding">

							<div class="col-xs-12 low-padding">
								<div class="col-md-6 low-padding">
									<label class="control-label">Kho lập</label>
								</div>
								<div class="col-md-6 low-padding">
									<input class="form-control input-sm i-col-m_fl text-left"
										style="width: 100%;" id="txtTimKho" name="txtTimKho" title="">
								</div>

							</div>

						</div>
						<div class="col-xs-6 low-padding">
							<select class="form-control input-sm i-col-m_fl text-left"
								style="width: 100%;" id="cboKhoLap"></select>
						</div>
					</div>
				</div>

				<div class="col-xs-8 low-padding    ">
					<div class="col-xs-3 mgt3">
						<input class="mgb5" type="checkbox" id="ckbThuocDaKhoa">
						Thuốc đã khóa
					</div>
					<div class="col-xs-4 mgt3">
						<div class="col-xs-2 low-padding mgt3">
							<label class="control-label" style="text-align: right;">Chọn</label>
						</div>
						<div class="col-xs-10 mgt3">
							<!-- 						<input class="mgb5" type="checkbox" id="ckbTonKho"> Tồn kho = 0 -->
							<select class="form-control input-sm" id="cboTonKho">
								<option value="0" selected>Tồn kho >0</option>
								<option value="1">Tồn kho = 0</option>
								<option value="2">Tất cả</option>
							</select>
						</div>
					</div>

					<div class="col-xs-2 low-padding mgt3">
						<input class="mgb5" type="checkbox" id="ckbXemChiTiet">
						Xem chi tiết
					</div>
					<div class="col-xs-3 mgt3" id="dsthuochethan"
						style="display: none;">
						<input class="mgb5" type="checkbox" id="ckbDSThuocHetHan">
						Ds thuốc hết hạn
					</div>
				</div>
			</div>

			<div class="col-xs-12 low-padding mgb0">
				<div class="col-xs-4 low-padding">
					<div class="low-padding">
						<div class="col-xs-12">
							<div class="col-xs-3 low-padding">
								<label class="control-label">Thuốc/VT</label>
							</div>
							<div class="col-xs-9 low-padding">
								<input class="form-control input-sm i-col-m_fl text-left"
									placeholder="Nhập tên hoặc mã thuốc VT tìm kiếm"
									style="width: 100%;" id="txtTimKiem" maxlength="750"
									name="txtTimKiem" title="Nhập từ tìm kiếm"> </select>
							</div>
						</div>
					</div>
				</div>

				<div class="col-xs-6 low-padding">
					<div class="low-padding">
						<div class="col-xs-12">
							<div class="col-xs-3 low-padding">
								<label class="control-label mgl20">Hoạt chất</label>
							</div>
							<div class="col-xs-9 low-padding">
								<input class="form-control input-sm i-col-m_fl text-left "
									placeholder="Nhập hoạt chất tìm kiếm"
									style="width: 94%; margin-left: 7px;" id="txtHoatChat"
									maxlength="750" name="txtHoatChat" title="Nhập từ tìm kiếm">
								</select>
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-2 low-padding" id = 'div_SoLuong' style="display:none;">
					<div class="low-padding">
						<div class="col-xs-12">
							<div class="col-xs-7 low-padding">
								<label class="control-label mgl5">SL tồn</label>
							</div>
							<div class="col-xs-5 low-padding">
								<input class="form-control input-sm i-col-m_fl text-left "
									 id="txtSoLuongTon"
									name="txtSoLuongTon">
								</select>
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-2 low-padding" id = 'div_LenhTon' style="display:none;">
					<div class="low-padding">
						<div class="col-xs-12">
							<input class="mgb5" type="checkbox" id="ckbLechTon">
							Lệch tồn
						</div>
					</div>
				</div>
			</div>


			<div class="col-xs-12 low-padding" id="guide_color"
				style="margin-bottom: -3px !important;">
				<div class="col-xs-3 ">
					<label
						style="float: left; margin-top: 3px; font-weight: 600 !important;">Cảnh
						báo hạn sử dụng</label>
				</div>
				<div class="col-xs-9 ">
					<div class="col-xs-1 low-padding ">
						<input class="form-control input-sm" type="text" id=""
							disabled="disabled" style="background: #ff8000">
					</div>
					<div class="col-xs-3 ">
						<!-- <label">HSD < 180 ngày</label> -->
						<input class="mgb5" type="checkbox" id="ckbHSD180">
						HSD < 180 ngày
					</div>

					<div class="col-xs-1 low-padding">
						<input class="form-control input-sm" type="text" id=""
							disabled="disabled"
							style="background: #ffff00; width: 100% !important;">
					</div>
					<div class="col-xs-3 ">
						<!-- <label>HSD < 90 ngày</label> -->
						<input class="mgb5" type="checkbox" id="ckbHSD90">
						HSD < 90 ngày
					</div>

					<div class="col-xs-1 low-padding ">
						<input class="form-control input-sm" type="text" id=""
							disabled="disabled" style="background: #ff66a3">
					</div>
					<div class="col-xs-3 ">
						<label>Hết hạn sử dụng</label>
					</div>
				</div>
			</div>
			
			<div class="col-xs-12 low-padding" id="canhbaosl_color"
				style="margin-bottom: -3px !important;">
				<div class="col-xs-3 ">
					<label
						style="float: left; margin-top: 3px; font-weight: 600 !important;">Cảnh
						báo số lượng</label>
				</div>
				<div class="col-xs-9 ">
					<div class="col-xs-1 low-padding ">
						<input class="form-control input-sm" type="text" id=""
							disabled="disabled" style="background: #334CFF">
					</div>
					<div class="col-xs-3 ">
						<!-- <label">HSD < 180 ngày</label> -->
						<input class="mgb5" type="checkbox" id="ckbtoithieu">
						Tối thiểu
					</div>

					<div class="col-xs-1 low-padding">
						<input class="form-control input-sm" type="text" id=""
							disabled="disabled"
							style="background: #46FF33; width: 100% !important;">
					</div>
					<div class="col-xs-3 ">
						<!-- <label>HSD < 90 ngày</label> -->
						<input class="mgb5" type="checkbox" id="ckbtoida">
						Tối đa & HSD < 90 ngày
					</div>

				</div>
			</div>
			
			<div class="col-xs-12 low-padding" 
				style="margin-bottom: -3px !important;">
				
			
				
				
					<div class="col-xs-4 low-padding">
					<div class="low-padding">
						<div class="col-xs-12">
							<div class="col-xs-3 low-padding">
								<label class="control-label">Nhóm TVT</label>
							</div>
							<div class="col-xs-9 low-padding">
								<!-- <select class="form-control input-sm i-col-m_fl text-left"
								style="width: 100%;" id="cboLoaiThuoc">
							</select> -->
							
							<select class="selectpicker SumoUnder" id="cboNhomThuoc"  
								multiple data-actions-box="true" data-selected-text-format="count > 2" data-width="auto" ></select>
							</div>
						</div>
					</div>
				</div>

				<div class="col-xs-6 low-padding">
					<div class="low-padding">
						<div class="col-xs-12">
							<div class="col-xs-3 low-padding">
								<label class="control-label mgl20">Nguồn CT</label>
							</div>
							<div class="col-xs-9 low-padding">
						 	<select class="form-control input-sm i-col-m_fl text-left"
						  style="width: 94%;margin-left: 7px;" id="cboNguonCT" name="cboNguonCT" title=""></select>
						  
						
						  
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-2 low-padding">
					<div class="low-padding">
						<div class="col-xs-12 low-padding">
							<button type="button" id="btnDoiLoaiBu" style="margin-top:7px; float:right;margin-right:10px" class="btn btn-lg btn-default wd100"><span class="glyphicon glyphicon-repeat"></span> Đổi loại bù </button>
						</div>
					</div>
				</div>
				
				
		 </div>
		 
		 <div class="col-xs-12 low-padding" 
				style="margin-bottom: -3px !important; display:none;" id = "divNhomBHXH">
	
					<div class="col-xs-4 low-padding">
					<div class="low-padding">
						<div class="col-xs-12">
							<div class="col-xs-3 low-padding">
								<label class="control-label">Nhóm BHXH</label>
							</div>
							<div class="col-xs-9 low-padding">
								<!-- <select class="form-control input-sm i-col-m_fl text-left"
								style="width: 100%;" id="cboLoaiThuoc">
							</select> -->
							
							<select class="form-control input-sm i-col-m_fl text-left"
						  style="width: 100%;margin-left: 7px;" id="cboNhomBHXH" name="cboNhomBHXH" title=""></select>
							</div>
						</div>
					</div>
				</div>
				
		 </div>
		 
		</div>
		
		<div class="col-xs-2 low-padding">
			<div class="col-xs-6 low-padding">
			<button type="button" class=" mgl-15 btn btn-sm btn-primary"
				style="margin-top: 3px;margin-left: -2px;" data-toggle="confirmation-singleton"
				data-placement="top" data-original-title title id="btnTimKiem">
				<span class="glyphicon glyphicon-search"></span> Tìm kiếm
			</button>
		</div>
		<div class="col-xs-6 low-padding">
			<a id="chk_xuatfiles"
				style="width: 100%; margin-right: 1px; margin-top: 3px; float: right;"
				class="btn btn-default"> Xuất excel </a>
		</div>
		<div class="col-xs-12 low-padding">
			<a id="btnXuatEXCS"
				style="width: 100%; margin-right: 4px; margin-top: 3px; float: right;"
				class="mgl-15 btn btn-default"> DM CS Thuốc </a>
		</div>
		<div class="col-xs-12 low-padding">
			<a id="btnLock"
				style="width: 100%; margin-right: 4px; margin-top: 3px; float: right;"
				class="mgl-15 btn btn-default"> Khóa/Mở khóa nhiều thuốc </a>
		</div>
		</div>
		
	</div>
	<!--
	<div class="col-xs-12 low-padding">
		<div style="height: auto; overflow: auto;">
			<div id="danhsachTHUOC">
				<table id="grvDSThuoc"></table>
				<div id="pager_grvDSThuoc"></div>
			</div>
		</div>
	</div>-->

	<div class="col-xs-12 low-padding mgt-5">
		<div id="danhsachGCT">
			<table id="grvDSThuoc"></table>
			<div id="pager_grvDSThuoc"></div>

			<div class="contextMenu" id="contextMenu"
				style="display: none; width: 250px;">
				<ul style="width: 250px; font-size: 65%;">
					<li id="selPhongLuu" name="selPhongLuu"><span class="ui-icon ui-icon-pencil"
						style="float: left"></span> <span
						style="font-size: 130%; font-family: Verdana" >Chọn nơi lưu trữ</span>
					</li>
					<li id="selKhoa_Mo" name="selKhoa_Mo"><span class="ui-icon ui-icon-pencil"
						style="float: left"></span> <span
						style="font-size: 130%; font-family: Verdana" class="khoathuoc">Khóa lại</span>
					</li>
					<li id="selLogKhoa_Mo" name="selLogKhoa_Mo"><span class="ui-icon ui-icon-pencil"
						style="float: left"></span> <span
						style="font-size: 130%; font-family: Verdana" class="lichsu">Lịch sử khóa thuốc/vật tư</span>
					</li>
				</ul>
			</div>
		</div>
	</div>

	<div class="col-xs-12 low-padding mgt-5">

		<div class="col-xs-1 low-padding">
			<label class="mgl5">Tổng số thuốc :</label>
		</div>
		<div class="col-xs-1 low-padding">
			<font size="3pt" color="red"><label class='text-left'
				style="width: 100%;" id="tongsothuoc"></label></font>
		</div>

		<div class="col-xs-1 low-padding">
			<label class="mgl5">Tổng tiền :</label>
		</div>
		<div class="col-xs-3 low-padding">
			<font size="3pt" color="red"><label class='text-left'
				style="width: 100%;" id="tongtien"></label></font>
		</div>

	</div>

	<input type="hidden" id="hidTonKhoID" value="-1"> <input
		type="hidden" id="hidTonKhoCTID" value="-1">

	<div id="divDlg" style="width: 100%; display: none">
		<iframe src="" id="ifmView" style="border: dotted 1px red"
			frameborder="0"></iframe>
	</div>
</div>


<script>
	var opt = [];
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang = "vn";
	console.log('uuid=' + uuid + ', schema=' + schema + ', province_id='
			+ province_id + ', hospital_id=' + hospital_id);
	var paramInfo = CommonUtil.decode('{paramData}');
	var session_par = [];
	session_par[1] = user_id;
	session_par[2] = schema;
	session_par[3] = province_id;
	session_par[0] = hospital_id;
	var table_name = '{table}';

	var _opts = new Object();
	_opts._param = session_par;
	_opts._uuid = uuid;
	_opts.lk = paramInfo.lk;
	_opts.cs = paramInfo.cs;
	_opts.hospitalId = hospital_id;
	initRest(_opts._uuid);

	var ttbn = new DSThuocTrongKho(_opts);
	ttbn.load();
</script>
