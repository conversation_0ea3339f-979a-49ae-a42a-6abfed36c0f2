class NotificationQueue {
    static ID = "NotificationQueue";
    static TEMPLATE = {
        INFO:
            `<div id="$id" 
                style="top: $toppx"
                role="status" class="nq-toast-notification nq-toast-notification--info">
                <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="20" height="20" viewBox="0 0 32 32" aria-hidden="true" class="nq-toast-notification__icon" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M16,8a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,13.875H17.125v-8H13v2.25h1.875v5.75H12v2.25h8Z" data-icon-path="inner-path"/>
                    <path d="M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,6a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,16,8Zm4,16.125H12v-2.25h2.875v-5.75H13v-2.25h4.125v8H20Z"/>
                    <title>Info</title>
                </svg>
                <div class="nq-toast-notification__details">
                    <div class="nq-toast-notification__title" dir="ltr">$title</div>
                    <div class="nq-toast-notification__subtitle" dir="ltr">$message</div>
                    <div class="nq-toast-notification__caption" dir="ltr">$date</div>
                </div>
                <button onclick="closeNotification(this)" aria-hidden="true" tabindex="-1" type="button" aria-label="closes notification" title="closes notification" class="nq-toast-notification__close-button">
                    <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="16" height="16" viewBox="0 0 32 32" aria-hidden="true" class="nq-toast-notification__close-icon" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.4141 16L24 9.4141 22.5859 8 16 14.5859 9.4143 8 8 9.4141 14.5859 16 8 22.5859 9.4143 24 16 17.4141 22.5859 24 24 22.5859 17.4141 16z"/>
                    </svg>
                </button>
            </div>`,
        ERROR:
            `<div  id="$id" 
                style="top: $toppx"
                role="status" class="nq-toast-notification nq-toast-notification--low-contrast nq-toast-notification--error">
                <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="20" height="20" viewBox="0 0 20 20" aria-hidden="true" class="nq-toast-notification__icon" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10,1c-5,0-9,4-9,9s4,9,9,9s9-4,9-9S15,1,10,1z M13.5,14.5l-8-8l1-1l8,8L13.5,14.5z"/>
                    <path d="M13.5,14.5l-8-8l1-1l8,8L13.5,14.5z" data-icon-path="inner-path" opacity="0"/>
                    <title>Error</title>
                </svg>
                <div class="nq-toast-notification__details">
                    <div class="nq-toast-notification__title" dir="ltr">$title</div>
                    <div class="nq-toast-notification__subtitle" dir="ltr">$message</div>
                    <div class="nq-toast-notification__caption" dir="ltr">$date</div>
                </div>
                <button onclick="closeNotification(this)" aria-hidden="true" tabindex="-1" type="button" aria-label="closes notification" title="closes notification" class="nq-toast-notification__close-button">
                    <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="16" height="16" viewBox="0 0 32 32" aria-hidden="true" class="nq-toast-notification__close-icon" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.4141 16L24 9.4141 22.5859 8 16 14.5859 9.4143 8 8 9.4141 14.5859 16 8 22.5859 9.4143 24 16 17.4141 22.5859 24 24 22.5859 17.4141 16z"/>
                    </svg>
                </button>
            </div>`,
        SUCCESS:
            `<div  id="$id" 
                style="top: $toppx"
                role="status" class="nq-toast-notification nq-toast-notification--low-contrast nq-toast-notification--success">
                <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="20" height="20" viewBox="0 0 20 20" aria-hidden="true" class="nq-toast-notification__icon" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10,1c-4.9,0-9,4.1-9,9s4.1,9,9,9s9-4,9-9S15,1,10,1z M8.7,13.5l-3.2-3.2l1-1l2.2,2.2l4.8-4.8l1,1L8.7,13.5z"/>
                    <path fill="none" d="M8.7,13.5l-3.2-3.2l1-1l2.2,2.2l4.8-4.8l1,1L8.7,13.5z" data-icon-path="inner-path" opacity="0"/>
                    <title>Success</title>
                </svg>
                <div class="nq-toast-notification__details">
                    <div class="nq-toast-notification__title" dir="ltr">$title</div>
                    <div class="nq-toast-notification__subtitle" dir="ltr">$message</div>
                    <div class="nq-toast-notification__caption" dir="ltr">$date</div>
                </div>
                <button onclick="closeNotification(this)" aria-hidden="true" tabindex="-1" type="button" aria-label="closes notification" title="closes notification" class="nq-toast-notification__close-button">
                    <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="16" height="16" viewBox="0 0 32 32" aria-hidden="true" class="nq-toast-notification__close-icon" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.4141 16L24 9.4141 22.5859 8 16 14.5859 9.4143 8 8 9.4141 14.5859 16 8 22.5859 9.4143 24 16 17.4141 22.5859 24 24 22.5859 17.4141 16z"/>
                    </svg>
                </button>
            </div>`,
        WARING:
            `<div  id="$id" 
                style="top: $toppx"
                role="status" class="nq-toast-notification nq-toast-notification--low-contrast nq-toast-notification--warning-alt">
                <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="20" height="20" viewBox="0 0 32 32" aria-hidden="true" class="nq-toast-notification__icon" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M16,26a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16,26Zm-1.125-5h2.25V12h-2.25Z" data-icon-path="inner-path"/>
                    <path d="M16.002,6.1714h-.004L4.6487,27.9966,4.6506,28H27.3494l.0019-.0034ZM14.875,12h2.25v9h-2.25ZM16,26a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,16,26Z"/>
                    <path d="M29,30H3a1,1,0,0,1-.8872-1.4614l13-25a1,1,0,0,1,1.7744,0l13,25A1,1,0,0,1,29,30ZM4.6507,28H27.3493l.002-.0033L16.002,6.1714h-.004L4.6487,27.9967Z"/>
                    <title>Warning</title>
                </svg>
                <div class="nq-toast-notification__details">
                    <div class="nq-toast-notification__title" dir="ltr">$title</div>
                    <div class="nq-toast-notification__subtitle" dir="ltr">$message</div>
                    <div class="nq-toast-notification__caption" dir="ltr">$date</div>
                </div>
                <button onclick="closeNotification(this)" aria-hidden="true" tabindex="-1" type="button" aria-label="closes notification" title="closes notification" class="nq-toast-notification__close-button">
                    <svg focusable="false" preserveAspectRatio="xMidYMid meet" fill="currentColor" width="16" height="16" viewBox="0 0 32 32" aria-hidden="true" class="nq-toast-notification__close-icon" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.4141 16L24 9.4141 22.5859 8 16 14.5859 9.4143 8 8 9.4141 14.5859 16 8 22.5859 9.4143 24 16 17.4141 22.5859 24 24 22.5859 17.4141 16z"/>
                    </svg>
                </button>
            </div>`
    };

    static close = function (el) {
        $(el).parent(".nq-toast-notification").hide( 120, function() {
            $( this ).remove();
        });
    }

    constructor({timeout = 13000, delayTimer = 1000, zIndex = 9, level = "current" }) {
        this._zIndex = zIndex;
        if (level == "top") {
            window.top.closeNotification = NotificationQueue.close;
            this._doc = window.top.document;
        } else {
            window.closeNotification = NotificationQueue.close;
            this._doc = document;
        }
        this._init();
        this._queueNotifyWaiting = new NotificationTimerQueue();
        this._delay = delayTimer;
        this._timeout = timeout;
    }

    _init() {
        let idElement = NotificationQueue.ID + "_Element"
        if (!this._doc.getElementById(idElement)) {
            let nqElement = this._doc.createElement('div');
            nqElement.id = idElement;
            this._doc.body.appendChild(nqElement);
        }
        this._container = this._doc.getElementById(idElement);
        this._loadCss();
    }

    error({ title = "", message = ""}) {
        this._toast({
            type: "error",
            title, message
        })
    }

    success({ title = "", message = ""}) {
        this._toast({
            type: "success",
            title, message
        })
    }

    info({ title = "", message = ""}) {
        this._toast({
            type: "info",
            title, message
        })
    }

    warning({ title = "", message = ""}) {
        this._toast({
            type: "warning",
            title, message
        })
    }

    _toast({ type = "info", title = "", message = "" }) {
        let notifyId = "notification-" + this._genRandonString(10);
        let toast = this._createElementFromHTML(NotificationQueue
            .TEMPLATE[type.toUpperCase()]
            .replace("$id", notifyId)
            .replace("$top", $("#divMenuCus", this._doc).height() + 5)
            .replace("$title", title)
            .replace("$message", message)
            .replace("$date", this._getDateString(new Date(), true))
        );
        this._container.prepend(toast);
        this._queueNotifyWaiting.addTask(() => {
            let firstElementHeight = $("#" + notifyId, this._doc).height();
            $(this._container).find(".nq-toast-notification[opened='1']").each(
                (idx, el) => {
                    let newPositionTop = ($(el).position().top + firstElementHeight + 15) + "px";
                    $(el).css("top", newPositionTop)
                })
            $("#" + notifyId, this._doc).css("z-index", this._zIndex);
            $("#" + notifyId, this._doc).css("right", "0");
            $("#" + notifyId, this._doc).attr("opened", "1");
            setTimeout(() => {
                $("#" + notifyId, this._doc).hide( 120, function() {
                    $( this ).remove();
                });
            }, this._timeout);
        }, this._queueNotifyWaiting.length() > 0 ? this._delay : 500);
    }

    _createElementFromHTML(htmlString) {
        let div = this._doc.createElement('div');
        div.innerHTML = htmlString.trim();
        return div.firstChild;
    }

    _genRandonString(length) {
        let chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let charLength = chars.length;
        let result = '';
        for ( let i = 0; i < length; i++ ) {
            result += chars.charAt(Math.floor(Math.random() * charLength));
        }
        return result;
    }

    _loadCss() {
        let cssId = NotificationQueue.ID + "_Style";
        if (!this._doc.getElementById(cssId))
        {
            let head  = this._doc.getElementsByTagName('head')[0];
            let link  = this._doc.createElement('link');
            link.id   = cssId;
            link.rel  = 'stylesheet';
            link.type = 'text/css';
            link.href = '/vnpthis/common/utility/NotificationQueue.css';
            link.media = 'all';
            head.appendChild(link);
        }
    }

    _getDateString(_pdate, _isGetDay) {
        let rdate;
        if (!_pdate) {
            rdate = new Date();
        } else {
            rdate = _pdate;
        }
        let datetime = this._lpadDate(rdate.getDate(), 2)
            + "/" + this._lpadDate(rdate.getMonth() + 1, 2)
            + "/" + rdate.getFullYear();
        if (_isGetDay === true) {
            datetime += " " + this._lpadDate(rdate.getHours(), 2) + ":"
                + this._lpadDate(rdate.getMinutes(), 2) + ":"
                + this._lpadDate(rdate.getSeconds(), 2);
        }
        return datetime;
    }

    _lpadDate(val, length) {
        return ("" + val).padStart(length, '0');
    }
}

class NotificationTimerQueue {
    constructor() {
        this.currentTimer = null;
        this.tasks = [];
    }

    addTask(callback, delay){
        this.tasks.push({ callback: callback, delay: delay });
        if(this.currentTimer) return;
        this.launchNextTask();
    };

    length() {
        return this.tasks.length;
    }

    launchNextTask(){
        if(this.currentTimer) return;
        let self = this;
        let nextTask = this.tasks.shift();
        if(!nextTask) return this.clear();
        this.currentTimer = setTimeout(function(){
            nextTask.callback.call();
            self.currentTimer = null;
            self.launchNextTask();
        }, nextTask.delay);
    };

    clear(){
        if (this.currentTimer) clearTimeout(this.currentTimer);
        this.currentTimer = null;
        this.tasks.length = 0;
    };
}