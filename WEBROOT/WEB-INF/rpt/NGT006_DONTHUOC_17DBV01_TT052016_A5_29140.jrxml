<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT006_DONTHUOC_17DBV01_TT052016_A5_30252" pageWidth="421" pageHeight="595" columnWidth="421" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="e1eb1f53-8863-4b59-afd2-65d31b810769">
	<property name="ireport.zoom" value="2.143588810000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="12"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="SUB1_DONTHUOC_LOAI_THUONG_29140" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="SUB2_DONTHUOC_LOAI_MIENPHI_29140" class="net.sf.jasperreports.engine.JasperReport"/>
	<queryString language="plsql">
		<![CDATA[{call indonthuoc_tach_30252($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{maubenhphamid}
,$P{ora_cursor})}]]>
	</queryString>
	<field name="LOAI" class="java.lang.Integer"/>
	<detail>
		<band height="104" splitType="Immediate">
			<printWhenExpression><![CDATA[$F{LOAI}==2]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="3" y="0" width="411" height="103" uuid="9638613e-204f-4e27-bda0-8fc16ad7563c"/>
				<subreportParameter name="org_name">
					<subreportParameterExpression><![CDATA[$P{org_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[HID]">
					<subreportParameterExpression><![CDATA[$P{[HID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[UID]">
					<subreportParameterExpression><![CDATA[$P{[UID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="maubenhphamid">
					<subreportParameterExpression><![CDATA[$P{maubenhphamid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="parent_name">
					<subreportParameterExpression><![CDATA[$P{parent_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[SCH]">
					<subreportParameterExpression><![CDATA[$P{[SCH]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dept_name">
					<subreportParameterExpression><![CDATA[$P{dept_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[IP]">
					<subreportParameterExpression><![CDATA[$P{[IP]}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUB2_DONTHUOC_LOAI_MIENPHI_29140}]]></subreportExpression>
			</subreport>
			<break>
				<reportElement positionType="Float" x="0" y="103" width="421" height="1" uuid="ac434c24-f987-492d-9f3d-305adb859a82"/>
			</break>
		</band>
		<band height="135" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{LOAI}==0]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="3" y="0" width="411" height="103" uuid="98e63432-802a-496e-bba3-5f3c0a461ea6"/>
				<subreportParameter name="org_name">
					<subreportParameterExpression><![CDATA[$P{org_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[HID]">
					<subreportParameterExpression><![CDATA[$P{[HID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[UID]">
					<subreportParameterExpression><![CDATA[$P{[UID]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="maubenhphamid">
					<subreportParameterExpression><![CDATA[$P{maubenhphamid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="parent_name">
					<subreportParameterExpression><![CDATA[$P{parent_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[SCH]">
					<subreportParameterExpression><![CDATA[$P{[SCH]}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dept_name">
					<subreportParameterExpression><![CDATA[$P{dept_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="[IP]">
					<subreportParameterExpression><![CDATA[$P{[IP]}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUB1_DONTHUOC_LOAI_THUONG_29140}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
