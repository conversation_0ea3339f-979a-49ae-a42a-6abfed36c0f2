/*
Mục đích  : <PERSON><PERSON> bi truoc mo
<PERSON>ời lập tr<PERSON><PERSON> cập nhật  <PERSON>hi chú
DUCTX	- 25102021 - Comment
*/
function NTU007_CbiTruocMo(opt) {
	this.load = doLoad;
	var that = this;
	var tcmo = '';
	var isKyCaClick = false;
	var v_truocmoid = '';
	var _khambenhid = '';
	var _khoacd = '';
	var _dichvukhambenhid = '';
	var _phieuid = '';
	var _gridPhieuTruocMo = "grdDSPhieuTruocMo";
	var _gridPhieuTruocMoHeader = "DICHVUKHAMBENHID,DICHVUKHAMBENHID,50,0,t,l,1,2;" + "KHAMBENHID,KHAMBENHID,50,0,t,l,1,2;" + "KHOAID,KHOAID,50,0,t,l,1,2;" + "TRUOCMO_ID,TRUOCMO_ID,50,0,t,l,1,2;"
			+ "FLAG_CA,FLAG_CA,50,0,t,l,1,2;" + " ,ICON,35,0,ns,l;" + "<PERSON><PERSON> phiếu,SOPHIEU,160,0,f,l,1,2;" + "Người tạo,NGUOI_TAO,160,0,f,l,1,2;" + "Khoa,KHOA,300,0,f,l,1,2;"
			+ "Phòng,PHONG,300,0,f,l,1,2;" + "Ngày thực hiện,NGAYTHUCHIEN,160,0,f,l,1,2";
	var cf = new Object();
	function doLoad() {
		this.validator = new DataValidator("divBKTC");
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		initControl();
		bindEvent();
		loadMauKB();
	}
	function initControl() {
		_khambenhid = this.opt.khambenhid;
		_khoacd = this.opt.khoacd;
		_dichvukhambenhid = this.opt.dichvukhambenhid;
		var timeStart = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		$('#txtNGAYTHUCHIEN').val(timeStart);
		$('#txtGIODUADIMO').val(timeStart);
		GridUtil.init(_gridPhieuTruocMo, "100%", "200", "Danh sách chuẩn bị trước mổ", false, _gridPhieuTruocMoHeader, false);
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU;CBTM_NOCHECK_EDIT;CBTM_NOT_LUUMAU');//L2PT-90985
		if (data_ar != null && data_ar.length > 0) {
			cf = data_ar[0];
		}
		if (cf.CBTM_NOCHECK_EDIT == '1') {//HaNv_101022: L2PT-27308
			$("#khth *").prop("disabled", false);
			$("#khcd *").prop("disabled", false);
			$("#tem *").prop("disabled", false);
		} else {
			if (this.opt.khoaid == this.opt.khoacd && this.opt.khoaid != this.opt.khoath) {
				$("#khth *").prop("disabled", true);
				$("#khcd *").prop("disabled", false);
				$("#tem *").prop("disabled", false);
			}
			if (this.opt.khoaid == this.opt.khoath && this.opt.khoaid != this.opt.khoacd) {
				$("#khcd *").prop("disabled", true);
				$("#khth *").prop("disabled", false);
				$("#tem *").prop("disabled", true);
			}
			if (this.opt.khoaid == this.opt.khoath && this.opt.khoaid == this.opt.khoacd) {
				$("#khth *").prop("disabled", false);
				$("#khcd *").prop("disabled", false);
				$("#tem *").prop("disabled", false);
			}
		}
		$("#txtMABENHNHAN").prop("disabled", true);
		$("#txtTENBENHNHAN").prop("disabled", true);
		$("#txtTUOI").prop("disabled", true);
		$("#txtGIOITINHID").prop("disabled", true);
		$("#cboGIOITINHID").prop("disabled", true);
		$("#txtMAHOSOBENHAN").prop("disabled", true);
		$("#txtKHOA").prop("disabled", true);
		// Xử lý sự kiện liên quan ký CA => START
		if (cf.HIS_SUDUNG_KYSO_KYDIENTU != '0' && typeof cf.HIS_SUDUNG_KYSO_KYDIENTU !== "undefined") {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnInKy").show();
		} else {
			$("#btnKyCa").hide();
			$("#btnHuyCa").hide();
			$("#btnInKy").hide();
		}
		// Xử lý sự kiện liên quan ký CA => END
		doLoadCombo("txtMACHANDOANRAVIEN", "txtCHANDOANRAVIEN");
		doLoadComboKemTheo("txtMACHANDOANRAVIEN_KEMTHEO", "txtCHANDOANRAVIEN_KEMTHEO");
		checkphim();
		loadGridData();
		loadCbiTruocmo();
		_checkKyCA();
	}
	function checkphim() {
		if ($("input[name='radPHOI']:checked").val() == "1") {
			$('#txtPHIMPHOI').show();
		} else {
			$('#txtPHIMPHOI').hide();
		}
		if ($("input[name='radPHIM']:checked").val() == "1") {
			$('#txtPHIMKHAC').show();
		} else {
			$('#txtPHIMKHAC').hide();
		}
	}
	function loadCbiTruocmo(truocmo_id) {
		//var param = opt.hosobenhanid + "";
		var obj = new Object();
		obj.KHAMBENHID = _khambenhid;
		obj.KHOACD = _khoacd;
		obj.DICHVUKHAMBENHID = _dichvukhambenhid;
		obj.TRUOCMOID = truocmo_id ? truocmo_id : 0;
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU007.S08", [ _khambenhid, _khoacd, _dichvukhambenhid ].join('$'));
		if (result != null && result.length > 0) {
			var obj = result[0];
			setTimeout(function() {
				FormUtil.setObjectToForm("divBKTC", "", obj);
			}, 500);
			$("#txtGIOITINHID").val(obj.GIOITINHID);
			$("#cboGIOITINHID").val(obj.GIOITINHID);
			//var chk = jsonrpc.AjaxJson.getOneValue("NGT.GETTRUOCMO", [{"name":"[0]", "value":opt.khambenhid},{"name":"[1]", "value":opt.dichvukhambenhid}]);
			tcmo = obj.TRUOCMO_ID;
			resetRadio();
			if (tcmo != '0' && tcmo != '') {
				var PHOI = 0;
				var PHIM = 0;
				if (obj.PHIMPHOI != '0') {
					PHOI = 1;
				} else {
					PHOI = 2;
				}
				if (obj.PHIMKHAC != '0') {
					PHIM = 1;
				} else {
					PHIM = 2;
				}
				$('#divBKTC input[name=radVSTG][value=' + obj.VSTG + ']').prop('checked', true);
				$('#divBKTC input[name=radSONMONG][value=' + obj.SONMONG + ']').prop('checked', true);
				$('#divBKTC input[name=radCATMONG][value=' + obj.CATMONG + ']').prop('checked', true);
				$('#divBKTC input[name=radTRANGSUC][value=' + obj.TRANGSUC + ']').prop('checked', true);
				$('#divBKTC input[name=radRANGGIA][value=' + obj.RANGGIA + ']').prop('checked', true);
				$('#divBKTC input[name=radQUANAO][value=' + obj.QUANAO + ']').prop('checked', true);
				$('#divBKTC input[name=radVSR][value=' + obj.VSR + ']').prop('checked', true);
				$('#divBKTC input[name=radVSDVC][value=' + obj.VSDVC + ']').prop('checked', true);
				$('#divBKTC input[name=radBANG][value=' + obj.BANG + ']').prop('checked', true);
				$('#divBKTC input[name=radHSBA][value=' + obj.HSBA + ']').prop('checked', true);
				$('#divBKTC input[name=radXN][value=' + obj.XN + ']').prop('checked', true);
				$('#divBKTC input[name=radDIEN][value=' + obj.DIEN + ']').prop('checked', true);
				$('#divBKTC input[name=radGMHS][value=' + obj.GMHS + ']').prop('checked', true);
				$('#divBKTC input[name=radPHOI][value=' + PHOI + ']').prop('checked', true);
				$('#divBKTC input[name=radPHIM][value=' + PHIM + ']').prop('checked', true);
				//L2PT-13279
				for ( var key in obj) {
					if (!obj.hasOwnProperty(key))
						continue;
					if ($('#divBKTC input[name=rad' + key + ']').length) {
						$('#divBKTC input[name=rad' + key + '][value=' + obj[key] + ']').prop('checked', true);
					}
				}
				checkphim();
			} else {
				resetRadio();
			}
		}
	}
	function resetRadio() {
		$('#divBKTC input[name=radVSTG]').prop('checked', false);
		$('#divBKTC input[name=radSONMONG]').prop('checked', false);
		$('#divBKTC input[name=radCATMONG]').prop('checked', false);
		$('#divBKTC input[name=radTRANGSUC]').prop('checked', false);
		$('#divBKTC input[name=radRANGGIA]').prop('checked', false);
		$('#divBKTC input[name=radQUANAO]').prop('checked', false);
		$('#divBKTC input[name=radVSR]').prop('checked', false);
		$('#divBKTC input[name=radVSDVC]').prop('checked', false);
		$('#divBKTC input[name=radBANG]').prop('checked', false);
		$('#divBKTC input[name=radHSBA]').prop('checked', false);
		$('#divBKTC input[name=radXN]').prop('checked', false);
		$('#divBKTC input[name=radDIEN]').prop('checked', false);
		$('#divBKTC input[name=radGMHS]').prop('checked', false);
		$('#divBKTC input[name=radPHOI]').prop('checked', false);
		$('#divBKTC input[name=radPHIM]').prop('checked', false);
	}
	function saveTruocMo(flag) {
		var obj = new Object();
		FormUtil.setFormToObject("divBKTC", "", obj);
		obj["TRUOCMO_ID"] = tcmo;
		obj["NGAYTHUCHIEN"] = $("#txtNGAYTHUCHIEN").val();
		obj["VSTG"] = $("input[name='radVSTG']:checked").val();
		obj["SONMONG"] = $("input[name='radSONMONG']:checked").val();
		obj["CATMONG"] = $("input[name='radCATMONG']:checked").val();
		obj["TRANGSUC"] = $("input[name='radTRANGSUC']:checked").val();
		obj["RANGGIA"] = $("input[name='radRANGGIA']:checked").val();
		obj["QUANAO"] = $("input[name='radQUANAO']:checked").val();
		obj["VSR"] = $("input[name='radVSR']:checked").val();
		obj["VSDVC"] = $("input[name='radVSDVC']:checked").val();
		obj["BANG"] = $("input[name='radBANG']:checked").val();
		obj["HSBA"] = $("input[name='radHSBA']:checked").val();
		obj["XN"] = $("input[name='radXN']:checked").val();
		obj["DIEN"] = $("input[name='radDIEN']:checked").val();
		obj["PHIMPHOI"] = $("#txtPHIMPHOI").val();
		obj["PHIMKHAC"] = $("#txtPHIMKHAC").val();
		obj["GMHS"] = $("input[name='radGMHS']:checked").val();
		obj["NHACNB"] = $("#txtNHACNB").val();
		obj["THUOCTIENME"] = $("#txtTHUOCTIENME").val();
		obj["GIODUADIMO"] = $("#txtGIODUADIMO").val();
		obj["TIENSUDIUNG"] = $("#txtTIENSUDIUNG").val();
		obj["MACHANDOANRAVIEN_KEMTHEO"] = $("#txtMACHANDOANRAVIEN_KEMTHEO").val();
		obj["CHANDOANRAVIEN_KEMTHEO"] = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
		obj["GHICHU_BENHPHU"] = $("#txtGHICHU_BENHPHU").val();
		obj["MACHANDOANRAVIEN"] = $("#txtMACHANDOANRAVIEN").val();
		obj["CHANDOANRAVIEN"] = $("#txtCHANDOANRAVIEN").val();
		obj["GHICHU_BENHCHINH"] = $("#txtGHICHU_BENHCHINH").val();
		obj["KS"] = $("#txtKS").val();
		obj["HST"] = $("#txtHST").val();
		obj["BC"] = $("#txtBC").val();
		obj["HC"] = $("#txtHC").val();
		obj["US"] = $("#txtUS").val();
		obj["MD"] = $("#txtMD").val();
		obj["MC"] = $("#txtMC").val();
		obj["NHOMMAU"] = $("#txtNHOMMAU").val();
		obj["CANNANG"] = $("#txtCANNANG").val();
		obj["CHIEUCAO"] = $("#txtCHIEUCAO").val();
		obj["NHIPTHO"] = $("#txtNHIPTHO").val();
		obj["HUYETAP"] = $("#txtHUYETAP").val();
		obj["NHIETDO"] = $("#txtNHIETDO").val();
		obj["MACH"] = $("#txtMACH").val();
		obj["CBTT"] = $("input[name='radCBTT']:checked").val();
		obj["CAMDOANPT"] = $("input[name='radCAMDOANPT']:checked").val();
		obj["TINHTRANG_BN"] = $("input[name='radTINHTRANG_BN']:checked").val();
		obj["XN_COBAN"] = $("input[name='radXN_COBAN']:checked").val();
		obj["SOTO_KQXN_MAU"] = $("input[name='radSOTO_KQXN_MAU']:checked").val();
		obj["SOTO_KQXN_NUOCTIEU"] = $("input[name='radSOTO_KQXN_NUOCTIEU']:checked").val();
		obj["SOTO_KQXN_CDHA"] = $("input[name='radSOTO_KQXN_CDHA']:checked").val();
		obj["SOTO_KQXN_CLSKHAC"] = $("input[name='radSOTO_KQXN_CLSKHAC']:checked").val();
		obj["VSCN"] = $("input[name='radVSCN']:checked").val();
		obj["KIEMTRA_NHIEMTRUNG"] = $("input[name='radKIEMTRA_NHIEMTRUNG']:checked").val();
		obj["NHINAN"] = $("input[name='radNHINAN']:checked").val();
		obj["TET_TOC"] = $("input[name='radTET_TOC']:checked").val();
		obj["DEO_VONGTEN"] = $("input[name='radDEO_VONGTEN']:checked").val();
		obj["TAY_SONMOI"] = $("input[name='radTAY_SONMOI']:checked").val();
		obj["THONG_DADAY"] = $("input[name='radTHONG_DADAY']:checked").val();
		obj["RUA_DADAY"] = $("input[name='radRUA_DADAY']:checked").val();
		obj["THONGTIEU"] = $("input[name='radTHONGTIEU']:checked").val();
		obj["THUT_THAOPHAN"] = $("input[name='radTHUT_THAOPHAN']:checked").val();
		obj["DICHUYEN_XEDAY"] = $("input[name='radDICHUYEN_XEDAY']:checked").val();
		obj["GHISO_BANGIAO"] = $("input[name='radGHISO_BANGIAO']:checked").val();
		obj["KHAMBENHID"] = this.opt.khambenhid;
		obj["DICHVUKHAMBENHID"] = this.opt.dichvukhambenhid;
		obj["MAUBENHPHAMID"] = this.opt.maubenhphamchaid;
		obj["KHOACD"] = this.opt.khoacd;
		obj["KHOATH"] = this.opt.khoath;
		var valid = that.validator.validateForm();
		if (valid) {
			var json_par = JSON.stringify(obj);
			var param = [ json_par ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01K007.I02", param.join('$'));
			if (result == 1) {
				if (isKyCaClick) {
					_caRpt('1');
					loadGridData();
				} else {
					DlgUtil.showMsg('Lưu thành công!', function() {}, 1200);
					loadGridData();
				}
			} else {
				DlgUtil.showMsg('Đã có lỗi xảy ra!', function() {});
			}
		}
	}
	function bindEvent() {
		$('input[type=radio][name=radPHOI]').change(function() {
			if ($("input[name='radPHOI']:checked").val() == "1") {
				$('#txtPHIMPHOI').show();
			} else {
				$('#txtPHIMPHOI').hide();
			}
		});
		$('input[type=radio][name=radPHIM]').change(function() {
			if ($("input[name='radPHIM']:checked").val() == "1") {
				$('#txtPHIMKHAC').show();
			} else {
				$('#txtPHIMKHAC').hide();
			}
		});
		$("#btnKyCa").bindOnce("click", function() {
			isKyCaClick = true;
			saveTruocMo(false);
		});
		$("#btnHuyCa").bindOnce("click", function() {
			if (v_truocmoid == '') {
				DlgUtil.showMsg('Bạn chưa chọn phiếu trong danh sách!', function() {});
				return;
			}
			_caRpt('2');
		});
		$("#btnInKy").bindOnce("click", function() {
			if (v_truocmoid == '') {
				DlgUtil.showMsg('Bạn chưa chọn phiếu trong danh sách!', function() {});
				return;
			}
			_exportKyCA();
		});
		$('#btnThemMoi').on('click', function(e) {
			FormUtil.clearForm("divBKTC", "");
			var timeStart = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			$('#txtNGAYTHUCHIEN').val(timeStart);
			$('#txtGIODUADIMO').val(timeStart);
			loadCbiTruocmo();
		});
		$('#btnLuu').on('click', function(e) {
			isKyCaClick = false;
			saveTruocMo(false);
		});
		$('#btnIn').on('click', function(e) {
			if (v_truocmoid == '') {
				DlgUtil.showMsg('Bạn chưa chọn phiếu trong danh sách!', function() {});
				return;
			}
			var par = [ {
				name : 'TRUOCMO_ID',
				type : 'String',
				value : v_truocmoid
			}, {
				name : 'khambenhid',
				type : 'String',
				value : opt.khambenhid
			}, {
				name : 'dichvukhambenhid',
				type : 'String',
				value : opt.dichvukhambenhid
			} ];
			openReport('window', 'NTU007_CBI_TRUOCMO', 'pdf', par);
		});
		$("#btnDong").on("click", function(e) {
			parent.DlgUtil.close("divDlgCbiTruocmo");
		});
		$("#btnEDITCHANDOANRAVIENKT").on("click", function(e) {
			var myVar = {
				benhphu : $('#txtCHANDOANRAVIEN_KEMTHEO').val(),
				chandoan_kt_bd : ""
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 500);
			DlgUtil.open("dlgBPKT");
		});
		EventUtil.setEvent("chinhsua_benhphu", function(e) {
			$('#txtCHANDOANRAVIEN_KEMTHEO').val(e.benhphu);
			DlgUtil.close("dlgBPKT");
		});
		$('#btnCLEARCHANDOANRAVIEN').on("click", function() {
			$('#txtCHANDOANRAVIEN_KEMTHEO').val('');
			$('#txtMACHANDOANRAVIEN_KEMTHEO').val('');
		});
		$("#btnLuuMauKB").on("click", function(e) {
			if ($("#txtTEN_MAU_KHAMBENH").val().trim() == "") {
				DlgUtil.showMsg("Tên mẫu không được để trống");
				$('#txtTEN_MAU_KHAMBENH').focus();
				return false;
			}
			var obj = new Object();
			FormUtil.setFormToObject("divBKTC", "", obj);
			obj["NGAYTHUCHIEN"] = $("#txtNGAYTHUCHIEN").val();
			obj["VSTG"] = $("input[name='radVSTG']:checked").val();
			obj["SONMONG"] = $("input[name='radSONMONG']:checked").val();
			obj["CATMONG"] = $("input[name='radCATMONG']:checked").val();
			obj["TRANGSUC"] = $("input[name='radTRANGSUC']:checked").val();
			obj["RANGGIA"] = $("input[name='radRANGGIA']:checked").val();
			obj["QUANAO"] = $("input[name='radQUANAO']:checked").val();
			obj["VSR"] = $("input[name='radVSR']:checked").val();
			obj["VSDVC"] = $("input[name='radVSDVC']:checked").val();
			obj["BANG"] = $("input[name='radBANG']:checked").val();
			obj["HSBA"] = $("input[name='radHSBA']:checked").val();
			obj["XN"] = $("input[name='radXN']:checked").val();
			obj["DIEN"] = $("input[name='radDIEN']:checked").val();
			obj["PHIMPHOI"] = $("#txtPHIMPHOI").val();
			obj["PHIMKHAC"] = $("#txtPHIMKHAC").val();
			obj["GMHS"] = $("input[name='radGMHS']:checked").val();
			obj["NHACNB"] = $("#txtNHACNB").val();
			obj["THUOCTIENME"] = $("#txtTHUOCTIENME").val();
			obj["GIODUADIMO"] = $("#txtGIODUADIMO").val();
			obj["TIENSUDIUNG"] = $("#txtTIENSUDIUNG").val();
			obj["MACHANDOANRAVIEN_KEMTHEO"] = $("#txtMACHANDOANRAVIEN_KEMTHEO").val();
			obj["CHANDOANRAVIEN_KEMTHEO"] = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
			obj["GHICHU_BENHPHU"] = $("#txtGHICHU_BENHPHU").val();
			obj["MACHANDOANRAVIEN"] = $("#txtMACHANDOANRAVIEN").val();
			obj["CHANDOANRAVIEN"] = $("#txtCHANDOANRAVIEN").val();
			obj["GHICHU_BENHCHINH"] = $("#txtGHICHU_BENHCHINH").val();
			obj["KS"] = $("#txtKS").val();
			obj["HST"] = $("#txtHST").val();
			obj["BC"] = $("#txtBC").val();
			obj["HC"] = $("#txtHC").val();
			obj["US"] = $("#txtUS").val();
			obj["MD"] = $("#txtMD").val();
			obj["MC"] = $("#txtMC").val();
			obj["NHOMMAU"] = $("#txtNHOMMAU").val();
			obj["CANNANG"] = $("#txtCANNANG").val();
			obj["CHIEUCAO"] = $("#txtCHIEUCAO").val();
			obj["NHIPTHO"] = $("#txtNHIPTHO").val();
			obj["HUYETAP"] = $("#txtHUYETAP").val();
			obj["NHIETDO"] = $("#txtNHIETDO").val();
			obj["MACH"] = $("#txtMACH").val();
			obj["TEN_MAU_KHAMBENH"] = $("#txtTEN_MAU_KHAMBENH").val();
			/*objData["KHOAID"] = that.opt.khoaId;
			objData["PHONGID"] = that.opt.phongId;*/
			var json_par = JSON.stringify(obj);
			var param = [ json_par ];
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01K007.I09", param.join('$'));
			if (ret == 1) {
				DlgUtil.showMsg("Lưu mẫu thành công", function() {}, 1200);
				loadMauKB();
			} else {
				DlgUtil.showMsg("Lưu mẫu không thành công");
			}
		});
		$("#btnXoaMauKB").on("click", function(e) {
			if (confirm("Bạn có thực sự muốn xóa")) {
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU07K002.MAUTMO_DEL", $("#cboMAU_KB").val());
				if (ret == 1) {
					DlgUtil.showMsg("Xóa mẫu khám bệnh thành công", function() {}, 1200);
					loadMauKB();
				} else {
					DlgUtil.showMsg("Xóa mẫu khám bệnh không thành công");
				}
			}
		});
		$("#cboMAU_KB").on("change", function(e) {
			loadCbiTruocmo();
			var _id = $("#cboMAU_KB").val();
			if (_id > 0) {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU07K002.MAUTMO_SEL", _id);
				if (data_ar != null && data_ar.length > 0) {
					var obj = data_ar[0];
					
					//L2PT-90985
					if(cf.CBTM_NOT_LUUMAU != '0'){
						var lstItem = cf.CBTM_NOT_LUUMAU.split(',');
						if(lstItem.length > 0){
							for(var i=0; i<lstItem.length; i++){
								delete obj[lstItem[i]];
							}
						}
					}
					
					FormUtil.setObjectToForm("divBKTC", "", obj);
					var PHOI = 0;
					var PHIM = 0;
					if (obj.PHIMPHOI != '0') {
						PHOI = 1;
					} else {
						PHOI = 2;
					}
					if (obj.PHIMKHAC != '0') {
						PHIM = 1;
					} else {
						PHIM = 2;
					}
					$('#divBKTC input[name=radVSTG][value=' + obj.VSTG + ']').prop('checked', true);
					$('#divBKTC input[name=radSONMONG][value=' + obj.SONMONG + ']').prop('checked', true);
					$('#divBKTC input[name=radCATMONG][value=' + obj.CATMONG + ']').prop('checked', true);
					$('#divBKTC input[name=radTRANGSUC][value=' + obj.TRANGSUC + ']').prop('checked', true);
					$('#divBKTC input[name=radRANGGIA][value=' + obj.RANGGIA + ']').prop('checked', true);
					$('#divBKTC input[name=radQUANAO][value=' + obj.QUANAO + ']').prop('checked', true);
					$('#divBKTC input[name=radVSR][value=' + obj.VSR + ']').prop('checked', true);
					$('#divBKTC input[name=radVSDVC][value=' + obj.VSDVC + ']').prop('checked', true);
					$('#divBKTC input[name=radBANG][value=' + obj.BANG + ']').prop('checked', true);
					$('#divBKTC input[name=radHSBA][value=' + obj.HSBA + ']').prop('checked', true);
					$('#divBKTC input[name=radXN][value=' + obj.XN + ']').prop('checked', true);
					$('#divBKTC input[name=radDIEN][value=' + obj.DIEN + ']').prop('checked', true);
					$('#divBKTC input[name=radGMHS][value=' + obj.GMHS + ']').prop('checked', true);
					$('#divBKTC input[name=radPHOI][value=' + PHOI + ']').prop('checked', true);
					$('#divBKTC input[name=radPHIM][value=' + PHIM + ']').prop('checked', true);
					checkphim();
				}
				$("#btnXoaMauKB").prop('disabled', false);
			} else
				$("#btnXoaMauKB").prop('disabled', true);
		});
		GridUtil.setGridParam(_gridPhieuTruocMo, {
			onSelectRow : function(id) {
				GridUtil.unmarkAll(_gridPhieuTruocMo);
				GridUtil.markRow(_gridPhieuTruocMo, id);
				if (id) {
					var _row = $("#" + _gridPhieuTruocMo).jqGrid('getRowData', id);
					//v_PhieuTruocMoid = _row.PhieuTruocMoID;
					_khambenhid = _row.KHAMBENHID;
					_khoacd = _row.KHOAID;
					_dichvukhambenhid = _row.DICHVUKHAMBENHID;
					v_truocmoid = _row.TRUOCMO_ID;
					loadCbiTruocmo(v_truocmoid);
					tcmo = _row.TRUOCMO_ID;
				}
			},
			gridComplete : function(data) {
				var ids = $("#" + _gridPhieuTruocMo).getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = $("#" + _gridPhieuTruocMo).jqGrid('getRowData', id);
					var _icon = '';
					if (row.FLAG_CA == '1') {
						_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
					}
					$("#" + _gridPhieuTruocMo).jqGrid('setCell', id, 'ICON', _icon);
				}
				//HaNv_141122: L2PT-28312
				$("#" + _gridPhieuTruocMo).setSelection(1);
			}
		});
		$('#btnXoa').on('click', function(e) {
			if (v_truocmoid == '') {
				DlgUtil.showMsg('Bạn chưa chọn phiếu trong danh sách!', function() {});
				return;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa phiếu này ko ?", function(flag) {
				if (flag) {
					var par = v_truocmoid;
					var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU007.09", par);
					if (_result == 1) {
						DlgUtil.showMsg("Xóa thông tin thành công!", function() {}, 1200);
						loadGridData();
						v_truocmoid = '';
					} else {
						DlgUtil.showMsg("Xóa thông tin không thành công! ");
					}
				}
			});
		});
	}
	function loadGridData() {
		var _sql_par = [ {
			"name" : "[0]",
			"value" : opt.hosobenhanid
		} ];
		GridUtil.loadGridBySqlPage(_gridPhieuTruocMo, "NTU007.10", _sql_par);
	}
	function loadMauKB() {
		var sql_par = [];
		ComboUtil.getComboTag("cboMAU_KB", "NTU07K002.MAUTMO_LST", sql_par, "", {
			value : '-1',
			text : '---Chọn mẫu---'
		}, 'sql');
	}
	function doLoadCombo(_txt, _txtDst) {
		var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		var _selfnc = function(event, ui) {
			$("#" + _txt).val(ui.item.ICD10CODE);
			$("#" + _txtDst).val(ui.item.ICD10NAME);
			return false;
		};
		ComboUtil.initComboGrid(_txt, 'CG.ICD10', [], "600px", _col, _selfnc);
	};
	function doLoadComboKemTheo(_txt, _txtDst) {
		var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		var _selfnc = function(event, ui) {
			$("#" + _txt).val(ui.item.ICD10CODE);
			$("#" + _txtDst).val($("#" + _txtDst).val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#" + _txtDst).val() + ";" + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			return false;
		};
		ComboUtil.initComboGrid(_txt, 'CG.ICD10', [], "600px", _col, _selfnc);
	};
	// Xử lý sự kiện liên quan ký CA => START
	function _caRpt(signType) {
		var _params = _getKyCaParam();
		CommonUtil.kyCA(_params, signType, true);
		EventUtil.setEvent("eventKyCA", function(e) {
			DlgUtil.showMsg(e.res);
			_checkKyCA();
		});
	}
	function _exportKyCA() {
		var _params = _getKyCaParam();
		CommonUtil.openReportGetCA2(_params, false);
	}
	function _checkKyCA() {
		var _params = _getKyCaParam();
		var _check = CommonUtil.checkKyCaByParam(_params);
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('EMR.GET.CHKC', 'NTU007_CBI_TRUOCMO');
		if (data_ar != null && data_ar.length > 0) {
			if (_check >= data_ar[0].SOLUONGKY) {
				$("#btnKyCa").prop("disabled", true);
				$("#btnLuu").prop("disabled", true);
			} else {
				$("#btnKyCa").prop("disabled", false);
				$("#btnLuu").prop("disabled", false);
			}
		}
	}
	function _getKyCaParam() {
		var _params = [ {
			name : 'hosobenhanid',
			type : 'String',
			value : opt.hosobenhanid
		}, {
			name : 'TRUOCMO_ID',
			type : 'String',
			value : v_truocmoid
		}, {
			name : 'khambenhid',
			type : 'String',
			value : opt.khambenhid
		}, {
			name : 'dichvukhambenhid',
			type : 'String',
			value : opt.dichvukhambenhid
		}, {
			name : 'RPT_CODE',
			type : 'String',
			value : 'NTU007_CBI_TRUOCMO'
		} ];
		return _params;
	}
	// Xử lý sự kiện liên quan ký CA => END
}