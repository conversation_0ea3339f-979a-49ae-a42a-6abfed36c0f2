(function($) {
	var _checkKy = '';
	var isKySo = false;
	var sign_type = '';
	var causer = -1;
	var capassword = -1;
	var smartcauser = -1;
	var _rptKyCa = '';
	var hospital_id = -1;
	var showAllDVKT = false; //L2PT-20786
	var cfObj = new Object();
	$.widget("ui.ntu02d025_cdha", {
		//Options to be used as defaults
		options : {
			_gridCDHA : 'grdCDHA',
			_gridCDHADetail : 'grdCDHAChiTiet',
			_khambenhid : "",
			_benhnhanid : "",
			_lnmbp : "",
			_modeView : "0", // =1 chi view; !=1 la update
			_hosobenhanid : "",
			_deleteDV : "0",
			_studyInstanceUID : "",
			_modePrint : "0", //Begin_HaNv_28052018: Them bien de hien thi menu in phieu - 1: Show toolbar inPhieu
			_modeTiepDon : "0", //Begin_HaNv_26072018: <PERSON>hap benh nhan tu khu tiep don - 1: full quyen - khong can qua ham _checkRoles
			_formCall : "", //Begin_HaNv_26032020: Xác định mã màn hình cha gọi đến widget để tùy biến - L2PT-18728
			checkLoad : false
		},
		containerId : '',
		//Setup widget (eg. element creation, apply theming
		// , bind events etc.)
		_create : function() {
			// _create will automatically run the first time this widget is called. Put the initial widget  setup code here, then you can access the element
			// on which the widget was called via this.element. The options defined above can be accessed via this.options this.element.addStuff();
			console.log('_create');
			this.containerId = $(this.element).attr('id');
			this._initWidget();
		},
		_t : function(_id) {
			var newId = '';
			if (_id.indexOf("=") > 0) {
				newId = _id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi, _self.containerId + "$1");
			} else if (_id.indexOf("#") == 0) {
				newId = _id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi, "$1" + _self.containerId + "$2");
			} else {
				newId = _self.containerId + _id;
			}
			return newId;
		},
		_initWidget : function() {
			var _self = this;
			_self.options.checkLoad = false;
			$(_self.element).load('../noitru/NTU02D025_ThongTinCDHA.tpl?v=1', function() {
				$(_self.element).find("[id]").each(function() {
					if (this.id == "pager_" + _self.options._gridCDHA) {
						this.id = "pager_" + _self.containerId + _self.options._gridCDHA;
					} else if (this.id == "pager_" + _self.options._gridCDHADetail) {
						this.id = "pager_" + _self.containerId + _self.options._gridCDHADetail;
					} else {
						this.id = _self.containerId + this.id;
					}
				})
				//L2PT-25016 start
				var ch_checkbox = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', "HIS_CHECKBOX_ALLDVKT");
				if (ch_checkbox == '1') {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", true);
				}
				//L2PT-25016 end
				//$("[data-i18n]").i18n();
				_self._loadData();
				_self._bindEvent();
				$('#divMain').css('height', $(window).height() + 150);
			});
		},
		_loadData : function() {
			var _self = this;
			var _look_sql = "NT.024.DSPHIEUCLS";
			var _gridHeader = " ,ICON,30,0,ns,l; ,ICON2,30,0,ns,l;Mức chờ CLS,ICON3,90,0,ns,l;Số phiếu,SOPHIEU,110,0,f,f;Phiếu điều trị,PHIEU_DTRI,110,0,f,l;Bác sỹ chỉ định,NGUOITAO,140,0,f,l;"
					+ "Bác sỹ thực hiện,NGUOITRAKETQUA,140,0,f,l;Thời gian chỉ đinh,NGAYMAUBENHPHAM,115,0,f,l;P. Thực hiện,PHONGCHIDINH,200,0,f,l;"
					+ "STT,SOTHUTUCHIDINH,100,0,f,l;Phòng,PHONGDIEUTRI,180,0,f,l;Khẩn,KHANCAP,120,0,f,l;Trạng thái,TRANGTHAI_PHIEU,95,0,f,l;"
					+ "Người tạo phiếu,NGUOITAO_CLS,120,0,f,l;TIEPNHANID,TIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
					+ "TRANGTHAIMAUBENHPHAM,TRANGTHAIMAUBENHPHAM,0,0,t,l;MABENHNHANa,MABENHNHAN,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;"
					+ "KHAMBENHID,KHAMBENHID,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;"
					+ "CHITIETGHICHU,CHITIETGHICHU,0,0,t,l;PHIEUDIEUTRIID,PHIEUDIEUTRIID,0,0,t,l;NGUOITAOCLS_ID,NGUOITAOCLS_ID,0,0,t,l;FLAG_CA,FLAG_CA,0,0,t,l;"
					+ "KHOADIEUTRI,KHOADIEUTRI,0,0,t,l;LOAI_PHONG_CANHBAO_ID,LOAI_PHONG_CANHBAO_ID,0,0,t,l";
			var _gridHeaderDetail = " ,ICON,30,0,ns,l; ,VIEW,30,0,f,l,ES;PARAM_HASHED,PARAM_HASHED,0,0,t,l;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;"
					+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;DICHVUTHUCHIENID,DICHVUTHUCHIENID,0,0,t,l;KETQUACLS,KETQUACLS,0,0,t,l;"
					+ "KETQUACLSID,KETQUACLSID,0,0,t,l;Mã DV,MADICHVU,100,0,f,l;Tên dịch vụ,TENDICHVU,250,0,f,l;Loại thanh toán,LOAITT,120,0,f,l;"
					+ "Số lượng,SOLUONG,100,0,f,l;Mô tả,KETQUACLS,30,html,f,l;Trạng thái,TENTRANGTHAI,150,0,f,l;Kết luận,GIATRI_KETQUA,280,0,f,l;Thuốc đi kèm,THUOC_DIKEM,120,0,f,l;"
					+ "Vật tư đi kèm,VATTU_DIKEM,120,0,f,l;Ghi chú KQ,GHICHUKQ,260,0,f,l;BS thực hiện,BACSITHUCHIEN,150,0,f,l;"
					+ "GHICHU2,GHICHU2,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;MANHOM,MANHOM,0,0,t,l;TG trả KQ,THOIGIANTRAKETQUA1,120,0,f,l;"
					+ "Ghi chú CĐ,GHICHU1,270,0,f,l;LINK_DICOM,LINK_DICOM,0,0,t,l;VIEW_CLS,VIEW_CLS,0,0,t,l";
			//Beg_HaNv_021021: Điều chỉnh widget - BVTM-5952
			var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "OPT_HOSPITAL_ID;SHOW_COL_BACSITH;PHONG_TUDONG_IN;"
					+ "HIS_SUDUNG_KYSO_KYDIENTU;HIS_SHOW_TOOLBAR_PHUTHU;NTU_ALLOW_HUYDV_TAMUNG;HIS_BACSY_YCHUY_DV;KBH_TATTHONGBAO_KBHB;"
					+ "BM2_NOTALLOW_XOA_CLS;NTU_SAVEOK_NOTMSG;XOA_CAPNHAT_PHIEU_KOCANHUY;HIS_CHECK_DIEUTRI_KISO;CDDV_USING_REPORT_CODE;"
					+ "HIS_FILEEXPORT_TYPE;CDDV_GIAODIEN_KHA;CDHA_HIDE_INKETQUA;CDHA_SHOW_SUA_PTH;NTU_CDHA_CHECKXOA_USER;CDHA_ALLOW_XOADV;"
					+ "CLS_COPY_NOCHECK_ROLE;CDDV_MANHOM_DV_HL7;RIS_DICOM_ID_TYPE;NTU_ALL_ACCESS_WIDGET;RIS_DICOM_LINK_TYPE;INCLS_KSK_THEO_DOAN;"
					+ "NTU_BM2_12397_INTD;INPHIEU_THEO_DV;HIS_SHOWALL_DVKT;WGCLS_SHOW_TOOLBAR_INPHIEU;CDHA_SHOW_INVIETTAY;NTU02D170_FORMPHIEU_CDHA_CHT;"
					+ "NTU_HUY_CHECK_DUYET_MP;RIS_DICOM_HL7_VNPT;"
					+ "WGCLS_HIDE_COPY;HIDE_MENU_INPHIEU_CLS_OLD;CLS_INBARCODE;WGCLS_SHOW_VIEW_KQ;NTU_DTKH_ALLOW_UPDATE_DVKT;"
					+ "HIS_KYSO_WIDGET_CĐHA_ALL;HIS_KYSO_WIDGET_CĐHA_RPT;WGCLS_SAVE_VIEWKQ");
			if (config_ar != null && config_ar.length > 0) {
				cfObj = config_ar[0];
			} else {
				DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
				return;
			}
			hospital_id = cfObj.OPT_HOSPITAL_ID;
			_rptKyCa = cfObj.HIS_KYSO_WIDGET_CĐHA_RPT;
			//End_HaNv_021021
			//Beg_HaNv_211022: Cấu hình GridHeader - L2PT-27795
			var dtMenu = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', 'NTU02D025_GRDCDHA');
			if (dtMenu != undefined && dtMenu != 0 && dtMenu.length > 100) {
				_gridHeader = dtMenu;
			}
			var dtMenuCt = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', 'NTU02D025_GRDCDHACHITIET');
			if (dtMenuCt != undefined && dtMenuCt != 0 && dtMenuCt.length > 100) {
				_gridHeaderDetail = dtMenuCt;
				_self._gridHeaderDetail = dtMenuCt;
			}
			//End_HaNv_211022
			// L2PT-20786 ttlinh start
			if (_self.options._formCall != '' && cfObj.HIS_SHOWALL_DVKT == '1' && "NTU_BDT,NTU_PHC,KB_MHC,TN_NGT1,TN_NGT2,".includes(_self.options._formCall + ',')) {
				showAllDVKT = true;
				$('#' + _self.containerId + 'divAllKhoa').show();
			}
			var _group_khoa = {
				groupField : [ 'KHOADIEUTRI' ],
				groupColumnShow : [ false ],
				groupText : [ '<b>{0}</b>' ]
			};
			if (cfObj.HIS_KYSO_WIDGET_CĐHA_ALL == '1') {
				if (showAllDVKT) {
					GridUtil.initGroup(_self.containerId + _self.options._gridCDHA, "100%", "145px", 'Danh sách chẩn đoán hình ảnh', true, _group_khoa, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					//HaNv_120921: L2PT-7213
					});
				} else {
					GridUtil.init(_self.containerId + _self.options._gridCDHA, "100%", "145px", 'Danh sách chẩn đoán hình ảnh', true, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					});//HaNv_05072019: L2PT-6559
				}
			} else {
				if (showAllDVKT) {
					GridUtil.initGroup(_self.containerId + _self.options._gridCDHA, "100%", "145px", 'Danh sách chẩn đoán hình ảnh', false, _group_khoa, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					//HaNv_120921: L2PT-7213
					});
				} else {
					GridUtil.init(_self.containerId + _self.options._gridCDHA, "100%", "145px", 'Danh sách chẩn đoán hình ảnh', false, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					});//HaNv_05072019: L2PT-6559
				}
			}
			// L2PT-20786 end
			var hight = "250px";//HaNv_081222: L2PT-29559
			if (hospital_id == '1133') {
				hight = "150px";
			}
			GridUtil.init(_self.containerId + _self.options._gridCDHADetail, "100%", hight, 'Danh sách kết quả chẩn đoán hình ảnh', false, _gridHeaderDetail, false, {
				rowNum : 10,
				rowList : [ 10, 20, 30 ]
			});
			$("#" + _self.containerId + _self.options._gridCDHADetail)[0].toggleToolbar();
			//Begin_HaNv_16032018: cấu hình ẩn/hiện cột bác sĩ thực hiện trên danh sách mbp
			var showBacSiTh = cfObj.SHOW_COL_BACSITH;
			if (showBacSiTh == '0') {
				$('#' + _self.containerId + _self.options._gridCDHA).jqGrid('hideCol', 'NGUOITRAKETQUA');
			}
			//End_HaNv_16032018
			//Beg_HaNv_300521: BVTM-3035
			if (hospital_id != '10284') {
				$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('hideCol', 'THUOC_DIKEM');
				$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('hideCol', 'VATTU_DIKEM');
				$('#' + _self.containerId + _self.options._gridCDHA).jqGrid('hideCol', 'ICON3'); //L2PT-22465
				$('#' + _self.containerId + 'divNote').remove();
			}
			//End_HaNv_300521
			//L2PT-22465 start
			if (hospital_id == '10284' && (_self.options._formCall == '' || !"NTU_BDT,KB_MHC,".includes(_self.options._formCall + ','))) {
				$('#' + _self.containerId + _self.options._gridCDHA).jqGrid('hideCol', 'ICON3'); //L2PT-22465
			}
			//L2PT-22465 end
			//Beg_DoanPV_20220111: L2PT-12778: Ẩn tạo bản sao với BND, NhiHDG (L2PT-30146)
			if ([ "951", "957" ].includes(hospital_id + "") || cfObj.WGCLS_HIDE_COPY == '1') {
				$('#' + _self.containerId + 'copyNote').remove();
			}
			var _sql_par = [];
			// L2PT-20786 ttlinh start
			if (showAllDVKT) {
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, $('#hidHOSOBENHANID').val() ]);
				} else {
					_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, -1 ]);
				}
			} else {
				_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
			}
			// L2PT-20786 end
			GridUtil.loadGridBySqlPage(_self.containerId + _self.options._gridCDHA, _look_sql, _sql_par);
			loadRISConfig();
			var _tudongin = '0';
			var _data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH_ND", 'NGT_PHIEUKHAM_TUDONGIN');
			if (_data_ar != null && _data_ar.length > 0) {
				_tudongin = _data_ar[0].NGT_PHIEUKHAM_TUDONGIN;
			}
			var dc_phong = cfObj.PHONG_TUDONG_IN;
			var dc_phongs = dc_phong.split(',');
			if (_tudongin != '1' || !$.inArray($('#hidPHONGID').val(), dc_phongs) >= 0) {
				$('#' + _self.containerId + 'printAuto').remove();
				$('#' + _self.containerId + 'printKQAuto').remove();
				$('#' + _self.containerId + 'printViewAuto').remove();
				$('#' + _self.containerId + 'printKQViewAuto').remove();
			}
			// Xử lý sự kiện liên quan ký CA => START
			_checkKy = cfObj.HIS_SUDUNG_KYSO_KYDIENTU;
			if (_checkKy != "1") {
				$('#' + _self.containerId + 'GROUP4').remove();
				$('#' + _self.containerId + 'kyCA').remove();
				$('#' + _self.containerId + 'printKyCA').remove();
				$('#' + _self.containerId + 'printSelectedCASigned').remove();
				$('#' + _self.containerId + 'printKyCAKQ').remove();
				$('#' + _self.containerId + 'printKyCA2').remove();
				$('#' + _self.containerId + 'printSelectedCASigned2').remove();
				$('#' + _self.containerId + 'printKyCAKQ2').remove();
				$('#' + _self.containerId + 'huyKyCA').remove();
				$("#" + _self.containerId + _self.options._gridCDHA).hideCol('ICON2');
			}
			// Xử lý sự kiện liên quan ký CA => END
			if (cfObj.NTU_BM2_12397_INTD != "1") {
				$('#' + _self.containerId + 'printCDHA').remove();
				$('#' + _self.containerId + 'printViewCDHA').remove();
			}
			//Beg_HaNv_120123: bổ sung menu In phiếu CĐHA viết tay - L2PT-33633
			if (cfObj.CDHA_SHOW_INVIETTAY != "1") {
				$('#' + _self.containerId + 'printKQVT').remove();
			}
			//End_HaNv_120123
			//Beg_HaNv_131023: Inbarcode CLS - L2PT-53101
			if (cfObj.CLS_INBARCODE != "1") {
				$('#' + _self.containerId + 'prinBarcode').remove();
			}
			//End_HaNv_131023
			//Begin_HaNv_10052018: Hiển thị toolbar phụ thu theo cấu hình L2DKBD-1230
			var toolbar_ar = [
			//Begin_HaNv_28052018: Hiển thị toolbar in phiếu L2DKHN-777
			{
				type : 'buttongroup',
				id : 'btnInPhieu',
				icon : 'print',
				text : ' In phiếu',
				hlink : '#',
				children : [ {
					id : 'btnInPhieu19BV01',
					icon : 'print',
					text : 'Phiếu Chụp X-Quang',
					hlink : '#'
				}, {
					id : 'btnInPhieu20BV01',
					icon : 'print',
					text : 'Phiếu Chụp Chụp Cắt Lớp',
					hlink : '#'
				}, {
					id : 'btnInPhieu21BV01',
					icon : 'print',
					text : 'Phiếu Chụp Cộng Hưởng Từ',
					hlink : '#'
				}, {
					id : 'btnPhieuIn27BV01TEST_TAMLY',
					icon : 'print',
					text : 'Phiếu test tâm lý',
					hlink : '#'
				},// nghiant HISL2TK-571
				{
					id : 'btnPhieuIn27BV01TV_TAMLY',
					icon : 'print',
					text : 'Phiếu tham vấn tâm lý',
					hlink : '#'
				},// nghiant HISL2TK-571
				{
					id : 'btnInPhieu22BV01',
					icon : 'print',
					text : 'Phiếu Siêu Âm',
					hlink : '#'
				}, {
					id : 'btnInPhieu27BV01',
					icon : 'print',
					text : 'Phiếu Siêu Âm Màu',
					hlink : '#'
				}, {
					id : 'btnInPhieu31BV01',
					icon : 'print',
					text : 'Phiếu Siêu Âm DOPPLER',
					hlink : '#'
				}, {
					id : 'btnInPhieu23BV01',
					icon : 'print',
					text : 'Phiếu Điện Tim',
					hlink : '#'
				}, {
					id : 'btnInPhieu24BV01',
					icon : 'print',
					text : 'Phiếu Điện Não',
					hlink : '#'
				}, {
					id : 'btnInPhieu25BV01',
					icon : 'print',
					text : 'Phiếu Nội Soi',
					hlink : '#'
				}, {
					id : 'btnInPhieuTMH74',
					icon : 'print',
					text : 'Phiếu Nội Soi Tai mũi họng',
					hlink : '#'
				}, {
					id : 'btnInPhieuNSPQ74',
					icon : 'print',
					text : 'Phiếu Nội Soi Phế quản',
					hlink : '#'
				}, {
					id : 'btnInPhieu26BV01',
					icon : 'print',
					text : 'Phiếu Đo Chức Năng Hô Hấp',
					hlink : '#'
				}, {
					id : 'btnInPhieu28BV01',
					icon : 'print',
					text : 'Phiếu Đo Thị Trường Trung Tâm',
					hlink : '#'
				}, {
					id : 'btnInPhieu29BV01',
					icon : 'print',
					text : 'Phiếu Chụp Đáy Mắt Không Huỳnh Quang',
					hlink : '#'
				}, {
					id : 'btnInPhieu30BV01',
					icon : 'print',
					text : 'Phiếu Kết Quả Đo JAVAL',
					hlink : '#'
				}, {
					id : 'btnInPhieu32BV01',
					icon : 'print',
					text : 'Phiếu Kết Quả Điện cơ',
					hlink : '#'
				} ]
			}, {
				type : 'buttongroup',
				id : 'btnhospitalfee',
				icon : 'open',
				text : 'Phụ thu',
				hlink : '#',
				children : [ {
					id : 'hospitalfee_1',
					icon : 'open',
					text : 'Tạo phiếu phụ thu',
					hlink : '#'
				}, {
					id : 'hospitalfee_2',
					icon : 'open',
					text : 'Danh sách phiếu phụ thu',
					hlink : '#'
				} ]
			} ];
			if (_self.options._modePrint == "1" || cfObj.WGCLS_SHOW_TOOLBAR_INPHIEU == '1') {
				//Beg_HaNv_220623: gen In Phiếu giống bên CLS - L2PT-46443
				if (cfObj.HIDE_MENU_INPHIEU_CLS_OLD == '1') {
					toolbar_ar[0].children = [];
				}
				var menus = jsonrpc.AjaxJson.ajaxCALL_SP_O("TOOLBAR_CLS_INPHIEU", '2');
				if (menus && menus.length > 0) {
					var childsInPhieuNew = menus.map(function(item) {
						var optionalParam = {};
						try {
							optionalParam = JSON.parse($("<div/>").html(item.OPTIONAL_PARAM).text());
						} catch (e) {
							optionalParam = {}
						}
						return {
							id : 'btnInPhieu_new_' + item.ID,
							icon : 'print',
							text : item.TEXT,
							order : parseInt(item.ORDER),
							hlink : '#',
							dataExternal : {
								report_code : item.REPORT_CODE,
								file_format : item.FILE_FORMAT,
								image_sql : item.IMAGES_CTL_SQL,
								image_count : item.IMAGES_COUNT,
								innhieu_phieu : item.INNHIEU_PHIEU,
								optional_param : optionalParam
							}
						}
					})
					toolbar_ar[0].children = toolbar_ar[0].children.concat(childsInPhieuNew);
				}
				//End_HaNv_220623
				toolbar = ToolbarUtil.build(_self.containerId + 'toolbarId', toolbar_ar);
				$("#" + _self.containerId + 'toolbarIdbtnInPhieu').attr("disabled", true);
				if (cfObj.HIS_SHOW_TOOLBAR_PHUTHU != 1) {
					$("#" + _self.containerId + 'toolbarIdbtnhospitalfee').hide();
				}
			} else {
				if (cfObj.HIS_SHOW_TOOLBAR_PHUTHU == 1) {
					toolbar = ToolbarUtil.build(_self.containerId + 'toolbarId', toolbar_ar);
					$("#" + _self.containerId + 'toolbarIdbtnInPhieu').closest("div").removeClass('wd100');
					$("#" + _self.containerId + 'toolbarIdbtnInPhieu').hide();
				} else {
					$("#" + _self.containerId + 'toolbarId').hide();
				}
			}
			//End_HaNv_28052018
			//End_HaNv_10052018
			//Begin_HaNv_05072019: Quy trình hoàn hủy với bệnh nhân đóng tạm ứng - L1PT-1245
			if (cfObj.NTU_ALLOW_HUYDV_TAMUNG == "1") {
				$('#' + _self.containerId + 'deleteRequest').remove();
			}
			//End_HaNv_05072019
			//Begin_HaNv_24032020: Bỏ quyền in kết quả CDHA theo cấu hình - L2PT-18522
			if (cfObj.CDHA_HIDE_INKETQUA == "1") {
				$('#' + _self.containerId + 'printKQ').remove();
				$('#' + _self.containerId + 'printKQView').remove();
			}
			//End_HaNv_24032020
			//Begin_HaNv_26032020: Xác định mã màn hình cha gọi đến widget để tùy biến - L2PT-18728
			if (_self.options._formCall != "KB_MHC") {
				$('#' + _self.containerId + 'GROUP2_PRINT').remove();
				$('#' + _self.containerId + 'editPhieuDTPrint').remove();
			}
			//End_HaNv_26032020
			//Begin_HaNv_17102020: [LSO-DK] bỏ bớt các phân hệ in kết quả cdha trong tra cứu bệnh nhân - L2PT-29033
			var hopital = cfObj.OPT_HOSPITAL_ID;
			if (_self.options._formCall == "NTU_TCBN" && hopital == 987) {
				$('#' + _self.containerId + 'toolbarIdbtnInPhieu19BV01').remove();
				$('#' + _self.containerId + 'toolbarIdbtnInPhieu20BV01').remove();
				$('#' + _self.containerId + 'toolbarIdbtnInPhieu21BV01').remove();
				$('#' + _self.containerId + 'toolbarIdbtnInPhieu22BV01').remove();
			}
			//End_HaNv_17102020
			//Beg_HaNv_021021: Điều chỉnh widget - BVTM-5952
			if (cfObj.HIS_BACSY_YCHUY_DV != "1") {
				$('#' + _self.containerId + 'sendRequestDeleteReject').remove();
				$('#' + _self.containerId + 'undoRequestDeleteReject').remove();
			}
			if (cfObj.CDHA_ALLOW_XOADV == "0") {
				$('#' + _self.containerId + 'sendRequestDV').remove();
				$('#' + _self.containerId + 'delRequestDV').remove();
				$('#' + _self.containerId + 'delDV').remove();
			} else if (cfObj.CDHA_ALLOW_XOADV == "1") {
				$('#' + _self.containerId + 'sendRequestDV').remove();
				$('#' + _self.containerId + 'delRequestDV').remove();
			}
			//End_HaNv_021021
			//Beg_HaNv_160123: Chặn in kết quả CLS tại khoa - L2PT-33201
			if (hospital_id == '1111' && (_self.options._formCall != '' && "NTU_BDT,KB_MHC,".includes(_self.options._formCall + ','))) {
				$('#' + _self.containerId + 'printKQ').remove();
			}
			//End_HaNv_160123
			//L2PT-36168
			if (cfObj.NTU02D170_FORMPHIEU_CDHA_CHT == '1') {
				$('#' + _self.containerId + 'contextMenuDVKTTDT ul').append(
						'<li id="' + _self.containerId + 'tamSoatChupCHT" class="menulevel2" style="margin: 0px; color: rgb(0, 0, 0); display: block; ' +
								'cursor: default; padding: 3px; border: 1px solid rgb(255, 255, 255); background-color: transparent;">' +
								'<span class="ui-icon ui-icon-print" style="float: left; margin-left: 15px;"></span> ' +
								'<span style="font-family: Verdana">BẢNG TẦM SOÁT CHỤP CỘNG HƯỞNG TỪ (MRI)</span> </li>');
			}
			//Beg_HaNv_131023: Chuyển thanh toán VP - L2PT-53262
			if (hospital_id != '10284' || _self.options._formCall != 'NTU02D175') {
				$('#' + _self.containerId + 'changeVP').remove();
			}
			//End_HaNv_131023
			//Beg_HaNv_271023: view kq cls - L2PT-57865
			if (cfObj.WGCLS_SHOW_VIEW_KQ == '0') {
				$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('hideCol', 'VIEW');
			}
			//End_HaNv_271023
		},
		_bindEvent : function() {
			var _self = this;
			GridUtil.setGridParam(_self.containerId + _self.options._gridCDHA, {
				onSelectRow : function(id, selected) {
					if (!selected) {
						return;
					}
					_self._viewCDHADetail(id);
					//Begin_HaNv_29112019: Fix lỗi hiển thị nút xem ảnh DICOM - L2PT-12329
					$('#' + _self.containerId + "btnDicomViewer").hide();
					$('#' + _self.containerId + "btnDicomViewer1").hide();//L2PT-80098
					//End_HaNv_29112019
					GridUtil.unmarkAll(_self.containerId + _self.options._gridCDHA);
					GridUtil.markRow(_self.containerId + _self.options._gridCDHA, id);
					var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', id);
					$("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
					//Begin_HaNv_28052018: Hiển thị toolbar in phiếu L2DKHN-777
					var trangthai = rowData.TRANGTHAIMAUBENHPHAM;
					if (trangthai == "2") {
						$("#" + _self.containerId + 'toolbarIdbtnInPhieu').attr("disabled", true);
					} else if (trangthai == "3" || trangthai == "4") {
						$("#" + _self.containerId + 'toolbarIdbtnInPhieu').attr("disabled", false);
					}
					//End_HaNv_28052018
					//Beg_HaNv_040324: Lưu thông tin người xem, ngày xem chỉ số KQCLS - L2PT-74410
					if (cfObj.WGCLS_SAVE_VIEWKQ == '1') {
						sql_par = [];
						sql_par.push({
							"name" : "[0]",
							"value" : rowData.MAUBENHPHAMID
						});
						jsonrpc.AjaxJson.execute("NTUD024.02", sql_par);
						$('#' + _self.containerId + _self.options._gridCDHADetail).trigger( 'reloadGrid' );
					}
					//End_HaNv_040324
				},
				ondblClickRow : function(id) {
					if (_self.options._modeView == "0") {
						_self._updatePhieuCDHA(id)
					}
				},
				gridComplete : function(id) {
					var rowCount = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("reccount");
					if (rowCount == 0) {
						return;
					}
					//hiển thị biểu tượng ký số/điện tử
					var ids = $("#" + _self.containerId + _self.options._gridCDHA).getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var row = $("#" + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', id);
						//set icon trang thai  benh nhan
						var _icon = '';
						// đã ký số
						if (row.FLAG_CA) {
							if (row.FLAG_CA == '1') {
								_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
							} else if (row.FLAG_CA == '99') {
								_icon = '<center><img src="../common/image/ca-3.png" width="15px"></center>';
							}
						}
						$("#" + _self.containerId + _self.options._gridCDHA).jqGrid('setCell', id, 'ICON2', _icon);
					}
					//L2PT-20786 start
					var modeView;
					if (showAllDVKT) {
						if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
							modeView = "1";
						} else {
							modeView = _self.options._modeView;
						}
					} else {
						modeView = _self.options._modeView;
					}
					//L2PT-20786 end
					if (modeView == "0") {
						//build menu
						$(".jqgrow", '#' + _self.containerId + _self.options._gridCDHA).contextMenu(_self.containerId + 'contextMenu', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHA);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridCDHA, rowId);
								return true;
							},
						});
						var showEditOrg = cfObj.CDHA_SHOW_SUA_PTH;
						if (showEditOrg == '1') {
							$("#" + _self.containerId + "editOrg").remove();
						}
						//xu ly su kien gui yeu cau
						$('#' + _self.containerId + 'sentRequest').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._sendRequest(rowKey);
						});
						//xu ly su kien xoa yeu cau
						$('#' + _self.containerId + 'deleteRequest').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowKey);
							if (rowData != null) {
								if (_checkKy == '1' && rowData.FLAG_CA == '1') {
									DlgUtil.showMsg("Phiếu đã ký số không được hủy yêu cầu!");
									return;
								}
							}
							_self._deleteRequest(rowKey);
						});
						//xu ly su kien xoa
						$('#' + _self.containerId + 'delete').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._xoaPhieuDichVu(rowKey);
						});
						//xoa cac dich vu khong co ket qua
						$('#' + _self.containerId + 'deleteAll').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._xoaPhieuDichVuKhongCoKetQua(rowKey);
						});
						//Sua phong thuc hien
						$('#' + _self.containerId + 'editOrg').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._editOrgDone(rowKey);
						});
						//cập nhật phiếu CDHA
						$('#' + _self.containerId + 'updateCDHA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._updatePhieuCDHA(rowKey)
						});
						//Tao ban sao
						$('#' + _self.containerId + 'copyNote').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._copyPhieuCDHA(rowKey);
						});
						//L2K74TW-301 - hongdq
						//cap nhat phieu dieu tri
						$('#' + _self.containerId + 'editPhieuDT').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._editPhieuDT(rowKey);
						});
						//L2K74TW-301 -hongdq
						//Begin_HaNv_06062020: Sửa bác sĩ chỉ định - L2PT-21679
						$('#' + _self.containerId + 'editBacSi').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowKey);
							if (rowData != null) {
								if (_checkKy == '1' && rowData.FLAG_CA == '1') {
									DlgUtil.showMsg("Phiếu đã ký số không được sửa gì hết, sửa gì nhiều thế!");
									return;
								}
							}
							_self._editBacSi(rowKey);
						});
						//End_HaNv_06062020
						//L2PT-119158
						$('#' + _self.containerId + 'CDVTHCLS').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowKey);
							if (rowData != null) {
								paramInput = {
										hosobenhanid : rowData.HOSOBENHANID,
										khambenhid : rowData.KHAMBENHID,
										maubenhphamid:rowData.MAUBENHPHAMID
									};
									dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuCDVTHCLS", "divDlg", "manager.jsp?func=../noitru/NTU_PhieuChuyenThucHienDichVu", paramInput, "Phiếu chuyển thực hiện dịch vụ CLS", 1250, 600);
									DlgUtil.open("divDlgPhieuCDVTHCLS");
							}
						});
						//In phieu CĐHA
						$('#' + _self.containerId + 'print').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportPCDHA(rowKey);
						});
						//START L2HOTRO-12397
						//Xem phieu CĐHA
						$('#' + _self.containerId + 'printCDHA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportPCDHAVIEW(rowKey);
						});
						$('#' + _self.containerId + 'printAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportPCDHAAuto(rowKey);
						});
						//START -- HISL2TK-611 --hongdq
						$('#' + _self.containerId + 'printSelected').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportSelected(rowKey);
						});
						//START -- HISL2TK-611 --hongdq
						//In Ket qua CĐHA
						$('#' + _self.containerId + 'printKQ').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKetQuaPCDHA(rowKey);
						});
						$('#' + _self.containerId + 'printKQAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKetQuaPCDHAAuto(rowKey);
						});
						//Beg_HaNv_120123: bổ sung menu In phiếu CĐHA viết tay - L2PT-33633
						$('#' + _self.containerId + 'printKQVT').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKetQuaPCDHAVT(rowKey);
						});
						//End_HaNv_120123
						//Beg_HaNv_131023: Inbarcode CLS - L2PT-53101
						$('#' + _self.containerId + 'prinBarcode').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportBarcode(rowKey);
						});
						//End_HaNv_131023
						//in giay cam doan pttt va gay me hoi suc
						$('#' + _self.containerId + 'printCamDoanPTTT').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
							_self._exportCamDoanPTTT(rowKey);
						});
						//START L2HOTRO-13031
						$('#' + _self.containerId + 'printDVCSC').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
							_self._exportprintDVCSC(rowKey);
						});
						//Xoa tat ca dich vu 
						$('#' + _self.containerId + 'deleteAllKQCDHA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._deleteAllKQCDHA(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => START
						$('#' + _self.containerId + 'kyCA').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selarrrow");
					        if (rowDatas.length > '1') {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
								_self._caRpt('1');
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
								_self._kyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'huyKyCA').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
								_self._caRpt('2');
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
								_self._huyKyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'printKyCA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printSelectedCASigned').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								_self._printSelectedCASigned();
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
								_self._exportKyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'printKyCAKQ').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKyKQ(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => END
					} else if (modeView == "1") {
						$(".jqgrow", '#' + _self.containerId + _self.options._gridCDHA).contextMenu(_self.containerId + 'contextMenuPrint', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHA);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridCDHA, rowId);
								return true;
							},
						})
						//cap nhat phieu dieu tri
						$('#' + _self.containerId + 'editPhieuDTPrint').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._editPhieuDT(rowKey);
						});
						$('#' + _self.containerId + 'printView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportPCDHA(rowKey);
						});
						//START L2HOTRO-12397
						//Xem phieu CĐHA
						$('#' + _self.containerId + 'printViewCDHA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportPCDHAVIEW(rowKey);
						});
						//START -- HISL2TK-611 --hongdq
						$('#' + _self.containerId + 'printViewSelected').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportSelected(rowKey);
						});
						//START -- HISL2TK-611 --hongdq
						$('#' + _self.containerId + 'printViewAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportPCDHAAuto(rowKey);
						});
						$('#' + _self.containerId + 'printKQView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKetQuaPCDHA(rowKey);
						});
						$('#' + _self.containerId + 'printKQViewAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKetQuaPCDHAAuto(rowKey);
						});
						//in giay cam doan pttt va gay me hoi suc
						$('#' + _self.containerId + 'printCamDoanPTTT').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
							_self._exportCamDoanPTTT(rowKey);
						});
						//START L2HOTRO-13031
						$('#' + _self.containerId + 'printDVCSC').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
							_self._exportprintDVCSC(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => START
						$('#' + _self.containerId + 'printKyCA2').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printSelectedCASigned2').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								_self._printSelectedCASigned();
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
								_self._exportKyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'printKyCAKQ2').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._exportKyKQ(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => END
					} else if (modeView == "2") {
						$(".jqgrow", '#' + _self.containerId + _self.options._gridCDHA).contextMenu(_self.containerId + 'contextMenuDtkh', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHA);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridCDHA, rowId);
								return true;
							},
						})
						//xu ly su kien gui yeu cau
						$('#' + _self.containerId + 'sentRequestDtkh').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._sendRequest(rowKey);
						});
						//xu ly su kien xoa
						$('#' + _self.containerId + 'deleteDtkh').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._xoaPhieuDichVu(rowKey);
						});
						//Xoa tat ca dich vu 
						$('#' + _self.containerId + 'deleteAllKQCDHA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._deleteAllKQCDHA(rowKey);
						});
					}
					//Begin_HaNv_14032018: modeView=3 Gọi từ form XoaDichVuCLS cho phép xóa dịch vụ
					else if (modeView == "3") {
						$(".jqgrow", '#' + _self.containerId + _self.options._gridCDHA).contextMenu(_self.containerId + 'contextMenuXoaCls', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHA);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridCDHA, rowId);
								return true;
							},
						});
						$('#' + _self.containerId + 'deleteAllKQCDHA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
							_self._deleteAllKQCDHA(rowKey);
						});
					}
					//End_HaNv_14032018
					//set anh
					_self._setImageStatus();
					//L2PT-22465 start
					if (hospital_id == '10284' && (_self.options._formCall != '' && "NTU_BDT,KB_MHC,".includes(_self.options._formCall + ','))) {
						_self._setAmountStatus();
					}
					//L2PT-22465 end
					_setFocusMauBenhPham($("#hidMAUBENHPHAMID").val(), _self.containerId + _self.options._gridCDHA);
				}
			});
			GridUtil.setGridParam(_self.containerId + _self.options._gridCDHADetail, {
				onSelectRow : function(id, selected) {
					_self._hideShowButtonImageIcon(id);
					GridUtil.unmarkAll(_self.containerId + _self.options._gridCDHADetail);
					GridUtil.markRow(_self.containerId + _self.options._gridCDHADetail, id);
				},
				gridComplete : function(id) {
					var rowCount = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("reccount");
					if (rowCount == 0) {
						return;
					}
					var ids = $("#" + _self.containerId + _self.options._gridCDHADetail).getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var _icon = '';
						// đã ký số
						var id = ids[i];
						var rowDataCd = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', id);
						if (rowDataCd.PARAM_HASHED && rowDataCd.PARAM_HASHED != '') {
							_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
						}
						$("#" + _self.containerId + _self.options._gridCDHADetail).jqGrid('setCell', id, 'ICON', _icon);
						if (hospital_id == '10284') {
							if ((rowDataCd.THUOC_DIKEM != '' && rowDataCd.THUOC_DIKEM != null) || (rowDataCd.VATTU_DIKEM != '' && rowDataCd.VATTU_DIKEM != null)) {
								$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('setRowData', id, "", 'mybold');
							}
						}
						//Beg_HaNv_251123: Hiển thị btnDicom trên grid - L2PT-60582
						if (_self._gridHeaderDetail && _self._gridHeaderDetail.includes("VIEW_DICOM")) {
							var _studyInstanceUID = '';
							if (cfObj.RIS_DICOM_LINK_TYPE == "0") {
								_studyInstanceUID = rowDataCd.GHICHU2;
							} else {
								_studyInstanceUID = rowDataCd.DICHVUKHAMBENHID;
							}
							if (_studyInstanceUID != "" && RIS_SERVICE_DOMAIN_NAME != "" && RIS_GET_DICOM_VIEWER != "") {
								var btn = '<button type="button" class="btn btn-primary" id="btnDC_' + _studyInstanceUID + '">';
								btn = btn + '<span class="glyphicon glyphicon-picture" aria-hidden="true"></span></button>';
								$("#" + _self.containerId + _self.options._gridCDHADetail).jqGrid('setCell', id, 'VIEW_DICOM', btn);
								$("#btnDC_" + _studyInstanceUID).on("click", function(e) {
									var cslid = e.currentTarget.id;
									var _stu = cslid.replace('btnDC_', '');
									_self.options._studyInstanceUID = _stu;
									$('#' + _self.containerId + "btnDicomViewer").click();
								});
							}
						}
						//End_HaNv_251123
						if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
							if (rowDataCd.YC_HOAN == '2') {
								$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('setRowData', id, "", {
									color : 'blue'
								});
								$('#' + _self.containerId + _self.options._gridCDHADetail).setCell(id, 'TENDICHVU', '', {
									'text-decoration' : 'line-through'
								});
							}
							if (rowDataCd.YC_HOAN == '1') {
								$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('setRowData', id, "", {
									color : 'red'
								});
								$('#' + _self.containerId + _self.options._gridCDHADetail).setCell(id, 'TENDICHVU', '', {
									'text-decoration' : 'line-through'
								});
							}
						}
						//Beg_HaNv_271023: view kq cls - L2PT-57865
						if (cfObj.WGCLS_SHOW_VIEW_KQ == '1') {
							var checked = rowDataCd.VIEW_CLS == '1' ? 'checked="true"' : "";
							var chk = '<label class="control-label" for="' + (rowDataCd.KETQUACLSID) + '_VIEW" style="margin-left:5px; margin-bottom: 0px;">';
							chk = chk + '<input type="checkbox" class="mgb5" ' + checked + ' id="' + (rowDataCd.KETQUACLSID) + '_VIEW"></label>';
							$("#" + _self.containerId + _self.options._gridCDHADetail).jqGrid('setCell', id, 'VIEW', chk);
							$("#" + rowDataCd.KETQUACLSID + '_VIEW').on("change", function(e) {
								var cslid = e.currentTarget.id;
								var check = e.currentTarget.checked ? '1' : '0';
								if (cslid) {
									cslid = cslid.replace('_VIEW', '');
									sql_par = [];
									sql_par.push({
										"name" : "[0]",
										"value" : check
									}, {
										"name" : "[1]",
										"value" : cslid
									});
									jsonrpc.AjaxJson.execute("NTUD024.01", sql_par);
								}
							});
						}
						//End_HaNv_271023
						//Beg_HaNv_040324: Lưu thông tin người xem, ngày xem chỉ số KQCLS - L2PT-74410
						if (cfObj.WGCLS_SAVE_VIEWKQ == '1' && rowDataCd.NGUOIXEM_KQ) {
							var _icon = '<center><span class="glyphicon glyphicon-eye-open"></span></center>';
							$("#" + _self.containerId + _self.options._gridCDHADetail).jqGrid('setCell', id, 'VIEW_KQ', _icon);
						}
						//End_HaNv_040324
					}
					// //Beg_HaNv_270621: In đậm chữ khi có thuốc, vật tư đi kèm - BVTM-3724
					// if (hospital_id == '10284') {
					// 	var ids = $('#' + _self.containerId + _self.options._gridCDHADetail).getDataIDs();
					// 	for (var i = 0; i < ids.length; i++) {
					// 		var id = ids[i];
					// 		var row_dt = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', id);
					// 		if ((row_dt.THUOC_DIKEM != '' && row_dt.THUOC_DIKEM != null) || (row_dt.VATTU_DIKEM != '' && row_dt.VATTU_DIKEM != null)) {
					// 			$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('setRowData', id, "", 'mybold');
					// 		}
					// 	}
					// }
					// //End_HaNv_270621
					// if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
					// 	var ids = $('#' + _self.containerId + _self.options._gridCDHADetail).getDataIDs();
					// 	for (var i = 0; i < ids.length; i++) {
					// 		var id = ids[i];
					// 		var rowDataCd = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', id);
					// 		if (rowDataCd.YC_HOAN == '2') {
					// 			$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('setRowData', id, "", {
					// 				color : 'blue'
					// 			});
					// 			$('#' + _self.containerId + _self.options._gridCDHADetail).setCell(id, 'TENDICHVU', '', {
					// 				'text-decoration' : 'line-through'
					// 			});
					// 		}
					// 		if (rowDataCd.YC_HOAN == '1') {
					// 			$('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('setRowData', id, "", {
					// 				color : 'red'
					// 			});
					// 			$('#' + _self.containerId + _self.options._gridCDHADetail).setCell(id, 'TENDICHVU', '', {
					// 				'text-decoration' : 'line-through'
					// 			});
					// 		}
					// 	}
					// }
					if (_self.options._modeView == "1" || _self.options._modeView == "3") {
						if (_self.options._deleteDV == "1") {
							//build menu
							$(".jqgrow", '#' + _self.containerId + _self.options._gridCDHADetail).contextMenu(_self.containerId + 'contextMenuDeleteDV', {
								onContextMenu : function(event, menu) {
									var rowId = $(event.target).parent("tr").attr("id")
									var grid = $('#' + _self.containerId + _self.options._gridCDHADetail);
									grid.setSelection(rowId);
									return true;
								},
							});
							if (!_self.options.checkLoad) {
								_self.options.checkLoad = true;
								//xu ly su kien gui yeu cau
								$('#' + _self.containerId + 'deleteDV').unbind("click").bind("click", function() {
									var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
									_self._deleteDVCDHA(rowKey);
								});
							};
						}
					} else {
						$(".jqgrow", '#' + _self.containerId + _self.options._gridCDHADetail).contextMenu(_self.containerId + 'contextMenuDVKTTDT', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHADetail);
								grid.setSelection(rowId);
								return true;
							},
						}, _self.containerId + 'contextMenuNhapSinhThiet', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHADetail);
								grid.setSelection(rowId);
								return true;
							},
						}, _self.containerId + 'contextMenuprintCamDoanPTTT', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridCDHADetail);
								grid.setSelection(rowId);
								return true;
							},
						});
						if (!_self.options.checkLoad) {
							_self.options.checkLoad = true;
							//Beg_HaNv_021021: Điều chỉnh widget - BVTM-5952
							$('#' + _self.containerId + 'sendRequestDV').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._sendRequestDV(rowKey);
							});
							$('#' + _self.containerId + 'delRequestDV').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._delRequestDV(rowKey);
							});
							$('#' + _self.containerId + 'delDV').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								if (cfObj.CDHA_ALLOW_XOADV == "1") {//xóa DV giống tool xóa CLS
									_self._deleteDVCDHA(rowKey);
								} else if (cfObj.CDHA_ALLOW_XOADV == "2") {//Tích hợp gửi, hủy, xóa call code anh vietDa
									_self._delDV(rowKey);
								}
							});
							//End_HaNv_021021
							$('#' + _self.containerId + 'sendRequestDeleteReject').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._sendRequestDeleteReject(rowKey);
							});
							$('#' + _self.containerId + 'undoRequestDeleteReject').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._undoRequestDeleteReject(rowKey);
							});
							//xu ly su kien gui yeu cau
							$('#' + _self.containerId + 'DVKTTDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._updatePTTTKhongThanhToanDT(rowKey);
							});
							$('#' + _self.containerId + 'anhDaiDien').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._chonAnhDaiDien(rowKey);
							});
							//Beg_HaNv_131023: Chuyển thanh toán VP - L2PT-53262
							$('#' + _self.containerId + 'changeVP').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._changeVP(rowKey);
							});
							//End_HaNv_131023
							$('#' + _self.containerId + 'NhapSinhThiet').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._nhapSinhThiet(rowKey);
							});
							//in giay cam doan pttt
							$('#' + _self.containerId + 'printCamDoanPTTT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._exportCamDoanPTTT(rowKey);
							});
							//START L2HOTRO-13031
							$('#' + _self.containerId + 'printDVCSC').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								_self._exportprintDVCSC(rowKey);
							});
						}
					}
					$('.ui-jqgrid tr.jqgrow td[aria-describedby*="grdCDHAChiTiet_KETQUA"]').tooltip({
						content : function() {
							return $(this).closest('tr[role="row"]').find('td[aria-describedby$="grdCDHAChiTiet_KETQUACLS"]').attr("title").replace(/<img .*?>/g, "");;
						}
					});
					//L2PT-36168
					$('#' + _self.containerId + 'tamSoatChupCHT').unbind("click").bind(
							"click",
							function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
								var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowKey);
								var selRowId = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getGridParam', 'selrow');
								var rowDataXn = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', selRowId);
								console.log('---rowData', rowData);
								//  RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
								var THONG_TIN = new Object();
								THONG_TIN.DICHVU = rowData;
								THONG_TIN.PHIEU = rowDataXn;
								THONG_TIN.KHAMBENHID = $("#hidKHAMBENHID").val();
								THONG_TIN.CHUCNANG = 'CDHA_CHT';
								var name = JSON.stringify(THONG_TIN); //chucNang + '@' + _self.options._khambenhid + '@' + ;
								var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU_CDHA_CHT&showMode=dlg', name,
										'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' + screen.height + ',width=' + screen.width);
								popup.moveTo(0, 0);
							});
				}
			});
			//BAT VIEWER CHO		
			$('#' + _self.containerId + "btnDicomViewer").on('click', function() {
				var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '?studyInstanceUID=' + _self.options._studyInstanceUID;
				var typeBdhn = (hospital_id == "10284" && RIS_CONNECTION_TYPE == "7");//HaNv_271222: L2PT-32721
				var typeQti = (RIS_CONNECTION_TYPE == "7" && cfObj.RIS_DICOM_HL7_VNPT == "1");//HaNv_230723: L2PT-85509
				var row = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
				var data = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', row);
				var rowCt = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
				var dataCt = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowCt);
				if ((RIS_CONNECTION_TYPE == "1" || RIS_CONNECTION_TYPE == "2" || typeBdhn || typeQti) && RIS_SERVICE_DOMAIN_NAME != "" && RIS_GET_DICOM_VIEWER != "") {
					//Beg_HaNv_241022: L2PT-27963
					if (cfObj.RIS_DICOM_ID_TYPE == "2") {
						request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '?requestCode=' + data.SOPHIEU + '&conceptCode=' + dataCt.MADICHVU;
					}
					//End_HaNv_241022
					$.ajax({
						type : "GET",
						contentType : "application/json; charset=utf-8",
						headers : {
							'Ris-Access-Hash' : getHashRIS(_self.options._studyInstanceUID),
							'Identify-Code' : _self.options._studyInstanceUID
						},
						data : "",
						url : request_url,
						success : function(data) {
							window.open(data.data);
							console.log(JSON.stringify(data));
						},
						error : function(xhr) {
							if (xhr.status == "404" || xhr.status == "0") {
								DlgUtil.showMsg("Ảnh không còn tồn tại");
							} else {
								console.log("get dicom viewer fail: " + JSON.stringify(xhr));
							}
						}
					});
				}
				//Begin_HaNv_260121: Tích hợp js btnDicomViewer by VIETDA - L2PT-32957
				else if (RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8" && RIS_SERVICE_DOMAIN_NAME != "") {
					request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&LID=' + RIS_USERNAME + '&LPW=' + RIS_SECRET_KEY + '&AN=' + _self.options._studyInstanceUID;
					//Beg_HaNv_310521: Tích hợp js btnDicomViewer by VIETDA - L2PT-2784
					var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
					var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowKey);
					if (RIS_PROVIDER == "INFINITT") {
						request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&LID=' + RIS_USERNAME + '&LPW=' + RIS_SECRET_KEY + '&AN=' + _self.options._studyInstanceUID;
					} else if (RIS_PROVIDER == "VBIT") {
						request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&acc=' + rowData.DICHVUKHAMBENHID + '&mrn=';
					} else if (RIS_PROVIDER == "VIETTEL") {
						//request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&serviceID=' + rowData.DICHVUKHAMBENHID;
						//Beg_HaNv_06062022: L2PT-20749
						var _dt = rowData.DICHVUKHAMBENHID;
						if (cfObj.RIS_DICOM_ID_TYPE == "1") {
							var h = '';
							for (var i = 0; i < _dt.length; i++) {
								h += _dt.charCodeAt(i).toString(16);
							}
							_dt = h;
						}
						request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + _dt;
						//End_HaNv_06062022
					}
					//Beg_HaNv_140223: L2PT-34257
					else if (RIS_PROVIDER == "VNPT-RIS") {
						var link_dicom = dataCt.LINK_DICOM;//HaNv_111023: L2PT-55589
						if (link_dicom && link_dicom != '' && link_dicom != "") {
							request_url = link_dicom;
						} else {
							request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '?ris_exam_id=' + data.SOPHIEU + '&service_id=' + _self.options._studyInstanceUID;
						}
					}
					//End_HaNv_140223
					window.open(request_url, "_blank");
					//End_HaNv_310521
				}
				//End_HaNv_260121
			});
			
			//L2PT-80098
			$('#' + _self.containerId + "btnDicomViewer1").on('click', function() {
				var request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + '?studyInstanceUID=' + _self.options._studyInstanceUID;
				var typeBdhn = (hospital_id == "10284" && RIS_CONNECTION_TYPE_V1 == "7");//HaNv_271222: L2PT-32721
				var row = $('#' + _self.containerId + _self.options._gridCDHA).getGridParam("selrow");
				var data = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', row);
				var rowCt = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
				var dataCt = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowCt);
				if ((RIS_CONNECTION_TYPE_V1 == "1" || RIS_CONNECTION_TYPE_V1 == "2" || typeBdhn) && RIS_SERVICE_DOMAIN_NAME_V1 != "" && RIS_GET_DICOM_VIEWER_V1 != "") {
					//Beg_HaNv_241022: L2PT-27963
					if (cfObj.RIS_DICOM_ID_TYPE_V1 == "2") {
						request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + '?requestCode=' + data.SOPHIEU + '&conceptCode=' + dataCt.MADICHVU;
					}
					//End_HaNv_241022
					$.ajax({
						type : "GET",
						contentType : "application/json; charset=utf-8",
						headers : {
							'Ris-Access-Hash' : getHashRIS(_self.options._studyInstanceUID),
							'Identify-Code' : _self.options._studyInstanceUID
						},
						data : "",
						url : request_url,
						success : function(data) {
							window.open(data.data);
							console.log(JSON.stringify(data));
						},
						error : function(xhr) {
							if (xhr.status == "404" || xhr.status == "0") {
								DlgUtil.showMsg("Ảnh không còn tồn tại");
							} else {
								console.log("get dicom viewer fail: " + JSON.stringify(xhr));
							}
						}
					});
				}
				//Begin_HaNv_260121: Tích hợp js btnDicomViewer by VIETDA - L2PT-32957
				else if (RIS_CONNECTION_TYPE_V1 == "7" || RIS_CONNECTION_TYPE_V1 == "8" && RIS_SERVICE_DOMAIN_NAME_V1 != "") {
					request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + '&LID=' + RIS_USERNAME_V1 + '&LPW=' + RIS_SECRET_KEY_V1 + '&AN=' + _self.options._studyInstanceUID;
					//Beg_HaNv_310521: Tích hợp js btnDicomViewer by VIETDA - L2PT-2784
					var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
					var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowKey);
					if (RIS_PROVIDER_V1 == "INFINITT") {
						request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + '&LID=' + RIS_USERNAME_V1 + '&LPW=' + RIS_SECRET_KEY_V1 + '&AN=' + _self.options._studyInstanceUID;
					} else if (RIS_PROVIDER_V1 == "VBIT") {
						request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + '&acc=' + rowData.DICHVUKHAMBENHID + '&mrn=';
					} else if (RIS_PROVIDER_V1 == "VIETTEL") {
						//request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&serviceID=' + rowData.DICHVUKHAMBENHID;
						//Beg_HaNv_06062022: L2PT-20749
						var _dt = rowData.DICHVUKHAMBENHID;
						if (cfObj.RIS_DICOM_ID_TYPE_V1 == "1") {
							var h = '';
							for (var i = 0; i < _dt.length; i++) {
								h += _dt.charCodeAt(i).toString(16);
							}
							_dt = h;
						}
						request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + _dt;
						//End_HaNv_06062022
					}
					//Beg_HaNv_140223: L2PT-34257
					else if (RIS_PROVIDER_V1 == "VNPT-RIS") {
						var link_dicom = dataCt.LINK_DICOM;//HaNv_111023: L2PT-55589
						if (link_dicom && link_dicom != '' && link_dicom != "") {
							request_url = link_dicom;
						} else {
							request_url = RIS_SERVICE_DOMAIN_NAME_V1 + RIS_GET_DICOM_VIEWER_V1 + '?ris_exam_id=' + data.SOPHIEU + '&service_id=' + _self.options._studyInstanceUID;
						}
					}
					//End_HaNv_140223
					window.open(request_url, "_blank");
					//End_HaNv_310521
				}
				//End_HaNv_260121
			});
			//START -- L2DKHN-758 -- hongdq 20180315
			//calback cho man hinh xoa DV CLS
			EventUtil.setEvent("assignSevice_KetquaXoaDVCLS", function(e) {
				if (typeof (e) != 'undefined') {
					//var _self = this;
					_self._initWidget();
					EventUtil.raiseEvent("assignSevice_KetquaXoaDVCLS_CDHA", {
						msg : e.msg
					});
				}
			});
			//END -- L2DKHN-758 -- hongdq 20180315
			//L2PT-20786 start
			$('#' + _self.containerId + 'chkAllKhoa').unbind("click").bind("click", function() {
				_self._loadData();
				_self._bindEvent();
			});
			//L2PT-20786 end
			//Begin_HaNv_10052018: Hiển thị toolbar phụ thu theo cấu hình L2DKBD-1230
			$('#' + _self.containerId + 'toolbarIdhospitalfee_1').unbind("click").bind("click", function() {
				var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
				if (rowKey == null || rowKey == '') {
					DlgUtil.showMsg('Chưa chọn dịch vụ chẩn đoán hình ảnh');
					return;
				}
				_self._taophieuphuthu(rowKey);
			});
			$('#' + _self.containerId + 'toolbarIdhospitalfee_2').unbind("click").bind("click", function() {
				var rowKey = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("selrow");
				if (rowKey == null || rowKey == '') {
					DlgUtil.showMsg('Chưa chọn dịch vụ chẩn đoán hình ảnh');
					return;
				}
				_self._getDanhsachphieuphuthu(rowKey);
			});
			//End_HaNv_10052018
			//Begin_HaNv_28052018: Hiển thị toolbar in phiếu L2DKHN-777
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu19BV01').unbind("click").bind("click", function() {
				_self._inPhieu19BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu20BV01').unbind("click").bind("click", function() {
				_self._inPhieu20BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu21BV01').unbind("click").bind("click", function() {
				_self._inPhieu21BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01TEST_TAMLY').unbind("click").bind("click", function() {
				_self._inPhieu27BV01TEST_TAMLY();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01TV_TAMLY').unbind("click").bind("click", function() {
				_self._inPhieu27BV01TV_TAMLY();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu22BV01').unbind("click").bind("click", function() {
				_self._inPhieu22BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu27BV01').unbind("click").bind("click", function() {
				_self._inPhieu27BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu31BV01').unbind("click").bind("click", function() {
				_self._inPhieu31BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu23BV01').unbind("click").bind("click", function() {
				_self._inPhieu23BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu24BV01').unbind("click").bind("click", function() {
				_self._inPhieu24BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu25BV01').unbind("click").bind("click", function() {
				_self._inPhieu25BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieuTMH74').unbind("click").bind("click", function() {
				_self._inPhieuTMH74();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieuNSPQ74').unbind("click").bind("click", function() {
				_self._inPhieuNSPQ74();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu26BV01').unbind("click").bind("click", function() {
				_self._inPhieu26BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu28BV01').unbind("click").bind("click", function() {
				_self._inPhieu28BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu29BV01').unbind("click").bind("click", function() {
				_self._inPhieu29BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu30BV01').unbind("click").bind("click", function() {
				_self._inPhieu30BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnInPhieu32BV01').unbind("click").bind("click", function() {
				_self._inPhieu32BV01();
			});
			//End_HaNv_28052018
			//Beg_HaNv_220623: gen In Phiếu giống bên CLS - L2PT-46443
			$('[id^="' + _self.containerId + 'toolbarIdbtnInPhieu_new_"]').click(function(event) {
				_self.inPhieuNew($(event.currentTarget));
			})
			//End_HaNv_220623
		},
		//Beg_HaNv_220623: gen In Phiếu giống bên CLS - L2PT-46443
		inPhieuNew : function(target) {
			var _self = this;
			var rptData = target.data("external");
			if (rptData && rptData.report_code) {
				var maubenhphamid = $("#hidMAUBENHPHAMID").val();
				if (maubenhphamid == "") {
					DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 1500);
				} else {
					var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : maubenhphamid
					}, {
						name : 'id_maubenhpham',
						type : 'String',
						value : maubenhphamid
					} ];
					if (rptData.optional_param) {
						// Param optional
						if (rptData.optional_param.optional && rptData.optional_param.optional.length > 0) {
							par = par.concat(rptData.optional_param.optional);
						}
						if (rptData.optional_param.maubenhpham && rptData.optional_param.maubenhpham.length > 0) {
							var selMBPRowId = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getGridParam', 'selrow');
							var dvkPar = rptData.optional_param.maubenhpham.map(function(field) {
								return {
									name : field.toLowerCase(),
									type : 'String',
									value : $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getCell', selMBPRowId, field.toUpperCase())
								}
							});
							par = par.concat(dvkPar);
						}
						if (rptData.optional_param.ketqua && rptData.optional_param.ketqua.length > 0) {
							var selKQRowId = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getGridParam', 'selrow');
							if (selKQRowId != null) {
								var dvkPar = rptData.optional_param.ketqua.map(function(field) {
									return {
										name : field.toLowerCase(),
										type : 'String',
										value : $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getCell', selKQRowId, field.toUpperCase())
									}
								});
								par = par.concat(dvkPar);
							}
						}
					}
					if (rptData.innhieu_phieu != 0) {
						var dvkbDatas = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getGridParam', 'selarrrow');
						if (dvkbDatas.length == 0) {
							dvkbDatas = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO("CLS01.DS.DVKB", [ {
								"name" : "[0]",
								"value" : maubenhphamid
							} ]));
							dvkbDatas.forEach(function(dvkb) {
								// lấy ảnh của từng dịch vụ- vietda 27042020
								if (rptData.image_sql && rptData.image_sql != "") {
									var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid + "$" + dvkb.DICHVUKHAMBENHID);
									var imageCount = (rptData.image_count != null && rptData.image_count != '') ? parseInt(rptData.image_count) : 0;
									for (var i = 0; i < data_ar.length; i++) {
										var hinh_anh = data_ar[i]["DUONGDANFILE"];
										if (hinh_anh != "" && hinh_anh != null)
											par.push({
												name : 'hinh_anh' + (i + imageCount),
												type : 'Image',
												value : hinh_anh
											});
										var ten_file = data_ar[i]["TENFILE"];
										if (ten_file != "" && ten_file != null)
											par.push({
												name : 'ten_file' + (i + imageCount),
												type : 'String',
												value : ten_file
											});
									}
								}
								// end of -lấy ảnh của từng dịch vụ- vietda 27042020
								var parInnhieu = par.concat([ {
									name : 'dichvukhambenhid',
									type : 'String',
									value : dvkb.DICHVUKHAMBENHID
								} ]);
								openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', parInnhieu);
							});
						} else {
							dvkbDatas.forEach(function(item) {
								var dvkb = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', item);
								// lấy ảnh của từng dịch vụ- vietda 27042020
								if (rptData.image_sql && rptData.image_sql != "") {
									var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid + "$" + dvkb.DICHVUKHAMBENHID);
									var imageCount = (rptData.image_count != null && rptData.image_count != '') ? parseInt(rptData.image_count) : 0;
									for (var i = 0; i < data_ar.length; i++) {
										var hinh_anh = data_ar[i]["DUONGDANFILE"];
										if (hinh_anh != "" && hinh_anh != null)
											par.push({
												name : 'hinh_anh' + (i + imageCount),
												type : 'Image',
												value : hinh_anh
											});
										var ten_file = data_ar[i]["TENFILE"];
										if (ten_file != "" && ten_file != null)
											par.push({
												name : 'ten_file' + (i + imageCount),
												type : 'String',
												value : ten_file
											});
									}
								}
								// end of -lấy ảnh của từng dịch vụ- vietda 27042020
								var parInnhieu = par.concat([ {
									name : 'dichvukhambenhid',
									type : 'String',
									value : dvkb.DICHVUKHAMBENHID
								} ]);
								openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', parInnhieu);
							});
						}
					} else {
						// lấy ảnh của tất cả dịch vụ- vietda 27042020
						if (rptData.image_sql && rptData.image_sql != "") {
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid);
							var imageCount = (rptData.image_count != null && rptData.image_count != '') ? parseInt(rptData.image_count) : 0;
							for (var i = 0; i < data_ar.length; i++) {
								var hinh_anh = data_ar[i]["DUONGDANFILE"];
								if (hinh_anh != "" && hinh_anh != null)
									par.push({
										name : 'hinh_anh' + (i + imageCount),
										type : 'Image',
										value : hinh_anh
									});
								var ten_file = data_ar[i]["TENFILE"];
								if (ten_file != "" && ten_file != null)
									par.push({
										name : 'ten_file' + (i + imageCount),
										type : 'String',
										value : ten_file
									});
							}
						}
						// end of -lấy ảnh của tất cả dịch vụ- vietda 27042020
						openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', par);
					}
				}
			} else {
				DlgUtil.showMsg("Đã có lỗi xảy ra: rptData", undefined, 1500);
			}
		},
		//End_HaNv_220623
		//Begin_HaNv_10052018: Các hàm xử lý L2DKBD-1230
		_taophieuphuthu : function(rowId) {
			var _self = this;
			var selRowId = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getGridParam', 'selrow');
			var rowDataXn = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', selRowId);
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					chidinhdichvu : '1',
					loaidichvu : '8',
					loaiphieumbp : '6',
					benhnhanid : rowDataXn.BENHNHANID,
					khambenhid : rowDataXn.KHAMBENHID,
					hosobenhanid : rowDataXn.HOSOBENHANID,
					tiepnhanid : rowDataXn.TIEPNHANID,
					doituongbenhnhanid : rowDataXn.DOITUONGBENHNHANID,
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
					maubenhphamchaid : rowData.MAUBENHPHAMID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					subDeptId : $("#hidPHONGID").val()
				}
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 8, paramInput, "Tạo phiếu phụ thu", 1300, 600);
				DlgUtil.open("divDlgDichVu");
			}
		},
		_getDanhsachphieuphuthu : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					dichvucha_id : rowData.DICHVUKHAMBENHID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDSPPT", "divDlg", "manager.jsp?func=../noitru/NTU02D040_DanhSachPhieuPhuThu", paramInput, "Danh sách phiếu phụ thu", 1250, 575);
				DlgUtil.open("divDlgDSPPT");
			}
		},
		//End_HaNv_10052018
		//Begin_HaNv_28052018: Các hàm xử lý in phiếu L2DKHN-777
		_inPhieu19BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'lanthu',
					type : 'String',
					value : ''
				} ];
				openReport('window', 'NTU011_PHIEUCHIEUCHUPXQUANG_19BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu20BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'lanthu',
					type : 'String',
					value : ''
				} ];
				openReport('window', 'NTU035_PHIEUCHUPCATLOPVITINH_20BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu21BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'lanthu',
					type : 'String',
					value : ''
				} ];
				openReport('window', 'NTU036_PHIEUCHUPCONGHUONGTU_21BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu27BV01TEST_TAMLY : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				par.push({
					name : 'type',
					type : 'String',
					value : "1"
				})
				openReport('window', 'PHIEU_TEST_TAMLY_QNI', 'pdf', par);
			}
		},
		_inPhieu27BV01TV_TAMLY : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				par.push({
					name : 'type',
					type : 'String',
					value : "2"
				})
				openReport('window', 'PHIEU_TEST_TAMLY_QNI', 'pdf', par);
			}
		},
		_inPhieu22BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var row = data_ar[0];
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				//HaNv_160123: L2PT-32974
				for (var i = 0; i < data_ar.length; i++) {
					var hinh_anh = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh != "" && hinh_anh != null)
						par.push({
							name : 'hinh_anh' + (i + 1),
							type : 'Image',
							value : hinh_anh
						});
				}
				openReport('window', 'NTU012_PHIEUSIEUAM_22BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu27BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var row = data_ar[0];
				console.log(JSON.stringify(row));
				var hinh_anh1 = data_ar[0]["DUONGDANFILE"];
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				if (hinh_anh1 != "" && hinh_anh1 != null)
					par.push({
						name : 'hinh_anh1',
						type : 'Image',
						value : hinh_anh1
					});
				openReport('window', 'NTU012_PHIEUSIEUAMMAU_24BV01_A4', 'pdf', par);
			}
		},
		_inPhieu31BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				//tuyennx_add_start_20170818 L2K47TW-128
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var max = (data_ar.length > 4 ? 4 : data_ar.length);
				for (var i = 0; i < data_ar.length; i++) {
					var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh1 != "" && hinh_anh1 != null)
						par.push({
							name : 'hinh_anh' + i,
							type : 'Image',
							value : hinh_anh1
						});
				}
				//tuyennx_add_end_20170818 L2K47TW-128
				openReport('window', 'NTU012_PHIEUSIEUAMDOPPLER_24BV01_A4', 'pdf', par);
			}
		},
		_inPhieu23BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU013_PHIEUDIENTIM_23BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu24BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU037_PHIEUDIENNAO_24BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu30BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU013_PHIEUKETQUADOJAVAL_23BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu32BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'CANLAMSANG_PHIEUKETQUA_DIENCO', 'pdf', par);
			}
		},
		_inPhieu25BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'lanthu',
					type : 'String',
					value : ''
				}, {
					name : 'giodn',
					type : 'String',
					value : ''
				}, {
					name : 'phutdn',
					type : 'String',
					value : ''
				}, {
					name : 'ngaydn',
					type : 'String',
					value : ''
				} ];
				//tuyennx_add_start_20170818 HISL2BVDKHN-354
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var max = (data_ar.length > 4 ? 4 : data_ar.length);
				for (var i = 0; i < data_ar.length; i++) {
					var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh1 != "" && hinh_anh1 != null)
						par.push({
							name : 'hinh_anh' + i,
							type : 'Image',
							value : hinh_anh1
						});
				}
				//tuyennx_add_end_20170818 HISL2BVDKHN-354
				openReport('window', 'NTU014_PHIEUNOISOI_25BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieuTMH74 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'lanthu',
					type : 'String',
					value : ''
				}, {
					name : 'giodn',
					type : 'String',
					value : ''
				}, {
					name : 'phutdn',
					type : 'String',
					value : ''
				}, {
					name : 'ngaydn',
					type : 'String',
					value : ''
				} ];
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var max = (data_ar.length > 4 ? 4 : data_ar.length);
				for (var i = 0; i < data_ar.length; i++) {
					var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh1 != "" && hinh_anh1 != null)
						par.push({
							name : 'hinh_anh' + i,
							type : 'Image',
							value : hinh_anh1
						});
				}
				openReport('window', 'KQ_NOISOI_TMH_944', 'pdf', par);
			}
		},
		_inPhieuNSPQ74 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'lanthu',
					type : 'String',
					value : ''
				}, {
					name : 'giodn',
					type : 'String',
					value : ''
				}, {
					name : 'phutdn',
					type : 'String',
					value : ''
				}, {
					name : 'ngaydn',
					type : 'String',
					value : ''
				} ];
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var max = (data_ar.length > 4 ? 4 : data_ar.length);
				for (var i = 0; i < data_ar.length; i++) {
					var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh1 != "" && hinh_anh1 != null)
						par.push({
							name : 'hinh_anh' + i,
							type : 'Image',
							value : hinh_anh1
						});
				}
				openReport('window', 'KQ_NOISOI_PHEQUAN_944', 'pdf', par);
			}
		},
		_inPhieu26BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU038_PHIEUDOCHUCNANGHOHAP_26BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu28BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU011_PHIEUDOTHITRUONGTRUNGTAM_19BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu29BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU011_PHIEUCHUPDAYMATKHONGHUYNHQUANG_19BV01_QD4069_A4', 'pdf', par);
			}
		},
		//End_HaNv_28052018
		_hideShowButtonImageIcon : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// ẩn hiện nút xem ảnh DICOM
				if (cfObj.RIS_DICOM_LINK_TYPE == "0") {
					_self.options._studyInstanceUID = rowData.GHICHU2;
				} else {
					_self.options._studyInstanceUID = rowData.DICHVUKHAMBENHID;
				}
				if (_self.options._studyInstanceUID != "" && RIS_SERVICE_DOMAIN_NAME != "" && RIS_GET_DICOM_VIEWER != "") {
					$('#' + _self.containerId + "btnDicomViewer").show();
				} else {
					$('#' + _self.containerId + "btnDicomViewer").hide();
				}
				//L2PT-80098
				if (_self.options._studyInstanceUID != "" && RIS_SERVICE_DOMAIN_NAME_V1 != "" && RIS_GET_DICOM_VIEWER_V1 != "") {
					$('#' + _self.containerId + "btnDicomViewer1").show();
				} else {
					$('#' + _self.containerId + "btnDicomViewer1").hide();
				}
			}
		},
		_copyPhieuCDHA : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//Beg_HaNv_270521: Cho phép bác sĩ tạo bản sao DVKT (XN, CĐHA, PTTT) của bác sĩ khác - L2PT-19264
				if (cfObj.CLS_COPY_NOCHECK_ROLE == '0') {
					var _nguoitaoid = rowData.NGUOITAO_ID;
					var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
					if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
						DlgUtil.showMsg("Bạn không có quyền tạo bản sao cho phiếu này!");
						return false;
					}
				}
				//End_HaNv_270521
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					khambenhid : rowData.KHAMBENHID,
					type : '2'
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgCopyMbp", "divDlg", "manager.jsp?func=../noitru/NTU02D070_ThoiGianDonThuoc", paramInput, "Tạo bản sao", 600, 360);
				DlgUtil.open("divDlgCopyMbp");
			}
		},
		//L2K74TW-301 - hongdq
		_editPhieuDT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV004", RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]));
				var data_ar = $.parseJSON(dataObj);
				if (data_ar != null && data_ar.length > 0) {
					data = data_ar[0];
					var paramInput = {
						maubenhphamid : rowData.MAUBENHPHAMID,
						khambenhid : rowData.KHAMBENHID,
						phieudieutriid : data.PHIEUDIEUTRIID,
						mode : _self.options._modeView == '1' ? '1' : '0',
						hosobenhanid : rowData.HOSOBENHANID,
						tgchidinh: data.TGCHIDINH
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgeditPhieuDT", "divDlg", "manager.jsp?func=../noitru/NTU02D081_Capnhat_PhieuDT", paramInput, "Cập nhật phiếu điều trị", 600, 360);
					DlgUtil.open("divDlgeditPhieuDT");
				}
			}
		},
		//Begin_HaNv_06062020: Sửa bác sĩ chỉ định - L2PT-21679
		_editBacSi : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					deptid : $("#hidKHOAID").val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgBacSyChiDinh", "divDlg", "manager.jsp?func=../noitru/NTU01H026_DoiBacSy", paramInput, "Đổi bác sĩ chỉ định", 500, 200);
				EventUtil.setEvent("assignSevice_saveChangeBS", function(e) {
					if (typeof (e) != 'undefined') {
						DlgUtil.showMsg(e.msg);
					}
					DlgUtil.close(e.divId);
				});
				DlgUtil.open("divDlgBacSyChiDinh");
			}
		},
		//End_HaNv_06062020
		//them phieu cam doan pttt tuonglt
		_exportCamDoanPTTT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'i_khambenhid',
					type : 'String',
					value : _self.options._khambenhid
				}, {
					name : 'i_dichvukhambenhid',
					type : 'String',
					value : rowData.DICHVUKHAMBENHID
				} ];
				openReport('window', "NTU027_GIAYCAMDOANCHAPNHANPTTT_A4_944", "pdf", par);
			}
		},
		//START L2HOTRO-13031
		_exportprintDVCSC : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			paramInput = {
				maubenhphamid : $("#hidMAUBENHPHAMID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D105_InPhieuCon", paramInput, "In Dịch vụ - chỉ số con", 1100, 600);
			DlgUtil.open("divDlgDeleteXN");
		},
		_deleteDVCDHA : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn xóa dịch vụ CĐHA không?", function(flag) {
					if (flag) {
						var par = [];
						obj = new Object();
						obj.DICHVUKHAMBENHID = rowData.DICHVUKHAMBENHID;
						obj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
						par.push(obj);
						var paramInput = {
							parram : par,
							type : 3
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgcapnhatLD", "divDlg", "manager.jsp?func=../noitru/NTU02D085_XoaDVCLS", paramInput, "Cập nhật lý do", 600, 250);
						DlgUtil.open("divDlgcapnhatLD");
					}
				});
			}
		},
		_deleteAllKQCDHA : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn xóa HẾT dịch vụ CĐHA không?", function(flag) {
					if (flag) {
						//Begin_HaNv_18032020: Điều chỉnh cách xóa dữ liệu CLS - L2PT-18272
						var par = [];
						obj = new Object();
						obj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
						par.push(obj);
						/*var length = rowData.length;
						var strJSON = "";
						for (var i = 0; i < length; i++) {
							obj = new Object();
							obj.DICHVUKHAMBENHID = rowData[i].DICHVUKHAMBENHID;
							obj.MAUBENHPHAMID = rowData[i].MAUBENHPHAMID;
							par.push(obj);
						}*/
						var paramInput = {
							parram : par,
							type : 4
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgcapnhatLD", "divDlg", "manager.jsp?func=../noitru/NTU02D085_XoaDVCLS", paramInput, "Cập nhật lý do", 600, 250);
						DlgUtil.open("divDlgcapnhatLD");
						//End_HaNv_18032020
					}
				});
			}
		},
		//======================================= Added by SONDN
		_nhapSinhThiet : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			var title = "Thông tin sinh thiết";
			if (rowData != null) {
				var myVar = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					tendichvu : rowData.TENDICHVU
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgMauSinhThiet", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K062_MauSinhThiet", myVar, title, 1000, 500);
				DlgUtil.open("dlgMauSinhThiet");
			}
		},
		//======================================= End Added by SONDN
		_updatePTTTKhongThanhToanDT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn chuyển loại dịch vụ CDHA sang dịch vụ miễn giảm thanh toán đồng thời không?", function(flag) {
					if (flag) {
						var _par = [ rowData.DICHVUKHAMBENHID ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT026.EV003", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Cập nhật thành công dịch vụ miễn giảm thanh toán đồng thời");
							_self._viewCKDetailMauBenhPham(rowData.MAUBENHPHAMID);
						} else if (_return == 0) {
							DlgUtil.showMsg("Cập nhật không thành công dịch vụ miễn giảm thanh toán đồng thời");
						} else if (_return == 2) {
							DlgUtil.showMsg("Bệnh nhân không phải là đối tượng BHYT hoặc không phải dịch vụ bảo hiểm");
						} else if (_return == 3) {
							DlgUtil.showMsg("Không tồn tại dịch vụ chính trong phiếu");
						} else if (_return == 4) {
							DlgUtil.showMsg("Dịch vụ đã thu tiền");
						} else if (_return == 5) {
							DlgUtil.showMsg("Bệnh nhân đã kết thúc điều trị. Không thể cập nhật");
						}
					}
				});
			}
		},
		//Beg_HaNv_131023: Chuyển thanh toán VP - L2PT-53262
		_chonAnhDaiDien : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var obj = new Object();
				obj["FUNC"] = "QLBA_GET_IMGS";
				obj["PARAMS"] = {
					dichvukhambenhid: rowData.DICHVUKHAMBENHID
				};
				obj["ACTIONS"] = [
					{
						name: "Chọn",
						style: "success",
						icon: "glyphicon-ok",
						close: true,
						onSubmit: function (ids, items, e) {
							console.log(ids);
							console.log(items);
							//luu quan ly benh an
							var obj = new Object();
							obj.HOSOBENHANID = $('#hidHOSOBENHANID').val();
							obj.KHAMBENHID = $("#hidKHAMBENHID").val();
							obj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
							obj.DICHVUKHAMBENHID = rowData.DICHVUKHAMBENHID;
							obj.IDS = ids;
							obj.ITEMS = items;
							var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S('HIS.ANH.DAIDIEN', JSON.stringify(obj));
							if (fl == '1') {
								DlgUtil.showMsg("Chọn ảnh thành công!");
							} else {
								DlgUtil.showMsg("Thất bại!");
							}
						}
					}
				]
				DlgUtil.buildPopupUrl(
					"dlgFileManagerItems2",
					"divDlg",
					"manager.jsp?func=../danhmuc/File_Manager_Items_2",
					obj,
					`File Manager: ${'Chọn ảnh đại diện'}`,
					window.innerWidth * 95 / 100,
					$(window).height() * 94 / 100
				);
				DlgUtil.open("dlgFileManagerItems2");
				EventUtil.setEvent("dlgFileManagerItems2_close", function (e) {
					DlgUtil.close("dlgFileManagerItems2");
				});
			}
		},
		_changeVP : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn chuyển loại thanh toán của dịch vụ sang viện phí không?", function(flag) {
					if (flag) {
						var objDVKB = new Object();
						objDVKB.HOSOBENHANID = $('#hidHOSOBENHANID').val();
						objDVKB.TIEPNHANID = $("#hidTIEPNHANID").val();
						objDVKB.DS_DVKBID = rowData.DICHVUKHAMBENHID;
						objDVKB.LOAIDOITUONG_MOI = '4';
						objDVKB.TENDTMOI = 'VIỆN PHÍ';
						objDVKB.HIS_BHYT_ID = '-1';
						objDVKB.FLAG_CHUYEN = "0";
						var r_json = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI01T004.09', JSON.stringify(objDVKB));
						if (r_json == '-1') {
							DlgUtil.showMsg("Xảy ra lỗi");
						} else if (r_json == '-2') {
							DlgUtil.showMsg("Đã kết thúc bệnh án, không thể chuyển loại thanh toán");
						} else if (r_json == '0') {
							DlgUtil.showMsg("Chuyển sang thẻ khác thành công");
						} else if (r_json == '-19') {
							DlgUtil.showMsg("Không thể chuyển dịch vụ sang loại hợp đồng với loại khám của BN này");
						} else if (r_json == '-191') {
							DlgUtil.showMsg("Tổng tiền các dịch vụ đã chọn vượt quá hạn mức, không thể chuyển sang loại hợp đồng");
						} else if (r_json == '-192') {
							DlgUtil.showMsg("Không thể chuyển đối tượng cho BN gửi thực hiện CLS");
						} else {
							try {
								var result = JSON.parse(r_json);
								if (!result || result.length == 0) {
									DlgUtil.showMsg("Không lấy được kết quả chuyển loại thanh toán");
								} else {
									if (!result.RESULT && result.RESULT == 0) {
										DlgUtil.showMsg("Chuyển loại thanh toán không thành công");
									}
									if (result.MESSAGE && result.MESSAGE.length > 0) {
										DlgUtil.showMsg(result.MESSAGE);
									}
								}
							} catch (e) {
								DlgUtil.showMsg("Có lỗi khi đọc kết quả chuyển loại thanh toán");
								console.log(e);
							}
						}
					}
				});
			}
		},
		//End_HaNv_131023
		_sendRequestDeleteReject : function(rowId) {
			var _self = this;
			if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
				var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
				if (rowData != null) {
					DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu hủy dịch vụ không?", function(flag) {
						if (flag) {
							var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID, 0 ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _par.join('$'));
							if (_return == 1) {
								//tuyennx_edit_start_20190425 L1PT-661
								if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
									DlgUtil.showMsg("Yêu cầu hủy dịch vụ thành công", undefined, 1500);
								}
								//tuyennx_edit_end_20190425 L1PT-661 		    		   	       			
								_self._initWidget();
							} else if (_return == 2) {
								DlgUtil.showMsg("Dịch vụ chưa thu tiền");
							} else if (_return == 3) {
								DlgUtil.showMsg("Phiếu đã được tiếp nhận hoặc có kết quả, không thể yêu cầu hủy dịch vụ");
							}
							//Begin_HaNv_05072019: Quy trình hoàn hủy với bệnh nhân đóng tạm ứng - L1PT-1245
							else if (_return == 4) {
								DlgUtil.showMsg("BN chưa đóng tạm ứng. Không thể hủy yêu cầu dịch vụ!");
							}
							//End_HaNv_05072019
							else {
								DlgUtil.showMsg("Yêu cầu hủy dịch vụ thất bại");
							}
						}
					});
				}
			}
		},
		_undoRequestDeleteReject : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			if (rowData != null && (rowData.YC_HOAN == '2' || rowData.YC_HOAN == '1')) {
				DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu khôi phục dịch vụ không?", function(flag) {
					if (flag) {
						var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.002", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thành công");
							_self._initWidget();
						} else if (_return == 2) {
							DlgUtil.showMsg("Dịch vụ chưa thu tiền");
						} else if (_return == 3) {
							DlgUtil.showMsg("Dịch vụ không yêu cầu hủy hoặc đã xác nhận, không thể khôi phục");
						} else {
							DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thất bại");
						}
					}
				});
			} else {
				DlgUtil.showMsg("Dịch vụ không yêu cầu hủy hoặc đã được xác nhận, không thể khôi phục");
			}
		},
		//Beg_HaNv_021021: Điều chỉnh widget - BVTM-5952
		_sendRequestDV : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			var rowIdCDHA = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid("getGridParam", "selrow");
			var rowDataCDHA = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowIdCDHA);
			var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID ];
			var requestCode = rowDataCDHA.SOPHIEU;
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.GYC", _par.join('$'));
			if (_return == 1) {
				//Beg_HaNv_281022: Cập nhật HL7 khi gửi, hủy DV - L2PT-28483
				if (RIS_SERVICE_DOMAIN_NAME != "" && RIS_DELETE_REQUEST != "" && (RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8")) {
					ajaxSvc.HL7_Gateway.sendChildRequest(requestCode, rowData.DICHVUKHAMBENHID, "IP");//HaNv_121222: L2PT-31444
				}
				//End_HaNv_281022
				DlgUtil.showMsg("Gửi yêu cầu thành công!");
			} else if (_return == 0) {
				DlgUtil.showMsg("Gửi yêu cầu phiếu thất bại!");
			} else if (_return == 5) {
				DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
			} else if (_return == 6) {
				DlgUtil.showMsg("Phiếu đã được gửi yêu cầu");
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra: " + _return);
			}
		},
		_delRequestDV : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			var rowIdCDHA = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid("getGridParam", "selrow");
			var rowDataCDHA = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowIdCDHA);
			var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID ];
			var requestCode = rowDataCDHA.SOPHIEU;
			var chyc = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.CHYC", _par.join('$'));
			if (chyc == 1) {
				if (RIS_CONNECTION_TYPE == "1" && RIS_SERVICE_DOMAIN_NAME != "") {
					var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_DELETE_ORDER;
					var data = {
						requestCode : requestCode,
						conceptCode : rowData.MADICHVU,
						cancelReason : "Bác sỹ hủy yêu cầu",
						cancelBy : user_id
					};
					$.ajax({
						type : "POST",
						contentType : "application/json; charset=utf-8",
						headers : {
							'Ris-Access-Hash' : getHashRIS(requestCode),
							'Identify-Code' : requestCode
						},
						data : JSON.stringify(data),
						url : request_url,
						success : function(data) {
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.HYC", _par.join('$'));
							if (_return == 1) {
								DlgUtil.showMsg("Hủy yêu cầu thành công!");
							} else if (_return == 0) {
								DlgUtil.showMsg("Hủy yêu cầu thất bại!");
							} else if (_return == 2) {
								DlgUtil.showMsg("Phiếu đã thu tiền nên không được hủy yêu cầu");
							} else if (_return == 5) {
								DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
							} else {
								DlgUtil.showMsg("Có lỗi xảy ra: " + _return);
							}
						},
						error : function(xhr) {
							if (xhr.status == "404") {
								var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.HYC", _par.join('$'));
								if (_return == 1) {
									DlgUtil.showMsg("Hủy yêu cầu thành công!");
								} else if (_return == 0) {
									DlgUtil.showMsg("Hủy yêu cầu thất bại!");
								} else if (_return == 2) {
									DlgUtil.showMsg("Phiếu đã thu tiền nên không được hủy yêu cầu");
								} else if (_return == 5) {
									DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
								} else {
									DlgUtil.showMsg("Có lỗi xảy ra: " + _return);
								}
							} else if (xhr.status == "0") {
								DlgUtil.showMsg('Không thể hủy yêu cầu bên RIS');
								console.log(JSON.stringify(xhr));
							} else {
								DlgUtil.showMsg(xhr.status + " - " + JSON.parse(xhr.responseText).message);
							}
						}
					});
				} else {
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.HYC", _par.join('$'));
					if (_return == 1) {
						//Beg_HaNv_281022: Cập nhật HL7 khi gửi, hủy DV - L2PT-28483
						if (RIS_SERVICE_DOMAIN_NAME != "" && RIS_DELETE_REQUEST != "" && (RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8")) {
							ajaxSvc.HL7_Gateway.sendChildRequest(requestCode, rowData.DICHVUKHAMBENHID, "CA");
						}
						//End_HaNv_281022
						DlgUtil.showMsg("Hủy yêu cầu thành công!");
					} else if (_return == 0) {
						DlgUtil.showMsg("Hủy yêu cầu thất bại!");
					} else if (_return == -1) {
						DlgUtil.showMsg("Phiếu đã thu tiền nên không được hủy yêu cầu");
					} else if (_return == 5) {
						DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
					} else {
						DlgUtil.showMsg("Có lỗi xảy ra: " + _return);
					}
				}
			} else if (chyc == 2) {
				DlgUtil.showMsg("Phiếu đã thu tiền không được hủy yêu cầu!");
			} else if (chyc == 5) {
				DlgUtil.showMsg("Không phải người chỉ định không thể hủy yêu cầu!");
			}
			//Beg_HaNv_201021: BVTM-6612
			else if (chyc == -5) {
				DlgUtil.showMsg("Dịch vụ có phiếu phụ thu đi kèm, không được phép hủy yêu cầu!");
			}
			//End_HaNv_201021
			else {
				DlgUtil.showMsg("Không thể hủy yêu cầu");
			}
		},
		_delDV : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', rowId);
			var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID ];
			DlgUtil.showConfirm("Bạn có chắc chắn xóa chỉ định CĐHA không?", function(flag) {
				if (flag) {
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.XYC", _par.join('$'));
					if (_return == 1) {
						DlgUtil.showMsg("Xóa yêu cầu thành công");
					} else if (_return == 0) {
						DlgUtil.showMsg("Xóa yêu cầu thất bại");
					} else if (_return == 2) {
						DlgUtil.showMsg("Dịch vụ đã thu tiền nên không được phép xóa");
					} else if (_return == 3) {
						DlgUtil.showMsg("Phiếu đã hoàn thành, không cho phép chỉnh sửa");
					} else if (_return == 4) {
						DlgUtil.showMsg("Dịch vụ không ở trạng thái Hủy, không cho phép xóa");
					} else if (_return == 5) {
						DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
					}
					//Beg_HaNv_201021: BVTM-6612
					else if (chyc == -5) {
						DlgUtil.showMsg("Dịch vụ có phiếu phụ thu đi kèm, không được phép xóa!");
					}
					//End_HaNv_201021
					else {
						DlgUtil.showMsg("Có lỗi xảy ra: " + _return);
					}
				}
			});
		},
		//End_HaNv_021021
		_setImageStatus : function() {
			var _self = this;
			var ids = $("#" + _self.containerId + _self.options._gridCDHA).getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var row = $("#" + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', id);
				var _icon = '';
				var _trangthai = parseInt(row.TRANGTHAIMAUBENHPHAM);
				if (_trangthai == 2) {
					_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
				} else if (_trangthai == 3) {
					_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
				} else if (_trangthai == 4) {
					_icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
				}
				$("#" + _self.containerId + _self.options._gridCDHA).jqGrid('setCell', id, "ICON", _icon);
			}
		},
		//L2PT-22465 start
		_setAmountStatus : function() {
			var _self = this;
			var ids = $("#" + _self.containerId + _self.options._gridCDHA).getDataIDs();
			var _soluong_cdha = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.PTH", "2");
			var _canhbaoSL_cdha = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'CANHBAO_SL_BN_CDHA');
			var _slxanh_cdha = _canhbaoSL_cdha.split(";")[0];
			var _sldo_cdha = _canhbaoSL_cdha.split(";")[1];
			var _soluong_tdcn = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.PTH", "3");
			var _canhbaoSL_tdcn = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'CANHBAO_SL_BN_TDCN');
			var _slxanh_tdcn = _canhbaoSL_tdcn.split(";")[0];
			var _sldo_tdcn = _canhbaoSL_tdcn.split(";")[1];
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var row = $("#" + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', id);
				var _icon = '';
				if (row.LOAI_PHONG_CANHBAO_ID == '2') {
					if (parseInt(_soluong_cdha) <= parseInt(_slxanh_cdha)) {
						_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
					} else if (parseInt(_slxanh_cdha) < parseInt(_soluong_cdha) && parseInt(_soluong_cdha) < parseInt(_sldo_cdha)) {
						_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
					} else if (parseInt(_soluong_cdha) >= parseInt(_sldo_cdha)) {
						_icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
					}
				} else if (row.LOAI_PHONG_CANHBAO_ID == '3') {
					if (parseInt(_soluong_tdcn) <= parseInt(_slxanh_tdcn)) {
						_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
					} else if (parseInt(_slxanh_tdcn) < parseInt(_soluong_tdcn) && parseInt(_soluong_tdcn) < parseInt(_sldo_tdcn)) {
						_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
					} else if (parseInt(_soluong_tdcn) >= parseInt(_sldo_tdcn)) {
						_icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
					}
				}
				$("#" + _self.containerId + _self.options._gridCDHA).jqGrid('setCell', id, "ICON3", _icon);
			}
		}, // L2PT-22465 end
		_xoaPhieuDichVuKhongCoKetQua : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 3) {
					DlgUtil.showConfirm("Bạn có chắc chắn xóa các dịch vụ không có kết quả không?", function(flag) {
						if (flag) {
							var _par = [ rowData.MAUBENHPHAMID ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.NO_RLT", _par.join('$'));
							if (_return == 1) {
								DlgUtil.showMsg("Xóa thành công các dịch vụ không có kết quả", undefined, 1500);
								_self._initWidget();
							} else if (_return == 0) {
								DlgUtil.showMsg("Xóa không thành công phiếu xét nghiệm");
							} else if (_return == 2) {
								DlgUtil.showMsg("Phiếu không có dịch vụ không có kết quả");
							}
						}
					});
				} else if (_trangthai < 3) {
					DlgUtil.showMsg("Phiếu này chưa kết thúc!");
				}
			}
		},
		_xoaPhieuDichVu : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				if (_self.options._modeView != "2") {
					var _nguoitaoid = rowData.NGUOITAO_ID;
					var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
					//START L2PT-4775
					if (cfObj.NTU_CDHA_CHECKXOA_USER == '1') {
						if (_nguoitaoid != $("#hidUserID").val()) {
							DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
							return false;
						}
					} else {
						if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
							DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
							return false;
						}
					}
				}
				//Begin_HaNv_08042019: Không cho phép xóa dịch vụ của BN BHYT - L1PT-428
				if (rowData.DOITUONGBENHNHANID == 1) {
					var isNotAllowDel = cfObj.BM2_NOTALLOW_XOA_CLS;
					if (isNotAllowDel == 1) {
						DlgUtil.showMsg("Không cho phép xóa dịch vụ của bệnh nhân BHYT!");
						return false;
					}
				}
				//End_HaNv_08042019
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				//Begin_HaNv_07012019: Không hiển thị thông báo khi xóa phiếu cls thành công - L2PT-516
				var isSaveNotMsg = cfObj.NTU_SAVEOK_NOTMSG;
				//End_HaNv_07012019
				//Begin_HaNv_23032018: Xoa, cap nhat phieu khong can thao tac huy L2DKBD-1032
				var isCapNhatKoCanHuy = cfObj.XOA_CAPNHAT_PHIEU_KOCANHUY;
				if (_trangthai == 1 || _trangthai == 8 || isCapNhatKoCanHuy == 1) {
					DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu CĐHA không?", function(flag) {
						if (flag) {
							if (_trangthai >= 2 && _trangthai != 8) {
								var rs = _self._deleteRequest(rowId, 1);
								if (rs == 1)
									return;
							}
							var _par = [ rowData.MAUBENHPHAMID ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.024", _par.join('$'));
							if (_return == 1) {
								if (isSaveNotMsg == 0) {
									DlgUtil.showMsg("Xóa thành công phiếu CĐHA", undefined, 1500);
								}
								_self._initWidget();
							} else if (_return == 0) {
								DlgUtil.showMsg("Xóa không thành công phiếu CĐHA");
							} else if (_return == -1) {
								if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
									DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
										if (flag) {
											var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
											var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
											if (_returnTmp == 1) {
												DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
												_self._initWidget();
												return 1;
											} else {
												DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
												return 1;
											}
										}
									});
								} else {
									DlgUtil.showMsg("Phiếu CĐHA đã thu tiền nên không được phép xóa");
									return 1;
								}
							}
						}
					});
				} else if (_trangthai >= 2 && _trangthai != 8) {
					DlgUtil.showMsg("Phiếu không ở trạng thái sửa phiếu hoặc hủy nên không thể xóa!");
				}
			}
		},
		_deleteRequestSynDicon : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			//them doan code cua viet
			if (RIS_SERVICE_DOMAIN_NAME != "" && RIS_DELETE_REQUEST != "") {
				var requestCode = rowData.SOPHIEU;
				var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_DELETE_REQUEST;
				var data = {
					requestCode : requestCode,
					cancelReason : "HIS hủy yêu cầu",
					cancelBy : user_id
				}
				$.ajax({
					type : "POST",
					contentType : "application/json; charset=utf-8",
					headers : {
						'Ris-Access-Hash' : getHashRIS(requestCode),
						'Identify-Code' : requestCode
					},
					data : JSON.stringify(data),
					url : request_url,
					success : function(data) {
						//_self._deleteRequest(rowId);
						console.log(JSON.stringify(data));
					},
					error : function(xhr) {
						if (xhr.status == "404" || xhr.status == "0") {
							//_self._deleteRequest(rowId);
							console.log(JSON.stringify(xhr));
						} else {
							DlgUtil.showMsg(xhr.status + " - " + JSON.parse(xhr.responseText).message);
						}
					}
				});
			}
//			else {
//				_self._deleteRequest(rowId);
//			}  
		},
		//Begin_HaNv_24022020: Vẫn cho hủy phiếu khi LIS trả về error 404 - L2PT-16877
		_delRequestProcess : function(rowId, type) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			var _par = [ rowData.MAUBENHPHAMID, 1, 1 ];
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
			if (_return == 1) {
				if (type == 1) {
					//tuyennx_edit_start_20190425 L1PT-661
					if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
						DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công!", undefined, 1500);
					}
					//tuyennx_edit_end_20190425 L1PT-661 	       		    
					_self._initWidget();
				} else {
					return 0;
				}
			} else if (_return == 0) {
				DlgUtil.showMsg("Hủy yêu cầu phiếu thất bại!");
				return 1;
			} else if (_return == -1) {
				if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
					if (rowData.TRANGTHAIMAUBENHPHAM != 2) {
						DlgUtil.showMsg('Phiếu đã được tiếp nhận hoặc đã có kết quả. Không thể yêu cầu hủy');
						return;
					} else {
						DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
							if (flag) {
								var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
								var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
								if (_returnTmp == 1) {
									DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
									_self._initWidget();
									return 1;
								} else {
									DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
									return 1;
								}
							}
						});
					}
				} else {
					DlgUtil.showMsg("Phiếu CĐHA đã thu tiền nên không được hủy yêu cầu");
					return 1;
				}
			}
		},
		//End_HaNv_24022020
		//Begin_HaNv_23032018: them type cho truong hop xoa sua ko can huy L2DKBD-1032
		_deleteRequest : function(rowId, type) {
			if (typeof (type) == "undefined") {
				type = 1;
			}
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
			if (rowData.PHIEUDIEUTRIID > 0 && cfObj.HIS_CHECK_DIEUTRI_KISO == '1') {
				var checkKiSo = jsonrpc.AjaxJson.ajaxCALL_SP_I("PDT.CHECK_KISO", rowData.PHIEUDIEUTRIID);
				if (checkKiSo > 0) {
					DlgUtil.showMsg('Không thể cập nhật thông tin phiếu được gắn với phiếu điều trị đã được kí số!');
					return 1;
				}
			}
			//End_HaNv_05102020
			//Begin_HaNv_050521: Check thu tiền trước khi gọi đến xử lý hủy - L2PT-1765
			var par_tt = [ {
				"name" : "[0]",
				"value" : rowData.MAUBENHPHAMID
			} ];
			var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.MBP.THUTIEN", par_tt);
			if (parseInt(checkThutien) > 0) {
				DlgUtil.showMsg("Phiếu CĐHA đã thu tiền nên không được hủy yêu cầu");
				return 1;
			}
			//End_HaNv_050521
			
			//L2PT-91016
			if (cfObj.NTU_HUY_CHECK_DUYET_MP == '1') {
				var _par = [ rowData.MAUBENHPHAMID, rowData.TIEPNHANID ];
				var data_dv_kb = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.CHECK.DMP", _par.join('$'));
				if(data_dv_kb != '0' && data_dv_kb != '-1'){
					DlgUtil.showMsg("Phiếu đã được duyệt CLS Miễn phí, yêu cầu gỡ duyệt cls trước khi hủy phiếu!");
					return 1;
				}
			}
			
			// L2PT-88453 start
			var check_duyet_cls = jsonrpc.AjaxJson.ajaxCALL_SP_S('CHECK.DUYET.CLS', rowData.MAUBENHPHAMID);
			if (check_duyet_cls == 'PHIEU_DADUYET_CLS') {
				DlgUtil.showMsg("Phiếu CĐHA đã duyệt CLS nên không được hủy yêu cầu");
				return 1;
			};
			// L2PT-88453 end
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
				if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
					DlgUtil.showMsg("Bạn không có quyền hủy yêu cầu phiếu này!");
					return 1;
				}
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2) {
					// Begin_laphm_13062019: không cho huỷ dịch vụ khi có phiếu phụ thu đi kèm - L2PT-5721
					var _sophieudikem = 0;
					var sql_par_chek = [];
					sql_par_chek = RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]);
					var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU.024.4", sql_par_chek);
					var rows = JSON.parse(data);
					if (rows != null && rows.length > 0) {
						_sophieudikem = rows[0].SOPHIEUDIKEM;
					}
					if (_sophieudikem == null)
						_sophieudikem = 0;
					if (_sophieudikem > 0) {
						DlgUtil.showMsg("Phiếu này đã có phụ thu đi kèm,\n không được phép hủy yêu cầu");
						return 1;
					}
					// End_laphm_13062019
					var requestCode = rowData.SOPHIEU;
					if (RIS_CONNECTION_TYPE == "1" && RIS_SERVICE_DOMAIN_NAME != "" && RIS_DELETE_REQUEST != "") {
						var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_DELETE_REQUEST;
						var data = {
							requestCode : requestCode,
							cancelReason : "HIS hủy yêu cầu",
							cancelBy : user_id
						}
						$.ajax({
							type : "POST",
							contentType : "application/json; charset=utf-8",
							headers : {
								'Ris-Access-Hash' : getHashRIS(requestCode),
								'Identify-Code' : requestCode
							},
							data : JSON.stringify(data),
							url : request_url,
							success : function(data) {
								_self._delRequestProcess(rowId, type);
							},
							error : function(xhr) {
								if (xhr.status == "404") {
									_self._delRequestProcess(rowId, type);
								} else if (xhr.status == "0") {
									DlgUtil.showMsg('Không thể hủy yêu cầu bên RIS');
									console.log(JSON.stringify(xhr));
								} else {
									DlgUtil.showMsg(xhr.status + " - " + JSON.parse(xhr.responseText).message);
								}
								return 1;
							}
						});
					}
					//Begin_HaNv_11122020: Tích hợp HL7 - by VietDa - L2PT-30803
					else if (RIS_SERVICE_DOMAIN_NAME != "" && RIS_DELETE_REQUEST != "" && (RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8")) {
						//Beg_HaNv_300522: Check mã nhóm dịch vụ được gửi HL7 - L2PT-19794
						var guiHL7 = false;
						if (cfObj.CDDV_MANHOM_DV_HL7 != '0') {
							var manhomdichvu = jsonrpc.AjaxJson.ajaxCALL_SP_S("GET.MA.NHOMDV", rowData.MAUBENHPHAMID);
							var cfs = cfObj.CDDV_MANHOM_DV_HL7.split(',');
							for (var c = 0; c < cfs.length; c++) {
								if ((manhomdichvu + ';').indexOf(cfs[c] + ';') > -1) {
									guiHL7 = true;
									break;
								}
							}
						} else {
							guiHL7 = true;
						}
						if (guiHL7) {
							var kq = ajaxSvc.HL7_Gateway.sendRequest(requestCode, "CA");
							if (kq != "" && kq.substr(0, 2) == "AA") {
								_self._delRequestProcess(rowId, type);
							} else {
								DlgUtil.showMsg('Không thể hủy yêu cầu bên RIS');
							}
						}
						//HaNv_200622: L2PT-21258
						else {
							//Beg_HaNv_221223: Gửi api với DV không thuộc nhóm gửi HL7 - L2PT-67846
							if (hospital_id == '30360') {
								var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_DELETE_REQUEST;
								var data = {
									requestCode : requestCode,
									cancelReason : "HIS hủy yêu cầu",
									cancelBy : user_id
								}
								$.ajax({
									type : "POST",
									contentType : "application/json; charset=utf-8",
									headers : {
										'Ris-Access-Hash' : getHashRIS(requestCode),
										'Identify-Code' : requestCode
									},
									data : JSON.stringify(data),
									url : request_url,
									success : function(data) {
										_self._delRequestProcess(rowId, type);
									},
									error : function(xhr) {
										if (xhr.status == "404") {
											_self._delRequestProcess(rowId, type);
										} else if (xhr.status == "0") {
											DlgUtil.showMsg('Không thể hủy yêu cầu bên RIS');
										} else {
											DlgUtil.showMsg(xhr.status + " - " + JSON.parse(xhr.responseText).message);
										}
										return 1;
									}
								});
							}
							//End_HaNv_221223
							else {
								_self._delRequestProcess(rowId, type);
							}
						}
						//End_HaNv_300522
					}
					//End_HaNv_11122020
					else {
						_self._delRequestProcess(rowId, type);
					}
				} else if (_trangthai == 1) {
					DlgUtil.showMsg("Phiếu đã được hủy yêu cầu!", undefined, 1500);
					return 1;
				} else if (_trangthai == 8) {
					if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
						DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
							if (flag) {
								var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
								var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
								if (_returnTmp == 1) {
									DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
									_self._initWidget();
									return 1;
								} else {
									DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
									return 1;
								}
							}
						});
					} else {
						DlgUtil.showMsg("Phiếu không ở trạng thái đã gửi nên không thể hủy yêu cầu");
						return 1;
					}
				} else if (_trangthai > 2 && _trangthai != 8) {
					DlgUtil.showMsg("Phiếu không ở trạng thái đã gửi phiếu nên không thể hủy yêu cầu");
					return 1;
				}
			}
		},
		//End_HaNv_23032018
		_sendRequest : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				if (_self.options._modeView != "2") {
					var _nguoitaoid = rowData.NGUOITAO_ID;
					var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
					if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
						DlgUtil.showMsg("Bạn không có quyền gửi yêu cầu phiếu này!");
						return false;
					}
				}
				// Check chuyen khoa dieu tri khong co quyen gui phieu
				if ($("#hidKHOAID").val() && cfObj.NTU_DTKH_ALLOW_UPDATE_DVKT != '1') {//L2PT-51997 cấu hình cho phép gửi lại phiếu đối với điều trị kết hợp
					var check_par = [];
					check_par.push({
						"name" : "[0]",
						"value" : $("#hidKHOAID").val()
					});
					check_par.push({
						"name" : "[1]",
						"value" : rowData.KHAMBENHID
					});
					var checkDtkh = jsonrpc.AjaxJson.getOneValue("CHECK.DTKH", check_par);
					if (checkDtkh !== "0") {
						DlgUtil.showMsg("Chuyên khoa điều trị kết hợp không được phép gửi yêu cầu!");
						return false;
					}
				}
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 1 || _trangthai == 8) {
					var _par = [ rowData.MAUBENHPHAMID, 2, 0 ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
					if (_return == 1) {
						if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
							DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công!");
						}
						_self._initWidget();
						//Begin_HaNv_11122020: Tích hợp HL7 - by VietDa - L2PT-30803
						if (RIS_SERVICE_DOMAIN_NAME != "" && RIS_DELETE_REQUEST != "" && (RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8")) {
							//Beg_HaNv_300522: Check mã nhóm dịch vụ được gửi HL7 - L2PT-19794
							var guiHL7 = false;
							if (cfObj.CDDV_MANHOM_DV_HL7 != '0') {
								var manhomdichvu = jsonrpc.AjaxJson.ajaxCALL_SP_S("GET.MA.NHOMDV", rowData.MAUBENHPHAMID);
								var cfs = cfObj.CDDV_MANHOM_DV_HL7.split(',');
								for (var c = 0; c < cfs.length; c++) {
									if ((manhomdichvu + ';').indexOf(cfs[c] + ';') > -1) {
										guiHL7 = true;
										break;
									}
								}
							} else {
								guiHL7 = true;
							}
							if (guiHL7) {
								ajaxSvc.HL7_Gateway.sendRequest(rowData.SOPHIEU, "XO");
							}
							//End_HaNv_300522
						}
						//End_HaNv_11122020
					} else if (_return == 0) {
						DlgUtil.showMsg("Gửi yêu cầu phiếu thất bại!");
					}
				} else {
					DlgUtil.showMsg("Phiếu đã được gửi yêu cầu!");
				}
			}
		},
		_exportPCDHA : function(rowId) {
			var _self = this;
			var hopital = cfObj.OPT_HOSPITAL_ID;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//Begin_HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
				var usingReportCode = cfObj.CDDV_USING_REPORT_CODE;
				//End_HaNv_05082019
				if (hopital == 965) {
					//lay loai dt bhyt
					doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
					if (doituongbenhnhanid == 1) {
						//lay loai dich vu la bhyt hay thuong
						var _loaidichvu = 0;
						var _par_loai = [ rowData.MAUBENHPHAMID ];
						var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loai.join('$'));
						if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
							for (var i = 0; i < arr_loaidichvu.length; i++) {
								_loaidichvu = arr_loaidichvu[i].BHYT;
								if (_loaidichvu == 1) {
									// openReport('window', "PHIEU_CDHA_A4_965", "pdf", par);
									_self._printOrPrintCa('PHIEU_CDHA_A4_965', par);
								} else
									// openReport('window', "PHIEU_CDHADICHVU_A4_965", "pdf", par);
									_self._printOrPrintCa('PHIEU_CDHADICHVU_A4_965', par);
							}
						}
					} else {
						// openReport('window', "PHIEU_CDHADICHVU_A4_965", "pdf", par);
						_self._printOrPrintCa('PHIEU_CDHADICHVU_A4_965', par);
					}
				}
				//Beg_HaNv_110822: In riêng các mẫu phiếu CLS đối với BN khám sức khỏe theo đoàn - L2PT-23549
				else if (cfObj.INCLS_KSK_THEO_DOAN == '1' && $('#hidLOAITIEPNHANID').val() == '5') {
					var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					} ];
					// openReport('window', "KSK_PHIEU_CDHA", "pdf", par);
					_self._printOrPrintCa('KSK_PHIEU_CDHA', par);
				}
				//End_HaNv_110822
				else if (usingReportCode == 1) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
					if (i_report_code != null && i_report_code.length > 0) {
						for (var i = 0; i < i_report_code.length; i++) {
							var _report_code = i_report_code[i].REPORT_CODE;
							var par_rpt = [ {
								name : 'maubenhphamid',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							}, {
								name : 'report_code',
								type : 'String',
								value : _report_code
							} ];
							//Begin_HaNv_040321: Tích hợp phiếu in BDHNI - BVTM-10
							if (hopital == 10284) {
								par_rpt.push({
									name : 'khambenhid',
									type : 'String',
									value : _self.options._khambenhid
								});
								par_rpt.push({
									name : 'maubenhphamids',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								});
								par_rpt.push({
									name : 'id_maubenhpham',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								});
								//Beg_HaNv_061021: Tùy biến formCall để in báo cáo - BVTM-5873
								if (_self.options._formCall == 'TN_NGT2') {
									par_rpt.push({
										name : 'ngoaitiepnhan',
										type : 'String',
										value : '1'
									});
								}
								//End_HaNv_061021
								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", _par_code.join('$'));
								for (var i = 0; i < data_ar.length; i++) {
									var hinh_anh = data_ar[i]["DUONGDANFILE"];
									if (hinh_anh != "" && hinh_anh != null)
										par_rpt.push({
											name : 'hinh_anh' + i,
											type : 'Image',
											value : hinh_anh
										});
									var ten_file = data_ar[i]["TENFILE"];
									if (ten_file != "" && ten_file != null)
										par_rpt.push({
											name : 'ten_file' + i,
											type : 'String',
											value : ten_file
										});
								}
							}
							//End_HaNv_040321
							// openReport('window', _report_code, "pdf", par_rpt);
							_self._printOrPrintCa(_report_code, par_rpt);
						}
					} else {
						var par_rpt = [ {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}, {
							name : 'report_code',
							type : 'String',
							value : 'PHIEU_CDHA_A4'
						} ];
						// openReport('window', "PHIEU_CDHA_A4", "pdf", par_rpt);
						_self._printOrPrintCa('PHIEU_CDHA_A4', par_rpt);
					}
				} else if (hopital == 957 || hopital == 38440) {//HaNv_140122: L2PT-13716
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSLOAI.DTCLS", _par_code.join('$'));
					var _loaidoituong = '';
					if (lstDoiTuong != null && lstDoiTuong.length > 0) {
						for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
							_loaidoituong = _loaidoituong + lstDoiTuong[i1].LOAIDOITUONG;
						}
					}
					//end tuyendv 0406
					var par_rpt = [ {
						name : 'nhommaubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'nhomdoituong',
						type : 'String',
						value : _loaidoituong
					} ];
					// openReport('window', "PHIEU_CLS_ALL", "pdf", par_rpt);
					_self._printOrPrintCa('PHIEU_CLS_ALL', par_rpt);
				} else if (hopital == 1014) {
					var _dtbnid = rowData.DOITUONGBENHNHANID;
					//START L2HOTRO-12397
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						// CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_BHYT', 'pdf', par, null, true, true);
						_self._printOrPrintCa('PHIEU_CHIDINH_CLS_BHYT', par, '1', null, true, true);
					} else {
						// openReport('window', "PHIEU_CHIDINH_CLS_BHYT", "pdf", par);
						_self._printOrPrintCa('PHIEU_CHIDINH_CLS_BHYT', par);
					}
				}
				//Begin_HaNv_24092019: Tich hop report CLS - nguoiphoihop: TuyenDv - L2PT-8688
				else if (hopital == 987) {
					if ($('#hidLOAITIEPNHANID').val() == '1') { //khambenh
						if (rowData.DOITUONGBENHNHANID == '1') {
							// openReport('window', "NGT_PHIEU_CDHA_BH", "pdf", par);
							_self._printOrPrintCa('NGT_PHIEU_CDHA_BH', par);
						} else {
							// openReport('window', "NGT_PHIEU_CDHA_DV", "pdf", par);
							_self._printOrPrintCa('NGT_PHIEU_CDHA_DV', par);
						}
					} else {
						if (rowData.DOITUONGBENHNHANID == '1') {
							// openReport('window', "PHIEU_CDHA_BH", "pdf", par);
							_self._printOrPrintCa('PHIEU_CDHA_BH', par);
						} else {
							// openReport('window', "PHIEU_CDHA_DV", "pdf", par);
							_self._printOrPrintCa('PHIEU_CDHA_DV', par);
						}
					}
				}
				//End_HaNv_24092019
				//Begin_HaNv_22082019: Tich hop report - nguoiyeucau: TruongLt - L2PT-7856
				else if (hopital == 939 && $grid.jqGrid("getRowData", 1).MANHOM == '03.XQUANG') {
					// openReport('window', "PHIEU_XQUANG_A5", "pdf", par);
					_self._printOrPrintCa('PHIEU_XQUANG_A5', par);
				}
				//End_HaNv_22082019
				else {
					// openReport('window', "PHIEU_CDHA", "pdf", par);
					_self._printOrPrintCa('PHIEU_CDHA', par);
				}
			}
		},
		//START L2HOTRO-12397
		_exportPCDHAVIEW : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				var _dtbnid = rowData.DOITUONGBENHNHANID;
				if (_dtbnid == 1) {
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_BHYT', 'pdf', par, null, false, true);
					}
				} else {
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_YEUCAU', 'pdf', par, null, false, true);
					}
				}
			}
		},
		//END L2HOTRO-12397
		//START -- HISL2TK-611 --hongdq
		_exportSelected : function(rowId) {
			var _self = this;
			paramInput = {
				benhnhanid : _self.options._benhnhanid,
				khambenhid : _self.options._khambenhid,
				hosobenhanid : _self.options._hosobenhanid,
				lnmbp : _self.options._lnmbp,
				loaitnid : $('#hidLOAITIEPNHANID').val(),//HaNv_110822 - L2PT-23549
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu CĐHA", 1100, 600);
			DlgUtil.open("divDlgDeleteXN");
		},
		//END -- HISL2TK-611 --hongdq
		_exportPCDHAAuto : function(rowId) {
			var _self = this;
			var _type = 'pdf';
			if (cfObj.HIS_FILEEXPORT_TYPE != '0') {
				_type = cfObj.HIS_FILEEXPORT_TYPE;
			}
			var hopital = cfObj.OPT_HOSPITAL_ID;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//Begin_HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
				var usingReportCode = cfObj.CDDV_USING_REPORT_CODE;
				//End_HaNv_05082019
				if (hopital == 965) {
					var rpName = "VNPTHIS_IN_A4_";
					rpName += rowData.SOPHIEU;
					rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
					rpName += "." + _type;
					//lay loai dt bhyt
					doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
					if (doituongbenhnhanid == 1) {
						//lay loai dich vu la bhyt hay thuong
						var _loaidichvu = 0;
						var _par_loai = [ rowData.MAUBENHPHAMID ];
						var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loai.join('$'));
						if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
							for (var i = 0; i < arr_loaidichvu.length; i++) {
								_loaidichvu = arr_loaidichvu[i].BHYT;
								if (_loaidichvu == 1) {
									//openReport('window', "PHIEU_CDHA_A4_965", "pdf", par);  
									CommonUtil.inPhieu('window', 'PHIEU_CDHA_A4_965', _type, par, rpName);
								} else {
									//openReport('window', "PHIEU_CDHADICHVU_A4_965", "pdf", par);
									CommonUtil.inPhieu('window', 'PHIEU_CDHADICHVU_A4_965', _type, par, rpName);
								}
							}
						}
					} else {
						//openReport('window', "PHIEU_CDHADICHVU_A4_965", "pdf", par);   
						CommonUtil.inPhieu('window', 'PHIEU_CDHADICHVU_A4_965', _type, par, rpName);
					}
				} else if (usingReportCode == 1) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
					if (i_report_code != null && i_report_code.length > 0) {
						for (var i = 0; i < i_report_code.length; i++) {
							var _report_code = i_report_code[i].REPORT_CODE;
							var par_rpt = [ {
								name : 'maubenhphamid',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							}, {
								name : 'report_code',
								type : 'String',
								value : _report_code
							} ];
							//Begin_HaNv_040321: Tích hợp phiếu in BDHNI - BVTM-10
							if (hopital == 10284) {
								par_rpt.push({
									name : 'khambenhid',
									type : 'String',
									value : _self.options._khambenhid
								});
								par_rpt.push({
									name : 'maubenhphamids',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								});
								par_rpt.push({
									name : 'id_maubenhpham',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								});
								//Beg_HaNv_061021: Tùy biến formCall để in báo cáo - BVTM-5873
								if (_self.options._formCall == 'TN_NGT2') {
									par_rpt.push({
										name : 'ngoaitiepnhan',
										type : 'String',
										value : '1'
									});
								}
								//End_HaNv_061021
								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", _par_code.join('$'));
								for (var i = 0; i < data_ar.length; i++) {
									var hinh_anh = data_ar[i]["DUONGDANFILE"];
									if (hinh_anh != "" && hinh_anh != null)
										par_rpt.push({
											name : 'hinh_anh' + i,
											type : 'Image',
											value : hinh_anh
										});
									var ten_file = data_ar[i]["TENFILE"];
									if (ten_file != "" && ten_file != null)
										par_rpt.push({
											name : 'ten_file' + i,
											type : 'String',
											value : ten_file
										});
								}
							}
							//End_HaNv_040321
							openReport('window', _report_code, "pdf", par_rpt);
						}
					} else {
						var par_rpt = [ {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}, {
							name : 'report_code',
							type : 'String',
							value : 'PHIEU_CDHA_A4'
						} ];
						openReport('window', "PHIEU_CDHA_A4", "pdf", par_rpt);
					}
				} else if (hopital == 957 || hopital == 38440) {//HaNv_140122: L2PT-13716
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSLOAI.DTCLS", _par_code.join('$'));
					var _loaidoituong = '';
					if (lstDoiTuong != null && lstDoiTuong.length > 0) {
						for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
							_loaidoituong = _loaidoituong + lstDoiTuong[i1].LOAIDOITUONG;
						}
					}
					//end tuyendv 0406
					var par_rpt = [ {
						name : 'nhommaubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'nhomdoituong',
						type : 'String',
						value : _loaidoituong
					} ];
					openReport('window', "PHIEU_CLS_ALL", "pdf", par_rpt);
				} else if (hopital == 1014) {
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_BHYT', 'pdf', par, null, true, true);
					} else {
						openReport('window', "PHIEU_CHIDINH_CLS_BHYT", "pdf", par);
					}
					/*var _dtbnid = rowData.DOITUONGBENHNHANID;
					if(_dtbnid==1){
					 openReport('window', "PHIEU_CHIDINH_CLS_BHYT", "pdf", par);   
					}else{
					 openReport('window', "PHIEU_CHIDINH_CLS_YEUCAU", "pdf", par);
					}*/
				} else {
					var rpName = "VNPTHIS_IN_A5_";
					rpName += rowData.SOPHIEU;
					rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
					rpName += "." + _type;
					//openReport('window', "PHIEU_CDHA", "pdf", par);
					if (rowData.DOITUONGBENHNHANID)
						CommonUtil.inPhieu('window', 'PHIEU_CDHA', _type, par, rpName);
				}
			}
		},
		_exportKetQuaPCDHA : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2 || _trangthai == 1) {
					DlgUtil.showMsg("Phiếu chưa thực hiện nên chưa có kết quả để in!");
					return false;
				}
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				}, {
					name : 'maubenhphamids',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				}, {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//Begin_HaNv_201220: Bổ sung hình ảnh khi in - By TuyenDv - L2PT-31603
				var param = [ rowData.MAUBENHPHAMID ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var max = (data_ar.length > 6 ? 6 : data_ar.length);
				for (var i = 0; i < max; i++) {
					var hinh_anh = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh != "" && hinh_anh != null) {
						par.push({
							name : 'hinh_anh' + i,
							type : 'Image',
							value : hinh_anh
						});
					}
				}
				for (var i = 0; i < max; i++) {
					var ten_file = data_ar[i]["TENFILE"];
					if (ten_file != "" && ten_file != null) {
						par.push({
							name : 'ten_file' + i,
							type : 'String',
							value : ten_file
						});
					}
				}
				//End_HaNv_201220
				//BVTM-5657
				var reccount = $('#' + _self.containerId + _self.options._gridCDHADetail).getGridParam("reccount");
				if (hospital_id == "10284") {
					var dvkbDatas = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getGridParam', 'selarrrow');
					if (dvkbDatas.length == 0) {
						for (var i = 1; i <= reccount; i++) {
							var row = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', i);
							var parInnhieu = par.concat([ {
								name : 'dichvukhambenhid',
								type : 'String',
								value : row.DICHVUKHAMBENHID
							} ]);
							openReport('window', 'CLS_KQ_CDHA', 'pdf', parInnhieu);
						}
					} else {
						dvkbDatas.forEach(function(item) {
							var dvkb = $('#' + _self.containerId + _self.options._gridCDHADetail).jqGrid('getRowData', item);
							var parInnhieu = par.concat([ {
								name : 'dichvukhambenhid',
								type : 'String',
								value : dvkb.DICHVUKHAMBENHID
							} ]);
							openReport('window', 'CLS_KQ_CDHA', 'pdf', parInnhieu);
						});
					}
					return;
				} else {
					//Beg_HaNv_101022: Tích hợp report_code kết quả - L2PT-27418
					if (cfObj.INPHIEU_THEO_DV == '1') {
						var _par_code = [ rowData.MAUBENHPHAMID ];
						var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE_KQ", _par_code.join('$'));
						for (var i = 0; i < i_report_code.length; i++) {
							var _report_code = i_report_code[i].REPORT_CODE;
							var k = _report_code.split(';');
							if (k.length > 0) {
								for (var j = 0; j < k.length; j++) {
									openReport('window', k[j], 'pdf', par);
								}
							} else {
								openReport('window', _report_code, 'pdf', par);
							}
						}
					} else {
						openReport('window', 'CLS_KQ_CDHA', 'pdf', par);
					}
					//End_HaNv_101022
				}
			}
		},
		_exportKetQuaPCDHAAuto : function(rowId) {
			var _self = this;
			var _type = 'pdf';
			if (cfObj.HIS_FILEEXPORT_TYPE != '0') {
				_type = cfObj.HIS_FILEEXPORT_TYPE;
			}
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2 || _trangthai == 1) {
					DlgUtil.showMsg("Phiếu chưa thực hiện nên chưa có kết quả để in!");
					return false;
				}
				var rpName = "VNPTHIS_IN_A4_";
				rpName += rowData.SOPHIEU;
				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
				rpName += "." + _type;
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//openReport('window', 'CLS_KQ_CDHA', 'pdf', par);   
				CommonUtil.inPhieu('window', 'CLS_KQ_CDHA', _type, par, rpName);
			}
		},
		//Beg_HaNv_120123: bổ sung menu In phiếu CĐHA viết tay - L2PT-33633
		_exportKetQuaPCDHAVT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				openReport('window', 'CLS_KETQUA_CDHA_VIETTAY', 'pdf', par);
			}
		},
		//End_HaNv_120123
		//Beg_HaNv_131023: Inbarcode CLS - L2PT-53101
		_exportBarcode : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				openReport('window', 'RPT_CLS_BARCODE_A6', 'pdf', par);
			}
		},
		//End_HaNv_131023
		_editOrgDone : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
				if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
					DlgUtil.showMsg("Bạn không có quyền sửa phòng thực hiện phiếu này!");
					return false;
				}
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 3) {
					DlgUtil.showMsg("Phiếu đã hoàn thành nên bạn không thể sửa phòng thực hiện");
					return false;
				}
				//mo popup nhap benh nhan khi dbclick on row
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					loaiPhieu : _self.options._lnmbp,
					org_type : 7,
					sophieu : rowData.SOPHIEU
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgEditOrgDone", "divDlg", "manager.jsp?func=../noitru/NTU02D038_SuaPhongThucHien", paramInput, "Chọn phòng thực hiện", 900, 450);
				DlgUtil.open("divDlgEditOrgDone");
			}
		},
		_updatePhieuCDHA : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
				//START L2PT-4775
				if (cfObj.NTU_CDHA_CHECKXOA_USER == '1') {
					if (_nguoitaoid != $("#hidUserID").val()) {
						DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
						return false;
					}
				} else {
					if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
						DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
						return false;
					}
				}
				//Beg_HaNv_080721: Check thu tiền trước khi gọi đến hàm xử lý - L2PT-5243
				var par_tt = [ {
					"name" : "[0]",
					"value" : rowData.MAUBENHPHAMID
				} ];
				var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.MBP.THUTIEN", par_tt);
				if (parseInt(checkThutien) > 0) {
					DlgUtil.showMsg("Phiếu CĐHA đã thu tiền nên không thể cập nhật phiếu!");
					return false;
				}
				//End_HaNv_080721
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				//Begin_HaNv_23032018: Xoa, cap nhat phieu khong can thao tac huy L2DKBD-1032
				var isCapNhatKoCanHuy = cfObj.XOA_CAPNHAT_PHIEU_KOCANHUY;
				if (_trangthai <= 1 || isCapNhatKoCanHuy == 1) {
					if (_trangthai > 1) {
						var rs = _self._deleteRequest(rowId, 0);
						if (rs == 1)
							return;
					}
					//End_HaNv_23032018
					//mo popup nhap benh nhan khi dbclick on row
					var paramInput = {
						benhnhanid : rowData.BENHNHANID,
						mabenhnhan : rowData.MABENHNHAN,
						khambenhid : rowData.KHAMBENHID,
						tiepnhanid : rowData.TIEPNHANID,
						hosobenhanid : rowData.HOSOBENHANID,
						doituongbenhnhanid : rowData.DOITUONGBENHNHANID,
						loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),//HaNv_07052020: Fix lỗi PDT - L2PT-20390
						maubenhphamid : rowData.MAUBENHPHAMID,
						loaiPhieu : _self.options._lnmbp,
						subDeptId : $("#hidPHONGID").val(),
						modeFunction : _self.options._modeView,
						modeTiepDon : _self.options._modeTiepDon
					//Begin_HaNv_26072018
					};
					//Begin_HaNv_30112020: Xây dựng giao diện chỉ định DV mới cho DLKHA - L2PT-30889
					var cddvDLKHA = cfObj.CDDV_GIAODIEN_KHA == '1' ? true : false;
					if (cddvDLKHA) {
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV_KHA" + "&loaidichvu=" + 5, paramInput,
								"Cập nhật phiếu chuẩn đoán hình ảnh", 1300, 600);
					} else {
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, paramInput,
								"Cập nhật phiếu chuẩn đoán hình ảnh", 1300, 600);
					}
					//End_HaNv_30112020
					DlgUtil.open("divDlgDichVu");
				} else {
					DlgUtil.showMsg("Không thể sửa phiếu này!\nPhiếu này đã hoặc đang được xử lý");
				}
			}
		},
		_viewCDHADetail : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var sql_par1 = [];
				sql_par1.push({
					"name" : "[0]",
					"value" : rowData.MAUBENHPHAMID
				});
				GridUtil.loadGridBySqlPage(_self.containerId + _self.options._gridCDHADetail, "NT.024.2", sql_par1);
			}
		},
		// Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
		destroy : function() {
			// this.element.removeStuff();
			// For UI 1.8, destroy must be invoked from the
			// base widget
			$.Widget.prototype.destroy.call(this);
			// For UI 1.9, define _destroy instead and don't
			// worry about
			// calling the base widget
		},
		methodB : function(event) {
			//_trigger dispatches callbacks the plugin user
			// can subscribe to
			// signature: _trigger( "callbackName" , [eventObject],
			// [uiObject] )
			// eg. this._trigger( "hover", e /*where e.type ==
			// "mouseenter"*/, { hovered: $(e.target)});
			console.log("methodB called");
		},
		methodA : function(event) {
			this._trigger("dataChanged", event, {
				key : "someValue"
			});
		},
		// Respond to any changes the user makes to the
		// option method
		_setOption : function(key, value) {
			switch (key) {
				case "someValue":
					//this.options.someValue = doSomethingWith( value );
				break;
				default:
					//this.options[ key ] = value;
				break;
			}
			// For UI 1.8, _setOption must be manually invoked
			// from the base widget
			$.Widget.prototype._setOption.apply(this, arguments);
			if (key == '_benhnhanid') {
				this._initWidget();
			}
			// For UI 1.9 the _super method can be used instead
			// this._super( "_setOption", key, value );
		},
		// Xử lý sự kiện liên quan ký CA => START
		_kyCA : function(rowId) {
			isKySo = true;
			sign_type = '1';
			var _self = this;
			_self._exportPCDHA(rowId);
		},
		_huyKyCA : function(rowId) {
			isKySo = true;
			sign_type = '2';
			var _self = this;
			_self._exportPCDHA(rowId);
		},
		_exportKyCA : function(rowId) {
			isKySo = true;
			sign_type = '0';
			var _self = this;
			_self._exportPCDHA(rowId);
		},
		_exportKyKQ : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			var data_ar2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", rowData.MAUBENHPHAMID + "$");
			if (data_ar2.length == '0') {
				DlgUtil.showMsg("Phiếu kết quả chưa thực hiện ký số/điện tử!");
				return;
			}
			for (var i = 0; i < data_ar2.length; i++) {
				var paramHashed = data_ar2[i].PARAM_HASHED;
				if (paramHashed != '') {
					CommonUtil.openReportGetCA3(paramHashed, false);
				} else {
					var par_rpt_KySo = [ {
						name : 'HOSOBENHANID',
						type : 'String',
						value : $('#hidHOSOBENHANID').val()
					}, {
						name : 'MAUBENHPHAMID',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'ID_MAUBENHPHAM',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'RPT_CODE',
						type : 'String',
						value : data_ar2[0].REPORTCODE
					} ];
					CommonUtil.openReportGetCA2(par_rpt_KySo, false);
				}
			}
		},
		_printOrPrintCa : function(_rptCode, _par, _typePrint, _rpName, _isPrintDirect, _isCountPrt) {
			var _self = this;
			if (isKySo) {
				isKySo = false;
				_par.push({
					name : 'hosobenhanid',
					type : 'String',
					value : $('#hidHOSOBENHANID').val()
				});
				_par.push({
					name : 'RPT_CODE',
					type : 'String',
					value : _rptCode
				});
				if (sign_type == '0') {
					CommonUtil.openReportGetCA2(_par, false);
				} else {
					CommonUtil.kyCA(_par, sign_type);
					EventUtil.setEvent("eventKyCA", function(e) {
						DlgUtil.showMsg(e.res);
						_self._initWidget();
					});
				}
			} else {
				if (_typePrint == '1') {
					CommonUtil.inPhieu('window', _rptCode, 'pdf', _par, _rpName, _isPrintDirect, _isCountPrt);
				} else {
					openReport('window', _rptCode, "pdf", _par);
				}
			}
		}, 
		_caRpt : function(signType) {
			var _self = this;
			let userCaConf = CaUtils.getCACachingConfig(_rptKyCa);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptKyCa);
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				var catype = row.CA_TYPE;
				var kieuky = row.KIEUKY;
				if (catype == '3' || catype == '6') {
					var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					let
					_paramInput = {
						params : null,
						smartca_method : 0
					};
					EventUtil.setEvent("dlgCaLogin_confirm", function() {
						DlgUtil.close("divCALOGIN");
						let
						_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
						causer = _hisl2SmartCa.token.refresh_token;
						capassword = _hisl2SmartCa.token.access_token;
						smartcauser = _hisl2SmartCa.user.uid;
						_self._caRpt2(signType, catype, kieuky);
					});
					let
					hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
						_paramInput.smartca_method = 1;
						if (_self.KYSO_TUDONG_KYDIENTU == '1') {
							let
							_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
							causer = _hisl2SmartCa.token.refresh_token;
							capassword = _hisl2SmartCa.token.access_token;
							smartcauser = _hisl2SmartCa.user.uid;
							_self._caRpt2(signType, catype, kieuky);
						} else {
							let
							_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						}
					} else {
						EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
							if (e.data && e.data.token && e.data.token.access_token) {
								_paramInput.smartca_method = 1;
							}
							DlgUtil.close("dlgCA_SMARTCA_LOGIN");
							let
							_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						});
						DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {
							isSignPopup : true
						}, "Smart Ca Login", 500, 650);
						DlgUtil.open("dlgCA_SMARTCA_LOGIN");
						return;
					}
				} else if (catype == '5') {
					_self._caRpt2(signType, catype, kieuky);
				} else if (userCaConf && _self.KYSO_TUDONG_KYDIENTU == '1') {
					causer = userCaConf.USER_NAME;
					capassword = userCaConf.PASS_WORD;
					_self._caRpt2(signType, catype, kieuky);
				} else {
					EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
						causer = e.username;
						capassword = e.password;
						DlgUtil.close("divCALOGIN");
						_self._caRpt2(signType, catype, kieuky);
					});
					EventUtil.setEvent("dlgCaLogin_close", function(e) {
						DlgUtil.close("divCALOGIN");
					});
					var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
					popup.open("divCALOGIN");
				}
			}
		},
		_caRpt2 : function(signType, catype, kieuky) {
			var _self = this;
			msgKyca = '';
			var rowDatas = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid("getGridParam", "selarrrow");
			rowDatas.forEach(function(el) {
				_self._caRpt3(el, signType, catype, kieuky);
				_self._initWidget();
			});
			DlgUtil.showMsg(msgKyca);
		},
		_caRpt3 : function(rowId, signType, catype, kieuky) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowId);
			if (signType == '1' && rowData.FLAG_CA == '1') {
				var _msg = rowData.SOPHIEU + ' - Phiếu đã thực hiện Ký điện tử';
				msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
				return;
			}
			var par_rpt_KySo = [ {
				name : 'HOSOBENHANID',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			par_rpt_KySo.push({
				name : 'RPT_CODE',
				type : 'String',
				value : _rptKyCa
			});
			par_rpt_KySo.push({
				name : 'maubenhphamid',
				type : 'String',
				value : rowData.MAUBENHPHAMID
			});
			var oData = {
				sign_type : signType,
				causer : causer,
				capassword : capassword,
				smartcauser : smartcauser,
				params : par_rpt_KySo
			};
			if (catype == '5') {
				CommonUtil.kyCA(par_rpt_KySo, signType, true);
				EventUtil.setEvent("eventKyCA", function(e) {
					DlgUtil.showMsg(e.res);
				});
			} else {
				var msg = CommonUtil.caRpt(oData, _rptKyCa, true, '', true, kieuky, catype);
				var _code = msg.split("|")[0];
				var _msg = rowData.SOPHIEU + ' - ' + msg.split("|")[1];
				var _caid = msg.split("|")[2];
				msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
				//update ky chot
				if (catype == '3') {
					if (_code == '0' || _code == '7' || _code == '8') {
						var intervalId = null;
						var smartCaLoaderFunction = function() {
							console.log("smartCaLoaderFunction is running!");
							var _sql_par = [];
							_sql_par.push({
								"name" : "[0]",
								value : _caid
							});
							var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
							if (fl == 1) {
								// bat phieu in
								CommonUtil.openReportGetCA2(_par, false);
								clearInterval(intervalId);
							}
						};
						intervalId = setInterval(smartCaLoaderFunction, 4000);
					}
				}
			}
		},
		_printSelectedCASigned : function() {
			var _self = this;
			var rowDatas = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._gridCDHA).jqGrid('getRowData', rowDatas[j]);
					if (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99') {
						var _par = [ {
							name : 'HOSOBENHANID',
							type : 'String',
							value : $("#hidHOSOBENHANID").val()
						}, {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}, {
							name : 'RPT_CODE',
							type : 'String',
							value : _rptKyCa
						} ];
						CommonUtil.openReportGetCA2(_par, false);
					}
				}
			}
		}
	// Xử lý sự kiện liên quan ký CA => END
	});
	//Beg_HaNv_290324: setSelection onContextMenu(chuột trái) với grid checkbox không bỏ chọn với dòng đã được chọn - L2PT-74241
	function _setSelectionOnContextMenu(_gridId, _rowId) {
		var grid = $('#' + _gridId);
		var rowDatas = grid.jqGrid("getGridParam", "selarrrow");
		var check = true;
		if (rowDatas.length > 0) {
			for (var j = 0; j < rowDatas.length; j++) {
				if (rowDatas[j] == _rowId) {
					check = false;
				}
			}
			if (check) {
				grid.setSelection(_rowId);
			}
		} else {
			grid.setSelection(_rowId);
		}
	}
	//End_HaN_290324
})(jQuery);
