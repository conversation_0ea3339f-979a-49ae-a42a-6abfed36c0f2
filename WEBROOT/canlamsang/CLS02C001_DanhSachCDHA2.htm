<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css"/>
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css"/>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20160105"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css"/>
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>
<script type="text/javascript" src="../canlamsang/RISConnector.js?v=20171122"></script>
<script type="text/javascript" src="../canlamsang/GoiBenhNhan.js?v=2"></script>
<!-- nghiant 27102017 -->
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<!--end nghiant 27102017 -->
<script type="text/javascript" src="https://code.responsivevoice.org/responsivevoice.js?key=9L20KRrj&v=20200401"></script>
<script type="text/javascript" src="../common/script/GoogleCloudService.js?v=20200317"></script>
<script type="text/javascript" src="../common/script/barcodedetect/jquery.scannerdetection.js"></script>
<script type="text/javascript" src="../canlamsang/CLS02C001_DanhSachCDHA2.js?v=202401114"></script>

<style>
	.ui-jqgrid tr.jqgrow td {
		white-space: nowrap !important;
		height:auto;
		vertical-align:text-top;
		padding-top:2px;
	}
	#toolbarId .wd100 {
	    float: none !important;
	}
	#toolbarIdbtnTiepNhanTheoLo {	
	    width: 150px;
	}
	.sitdownplease {
		vertical-align: middle !important;
		line-height: 18px;
		padding-bottom: 2px !important;
	}
</style>

<div width="100%" id="divMain" class="container" help="help_VI_1_NhapthongtinthuchienCDHA">
	<div id='toolbarId'></div>
	<div id="goibenhnhan">
		<div id="playlist" style="display: none;">
	        <audio controls autoplay></audio>
	    </div>
	</div>
	
	<div class="panel-body mgt5">
		<div class="col-md-3 low-padding">
			<div class="col-md-6 low-padding">
				<label class="radio-inline" for="rdoChoThucHien">
					<input id="rdoChoThucHien" type="radio" name="rdoTrangThai" value="2" checked>BN đang làm
				</label>
			</div>
			<div class="col-md-6 low-padding">
				<label class="radio-inline" for="rdoTraKetQua">
					<input id="rdoTraKetQua" type="radio" name="rdoTrangThai" value="3">BN đã làm
				</label>	
			</div>				
		</div>
		<div class="col-md-9 low-padding">
			<div class="col-md-6 low-padding">
				<div class="col-md-4 low-padding">
					<label class="mgl10">Phòng thực hiện</label> 
				</div>
				<div class="col-md-8 low-padding">
					<select class="form-control input-sm" id="cboPhongThucHien" style="width:90%">
						<option value=""></option>
					</select> 
				</div>	
			</div> 
			<div class="col-md-6 low-padding">
				<div class="col-md-1 low-padding">
					<label>Từ</label>
				</div>
				<div class="col-md-3 low-padding"> 
					<div class='input-group'>							
						<input class="form-control input-sm" id="txtTuNgay" valrule="Từ ngày,required|datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div>
					
				</div>
				<div class="col-md-1 low-padding"></div>
				<div class="col-md-1 low-padding">
					<label>Đến</label> 
				</div>
				<div class="col-md-3 low-padding"> 
					<div class='input-group'>							
						<input class="form-control input-sm" id="txtDenNgay" valrule="Đến ngày,required|datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div> 
					 
				</div>
				<div class="col-md-1 low-padding"></div>
				<div class="col-md-2 low-padding">
					<button class="btn btn-sm btn-primary mgt-3" id="btnXem">
						<span class="glyphicon glyphicon-search"></span> Xem
					</button> 
				</div>
			</div>
		</div>	
	</div>
	
	<div id="divDanhSachCDHA">
		<div class="col-md-12 low-padding mgt-10 mgb-5">
			<table id="grdDanhSachCDHA"></table>
			<div id="pager_grdDanhSachCDHA"></div>
		</div>
		
		<div class="col-md-12 low-padding mgt-5 mgb10">
			<div class="col-md-6 low-padding">
				<div class="col-md-12 low-padding">
					<div class="col-md-3 low-padding">
						<button type="button" class="btn btn-primary" id="btnDicomViewer" style="display:none;width:136px">
							<span class="glyphicon glyphicon-picture" aria-hidden="true"></span> Xem ảnh DICOM
						</button>
					</div>
					<div class="col-md-4 low-padding">
<!-- 						<input class="form-control input-sm" style="width:95% !important;" id="txtTKPHUMO1" title="" attrIcd="0" modeDisXT="" placeholder="Gõ để tìm kiếm KTV"> -->
					</div>
					<div class="col-md-4 low-padding">
<!-- 						<select class="form-control input-sm" style="width:95% !important;" id="cboPHUMO1" modeDisXT="" attrCl="clear"> -->
<!-- 							<option value="-1"></option> -->
<!-- 						</select> -->
					</div>
					<div class="col-md-1 low-padding">
<!-- 						<button type="button" class="form-control input-sm glyphicon" id="btnCLEARPHUMO1" modeDisXT=""> -->
<!-- 							<span class="glyphicon glyphicon-remove"></span> -->
<!-- 						</button> -->
					</div>
				</div>
			</div>
			
			<div class="col-md-6 low-padding">
				<div class="col-md-12 low-padding" style="text-align: right;">
					<button class="btn btn-sm btn-primary" id="btnHenNgay">
						<span class="glyphicon glyphicon-calendar"></span> Hẹn ngày thực hiện
					</button>
					<button class="btn btn-sm btn-primary" id="btnTiepNhan">
						<span class="glyphicon glyphicon-log-in"></span> Bắt đầu thực hiện
					</button>
					<button class="btn btn-sm btn-primary" id="btnHuyTiepNhan" style="display: none;">
						<span class="glyphicon glyphicon-remove"></span> Hủy tiếp nhận
					</button>
<!-- 					<button class="btn btn-sm btn-primary" id="btnLuuKetQua" style="display: none;"> -->
<!-- 						<span class="glyphicon glyphicon-floppy-disk"></span> Lưu kết quả -->
<!-- 					</button> -->
					<button class="btn btn-sm btn-primary" id="btnHenTraKetQua" style="display: none;">
						<span class="glyphicon glyphicon-calendar"></span> Hẹn trả KQ
					</button>
					<button class="btn btn-sm btn-primary" id="btnTraKetQua" style="display: none;">
						<span class="glyphicon glyphicon-check"></span> Trả kết quả
					</button>
					<button class="btn btn-sm btn-primary" id="btnSuaNgayTN" style="display: none;">
						<span class="glyphicon glyphicon-edit"></span> Sửa TG tiếp nhận
					</button>
					<!-- begin nghiant 26062017 -->
					<button class="btn btn-sm btn-primary" id="btnEditTimeCDHA" style="display: none;">
						<span class="glyphicon glyphicon-edit"></span> Sửa TG trả KQ
					</button>
					<!-- end nghiant 26062017 -->
					<button class="btn btn-sm btn-primary" id="btnHuyKetQua" style="display: none;">
						<span class="glyphicon glyphicon-remove"></span> Hủy kết quả
					</button>
					<button class="btn btn-sm btn-primary" id="btnInPhieu" style="display: none;">
						<span class="glyphicon glyphicon-print"></span> In phiếu
					</button>
<!-- 					<button class="btn btn-sm btn-primary" id="btnKySo" style="display: none;"> -->
<!-- 						<span class="glyphicon glyphicon-check"></span> Ký số -->
<!-- 					</button> -->
<!-- 					<button class="btn btn-sm btn-primary" id="btnHuyKySo" style="display: none;"> -->
<!-- 						<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký -->
<!-- 					</button> -->
<!-- 					<button class="btn btn-sm btn-primary" id="btnInPhieuKy" style="display: none;"> -->
<!-- 						<span class="glyphicon glyphicon-print"></span> In ký -->
<!-- 					</button> -->
				</div>
			</div>
		</div>
	</div>
	
	<div class="contextMenu" id="contextMenuBP" style="display:none;">
        <ul style="width:235px !important; font-size: 65%;">
            <li id="CapSTT" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Cấp STT thực hiện</span>
            </li>
            <li id="ChuyenPhongTH" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Chuyển phòng thực hiện</span>
            </li><li id="GROUP2" class="menulevel1">
				<span class="ui-icon ui-icon-folder-open" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">THUỐC, VẬT TƯ ĐI KÈM</span>
			</li>
			<li id="TAOPHIEUTHUOCKEM_HAOPHI" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kevattutheogoi">Kê thuốc vật tư theo gói</span>
			</li>
			 <li id="TAOPHIEUTHUOCKEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kethuocdikem">Kê thuốc đi kèm</span>
			</li> 
			<li id="TAOPHIEUVATTUKEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kevattudikem">Kê vật tư đi kèm</span>
			</li>
			<li id="DSPTVT_KEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="dsthuocvttheogoi">Danh sách thuốc vật tư theo gói</span>
			</li> 
			<li id="DSPTVT" class="menulevel2"> 
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Danh sách phiếu thuốc, vật tư</span>
			</li>
			<!-- ChienDV START L2PT-82498 -->
			<li id="KTTSDU" class="menulevel1 hidden">
				<span class="ui-icon ui-icon-pencil" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">Tạo phiếu khai thác tiền sử dị ứng</span>
			</li>
			<li id="XNBETAHCG" class="menulevel1 hidden">
				<span class="ui-icon ui-icon-pencil" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">Phiếu cam kết XN beta HCG</span>
			</li>
			<!-- ChienDV END L2PT-82498 -->
			<li id="CDVTHCLS" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Phiếu chuyển thực hiện dịch vụ</span>
            </li>
			<!-- QuyetnpL2PT-82656 -->
			<li id="inTheBenhAn" class="menulevel1">
                <span class="ui-icon ui-icon-print" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">In mã bệnh án</span>
            </li>	
			<!-- Quyetnp -->
		</ul>
    </div>
	
	<div class="row"></div>
	
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDGhiChu" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDKetQuaCLS" value="">
<input type="hidden" id="hdfSoPhieu" value="">
<input type="hidden" id="hdfNgayDichVu" value="">
<input type="hidden" id="hdfMAHOSOBENHAN" value="">

<div id="divNhapKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmViewNhapKQ" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<div id="divSuaKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmViewSuaKQ" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script>
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang= "vn";
	console.log('user_id='+user_id+', schema='+schema+', province_id='+province_id+', hospital_id='+hospital_id);
	
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	var table_name='{table}';
	
	var _opts=new Object();	
	_opts._param=session_par;
	_opts._uuid=uuid;
	_opts.subdept_id = '{subdept_id}';
	_opts.dept_id = '{dept_id}';//nghiant 24102017 
	
	initRest(_opts._uuid);
	
	var ds = new DanhSachCDHA(_opts);
	ds.load();
	
	var GBN = new JSF_GoiBenhNhan();	
</script>