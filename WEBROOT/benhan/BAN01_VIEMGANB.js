
function BAN01_VIEMGANB(_opts) {
	this.load = doLoad;
	var benhnhanid = _opts.benhnhanid;
	var loaibenhanid = _opts.loaibenhanid;
	var khambenhid = _opts.khambenhid;
	var hosobenhanid = _opts.hosobenhanid;
	var loaihsba_code = _opts.maloaibenhan;
	var checkInsert = 1;
	var arrMaHoiKham = [];
	var arrMaHoiKhamTable = [];
	var i_hid = hospital_id;	
	var checkInput = $(':input');
	
	// mang duyet list cac loai ho so phim anh
	var arrSoPhimAnh = [ "txtSOXQUANG", "txtSOSCANNER", "txtSOSIEUAM",
			"txtSOXETNGHIEM", "txtSOKHAC", "txtSOTOANBOHOSO" ];

	//ham thuc hien load trang ungdung
	function doLoad() { 
		
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";		
		// khoi tao trang ho so benh an
		_initControl();
		// xu ly su kien click tren nut them moi
		_bindEvent();
		// export bieu mau
		if (hosobenhanid != null && hosobenhanid != '0') {
			exportBan(hosobenhanid, loaibenhanid, benhnhanid,_opts._param[4], 'BAN01_VIEMGANB');
		} 
		// close trang ung dung
		closePage();
	}
	
	function _initControl() {
		$('#txtNGAY_HBSAG').mask('00/00/0000');
		$('#txtNGAY_VGVRB').mask('00/00/0000');
		$('#txtNGAY_DTTU1').mask('00/00/0000');
		$('#txtNGAY_DTDEN1').mask('00/00/0000');
		$('#txtNGAY_DTTU2').mask('00/00/0000');
		$('#txtNGAY_DTDEN2').mask('00/00/0000');
		$('#cldDTNTTUNGAY').mask('00/00/0000');
		$('#cldDTNTDENNGAY').mask('00/00/0000');
		
		//pickerTime('txtNGAY_VGVRB', 'ddmmyyyy'); 
		//pickerTime('txtNGAY_HBSAG', 'ddmmyyyy hhmiss');
		
		// xu ly su kien load trang khi moi vao
		doLoadCombo("txtMABENHCHINH","txtBENHCHINH");
		doLoadComboKemTheo("txtMABENHKEMTHEO","txtBENHKEMTHEO"); 
		doLoadCombo("txtMACDBANDAU","txtCDBANDAU");
		doLoadCombo("txtMACHANDOANRAVIEN","txtCHANDOANRAVIEN");
 
		var _col_loaduser="Tài khoản,USERNAME,20,0,f,l;Họ và tên,FULLNAME,30,0,f,l";
		var sql_par=[];
		sql_par.push(
		{
			"name" : "[0]",
			"value" : i_hid
		}, {
			"name" : "[1]",
			"value" : _opts.khoaid
		});
		ComboUtil.initComboGrid("txtNGUOINHAN_TK",'GET_USER',sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtNGUOINHAN_TK").val(""); 
			$("#txtNGUOINHAN").val(ui.item.FULLNAME);  
			return false;
		});
		ComboUtil.initComboGrid("txtBSDIEUTRI_TK",'GET_USER',sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtBSDIEUTRI_TK").val(""); 
			$("#txtBSDIEUTRI").val(ui.item.FULLNAME);  
			return false;
		});
		ComboUtil.initComboGrid("txtNGUOIGIAO_TK",'GET_USER',sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtNGUOIGIAO_TK").val(""); 
			$("#txtNGUOIGIAO").val(ui.item.FULLNAME);  
			return false;
		});
		ComboUtil.initComboGrid("txtBSKHAM_TK",'GET_USER',sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtBSKHAM_TK").val(""); 
			$("#txtBSKHAM").val(ui.item.FULLNAME);  
			return false;
		}); 
		ComboUtil.initComboGrid("txtGIAMDOC_TK",'GET_USER',sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtGIAMDOC_TK").val(""); 
			$("#txtGIAMDOC").val(ui.item.FULLNAME);  
			return false;
		});  
		
		
		// Check xem da co du lieu ho so benh an khong
		loadData(benhnhanid, loaibenhanid, i_hid, loaihsba_code, hosobenhanid, khambenhid);

		if(hosobenhanid != undefined){
			checkInsert = 0;
		}
		// lấy danh sách mã hỏi khám bệnh của khoa
		arrMaHoiKham = getMaHoiKham(loaibenhanid);
		
		// Lấy danh sách mã hỏi khám bệnh dang table của khoa
		arrMaHoiKhamTable = getMaHoiKhamMaTran(loaibenhanid);
		
		// reset gia tri tong so ho so phim anh khi thay doi cac so
		// lieu(x-quang, scanner, xieu am,...)
		changePhimAnh(arrSoPhimAnh);
		//xu ly khi kich checkbox dac diem lien quan
		changeDataCheck();
		$("#txtLYDOVAOVIEN").focus();
//		$("#cldPHUONGPHAPDIEUTRI11").val(moment().format('DD/MM/YYYY HH:mm:ss'));
		
		var f2 = 113;
		$(document).unbind('keydown').keydown(function (e) {                             
		 if(e.keyCode == f2){
		  getIcd(e.target);
		 }
		});
		
		EventUtil.setEvent("assignSevice_resultTK", function(e) {
		   if(e.mode == '0'){
		    $('#' + e.ctrId).combogrid("setValue",e.text);
		   } else if(e.mode == '1'){
		    $('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
		   }
		   DlgUtil.close(e.popupId);
		  });
		
		if ($('#txtTOMTATKQCLS').val().trim() != "" ){
			$('#btnKQCLS').text("x");
		}
	}

	//ham xu ly luu va cap nhat thong tin
	function _bindEvent() { 
		$("#txtCANNANG").on("blur",function(e){
			_calBMI();
		});
		$("#txtCHIEUCAO").on("blur",function(e){
			_calBMI();
		});
		
		
		$("#btnThemMoi").on(
				"click",
				function(e) {
					_save(true);
				});
		$("#btnLuu").on(
				"click",
				function(e) {
					_save(false);
				});
				
		$("#btnKQCLS").on("click", function(e) {
			if ($('#txtTOMTATKQCLS').val().trim() != ""){
				$('#btnKQCLS').text("...");
				$('#txtTOMTATKQCLS').val("");
				return;
			}

			if(khambenhid == "-1" || khambenhid == ""){
				DlgUtil.showMsg("Chưa có thông tin khám bệnh. Xin kiểm tra lại.");
				return;
			}
			var myVar={
				khambenhid : khambenhid,
				benhnhanid : benhnhanid,
				hosobenhanid :hosobenhanid,
				mode : '1'//goi tu phieu dieu tri
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKQCLS","divDlg","manager.jsp?func=../ngoaitru/NGT02K031_ChonKQCLS",myVar,"CHỌN KẾT QUẢ CẬN LÂM SÀNG",900,350);
			DlgUtil.open("dlgKQCLS");
		});
		EventUtil.setEvent("assignSevice_luu",function(e){
			$('#txtTOMTATKQCLS').val(e.msg);
			DlgUtil.close("dlgKQCLS");
			$('#btnKQCLS').text("x");
		});
	}
	
	function _save(checkClose){
		// validate thong tin khi thuc hien cap nhat
		// them moi
		var validate = new DataValidator("divMain-BA");
		if(validate.validateForm() && _validateForm()){
			if (checkInsert == 1) {
				// buoc 1:insert thong tin ho so benh an
				hosobenhanid = insHSBenhAn(benhnhanid, loaibenhanid, khambenhid, i_hid, checkInsert);
			}
			
			// buoc 2: insert thong tin cho ho so phim anh
			var r1=insHSPhimAnh(arrSoPhimAnh, hosobenhanid, checkInsert);

			// buoc 3 insert thong tin hoi kham benh va tong ket benh an
			var r2=insHoiKham(checkInput, loaihsba_code, arrMaHoiKham, hosobenhanid, checkInsert);
			
			// insert thong tin hoi kham benh va tong ket benh an dang ma tran
			var r3=insHoiKhamMaTran(checkInput, loaihsba_code, arrMaHoiKhamTable, hosobenhanid, checkInsert);
			//dong bo du lieu
			var _par = [hosobenhanid, khambenhid];	
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.RIGHT",_par.join('$'));
			
			if($("#txtMABENHCHINH").val()!=''){
				var _par_td = [$("#txtMABENHCHINH").val(), _opts.khoaid];
				var _r_td=jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.ICD.TD",_par_td.join('$'));	
			}				
			
			if(_return==1 && r1==1 && r2==1 && r3==1){
				//xu ly su kien callback
				var evFunc=EventUtil.getEvent("assignSevice_saveHSBADetail");			
				if(typeof evFunc==='function') {	
					if(checkClose){
						evFunc({msg:"Cập nhật hồ sơ bệnh án thành công"});
					}
					else{
						DlgUtil.showMsg("Cập nhật hồ sơ bệnh án thành công");
					}
				}
				else {
					console.log('evFunc not a function');
				} 
			}else{
				callBack(_return, checkInsert);
			}
									
		}
	} 

	function _calBMI(){
		if($("#txtCHIEUCAO").val() != '' && $("#txtCANNANG").val() != ''){
			if(isNaN(parseFloat($("#txtCANNANG").val())) || isNaN(parseFloat($("#txtCHIEUCAO").val())))
				return;
			var _cannang = parseFloat($("#txtCANNANG").val());
			var _chieucao = parseFloat($("#txtCHIEUCAO").val());

			var bmi = chisobmi(_cannang, _chieucao);

			$("#txtBMI").text(bmi);
		}
	}
	
	function _validateForm() {
		var comp = $('#cldDTNTTUNGAY').val().split('/');
		var d = parseInt(comp[0], 10);
		var m = parseInt(comp[1], 10);
		var y = parseInt(comp[2], 10);
		var tuNgay = new Date(y, m - 1, d);
		var comp2 = $('#cldDTNTDENNGAY').val().split('/');
		var d2 = parseInt(comp2[0], 10);
		var m2 = parseInt(comp2[1], 10);
		var y2 = parseInt(comp2[2], 10);
		var denNgay = new Date(y2, m2 - 1, d2);
		var SELECTOR_ERRORS = '';
		var check = true;
		if (tuNgay.getTime() > denNgay.getTime()) {
			SELECTOR_ERRORS += 'Điều trị ngoại trú từ ngày phải nhỏ hơn đến ngày' + '<br>';
			onfocus('#cldDTNTTUNGAY');
			check = false;
		}
		if($('#txtCHIEUCAO').val()<0){
			SELECTOR_ERRORS += 'Chiều cao phải lớn hơn 0' + '<br>';
			onfocus('#txtCHIEUCAO');
			check =  false;
		}
		if($('#txtCANNANG').val()<0){
			SELECTOR_ERRORS += 'Cân nặng phải lớn hơn 0' + '<br>'; 
			onfocus('#txtCANNANG');
			check =  false;
		}
		if($('#txtCANNANG').val().split('.')[1] != null && $('#txtCANNANG').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Cân nặng chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtCANNANG');
			check =  false;
		}
		if($('#txtNHIETDO').val()<0){
			SELECTOR_ERRORS += 'Nhiệt độ phải lớn hơn 0' + '<br>';
			onfocus('#txtNHIETDO');
			check =  false;
		}
		if($('#txtNHIETDO').val().split('.')[1] != null && $('#txtNHIETDO').val().split('.')[1].length > 1){
			SELECTOR_ERRORS += 'Nhiệt độ chỉ nhập 1 số thập phân' + '<br>';
			onfocus('#txtNHIETDO');
			check =  false;
		}
		if($('#txtMACDBANDAU').val() != null && $('#txtMACDBANDAU').val().trim() != '' 
			&& !checkIcdCode($('#txtMACDBANDAU').val().trim())){
			SELECTOR_ERRORS += 'Mã chẩn đoán ban đầu không đúng' + '<br>';
			onfocus('#txtMACDBANDAU');
			check =  false;
		}
		if($('#txtCDBANDAU').val() != null && $('#txtCDBANDAU').val() != '' 
			&& !checkIcdName($('#txtCDBANDAU').val().trim())){
			SELECTOR_ERRORS += 'Tên chẩn đoán ban đầu không đúng' + '<br>';
			onfocus('#txtCDBANDAU');
			check =  false;
		}
		if($('#txtMACHANDOANRAVIEN').val() != null && $('#txtMACHANDOANRAVIEN').val().trim() != '' 
			&& !checkIcdCode($('#txtMACHANDOANRAVIEN').val().trim())){
			SELECTOR_ERRORS += 'Mã chẩn đoán ra viện không đúng' + '<br>';
			onfocus('#txtMACHANDOANRAVIEN');
			check =  false;
		}
		if($('#txtCHANDOANRAVIEN').val() != null && $('#txtCHANDOANRAVIEN').val() != '' 
			&& !checkIcdName($('#txtCHANDOANRAVIEN').val().trim())){
			SELECTOR_ERRORS += 'Tên chẩn đoán ra viện không đúng' + '<br>';
			onfocus('#txtCHANDOANRAVIEN');
			check =  false;
		}
		if($('#txtMABENHCHINH').val() != null && $('#txtMABENHCHINH').val().trim() != '' 
			&& !checkIcdCode($('#txtMABENHCHINH').val().trim())){
			SELECTOR_ERRORS += 'Mã bệnh chính không đúng' + '<br>';
			onfocus('#txtMABENHCHINH');
			check =  false;
		}
		if($('#txtBENHCHINH').val() != null && $('#txtBENHCHINH').val() != '' 
			&& !checkIcdName($('#txtBENHCHINH').val().trim())){
			SELECTOR_ERRORS += 'Tên bệnh chính không đúng' + '<br>';
			onfocus('#txtBENHCHINH');
			check =  false;
		}
//		if($('#txtMABENHKEMTHEO').val() != null && $('#txtMABENHKEMTHEO').val().trim() != '' 
//			&& !checkIcdCode($('#txtMABENHKEMTHEO').val().trim())){
//			SELECTOR_ERRORS += 'Mã bệnh kèm theo ko đúng' + '<br>';
//			onfocus('#txtMABENHKEMTHEO');
//			check =  false;
//		}
//		if($('#txtBENHKEMTHEO').val() != null && $('#txtBENHKEMTHEO').val() != '' 
//			&& !checkIcdName($('#txtBENHKEMTHEO').val().trim())){
//			SELECTOR_ERRORS += 'Tên bệnh kèm theo ko đúng' + '<br>';
//			onfocus('#txtBENHKEMTHEO');
//			check =  false;
//		}
		if(!check){
			DlgUtil.showMsg(SELECTOR_ERRORS);
		}
		return check;
	}

	function pickerTime(idInput, type){
	var timeFormat = "";
	var maskInput = "";
	var placeHolderInput = "";
	var maskInputPopup = "";
	var placeHolderInputPopup = "";
	var showTime = true;

	// ngay thang nam
	if (type == 'ddmmyyyy') {
		maskInput = "00/00/0000";
		placeHolderInput = "dd/MM/yyyy";
	}
	// ngay thang nam gio phut giay
	else if (type == 'ddmmyyyy hhmiss') {
		timeFormat = "HH:mm:ss";
		maskInput = "00/00/0000 00:00:00";
		placeHolderInput = "dd/MM/yyyy hh:mm:ss";
		maskInputPopup = "00:00:00";
		placeHolderInputPopup = "hh:mm:ss";
	}
	// ngay thang nam gio phut
	else if (type == 'ddmmyyyy hhmi') {
		timeFormat = "HH:mm";
		maskInput = "00/00/0000 00:00";
		placeHolderInput = "dd/MM/yyyy hh:mm";
		maskInputPopup = "00:00";
		placeHolderInputPopup = "hh:mm";
	}

	$('#' + idInput).mask(maskInput);
	$('#' + idInput).attr("placeholder", placeHolderInput);

	if (type == 'ddmmyyyy' ) {
		$('#' + idInput).datepicker({
			showButtonPanel: true
		});
		// mặc định nhấn nút Hiện thời không chọn ngày hiện thời mà chỉ về trang của ngày hiện thời
		$.datepicker._gotoToday = function(id) {
			//$(id).datepicker('setDate', new Date()).datepicker('hide').blur();
			var date,
				target = $( id ),
				inst = this._getInst( target[ 0 ] );

			if ( this._get( inst, "gotoCurrent" ) && inst.currentDay ) {
				inst.selectedDay = inst.currentDay;
				inst.drawMonth = inst.selectedMonth = inst.currentMonth;
				inst.drawYear = inst.selectedYear = inst.currentYear;
			} else {
				date = new Date();
				inst.selectedDay = date.getDate();
				inst.drawMonth = inst.selectedMonth = date.getMonth();
				inst.drawYear = inst.selectedYear = date.getFullYear();
			}
			this._notifyChange( inst );
			this._adjustDate( target );

			this._setDateDatepicker(target, date);
			this._selectDate(id, this._getDateDatepicker(target));
			$(id).blur();
		};
	} else {
		$('#' + idInput).datetimepicker({
			showTime: showTime
			,dateFormat: "dd/mm/yy"
			,timeFormat: timeFormat
			,timeInput: true
			,showHour: false
			,showMinute: false
			,showSecond: false
			,onSelect: function (selectedDateTime){
				setTimeout(function () {
					$('.ui_tpicker_time_input').mask(maskInputPopup);
					$('.ui_tpicker_time_input').attr( 'placeholder',placeHolderInputPopup);
				}, 1000);
			}
		}).focus(function() {
			$('.ui_tpicker_time_input').mask(maskInputPopup);
			$('.ui_tpicker_time_input').attr( 'placeholder',placeHolderInputPopup);
		});
	}


	// nut chon ben canh textbox
	var spanNext = $("#" + idInput).next();
	if ($(spanNext).hasClass("glyphicon-calendar")) {
		$(spanNext).on("click", function (e) {
			$('#'+idInput).focus();
		});
	}

	// fix lỗi vị trí
	$.extend($.datepicker,{_checkOffset:function(inst,offset,isFixed){return offset}});

}

}