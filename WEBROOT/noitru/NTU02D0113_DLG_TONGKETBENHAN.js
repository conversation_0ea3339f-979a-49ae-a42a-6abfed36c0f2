/*
Mã màn hình  : NTU02D055
File mã nguồn : NTU02D055_SoKetDieuTri.js
Mục đích  : <PERSON><PERSON> sach SO KET DIEU TRI
Tham số vào : 
  khambenhid: id của khám bệnh
  BENHNHANID: id cua BENH NHAN
  BACSYDIEUTRIID: ID CUA BAC SY DIEU TRI  
Người lập trình	<PERSON> cập nhật  <PERSON>hi chú
HUONGPV	- 13092016 - Comment
 */
function NTU02D0113_DLG_TONGKETBENHAN(opt) {
    var that = this;
    this.load = doLoad;
    var _hosobenhanid = opt.hosobenhanid;
    var _khambenhid = opt.khambenhid
    var type = 0;
    var TT32 = 0
    var loadFirst = true; 				// load du lieu lan dau tien;

    $("#hidHOSOBENHANID").val(_hosobenhanid);

    var _rpt_code_kyso = 'TOMTAT_BA_965';

    function doLoad() {
        this.validator = new DataValidator("divMain");
        $.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
        TT32 = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'HIS_TTBA_TT32');
        if (TT32 == 1) {
            $('#dvMACDINH').remove();
            $('#dvTT32').show();
            $('#tabNoiTru').hide();
            $('#tabNgoaiTru').hide();
            $('#tabBenhAnDieuTriNoiTru').hide();
            $('#tabBenhAnDieuTriNgoaiTru').hide();
            $('#dvSINHSAN').hide();
            $('#dvNOILAMVIEC').hide();
        }else{
            $('#dvTT32').remove();
        }
        var HIS_TTBA_QD4750 = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'HIS_TTBA_QD4750');
        if(HIS_TTBA_QD4750 == 1){
            $('#dvSINHSAN').show();
            $('#dv4750').show();
        }
        initControl();
        bindEvent();
        loadData();
        loadDsMau();
        if (opt.loaitiepnhanid == 0) {
            $('#tabNgoaiTru').hide();
        }else if (opt.loaitiepnhanid == 3){
            $('#tabNoiTru').hide();
        }
        var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
        if (kyso_kydientu != "1") {
            $('#' + 'btnKySo').remove();
            $('#' + 'btnHuyKy').remove();
            $('#' + 'btnInKySo').remove();
        }
    }

    function initControl() {
        // L2PT-76863 start
        if (opt.trangthaikhambenh == 9){
            $("#divMain").find("button").not("#btnPrint, #btnPrintDoc, #btnPrintNGOAITRU, #btnPrintDocNGOAITRU, #btnPrintNOITRU, #btnPrintDocNOITRU").remove();
        }
        // L2PT-76863 end
        $("#hidHOSOBENHANID").val(opt.hosobenhanid);
        ComboUtil.getComboTag("cboTT_RAVIEN_ID","NT.0010",[{"name":"[0]","value":"3"}],"", {value:'',text:'--Tất cả--'},"sql","","");
        ComboUtil.getComboTag("cboKETQUADIEUTRIID","NT.0010_1",[{"name":"[0]","value":"6"}],"", {value:'',text:'--Chọn--'},"sql","","");
        //L2PT-122003
        if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU02D0113_SHOW_PDT') == '1') {
			$('#divTAB_PHIEUDIEUTRI').show();
			sql_par = RSUtil.buildParam("", [ opt.khambenhid, "4" ]);
			ComboUtil.getComboTag("cboPHIEUDIEUTRI", "COM.PHIEUDIEUTRI", sql_par, "", {
				value : '',
				text : '-- Chọn --'
			}, "sql", '', '');
		}

        var sql_par = [];
        var _colDIAPHUONG = "Tên viết tắt,TENVIETTATDAYDU,30,0,f,l;Địa phương,NAME,70,0,f,l";
        ComboUtil.initComboGrid("txtTKDIAPHUONGID_CT", "DMDP.002", [], "600px", _colDIAPHUONG, function (event, ui) {
            var option = $('<option value="' + ui.item.VALUE + '">' + ui.item.NAME + '</option>');
            $("#cboDIAPHUONGID_CT").empty();
            $("#cboDIAPHUONGID_CT").append(option);
            getDiaChi(ui.item.VALUE, 'cboTT_TINHID', 'cboTT_HUYENID', 'cboTT_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', false);
            $('#txtDIACHITT').val(ui.item.NAME);
            return false;
        });
        ComboUtil.getComboTag("cboTT_TINHID", "NGTTI.002", [], '', {
            extval: true,
            value: '',
            text: 'Chọn'
        }, 'sql', '', '');

        $('#cboDIAPHUONGID_CT').on('change', function (e) {
            getDiaChi($('#cboDIAPHUONGID_CT').val(), 'cboTT_TINHID', 'cboTT_HUYENID', 'cboTT_XAID', 'cboDIABANID', '', 'txtTKHC_TINHID', 'txtTKHC_HUYENID', 'txtTKHC_XAID', 'txtTKDIABANID', 'cboDTBNID', false);
        });
        $('#radPPDT_PT_CO').change(function () {
            var check = $("input[name='radPPDT_PT']:checked").val();
            if(check == 2){
                $("#txtPPDT_DT_VAL").prop("disabled", false);
            }else{
                $("#txtPPDT_DT_VAL").prop("disabled", true);
                $('#txtPPDT_DT_VAL').val("");
            }
        });
        $('#radPPDT_PT_KHONG').change(function () {
            var check = $("input[name='radPPDT_PT']:checked").val();
            if(check == 2){
                $("#txtPPDT_DT_VAL").prop("disabled", false);
            }else{
                $("#txtPPDT_DT_VAL").prop("disabled", true);
                $('#txtPPDT_DT_VAL').val("");
            }
        });
        doLoadCombovaovien("txtMACHANDOANVAOVIEN","txtCHANDOANVAOVIENNEW");
        doLoadCombo("txtMACHANDOANRAVIEN","txtCHANDOANRAVIEN");
        var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
        ComboUtil.initComboGrid("txtMA_CHANDOAN_RA_KEMTHEO","NT.SEARCH.ICD10",[], "600px", _col, function(event, ui) {
            var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
            if(str.indexOf(ui.item.ICD10CODE+'-') > -1){
                DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
                return false;
            }

            if((ui.item.ICD10CODE.indexOf($('#txtMACHANDOANRAVIEN').val()) > -1) && $('#txtMACHANDOANRAVIEN').val() != ''){
                DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
                return false;
            }
            if(str != '')
                str += ";";
            $("#txtTEN_CHANDOAN_RA_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
            $("#txtMA_CHANDOAN_RA_KEMTHEO").val('');
            $("#txtMA_CHANDOAN_RA_KEMTHEO").trigger("focus");
            return false;
        });

        $('#divMain input[name=radPPDT_PT]').prop('checked', false);
        $('#divMain input[name=radPPDT_PT][value=1]').prop('checked', true);
        // $('#divMain input[name=radPPDT_NOIKHOA]').prop('checked', false);
        // $('#divMain input[name=radPPDT_NOIKHOA][value=2]').prop('checked', true);

        var CDVV = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'DLG_TKBA_CDVV');
        if (CDVV == 1) {
            $('#divCDVT').css('display', '');
            var element = document.getElementById("lbCDVV");
            element.classList.add("required");
            element = document.getElementById("txtCHANDOANVAOVIEN");
            element.classList.add("required");
            $('#txtCHANDOANVAOVIEN').attr('valrule', 'Chẩn đoán vào viện,required');
        }
        validator = new DataValidator("divMain");
        var _sql_par = RSUtil.buildParam("", [opt.khambenhid]);
        var _check = jsonrpc.AjaxJson.getOneValue('GET.TTKB', _sql_par);
        if (_check == '1' && jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'HIS_SHOW_BENHAN_ALL') != '0') { //L2PT-62181
            $("#btnLuu").hide();
            $("#btnXoa").hide();
        }

        loadDsMau();

    }
    //L2PT-122003
    $("#cboPHIEUDIEUTRI").on("change", function(e) {
		var tmp_id = $("#cboPHIEUDIEUTRI").val();
		if (tmp_id != '') {
    		var obj = {
    	            maubenhphamid: tmp_id + "",
    	        }
    	    var param = JSON.stringify(obj);
			data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D170.GETPDT", param);
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				$("#txtQUATRINHBENHLY").val(row.QT_BENHLY);
				$("#txtNTU_QUATRINHBENHLY").val(row.QT_BENHLY);
			}
		}
	});
    $("#btnEDITCHANDOANRAVIENKT").on("click", function(e) {
        var myVar = {
            benhphu : $('#txtTEN_CHANDOAN_RA_KEMTHEO').val(),
            benhphu1 : $('#txtTEN_CHANDOAN_RA_KEMTHEO').val(),
            chandoan_kt_bd : ""
        };
        dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
        DlgUtil.open("dlgBPKT");
    });
    EventUtil.setEvent("chinhsua_benhphu", function(e) {
        $('#txtTEN_CHANDOAN_RA_KEMTHEO').val(e.benhphu);
        DlgUtil.close("dlgBPKT");
    });
    $("#btnCLEARCHANDOANRAVIEN").on("click", function(e) {
        $('#txtTEN_CHANDOAN_RA_KEMTHEO').val('');
    });
    function doLoadCombo(_txt,_txtDst){
        var _selfnc=function(event, ui) {
            var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
            if(str.indexOf(ui.item.ICD10CODE) > -1){
                DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
                return false;
            }
            $("#"+_txt).val(ui.item.ICD10CODE);
            _MACHANDOAN = ui.item.ICD10CODE; //L2PT-23947
            $("#"+_txtDst).val(ui.item.ICD10NAME);
            $("#txtMA_CHANDOAN_RA_KEMTHEO").trigger("focus");
            return false;
        };
        var _sql_par = [];
        var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
        ComboUtil.initComboGrid(_txt,"CG.ICD10",_sql_par,"600px",_col,_selfnc);
    };
    function doLoadCombovaovien(_txt,_txtDst){
        var _selfnc=function(event, ui) {
            $("#"+_txt).val(ui.item.ICD10CODE);
            _MACHANDOAN = ui.item.ICD10CODE; //L2PT-23947
            $("#"+_txtDst).val(ui.item.ICD10NAME);
            return false;
        };
        var _sql_par = [];
        var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
        ComboUtil.initComboGrid(_txt,"CG.ICD10",_sql_par,"600px",_col,_selfnc);
    };

    function bindEvent() {
        $("#tabNgoaiTru").on("click", function () {
            $("#dvCHUNG").css('display', 'none');
            $("#btnPrintNOITRU").css('display', 'none');
            $("#btnPrintDocNOITRU").css('display', 'none');
            $("#btnPrint").css('display', 'none');
            $("#btnPrintDoc").css('display', 'none');
            $("#btnPrintDocNGOAITRU").show();
            $("#btnPrintNGOAITRU").show();

        });
        $("#tabChung").on("click", function () {
            $("#dvCHUNG").show();
            $("#btnPrintDocNOITRU").css('display', 'none');
            $("#btnPrintNOITRU").css('display', 'none');
            $("#btnPrintDocNGOAITRU").css('display', 'none');
            $("#btnPrintNGOAITRU").css('display', 'none');
            $("#btnPrint").show();
            $("#btnPrintDoc").show();
        });
        $("#tabNoiTru").on("click", function () {
            $("#dvCHUNG").show();
            $("#btnPrintDoc").css('display', 'none');
            $("#btnPrint").css('display', 'none');
            $("#btnPrintNGOAITRU").css('display', 'none');
            $("#btnPrintDocNGOAITRU").css('display', 'none');
            $("#btnPrintNOITRU").show();
            $("#btnPrintDocNOITRU").show();
        });
        $("#btnLuu").on("click", function () {
            save();
        });

        $("#btnXoa").click(function () {
            xoa();
        });

        $("#btn_Close").on("click", function (e) {
            parent.DlgUtil.close("divDlgTomTatBA");
        });
        $("#btnPrint").on("click", function (e) {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: opt.hosobenhanid
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: ''
            }, {
                name: 'khambenhid',
                type: 'String',
                value: opt.khambenhid
            }];
            openReport('window', "TOMTAT_BA_965", "pdf", par);
        });

        $("#btnPrintDoc").on("click", function (e) {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: opt.hosobenhanid
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: ''
            }, {
                name: 'khambenhid',
                type: 'String',
                value: opt.khambenhid
            }];
            var _type = "rtf"; //L2PT-116060
            var rpName = "TOMTAT_BA_965" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _type;
            CommonUtil.inPhieu('window', 'TOMTAT_BA_965', _type, par, rpName);
        });
        $("#btnPrintNOITRU").on("click", function (e) {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: opt.hosobenhanid
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: ''
            }, {
                name: 'khambenhid',
                type: 'String',
                value: opt.khambenhid
            }];
            openReport('window', "RPT_TOMTAT_BANT_26060", "pdf", par);
        });

        $("#btnPrintDocNOITRU").on("click", function (e) {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: opt.hosobenhanid
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: ''
            }, {
                name: 'khambenhid',
                type: 'String',
                value: opt.khambenhid
            }];
            var _type = "docx";
            var rpName = "RPT_TOMTAT_BANT_26060" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _type;
            CommonUtil.inPhieu('window', 'RPT_TOMTAT_BANT_26060', _type, par, rpName);
        });
        $("#btnPrintNOITRU").on("click", function (e) {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: opt.hosobenhanid
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: ''
            }, {
                name: 'khambenhid',
                type: 'String',
                value: opt.khambenhid
            }];
            openReport('window', "RPT_TOMTAT_BANGT_26060", "pdf", par);
        });

        $("#btnPrintDocNOITRU").on("click", function (e) {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: opt.hosobenhanid
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: ''
            }, {
                name: 'khambenhid',
                type: 'String',
                value: opt.khambenhid
            }];
            var _type = "docx";
            var rpName = "RPT_TOMTAT_BANGT_26060" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _type;
            CommonUtil.inPhieu('window', 'RPT_TOMTAT_BANGT_26060', _type, par, rpName);
        });

        $("#btnLuuThanhMau").on("click", function (e) {
            if ($('#txtTENMAU').val().trim() == '') {
                DlgUtil.showMsg('Tên mẫu không được để trống', function () {
                });
                return;
            }
            saveMau();
        });

        $("#btnXoaMau").on("click", function (e) {
            if ($('#cboMAU').val() == '') {
                DlgUtil.showMsg('Vui lòng chọn mẫu', function () {
                });
                return;
            }
            DlgUtil.showConfirm("Bạn có muốn xóa mẫu này không?", function (flag) {
                if (flag) {
                    var par = [
                        $('#cboMAU').val()
                    ];
                    var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D0113.L02", par.join('$'));
                    var data = $.parseJSON(result);
                    if (data == '-1') {
                        DlgUtil.showMsg('Đã có lỗi xảy ra', function () {
                        });
                    } else {
                        DlgUtil.showMsg('Đã xóa mẫu', function () {
                            loadDsMau();
                        });
                    }
                }
            });
        });
        //L2PT-74420
        $("#btnPTTT").on("click", function(e) {
            var myVar = {
                khambenhid: _khambenhid,
                hosobenhanid: _hosobenhanid
                //loadlaidulieu : loadlaidulieu_cls,
                //ids_cls : ids_cls
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgPTTT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K031_ChonPTTT", myVar, "CHỌN PTTT", 1100, 512); // L2PT-43257 duonghn
            DlgUtil.open("dlgPTTT");

        });
        EventUtil.setEvent("assignSevice_pttt", function(e) {
            DlgUtil.close("dlgPTTT");
            if(e.msg)
                $('#txtPPDT_DT_VAL').val(e.msg);
        });

        $('#cboMAU').change(function () {
            $('#txtTENMAU').val('');
            clearTTDeLuuMau();
            if ($('#cboMAU').val() != '') {
                fillMauToView($('#cboMAU').val());
            }
        });

        $("#btnKySo").on("click", function (e) {
            _kySo();
        });
        $("#btnHuyKy").on("click", function (e) {
            _huyKySo();
        });
        $("#btnInKySo").on("click", function (e) {
            _inPhieuKySo();
        });

        $("#btnKETQUA_CLS").on('click', function () {
            if (_khambenhid == "-1") {
                DlgUtil.showMsg("Bệnh nhân chưa được tiếp nhận khám. Xin kiểm tra lại.");
                return;
            }
            EventUtil.setEvent("assignSevice_luu", function (e) {
                var strArr = e.msg.split(';');
                var str = "";
                for (i = 0; i < strArr.length; i++) {
                    str += strArr[i] + "\n";
                }

                $('#txtKQ_CLS_XN').val(str);
                DlgUtil.close("dlgKQCLS");
            });

            var myVar = {
                khambenhid: _khambenhid,
                hosobenhanid: _hosobenhanid,
                mode: '1',
                formCall: 'TOMTATBA'
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKQCLS", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K031_ChonKQCLS", myVar, "CHỌN KẾT QUẢ CẬN LÂM SÀNG", 1200, 600);
            DlgUtil.open("dlgKQCLS");
        });
        $("#btnNGT_KETQUA_CLS").on('click', function () {
            if (_khambenhid == "-1") {
                DlgUtil.showMsg("Bệnh nhân chưa được tiếp nhận khám. Xin kiểm tra lại.");
                return;
            }
            EventUtil.setEvent("assignSevice_luu", function (e) {
                var strArr = e.msg.split(';');
                var str = "";
                for (i = 0; i < strArr.length; i++) {
                    str += strArr[i] + "\n";
                }

                $('#txtNTU_KQ_CLS_XN').val(str);
                DlgUtil.close("dlgKQCLS");
            });

            var myVar = {
                khambenhid: _khambenhid,
                hosobenhanid: _hosobenhanid,
                mode: '1'
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKQCLS", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K031_ChonKQCLS", myVar, "CHỌN KẾT QUẢ CẬN LÂM SÀNG", 1200, 600);
            DlgUtil.open("dlgKQCLS");
        });
        $("#btnKETQUA_CLSNGT").on('click', function () {
            if (_khambenhid == "-1") {
                DlgUtil.showMsg("Bệnh nhân chưa được tiếp nhận khám. Xin kiểm tra lại.");
                return;
            }
            EventUtil.setEvent("assignSevice_luu", function (e) {
                var strArr = e.msg.split(';');
                var str = "";
                for (i = 0; i < strArr.length; i++) {
                    str += strArr[i] + "\n";
                }

                $('#txtNGT_KQ_CLS_XN').val(str);
                DlgUtil.close("dlgKQCLS");
            });

            var myVar = {
                khambenhid: _khambenhid,
                hosobenhanid: _hosobenhanid,
                mode: '1'
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKQCLS", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K031_ChonKQCLS", myVar, "CHỌN KẾT QUẢ CẬN LÂM SÀNG", 1200, 600);
            DlgUtil.open("dlgKQCLS");
        });
    }

    function _kySo() {
        _caRpt('1');
    }

    function _huyKySo() {
        _caRpt('2');
    }

    function _inPhieuKySo() {
        _caRpt('0');
    }

    function _caRpt(signType) {
        var par_rpt_KySo = [];
        par_rpt_KySo = [{
            name: 'HOSOBENHANID',
            type: 'String',
            value: _hosobenhanid
        }, {
            name: 'loaibenhanid',
            type: 'String',
            value: ''
        }, {
            name: 'KHAMBENHID',
            type: 'String',
            value: _khambenhid
        }, {
            name: 'RPT_CODE',
            type: 'String',
            value: _rpt_code_kyso
        }];

        if (signType == '0') {
            CommonUtil.openReportGetCA2(par_rpt_KySo, false);
        } else {
            var msg = CommonUtil.kyCA(par_rpt_KySo, signType, true, true);
            EventUtil.setEvent("eventKyCA", function (e) {
                DlgUtil.showMsg(e.res);
                initControl();
            });
        }
    }

    function loadData() {
        FormUtil.clearForm("divMain", "");
        $("#hidHOSOBENHANID").val(_hosobenhanid);
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.TOMTAT.LOAD1",
            _hosobenhanid + '$' + opt.khoaid + '$' + opt.khambenhid);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            FormUtil.clearForm("divMain", "");
            $("#txtMACHANDOANRAVIEN").val(row.MACHANDOANRAVIEN);
            $("#txtMACHANDOANVAOVIEN").val(row.MACHANDOANVAOVIEN);

            delete row['MACHANDOANRAVIEN'];
            delete row['MACHANDOANVAOVIEN'];
            FormUtil.setObjectToForm("divMain", "", row);
            // $('#divMain input[name=radPPDT_NOIKHOA][value=' + (row.PPDT_NOIKHOA ? row.PPDT_NOIKHOA : 0) + ']').prop('checked', true);
            $('#divMain input[name=radPPDT_PT][value=' + (row.PPDT_PT ? row.PPDT_PT : 0) + ']').prop('checked', true);
            // if(row.PPDT_NOIKHOA==2)
            // 	$("#txtPPDT_NOIKHOA_VAL").prop("disabled", false);
            // if(row.PPDT_PT==2)
            // 	$("#txtPPDT_DT_VAL").prop("disabled", false);
            //L2PT-38766
            $('#cboDIAPHUONGID_CT').find('option').remove();
            var newOption = $('<option value="' + data_ar[0].DIAPHUONGID + '" selected>' + data_ar[0].TENDIAPHUONG + '</option>');
            $("#cboDIAPHUONGID_CT").append(newOption);
            $('#cboDIAPHUONGID_CT').change();
        }
        var data_ar2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.TOMTAT.LOAD_CT",
            _hosobenhanid + '$' + opt.khoaid + '$' + opt.khambenhid);
        if (data_ar2 != null && data_ar2.length > 0) {
            var row = data_ar2[0];
            FormUtil.setObjectToForm("divMain", "", row);
        }
        // $('#txtKQ_CLS_XN').val('');
        if ($('#txtPHUONGPHAPDT').val()== null || $('#txtPHUONGPHAPDT').val() == '' ||$('#txtPHUONGPHAPDT1').val()== null || $('#txtPHUONGPHAPDT1').val() == '' ){
            var sql_par = [];
//            sql_par.push({"name": "[0]", "value": opt.khambenhid});
//            var PPDTS = jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.GETPPDT", sql_par);
//            var PPDT = $.parseJSON(PPDTS);
//			if (PPDT.length != 0){
//			$('#txtPHUONGPHAPDT').val(PPDT[0].PHUONGPHAPDIEUTRI);
//            $('#txtPHUONGPHAPDT1').val(PPDT[0].PHUONGPHAPDIEUTRI);
//			}
        }
        // if ($('#txtPPDT_NOIKHOA_VAL').val() != "") {
        //     $('#divMain input[name=radPPDT_NOIKHOA][value=2]').prop('checked', true);
        // }
        if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'BAN_TTBA_DONGBODBB') == 1 ) {
            var sql_par = [];
            sql_par.push({"name": "[0]", "value": opt.hosobenhanid});
            if ($('#txtQUATRINHBENHLY').val() == null || $('#txtQUATRINHBENHLY').val() == '' ){
                var PPDTS = jsonrpc.AjaxJson.getOneValue("BAN_TTBA_GETDBDT", sql_par);
                $('#txtQUATRINHBENHLY').val(PPDTS);
            }
            if ($('#txtTINHTRANGRAVIEN').val() == null || $('#txtTINHTRANGRAVIEN').val() == '' ){
                var PPDTS = jsonrpc.AjaxJson.getOneValue("BAN_TTBA_GETDBDT1", sql_par);
                $('#txtTINHTRANGRAVIEN').val(PPDTS);
            }
        }
        if ($("#hidSERI").val() == '') {
            $("#btnXoa").attr('disabled', true);
        } else {
            $("#btnXoa").attr('disabled', false);
        }
    }

    function xoa() {
        DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko ?", function (flag) {
            if (flag) {
                objData = new Object();
                objData["TYPE"] = "DEL";
                objData["LOAIBENHANID"] = "";
                objData["KHAMBENHID"] = _khambenhid;
                FormUtil.setFormToObject("", "", objData);
                var par = [JSON.stringify(objData)];
                var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN_TOMTAT_SAVE", par.join('$'));
                if (_result == 2) {
                    DlgUtil.showMsg("Xóa thông tin thành công!");
                    loadData();
                } else {
                    DlgUtil.showMsg("Lưu thông tin không thành công! ");
                }
            }
        });
    }

    function save() {
        if ($("#tabBenhAnDieuTriNgoaiTru").is(":visible")) {
            var validate = new DataValidator("tabBenhAnDieuTriNgoaiTru");
        }
        if ($("#tabBenhAnDieuTriNoiTru").is(":visible")) {
            var validate = new DataValidator("tabBenhAnDieuTriNoiTru");
            if ($("#txtHUONGDIEUTRI").val().trim() == '' || $("#txtHUONGDIEUTRI").val().trim() == null) {
                DlgUtil.showMsg("Hướng điều trị không được để trống ! ");
                return;
            }
        }
        if ($("#tabBenhAnChung").is(":visible")) {
            var validate = new DataValidator("tabBenhAnChung");
            if ($("#cboTT_RAVIEN_ID").val().trim() == '' || $("#cboTT_RAVIEN_ID").val().trim() == null) {
                DlgUtil.showMsg("Chọn tình trạng ra viện ! ");
                return;
            }
        }
        if ($("#txtPHUONGPHAPDT1").val() != '' && $("#tabBenhAnDieuTriNgoaiTru").is(":visible")) {
            $("#txtPHUONGPHAPDT").val($("#txtPHUONGPHAPDT1").val());
        }
        if ($("#txtNGAYCONCHET").val() != '' ||  $("#txtSOCONCHET").val() != '') {
            if ( $("#txtNGAYSINHCON").val() == '') {
                DlgUtil.showMsg("Vui lòng nhập ngày sinh con ! ");
                return;
            }else if ($("#txtNGAYCONCHET").val() == '' ) {
                DlgUtil.showMsg("Vui lòng nhập ngày con chết ! ");
                return;
            }else if ( $("#txtSOCONCHET").val() == '') {
                DlgUtil.showMsg("Vui lòng nhập số con chết ! ");
                return;
            }
        }
        if($('input[name="radPPDT_PT"]:checked').val() == 2 && $('#txtPPDT_DT_VAL').val() == '') {
            DlgUtil.showMsg("Chưa nhập thông tin phương pháp PTTT!");
            return;
        }
        if (validate.validateForm()) {
            var objData = new Object();
            objData["TYPE"] = "INS";
            FormUtil.setFormToObject("divMain", "", objData);
            objData["LOAIBENHANID"] = "";
            objData["KHAMBENHID"] = _khambenhid;
            var par = [JSON.stringify(objData)];
            var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN_TOMTAT_SAVE", par
                .join('$'));
            if (_result == 1) {
                //HaNv_240723: L2PT-47559
                DlgUtil.showMsg("Lưu thông tin thành công!",undefined,jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_TIMEOUT_THONGBAO'));
                loadData();
            }else if (_result == 2){
                DlgUtil.showMsg("Chưa nhập quá trình bệnh lý!");
                return;
            }else if (_result == 3){
                DlgUtil.showMsg("Chưa nhập phương pháp điều trị!");
                return;
            }else if (_result == 4){
                DlgUtil.showMsg("Chưa nhập tóm tắt kết quả xét nghiệm cận lâm sàng!");
                return;
            }else if (_result == 5){
                DlgUtil.showMsg("Tóm tắt quả xét nghiệm cận lâm sàng không được quá 4000 ký tự!");
                return;
            }else {
                DlgUtil.showMsg("Lưu thông tin không thành công!");
                return;
            }
        }
    }

    function loadDsMau() {
        var cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TTBA_GETMAU');
        if (cauhinh == '1') {
            ComboUtil.getComboTag("cboMAU", "NTU02D0113.L11", [{
                "name": "[0]",
                "value": ""
            }], "", {
                value: '',
                text: '-- Chọn mẫu --'
            }, "");
        } else {
            ComboUtil.getComboTag("cboMAU", "NTU02D0113.L01", [{
                "name": "[0]",
                "value": ""
            }], "", {
                value: '',
                text: '-- Chọn mẫu --'
            }, "");
        }
    }

    function saveMau() {
        var obj = {
            TENMAU: $('#txtTENMAU').val() + ""
        };
        FormUtil.setFormToObject("divMain", "", obj);
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D0113.L03", param); // L7T.G01
        if (result == '0') {
            DlgUtil.showMsg("Đã xảy ra lỗi");
            return;
        } else {
            DlgUtil.showMsg('Lưu mẫu thành công!', function () {
                loadDsMau();
                $('#txtTENMAU').val('');
            });
            return;
        }
    }

    function clearTTDeLuuMau() {
        $('#txtQUATRINHBENHLY').val('');
        $('#txtPHUONGPHAPDT').val('');
        $('#txtPHUONGPHAPDT1').val('');
        $('#txtKQ_CLS_XN').val('');
        $('#txtTINHTRANGRAVIEN').val('');
        $('#txtTT_RAVIEN_ID').val('');
        $('#txtGHICHU').val('');
        $('#txtNGAYSINHCON').val('');
        $('#txtNGAYCONCHET').val('');
        $('#txtSOCONCHET').val('');
        $('#txtSERI').val('');
        $('#txtCHANDOANVAOVIEN').val('');
        $('#txtLOAIBENHAN').val('');
        $('#txtHUONGDIEUTRI').val('');
        $('#txtQUATRINHBENHTAT').val('');
        $('#txtKQ_CLS_KB').val('');
    }

    function fillMauToView(idMau) {
        var obj = {
            idMau: idMau + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D0113.L04", param);
        if (result && result.length > 0) {
            var data = result[0];
            FormUtil.setObjectToForm("divMain", "", data);
        }

    }
}