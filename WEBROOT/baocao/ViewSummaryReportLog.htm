<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/RestService.js?v=181218"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js?v=181218"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.core.css"/>
<link rel="stylesheet" href="../common/script/alertify/themes/alertify.default.css" id="toggleCSS"/>
<script type="text/javascript" src="../common/script/alertify/alertify.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js?v=1"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script type="text/javascript" src="../common/script/jszip/jszip.js"></script>
<script type="text/javascript" src="../common/script/jszip/FileSaver.js"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<link href="../common/script/select2/dist/css/select2.min.css" rel="stylesheet"/>
<script src="../common/script/select2/dist/js/select2.min.js"></script>
<script type="text/javascript" src="../baocao/ViewHashMap.js"></script>
<script type="text/javascript" src="../baocao/ViewReportUtil.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.sumoselect.js"></script>
<link rel="stylesheet" href="../common/script/jquery/sumoselect.css" type="text/css"/>
<link rel="stylesheet" type="text/css" href="../common/script/datetimepicker/MonthPicker.css" media="all"/>
<script type="text/javascript" src="../common/script/datetimepicker/MonthPicker.js"></script>
<script type="text/javascript" src="../baocao/ViewSummaryReportLog.js?v=20250311_01"></script>

<style>
    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
        background-color: #b0b0b0 !important;
    }

    .select2-container--default .select2-selection--single {
        border-radius: 2px;
    }

    .table tr th, table tr td {
        background: white;
        padding: 2px;
    }

    .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
        background: white;
    }

    .ui-search-clear {
        background: white;
    }

    .tab-pane label {
        margin-top: 5px;
        min-width: 1px;
    }

    .dropdown-menu > li > a {
        text-align: left;
    }

    #divParam select, #divParam input {
        padding: 3px 5px;
        height: 27px !important;
        font-size: .9em;
        border-radius: 0;
    }

    div.ui-jqgrid {
        margin-top: 2px !important;
        border: 0px solid #a6c9e2;
        box-sizing: initial;
    }

    td {
        word-break: break-word;
    }

</style>
<div width="100%" id="divMain" class="container">
    <div class="col-md-12 low-padding">
        <div class="col-md-12 low-padding mgt5 mgb5">
            <div class="col-xs-3">
                <div class="col-xs-12 low-padding mgt2">
                    <div class="col-xs-3 low-padding">
                        <label class="control-label mgt3 mgl3">Từ ngày</label>
                    </div>
                    <div class="col-xs-9">
                        <div class="input-group mgt3">
                            <input class="form-control input-sm" id="txtTuNgay" name="txtTuNgay" title="" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss">
                            <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"
                                  onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',true,'24',true)"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-3">
                <div class="row">
                    <div class="col-xs-12 low-padding mgt2">
                        <div class="col-xs-3 low-padding">
                            <label class="control-label mgt3 mgl3">Đến ngày</label>
                        </div>
                        <div class="col-xs-9">
                            <div class="input-group mgt3">
                                <input class="form-control input-sm" id="txtDenNgay" name="txtDenNgay" title="" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss">
                                <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"
                                      onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',true,'24',true)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 low-padding mgb5" style="text-align: center;">
            <button type="button" class="btn btn-sm" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnTimKiem">
                <span class="glyphicon glyphicon-search"></span> Tìm kiếm
            </button>
            <button type="button" class="btn btn-sm" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnXoaTimKiem">
                <span class="glyphicon glyphicon-trash"></span> Xóa tìm kiếm
            </button>
        </div>
        <div class="col-md-12 low-padding">
            <table id="grdDSLogReport"></table>
            <div id="pager_grdDSLogReport"></div>
        </div>
    </div>
</div>
<script>
    var opt = [];
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var dept_id = '{dept_id}';
    var subdept_id = '{subdept_id}'
    var uuid = '{uuid}';
    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    var table_name = '{table}';
    initRest(uuid, "/vnpthis");
    parent.DlgUtil.tunnel(DlgUtil.moveEvent);
    objVar = EventUtil.getVar("dlgVar");
    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid
    _opts._dept_id = dept_id;
    _opts._subdept_id = subdept_id
    _opts._user_id = user_id;
    _opts._hospital_id = hospital_id;
    var DS = new ViewSummaryReportLog(_opts);
    DS.load(hospital_id);
</script>