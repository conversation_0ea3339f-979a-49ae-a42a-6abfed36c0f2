<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC_BC_TONGHOP_XUATTHUOC" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" uuid="6b7073b1-415c-4ada-9b18-5d26670f90f0">
	<property name="ireport.zoom" value="2.8531167061100042"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false"/>
	<parameter name="vloaixuatbnid" class="java.lang.String"/>
	<parameter name="VPARS_loaitvt_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_khoid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_coso_VALUE" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_BC_TONGHOP_XUATTHUOC($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{tungay},
$P{denngay},
$P{VPARS_loaitvt_VALUE},
$P{VPARS_khoid_VALUE},
$P{vloaixuatbnid},
$P{VPARS_coso_VALUE},
$P{ora_cursor})}]]>
	</queryString>
	<field name="VLOAI" class="java.lang.String"/>
	<field name="VLOAIXUATBN" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="DONVI" class="java.lang.String"/>
	<field name="NUOCSX" class="java.lang.String"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="THANHTIEN" class="java.math.BigDecimal"/>
	<field name="KHOXUAT" class="java.lang.String"/>
	<field name="SOPHIEUCAP" class="java.lang.String"/>
	<field name="TEN_COSO" class="java.lang.String"/>
	<field name="MATHUOC" class="java.lang.String"/>
	<variable name="TONG_TIEN" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<variable name="TONG_SOLUONG" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="TONG_DON" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$V{REPORT_COUNT}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="137" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="239" height="17" uuid="039532cf-e8f0-4a76-90b4-67fb39181a1b"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="17" width="239" height="16" uuid="bb91f640-ea42-4af8-8b0c-7130d820a268"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="44" width="555" height="20" uuid="67b3b607-3746-474b-b1c5-48ea888b26db"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BÁO CÁO TỔNG HỢP XUẤT " +($F{VLOAI} == null ? "":$F{VLOAI})+" "+($F{VLOAIXUATBN} == null ? "":$F{VLOAIXUATBN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="64" width="555" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="af0bf395-4249-4ed4-b417-9ea59a266de3"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + " đến ngày "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="84" width="555" height="20" uuid="8fb15d88-9b48-4987-948c-f44ff3e247e9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Cơ sở: " + ($F{TEN_COSO} == null ? "" : "-1".equals($F{TEN_COSO}) ? "Tất cả các cơ sở" : $F{TEN_COSO})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="104" width="555" height="20" uuid="4fcc4bba-d36a-4160-8da4-42597149c806"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Kho: " + ($F{KHOXUAT} == null ? "" : $F{KHOXUAT})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="485" y="0" width="70" height="17" uuid="290a6636-6068-4c53-9ac7-c1c03037a069"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Mẫu 7A]]></text>
			</staticText>
		</band>
	</title>
	<columnHeader>
		<band height="51" splitType="Stretch">
			<staticText>
				<reportElement x="485" y="0" width="70" height="32" uuid="6a148e1d-008b-43c6-b9ef-934b85917897"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="89" y="0" width="152" height="32" uuid="5cd40592-996c-4b7b-8049-f0ca8dca5b02"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên thuốc - hàm lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="241" y="32" width="44" height="19" uuid="6dfd8b92-94ff-4b27-ab27-5d68192c05ec"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[4]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="32" width="80" height="19" uuid="6f416a62-15f8-40aa-881d-87908f05c797"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[5]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="32" width="27" height="19" uuid="008aa3da-7ebb-4c0e-b256-27a1abac8c94"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[1]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="0" width="80" height="32" uuid="c675337e-1bc0-4e00-8cae-871b026ec456"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Nước sản xuất]]></text>
			</staticText>
			<staticText>
				<reportElement x="485" y="32" width="70" height="19" uuid="5c6e9edb-f6f2-4ae3-b9cd-211b470689c4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[8]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="27" height="32" uuid="65f58e39-fb80-40ab-8552-d9b9889acc91"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="241" y="0" width="44" height="32" uuid="e4eb3ce2-dbab-4e4f-bf27-13f52e199384"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn vị tính]]></text>
			</staticText>
			<staticText>
				<reportElement x="415" y="32" width="70" height="19" uuid="e7922478-c516-4ddc-92de-2512e2dbdc51"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[7]]></text>
			</staticText>
			<staticText>
				<reportElement x="415" y="0" width="70" height="32" uuid="e0ac7ccb-b5dc-400b-b6cb-cae163cef487"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá (có VAT)]]></text>
			</staticText>
			<staticText>
				<reportElement x="365" y="0" width="50" height="32" uuid="749470c0-089d-4428-9b99-cc644fd41303"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="365" y="32" width="50" height="19" uuid="08b75724-3495-4c3f-a86b-2b7833229cc1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[6]]></text>
			</staticText>
			<staticText>
				<reportElement x="89" y="32" width="152" height="19" uuid="7975c188-ad6f-4f43-a444-2cb711c104f8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[3]]></text>
			</staticText>
			<staticText>
				<reportElement x="27" y="0" width="62" height="32" uuid="dec544e3-0981-4860-b46c-252d35c3b6ca"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement x="27" y="32" width="62" height="19" uuid="041bbd85-df04-4cde-8b68-1f29d995c06e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="23" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="27" height="23" uuid="c682dfe5-3dce-4953-b097-2a8f7ea8ea58"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="89" y="0" width="152" height="23" uuid="894b48ee-eb1b-4282-b1d6-08448bbc9eba"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="241" y="0" width="44" height="23" uuid="af6c5933-18b4-4df7-b76a-be7992a0edb8"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONVI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="285" y="0" width="80" height="23" uuid="bd657255-5bd5-44d8-97ec-e1f740e7aef1"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUOCSX}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="365" y="0" width="50" height="23" uuid="af67ad26-ad34-438e-b66a-98d4f140a059"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($F{SOLUONG}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="415" y="0" width="70" height="23" uuid="aa8c9aa8-61a9-46b4-bae9-24cae4b350f0"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($F{DONGIA}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="485" y="0" width="70" height="23" uuid="d256ed55-95a3-46fb-b15e-7e2284483f36"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($F{THANHTIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="27" y="0" width="62" height="23" uuid="f12c4636-8362-4835-a185-15c9928f515c"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MATHUOC}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20">
			<textField>
				<reportElement x="415" y="3" width="140" height="14" uuid="398e46b6-d836-43c9-bdde-3651d2588610"/>
				<box>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("dd/MM/yyyy HH:mm:ss")).format(new Date())]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="193" y="3" width="172" height="14" uuid="20a0e629-b3e3-41a5-87a6-8c803582e127"/>
				<box>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="186" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="27" height="22" uuid="b79ce65a-f653-456f-b140-15d28d5de7d6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="89" y="0" width="152" height="22" uuid="f86cc18a-7702-44fe-913b-c2579402ae3b"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="241" y="0" width="44" height="22" uuid="b2f8bae7-b45a-42db-bc7a-959125c661b0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="0" width="80" height="22" uuid="484b1515-beaf-4d7f-8b59-43a6f76351fe"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement x="365" y="0" width="50" height="22" uuid="9d9d6896-c6b1-4400-aaab-b1083192408d"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($V{TONG_SOLUONG}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="415" y="0" width="70" height="22" uuid="8c45c738-d5cd-499c-a6d5-82c11c5a9fab"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement x="485" y="0" width="70" height="22" uuid="5eb93603-4217-4c81-9aa5-bf19ac6a4a21"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($V{TONG_TIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="107" width="179" height="20" uuid="df7bee11-5dd1-45a3-bdf3-47b147ec4046"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG KHOA DƯỢC]]></text>
			</staticText>
			<staticText>
				<reportElement x="319" y="107" width="236" height="20" uuid="f270374e-077f-4068-a020-5d35ee5934c2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[THỦ KHO]]></text>
			</staticText>
			<staticText>
				<reportElement x="179" y="107" width="140" height="20" uuid="f3b29a7a-371d-475e-b358-0f8bac2c191a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾ TOÁN]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="57" width="179" height="20" uuid="63e2e3d5-8516-4e53-a92a-e1e82928c7e6"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[- Tổng số tiền đã cấp: ]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="37" width="179" height="20" uuid="a2bc1d0a-4ef7-40ec-b36f-9f51663572c2"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[- Tổng số đơn cấp: ]]></text>
			</staticText>
			<textField pattern="yyyy">
				<reportElement x="319" y="87" width="236" height="20" uuid="f428b885-0f19-49f0-ab73-a636b3f68601"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Hà Nội, ngày " + (new SimpleDateFormat("dd")).format(new java.util.Date()) +
" tháng " + (new SimpleDateFormat("MM")).format(new java.util.Date()) +
" năm " + (new SimpleDateFormat("yyyy")).format(new java.util.Date())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="179" y="37" width="140" height="20" uuid="7111ba4f-982f-4d06-ab8d-8e1e541ead82"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOPHIEUCAP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="179" y="57" width="140" height="20" uuid="1243ba8c-b717-43de-8f94-1b97656e7b4b"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new java.text.DecimalFormat("#,###,###,###,##0.00").format($V{TONG_TIEN}.doubleValue())).replace(".",";").replace(",",".").replace(";",",")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="319" y="37" width="236" height="20" uuid="eebd6984-0b92-4090-8c27-6ce2b6df88f3"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[(Phiếu)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="319" y="57" width="236" height="20" uuid="2c3635b2-3cad-40b6-a3f2-cb815d4fcd73"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[(Đồng)]]></text>
			</staticText>
			<staticText>
				<reportElement x="27" y="0" width="62" height="22" uuid="94782db3-ffe4-4b6e-9e13-75e2bc5e3713"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
